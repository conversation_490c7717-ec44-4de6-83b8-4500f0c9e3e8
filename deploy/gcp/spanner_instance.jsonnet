// Create the Spanner instance(s) for a cluster.
function(cloud)
  local cloudInfo = (import 'deploy/common/cloud_info.jsonnet')[cloud];
  local appName = 'spanner-instance';

  local createInstance = function(env, namespace)
    local instanceName = 'spanner-%s-%s' % [cloudInfo.shortName, namespace];
    // We have the option of configuring with a number of nodes or number of processing units.
    // We use processing units because they're more granular. 1000 processing units is 1 node.
    local processingUnits = {
      // TODO(jacqueline): Re-evaluate node requirements once the auth spanner implementation is
      // done. I think it's very possible that auth is low-volume enough that we can get away with
      // a tiny instance in prod.
      DEV: 100,
      STAGING: 100,
      PROD: 100,
    }[env];

    {
      apiVersion: 'spanner.cnrm.cloud.google.com/v1beta1',
      kind: 'SpannerInstance',
      metadata: {
        name: instanceName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        config: 'regional-%s' % cloudInfo.region,
        displayName: instanceName,
        resourceID: instanceName,
        processingUnits: processingUnits,
        // We can upgrade later, so starting with standard edition for now.
        // https://cloud.google.com/spanner/docs/create-manage-instances#upgrade-edition
        // edition: 'STANDARD',
      },
    }
  ;

  local instances =
    if cloud == 'GCP_US_CENTRAL1_DEV' then
      [
        createInstance('DEV', 'central-dev'),
      ]
    else if cloud == 'GCP_US_CENTRAL1_PROD' then
      [
        createInstance('STAGING', 'central-staging'),
        createInstance('PROD', 'central'),
      ]
    else [];

  instances
