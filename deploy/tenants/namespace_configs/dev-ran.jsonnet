local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

config + {
  flags: config.flags
         .override('forceMtls', true)
         .override('useSharedDevRequestInsightBigquery', false)
         .override('extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
    smart_paste_model: 'forger-smart-paste-sc2-7b-32k-edit',
    instruction_model: 'claude-instruction-v2-edit',
    chat_model: 'claude-sonnet-3-5-16k-v11-2-chat',
    chat_raw_output_model: 'claude-sonnet-3-5-16k-v11-2-chat',
    chat_fallback_model: 'claude-sonnet-3-5-16k-v11-2-direct-chat',
  })
         .override('exportAnnotations', {
    enabled: true,
    webhook_url: '',
  }),
  defaultTenantFlags: config.defaultTenantFlags.override('supportAccessControl', false),
}
