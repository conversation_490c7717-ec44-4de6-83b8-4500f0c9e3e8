local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

// Slack secrets for XZ-Augment-Dev.
local slackSealedSecrets = {
  basic_creds: 'AgB1jkzbQfF1kvt/lJmgb1oT+t93UFbJPib2+m5vc+B7IevI6wkQYxgprXmH7jc65r5DsX9Mt2Cjn+Ldu+nFh/mwF2bSOcbuFo9qZ+GP6udJht5VF45mQIzoj7Z64D/1nsuyRYchnW+85oL3dSsr2EkrsgiH9LJyt0ugu9LExMqOeSpTZJmh+t1NLiQx9iDA19113WJnCHnZHxbbgCJVuWgNHYL627xSLNW00ZfNoNGRKOyfbrY7qEJ9Gbp/IpKkiseS9aLS0H7qh+DFqmoRj35bXVjyMmunCCFtAR1YRvmbHWNb0gL8JiRpBNePzqJeTYdvmd9J2BwgG+1EwNCnxn1yb5YvJMai4HpvBC1Os2H7JAocOfBpIzSZuXWsKg6Yn5iZUGSzxgKatdzAT02yqfc3EUad0qOLWjj//ODyl6gxvSku8bKWwHskf0MlxSp1RDJdrJBGFMuCzj1ZoD7FA05Cx8Rj3j6OaI5UiOkvEKP0KVZ2Z5mc7jPlO49DFuXj5JHQn3vkkv79OT2I+FPB3Bclbtao+ae2dTrjFpdXCpvHHB/aZLgYbg1neL2Flp87PQBgSTD36Fms2SEYpp4hsl3uoY7fTU6bq7kZ5LGTfKsb+KaC/omQKmHATDxGIiNn/VWg1Ww76cNcclfT9G49j8eXORxyHXFhWChVvHSEfH9tYeTu+H36HTBmiJn5xZqsOlwHNJFMIAnoP3ddnA8kt6E/62+7GZCCBO+5nzACZr2MajpCvEKoiaOsFUHBdisUR/4nxh96WDvh+lnbkYbrd8TUlVNndSxl9OpSVQDVdvpwiQ==',  // pragma: allowlist secret
  signing_secret: 'AgBfE5wzYrVGe5fPiY6XRbMMv9T7ZF1RpZr+LTL6NFfjzuo6NdEv2A0AnwGipoMLviWX4AzgUZ/HXAjTXd2bp7dU/YnKPNoOaPZpyAI104AmmZt+UFq/gNG6RSx/KTQhR7F+HAcSX847OUsnGt+G2CTbcg6DuDRc2BMS9bs/LfDjUh3fUbxwshuzoZGeLReqHZYC6crp8qKzgyntPF/gbxRQBchgg9+GAZj4rgOCG02SjWSOy9VikvUiJfNXlmEIjYBaMiKzRoJB4uYY5UQ+33Vx7jVTign7/muG3cIqA+wdc9TD9nWLA0PhNFz7dj+zRfABDP+ixcsSFQbTDzT8xECM2txXktRVvlmpIN/kpRgGxts0eanFj0UG1Hnfp5i/+TghDT4KHedIMgqunS/0fjxxQLQ9YItJxPt6cMTXAaJ9UyoP2op5z7a9pM659u/z3hhNjx05kU7n8IuvsxmCNPWW2BWse2E96kEosFzvn2b+hS2HMtIJU/EDvn56azSsu72RkxwtCYtpY4dmJNJmot/saHrZQRMx7BpON7iIzuqbXONI89+PygJ6vsf0hJXtn442ToviC1hJulPtlOKhaJdZ9AN2Ef85YtGVkstSF4UqeEJol8lX3vJ0PfhFhMulnb8NrfYLyNgwN79KEmGTzcwWRO+OgZqLLdH7BO+TPBONX5YM1plfNGrOYRHc4a1BhKSd2NqrEtsavwcRszDyMYPG2zuJ5IingFd/XyhRCWkyTA==',  // pragma: allowlist secret
  token: 'AgCO/OcyAR2H4NKorpvjQgnG2Kd5+7lEA48VdgGwcyk2s/KDfLfWZ9Y123wIXF3HrEMAwr085G9+RkwaDTrZyDbTG5oeX5Gga3xOVhCQCWn1RfDG/J2pmym4P9u+zAjCJIFaNXzwi4jES79OP96gMrsOSYbIks6+Qmt7eMlPlpWT/nWlR8R0bd+Km1iwdxGZDrwUFq6Y2jQzeb76mBJCs2RsamRoeUbKQuwOJ+kGvKY5j/Ar/9FsA0ho+qFiI4VyhX9vZqTb8oKnjk40valH/yxgx/6KGmXgIvmH43ne5TX/2IPp7hLdM5/VekrTHu6KD9Fh3F+RaZSnz4oGvYevBkxnimzc3HdRrPpOkRY0rqGaxMfFJubDE91LXwEjdsLGSSiVYexWE9MBNxT5tD445OSnC7bEvzGRZlAuWfaQO+icKJPEzY5N4MIRnfYixwAazFEE2sbR5E0SLmyZpWMAUOziKExUEHnLImUVZlfBnNSSR7ryMf7gCo18/AlmZh/mgae0TSDSHHRrW8Zg+pfWVvqT9gblSmYakLxbVl/MWO3/HDdOUttpAvTKRcQJwy7CvcKujhgtNxwNvD9pFxmtG74vlgAj3DRw17hq2FHc+v80mRdZmnjwyxhHhvWg9HdfzdGV+eGl35sC4o4k2avTKTpDVLTXu1udkotlmu1yQD0EZeZKxMBCEOrm5YZmWW0TZ6KVuEh6LvQ7TifCkoHqfIrwvjGu1Ilw7LIv+56xJVTkZGJsja0SUWQ91vffwYDgrW8xtoDBUoxdCJU=',  // pragma: allowlist secret
};

// Setup instructions: https://www.notion.so/Setting-up-a-dev-Github-app-11cbba10175a8030af17de8f140724eb
local githubSealedSecrets = {
  app_id: 'AgC71RzkGNw+9vetJ6zqQfGIvfTMiGbaQPoUyrKcF9WUmkvZJNExZS/SILLrUKUkTvShhCH24VcU2GFtGaf24aDQ+f4fyq2zP6TQWUwF0V0H0hgatgAroGVtBFTue07i10SOBEkeActPI9ww7g4U9G3glE40ZWGUfBwxMe7IxjWaUm+c1tkpF6/7kF3s/x7eNBTY0q+5X0hg4ZB4GfM7Lb/RkHLTP5fV2RYrAzAgofH+xCdKsaeFDSSun9EMHGMZRdUbQiu7rn+W+7g8F16PPi2RS+m67x2TkbbPe1O+Vplivj5uY4z/kniLtYcY64nb/OLr4hF9LgWQ0AzJt1ygHtLRdCDOWTVETu1qt7qhDIZqvY/JheyS74fgZVJaybdp6HT2LPMlpWIyIN9fyj8EEV5mE7+XsbcJOyy3WhAm4fQVv2Fz7bNSHB8+GHbgPE5ZR/W4/QN0TUhZHKjy7iYJXy4J0dCZybLiC3jTYBDjSSOyFjdFX1ePe5bMr4W4Xwp46WO5jqCDyxM/iNoGlmdiob2ZPRDgw+stRQjakrAGgjs2Wcj5BfZC26Gfb0+kPPuz6Y/it2V4mlzNSRYVs7phKG+IiIi0HV53J1m1J+FSJZJOtO6dCslMyaNSZdh3dwud5OLI4V88GymWIXgt1L/+7x2fvUnCsh/NM5mVJp7GGQ5c9Iu099ULcdX/8/Kgbt4/5Q7V6J4j59rd',  // pragma: allowlist secret
  client_id: 'AgCj9UFMAzOxaJRHMcFOtB30mazRiV+yyqbBW34xeojQX2TCJpIr8XBFf6bJNVkZ2I59bsSmVwlp7XhnwF4YDy2X1B5Tu6XrUtroeZfkbP6xfa4ZEJEcNBFJ8i9QByBcvLW5RhTCWRC2PF3FqcF+hCmrIP2Ic6f/BbIqDCq3k/O7E0WbbUD51jlilpcC4dSsciFzm94bSYJXhT54+t1RD/bo+dY/XlbNWtRsDV5Iu+3NmrpvD87Y5c5zQkAw9qtw0UdgSVUEgoTDk23cXAbIIptLL2o1oRZN/+VM6YjUxlhB8ogkCet5vGlOzITzSiSVkC68tAlgjhaMt70+Eir1onChaFpYYS5kvAmaINuy48E1yEsPj6B0ONOCY0dIR+PSItN81PFlwkLL3UCyjznouK1kYgG0BLHZjJ+up3PWSmEjghzbOPOpEpvPw9pgpUtAJ9lSC32dg+cjXjq9vcnps03E90hOkhhS3y7Mz97rGzt+UyPQOFzCCKdSpgVoeS4gZziPsiltIwoq/dccFk/HzdDF963UU9PrvVR6vT6lGIDivPSiOpSQV6/SgaHxIv2tb9d8CGtq2YaHY6/XZLDce7skyXrgH9c8WtPfwlirZsUQBc29PtHoTRk+0ujE/I1E7rpdjWamkUQOtUMJKx6r2pzhqaARVlK+zqAQ5zdzChipi8XDGoNTYiRMsn4Rp0VXaydfcM/rdAfPt2300rH8BvZiEeMveg==',  // pragma: allowlist secret
  client_secret: 'AgAP5j7tTXkmt3alGs4PO8Gap1pFd5Avq9GlqYQCophmohjSKaXOeTwopU60dhTFpHIEor/ArajaERZ2kUnMrNgNew2V1orGCiQWLNl8OpSRjxzWTq5ieMU1HqWEPuNJ5ecy3kZiq+OC5qk9TlxEn0LgjRWNY7La9g1bZtaNgnWdBp2V+WuVSVeEvmGaBpzkFXNYH3QVayi0Q6WjWYFns2NgjJIWiE5479gMJRUma1yypUX8eZAy8wuv0slocWpdbQfGdi3l4l798mydgk054JuVYTyUsJWLNd/7dKiH1dYZ7TsPsDZOFyVZ/ai00v1U2/Frmm2rJ7/xhv8yxRToVnysp6v1dXKi8+giifdMvdpjCgGp8TGP/8ATbdwrfISJP6cLA+xeZVQn0gyHj6ndk+LKGy9BvsgeKl54CIQDNa4VvbZ6Rf5C07H33OTm4osGFVb/t/SbW7wcQjhz6I4sxNfYBh+aTZURnnCiomltRF2nZUoKTh3q+ygUrxbMc4SDayszVrPWsv0mk/jSRlH6oHSk7IWd6eYiCI9kA63v1/EYuaoHyckRytklha5AD8qrA2j2uUZgEuyCUxDG4sM0n/8yDkkjKI5QnW1X0XC4PNSYu4/XkbcOKA2SyvRarbVRkaS66FPRnpK31kRFouWM1ZBcoKeF9LAtp3/4ygFr3fIkvWO/uRnpCi5Dos9t+9UZicIu/ohKI+ZzOUQjElJ2uZAujaVjOLQ7s3pWnxSCI7wNabbr4hBTzDk7',  // pragma: allowlist secret
};

local githubPrivateKeyInfo = {
  name: 'dev-xiaolei-github-processor-private-key',
  version: 4,
};

local githubOauthSecretOverride = {
  name: 'dev-xiaolei-github-processor-oauth-app-secret',
  version: 'latest',
};

local supabaseOauthSecretOverride = {
  name: 'supabase-oauth-secret-dev-xiaolei',
  version: '3',
};

local linearOauthClientIdOverride = {
  name: 'dev-xiaolei-linear-client-id',
  version: '1',
};

local linearOauthClientSecretOverride = {
  name: 'dev-xiaolei-linear-client-secret',
  version: '1',
};

local orbWebhookSecretOverride = {
  name: 'dev-orb-webhook-signing-secret-dev-xiaolei',
  version: 'latest',
};

config + {
  flags: config.flags
         .override(
    'extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
      slackbot_enable_v2_formatter: true,
      slack_bot_include_repo_info_in_prompt: true,
      slackbot_select_repo_context: true,
      enable_glean: true,
      enable_agents: true,
      vscode_chat_with_tools_min_version: '0.0.0',
      vscode_agent_mode_min_version: '0.0.0',
      vscode_design_system_rich_text_editor_min_version: '0.0.0',
      auth_central_enable_stripe_event_processor: true,
      auth_central_create_setup_intent_on_customer_created: true,
      misuse_monitor_dry_run: false,
      chat_server_block_clients_by_prompt_regex: 'TEST_THIS0123',
      chat_server_ban_rules_url_and_hash: 'https://storage.googleapis.com/augment-bazel-data/public/blockexp_2025-03-31T18-42-10-global.txt',
      misuse_monitor_banned_users_gcs_path: 'test_ban.csv',
    }
  ).override('slackSealedSecrets', slackSealedSecrets)
         .override('githubSealedSecrets', githubSealedSecrets)
         .override('githubPrivateKeyInfo', githubPrivateKeyInfo)
         .override('githubOauthSecretOverride', githubOauthSecretOverride)
         .override('supabaseOauthSecretOverride', supabaseOauthSecretOverride)
         .override('linearOauthClientIdOverride', linearOauthClientIdOverride)
         .override('linearOauthClientSecretOverride', linearOauthClientSecretOverride)
         .override('orbWebhookSecretOverride', orbWebhookSecretOverride),
}
