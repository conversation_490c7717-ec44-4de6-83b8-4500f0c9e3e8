"""CLI application for kubecfg tombstoning."""

import pathlib
import argparse
import logging
import sys

from InquirerPy import prompt  # type: ignore

from base.logging.console_logging import setup_console_logging
from base.cloud.k8s import kubectl_factory
from tools.kubecfg.kubecfg import (
    KubeCfgException,
)
from tools.kubecfg import kubecfg_deletion


def _confirm(message: str) -> bool:
    questions = [
        {
            "type": "confirm",
            "message": message,
            "name": "continue",
            "default": True,
        },
    ]

    answers = prompt(questions)
    return bool(answers["continue"])


class KubecfgDeletionUtil:
    """CLI utility for kubecfg_deletion."""

    def __init__(
        self,
        kubecfg: kubecfg_deletion.KubecfgDeletion,
    ):
        """Constructs a KubecfgDeletionUtil instance.

        Args:
            kubecfg: KubecfgDeletion instance
        """
        self.kubecfg = kubecfg

    def delete(
        self,
        api_version: str,
        kind: str,
        name: list[str],
        namespace: str,
        dry_run: bool,
    ) -> int:
        """Deletions objects from a namespace.

        Args:
            api_version: The api version of the object.
            kind: The kind of the object.
            name: The name of the object.
            namespace: The namespace of the object.
            dry_run: Whether to dry run the tombstone.

        Returns:
            True if the object was found and deleted, False otherwise.
        """
        found = False
        for n in name:
            if not dry_run:
                confirmed = _confirm(f"Deleting {kind} {n} from namespace {namespace}?")
                if not confirmed:
                    return 1
            r = self.kubecfg.delete(api_version, kind, n, namespace, dry_run)
            if r.deleted:
                found = True
        if found:
            return 0
        logging.info("No objects deleted")
        return 2


def main():
    """Entrypoint."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--cloud",
        required=True,
        type=str.upper,
        choices=[
            "GCP_US_CENTRAL1_PROD",
            "GCP_EU_WEST4_PROD",
            "GCP_US_CENTRAL1_DEV",
        ],
    )
    parser.add_argument("--namespace", required=True, help="Namespace to delete from")
    parser.add_argument("--api-version")
    parser.add_argument("--kind", required=True, help="Kind of object to delete")
    parser.add_argument("--name", default=[], action="append")
    parser.add_argument("--dry-run", action="store_true", default=False)
    parser.add_argument(
        "--kube-config-file",
        default=pathlib.Path.home().joinpath(".kube", "config"),
        type=pathlib.Path,
    )

    args = parser.parse_args()
    setup_console_logging(add_timestamp=False)

    logging.debug("Args: %s", args)
    if not args.name:
        parser.exit(1, "Must specify at least one name")
    if args.kube_config_file:
        if not pathlib.Path(args.kube_config_file).exists():
            parser.exit(1, f"kube-config-file '{args.kube_config_file}' does not exist")
    if args.kind != "app" and not args.api_version:
        parser.exit(1, "Must specify api-version for non-app kinds")

    kubectl_instance = kubectl_factory.create_kubectl_factory(args.kube_config_file)(
        args.cloud
    )

    kubecfg = kubecfg_deletion.KubecfgDeletion(
        kubectl=kubectl_instance,
    )
    kubecfg_util = KubecfgDeletionUtil(
        kubecfg,
    )

    try:
        sys.exit(
            kubecfg_util.delete(
                args.api_version, args.kind, args.name, args.namespace, args.dry_run
            )
        )
    except KubeCfgException as e:
        logging.error("%s", e.msg)
        if e.stdout:
            logging.error("%s", e.stdout)
        if e.stderr:
            logging.error("%s", e.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
