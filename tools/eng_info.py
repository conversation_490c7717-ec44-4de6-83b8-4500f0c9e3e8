"""Information about the engineers extracted from eng.jsonnet."""

from dataclasses import dataclass, field
import json
import pathlib
import re


@dataclass()
class Eng:
    """Engineer information.

    The term engineer is used here loosely. Anybody who has access to any
    production or dev system or github is considered an engineer.
    """

    fullname: str
    """Full name."""

    username: str
    """Username."""

    gcp_access: str | None
    """GCP access."""

    pii_access: str
    """PII access: full | masked"""

    github: str | None = None
    """GitHub username."""

    glassbreaker_dev: bool = False
    """Glassbreaker access in dev."""

    glassbreaker_prod: bool = False
    """Glassbreaker access in prod."""

    alternative_github_emails: list[str] = field(default_factory=list)
    """Other email addresses that the engineer uses for github."""

    @property
    def email(self):
        """Get the mail email address."""
        return f"{self.username}@augmentcode.com"


_GITHUB_NO_REPLY_RE = r"^\d+\+(.+)@users.noreply.github.com$"


class EngInfo:
    """Engineer information."""

    def __init__(self, engs: list[Eng]):
        self._engs = engs

    @classmethod
    def from_file(cls, path: pathlib.Path):
        """Loads the engineers from a file."""
        eng_list = json.loads(path.read_text())
        engs = [
            Eng(
                fullname=e["fullname"],
                username=e["username"],
                gcp_access=e["gcp_access"],
                pii_access=e["piiAccess"],
                github=e.get("github"),
                glassbreaker_dev=e.get("glassbreakerDev", False),
                glassbreaker_prod=e.get("glassbreakerProd", False),
                alternative_github_emails=e.get("alternativeGithubEmails", []),
            )
            for e in eng_list
        ]
        return cls(engs)

    @classmethod
    def load(cls):
        """Loads the engineers from the default file."""
        return cls.from_file(pathlib.Path("deploy/common/eng.json"))

    def get_by_username(self, username: str) -> Eng | None:
        """Gets the engineer by username."""
        i = [e for e in self._engs if e.username == username]
        if len(i) == 1:
            return i[0]
        else:
            return None

    def resolve_commit_email(self, author: str) -> Eng | None:
        """Gets the engineer by commit email address."""
        if author.endswith("@augmentcode.com"):
            return self.get_by_username(author[: -len("@augmentcode.com")])
        elif author.endswith("users.noreply.github.com"):
            m = re.search(_GITHUB_NO_REPLY_RE, author)
            if m:
                g = m.groups()[0]
                e = [e for e in self._engs if e.github == g]
                if e:
                    return e[0]
        for e in self._engs:
            if author in e.alternative_github_emails:
                return e
        return None
