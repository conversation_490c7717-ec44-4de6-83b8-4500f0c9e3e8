import uuid
from unittest.mock import MagicMock, patch

import pytest
import github

from tools.bazel_runner.ci.consumers.post_merge_consumer import all_excluded_files
from tools.bazel_runner.ci.consumers.last_known_good_consumer import (
    get_previous_failing_postmerge_test,
    LastRuns,
)
from tools.bazel_runner.server import test_runner_pb2
import tools.bazel_runner.git.checkout_pb2 as checkout_pb2
from tools.bazel_runner.control import bazel_runner_pb2
from tools.bot import bot_pb2
import third_party.proto.bazel_build.build_event_stream_pb2 as build_event_stream_pb2


@pytest.fixture
def mock_github_client():
    client = MagicMock(spec=github.Github)
    repo = MagicMock()
    commit = MagicMock()
    client.get_repo.return_value = repo
    repo.get_commit.return_value = commit
    commit.commit.author.email = "<EMAIL>"
    commit.commit.author.name = "Test Author"
    commit.commit.message = "Test commit message"
    commit.sha = "abcdef1234567890"
    return client


def test_get_previous_failing_postmerge_test(mock_github_client):
    past_runs = [
        test_runner_pb2.TestRunInfo(
            run_id=str(uuid.uuid4()),
            tags=["post-merge"],
            state=test_runner_pb2.RUN_STATE_FAILURE,
            test_execution=bazel_runner_pb2.BazelRunnerExecutionGroupSpec(
                checkout=checkout_pb2.CheckoutSpec(
                    owner="test-owner",
                    repo_name="test-repo",
                    commit_checkout=checkout_pb2.CommitCheckoutSpec(ref="test-ref"),
                )
            ),
            jobs=[
                test_runner_pb2.JobInfo(
                    tests=[
                        test_runner_pb2.TestInfo(
                            target_name="//test/target:name",
                            status=build_event_stream_pb2.TestStatus.FAILED,
                        )
                    ]
                )
            ],
        ),
        test_runner_pb2.TestRunInfo(
            run_id=str(uuid.uuid4()),
            tags=["post-merge"],
            state=test_runner_pb2.RUN_STATE_PASSED,
        ),
    ]
    result = get_previous_failing_postmerge_test(
        mock_github_client,
        "https://test-viewer.example.com",
        "//test/target:name",
        past_runs,
    )

    assert isinstance(result, bot_pb2.BreakageInfo)
    assert result.run_id == past_runs[0].run_id
    assert (
        result.run_url == f"https://test-viewer.example.com/run/{past_runs[0].run_id}"
    )
    assert result.commit.author_email == "<EMAIL>"
    assert result.commit.author_name == "Test Author"
    assert result.commit.commit_message == "Test commit message"
    assert result.commit.repo_name == "test-repo"
    assert result.commit.repo_owner == "test-owner"
    assert result.commit.sha == "abcdef1234567890"


def test_get_previous_failing_postmerge_multiple_test(mock_github_client):
    past_runs = [
        test_runner_pb2.TestRunInfo(
            run_id=str(uuid.uuid4()),
            tags=["post-merge"],
            state=test_runner_pb2.RUN_STATE_FAILURE,
            test_execution=bazel_runner_pb2.BazelRunnerExecutionGroupSpec(
                checkout=checkout_pb2.CheckoutSpec(
                    owner="test-owner",
                    repo_name="test-repo",
                    commit_checkout=checkout_pb2.CommitCheckoutSpec(ref="test-ref"),
                )
            ),
            jobs=[
                test_runner_pb2.JobInfo(
                    tests=[
                        test_runner_pb2.TestInfo(
                            target_name="//test/target:name",
                            status=build_event_stream_pb2.TestStatus.FAILED,
                        )
                    ]
                )
            ],
        ),
        test_runner_pb2.TestRunInfo(
            run_id=str(uuid.uuid4()),
            tags=["post-merge"],
            state=test_runner_pb2.RUN_STATE_FAILURE,
            test_execution=bazel_runner_pb2.BazelRunnerExecutionGroupSpec(
                checkout=checkout_pb2.CheckoutSpec(
                    owner="test-owner",
                    repo_name="test-repo",
                    commit_checkout=checkout_pb2.CommitCheckoutSpec(ref="test-ref"),
                )
            ),
            jobs=[
                test_runner_pb2.JobInfo(
                    tests=[
                        test_runner_pb2.TestInfo(
                            target_name="//test/target:name",
                            status=build_event_stream_pb2.TestStatus.FAILED,
                        )
                    ]
                )
            ],
        ),
        test_runner_pb2.TestRunInfo(
            run_id=str(uuid.uuid4()),
            tags=["post-merge"],
            state=test_runner_pb2.RUN_STATE_PASSED,
        ),
    ]
    result = get_previous_failing_postmerge_test(
        mock_github_client,
        "https://test-viewer.example.com",
        "//test/target:name",
        past_runs,
    )

    assert isinstance(result, bot_pb2.BreakageInfo)
    assert result.run_id == past_runs[1].run_id
    assert (
        result.run_url == f"https://test-viewer.example.com/run/{past_runs[1].run_id}"
    )
    assert result.commit.author_email == "<EMAIL>"
    assert result.commit.author_name == "Test Author"
    assert result.commit.commit_message == "Test commit message"
    assert result.commit.repo_name == "test-repo"
    assert result.commit.repo_owner == "test-owner"
    assert result.commit.sha == "abcdef1234567890"


def test_get_previous_failing_postmerge_test_intermediate_passing(mock_github_client):
    """Return the right information when a test has been failing in the past, but was passing most recently."""
    past_runs = [
        test_runner_pb2.TestRunInfo(
            run_id=str(uuid.uuid4()),
            tags=["post-merge"],
            state=test_runner_pb2.RUN_STATE_PASSED,
            test_execution=bazel_runner_pb2.BazelRunnerExecutionGroupSpec(
                checkout=checkout_pb2.CheckoutSpec(
                    owner="test-owner",
                    repo_name="test-repo",
                    commit_checkout=checkout_pb2.CommitCheckoutSpec(ref="test-ref"),
                )
            ),
            jobs=[
                test_runner_pb2.JobInfo(
                    tests=[
                        test_runner_pb2.TestInfo(
                            target_name="//test/target:name",
                            status=build_event_stream_pb2.TestStatus.PASSED,
                        )
                    ]
                )
            ],
        ),
        test_runner_pb2.TestRunInfo(
            run_id=str(uuid.uuid4()),
            tags=["post-merge"],
            state=test_runner_pb2.RUN_STATE_FAILURE,
            test_execution=bazel_runner_pb2.BazelRunnerExecutionGroupSpec(
                checkout=checkout_pb2.CheckoutSpec(
                    owner="test-owner",
                    repo_name="test-repo",
                    commit_checkout=checkout_pb2.CommitCheckoutSpec(ref="test-ref"),
                )
            ),
            jobs=[
                test_runner_pb2.JobInfo(
                    tests=[
                        test_runner_pb2.TestInfo(
                            target_name="//test/target:name",
                            status=build_event_stream_pb2.TestStatus.FAILED,
                        )
                    ]
                )
            ],
        ),
        test_runner_pb2.TestRunInfo(
            run_id=str(uuid.uuid4()),
            tags=["post-merge"],
            state=test_runner_pb2.RUN_STATE_PASSED,
            test_execution=bazel_runner_pb2.BazelRunnerExecutionGroupSpec(
                checkout=checkout_pb2.CheckoutSpec(
                    owner="test-owner",
                    repo_name="test-repo",
                    commit_checkout=checkout_pb2.CommitCheckoutSpec(ref="test-ref"),
                )
            ),
            jobs=[
                test_runner_pb2.JobInfo(
                    tests=[
                        test_runner_pb2.TestInfo(
                            target_name="//test/target:name",
                            status=build_event_stream_pb2.TestStatus.PASSED,
                        )
                    ]
                )
            ],
        ),
    ]
    result = get_previous_failing_postmerge_test(
        mock_github_client,
        "https://test-viewer.example.com",
        "//test/target:name",
        past_runs,
    )
    assert result is None


def test_get_previous_failing_postmerge_test_ignore_premerge_test(mock_github_client):
    """Return None when the test is not a postmerge test."""
    past_runs = [
        test_runner_pb2.TestRunInfo(
            run_id=str(uuid.uuid4()),
            tags=["pre-merge"],
            state=test_runner_pb2.RUN_STATE_FAILURE,
            test_execution=bazel_runner_pb2.BazelRunnerExecutionGroupSpec(
                checkout=checkout_pb2.CheckoutSpec(
                    owner="test-owner",
                    repo_name="test-repo",
                    commit_checkout=checkout_pb2.CommitCheckoutSpec(ref="test-ref"),
                )
            ),
            jobs=[
                test_runner_pb2.JobInfo(
                    tests=[
                        test_runner_pb2.TestInfo(
                            target_name="//test/target:name",
                            status=build_event_stream_pb2.TestStatus.FAILED,
                        )
                    ]
                )
            ],
        ),
        test_runner_pb2.TestRunInfo(
            run_id=str(uuid.uuid4()),
            tags=["post-merge"],
            state=test_runner_pb2.RUN_STATE_PASSED,
            test_execution=bazel_runner_pb2.BazelRunnerExecutionGroupSpec(
                checkout=checkout_pb2.CheckoutSpec(
                    owner="test-owner",
                    repo_name="test-repo",
                    commit_checkout=checkout_pb2.CommitCheckoutSpec(ref="test-ref"),
                )
            ),
            jobs=[
                test_runner_pb2.JobInfo(
                    tests=[
                        test_runner_pb2.TestInfo(
                            target_name="//test/target:name",
                            status=build_event_stream_pb2.TestStatus.PASSED,
                        )
                    ]
                )
            ],
        ),
    ]
    result = get_previous_failing_postmerge_test(
        mock_github_client,
        "https://test-viewer.example.com",
        "//test/target:name",
        past_runs,
    )
    assert result is None


def test_get_previous_failing_postmerge_test_no_failure(mock_github_client):
    past_runs_no_failure = [
        test_runner_pb2.TestRunInfo(
            run_id=str(uuid.uuid4()),
            tags=["post-merge"],
            state=test_runner_pb2.RUN_STATE_PASSED,
            test_execution=bazel_runner_pb2.BazelRunnerExecutionGroupSpec(
                checkout=checkout_pb2.CheckoutSpec(
                    owner="test-owner",
                    repo_name="test-repo",
                    commit_checkout=checkout_pb2.CommitCheckoutSpec(ref="test-ref"),
                )
            ),
            jobs=[
                test_runner_pb2.JobInfo(
                    tests=[
                        test_runner_pb2.TestInfo(
                            target_name="//test/target:name",
                            status=build_event_stream_pb2.TestStatus.PASSED,
                        )
                    ]
                )
            ],
        )
    ]
    result = get_previous_failing_postmerge_test(
        mock_github_client,
        "https://test-viewer.example.com",
        "//test/target:name",
        past_runs_no_failure,
    )
    assert result is None


@pytest.mark.parametrize(
    "files, exclude_patterns, expected",
    [
        # Test case 1: No exclusion patterns - should return True if any files exist
        (["foo.py", "bar.js"], [], False),
        # Test case 2: Basic exclusion pattern
        (["docs/README.md", "src/main.py"], ["*.md"], False),
        # Test case 3: All files excluded
        (["docs/README.md", "docs/CONTRIBUTING.md"], ["*.md"], True),
        # Test case 4: Multiple patterns
        (
            ["docs/README.md", "src/main.py", "test.txt"],
            ["*.md", "*.txt"],
            False,
        ),
        # Test case 5: Directory patterns
        (
            ["docs/guide.md", "src/test.py", "tests/test_main.py"],
            ["docs/*", "tests/*"],
            False,
        ),
        # Test case 6: Empty file list
        ([], ["*.md"], True),
        # Test case 7: Complex patterns
        (
            ["src/foo/bar.py", "docs/api/v1/spec.md", "README.md"],
            ["docs/**/*", "*.md"],
            False,
        ),
        # Test case 8: All files match exclusion patterns
        (
            ["README.md", "CONTRIBUTING.md", "docs/guide.md"],
            ["*.md", "docs/*.md"],
            True,
        ),
        # Test case 9: prefix
        (
            ["research/umpalumpa/foo.py"],
            ["research/**"],
            True,
        ),
    ],
)
def test_check_files(files, exclude_patterns, expected):
    """Test check_files function with various combinations of files and patterns."""
    result = all_excluded_files(files, exclude_patterns)
    assert result == expected


def test_check_files_empty_patterns():
    """Test check_files with empty exclusion patterns."""
    files = ["src/main.py", "tests/test_main.py"]
    result = all_excluded_files(files, [])
    assert not result


def test_check_files_empty_files():
    """Test check_files with empty files list."""
    patterns = ["*.py", "*.md"]
    result = all_excluded_files([], patterns)
    assert result


@pytest.fixture
def mock_bazel_runner_client():
    client = MagicMock()
    return client


def test_last_runs_init(mock_bazel_runner_client):
    """Test LastRuns initialization."""
    run_id = uuid.uuid4()
    chunk_size = 5

    # Mock the get_runs method to return a list of test run infos
    mock_runs = [
        test_runner_pb2.TestRunInfo(run_id=str(uuid.uuid4())) for _ in range(chunk_size)
    ]
    mock_bazel_runner_client.get_runs.return_value = mock_runs

    # Initialize LastRuns
    last_runs = LastRuns(mock_bazel_runner_client, run_id, chunk_size)

    # Verify the client was called with the correct parameters
    mock_bazel_runner_client.get_runs.assert_called_once_with(
        newest_run_id=run_id, max_count=chunk_size
    )

    # Verify the past_runs list was initialized correctly
    assert len(last_runs.past_runs) == 1
    assert len(last_runs.past_runs[0]) == chunk_size
    assert last_runs.chunk_size == chunk_size


def test_last_runs_get_next_chunk(mock_bazel_runner_client):
    """Test LastRuns _get_next_chunk method."""
    run_id = uuid.uuid4()
    chunk_size = 5

    # Create two sets of mock runs
    first_chunk = [
        test_runner_pb2.TestRunInfo(run_id=str(uuid.uuid4())) for _ in range(chunk_size)
    ]
    second_chunk = [
        test_runner_pb2.TestRunInfo(run_id=str(uuid.uuid4())) for _ in range(chunk_size)
    ]

    # Configure the mock to return different values on subsequent calls
    mock_bazel_runner_client.get_runs.side_effect = [first_chunk, second_chunk]

    # Initialize LastRuns
    last_runs = LastRuns(mock_bazel_runner_client, run_id, chunk_size)

    # Call _get_next_chunk
    last_runs._get_next_chunk()

    # Verify the client was called with the correct parameters for the second call
    assert mock_bazel_runner_client.get_runs.call_count == 2
    mock_bazel_runner_client.get_runs.assert_called_with(
        newest_run_id=uuid.UUID(first_chunk[-1].run_id), max_count=chunk_size
    )

    # Verify the past_runs list was updated correctly
    assert len(last_runs.past_runs) == 2
    assert len(last_runs.past_runs[1]) == chunk_size


def test_last_runs_iterable(mock_bazel_runner_client):
    """Test LastRuns Iterable class."""
    run_id = uuid.uuid4()
    chunk_size = 3

    # Create valid UUIDs for the test run IDs
    first_chunk_ids = [str(uuid.uuid4()) for _ in range(chunk_size)]
    second_chunk_ids = [str(uuid.uuid4()) for _ in range(chunk_size)]

    # Create two sets of mock runs with valid UUIDs
    first_chunk = [
        test_runner_pb2.TestRunInfo(run_id=first_chunk_ids[i])
        for i in range(chunk_size)
    ]
    second_chunk = [
        test_runner_pb2.TestRunInfo(run_id=second_chunk_ids[i])
        for i in range(chunk_size)
    ]
    empty_chunk = []

    # Configure the mock to return different values on subsequent calls
    mock_bazel_runner_client.get_runs.side_effect = [
        first_chunk,
        second_chunk,
        empty_chunk,
    ]

    # Initialize LastRuns
    last_runs = LastRuns(mock_bazel_runner_client, run_id, chunk_size)

    # Use the iter method to get an iterable
    iterable = last_runs.iter()

    # Collect all items from the iterable
    items = list(iterable)

    # Verify we got all items from both chunks
    assert len(items) == chunk_size * 2
    assert items[0].run_id == first_chunk_ids[0]
    assert items[-1].run_id == second_chunk_ids[-1]

    # Verify the client was called the expected number of times
    assert mock_bazel_runner_client.get_runs.call_count == 3


def test_last_runs_iterable_empty_first_chunk(mock_bazel_runner_client):
    """Test LastRuns Iterable with an empty first chunk."""
    run_id = uuid.uuid4()
    chunk_size = 3

    # Mock an empty response
    mock_bazel_runner_client.get_runs.return_value = []

    # Initialize LastRuns
    last_runs = LastRuns(mock_bazel_runner_client, run_id, chunk_size)

    # Use the iter method to get an iterable
    iterable = last_runs.iter()

    # Collect all items from the iterable (should be empty)
    items = list(iterable)

    # Verify we got no items
    assert len(items) == 0

    # Verify the client was called only once
    mock_bazel_runner_client.get_runs.assert_called_once()
