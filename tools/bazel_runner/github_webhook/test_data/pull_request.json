{"expected": {"pullRequest": {"action": "edited", "number": 3866, "head": {"ref": "vzhao-01-16-dev-deploy-proj1024", "commit": "f57668a017a5705dc3e20abff343b5619632fdab", "repoOwner": "augmentcode", "repoName": "augment"}, "base": {"ref": "vzhao-01-16-embedder-cudagraph", "commit": "04212a30fb9565ff65f8c72febb00ae789069006", "repoOwner": "augmentcode", "repoName": "augment"}, "author": {"login": "zyzzhaoyuzhe"}}}, "data": {"action": "edited", "number": 3866, "pull_request": {"url": "https://api.github.com/repos/augmentcode/augment/pulls/3866", "id": 1704692182, "node_id": "PR_kwDOHnlRLM5lm4nW", "html_url": "https://github.com/augmentcode/augment/pull/3866", "diff_url": "https://github.com/augmentcode/augment/pull/3866.diff", "patch_url": "https://github.com/augmentcode/augment/pull/3866.patch", "issue_url": "https://api.github.com/repos/augmentcode/augment/issues/3866", "number": 3866, "state": "open", "locked": false, "title": "[Embedder] Creates configs for starethanol6_16_1_proj1024", "user": {"login": "zyzzhaoyuzhe", "id": 5614195, "node_id": "MDQ6VXNlcjU2MTQxOTU=", "avatar_url": "https://avatars.githubusercontent.com/u/5614195?v=4", "gravatar_id": "", "url": "https://api.github.com/users/zyzzhaoyuzhe", "html_url": "https://github.com/zyzzhaoyuzhe", "followers_url": "https://api.github.com/users/zyzzhaoyuzhe/followers", "following_url": "https://api.github.com/users/zyzzhaoyuzhe/following{/other_user}", "gists_url": "https://api.github.com/users/zyzzhaoyuzhe/gists{/gist_id}", "starred_url": "https://api.github.com/users/zyzzhaoyuzhe/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/zyzzhaoyuzhe/subscriptions", "organizations_url": "https://api.github.com/users/zyzzhaoyuzhe/orgs", "repos_url": "https://api.github.com/users/zyzzhaoyuzhe/repos", "events_url": "https://api.github.com/users/zyzzhaoyuzhe/events{/privacy}", "received_events_url": "https://api.github.com/users/zyzzhaoyuzhe/received_events", "type": "User", "site_admin": false}, "body": "Tested via deployment.\r\n\r\n![image](https://github.com/augmentcode/augment/assets/5614195/af60d26b-d1ff-4639-99e2-17704264ba48)\r\n", "created_at": "2024-01-31T19:07:52Z", "updated_at": "2024-02-01T00:18:30Z", "closed_at": null, "merged_at": null, "merge_commit_sha": "dd991d285deb4dcc88ee751e3776c8bfb27e9af7", "assignee": null, "assignees": [], "requested_reviewers": [{"login": "Markus<PERSON><PERSON>", "id": 8495990, "node_id": "MDQ6VXNlcjg0OTU5OTA=", "avatar_url": "https://avatars.githubusercontent.com/u/8495990?v=4", "gravatar_id": "", "url": "https://api.github.com/users/MarkusRabe", "html_url": "https://github.com/MarkusRabe", "followers_url": "https://api.github.com/users/MarkusRabe/followers", "following_url": "https://api.github.com/users/MarkusRabe/following{/other_user}", "gists_url": "https://api.github.com/users/MarkusRabe/gists{/gist_id}", "starred_url": "https://api.github.com/users/MarkusRabe/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/MarkusRabe/subscriptions", "organizations_url": "https://api.github.com/users/MarkusRabe/orgs", "repos_url": "https://api.github.com/users/MarkusRabe/repos", "events_url": "https://api.github.com/users/MarkusRabe/events{/privacy}", "received_events_url": "https://api.github.com/users/MarkusRabe/received_events", "type": "User", "site_admin": false}], "requested_teams": [], "labels": [], "milestone": null, "draft": false, "commits_url": "https://api.github.com/repos/augmentcode/augment/pulls/3866/commits", "review_comments_url": "https://api.github.com/repos/augmentcode/augment/pulls/3866/comments", "review_comment_url": "https://api.github.com/repos/augmentcode/augment/pulls/comments{/number}", "comments_url": "https://api.github.com/repos/augmentcode/augment/issues/3866/comments", "statuses_url": "https://api.github.com/repos/augmentcode/augment/statuses/f57668a017a5705dc3e20abff343b5619632fdab", "head": {"label": "augmentcode:vzhao-01-16-dev-deploy-proj1024", "ref": "vzhao-01-16-dev-deploy-proj1024", "sha": "f57668a017a5705dc3e20abff343b5619632fdab", "user": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "repo": {"id": 511267116, "node_id": "R_kgDOHnlRLA", "name": "augment", "full_name": "augmentcode/augment", "private": true, "owner": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "html_url": "https://github.com/augmentcode/augment", "description": null, "fork": false, "url": "https://api.github.com/repos/augmentcode/augment", "forks_url": "https://api.github.com/repos/augmentcode/augment/forks", "keys_url": "https://api.github.com/repos/augmentcode/augment/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/augmentcode/augment/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/augmentcode/augment/teams", "hooks_url": "https://api.github.com/repos/augmentcode/augment/hooks", "issue_events_url": "https://api.github.com/repos/augmentcode/augment/issues/events{/number}", "events_url": "https://api.github.com/repos/augmentcode/augment/events", "assignees_url": "https://api.github.com/repos/augmentcode/augment/assignees{/user}", "branches_url": "https://api.github.com/repos/augmentcode/augment/branches{/branch}", "tags_url": "https://api.github.com/repos/augmentcode/augment/tags", "blobs_url": "https://api.github.com/repos/augmentcode/augment/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/augmentcode/augment/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/augmentcode/augment/git/refs{/sha}", "trees_url": "https://api.github.com/repos/augmentcode/augment/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/augmentcode/augment/statuses/{sha}", "languages_url": "https://api.github.com/repos/augmentcode/augment/languages", "stargazers_url": "https://api.github.com/repos/augmentcode/augment/stargazers", "contributors_url": "https://api.github.com/repos/augmentcode/augment/contributors", "subscribers_url": "https://api.github.com/repos/augmentcode/augment/subscribers", "subscription_url": "https://api.github.com/repos/augmentcode/augment/subscription", "commits_url": "https://api.github.com/repos/augmentcode/augment/commits{/sha}", "git_commits_url": "https://api.github.com/repos/augmentcode/augment/git/commits{/sha}", "comments_url": "https://api.github.com/repos/augmentcode/augment/comments{/number}", "issue_comment_url": "https://api.github.com/repos/augmentcode/augment/issues/comments{/number}", "contents_url": "https://api.github.com/repos/augmentcode/augment/contents/{+path}", "compare_url": "https://api.github.com/repos/augmentcode/augment/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/augmentcode/augment/merges", "archive_url": "https://api.github.com/repos/augmentcode/augment/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/augmentcode/augment/downloads", "issues_url": "https://api.github.com/repos/augmentcode/augment/issues{/number}", "pulls_url": "https://api.github.com/repos/augmentcode/augment/pulls{/number}", "milestones_url": "https://api.github.com/repos/augmentcode/augment/milestones{/number}", "notifications_url": "https://api.github.com/repos/augmentcode/augment/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/augmentcode/augment/labels{/name}", "releases_url": "https://api.github.com/repos/augmentcode/augment/releases{/id}", "deployments_url": "https://api.github.com/repos/augmentcode/augment/deployments", "created_at": "2022-07-06T19:28:06Z", "updated_at": "2024-01-17T16:28:32Z", "pushed_at": "2024-02-01T00:16:00Z", "git_url": "git://github.com/augmentcode/augment.git", "ssh_url": "**************:augmentcode/augment.git", "clone_url": "https://github.com/augmentcode/augment.git", "svn_url": "https://github.com/augmentcode/augment", "homepage": "augment-frontend.vercel.app", "size": 152736, "stargazers_count": 9, "watchers_count": 9, "language": "Jupyter Notebook", "has_issues": false, "has_projects": false, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 31, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 129, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": [], "visibility": "private", "forks": 31, "open_issues": 129, "watchers": 9, "default_branch": "main", "allow_squash_merge": true, "allow_merge_commit": false, "allow_rebase_merge": true, "allow_auto_merge": true, "delete_branch_on_merge": true, "allow_update_branch": true, "use_squash_pr_title_as_default": true, "squash_merge_commit_message": "PR_BODY", "squash_merge_commit_title": "PR_TITLE", "merge_commit_message": "PR_TITLE", "merge_commit_title": "MERGE_MESSAGE"}}, "base": {"label": "augmentcode:vzhao-01-16-embedder-cudagraph", "ref": "vzhao-01-16-embedder-cudagraph", "sha": "04212a30fb9565ff65f8c72febb00ae789069006", "user": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "repo": {"id": 511267116, "node_id": "R_kgDOHnlRLA", "name": "augment", "full_name": "augmentcode/augment", "private": true, "owner": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "html_url": "https://github.com/augmentcode/augment", "description": null, "fork": false, "url": "https://api.github.com/repos/augmentcode/augment", "forks_url": "https://api.github.com/repos/augmentcode/augment/forks", "keys_url": "https://api.github.com/repos/augmentcode/augment/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/augmentcode/augment/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/augmentcode/augment/teams", "hooks_url": "https://api.github.com/repos/augmentcode/augment/hooks", "issue_events_url": "https://api.github.com/repos/augmentcode/augment/issues/events{/number}", "events_url": "https://api.github.com/repos/augmentcode/augment/events", "assignees_url": "https://api.github.com/repos/augmentcode/augment/assignees{/user}", "branches_url": "https://api.github.com/repos/augmentcode/augment/branches{/branch}", "tags_url": "https://api.github.com/repos/augmentcode/augment/tags", "blobs_url": "https://api.github.com/repos/augmentcode/augment/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/augmentcode/augment/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/augmentcode/augment/git/refs{/sha}", "trees_url": "https://api.github.com/repos/augmentcode/augment/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/augmentcode/augment/statuses/{sha}", "languages_url": "https://api.github.com/repos/augmentcode/augment/languages", "stargazers_url": "https://api.github.com/repos/augmentcode/augment/stargazers", "contributors_url": "https://api.github.com/repos/augmentcode/augment/contributors", "subscribers_url": "https://api.github.com/repos/augmentcode/augment/subscribers", "subscription_url": "https://api.github.com/repos/augmentcode/augment/subscription", "commits_url": "https://api.github.com/repos/augmentcode/augment/commits{/sha}", "git_commits_url": "https://api.github.com/repos/augmentcode/augment/git/commits{/sha}", "comments_url": "https://api.github.com/repos/augmentcode/augment/comments{/number}", "issue_comment_url": "https://api.github.com/repos/augmentcode/augment/issues/comments{/number}", "contents_url": "https://api.github.com/repos/augmentcode/augment/contents/{+path}", "compare_url": "https://api.github.com/repos/augmentcode/augment/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/augmentcode/augment/merges", "archive_url": "https://api.github.com/repos/augmentcode/augment/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/augmentcode/augment/downloads", "issues_url": "https://api.github.com/repos/augmentcode/augment/issues{/number}", "pulls_url": "https://api.github.com/repos/augmentcode/augment/pulls{/number}", "milestones_url": "https://api.github.com/repos/augmentcode/augment/milestones{/number}", "notifications_url": "https://api.github.com/repos/augmentcode/augment/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/augmentcode/augment/labels{/name}", "releases_url": "https://api.github.com/repos/augmentcode/augment/releases{/id}", "deployments_url": "https://api.github.com/repos/augmentcode/augment/deployments", "created_at": "2022-07-06T19:28:06Z", "updated_at": "2024-01-17T16:28:32Z", "pushed_at": "2024-02-01T00:16:00Z", "git_url": "git://github.com/augmentcode/augment.git", "ssh_url": "**************:augmentcode/augment.git", "clone_url": "https://github.com/augmentcode/augment.git", "svn_url": "https://github.com/augmentcode/augment", "homepage": "augment-frontend.vercel.app", "size": 152736, "stargazers_count": 9, "watchers_count": 9, "language": "Jupyter Notebook", "has_issues": false, "has_projects": false, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 31, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 129, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": [], "visibility": "private", "forks": 31, "open_issues": 129, "watchers": 9, "default_branch": "main", "allow_squash_merge": true, "allow_merge_commit": false, "allow_rebase_merge": true, "allow_auto_merge": true, "delete_branch_on_merge": true, "allow_update_branch": true, "use_squash_pr_title_as_default": true, "squash_merge_commit_message": "PR_BODY", "squash_merge_commit_title": "PR_TITLE", "merge_commit_message": "PR_TITLE", "merge_commit_title": "MERGE_MESSAGE"}}, "_links": {"self": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/3866"}, "html": {"href": "https://github.com/augmentcode/augment/pull/3866"}, "issue": {"href": "https://api.github.com/repos/augmentcode/augment/issues/3866"}, "comments": {"href": "https://api.github.com/repos/augmentcode/augment/issues/3866/comments"}, "review_comments": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/3866/comments"}, "review_comment": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/comments{/number}"}, "commits": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/3866/commits"}, "statuses": {"href": "https://api.github.com/repos/augmentcode/augment/statuses/f57668a017a5705dc3e20abff343b5619632fdab"}}, "author_association": "CONTRIBUTOR", "auto_merge": null, "active_lock_reason": null, "merged": false, "mergeable": true, "rebaseable": true, "mergeable_state": "unstable", "merged_by": null, "comments": 1, "review_comments": 8, "maintainer_can_modify": false, "commits": 1, "additions": 133, "deletions": 4, "changed_files": 6}, "changes": {"body": {"from": ""}}, "repository": {"id": 511267116, "node_id": "R_kgDOHnlRLA", "name": "augment", "full_name": "augmentcode/augment", "private": true, "owner": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "html_url": "https://github.com/augmentcode/augment", "description": null, "fork": false, "url": "https://api.github.com/repos/augmentcode/augment", "forks_url": "https://api.github.com/repos/augmentcode/augment/forks", "keys_url": "https://api.github.com/repos/augmentcode/augment/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/augmentcode/augment/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/augmentcode/augment/teams", "hooks_url": "https://api.github.com/repos/augmentcode/augment/hooks", "issue_events_url": "https://api.github.com/repos/augmentcode/augment/issues/events{/number}", "events_url": "https://api.github.com/repos/augmentcode/augment/events", "assignees_url": "https://api.github.com/repos/augmentcode/augment/assignees{/user}", "branches_url": "https://api.github.com/repos/augmentcode/augment/branches{/branch}", "tags_url": "https://api.github.com/repos/augmentcode/augment/tags", "blobs_url": "https://api.github.com/repos/augmentcode/augment/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/augmentcode/augment/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/augmentcode/augment/git/refs{/sha}", "trees_url": "https://api.github.com/repos/augmentcode/augment/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/augmentcode/augment/statuses/{sha}", "languages_url": "https://api.github.com/repos/augmentcode/augment/languages", "stargazers_url": "https://api.github.com/repos/augmentcode/augment/stargazers", "contributors_url": "https://api.github.com/repos/augmentcode/augment/contributors", "subscribers_url": "https://api.github.com/repos/augmentcode/augment/subscribers", "subscription_url": "https://api.github.com/repos/augmentcode/augment/subscription", "commits_url": "https://api.github.com/repos/augmentcode/augment/commits{/sha}", "git_commits_url": "https://api.github.com/repos/augmentcode/augment/git/commits{/sha}", "comments_url": "https://api.github.com/repos/augmentcode/augment/comments{/number}", "issue_comment_url": "https://api.github.com/repos/augmentcode/augment/issues/comments{/number}", "contents_url": "https://api.github.com/repos/augmentcode/augment/contents/{+path}", "compare_url": "https://api.github.com/repos/augmentcode/augment/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/augmentcode/augment/merges", "archive_url": "https://api.github.com/repos/augmentcode/augment/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/augmentcode/augment/downloads", "issues_url": "https://api.github.com/repos/augmentcode/augment/issues{/number}", "pulls_url": "https://api.github.com/repos/augmentcode/augment/pulls{/number}", "milestones_url": "https://api.github.com/repos/augmentcode/augment/milestones{/number}", "notifications_url": "https://api.github.com/repos/augmentcode/augment/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/augmentcode/augment/labels{/name}", "releases_url": "https://api.github.com/repos/augmentcode/augment/releases{/id}", "deployments_url": "https://api.github.com/repos/augmentcode/augment/deployments", "created_at": "2022-07-06T19:28:06Z", "updated_at": "2024-01-17T16:28:32Z", "pushed_at": "2024-02-01T00:16:00Z", "git_url": "git://github.com/augmentcode/augment.git", "ssh_url": "**************:augmentcode/augment.git", "clone_url": "https://github.com/augmentcode/augment.git", "svn_url": "https://github.com/augmentcode/augment", "homepage": "augment-frontend.vercel.app", "size": 152736, "stargazers_count": 9, "watchers_count": 9, "language": "Jupyter Notebook", "has_issues": false, "has_projects": false, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 31, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 129, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": [], "visibility": "private", "forks": 31, "open_issues": 129, "watchers": 9, "default_branch": "main", "custom_properties": {}}, "organization": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "url": "https://api.github.com/orgs/augmentcode", "repos_url": "https://api.github.com/orgs/augmentcode/repos", "events_url": "https://api.github.com/orgs/augmentcode/events", "hooks_url": "https://api.github.com/orgs/augmentcode/hooks", "issues_url": "https://api.github.com/orgs/augmentcode/issues", "members_url": "https://api.github.com/orgs/augmentcode/members{/member}", "public_members_url": "https://api.github.com/orgs/augmentcode/public_members{/member}", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "description": ""}, "sender": {"login": "zyzzhaoyuzhe", "id": 5614195, "node_id": "MDQ6VXNlcjU2MTQxOTU=", "avatar_url": "https://avatars.githubusercontent.com/u/5614195?v=4", "gravatar_id": "", "url": "https://api.github.com/users/zyzzhaoyuzhe", "html_url": "https://github.com/zyzzhaoyuzhe", "followers_url": "https://api.github.com/users/zyzzhaoyuzhe/followers", "following_url": "https://api.github.com/users/zyzzhaoyuzhe/following{/other_user}", "gists_url": "https://api.github.com/users/zyzzhaoyuzhe/gists{/gist_id}", "starred_url": "https://api.github.com/users/zyzzhaoyuzhe/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/zyzzhaoyuzhe/subscriptions", "organizations_url": "https://api.github.com/users/zyzzhaoyuzhe/orgs", "repos_url": "https://api.github.com/users/zyzzhaoyuzhe/repos", "events_url": "https://api.github.com/users/zyzzhaoyuzhe/events{/privacy}", "received_events_url": "https://api.github.com/users/zyzzhaoyuzhe/received_events", "type": "User", "site_admin": false}}}