{
  deployment: [
    {
      name: 'trivy-mirror',
      kubecfg: {
        target: '//tools/k8s/trivy_mirror:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
            env: 'PROD',
            namespace: 'devtools',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['surbhi', 'dirk'],
          slack_channel: '#services',
        },
      },
    },
    {
      name: 'trivy-mirror-monitoring',
      kubecfg: {
        target: '//tools/k8s/trivy_mirror:kubecfg_monitoring',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['surbhi', 'dirk'],
          slack_channel: '#services',
        },
      },
    },
  ],
}
