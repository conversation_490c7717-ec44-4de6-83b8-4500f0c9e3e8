// Create k8s objects for bigtable backup
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(cloud, env, namespace, namespace_config)
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);
  local projectId = cloudInfo[cloud].projectId;

  local serviceAccount = gcpLib.createServiceAccount(
    app='backup-bigtable',
    env=env,
    namespace=namespace,
    cloud=cloud,
    iam=true
  );
  local ram_limit_gb = 1;
  local cpu_limit = 0.01;
  local config = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: 'backup-bigtable-config',
      namespace: namespace,
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
      labels: {
        app: 'backup-bigtable',
      },
    },
    data: {
      'config.json': std.manifestJson({
        project: cloudInfo[cloud].projectId,
        instance: {
          GCP_US_CENTRAL1_PROD: if env == 'PROD' then 'bigtable-central' else 'bigtable-central-staging',
          GCP_US_CENTRAL1_DEV: 'bigtable-central-dev',
          GCP_EU_WEST4_PROD: if env == 'PROD' then 'bigtable-eu-w4-central' else 'bigtable-eu-w4-central-staging',
        }[cloud],
        targetClusterId: {
          GCP_US_CENTRAL1_PROD: if env == 'PROD' then 'bigtable-central' else 'bigtable-central-staging',
          GCP_US_CENTRAL1_DEV: 'bigtable-central-dev',
          GCP_EU_WEST4_PROD: if env == 'PROD' then 'bigtable-central' else 'bigtable-central-staging',
        }[cloud],
        extraInstances: [],
        // Say startEarlyMinutes is 30 minutes and the period
        // in the backup policy is every 6 hours. Say 5.5 hours
        // have elapsed since the previous backup began. The next
        // backup will start despite 6 hours not having elapsed.
        //
        // Needed because k8s jobs can take a range of time to
        // get scheduled and start.
        startEarlyMinutes: 30,
        tableGroupBackupPolicies: [
          {
            // Do not backup dev-* or test-* tables
            tableNameExcludeRegex: '^(dev|test)-',
            tableNameRegex: '.*-(auth-central|genie-main$|github|settings|share)',
            // Consider the cronjob schedule when setting the periods
            policies: [
              {
                periodHours: 672,
                retentionHours: 800,
              },
              {
                periodHours: 168,
                retentionHours: 672,
              },
              {
                periodHours: 24,
                retentionHours: 168,
              },
              {
                periodHours: 6,
                retentionHours: 24,
              },
            ],
          },
          {
            // Backup the central content manager table to safeguard against
            // anything tenant-gc may do. This has its own policy so it can use
            // a shorter retention period (1d and 7d). This table is huge and we
            // don't want to pay too much if those backups need to maintain
            // separate storage.
            tableNameRegex: 'central.*-content-manager',
            policies: [
              {
                periodHours: 24,
                retentionHours: 168,
              },
              {
                periodHours: 6,
                retentionHours: 24,
              },
            ],
          },
        ],
      }),
    },
  };
  local container =
    {
      name: 'backup-bigtable',
      target: {
        name: '//tools/backup/bigtable:image',
        dst: 'backup-bigtable-image',
      },
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
      ],
      args: [
        '--config',
        '/config/config.json',
      ],
      env: [
        {
          name: 'POD_NAME',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.name',
            },
          },
        },
        {
          name: 'POD_NAMESPACE',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.namespace',
            },
          },
        },
      ],
      resources: {
        limits: {
          cpu: cpu_limit,
          memory: '%sGi' % ram_limit_gb,
        },
      },
    };
  local bigTableAccessGrant = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: 'backup-bigtable-access-policy',
      namespace: namespace,
      labels: {
        app: 'backup-bigtable',
      },
    },
    spec: {
      resourceRef: {
        kind: 'Project',
        external: 'project/%s' % projectId,
      },
      bindings: [
        {
          role: 'roles/bigtable.reader',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
        {
          role: 'projects/%s/roles/augment.bigtable.backup' % projectId,
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ],
    },
  };
  local pod =
    {
      restartPolicy: 'Never',
      containers: [
        container,
      ],
      tolerations: tolerations,
      affinity: affinity,
      securityContext: {
        // bazel cannot be run as root
        runAsUser: 1000,
        fsGroup: 1000,
        fsGroupChangePolicy: 'OnRootMismatch',
      },
      serviceAccountName: 'backup-bigtable-sa',
      volumes: [
        {
          name: 'config',
          configMap: {
            name: 'backup-bigtable-config',
          },
        },
      ],
    };
  local cronjob =
    {
      apiVersion: 'batch/v1',
      kind: 'CronJob',
      metadata: {
        name: 'backup-bigtable',
        namespace: namespace,
        labels: {
          app: 'backup-bigtable',
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        schedule: '0 0,6,12,18 * * *',  // see activeDeadlineSeconds below too
        // Don't start backups that were skipped when this job was suspended. Set to 2 minutes
        // to give the scheduler some slack.
        startingDeadlineSeconds: 120,
        successfulJobsHistoryLimit: 16,
        failedJobsHistoryLimit: 4,
        concurrencyPolicy: 'Forbid',
        timeZone: 'America/Los_Angeles',
        jobTemplate: {
          metadata: {
            labels: {
              app: 'backup-bigtable',
            },
          },
          spec: {
            activeDeadlineSeconds: 4 * 60 * 60,  // 4 hours
            // disable backoff
            backoffLimit: 0,
            template: {
              metadata: {
                labels: {
                  app: 'backup-bigtable',
                },
              },
              spec: pod,
            },
          },
        },
      },
    };
  lib.flatten([
    config,
    bigTableAccessGrant,
    serviceAccount.objects,
    cronjob,
  ])
