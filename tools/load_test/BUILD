load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_proto_library")
load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")
load("//tools/bzl:python.bzl", "py_proto_library")

go_library(
    name = "ioutils",
    srcs = ["ioutils.go"],
    importpath = "github.com/augmentcode/augment/tools/load_test/ioutils",
    visibility = ["//tools/load_test:__subpackages__"],
    deps = [
        "@com_github_googleapis_gax_go_v2//:gax-go",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_storage//:storage",
    ],
)

go_library(
    name = "blob_setup",
    srcs = ["blob_setup.go"],
    importpath = "github.com/augmentcode/augment/tools/load_test/blob_setup",
    visibility = ["//tools/load_test:__subpackages__"],
    deps = [
        ":ioutils",
        "//base/augment_client/golang:client_go",
        "//services/api_proxy:public_api_go_proto",
        "@com_github_google_uuid//:uuid",
        "@com_github_rs_zerolog//log",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_x_sync//errgroup",
    ],
)

go_library(
    name = "load_test_lib",
    srcs = ["main.go"],
    data = glob([
        "configs/*",
        "configs/users/*",
    ]),
    importpath = "github.com/augmentcode/augment/tools/load_test",
    visibility = ["//visibility:private"],
    deps = [
        ":blob_setup",
        ":ioutils",
        ":load_test_go_proto",
        "//base/augment_client/golang:client_go",
        "//services/api_proxy:public_api_go_proto",
        "//tools/load_test/telemetry",
        "@com_github_google_uuid//:uuid",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@google_jsonnet_go//:go_default_library",
        "@io_opentelemetry_go_otel//semconv/v1.24.0:v1_24_0",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
    ],
)

go_binary(
    name = "load_test",
    embed = [":load_test_lib"],
    visibility = ["//visibility:public"],
)

proto_library(
    name = "load_test_proto",
    srcs = ["load_test.proto"],
    visibility = ["//visibility:public"],
    deps = [
        "//services/api_proxy:public_api_proto",
    ],
)

go_proto_library(
    name = "load_test_go_proto",
    importpath = "github.com/augmentcode/augment/tools/load_test/proto",
    proto = ":load_test_proto",
    visibility = ["//visibility:private"],
    deps = [
        "//services/api_proxy:public_api_go_proto",
    ],
)

py_proto_library(
    name = "load_test_py_proto",
    protos = [":load_test_proto"],
    visibility = ["//tools/load_test:__subpackages__"],
)
