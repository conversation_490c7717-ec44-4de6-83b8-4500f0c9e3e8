"""Python language rules."""

load(
    "@aspect_rules_py//py:defs.bzl",
    aspect_py_binary = "py_binary",
    aspect_py_library = "py_library",
    aspect_py_test = "py_test",
)
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", pytest_test_impl = "pytest_test")
load("//tools/bzl/lint:defs.bzl", "lint_test")
load("@rules_proto_grpc_python//:defs.bzl", "python_grpc_compile", "python_proto_compile")
load("//tools/bzl:mypy.bzl", "grpc_mypy_compile", "proto_mypy_compile")
load("@aspect_bazel_lib//lib:tar.bzl", "mtree_mutate", "mtree_spec", "tar")
load("//tools/bzl:image.bzl", "image")

# Grants visibility to the proto typestubs tool.
TYPESTUB_VISIBILITY = "//tools/generate_proto_typestubs:__pkg__"
TYPESTUB_TAG = "gen_proto_typestub"

def extend_visibility(visibility = None):
    """Extend the visibility to typestub tool.

    Args:
        visibility: The original visibility of the target.

    Returns:
        The visibility of the target extended to include the typestub tool.
    """
    if visibility == None:
        visibility = [TYPESTUB_VISIBILITY]
    elif "//visibility:public" in visibility or "//visibility:private" in visibility:
        pass
    else:
        # Create a mutable copy.
        visibility = visibility[:]
        visibility.append(TYPESTUB_VISIBILITY)
    return visibility

def py_binary(name, srcs, deps = [], data = [], pyright = True, ruff = True, pyright_extra_args = {}, bandit = True, **kwargs):
    """ Rule for Python binaries.

    See https://docs.aspect.build/rules/aspect_rules_py/docs/py_binary/ for usage

    The additional parameter lint can be used to disable linting on the target.

    See https://www.notion.so/Linting-59ee6591aea2427f9bb07fe1ca1ea615?pvs=4 for more information about linting.
    """
    if len(srcs) > 1 and "main" not in kwargs:
        # Actually multiple sources work and the first is chosen as the main
        # file by default, but this can easily lead to configuration errors, so
        # require the main file configuration to be explicit if there are
        # multiple source
        fail("py_binary with multiple source files must specify 'main' file")

    aspect_py_binary(name = name, srcs = srcs, deps = deps, data = data, **kwargs)

    lint_test(
        name + ".lint",
        srcs = srcs,
        deps = deps,
        data = data,
        pyright = pyright,
        bandit = bandit,
        ruff = ruff,
        pyright_extra_args = pyright_extra_args,
    )

def py_library(name, srcs, deps = [], data = [], pyright = True, ruff = True, pyright_extra_args = {}, bandit = True, **kwargs):
    """ Rule for Python libraries.

    See https://docs.aspect.build/rules/aspect_rules_py/docs/py_library/ for usage

    The additional parameter lint can be used to disable linting on the target.

    See https://www.notion.so/Linting-59ee6591aea2427f9bb07fe1ca1ea615?pvs=4 for more information about linting.
    """
    aspect_py_library(name = name, srcs = srcs, deps = deps, data = data, **kwargs)

    lint_test(
        name + ".lint",
        srcs = srcs,
        deps = deps,
        data = data,
        pyright = pyright,
        bandit = bandit,
        ruff = ruff,
        pyright_extra_args = pyright_extra_args,
    )

def py_test(name, srcs, deps = [], data = [], **kwargs):
    """ Rule for Python-based tests.

    For most tests, the pytest_based `pytest_test` should be preferred

    See https://docs.aspect.build/rules/aspect_rules_py/docs/py_test/ for usage
    """
    aspect_py_test(name = name, srcs = srcs, deps = deps, data = data, **kwargs)

# re-export
pytest_test = pytest_test_impl

def py_grpc_library(
        name,
        protos,
        visibility = None,
        output_mode = "NO_PREFIX_FLAT",
        deps = [],
        verbose = 0,
        **kwargs):
    """Rule for generated GRPC python libraries.

    Do not use "@rules_proto_grpc//python:defs.bzl's python_grpc_library
    as it is not using the right py_library implementation

    Usage

        proto_library(
            name = "my_proto",
            srcs = ["my.proto"],
        )

        py_grpc_library(
            name = "my_py_grpc",
            protos = [":my_proto"],
        )

    """

    python_grpc_compile(
        name = name + "_src",
        output_mode = output_mode,
        protos = protos,
        verbose = verbose,
    )

    proto_mypy_compile(
        name = name + "_mypy",
        protos = protos,
        output_mode = output_mode,
        verbose = verbose,
    )

    grpc_mypy_compile(
        name = name + "_grpc_mypy",
        protos = protos,
        output_mode = output_mode,
        verbose = verbose,
    )

    py_library(
        name = name,
        srcs = [
            name + "_src",
            name + "_mypy",
            name + "_grpc_mypy",
        ],
        ruff = False,
        pyright = False,
        bandit = False,
        visibility = extend_visibility(visibility),
        deps = [
            requirement("grpcio"),
            requirement("protobuf"),
        ] + deps,
        tags = [TYPESTUB_TAG],
        **kwargs
    )

def py_proto_library(
        name,
        protos,
        visibility = None,
        output_mode = "NO_PREFIX_FLAT",
        deps = [],
        verbose = 0,
        imports = [],
        **kwargs):
    """Rule for generated protobuf python libraries.

    Do not use "@rules_proto_grpc//python:defs.bzl's python_proto_library
    as it is not using the right py_library implementation

    Usage

        proto_library(
            name = "my_proto",
            srcs = ["my.proto"],
        )

        py_proto_library(
            name = "my_py_proto",
            protos = [":my_proto"],
        )

    """

    python_proto_compile(
        name = name + "_src",
        output_mode = output_mode,
        protos = protos,
        verbose = verbose,
    )

    proto_mypy_compile(
        name = name + "_mypy",
        protos = protos,
        output_mode = output_mode,
        verbose = verbose,
    )

    py_library(
        name = name,
        srcs = [
            name + "_src",
            name + "_mypy",
        ],
        ruff = False,
        pyright = False,
        bandit = False,
        imports = imports,
        visibility = extend_visibility(visibility),
        deps = [
            requirement("grpcio"),
            requirement("protobuf"),
        ] + deps,
        tags = [TYPESTUB_TAG],
        **kwargs
    )

# match *only* external repositories that have the string "python"
# e.g. this will match
#   `/hello_world/hello_world_bin.runfiles/rules_python~0.21.0~python~python3_9_aarch64-unknown-linux-gnu/bin/python3`
# but not match
#   `/hello_world/hello_world_bin.runfiles/_main/python_app`
PY_INTERPRETER_REGEX = "\\.runfiles.rules_python~~python~python"

# match *only* external pip like repositories that contain the string "site-packages" and
# the libarchive and solib directories (used by compiled pytorch C++ extensions).
SITE_PACKAGES_REGEX = "site-packages|python~pip|libtorch.*archive|_solib"

# ignore these files.
DEFAULT_IGNORE_PATTERN = "^$$"

def py_layers(name, binary, ignore_pattern = DEFAULT_IGNORE_PATTERN):
    """Create three layers for a py_binary target: interpreter, third-party dependencies, and application code.

    This allows a container image to have smaller uploads, since the application layer usually changes more
    than the other two.

    Args:
        name: prefix for generated targets, to ensure they are unique within the package
        binary: a py_binary target
    Returns:
        a list of labels for the layers, which are tar files
    """

    # Produce layers in this order, as the app changes most often
    layers = ["interpreter", "packages", "app"]

    # Produce the manifest for a tar file of our py_binary, but don't tar it up yet, so we can split
    # into fine-grained layers for better docker performance.
    mtree_spec(
        name = name + ".base.mf",
        srcs = [binary],
    )

    mtree_mutate(
        name = name + ".mf",
        mtree = name + ".base.mf",
        owner = "1000",
    )

    native.genrule(
        name = name + ".interpreter_tar_manifest",
        srcs = [name + ".mf"],
        outs = [name + ".interpreter_tar_manifest.spec"],
        cmd = "grep -E '{}' $< | grep -vP '{}' >$@".format(PY_INTERPRETER_REGEX, ignore_pattern),
    )

    native.genrule(
        name = name + ".packages_tar_manifest",
        srcs = [name + ".mf"],
        outs = [name + ".packages_tar_manifest.spec"],
        cmd = "grep -E '{}' $< | grep -vE '{}' | grep -vP '{}' >$@".format(SITE_PACKAGES_REGEX, PY_INTERPRETER_REGEX, ignore_pattern),
    )

    # Any lines that didn't match one of the two grep above
    native.genrule(
        name = name + ".app_tar_manifest",
        srcs = [name + ".mf"],
        outs = [name + ".app_tar_manifest.spec"],
        cmd = "grep -vE '{}' $< | grep -Pv '{}' | grep -vP '{}' >$@".format(SITE_PACKAGES_REGEX, PY_INTERPRETER_REGEX, ignore_pattern),
    )

    result = []
    for layer in layers:
        layer_target = "{}.{}_layer".format(name, layer)
        result.append(layer_target)
        tar(
            name = layer_target,
            srcs = [binary],
            mtree = "{}.{}_tar_manifest".format(name, layer),
        )

    return result

def py_oci_image(
        name,
        binary,
        package_name,
        entrypoint = None,
        cmd = [],
        workdir = None,
        base = "//tools/docker:ubuntu_base_image",
        tars = [],
        **kwargs):
    """Wrapper around oci_image that splits the py_binary into layers.

    The layers are:
    - interpreter
    - packages
    - app

    Args:
        name: The name of the oci_image target.
        binary: The py_binary target to use as the application.
        package_name: The name of the package that the binary is in, used to determine the entrypoint. Use package_name()
        entrypoint: The entrypoint for the container. Optional, if not specified, will be set automatically.
        cmd: The command to run in the container. Optional, if not specified, empty
        workdir: The workdir for the container. Optional, if not specified, will be set automatically.
        base: The base image to use. Optional, if not specified, will be set automatically.
        tars: A list of tar files to include in the image.
        **kwargs: All other arguments to oci_image

    """

    b = (binary[1:] if binary.startswith(":") else binary)
    if not entrypoint:
        if binary.startswith("//"):
            fail("need to specify entrypoint")
        entrypoint = "/" + package_name + "/" + b
    if not workdir:
        workdir = "/" + package_name + "/" + b + ".runfiles/_main"

    image(
        name = name,
        base = base,
        workdir = workdir,
        entrypoint = [entrypoint],
        cmd = cmd,
        tars = tars + py_layers(name, binary),
        **kwargs
    )

def py_torch_oci_image(
        name,
        binary,
        package_name,
        entrypoint = None,
        cmd = [],
        workdir = None,
        base = "//tools/docker:cuda_base_image",
        tars = [],
        **kwargs):
    """Wrapper around oci_image that splits the py_binary into layers.

    The layers are:
    - interpreter
    - packages
    - app

    This varianat contains special handling for torch/cuda:
    It is based on a different base image, it will fiter out
    redunant files so that the large cuda/torch libraries are not included multiple times

    Args:
        name: The name of the oci_image target.
        binary: The py_binary target to use as the application.
        package_name: The name of the package that the binary is in, used to determine the entrypoint. Use package_name()
        entrypoint: The entrypoint for the container. Optional, if not specified, will be set automatically.
        cmd: The command to run in the container. Optional, if not specified, empty
        workdir: The workdir for the container. Optional, if not specified, will be set automatically.
        base: The base image to use. Optional, if not specified, will be set automatically.
        tars: A list of tar files to include in the image.
        **kwargs: All other arguments to oci_image

    """

    b = (binary[1:] if binary.startswith(":") else binary)
    if not entrypoint:
        if binary.startswith("//"):
            fail("need to specify entrypoint")
        entrypoint = "/" + package_name + "/" + b
    if not workdir:
        workdir = "/" + package_name + "/" + b + ".runfiles/_main"

    image(
        name = name,
        base = base,
        workdir = workdir,
        entrypoint = [entrypoint],
        cmd = cmd,
        tars = tars + py_layers(
            name,
            binary,
            ignore_pattern = "libtorch.*archive|solib(?!.*(nvjitlink|cusolver|cusparse))",
        ),
        # override the LD_LIBRARY_PATH to include torch, torch (in torch 2.1) will include all torch libraries
        # as well as most the cuda libraries
        # TODO but we need cusolver and nvjitlink
        env = {
            "LD_LIBRARY_PATH": ":".join(
                [
                    workdir + "/../rules_python~~pip~python_pip_311_torch/site-packages/torch/lib",
                    workdir + "/../rules_python~~pip~python_pip_311_nvidia_cuda_nvrtc_cu12/site-packages/nvidia/cuda_nvrtc/lib",
                    workdir + "/../rules_python~~pip~python_pip_311_nvidia_nvtx_cu12/site-packages/nvidia/nvtx/lib",
                    "/usr/local/nvidia/lib64/",
                ],
            ),
        },
        **kwargs
    )
