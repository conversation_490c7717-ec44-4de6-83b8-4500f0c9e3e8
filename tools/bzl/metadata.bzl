load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "JsonnetLibraryInfo",
)

def _setup_deps(deps):
    """Collects source files and import flags of transitive dependencies.

    Args:
      deps: List of deps labels from ctx.attr.deps.

    Returns:
      Returns a struct containing the following fields:
        transitive_sources: List of Files containing sources of transitive
            dependencies
    """
    transitive_sources = []
    for dep in deps:
        transitive_sources.append(dep[JsonnetLibraryInfo].transitive_jsonnet_files)

    return struct(
        transitive_sources = depset(transitive = transitive_sources, order = "postorder"),
    )

def _shell_quote(s):
    return "'" + s.replace("'", "'\\''") + "'"

def _metadata_test_impl(ctx):
    """Implementation of the metadata test rule."""
    depinfo = _setup_deps(ctx.attr.deps)

    kubecfg_command = " ".join(
        [ctx.executable.metadata_test_util.short_path],
    )

    args = []

    args.append("--max-tasks-per-target")
    args.append(str(ctx.attr.max_tasks_per_target))

    args.append("--file")
    args.append(ctx.file.src.short_path)

    command = """#!/bin/bash
set -eu
 # --- begin runfiles.bash initialization v3 ---
       # Copy-pasted from the Bazel Bash runfiles library v3.
       set -uo pipefail; set +e; f=bazel_tools/tools/bash/runfiles/runfiles.bash
       source "${RUNFILES_DIR:-/dev/null}/$f" 2>/dev/null || \
         source "$(grep -sm1 "^$f " "${RUNFILES_MANIFEST_FILE:-/dev/null}" | cut -f2- -d' ')" 2>/dev/null || \
         source "$0.runfiles/$f" 2>/dev/null || \
         source "$(grep -sm1 "^$f " "$0.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null || \
         source "$(grep -sm1 "^$f " "$0.exe.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null || \
         { echo>&2 "ERROR: cannot find $f"; exit 1; }; f=; set -e
  # --- end runfiles.bash initialization v3 ---

""".split("\n") + [
        '%s %s "$@"' % (kubecfg_command, " ".join([_shell_quote(arg) for arg in args])),
    ]
    output_sh = ctx.actions.declare_file("%s.sh" % ctx.label.name)

    ctx.actions.write(
        output = output_sh,
        content = "\n".join(command),
        is_executable = True,
    )

    transitive_data = depset(transitive = [ctx.attr.src.data_runfiles.files] +
                                          [ctx.attr.metadata_test_util[DefaultInfo].files])
    inputs = (
        [ctx.executable.metadata_test_util] + [ctx.file.src] +
        transitive_data.to_list() +
        depinfo.transitive_sources.to_list() +
        ctx.attr.metadata_test_util[DefaultInfo].data_runfiles.files.to_list() +
        ctx.files.data
    )

    return [DefaultInfo(
        runfiles = ctx.runfiles(
            files = inputs,
            transitive_files = transitive_data,
            collect_data = True,
        ),
        executable = output_sh,
    )]

_metadata_test_attrs = {
    "metadata_test_util": attr.label(
        default = Label("//tools/deploy_runner:metadata_test_util"),
        cfg = "target",
        executable = True,
    ),
    "src": attr.label(
        doc = "A metadata file.",
        allow_single_file = True,
    ),
    "deps": attr.label_list(
        doc = "List of kubecfg targets.",
        allow_files = False,
    ),
    "data": attr.label_list(
        allow_files = True,
    ),
    "max_tasks_per_target": attr.int(
        default = 1000,
        doc = "Maximum number of tasks to test per deploy target",
    ),
}

metadata_test = rule(
    _metadata_test_impl,
    test = True,
    attrs = dict(_metadata_test_attrs.items()),
    doc = """metadata_test

    Tests a METADATA file, e.g. if all targets are valid.

    Args:
        src: A METADATA file to be tested
        deps: Dependencies, e.g. kubecfg targets
    """,
)
