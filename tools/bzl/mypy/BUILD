load("//tools/bzl:python.bzl", "py_binary")
load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto_grpc//:defs.bzl", "proto_plugin")

py_binary(
    name = "mypy_proto_gen",
    srcs = ["mypy_proto_gen.py"],
    deps = [requirement("mypy-protobuf")],
)

py_binary(
    name = "mypy_grpc_gen",
    srcs = ["mypy_grpc_gen.py"],
    deps = [requirement("mypy-protobuf")],
)

proto_plugin(
    name = "proto_mypy_plugin",
    outputs = ["{protopath}_pb2.pyi"],  # {protopath} gets replaced with relative path to each .proto file
    tool = ":mypy_proto_gen",
    visibility = ["//visibility:public"],
)

proto_plugin(
    name = "grpc_mypy_plugin",
    outputs = ["{protopath}_pb2_grpc.pyi"],  # {protopath} gets replaced with relative path to each .proto file
    tool = ":mypy_grpc_gen",
    visibility = ["//visibility:public"],
)
