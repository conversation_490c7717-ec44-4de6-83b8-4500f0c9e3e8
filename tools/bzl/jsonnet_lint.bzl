load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "JsonnetLibraryInfo",
)

_JSONNET_FILETYPE = [
    ".jsonnet",
    ".libsonnet",
    ".json",
]

def _setup_deps(deps):
    """Collects source files and import flags of transitive dependencies.

    Args:
      deps: List of deps labels from ctx.attr.deps.

    Returns:
      Returns a struct containing the following fields:
        transitive_sources: List of Files containing sources of transitive
            dependencies
    """
    transitive_sources = []
    for dep in deps:
        transitive_sources.append(dep[JsonnetLibraryInfo].transitive_jsonnet_files)

    return struct(
        transitive_sources = depset(transitive = transitive_sources, order = "postorder"),
    )

def _jsonnet_lint_test_impl(ctx):
    """Implementation of the jsonnet_lint_test rule."""
    command = """#!/bin/bash
JSONNET_LINT=%s
SRCS=(%s)
RESULT=0
for SRC in $SRCS ; do
    $JSONNET_LINT -J . $SRC
    if [ $? -ne 0 ]; then
        echo "Error: jsonnnet_lint failed on $SRC"
        # if BUILD_WORKSPACE_DIRECTORY  is set
        # then we can run the command to fix it
        if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
            echo "Run 'bazel run //tools:jsonnet_lint -- -i $BUILD_WORKSPACE_DIRECTORY/$SRC'"
        else
            echo "Run 'bazel run //tools:jsonnet_lint -- -i \\$AUGMENT_SRC/$SRC'"
        fi
        echo
        RESULT=1
    fi
done
exit $RESULT
""" % (ctx.executable.jsonnetlint.short_path, " ".join([f.path for f in ctx.files.srcs]))

    ctx.actions.write(
        output = ctx.outputs.executable,
        content = command,
        is_executable = True,
    )

    depinfo = _setup_deps(ctx.attr.deps)
    transitive_data = depset(
        transitive = [dep.data_runfiles.files for dep in ctx.attr.deps] +
                     [dep.data_runfiles.files for dep in ctx.attr.data],
    )
    inputs = (
        ctx.files.srcs + [ctx.executable.jsonnetlint] +
        transitive_data.to_list() +
        depinfo.transitive_sources.to_list()
    )

    return [DefaultInfo(
        files = depset([ctx.outputs.executable]),
        runfiles = ctx.runfiles(
            files = inputs,
            transitive_files = transitive_data,
            collect_data = True,
        ),
        executable = ctx.outputs.executable,
    )]

_jsonnetlint_attrs = {
    "jsonnetlint": attr.label(
        doc = "A jsonnetlint binary",
        default = Label("//tools:jsonnet_lint"),
        cfg = "exec",
        executable = True,
        allow_single_file = True,
    ),
    "srcs": attr.label_list(
        doc = "The `.jsonnet` files to test.",
        allow_files = _JSONNET_FILETYPE,
        allow_empty = False,
    ),
    "deps": attr.label_list(
        doc = "The dependencies of the jsonnet files.",
        allow_files = False,
    ),
    "data": attr.label_list(
        allow_files = True,
    ),
}

jsonnet_lint_test = rule(
    _jsonnet_lint_test_impl,
    attrs = _jsonnetlint_attrs,
    executable = True,
    test = True,
    doc = """\
Lints jsonnet files

Example:

  `config/BUILD`:

  ```python
  load(
      "@tools/bzl//jsonnet_lint.bzl",
      "jsonnet_lint_test")

  jsonnet_lint_test(
      name = "test_config_lint_test",
      srcs = ["test_config.jsonnet"],
  )
  ```

""",
)
