"""Test Fill-In-the-Middle.

pytest research/fim/tests/test_pretraining_fim.py
"""

import re

import numpy as np
from megatron.tokenizer.tokenizer import get_tokenizer

from research.fim.pretraining_fim import PreTrainingFIM

RANDOM_SEED = 42


def _undo_fim(fim_segment, fim_spm_rate):
    if fim_spm_rate == 0:
        m = re.match(
            r"^<fim_prefix>(.*)<fim_suffix>(.*)<fim_middle>(.*)$",
            fim_segment,
            re.MULTILINE | re.DOTALL,
        )
        assert m, f"fim_segment:\n{fim_segment}"
        prefix = m.group(1)
        suffix = m.group(2)
        middle = m.group(3)
        reconstructed = prefix + middle + suffix
        return reconstructed, prefix, middle, suffix
    else:
        assert fim_spm_rate == 1
        # Variant 2 from the FIM paper
        m = re.match(
            r"^<fim_prefix><fim_suffix>(.*)<fim_middle>(.*)$",
            fim_segment,
            re.MULTILINE | re.DOTALL,
        )
        assert m, f"fim_segment:\n{fim_segment}"
        suffix = m.group(1)
        prefix = m.group(2)
        middle = ""
        reconstructed = prefix + middle + suffix
        return reconstructed, prefix, middle, suffix


def test_fim_single_segment():
    """Test FIM with a sample that contains a single document."""
    tokenizer = get_tokenizer("StarCoderTokenizer")

    sample = """Lorem Ipsum is simply dummy text of the printing and typesetting
        industry. Lorem Ipsum has been the industry's standard dummy text ever
        since the 1500s, when an unknown printer took a galley of type and
        scrambled it to make a type specimen book. It has survived not only five
        centuries, but also the leap into electronic typesetting, remaining
        essentially unchanged. It was popularised in the 1960s with the release
        of Letraset sheets containing Lorem Ipsum passages, and more recently
        with desktop publishing software like Aldus PageMaker including versions
        of Lorem Ipsum."""

    sample_tokens = np.array(tokenizer.tokenize(sample))

    result_tokens = PreTrainingFIM(
        fim_rate=1, fim_spm_rate=0, tokenizer=tokenizer
    ).apply_on_samples([sample_tokens], seed=RANDOM_SEED, truncate_or_pad=False)[0]

    result_text = tokenizer.detokenize(result_tokens.tolist())

    _, prefix, middle, suffix = _undo_fim(result_text, fim_spm_rate=0)
    # They don't exactly match because the sample may have dropped some tokens
    assert sample.startswith(prefix)
    assert sample.endswith(suffix)
    assert middle in sample


def test_fim():
    """Test FIM with samples that contain multiple documents."""
    tokenizer = get_tokenizer("StarCoderTokenizer")

    segments = [
        """Lorem Ipsum is simply dummy text of the printing and typesetting
        industry. Lorem Ipsum has been the industry's standard dummy text ever
        since the 1500s, when an unknown printer took a galley of type and
        scrambled it to make a type specimen book. It has survived not only five
        centuries, but also the leap into electronic typesetting, remaining
        essentially unchanged. It was popularised in the 1960s with the release
        of Letraset sheets containing Lorem Ipsum passages, and more recently
        with desktop publishing software like Aldus PageMaker including versions
        of Lorem Ipsum.""",
        """Contrary to popular belief, Lorem Ipsum is not simply random text. It
        has roots in a piece of classical Latin literature from 45 BC, making it
        over 2000 years old. Richard McClintock, a Latin professor at
        Hampden-Sydney College in Virginia, looked up one of the more obscure
        Latin words, consectetur, from a Lorem Ipsum passage, and going through
        the cites of the word in classical literature, discovered the
        undoubtable source. Lorem Ipsum comes from sections 1.10.32 and 1.10.33
        of "de Finibus Bonorum et Malorum" (The Extremes of Good and Evil) by
        Cicero, written in 45 BC. This book is a treatise on the theory of
        ethics, very popular during the Renaissance. The first line of Lorem
        Ipsum, "Lorem ipsum dolor sit amet..", comes from a line in section
        1.10.32.""",
        """It is a long established fact that a reader will be distracted by the
        readable content of a page when looking at its layout. The point of
        using Lorem Ipsum is that it has a more-or-less normal distribution of
        letters, as opposed to using 'Content here, content here', making it
        look like readable English. Many desktop publishing packages and web
        page editors now use Lorem Ipsum as their default model text, and a
        search for 'lorem ipsum' will uncover many web sites still in their
        infancy. Various versions have evolved over the years, sometimes by
        accident, sometimes on purpose (injected humour and the like).""",
    ]

    sample_text = "<|endoftext|>".join(segments)
    sample_tokens = np.array(tokenizer.tokenize(sample_text))

    # test don't fim
    result_tokens = PreTrainingFIM(
        fim_rate=0, fim_spm_rate=0, tokenizer=tokenizer
    ).apply_on_samples([sample_tokens], seed=RANDOM_SEED, truncate_or_pad=False)[0]

    assert (result_tokens == sample_tokens).all()

    # test fim, both PSM and SPM
    for fim_spm_rate in [0, 1]:
        result_tokens = PreTrainingFIM(
            fim_rate=1, fim_spm_rate=fim_spm_rate, tokenizer=tokenizer
        ).apply_on_samples([sample_tokens], seed=RANDOM_SEED, truncate_or_pad=False)[0]

        result_text = tokenizer.detokenize(result_tokens.tolist())
        result_segments = result_text.split("<|endoftext|>")

        # All segments except for the last one should remain intact.
        # The last segment may gain/lose some tokens, so test it separately.
        for i, (segment, result_segment) in enumerate(zip(segments, result_segments)):
            if i == len(segments) - 1:
                break
            reconstructed, _, _, _ = _undo_fim(result_segment, fim_spm_rate)
            assert reconstructed == segment, (
                f"\nsegment:\n{segment}\nresult\n:{result_segment}\n"
                f"reconstructed:\n{reconstructed}"
            )

        last_segment = segments[-1]
        last_result_segment = result_segments[-1]
        reconstructed, prefix, middle, suffix = _undo_fim(
            last_result_segment, fim_spm_rate
        )
        assert last_segment.startswith(prefix)
        assert last_segment.endswith(suffix)
        assert middle in last_segment
