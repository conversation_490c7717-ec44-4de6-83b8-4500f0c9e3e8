"""Interface to BM25 Retriever."""

import copy
import logging
from typing import AbstractSet, Iterable, Optional

from research.core.model_input import ModelInput  # noqa: E402
from research.retrieval.chunking_functions import get_chunk_id
from research.retrieval.types import (
    Chunk,
    Document,
    DocumentId,
    DocumentIndex,
    RetrievalScore,
)
from research.static_analysis.parsing import LineMap

from . import ingest


class Bm25DocumentIndex(DocumentIndex):
    """The public interface for BM25-based retrieval."""

    def __init__(self, tokenizer, max_chunk_lines: int):
        self.doc_store: dict[DocumentId, Document] = {}
        self.token = tokenizer
        self.max_chunk_lines = max_chunk_lines
        self.index = None
        self.skip_indexed = True

    def _rebuild(self):
        repo = {doc_id: doc for doc_id, doc in self.doc_store.items()}
        if not repo:
            self.index = None
        else:
            self.index = ingest.bm25_index_from_text(
                repo, self.token, self.max_chunk_lines
            )

    def add_docs(self, docs: Iterable[Document]) -> None:
        """Add documents to index, and use name to reference."""
        to_add = list(docs)
        if self.skip_indexed:
            to_add = [d for d in to_add if d.id not in self.doc_store]
        if not to_add:
            return

        for doc in to_add:
            if not isinstance(doc.id, str):
                raise ValueError(f"Document IDs must be strings (doc.id={doc.id})")
            self.doc_store[doc.id] = copy.deepcopy(doc)
        self._rebuild()

    def remove_docs(self, ids: Iterable[DocumentId]) -> list[Document]:
        """Remove documents by name."""
        to_remove = [self.doc_store.pop(x) for x in ids if x in self.doc_store]
        self._rebuild()
        return to_remove

    def remove_all_docs(self) -> list[Document]:
        """Remove all documents."""
        to_remove = list(self.doc_store.values())
        self.doc_store = {}
        self._rebuild()
        return to_remove

    def get_docs(self, ids: Iterable[DocumentId]) -> list[Document]:
        """Return the list of documents."""
        # TODO(rich): What if documents are missing?
        return [self.doc_store[x] for x in ids if x in self.doc_store]

    def get_doc_ids(self) -> AbstractSet[DocumentId]:
        return self.doc_store.keys()

    def query(
        self,
        model_input: ModelInput,
        query_meta: Optional[dict] = None,
        doc_ids: Optional[Iterable[DocumentId]] = None,
        top_k: Optional[int] = None,
    ) -> tuple[list[Chunk], list[RetrievalScore]]:
        """Return list of document chunks, ordered from high to low score."""
        assert self.index is not None, "Index is not built."
        assert doc_ids is None, "Filtering on document names is not yet supported."
        if model_input.suffix:
            logging.info(
                "Bm25DocumentIndex does not support suffixes, but one was passed."
            )

        # convert query to tokens
        query_tokens = self.token.tokenize(model_input.prefix)

        doc_iter = self.index.query(query_tokens)
        docs = [x for x in doc_iter]
        chunks = []
        scores = []

        if top_k is None:
            top_k = len(docs)

        for bm25doc, score in docs[:top_k]:
            span = bm25doc.span
            doc_id = self.token.detokenize(bm25doc.path_tokens)
            text = self.token.detokenize(bm25doc.text_tokens)
            parent_doc = self.doc_store[doc_id]

            char_offset = span.start
            length_in_chars = len(span)
            line_map = LineMap(parent_doc.text)
            line_offset = line_map.get_line_number(char_offset)
            length_in_lines = (
                line_map.get_line_number(char_offset + length_in_chars) - line_offset
            )
            chunk = Chunk(
                id=get_chunk_id(parent_doc.id, text),
                text=text,
                parent_doc=parent_doc,
                char_offset=char_offset,
                length=length_in_chars,
                line_offset=line_offset,
                length_in_lines=length_in_lines,
                meta={},
            )

            chunks.append(chunk)
            scores.append(score)

        return chunks, scores

    def is_in(self, ids: Iterable[DocumentId]) -> list[bool]:
        """Return list of bools indicating whether the document is present or not."""
        return [x in self.doc_store for x in ids]

    @classmethod
    def from_yaml_config(cls, config: dict) -> "Bm25DocumentIndex":
        """Instantiates an object from the config."""
        raise NotImplementedError("Implement the method when needed.")
