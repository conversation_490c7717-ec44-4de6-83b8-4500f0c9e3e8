"""Jaccard document similarity."""

from collections.abc import Sequence
from typing import cast

Tokens = Sequence[int]


def _get_ngrams(tokens: Tokens, n: int):
    """Returns n-grams for the given list of tokens.

    Returns an empty list if the n-gram size exceeds the list of tokens.
    """
    return [tuple(tokens[i : i + n]) for i in range(len(tokens) - n + 1)]


def _jaccard_sim(x, y):
    """Jaccard similarity between two sets.

    Assumes at least one set is non-empty.
    """
    x = set(x)
    y = set(y)
    intersection = x.intersection(y)
    # using len is more performant than taking the union
    return float(len(intersection)) / (len(x) + len(y) - len(intersection))


def _jaccard_sim_ngram(x: Tokens, y: Tokens, ngram_size: int):
    """Compute the jaccard similarity on ngrams.

    If neither list is long enough to generate the desired ngrams,
    test the input for equality. Empty sets will evaluate as equal.
    """
    xn = _get_ngrams(x, ngram_size)
    yn = _get_ngrams(y, ngram_size)
    return _jaccard_sim(xn, yn) if xn or yn else float(x == y)


def _overlapping_window(tokens: Tokens, window_size: int):
    """Returns overlapping (sliding) window over the tokens."""
    if window_size == 0:
        return [tuple(tokens)]
    elif window_size == 1:
        return [
            tuple(tokens[i : i + window_size])
            for i in range(0, len(tokens), window_size)
        ]

    assert window_size > 1
    a = [tuple(tokens[i : i + window_size]) for i in range(0, len(tokens), window_size)]
    b = [
        tuple(tokens[i : i + window_size])
        for i in range(window_size // 2, len(tokens), window_size)
    ]

    result = cast(list[Tokens], [None] * (len(a) + len(b)))
    result[::2] = a
    result[1::2] = b

    return result


def _overlapping_window_jaccard_sim_ngram(
    chunk: Tokens, prompt: Tokens, ngram_size: int
) -> float:
    return max(
        _jaccard_sim_ngram(x, chunk, ngram_size)
        for x in _overlapping_window(prompt, len(chunk))
    )


def document_similarity(document: Tokens, prompt: Tokens, ngram_size: int) -> float:
    """Public entrypoint for jaccard document similarity calculation."""
    return _overlapping_window_jaccard_sim_ngram(document, prompt, ngram_size)
