"""Command-line utility for testing bm-25 retrieval."""

from typing import Iterator, Optional

from research.retrieval.jaccard_sim import document_similarity
from research.retrieval.legacy_retrieval_implementations.bm25.bm25 import (
    DocumentIndex,
    Tokens,
)


class RetrievalIter:
    """Iterator over documents retrieved from the given DocumentIndex and prompt.

    Each document is returned as a 2-element tuple consisting of
        [path_tokens, body_tokens].

    Documents are returned with descending scores, so the first returned
    document is the most relevant one.

    top_k and max_tokens can be used to control how many documents are retrieved:
    * top_k limits the number of documents retrieved
    * max_tokens limits the number of tokens retrieved
    You can specify either, both, or neither. In the latter case, all documents
    in the corpus will be retrieved. Documents are retrieved in their entirety.
    If retrieving a given document would exceed max_tokens, the document is
    discarded and the iteration terminates. It does not return partial documents
    to fill out max_tokens.

    Results are filtered using Jaccard similarity with the given ngram_size if
    similarity_cutoff is not None.
    """

    def __init__(
        self,
        retrieval_index: DocumentIndex,
        prompt_tokens: Tokens,
        top_k: Optional[int] = None,
        max_tokens: Optional[int] = None,
        per_document_overhead: int = 0,
        similarity_cutoff: Optional[float] = None,
        ngram_size: Optional[int] = None,
    ):
        if similarity_cutoff:
            assert ngram_size is not None

        self.prompt_tokens = prompt_tokens
        self.it = retrieval_index.query(prompt_tokens)
        self.top_k = top_k
        self.max_tokens = max_tokens
        self.per_document_overhead = per_document_overhead
        self.similarity_cutoff = similarity_cutoff
        self.ngram_size = ngram_size

        self.documents_emitted = 0
        self.tokens_emitted = 0

    def __iter__(self):
        return self

    def __next__(self):
        if self.top_k is not None and self.documents_emitted == self.top_k:
            raise StopIteration
        while True:
            document, _score = next(self.it)
            if self.similarity_cutoff is not None:
                assert self.ngram_size is not None
                sim = document_similarity(
                    document.text_tokens[:-1], self.prompt_tokens, self.ngram_size
                )
                if sim > self.similarity_cutoff:
                    continue

            document_tokens = (
                len(document.path_tokens)
                + len(document.text_tokens)
                + self.per_document_overhead
            )
            if (
                self.max_tokens is not None
                and self.tokens_emitted + document_tokens > self.max_tokens
            ):
                raise StopIteration

            self.documents_emitted += 1
            self.tokens_emitted += document_tokens
            return document


def _detokenize_iterator(iterator, tokenizer) -> Iterator[tuple[str, str]]:
    """Wrapper around RetrievalIter that detokenizes results."""
    for doc in iterator:
        yield (
            tokenizer.detokenize(doc.path_tokens),
            tokenizer.detokenize(doc.text_tokens),
        )


def retrieve_tokens(
    retrieval_index: DocumentIndex,
    prompt_tokens: Tokens,
    top_k: Optional[int] = None,
    similarity_cutoff: Optional[float] = 0.5,
    ngram_size: int = 20,
):
    """Return an iterator over documents retrieved from the given index and prompt.

    Documents are returned with descending scores, so the first returned
    document is the most relevant one.

    Parameters are interpreted as by RetrievalIter.
    """
    return RetrievalIter(
        retrieval_index, prompt_tokens, top_k, None, 0, similarity_cutoff, ngram_size
    )


def retrieve_text(
    retrieval_index: DocumentIndex,
    prompt: str,
    tokenizer,
    top_k: Optional[int] = None,
    similarity_cutoff: Optional[float] = None,
    ngram_size: int = 20,
) -> Iterator[tuple[str, str]]:
    """Convenience wrapper around retrieve_tokens.

    Documents are returned with descending scores, so the first returned
    document is the most relevant one.

    Operates on txt instead of tokens.
    """
    prompt_tokens = tokenizer.tokenize(prompt)
    return _detokenize_iterator(
        retrieve_tokens(
            retrieval_index, prompt_tokens, top_k, similarity_cutoff, ngram_size
        ),
        tokenizer,
    )


def augment_prompt_tokens(
    retrieval_index: DocumentIndex,
    prompt_tokens: Tokens,
    end_of_path: Tokens,
    end_of_document: Tokens,
    max_tokens: int,
    similarity_cutoff: float = 0.5,
    ngram_size: int = 20,
) -> Tokens:
    """Return the given prompt augmented with retrieved documents.

    max_tokens, similarity_cutoff, and ngram_size are interpreted as by RetrievalIter.
    The end-of-path token sequence, if present, is placed after each document's path
    (if present). The end-of-document token sequence, if present, is placed at the end
    of each document, so the format of the returned prompt is:

        [<path><end_of_path><document_body><end_of_document>]*<prompt>

    Documents are prepended to the path in reverse order of relevance, so the most
    relevant document is closest to the original prompt.
    """
    max_retrieval_tokens = (
        None if max_tokens is None else max(max_tokens - len(prompt_tokens), 0)
    )
    per_document_overhead = len(end_of_path) + len(end_of_document)
    it = RetrievalIter(
        retrieval_index,
        prompt_tokens,
        None,
        max_retrieval_tokens,
        per_document_overhead,
        similarity_cutoff,
        ngram_size,
    )

    retrieved_chunks = [*it]
    prompt_prefix = []
    for doc in reversed(retrieved_chunks):
        if doc.path_tokens:
            prompt_prefix += doc.path_tokens
            prompt_prefix += end_of_path
        prompt_prefix += doc.text_tokens
        prompt_prefix += end_of_document

    return prompt_prefix + prompt_tokens


def prepare_retrieval_augmented_prompt(
    retrieval_index: DocumentIndex,
    prompt: str,
    max_tokens: int,
    tokenizer,
    end_of_path: str = "\n",
    end_of_document: str = "<|ret-endofdoc|>",
    similarity_cutoff: float = 0.5,
    ngram_size: int = 20,
) -> str:
    """Convenience wrapper around augment_prompt_tokens."""
    prompt_tokens = tokenizer.tokenize(prompt)
    end_of_path_tokens = tokenizer.tokenize(end_of_path)
    end_of_document_tokens = tokenizer.tokenize(end_of_document)

    augmented_prompt_tokens = augment_prompt_tokens(
        retrieval_index,
        prompt_tokens,
        end_of_path_tokens,
        end_of_document_tokens,
        max_tokens,
        similarity_cutoff,
        ngram_size,
    )

    return tokenizer.detokenize(augmented_prompt_tokens)
