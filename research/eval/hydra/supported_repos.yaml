# This is where we enforce the policy that containers used by hydra must be
# tagged (we don't use latest) and that configuration must be checked into
# git. To add a new repo/container or to upgrade one, please update this file.

# Dictionary of supported repositories and the required container:tag

# repo-coder related repositories

# 2: Ensure that ${PYTHONPATH} always has `pytest` and `/code/src`.
amazon-science/patchcore-inspection: 3

carperai/trlx: 2
deepmind/tracr: 1
facebookresearch/omnivore: 1
google/lightweight_mmm: 1
leopard-ai/betty: 1
lucidrains/imagen-pytorch: 2
maxhumber/redframes: 1
seata/seata: 1
airbnb/epoxy: 1.0
google/pyglove: 1.0
pydantic/pydantic: 1.0
