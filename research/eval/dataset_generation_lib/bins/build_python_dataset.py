"""Build the Python-based Datasets for HydraTask."""
from __future__ import annotations

import argparse
import collections
import copy
import logging
import pathlib
import random
from typing import Any

import torch

from research.core import utils_for_log, utils_for_str
from research.core.types import Document
from research.eval.dataset_generation_lib import (
    finegrained_patch_generators as patch_generate_lib,
)
from research.models.all_models import get_formatter_by_model_name
from research.static_analysis.experimental_parsing import (
    SimpleNode,
    StructuralAnalysis,
    dict_to_parsedfile,
    parsedfile_to_dict,
)
from research.static_analysis.usage_analysis import ParsedFile

StructuralType = StructuralAnalysis.XType


def show_status_fn(
    nodes: list[SimpleNode],
    doc_by_filename: dict[str, ParsedFile],
    logger: logging.Logger,
):
    """Show the structure info of nodes.."""
    counters = {"structure": collections.Counter(), "pipeline": collections.Counter()}
    for node in nodes:
        code = doc_by_filename[node.filename].code
        crange = node.crange
        prefix = code[: crange.start]
        middle = code[crange.start : crange.stop]
        suffix = code[crange.stop :]
        xtypes = StructuralAnalysis.get_type(prefix, suffix=suffix, middle=middle)
        for xtype in xtypes:
            counters["structure"][xtype.name] += 1
        pipeline = "-".join(node.pipeline_history)
        counters["pipeline"][pipeline] += 1
    for key, value in counters["structure"].items():
        logger.info(f"{key:40s} : {value:3d} / {len(nodes)}")
    logger.info("-" * 64)
    for key, value in counters["pipeline"].items():
        logger.info(f"{key:40s} : {value:3d} / {len(nodes)}")
    logger.info("-" * 64)


def merge_nodes(
    nodes: list[SimpleNode], nodes_v2: list[SimpleNode]
) -> list[SimpleNode]:
    """Merge two nodes's information."""
    final_nodes = []
    for idx, (node, new_node) in enumerate(zip(nodes, nodes_v2)):
        if node.filename != new_node.filename:
            raise ValueError(
                f"The {idx}-th node has different filename {node} vs. {new_node}."
            )
        if node.crange != new_node.crange:
            raise ValueError(
                f"The {idx}-th node has different range {node} vs. {new_node}."
            )
        final_node = copy.deepcopy(node)
        diff_keys = set(new_node.extra.keys()) - set(final_node.extra.keys())
        for xkey in diff_keys:
            final_node.extra[xkey] = new_node.extra[xkey]
        final_nodes.append(final_node)
    return final_nodes


def _chech_eq(list_a, list_b):
    assert len(list_a) == len(list_b)
    for x, y in zip(list_a, list_b):
        assert type(x) == type(y)
        if isinstance(x, (str, int, float)):
            assert x == y


def load_data_from_dir(
    input_dir: pathlib.Path, logger: logging.Logger
) -> tuple[list[SimpleNode], dict[str, ParsedFile], list[Document], str]:
    """Load and merge data from sub directories."""
    # Load the hydra results
    raw_data_path_v1 = input_dir / "hydra-default" / "all-results.torch"
    raw_data_v1 = torch.load(raw_data_path_v1)
    nodes_v1, repo_v1 = raw_data_v1["nodes"], raw_data_v1["repo_for_system"]
    doc_by_filename_v1: dict[str, ParsedFile] = {
        key: dict_to_parsedfile(docdict)
        for key, docdict in raw_data_v1["docdict_by_filename"].items()
    }
    logger.info(f"Data keys from {raw_data_path_v1}: {raw_data_v1.keys()}")
    logger.info(f"Loaded {len(nodes_v1)} nodes and {len(repo_v1)} documents.")

    # Load the system default
    raw_data_path_v2 = input_dir / "system-default" / "trim-sc-3b" / "all-results.torch"
    raw_data_v2 = torch.load(raw_data_path_v2)
    nodes_v2, repo_v2 = raw_data_v2["nodes"], raw_data_v2["repo_for_system"]
    doc_by_filename_v2: dict[str, ParsedFile] = {
        key: dict_to_parsedfile(docdict)
        for key, docdict in raw_data_v2["docdict_by_filename"].items()
    }
    _chech_eq(repo_v1, repo_v2)
    _chech_eq(list(doc_by_filename_v1.keys()), list(doc_by_filename_v2.keys()))
    nodes = merge_nodes(nodes_v1, nodes_v2)
    return nodes, doc_by_filename_v1, repo_v1, raw_data_v1["image_name"]


def _get_pass_result_from_dict(xdict: dict) -> bool | None:
    if "result_str" not in xdict:
        return False
    result_str = xdict["result_str"]
    if result_str == "":
        return None
    parts = utils_for_str.extract_colored_parts(result_str)
    if len(parts) == 1:
        part = parts[0]
    else:
        return None
    if part == "PASSED":
        return True
    elif part in "FAILED":
        return False
    elif part in ("TIMEOUT", "OTHERS"):
        return None
    else:
        raise ValueError(f"{part}  <== {result_str}")


def dedupe_nodes_fn(
    nodes: list[SimpleNode],
    doc_by_filename: dict[str, ParsedFile],
    logger: logging.Logger,
) -> list[SimpleNode]:
    node_by_unique_key: dict[Any, SimpleNode] = dict()
    for node in nodes:
        key = (node.filename, node.crange.start, node.crange.stop)
        node_by_unique_key[key] = node
    unique_nodes: list[SimpleNode] = list(node_by_unique_key.values())

    node_by_unique_key: dict[Any, SimpleNode] = dict()
    for node in unique_nodes:
        key_code = doc_by_filename[node.filename].code[
            node.crange.start : node.crange.stop
        ]
        node_by_unique_key[
            patch_generate_lib._normalize_python_code(  # pylint: disable=protected-access
                key_code, remove_comment=False
            )
        ] = node
    unique_nodes_v2: list[SimpleNode] = list(node_by_unique_key.values())
    logger.info(
        f"After dedupe, we reduced {len(nodes)} nodes to {len(unique_nodes)} unique nodes to {len(unique_nodes_v2)} unique nodes"
    )
    return unique_nodes_v2


def filter_nodes_fn(nodes: list[SimpleNode], logger: logging.Logger):
    """Filter invalid nodes based on hydra and system outputs."""
    key_to_fail = "hydra-simple-patch_is_assert0=1"
    key_is_empty = "hydra-simple-patch_is_empty"
    key_to_pass = "hydra-simple-patch_to_pass"

    new_nodes, pass_by_3b, full_decorator, partial_match = [], 0, 0, 0
    for node in nodes:
        if not (
            (node.extra[key_to_fail]["status"] == "complete")
            and (node.extra[key_is_empty]["status"] == "complete")
            and (node.extra[key_to_pass]["status"] == "complete")
        ):
            continue
        if not _get_pass_result_from_dict(node.extra[key_to_pass]):
            continue
        if _get_pass_result_from_dict(node.extra[key_to_fail]):
            continue
        if _get_pass_result_from_dict(node.extra[key_is_empty]):
            continue
        if _get_pass_result_from_dict(node.extra["hydra-by-sys-trim-sc-3b"]):
            pass_by_3b += 1
            continue
        if node.extra["text"].strip().startswith("@"):
            full_decorator += 1
            continue
        if patch_generate_lib._normalize_python_code(  # pylint: disable=protected-access
            node.extra["sys-generation-by-trim-sc-3b"], remove_comment=False
        ).startswith(
            patch_generate_lib._normalize_python_code(  # pylint: disable=protected-access
                node.extra["text"], remove_comment=False
            )
        ):
            partial_match += 1
            continue
        new_nodes.append(node)
    logger.info(f"Removed {pass_by_3b} nodes because the 3B model passed.")
    # TODO(Xuanyi): smarter sampling over decorator.
    logger.info(f"Removed {full_decorator} nodes because it is the full decorator.")
    logger.info(
        f"Removed {partial_match} nodes because it partially matched with the ground truth."
    )
    logger.info(
        f"After filtering, we reduced from {len(nodes)} nodes to {len(new_nodes)} nodes."
    )
    return new_nodes


def balanced_subsample(
    nodes: list[SimpleNode],
    doc_by_filename: dict[str, ParsedFile],
    limit: int,
    logger: logging.Logger,
) -> list[SimpleNode]:
    """Balanced sub sampling over the entire nodes."""

    def _get_type(idx: int):
        crange = nodes[idx].crange
        code = doc_by_filename[nodes[idx].filename].code
        prefix = code[: crange.start]
        middle = code[crange.start : crange.stop]
        suffix = code[crange.stop :]
        return StructuralAnalysis.get_type(prefix, suffix=suffix, middle=middle)

    def _get_tokens(idx: int):
        extra = nodes[idx].extra
        if "text.#tokens" in extra:
            return extra["text.#tokens"]
        else:
            return -1

    def _get_lines(idx: int):
        extra = nodes[idx].extra
        if "text.#lines" in extra:
            return extra["text.#lines"]
        else:
            return -1

    all_indexes = list(range(len(nodes)))

    final_indexes = []
    conditions = [
        (
            "Indent & #lines > 1",
            lambda i: StructuralType.CompletionStartsWithIndentation in _get_type(i)
            and _get_lines(i) > 1,
            0.2,
        ),
        (
            "NewLine & #lines > 1",
            lambda i: StructuralType.CompletionStartsWithNewLine in _get_type(i)
            and _get_lines(i) > 1,
            0.1,
        ),
        (
            "Others & #lines > 1",
            lambda i: StructuralType.CompletionStartsOthers in _get_type(i)
            and _get_lines(i) > 1,
            0.3,
        ),
        (
            "sample_random_symbols",
            lambda i: nodes[i].pipeline_history[-1] == "sample_random_symbols",
            0.15,
        ),
        (
            "interface:class_head",
            lambda i: nodes[i].pipeline_history[0] == "interface:class_head",
            0.2,
        ),
        (
            "EndsNewLine & #lines > 1",
            lambda i: StructuralType.CompletionEndsBeforeNewLine in _get_type(i)
            and _get_lines(i) > 1,
            0.2,
        ),
        ("tokens > 128", lambda i: _get_tokens(i) > 128, 0.2),
        ("type.empty", lambda i: StructuralType.EmptyLike in _get_type(i), 0.1),
        ("type.multi-line", lambda i: StructuralType.MultiLines in _get_type(i), 0.2),
        ("type.single-line", lambda i: StructuralType.SingleLine in _get_type(i), 0.1),
        (
            "StartsWithIndent",
            lambda i: StructuralType.CompletionStartsWithIndentation in _get_type(i),
            0.2,
        ),
        (
            "StartsWithNewLine",
            lambda i: StructuralType.CompletionStartsWithNewLine in _get_type(i),
            0.1,
        ),
        (
            "EndsNewLine",
            lambda i: StructuralType.CompletionEndsBeforeNewLine in _get_type(i),
            0.2,
        ),
    ]
    for attr, lambda_fn, percentage in conditions:
        expected_num = int(limit * percentage)
        current_num = sum([lambda_fn(i) for i in final_indexes])
        missing_num = expected_num - current_num
        candidates = [i for i in all_indexes if lambda_fn(i) and i not in final_indexes]
        select_num = max(0, min(missing_num, len(candidates)))
        message = (
            f"{attr:25s} : [{len(final_indexes):4d}] : expect {expected_num:4d} nodes"
            f" and currently we have {current_num:4d} nodes over {len(final_indexes):4d} nodes,"
            f" and miss {missing_num:4d} nodes."
        )
        if select_num:
            selected_indexes = random.sample(candidates, select_num)
            message += f"\tsampled {len(selected_indexes):3d}/{select_num:3d} nodes"
            final_indexes.extend(selected_indexes)
        logger.info(message)
    final_nodes = []
    for index in set(final_indexes):
        final_nodes.append(nodes[index])
    return final_nodes


def main(args):
    """The real main function."""
    input_dir = pathlib.Path(args.input_dir)
    output_dir = pathlib.Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    log_filepath = output_dir / utils_for_str.sanitize_filename(
        pathlib.Path(__file__).stem + "-" + utils_for_log.time_string() + ".log"
    )
    logger = utils_for_log.create_logger(__file__, log_filepath)
    logger.info(f"Args: {args}")

    logger.info(f"input_dir: {input_dir}")
    logger.info(f"The output directory is : {output_dir}")
    logger.info(f"The output log file is : {log_filepath}")

    nodes, doc_by_filename, repo_for_system, image_name = load_data_from_dir(
        input_dir, logger=logger
    )

    # Apply filtering
    nodes_v1 = filter_nodes_fn(nodes, logger)
    logger.info("-" * 100)
    logger.info("After filtering :: ")
    show_status_fn(nodes_v1, doc_by_filename, logger)
    patch_generate_lib.show_statistics_of_simple_nodes(nodes_v1, logger)

    # Apply dedup
    logger.info("\n\n")
    nodes_v2 = dedupe_nodes_fn(nodes_v1, doc_by_filename, logger)
    logger.info("-" * 100)
    logger.info("After dedup :: ")
    show_status_fn(nodes_v2, doc_by_filename, logger)
    patch_generate_lib.show_statistics_of_simple_nodes(nodes_v2, logger)

    # Post check
    formatter = get_formatter_by_model_name("starcoderbase")
    for node in nodes_v2:
        code = doc_by_filename[node.filename].code
        prefix = code[: node.crange.start]
        suffix = code[node.crange.stop :]
        text = code[node.crange.start : node.crange.stop]
        token_length = len(formatter.tokenizer.tokenize(text))
        assert token_length == node.extra["text.#tokens"]
        assert text == node.extra["text"]
        types = StructuralAnalysis.get_type(prefix, suffix, middle=text)
        node.extra["last-5-lines.prefix"] = utils_for_str.get_last_n_lines(prefix, 5)
        node.extra["first-5-lines.suffix"] = utils_for_str.get_first_n_lines(suffix, 5)
        node.extra["xtypes"] = [x.name for x in types]

    # Apply balanced_subsample
    logger.info("\n\n")
    for limit in args.limit:
        logger.info(f"Re-balance sub-sample with limit={limit} :: ")
        final_nodes = balanced_subsample(nodes_v2, doc_by_filename, limit, logger)

        show_status_fn(final_nodes, doc_by_filename, logger)
        patch_generate_lib.show_statistics_of_simple_nodes(final_nodes, logger)

        output_file = output_dir / f"{limit:03d}-results.torch"
        torch.save(
            {
                "nodes": final_nodes,
                "docdict_by_filename": {
                    f: parsedfile_to_dict(doc) for f, doc in doc_by_filename.items()
                },
                "image_name": image_name,
                "repo_for_system": repo_for_system,
            },
            output_file,
        )
        cache_dir = output_dir / f"cache-total-num-is-{limit:03d}"
        cache_dir.mkdir(parents=True, exist_ok=True)
        for idx, node in enumerate(final_nodes):
            try:
                node.cache_nodes_to_dir(
                    cache_dir,
                    extrakey_by_name={
                        "Structural types": "xtypes",
                        "Prefix (last 5 lines)": "last-5-lines.prefix",
                        "Suffix (first 5 lines)": "first-5-lines.suffix",
                    },
                )
            except UnicodeEncodeError as e:
                logger.info(f"Skip cache the {idx}/{len(nodes)}-th node due to {e}")
        logger.info(f"Save the cache into {cache_dir}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.add_argument(
        "--input_dir",
        type=str,
        required=True,
        help="The input directory.",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="The output directory.",
    )
    parser.add_argument(
        "--limit",
        type=int,
        required=True,
        nargs="+",
        help="The maximum number of final nodes",
    )
    main(parser.parse_args())
