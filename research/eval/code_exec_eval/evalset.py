#!/usr/bin/env python3
"""
Utilities for working with evaluation sets.
"""

import base64
import enum
import sys
from argparse import ArgumentParser
from dataclasses import dataclass
from pathlib import Path
from random import randint

import pandas as pd
import yaml
from compress_pickle import loads as compressed_loads


class EvalFixTypes(enum.StrEnum):
    """The type of fix to apply"""

    SHA = "sha"
    PATCH = "patch"


DEFAULT_PATH: str = (
    "/mnt/efs/augment/user/colin/bugfix_localization_model/v11/stage5_with_logs"
)


class EvalSetGenerator:
    """Generate an EvalSet from a directory of parquet files."""

    eval_ratio: float = 0.08  # Ratio of files to evals, empirically determined
    max_file_chunks: int = 100

    def __init__(self, data_path: str | Path = DEFAULT_PATH):
        """
        Initialize the EvalSetGenerator.

        Args:
            data_path: The path to the directory containing parquet files.

        Returns:
            None
        """
        self.data_path = Path(data_path)

    def load_data(self, num_files: int) -> pd.DataFrame:
        """
        Load data from a directory containing parquet files.

        Args:
            num_files: The number of files to sample.

        Returns:
            A dataframe containing the data.
        """
        print(f"Loading {num_files} files from {self.data_path}")
        random_state = randint(0, 100000)

        files = list(self.data_path.glob("*.parquet"))
        sample_files = (
            pd.Series(files).sample(n=num_files, random_state=random_state).to_list()
        )
        return pd.concat([pd.read_parquet(f) for f in sample_files])

    def generate_set(
        self,
        eval_count: int,
        fix_type: EvalFixTypes = EvalFixTypes.PATCH,
        min_repo_size: int = 0,
        max_repo_size: int = 0,
    ) -> "EvalSet":
        """
        Generate an EvalSet with the given number of evals.

        Args:
            eval_count: The number of evals to generate.
            fix_type: The type of fix to apply.
            min_repo_size: The minimum number of files in a repo to include.
            max_repo_size: The maximum number of files in a repo to include.

        Returns:
            An EvalSet.
        """
        # Don't try to import from research if we're in the eval environment
        # pylint: disable=import-outside-toplevel
        from research.utils.repo_change_utils import patchset_from_repo_change

        print(f"Generating EvalSet with {eval_count} evals...")
        es: EvalSet = EvalSet()
        while len(es) < eval_count:
            print(f"EvalSet has {len(es)} evals, generating more...")
            remaining = eval_count - len(es)
            file_count = min(
                int(max(remaining * self.eval_ratio, 1)), self.max_file_chunks
            )
            df: pd.DataFrame = self.load_data(num_files=file_count)
            try:
                print("Processing problems...")
                df["problems"] = df["problems"].apply(
                    lambda x: compressed_loads(x, compression="gzip")
                )
                df = df.explode("problems").dropna()
                df["repo_name"] = df["repo_id"]
                print("Getting checkrun names...")
                df["checkrun_name"] = df["problems"].apply(
                    lambda x: x.logs_check_run_name
                )
                df = df.drop_duplicates(
                    subset=["repo_name", "checkrun_name"], keep="first"
                )

                df["file_count"] = df["problems"].apply(
                    lambda x: len(x.fixing_change.before_files)
                )
                max_str = max_repo_size if max_repo_size is not None else "unlimited"
                raw_len = len(df)
                if min_repo_size or max_repo_size:
                    print("Filtering repos by size...")
                if min_repo_size:
                    df: pd.DataFrame = pd.DataFrame(
                        df[df["file_count"] >= min_repo_size]
                    )
                if max_repo_size:
                    df: pd.DataFrame = pd.DataFrame(
                        df[df["file_count"] <= max_repo_size]
                    )
                if min_repo_size or max_repo_size:
                    filtered_len = len(df)
                    print(
                        f"Filtering repos with {min_repo_size} to {max_str} files; {raw_len} -> {filtered_len} remaining"
                    )

                print("Getting logs...")
                df["log"] = df["problems"].apply(lambda x: x.logs[0])
                df["sha"] = df["problems"].apply(lambda x: x.logs_sha)
                print("Generating diffs...")
                df["fixing_diff"] = df["problems"].apply(
                    lambda x: str(
                        patchset_from_repo_change(x.fixing_change, num_context_lines=3)
                    )
                )
                print("Getting fixing shas...")
                df["fixing_sha"] = df["problems"].apply(lambda x: x.passing_child.sha)
            except Exception as e:  # pylint: disable=broad-exception-caught
                # Very occasionally, the generation fails in the diff parser or elsewhere.
                # In this case, just skip it and try again with a new random sample.
                print(f"Failed to process problems, retrying: {e}")
                continue

            print(f"Generating evals from {len(df)} repos...")
            for _, row in df.iterrows():
                ei = {
                    "sha": row["sha"],  # type: ignore
                    "repo_id": row["repo_name"],  # type: ignore
                    "run_name": row["checkrun_name"],  # type: ignore
                }
                if fix_type == EvalFixTypes.SHA:
                    ei["fix_sha"] = row["fixing_sha"]
                elif fix_type == EvalFixTypes.PATCH:
                    ei["patch_text"] = base64.b64encode(
                        row["fixing_diff"].encode("utf-8")  # type: ignore
                    ).decode("utf-8")
                else:
                    raise NotImplementedError(f"Fix type {fix_type} not supported")
                try:
                    es.append(EvalItem(**ei))  # type: ignore
                    if len(es) >= eval_count:
                        break
                except Exception:  # pylint: disable=broad-exception-caught
                    # If we get a bad eval, just skip it
                    pass

        print(f"{len(es)} evals generated.")
        return es


@dataclass
class EvalItem:
    """An evaluation item.

    Exactly one of fix_sha, patch_text must be set.
    If patch_text is set, it is assumed to be a base64 encoded string.
    """

    sha: str
    repo_id: str
    run_name: str
    fix_sha: str | None = None
    patch_text: str | None = None  # TODO(marcmac) store this compressed internally?

    def __post_init__(self):
        """
        Validate that exactly one of fix_sha or patch_text is set.

        Args:
            None

        Returns:
            None
        """
        assert bool(self.fix_sha) ^ bool(
            self.patch_text
        ), "Exactly one of fix_sha, patch_text must be set."

    def __str__(self):
        """
        Return a string representation of the EvalItem.

        Args:
            None

        Returns:
            str: A string representation of the EvalItem.
        """
        fix = self.patch_text
        if not fix:
            fix = self.fix_sha[0:10]  # type: ignore
        else:
            fix = f"(len {len(fix)})"
        return f"{self.repo_id} {self.run_name} {self.sha[0:10]} {fix}"

    @property
    def fix_text(self):
        """
        Get the fix text, decoded from base64.

        Args:
            None

        Returns:
            str: The decoded fix text.
        """
        if self.patch_text:
            return base64.b64decode(self.patch_text.encode("utf-8")).decode("utf-8")
        return ""

    def item_key(self):
        """
        Get the item key (Repo, run name, sha[:10]).

        Args:
            None

        Returns:
            str: The item key.
        """
        return f"{self.repo_id} {self.run_name} {self.sha[:10]}"

    def to_dict(self):
        """
        Convert the EvalItem to a dict for serialization.

        Args:
            None

        Returns:
            dict: A dictionary representation of the EvalItem.
        """
        return {
            "sha": self.sha,
            "repo_id": self.repo_id,
            "run_name": self.run_name,
            "fix_sha": self.fix_sha,
            "patch_text": self.patch_text,
        }

    @classmethod
    def from_patch_file(cls, sha: str, repo_id: str, run_name: str, path: Path):
        """
        Create an EvalItem from a base64 encoded patch file.

        Args:
            sha: The SHA of the commit.
            repo_id: The repository ID.
            run_name: The name of the run.
            path: The path to the patch file.

        Returns:
            EvalItem: An EvalItem instance.
        """
        return cls(
            sha=sha, repo_id=repo_id, run_name=run_name, patch_text=path.read_text()
        )


class EvalSet:
    """A set of evaluation items."""

    def __init__(self, evals: list[EvalItem] | None = None):
        """
        Initialize an EvalSet.

        Args:
            evals: A list of EvalItems or None.

        Returns:
            None
        """
        if evals is None:
            evals = []
        self._pd = pd.DataFrame([e.to_dict() for e in evals])

    @property
    def pd(self):
        """
        Get the underlying pandas dataframe.

        Args:
            None

        Returns:
            pd.DataFrame: The underlying pandas dataframe.
        """
        return self._pd

    @pd.setter
    def pd(self, value):
        """
        Set the underlying pandas dataframe.

        Args:
            value: The new pandas dataframe.

        Returns:
            None
        """
        self._pd = value

    @property
    def evals(self):
        """
        Get the list of evals.

        Args:
            None

        Returns:
            list[EvalItem]: The list of EvalItems.
        """
        return [EvalItem(**row) for _, row in self._pd.iterrows()]  # type: ignore

    def eval_keys(self):
        """
        Get the list of eval keys.

        Args:
            None

        Returns:
            list[str]: The sorted list of eval keys.
        """
        return sorted([e.item_key() for e in self.evals])

    def eval(self, key: str):
        """
        Get an eval by key.

        Args:
            key: The key of the eval to retrieve.

        Returns:
            EvalItem: The EvalItem corresponding to the given key.

        Raises:
            ValueError: If the eval is not found for the given key.
        """
        try:
            return [e for e in self.evals if e.item_key() == key][0]
        except IndexError as exc:
            raise ValueError(f"Eval not found for key {key}") from exc

    def append(self, eval_item: EvalItem):
        """
        Append an eval item to the set.

        Args:
            eval_item: The EvalItem to append.

        Returns:
            None
        """
        self._pd = pd.concat(
            [self._pd, pd.DataFrame([eval_item.to_dict()])], ignore_index=True
        )

    def __len__(self):
        """
        Get the number of evals in the set.

        Args:
            None

        Returns:
            int: The number of evals in the set.
        """
        return len(self._pd)

    def to_yaml(self):
        """
        Convert the eval set to a YAML string.

        Args:
            None

        Returns:
            str: The YAML representation of the eval set.
        """
        yml = []
        for _, row in self._pd.iterrows():
            yml.append(row.to_dict())
        return yaml.safe_dump(yml)

    @staticmethod
    def parquet_path(path: Path):
        """
        Get the path to the parquet file.

        Args:
            path: The base path.

        Returns:
            Path: The path to the parquet file.
        """
        return path.with_suffix(".zstd.parquet")

    def write_parquet(self, path: Path) -> Path:
        """
        Write the eval set to a parquet file.

        Args:
            path: The path to write the parquet file.

        Returns:
            Path: The path to the written parquet file.
        """
        ppath = self.parquet_path(path)
        ppath.parent.mkdir(parents=True, exist_ok=True)
        self._pd.to_parquet(ppath, compression="zstd")
        return ppath

    @classmethod
    def from_parquet(cls, path: Path):
        """
        Create an EvalSet from a parquet file.

        Args:
            path: The path to the parquet file.

        Returns:
            EvalSet: An EvalSet instance.
        """
        c = cls()
        c.pd = pd.read_parquet(path)
        return c

    def __add__(self, other: "EvalSet"):
        """
        Add two EvalSets together.

        Args:
            other: The other EvalSet to add.

        Returns:
            EvalSet: A new EvalSet containing the combined evals.
        """
        es = EvalSet()
        es.pd = pd.concat([self.pd, other.pd], ignore_index=True)
        return es


def from_config(cf: dict):
    """
    Create an EvalSet from a configuration dictionary.

    Args:
        cf: The configuration dictionary.

    Returns:
        EvalSet: An EvalSet instance.

    Raises:
        AssertionError: If no evals definition is found in the configuration.
    """
    if evalset := cf.get("evalset", None):
        return evalset
    if eval_path := cf.get("eval_path", None):
        evals = yaml.safe_load(Path(eval_path).read_text(encoding="utf-8"))
    else:
        evals = cf.get("evals", None)
    assert evals is not None, "No evals definition"
    return EvalSet([EvalItem(**e) for e in evals])


if __name__ == "__main__":
    parser = ArgumentParser()
    cmds = parser.add_subparsers(dest="command", required=True)
    gen_cmd = cmds.add_parser("generate")

    gen_cmd.add_argument(
        "--eval_count",
        type=int,
        default=1000,
        help="Number of evals to generate (defaut 1000).",
    )
    gen_cmd.add_argument(
        "--output_path",
        "-o",
        type=Path,
        default=None,
        required=True,
        help="Path to output file (- for stdout).",
    )
    gen_cmd.add_argument(
        "--append",
        "-a",
        action="store_true",
        help="Append to existing output file.",
    )
    gen_cmd.add_argument(
        "--fix_type",
        type=EvalFixTypes,
        default=EvalFixTypes.PATCH,
        required=True,
        choices=list(EvalFixTypes),
        help="""Type of fix to generate.""",
    )
    gen_cmd.add_argument(
        "--min_repo_size",
        type=int,
        default=1,
        help="Minimum size of repo to include (number of files, default 1).",
    )
    gen_cmd.add_argument(
        "--max_repo_size",
        default=None,
        help="Maximum size of repo to include (number of files, default unlimited).",
    )

    load_cmd = cmds.add_parser("load")
    load_cmd.add_argument(
        "input_path",
        type=Path,
        default=None,
        help="Path to input file.",
    )

    consolidate_cmd = cmds.add_parser("consolidate")
    consolidate_cmd.add_argument(
        "input_path",
        type=Path,
        nargs="+",
        default=None,
        help="Path to input files.",
    )
    consolidate_cmd.add_argument(
        "--output_path",
        "-o",
        type=Path,
        default=None,
        required=True,
        help="Path to output file.",
    )

    to_yaml_cmd = cmds.add_parser("to_yaml")
    to_yaml_cmd.add_argument(
        "input_path",
        type=Path,
        default=None,
        help="Path to input file.",
    )

    args = parser.parse_args()

    if args.command == "generate":
        assert (
            args.max_repo_size == 0 or args.max_repo_size >= args.min_repo_size
        ), "max_repo_size must be >= min_repo_size"
        if args.output_path.as_posix() != "-" and not args.append:
            if EvalSet.parquet_path(args.output_path).exists():
                print(
                    f"Warning: {EvalSet.parquet_path(args.output_path)} already exists, quitting.  Use --append to append."
                )
                sys.exit(1)

        esg = EvalSetGenerator()
        es = esg.generate_set(
            eval_count=args.eval_count,
            fix_type=args.fix_type,
            min_repo_size=args.min_repo_size,
            max_repo_size=args.max_repo_size,
        )
        if args.output_path.as_posix() == "-":
            print(es.to_yaml())
        else:
            ppath = es.write_parquet(args.output_path)
            print(f"EvalSet written to {ppath}")

    elif args.command == "load":
        ip = args.input_path
        if not ip.exists():
            ip = EvalSet.parquet_path(ip)
        if not ip.exists():
            print(f"Can't find input file at {args.input_path} or {ip}!")
            sys.exit(1)
        es = EvalSet.from_parquet(ip)
        print(f"Loaded {len(es)} evals from {ip}")
    elif args.command == "consolidate":
        es = EvalSet()
        for ip in args.input_path:
            if not ip.exists():
                ip = EvalSet.parquet_path(ip)
            if not ip.exists():
                print(f"Can't find input file at {ip}!")
                sys.exit(1)
            es += EvalSet.from_parquet(ip)
        ppath = es.write_parquet(args.output_path)
        print(f"EvalSet written to {ppath}")
    elif args.command == "to_yaml":
        ip = args.input_path
        if not ip.exists():
            ip = EvalSet.parquet_path(ip)
        if not ip.exists():
            print(f"Can't find input file at {args.input_path} or {ip}!")
            sys.exit(1)
        es = EvalSet.from_parquet(ip)
        print(es.to_yaml())
    else:
        print(f"Unknown command {args.command}")
        sys.exit(1)
