#!/bin/bash
#
# Example Usage:
# bash research/eval/edit/evaluate_all.sh research/eval/edit/data prompt_v1 ~/cache/CodeEditEval/
#
set -e

if [ "$#" -ne 3 ] ;then
  echo "Input illegal number of parameters " $#
  exit 1
fi

data_path=$1
prompt_name=$2
output_dir=$3

SCRIPT_DIR=$(dirname "$(dirname "$(realpath "${BASH_SOURCE[0]}")")")
echo "Script directory is: $SCRIPT_DIR"

# Get the list of available systems
systems=$(python ${SCRIPT_DIR}/eval.py -l | grep '^System:' | grep 'Base' | sed 's/System: //')
echo "All avaliable systems:"
echo "${systems}"
echo "--------------------------------"

# To evaluate a specific subset of systems,
# replace previous line with this one:
# systems=("DeepSeek1BCodeEditSystemHF" "DeepSeek6BCodeEditSystemHF")

for system in ${systems[@]}; do
  echo "Processing system: $system"
  python ${SCRIPT_DIR}/eval.py -s $system -i $data_path -p $prompt_name -o $output_dir/${system} --num_in_context_examples 1
done
