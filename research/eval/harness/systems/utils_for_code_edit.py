"""Util functions for the model server."""

import dataclasses
import re
import typing

from research.core.edit_prompt_input import ResearchEditPromptInput
from research.core.model_input import ModelInput
from research.core.utils_for_str import (
    delete_lines_from_content,
    delete_prefix_suffix_from_content,
    extract_the_last_markdown_block,
    get_first_n_lines,
    get_last_n_lines,
)


def postprocess_fn(text: str, strategy: typing.Optional[str] = None) -> str:
    """Post-process the generated text for the code edit request."""
    if strategy is None:
        clean_text = text
    elif strategy == "droid":
        match = re.search(r"^(.*)\n```\n*$", text, re.DOTALL)
        clean_text = match.group(1) if match else text
    else:
        raise ValueError(f"Invalid strategy: {strategy}")
    return clean_text


def postprocess_model_response_for_edit(
    generated_text: str,
    start_symbols: typing.Optional[str] = None,
    end_symbols: typing.Optional[str] = None,
    prefix: typing.Optional[str] = None,
    suffix: typing.Optional[str] = None,
    delete_context: bool = True,
) -> str:
    """Post-process the generated text for the code edit request.

    Based on the implementation from experimental/dxy/demo/post_processing.py
    """

    # First, find the code within backticks.
    # If there are multiple block, only the last one is used.
    # This way we could potentially generate CoT type of responses with multiple code blocks.
    extracted_code = extract_the_last_markdown_block(generated_text)
    extracted_code = extracted_code or generated_text

    if start_symbols is not None:
        start_symbols = re.escape(start_symbols)
    if end_symbols is not None:
        end_symbols = re.escape(end_symbols)

    if start_symbols is not None and end_symbols is not None:
        # Find the code within the special symbols
        pattern = rf"{start_symbols}\n(.*?){end_symbols}"
        match = re.search(pattern, extracted_code, re.DOTALL)
        if match:
            return match.group(1)
    if start_symbols is not None:
        # Find the codes after the start symbols
        pattern = rf"{start_symbols}(.*?)"
        match = re.search(pattern, extracted_code, re.DOTALL)
        if match:
            print(rf"Match the pattern: : {pattern}")
            response = match.group(1)
            response, success_del = delete_prefix_suffix_from_content(
                response, prefix=prefix, suffix=None
            )
            return response

    response = extracted_code
    if delete_context:
        # Finally, manually strip prefix and suffix from the response.
        response, success_del = delete_prefix_suffix_from_content(
            response, prefix=prefix, suffix=suffix
        )
        if not success_del[0] and not success_del[1]:
            response = delete_lines_from_content(
                response,
                start=0 if prefix is None else len(prefix.splitlines(keepends=True)),
                end=0 if suffix is None else len(suffix.splitlines(keepends=True)),
            )
        elif not success_del[0]:
            response = delete_lines_from_content(
                response,
                start=0 if prefix is None else len(prefix.splitlines(keepends=True)),
                end=0,
            )
        elif not success_del[1]:
            response = delete_lines_from_content(
                response,
                start=0,
                end=0 if suffix is None else len(suffix.splitlines(keepends=True)),
            )
    return response


def compute_actual_prefix_and_suffix(
    prefix: str,
    suffix: str,
    selected_code: str,
    lines_in_prefix_suffix: int,
    edits_input_token_budget: int,
) -> tuple[bool, str, str]:
    """Truncates prefix and suffix based on limits."""
    if lines_in_prefix_suffix is None:
        # The default behavior for the contractor's use case, needs to be improved
        # to get a better trade-off between accuracy and efficiency.
        if len(selected_code.splitlines(True)) >= 16:
            is_context_free = True
            actual_prefix = ""
            actual_suffix = ""
        else:
            is_context_free = False
            actual_prefix = get_last_n_lines(prefix, 6)
            actual_suffix = get_first_n_lines(suffix, 6)
    elif lines_in_prefix_suffix == 0:
        is_context_free = True
        actual_prefix = ""
        actual_suffix = ""
    elif lines_in_prefix_suffix == -1:
        # Approximation for number of tokens in string s
        def get_tokens_length(s):
            return len(s) / 3

        prefix_lines = prefix.splitlines(True)
        suffix_lines = list(
            reversed(suffix.splitlines(True))
        )  # Reversed here so that we can iterate from the end of list and remove elements efficiently

        actual_prefix = []
        actual_suffix = []

        number_of_tokens = get_tokens_length(selected_code)
        while len(prefix_lines) + len(suffix_lines) > 0:
            if len(prefix_lines) > 0:
                prefix_line = prefix_lines.pop(-1)
                prefix_line_tokens = get_tokens_length(prefix_line)
                if number_of_tokens + prefix_line_tokens <= edits_input_token_budget:
                    actual_prefix.append(prefix_line)
                    number_of_tokens += prefix_line_tokens
                else:
                    break

            if len(suffix_lines) > 0:
                suffix_line = suffix_lines.pop(-1)
                suffix_line_tokens = get_tokens_length(suffix_line)
                if number_of_tokens + suffix_line_tokens <= edits_input_token_budget:
                    actual_suffix.append(suffix_line)
                    number_of_tokens += suffix_line_tokens
                else:
                    break

        is_context_free = len(actual_prefix) + len(actual_suffix) == 0
        actual_prefix = "".join(reversed(actual_prefix))
        actual_suffix = "".join(actual_suffix)
    else:
        is_context_free = False
        actual_prefix = get_last_n_lines(prefix, lines_in_prefix_suffix)
        actual_suffix = get_first_n_lines(suffix, lines_in_prefix_suffix)

    return is_context_free, actual_prefix, actual_suffix


def add_code_edit_default_extras(
    model_input: ResearchEditPromptInput,
    default_selected_code: typing.Optional[str],
    default_instruction: typing.Optional[str],
) -> ResearchEditPromptInput:
    model_input = model_input.clone()
    if default_instruction and model_input.instruction == "":
        model_input = dataclasses.replace(model_input, instruction=default_instruction)

    if default_selected_code and model_input.selected_code == "":
        last_lines = [s for s in model_input.prefix.splitlines() if s.strip()]
        last_line = last_lines[-1] if last_lines else ""
        indent_str = last_line[: (len(last_line) - len(last_line.lstrip()))]
        model_input = dataclasses.replace(
            model_input, selected_code=(indent_str + "\t" + default_selected_code)
        )
    model_input.extra["lines_in_prefix_suffix"] = 10_000  # fixme: hack to ignore this
    return model_input


def apply_grab_instruction_and_selected_code_from_extra(
    model_input: ModelInput,
) -> ModelInput:
    """This utility grabs the instruction and selected code from the extra field where Hydra stores them."""
    model_input = model_input.clone()
    # pylint: disable=protected-access
    selected_code = model_input.extra["patch"]._extra["buggy_version"]
    instruction = model_input.extra["patch"]._extra["instruction"]
    # pylint: enable=protected-access
    model_input.extra["selected_code"] = selected_code
    model_input.extra["instruction"] = instruction
    return model_input
