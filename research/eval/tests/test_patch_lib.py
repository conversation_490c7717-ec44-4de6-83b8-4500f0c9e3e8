"""Tests for research.eval.patch."""

import dataclasses
import textwrap

import pytest

from research.eval import patch_lib


def test_patch_for_span():
    original_text = """Line 1.\nLine 2.\nLine 3.\nLine 4.\n"""

    patch = patch_lib.Patch.for_span(original_text, 16, 24)
    assert patch.file_content == original_text
    assert patch.patch_content == "Line 3.\n"
    assert patch.patched_file_content == patch.file_content


def test_patch_empty():
    original_text = """Line 1.\nLine 2.\nLine 3.\nLine 4.\n"""

    patch = patch_lib.Patch.empty(original_text)
    assert patch.file_content == original_text
    assert patch.char_start == 0
    assert patch.char_end == 0
    assert patch.patch_content == ""
    assert patch.patched_file_content == patch.file_content


def test_patch_str():
    original_text = """Line 1.\nLine 2.\nLine 3.\nLine 4.\n"""

    patch = patch_lib.Patch.for_span(original_text, 16, 24).with_patch_content(
        "Circle 3.\n"
    )
    assert str(patch) == (
        "--- \n+++ \n@@ -1,5 +1,5 @@\n Line 1.\n Line 2.\n"
        + "-Line 3.\n+Circle 3.\n Line 4.\n "
    )


def test_patch_repr():
    patch = patch_lib.Patch(
        file_content="this is a test.", char_start=5, char_end=7, patch_content="isn't"
    )
    assert repr(patch) == "[Patch 5:7]"


def test_patch_original_patch_content():
    patch = patch_lib.Patch(
        file_content="this is a test.", char_start=5, char_end=7, patch_content="isn't"
    )
    assert patch.original_patch_content == "is"


def test_patch_patched_file_content():
    patch = patch_lib.Patch(
        file_content="this is a test.", char_start=5, char_end=7, patch_content="isn't"
    )
    assert patch.patched_file_content == "this isn't a test."


def test_patch_patched_text_empty():
    patch = patch_lib.Patch(
        file_content="this is a test.", char_start=7, char_end=7, patch_content=" not"
    )
    assert patch.patched_file_content == "this is not a test."


def test_patch_with_patch_content():
    patch = patch_lib.Patch.for_span("this is a test.", 7, 7)
    new_patch = patch.with_patch_content(" not")
    assert (
        patch.patched_file_content == "this is a test."
    ), "Original patch was modified."
    assert (
        new_patch.patched_file_content == "this is not a test."
    ), "New patch wasn't updated correctly."


def test_patch_patch_size_bytes():
    patch = patch_lib.Patch(
        file_content="this is a test.", char_start=5, char_end=7, patch_content="isn't"
    )
    assert patch.patch_size_bytes == len("isn't")


def test_patch_patch_size_lines():
    patch = patch_lib.Patch(
        file_content="this is a test.", char_start=5, char_end=7, patch_content="isn't"
    )
    assert patch.patch_size_lines == 1

    patch = patch_lib.Patch(
        file_content="this is a test.",
        char_start=5,
        char_end=7,
        patch_content="isn't\nthis long\n",
    )
    assert patch.patch_size_lines == 3


def test_patch_as_udiff():
    original_text = """def hello_world():\n""" + """    print("hello world!")\n"""

    original_patch = patch_lib.Patch.for_span(original_text, 19, 45)
    assert original_patch.patch_content == """    print("hello world!")\n"""
    assert original_patch.as_udiff() == ""

    updated_patch = original_patch.with_patch_content("""    print("Hello World!")\n""")
    assert updated_patch.as_udiff() == (
        """--- \n+++ \n@@ -1,3 +1,3 @@\n"""
        + """ def hello_world():\n"""
        + """-    print("hello world!")\n"""
        + """+    print("Hello World!")\n"""
        + """ """
    )

    assert updated_patch.as_udiff(with_delimiters=True) == (
        """--- \n+++ \n@@ -1,5 +1,5 @@\n"""
        + """ def hello_world():\n"""
        + """ # START COMPLETION\n"""
        + """-    print("hello world!")\n"""
        + """+    print("Hello World!")\n"""
        + """ # END COMPLETION\n"""
        + """ """
    )


def test_patch_as_udiff_with_character_level_change():
    original_text = (
        """def call_api():\n"""
        + """    request = {"foo": "bar"}\n"""
        + """    response = my_api.call(request)\n"""
        + """    return response\n"""
    )

    original_patch = patch_lib.Patch.for_span(original_text, 17 + 30 + 13, 17 + 30 + 24)
    assert original_patch.patch_content == "my_api.call"

    updated_patch = original_patch.with_patch_content("your_api.call")
    assert updated_patch.as_udiff() == (
        """--- \n+++ \n@@ -1,5 +1,5 @@\n"""
        + """ def call_api():\n"""
        + """     request = {"foo": "bar"}\n"""
        + """-    response = my_api.call(request)\n"""
        + """+    response = your_api.call(request)\n"""
        + """     return response\n"""
        + """ """
    )

    assert updated_patch.as_udiff(with_delimiters=True) == (
        """--- \n+++ \n@@ -1,7 +1,7 @@\n"""
        + """ def call_api():\n"""
        + """     request = {"foo": "bar"}\n"""
        + """ # START COMPLETION\n"""
        + """-    response = my_api.call(request)\n"""
        + """+    response = your_api.call(request)\n"""
        + """ # END COMPLETION\n"""
        + """     return response\n"""
        + """ """
    )


def test_load_patches_from_text_single():
    patches = patch_lib.load_patches_from_text(
        textwrap.dedent(
            """
    def hello_world():
    # START COMPLETION
        print("hello world!")
    # END COMPLETION
    """
        )
    )

    for patch in patches:
        assert patch.patched_file_content == patch.file_content

    original_text = textwrap.dedent(
        """
    def hello_world():
        print("hello world!")
    """
    )

    expected_patches = [patch_lib.Patch.for_span(original_text, 20, 46, patch_id="/0")]
    assert patches == expected_patches


def test_load_patches_from_text_multiple():
    patches = patch_lib.load_patches_from_text(
        textwrap.dedent(
            """
    @dataclass
    class Turtle:
    # START COMPLETION
        x: float = 0.0
        y: float = 0.0
        dx: float = 1.0
        dy: float = 0.0
    # END COMPLETION


    def turn_right(turtle):
    # START COMPLETION
        turtle.dx, turtle.dy = turtle.dy, -turtle.dx
    # END COMPLETION


    def turn_left(turtle):
    # START COMPLETION
        turtle.dx, turtle.dy = -turtle.dy, turtle.dx
    # END COMPLETION
    """
        )
    )

    original_text = textwrap.dedent(
        """
    @dataclass
    class Turtle:
        x: float = 0.0
        y: float = 0.0
        dx: float = 1.0
        dy: float = 0.0


    def turn_right(turtle):
        turtle.dx, turtle.dy = turtle.dy, -turtle.dx


    def turn_left(turtle):
        turtle.dx, turtle.dy = -turtle.dy, turtle.dx
    """
    )

    for patch in patches:
        assert patch.patched_file_content == patch.file_content

    expected_patches = [
        patch_lib.Patch.for_span(original_text, 26, 104, patch_id="/0"),
        patch_lib.Patch.for_span(original_text, 130, 179, patch_id="/1"),
        patch_lib.Patch.for_span(original_text, 204, 253, patch_id="/2"),
    ]

    assert patches == expected_patches


def test_patch_asdict():
    patch = patch_lib.Patch(
        file_content="some text",
        char_start=0,
        char_end=4,
        patch_content="any",
        patch_id="id",
        repository="repo",
        commit_sha="commit_sha",
        file_name="file_name",
    )
    assert dataclasses.asdict(patch) == {
        "file_content": "some text",
        "char_start": 0,
        "char_end": 4,
        "patch_content": "any",
        "patch_id": "id",
        "repository": "repo",
        "commit_sha": "commit_sha",
        "file_name": "file_name",
        "_extra": {},
    }


def test_patch_fromdict():
    expected_patch = patch_lib.Patch(
        file_content="some text",
        char_start=0,
        char_end=4,
        patch_content="any",
        patch_id="id",
        repository="repo",
        commit_sha="commit_sha",
        file_name="file_name",
    )
    assert expected_patch == patch_lib.Patch(
        **{
            "file_content": "some text",
            "char_start": 0,
            "char_end": 4,
            "patch_content": "any",
            "patch_id": "id",
            "repository": "repo",
            "commit_sha": "commit_sha",
            "file_name": "file_name",
            "_extra": {},
        }
    )


@pytest.mark.parametrize(
    "original, new, char_start, char_end, patch_content",
    [
        # Replace
        (
            """Line 1.\nLine 2.\nLine 3.\nLine 4.\n""",
            """Line 1.\nLine 2.\nLine X.\nLine 4.\n""",
            16,
            24,
            "Line X.\n",
        ),
        # Insert
        (
            """Line 1.\nLine 2.\nLine 3.\nLine 4.\n""",
            """Line 1.\nLine 2.\nLine 3.\nLine X.\nLine 4.\n""",
            24,
            24,
            "Line X.\n",
        ),
        # Removal
        (
            """Line 1.\nLine 2.\nLine 3.\nLine 4.\n""",
            """Line 1.\nLine 2.\nLine 4.\n""",
            16,
            24,
            "",
        ),
    ],
)
def test_create_patch_from_files_whole_line(
    original, new, char_start, char_end, patch_content
):
    patch = patch_lib.create_patch_from_files(
        original, new, patch_lib.Alignment.WHOLE_LINE
    )
    assert patch.file_content == original
    assert patch.char_start == char_start
    assert patch.char_end == char_end
    assert patch.patch_content == patch_content
    assert patch.patched_file_content == new


@pytest.mark.parametrize(
    "original, new, char_start, char_end, patch_content",
    [
        # Replace
        (
            """Line 1.\nLine 2.\nLine 3.\nLine 4.\n""",
            """Line 1.\nLine 2.\nLine X.\nLine 4.\n""",
            21,
            24,
            "X.\n",
        ),
        # Insert
        (
            """Line 1.\nLine 2.\nLine 3.\nLine 4.\n""",
            """Line 1.\nLine 2.\nLine 3.\nLine X.\nLine 4.\n""",
            24,
            24,
            "Line X.\n",
        ),
        # Removal
        (
            """Line 1.\nLine 2.\nLine 3.\nLine 4.\n""",
            """Line 1.\nLine 2.\nLine 4.\n""",
            16,
            24,
            "",
        ),
    ],
)
def test_create_patch_from_files_till_line_end(
    original, new, char_start, char_end, patch_content
):
    patch = patch_lib.create_patch_from_files(
        original, new, patch_lib.Alignment.TILL_LINE_END
    )
    assert patch.file_content == original
    assert patch.char_start == char_start
    assert patch.char_end == char_end
    assert patch.patch_content == patch_content
    assert patch.patched_file_content == new


@pytest.mark.parametrize(
    "original, new, char_start, char_end, patch_content",
    [
        # Replace
        (
            """Line 1.\nLine 2.\nLine 3.\nLine 4.\n""",
            """Line 1.\nLine 2.\nLine X.\nLine 4.\n""",
            21,
            22,
            "X",
        ),
        # Insert
        (
            """Line 1.\nLine 2.\nLine 3.\nLine 4.\n""",
            """Line 1.\nLine 2.\nLine 3.\nLine X.\nLine 4.\n""",
            24,
            24,
            "Line X.\n",
        ),
        # Removal
        (
            """Line 1.\nLine 2.\nLine 3.\nLine 4.\n""",
            """Line 1.\nLine 2.\nLine 4.\n""",
            16,
            24,
            "",
        ),
    ],
)
def test_create_patch_from_files_within_line(
    original, new, char_start, char_end, patch_content
):
    patch = patch_lib.create_patch_from_files(
        original, new, patch_lib.Alignment.WITHIN_LINE
    )
    assert patch.file_content == original
    assert patch.char_start == char_start
    assert patch.char_end == char_end
    assert patch.patch_content == patch_content
    assert patch.patched_file_content == new
