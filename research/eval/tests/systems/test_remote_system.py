"""Unit tests for the remote system."""

import uuid
from typing import Iterable, Optional
from unittest.mock import MagicMock, patch

import shortuuid

from base.augment_client.client import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    CompleteResponse,
    FeatureFlags,
    FindMissingResponse,
    GetModelsResponse,
    Language,
    Model,
    UploadContent,
)
from base.blob_names.python.blob_names import get_blob_name
from research.core.model_input import ModelInput
from research.core.types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from research.eval.harness.factories import create_system
from research.retrieval.types import Document


class MockClient:
    """Mock for the api_client interface.

    The interface covers a subset of the functionality of the AugmentClient and a subset of the
    functionality of the AugmentModelClient. The interfaces are combined in one mock object to
    make it easier to test the remote system.
    """

    def __init__(self, all_docs: Iterable[Document]):
        # all documents expected to be used by the system for retrieval
        self.all_docs: list[Document] = list(all_docs)
        # all ids that have been checkpointed
        self.checkpointed: set[str] = set()
        # the latest checkpoint id returned by the mock
        self.checkpoint_id: Optional[str] = None

    def get_models(self):
        models = [
            Model(
                name="test",
                suggested_prefix_char_count=4096,
                suggested_suffix_char_count=4096,
            )
        ]
        languages = [Language(name="test", vscode_name="test", extensions=[".py"])]
        feature_flags = FeatureFlags(
            bypass_language_filter=False,
            max_upload_size_bytes=128 * 1024,
        )
        return GetModelsResponse(
            default_model="test",
            models=models,
            languages=languages,
            feature_flags=feature_flags,
        )

    def find_missing(
        self, model_name: str, memory_object_names: list[str]
    ) -> FindMissingResponse:
        fmr = FindMissingResponse(unknown_memory_names=[], nonindexed_blob_names=[])
        return fmr

    # Note, the return value of this function may be overridden by the outer
    # magic-mock object to return itself. This way the outer mock can capture
    # method calls, etc.
    def client_for_model(self, model_name):
        return self

    def batch_upload(self, blobs: list[UploadContent]) -> list[str]:
        # Validate that all paths are known and return the corresponding ids.
        path_to_id = {x.path: x.id for x in self.all_docs}
        return list([path_to_id[x.path_name] for x in blobs])

    def checkpoint_blobs(self, blobs: BlobsJson) -> str:
        # Validate that all added blobs are known, and track all checkpointed blobs for later validation.
        assert set(blobs.added_blobs).issubset(set([x.id for x in self.all_docs]))
        assert not blobs.deleted_blobs
        self.checkpointed.update(blobs.added_blobs)
        self.checkpoint_id = shortuuid.uuid()
        return self.checkpoint_id

    def complete(self, *args, **kwargs):
        # Validate that the entire set of blobs is known and is exactly our set of documents.
        blobs: BlobsJson = kwargs["blobs"]
        assert blobs.checkpoint_id == self.checkpoint_id
        assert blobs.added_blobs
        assert not blobs.deleted_blobs
        assert self.checkpointed
        assert set(blobs.added_blobs) | self.checkpointed == set(
            [x.id for x in self.all_docs]
        )
        return CompleteResponse(
            text="test",
            completion_items=[],
            unknown_memory_names=[],
            request_id=uuid.uuid4(),
            checkpoint_not_found=False,
            suggested_prefix_char_count=4096,
            suggested_suffix_char_count=4096,
        )


def test_remote_system():
    """Validate that the remote system works as expected through the mock client."""

    # Lower the default working set limits to trigger checkpointing.
    max_working_set_items = 100
    max_checkpoint_items = 100

    config = {
        "name": "remote_completion",
        "client": {"url": "http://localhost:8080"},
        "retriever": {
            "max_working_set_items": max_working_set_items,
            "max_checkpoint_items": max_checkpoint_items,
        },
    }

    # Create enough documents to trigger checkpointing.
    total_docs = max_working_set_items + 1
    indexed_paths = [f"{shortuuid.uuid()}.py" for _ in range(total_docs)]
    indexed_docs = [
        Document(id=get_blob_name(path, "hello"), text="hello", path=path)
        for path in indexed_paths
    ]
    indexed_ids = [doc.id for doc in indexed_docs]

    # We wrap our mock api client with a magic-mock. This allows us to implement the
    # interface of the api-client while also capturing calls to the api-client
    # through the magic-mock.
    inner_obj = MockClient(indexed_docs)
    mc = MagicMock(wraps=inner_obj)

    # Note: we return the outer magic-mock object to the caller, so we can (1)
    # use the same mock for both the AugmentClient and the AugmentModelClient,
    # and (2) so we can continue to monitor calls into the mock.
    mc.client_for_model.return_value = mc

    # Add a doc that will get filtered out (too large), the mock client will assert
    # that it only receives the valid docs.
    bad_ids = [shortuuid.uuid()]
    bad_docs = [
        Document(id=x, text="x" * (1024 * 1024 + 1), path=f"{x}.py") for x in bad_ids
    ]
    all_ids = indexed_ids + bad_ids
    all_docs = indexed_docs + bad_docs

    # Note that the patch to the get_augment_client factory happens in the
    # completion system, not in the module where it was defined.  We could also
    # patch the AugmentClient in the remote_lib.py module, but mocking the
    # factory function was slightly simpler.
    with patch(
        "research.eval.harness.systems.remote_completion_system.get_augment_client",
        return_value=mc,
    ):
        system = create_system(config)
        system.load()
        system.add_docs(all_docs)
        system.generate(
            ModelInput(
                prefix="hello",
                cursor_position=len("hello"),
                doc_ids=all_ids,
            )
        )
        system.unload()

    # Validate expected number of calls.
    assert mc.checkpoint_blobs.call_count == 1
    assert mc.complete.call_count == 1
