"""Data utilities for Vulcan tests."""

import dataclasses
import logging
import pathlib
import re
from typing import Any, Sequence

import yaml

from research.core.model_input import ModelInput
from research.eval.patch_lib import Patch
from research.models import GenerationOptions, GenerativeLanguageModel

VULCAN_TEST_ROOT = pathlib.Path(__file__).parent / "testdata"
DEFAULT_CURSOR_TOKEN = "<FILL-HERE>"


@dataclasses.dataclass
class VulcanTest:
    """Describes the structure of a Vulcan test."""

    filename: str
    """The filename of the test."""
    description: str
    """A description of what the whole code snippet should do. This description may be
       used to prompt an automatic LLM judge."""
    expected: str
    """The expected completion between prefix and suffix."""
    prefix: str
    """The prefix of the code completion request."""
    suffix: str
    """The suffix of the code completion request."""
    meta: dict[str, Any]
    """Any additional metadata."""

    def as_patch(self, patch_content: str = "") -> Patch:
        """Converts Vulcan test into a Patch."""
        return Patch(
            patch_id=self.filename,
            file_name=self.filename,
            file_content=self.prefix + self.expected + self.suffix,
            char_start=len(self.prefix),
            char_end=len(self.prefix) + len(self.expected),
            patch_content=patch_content,
        )

    def run(
        self,
        model: GenerativeLanguageModel,
        generation_options: "GenerationOptions | None" = None,
    ) -> str:
        """Run this test case against `model` with canonical length settings."""
        if generation_options is None:
            generation_options = GenerationOptions()

        expected_tokens = self.meta.get(
            "max_generated_tokens", len(self.expected) if self.expected else None
        )

        if expected_tokens is not None:
            generation_options = generation_options.rebind(
                max_generated_tokens=expected_tokens
            )

        return model.generate(
            ModelInput(
                prefix=self.prefix,
                suffix=self.suffix,
            ),
            generation_options,
        )

    @classmethod
    def from_text(
        cls,
        text: str,
        filename: str,
        cursor_token: str = DEFAULT_CURSOR_TOKEN,
    ) -> "VulcanTest":
        """Parse text to a VulcanTest by finding a cursor token or begin/end tokens."""
        _, suffix = filename.rsplit(".", 1)
        if suffix == "py":
            linecomment = "# "
            begin_token = r"^\s*# BEGIN\n"
            end_token = r"^\s*# END\n"
        elif suffix in ["cpp", "c", "go", "java", "js", "ts"]:
            linecomment = "// "
            begin_token = r"(^\s*/\*begin\*/\n|/\*begin\*/)"
            end_token = r"(^\s*/\*end\*/\n|/\*end\*/)"
        else:
            raise NotImplementedError(
                f"VulcanTest doesn't yet parse files like {filename}"
            )
        frontmatter_delimiter = f"{linecomment}---\n"

        inline_begin_token = "<begin>"
        inline_end_token = "<end>"

        if text.startswith(frontmatter_delimiter):
            frontmatter_start = len(frontmatter_delimiter)

            if (
                frontmatter_end := text.find(frontmatter_delimiter, frontmatter_start)
            ) < 0:
                raise ValueError(f"{filename} frontmatter doesn't end.")

            frontmatter = "".join(
                [
                    line[len(linecomment) :] if line.startswith(linecomment) else line
                    for line in text[frontmatter_start:frontmatter_end].splitlines(
                        keepends=True
                    )
                ]
            )
            meta = yaml.safe_load(frontmatter)
            text = text[frontmatter_end + len(frontmatter_delimiter) :]
        else:
            meta = {}

        # Find the cursor text.
        # We support a few types of cursors.
        if (cursor_idx := text.find(cursor_token)) >= 0:
            # A <FILL-HERE> type of cursor.
            expected = meta.pop("expected", "")
            prefix = text[0:cursor_idx]
            suffix = text[cursor_idx + len(cursor_token) :]
        elif (begin_pos := text.find(inline_begin_token)) >= 0 and (
            end_pos := text.find(inline_end_token, begin_pos + len(inline_begin_token))
        ) >= 0:
            expected = text[begin_pos + len(inline_begin_token) : end_pos]
            prefix = text[0:begin_pos]
            suffix = text[end_pos + len(inline_end_token) :]
        elif (
            begin_idx := re.search(begin_token, text, re.MULTILINE | re.IGNORECASE)
        ) and (end_idx := re.search(end_token, text, re.MULTILINE | re.IGNORECASE)):
            leading_spaces = _get_leading_spaces(text[begin_idx.end() :])
            expected = text[begin_idx.end() + leading_spaces : end_idx.start() - 1]
            prefix = text[0 : begin_idx.start()]
            suffix = text[end_idx.end() - 1 :]
        else:
            raise ValueError("Couldn't find cursor marker in test file")

        return cls(
            filename=filename,
            description=meta.pop("description", ""),
            expected=expected,
            prefix=prefix,
            suffix=suffix,
            meta=meta,
        )

    @classmethod
    def from_file(
        cls, filename: pathlib.Path, cursor_token: str = DEFAULT_CURSOR_TOKEN
    ) -> "VulcanTest":
        with filename.open() as f:
            text = f.read()
            return cls.from_text(text, str(filename), cursor_token=cursor_token)


def _get_leading_spaces(text: str) -> int:
    """Returns the leading spaces of a string."""
    n = 0
    while text[n].isspace():
        n += 1
    return n


def load_tests(
    test_root: pathlib.Path = VULCAN_TEST_ROOT,
    cursor_token: str = DEFAULT_CURSOR_TOKEN,
    test_pattern: str = "",
    tags: Sequence[str] = (),
) -> list[VulcanTest]:
    tags_ = set(tags)

    ret = list[VulcanTest]()
    for file in test_root.glob("**/*"):
        if not file.is_file() or file.name.startswith("."):
            continue
        if test_pattern and not re.match(
            test_pattern, str(file.relative_to(test_root))
        ):
            continue
        try:
            test = VulcanTest.from_file(file, cursor_token)
            # Update the filename to make the patch ids nicer to read.
            test.filename = str(file.relative_to(test_root))
            if not tags_ or (tags_ & set(test.meta.get("tags", []))):
                ret.append(test)
        # pylint:disable-next=broad-exception-caught
        except Exception:
            logging.exception("Failed to load test %s", file)
            continue
    ret.sort(key=lambda x: x.filename)
    return ret
