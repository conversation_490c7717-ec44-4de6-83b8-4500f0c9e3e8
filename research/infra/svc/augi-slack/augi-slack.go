package main

import (
	"context"
	"os"
	"os/signal"
	"strings"
	"syscall"

	"github.com/spf13/pflag"

	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/research/infra/lib/slack/augislack"
)

func main() {
	flags := struct {
		botToken   string
		appToken   string
		kubeconfig string
	}{}

	fs := pflag.NewFlagSet(os.Args[0], pflag.ExitOnError)
	fs.StringVar(&flags.botToken, "bot-token", "", "Slack 'bot' token as a file path or string beginning with 'xoxb-'")
	fs.StringVar(&flags.appToken, "app-token", "", "Slack 'app' token as a file path or string beginning with 'xapp-'")
	fs.StringVar(&flags.kubeconfig, "kubeconfig", "", "Path to kubeconfig file.")
	flog := logger.GlobalLoggerFromFlags(fs)

	fs.Parse(os.Args[1:])

	log, err := flog(os.Stderr)
	if err != nil {
		log.Fatal().Err(err).Send() // The nil logger still works on err.
	}

	/// Read flags from file, if needed.

	if err := readFlag(&flags.botToken); err != nil {
		log.LogErr("Error reading --bot-token: %v.", err)
		os.Exit(1)
	}
	if err := readFlag(&flags.appToken); err != nil {
		log.LogErr("Error reading --app-token: %v.", err)
		os.Exit(1)
	}

	/// Handle SIGTERM

	ctx, cancel := context.WithCancelCause(context.Background())
	ch := make(chan os.Signal, 1)
	signal.Notify(ch, syscall.SIGTERM, syscall.SIGINT)
	go func() {
		sig := <-ch
		log.LogNotice("Caught %v.", sig)
		cancel(nil)
	}()

	/// Build and run the bot.

	bot, err := augislack.NewBot(flags.botToken, flags.appToken, flags.kubeconfig)
	if err != nil {
		log.LogErr("%v.", err)
		os.Exit(1)
	}
	if err := bot.Run(ctx); err != nil {
		log.LogErr("%v.", err)
		os.Exit(1)
	}
}

func readFlag(flag *string) error {
	if flag != nil && (strings.HasPrefix(*flag, "/") || strings.HasPrefix(*flag, "./")) {
		if buf, err := os.ReadFile(*flag); err != nil {
			return err
		} else {
			*flag = string(buf)
		}
	}
	return nil
}
