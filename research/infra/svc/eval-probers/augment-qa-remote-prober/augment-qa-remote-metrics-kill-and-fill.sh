#!/bin/bash

# This script rebuilds a sqlite DB with all eval-prober results in the current directory.
# It then runs the query passed on the command line, or a default one if nothing is provided.
# It's a bit heavy weight since all columns need to be supported individually (as opposed to
# a giant json payload column) but the end result is the easiest to query.

set -eu

main() {
	declare -r BASENAME='augment-qa-remote'

	declare -r tmp="$(mktemp "/tmp/$BASENAME-prober-metrics-tmp.XXXXXXXX")"
	declare -r tmp_as_array="$tmp.array"
	declare -r sqlite_db="$BASENAME.sqlite.db"
	declare -r html="$BASENAME.results.html"

	#
	# Normalize various input formats into a single file.
	# Postgre wants a jsonl file, Sqlite wants single json array.
	#
	shopt -s nullglob

	log "Aggregating jsonl results: %s..." "$tmp"
	jq -cr 'if type == "array" then .[] else . end | .+= {"filename": input_filename}' */*results.jsonl */*/*/*results.jsonl > "$tmp"
	log "Aggregating json results: %s..." "$tmp_as_array"
	jq -sr '.' "$tmp" > "$tmp_as_array"

	#
	# Kill-and-fill `devex-metrix` postgre table
	#

	postgre_kill_and_fill "$tmp"

	#
	# Kill-and-fill local sqlite table
	#

	sqlite_kill_and_fill "$sqlite_db" "$tmp_as_array"

	#
	# Regenerate HTML report
	#

	regenerate_html_report "$html" "$sqlite_db"

	#
	# Cleanup
	#

	rm -f "$tmp" "$tmp_as_array"
}

log() {
	declare -r fmt="$1"; shift
	printf "%(%Y-%m-%d %H:%M:%S %Z)T INFO $fmt\n" -1 "$@"
}

pgval() {
	declare -r envvar="$1"
	declare -r cfgfile="$2"
	declare -r default="$3"

	if [[ -v "$envvar" ]]; then
		echo -n "${!envvar}"
	elif [[ -r "$cfgfile" ]]; then
		echo -n "$(<"$cfgfile")"
	else
		echo -n "$default"
	fi
}

postgre_kill_and_fill() {
	declare -r metrics_file="$1"

	log "Reloading Postgre Data from %s" "$metrics_file"

	declare -r pgcfg="/run/augment/secrets/eval-prober-psql-creds"
	declare -r pghostname="$(pgval PGHOSTNAME "$pgcfg/hostname" "devex-metrics-postgresql")"
	declare -r pgusername="$(pgval PGUSERNAME "$pgcfg/username" "augment")"
	declare -r pgpassword="$(pgval PGPASSWORD "$pgcfg/password" "")"
	declare -r pgdatabase="$(pgval PGDATABASE "$pgcfg/database" "metrics")"

	PGPASSWORD="$pgpassword" psql --host="$pghostname" --user="$pgusername" "$pgdatabase" --file=/dev/stdin <<-EOF
	BEGIN;

	DROP TABLE IF EXISTS augment_qa_prober_tmp;
	CREATE TABLE augment_qa_prober_tmp (r jsonb);
	\\copy augment_qa_prober_tmp (r) FROM '$metrics_file';

	COMMIT;

	BEGIN;

	DROP TABLE IF EXISTS augment_qa_prober;
	CREATE TABLE augment_qa_prober (
		start_time timestamp,
		duration REAL,
		"limit" INT,

		"answer_keyword_recall" REAL,
		"retrievals_keyword_recall" REAL,
		"gold_paths_recall" REAL,
		"gold_paths_mrr" REAL,
		"generation_time" REAL,
		"samples" INT,

		filename VARCHAR,
		dirname VARCHAR,
		url VARCHAR,
		html_report_url VARCHAR
	);

	INSERT INTO augment_qa_prober SELECT
		to_timestamp( (r->'start_time')::int ),
		(r->'run_duration')::real / 60,
		(r->'limit')::int,

		(r#>'{metrics,answer_keyword_recall}')::real,
		(r#>'{metrics,retrievals_keyword_recall}')::real,
		(r#>'{metrics,gold_paths_recall}')::real,
		(r#>'{metrics,gold_paths_mrr}')::real,
		(r#>'{metrics,generation_time}')::real,
		(r#>'{metrics,samples}')::int,

		r->>'filename',
		regexp_replace(r->>'filename', '/[^/]*$', ''),
		'https://webserver.gcp-us1.r.augmentcode.com/eval-probers/$BASENAME/' || regexp_replace(r->>'filename', '/[^/]*$', '/'),
		'https://webserver.gcp-us1.r.augmentcode.com/eval-probers/$BASENAME/' || regexp_replace(r->>'filename', '_results.jsonl', '_report.html')

	FROM augment_qa_prober_tmp;

	COMMIT;
	EOF
}

sqlite_kill_and_fill() {
	declare -r db="$1"
	declare -r metrics_file="$2"

	log "Recreating sqlite db %s" "$db"

	TZ=US/Pacific sqlite3 "$db" <<-'EOF'
	DROP TABLE IF EXISTS results;
	CREATE TABLE results (
		start_time STRING PRIMARY KEY,
		duration REAL,
		`limit` INT,

		`answer_keyword_recall` REAL,
		`retrievals_keyword_recall` REAL,
		`gold_paths_recall` REAL,
		`gold_paths_mrr` REAL,
		`generation_time` REAL,
		`samples` INT,

		url TEXT
	);
	EOF

	log "Reloading sqlite db %s from %s" "$db" "$metrics_file"

	TZ=US/Pacific sqlite3 "$db" <<-EOF
	INSERT OR REPLACE INTO results SELECT
		DATETIME(value->'$.start_time', 'unixepoch', 'localtime'),
		CAST(value->'$.run_duration' / 60.0 AS REAL),
		value->'$.limit',

		value->'$.metrics.answer_keyword_recall',
		value->'$.metrics.retrievals_keyword_recall',
		value->'$.metrics.gold_paths_recall',
		value->'$.metrics.gold_paths_mrr',
		value->'$.metrics.generation_time',
		value->'$.metrics.samples',

		format('https://webserver.gcp-us1.r.augmentcode.com/eval-probers/$BASENAME/%s', value->>'$.filename')

	FROM json_each(readfile('$metrics_file'));
	EOF
}

regenerate_html_report() {
	declare -r html="$1"
	declare -r db="$2"

	log "Rebuilding HTML report %s" "$html"

	cat > "$html" <<-EOF
	<!DOCTYPE html>
	<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
		<title>AugmentQA Results</title>

		<style type="text/css">
		body {
			background-color: Canvas;
			color: CanvasText;
			color-scheme: light dark;
		}
		</style>
	</head>
	<body>
		<pre>$(sqlite3 --header --column "$db" 'SELECT * FROM results ORDER BY start_time ASC')</pre>
	</body>
	</html>
	EOF
}

main "$@"
