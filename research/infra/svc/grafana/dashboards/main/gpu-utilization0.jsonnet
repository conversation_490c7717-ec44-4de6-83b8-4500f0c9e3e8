{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON>ana --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        target: {
          limit: 100,
          matchAny: false,
          tags: [],
          type: 'dashboard',
        },
        type: 'dashboard',
      },
    ],
  },
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [],
  liveNow: false,
  panels: [
    {
      datasource: {
        default: false,
        type: 'datasource',
        uid: '-- Mixed --',
      },
      description: 'Utilization of reserved GPUs',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: 'GPU Count',
            axisPlacement: 'auto',
            axisSoftMin: 0,
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 16,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'dashed',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'green',
                value: 128,
              },
            ],
          },
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'kubernetes.io/container/accelerator/request',
            },
            properties: [
              {
                id: 'displayName',
                value: 'nvidia-h100-mega-80gb',
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 0,
      },
      id: 2,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.2.2',
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'cw-east4',
          },
          editorMode: 'code',
          expr: 'billing_gpu{label_gpu_nvidia_com_model=~"A100.*|H100.*", namespace="cw-east4"}',
          legendFormat: '{{label_gpu_nvidia_com_model}}',
          range: true,
          refId: 'A',
        },
        {
          aliasBy: '',
          datasource: {
            type: 'stackdriver',
            uid: 'gcp',
          },
          hide: false,
          queryType: 'timeSeriesList',
          refId: 'B',
          timeSeriesList: {
            alignmentPeriod: 'cloud-monitoring-auto',
            crossSeriesReducer: 'REDUCE_SUM',
            filters: [
              'metadata.user_labels."aug.gpu_type"',
              '=~',
              '.*h100.*',
              'AND',
              'metric.type',
              '=',
              'kubernetes.io/container/accelerator/request',
            ],
            groupBys: [],
            perSeriesAligner: 'ALIGN_MEAN',
            preprocessor: 'none',
            projectName: 'augment-research-gsc',
            view: 'FULL',
          },
        },
      ],
      title: 'A100/H100 GPU Usage',
      type: 'timeseries',
    },
    {
      datasource: {
        default: false,
        type: 'datasource',
        uid: '-- Mixed --',
      },
      description: 'Utilization of reserved GPUs',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: 'GPU Count',
            axisPlacement: 'auto',
            axisSoftMin: 0,
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 16,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'dashed',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'green',
                value: 128,
              },
            ],
          },
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'kubernetes.io/container/accelerator/request',
            },
            properties: [
              {
                id: 'displayName',
                value: 'nvidia-l4',
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 8,
      },
      id: 8,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.2.2',
      targets: [
        {
          aliasBy: '',
          datasource: {
            type: 'stackdriver',
            uid: 'gcp',
          },
          hide: false,
          queryType: 'timeSeriesList',
          refId: 'B',
          timeSeriesList: {
            alignmentPeriod: 'cloud-monitoring-auto',
            crossSeriesReducer: 'REDUCE_SUM',
            filters: [
              'metadata.user_labels."aug.gpu_type"',
              '=~',
              '.*l4.*',
              'AND',
              'metric.type',
              '=',
              'kubernetes.io/container/accelerator/request',
            ],
            groupBys: [],
            perSeriesAligner: 'ALIGN_MEAN',
            preprocessor: 'none',
            projectName: 'augment-research-gsc',
            view: 'FULL',
          },
        },
      ],
      title: 'L4 GPU Usage',
      type: 'timeseries',
    },
    {
      datasource: {
        name: 'CW-EAST-4 Prometheus',
        type: 'prometheus',
        uid: 'cw-east4',
      },
      description: 'Utilization of reserved GPUs',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'thresholds',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            axisSoftMin: 0,
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 13,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'normal',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          max: 1.1,
          min: 0,
          thresholds: {
            mode: 'percentage',
            steps: [
              {
                color: 'dark-red',
                value: null,
              },
              {
                color: 'red',
                value: 25,
              },
              {
                color: 'dark-orange',
                value: 50,
              },
              {
                color: 'dark-yellow',
                value: 75,
              },
              {
                color: 'dark-blue',
                value: 85,
              },
              {
                color: 'blue',
                value: 95,
              },
              {
                color: 'dark-green',
                value: 99,
              },
            ],
          },
          unit: 'percentunit',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 0,
        y: 16,
      },
      id: 6,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'cw-east4',
          },
          editorMode: 'code',
          expr: 'billing_gpu{label_gpu_nvidia_com_model=~"A100.*", namespace="cw-east4"}',
          legendFormat: '{{label_gpu_nvidia_com_model}}',
          range: true,
          refId: 'A',
        },
      ],
      title: 'CW A100 GPU usage efficiency',
      transformations: [
        {
          id: 'calculateField',
          options: {
            alias: 'All A100',
            mode: 'reduceRow',
            reduce: {
              include: [
                'A100_NVLINK_80GB',
                'A100_PCIE_40GB',
                'A100_PCIE_80GB',
              ],
              reducer: 'sum',
            },
            replaceFields: true,
          },
        },
        {
          id: 'calculateField',
          options: {
            alias: 'PCT Utilization',
            binary: {
              left: 'All A100',
              operator: '/',
              reducer: 'sum',
              right: '$A100_COUNT',
            },
            mode: 'binary',
            reduce: {
              reducer: 'sum',
            },
            replaceFields: true,
          },
        },
      ],
      type: 'timeseries',
    },
    {
      datasource: {
        default: false,
        type: 'prometheus',
        uid: 'cw-east4',
      },
      description: 'Utilization of reserved GPUs',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'thresholds',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            axisSoftMin: 0,
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 13,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'normal',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          max: 1.1,
          min: 0,
          thresholds: {
            mode: 'percentage',
            steps: [
              {
                color: 'dark-red',
                value: null,
              },
              {
                color: 'red',
                value: 25,
              },
              {
                color: 'dark-orange',
                value: 50,
              },
              {
                color: 'dark-yellow',
                value: 75,
              },
              {
                color: 'dark-blue',
                value: 85,
              },
              {
                color: 'blue',
                value: 95,
              },
              {
                color: 'dark-green',
                value: 99,
              },
            ],
          },
          unit: 'percentunit',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 12,
        y: 16,
      },
      id: 7,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'cw-east4',
          },
          editorMode: 'code',
          expr: 'billing_gpu{label_gpu_nvidia_com_model=~"H100.*", namespace="cw-east4"}',
          legendFormat: '{{label_gpu_nvidia_com_model}}',
          range: true,
          refId: 'A',
        },
      ],
      title: 'CW H100 GPU usage efficiency',
      transformations: [
        {
          id: 'calculateField',
          options: {
            alias: 'All H100',
            mode: 'reduceRow',
            reduce: {
              include: [],
              reducer: 'sum',
            },
            replaceFields: false,
          },
        },
        {
          id: 'calculateField',
          options: {
            alias: 'PCT Utilization',
            binary: {
              left: 'All H100',
              operator: '/',
              reducer: 'sum',
              right: '$H100_COUNT',
            },
            mode: 'binary',
            reduce: {
              reducer: 'sum',
            },
            replaceFields: true,
          },
        },
      ],
      type: 'timeseries',
    },
  ],
  refresh: '',
  schemaVersion: 39,
  tags: [],
  templating: {
    list: [
      {
        current: {
          selected: true,
          text: '144',
          value: '144',
        },
        hide: 0,
        name: 'A100_COUNT',
        options: [
          {
            selected: true,
            text: '144',
            value: '144',
          },
        ],
        query: '144',
        skipUrlSync: false,
        type: 'textbox',
      },
      {
        current: {
          selected: true,
          text: '512',
          value: '512',
        },
        hide: 0,
        name: 'H100_COUNT',
        options: [
          {
            selected: true,
            text: '512',
            value: '512',
          },
        ],
        query: '512',
        skipUrlSync: false,
        type: 'textbox',
      },
    ],
  },
  time: {
    from: 'now-7d',
    to: 'now',
  },
  timepicker: {},
  timezone: '',
  title: 'GPU Utilization',
  uid: 'gpu-utilization0',
  weekStart: '',
}
