package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"

	flag "github.com/spf13/pflag"

	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/research/infra/cfg/clusters"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/ctrl"
)

func main() {
	flags := struct {
		wetRun  bool
		noDiff  bool
		cluster string
	}{}
	flag.BoolVarP(&flags.wetRun, "wet-run", "w", false, "Wet run.")
	flag.BoolVar(&flags.noDiff, "no-diff", false, "Don't log diffs or avoid Apply() on no diff.")
	flag.StringVar(&flags.cluster, "cluster", "", "The current cluster name, autodetect if empty.")
	flog := logger.GlobalLoggerFromFlags(flag.CommandLine)

	flag.Parse()

	log, err := flog(nil)
	if err != nil {
		log.Fatal().Err(err).Send() // The nil logger still works on err.
	}

	// Handle SIGTERM
	ctx, cancel := context.WithCancelCause(context.Background())
	ch := make(chan os.Signal, 1)
	signal.Notify(ch, syscall.SIGTERM, syscall.SIGINT)
	go func() {
		sig := <-ch
		log.LogNotice("Caught %v.", sig)
		cancel(nil)
	}()

	db, err := clusters.New()
	if err != nil {
		log.LogErr("%v.", err)
		os.Exit(1)
	}
	cluster, err := func() (clusters.Cluster, error) {
		if flags.cluster == "" {
			return db.Current(ctx)
		}
		return db.Cluster(flags.cluster)
	}()
	if err != nil {
		log.LogErr("%v.", err)
		os.Exit(1)
	}

	c, err := ctrl.NewController(ctx, cluster, ctrl.WetRun(flags.wetRun), ctrl.NoDiff(flags.noDiff))
	if err != nil {
		log.LogErr("%v.", err)
		os.Exit(1)
	}
	if err := c.Run(ctx); err != nil {
		log.LogErr("%v", err)
		os.Exit(1)
	}
}
