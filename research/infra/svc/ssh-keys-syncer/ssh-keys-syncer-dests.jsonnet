local aug = import '../../../../infra/cfg/augment/augment-enums.jsonnet';
local clusters = import '../../cfg/clusters/clusters.jsonnet';

{
  //////////////////////////////////////////////////////////////////////////////
  //
  // Main configuration: ADD MORE DESTS HERE!
  //
  // This file is consumed by <PERSON><PERSON><PERSON>, a shell script, and the other *.jsonnet
  // in this dir. When updating this list, run:
  // `make ssh-keys-syncer.kubeconfig.sealed.json`
  //

  SourceName: aug.CW_AUTHORIZED_KEYS,
  Dests: [
    $.Dest + {
      Cluster: 'cw-east4',
      Namespace: 'aug-system',
      Name: null,
    },
    $.Dest + {
      Cluster: 'cw-east4',
      Name: null,
    },
    $.Dest + {
      Cluster: 'gcp-us1',
      Name: null,
    },
    $.Dest + {
      Cluster: 'gcp-us1',
      Name: null,
      Namespace: 'aug-system',
    },
  ],

  //////////////////////////////////////////////////////////////////////////////
  //
  // Command Line Args
  //

  SyncArgs: [$.SourceName] + $.Specs,
  Specs: [
    d.Spec
    for d in $.Dests
  ],

  //////////////////////////////////////////////////////////////////////////////
  //
  // Dest K8s: Stub ConfigMaps, receiving ServiceAccount, Role, RoleBinding.
  //

  DestClusterToK8s: {
    [c]: $.DestK8s(c)
    for c in $.DestClusters
  },

  DestK8s(c):: {
    local k = self,

    local dests = [d for d in $.Dests if d.Cluster == c],
    local dest0 = dests[0],

    local C = clusters.cluster(c),
    local k8s = C.k8s + {
      BaseLabels+:: {
        'aug.service': 'ssh-keys-syncer',
      },
      Object+:: {
        namespace:: dest0.Namespace,
      },
    },

    cms: [
      k8s.ConfigMap + {
        name:: d.EffectiveName,
        namespace:: d.Namespace,
      }
      for d in dests
    ],

    sa: k8s.ServiceAccount + {
      name:: 'ssh-keys-syncer',
      secrets: [
        { name: k.satok.metadata.name },
      ],
    },

    satok: k8s.Secret + {
      i_know_what_im_doing: true,
      name:: k.sa.metadata.name + '-tok',
      type: 'kubernetes.io/service-account-token',
      metadata+: {
        annotations+: {
          'kubernetes.io/service-account.name': k.sa.metadata.name,
        },
      },
    },

    roles_and_bindings: [
      {
        local rab = self,
        role: k8s.Role + {
          name:: 'ssh-keys-syncer',
          namespace:: namespace,
          rules: [
            {
              apiGroups: [''],
              resources: ['configmaps'],
              resourceNames: [cm.metadata.name for cm in k.cms if cm.metadata.namespace == namespace],
              verbs: k8s.WRITE_VERBS + k8s.READ_VERBS,
            },
          ],
        },
        rb: k8s.RoleBinding + {
          name:: 'ssh-keys-syncers',
          namespace:: namespace,
          role_name:: rab.role.metadata.name,
          sas:: [k.sa],
        },
      }
      for namespace in std.set([d.Namespace for d in dests])
    ],
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Misc Helpers
  //

  DestClusters: std.set([d.Cluster for d in $.Dests]),
  KubeConfigs: std.set([d.KubeConfig for d in $.Dests]),

  Dest:: {
    Cluster: error 'Dest.Cluster is required',
    Name: null,

    _c:: clusters.cluster(self.Cluster),

    Namespace: self._c.main_namespace,
    Context: self._c.context,
    SpecCluster: self.Context + (if self.Namespace != self._c.main_namespace then '-' + self.Namespace else ''),
    Spec: self.SpecCluster + (if self.Name != null then ':' + self.Name else ''),
    EffectiveName: if self.Name != null then self.Name else $.SourceName,

    KubeConfig: self.SpecCluster + ':' + self.Cluster + ':' + self.Namespace,
  },

}
