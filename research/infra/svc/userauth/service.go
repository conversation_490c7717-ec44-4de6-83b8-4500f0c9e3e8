package main

import (
	"context"
	"embed"
	"fmt"
	"html/template"
	"net/http"
	"os"
	"strings"

	"github.com/gorilla/sessions"
	"github.com/markbates/goth"
	"github.com/markbates/goth/gothic"
	"github.com/markbates/goth/providers/google"

	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/research/infra/lib/augment"
)

//go:embed *.html
var htmlFS embed.FS

//go:embed favicon.ico
var favicon []byte

type Service struct {
	*logger.Logger

	Port     int
	Hostname string

	AuthorizedKeysConfigMap string
	GitHubUserNameConfigMap string

	OauthLimitDomain       string
	OauthGoogleClientID    string // may be literal or path to file
	OauthGoogleClientSec   string // may be literal or path to file
	CookieStoreSessionSec  string // may be literal or path to file
	CookieStoreSessionName string

	store sessions.Store
	cli   augment.Clients
	tmpl  *template.Template
}

func (svc *Service) Run(ctx context.Context) error {
	readOrLiteral := func(flag string) (string, error) {
		if strings.HasPrefix(flag, "/") || strings.HasPrefix(flag, "./") {
			buf, err := os.ReadFile(flag)
			return string(buf), err
		}
		return flag, nil
	}

	/// Init: HTML Templates

	if t, err := template.ParseFS(htmlFS, "*.html"); err != nil {
		return err
	} else {
		svc.tmpl = t
	}

	/// Init: Augment Backends Service

	if cli, err := augment.NewClients(""); err != nil {
		return err
	} else {
		cli.SetAuthorizedKeysConfigMap(svc.AuthorizedKeysConfigMap)
		cli.SetGitHubUserNameConfigMap(svc.GitHubUserNameConfigMap)
		svc.cli = cli
	}

	/// Init: Cookie Store

	if secret, err := readOrLiteral(svc.CookieStoreSessionSec); err != nil {
		return err
	} else {
		store := sessions.NewCookieStore([]byte(secret))
		store.MaxAge(7 * 3600) // 7 hours
		store.Options.Path = "/"
		store.Options.HttpOnly = true
		store.Options.Secure = true

		svc.store = store
		gothic.Store = store // really?
	}

	/// Init: Oauth2

	if oID, err := readOrLiteral(svc.OauthGoogleClientID); err != nil {
		return err
	} else if oSec, err := readOrLiteral(svc.OauthGoogleClientSec); err != nil {
		return err
	} else {
		url := fmt.Sprintf("https://%s/auth/google/callback", svc.Hostname)
		goog := google.New(oID, oSec, url, "email", "profile")
		if d := svc.OauthLimitDomain; d != "" {
			goog.SetHostedDomain(d)
		}
		goth.UseProviders(goog)
	}

	/// Init: HTTP Routes

	mux := http.NewServeMux()
	h := func(pat string, f http.HandlerFunc) {
		mux.HandleFunc(pat, func(w http.ResponseWriter, r *http.Request) {
			svc.LogInfo("RECV FROM %s %s %s.", r.RemoteAddr, r.Method, r.RequestURI)
			// TODO(mattm): Send upstream support to gothic for PathValue
			if v := r.PathValue("provider"); v != "" {
				r = r.WithContext(context.WithValue(r.Context(), gothic.ProviderParamKey, v))
			}
			f(w, r)
		})
	}

	// API
	h("GET  /api/authorized_keys", svc.apiGetAuthorizedKeys)
	h("PUT  /api/authorized_keys", svc.apiSetAuthorizedKeys)
	h("POST /api/authorized_keys/set_from_github", svc.apiSetAuthorizedKeysFromGH)
	h("POST /api/authorized_keys/append_from_github", svc.apiAppendAuthorizedKeysFromGH)
	h("GET  /api/github_username", svc.apiGetGitHubUsername)
	h("PUT  /api/github_username", svc.apiSetGitHubUsername)
	h("GET  /api/user_details", svc.apiGetUserDetails)

	// Auth
	h("GET /auth/{provider}/callback", svc.hAuthCallback)
	h("GET /auth/{provider}", svc.hAuth)
	h("GET /logout/{provider}", svc.hAuthLogout)

	// UI
	h("GET /favicon.ico", svc.hFavicon)
	h("GET /", svc.hUI)

	/// Listen!

	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", svc.Port),
		Handler: mux,
	}
	svc.LogInfo("Listening on: %s.", srv.Addr)
	return srv.ListenAndServe()
}

func (svc Service) hUI(w http.ResponseWriter, r *http.Request) {
	if user, err := svc.getUser(r); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
	} else if err := user.IsValid(svc.OauthLimitDomain); err != nil {
		http.Redirect(w, r, "/auth/google", http.StatusTemporaryRedirect)
	} else if euser, err := svc.extendedUser(r.Context(), user); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
	} else {
		data := struct {
			Error string
			User  ExtendedUser
		}{
			Error: r.URL.Query().Get("error"),
			User:  euser,
		}
		if err := svc.tmpl.ExecuteTemplate(w, "index.html", data); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		}
	}
}

func (svc Service) hFavicon(w http.ResponseWriter, r *http.Request) {
	if _, err := w.Write(favicon); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
	} else {
		w.Header().Add("Content-Type", "image/x-icon")
	}
}
