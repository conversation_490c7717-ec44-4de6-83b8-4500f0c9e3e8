load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

# This is a private, standalone binary that was built before most of the infra lib materialized. We
# don't expect any imports, even from //research/infra:internal.
package(default_visibility = ["//visibility:private"])

go_library(
    name = "userauth_lib",
    srcs = [
        "service.go",
        "service-api.go",
        "service-auth.go",
        "service-backends.go",
        "service-user.go",
        "userauth.go",
    ],
    embedsrcs = [
        "favicon.ico",
        "index.html",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/svc/userauth",
    deps = [
        "//infra/lib/logger",
        "//research/infra/lib/augment",
        "@com_github_gorilla_sessions//:sessions",
        "@com_github_markbates_goth//:goth",
        "@com_github_markbates_goth//gothic",
        "@com_github_markbates_goth//providers/google",
        "@com_github_spf13_pflag//:pflag",
        "@org_golang_x_sync//errgroup",
    ],
)

go_binary(
    name = "userauth",
    embed = [":userauth_lib"],
)

go_test(
    name = "userauth_test",
    srcs = ["userauth_test.go"],
    embed = [":userauth_lib"],
)
