package cli

import (
	"fmt"
	"io"
	"os"
	"sort"
	"strings"

	"github.com/mattn/go-isatty"
	"github.com/spf13/cobra"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/infra/lib/utils"
	"github.com/augmentcode/augment/research/infra/cfg"
)

func init() {
	rootCmd.PersistentFlags().StringP("context", "K", "", "Kubeconfig context.")
	rootCmd.PersistentFlags().String("namespace", "", "Kubeconfig namespace. Uses default from context when empty. Some commands such as DevPods will ignore this.")
	addCommand(nil, nil, k8sRoot())
}

func getK8sOrDie() *k8s.Client {
	context, err := rootCmd.Flags().GetString("context")
	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to build K8s client: %v.", err)
		os.Exit(1)
	}
	ns, err := rootCmd.Flags().GetString("namespace")
	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to build K8s client: %v.", err)
		os.Exit(1)
	}

	k, err := k8s.New(context)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to build K8s client: %v.", err)
		os.Exit(1)
	}

	if ns != "" {
		k2 := k.InNamespace(ns)
		k = &k2
	}

	return k
}

func k8sRoot() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "k8s",
		Aliases: []string{"kubernetes"},
		Short:   "Commands for working with Kubernetes.",
	}

	addCommand(cmd, nil, k8sGetSecret())
	addCommand(cmd, nil, k8sGetConfigMap())
	addCommand(cmd, nil, k8sResolvePod())
	addCommand(cmd, nil, k8sSAtoConfig())
	addCommand(cmd, nil, k8sSync())
	addCommand(cmd, nil, k8sJSON())
	addCommand(cmd, nil, k8sToken())
	addCommand(cmd, nil, k8sSeal())
	addCommand(cmd, nil, k8sWhoAmI())
	addCommand(cmd, nil, k8sSchedDebug())
	addCommand(cmd, nil, k8sStartupTimes())

	return cmd
}

func k8sGetSecret() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "secret",
		Aliases: []string{"sec"},
		Short:   "Commands for getting, setting, and editing secrets",
	}
	cmdget := &cobra.Command{
		Use:   "get <name> [<key>]",
		Short: "Print plaintext (base64 decoded) secret, or individual value.",
		Args:  cobra.RangeArgs(1, 2),
		RunE: func(cmd *cobra.Command, args []string) error {
			secret, key := args[0], ""
			if len(args) == 2 {
				key = args[1]
			}

			k := getK8sOrDie()
			sec, err := k.GetSecret(cmd.Context(), secret)
			if err != nil {
				return err
			}

			if key == "" {
				for k, v := range sec.StringData() {
					fmt.Printf("%s: %s\n", k, v)
				}
			} else if val, err := sec.KeyErr(key); err != nil {
				return err
			} else {
				fmt.Println(val)
			}

			return nil
		},
	}
	cmdset := &cobra.Command{
		Use:   "set <name> <key>=[<value>]...",
		Short: "Sets (or drops) one or more keys from a Secret.",
		Args:  cobra.MinimumNArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			name, args := args[0], args[1:]

			updates := map[string]string{}
			for _, arg := range args {
				key, val, found := strings.Cut(arg, "=")
				if !found {
					return fmt.Errorf("%s: invalid arg, must have an '='", arg)
				}
				updates[key] = val
			}

			k := getK8sOrDie()

			sec, err := k.GetSecret(cmd.Context(), name)
			if err != nil {
				return err
			}

			_, err = sec.SetData(cmd.Context(), k, updates)
			return err
		},
	}
	cmdedit := &cobra.Command{
		Use:   "edit <name>",
		Short: "Edit the secret in $EDITOR (default vim).",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			name := args[0]

			k := getK8sOrDie()
			sec, err := k.GetSecret(cmd.Context(), name)
			if err != nil {
				return err
			}

			_, err = sec.Edit(cmd.Context(), k)
			return err
		},
	}

	addCommand(cmd, nil, cmdget)
	addCommand(cmd, nil, cmdset)
	addCommand(cmd, nil, cmdedit)
	return cmd
}

func k8sGetConfigMap() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "cm",
		Aliases: []string{"configmap"},
		Short:   "ConfigMap commands.",
	}
	cmdget := &cobra.Command{
		Use:   "get <name> [<key>]",
		Short: "Prints a ConfigMap, or individual value.",
		Args:  cobra.RangeArgs(1, 2),
		RunE: func(cmd *cobra.Command, args []string) error {
			name, key := args[0], ""
			if len(args) == 2 {
				key = args[1]
			}

			k := getK8sOrDie()

			cm, err := k.GetConfigMap(cmd.Context(), name)
			if err != nil {
				return err
			}

			if key == "" {
				for k, v := range cm.Data() {
					fmt.Printf("%s: %s\n", k, v)
				}
			} else {
				fmt.Println(cm.Key(key))
			}

			return nil
		},
	}
	cmdset := &cobra.Command{
		Use:   "set <name> <key>=[<value>]...",
		Short: "Sets (or drops) one or more keys from a ConfigMap.",
		Args:  cobra.MinimumNArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			verbose, err := cmd.Flags().GetBool("verbose")
			if err != nil {
				return err
			}

			name, args := args[0], args[1:]
			updates := map[string]string{}
			for _, arg := range args {
				key, val, found := strings.Cut(arg, "=")
				if !found {
					return fmt.Errorf("%s: invalid arg, must have an '='", arg)
				}
				updates[key] = val
			}

			k := getK8sOrDie()
			cm, err := k.GetConfigMap(cmd.Context(), name)
			if err != nil {
				return err
			}

			res, err := cm.SetData(cmd.Context(), k, updates)
			if err != nil {
				return err
			}

			if verbose {
				for k, v := range res.Data() {
					fmt.Printf("%s: %s\n", k, v)
				}
			}

			return nil
		},
	}
	cmdset.Flags().BoolP("verbose", "v", false, "Print the resulting ConfigMap data.")
	addCommand(cmd, nil, cmdget)
	addCommand(cmd, nil, cmdset)
	return cmd
}

func k8sResolvePod() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "resolve-pod <deployment>",
		Aliases: []string{"rp"},
		Short:   "Resolves a K8s Deployment to its current pod(s).",
		Args:    cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			one, err := cmd.Flags().GetBool("one")
			if err != nil {
				return err
			}
			name := args[0]

			k := getK8sOrDie()

			if one {
				pod, err := k.GetDeploymentPod(cmd.Context(), name)
				if err != nil {
					return err
				}
				fmt.Println(pod.Name())
			} else {
				d, err := k.GetDeployment(cmd.Context(), name)
				if err != nil {
					return err
				}
				pods, err := d.GetPods(cmd.Context(), k)
				if err != nil {
					return err
				}
				for _, pod := range pods {
					fmt.Println(pod.Name())
				}
			}
			return nil
		},
	}
	cmd.Flags().BoolP("one", "1", false, "Assert exactly 1 pod is resolved.")
	return cmd
}

func k8sSAtoConfig() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "sa-to-config [--in-cluster|--external] [-e seconds] [--namespace=<ns>] <name>",
		Short: "Convert a Service Account to a KubeConfig. Defaults to --external.",
		Args:  cobra.ExactArgs(1),
	}

	flags := struct {
		inCluster bool
		external  bool
		ctxName   string
		namespace string
		exp       int64
	}{}
	cmd.Flags().BoolVar(&flags.inCluster, "in-cluster", false, "Generate a config appropriate for in-cluster usage.")
	cmd.Flags().BoolVar(&flags.external, "external", false, "Generate a config appropriate for cluster-external usage.")
	cmd.Flags().StringVarP(&flags.ctxName, "context-name", "c", "", "The name of the generated context, defaults to the ServiceAccount name.")
	cmd.Flags().StringVarP(&flags.namespace, "namespace", "n", "", "Set the namespace in the kubeconfig context, defaults to the namespace of the SA.")
	cmd.Flags().Int64VarP(&flags.exp, "duration", "e", 0, "An expiration for the TokenRequest API.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		name := args[0]
		if flags.inCluster && flags.external {
			return fmt.Errorf("--in-cluster and --external are mutually exlusive")
		} else if !flags.inCluster && !flags.external {
			flags.external = true
		}

		k := getK8sOrDie()

		cfg, err := func() (*k8s.KubeConfig, error) {
			if flags.external {
				return k.KubeConfigFromServiceAccountExternal(cmd.Context(), name, flags.ctxName, flags.namespace)
			} else if flags.exp == 0 {
				return k.KubeConfigFromServiceAccount(cmd.Context(), name, flags.ctxName, flags.namespace)
			} else {
				return k.KubeConfigFromTokenRequest(cmd.Context(), name, flags.ctxName, flags.namespace, flags.exp)
			}
		}()
		if err != nil {
			return err
		}
		fmt.Print(cfg.String())

		return nil
	}
	return cmd
}

func k8sSync() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "sync [--daemon] <type/name> <dest[:name]>...",
		Short: "Syncs K8s object(s) to one or more destinations.",
		Args:  cobra.MinimumNArgs(2),
	}

	flags := struct {
		daemon  bool
		dryRun  bool
		kubecfg string
	}{}
	cmd.Flags().BoolVarP(&flags.daemon, "daemon", "d", false, "Run continuously.")
	cmd.Flags().BoolVarP(&flags.dryRun, "dry-run", "n", false, "Only print diffs.")
	cmd.Flags().StringVarP(&flags.kubecfg, "kubeconfig-base", "B", "", "If present, read dest kubeconfigs from <base>/<dest>.yaml.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		name, dests := args[0], args[1:]
		k := getK8sOrDie()
		if flags.daemon {
			return k.SyncRun(cmd.Context(), name, flags.kubecfg, flags.dryRun, dests...)
		} else {
			return k.Sync(cmd.Context(), name, flags.kubecfg, flags.dryRun, dests...)
		}
	}
	return cmd
}

func k8sJSON() *cobra.Command {
	cmd := &cobra.Command{
		Use:    "json <path>",
		Short:  "Generate objects from EMBEDDED jsonnet. (for internal debugging)",
		Args:   cobra.ExactArgs(1),
		Hidden: true,
	}
	flags := struct {
		tla map[string]string
	}{}
	cmd.Flags().StringToStringVarP(&flags.tla, "tla", "a", nil, "TLA Code arguments.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		path := args[0]
		vm, err := cfg.NewVM()
		if err != nil {
			return err
		}
		objs, err := vm.EvalK8s(path, flags.tla, "")
		if err != nil {
			return err
		}
		for _, o := range objs {
			fmt.Printf("--- %s/%s.%s\n", o.ShortKind(), o.Namespace(), o.Name())
			if y, err := o.YAML(); err != nil {
				fmt.Println(err)
			} else {
				fmt.Print(y)
			}
			fmt.Println()
		}
		return nil
	}
	return cmd
}

func k8sToken() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "token <service-account> [-e <seconds>] [-v|--verbose]",
		Aliases: []string{"tok"},
		Short:   "Get ServiceAccount token using the TokenRequest API.",
		Args:    cobra.ExactArgs(1),
	}
	flags := struct {
		exp     int64
		verbose bool
	}{}
	cmd.Flags().Int64VarP(&flags.exp, "duration", "e", 3600, "Expiration duration (seconds) for the token.")
	cmd.Flags().BoolVarP(&flags.verbose, "verbose", "v", false, "Print details.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		name := args[0]
		k := getK8sOrDie()
		req, err := k.CreateToken(cmd.Context(), name, flags.exp)
		if err != nil {
			return err
		}
		fmt.Println(req.Token())
		if flags.verbose {
			fmt.Println()
			fmt.Println("Expiration Time:", req.ExpirationTime())
			if aud := req.Raw().Spec.Audiences; len(aud) == 0 {
				fmt.Println("Audiences: <none>")
			} else {
				fmt.Println("Audiences:")
				for _, a := range aud {
					fmt.Println(" -", a)
				}
			}
			if hdr, payload, err := req.JWTHeaderAndPayloadUnverified(); err != nil {
				return err
			} else {
				fmt.Println("JWT Header:", hdr)
				fmt.Println("JWT Payload:", payload)
			}
		}
		return nil
	}
	return cmd
}

func k8sSeal() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "seal <namespace>|- <name> [[.]/<file>|-|<data>]",
		Aliases: []string{},
		Short:   "Seal raw data, a JSON Secret, or YAML secret.",
		Args:    cobra.RangeArgs(2, 3),
		Long: strings.Join([]string{
			"This subcommand is a variation of the official Bitnami kubeseal cli. It has \"smart\"",
			"support for triming whitespace, automatic SealingScope (Strict, Namespace, or Cluster)",
			"detection, and automation raw/json/yaml detection.",
			"",
			"Sealed Secrets are scoped to a specific Namespace+Name (Strict); to any name in a Namespace;",
			"or to any name or namespace in a Cluster.",
			"",
			"Positional Args:",
			"  <namespace>:   May be '-' to use the kubeconfig default. May be empty '' for Cluster SealingScope.",
			"  <name>:        May be empty '' for Namespace or Cluster SealingScope.",
			"  <file>/<data>: If staring with './' or '/' treat as a file; if empty or '-' treat as stdin; otherwise treat as literal data.",
			"",
			"Flags:",
			"  --newline|-N   When taking input from stdin and the input is detected to be a single line, *don't*strip whitespace.",
			"",
			"When output is detected to be a TTY, an additional newline is added.",
			"",
		}, "\n"),
	}
	flags := struct {
		newline bool
	}{}
	cmd.Flags().BoolVarP(&flags.newline, "newline", "N", false, "Only for stdin. Avoid stripping newlines when only a single line of data is detected.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		/// Step 1: Get data.

		data, err := func() ([]byte, error) {
			arg3 := ""
			if len(args) == 3 {
				arg3 = args[2]
			}

			if arg3 == "" || arg3 == "-" {
				buf, err := io.ReadAll(os.Stdin)
				if err != nil {
					return nil, err
				}
				if !flags.newline {
					// Try to be "smart" about when we want to strip trailing newlines from the input.
					buf = utils.TrimOneLine(buf)
				}
				return buf, nil
			} else if strings.HasPrefix(arg3, "/") || strings.HasPrefix(arg3, "./") {
				return os.ReadFile(arg3)
			} else {
				return []byte(arg3), nil
			}
		}()
		if err != nil {
			return err
		}

		/// Step 2: Seal data (auto-detect raw data vs k8s Secret JSON vs k8s Secret YAML)

		k := getK8sOrDie()
		namespace, name := args[0], args[1]
		if namespace == "-" {
			if ns := k.Namespace(); ns == "" {
				return fmt.Errorf("no default namespace found for k8s client")
			} else {
				namespace = ns
			}
		}

		maybeSec := k8s.NewSecret(nil)
		if err := maybeSec.FromJSON(string(data)); err == nil && maybeSec.Name() != "" {
			if ssec, err := k.SealSecret(cmd.Context(), *maybeSec); err != nil {
				return err
			} else if s, err := ssec.JSON(); err != nil {
				return err
			} else {
				fmt.Print(s)
				return nil
			}
		} else if err := maybeSec.FromYAML(string(data)); err == nil && maybeSec.Name() != "" {
			if ssec, err := k.SealSecret(cmd.Context(), *maybeSec); err != nil {
				return err
			} else if s, err := ssec.YAML(); err != nil {
				return err
			} else {
				fmt.Print(s)
				return nil
			}
		} else {
			sdata, err := k.Seal(cmd.Context(), namespace, name, data)
			if err != nil {
				return err
			}
			if isatty.IsTerminal(os.Stdout.Fd()) {
				fmt.Println(string(sdata))
			} else {
				fmt.Print(string(sdata))
			}
			return nil
		}
	}
	return cmd
}

func k8sWhoAmI() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "whoami",
		Aliases: []string{},
		Short:   "Print current user information.",
		Args:    cobra.NoArgs,
	}
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		k := getK8sOrDie()
		ssr, err := k.WhoAmI(cmd.Context())
		if err != nil {
			return err
		}
		fmt.Println(ssr.UserInfoString())
		return nil
	}
	return cmd
}

func k8sSchedDebug() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "sched-debug <pod> [node]",
		Aliases: []string{},
		Short:   "Debug a pod against nodes in the cluster.",
		Args:    cobra.RangeArgs(1, 2),
	}
	flags := struct {
		tol bool
		sel bool
	}{}
	cmd.Flags().BoolVarP(&flags.tol, "tolerations", "T", false, "Don't skip analysis when tolerations don't match.")
	cmd.Flags().BoolVarP(&flags.sel, "selectors", "S", false, "Don't skip analysis when selectors don't match.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		k := getK8sOrDie()
		podName := strings.TrimPrefix(args[0], "pod/")
		nodeName := func() string {
			if len(args) >= 2 {
				return strings.TrimPrefix(strings.TrimPrefix(args[1], "no/"), "node/")
			}
			return ""
		}()

		/// Get the requested pod.

		pod, err := k.GetPod(cmd.Context(), podName)
		if err != nil {
			return err
		}

		/// Get all nodes (and pods), with usage tallied.

		nodes, allpods, err := k.ListNodesWithUsage(cmd.Context())
		if err != nil {
			return err
		}
		sort.Slice(nodes, func(i, j int) bool {
			return nodes[i].ShortName() < nodes[j].ShortName()
		})

		/// Print a one-line summary for each node.

		for _, n := range nodes {

			/// Check tolerations.

			t := n.Tolerates(pod)
			if !t && !flags.tol {
				continue
			}

			/// Check selectors.

			s, err := n.Selects(pod)
			if err != nil {
				k.LogErr("Error checking if %s selects %s: %v.", pod.ShortName(), n.ShortName(), err)
			}
			if !s && !flags.sel {
				continue
			}

			/// Check fit and summarize resource details for those that don't fit.

			f, _, nofits := n.Fits(pod)
			req, avail, total := pod.Requests(), n.Available(), n.Allocatable()
			details := []string{}
			for t := range nofits {
				// negative, requested, available, allocatable
				details = append(details, fmt.Sprintf("%s=%s/%s/%s/%s", t,
					k8s.Humanize(nofits[t]),
					k8s.Humanize(req[t]),
					k8s.Humanize(avail[t]),
					k8s.Humanize(total[t]),
				))
			}

			/// Print

			fmt.Printf("%s   tol=%t   sel=%t   fit=%t: %s\n", n.ShortName(), t, s, f, strings.Join(details, ";"))

			/// Add per-pod lines for missed resources when a node is specified.

			if n.Name() == nodeName {
				for _, p := range allpods {
					if p.NodeName() != nodeName {
						continue
					}
					details := []string{}
					for t := range nofits {
						pq := p.Requests()[t]
						if !pq.IsZero() {
							details = append(details, fmt.Sprintf("%s=%s", t, k8s.Humanize(pq)))
						}
					}
					if len(details) == 0 {
						continue
					}
					fmt.Printf("\t%s: %s\n", p.ShortName(), strings.Join(details, ";"))
				}
			}
		}

		return nil
	}
	return cmd
}

func k8sStartupTimes() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "startup-time <pod>",
		Aliases: []string{},
		Short:   "Debug pod startup times.",
		Args:    cobra.ExactArgs(1),
	}
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		podName := strings.TrimPrefix(args[0], "pod/")
		k := getK8sOrDie()

		pod, err := k.GetPod(cmd.Context(), podName)
		if err := k8s.NotFoundOK(err); err != nil {
			return err
		}
		evs, err := k.ListEventsFor(cmd.Context(), "Pod", podName)
		if err != nil {
			return err
		}
		evs = evs.MostRecent()

		if len(evs) == 0 {
			return fmt.Errorf("no events found for %s", podName)
		}
		if pod != nil {
			// Insert a fake event representing the pod creation.
			evs = append(k8s.EventList{k8s.NewCreationEvent(pod.Object)}, evs...)
		}

		first, last := evs[0].Timestamp(), evs[0].Timestamp()
		for _, ev := range evs {
			d0 := ev.Timestamp().Sub(first)
			dn := ev.Timestamp().Sub(last)
			fmt.Printf("%v %v %v %s %s\n", d0, dn, ev.Timestamp(), ev.Raw().Reason, ev.Raw().Message)
			last = ev.Timestamp()
		}
		return nil
	}
	return cmd
}
