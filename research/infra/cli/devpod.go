package cli

import (
	"fmt"
	"os"
	"os/exec"
	"regexp"
	"strconv"
	"strings"

	"github.com/spf13/cobra"
	"k8s.io/apimachinery/pkg/api/resource"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/research/infra/cfg/clusters"
	"github.com/augmentcode/augment/research/infra/lib/augment"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/crd/devpodv1"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/spec"
)

func init() {
	addCommand(nil, nil, devpodRoot())
}

// dpGetClusterAndClients gets a Cluster definition and backend Clients. Typically, in this CLI
// we get the K8s context from the --context flag. However, Cluster also contains a default context.
// We support both by starting with the Cluster's definition, then only use --context if it's
// not empty.
func dpGetClusterAndClients(cmd *cobra.Command) (*clusters.Cluster, augment.Clients, error) {
	cluster, err := clustersGetCluster(cmd)
	if err != nil {
		return nil, augment.Clients{}, err
	}
	if cur, err := cmd.Flags().GetString("context"); err != nil {
		return nil, augment.Clients{}, err
	} else if cur == "" {
		if err := cmd.Flags().Set("context", cluster.Context); err != nil {
			return nil, augment.Clients{}, err
		}
	}
	cli := getClientsOrDie(cmd)
	cli.K8s().SetDefaultFieldManager("augi")
	return &cluster, cli, nil
}

func dpGetUserName(cmd *cobra.Command) (string, error) {
	if u, err := cmd.Flags().GetString("user"); err != nil {
		return "", err
	} else if u != "" {
		return u, nil
	}
	return augment.WhoAmIErr()
}

func dpGetUserAndDevPodNames(cmd *cobra.Command, arg string) (string, string, error) {
	user, err := dpGetUserName(cmd)
	if err != nil {
		return "", "", err
	}

	// Map '.' to the current short hostname
	if arg == "." {
		if hn, err := os.Hostname(); err != nil {
			return "", "", err
		} else {
			short, _, _ := strings.Cut(hn, ".")
			arg = short
		}
	} else {
		// Protect against "{user}-{user}foo".
		if strings.HasPrefix(arg, user) && !strings.HasPrefix(arg, user+"-") {
			return "", "", fmt.Errorf("pod_name must start with '%s-'.", user)
		}

		// Auto-prepend "{user}-".
		if autoPrefix, err := cmd.Flags().GetBool("auto-user-prefix"); err != nil {
			return "", "", err
		} else if autoPrefix && !strings.HasPrefix(arg, user+"-") {
			arg = user + "-" + arg
		}
	}

	// Enforce "{user}-" prefix.
	if !strings.HasPrefix(arg, user+"-") {
		return "", "", fmt.Errorf("pod_name '%s' must start with '%s-'.", arg, user)
	}

	// Pre-validate DNS naming
	// https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#dns-subdomain-names
	// Impose a slightly shorter limit. With the "user-" requirement we don't need to include
	// very short 1 and 2 char names.
	if b, err := regexp.MatchString(`^[a-z0-9][a-z0-9.-]{0,30}[a-z0-9]$`, arg); err != nil {
		return "", "", err
	} else if !b {
		return "", "", fmt.Errorf("pod_name '%s' must be a valid DNS subdomain: lowercase, alphanumeric, '.', '-'.", arg)
	}

	return user, arg, nil
}

func dpGetDevPod(cmd *cobra.Command, arg string) (*devpod.Manager, error) {
	user, name, err := dpGetUserAndDevPodNames(cmd, arg)
	if err != nil {
		return nil, err
	}
	c, cli, err := dpGetClusterAndClients(cmd)
	if err != nil {
		return nil, err
	}
	k := cli.K8s()
	return devpod.NewManager(cmd.Context(), k, c, user, name)
}

func maybePrintNodeTypes(cmd *cobra.Command) (bool, error) {
	gv, gerr := cmd.Flags().GetString("gpu-type")
	cv, cerr := cmd.Flags().GetString("cpu-type")
	// Continue on if either value is "list" or there are flag errors.
	if gv != "list" && cv != "list" && gerr == nil && cerr == nil {
		return false, nil
	}

	c, err := clustersGetCluster(cmd)
	if err != nil {
		return false, err
	}
	fmt.Println("gpus:")
	c.DumpNodes(" - ", true, false)
	fmt.Println()
	fmt.Println("cpus:")
	c.DumpNodes(" - ", false, true)
	fmt.Println()
	return true, nil
}

func devpodRoot() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "devpod",
		Aliases: []string{"dp", "pod"},
		Short:   "Commands for working with DevPods (launch_pod.py replacement).",
		Long: strings.Join([]string{
			"Almost all commands take <name> as the first positional argument. The name\n",
			"MUST be of the form <username>-<subname>. If <username> cannot be determined,\n",
			"an error will be returned. If the name is '.', the current hostname is used.\n",
		}, ""),
	}
	cmd.PersistentFlags().StringP("user", "u", augment.WhoAmI(), "The @augmentcode.com (without the domain) user to operate on.")
	cmd.PersistentFlags().StringP("cluster", "c", "", "The cluster (aka provider) for commands that take one. Defaults to autodetect.")
	cmd.PersistentFlags().Bool("auto-user-prefix", false, "Auto-prepend <user> to <name> when not already included.")
	cmd.PersistentFlags().MarkHidden("auto-user-prefix")

	gbasic := &cobra.Group{ID: "devpod_basic", Title: "Basic Lifecycle Commands:"}
	gpower := &cobra.Group{ID: "devpod_power", Title: "Power (On/Off) Commands:"}
	gconn := &cobra.Group{ID: "dev_conn", Title: "Connectivity Commands:"}
	gres := &cobra.Group{ID: "devpod_res", Title: "Update Commands:"}
	ghome := &cobra.Group{ID: "devpod_home", Title: "Home Directory Commands:"}
	gdot := &cobra.Group{ID: "devpod_dot", Title: "Dotfiles Commands:"}

	cmd.AddGroup(gbasic, gpower, gconn, gres, ghome, gdot)

	addCommand(cmd, gbasic, devpodList())
	addCommand(cmd, gbasic, devpodBootstrap(), ADMIN)
	addCommand(cmd, gbasic, devpodCreate())
	addCommand(cmd, gbasic, devpodUpdate())
	addCommand(cmd, gbasic, devpodDelete())
	addCommand(cmd, gbasic, devpodStatus())
	addCommand(cmd, gbasic, devpodLogs())
	addCommand(cmd, gbasic, devpodAugi())

	addCommand(cmd, gpower, devpodOn())
	addCommand(cmd, gpower, devpodOff())
	addCommand(cmd, gpower, devpodReboot())

	addCommand(cmd, gconn, devpodExposePort())
	addCommand(cmd, gconn, devpodHidePort())
	addCommand(cmd, gconn, devpodSSH())
	addCommand(cmd, gconn, devpodExec())

	addCommand(cmd, gres, devpodUpdateImage())
	addCommand(cmd, gres, devpodUpdateInit())
	addCommand(cmd, gres, devpodResize())
	addCommand(cmd, gres, devpodAttachVolume())
	addCommand(cmd, gres, devpodDetachVolume())
	addCommand(cmd, gres, devpodEdit())

	addCommand(cmd, ghome, devpodGrowHome())
	addCommand(cmd, ghome, devpodGoHome())

	addCommand(cmd, gdot, devpodDotDiff())
	addCommand(cmd, gdot, devpodDotApply())
	addCommand(cmd, gdot, devpodDotEdit())
	addCommand(cmd, gdot, devpodDotLs())
	addCommand(cmd, gdot, devpodDotUpdate())
	addCommand(cmd, gdot, devpodDotCreate())

	return cmd
}

func devpodList() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "list [--one-cluster|-O]",
		Aliases: []string{"ls"},
		Args:    cobra.NoArgs,
		Short:   "List your own, or all DevPods.",
	}

	flags := struct {
		one      bool
		all      bool
		names    bool
		verbose  bool
		augiPath bool
	}{}
	cmd.Flags().BoolVarP(&flags.one, "one-cluster", "O", false, "Only list one cluster. By default lists from all prod clusters if --cluster isn't set.")
	cmd.Flags().BoolVarP(&flags.all, "all", "a", false, "List all users' DevPods, not just your own.")
	cmd.Flags().BoolVarP(&flags.names, "names-only", "n", false, "List just the DevPod names, not a short status line.")
	cmd.Flags().BoolVarP(&flags.verbose, "verbose", "v", false, "List full status details for each DevPod.")
	cmd.Flags().BoolVar(&flags.augiPath, "augi", false, "Resolve the augi-path (version).")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		if flags.names && flags.verbose {
			return fmt.Errorf("--names-only (-n) and --verbose (-v) are mutually exclusive")
		}
		if flags.names && flags.augiPath {
			return fmt.Errorf("--names-only (-n) and --augi are mutually exclusive")
		}

		user := ""
		if !flags.all {
			var err error
			if user, err = dpGetUserName(cmd); err != nil {
				return err
			}
		}

		var sets devpod.Sets
		if flags.one || cmd.Flags().Changed("cluster") {
			c, cli, err := dpGetClusterAndClients(cmd)
			if err != nil {
				return err
			}

			sets, err = devpod.List(cmd.Context(), cli.K8s(), c, user, flags.augiPath)
			if err != nil {
				return err
			}
		} else {
			db, err := clusters.New()
			if err != nil {
				return err
			}
			sets, err = devpod.ListMulti(cmd.Context(), user, flags.augiPath, db.Prod().Available().List()...)
			if err != nil {
				return err
			}
		}

		if flags.names {
			for _, set := range sets {
				fmt.Println(set.Name())
			}
		} else if flags.verbose {
			fmt.Print(sets.Status())
		} else {
			fmt.Print(sets.Table("", flags.augiPath))
		}

		return nil
	}

	return cmd
}

func devpodBootstrap() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "bootstrap [--force]",
		Aliases: []string{},
		Args:    cobra.NoArgs,
		Short:   "Bootstrap a CPU-only devpod/<user>-homepod.",
	}
	flags := struct {
		force bool
	}{}
	cmd.Flags().BoolVarP(&flags.force, "force", "f", false, "Force when devpod/<user>-homepod already exists.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		/// Basic Info from CLI: Force the name "homepod".

		cmd.Flags().Set("auto-user-prefix", "true")
		user, name, err := dpGetUserAndDevPodNames(cmd, "homepod")
		if err != nil {
			return err
		}
		c, cli, err := dpGetClusterAndClients(cmd)
		if err != nil {
			return err
		}

		/// Checkfail on user's SSH keys.

		if cm, err := cli.K8s().GetConfigMap(cmd.Context(), augment.AuthorizedKeysConfigMap); err != nil {
			return err
		} else if keys := cm.Key(user); keys == "" {
			return fmt.Errorf("no SSH keys found for user %q: please register at https://userauth.r.augmentcode.com", user)
		}

		/// Create bootstrap Spec, Builder, Manager (vanilla 4CPU)

		spec := devpodv1.NewDevPodFromNames(c.Name, user, name, c.MainNamespace)
		spec.DevPod().Resources.CPU = resource.NewQuantity(4, resource.DecimalSI)
		mgr := devpod.NewManagerFromSpec(cli.K8s(), c, spec)
		mgr.Builder().SkipDot = true

		if err := mgr.Spec().Validate(); err != nil {
			return err
		}

		/// Check for existing, or --force.

		if cur, err := devpod.FindSpec(cmd.Context(), cli.K8s(), c, user, name); k8s.NotFoundOK(err) != nil {
			return err
		} else if cur != nil && !flags.force {
			return fmt.Errorf("devpod/%s already exists, use the --force flag to continue anyway", cur.Name())
		} else if cur != nil && flags.force {
			mgr.LogWarn("devpod/%s already exists, but --force was used so it will first be deleted.", cur.Name())
			if err := mgr.Delete(cmd.Context(), false); err != nil {
				return err
			}
		}

		/// Build the node from the DevPodSpec.

		if node, err := mgr.Spec().DevPod().Node(mgr.Builder().Cluster()); err != nil {
			return err
		} else {
			mgr.Builder().SetNode(node)
		}

		/// Build (all objects).

		set, err := mgr.BuildSet(cmd.Context(), nil)
		if err != nil {
			return err
		}

		/// Apply (all objects).

		if err := mgr.Apply(cmd.Context(), set, false); err != nil {
			return err
		}

		mgr.LogInfo("devpod/%s bootstrapped. Log in and run `augi devpod apply . [--flags]` to further customize it.", spec.Name())

		/// Wait ready.

		if set, err := mgr.WaitReady(cmd.Context(), true); err != nil {
			return err
		} else {
			fmt.Fprint(cmd.OutOrStdout(), set.Status())
		}

		return nil
	}

	return cmd
}

func devpodCreate() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "apply <name> [--dry-run|--diff|--vimdiff] [--print-node-details] [flags...]",
		Aliases: []string{"create", "replace"},
		Args:    cobra.ExactArgs(1),
		Short:   "Apply (Create or Modify) a DevPod.",
	}
	cmd.SetUsageTemplate(string([]byte{0}))

	dedent := func(text string) string {
		text = strings.TrimLeft(text, "\n")
		lines := strings.Split(text, "\n")
		for i := range lines {
			lines[i] = strings.TrimPrefix(lines[i], "\t")
		}
		return strings.Join(lines, "\n")
	}
	cmd.Long = dedent(`
	augi devpod apply -- Create/Modify a personal Augment K8s DevPod

	A DevPod is specified by a K8s Custom Resource Definition (CRD), which is then used to generate the other K8s
	resources which make it up (Deployment, Pod, Service, PersistentVolumeClaims, etc). The flags below modify the
	DevPod CRD before applying it; this includes new DevPods which can be thought of as an empty/default CRD.

	A DevPod can be a long-lived personal development environment, or a scratch throwaway environment holding extra
	GPU resources. It can be deployed to GCP or CoreWeave. By default DevPods are SSH-accessible from your Augment
	laptop, have a persistent home directory, and use an Augment GPU-specific or CPU-specific base image. A DevPod
	*Deployment* is persistent but the *Pod* itself my restart from time to time due to K8s administrative events.

	USAGE
		augi devpod apply  <name> [--dry-run] [--print-node-details] [--flags...]
		augi devpod create <name> [--dry-run] [--print-node-details] [--flags...]

		The 'create' and 'apply' aliases are identical except that when called as 'create' an error is raised if
		the DevPod already exists.

		--print-node-details, -P  Print a summary of resources being requested and exit.
		--dry-run, -n             Only print the DevPod Spec and K8s objects, don't apply updates.
		--diff --vimdiff          Show a diff or run vimdiff and (implies --dry-run).

		--allow-reboot            Required to acknowledge when DevPod might reboot.
		--no-wait                 Don't wait for DevPod to be ready.
		--ssh                     Automatically SSH into DevPod when it's ready (cannot be used with --no-wait).
		--power-on --power-off    By default DevPods are created powerd on and retain their current state when modified.

	NAMING
		The <name> argument must begin with "<user>-". If the <user> cannot be detected automatically, the --user flag
		will be required. Note that the K8s Deployment will be named <name>, but K8s Pod will have a random suffix
		appended to it by K8s.

		<name>        The basename of the DevPod and all its K8s resources.
		--user=<user> The @augmentcode.com username of the DevPod. Normally detected automatically.
		--namespaced  Create DevPod in the "aug-user-<user>" namespace [future default].

	IMAGE
		The default DevPod image is either "augment_devpod_gpu:main" or "augment_devpod_cpu:main" (depending on the
		requested resources). The latest ":main" will be pulled every time the DevPod (re)boots. Pinning to a specific
		version is possible (see --image-tag).

		Custom images are supported, best-effort; Debian derivatives such as Ubuntu work best.

		--custom-image=<image>  Override the default, either for an entirely separate image, or to select an older version.
		--image-tag=<tag>       Override the image tag (e.g., to select an older version temporarily).
		--init-spec=<spec>      [advanced] Customize the DevPod's "init.sh" entrypoint.

	VOLUMES
		The DevPod has a number of shared storage volumes by default, such as "aug-cw-las1" mounted to "/mnt/efs/augment".
		Custom volumes can also be attached. See also the "attach-volume" and "detach-volume" subcommands.

		--vol-exclude-defaults    Exclude ALL default volumes defined for the cluster.
		--vol-exclude <name>...   Exclude individual volumes, by name. (May be specified multiple times.)
		--vols=<name:path>...     One or more PVC_NAME:MOUNT_PATH pairs. This format is limited to PVCs, the DevPod Spec
		                          format is more flexible.

	HOMEDIR
		By default, the DevPod will have a *persistent* home directory which will survive Pod restarts and can be moved
		from Pod to Pod. The --home-class flag can be used to set an "ephemeral" home directory which only lives
		on the node's scratch storage; it can be deleted unexpectedly at any time (e.g., due to machine failure).
		NOTE: The --home-class and --home-mode flag values can be confusing and are cluster-dependant. Reach out for
		help if you have questions.

		Dotfiles from your local home directory will be copied into the DevPod's K8s ConfigMap. A new home directory
		will have these dotfiles copied in. To re-apply these dotfiles, remove the ~/..launch_pod_dotfiles_unpacked file
		from within the pod. Note the ~/.config/pod-init.d/*.sh dotfiles can be included to run automatically when the
		pod starts, e.g. to install custom packages.

		--home-class="ephemeral"|"local"  Don't use a persistent home directory. Instead, use ephemeral storage on the host.
		--home-class=<class>              Set the K8s StorageClass to request for the Home Directory PersistentVolumeClaim. This
		                                  value controls the performance and storage characteristics of the PVC. Block-based classes
		                                  can only be used by one DevPod at a time. Filesystem-based classes can be shared across
		                                  multiple DevPods simultaneously.  Valid values are cluster-specific.

		                                   - CoreWeave: "shared-vast" (default), ...
		                                   - GCP: "premium-rwo" (default), "enterprise-multishare-rwx-vpc0", ...

		--home-mode="rwo"|"rwx"           The PVC access mode. "rwo" (default) is for ReadWriteOnce and is valid for both block-based
		                                  and filesystem-based storage. "rwx" is for ReadWriteMany and is only valid for
		                                  filesystem-based storage.

		--home-size=<quantity>            Defaults to 256Gi. Can be grown later, but not shrunk.
		--home-name=<name>                Override the default home directory name. This is useful for creating a homedir which
		                                  isn't specific to any one DevPod.
		--dotfiles=<files>                Additional dotfiles to copy from your local system along with the default set of dotfiles.
		--skip-dotfiles                   Don't handle dotfiles. This is enabled by default when the --user flag overrides the caller.

	OPERATING SYSTEM
		These settings apply to the operating system during bootup of the pod, and may be ignored if using a custom image.

		--shell=<shell>       Set your user's shell. The default is /bin/bash. /usr/bin/zsh and /usr/bin/fish are also installed in
		                      the default image. Other shells can be added as needed.
		--timezone<tz>        Set the system timezone. Defaults to UTC. Supports names under /usr/share/zoneinfo. For example: "US/Mountain" and "America/Denver".
		--personal-username   Use your personal username (<user>@augmentcode.com) rather than "augment". This will eventually become the default.

	RESOURCES
		Only one of --gpu-type xor --cpu-type should be specified. Calling --gpu-type="list" or --cpu-type="list" will print out the
		list of available options and exit. Typically you will pass --gpu-type and --gpu-count OR --cpu-type and --cpu-count, and the
		remaining resources will be chosen as a ratio of the machines overall GPU/CPU/RAM capacity.

		--gpu-type=<type>       The type of GPU to select, or unset. Typically "L4" (cheaper), "H100", or "A100" is what you want.
		--cpu-type=<type>       The type of CPU to select, or unset.
		--gpu-count=<int>       The number of GPUs to request.
		--cpu-count=<quantity>  The amount of CPU to request.
		--ram=<quantity>        The amount of RAM to request. Units default to G, but M, G,, Mi, Gi, etc are all supported.

		--ignore-limits, -L     Ignore validation errors due to pre-defined machine limits.
		--ignore-validation     Ignore validation errors.

	PLACEMENT
		The --gpu-type or --cpu-type are the main driver for choosing where to place your DevPod. The flags below also
		affect placement. Note that these can cause scheduling failures, for example if --node-name doesn't provide
		the correct --gpu-type.

		--node-name=<name>     Request a specific node name.
		--pool-type=<type>     Override the r.augmentcode.com/pool-type selector.
		--tolerations=<tol>... One or more additional tolerations in the form KEY[=VALUE][:EFFECT]

	CONNECTIVITY
		Your DevPod is reachable over SSH from anywhere using a public DNS name; NOTE that there can be a small DNS propogation
		delay after the pod starts. The Pod's SSH port is *22022*; we avoid the standard 22 to cut down on brute force connection
		attempts. As a fallback, the "augi devpod exec" subcommand can be used to directly exec into your pod.

		By default, SSH to your DevPod uses centrally defined keys managed at https://userauth.r.augmentcode.com,
		but it's possible to fallback to ~/.ssh/authorized_keys as well.

		In addition to SSH, your DevPod is reachable over HTTPS. See the "expose" and "hide" subcommands for adding and removing
		ports. The URL https://<port>-<name>.<cluster>.r.augmentcode.com redirects <port> on your DevPod.

		--exposed-ports=<int,...>  Defalts to none. A list of HTTPS ports to expose over a K8s Ingress. PORT[:TARGET] or [NAME:]PORT:[TARGET].
		--ssh-std-port             Also expose SSH port 22. This is disabled by default to avoid excessive brute-force attempts.
		--no-ssh-centralized-keys  Use local ~/.ssh/authorized_keys (e.g., from dotfiles) rather than centrally managed keys.
		--eternal-terminal         Run etserver on port *22023*. (Not enabled by default because it adds a process to the DevPod).
		--mosh-start=<int>         Defaults to 0 (disabled by default). Mosh port range start. Setting to 6000 enables mosh.
		--mosh-count=<int>         Defaults to 6. The number of mosh ports. Setting to 0 disables mosh.

	Global Flags:
	  -c, --cluster string   The cluster (aka provider) for commands that take one. Defaults to autodetect.
	  -K, --context string   Kubeconfig context.
	  -u, --user string      The @augmentcode.com (without the domain) user to operate on.
	`)

	flags := struct {
		// Dry-run flags.
		printNodeDetails bool
		dryRun           bool
		diff             bool
		vimDiff          bool

		// Command flags.
		allowReboot bool
		noWait      bool
		autoSSH     bool
		replace     bool // deprecated: 'create --replace' == 'apply'

		// Builder-related flags.
		dotfiles     []string
		skipDotfiles bool
		namespaced   bool
	}{}

	// Spec flags.
	specf := spec.FromFlags(cmd.Flags())

	// Dry-run flags.
	cmd.Flags().BoolVarP(&flags.printNodeDetails, "print-node-details", "P", false, "Print node details and exit.")
	cmd.Flags().BoolVarP(&flags.dryRun, "dry-run", "n", false, "Dry-run. Print raw K8s objects.")
	cmd.Flags().BoolVar(&flags.diff, "diff", false, "Show a diff (implies --dry-run).")
	cmd.Flags().BoolVar(&flags.vimDiff, "vimdiff", false, "Run vimdiff (implies --dry-run).")

	// Command flags.
	cmd.Flags().BoolVar(&flags.allowReboot, "allow-reboot", false, "With --replace, to acknowledge a reboot will happen.")
	cmd.Flags().BoolVar(&flags.noWait, "no-wait", false, "Don't wait for DevPod to be ready.")
	cmd.Flags().BoolVar(&flags.autoSSH, "ssh", false, "Automatically SSH into DevPod when it's ready (cannot be used with --no-wait).")
	cmd.Flags().BoolVarP(&flags.replace, "replace", "R", false, "Deprecated: 'create --replace' == 'apply'.")
	cmd.Flags().MarkHidden("replace")

	// Builder-related flags.
	cmd.Flags().StringSliceVar(&flags.dotfiles, "dotfiles", nil, "Additional dotfile(s) to include, relative to $HOME.")
	cmd.Flags().BoolVar(&flags.skipDotfiles, "skip-dotfiles", false, "Don't handle dotfiles. This is enabled by default when the --user flag overrides the caller.")
	cmd.Flags().BoolVar(&flags.namespaced, "namespaced", false, "Create DevPod in the \"aug-user-<user>\" namespace [future default].")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		/// Check early for --gpu-type=list or --cpu-type=list
		if exit, err := maybePrintNodeTypes(cmd); err != nil {
			return err
		} else if exit {
			return nil
		}

		/// Basic Info from CLI.
		user, name, err := dpGetUserAndDevPodNames(cmd, args[0])
		if err != nil {
			return err
		}
		c, cli, err := dpGetClusterAndClients(cmd)
		if err != nil {
			return err
		}

		/// Find existing Spec, or init new one.
		spec, err := devpod.FindSpec(cmd.Context(), cli.K8s(), c, user, name)
		if k8s.NotFoundOK(err) != nil {
			return err
		} else if spec == nil {
			spec = devpodv1.NewDevPodFromNames(c.Name, user, name, func() string {
				if flags.namespaced {
					return "aug-user-" + user
				}
				return c.MainNamespace
			}())
		}

		/// Get the basic DevPod Builder and Manager

		mgr := devpod.NewManagerFromSpec(cli.K8s(), c, spec)
		bld := mgr.Builder()

		/// Check 'create' / '--replace'.

		if cmd.Flags().Changed("replace") {
			mgr.LogWarn("The --replace flag is deprecated: 'augi devpod create --replace' is equivalent to 'augi devpod apply'.")
		}
		if spec.HasUID() && cmd.CalledAs() == "create" && !flags.replace {
			return fmt.Errorf("DevPod %s already exists, use 'augi devpod apply' instead", name)
		}

		/// Get existing DevPod, if available.

		set0, err := func() (*devpod.Set, error) {
			if spec.HasUID() {
				return mgr.GetSet(cmd.Context())
			}
			return nil, nil
		}()

		/// Build or Update Spec from CLI flags, and Validate.

		if s, err := specf(c.Name, user, name, mgr.Spec().DevPod()); err != nil {
			return err
		} else {
			mgr.SwapSpec(s)
		}
		if err := mgr.Spec().Validate(); err != nil {
			return err
		}

		/// Build the node from the DevPodSpec.

		node, err := mgr.Spec().DevPod().Node(bld.Cluster())
		if err != nil {
			return fmt.Errorf("error building node, see --gpu-type=list for available aliases: %v", err)
		}
		bld.SetNode(node)

		/// Print node details and exit on -P.

		if flags.printNodeDetails {
			fmt.Println(strings.Join(node.TableHeader(), " | "))
			fmt.Println(strings.Join(node.TableRow(), " | "))
			return nil
		}

		/// Update Builder with CLI Flags

		bld.DotExtra = flags.dotfiles
		if !cmd.Flags().Changed("skip-dotfiles") && bld.User() != augment.WhoAmI() {
			bld.SkipDot = true
		} else {
			bld.SkipDot = flags.skipDotfiles
		}
		if mgr.Spec().DevPod().DockerMode {
			mgr.LogInfo("Building DevPod in --docker mode. Some flags are adjusted automatically.")
		}
		if flags.namespaced {
			if set0 != nil {
				return fmt.Errorf("The --namespaced flag can only be used for new DevPods.")
			}
			bld.Namespaced = true
		}

		/// Build (all objects), and [Optional] Apply from set0

		set, err := mgr.BuildSet(cmd.Context(), set0)
		if err != nil {
			return err
		}

		/// Print and exit on --dry-run

		if flags.dryRun || flags.diff || flags.vimDiff {
			if err := mgr.Apply(cmd.Context(), set, true); err != nil {
				return err
			}
			if flags.vimDiff {
				return devpod.DpVimDiff(cmd.Context(), set0, set)
			} else if flags.diff {
				return devpod.DpDiff(cmd.Context(), set0, set)
			} else {
				if y, err := set.YAML(); err != nil {
					return err
				} else {
					fmt.Print(y)
				}
			}
			return nil
		}

		/// Apply (all objects)

		if set0 != nil && !flags.allowReboot {
			return fmt.Errorf("the --allow-reboot flag is required, your DevPod may be rebooted by this command")
		}
		if err := mgr.Apply(cmd.Context(), set, false); err != nil {
			return err
		}

		mgr.LogInfo("DevPod %s created. To delete it later, run: augi devpod delete %s --[no-]delete-home.", bld.Name(), bld.Name())

		/// Wait and Status (unless --no-wait)

		if flags.noWait {
			fmt.Print(set.Status())
			return nil
		}
		if set, err := mgr.WaitReady(cmd.Context(), true); err != nil {
			return err
		} else {
			fmt.Print(set.Status())
		}

		/// SSH, if requested

		if flags.autoSSH {
			mgr.LogInfo("DevPod creation successful, SSH'ing in as requested (--ssh flag)...")
			return mgr.SSH(cmd.Context())
		}

		return nil
	}

	return cmd
}

func devpodUpdate() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "update <name> [--dry-run|--diff|--vimdiff] [--allow-reboot] [--wait|-w] [--ssh]",
		Args:  cobra.ExactArgs(1),
		Short: "Update DevPod with latest schema. May require a reboot, homedir is preserved.",
	}

	flags := struct {
		// Dry-run flags.
		dryRun  bool
		diff    bool
		vimDiff bool

		// Command flags.
		allowReboot bool
		wait        bool
		ssh         bool
	}{}

	// Dry-run flags.
	cmd.Flags().BoolVarP(&flags.dryRun, "dry-run", "n", false, "Dry-run. Print raw K8s objects.")
	cmd.Flags().BoolVar(&flags.diff, "diff", false, "Show a diff (implies --dry-run).")
	cmd.Flags().BoolVar(&flags.vimDiff, "vimdiff", false, "Run vimdiff (implies --dry-run).")

	// Command flags.
	cmd.Flags().BoolVar(&flags.allowReboot, "allow-reboot", false, "Acknowledge a reboot will happen.")
	cmd.Flags().BoolVarP(&flags.wait, "wait", "w", false, "Call WaitReady() before getting status.")
	cmd.Flags().BoolVar(&flags.ssh, "ssh", false, "Automatically SSH into DevPod when it's ready (only with --wait).")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		if flags.ssh && !flags.wait {
			return fmt.Errorf("--ssh only supported with --wait.")
		}

		/// Get existing DevPod.
		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		set0, err := mgr.GetSet(cmd.Context())
		if err != nil {
			return err
		}
		if err := mgr.Spec().Validate(); err != nil {
			mgr.LogWarn("devpod/%s has validation errors: %v...", mgr.Name(), err)
		}

		/// Build the node from the DevPodSpec.
		node, err := mgr.Spec().DevPod().Node(*mgr.Cluster())
		if err != nil {
			return err
		}
		mgr.Builder().SetNode(node)

		/// Build (all objects), and [Optional] Apply from set0
		mgr.Builder().SkipDot = true
		set, err := mgr.BuildSet(cmd.Context(), set0)
		if err != nil {
			return err
		}

		/// Print and exit on --dry-run
		if flags.dryRun || flags.diff || flags.vimDiff {
			if err := mgr.Apply(cmd.Context(), set, true); err != nil {
				return err
			}
			if flags.vimDiff {
				return devpod.DpVimDiff(cmd.Context(), set0, set)
			} else if flags.diff {
				return devpod.DpDiff(cmd.Context(), set0, set)
			} else {
				if y, err := set.YAML(); err != nil {
					return err
				} else {
					fmt.Print(y)
				}
			}
			return nil
		}

		/// Apply (all objects)

		if !flags.allowReboot {
			return fmt.Errorf("the --allow-reboot flag is required when not in dry-run mode")
		}
		if err := mgr.Apply(cmd.Context(), set, false); err != nil {
			return err
		}

		mgr.LogInfo("devpod/%s updated.", mgr.Name())

		/// Wait and Status
		if flags.wait {
			if set, err := mgr.WaitReady(cmd.Context(), true); err != nil {
				return err
			} else {
				fmt.Print(set.Status())
			}
		}

		/// SSH, if requested
		if flags.ssh {
			mgr.LogInfo("devpod/%s is ready, SSH'ing in as requested (--ssh flag)....", mgr.Name())
			return mgr.SSH(cmd.Context())
		}

		return nil
	}

	return cmd
}

func devpodDelete() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "delete <name...> --delete-home|--no-delete-home",
		Aliases: []string{"destroy", "del", "rm", "kill"},
		Short:   "Delete one or more DevPods.",
		Long: strings.Join([]string{
			"Delete all K8s objects associated with one or more DevPods. Home is deleted",
			"IFF it is named as <pod_name>-home (the default). Custom --home-name's are",
			"never deleted with this command.",
		},
			"\n",
		),
		Args: cobra.MinimumNArgs(1),
	}
	flags := struct {
		deleteHome   bool
		noDeleteHome bool
	}{}
	cmd.Flags().BoolVarP(&flags.deleteHome, "delete-home", "H", false, "Also delete home (--delete-home or --no-delete-home required).")
	cmd.Flags().BoolVar(&flags.noDeleteHome, "no-delete-home", false, "Don't delete home (--delete-home or --no-delete-home required).")
	cmd.Flags().Lookup("no-delete-home").Hidden = true
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		if flags.deleteHome == flags.noDeleteHome {
			return fmt.Errorf("One of --delete-home or --no-delete-home is required")
		}
		for _, arg := range args {
			if mgr, err := dpGetDevPod(cmd, arg); err != nil {
				return err
			} else if err := mgr.Delete(cmd.Context(), flags.deleteHome); err != nil {
				return err
			}
		}
		return nil
	}
	return cmd
}

func devpodStatus() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "status <name> [--wait|-w] [-ssh] [--raw|-r|--spec|-s]",
		Short: "Check DevPod status.",
		Args:  cobra.ExactArgs(1),
	}
	flags := struct {
		wait bool
		raw  bool
		spec bool
		ssh  bool
	}{}
	cmd.Flags().BoolVarP(&flags.wait, "wait", "w", false, "Call WaitReady() before getting status.")
	cmd.Flags().BoolVarP(&flags.raw, "raw", "r", false, "Print out the raw YAML status of all objects.")
	cmd.Flags().BoolVarP(&flags.spec, "spec", "s", false, "Print out the DevPod Spec.")
	cmd.Flags().BoolVar(&flags.ssh, "ssh", false, "Automatically SSH into DevPod when it's ready (only with --wait).")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		if flags.ssh && !flags.wait {
			// NOTE(mattm): `augi devpod status --ssh` is technically fine, but stear people towards
			// the more general `augi devpod ssh` command if they start using status simply to ssh.
			// --wait --ssh is useful to pick up after `augi devpod create --ssh` if <ctrl-c> was hit
			// by accident.
			return fmt.Errorf("--ssh only supported with --wait, or see the 'devpod ssh' subcommand")
		}

		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		var set *devpod.Set
		if flags.wait {
			set, err = mgr.WaitReady(cmd.Context(), true)
			if err == nil {
				fmt.Println() // pad with newline to separate from logs
			}
		} else {
			set, err = mgr.Status(cmd.Context())
		}
		if err != nil {
			return err
		}
		if flags.spec {
			if spec := set.Spec(); spec == nil {
				return fmt.Errorf("DevPod has no spec.")
			} else if y, err := spec.YAML(); err != nil {
				return err
			} else {
				fmt.Print(y)
			}
		} else if flags.raw {
			if y, err := set.YAML(); err != nil {
				return err
			} else {
				fmt.Print(y)
			}
		} else {
			fmt.Print(set.Status())
		}
		if flags.ssh {
			mgr.LogInfo("DevPod is ready, SSH'ing in as requested (--ssh flag)...")
			return mgr.SSH(cmd.Context())
		}
		return nil
	}
	return cmd
}

func devpodLogs() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "logs <name> [-f]",
		Short: "Print container logs.",
		Args:  cobra.ExactArgs(1),
	}
	flags := struct {
		follow     bool
		timestamps bool
		tail       int64
	}{}
	cmd.Flags().BoolVarP(&flags.follow, "follow", "f", false, "Follow logs.")
	cmd.Flags().BoolVar(&flags.timestamps, "timestamps", false, "Include timestamps on each line.")
	cmd.Flags().Int64Var(&flags.tail, "tail", 0, "Display the most recent N lines.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		iter := mgr.Logs(
			cmd.Context(),
			k8s.PodLogFollow(flags.follow),
			k8s.PodLogTimestamps(flags.timestamps),
			k8s.PodLogTail(flags.tail),
		)
		for line, err := range iter {
			if line != "" {
				fmt.Println(line)
			}
			if err != nil {
				return err
			}
		}
		return nil
	}
	return cmd
}

func devpodAugi() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "augi-path",
		Short: "Resolve the path from /usr/local/bin/augi which is a good indicator of the `augi` version on the DevPod.",
		Args:  cobra.ExactArgs(1),
	}
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		out, err := mgr.AugiPath(cmd.Context(), "")
		if out != "" {
			fmt.Println(out)
		}
		return err
	}
	return cmd
}

func devpodOn() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "on <name>",
		Short:   "Turn a DevPod \"on\" by setting replicas=1.",
		Aliases: []string{"poweron", "start", "boot"},
		Args:    cobra.ExactArgs(1),
	}
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		return mgr.PowerOn(cmd.Context())
	}
	return cmd
}

func devpodOff() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "off",
		Short:   "Turn a DevPod \"off\" by setting replicas=0, freeing up GPU/CPU/RAM.",
		Aliases: []string{"poweroff", "stop", "shutdown", "halt", "kill"},
		Args:    cobra.ExactArgs(1),
	}
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		return mgr.PowerOff(cmd.Context())
	}
	return cmd
}

func devpodReboot() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "reboot [--force|-f] [<name>]",
		Short:   "Reboot a DevPod. Defaults to '.' (the current pod) if not specified.",
		Aliases: []string{"bounce"},
		Args:    cobra.RangeArgs(0, 1),
	}
	flags := struct {
		force bool
	}{}
	cmd.Flags().BoolVarP(&flags.force, "force", "f", false, "Force reboot without a confirmation.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		arg := func() string {
			if len(args) == 1 {
				return args[0]
			}
			return "."
		}()
		mgr, err := dpGetDevPod(cmd, arg)
		if err != nil {
			return err
		}
		if !flags.force {
			fmt.Println("Rebooting a DevPod will lose all running shells, tmux sessions, etc, but the home directory will be safe.")
			fmt.Printf("Reboot DevPod %s?: [y/N] ", mgr.Name())
			var resp string
			fmt.Scanln(&resp)
			resp = strings.ToLower(resp)
			if resp != "y" && resp != "yes" {
				return fmt.Errorf("reboot aborted")
			}
		}
		return mgr.Reboot(cmd.Context())
	}
	return cmd
}

func devpodExposePort() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "expose <name> [--set] [int32|int32:int32]...",
		Short:   "Add port(s) to ingress https://<port>.<name>.<cluster>.r.augmentcode.com.",
		Aliases: []string{"expose-port"},
		Args:    cobra.MinimumNArgs(2),
	}
	flags := struct {
		set bool
	}{}
	cmd.Flags().BoolVarP(&flags.set, "set", "s", false, "Set the exposed ports to those given on the cmdline. Default is to append.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		ports := []any{}
		for _, arg := range args[1:] {
			ports = append(ports, arg)
		}
		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		if err := mgr.ExposePorts(cmd.Context(), flags.set, ports...); err != nil {
			return err
		}
		if set, err := mgr.GetSet(cmd.Context()); err != nil {
			mgr.LogWarn("Error getting status (but expose command succeeded): %v.", err)
		} else {
			fmt.Println("Exposed URLs are now:")
			for _, url := range set.ExposedURLs() {
				fmt.Println("-", url)
			}
		}
		return nil
	}
	return cmd
}

func devpodHidePort() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "hide <name> [int32...]",
		Short:   "Remove port(s) from ingress https://<port>.<name>.<cluster>.r.augmentcode.com. When empty, hide all ports.",
		Aliases: []string{"hide-port"},
		Args:    cobra.MinimumNArgs(1),
	}
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		ports := []int32{}
		for _, arg := range args[1:] {
			if p, err := strconv.ParseInt(arg, 10, 32); err != nil {
				return fmt.Errorf("Cannot parse '%s' as port (int32): %v", arg, err)
			} else {
				ports = append(ports, int32(p))
			}
		}
		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		if err := mgr.HidePorts(cmd.Context(), ports...); err != nil {
			return err
		}
		if set, err := mgr.GetSet(cmd.Context()); err != nil {
			mgr.LogWarn("Error getting status (but hide command succeeded): %v.", err)
		} else if urls := set.ExposedURLs(); len(urls) == 0 {
			fmt.Println("No more exposed URLs.")
		} else {
			fmt.Println("Exposed URLs are now:")
			for _, url := range urls {
				fmt.Println("-", url)
			}
		}
		return nil
	}
	return cmd
}

func devpodSSH() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "ssh [--wait|-w] <name> [[--] <cmdline>...]",
		Short: "SSH into DevPod for interactive shell OR to run a remote command.",
		Args:  cobra.MinimumNArgs(1),
	}
	flags := struct {
		wait bool
	}{}
	cmd.Flags().BoolVarP(&flags.wait, "wait", "w", false, "Wait for DevPod to be ready before SSH'ing.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		if flags.wait {
			if _, err := mgr.WaitReady(cmd.Context(), true); err != nil {
				mgr.LogErr("Wait failed: %v.", err)
				mgr.LogWarn("Trying SSH anyway...")
			}
		}
		return mgr.SSH(cmd.Context(), args[1:]...)
	}
	return cmd
}

func devpodExec() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "exec [--root] <name> -- [<command>...]",
		Short: "Exec into DevPod for interactive shell OR to run a remote command (use as a fallback, SSH strongly preferred).",
		Args:  cobra.MinimumNArgs(1),
	}
	flags := struct {
		asRoot bool
	}{}
	cmd.Flags().BoolVarP(&flags.asRoot, "root", "r", false, "Exec into pod as default user (root) rather than wrapping with `sudo -u augment`.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		return mgr.Exec(cmd.Context(), flags.asRoot, args[1:]...)
	}
	return cmd
}

func devpodUpdateImage() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "update-image <name> [image]",
		Short: "Update the augment_devpod_cpu or augment_devpod_gpu image, or set an image directly; and init.sh. (reboot-required)",
		Args:  cobra.RangeArgs(1, 2),
	}
	flags := struct {
		dryRun      bool
		allowReboot bool
		noInit      bool
		initSpec    string
	}{}
	cmd.Flags().BoolVarP(&flags.dryRun, "dry-run", "n", false, "Dry-run.")
	cmd.Flags().BoolVar(&flags.allowReboot, "allow-reboot", false, "Required flag to acknowledge that the pod will reboot immediately.")
	cmd.Flags().BoolVar(&flags.noInit, "no-init", false, "Only update the container image, skip init.sh.")
	cmd.Flags().StringVar(&flags.initSpec, "init-spec", "", "Override the default spec when updating init.sh.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		if !flags.allowReboot && !flags.dryRun {
			return fmt.Errorf("The --allow-reboot flag is required for `update-image`.")
		}

		image := func() string {
			if len(args) > 1 {
				return args[1]
			}
			return ""
		}()

		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}

		return mgr.UpdateImage(cmd.Context(), image, flags.dryRun, !flags.noInit, flags.initSpec)
	}
	return cmd
}

func devpodUpdateInit() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "update-init <name> [<spec>] [--dry-run]",
		Short: "Update the container's init.sh entrypoint.",
		Args:  cobra.RangeArgs(1, 2),
	}
	flags := struct {
		dryRun      bool
		allowReboot bool
		noInit      bool
		initSpec    string
	}{}
	cmd.Flags().BoolVarP(&flags.dryRun, "dry-run", "n", false, "Dry-run. Only log unified diff.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		dryRun, err := cmd.Flags().GetBool("dry-run")
		if err != nil {
			return err
		}
		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		if dryRun {
			mgr.LogWarn("NOTE: `update-init` is currently disabled. Come to #research-infra on Slack for help.")
		} else {
			return fmt.Errorf("NOTE: `update-init` is currently disabled. Come to #research-infra on Slack for help.")
		}
		spec := func() string {
			if len(args) > 1 {
				return args[1]
			}
			return ""
		}()
		return mgr.UpdateInit(cmd.Context(), spec, dryRun)
	}
	return cmd
}

func devpodResize() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "resize <name>",
		Short: "Modify GPU, CPU, and/or RAM requirements. (reboot-required). [unimplemented, but see `create --replace`]",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return fmt.Errorf("Unimplemented, but see `create --replace`")
		},
	}
	return cmd
}

func devpodAttachVolume() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "attach-volume <name>",
		Short: "Attach and mount a new volume. (reboot-required)",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return fmt.Errorf("Unimplemented")
		},
	}
	return cmd
}

func devpodDetachVolume() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "detach-volume <name>",
		Short: "Unmount and detach a volume. (reboot-required)",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return fmt.Errorf("Unimplemented")
		},
	}
	return cmd
}

func devpodEdit() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "edit <name>",
		Short: "Call `kubectl edit ...` on all objects in the DevPod.",
		Args:  cobra.ExactArgs(1),
	}
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		set, err := mgr.GetSet(cmd.Context())
		if err != nil {
			return err
		}
		k := exec.CommandContext(cmd.Context(), "kubectl", append([]string{"edit", "--context=" + mgr.K8sContext()}, set.NameList()...)...)
		k.Stdin, k.Stdout, k.Stderr = os.Stdin, os.Stdout, os.Stderr
		return k.Run()
	}
	return cmd
}

func devpodGrowHome() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "grow-home <name> [+]<int>[Gi]",
		Short: "Increase the size of the /home volume. Block homedirs require a reboot, FS homedirs are immediate.",
		Args:  cobra.ExactArgs(2),
	}
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		devpod, size := args[0], args[1]
		mgr, err := dpGetDevPod(cmd, devpod)
		if err != nil {
			return err
		}
		return mgr.GrowHome(cmd.Context(), size)
	}
	return cmd
}

func devpodGoHome() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "go-home <name> <new-home>|ephemeral --reboot [--new --home-type --home-class --home-size]",
		Short: "Change DevPod /home volume.",
		Args:  cobra.ExactArgs(1),
	}
	flags := struct {
		reboot    bool
		new       bool
		homeType  string
		homeClass string
		homeSize  int
	}{}
	cmd.Flags().BoolVar(&flags.reboot, "reboot", false, "Required flag to acknowledge pod will reboot and state (tmux, running procs) will be lost.")
	cmd.Flags().BoolVar(&flags.new, "new", false, "Build a new PVC instead of using an existing one.")
	cmd.Flags().StringVar(&flags.homeType, "home-type", "block", "'block' (default) or 'fs'.")
	cmd.Flags().StringVar(&flags.homeClass, "home-class", "nvme", "'nvme' (default) or 'vast'.")
	cmd.Flags().IntVar(&flags.homeSize, "home-size", 256, "The size in Gi of the PVC.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		devpod, home := args[0], args[1]
		if !flags.reboot {
			return fmt.Errorf("the --reboot flag is required")
		}
		mgr, err := dpGetDevPod(cmd, devpod)
		if err != nil {
			return err
		}
		if flags.new {
			return mgr.GoHome(cmd.Context(), home)
		} else {
			return mgr.GoNewHome(cmd.Context(), home, flags.homeType, flags.homeClass, flags.homeSize)
		}
	}
	return cmd
}

func devpodDotDiff() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "dot-diff <name>",
		Short: "Diff current DevPod dotfiles to new dotfiles.",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return fmt.Errorf("Unimplemented")
		},
	}
	return cmd
}

func devpodDotApply() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "dot-apply <name>",
		Short: "Apply DevPod dotfile configmap to this pod.",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return fmt.Errorf("Unimplemented")
		},
	}
	return cmd
}

func devpodDotUpdate() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "dot-update [--dry-run|-n] [--diff|--vimdiff] [--add-files <file>...] [--rm-files <file>...] [podname]",
		Short: "Update the dotfile configMap for the named pod (default current).",
		Args:  cobra.MaximumNArgs(1),
		// TODO better long description
		Long: "augi devpod dot-update [--dry-run|-n] [--diff|--vimdiff] [-add-files <file>...], [-rm-files <file>...] [podname]",
	}
	flags := struct {
		DryRun  bool
		Diff    bool
		VimDiff bool
		Add     []string
		Remove  []string
	}{}
	cmd.Flags().BoolVarP(&flags.DryRun, "dry-run", "n", false, "Dry-run. Print raw K8s objects.")
	cmd.Flags().BoolVar(&flags.Diff, "diff", false, "With --replace, show a diff (implies --dry-run)")
	cmd.Flags().BoolVar(&flags.VimDiff, "vimdiff", false, "With --replace, show a diff (implies --dry-run)")

	// Add/Remove Options
	cmd.Flags().StringSliceVarP(&flags.Add, "add-file", "a", []string{}, "Add file(s) to the config map.")
	cmd.Flags().StringSliceVarP(&flags.Remove, "rm-file", "r", []string{}, "Remove file(s) from the config map.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		if flags.Diff || flags.VimDiff {
			flags.DryRun = true
		}
		if flags.VimDiff && flags.Diff {
			return fmt.Errorf("only one of --diff or --vimdiff may be specified")
		}

		if len(args) == 0 {
			args = []string{"."}
		}

		addFiles, err := cmd.Flags().GetStringSlice("add-file")
		if err != nil {
			return err
		}
		rmFiles, err := cmd.Flags().GetStringSlice("rm-file")
		if err != nil {
			return err
		}
		// Convert to sets and dedupe
		addSet := make(map[string]bool)
		rmSet := make(map[string]bool)

		for _, file := range addFiles {
			addSet[file] = true
		}

		for _, file := range rmFiles {
			if _, exists := addSet[file]; exists {
				return fmt.Errorf("file %s is both added and removed", file)
			}
			rmSet[file] = true
		}
		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		return mgr.UpdateDotFiles(cmd.Context(), addSet, rmSet, flags.DryRun, flags.Diff, flags.VimDiff)
	}

	return cmd
}

func devpodDotEdit() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "dot-edit <name>",
		Short: "Edit individual file in $EDITOR.",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return fmt.Errorf("Unimplemented")
		},
	}
	return cmd
}

func devpodDotLs() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "dot-ls [podname]",
		Short: "List files in dotfiles.",
		Args:  cobra.MaximumNArgs(1),
		Long: strings.Join([]string{
			"List files in dotfiles for the named pod (default current).\n",
		}, ""),
	}
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		if len(args) == 0 {
			args = []string{"."}
		}
		mgr, err := dpGetDevPod(cmd, args[0])
		if err != nil {
			return err
		}
		df, err := mgr.GetDotFilesFormatted(cmd.Context())
		if err != nil {
			return err
		}
		fmt.Print(df)
		return nil
	}
	return cmd
}

func devpodDotCreate() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "dot-create",
		Short: "Create dotfile tgz and write to stdout.",
		Args:  cobra.NoArgs,
	}
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		df := devpod.NewDotfiles()
		if _, err := df.BuildAndClose(); err != nil {
			return err
		} else if buf, err := df.TGZ(); err != nil {
			return err
		} else {
			_, err := os.Stdout.Write(buf)
			return err
		}
	}
	return cmd
}
