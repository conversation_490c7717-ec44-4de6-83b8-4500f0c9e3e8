package cli

import (
	"context"
	"fmt"
	"os"
	"strings"

	"github.com/spf13/cobra"

	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/research/infra/lib/augment"
)

const (
	ADMIN = true
)

var rootCmd = &cobra.Command{
	Use:           "augi",
	Short:         "augi - the Augment Infra CLI.",
	SilenceUsage:  true,
	SilenceErrors: true,
}

var rootGroup = &cobra.Group{
	ID:    "root_subcommands",
	Title: "Commands",
}

func init() {
	rootCmd.AddGroup(rootGroup)

	// Setup structured logger.
	flogger := logger.GlobalLoggerFromFlags(rootCmd.PersistentFlags())
	rootCmd.PersistentPreRunE = func(cmd *cobra.Command, args []string) error {
		_, err := flogger(cmd.ErrOrStderr())
		return err
	}
	// Add sub commands
	addCommand(nil, nil, rootHelpAll())
}

func Main(ctx context.Context) {
	cobra.EnableCommandSorting = false
	if err := rootCmd.ExecuteContext(ctx); err != nil {
		fmt.Fprintf(os.Stderr, "Error running CLI: %v\n", err)
		os.Exit(1)
	}
}

func isAdminMode() bool {
	switch val := os.Getenv("AUGI_ADMIN_MODE"); val {
	case "":
		defaultAdmins := map[string]bool{
			"mattm":   true,
			"marcmac": true,
		}
		user := augment.WhoAmI()
		return defaultAdmins[user]
	case "0", "false", "FALSE":
		return false
	default:
		return true
	}
}

func addCommand(parent *cobra.Command, group *cobra.Group, child *cobra.Command, admins ...bool) {
	admin := false
	for _, a := range admins {
		admin = a
	}
	child.Hidden = child.Hidden || (admin && !isAdminMode())

	child.DisableFlagsInUseLine = false

	if parent == nil {
		parent = rootCmd
	}
	if parent == rootCmd && group == nil && !strings.HasPrefix(child.Use, "help-all") {
		group = rootGroup
	}
	if group != nil {
		child.GroupID = group.ID
	}
	parent.AddCommand(child)
}

func rootHelpAll() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "help-all [--hidden]",
		Aliases: []string{"helpall"},
		Short:   "Dump all help output at once.",
	}
	flags := struct {
		hidden bool
	}{}
	cmd.Flags().BoolVar(&flags.hidden, "hidden", false, "Include hidden commands.")
	cmd.RunE = func(_ *cobra.Command, _ []string) error {
		line := strings.Repeat("=", 80)

		// Depth-first, so child subcommands are displayed after parents.
		var dump func(*cobra.Command)
		dump = func(cmd *cobra.Command) {
			if cmd.Hidden && !flags.hidden {
				return
			}
			pline := "== " + cmd.CommandPath() + " "
			pline += strings.Repeat("=", len(line)-len(pline))
			fmt.Println(line)
			fmt.Println(pline)
			cmd.Help()
			fmt.Println()
			for _, child := range cmd.Commands() {
				dump(child)
			}
		}
		dump(rootCmd)
		return nil
	}
	return cmd
}
