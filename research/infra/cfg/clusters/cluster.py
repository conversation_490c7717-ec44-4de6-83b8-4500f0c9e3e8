#!/usr/bin/env python3

from dataclasses import dataclass
from pathlib import Path

import _gojsonnet as jsonnet
from dataclasses_json import Undefined, dataclass_json
from research.infra.cfg.clusters.node import Node
from research.infra.cfg.clusters.volmount import VolMount


@dataclass_json(undefined=Undefined.EXCLUDE)
@dataclass()
class Cluster:
    name: str
    long_name: str
    prod: bool
    registry_hostname: str | None
    registry_url: str | None
    registry_basepath: str | None
    registry: str | None
    context: str
    context_admin: str
    main_namespace: str | None
    checkpoint_bucket: str | None
    shared_mount: str | None
    determined_url: str | None
    k8s_url_external: str | None
    k8s_url_internal: str | None
    augi_release_readers: str
    images: dict[str, str]
    images_main: dict[str, str]
    nodes: list[Node]
    node_names: list[str]
    volmounts: list[VolMount]

    def dumps(self) -> str:
        return self.schema().dumps(self, indent=4)  # type: ignore

    def equals(self, other: "Cluster") -> bool:
        return self.name == other.name

    def is_gcp(self) -> bool:
        return self.name.lower().startswith("gcp")

    def node_from_args(self, ignore_validations: bool = False, **kwargs):
        """See the node_from_args() args in cluser.jsonnet.

        The most common cases:
         - gpu_type:str, and optionally gpu_count:int.
         - cpu_type:str, and optionally cpu_count:int.
        """
        fname = self.name + ".node_from_args()"
        basedir = str(Path(__file__).parent)
        snippet = "\n".join(
            [
                f"local clusters = import '{basedir}/clusters.jsonnet';",
                f"local cluster = clusters.cluster('{self.name}');",
                "cluster.node_from_args",
            ]
        )

        tla_codes: dict[str, str] = {}
        for k, v in kwargs.items():
            if v is None:
                tla_codes[k] = "null"
            elif isinstance(v, bool):
                tla_codes[k] = "true" if v else "false"
            elif isinstance(v, int):
                tla_codes[k] = str(v)
            else:
                tla_codes[k] = "'" + v + "'"

        s: str = jsonnet.evaluate_snippet(fname, snippet, tla_codes=tla_codes)
        node = Node.loads(s)
        if not ignore_validations:
            node.raise_if_errors()
        return node
