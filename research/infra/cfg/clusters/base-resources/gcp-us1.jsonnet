local clusters = import '../clusters.jsonnet';

local bitnami_ss = import 'tmpl/bitnami-ss.jsonnet';
local cert_manager = import 'tmpl/cert-manager.jsonnet';
local config_connector = import 'tmpl/config-connector.jsonnet';
local external_dns = import 'tmpl/external-dns.jsonnet';
local gke_cluster = import 'tmpl/gke_cluster.jsonnet';
local image_primer = import 'tmpl/image-primer.jsonnet';
local ingress_nginx = import 'tmpl/ingress-nginx.jsonnet';
local kuberay_operator = import 'tmpl/kuberay-operator.jsonnet';
local sysctl = import 'tmpl/sysctl.jsonnet';

local lib = import 'tmpl/lib.jsonnet';

// Outer (hosted on gcp-core0's ConfigConnector):
//   Stage0: The GKE Cluster Outer (Cluster, Networks, Node SA, NodePools)
//   Stage1: ConfigConnecter Outer (IAM ServiceAccount and Roles)
// Inner (internal K8s resources):
//   Stage2: ConfigConnecter Install (configconnector-operator-system)
//   Stage3: ConfigConnector and ConfigConnectorContext CRD (namespace config)
//   Stage4: The GKE Cluster Inner (StorageClass, NetDev, StorageClass)
//   Stage5: Marine Services + supporting ConfigConnector CRDs, DNS Zone, etc

function(aH=null, cluster='gcp-us1', stages=[2, 3, 4, 5]) {
  local H = if aH == null then clusters.cluster('gcp-core0') else aH,
  local C = clusters.cluster(cluster),

  // Helper wrapper to apply the 'svc' NoSchedule toleration.
  local svcTolerate = function(x) lib.ApplyToleration(x, 'r.augmentcode.com/pool-type', 'svc'),

  //////////////////////////////////////////////////////////////////////////////
  //
  // Stage0: GKE Cluster Outer (Cluster, Networks, Node SA, NodePools)
  // Stage4: GKE Cluster Inner (StorageClass, NetDev, ...)
  //

  cluster:: gke_cluster + {
    host_cluster: H.name,
    host_namespace: 'cc-' + C.name,

    name:: C.name,
    project_id:: C.gcp_project,
    location:: C.gcp_region,
    primary_zone:: 'us-central1-a',
    description:: 'Research %s cluster in %s.' % [C.name, C.gcp_region],
    num_nets:: 9,

    h100_reservation:: 'projects/augment-parent-gsc/reservations/h100-gsc-0',
    l4_reservations:: ['us-central1-a-l4-x4-0'],
    ci_pool:: true,
    hydra_pool:: true,
  },
  stage0:: {
    outer: $.cluster.Outer,

    spark_high_mem: $.cluster.Outer._base_pool + {
      name_suffix:: 'spark-highmem',
      machine_type:: 'c3-highmem-88',
      disk_type:: 'pd-ssd',
      disk_size_gb:: 2048,  // ephemeral storage on boot disk
      total_min_node_count:: 1,
      total_max_node_count:: 64,  // we need some non-zero number
      min_node_count:: null,
      max_node_count:: null,
      taints+:: [{ effect: 'NO_SCHEDULE', key: 'r.augmentcode.com/pool-type', value: 'spark-highmem' }],
      spec+: {
        nodeConfig+: {
          labels+: { 'r.augmentcode.com/pool-type': 'spark-highmem' },
        },
      },
    },
    spark_high_mem_c4: $.cluster.Outer._base_pool + {
      name_suffix:: 'spark-highmem-c4',
      machine_type:: 'c4-highmem-96',
      disk_type:: 'hyperdisk-balanced',
      disk_size_gb:: 2048,  // ephemeral storage on boot disk
      total_min_node_count:: 1,
      total_max_node_count:: 64,  // we need some non-zero number
      min_node_count:: null,
      max_node_count:: null,
      taints+:: [{ effect: 'NO_SCHEDULE', key: 'r.augmentcode.com/pool-type', value: 'spark-highmem' }],
      spec+: {
        nodeConfig+: {
          labels+: { 'r.augmentcode.com/pool-type': 'spark-highmem' },
        },
      },
    },
    spark_high_mem_n4: $.cluster.Outer._base_pool + {
      name_suffix:: 'spark-highmem-n4',
      machine_type:: 'n4-highmem-80',
      disk_type:: 'hyperdisk-balanced',
      disk_size_gb:: 2048,  // ephemeral storage on boot disk
      total_min_node_count:: 1,
      total_max_node_count:: 64,  // we need some non-zero number
      min_node_count:: null,
      max_node_count:: null,
      taints+:: [{ effect: 'NO_SCHEDULE', key: 'r.augmentcode.com/pool-type', value: 'spark-highmem' }],
      spec+: {
        nodeConfig+: {
          labels+: { 'r.augmentcode.com/pool-type': 'spark-highmem' },
        },
      },
    },
  },
  stage4:: {
    cluster_inner: $.cluster.Inner,

    local repo = (import 'gcp-global.jsonnet')().repos.us + {
      project_id: C.gcp_project,
    },

    image_pull: C.k8s.GCP.IAM.PolicyMember + {
      local repo = (import 'gcp-global.jsonnet')().repos.us,

      name:: $.cluster.Outer.node_sa.name + '.' + repo.metadata.name + '.reader',
      namespace:: $.cluster.host_namespace,
      spec+: {
        resourceRef: {
          kind: repo.kind,
          external: repo.ref,
        },
        member: 'serviceAccount:' + $.cluster.Outer.node_sa.email,
        role: 'roles/artifactregistry.reader',
      },
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Stage1: ConfigConnecter Outer (IAM ServiceAccount and Roles)
  // Stage2: ConfigConnecter Install
  // Stage3: ConfigConnector Config
  //

  config_connector:: config_connector.InstallGlobal(C.k8s, contexts=[
    {
      H: H,
      C: C,
      namespace: $.cluster.host_namespace,
      gcp_target_project: C.gcp_project,
    },
  ]),
  stage1:: self.config_connector.Outer,
  stage2:: svcTolerate(self.config_connector.Install),
  stage3:: self.config_connector.Config,

  //////////////////////////////////////////////////////////////////////////////
  //
  // Stage5: Marines Resources
  //

  stage5:: C.k8s + {
    local k = self,

    ////////////////////////////////////////////////////////////////////////////
    //
    // Standard Namespaces ('aug-system' and '{cluster_name}')
    //

    // Default namespace, may go unused or be useful to tmp stuff.
    main_ns: k.Namespace + {
      name:: C.main_namespace,
    },

    // Augment-specific namespace, like kube-system.
    aug_system_ns: k.Namespace + {
      name:: 'aug-system',
    },

    eng_secrets_ns: k.Namespace + {
      name:: 'eng-secrets',
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // RBAC (admin + users)
    //

    admin_rb: k.ClusterRoleBinding + {
      name:: 'aug:cluster-admins',
      role_name:: 'cluster-admin',  // TODO(mattm): Maybe something slightly less powerfull, or scoped to a list of namespaces.
      groups:: ['<EMAIL>'],
    },

    root_rb: k.ClusterRoleBinding + {
      name:: 'aug:cluster-root',
      role_name:: 'cluster-admin',
      groups:: ['<EMAIL>'],
    },

    user_rbs: {
      local u = self,
      view: k.ClusterRoleBinding + {
        name:: 'aug:cluster-users',
        role_name:: 'view',  // TODO(mattm): Something custom?
        groups:: ['<EMAIL>'],
      },
      view_extra_role: k.ClusterRole + {
        name:: 'aug:aggregate-to-view',
        metadata+: {
          labels+: {
            'rbac.authorization.k8s.io/aggregate-to-view': 'true',
          },
        },
        rules+: [
          {
            apiGroups: [''],
            resources: ['nodes'],
            verbs: k.READ_VERBS,
          },
        ],
      },
      edits: [
        k.RoleBinding + {
          role_ref_kind:: 'ClusterRole',
          name:: 'aug:cluster-users',
          namespace:: ns,
          role_name:: 'edit',  // TODO(mattm): Something custom?
          groups:: ['<EMAIL>'],
        }
        for ns in [k.main_ns.metadata.name]
      ],
    },

    // Role and binding for eng-secrets
    eng_secrets: {
      local es = self,
      namespace:: 'eng-secrets',
      eng_secrets_sa: k.ServiceAccount + {
        name:: 'eng-secrets-sa',
      },
      eng_secrets_reader_role: k.Role + {
        name:: 'aug:eng-secrets-reader-role',
        namespace:: es.namespace,
        rules: [
          {
            apiGroups: [''],
            resources: ['secrets'],
            verbs: k.READ_VERBS,
          },
          {
            apiGroups: ['bitnami.com'],
            resources: ['sealedsecrets'],
            verbs: k.READ_VERBS,
          },

        ],
      },
      eng_secrets_writer_role: k.Role + {
        name:: 'aug:eng-secrets-writer-role',
        namespace:: es.namespace,
        rules: [
          {
            apiGroups: [''],
            resources: ['secrets'],
            verbs: k.READ_VERBS,
          },
          {
            apiGroups: ['bitnami.com'],
            resources: ['sealedsecrets'],
            verbs: k.READ_VERBS + k.WRITE_VERBS_NO_COLLECTION,
          },

        ],
      },
      eng_secrets_reader_role_binding: k.RoleBinding + {
        name:: 'aug:eng-secrets-reader-role-binding',
        namespace:: es.namespace,
        role_name:: es.eng_secrets_reader_role.name,
        groups:: ['<EMAIL>'],
        sas:: [{ metadata: { name: 'default', namespace: 'gcp-us1' } }, { metadata: { name: 'determined-service-account', namespace: 'gcp-us1' } }],
      },
      eng_secrets_writer_role_binding: k.RoleBinding + {
        name:: 'aug:eng-secrets-writer-role-binding',
        namespace:: es.namespace,
        role_name:: es.eng_secrets_writer_role.name,
        sas:: [{ metadata: { name: es.eng_secrets_sa.name, namespace: es.eng_secrets_sa.namespace } }],
      },
    },
    ////////////////////////////////////////////////////////////////////////////
    //
    // Bitnami Sealed Secrets
    //
    // This runs in the `kube-system` namespace to make it easy to run the `kubeseal` CLI.
    //

    bitnami_ss: {
      release: svcTolerate(bitnami_ss(C.k8s, name='bitnami-ss0', values={})),
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // GCP ConfigConnector
    //

    GCP+:: {
      Object+:: {
        project_id:: C.gcp_project,
        namespace:: $.cluster.host_namespace,
        metadata+: {
          labels: {},
        },
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // DNS Zone
    //
    // This a zone scoped to this cluster. We need to setup the zone itself, plus
    // the subdomain records in the parent zone.
    //

    dns: k.GCP.DNS.ManagedZone + {
      spec+: {
        dnsName: C.name + '.r.augmentcode.com.',
        description: 'Cluster-scoped zone for ' + C.name + '.',
        visibility: self.VISIBILITY.PUBLIC,
        dnssecConfig+: {
          state: 'off',
        },
      },
    },

    dns_parent_ns: k.GCP.DNS.RecordSet + {
      spec+: {
        managedZoneRef+: { external: 'r-augmentcode-com' },
        name: k.dns.spec.dnsName,
        type: 'NS',
        rrdatas: [
          'ns-cloud-d1.googledomains.com.',
          'ns-cloud-d2.googledomains.com.',
          'ns-cloud-d3.googledomains.com.',
          'ns-cloud-d4.googledomains.com.',
        ],
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // External DNS
    //
    // This monitors Services (LoadBalancer) and Ingresses and creates A-records
    // in DNS. It's configured with an IAM ServiceAccount which can read all zones
    // in the project (to find the right one) and then write to zones where we want
    // to manage records.
    //

    external_dns: svcTolerate(external_dns(C.k8s, C.gcp_project, zones=[k.dns.spec.dnsName], namespace=k.aug_system_ns.metadata.name, owner_id=std.asciiLower(C.name)) + {
      sa+: {
        metadata+: {
          annotations+: {
            'iam.gke.io/gcp-service-account': '%s@%s.iam.gserviceaccount.com' % [k.external_dns_sa.sa.metadata.name, C.gcp_project],
          },
        },
      },
    }),

    external_dns_sa: {
      local s = self,
      sa: k.GCP.IAM.ServiceAccount + {
        name:: C.name + '-external-dns',
        description:: 'K8s External DNS',
      },
      sa_workload_identity: k.GCP.IAM.PolicyMember + {
        name:: s.sa.metadata.name + '.workload-identity-user',
        spec+: {
          resourceRef: s.sa.localKindRef,
          member: 'serviceAccount:%s.svc.id.goog[%s/%s]' % [C.gcp_project, k.external_dns.sa.metadata.namespace, k.external_dns.sa.metadata.name],
          role: 'roles/iam.workloadIdentityUser',
        },
      },
      sa_role_project_reader: k.GCP.IAM.PolicyMember + {
        name:: s.sa.metadata.name + '.' + C.gcp_project + '.reader',
        spec+: {
          resourceRef: {
            kind: 'Project',
            external: 'project/' + C.gcp_project,
          },
          memberFrom: self.memberFromSA(s.sa),
          role: 'roles/dns.reader',
        },
      },
      sa_role_zone_admin: k.GCP.IAM.PolicyMember + {
        name:: s.sa.metadata.name + '.' + C.name + '.admin',
        spec+: {
          resourceRef: k.dns.localKindRef,
          memberFrom: self.memberFromSA(s.sa),
          role: 'roles/dns.admin',
        },
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // Cert Manager
    //
    // We configure a dns01 solver for the above zone(s). The dns01 solver is used
    // instead of http01 because 1) only dns01 supports wildcards and 2) it avoids a
    // dependency on the ingress controller.

    cert_manager: {
      local cm = self,
      release: svcTolerate(cert_manager(C.k8s, name='cert-manager0', namespace='cert-manager', values={
        serviceAccount+: {
          annotations+: {
            'iam.gke.io/gcp-service-account': '%s@%s.iam.gserviceaccount.com' % [cm.dns01_sa.metadata.name, C.gcp_project],
          },
        },
      })),
      // ServiceAccount for dns01 solver.
      dns01_sa: k.GCP.IAM.ServiceAccount + {
        name:: C.name + '-dns01-solver',
        description:: 'K8s CertManager (dns01 solver)',
      },
      // Workload Identity binding.
      dns01_workload_identity: k.GCP.IAM.PolicyMember + {
        name:: cm.dns01_sa.metadata.name + '.workload-identity-user',
        spec+: {
          resourceRef: cm.dns01_sa.localKindRef,
          member: 'serviceAccount:' + C.gcp_project + '.svc.id.goog[cert-manager/cert-manager0]',
          role: 'roles/iam.workloadIdentityUser',
        },
      },
      // Read all zones, so that the solver can find the correct zone.
      dns01_reader_role: k.GCP.IAM.PolicyMember + {
        name:: cm.dns01_sa.metadata.name + '.' + C.gcp_project + '.reader',
        spec+: {
          resourceRef: {
            kind: 'Project',
            external: 'project/' + C.gcp_project,
          },
          memberFrom: self.memberFromSA(cm.dns01_sa),
          role: 'roles/dns.reader',
        },
      },
      // Access to {cluster}.r.augmentcode.com
      dns01_role_cluster: k.GCP.IAM.PolicyMember + {
        name:: cm.dns01_sa.metadata.name + '.' + C.name + '.admin',
        spec+: {
          resourceRef: k.dns.localKindRef,
          memberFrom: self.memberFromSA(cm.dns01_sa),
          role: 'roles/dns.admin',
        },
      },
      dns01_issuer: k.ClusterIssuer + {
        name:: 'letsencrypt-prod',
        spec+: {
          acme: {
            privateKeySecretRef: {
              name: 'letsencrypt-account-key',
            },
            server: 'https://acme-v02.api.letsencrypt.org/directory',
            solvers: [
              {
                dns01: {
                  cloudDNS: {
                    project: C.gcp_project,
                  },
                },
              },
            ],
          },
        },
      },
      ss_issuer: k.ClusterIssuer + {
        name:: 'selfsigned',
        spec+: {
          selfSigned: {},
        },
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // nginx Ingress Controller
    //
    // We enforce http->https redirect at the controller level, so it's not possible
    // for users to configure a plain HTTP ingress. We do *not* enforce auth here, that
    // happens on a per-ingress basis.
    //

    ingress_nginx: {
      release: svcTolerate(ingress_nginx(C.k8s, name='nginx0')),
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // KubeRay Operator
    //
    // Manages Ray clusters in Kubernetes
    //

    kuberay_operator: {
      local ray = self,

      release: svcTolerate(kuberay_operator(C.k8s, name='kuberay-operator', namespace='ray-system', values={})),

      ksa: k.ServiceAccount + {
        name:: 'ray',
        namespace:: C.main_namespace,
        metadata+: {
          annotations+: {
            'iam.gke.io/gcp-service-account': ray.gsa.email,
          },
        },
      },
      wi: k.GCP.IAM.PolicyMember + {
        name:: ray.gsa.metadata.name + '.workload-identity-user',
        spec+: {
          resourceRef: ray.gsa.localKindRef,
          member: 'serviceAccount:%s.svc.id.goog[%s/%s]' % [C.gcp_project, ray.ksa.metadata.namespace, ray.ksa.metadata.name],
          role: 'roles/iam.workloadIdentityUser',
        },
      },
      gsa: k.GCP.IAM.ServiceAccount + {
        name:: C.name + '-ray',
        description:: 'K8s Ray Service Account',
      },
      acls: [
        [
          k.GCP.IAM.PolicyMember + {
            name:: ray.gsa.metadata.name + '.' + bkt + '.storage-bucketviewer',
            spec+: {
              resourceRef: {
                kind: 'StorageBucket',
                external: bkt,
              },
              memberFrom: self.memberFromSA(ray.gsa),
              role: 'roles/storage.bucketViewer',
            },
          },
          k.GCP.IAM.PolicyMember + {
            name:: ray.gsa.metadata.name + '.' + bkt + '.storage-objectuser',
            spec+: {
              resourceRef: {
                kind: 'StorageBucket',
                external: bkt,
              },
              memberFrom: self.memberFromSA(ray.gsa),
              role: 'roles/storage.objectUser',
            },
          },
        ]
        for bkt in ['gcp-us1-user', 'gcp-us1-checkpoints']
      ],

    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // DevPod PriorityClass
    //

    pc: k.PriorityClass + {
      name:: 'aug-devpod-default-priority',
      value: 1000000,
      preemptionPolicy: k.PREEMPTION_POLICY.NEVER,
      description: |||
        The default PriorityClass for DevPods. Designed to match "determined-system-priority" which
        was used historically for this case.
      |||,
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // Image Primer
    //
    // Warm per-node image caches for the base/spark/devpod cpu/gpu images. An
    // alternative to this on GKE could be to use the "Image Streaming" feature,
    // which mounts images from NFS.
    //

    primer: image_primer(C, cycle='5m'),

    ////////////////////////////////////////////////////////////////////////////
    //
    // Per-Node sysctl settings
    //

    sysctl: sysctl(C, max_user_watches=4 * 512 * 1024),

    ////////////////////////////////////////////////////////////////////////////
    //
    // Storage Bucket <> FUSE Mounts
    //
    // The bucket is scoped (by naming convention) to the cluster.
    //
    // Access to the bucket controlled by Pod's K8s ServiceAccount and WorkloadIdentity
    // (AFAIK // this is currently the /only/ option). This means access can be
    // provided to:
    //  - A specific namespace + serviceaccount (in all gke clusters).
    //  - A specific namespace + ALL serviceaccounts (in all gke clusters).
    //  - All pods in all namespaces, but in a specific gke cluster.
    //
    // Using a specific serviceaccount is tempting but annoying. We'd wind up
    // adding "default" and arbitrarily any other. So below we go with the option
    // "specific namespace + ALL serviceaccounts".
    //
    // The bucket mount can be configured entirely in the Pod, or partially by a PV+PVC.
    // We go with the latter because centralizing the config is easier. There can be
    // a PVC in multiple namespaces; for now we do the "main" namespace.
    //
    // The mount is configured for 1000:1000 with rwX:rwX:- permissions, and
    // the noexec,noatime mount options. chown/chmod do not work or make sense
    // on this filesystem.
    //
    // Outside of this config. Pods need the annotation `gke-gcsfuse/volumes: "true"` and then
    // to populate pod.volumes and pod.containers[].volumeMount as normal. Many of the tuning
    // options happen on the pod, however. The annotation signals to GKE to start a sidecar
    // container in the pod (this can break tooling that assumes only a single container!).
    //
    // https://cloud.google.com/kubernetes-engine/docs/how-to/persistent-volumes/cloud-storage-fuse-csi-driver
    //

    fuse_buckets: [
      self.fuse_bucket(C.name + '-augment-ci-testing'),
      self.fuse_bucket(C.name + '-checkpoints'),
      self.fuse_bucket(C.name + '-configs'),
      self.fuse_bucket(C.name + '-data'),
      self.fuse_bucket(C.name + '-eval'),
      self.fuse_bucket(C.name + '-external-models'),
      self.fuse_bucket(C.name + '-ftm-checkpoints'),
      self.fuse_bucket(C.name + '-public-html'),
      self.fuse_bucket(C.name + '-python-env', softdelete=false),
      self.fuse_bucket(C.name + '-spark-data'),
      self.fuse_bucket(C.name + '-user'),

      self.fuse_bucket('gcp-core0-harbor-master-images', create_bucket=false, users=false, softdelete=false),
    ],

    fuse_bucket:: function(name, create_bucket=true, users=true, softdelete=true) {
      local B = self,

      _bucket: if create_bucket then B.bucket,
      bucket:: k.GCP.Storage.Bucket + {
        name:: name,
        spec+: {
          location: C.gcp_region,
          storageClass: 'STANDARD',
          publicAccessPrevention: 'enforced',
          uniformBucketLevelAccess: true,
          softDeletePolicy: if softdelete then {
            retentionDurationSeconds: 14 * 24 * 3600,  // 14 days
          },
          // NOTE(mattm): We've enabled Hierarchical Namespace support, but this isn't yet supported by
          // ConfigConnector. See: https://github.com/GoogleCloudPlatform/k8s-config-connector/issues/2746.
        },
      },

      acl: k.GCP.IAM.PolicyMember + {
        name:: C.name + '-workload.' + B.bucket.name + '.' + 'storage-objectuser',
        spec+: {
          resourceRef: if create_bucket then B.bucket.localKindRef else {
            kind: 'StorageBucket',
            external: B.bucket.name,
          },
          member: 'principalSet://iam.googleapis.com/projects/%d/locations/global/workloadIdentityPools/%s.svc.id.goog/namespace/%s' % [
            C.gcp_project_number,
            C.gcp_project,
            C.name,
          ],
          role: 'roles/storage.objectUser',
        },
      },

      acl_humans: if users then k.GCP.IAM.PolicyMember + {
        name:: 'group-gcp-users.' + B.bucket.name + '.' + 'storage-objectuser',
        spec+: {
          resourceRef: B.bucket.localKindRef,
          member: 'group:<EMAIL>',
          role: 'roles/storage.objectUser',
        },
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // Spark
    //

    spark: {
      local spark = self,

      // Google Service Account
      gsa: k.GCP.IAM.ServiceAccount + {
        name: C.name + '-spark-sa',
      },

      // K8s Service Account (with annotation for GSA linking)
      ksa: k.ServiceAccount + {
        name:: 'spark-sa',
        namespace:: k.main_ns.metadata.name,
        metadata+: {
          annotations+: {
            'iam.gke.io/gcp-service-account': spark.gsa.email,
          },
        },
      },

      // IAM Policy allowing the linking.
      workload_identity: k.GCP.IAM.PolicyMember + {
        name:: spark.gsa.metadata.name + '.workload-identity-user',
        spec+: {
          resourceRef: spark.gsa.localKindRef,
          member: 'serviceAccount:%s.svc.id.goog[%s/%s]' % [C.gcp_project, spark.ksa.metadata.namespace, spark.ksa.metadata.name],
          role: 'roles/iam.workloadIdentityUser',
        },
      },

      _bucket_acl:: function(bucket_name, rw=false) k.GCP.IAM.PolicyMember + {
        name:: spark.gsa.name + '.' + bucket_name + '.' + 'storage-object' + (if rw then 'user' else 'viewer'),
        spec+: {
          resourceRef: {
            kind: 'StorageBucket',
            external: bucket_name,
          },
          memberFrom: self.memberFromSA(spark.gsa),
          role: if rw then 'roles/storage.objectUser' else 'roles/storage.objectViewer',
        },
      },

      // GCS RW and RO ACLs
      buckets: [
        spark._bucket_acl(C.name + '-spark-data', rw=true),
        spark._bucket_acl(C.name + '-user', rw=true),
        spark._bucket_acl(C.name + '-external-models', rw=false),
        spark._bucket_acl(C.name + '-python-env', rw=false),
      ],

      // BigQuery ACL, for the whole project because we don't understand the granularity well enough. We also
      // grant a broad principalSet ACL since part of spark (the driver) runs from users' DevPods.
      bq_acl: k.GCP.IAM.PolicyMember + {
        name:: spark.gsa.name + '.' + C.gcp_project + '.' + 'bigquery-data-editor',
        spec+: {
          resourceRef: {
            kind: 'Project',
            external: 'project/' + C.gcp_project,
          },
          memberFrom: self.memberFromSA(spark.gsa),
          role: 'roles/bigquery.dataEditor',
        },
      },
      bq_devpod_acls: [
        k.GCP.IAM.PolicyMember + {
          name:: C.name + '-workload.' + C.gcp_project + '.' + 'bigquery-data-editor',
          spec+: {
            resourceRef: {
              kind: 'Project',
              external: 'project/' + C.gcp_project,
            },
            member: 'principalSet://iam.googleapis.com/projects/%d/locations/global/workloadIdentityPools/%s.svc.id.goog/namespace/%s' % [
              C.gcp_project_number,
              C.gcp_project,
              C.name,
            ],
            role: 'roles/bigquery.dataEditor',
          },
        },
        k.GCP.IAM.PolicyMember + {
          name:: C.name + '-workload.' + C.gcp_project + '.' + 'bigquery-user',
          spec+: {
            resourceRef: {
              kind: 'Project',
              external: 'project/' + C.gcp_project,
            },
            member: 'principalSet://iam.googleapis.com/projects/%d/locations/global/workloadIdentityPools/%s.svc.id.goog/namespace/%s' % [
              C.gcp_project_number,
              C.gcp_project,
              C.name,
            ],
            role: 'roles/bigquery.user',
          },
        },
      ],
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // CI SA
    //

    ci: {
      local ci = self,

      // Google Service Account
      gsa: k.GCP.IAM.ServiceAccount + {
        name: C.name + '-ci-sa',
      },

      // K8s Service Account (with annotation for GSA linking)
      ksa: k.ServiceAccount + {
        name:: 'ci-sa',
        namespace:: k.main_ns.metadata.name,
        metadata+: {
          annotations+: {
            'iam.gke.io/gcp-service-account': ci.gsa.email,
          },
        },
      },

      // IAM Policy allowing the linking.
      workload_identity: k.GCP.IAM.PolicyMember + {
        name:: ci.gsa.metadata.name + '.workload-identity-user',
        spec+: {
          resourceRef: ci.gsa.localKindRef,
          member: 'serviceAccount:%s.svc.id.goog[%s/%s]' % [C.gcp_project, ci.ksa.metadata.namespace, ci.ksa.metadata.name],
          role: 'roles/iam.workloadIdentityUser',
        },
      },

      _bucket_acl:: function(bucket_name, role) k.GCP.IAM.PolicyMember + {
        local shortRole = std.splitLimit(role, '.', 1)[1],
        name:: ci.gsa.name + '.' + bucket_name + '.' + 'storage-object.' + std.asciiLower(shortRole),
        spec+: {
          resourceRef: {
            kind: 'StorageBucket',
            external: bucket_name,
          },
          memberFrom: self.memberFromSA(ci.gsa),
          role: role,
        },
      },

      // GCS RW and RO ACLs
      buckets: [
        [ci._bucket_acl(C.name + '-public-html', role)]
        for role in ['roles/storage.objectUser', 'roles/storage.insightsCollectorService']
      ],
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // Misc ACLs
    //
    main_ns_cluster_ai_admin: k.GCP.IAM.PolicyMember + {
      name:: C.name + '-workload.' + C.gcp_project + '.' + 'aiplatform-admin',
      spec+: {
        resourceRef: {
          kind: 'Project',
          external: 'project/' + C.gcp_project,
        },
        member: 'principalSet://iam.googleapis.com/projects/%d/locations/global/workloadIdentityPools/%s.svc.id.goog/namespace/%s' % [
          C.gcp_project_number,
          C.gcp_project,
          C.main_namespace,
        ],
        role: 'roles/aiplatform.admin',
      },
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Selectively return stages
  //

  ret:: [
    $['stage%d' % [num]]
    for num in stages
  ],

}.ret
