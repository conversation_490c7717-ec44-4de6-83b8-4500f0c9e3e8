function(
  C,
  name='aug-sysctl',
  max_user_watches=1 * 512 * 1024,
) C.k8s + {

  BaseLabels+:: {
    'aug.service': name,
  },
  Object+:: {
    name:: name,
    namespace:: C.sys_namespace,
  },

  daemon: $.DaemonSet + {
    spec+: {
      template+: {
        spec+: {
          enableServiceLinks: false,
          restartPolicy: 'Always',
          tolerations: [
            { operator: 'Exists' },  // empty toleration to run everywhere
          ],
          initContainers: [
            $.Container + {
              name: name,
              image: 'busybox:latest',
              command: ['/bin/sh', '-c'],
              args: [
                |||
                  set -xe
                  sysctl -w fs.inotify.max_user_watches=%(max_user_watches)d
                ||| % {
                  max_user_watches: max_user_watches,
                },
              ],
              securityContext: {
                privileged: true,
              },
            },
          ],
          containers: [
            $.Container + {
              name: 'sleep',
              image: 'busybox:latest',
              command: ['/bin/sleep', 'infinity'],
            },
          ],
        },
      },
    },
  },
}
