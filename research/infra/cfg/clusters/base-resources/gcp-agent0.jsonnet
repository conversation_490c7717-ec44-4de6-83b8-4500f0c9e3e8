local tmpl = import 'agent-workspaces.tmpl.jsonnet';

function(aH=null, stages=[2, 3, 4, 5])
  tmpl(
    aH=aH,
    cluster='gcp-agent0',
    envs=['DEV'],
    ws_dns='ws.dev.augmentcode.com',
    ws_dns_parent='projects/system-services-dev/managedZones/dev-zone',
    // ws_dns_shard='d',
    cluster_desc_prefix='Research Agent',
    glassbreakers=[
      '<EMAIL>',
    ],
    stages=stages,
    test_janitor=true,
  )
