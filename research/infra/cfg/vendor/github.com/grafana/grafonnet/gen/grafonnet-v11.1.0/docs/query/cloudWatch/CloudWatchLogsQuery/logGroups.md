# logGroups



## Index

* [`fn withAccountId(value)`](#fn-withaccountid)
* [`fn withAccountLabel(value)`](#fn-withaccountlabel)
* [`fn withArn(value)`](#fn-witharn)
* [`fn withName(value)`](#fn-withname)

## Fields

### fn withAccountId

```jsonnet
withAccountId(value)
```

PARAMETERS:

* **value** (`string`)

AccountId of the log group
### fn withAccountLabel

```jsonnet
withAccountLabel(value)
```

PARAMETERS:

* **value** (`string`)

Label of the log group
### fn withArn

```jsonnet
withArn(value)
```

PARAMETERS:

* **value** (`string`)

ARN of the log group
### fn withName

```jsonnet
withName(value)
```

PARAMETERS:

* **value** (`string`)

Name of the log group