// Package cfg provides a Jsonnet VM wrapper around jsonnet files in the library.
package cfg

import (
	"embed"
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"github.com/google/go-jsonnet"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/research/environments"
	"github.com/augmentcode/augment/research/infra/lib/augment"
)

//go:embed clusters/*.jsonnet
//go:embed clusters/clusters/*.jsonnet
//go:embed clusters/base-resources/*.jsonnet
var embedFS embed.FS

// VM is a wrapper around a raw Jsonnet VM.
type VM struct {
	vm    *jsonnet.VM
	tlaLk sync.Mutex
	data  map[string]jsonnet.Contents
}

// NewVM builds a new VM wrapper from embedded .jsonnet files.
func NewVM() (*VM, error) {
	return NewVMFromFS(embedFS)
}

func NewEmptyVM() *VM {
	data := map[string]jsonnet.Contents{}
	imp := &RelativeMemoryImporter{
		Data: data,
	}
	vm := jsonnet.MakeVM()
	vm.Importer(imp)

	return &VM{
		vm:   vm,
		data: data,
	}
}

// NewVMFromFS builds a new VM wrapper from .jsonnet files AND *_tag.txt data files.
// NOTE(mattm): If calling with access to the augment repo, the tag data is read from the local filesystem AT STARTUP; if
// calling without access to the augment repo, the tag data is read from embedded data in the binary.
func NewVMFromFS(filesystem fs.ReadFileFS) (*VM, error) {
	data := map[string]jsonnet.Contents{}

	addFS := func(fs0 fs.ReadFileFS, pfx, sfx string) error {
		return fs.WalkDir(fs0, ".", func(p string, d fs.DirEntry, err error) error {
			if err != nil {
				return err
			}
			if d.IsDir() {
				return nil
			}
			if sfx != "" && !strings.HasSuffix(p, sfx) {
				return nil
			}
			buf, err := fs0.ReadFile(p)
			if err != nil {
				return err
			}
			data[pfx+p] = jsonnet.MakeContentsRaw(buf)
			return nil
		})
	}
	if err := addFS(filesystem, "", ".jsonnet"); err != nil {
		return nil, err
	}
	if envpath, err := augment.Dir("research/environments"); err == nil {
		if err := addFS(os.DirFS(envpath).(fs.ReadFileFS), "../../environments/", "_tag.txt"); err != nil {
			return nil, err
		}
	} else {
		if err := addFS(environments.TagFS, "../../environments/", "_tag.txt"); err != nil {
			return nil, err
		}
	}

	imp := &RelativeMemoryImporter{
		Data: data,
	}
	vm := jsonnet.MakeVM()
	vm.Importer(imp)

	return &VM{vm: vm}, nil
}

// AddFile is **not** thread-safe.
func (vm *VM) AddFile(name string, buf []byte) {
	vm.data[name] = jsonnet.MakeContentsRaw(buf)
}

func (vm *VM) evalSnippet(fname, snippet string) (string, error) {
	buf, err := vm.vm.EvaluateAnonymousSnippet(fname, snippet)
	if err != nil {
		lines := []string{}
		for i, snipline := range strings.Split(snippet, "\n") {
			lines = append(lines, fmt.Sprintf("\t%4d: %s", i+1, snipline))
		}
		return "", fmt.Errorf("Input Snippet:\n%s\n%v", strings.Join(lines, "\n"), err)
	}
	return buf, nil
}

// Eval evaluates a json buffer (string) from either a file or a snippet. When `snippet` is
// empty, `fname` is evaluated with the given TLA code args. When `snippet` is not empty,
// it is evaluated with the given TLA coe args and `fname` is only used for formatting errors.
func (vm *VM) Eval(fname string, tla map[string]string, snippet string) (string, error) {
	vm.tlaLk.Lock()
	defer vm.tlaLk.Unlock()
	vm.vm.TLAReset()
	for k, v := range tla {
		vm.vm.TLACode(k, v)
	}

	if snippet == "" {
		return vm.vm.EvaluateFile(fname)
	} else {
		return vm.evalSnippet(fname, snippet)
	}
}

// EvalJSON wraps `Eval()` and calls `json.Unmarshal()`.
func (vm *VM) EvalJSON(fname string, tla map[string]string, snippet string, out any) error {
	buf, err := vm.Eval(fname, tla, snippet)
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(buf), out)
}

func (vm *VM) EvalK8s(fname string, tla map[string]string, snippet string) ([]k8s.Object, error) {
	buf, err := vm.Eval(fname, tla, snippet)
	if err != nil {
		return nil, err
	}
	return k8s.FromJSON([]byte(buf))
}

////////////////////////////////////////////////////////////////////////////////
// RelativeMemoryImporter
//

// RelativeMemoryImporter is like the upstream MemoryImporter, but supports relative paths
// and some special-casing around the *_tag.txt image version data.
type RelativeMemoryImporter struct {
	Data map[string]jsonnet.Contents
}

func (i RelativeMemoryImporter) Import(importedFrom, importedPath string) (jsonnet.Contents, string, error) {
	// First, try the "absolutle" path.
	if content, ok := i.Data[importedPath]; ok {
		return content, importedPath, nil
	}
	// Second, try a relative path.
	if importedFrom != "" {
		dir, _ := filepath.Split(importedFrom)
		rel := dropDots(dir + importedPath)
		if content, ok := i.Data[rel]; ok {
			return content, rel, nil
		}
	}
	// Third, try tag file (which would have been from working tree or embedded data).
	if strings.HasSuffix(importedPath, "_tag.txt") {
		_, fname := filepath.Split(importedPath)
		rel := "../../environments/" + fname
		if content, ok := i.Data[rel]; ok {
			return content, rel, nil
		}
	}
	return jsonnet.Contents{}, "", fmt.Errorf("import not available from %s: %s", importedFrom, importedPath)
}

// dropDots consumes `.` and `..` from paths.
func dropDots(orig string) string {
	parts := []string{}
	for _, part := range strings.Split(orig, "/") {
		if part == "." {
			continue
		} else if part != ".." {
			parts = append(parts, part)
		} else if l := len(parts); l > 0 {
			parts = parts[:l-1]
		}
	}
	return strings.Join(parts, "/")
}
