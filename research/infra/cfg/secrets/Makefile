K8S_CONF := secret.jsonnet
CLUSTERS := cw-east4 gcp-us1

KUBECFG  := kubecfg $$context_flag --tla-code="cluster_name='$$cluster'" --tla-code="encrypted_data='$$enc_data'"

FILES   := $(wildcard eng_secrets/*.json)

show:
	make loop LOOP_COMMAND=show

diff:
	make loop LOOP_COMMAND="diff --diff-strategy=subset"

apply:
	make loop LOOP_COMMAND=update

# the get-contexts grep is to detect if we're using a service account on a pod that
# has no contexts
loop:
	for file in $(FILES); do \
		enc_data=$$(cat $$file); \
		secret_name=$$(basename $$file .json); \
		clusters_in_secret=$$(echo "$$enc_data" | jq -r '.encrypted_data | keys[]' 2>/dev/null || echo ""); \
		for cluster in $(CLUSTERS); do \
			echo ====== "$$cluster" ======; \
			if [ -n "$$clusters_in_secret" ] && ! echo "$$clusters_in_secret" | grep -q "$$cluster"; then \
				echo "Secret $$secret_name not available for cluster $$cluster, skipping"; \
				continue; \
			fi; \
			context=$$(jsonnet -A name="$$cluster" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop") ; \
			available_contexts=$$(kubectl config get-contexts -o name 2>/dev/null | grep "$$context" || echo ""); \
			if [ -z "$$available_contexts" ]; then \
				echo "Context $$context not available for cluster $$cluster, skipping"; \
				continue; \
			fi; \
			context_flag="--context=$$context"; \
			$(KUBECFG) $(LOOP_COMMAND) $(K8S_CONF) || echo "Failed to apply secret for cluster $$cluster, continuing"; \
		done \
	done
