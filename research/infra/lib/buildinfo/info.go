package buildinfo

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

const Unknown = "<unknown>"

var (
	bkVersion   = Unknown
	bkTimestamp = Unknown
	bkBuilder   = Unknown
	bkHostname  = Unknown
	bkBranch    = Unknown
	bkCommit    = Unknown
)

type Info struct {
	Version   string
	Timestamp string // Unix epoch seconds as string
	Builder   string
	Hostname  string
	Branch    string
	Commit    string
}

func StaticInfo() Info {
	return Info{
		Version:   bkVersion,
		Timestamp: bkTimestamp,
		Builder:   bkBuilder,
		Hostname:  bkHostname,
		Branch:    bkBranch,
		Commit:    bkCommit,
	}
}

func CollectInfo() Info {
	return newCollector().Info()
}

func (i Info) Time() (time.Time, error) {
	if str := i.Timestamp; str == "" {
		return time.Time{}, nil
	} else if secs, err := strconv.ParseInt(i.Timestamp, 10, 64); err != nil {
		return time.Time{}, err
	} else {
		return time.Unix(secs, 0), nil
	}
}

func (i Info) TimeString() string {
	if t, err := i.Time(); err != nil {
		return fmt.Sprintf("%s [%v]", i.Timestamp, err)
	} else {
		tstr := t.Local().Format("2006-01-02 15:04 MST")
		return fmt.Sprintf("%s (%s)", tstr, i.Timestamp)
	}
}

func (i Info) Simple() string {
	return i.Version
}

// MultiLine is a human-friendly output of the build info. It's used by the `augi version` subcommand.
func (i Info) MultiLine() string {
	lines := []string{
		"Version:   " + i.Version,
		"Timestamp: " + i.TimeString(),
		"Builder:   " + i.Builder,
		"Hostname:  " + i.Hostname,
		"Branch:    " + i.Branch,
		"Commit:    " + i.Commit,
		"",
	}
	return strings.Join(lines, "\n")
}

// BazelWorkspaceStatusCommand is used when building with `bazel` to stamp in build info. The timestamp and
// hostname aren't included because bazel includes those by default.
func (i Info) BazelWorkspaceStatusCommand() string {
	lines := []string{
		"STABLE_AUGI_VERSION " + i.Version,
		"#UNSTABLE_AUGI_TIMESTAMP " + i.TimeString(),
		"STABLE_AUGI_BUILDER " + i.Builder,
		"#STABLE_AUGI_HOSTNAME " + i.Hostname,
		"STABLE_AUGI_BRANCH " + i.Branch,
		"STABLE_AUGI_COMMIT " + i.Commit,
		"",
	}
	return "printf '" + strings.Join(lines, "\\n") + "'"
}

// LDFlags is used when building with `go build` directly (so not with bazel) to stamp in build info.
func (i Info) LDFlags() string {
	flags := []string{}
	f := func(key, val string) {
		if val != "" {
			flags = append(flags, "-X", "'github.com/augmentcode/augment/research/infra/lib/buildinfo."+key+"="+val+"'")
		}
	}
	f("bkVersion", i.Version)
	f("bkTimestamp", i.Timestamp)
	f("bkBuilder", i.Builder)
	f("bkHostname", i.Hostname)
	f("bkBranch", i.Branch)
	f("bkCommit", i.Commit)
	return strings.Join(flags, " ")
}
