package buildinfo

import (
	"os"
	"strconv"
	"time"

	gogit "github.com/go-git/go-git/v5"

	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/research/infra/lib/augment"
)

type collector struct {
	*logger.Logger

	git *gogit.Repository

	tNow       func() time.Time
	osHostname func() (string, error)
}

func newCollector() collector {
	c := collector{
		Logger: logger.New(nil),

		tNow:       time.Now,
		osHostname: os.Hostname,
	}

	if aug, err := augment.Root(); err != nil {
		c.<PERSON>n("Build Info Collector: Unable to find Augment Repo: %v.", err)
	} else if repo, err := gogit.PlainOpen(aug); err != nil {
		c.Log<PERSON>arn("Build Info Collector: Unable to open Git Repo: %v.", err)
	} else {
		c.git = repo
	}

	return c
}

func (c collector) Info() Info {
	return Info{
		Version:   Unknown,
		Timestamp: c.<PERSON>tam<PERSON>(),
		Builder:   c.Builder(),
		Hostname:  c.Hostname(),
		Branch:    c.<PERSON>(),
		Commit:    c.Commit(),
	}
}

func (c collector) Timestamp() string {
	return strconv.FormatInt(time.Now().Unix(), 10)
}

func (c collector) Builder() string {
	return augment.WhoAmI()
}

func (c collector) Hostname() string {
	if hn, err := c.osHostname(); err != nil {
		c.LogWarn("Build Info Collector: Unable to get hostname: %v.", err)
		return ""
	} else {
		return hn
	}
}

func (c collector) Branch() string {
	if c.git == nil {
		return ""
	} else if ref, err := c.git.Head(); err != nil {
		c.LogWarn("Build Info Collector: Branch(): Unable to get Head: %v.", err)
		return ""
	} else {
		return ref.Name().String()
	}
}

func (c collector) Commit() string {
	if c.git == nil {
		return ""
	} else if ref, err := c.git.Head(); err != nil {
		c.LogWarn("Build Info Collector: Commit(): Unable to get Head: %v.", err)
		return ""
	} else {
		return ref.Hash().String()
	}
}
