package gcs

import (
	"context"
	"errors"
	"fmt"
	"io"
	"sort"
	"strings"

	"cloud.google.com/go/storage"
	ctrlpb "cloud.google.com/go/storage/control/apiv2/controlpb"
	"golang.org/x/sync/errgroup"
	"google.golang.org/api/iterator"

	"github.com/augmentcode/augment/infra/lib/logger"
)

// Bucket is a wrapper around a `BucketHandle`. Like a `BucketHandle`, a `Bucket`
// retains a reference to its original `Client`.
type Bucket struct {
	*logger.Logger
	cli *Client
	bh  *storage.BucketHandle
}

func (b *Bucket) Name() string {
	return b.bh.BucketName()
}

func (b *Bucket) BucketPath() string {
	return "projects/_/buckets/" + b.Name()
}

func (b *Bucket) FoldersPath() string {
	return b.BucketPath() + "/folders"
}

// ListFolders returns GCS Folders (for when Hierarchical Namespaces are enabled). These folders may or may not appear
// as objects in the normal Bucket/Object API. The ones that don't appear are invsibile to GCS FUSE even though they
// affect its performance.
func (b *Bucket) ListFolders(ctx context.Context, prefix string, fullyQualified, stripPrefix bool) ([]string, error) {
	if fullyQualified && stripPrefix {
		return nil, fmt.Errorf("ListFolders() cannot use both fully-qualified and strip-prefix")
	}
	format := func(orig string) string {
		if fullyQualified {
			return orig
		}
		ret := orig
		ret = strings.TrimPrefix(ret, b.FoldersPath()+"/")
		if stripPrefix {
			ret = strings.TrimPrefix(ret, prefix)
		}
		return ret
	}

	ret := []string{}
	req := &ctrlpb.ListFoldersRequest{
		Parent: b.BucketPath(),
		Prefix: prefix,
	}
	for f, err := range b.cli.ctrl.ListFolders(ctx, req).All() {
		if err != nil {
			return nil, err
		}
		ret = append(ret, format(f.GetName()))
	}
	return ret, nil
}

// EmptyFolders returns the subset of folders that appear as Objects but have no "children". This does not use the
// ListFolders() API.
func (b *Bucket) EmptyFolders(ctx context.Context, prefix string) ([]string, error) {
	root, err := b.objectTree(ctx, prefix)
	if err != nil {
		return nil, err
	}
	empty := root.EmptyFolders()
	// Reverse sort is the most convenient for empty dirs, which would need to be deleted in reverse order.
	sort.Sort(sort.Reverse(sort.StringSlice(empty)))
	return empty, nil
}

func (b *Bucket) PrintObjectTree(ctx context.Context, prefix string, w io.Writer) error {
	if root, err := b.objectTree(ctx, prefix); err != nil {
		return err
	} else {
		root.Print("", w)
		return nil
	}
}

func (b *Bucket) Object(name string) *storage.ObjectHandle {
	return b.bh.Object(name)
}

func (b *Bucket) Objects(ctx context.Context, prefix string) ([]*storage.ObjectAttrs, error) {
	it := b.bh.Objects(ctx, &storage.Query{
		Projection: storage.ProjectionNoACL,
		Prefix:     prefix,
	})
	ret := []*storage.ObjectAttrs{}
	for {
		attrs, err := it.Next()
		if err == iterator.Done {
			break
		} else if err != nil {
			return nil, err
		} else {
			ret = append(ret, attrs)
		}
	}
	return ret, nil
}

func (b *Bucket) objectTree(ctx context.Context, prefix string) (*treeItem, error) {
	// To get a total view, we need to get both Folders from the Control API and Objects. Tooling such as the FUSE driver
	// pre-HNS support created empty placeholder objects for folders. Newer Folders don't have such objects.

	grp, gctx := errgroup.WithContext(ctx)

	var folders []string
	grp.Go(func() error {
		var err error
		folders, err = b.ListFolders(gctx, prefix, false, false)
		return err
	})

	var objects []string
	grp.Go(func() error {
		attrs, err := b.Objects(gctx, prefix)
		if err == nil {
			objects = make([]string, len(attrs))
			for i, attr := range attrs {
				objects[i] = attr.Name
			}
		}
		return err
	})

	if err := grp.Wait(); err != nil {
		return nil, err
	}

	names := append(folders, objects...)
	return newTree(b.Name(), names...)
}

// DeleteFolders deletes one or more folders from GCS using the GCS Control `DeleteFolder()` API. It is currently *dumb*
// in that it deletes one at a time, in series. Speeding things up in a worker pool is possible but we need to make sure children
// are deleted before parents.
func (b *Bucket) DeleteFolders(ctx context.Context, dryrun, quiet bool, folders ...string) error {
	// If we have an error deleting a Folder, avoid trying to delete ancestors since we know they'll fail.
	skip := map[string]bool{}
	skipancestors := func(folder string) {
		parts := strings.SplitAfter(folder, "/")
		// The last two items in parts should be [foldername, ""]. So we're done if
		// we don't have at least 3 parts.
		if len(parts) < 3 {
			return
		}
		parts = parts[:len(parts)-2]
		// Skip each successive ancestor.
		for len(parts) > 0 {
			skip[strings.Join(parts, "")] = true
			parts = parts[:len(parts)-1]
		}
	}

	sort.Sort(sort.Reverse(sort.StringSlice(folders)))
	errs := []error{}
	for _, f := range folders {
		if skip[f] {
			b.LogWarn("DeleteFolder(dryrun=%t): %s: skipping due to child error.", dryrun, f)
		} else if err := b.DeleteFolder(ctx, dryrun, quiet, f); err != nil {
			skipancestors(f)
			errs = append(errs, fmt.Errorf("%s: %w", f, err))
		}
	}
	return errors.Join(errs...)
}

// DeleteFolder is a thin wrapper around the GCS Control `DeleteFolder()` API. It adds the fully-qualified folder URI prefix
// if not already present. Note that GCS DeleteFolder() will fail unless the argument is indeed a Folder, and the Folder is empty,
func (b *Bucket) DeleteFolder(ctx context.Context, dryrun, quiet bool, folder string) error {
	if !strings.HasPrefix(folder, b.FoldersPath()) {
		folder = b.FoldersPath() + "/" + folder
	}
	if !quiet {
		b.LogInfo("DeleteFolder(dryrun=%t): %s.", dryrun, folder)
	}
	if dryrun {
		return nil
	}
	req := &ctrlpb.DeleteFolderRequest{Name: folder}
	err := b.cli.ctrl.DeleteFolder(ctx, req)
	if !quiet && err != nil {
		b.LogErr("DeleteFolder(dryrun=%t): %s: %v.", dryrun, folder, err)
	}
	return err
}
