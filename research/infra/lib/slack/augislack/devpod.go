package augislack

import (
	"fmt"

	"github.com/slack-go/slack"
	"github.com/spf13/cobra"
	"k8s.io/apimachinery/pkg/api/resource"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/research/infra/cfg/clusters"
	"github.com/augmentcode/augment/research/infra/lib/augment"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/crd/devpodv1"
)

const (
	targetCluster = "gcp-us1"
)

func (b *Bot) devpodCmd(scmd slack.SlashCommand) *cobra.Command {
	devpod := &cobra.Command{
		Use:     "devpod",
		Aliases: []string{"dp"},
		Short:   "DevPod commands.",
	}
	devpod.AddCommand(b.bootstrapCmd(scmd))
	return devpod
}

func (b *Bot) bootstrapCmd(scmd slack.SlashCommand) *cobra.Command {
	cmd := &cobra.Command{
		Use:     "bootstrap [--force]",
		Args:    cobra.NoArgs,
		Aliases: []string{},
		Short:   fmt.Sprintf("Bootstrap a CPU-only devpod/%s-homepod.", scmd.UserName),

		DisableFlagsInUseLine: true,
	}
	flags := struct {
		force bool
	}{}
	cmd.Flags().BoolVarP(&flags.force, "force", "f", false, fmt.Sprintf("Force when devpod/%s-homepod already exists.", scmd.UserName))
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		/// Basic Info

		user := scmd.UserName
		name := scmd.UserName + "-homepod"

		/// Cluster and Clients

		db, err := clusters.New()
		if err != nil {
			return err
		}

		c, err := db.Cluster(targetCluster)
		if err != nil {
			return err
		}

		k := b.K8s(cmd.ErrOrStderr())

		/// Checkfail on user's SSH keys.

		if cm, err := k.InNamespace(c.MainNamespace).GetConfigMap(cmd.Context(), augment.AuthorizedKeysConfigMap); err != nil {
			return err
		} else if keys := cm.Key(user); keys == "" {
			return fmt.Errorf("no SSH keys found for user %q: please register at https://userauth.r.augmentcode.com", user)
		}

		/// Create bootstrap Spec, Builder, Manager (vanilla 4CPU)

		spec := devpodv1.NewDevPodFromNames(c.Name, user, name, c.MainNamespace)
		spec.DevPod().Resources.CPU = resource.NewQuantity(4, resource.DecimalSI)
		mgr := devpod.NewManagerFromSpec(k, &c, spec)
		mgr.Logger = logger.New(cmd.ErrOrStderr()).WithString("component", "devpod.mgr")
		mgr.Builder().SkipDot = true

		if err := mgr.Spec().Validate(); err != nil {
			return err
		}

		/// Check for existing, or --force.

		if cur, err := devpod.FindSpec(cmd.Context(), k, &c, user, name); k8s.NotFoundOK(err) != nil {
			return err
		} else if cur != nil && !flags.force {
			return fmt.Errorf("devpod/%s already exists, use the --force flag to continue anyway", cur.Name())
		} else if cur != nil && flags.force {
			mgr.LogWarn("devpod/%s already exists, but --force was used so it will first be deleted.", cur.Name())
			if err := mgr.Delete(cmd.Context(), false); err != nil {
				return err
			}
		}

		/// Build the node from the DevPodSpec.

		if node, err := mgr.Spec().DevPod().Node(mgr.Builder().Cluster()); err != nil {
			return err
		} else {
			mgr.Builder().SetNode(node)
		}

		/// Build (all objects).

		set, err := mgr.BuildSet(cmd.Context(), nil)
		if err != nil {
			return err
		}

		/// Apply (all objects).

		if err := mgr.Apply(cmd.Context(), set, false); err != nil {
			return err
		}

		mgr.LogInfo("devpod/%s bootstrapped. Log in and run `augi devpod apply . [--flags]` to further customize it.", spec.Name())

		/// Wait ready.

		if set, err := mgr.WaitReady(cmd.Context(), true); err != nil {
			return err
		} else {
			fmt.Fprint(cmd.OutOrStdout(), set.Status())
		}

		return nil
	}
	return cmd
}
