#!/bin/bash

set -eu -o pipefail

declare -r _DEF_USER="augment"
declare -r _DEF_GROUP="augment"
declare -ri _DEF_UID=1000
declare -ri _DEF_GID=1000
declare -r _DEF_SHELL="/bin/bash"
declare -r _DEF_AUG_USER="/startup/user"
declare -ri _DEF_SSH_PORT=22022
declare -ri _DEF_ET_PORT=0

info() {
    declare -r fmt="$1"
    shift
    printf "[init.sh] $fmt\n" "$@"
}

resolve_flag() {
	declare -r val="$1"
	if [[ "$val" == /* ]] || [[ "$val" == ./* ]]; then
		if [[ -e "$val" ]]; then
			cat "$val"
			return
		fi
	fi
	printf "%s" "$val"
}

usage() {
    cat <<EOF
Usage:
  $0 [options]

  This is the Augment DevPod "init.sh" script. It is the entrypoint for DevPod containers. It does
  the following:
    USER:
      - Deletes user:group $_DEF_UID:$_DEF_GID if name != $_DEF_USER (e.g., in upstream ubuntu image).
      - Creates $_DEF_USER ($_DEF_UID) user.
      - Ensures sudo package installed and $_DEF_USER can sudo w/o password.
      - Ensures XDG_RUNTIME_DIR.
    HOME:
      - Ensure /home/<USER>/ proper ownership and permissions.
      - If /home/<USER>/.launch_pod_dotfiles_unpacked doesn't exist, unpack dotfiles mounted from K8s ConfigMap.
      - Ensures /startup/authorized_keys are in ~/.ssh/authorized_keys
      - Ensures ~/.ssh/authorized_keys ownership and permissions
    SYSTEM:
      - Mostly /etc/profile.d/* snippets to be run during each login.
      - Run user's ~/.config/post-init.d/* snippets.
      - Run augi release sync && augi release update in a background loop.
    SSH:
      - Ensure openssh-server package is installed.
      - Set port=$_DEF_SSH_PORT, no root, no passwords.
      - If using centralized SSH keys, set AuthorizedKeysCommand, disabled AuthorizedKeysFile.
      - Point hostkeys to persistent storage and generate them if needed.
      - Also run EterninalTerminal if requested.
    SPARK:
      - When running the image in --spark mode, use the Spark entrypoint instead.

   The PID 1 process tree will look like:

    - dumb-init
     - init.sh
       - sshd
       - etserver
       - ... others?
     - /bin/bash ...
     - /bin/bash ...

  Options:
    --user="$_DEF_USER"
    --group="$_DEF_GROUP"
    --uid="$_DEF_UID"
    --gid="$_DEF_GID"
    --shell="$_DEF_SHELL"
    --augment-user="$_DEF_AUG_USER"
    --ssh-port="$_DEF_SSH_PORT"
    --et-port="$_DEF_ET_PORT"  (eternalterminal, disabled by default, enable with --et-port=22023)
    --docker (default false)
    --timezone="UTC"
EOF
}

main() {
    info "Augment Dev Pod, Starting..."

    if [[ "$0" == "/startup/init.sh" && -x "/usr/local/bin/init.sh" ]]; then
        info "Switching to image init.sh..."
        exec /usr/local/bin/init.sh "$@"
    fi

    if (( $$ == 1 )) && [[ -x /usr/bin/dumb-init ]]; then
        info "Re-exec'ing with dumb-init..."
        exec /usr/bin/dumb-init --verbose -- /bin/bash "$0" "$@"
    fi

    eval set -- $(getopt -n "$0" -o 'h' --long 'user:,group:,uid:,gid:,shell:,augment-user:,ssh-port:,et-port:,docker,spark,timezone:,help,usage' -- "$@")

    declare user="$_DEF_USER"
    declare group="$_DEF_GROUP"
    declare -i uid="$_DEF_UID"
    declare -i gid="$_DEF_GID"
    declare augment_user="$_DEF_AUG_USER"
    declare -i ssh_port="$_DEF_SSH_PORT" et_port="$_DEF_ET_PORT"
    declare -i docker=0 spark=0
    declare shell="$_DEF_SHELL" timezone=""

    while true; do
        case "$1" in
        --) shift; break;;
        --user) user="$2"; shift 2; continue;;
        --group) group="$2"; shift 2; continue;;
        --uid) uid="$2"; shift 2; continue;;
        --gid) gid="$2"; shift 2; continue;;
        --shell) shell="$2"; shift 2; continue;;
        --augment-user) augment_user="$2"; shift 2; continue;;
        --ssh-port) ssh_port="$2"; shift 2; continue;;
        --et-port) et_port="$2"; shift 2; continue;;
        --docker) docker=1; shift 1; continue;;
        --spark) spark=1; shift 1; continue;;
        --timezone) timezone="$2"; shift 2; continue;;
        -h|--help) usage; return 0;;
        *)
            printf "Invalid option: %s\n" "$1" >&2
            usage
            return 1
            ;;
        esac
    done

    declare -r user="$(resolve_flag "$user")"
    declare -r group="$(resolve_flag "$group")"
    declare -ri uid="$(resolve_flag "$uid")"
    declare -ri gid="$(resolve_flag "$gid")"
    declare -r shell
    declare -r augment_user="$(resolve_flag "$augment_user")"
    declare -ri ssh_port et_port
    declare -r docker spark timezone

    info "USER %s:%s (%d:%d)" "$user" "$group" "$uid" "$gid"
    info "SHELL: %s" "$shell"
    info "AUGMENT USER: %s" "$augment_user"
    info "SSH PORT: %d" "$ssh_port"
    (( et_port > 0 )) && info "EternalTerminal PORT: %d" "$et_port"
    info "TIMEZONE: %s" "${timezone:-"UTC (default)"}"

    user_setup "$user" "$group" "$uid" "$gid" "$shell" "$augment_user"
    user_home_setup "$user" "$group"
    system_setup "$user" "$timezone"
    user_init "$user"

    if (( $spark )); then
        info "Running in --spark mode, some other flags are ignored, and no SSH will be started."
        cd /home/"$user"
        info "Execing spark entrypoint as %s (%d): /opt/spark/kubernetes/dockerfiles/spark/entrypoint.sh %s" "$user" "$uid" "$*"
        exec sudo -i -u "$user" -- /opt/spark/kubernetes/dockerfiles/spark/entrypoint.sh "$@"
        return 1 # We shouldn't get here, but if we do it's an error.
    fi

    (( $docker )) && run_docker "$user"
    run_ssh "$ssh_port" "$augment_user"
    (( "$et_port" > 0 )) &&  run_eternal_terminal "$et_port"
    run_augi_updates

    info "All services started, you may SSH in now!"
    trap 'info "All services have stopped, exiting. Good Bye!"' EXIT
    wait
}

run_ssh() {
    declare -ri ssh_port="$1"
    declare -r augment_user="$2"

    # HostKeys are in a separate dir so they can live on a volume.
    # The "etc/ssh" part stutters because that's how `ssh-keygen -A -f` works...
    declare -r hostkey_mount="/etc/ssh/hostkeys"
    declare -r hostkey_dir="$hostkey_mount/etc/ssh"
    declare -ra hostkey_algs=(rsa ecdsa ed25519)

    if ! type sshd >/dev/null 2>&1; then
        info "Uh oh, SSH isn't installed... Trying to install..."
        apt-get update -y
        apt-get install -y openssh-server
    fi

    info "Configuring SSH..."
    install -oroot -groot -m644 -D /dev/stdin /etc/ssh/sshd_config.d/00-augment-dev-pod.conf <<<$(
        for alg in "${hostkey_algs[@]}"; do
            printf "HostKey /etc/ssh/ssh_host_%s_key\n" "$alg"
        done
        printf "\n"
        printf "Port %d\n" "$ssh_port"
        printf "PermitRootLogin no\n"
        printf "PasswordAuthentication no\n"

        if [[ ! -r /startup/authorized_keys && -n "$augment_user" ]]; then
            printf "\n"
            printf "AuthorizedKeysFile none\n"
            printf "AuthorizedKeysCommandUser nobody\n"
            printf "AuthorizedKeysCommand /usr/bin/cat /etc/ssh/authorized_keys/%s\n" "$augment_user"
        fi
    )

    # Clear out any hostkeys that are present in the image (eww).
    rm -f /etc/ssh/*_key{,.pub}

    # Manage the keys as symlinks from their default location so that any SSH updates, dpkg-reconfigure, etc doesn't
    # think that new keys are needed.
    declare -i missing_keys=0
    for alg in "${hostkey_algs[@]}"; do
        ln -sf "$hostkey_dir/ssh_host_${alg}_key" "/etc/ssh/ssh_host_${alg}_key"
        ln -sf "$hostkey_dir/ssh_host_${alg}_key.pub" "/etc/ssh/ssh_host_${alg}_key.pub"
        [[ -r "$hostkey_dir/ssh_host_${alg}_key" ]] || missing_keys=1
    done

    if (( !missing_keys )); then
        info "SSH HostKeys are already generated."
    else
        info "Generating SSH HostKeys..."
        mkdir -p "$hostkey_dir"
        /usr/bin/ssh-keygen -A -f "$hostkey_mount"
    fi

    info "Starting SSH (port %d)..." "$ssh_port"
    mkdir -p /run/sshd
    $(type -p sshd) -eD &
}

run_eternal_terminal() {
    if ! type etserver > /dev/null 2>&1; then
        info "Uh oh, EternalTerminal isn't installed... Trying to install..."
        add-apt-repository ppa:jgmath2000/et
        apt-get update -y
        apt-get install -y et-server
    fi
    info "Starting EternalTerminal (port %d)..." "$et_port"
    $(type -p etserver) --port="$et_port" --telemetry=false --logtostdout &
}

run_augi_updates() {
    declare -i ret
    while true; do
	info "Running augi release sync..."
	augi release sync --config-file /run/augment/secrets/augi-release-readers/.dockerconfigjson && ret="$?" || ret="$?"
	info "augi release sync exited with code=%d" "$ret"

	info "Running augi release update..."
	augi release update && ret="$?" || ret="$?"
	info "augi release update exited with code=%d" "$ret"

	info "Updating completions..."
	augi completion bash > /usr/local/share/bash-completion/completions/augi
	augi completion zsh  > /usr/local/share/zsh/site-functions/_augi
	augi completion fish > /usr/local/share/fish/vendor_completions.d/augi.fish

	sleep 10m || true # allow for killing sleep to nudge updater
    done &
}

run_docker() {
    declare -r _USER="$1"

    info "Adding user %s to docker group..." "$_USER"
    gpasswd -a "$_USER" docker

    declare -i ret_containerd
    while true; do
        info "Running containerd..."
        containerd && ret_containerd="$?" || ret_containerd="$?"
        info "containerd exited with code=%d" "$ret_containerd"
        sleep 5s
    done &

    declare -i ret_dockerd
    while true; do
        info "Running dockerd..."
        dockerd -H tcp://127.0.0.1:2375 -H unix:///var/run/docker.sock --containerd=/run/containerd/containerd.sock --data-root="/home/<USER>/docker" && ret_dockerd="$?" || ret_dockerd="$?"
        info "dockerd exited with code=%d" "$ret_dockerd"
        sleep 5s
    done &
}

user_setup() {
    declare -r _USER="$1" _GROUP="$2"
    declare -ri _UID="$3" _GID="$4"
    declare -r _SHELL="$5"
    declare -r augment_user="$6"

    # Normally, we're using one of our Augment base images, but we should also
    # try to work with other upstream images with the --custom-image flag. We
    # want to ensure that the augment:augment (1000:1000) user:group is
    # available. At some point, ubuntu images changes from no default 1000:1000
    # user, ubuntu:ubuntu.

    info "Checking for user:group '%s:%s' as %d:%d..." "$_USER" "$_GROUP" "$_UID" "$_GID"
    declare -r cur_user="$(id -nu "$_UID" 2>/dev/null || true)"
    declare -r cur_group="$(id -ng "$_GID" 2>/dev/null || true)"

    if [[ "$cur_group" == "$_GROUP" ]]; then
        info "Group '%s' already exists." "$_GROUP"
    else
        if [[ -n "$cur_group" ]]; then
            info "Deleting existing group %d (%s)..." "$_GID" "$cur_group"
            groupdel "$cur_group" || true
        fi
        info "Creating group '%s' (%d)..." "$_GROUP" "$_GID"
        groupadd -g "$_GID" "$_GROUP"
    fi

    if [[ "$cur_user" == "$_USER" ]]; then
        info "User '%s' already exists." "$_USER"
    else
        if [[ -n "$cur_user" ]]; then
            info "Deleting existing user %d (%s)..." "$_UID" "$cur_user"
            userdel "$cur_user"
            info "Creating group '%s' (%d)..." "$_GROUP" "$_GID"
            groupadd -g "$_GID" "$_GROUP"
        fi
        info "Creating %s:%s (%d:%d)..." "$_USER" "$_GROUP" "$_UID" "$_GID"
        useradd -u "$_UID" -g "$_GID" -N --shell "$_SHELL" --no-create-home "$_USER"
	gpasswd -a "$_USER" "$_GROUP"

        if ! type sudo >/dev/null 2>&1; then
            info "Warning, sudo not available. Trying to install..."
            apt-get update -y
            apt-get install -y sudo
        fi

        info "Giving %s sudo NOPASSWD access." "$_USER"
        install -o root -g root -m750 -d /etc/sudoers.d
        install -o root -g root -m400 /dev/stdin /etc/sudoers.d/zzz-"$_USER" <<<"$(printf "%s ALL=(ALL:ALL) NOPASSWD: ALL\n" "$_USER")"
    fi

    # This is baked into the image, remove when running as the per-user username.
    if [[ "$_USER" != "augment" ]]; then
        rm -fr /etc/sudoers.d/zzz-augment
    fi

    declare -r cur_shell="$(getent passwd "$_USER" | cut -d: -f7)"
    if [[ "$cur_shell" != "$_SHELL" ]]; then
        info "Changing shell for %s from %s to %s..." "$_USER" "$cur_shell" "$_SHELL"
        usermod -s "$_SHELL" "$_USER"
    fi

    # Also ensure XDG_RUNTIME_DIR.
    # This should really be done upon login, also setting XDG_RUNTIME_DIR, but "login"
    # is not well-defined for a k8s pod.
    info "Creating XDG_RUNTIME_DIR..."
    install -o "$_USER" -g "$_GROUP" -m700 -d "/run/user/$(id -u "$_USER")"
}

user_home_setup() {
    declare -r _USER="$1" _GROUP="$2"
    declare -r _HOME="/home/<USER>"
    declare -r _UNPACK="$_HOME/.launch_pod_dotfiles_unpacked"

    # We may be in an ephemeral pod with no persistent HOME and things leftover from Dockerfile. Or
    # we may be booting first time with a new home volume (mounted at /home OR /home/<USER>
    # may be booting into an aready-configured HOME, so be as idempotent as possible.

    # Ensure HOME exists, with sane top-level permissions.
    if [[ ! -d "$_HOME" ]]; then
        info "Creating homedir %s..." "$_HOME"
        install -o"$_USER" -g"$_GROUP" -d "$_HOME"
    else
        info "Not creating homedir %s (already exists)." "$_HOME"
    fi
    info "Setting homedir permissions (chmod u=rwx,g=rx,o= %s)..." "$_HOME"
    chmod u=rwx,g=rx,o= "$_HOME"
    info "Setting homedir ownership (chown %s:%s %s)..." "$_USER" "$_GROUP" "$_HOME"
    chown "$_USER:$_GROUP" "$_HOME"

    # Unpack the dotfiles, just once. (Users can remove the "unpack" file to do it again).
    if [[ ! -e /startup/dotfiles.tgz.b64 ]]; then
        info "No dotfiles to unpack."
    elif [[ ! -e "$_UNPACK" ]]; then
        info "Unpacking dotfiles to %s..." "$_HOME"
        base64 -d /startup/dotfiles.tgz.b64 | sudo -u "$_USER" tar xzf - -C "$_HOME"
        sudo -u "$_USER" touch "$_UNPACK"
    else
        info "NOT unpacking dotfiles; remove %s to do so on next boot." "$_UNPACK"
    fi

    if [[ -r /startup/authorized_keys ]]; then
        # SSH: Append the auto-generated authorized_keys from launch_pod.py (only once).
        # TODO(mattm): Stop doing this and rely on users' creds on end laptops AND/OR
        # their stored creds from "userauth".
        mkdir -p "$_HOME"/.ssh
        if ! grep -q "$(cat /startup/authorized_keys)" "$_HOME"/.ssh/authorized_keys; then
            info "Appending /startup/authorized_keys to %s/.ssh/authorized_keys." "$_HOME"
            cat /startup/authorized_keys >> "$_HOME"/.ssh/authorized_keys
        fi

        # SSH: Enforce good perms, bad ones can break login.
        info "Enforcing permissions on .ssh and .ssh/authorized_keys."
        chmod 700 "$_HOME"/.ssh
        chmod 600 "$_HOME"/.ssh/authorized_keys
        chown "$_USER:$_GROUP" "$_HOME"/.ssh "$_HOME"/.ssh/authorized_keys
    fi

    # From dev_vm/10_user.sh.
    if [[ ! -f "$_HOME"/.augment/user.json && ! -f "$_HOME/.augment" ]]; then
        declare -r whoami="$(sudo -u "$_USER" augi whoami || true)"
        if [[ "$whoami" ]]; then
            info "Creating .augment/user.json for %s." "$whoami"
            install -o"$_USER" -g"$_GROUP" -m0750 -d "$_HOME"/.augment
            install -o"$_USER" -g"$_GROUP" -m0640 <(jq -n --arg whoami "$whoami" '{"name": $whoami}') "$_HOME"/.augment/user.json
        else
            info "Not creating .augment/user.json: 'augi whoami' failed."
        fi

    fi
}

user_init() {
    declare -r _USER="$1"
    declare -r _HOME="/home/<USER>"
    declare -r _INIT_D="$_HOME/.config/pod-init.d"

    if type -t rustup > /dev/null 2>&1; then
        if sudo -i -u "$_USER" rustup toolchain list | grep 'no installed toolchains'; then
            info "rustup detected no installed toolchains, installing stable and setting as default (in the background)."
            sudo -i -u "$_USER" rustup default stable &
        fi
    fi

    # NOTE(mattm): sudo -D requires custom policy in recent versions of sudo. Instead, do a `cd` in
    # a subshell.
    (
        declare f
        cd "$_HOME"
        for f in "$_INIT_D"/*; do
            [[ -x "$f" ]] || continue
            info "Executing user pod-init.d snippet: %s...\n" "$f"
            sudo -u "$_USER" "$f" || true
            info "Executing user pod-init.d snippet: %s [DONE].\n" "$f"
        done
    )
}

system_setup() {
    declare -r _USER="$1"
    declare -r _TIMEZONE="${2:-}"

    # Save the current environment (which should be the original from the image).
    /usr/bin/env | sort > /run/original.env

    cat > /etc/profile.d/00-original-env.sh <<"EOF"
if [[ -r /run/original.env ]]; then
    while read -r line; do
        export "$line"
    done < <(grep -Ev '^(HOME|HOSTNAME|USER|_|BASH.*|PWD|SHELL|SHLVL|SSH.*|DEBIAN_FRONTEND)=' /run/original.env)
fi
EOF

    # Timezone support.
    if [[ "$_TIMEZONE" ]]; then
        info "TIMEZONE: Setting to %s..." "$_TIMEZONE"
        ln -sf ../usr/share/zoneinfo/"$_TIMEZONE" /etc/localtime
    else
        info "TIMEZONE: Not changing (default is UTC)."
    fi

    # Fix the weird install/mount of nvidia-smi on GCP boxes
    if [[ -d /usr/local/nvidia ]]; then
        rm -rf /usr/local/nv
        mkdir -p /usr/local/nv
        cp -R /usr/local/nvidia/* /usr/local/nv/
    fi

    # Workaround known issue seen in nvcr.io/nvidia/pytorch:24.05-py3 (/usr/bin owned by 1000:1000)
    chown root:root   / /usr /usr/bin /usr/bin/cat
    chmod u=rwx,go=rx / /usr /usr/bin /usr/bin/cat

    # Update the path to find the new nvidia location (if necessary) and
    # cuda for nvcc
    printf 'export PATH=/usr/local/nv/bin:/usr/local/cuda/bin:$PATH\n'     >  /etc/profile.d/augment-nvidia.sh
    printf 'export LD_LIBRARY_PATH=/usr/local/nv/lib64:$LD_LIBRARY_PATH\n' >> /etc/profile.d/augment-nvidia.sh

    if [[ -n $(curl -sH "Metadata-Flavor:Google" metadata.google.internal/computeMetadata/v1/instance/zone | awk -F/ '{print $NF}') ]]; then
        if [[ -d /usr/local/nvidia/lib64 ]]; then
            if ! ldconfig -p | grep -q libcuda.so; then
                ldconfig /usr/local/nvidia/lib64
            fi
        fi
        GPU_TYPE=$(LD_LIBRARY_PATH=/usr/local/nvidia/lib64 /usr/local/nv/bin/nvidia-smi --query-gpu gpu_name --format=csv,noheader | head -1 || true)
        if [[ "$GPU_TYPE" = "NVIDIA H100 80GB HBM3" ]]; then
            printf 'export NCCL_LIB_DIR="/usr/local/nvidia/lib64"\n' >> /etc/profile.d/augment-nvidia.sh
            printf 'source $NCCL_LIB_DIR/nccl-env-profile.sh\n' >> /etc/profile.d/augment-nvidia.sh
            printf 'export NCCL_FASTRAK_LLCM_DEVICE_DIRECTORY=/dev/aperture_devices\n' >> /etc/profile.d/augment-nvidia.sh
            printf 'export NCCL_NET_PLUGIN="none"' >> /etc/profile.d/augment-nvidia.sh
            printf 'export NCCL_TUNER_PLUGIN="UNUSED"' >> /etc/profile.d/augment-nvidia.sh
            printf 'export NCCL_SHIMNET_SHIM_LAYERS="UNUSED"' >> /etc/profile.d/augment-nvidia.sh
        fi
    fi

    printf "\n" >> /root/.bashrc
    printf "# NOTE(mattm): A typical 'kubectl exec -ti POD -- bash' will result in being root\n" >> /root/.bashrc
    printf "# without all of the /etc/profile.d environment. Detect this as an interactive,\n" >> /root/.bashrc
    printf "# non-login shell, with a tty, and a ppid of 0. Then, login as the augment user.\n" >> /root/.bashrc
    printf "if ! shopt -q login_shell && tty -s && [[ \$(ps -p \$\$ -o ppid:1=) == 0 ]]; then\n" >> /root/.bashrc
    printf "    echo 'NOTE(%s): Auto-switching from root to an augment login.'\n" "$_USER" >> /root/.bashrc
    printf "    exec login -f %s\n" "$_USER" >> /root/.bashrc
    printf "fi\n" >> /root/.bashrc

    cat > /etc/profile.d/00-augment-xdg.sh <<"EOF"
XDG_RUNTIME_DIR=/run/user/$(id -u)
[[ -d "$XDG_RUNTIME_DIR" ]] && export XDG_RUNTIME_DIR
export XDG_CONFIG_DIR="$HOME/.config"
EOF

    # Install the profile script
    cat > /etc/profile.d/augment-paths.sh <<"EOF"
add_path() {
    local path
    for path in "$@"; do
        [[ "${PATH#*$path:}" == "$PATH" ]] || continue
        export PATH="$path:$PATH"
    done
}

add_path /usr/local/go/bin
add_path /opt/conda/bin
add_path "$HOME"/.local/bin
EOF

    cat > /etc/profile.d/augment-kubeconfig.sh <<'EOF'
# Kubeconfigs use first match as priority. Setup the following order:
#
#   0. $XDG_CONFIG_DIR/kube/config
#   1. $XDG_CONFIG_DIR/kube/config.d/*.conf [00 -> ZZ]
#   2. $HOME/.kube/config
#   3. $HOME/.kube/config.d/*.conf [00 -> ZZ]
#   4. $XDG_RUNTIME_DIR/kubeconfig.d/*.conf [00 - ZZ]

KUBECONFIG="$KUBECONFIG${KUBECONFIG:+":"}$XDG_CONFIG_DIR/kube/config"
for f in "$XDG_CONFIG_DIR"/kube/config.d/*.conf; do
    [[ -r "$f" ]] && KUBECONFIG="$KUBECONFIG:$f"
done

KUBECONFIG="$KUBECONFIG:$HOME/.kube/config"
for f in "$HOME"/.kube/config.d/*.conf; do
    [[ -r "$f" ]] && KUBECONFIG="$KUBECONFIG:$f"
done

for f in "$XDG_RUNTIME_DIR"/kubeconfig.d/*.conf; do
    [[ -r "$f" ]] && KUBECONFIG="$KUBECONFIG:$f"
done

# Revert back to a single file, since a number of k8s libraries don't handle multiple paths
for _p in $(tr ':' $'\n' <<<"$KUBECONFIG"); do
  if [[ -e "$_p" ]]; then
    KUBECONFIG="$_p"
    break
  fi
done
unset _p

export KUBECONFIG
EOF

    install -oroot -groot -m0644 /dev/stdin /etc/profile.d/90-augment-gcloud-login.sh <<'EOF'
# If interactive, not a system user, and available
if [ "$PS1" -a `id -u` -ge 1000 ] && command -v gcloud-login > /dev/null 2>&1; then
    gcloud-login --quiet
fi
EOF

    install -oroot -groot -m0644 /dev/stdin /etc/profile.d/90-augment-docker-config-setup.sh <<'EOF'
# If interactive, not a system user, and available
if [ "$PS1" -a `id -u` -ge 1000 ] && command -v gcloud-login > /dev/null 2>&1; then
    # NOTE(mattm): Replace this with an `augi docker setup` command.
    if type jq >/dev/null 2>&1; then
        if [ ! -e ~/.docker/config.json ]; then
            mkdir -p ~/.docker
            echo '{}' > ~/.docker/config.json
        fi

        mv ~/.docker/config.json ~/.docker/config.json.bak

        # $h is the helper
        # $regions is a space-sparated list of regions we care about
        # Overwrite helpers for the above regions. (To switch to only setting as a default if not already set,
        # add a trailing '* .' to the jq filter but leave the leading '. *' to preserve field ordering.
        jq --arg h gcloud --arg regions 'us-central1 europe-west4 asia-southeast1' \
            '. * {"credHelpers": (reduce ($regions|split(" "))[] as $r ({}; . += {($r+"-docker.pkg.dev"): $h}))}' \
            ~/.docker/config.json.bak \
            > ~/.docker/config.json
    fi
fi
EOF
}

main "$@"
