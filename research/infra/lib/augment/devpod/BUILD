load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//research/infra:internal"])

go_library(
    name = "devpod",
    srcs = [
        "builder.go",
        "diff.go",
        "dotfiles.go",
        "init.go",
        "manager.go",
        "set.go",
    ],
    embedsrcs = ["init.sh"],
    importpath = "github.com/augmentcode/augment/research/infra/lib/augment/devpod",
    deps = [
        "//infra/lib/distribution",
        "//infra/lib/k8s",
        "//infra/lib/logger",
        "//infra/lib/ssh",
        "//infra/lib/utils",
        "//research/infra/cfg/clusters",
        "//research/infra/lib/augment",
        "//research/infra/lib/augment/devpod/crd/devpodv1",
        "//research/infra/lib/augment/devpod/spec",
        "@com_github_pmezard_go_difflib//difflib",
        "@com_github_thediveo_go_asciitree//:go-asciitree",
        "@io_k8s_api//apps/v1:apps",
        "@io_k8s_api//core/v1:core",
        "@io_k8s_apimachinery//pkg/api/resource",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_client_go//applyconfigurations/apps/v1:apps",
        "@io_k8s_client_go//applyconfigurations/core/v1:core",
        "@io_k8s_client_go//applyconfigurations/meta/v1:meta",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "devpod_test",
    srcs = [
        "builder_test.go",
        "dotfiles_test.go",
        "init_test.go",
    ],
    embed = [":devpod"],
    deps = [
        "//infra/lib/k8s",
        "//infra/lib/ssh",
        "//research/infra/cfg/clusters",
        "//research/infra/lib/augment/devpod/spec",
        "@com_github_google_go_cmp//cmp",
        "@com_github_google_go_cmp//cmp/cmpopts",
        "@io_k8s_api//core/v1:core",
        "@io_k8s_apimachinery//pkg/api/resource",
    ],
)
