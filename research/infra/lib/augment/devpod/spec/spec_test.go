package spec

import (
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"

	"github.com/augmentcode/augment/research/infra/cfg/clusters"
)

func quanT(t *testing.T, s string) *resource.Quantity {
	t.Helper()
	q, err := resource.ParseQuantity(s)
	if err != nil {
		t.Fatalf("resource.ParseQuantity(%s): %v.", s, err)
	}
	return &q
}

func TestExposedPortsFrom(t *testing.T) {
	t.<PERSON>()
	tests := map[string]struct {
		in      any
		want    ExposedPorts
		wantErr string
	}{
		"int32": {
			in:      int32(42),
			want:    ExposedPorts{{Port: 42}},
			wantErr: "",
		},
		"int32-zero": {
			in:      int32(0),
			want:    ExposedPorts{{}},
			wantErr: "",
		},

		"string-port": {
			in:      "42",
			want:    ExposedPorts{{Port: 42}},
			wantErr: "",
		},
		"string-port-target": {
			in:      "42:4242",
			want:    ExposedPorts{{Port: 42, Target: 4242}},
			wantErr: "",
		},
		"string-port-empty-target": {
			in:      "42:",
			want:    ExposedPorts{{Port: 42}},
			wantErr: "",
		},
		"string-name-port-target": {
			in:      "port42:42:4242",
			want:    ExposedPorts{{Name: "port42", Port: 42, Target: 4242}},
			wantErr: "",
		},
		"string-name-port-empty-target": {
			in:      "port42:42:",
			want:    ExposedPorts{{Name: "port42", Port: 42}},
			wantErr: "",
		},
		"string-empty": {
			in:      "",
			want:    ExposedPorts{},
			wantErr: ": port must be an int32",
		},
		"string-too-many-fields": {
			in:      ":2:3:",
			want:    ExposedPorts{},
			wantErr: ":2:3:: must be in the form",
		},
		"string-parse-port-error": {
			in:      "0:_nan_:0",
			want:    ExposedPorts{},
			wantErr: `0:_nan_:0: port must be an int32: strconv.ParseInt: parsing "_nan_"`,
		},
		"string-parse-target-error": {
			in:      "0:0:_nan_",
			want:    ExposedPorts{},
			wantErr: `0:0:_nan_: target must be an int32: strconv.ParseInt: parsing "_nan_"`,
		},

		"unhandled-type-int": {
			in:      int(42),
			want:    ExposedPorts{},
			wantErr: "42 of type int: unsupported type",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got, gotErr := ExposedPortsFrom(tc.in)
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}
			if diff := cmp.Diff(tc.want, got); diff != "" {
				t.Errorf("ExposedPortFrom(%v): -want +got:\n%s", tc.in, diff)
			}
		})
	}
}

func TestEqualIDs(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		a    DevPod
		b    DevPod
		want bool
	}{
		"empty/empty": {
			a:    DevPod{},
			b:    DevPod{},
			want: true,
		},
		"full/full": {
			a: DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
			},
			b: DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
			},
			want: true,
		},
		"cluster-mismatch": {
			a: DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
			},
			b: DevPod{
				ClusterName: "cluster1",
				UserName:    "user0",
				DevPodName:  "name0",
			},
			want: false,
		},
		"user-mismatch": {
			a: DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
			},
			b: DevPod{
				ClusterName: "cluster0",
				UserName:    "user1",
				DevPodName:  "name0",
			},
			want: false,
		},
		"name-mismatch": {
			a: DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
			},
			b: DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name1",
			},
			want: false,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			if got, want := tc.a.EqualIDs(tc.b), tc.want; got != want {
				t.Errorf("got %v, want %v", got, want)
			}
		})
	}
}

func TestID(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		in   DevPod
		want DevPod
	}{
		"empty": {
			in:   DevPod{},
			want: DevPod{},
		},
		"full": {
			in: DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
			},
			want: DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
			},
		},
		"extra": {
			in: DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				DockerMode:  true,
			},
			want: DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got := tc.in.ID()
			if diff := cmp.Diff(tc.want, got); diff != "" {
				t.Errorf("ID(): -want +got:\n%s", diff)
			}
		})
	}
}

func TestJSON(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		in      DevPod
		wantErr string
		want    []string
	}{
		"emtpy": {
			in:      DevPod{},
			wantErr: "",
			want: []string{
				`{`,
				`  "cluster_name": "",`,
				`  "user_name": "",`,
				`  "devpod_name": "",`,
				// TODO(mattm): Figure out why these aren't omitted.
				`  "resources": {},`,
				`  "cxn": {},`,
				`  "image": {},`,
				`  "home": {},`,
				`  "volumes": {},`,
				`  "os": {}`,
				`}`,
				``,
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got, gotErr := tc.in.JSON()
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}
			if diff := cmp.Diff(strings.Join(tc.want, "\n"), got); diff != "" {
				t.Errorf("JSON(): -want +got:\n%s", diff)
			}
			// Test the reverse, for coverage.
			if rev, err := FromJSON(got); err != nil {
				t.Errorf("FromJSON(): %v.", err)
			} else if diff := cmp.Diff(tc.in, *rev); diff != "" {
				t.Errorf("FromJSON(): -want +got:\n%s", diff)
			}
		})
	}
}

func TestYAML(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		in      DevPod
		wantErr string
		want    []string
	}{
		"emtpy": {
			in:      DevPod{},
			wantErr: "",
			want: []string{
				`cluster_name: ""`,
				`user_name: ""`,
				`devpod_name: ""`,
				``,
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got, gotErr := tc.in.YAML()
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}
			if diff := cmp.Diff(strings.Join(tc.want, "\n"), got); diff != "" {
				t.Errorf("YAML(): -want +got:\n%s", diff)
			}
			// Test the reverse, for coverage.
			if rev, err := FromYAML(got); err != nil {
				t.Errorf("FromYAML(): %v.", err)
			} else if diff := cmp.Diff(tc.in, *rev); diff != "" {
				t.Errorf("FromYAML(): -want +got:\n%s", diff)
			}
		})
	}
}

func TestPower(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		spec    DevPod
		wantHas bool
		wantOff bool
	}{
		"empty": {
			spec:    DevPod{},
			wantHas: false,
			wantOff: false,
		},
		"nil": {
			spec:    DevPod{PowerOff_p: nil},
			wantHas: false,
			wantOff: false,
		},
		"off": {
			spec:    DevPod{PowerOff_p: func() *bool { b := false; return &b }()},
			wantHas: true,
			wantOff: false,
		},
		"on": {
			spec:    DevPod{PowerOff_p: func() *bool { b := false; return &b }()},
			wantHas: true,
			wantOff: false,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			if got, want := tc.spec.HasPowerOff(), tc.wantHas; got != want {
				t.Errorf("has: got %v, want %v", got, want)
			}
			if got, want := tc.spec.PowerOff(), tc.wantOff; got != want {
				t.Errorf("off: got %v, want %v", got, want)
			}
			if got, want := tc.spec.PowerOn(), !tc.wantOff; got != want {
				t.Errorf("on: got %v, want %v", got, want)
			}
		})
	}
}

func TestServicePort(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inPort   ExposedPort
		wantPort corev1.ServicePort
	}{
		"empty": {
			inPort: ExposedPort{},
			wantPort: corev1.ServicePort{
				Name: "0",
			},
		},
		"just-port": {
			inPort: ExposedPort{
				Port: 42,
			},
			wantPort: corev1.ServicePort{
				Name: "42",
				Port: 42,
			},
		},
		"name-and-target": {
			inPort: ExposedPort{
				Name:   "port42",
				Port:   42,
				Target: 4242,
			},
			wantPort: corev1.ServicePort{
				Name:       "port42",
				Port:       42,
				TargetPort: intstr.FromInt32(4242),
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			gotPort := tc.inPort.ServicePort()
			if diff := cmp.Diff(tc.wantPort, gotPort); diff != "" {
				t.Errorf("ServicePort(): -want +got:\n%s", diff)
			}
		})
	}
}

func TestServicePortApplyConfig(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inPort   ExposedPort
		wantPort *corev1a.ServicePortApplyConfiguration
	}{
		"empty": {
			inPort: ExposedPort{},
			wantPort: corev1a.ServicePort().
				WithName("0").
				WithProtocol(corev1.Protocol("TCP")).
				WithPort(0).
				WithTargetPort(intstr.FromInt32(0)),
		},
		"just-port": {
			inPort: ExposedPort{
				Port: 42,
			},
			wantPort: corev1a.ServicePort().
				WithName("42").
				WithProtocol(corev1.Protocol("TCP")).
				WithPort(42).
				WithTargetPort(intstr.FromInt32(42)),
		},
		"name-and-target": {
			inPort: ExposedPort{
				Name:   "port42",
				Port:   42,
				Target: 4242,
			},
			wantPort: corev1a.ServicePort().
				WithName("port42").
				WithProtocol(corev1.Protocol("TCP")).
				WithPort(42).
				WithTargetPort(intstr.FromInt32(4242)),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			gotPort := tc.inPort.ServicePortApplyConfig()
			if diff := cmp.Diff(tc.wantPort, gotPort); diff != "" {
				t.Errorf("ServicePortApplyConfig(): -want +got:\n%s", diff)
			}
		})
	}
}

func TestServicePorts(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		spec Cxn
		want []corev1.ServicePort
	}{
		"empty": {
			spec: Cxn{},
			want: nil,
		},
		"ports": {
			spec: Cxn{
				ExposedPorts: []ExposedPort{
					{},
					{Port: 42},
					{Name: "port42", Port: 42, Target: 4242},
				},
			},
			want: []corev1.ServicePort{
				{Name: "0"},
				{Name: "42", Port: 42},
				{Name: "port42", Port: 42, TargetPort: intstr.FromInt32(4242)},
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got := tc.spec.ServicePorts()
			if diff := cmp.Diff(tc.want, got, cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("ServicePorts(): -want +got:\n%s", diff)
			}
		})
	}
}

func TestServicePortsApplyConfig(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		spec Cxn
		want []*corev1a.ServicePortApplyConfiguration
	}{
		"empty": {
			spec: Cxn{},
			want: nil,
		},
		"ports": {
			spec: Cxn{
				ExposedPorts: []ExposedPort{
					{},
					{Port: 42},
					{Name: "port42", Port: 42, Target: 4242},
				},
			},
			want: []*corev1a.ServicePortApplyConfiguration{
				corev1a.ServicePort().
					WithName("0").
					WithProtocol(corev1.Protocol("TCP")).
					WithPort(0).
					WithTargetPort(intstr.FromInt32(0)),
				corev1a.ServicePort().
					WithName("42").
					WithProtocol(corev1.Protocol("TCP")).
					WithPort(42).
					WithTargetPort(intstr.FromInt32(42)),
				corev1a.ServicePort().
					WithName("port42").
					WithProtocol(corev1.Protocol("TCP")).
					WithPort(42).
					WithTargetPort(intstr.FromInt32(4242)),
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got := tc.spec.ServicePortsApplyConfig()
			if diff := cmp.Diff(tc.want, got, cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("ServicePortsApplyConfig(): -want +got:\n%s", diff)
			}
		})
	}
}

func TestPublicIP(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		spec Cxn
		want bool
	}{
		"empty": {
			spec: Cxn{},
			want: true,
		},
		"nil": {
			spec: Cxn{PublicIP_p: nil},
			want: true,
		},
		"true": {
			spec: Cxn{PublicIP_p: func() *bool { b := true; return &b }()},
			want: true,
		},
		"false": {
			spec: Cxn{PublicIP_p: func() *bool { b := false; return &b }()},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			if got, want := tc.spec.PublicIP(), tc.want; got != want {
				t.Errorf("PublicIP(): got %v, want %v", got, want)
			}
		})
	}
}

func TestImage(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		spec Image
		gpu  bool
		want string
	}{
		"empty-cpu": {
			spec: Image{},
			gpu:  false,
			want: "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_cpu:main",
		},
		"empty-gpu": {
			spec: Image{},
			gpu:  true,
			want: "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_gpu:main",
		},
		"cpu-version": {
			spec: Image{Tag: "v2"},
			gpu:  false,
			want: "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_cpu:v2",
		},
		"gpu-version": {
			spec: Image{Tag: "v2"},
			gpu:  true,
			want: "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_gpu:v2",
		},
		"gpu-@main": {
			spec: Image{Tag: "@main"},
			gpu:  true,
			want: "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_gpu:v1",
		},
		"override-all": {
			spec: Image{Registry: "reg7", Path: "path/to/7", Tag: "tag7"},
			want: "reg7/path/to/7:tag7",
		},
	}
	db, err := clusters.New()
	if err != nil {
		t.Fatalf("clusters.New(): %v.", err)
	}
	c, err := db.Cluster("_unittest_")
	if err != nil {
		t.Fatalf("db.Cluster(_unittest_): %v.", err)
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			if got, want := tc.spec.Image(c, tc.gpu), tc.want; got != want {
				t.Errorf("Image(): got %v, want %v", got, want)
			}
		})
	}
}

func TestHomePVCName(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		spec DevPod
		want string
	}{
		"empty": {
			spec: DevPod{},
			want: "-home",
		},
		"default": {
			spec: DevPod{DevPodName: "devpod0"},
			want: "devpod0-home",
		},
		"custom": {
			spec: DevPod{DevPodName: "devpod0", Home: Home{CustomName: "home0"}},
			want: "home0",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got := tc.spec.HomePVCName()
			if got != tc.want {
				t.Errorf("got %v, want %v", got, tc.want)
			}
		})
	}
}

func TestHomeSize(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		spec Home
		want resource.Quantity
	}{
		"empty": {
			spec: Home{},
			want: *quanT(t, "256Gi"),
		},
		"zero": {
			spec: Home{Size_p: quanT(t, "0")},
			want: resource.Quantity{},
		},
		"custom": {
			spec: Home{Size_p: quanT(t, "1024Gi")},
			want: *quanT(t, "1Ti"),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			if got, want := tc.spec.Size(), tc.want; !got.Equal(want) {
				t.Errorf("Size(): got %v, want %v", got.String(), want.String())
			}
		})
	}
}

func TestCombinedAllVolMounts(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		spec    Volumes
		wantErr string
		want    clusters.VolMounts
		user    string
	}{
		"empty": {
			spec: Volumes{},
			want: clusters.VolMounts{
				clusters.VolMount{
					Name: "gcs-_unittest_-checkpoints0",
					Vol: corev1.Volume{
						VolumeSource: corev1.VolumeSource{
							CSI: &corev1.CSIVolumeSource{
								Driver: "gcsfuse.csi.storage.gke.io",
								VolumeAttributes: map[string]string{
									"bucketName":            "_unittest_-checkpoints0",
									"fileCacheCapacity":     "-1",
									"fileCacheForRangeRead": "false",
									"mountOptions": strings.Join([]string{
										"implicit-dirs",
										"uid=1000",
										"gid=1000",
										"file-mode=0660",
										"dir-mode=0770",
										"o=noexec",
										"o=noatime",
										"file-cache:enable-parallel-downloads:true",
										"file-cache:parallel-downloads-per-file:8",
										"file-cache:max-parallel-downloads:-1",
										"file-cache:download-chunk-size-mb:8",
										"file-system:kernel-list-cache-ttl-secs:30",
										"file-system:ignore-interrupts:false",
									}, ","),
								},
							},
						},
					},
					Mounts: []corev1.VolumeMount{{
						Name:      "gcs-_unittest_-checkpoints0",
						MountPath: "/mnt/efs/checkpoints0",
					}},
				},
				clusters.VolMount{
					Name: "gcs-_unittest_-bucket1",
					Vol: corev1.Volume{
						VolumeSource: corev1.VolumeSource{
							CSI: &corev1.CSIVolumeSource{
								Driver: "gcsfuse.csi.storage.gke.io",
								VolumeAttributes: map[string]string{
									"bucketName":            "_unittest_-bucket1",
									"fileCacheCapacity":     "-1",
									"fileCacheForRangeRead": "false",
									"mountOptions": strings.Join([]string{
										"implicit-dirs",
										"uid=1000",
										"gid=1000",
										"file-mode=0660",
										"dir-mode=0770",
										"o=noexec",
										"o=noatime",
										"file-cache:enable-parallel-downloads:true",
										"file-cache:parallel-downloads-per-file:8",
										"file-cache:max-parallel-downloads:-1",
										"file-cache:download-chunk-size-mb:8",
										"file-system:kernel-list-cache-ttl-secs:30",
										"file-system:ignore-interrupts:false",
									}, ","),
								},
							},
						},
					},
					Mounts: []corev1.VolumeMount{{
						Name:      "gcs-_unittest_-bucket1",
						MountPath: "/mnt/bucket1",
					}},
				},
				clusters.VolMount{
					Name: "sec0",
					Vol: corev1.Volume{
						VolumeSource: corev1.VolumeSource{
							Secret: &corev1.SecretVolumeSource{
								SecretName: "sec0",
							},
						},
					},
					Mounts: []corev1.VolumeMount{
						{
							Name:      "sec0",
							MountPath: "/run/sec0",
						},
						{
							Name:      "sec0",
							MountPath: "/run/sec0-extra",
						},
					},
				},
			},
		},
		"exclude-cluster-partial": {
			spec: Volumes{
				Exclude: []string{"gcs-_unittest_-checkpoints0", "gcs-_unittest_-bucket1"},
			},
			want: clusters.VolMounts{
				clusters.VolMount{
					Name: "sec0",
					Vol: corev1.Volume{
						VolumeSource: corev1.VolumeSource{
							Secret: &corev1.SecretVolumeSource{
								SecretName: "sec0",
							},
						},
					},
					Mounts: []corev1.VolumeMount{
						{
							Name:      "sec0",
							MountPath: "/run/sec0",
						},
						{
							Name:      "sec0",
							MountPath: "/run/sec0-extra",
						},
					},
				},
			},
		},
		"exclude-all-partial": {
			spec: Volumes{
				Exclude: []string{"gcs-_unittest_-checkpoints0", "gcs-_unittest_-bucket1", "vol1", "vm1"},
				Volumes: []string{"vol1:/vol1", "vol2:/vol2"},
				VolMounts: clusters.VolMounts{
					{Name: "vm1"},
					{Name: "vm2"},
				},
			},
			want: clusters.VolMounts{
				clusters.VolMount{
					Name: "vm2",
				},
				clusters.VolMount{
					Name:   "vol2",
					Mounts: []corev1.VolumeMount{{MountPath: "/vol2"}},
				},
				clusters.VolMount{
					Name: "sec0",
					Vol: corev1.Volume{
						VolumeSource: corev1.VolumeSource{
							Secret: &corev1.SecretVolumeSource{
								SecretName: "sec0",
							},
						},
					},
					Mounts: []corev1.VolumeMount{
						{
							Name:      "sec0",
							MountPath: "/run/sec0",
						},
						{
							Name:      "sec0",
							MountPath: "/run/sec0-extra",
						},
					},
				},
			},
		},
		"exclude-all-cluster": {
			spec: Volumes{
				ExcludeClusterDefaults: true,
				Exclude:                []string{"vol1", "vm1"},
				Volumes:                []string{"vol1:/vol1", "vol2:/vol2"},
				VolMounts: clusters.VolMounts{
					{Name: "vm1"},
					{Name: "vm2"},
				},
			},
			want: clusters.VolMounts{
				clusters.VolMount{
					Name: "vm2",
				},
				clusters.VolMount{
					Name:   "vol2",
					Mounts: []corev1.VolumeMount{{MountPath: "/vol2"}},
				},
			},
		},
		"parse-error": {
			spec: Volumes{
				ExcludeClusterDefaults: true,
				Volumes:                []string{"vol1:/vol1", "vol2:/vol2", "vol3"},
			},
			wantErr: `volmount "vol3" must be in the format`,
		},
		"snapshots": {
			user: "user0",
			spec: Volumes{
				ExcludeClusterDefaults: true,
				VolMounts: clusters.VolMounts{
					{Name: "vm1"},
					{Name: "vm2"},
				},
				Snapshots: []string{"user0-devpod0-home.snap00", "user0-devpod0-home.snap01"},
			},
			want: clusters.VolMounts{
				clusters.VolMount{
					Name: "vm1",
				},
				clusters.VolMount{
					Name: "vm2",
				},
				clusters.VolMount{
					Name:   "user0-devpod0-home-snap00",
					Mounts: []corev1.VolumeMount{{MountPath: "/mnt/snapshots/user0-devpod0-home.snap00", ReadOnly: true}},
					Vol: corev1.Volume{
						VolumeSource: corev1.VolumeSource{
							Ephemeral: &corev1.EphemeralVolumeSource{
								VolumeClaimTemplate: &corev1.PersistentVolumeClaimTemplate{
									ObjectMeta: metav1.ObjectMeta{
										Labels: map[string]string{
											"aug.devpod": "true",
											"aug.user":   "user0",
										},
									},
									Spec: corev1.PersistentVolumeClaimSpec{
										AccessModes: []corev1.PersistentVolumeAccessMode{corev1.ReadOnlyMany},
										Resources: corev1.VolumeResourceRequirements{
											Requests: corev1.ResourceList{
												corev1.ResourceStorage: *quanT(t, "0"),
											},
										},
										DataSourceRef: &corev1.TypedObjectReference{
											APIGroup: func() *string { s := "snapshot.storage.k8s.io"; return &s }(),
											Kind:     "VolumeSnapshot",
											Name:     "user0-devpod0-home.snap00",
										},
									},
								},
							},
						},
					},
				},
				clusters.VolMount{
					Name:   "user0-devpod0-home-snap01",
					Mounts: []corev1.VolumeMount{{MountPath: "/mnt/snapshots/user0-devpod0-home.snap01", ReadOnly: true}},
					Vol: corev1.Volume{
						VolumeSource: corev1.VolumeSource{
							Ephemeral: &corev1.EphemeralVolumeSource{
								VolumeClaimTemplate: &corev1.PersistentVolumeClaimTemplate{
									ObjectMeta: metav1.ObjectMeta{
										Labels: map[string]string{
											"aug.devpod": "true",
											"aug.user":   "user0",
										},
									},
									Spec: corev1.PersistentVolumeClaimSpec{
										AccessModes: []corev1.PersistentVolumeAccessMode{corev1.ReadOnlyMany},
										Resources: corev1.VolumeResourceRequirements{
											Requests: corev1.ResourceList{
												corev1.ResourceStorage: *quanT(t, "0"),
											},
										},
										DataSourceRef: &corev1.TypedObjectReference{
											APIGroup: func() *string { s := "snapshot.storage.k8s.io"; return &s }(),
											Kind:     "VolumeSnapshot",
											Name:     "user0-devpod0-home.snap01",
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	db, err := clusters.New()
	if err != nil {
		t.Fatalf("clusters.New(): %v.", err)
	}
	c, err := db.Cluster("_unittest_")
	if err != nil {
		t.Fatalf("db.Cluster(_unittest_): %v.", err)
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got, gotErr := tc.spec.CombinedAllVolMounts(c, tc.user)
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}
			if diff := cmp.Diff(tc.want, got, cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("CombinedAllVolMounts(): -want +got:\n%s", diff)
			}
		})
	}
}

func TestPersonalUsername(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		spec OS
		want bool
	}{
		"empty": {
			spec: OS{},
			want: false,
		},
		"nil": {
			spec: OS{PersonalUsername_p: nil},
			want: false,
		},
		"true": {
			spec: OS{PersonalUsername_p: func() *bool { b := true; return &b }()},
			want: true,
		},
		"false": {
			spec: OS{PersonalUsername_p: func() *bool { b := false; return &b }()},
			want: false,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			if got, want := tc.spec.PersonalUsername(), tc.want; got != want {
				t.Errorf("PersonalUsername(): got %v, want %v", got, want)
			}
		})
	}
}

func TestOSUserName(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		spec DevPod
		want string
	}{
		"empty": {
			spec: DevPod{},
			want: "augment",
		},
		"personal-username": {
			spec: DevPod{UserName: "user0", OS: OS{PersonalUsername_p: func() *bool { b := true; return &b }()}},
			want: "user0",
		},
		"not-personal-username": {
			spec: DevPod{UserName: "user0", OS: OS{PersonalUsername_p: func() *bool { b := false; return &b }()}},
			want: "augment",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			if got, want := tc.spec.OSUserName(), tc.want; got != want {
				t.Errorf("OSUserName(): got %v, want %v", got, want)
			}
		})
	}
}
