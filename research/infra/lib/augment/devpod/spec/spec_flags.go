package spec

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/spf13/pflag"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"

	"github.com/augmentcode/augment/infra/lib/distribution"
	"github.com/augmentcode/augment/research/infra/cfg/clusters"
)

// FromFlags adds flags to the given `pflags` and returns a function that is then used to
// build a Spec after flags have been parsed. Any errors returned are specific to flag handling,
// the Spec.Validate() method should be called separately.
//
// If `orig` is provided it is used as a base to then update, but is not modified in place.
func FromFlags(flags *pflag.FlagSet) func(cluster, user, name string, orig *DevPod) (*DevPod, error) {
	v := struct {
		poweroff bool
		poweron  bool

		gpuType string
		cpuType string
		gpus    int32
		cpus    string
		ram     string
		disk    string

		nodeName    string
		poolType    string
		tolerations []string

		ignoreLimits     bool
		ignoreValidation bool

		ports                     []string
		sshStdPort                bool
		sshDisableCentralizedKeys bool
		eternalTerminal           bool
		moshStart                 int32
		moshCount                 int32
		publicIP                  bool

		image    string
		imageTag string

		homeName  string
		homeClass string
		homeMode  string
		homeSize  string

		volExcludeDefaults bool
		volExclude         []string
		vols               []string

		shell            string
		timezone         string
		personalUsername bool

		dockerMode bool
		privileged bool
	}{}

	flags.BoolVar(&v.poweron, "power-on", true, "Power-on is the default when creating, and matching the existing state when replacing.")
	flags.BoolVar(&v.poweroff, "power-off", false, "Create DevPod as powered off.")

	flags.StringVar(&v.gpuType, "gpu-type", "", "The GPU type (alias) for node selectors.")
	flags.StringVar(&v.cpuType, "cpu-type", "", "The CPU type (alias) for node selectors.")
	flags.Int32Var(&v.gpus, "gpu-count", 0, "The number of GPUs to request.")
	flags.StringVar(&v.cpus, "cpu-count", "", "The number of CPUs to request.")
	flags.StringVar(&v.ram, "ram", "", "The amount of RAM to request. Units default to 'G' but M, Mi, G, Gi, etc are supported.")
	flags.StringVar(&v.disk, "disk", "", "The amount of ephemeral disk to request. Units default to 'Gi' but M, Mi, G, Gi, etc are supported.")

	flags.StringVar(&v.nodeName, "node-name", "", "Request a specific node, by name. (Not typically needed).")
	flags.StringVar(&v.poolType, "pool-type", "", "Set or override the r.augmentcode.com/pool-type toleration. (Not typically needed).")
	flags.StringSliceVar(&v.tolerations, "tolerations", []string{}, "Custom tolerations to add in the form KEY[=VAL][:EFFECT]. (Not typically needed).")

	flags.BoolVarP(&v.ignoreLimits, "ignore-limits", "L", false, "Ignore validation errors due to pre-defined machine limits.")
	flags.BoolVar(&v.ignoreValidation, "ignore-validation", false, "Ignore validation errors.")

	flags.StringSliceVar(&v.ports, "exposed-ports", []string{}, "Defaults to none. A list of HTTPS ports to expose in the form PORT[:TARGET] or [NAME:]PORT:[TARGET].")
	flags.BoolVar(&v.sshStdPort, "ssh-std-port", false, "Also expose SSH port 22. This is disabled by default to avoid excessive brute-force attempts.")
	flags.BoolVar(&v.sshDisableCentralizedKeys, "ssh-disable-centralized-keys", false, "Use local ~/.ssh/authorized_keys (e.g., from dotfiles) rather than centrally managed keys.")
	flags.BoolVar(&v.eternalTerminal, "eternal-terminal", false, "Run an etserver on port *22023*. (Not enabled by default because it adds a process to the DevPod.)")
	flags.Int32Var(&v.moshStart, "mosh-start", 0, "Defaults to 0 (disabled by default). Mosh port range start. Setting to 6000 enables mosh.")
	flags.Int32Var(&v.moshCount, "mosh-count", 6, "Defaults to 6. The number of mosh ports. Setting to 0 disables mosh.")
	flags.BoolVar(&v.publicIP, "public-ip", true, "Expose a public IP address.")

	flags.StringVar(&v.image, "custom-image", "", "Override the default, either for an entirely separate image, or to select an older version.")
	flags.StringVar(&v.imageTag, "image-tag", "", "Override the image tag (e.g., to select an older version temporarily).")

	flags.StringVar(&v.homeName, "home-name", "", "Override the default home directory name.")
	flags.StringVar(&v.homeClass, "home-class", "", "The storage class of the home volume.")
	flags.StringVar(&v.homeMode, "home-mode", "rwo", "The PVC access mode. 'rwo' or 'rwx'.")
	flags.StringVar(&v.homeSize, "home-size", "256", "Size of homedirectory. Defaults to 256. Units default to 'Gi' but M, Mi, G, Gi, etc are supported.")

	flags.BoolVar(&v.volExcludeDefaults, "vol-exclude-defaults", false, "Exclude all default volumes (e.g., /mnt/efs/augment/*).")
	flags.StringSliceVar(&v.volExclude, "vol-exclude", []string{}, "Exclude specific default volumes.")
	flags.StringSliceVar(&v.vols, "vols", []string{}, "Additional volumes to mount in the form NAME:PATH.")

	flags.StringVar(&v.shell, "shell", "", "Set your user's shell. Defaults to /bin/bash.")
	flags.StringVar(&v.timezone, "timezone", "", "Set the system timezone. Defaults to UTC. Supports names under /usr/share/zoneinfo e.g. 'US/Mountain' and 'America/Denver'.")
	flags.BoolVar(&v.personalUsername, "personal-username", false, "Use your personal username rather than \"augment\".")

	flags.BoolVar(&v.dockerMode, "docker", false, "Enable 'docker' mode.")
	flags.BoolVar(&v.privileged, "privileged", false, "Enable privileged mode. (implied by --docker).")

	// For list items, if the first character is a "+" then treat as an append, otherwise replace the whole list.
	isAppend := func(ss []string) bool {
		if len(ss) > 0 && strings.HasPrefix(ss[0], "+") {
			ss[0] = ss[0][1:]
			return true
		}
		return false
	}

	return func(cluster, user, name string, orig *DevPod) (*DevPod, error) {
		/// Init new or updated DevPod spec and ID fields.

		spec := orig.DeepCopy()
		if spec == nil {
			spec = &DevPod{
				ClusterName: cluster,
				UserName:    user,
				DevPodName:  name,
			}
		} else if got, want := spec.ClusterName, cluster; got != want {
			return nil, fmt.Errorf("spec.FromFlags: cluster_name mismatch (%s != %s)", got, want)
		} else if got, want := spec.UserName, user; got != want {
			return nil, fmt.Errorf("spec.FromFlags: user_name mismatch (%s != %s)", got, want)
		} else if got, want := spec.DevPodName, name; got != want {
			return nil, fmt.Errorf("spec.FromFlags: devpod_name mismatch (%s != %s)", got, want)
		}

		/// Power State

		if on, off := flags.Changed("power-on"), flags.Changed("power-off"); on && off {
			return nil, fmt.Errorf("--power-on and --power-off are mutually exclusive.")
		} else if on {
			spec.PowerOff_p = func() *bool { b := false; return &b }()
		} else if off {
			spec.PowerOff_p = func() *bool { b := true; return &b }()
		}

		/// Resources: Resolve either --gpu-type or --cpu-type down to one alias.

		if !flags.Changed("gpu-type") && !flags.Changed("cpu-type") {
			// pass
		} else if v.gpuType != "" && v.cpuType != "" {
			return nil, fmt.Errorf("cannot specify both --gpu-type and --cpu-type")
		} else if v.gpuType != "" {
			spec.Resources.NodeType = v.gpuType
		} else {
			spec.Resources.NodeType = v.cpuType
		}

		/// Resources: Quantities

		pq := func(fn, val, units string, p **resource.Quantity) error {
			if !flags.Changed(fn) {
				return nil
			}
			if val == "" {
				*p = nil
				return nil
			}
			if _, err := strconv.ParseFloat(val, 64); err == nil {
				val += units
			}
			if q, err := resource.ParseQuantity(val); err != nil {
				return fmt.Errorf("--%s=%s: %w", fn, val, err)
			} else {
				*p = &q
				return nil
			}
		}

		if flags.Changed("gpu-count") {
			spec.Resources.GPU = func() *int32 {
				if q := v.gpus; q == 0 {
					return nil
				} else {
					return &q
				}
			}()
		}

		if err := pq("cpu-count", v.cpus, "", &spec.Resources.CPU); err != nil {
			return nil, err
		}

		if err := pq("ram", v.ram, "G", &spec.Resources.RAM); err != nil {
			return nil, err
		}

		if err := pq("disk", v.disk, "Gi", &spec.Resources.Disk); err != nil {
			return nil, err
		}

		/// Resources: Node/Pool Selection

		parseTol := func(t string) corev1.Toleration {
			// First, we need to "RSplit" in case there's an optional effect.
			effect := corev1.TaintEffectNoSchedule
			if parts := strings.Split(t, ":"); len(parts) > 1 {
				if e := parts[len(parts)-1]; len(e) > 0 {
					effect = corev1.TaintEffect(e)
				}
				t = strings.Join(parts[:len(parts)-1], ":")
			}

			// Now we can split on the key and value.
			key, val, _ := strings.Cut(t, "=")

			// Operator is implicit based on whether there's a value.
			op := corev1.TolerationOpExists
			if len(val) > 0 {
				op = corev1.TolerationOpEqual
			}

			// Finally, build the toleration.
			return corev1.Toleration{
				Key:      key,
				Operator: op,
				Value:    val,
				Effect:   effect,
			}
		}

		if flags.Changed("node-name") {
			spec.Resources.NodeName = v.nodeName
		}
		if flags.Changed("pool-type") {
			spec.Resources.PoolType = v.poolType
		}
		if flags.Changed("tolerations") {
			if !isAppend(v.tolerations) {
				spec.Resources.Tolerations = nil
			}
			for _, t := range v.tolerations {
				spec.Resources.Tolerations = append(spec.Resources.Tolerations, parseTol(t))
			}
		}

		/// Resources: Validations (these are done inside the jsonnet library)

		if flags.Changed("ignore-limits") {
			spec.Resources.IgnoreLimits = v.ignoreLimits
		}
		if flags.Changed("ignore-validation") {
			spec.Resources.IgnoreValidation = v.ignoreValidation
		}

		/// Connection: Exposed Ports

		if flags.Changed("exposed-ports") {
			if !isAppend(v.ports) {
				spec.Cxn.ExposedPorts = nil
			}
			if eps, err := ExposedPortsFrom(v.ports...); err != nil {
				return nil, fmt.Errorf("--exposed-ports=%w", err)
			} else {
				spec.Cxn.ExposedPorts = append(spec.Cxn.ExposedPorts, eps...)
			}
		}

		/// Connection: Everything Else (SSH, ET, Mosh, Public IP)

		if flags.Changed("ssh-std-port") {
			spec.Cxn.SSHStdPort = v.sshStdPort
		}
		if flags.Changed("ssh-disable-centralized-keys") {
			spec.Cxn.SSHDisableCentralizedKeys = v.sshDisableCentralizedKeys
		}
		if flags.Changed("eternal-terminal") {
			spec.Cxn.EternalTerminal = v.eternalTerminal
		}
		if flags.Changed("mosh-start") || flags.Changed("mosh-count") {
			if v.moshStart > 0 && v.moshCount > 0 {
				spec.Cxn.MoshStart = v.moshStart
				spec.Cxn.MoshCount = v.moshCount
			} else {
				spec.Cxn.MoshStart = 0
				spec.Cxn.MoshCount = 0
			}
		}
		if flags.Changed("public-ip") {
			spec.Cxn.PublicIP_p = func() *bool {
				if b := v.publicIP; b {
					return nil
				} else {
					return &b
				}
			}()
		}

		/// Image

		if flags.Changed("custom-image") {
			spec.Image.Registry, spec.Image.Path, spec.Image.Tag = distribution.ParseImage(v.image)
		}
		if flags.Changed("image-tag") {
			spec.Image.Tag = v.imageTag
		}

		/// Home Directory

		if flags.Changed("home-name") {
			spec.Home.CustomName = v.homeName
		}
		if flags.Changed("home-class") {
			spec.Home.Class = v.homeClass
		}
		if flags.Changed("home-mode") {
			spec.Home.Mode = v.homeMode
		}
		if err := pq("home-size", v.homeSize, "Gi", &spec.Home.Size_p); err != nil {
			return nil, err
		} else if spec.Home.Size_p != nil && spec.Home.Size_p.Equal(*resource.NewQuantity(256*1024*1024*1024, resource.BinarySI)) {
			spec.Home.Size_p = nil
		}

		/// Volumes

		if flags.Changed("vol-exclude-defaults") {
			spec.Volumes.ExcludeClusterDefaults = v.volExcludeDefaults
		}
		if flags.Changed("vol-exclude") {
			if !isAppend(v.volExclude) {
				spec.Volumes.Exclude = nil
			}
			spec.Volumes.Exclude = append(spec.Volumes.Exclude, v.volExclude...)
		}
		if flags.Changed("vols") {
			if !isAppend(v.vols) {
				spec.Volumes.VolMounts = nil
			}
			for _, v := range v.vols {
				if vm, err := clusters.VolMountFromString(v); err != nil {
					return nil, fmt.Errorf("--vols=%s: %w", v, err)
				} else {
					spec.Volumes.VolMounts = append(spec.Volumes.VolMounts, vm)
				}
			}
		}

		// OS

		if flags.Changed("shell") {
			spec.OS.Shell = v.shell
		}
		if flags.Changed("timezone") {
			spec.OS.Timezone = v.timezone
		}
		if flags.Changed("personal-username") {
			b := v.personalUsername
			spec.OS.PersonalUsername_p = &b
		}

		/// Misc

		if flags.Changed("docker") {
			spec.DockerMode = v.dockerMode
		}

		if flags.Changed("privileged") {
			spec.Privileged = v.privileged
		}

		return spec, nil
	}
}
