package ctrl

import (
	"context"
	"fmt"
	"time"

	"golang.org/x/sync/errgroup"

	"k8s.io/apimachinery/pkg/watch"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/research/infra/cfg/clusters"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/crd/devpodv1"
)

const (
	sshAuthKeysNamespace = "aug-system"                       // pragma: allowlist secret
	sshAuthKeysConfigMap = "augment-user-ssh-authorized-keys" // pragma: allowlist secret
	userNamespacePrefix  = "aug-user-"
	FieldManagerName     = "devpod-controller"

	watchTimer = watch.EventType("TIMER")
)

var (
	secrets    = []string{"augi-release-readers"}
	configmaps = []string{sshAuthKeysConfigMap}
)

type ControllerOpt func(*Controller)

func WetRun(w bool) ControllerOpt {
	return func(c *Controller) {
		c.wetRun = w
	}
}

func NoDiff(b bool) ControllerOpt {
	return func(c *Controller) {
		c.noDiff = b
	}
}

type Controller struct {
	*logger.Logger

	cluster clusters.Cluster
	k8s_    *k8s.Client
	wetRun  bool
	noDiff  bool

	// ConfigMaps and Secrets to copy from Cluster.MainNamespace into user namespace.
	cms  []string
	secs []string
}

func NewController(ctx context.Context, cluster clusters.Cluster, opts ...ControllerOpt) (*Controller, error) {
	c := &Controller{
		Logger:  logger.New(nil).WithString("component", "ctrl"),
		cluster: cluster,
		cms:     configmaps,
		secs:    secrets,
	}
	for _, o := range opts {
		o(c)
	}
	if c.k8s_ == nil {
		if k, err := c.cluster.NewK8s(); err != nil {
			return nil, err
		} else {
			c.k8s_ = k
		}
	}
	c.k8s_.SetDefaultFieldManager(FieldManagerName)
	if err := c.k8s_.SetNewUnstructuredExtractor(ctx); err != nil {
		return nil, err
	}
	return c, nil
}

func (c *Controller) user(name string) *user {
	return &user{n: name, c: c}
}

func (c *Controller) srcNS() string {
	return c.cluster.MainNamespace
}

func (c *Controller) k8s(ns string) *devpodv1.Client {
	k := c.k8s_.InNamespace(ns)
	return devpodv1.WrappedClient(&k)
}

func (c *Controller) srcK8s() *devpodv1.Client {
	return c.k8s(c.srcNS())
}

func (c *Controller) ccK8s() *devpodv1.Client {
	return c.k8s(c.cluster.CCNamespace())
}

func (c *Controller) Usernames(ctx context.Context) ([]string, error) {
	k := c.k8s(sshAuthKeysNamespace)
	if cm, err := k.GetConfigMap(ctx, sshAuthKeysConfigMap); err != nil {
		return nil, fmt.Errorf("cm/%s: %w", sshAuthKeysConfigMap, err)
	} else {
		return cm.Keys(), nil
	}
}

func (c *Controller) Run(ctx context.Context) error {
	c.LogNotice("Running DevPod controller for cluster %s [dryrun=%t].", c.cluster.Name, !c.wetRun)

	grp, gctx := errgroup.WithContext(ctx)
	ch := make(chan bool, 1)
	nudge := func() {
		select {
		case ch <- true:
		default:
		}
	}

	grp.Go(func() error {
		return c.k8s("").WatchDevPods(gctx, func(ev string, dp *devpodv1.DevPod) {
			c.handleDevPod(gctx, ev, dp)
		})
	})
	grp.Go(func() error {
		for {
			select {
			case <-gctx.Done():
				return nil
			case <-time.After(5 * time.Minute):
				c.LogInfo("Nudging ApplyAllUsers() from timer.")
				c.ApplyAllUsers(gctx)
			case <-ch:
				c.ApplyAllUsers(gctx)
			}
		}
	})
	for _, cm := range c.cms {
		grp.Go(func() error {
			return c.srcK8s().WatchConfigMaps(gctx, func(ev string, cm *k8s.ConfigMap) {
				c.LogInfo("Nudging ApplyAllUsers() from %s.", cm.ShortName())
				nudge()
			}, k8s.ListByName(cm))
		})
	}
	for _, sec := range c.secs {
		grp.Go(func() error {
			return c.srcK8s().WatchSecrets(gctx, func(ev string, sec *k8s.Secret) {
				c.LogInfo("Nudging ApplyAllUsers() from %s.", sec.ShortName())
				nudge()
			}, k8s.ListByName(sec))
		})
	}

	if err := grp.Wait(); err != nil {
		c.LogErr("DevPod controller exiting, with error: %v.", err)
		return err
	}

	c.LogNotice("DevPod controller exiting, cleanly.")
	return nil
}

func (c *Controller) handleDevPod(ctx context.Context, ev string, dp *devpodv1.DevPod) {
	if err := dp.Validate(); err != nil {
		c.LogErr("dp/%s %v: VALIDATION ERROR(s): %v.", dp.Name(), ev, err)
	} else {
		c.LogInfo("dp/%s %v: VALIDATION OK.", dp.Name(), ev)
	}
}

// mirrorObjectForUser copies the given K8s Object to the user's namespace. The given `o`'s UID and Ownership are clared.
// Updates are logged and writes only made with `c.WetRun`.
func (c *Controller) mirrorObjectForUser(ctx context.Context, ev watch.EventType, o *k8s.Object, username string) error {
	u := c.user(username)
	k := u.k8s().DynamicForObject(o)

	if ev == watch.Deleted {
		c.LogInfo("user/%s: %s: %v(dyrun=%t).", u.name(), o.ShortName(), ev, !c.wetRun)
		if c.wetRun {
			return k.Delete(ctx, o.Name(), k8s.DeleteOptions())
		}
		return nil
	}

	cpy := o.DeepCopy()
	cpy.SetNamespace(u.namespace())
	cpy.SetUID("")
	cpy.ClearOwners()
	cpy.SetResourceVersion("")
	cpy.SetLabel("aug.mirrored", "true")

	if cur, err := k.Get(ctx, o.Name(), k8s.GetOptions()); k8s.NotFoundOK(err) != nil {
		return err
	} else if cur == nil {
		c.LogInfo("user/%s: %s: %v Create(dyrun=%t).", u.name(), o.ShortName(), ev, !c.wetRun)
	} else {
		cpy.SetUID(string(cur.GetUID()))
		// Avoid overly noisy logging for timer-driven updates.
		if ev != watchTimer || !c.wetRun {
			c.LogInfo("user/%s: %s: %v Update(dyrun=%t).", u.name(), o.ShortName(), ev, !c.wetRun)
		}
	}

	ucpy, err := cpy.Unstructured()
	if err != nil {
		return err
	}

	if c.wetRun {
		if !cpy.HasUID() {
			_, err := k.Create(ctx, ucpy, k8s.CreateOptions())
			return err
		} else {
			_, err := k.Update(ctx, ucpy, k8s.UpdateOptions())
			return err
		}
	}

	return nil
}

func ctxDone(ctx context.Context) bool {
	select {
	case <-ctx.Done():
		return true
	default:
		return false
	}
}
