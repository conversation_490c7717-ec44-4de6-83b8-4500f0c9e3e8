#!/bin/bash

# NOTE(mattm): This script is designed to run either explicitly by a user or from an /etc/profile.d/ snippet. In the latter case,
# the script is quiet when nothing is required. We want to nudge people to login to google when they first login to a new home
# directory, but we don't want to be overly noisy about it and we want to provide a way for people to ignore.

set -eu -o pipefail

declare -r  _GCLOUD=gcloud
declare -ra _GCLOUD_LOGIN=($_GCLOUD auth login --update-adc)
declare -r  _IGNORE_FILE_BASE='.local/augment-gcloud-login-check-ignore'
declare -r  _IGNORE_FILE="$HOME/$_IGNORE_FILE_BASE"

declare -r _bold="$(tput bold)"
declare -r _red="$(tput setaf 1)"
declare -r _green="$(tput setaf 2)"
declare -r _boldr="${_bold}${_red}"
declare -r _boldg="${_bold}${_green}"
declare -r _reset="$(tput sgr0)"

usage() {
	cat <<-EOF
	Usage:
	  $0 [-f|--force] [-q|--quiet]

	Your glcoud login credentials are used for all of a) running gcloud manually; b) accessing GCP APIs from python scripts;
	and c) accessing GKE K8s cluster(s). This command detects when it's time to login to google by calling:

	  ${_boldg}${_GCLOUD_LOGIN[@]}${_reset}

	which is a unified way to run both:

	  gcloud auth login
	  gcloud auth application-default-login

	The following prereqs must be met or this command will exit without doing anything:

	  - The file ${_bold}'\$HOME/$_IGNORE_FILE_BASE'${_reset} must not exist.
	    (This file is used to prevent this script from asking again).
	  - An ${_bold}@augmentcode.com${_reset} account must not already appear in 'gcloud auth list'.
	  - The caller must be a normal user (not root).
	  - The caller must be connected to a TTY, ie an interactive shell.
	  - The caller must confirm an ${_bold}interactive prompt${_reset}.

	Options:
	  ${_boldg}-f --force${_reset}  Overide all of the above prereqs and force a login.
	  -q --quiet  Be quieter than normal.

	EOF
}

info() {
	declare -r fmt="$1"
	shift
	printf "${_boldg}[gcloud-login]${_reset} $fmt\n" "$@"
}

infoq() {
	(( quiet )) || info "$@"
}

err() {
	declare -r fmt="${_boldr}ERROR${_reset} $1"
	shift
	info "$fmt" "$@" >&2
}

main() {
	declare -i force=0 quiet=0
	eval set -- $(getopt -n "$0" -o 'fqh' --long 'force,quiet,help,usage' -- "$@")
	while true; do
		case "$1" in
		--) shift; break;;
		-f|--force) force=1; shift; continue ;;
		-q|--quiet) quiet=1; shift; continue ;;
		-h|--help|--usage) usage; return 0 ;;
		*)
			printf "Invalid Option: %s\n\n" "$1" >&2
			usage
			return 1
		;;
		esac
	done
	declare -r force quiet

	if (( !force )); then
		if [[ -e "$_IGNORE_FILE" ]]; then
			infoq "The ignore file ${_bold}%q${_reset} exists. Remove it or use --force to login anyway." "$_IGNORE_FILE"
			return 0
		fi

		if (( UID < 1000 )); then
			err "Calling as a system user ${_boldr}%d (%s)${_reset}. Use --force to login anyway." "$UID" "$USER" >&2
			return 1
		fi

		if [[ ! -t 0 ]]; then
			infoq 'Not calling from an interactive terminal. Use --force to login anyway.'
			return 0
		fi

		if ! type -t "$_GCLOUD" > /dev/null; then
			err '%s not found in $PATH!' "$_GCLOUD"
			return 1
		fi

		declare -r current_auth="$(gcloud auth list |& sed -nr 's|.* ([a-zA-Z0-9._-]+@augmentcode.com).*|\1|p')"
		if [[ "$current_auth" ]]; then
			infoq "The user ${_boldg}%s${_reset} is already logged in. Use --force to login anyway." "$current_auth"
			return 0
		fi

		declare answer=
		while true; do
			printf "${_boldg}[gcloud-login]${_reset} Do you want to login now? ${_bold}yes${_reset}/no/x (don't ask again): [yes] "

			read -r answer
			answer="${answer,,}" # to lower

			case "$answer" in
			x)
				mkdir -p $(dirname "$_IGNORE_FILE")
				touch "$_IGNORE_FILE"
				return 0
				;;
			n|no)
				return 0
				;;
			y|yes|'')
				break
				;;
			*)
				printf "\n"
				err "'${_boldr}%s${_reset}' -- not a valid response" "$answer"
				printf "\n"
				answer=
				continue
				;;
			esac
		done
	fi

	# The gke_gcloud_auth_plugin_cache can be stale and cause problems if someone attempts
	# to kubectl before login. Clear it when we're going to (re-)login.
	rm -f "$HOME/.kube/gke_gcloud_auth_plugin_cache"

	info "Running: %s" "${_GCLOUD_LOGIN[*]}"
	exec "${_GCLOUD_LOGIN[@]}"
}

main "$@"
