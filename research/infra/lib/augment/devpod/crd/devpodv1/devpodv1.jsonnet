local clusters = import '../../../../../cfg/clusters/clusters.jsonnet';

function(cluster='_unittest_') local C = clusters.cluster(cluster); C.k8s + {
  crd: $.CRD + {
    spec+: {
      group: 'r.augmentcode.com',
      names+: {
        kind: 'DevPod',
        singular: 'devpod',
        plural: 'devpods',
        shortNames: ['dp'],
        categories+: ['all'],
      },
      scope: self.SCOPE_NAMESPACED,
      global_validations+:: [
        {
          rule: 'self.metadata.name == self.spec.devpod_name',
          message: 'The metadata.name and devpod_name must be identical.',
        },
      ],
      selectable_fields+:: [
        '.spec.cluster_name',
        '.spec.user_name',
        '.spec.devpod_name',
        '.spec.resources.node_type',
      ],
      spec_prop+:: {
        description: |||
          A DevPod is an Augment Research and Engineering environment built on Kubernetes.
        |||,
        required: ['cluster_name', 'user_name', 'devpod_name'],
        'x-kubernetes-validations': [
          {
            rule: 'self.devpod_name.startsWith(self.user_name + "-")',
            message: 'devpod_name must begin with "${user_name}-".',
          },
        ],
        properties+: {

          //////////////////////////////////////////////////////////////////////
          //
          // Basic ID Fields
          //

          cluster_name: {
            type: 'string',
            pattern: '^(cw|gcp)-[a-z][a-z0-9-]+$',
          },

          user_name: {
            type: 'string',
            pattern: '^[a-z][a-z0-9-]+$',
          },

          devpod_name: {
            type: 'string',
            pattern: '^[a-z][a-z0-9-]+-[a-z0-9-]+$',
          },

          //////////////////////////////////////////////////////////////////////
          //
          // Resources and Node Placement
          //

          resources: {
            type: 'object',
            properties+: {
              node_type: { type: 'string' },

              gpu: { type: 'number', nullable: true },
              cpu: { type: 'string', nullable: true },
              ram: { type: 'string', nullable: true },
              disk: { type: 'string', nullable: true },

              node_name: { type: 'string' },
              pool_type: { type: 'string' },
              tolerations: {
                type: 'array',
                items: {
                  type: 'object',
                  'x-kubernetes-preserve-unknown-fields': true,
                },
              },

              ignore_limits: { type: 'boolean' },
              ignore_validations: { type: 'boolean' },
            },
          },

          //////////////////////////////////////////////////////////////////////
          //
          // Connectivity
          //

          cxn: {
            type: 'object',
            properties+: {
              exposed_ports: {
                type: 'array',
                items: {
                  type: 'object',
                  properties+: {
                    name: { type: 'string' },
                    port: { type: 'integer' },
                    target: { type: 'integer' },
                  },
                },
              },
              ssh_standard_port: { type: 'boolean' },
              ssh_disable_centralized_keys: { type: 'boolean' },
              eternal_terminal: { type: 'boolean' },
              mosh_start: { type: 'integer' },
              mosh_count: { type: 'integer' },
              public_ip: { type: 'boolean', nullable: true },
            },
          },

          //////////////////////////////////////////////////////////////////////
          //
          // Container Image
          //

          image: {
            type: 'object',
            properties+: {
              registry: { type: 'string' },
              image: { type: 'string' },
              tag: { type: 'string' },
            },
          },

          //////////////////////////////////////////////////////////////////////
          //
          // Home Directory
          //

          home: {
            type: 'object',
            properties+: {
              name: { type: 'string' },
              class: { type: 'string' },
              mode: { type: 'string', pattern: '^(rwo|rwx)$' },
              size: { type: 'string', nullable: true },
            },
          },

          //////////////////////////////////////////////////////////////////////
          //
          // Volumes (System and Data)
          //

          volumes: {
            type: 'object',
            properties+: {
              exclude_cluster_defaults: { type: 'boolean' },
              exclude: { type: 'array', items: { type: 'string' } },
              volmounts: { type: 'array', items: { type: 'object', 'x-kubernetes-preserve-unknown-fields': true } },
              volumes: { type: 'array', items: { type: 'string' } },
              snapshots: { type: 'array', items: { type: 'string' } },
            },
          },

          //////////////////////////////////////////////////////////////////////
          //
          // OS
          //

          os: {
            type: 'object',
            properties+: {
              shell: { type: 'string' },
              timezone: { type: 'string' },
              personal_username: { type: 'boolean', nullable: true },
            },
          },

          //////////////////////////////////////////////////////////////////////
          //
          // Misc Top-Level Properties

          power_off: { type: 'boolean', nullable: true },
          docker_mode: { type: 'boolean' },
          privileged: { type: 'boolean' },

        },
      },
    },
  },

  view_role: $.ClusterRole + {
    name:: 'aug:devpod-view',
    metadata+: {
      labels+: {
        'rbac.authorization.k8s.io/aggregate-to-view': 'true',
      },
    },
    rules+: [
      {
        apiGroups: [$.crd.spec.group],
        resources: [$.crd.spec.names.plural],
        verbs: $.READ_VERBS,
      },
    ],
  },

  edit_role: $.ClusterRole + {
    name:: 'aug:devpod-edit',
    metadata+: {
      labels+: {
        'rbac.authorization.k8s.io/aggregate-to-edit': 'true',
      },
    },
    rules+: [
      {
        apiGroups: [$.crd.spec.group],
        resources: [$.crd.spec.names.plural],
        verbs: $.WRITE_VERBS,
      },
    ],
  },
}
