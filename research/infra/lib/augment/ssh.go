package augment

import (
	"context"
	"fmt"
	"os"
	"strings"

	"github.com/augmentcode/augment/infra/lib/ssh"
)

// Swappable for testing.
var tReadFile = os.ReadFile

func (c Clients) AuthorizedKeys(ctx context.Context, user string, opts ...CommonOpt) (string, error) {
	o, err := mkCommonOpts(opts...)
	if err != nil {
		return "", err
	}

	maybeMap := func(fname, orig string) (string, error) {
		orig2, err := ssh.MaybeMapPrivateToPublic(orig)
		if err != nil {
			return "", err
		} else if orig != orig2 {
			c.LogWarn("%s: Auto-converting SSH PrivateKey to SSH PublicKey. If the file has multiple PrivateKey blocks, the rest were ignored.", fname)
		}
		return orig2, nil
	}

	switch {
	case len(o.fromBuf) > 0:
		return maybeMap("<memory>", o.fromBuf)

	case len(o.fromFile) > 0:
		if buf, err := tReadFile(o.fromFile); err != nil {
			return "", err
		} else {
			return maybeMap(o.fromFile, string(buf))
		}

	case o.fromK8s:
		if cm, err := c.k8s.GetConfigMap(ctx, c.authKeysConfigMap); err != nil {
			return "", err
		} else {
			return cm.Key(user), nil
		}

	case o.fromGH && len(o.ghUserName) != 0:
		return c.GitHubSSHKeysForGHUser(ctx, o.ghUserName)

	case o.fromGH && len(o.ghUserName) == 0:
		return c.GitHubSSHKeys(ctx, user)

	default:
		return "", fmt.Errorf("no input keys provided")
	}
}

func (c Clients) AuthorizedKeysSet(ctx context.Context, user string, opts ...CommonOpt) (string, error) {
	keys, err := c.AuthorizedKeys(ctx, user, opts...)
	if err != nil {
		return "", err
	}

	cm, err := c.k8s.GetConfigMap(ctx, c.authKeysConfigMap)
	if err != nil {
		return "", err
	}

	o, err := mkCommonOpts(opts...)
	if err != nil {
		return "", err
	}

	if o.extend {
		if cur := cm.Key(user); len(cur) > 0 {
			if strings.HasSuffix(cur, "\n\n") {
				// noop
			} else if strings.HasSuffix(cur, "\n") {
				cur += "\n"
			} else {
				cur += "\n\n"
			}
			keys = cur + keys
		}
	}

	if _, err := ssh.ValidateAuthorizedKeys(keys); err != nil {
		return "", err
	}

	_, err = cm.SetKey(ctx, c.k8s, user, keys)
	return keys, err
}

func (c Clients) ValidateAuthorizedKeysFrom(ctx context.Context, user string, os ...CommonOpt) (int, error) {
	keys, err := c.AuthorizedKeys(ctx, user, os...)
	if err != nil {
		return -1, err
	}
	return ssh.ValidateAuthorizedKeys(keys)
}
