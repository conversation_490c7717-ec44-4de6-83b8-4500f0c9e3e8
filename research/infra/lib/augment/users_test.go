package augment

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"os/user"
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	corev1 "k8s.io/api/core/v1"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/infra/lib/logger"
)

func TestWhoAmIMetadata(t *testing.T) {
	tests := map[string]struct {
		wFileName string
		fBuf      string
		fErr      error
		want      string
	}{
		"io-error": {
			wFileName: "/startup/user",
			fBuf:      "__ignored__",
			fErr:      fmt.Errorf("IO Error"),
			want:      "",
		},
		"ignore-augment": {
			wFileName: "/startup/user",
			fBuf:      "augment",
			fErr:      nil,
			want:      "",
		},
		"success": {
			wFileName: "/startup/user",
			fBuf:      "user0",
			fErr:      nil,
			want:      "user0",
		},
		"success-trim": {
			wFileName: "/startup/user",
			fBuf:      " user0\n",
			fErr:      nil,
			want:      "user0",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			// NOTE: Cannot be parallel because of tHostname global var.

			oReadFile := tReadFile
			t.Cleanup(func() { tReadFile = oReadFile })
			tReadFile = func(fname string) ([]byte, error) {
				if got, want := fname, tc.wFileName; got != want {
					t.Errorf("filename: got %v, want %v.", got, want)
				}
				return []byte(tc.fBuf), tc.fErr
			}

			if got, want := WhoAmIMetadata(), tc.want; got != want {
				t.Errorf("got %v, want %v.", got, want)
			}
		})
	}
}

func TestWhoAmIHostname(t *testing.T) {
	tests := map[string]struct {
		fName string
		fErr  error
		want  string
	}{
		"error": {
			fName: "user0",
			fErr:  fmt.Errorf("error"),
			want:  "",
		},
		"no-hyphen": {
			fName: "user0dev",
			want:  "",
		},
		"no-dev": {
			fName: "user0-nod-ev",
			want:  "",
		},
		"dev-prefix": {
			fName: "dev-user0",
			want:  "",
		},
		"match": {
			fName: "user0-dev",
			want:  "user0",
		},
		"match-start": {
			fName: "user0-dev-user1-dev",
			want:  "user0",
		},
		"invalid-augment": {
			fName: "augment-dev",
			want:  "",
		},
		"invalid-root": {
			fName: "root-dev",
			want:  "",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			// NOTE: Cannot be parallel because of tHostname global var.

			oHostname := tHostname
			t.Cleanup(func() { tHostname = oHostname })
			tHostname = func() (string, error) {
				return tc.fName, tc.fErr
			}

			if got, want := WhoAmIHostname(), tc.want; got != want {
				t.Errorf("got %v, want %v.", got, want)
			}
		})
	}
}

func TestWhoAmISystem(t *testing.T) {
	tests := map[string]struct {
		fName string
		fErr  error
		want  string
	}{
		"error": {
			fName: "fname0",
			fErr:  fmt.Errorf("error"),
			want:  "",
		},
		"no-error": {
			fName: "fname0",
			fErr:  nil,
			want:  "fname0",
		},
		"no-error-blank": {
			fName: "",
			fErr:  nil,
			want:  "",
		},
		"invalid-augment": {
			fName: "augment",
			fErr:  nil,
			want:  "",
		},
		"invalid-root": {
			fName: "root",
			fErr:  nil,
			want:  "",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			// NOTE: Cannot be parallel because of tUserCurrent global var.

			oUserCurrent := tUserCurrent
			t.Cleanup(func() { tUserCurrent = oUserCurrent })
			tUserCurrent = func() (*user.User, error) {
				return &user.User{Username: tc.fName}, tc.fErr
			}

			if got, want := WhoAmISystem(), tc.want; got != want {
				t.Errorf("got %v, want %v.", got, want)
			}
		})
	}
}

func TestWhoAmIAugmentConfig(t *testing.T) {
	tests := map[string]struct {
		fuName     string
		fuHome     string
		fuErr      error
		wrFileName string
		frBuf      string
		frErr      error
		want       string
	}{
		"user-current-error": {
			fuName:     "user0",
			fuHome:     "/home/<USER>",
			fuErr:      fmt.Errorf("user.Current() error"),
			wrFileName: "_unused_filename_",
			frBuf:      "_unused_json_",
			frErr:      fmt.Errorf("os.ReadFile() error"),
			want:       "",
		},
		"readfile-error": {
			fuName:     "user0",
			fuHome:     "/home/<USER>",
			fuErr:      nil,
			wrFileName: "/home/<USER>/.augment/user.json",
			frBuf:      "_unused_json_",
			frErr:      fmt.Errorf("os.ReadFile() error"),
			want:       "",
		},
		"invalid-json-error": {
			fuName:     "user0",
			fuHome:     "/home/<USER>",
			fuErr:      nil,
			wrFileName: "/home/<USER>/.augment/user.json",
			frBuf:      "_invalid_json_",
			frErr:      nil,
			want:       "",
		},
		"missing-key": {
			fuName:     "user0",
			fuHome:     "/home/<USER>",
			fuErr:      nil,
			wrFileName: "/home/<USER>/.augment/user.json",
			frBuf:      `{"key1": "val1"}`,
			frErr:      nil,
			want:       "",
		},
		"root-user": {
			fuName:     "user0",
			fuHome:     "/home/<USER>",
			fuErr:      nil,
			wrFileName: "/home/<USER>/.augment/user.json",
			frBuf:      `{"key1": "val1", "name": "root"}`,
			frErr:      nil,
			want:       "",
		},
		"valid-user": {
			fuName:     "user0",
			fuHome:     "/home/<USER>",
			fuErr:      nil,
			wrFileName: "/home/<USER>/.augment/user.json",
			frBuf:      `{"key1": "val1", "name": "user42"}`,
			frErr:      nil,
			want:       "user42",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			// NOTE: Cannot be parallel because of tUserCurrent global var.

			oUserCurrent := tUserCurrent
			t.Cleanup(func() { tUserCurrent = oUserCurrent })
			tUserCurrent = func() (*user.User, error) {
				return &user.User{Username: tc.fuName, HomeDir: tc.fuHome}, tc.fuErr
			}

			oReadFile := tReadFile
			t.Cleanup(func() { tReadFile = oReadFile })
			tReadFile = func(fname string) ([]byte, error) {
				if got, want := fname, tc.wrFileName; got != want {
					t.Errorf("filename: got %v, want %v.", got, want)
				}
				return []byte(tc.frBuf), tc.frErr
			}

			if got, want := WhoAmIAugmentConfig(), tc.want; got != want {
				t.Errorf("got %v, want %v.", got, want)
			}
		})
	}
}

func TestOSUserAdd(t *testing.T) {
	tests := map[string]struct {
		inUsername   string
		inCreateHome bool
		wantPathSfx  string
		wantArgs     []string
		wantErr      string
		fakeErr      error
	}{
		"basic-success": {
			inUsername:   "user0",
			inCreateHome: false,
			wantPathSfx:  "/useradd",
			wantArgs:     []string{"useradd", "--shell", "/bin/bash", "-U", "-G", "augment", "user0"},
			wantErr:      "",
			fakeErr:      nil,
		},
		"with-home": {
			inUsername:   "user0",
			inCreateHome: true,
			wantPathSfx:  "/useradd",
			wantArgs:     []string{"useradd", "--shell", "/bin/bash", "-m", "-U", "-G", "augment", "user0"},
			wantErr:      "",
			fakeErr:      nil,
		},
		"space in name": {
			inUsername:   "user 0",
			inCreateHome: false,
			wantPathSfx:  "/useradd",
			wantArgs:     []string{"useradd", "--shell", "/bin/bash", "-U", "-G", "augment", "user 0"},
			wantErr:      "",
			fakeErr:      nil,
		},
		"empty name": {
			inUsername:   "",
			inCreateHome: false,
			wantPathSfx:  "__untested__",
			wantArgs:     []string{"__untested__"},
			wantErr:      "empty username",
			fakeErr:      nil,
		},
		"root": {
			inUsername:   "root",
			inCreateHome: false,
			wantPathSfx:  "__untested__",
			wantArgs:     []string{"__untested__"},
			wantErr:      "funny business",
			fakeErr:      nil,
		},
		"space around name": {
			inUsername:   "  user 0 \t  ",
			inCreateHome: false,
			wantPathSfx:  "/useradd",
			wantArgs:     []string{"useradd", "--shell", "/bin/bash", "-U", "-G", "augment", "user 0"},
			wantErr:      "",
			fakeErr:      nil,
		},
		"run-error": {
			inUsername:   "user0",
			inCreateHome: false,
			wantPathSfx:  "/useradd",
			wantArgs:     []string{"useradd", "--shell", "/bin/bash", "-U", "-G", "augment", "user0"},
			wantErr:      "__fake_error__",
			fakeErr:      fmt.Errorf("__fake_error__"),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			oCmdRun := tCmdRun
			t.Cleanup(func() { tCmdRun = oCmdRun })
			// NOTE(mattm): Rethink what we really want to test here, but for now adding to
			// PATH is a quick fix while switching to bazel.
			t.Setenv("PATH", "/usr/sbin:"+os.Getenv("PATH"))
			tCmdRun = func(cmd *exec.Cmd) error {
				// Consider adding more testing if we start to use these.
				if got, want := len(cmd.Env), 0; got != want {
					t.Errorf("len(cmd.Env): got %v, want %v.", got, want)
				}
				if cmd.Stdin != nil {
					t.Errorf("stdin: got %v, want nil.", cmd.Stdin)
				}

				if got, want := cmd.Path, tc.wantPathSfx; !strings.HasSuffix(got, want) {
					t.Errorf("path: got %v, want suffix %v.", got, want)
				}
				if diff := cmp.Diff(tc.wantArgs, cmd.Args); diff != "" {
					t.Errorf("args: -want +got:\n%s", diff)
				}

				return tc.fakeErr
			}

			ctx := context.Background()
			cli := Clients{Logger: logger.New(nil)}
			gotErr := cli.OSUserAdd(ctx, tc.inUsername, tc.inCreateHome)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want error containing '%s'.", gotErr, tc.wantErr)
			}
		})
	}
}

func TestOSUserSet(t *testing.T) {
	tests := map[string]struct {
		inUsername   string
		inCreateHome bool
		wantErr      string

		wantLookupCalled  string
		fakeUserLookupErr error
		wantCmdRunCalled  []string
		fakeCmdRunErr     error
	}{
		"empty": {
			inUsername:        "",
			inCreateHome:      false,
			wantErr:           "userset: empty username",
			wantLookupCalled:  "",
			fakeUserLookupErr: nil,
			wantCmdRunCalled:  nil,
			fakeCmdRunErr:     nil,
		},
		"root": {
			inUsername:        "root",
			inCreateHome:      false,
			wantErr:           "userset: root?",
			wantLookupCalled:  "",
			fakeUserLookupErr: nil,
			wantCmdRunCalled:  nil,
			fakeCmdRunErr:     nil,
		},
		"trim-input": {
			inUsername:        "  root \t  \t",
			inCreateHome:      false,
			wantErr:           "userset: root?",
			wantLookupCalled:  "",
			fakeUserLookupErr: nil,
			wantCmdRunCalled:  nil,
			fakeCmdRunErr:     nil,
		},
		"user-exists": {
			inUsername:        "user0",
			inCreateHome:      false,
			wantErr:           "",
			wantLookupCalled:  "user0",
			fakeUserLookupErr: nil,
			wantCmdRunCalled:  nil,
			fakeCmdRunErr:     nil,
		},
		"user-lookup-error": {
			inUsername:        "user0",
			inCreateHome:      false,
			wantErr:           "__error__",
			wantLookupCalled:  "user0",
			fakeUserLookupErr: fmt.Errorf("__error__"),
			wantCmdRunCalled:  nil,
			fakeCmdRunErr:     nil,
		},
		"basic-success": {
			inUsername:        "user0",
			inCreateHome:      false,
			wantErr:           "",
			wantLookupCalled:  "user0",
			fakeUserLookupErr: user.UnknownUserError("unknown user"),
			wantCmdRunCalled:  []string{"useradd", "--shell", "/bin/bash", "-U", "-G", "augment", "user0"},
			fakeCmdRunErr:     nil,
		},
		"basic-success-with-home": {
			inUsername:        "user0",
			inCreateHome:      true,
			wantErr:           "",
			wantLookupCalled:  "user0",
			fakeUserLookupErr: user.UnknownUserError("unknown user"),
			wantCmdRunCalled:  []string{"useradd", "--shell", "/bin/bash", "-m", "-U", "-G", "augment", "user0"},
			fakeCmdRunErr:     nil,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			oUserLookup := tUserLookup
			t.Cleanup(func() { tUserLookup = oUserLookup })
			gotLU, gotLUWith := false, ""
			tUserLookup = func(username string) (*user.User, error) {
				gotLU, gotLUWith = true, username
				return nil, tc.fakeUserLookupErr
			}

			oCmdRun := tCmdRun
			t.Cleanup(func() { tCmdRun = oCmdRun })
			gotRun, gotRunArgs := false, []string(nil)
			tCmdRun = func(cmd *exec.Cmd) error {
				// Consider adding more testing if we start to use these.
				if got, want := len(cmd.Env), 0; got != want {
					t.Errorf("len(cmd.Env): got %v, want %v.", got, want)
				}
				if cmd.Stdin != nil {
					t.Errorf("stdin: got %v, want nil.", cmd.Stdin)
				}
				gotRun, gotRunArgs = true, cmd.Args
				return tc.fakeCmdRunErr
			}

			ctx := context.Background()
			cli := Clients{Logger: logger.New(nil)}
			gotErr := cli.OSUserSet(ctx, tc.inUsername, tc.inCreateHome)

			if got, want := gotLU, (tc.wantLookupCalled != ""); got != want {
				t.Errorf("UserLookup() called: got %v, want %v.", got, want)
			}
			if got, want := gotLUWith, tc.wantLookupCalled; got != want {
				t.Errorf("got UserLookup(%v), want UserLookup(%v).", got, want)
			}

			if got, want := gotRun, (tc.wantCmdRunCalled != nil); got != want {
				t.Errorf("useradd called: got %v, want %v.", got, want)
			}
			if diff := cmp.Diff(tc.wantCmdRunCalled, gotRunArgs); diff != "" {
				t.Errorf("useradd: -want +got:\n%s", diff)
			}

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want error containing '%s'.", gotErr, tc.wantErr)
			}
		})
	}
}

func TestOSUserSetAllFromConfigMap(t *testing.T) {
	tests := map[string]struct {
		fakeData          map[string]string
		wantErr           string
		wantLookupCalled  []string
		fakeUserLookupErr error
	}{
		"all-exist": {
			fakeData: map[string]string{
				"user0": "",
				"user1": "",
				"user2": "",
			},
			fakeUserLookupErr: nil,
			wantLookupCalled:  []string{"user0", "user1", "user2"},
			wantErr:           "",
		},
		"all-error": {
			fakeData: map[string]string{
				"user0": "",
				"user1": "",
				"user2": "",
			},
			fakeUserLookupErr: fmt.Errorf("__test_error__"),
			wantLookupCalled:  []string{"user0", "user1", "user2"},
			wantErr:           "__test_error__",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			oUserLookup := tUserLookup
			t.Cleanup(func() { tUserLookup = oUserLookup })
			gotLU := []string{}
			tUserLookup = func(username string) (*user.User, error) {
				gotLU = append(gotLU, username)
				return nil, tc.fakeUserLookupErr
			}

			oCmdRun := tCmdRun
			t.Cleanup(func() { tCmdRun = oCmdRun })
			tCmdRun = func(cmd *exec.Cmd) error {
				t.Fatal("useradd not expected for this test.")
				return nil
			}

			raw := &corev1.ConfigMap{Data: tc.fakeData}
			cm := k8s.NewConfigMap(raw)

			ctx := context.Background()
			cli := Clients{Logger: logger.New(nil)}
			gotErr := cli.OSUserSetAllFromConfigMap(ctx, cm, false)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want error containing '%s'.", gotErr, tc.wantErr)
			}

			if diff := cmp.Diff(tc.wantLookupCalled, gotLU); diff != "" {
				t.Errorf("users: -want +got:\n%s", diff)
			}
		})
	}
}
