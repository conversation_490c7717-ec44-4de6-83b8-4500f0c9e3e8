package augment

import (
	"bytes"
	"context"
	"fmt"
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/rs/zerolog"

	"github.com/augmentcode/augment/infra/lib/logger"
)

const (
	sshTypeRSA   = "ssh-rsa"
	sshPubRSA    = "AAAAB3NzaC1yc2EAAAADAQABAAABgQDIGuJoesE1QkccH5EkBydksRElEPG2zLVF10P2HwugtyBHwv/Br8TdHZznp438+4NtmO1Pf9TSf8DWYp8/gRS8ODqFoniiosUi+zHGqj6/WWqY8/eE/qwWtXg3WNmyWtbK68jkHaQ/xDPnawSJpRF96TgDnKKjXwDkczCapXfzUF3S5yhQ9Mg7r9wsttJzoemzaaPYcjqz8wHvSDPHFNNlZ4Igz4TPUuxIzo0YsbDnggCzVOAc/dhCs7W/ptLVmoU4qzhWJd/u9BB9AFG7Am1NgXyRuzQjs2MsI2MbXoeCtE60P3fsE17wkY77x5hQM4hbUdHyCJPwg/kTuP2CpqerAOc9l0BHLBOmC6WGtRWvZ6rpJ7eliwU5ApqTQ23hu+jcmIU/PCeLRAiecGWshfUSUVBhNN3UcB3KIR+4r8ps4cEiZfKYP4IM2W2oFntXrIRSJTZxsiuRMBDkAJUA6y42PiD4RDxmVQ8OUrURAQ86pRzoqqKguRNLC5hduXK/mPk="
	sshType25519 = "ssh-ed25519"
	sshPub25519  = "AAAAC3NzaC1lZDI1NTE5AAAAIASEsyJoTMgSLcaKobkm3BhA2WMi/rgshe/YrZ+S3+zh"
	// pragma: allowlist nextline secret
	sshPriv25519 = `****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
)

func TestAuthorizedKeys(t *testing.T) {
	tests := map[string]struct {
		inUser   string
		inOpts   []CommonOpt
		wantKeys string
		wantErr  string
		wantLogs []string

		wantReadFileCalled string
		fReadFileBuf       string
		fReadFileErr       error
	}{
		"no-input-error": {
			inOpts:   nil,
			wantKeys: "",
			wantErr:  "no input keys provided",

			wantReadFileCalled: "",
			fReadFileBuf:       "__unused_buf__",
			fReadFileErr:       fmt.Errorf("__unused_err__"),
		},
		"prefer-buf-1": {
			inOpts: []CommonOpt{
				OptFromBuf("keys0"),
				OptFromFile("file0"),
				OptFromK8s(true),
				OptFromGitHub(true),
			},
			wantKeys: "keys0",
			wantErr:  "",

			wantReadFileCalled: "",
			fReadFileBuf:       "__unused_buf__",
			fReadFileErr:       fmt.Errorf("__unused_err__"),
		},
		"prefer-file-2": {
			inOpts: []CommonOpt{
				OptFromBuf(""),
				OptFromFile("file0"),
				OptFromK8s(true),
				OptFromGitHub(true),
			},
			wantKeys: "keys0",
			wantErr:  "",

			wantReadFileCalled: "file0",
			fReadFileBuf:       "keys0",
			fReadFileErr:       nil,
		},
		"prefer-file-2-error": {
			inOpts: []CommonOpt{
				OptFromBuf(""),
				OptFromFile("file0"),
				OptFromK8s(true),
				OptFromGitHub(true),
			},
			wantKeys: "",
			wantErr:  "error0",

			wantReadFileCalled: "file0",
			fReadFileBuf:       "keys0",
			fReadFileErr:       fmt.Errorf("error0"),
		},
		"mapped": {
			inOpts: []CommonOpt{
				OptFromBuf(sshPriv25519),
			},
			wantKeys: sshType25519 + " " + sshPub25519 + "\n",
			wantErr:  "",
			wantLogs: []string{
				"WRN <memory>: Auto-converting SSH PrivateKey to SSH PublicKey. If the file has multiple PrivateKey blocks, the rest were ignored.",
			},

			wantReadFileCalled: "",
			fReadFileBuf:       "__unused_buf__",
			fReadFileErr:       fmt.Errorf("__unused_err__"),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			oReadFile := tReadFile
			t.Cleanup(func() { tReadFile = oReadFile })
			gotRF, gotRFWith := false, ""
			tReadFile = func(fname string) ([]byte, error) {
				gotRF, gotRFWith = true, fname
				return []byte(tc.fReadFileBuf), tc.fReadFileErr
			}

			ctx := context.Background()
			buf := &bytes.Buffer{}
			cw := zerolog.ConsoleWriter{Out: buf}
			cw.NoColor = true
			cw.PartsOrder = []string{zerolog.LevelFieldName, zerolog.MessageFieldName}
			cli := Clients{Logger: logger.New(cw)}
			gotKeys, gotErr := cli.AuthorizedKeys(ctx, tc.inUser, tc.inOpts...)

			if got, want := gotRF, (tc.wantReadFileCalled != ""); got != want {
				t.Errorf("os.ReadFile() called: got %v, want %v.", got, want)
			}
			if got, want := gotRFWith, tc.wantReadFileCalled; got != want {
				t.Errorf("got os.ReadFile(%v), want os.ReadFile(%v).", got, want)
			}

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want error containing '%s'.", gotErr, tc.wantErr)
			}

			if diff := cmp.Diff(tc.wantKeys, gotKeys); diff != "" {
				t.Errorf("keys: -want +got:\n%s", diff)
			}

			gotlogs := func() []string {
				if str := buf.String(); str == "" {
					return nil
				} else {
					return strings.Split(strings.TrimSpace(str), "\n")
				}
			}()
			if diff := cmp.Diff(tc.wantLogs, gotlogs, cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("logs: -want +got:\n%s", diff)
			}
		})
	}
}

func TestAuthorizedKeysSet(t *testing.T) {
	tests := map[string]struct {
		inUser   string
		inOpts   []CommonOpt
		wantKeys string
		wantErr  string
	}{
		"no-input-error": {
			inOpts:   nil,
			wantKeys: "",
			wantErr:  "no input keys provided",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			cli := Clients{}
			gotKeys, gotErr := cli.AuthorizedKeysSet(ctx, tc.inUser, tc.inOpts...)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want error containing '%s'.", gotErr, tc.wantErr)
			}

			if diff := cmp.Diff(tc.wantKeys, gotKeys); diff != "" {
				t.Errorf("keys: -want +got:\n%s", diff)
			}
		})
	}
}

func TestValidateAuthorizedKeysFrom(t *testing.T) {
	tests := map[string]struct {
		inUser    string
		inOpts    []CommonOpt
		wantCount int
		wantErr   string
	}{
		"no-input-error": {
			inOpts:    nil,
			wantCount: -1,
			wantErr:   "no input keys provided",
		},
		"valid": {
			inOpts: []CommonOpt{OptFromBuf(
				sshTypeRSA + " " + sshPubRSA + "\n" + sshType25519 + " " + sshPub25519 + "\n",
			)},
			wantCount: 2,
			wantErr:   "",
		},
		"invalid": {
			inOpts: []CommonOpt{OptFromBuf(
				sshTypeRSA + " " + sshPubRSA + "\n" + sshType25519 + " " + sshPub25519 + "_BAD_\n",
			)},
			wantCount: 1,
			wantErr:   "line 2",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			cli := Clients{}
			gotCount, gotErr := cli.ValidateAuthorizedKeysFrom(ctx, tc.inUser, tc.inOpts...)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want error containing '%s'.", gotErr, tc.wantErr)
			}

			if got, want := gotCount, tc.wantCount; got != want {
				t.Errorf("count: got %v, want %v.", got, want)
			}
		})
	}
}
