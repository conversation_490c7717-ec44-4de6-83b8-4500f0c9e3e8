package augment

import (
	"github.com/augmentcode/augment/infra/lib/github"
	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/infra/lib/logger"
)

type Clients struct {
	*logger.Logger

	k8s *k8s.Client
	gh  github.Client

	authKeysConfigMap string
	ghUsersConfigMap  string
}

func NewClients(k8sContext string) (Clients, error) {
	k, err := k8s.New(k8sContext)
	if err != nil {
		return Clients{}, err
	}

	return Clients{
		Logger: logger.New(nil),

		k8s: k,
		gh:  github.NewPlainClient(),

		authKeysConfigMap: AuthorizedKeysConfigMap, // pragma: allowlist secret
		ghUsersConfigMap:  GitHubUserNameConfigMap,
	}, nil
}

func (c Clients) K8s() *k8s.Client {
	return c.k8s
}

func (c Clients) GitHub() github.Client {
	return c.gh
}

func (c *Clients) SetAuthorizedKeysConfigMap(cm string) {
	if cm == "" {
		cm = AuthorizedKeysConfigMap
	}
	c.authKeysConfigMap = cm // pragma: allowlist secret
}

func (c *Clients) SetGitHubUserNameConfigMap(cm string) {
	if cm == "" {
		cm = GitHubUserNameConfigMap
	}
	c.ghUsersConfigMap = cm
}

type commonOpts struct {
	fromBuf  string
	fromFile string
	fromK8s  bool
	fromGH   bool

	ghUserName string
	extend     bool
}

func mkCommonOpts(opts ...CommonOpt) (commonOpts, error) {
	ret := commonOpts{}
	for _, o := range opts {
		o(&ret)
	}
	return ret, nil
}

type CommonOpt func(*commonOpts)

func OptFromBuf(buf string) CommonOpt {
	return func(opts *commonOpts) {
		opts.fromBuf = buf
	}
}

func OptFromFile(fname string) CommonOpt {
	return func(opts *commonOpts) {
		opts.fromFile = fname
	}
}

func OptFromK8s(bs ...bool) CommonOpt {
	b := true
	for _, b = range bs {
	}
	return func(opts *commonOpts) {
		opts.fromK8s = b
	}
}

func OptFromGitHub(bs ...bool) CommonOpt {
	b := true
	for _, b = range bs {
	}
	return func(opts *commonOpts) {
		opts.fromGH = b
	}
}

func OptGitHubUser(name string) CommonOpt {
	return func(opts *commonOpts) {
		opts.ghUserName = name
	}
}

func OptAppend(bs ...bool) CommonOpt {
	b := true
	for _, b = range bs {
	}
	return func(opts *commonOpts) {
		opts.extend = b
	}
}
