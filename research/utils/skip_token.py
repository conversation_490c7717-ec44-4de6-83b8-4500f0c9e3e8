"""Processing logic related to skip tokens."""
import logging
from dataclasses import dataclass
from typing import Sequence

logger = logging.getLogger(__file__)


@dataclass
class ReplaceSkipTokenResult:
    """Results of replace_skip_tokens()."""

    completion_text: str
    """Text inserted before the first skip token."""

    suffix_replacement_text: str
    """The new string to replace suffix characters with."""

    skipped_suffix: str
    """The suffix characters that was skipped.
    Returned for veification purposes."""


def replace_skip_tokens(
    raw_output: str,
    suffix: str,
    skip_token: str,
    closing_strings: Sequence[str] = ("'''", '"""', "'", '"', "`", "]", "}", ")", ">"),
) -> ReplaceSkipTokenResult:
    """Replace the detokenized skip tokens with the substrings in suffix.

    Also count the number of suffix characters that is skipped and return that info.
    The closing symbols list serves also as a priority list, to deal with situations
    where one symbol is a substring of another.

    Args:
        text: the text to replace the skip tokens in.
        suffix: the suffix to replace the skip tokens with.
        skip_token: the skip token string
        closing_strings: list of strings considered as closign symbols.

    Returns:
        completion_text: The completion text that terminates at the first skip token.
        suffix_replacement_text: New text to replaced the skipped suffix with.
        skipped_suffix: The suffix characters that are skipped.
    """

    segments = raw_output.split(skip_token)
    completion_text = segments.pop(0)
    n_skipped_char = 0
    suffix_replacement_text = ""
    skipped_suffix = ""
    while segments:
        # first get all whitespace chars
        for i in range(n_skipped_char, len(suffix)):
            if not suffix[i].isspace():
                leading_whitespaces = suffix[n_skipped_char:i]
                break
        else:
            leading_whitespaces = suffix[n_skipped_char:]
        tail_start = n_skipped_char + len(leading_whitespaces)
        # identify which closing brackets we have here
        for closing in closing_strings:
            if suffix[tail_start:].startswith(closing):
                break
        else:  # be conservative and stop the generation here
            logger.warning(
                "Cannot associate characters in the suffix to a skip token. "
                "Start of suffix: [%s]",
                suffix[tail_start : tail_start + 10],
            )
            return ReplaceSkipTokenResult(
                completion_text,
                suffix_replacement_text,
                skipped_suffix=skipped_suffix,
            )
        # replace the skip token with the closing bracket
        n_skipped_char = tail_start + len(closing)
        suffix_replacement_text += leading_whitespaces + closing + segments.pop(0)
        skipped_suffix += leading_whitespaces + closing
    return ReplaceSkipTokenResult(
        completion_text, suffix_replacement_text, skipped_suffix
    )
