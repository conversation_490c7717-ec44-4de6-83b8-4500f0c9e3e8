#!/bin/bash -e

GIT_ROOT=$(git rev-parse --show-toplevel)

if [[ -z "$GIT_ROOT" ]]; then
  echo "Run from inside the git repository"
  exit 1
fi

sm=$(sum $GIT_ROOT/.secrets.baseline)
echo -n "Scanning for updated secrets (this takes a few minutes)..."
(cd $GIT_ROOT && detect-secrets scan \
  --baseline .secrets.baseline \
  --exclude-files 'package.lock.json|pnpm-lock.yaml|dataset_infos.json')
echo "done"
nsm=$(sum $GIT_ROOT/.secrets.baseline)
if [[ "$sm" != "$nsm" ]]; then
  echo "$GIT_ROOT/.secrets.baseline has changed; please add it to your commit"
else
  echo "$GIT_ROOT/.secrets.baseline is unchanged"
fi
