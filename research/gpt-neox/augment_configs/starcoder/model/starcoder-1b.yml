# NOTE: The 1B and 3B models use a different activation function.
activation: gelu_tanh_approximation
# Fusion doesn't work for the gelu approximation.
bias-gelu-fusion: false
hidden-size: 2048
load: /mnt/efs/augment/checkpoints/starcoderbase-1b_neox
model-parallel-size: 1
num-attention-heads: 16
num-layers: 24

# NOTE: Learning rate, etc. weren't provided for the 3b model.
train_micro_batch_size_per_gpu: 16   # Fills up a single A100 with 8k seq. length.
