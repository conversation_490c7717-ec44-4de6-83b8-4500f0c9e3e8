hidden-size: 6144
load: /mnt/efs/augment/checkpoints/starcoderbase-16b_neox_mp2
model-parallel-size: 2
num-attention-heads: 48
num-layers: 40

# Optimization configuration for fine tuning as reported in the paper.
# NOTE: The paper assumes a training batch size of 4M tokens, which would require
#   either a lot of gradient accumulation or 64 nodes. Please adjust your lr
#   accordingly.

train_micro_batch_size_per_gpu: 2    # Fills up a single A100 with 8k seq. length.
finetune: true
warmup: 0.01  # Typically this is ~1k iterations.
lr_decay_style: cosine
min_lr: 5.0e-6
optimizer:
  params:
    betas:
      _ 0.9
      _ 0.95
    eps: 1.0e-08
    lr: 5.0e-05
  type: Adam
weight_decay: 0.1
