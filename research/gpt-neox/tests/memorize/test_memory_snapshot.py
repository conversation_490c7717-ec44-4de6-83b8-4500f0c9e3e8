"""Tests for _MemorySnapshot."""

import numpy as np
import torch


def test_concat_array():
    from megatron.memorize.memory_snapshot import _ConcatArray

    # Construct an example _ConcatArray
    conc_arr = _ConcatArray(
        [
            np.array([[1, 2, 3]]),
            np.array([[11, 12, 13], [14, 15, 16], [17, 18, 19]]),
            np.array([[31, 32, 33], [34, 35, 36]]),
        ]
    )

    # Validate single-element accesses

    np.testing.assert_equal(conc_arr.get(np.array([0])), [[1, 2, 3]])

    np.testing.assert_equal(conc_arr.get(np.array([1])), [[11, 12, 13]])

    np.testing.assert_equal(conc_arr.get(np.array([2])), [[14, 15, 16]])

    np.testing.assert_equal(conc_arr.get(np.array([3])), [[17, 18, 19]])

    np.testing.assert_equal(conc_arr.get(np.array([4])), [[31, 32, 33]])

    np.testing.assert_equal(conc_arr.get(np.array([5])), [[34, 35, 36]])

    # Validate multi-element accesses with increasing indexes

    np.testing.assert_equal(
        conc_arr.get(np.array([0, 1, 2, 3, 4, 5])),
        [
            [1, 2, 3],
            [11, 12, 13],
            [14, 15, 16],
            [17, 18, 19],
            [31, 32, 33],
            [34, 35, 36],
        ],
    )

    np.testing.assert_equal(
        conc_arr.get(np.array([1, 3, 5])), [[11, 12, 13], [17, 18, 19], [34, 35, 36]]
    )

    # Validate multi-element accesses with repeating indexes

    np.testing.assert_equal(
        conc_arr.get(np.array([0, 0, 0, 5, 5])),
        [[1, 2, 3], [1, 2, 3], [1, 2, 3], [34, 35, 36], [34, 35, 36]],
    )

    # Validate multi-element accesses with decreasing indexes
    # NOTE: the output ordering may be unexpected. See _ConcatArray.get().

    np.testing.assert_equal(
        conc_arr.get(np.array([5, 3, 1])), [[34, 35, 36], [17, 18, 19], [11, 12, 13]]
    )

    expected_shape = [6, 3]
    assert len(conc_arr.shape) == len(expected_shape)
    assert all([a == b for a, b in zip(conc_arr.shape, expected_shape)])

    assert conc_arr.dtype == np.array([[1]]).dtype


def test_memory_snapshot():
    from megatron.memorize.memory_snapshot import _ConcatArray, _MemorySnapshot

    snapshot = _MemorySnapshot(
        header={"dim": 3, "heads": 2},
        layer_number=0,
        keys=[
            _ConcatArray(np.array([[[0, 0, 1], [0, 1, 0], [1, 0, 0]]], dtype=np.half)),
            _ConcatArray(np.array([[[0, 0, 1], [0, 1, 0], [1, 0, 0]]], dtype=np.half)),
        ],
        values=[
            _ConcatArray(
                np.array([[[0, 0, 10], [0, 10, 0], [10, 0, 0]]], dtype=np.half)
            ),
            _ConcatArray(np.array([[[0, 0, 5], [0, 5, 0], [5, 0, 0]]], dtype=np.half)),
        ],
        memory_knn_top_k=1,
    )

    #  [sq, b, np, hn]
    queries = torch.tensor(
        [[[[1, 0, 0], [1, 0, 0]]], [[[0, 0, 1], [0, 1, 0]]], [[[1, 0, 0], [0, 0, 1]]]],
        dtype=torch.half,
    )

    keys, vals, _mask = snapshot.get_memories(queries)

    np.testing.assert_equal(keys.squeeze(dim=1), queries.numpy())

    np.testing.assert_equal(
        vals.squeeze(dim=1),
        np.array(
            [
                [[[10, 0, 0], [5, 0, 0]]],
                [[[0, 0, 10], [0, 5, 0]]],
                [[[10, 0, 0], [0, 0, 5]]],
            ],
            dtype=np.half,
        ),
    )
