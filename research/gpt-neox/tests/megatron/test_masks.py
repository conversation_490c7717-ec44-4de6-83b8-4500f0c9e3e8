"""Tests for loss and attention masking."""

import pytest
import torch
from megatron.utils import LossMaskPolicyName, _get_ctx_loss_mask, get_loss_mask_hook


def _test_get_ltor_masks_and_position_ids(
    data,
    eod_token,
    target=None,
    pad_token=None,
    attn_mask_mode="causal",
    loss_mask_mode: LossMaskPolicyName = "none",
    exp_attn_mask=None,
    exp_loss_mask=None,
):
    from megatron.utils import (
        get_ltor_masks_and_position_ids,
        register_loss_mask_policy,
    )

    if target is None:
        target = data
    if pad_token is None:
        pad_token = eod_token

    register_loss_mask_policy(loss_mask_mode, {"pad": pad_token})
    attn_mask, loss_mask, _position_ids = get_ltor_masks_and_position_ids(
        data=data, target=target, eod_token=eod_token, attn_mask_mode=attn_mask_mode
    )

    if exp_attn_mask is not None:
        assert torch.equal(attn_mask, exp_attn_mask)

    if exp_loss_mask is not None:
        assert torch.equal(loss_mask["lm_loss"], exp_loss_mask)


def test_doc_mask():
    from megatron.utils import get_doc_attn_mask, register_attn_mask_hook

    eod = 50000

    _test_get_ltor_masks_and_position_ids(
        data=torch.tensor([[10, 20, 10, eod, 30, eod, 10], [7, 7, eod, 4, 5, 6, 7]]),
        eod_token=eod,
        attn_mask_mode="doc_causal",
        exp_attn_mask=torch.tensor(
            [
                [
                    [
                        [False, True, True, True, True, True, True],
                        [False, False, True, True, True, True, True],
                        [False, False, False, True, True, True, True],
                        [False, False, False, False, True, True, True],
                        [True, True, True, True, False, True, True],
                        [True, True, True, True, False, False, True],
                        [True, True, True, True, True, True, False],
                    ]
                ],
                [
                    [
                        [False, True, True, True, True, True, True],
                        [False, False, True, True, True, True, True],
                        [False, False, False, True, True, True, True],
                        [True, True, True, False, True, True, True],
                        [True, True, True, False, False, True, True],
                        [True, True, True, False, False, False, True],
                        [True, True, True, False, False, False, False],
                    ]
                ],
            ]
        ),
        exp_loss_mask=torch.ones(2, 7, dtype=torch.float),
    )

    _test_get_ltor_masks_and_position_ids(
        data=torch.tensor([[10, 20, 10, eod, 30, eod, 10], [7, 7, eod, 4, 5, 6, 7]]),
        eod_token=eod,
        attn_mask_mode="retrieve_causal",
        exp_attn_mask=torch.tensor(
            [
                [
                    [
                        [False, True, True, True, True, True, True],
                        [False, False, True, True, True, True, True],
                        [False, False, False, True, True, True, True],
                        [False, False, False, False, True, True, True],
                        [True, True, True, True, False, True, True],
                        [True, True, True, True, False, False, True],
                        [False, False, False, False, False, False, False],
                    ]
                ],
                [
                    [
                        [False, True, True, True, True, True, True],
                        [False, False, True, True, True, True, True],
                        [False, False, False, True, True, True, True],
                        [False, False, False, False, True, True, True],
                        [False, False, False, False, False, True, True],
                        [False, False, False, False, False, False, True],
                        [False, False, False, False, False, False, False],
                    ]
                ],
            ]
        ),
        exp_loss_mask=torch.ones(2, 7, dtype=torch.float),
    )

    # Let's treat token 10 as the EOD token
    register_attn_mask_hook(
        lambda attention_mask, tokens, eod_token: get_doc_attn_mask(
            attention_mask, "doc_causal", tokens, eod_token=10
        )
    )

    _test_get_ltor_masks_and_position_ids(
        data=torch.tensor([[10, 20, 10, eod, 30, eod, 10], [7, 7, eod, 10, 5, 6, 7]]),
        eod_token=eod,
        attn_mask_mode="hook",
        exp_attn_mask=torch.tensor(
            [
                [
                    [
                        [False, True, True, True, True, True, True],
                        [True, False, True, True, True, True, True],
                        [True, False, False, True, True, True, True],
                        [True, True, True, False, True, True, True],
                        [True, True, True, False, False, True, True],
                        [True, True, True, False, False, False, True],
                        [True, True, True, False, False, False, False],
                    ]
                ],
                [
                    [
                        [False, True, True, True, True, True, True],
                        [False, False, True, True, True, True, True],
                        [False, False, False, True, True, True, True],
                        [False, False, False, False, True, True, True],
                        [True, True, True, True, False, True, True],
                        [True, True, True, True, False, False, True],
                        [True, True, True, True, False, False, False],
                    ]
                ],
            ]
        ),
        exp_loss_mask=torch.ones(2, 7, dtype=torch.float),
    )


def test_loss_mask():
    from megatron.utils import register_loss_mask_hook

    eod = 50000
    pad = 10

    data = torch.tensor([[pad, 20, pad, eod, 30, eod, pad], [7, 7, pad, 4, 5, 6, 7]])
    # left rotate such that the i+1 data element becomes the target for element i.
    target = data.roll(shifts=-1, dims=1)
    exp_pad_loss_mask = torch.ones_like(data, dtype=torch.float)
    exp_pad_loss_mask[target == pad] = 0

    _test_get_ltor_masks_and_position_ids(
        data=data,
        eod_token=eod,
        pad_token=pad,
        exp_loss_mask=torch.ones_like(data, dtype=torch.float),
    )

    _test_get_ltor_masks_and_position_ids(
        data=data,
        eod_token=eod,
        pad_token=pad,
        exp_loss_mask=torch.ones_like(data, dtype=torch.float),
    )

    # Validate that the pad policy uses only the "target" side
    _test_get_ltor_masks_and_position_ids(
        data=data,
        target=target,
        eod_token=eod,
        pad_token=pad,
        loss_mask_mode="pad",
        exp_loss_mask=exp_pad_loss_mask,
    )

    # Mask loss for the pad token
    register_loss_mask_hook(lambda data, target: (data != pad).float())

    _test_get_ltor_masks_and_position_ids(
        data=data,
        eod_token=eod,
        loss_mask_mode="hook",
        exp_loss_mask=torch.tensor(
            [[0.0, 1.0, 0.0, 1.0, 1.0, 1.0, 0.0], [1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0]]
        ),
    )


def test_ctx_loss_mask():
    loss_mask_f = _get_ctx_loss_mask(start_tks={-1}, end_tks={-2})

    # no loss
    target = torch.Tensor([[5, 6, 7, 8, 9]]).long()
    expect = torch.Tensor([[0, 0, 0, 0, 0]]).long()
    mask = loss_mask_f(target, target)
    assert (mask == expect).all()

    # start early
    target = torch.Tensor([[-1, 1, 2, 3, 4]]).long()
    expect = torch.Tensor([[0, 1, 1, 1, 1]]).long()
    mask = loss_mask_f(target, target)
    assert (mask == expect).all()

    # start then end
    target = torch.Tensor([[5, 6, -1, 7, 8, 9, -2, 4, 3]]).long()
    expect = torch.Tensor([[0, 0, 0, 1, 1, 1, 1, 0, 0]])
    mask = loss_mask_f(target, target)
    assert (mask == expect).all()

    # switch start and end
    target = torch.Tensor([[5, 6, -2, 7, 8, 9, -1, 4, 3]]).long()
    expect = torch.Tensor([[0, 0, 0, 0, 0, 0, 0, 1, 1]])
    mask = loss_mask_f(target, target)
    assert (mask == expect).all()

    # two mid spans
    target = torch.Tensor([[3, -1, 4, -2, 5, 6, -1, 7, 8]]).long()
    expect = torch.Tensor([[0, 0, 1, 1, 0, 0, 0, 1, 1]])
    mask = loss_mask_f(target, target)
    assert (mask == expect).all()

    loss_mask_f = _get_ctx_loss_mask(start_tks=[-1], end_tks=[-4, -3, -2])

    # start then end
    target = torch.Tensor([[5, 6, -1, 7, 8, 9, -3, 4, 3]]).long()
    expect = torch.Tensor([[0, 0, 0, 1, 1, 1, 1, 0, 0]])
    mask = loss_mask_f(target, target)
    assert (mask == expect).all()

    # two mid spans
    target = torch.Tensor([[3, -1, 4, -2, 5, 6, -1, 7, 8]]).long()
    expect = torch.Tensor([[0, 0, 1, 1, 0, 0, 0, 1, 1]])
    mask = loss_mask_f(target, target)
    assert (mask == expect).all()

    # inverted mask
    loss_mask_f = _get_ctx_loss_mask(start_tks={-1}, end_tks={-2}, inverted=True)
    # two mid spans
    target = torch.Tensor([[3, -1, 4, -2, 5, 6, -1, 7, 8]]).long()
    expect = torch.Tensor([[1, 1, 0, 0, 1, 1, 1, 0, 0]])
    mask = loss_mask_f(target, target)
    assert (mask == expect).all()

    # first span only
    loss_mask_f = _get_ctx_loss_mask(start_tks={-1}, end_tks={-2}, first_span_only=True)
    # two mid spans
    target = torch.Tensor([[3, -1, 4, -2, 5, 6, -1, 7, 8]]).long()
    expect = torch.Tensor([[0, 0, 1, 1, 0, 0, 0, 0, 0]]).long()
    mask = loss_mask_f(target, target)
    assert (mask == expect).all()

    # first span with multiple ends only
    loss_mask_f = _get_ctx_loss_mask(
        start_tks={-1}, end_tks={-2, -3, -4}, first_span_only=True
    )
    # two mid spans
    target = torch.Tensor([[-2, -3, -1, 4, -2, 5, 6, -1, 7, 8]]).long()
    expect = torch.Tensor([[0, 0, 0, 1, 1, 0, 0, 0, 0, 0]]).long()
    mask = loss_mask_f(target, target)
    assert (mask == expect).all()


def test_fim_loss_mask():
    loss_mask_f = get_loss_mask_hook(
        "fim-context",
        {"pad": 0, "eod": -1, "fim_prefix": -2, "fim_suffix": -3, "fim_middle": -4},
    )
    # check that pad tokens shouldn't have loss
    target = torch.Tensor([[-2, 1, 2, -4, 3, 4, 0, 0, 0]]).long()
    expect = torch.Tensor([[-0, 0, 0, -0, 1, 1, 0, 0, 0]]).long()
    mask = loss_mask_f(target, target)
    assert (mask == expect).all()


def test_hook_not_registered():
    """Validate that using unregistered hooks fails."""
    from megatron.utils import reset_attn_mask_hook, reset_loss_mask_hook

    reset_attn_mask_hook()
    reset_loss_mask_hook()

    # raise ValueError because no attention mask hook is registered
    with pytest.raises(ValueError):
        _test_get_ltor_masks_and_position_ids(
            data=torch.tensor([[1]]),
            eod_token=50000,
            attn_mask_mode="hook",
            loss_mask_mode="hook",
        )

    # raise ValueError because no loss mask hook is registered
    with pytest.raises(ValueError):
        _test_get_ltor_masks_and_position_ids(
            data=torch.tensor([[1]]), eod_token=50000, loss_mask_mode="hook"
        )

    # this should work
    _test_get_ltor_masks_and_position_ids(data=torch.tensor([[1]]), eod_token=50000)
