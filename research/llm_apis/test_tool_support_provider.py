"""Tests for the ToolSupportProvider class."""

import json
import pytest
from typing import Dict, List, Any

from research.llm_apis.llm_client import (
    ToolSupportProvider,
    ToolParam,
    TextResult,
    ToolCall,
    AssistantContentBlock,
)


class TestToolSupportProvider:
    """Tests for the ToolSupportProvider class."""

    def test_create_tool_system_prompt(self):
        """Test creating a system prompt with tool details."""
        # Define test tools
        tools = [
            ToolParam(
                name="get_weather",
                description="Get the weather for a location",
                input_schema={
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The location to get weather for",
                        }
                    },
                    "required": ["location"],
                },
            ),
            ToolParam(
                name="get_time",
                description="Get the current time for a location",
                input_schema={
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The location to get time for",
                        }
                    },
                    "required": ["location"],
                },
            ),
        ]

        # Test with no base system prompt
        prompt = ToolSupportProvider.create_tool_system_prompt(tools=tools)
        assert "Function Name: 'get_weather'" in prompt
        assert "Function Name: 'get_time'" in prompt
        assert "Instructions for Using Functions:" in prompt
        assert "<function=function_name>" in prompt

        # Test with a base system prompt
        base_prompt = "You are a helpful assistant."
        prompt_with_base = ToolSupportProvider.create_tool_system_prompt(
            tools=tools, base_system_prompt=base_prompt
        )
        assert base_prompt in prompt_with_base
        assert "Function Name: 'get_weather'" in prompt_with_base
        assert "Function Name: 'get_time'" in prompt_with_base

    def test_extract_tool_calls_from_text(self):
        """Test extracting tool calls from text using convert_to_assistant_content_blocks."""
        # Test with a single tool call
        text = 'I need to check the weather. <function=get_weather>{"location": "San Francisco"}</function>'
        blocks, _ = ToolSupportProvider.convert_to_assistant_content_blocks(text)

        # Find ToolCall blocks
        tool_calls = [block for block in blocks if isinstance(block, ToolCall)]
        assert len(tool_calls) == 1
        assert tool_calls[0].tool_name == "get_weather"
        assert tool_calls[0].tool_input == {"location": "San Francisco"}
        assert tool_calls[0].tool_call_id is not None

        # Test with multiple tool calls
        text = (
            "I need to check the weather in multiple places. "
            '<function=get_weather>{"location": "San Francisco"}</function> '
            'and also <function=get_weather>{"location": "New York"}</function>'
        )
        blocks, _ = ToolSupportProvider.convert_to_assistant_content_blocks(text)

        # Find ToolCall blocks
        tool_calls = [block for block in blocks if isinstance(block, ToolCall)]
        assert len(tool_calls) == 2
        assert tool_calls[0].tool_name == "get_weather"
        assert tool_calls[0].tool_input == {"location": "San Francisco"}
        assert tool_calls[1].tool_name == "get_weather"
        assert tool_calls[1].tool_input == {"location": "New York"}

        # Test with no tool calls
        text = "The weather is sunny today."
        blocks, _ = ToolSupportProvider.convert_to_assistant_content_blocks(text)

        # Find ToolCall blocks
        tool_calls = [block for block in blocks if isinstance(block, ToolCall)]
        assert len(tool_calls) == 0

        # Test with invalid JSON in tool call
        text = '<function=get_weather>{"location": "San Francisco"</function>'
        blocks, _ = ToolSupportProvider.convert_to_assistant_content_blocks(text)

        # With invalid JSON, we should get a TextResult instead of a ToolCall
        tool_calls = [block for block in blocks if isinstance(block, ToolCall)]
        assert len(tool_calls) == 0

    def test_convert_to_assistant_content_blocks_no_tools(self):
        """Test converting text with no tool calls to assistant content blocks."""
        text = "The weather is sunny today."
        blocks, metadata = ToolSupportProvider.convert_to_assistant_content_blocks(text)

        assert len(blocks) == 1
        assert isinstance(blocks[0], TextResult)
        assert blocks[0].text == text
        assert metadata["tool_support_provider"] == "used"
        assert metadata["original_text"] == text

    def test_convert_to_assistant_content_blocks_single_tool(self):
        """Test converting text with a single tool call to assistant content blocks."""
        text = '<function=get_weather>{"location": "San Francisco"}</function>'
        blocks, metadata = ToolSupportProvider.convert_to_assistant_content_blocks(text)

        assert len(blocks) == 1
        assert isinstance(blocks[0], ToolCall)
        assert blocks[0].tool_name == "get_weather"
        assert blocks[0].tool_input == {"location": "San Francisco"}
        assert metadata["segments_count"] == 1

    def test_convert_to_assistant_content_blocks_text_and_tool(self):
        """Test converting text with text and a tool call to assistant content blocks."""
        text = 'I need to check the weather. <function=get_weather>{"location": "San Francisco"}</function>'
        blocks, metadata = ToolSupportProvider.convert_to_assistant_content_blocks(text)

        assert len(blocks) == 2
        assert isinstance(blocks[0], TextResult)
        assert blocks[0].text == "I need to check the weather."
        assert isinstance(blocks[1], ToolCall)
        assert blocks[1].tool_name == "get_weather"
        assert blocks[1].tool_input == {"location": "San Francisco"}
        assert metadata["segments_count"] == 2

    def test_convert_to_assistant_content_blocks_tool_and_text(self):
        """Test converting text with a tool call followed by text."""
        text = '<function=get_weather>{"location": "San Francisco"}</function> The weather is usually mild there.'
        blocks, metadata = ToolSupportProvider.convert_to_assistant_content_blocks(text)

        assert len(blocks) == 2
        assert isinstance(blocks[0], ToolCall)
        assert blocks[0].tool_name == "get_weather"
        assert blocks[0].tool_input == {"location": "San Francisco"}
        assert isinstance(blocks[1], TextResult)
        assert blocks[1].text == "The weather is usually mild there."
        assert metadata["segments_count"] == 2

    def test_convert_to_assistant_content_blocks_multiple_tools(self):
        """Test converting text with multiple tool calls to assistant content blocks."""
        text = (
            '<function=get_weather>{"location": "San Francisco"}</function> '
            '<function=get_time>{"location": "New York"}</function>'
        )
        blocks, metadata = ToolSupportProvider.convert_to_assistant_content_blocks(text)

        # The implementation strips whitespace, so we only get the 2 tool calls
        assert len(blocks) == 2  # 2 tool calls
        assert isinstance(blocks[0], ToolCall)
        assert blocks[0].tool_name == "get_weather"
        assert blocks[0].tool_input == {"location": "San Francisco"}
        assert isinstance(blocks[1], ToolCall)
        assert blocks[1].tool_name == "get_time"
        assert blocks[1].tool_input == {"location": "New York"}
        assert metadata["segments_count"] == 2

    def test_convert_to_assistant_content_blocks_complex_mixed_content(self):
        """Test converting complex mixed content with multiple tool calls and text."""
        text = (
            "I need to check the weather in multiple places.\n\n"
            '<function=get_weather>{"location": "San Francisco"}</function>\n\n'
            "San Francisco is usually mild.\n\n"
            '<function=get_weather>{"location": "New York"}</function>\n\n'
            "New York has more variable weather.\n\n"
            '<function=get_time>{"location": "Tokyo"}</function>\n\n'
            "Tokyo is in a different time zone."
        )
        blocks, metadata = ToolSupportProvider.convert_to_assistant_content_blocks(text)

        assert len(blocks) == 7  # 3 tool calls + 4 text segments

        assert isinstance(blocks[0], TextResult)
        assert "I need to check the weather in multiple places." in blocks[0].text

        assert isinstance(blocks[1], ToolCall)
        assert blocks[1].tool_name == "get_weather"
        assert blocks[1].tool_input == {"location": "San Francisco"}

        assert isinstance(blocks[2], TextResult)
        assert "San Francisco is usually mild." in blocks[2].text

        assert isinstance(blocks[3], ToolCall)
        assert blocks[3].tool_name == "get_weather"
        assert blocks[3].tool_input == {"location": "New York"}

        assert isinstance(blocks[4], TextResult)
        assert "New York has more variable weather." in blocks[4].text

        assert isinstance(blocks[5], ToolCall)
        assert blocks[5].tool_name == "get_time"
        assert blocks[5].tool_input == {"location": "Tokyo"}

        assert isinstance(blocks[6], TextResult)
        assert "Tokyo is in a different time zone." in blocks[6].text

        assert metadata["segments_count"] == 7

    def test_convert_to_assistant_content_blocks_invalid_json(self):
        """Test handling invalid JSON in tool calls."""
        text = (
            "I need to check the weather. "
            '<function=get_weather>{"location": "San Francisco"</function> '
            'and also <function=get_time>{"location": "New York"}</function>'
        )
        blocks, metadata = ToolSupportProvider.convert_to_assistant_content_blocks(text)

        # The implementation treats the entire text as a single block when there's invalid JSON
        assert len(blocks) == 2
        assert isinstance(blocks[0], TextResult)
        assert "I need to check the weather." in blocks[0].text

        # The second block contains both the invalid function call and the valid one
        # This is because our regex pattern can't properly match the invalid JSON
        assert isinstance(blocks[1], TextResult)
        assert "<function=get_weather>" in blocks[1].text
        assert "<function=get_time>" in blocks[1].text
