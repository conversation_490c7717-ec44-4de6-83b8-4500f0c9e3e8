"""Contains clients for local models using production abstractions."""

import json
from typing import Any, Optional, Sequence

import jsonschema

from base.prompt_format_chat import get_struct_to_tokens_prompt_formatter_by_name
from base.prompt_format_chat.prompt_formatter import (
    StructToTokensPromptFormatter,
    StructuredChatPromptOutput,
)
from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelResponse,
    ToolChoice,
    ToolDefinition,
    ToolUseResponse,
)
from research.models import GenerationOptions, GenerativeLanguageModel, get_model


class LocalModelClient:
    """A client for local models that converts non-streamed generation to streamed generation."""

    def __init__(
        self,
        model: GenerativeLanguageModel,
        tokenize_formatter: StructToTokensPromptFormatter,
        stop_tokens: list[int],
    ):
        """Initialize the local model client."""
        self.model = model
        self.tokenize_formatter = tokenize_formatter
        self.stop_tokens = stop_tokens

    def generate_response(
        self,
        prompt_output: StructuredChatPromptOutput,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        tool_choice: ToolChoice | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        model_caller: str | None = None,
        request_context: Optional[Any] = None,
    ) -> list[ThirdPartyModelResponse]:
        """Generate a response from a local model."""

        if tool_choice is not None:
            raise NotImplementedError("Tool choice is not supported yet.")

        if prefill is not None:
            raise NotImplementedError("Prefill is not supported yet.")

        if use_caching:
            raise NotImplementedError("Caching is not supported yet.")

        if temperature is not None and temperature != 0.0:
            raise NotImplementedError("Temperature is not supported yet.")

        prompt_tokens = self.tokenize_formatter.format_prompt(prompt_output).tokens

        prompt_tokens = self.handle_prefill(
            prompt_tokens=prompt_tokens, tool_choice=tool_choice, prefill=prefill
        )

        raw_generate_output = self.model.raw_generate_tokens(
            prompt_tokens=prompt_tokens,
            options=GenerationOptions(
                temperature=temperature or 0.0,
                max_generated_tokens=max_output_tokens or 2048,
                stop_tokens=self.stop_tokens,
            ),
        )
        raw_tokens = raw_generate_output.tokens

        return self.parse_response(raw_tokens, prompt_output=prompt_output)

    def parse_response(
        self, response_tokens: list[int], prompt_output: StructuredChatPromptOutput
    ) -> list[ThirdPartyModelResponse]:
        """Parse a response from a local model."""

        # Remove the stop token
        actual_response_tokens = response_tokens[:-1]
        detokenized_response = self.model.tokenizer.detokenize(actual_response_tokens)

        return [ThirdPartyModelResponse(text=detokenized_response)]

    def handle_prefill(
        self,
        prompt_tokens: list[int],
        tool_choice: ToolChoice | None,
        prefill: str | None,
    ) -> list[int]:
        """Handle prefill."""
        if tool_choice is not None:
            raise NotImplementedError("Tool choice is not supported yet.")

        if prefill is not None:
            raise NotImplementedError("Prefill is not supported yet.")
        return prompt_tokens


class ToolParsingError(Exception):
    """Error raised when parsing tool calls from a response."""

    pass


class QwenLocalClient(LocalModelClient):
    """A client for Qwen local models."""

    def __init__(
        self,
        model: GenerativeLanguageModel,
        tokenize_formatter: StructToTokensPromptFormatter,
        stop_tokens: list[int],
        tool_start_token: int | None = None,
        tool_end_token: int | None = None,
    ):
        """Initialize the local model client."""
        super().__init__(model, tokenize_formatter, stop_tokens)
        self.tool_start_token = tool_start_token
        self.tool_end_token = tool_end_token

    def validate_tool_call(
        self,
        tool_name: str,
        tool_input: dict,
        tool_definitions: Sequence[ToolDefinition] | None = None,
    ) -> bool:
        """Validate a tool call against the tool definitions.

        Args:
            tool_name: The name of the tool to validate.
            tool_input: The input to the tool to validate.

        Returns:
            True if the tool is valid, False otherwise.

        Raises:
            ToolParsingError: If the tool is not found in the tool definitions or if the input doesn't match the schema.
        """
        if tool_definitions is None:
            tool_definitions = []

        # Find the tool definition
        tool_def = None
        for definition in tool_definitions:
            if definition.name == tool_name:
                tool_def = definition
                break

        if not tool_def:
            raise ToolParsingError(f"Tool '{tool_name}' not found in tool definitions")

        # Validate the tool input against the schema
        if tool_def.input_schema_json:
            try:
                # Parse the schema JSON
                schema = json.loads(tool_def.input_schema_json)

                # Validate the input against the schema
                jsonschema.validate(instance=tool_input, schema=schema)
            except json.JSONDecodeError as e:
                raise ToolParsingError(
                    f"Invalid schema JSON for tool '{tool_name}': {e}"
                )
            except jsonschema.ValidationError as e:
                raise ToolParsingError(
                    f"Tool input validation failed for '{tool_name}': {e}"
                )

        return True

    def parse_response(
        self, response_tokens: list[int], prompt_output: StructuredChatPromptOutput
    ) -> list[ThirdPartyModelResponse]:
        """Parse a response from a local model.

        If tool_start_token and tool_end_token are provided, this will parse the response
        into text and tool sections. Tool sections are validated against the tool definitions.

        Returns:
            A list of ThirdPartyModelResponse objects.

        Raises:
            ToolParsingError: If there is an error parsing the tool calls.
        """

        # Remove the stop token
        actual_response_tokens = response_tokens[:-1]

        # If no tool tokens are defined, just return the full response as text
        if self.tool_start_token is None:
            detokenized_response = self.model.tokenizer.detokenize(
                actual_response_tokens
            )
            return [ThirdPartyModelResponse(text=detokenized_response)]

        # Find all tool sections in the response
        responses = []
        tool_start_indices = []
        tool_end_indices = []

        # Find all tool start and end tokens
        for i, token in enumerate(actual_response_tokens):
            if token == self.tool_start_token:
                tool_start_indices.append(i)
            elif token == self.tool_end_token:
                tool_end_indices.append(i)

        # Validate that we have matching start and end tokens
        if len(tool_start_indices) != len(tool_end_indices):
            raise ToolParsingError("Mismatched tool start and end tokens")

        # If no tool sections found, return the full response as text
        if not tool_start_indices:
            detokenized_response = self.model.tokenizer.detokenize(
                actual_response_tokens
            )
            return [ThirdPartyModelResponse(text=detokenized_response)]

        # Sort the indices to ensure they're in order
        tool_start_indices.sort()
        tool_end_indices.sort()

        # Validate that each start comes before its corresponding end
        for i in range(len(tool_start_indices)):
            if tool_start_indices[i] >= tool_end_indices[i]:
                raise ToolParsingError(
                    "Tool start token must come before tool end token"
                )
            if i > 0 and tool_start_indices[i] < tool_end_indices[i - 1]:
                raise ToolParsingError("Overlapping tool sections")

        # Process the response in chunks
        current_pos = 0
        for i in range(len(tool_start_indices)):
            start_idx = tool_start_indices[i]
            end_idx = tool_end_indices[i]

            # Add text before the tool section
            if current_pos < start_idx:
                text_tokens = actual_response_tokens[current_pos:start_idx]
                text = self.model.tokenizer.detokenize(text_tokens)
                if text.strip():
                    responses.append(ThirdPartyModelResponse(text=text))

            # Process the tool section
            tool_tokens = actual_response_tokens[start_idx + 1 : end_idx]
            tool_text = self.model.tokenizer.detokenize(tool_tokens)

            try:
                # Parse the tool text as JSON
                tool_data = json.loads(tool_text)

                # Extract tool name and input
                if not isinstance(tool_data, dict):
                    raise ToolParsingError(
                        f"Tool data must be a JSON object, got {type(tool_data)}"
                    )

                if "name" not in tool_data:
                    raise ToolParsingError("Tool data must contain a 'name' field")

                if "arguments" not in tool_data:
                    raise ToolParsingError(
                        "Tool data must contain an 'arguments' field"
                    )

                tool_name = tool_data["name"]
                tool_input = tool_data["arguments"]

                # Validate the tool call
                self.validate_tool_call(
                    tool_name,
                    tool_input,
                    tool_definitions=prompt_output.tool_definitions,
                )

                # Create a tool use response
                responses.append(
                    ThirdPartyModelResponse(
                        text="",
                        tool_use=ToolUseResponse(
                            tool_name=tool_name,
                            input=tool_input,
                            tool_use_id="",  # We don't have an ID in this context
                        ),
                    )
                )
            except json.JSONDecodeError as e:
                raise ToolParsingError(f"Failed to parse tool JSON: {e}")

            # Update current position
            current_pos = end_idx + 1

        # Add any remaining text after the last tool section
        if current_pos < len(actual_response_tokens):
            text_tokens = actual_response_tokens[current_pos:]
            text = self.model.tokenizer.detokenize(text_tokens)
            if text.strip():
                responses.append(ThirdPartyModelResponse(text=text))

        return responses

    def handle_prefill(
        self,
        prompt_tokens: list[int],
        tool_choice: ToolChoice | None,
        prefill: str | None,
    ) -> list[int]:
        """Handle prefill."""
        if tool_choice is not None:
            raise NotImplementedError("Tool choice is not supported yet.")

        if prefill is not None:
            raise NotImplementedError("Prefill is not supported yet.")
        return prompt_tokens


def get_client(
    client_name: str,
    model_config: dict[str, Any],
    tokens_prompt_formatter_name: str,
    stop_tokens: list[int],
    **kwargs,
) -> LocalModelClient:
    """Get a client for a given client name."""

    model_name = model_config.pop("name")
    model = get_model(model_name=model_name, **model_config)
    model.load()
    tokenizer = model.tokenizer
    tokens_prompt_formatter = get_struct_to_tokens_prompt_formatter_by_name(
        tokens_prompt_formatter_name, tokenizer
    )

    if client_name == "qwen":
        # Create a QwenLocalClient instance
        local_client = QwenLocalClient(
            model=model,
            tokenize_formatter=tokens_prompt_formatter,
            stop_tokens=stop_tokens,
            **kwargs,
        )
    else:
        raise ValueError(f"Unknown client name: {client_name}")

    return local_client
