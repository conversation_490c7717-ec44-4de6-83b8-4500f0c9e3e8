#!/usr/bin/env python3
"""Data on all available providers."""

# pylint: disable=no-member

from __future__ import annotations

import argparse
import base64
import typing
from pathlib import Path
from typing import Any, Literal, Optional, Sequence, Tuple

import requests
import yaml
from kubernetes import client, config
from requests.adapters import HTTPAdapter, Retry

import research.infra.cfg.clusters

try:
    config.load_kube_config()
except:  # noqa: E722 pylint: disable=bare-except
    config.load_config()

try:
    c = client.Configuration().get_default_copy()
except AttributeError:
    c = client.Configuration()
client.Configuration.set_default(c)


class Provider:
    """Base provider class."""

    _checkpoint_bucket: str
    _context: str
    _determined_url: str
    _namespace: str
    _provider: str
    _registry: str
    _shared_mount: str

    _region_map = {}

    _bash_attr_map = {
        "provider": "AUGMENT_DEV_CLOUD_PROVIDER",
        "registry": "AUGMENT_DEV_CONTAINER_REPO",
        "gpu_tag": "AUGMENT_DEV_GPU_TAG",
        "cpu_tag": "AUGMENT_DEV_CPU_TAG",
        "devpod_cpu_tag": "AUGMENT_DEVPOD_CPU_TAG",
        "devpod_gpu_tag": "AUGMENT_DEVPOD_GPU_TAG",
        "determined_url": "AUGMENT_DEV_DETERMINED_URL",
        "shared_mount": "AUGMENT_DEV_SHARED_MOUNT_PATH",
    }

    def _set_provider(self, provider_region: str) -> None:
        """We try to be as accomodating as possible on the input."""
        assert (
            provider_region in self._region_map.keys()
        ), f"Unknown provider region {provider_region}"
        self._provider = provider_region

    def __getattr__(self, __name: str) -> Any:
        if __name in self._region_map[self._provider]:
            return self._region_map[self._provider][__name]
        return super().__getattribute__(__name)

    def __init__(self, provider_region: str) -> None:
        self._set_provider(provider_region)

    def bash_env(self, export: bool = True) -> None:
        """Print the environment for bash consumption."""
        exp_str = ""
        if export:
            exp_str = "export "
        for attr, var in self._bash_attr_map.items():
            print(f"{exp_str}{var}={getattr(self, attr)}")

    def dump_provider(self):
        # special-case 'provider' so it prints first
        print(f"provider: {self.provider}")
        for k in dir(self):
            if k == "provider":
                continue
            if isinstance(getattr(type(self), k, None), property):
                v = getattr(self, k)
                print(f"{k}: {v}")

    def is_read_only_volume(self, volume: dict[str, Any]) -> bool:
        return "tags" in volume and "read-only" in volume["tags"]

    def get_volumes_and_mounts(
        self,
        include_tags: list[str] | None = None,
        additional_volumes: list[dict[str, Any]] | None = None,
    ) -> tuple:
        mount_config = yaml.safe_load(
            (Path(__file__).absolute().parent / "node_mounts.yaml").open(
                encoding="utf-8"
            )
        )
        vam = mount_config[self.provider]
        if additional_volumes:
            vam += additional_volumes
        # If the volume is untagged, always include it, otherwise all tags must be included
        if include_tags:
            vam = [
                v
                for v in vam
                if "tags" not in v or set(v["tags"]).issuperset(set(include_tags))
            ]
        mounts = [
            {
                "mountPath": volume["path"],
                "name": volume["name"],
                "readOnly": self.is_read_only_volume(volume),
            }
            for volume in vam
            if "path" in volume
        ]
        mounts += [
            {
                "mountPath": f"/run/determined/secrets/{volume['name']}",
                "name": volume["name"],
                "readOnly": True,
            }
            for volume in vam
            if "secret" in volume
        ]
        volumes = [
            {
                "name": volume["name"],
                "persistentVolumeClaim": {"claimName": volume["name"]},
            }
            for volume in vam
            if "path" in volume and "bucketName" not in volume
        ]
        volumes += [
            {
                "name": volume["name"],
                "csi": {
                    "driver": "gcsfuse.csi.storage.gke.io",
                    "readOnly": self.is_read_only_volume(volume),
                    "volumeAttributes": {
                        "bucketName": volume["bucketName"],
                        "mountOptions": "implicit-dirs,uid=1000,gid=1000,file-mode=0666,dir-mode=777,o=noexec,o=noatime",
                        "fileCacheCapacity": "740Gi",
                        "fileCacheForRangeRead": "false",
                    },
                },
            }
            for volume in vam
            if "path" in volume and "bucketName" in volume
        ]
        volumes += [
            {
                "name": volume["name"],
                "secret": {"secretName": volume["secret"]},
            }
            for volume in vam
            if "secret" in volume
        ]
        volumes += [
            {
                "name": "dshm",
                "emptyDir": {"medium": "Memory"},
            },
        ]

        return volumes, mounts

    def kubectl(self):
        """Return the base kubectl invocation array."""
        return ["kubectl", "--context", self.context, "--namespace", self.namespace]

    def k8s_client(self):
        """Return a K8s client for this provider."""
        try:
            return config.new_client_from_config(context=self.context)
        except config.ConfigException:
            config.load_incluster_config()
            return client.ApiClient()

    def k8s_corev1(self):
        return client.CoreV1Api(self.k8s_client())

    @property
    def provider(self) -> str:
        return self._provider

    @property
    def registry(self) -> str:
        return self._registry

    @property
    def registry_secret_key(self) -> str:
        return self.registry.split("/")[0]

    @property
    def gpu_tag(self) -> str:
        tag = (Path(__file__).parent / "gpu_tag.txt").read_text().strip()
        return f"{self._registry}/{tag}"

    @property
    def cpu_tag(self) -> str:
        tag = (Path(__file__).parent / "cpu_tag.txt").read_text().strip()
        return f"{self._registry}/{tag}"

    @property
    def devpod_cpu_tag(self) -> str:
        tag = (Path(__file__).parent / "devpod_cpu_tag.txt").read_text().strip()
        return f"{self._registry}/{tag}"

    @property
    def devpod_gpu_tag(self) -> str:
        tag = (Path(__file__).parent / "devpod_gpu_tag.txt").read_text().strip()
        return f"{self._registry}/{tag}"

    @property
    def spark_cpu_tag(self) -> str:
        tag = (Path(__file__).parent / "spark_cpu_tag.txt").read_text().strip()
        return f"{self._registry}/{tag}"

    @property
    def spark_gpu_tag(self) -> str:
        tag = (Path(__file__).parent / "spark_gpu_tag.txt").read_text().strip()
        return f"{self._registry}/{tag}"

    @property
    def context(self) -> str:
        return self._context

    @property
    def namespace(self) -> str:
        return self._namespace

    @property
    def checkpoint_bucket(self) -> str:
        return self._checkpoint_bucket

    @property
    def determined_url(self) -> str:
        return self._determined_url

    @property
    def shared_mount(self) -> str:
        return self._shared_mount

    def get_docker_username_and_password(self) -> Tuple[str, str]:
        """Retrieves username and password needed for the local docker registry.

        Will look for a file path (common in Determined jobs). If that fails, will
        attempt to use k8s to retreive docker registry name & password.

        Returns:
        Tuple[str, str]: username, password
        """
        # Most likely running via Determined
        secret_path = Path("/run/determined/secrets/registry-readers/.dockerconfigjson")
        if secret_path.exists():
            data = secret_path.read_text()
        else:
            secret = self.k8s_corev1().read_namespaced_secret(
                name="registry-readers", namespace=self.namespace
            )
            data = base64.b64decode(secret.data[".dockerconfigjson"]).decode("utf-8")  # type: ignore
        data = yaml.safe_load(data)["auths"]
        username, password = (
            base64.b64decode(data[self.registry_secret_key]["auth"])
            .decode("utf-8")
            .split(":")
        )

        return username, password

    def get_images_and_tags(
        self, prefix: Optional[str] = None, include_registry: bool = True
    ) -> list[Tuple[str, list[str]]]:
        """Retrieve images and associated tags from the local Docker registry.

        Arguments:
        prefix (Optional[str]): Prefix to filter images by name (left match).
                                Defaults to None.

        Returns:
        List[Tuple[image, List[tags]]]: List of image names and their associated tags.
        """
        username, password = self.get_docker_username_and_password()
        auth = (username, password)

        try:
            session = requests.Session()

            retries = Retry(
                total=5,
                backoff_factor=0.1,  # type: ignore pylance is wrong about this
                status_forcelist=[500, 502, 503, 504],
            )

            session.mount("https://", HTTPAdapter(max_retries=retries))

            # hack for hydra image location
            # pull the registry name and prefix apart
            parts = self.registry.replace("/infra", "").split("/", 1)
            reg, rprefix = parts if len(parts) == 2 else (parts[0], "")
            if rprefix:
                rprefix = f"{rprefix}/"
            r_url = f"https://{reg}/v2/_catalog"
            print(f"Getting images from {r_url}")
            response = session.get(r_url, auth=auth, timeout=60)
            response.raise_for_status()
            repositories = response.json()["repositories"]
            images_with_tags = []

            for image_name in repositories:
                if not prefix or image_name.startswith(f"{rprefix}{prefix}"):
                    print(f"Getting tags for image {image_name}")
                    tags_url = f"https://{reg}/v2/{image_name}/tags/list"
                    tags_response = session.get(tags_url, auth=auth, timeout=30)
                    tags_response.raise_for_status()
                    tags_data = tags_response.json()
                    tags = tags_data.get("tags", [])
                    images_with_tags.append(
                        (f"{reg}/{image_name}", tags)
                        if include_registry
                        else (image_name, tags)
                    )

            return images_with_tags

        except requests.exceptions.RequestException as exc:
            print(f"Error occurred accessing docker registry: {exc}")
            raise exc

    def get_eng_secret(self, secret_name: str, namespace="eng-secrets") -> str:
        """Get a secret from the eng-secrets namespace."""
        secret = self.k8s_corev1().read_namespaced_secret(
            name=secret_name, namespace=namespace
        )
        if "secret" in secret.data:  # type:ignore
            return base64.b64decode(secret.data["secret"]).decode("utf-8")  # type: ignore
        elif "token" in secret.data:  # type:ignore
            return base64.b64decode(secret.data["token"]).decode("utf-8")  # type: ignore
        else:
            return ""


class CW_Provider(Provider):
    """Coreweave provider."""

    _region_map = {
        "CW-EAST4": {
            "_registry": "registry.cw-east4.r.augmentcode.com/infra",
            "_context": "cw-east4",
            "_namespace": "cw-east4",
            "_checkpoint_bucket": "s3://dev-training-dai",
            "_shared_mount": "/mnt/efs/augment",
            "_determined_url": "https://determined.cw-east4.r.augmentcode.com",
        },
    }


class GCP_Provider(Provider):
    """GCP provider."""

    _region_map = {
        "GCP-US1": {
            "_registry": "us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1",
            "_context": "gcp-us1",
            "_namespace": "gcp-us1",
            "_checkpoint_bucket": "gs://determined-checkpoint-storage",
            "_determined_url": "https://determined.gcp-us1.r.augmentcode.com",
            "_shared_mount": "/mnt/efs/augment",
            "_project_id": "augment-research-gsc",
            "_location": "us-central1",
        },
    }


def get_provider(name: str | None = None) -> Provider:
    if name is None:
        return get_provider(detect_provider())
    if name.startswith("CW"):
        return CW_Provider(name)
    elif name.startswith("GCP"):
        return GCP_Provider(name)
    else:
        raise AssertionError(f"Unknown provider {name}")


ClusterName = Literal["GCP-US1", "CW-EAST4"]


def list_providers() -> Sequence[ClusterName]:
    """list all available providers."""
    return typing.get_args(ClusterName)


def detect_provider() -> str:
    """Detect the current environment. (Wrapper around clusters.py library)."""
    name = research.infra.cfg.clusters.Clusters.detect_cluster()
    return name.upper()


# If executed from the command line, assume someone wants the bash environment.
if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--no-export", default=False, action="store_true")
    parser.add_argument("--list", default=False, action="store_true")
    parser.add_argument("--all", default=False, action="store_true")
    parser.add_argument(
        "--all-registries",
        default=False,
        action="store_true",
        help="List all registries",
    )
    parser.add_argument("provider", nargs="*")
    args = parser.parse_args()
    if args.list:
        print("\n".join(list_providers()))
    elif args.all_registries:
        print(
            "\n".join(
                set(
                    [
                        get_provider(provider_name).registry
                        for provider_name in list_providers()
                    ]
                )
            )
        )
    elif args.all:
        for provider_name in list_providers():
            p = get_provider(provider_name)
            p.dump_provider()
            print("")
    else:
        if args.provider:
            provider = args.provider[0]
        else:
            provider = detect_provider()
        get_provider(provider).bash_env(not args.no_export)


def get_eng_secret(secret_name: str, namespace="eng-secrets") -> str:
    """Get a secret from the eng-secrets namespace."""
    return get_provider().get_eng_secret(secret_name, namespace=namespace)
