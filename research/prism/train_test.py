"""Unit tests for train.py.

pytest research/prism/train_test.py -rF
"""

from datetime import datetime

from research.prism.train import fliter_data
from base.datasets.completion import (
    CompletionDatum,
    CompletionRequest,
    CompletionResponse,
    CompletionResolution,
)


def create_completion_datum(
    request_id: str, response_time_seconds, resolution_time_seconds
):
    completion_datum = CompletionDatum(
        request_id=request_id,
        user_id="test-user-id",
        user_agent="test-agent",
        request=CompletionRequest(
            prefix="test-prefix",
            suffix="test-suffix",
            path="test-path",
            blob_names=["test-blob-name"],
            output_len=10,
            timestamp=datetime(2024, 1, 1, 0, 0, 1),
        ),
        response=CompletionResponse(
            text="test-text",
            model="test-model",
            skipped_suffix="",
            suffix_replacement_text="",
            unknown_blob_names=[],
            timestamp=datetime(2024, 1, 1, 0, 0, response_time_seconds),
            retrieved_chunks=[],
            prompt_tokens=[],
            tokens=[],
            token_log_probs=[2, 4, 5],
        ),
        resolution=CompletionResolution(
            accepted=True,
            timestamp=datetime(2024, 1, 1, 0, 0, resolution_time_seconds),
        ),
        feedback=None,
    )
    return completion_datum


def test_filter_data_two():
    completion_datum = [
        create_completion_datum("test-request-id1", 1, 5),
        create_completion_datum("test-request-id2", 1, 1),
    ]
    data = fliter_data(completion_datum)
    assert len(data) == 1
