"""This tests the diff parsing logic in experimental/colin/data_curation/process_pool_scripts/generate_diff_pairs/diff_parser_and_chunker.py."""
# The following disabled rules don't play nicely with pylint fixtures.
# pylint: disable=redefined-outer-name
# pylint: disable=unused-argument

import itertools
import os
import random as rand
import sys
from difflib import ndiff
from pathlib import Path
from types import SimpleNamespace  # pylint: disable=no-name-in-module

import numpy as np
import pytest
from megatron.tokenizer.tokenizer import CodeGenTokenizer

# FIXME (@c-flaherty, @richhankins): Add modules in research to PYTHONPATH in CI by default.
research_path = str(Path(__file__).parent.parent.parent.parent.parent)
if research_path not in sys.path:
    sys.path.append(research_path)
from data.slurm.diffs_retriever_dataset_v1_2023_05_23.repos_to_diff_pairs import (  # noqa: E402
    _process_repo,
)
from data.slurm.diffs_retriever_dataset_v1_2023_05_23.utils.diff_parser_and_chunker import (  # noqa: E402
    _get_chunks,
    _get_line_idx_of_next_chunk_of_additions,
    get_commit_chunk_pairs,
)
from data.slurm.diffs_retriever_dataset_v1_2023_05_23.utils.types import (  # noqa: E402
    LineType,
    RepoMetadata,
)

tokenizer = CodeGenTokenizer()

TMP_DIR = f"/tmp/pytest_scratch_{os.getpid()}"
TMP_DIR_LOGS = f"{TMP_DIR}/logs"

# It has a couple of commits in it.
TEST_REPO = "/mnt/efs/aug-cw-lga1-nvme/pytest/test_repo.git"


@pytest.fixture
def random():
    """Makes testing deterministic."""
    rand.seed(0)
    np.random.seed(0)


@pytest.fixture(scope="session", autouse=True)
def cleanup(request):
    """Cleanup a testing directory once we are finished.

    See https://stackoverflow.com/questions/41871278/pytest-run-a-function-at-the-end-of-the-tests.
    """

    def remove_test_dir():
        os.system(f"rsync -a --delete /tmp/empty/ {TMP_DIR}/")
        os.system(f"rm -fr {TMP_DIR}")

    try:
        remove_test_dir()
    except Exception:  # pylint: disable=broad-exception-caught
        pass

    Path(TMP_DIR).mkdir()
    Path(TMP_DIR_LOGS).mkdir()

    request.addfinalizer(remove_test_dir)


@pytest.fixture
def old_file1():
    return [
        "This is line 0",
        "This is line 1",
        "This is line 2",
        "This is line 3",
        "This is line 4",
        "This is line 5",
        "This is line 6",
        "This is line 7",
        "This is line 8",
        "This is line 9",
        "This is line 10",
        "This is line 11",
        "This is line 12",
        "This is line 13",
        "This is line 14",
        "This is line 15",
        "This is line 16",
        "This is line 17",
        "This is line 18",
        "This is line 19",
    ]


@pytest.fixture
def modified_file1():
    return [
        "This is line 0",
        "This is line 1",
        "This is line 2",
        "This is changed line 3",
        "This is changed line 4",
        "This is changed line 5",
        "This is changed line 6",
        "This is line 7",
        "This is line 8",
        "This is line 9",
        "This is line 10",
        "This is line 11",
        "This is changed line 12",
        "This is line 13",
        "This is line 14",
        "This is line 15",
        "This is line 16",
        "This is line 17",
        "This is changed line 18",
        "This is changed line 19",
    ]


@pytest.fixture
def old_file2():
    return [
        "This is line 0",
        "This is line 1",
        "This is line 2",
        "This is line 3",
        "This is line 4",
        "This is line 5",
        "This is line 6",
    ]


@pytest.fixture
def modified_file2():
    return [
        "This is changed line 0",
        "This is changed line 1",
        "This is changed line 2",
        "This is line 3",
        "This is line 4",
        "This is line 5",
        "This is changed line 6",
    ]


@pytest.fixture
def old_file3():
    return [
        "This is line 0;",
        "This is line 1;",
        "This is line 2;",
        "This is line 3;",
        "This is line 4;",
        "This is line 5;",
        "This is line 6;",
    ]


@pytest.fixture
def modified_file3():
    return [
        "This is line 0;",
        "This is line 1;",
        "This is changed line 2;",
        "This is line 3;",
        "This is line 4;",
        "This is line 5;",
        "This is changed line 6;",
    ]


@pytest.fixture
def old_file4():
    return [
        "This is line 0",
        "This is line 1",
        "This is line 2",
        "This is line 3",
        "This is line 4",
        "This is line 5",
        "This is line 6",
    ]


@pytest.fixture
def modified_file4():
    return [
        "This is changed line 0",
        "This is changed line 1",
        "This is changed line 2",
        "This is line 3",
        "This is line 4",
        "This is line 5",
        "This is changed line 6",
        "This is changed line 7",
    ]


@pytest.fixture
def old_file5_longline():
    return [
        "This is line 0",
        "This is line 1. This is line 1. This is line 1. This is line 1. This is line 1. This is line 1. This is line 1. ",
        "This is line 2",
        "This is line 3",
    ]


@pytest.fixture
def modified_file5_longline():
    return [
        "This is line 0",
        "This is line 1. This is line 1. This is line 1. This is changed line 1. This is line 1. This is line 1. This is line 1. ",
        "This is changed line 2",
        "This is line 3",
    ]


###
# Testing function _get_line_idx_of_next_chunk_of_additions
###


def test_get_line_idx_of_next_chunk_of_additions_fn_basic_functionality(
    old_file1, modified_file1
):
    raw_difflines = [
        line
        for line in ndiff(old_file1, modified_file1)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)

    chunk_indices = [(3, 6), (5, 5), (5, 6)]
    max_chunk_length = 512  # basically never truncate on chunk length

    # Confirm grabbing correct indices
    while len(chunk_indices) > 0:
        start_idx, end_idx, _ = _get_line_idx_of_next_chunk_of_additions(
            difflines, max_chunk_length, max_chunk_length
        )
        assert start_idx == chunk_indices[0][0] and end_idx == chunk_indices[0][1], (
            start_idx,
            end_idx,
            chunk_indices[0],
        )
        chunk_indices = chunk_indices[1:]
        difflines = (
            difflines[(end_idx + 1) :] if end_idx + 1 != len(difflines) else None
        )


# Edge cases


def test_get_line_idx_of_next_chunk_of_additions_fn_does_not_modify_input(
    old_file1, modified_file1
):
    raw_difflines = [
        line
        for line in ndiff(old_file1, modified_file1)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)

    max_chunk_length = 512  # basically never truncate on chunk length

    # Confirm not writing input
    old_difflines = [*difflines]  # copy difflines
    _get_line_idx_of_next_chunk_of_additions(
        difflines, max_chunk_length, max_chunk_length
    )
    assert difflines == old_difflines


def test_get_line_idx_of_next_chunk_of_additions_fn_uses_max_chunk_length_arg_correctly(
    old_file1, modified_file1
):
    raw_difflines = [
        line
        for line in ndiff(old_file1, modified_file1)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)

    ###
    # Max chunk length set to exactly 2 modified lines
    ###
    chunk_indices = [(3, 4), (0, 1), (5, 5), (5, 6)]
    max_chunk_length = 10
    working_difflines = [*difflines]

    # Confirm grabbing correct indices
    while len(chunk_indices) > 0:
        start_idx, end_idx, _ = _get_line_idx_of_next_chunk_of_additions(
            working_difflines, max_chunk_length, max_chunk_length
        )
        assert start_idx == chunk_indices[0][0] and end_idx == chunk_indices[0][1], (
            start_idx,
            end_idx,
            chunk_indices[0],
        )
        chunk_indices = chunk_indices[1:]
        working_difflines = (
            working_difflines[(end_idx + 1) :]
            if end_idx + 1 != len(working_difflines)
            else None
        )

    ###
    # Max chunk length set to 2 modified lines + half a line (3 tokens whereas each line is 5 tokens)
    ###
    chunk_indices = [(3, 4), (0, 1), (5, 5), (5, 6)]
    max_chunk_length = 13
    working_difflines = [*difflines]

    # Confirm grabbing correct indices
    while len(chunk_indices) > 0:
        start_idx, end_idx, _ = _get_line_idx_of_next_chunk_of_additions(
            working_difflines, max_chunk_length, max_chunk_length
        )
        assert start_idx == chunk_indices[0][0] and end_idx == chunk_indices[0][1], (
            start_idx,
            end_idx,
            chunk_indices[0],
        )
        chunk_indices = chunk_indices[1:]
        working_difflines = (
            working_difflines[(end_idx + 1) :]
            if end_idx + 1 != len(working_difflines)
            else None
        )

    ###
    # Max chunk length set to exactly 3 modified lines
    ###
    chunk_indices = [(3, 5), (0, 0), (5, 5), (5, 6)]
    max_chunk_length = 15
    working_difflines = [*difflines]

    # Confirm grabbing correct indices
    while len(chunk_indices) > 0:
        print(
            _get_line_idx_of_next_chunk_of_additions(
                working_difflines, max_chunk_length, max_chunk_length
            )
        )
        start_idx, end_idx, _ = _get_line_idx_of_next_chunk_of_additions(
            working_difflines, max_chunk_length, max_chunk_length
        )
        assert start_idx == chunk_indices[0][0] and end_idx == chunk_indices[0][1], (
            start_idx,
            end_idx,
            chunk_indices[0],
        )
        chunk_indices = chunk_indices[1:]
        working_difflines = (
            working_difflines[(end_idx + 1) :]
            if end_idx + 1 != len(working_difflines)
            else None
        )


# Really long line edge case


def test_get_line_idx_of_next_chunk_of_additions_fn_handles_really_long_lines(
    old_file5_longline, modified_file5_longline
):
    raw_difflines = [
        line
        for line in ndiff(old_file5_longline, modified_file5_longline)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)

    ###
    # Max chunk length is too small for line 1 but okay for line 2
    max_chunk_length = 5
    working_difflines = [*difflines]

    # Confirm grabbing correct indices
    start_idx, end_idx, chunk_len = _get_line_idx_of_next_chunk_of_additions(
        working_difflines, max_chunk_length, max_chunk_length
    )
    assert start_idx == 2 and end_idx == 2 and chunk_len == 5, (
        start_idx,
        end_idx,
        chunk_len,
    )


###
# Testing function _get_chunks
###


def test_get_chunks_new_small_file(modified_file2, random):
    """Test for rare infinite loop.

    This tests if a file is properly skipped if the entirety of the content is smaller
    than the min length configuration and empty chunk list is properly returned
    Args:
        modified_file
    """
    # modified_file2 is the new added file.  every line is a new line so we are not even diffing
    added_lines = []
    for line in modified_file2:
        tokenized_line = tokenizer.tokenize(line)
        new_line = (LineType.ADDITION, tokenized_line, len(tokenized_line))
        added_lines.append(new_line)
    # Make sure to set the min length big so that it produce the proper test condition
    args = SimpleNamespace(
        **{
            "output_path": TMP_DIR,
            "min_query_length": 60,
            "max_query_length": 100,
            "min_key_length": 60,
            "max_key_length": 100,
        }
    )
    # this should return [] and []
    query_chunks, key_chunks = _get_chunks(list(added_lines), args)
    flattened_chunks = [
        chunk
        for chunk_pair in itertools.zip_longest(
            query_chunks, key_chunks, fillvalue=None
        )
        for chunk in chunk_pair
        if chunk is not None
    ]
    # The file is smaller than min length threshold, so it should return nothing; if not we should raise
    assert not flattened_chunks, f"Return is not empty: {flattened_chunks}"


def test_get_chunks_fn_basic_functionality(old_file1, modified_file1, random):
    raw_difflines = [
        line
        for line in ndiff(old_file1, modified_file1)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)

    # query is must be 4 changed lines and 2 context lines
    # key must be 1 changed line
    args = SimpleNamespace(
        **{
            "output_path": TMP_DIR,
            "min_query_length": 28,
            "max_query_length": 30,
            "min_key_length": 6,
            "max_key_length": 8,
        }
    )

    working_diff = [*difflines]
    query_chunks, key_chunks = _get_chunks(working_diff, args)
    flattened_chunks = [
        chunk
        for chunk_pair in itertools.zip_longest(
            query_chunks, key_chunks, fillvalue=None
        )
        for chunk in chunk_pair
        if chunk is not None
    ]

    # Since we fix seed, this assert is invariant to the chunk length sampling code in _get_chunks
    expected_flattened_chunks = [
        [
            (LineType.CONTEXT, [1212, 318, 1627, 362], 4),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 513], 5),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 604], 5),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 642], 5),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 718], 5),
            (LineType.CONTEXT, [1212, 318, 1627, 767], 4),
        ],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 1105], 5)],
        [
            (LineType.CONTEXT, [1212, 318, 1627, 1478], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 1315], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 1467], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 1596], 4),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 1248], 5),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 678], 5),
        ],
    ]

    for actual_chunk, expected_chunk in itertools.zip_longest(
        flattened_chunks, expected_flattened_chunks, fillvalue=None
    ):
        assert actual_chunk == expected_chunk, (actual_chunk, expected_chunk)


# Edge cases
def test_get_chunks_fn_chunk_sizes_at_file_size(old_file1, modified_file1, random):
    raw_difflines = [
        line
        for line in ndiff(old_file1, modified_file1)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)

    # query and key both at length of entire file (13*4 + 7*5 = 87 tokens)
    args = SimpleNamespace(
        **{
            "output_path": TMP_DIR,
            "min_query_length": 84,
            "max_query_length": 89,
            "min_key_length": 84,
            "max_key_length": 89,
        }
    )

    working_diff = [*difflines]
    query_chunks, key_chunks = _get_chunks(working_diff, args)
    flattened_chunks = [
        chunk
        for chunk_pair in itertools.zip_longest(
            query_chunks, key_chunks, fillvalue=None
        )
        for chunk in chunk_pair
        if chunk is not None
    ]

    # Since we fix seed, this assert is invariant to the chunk length sampling code in _get_chunks
    expected_flattened_chunks = [
        [
            (LineType.CONTEXT, [1212, 318, 1627, 657], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 352], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 362], 4),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 513], 5),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 604], 5),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 642], 5),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 718], 5),
            (LineType.CONTEXT, [1212, 318, 1627, 767], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 807], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 860], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 838], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 1367], 4),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 1105], 5),
            (LineType.CONTEXT, [1212, 318, 1627, 1511], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 1478], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 1315], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 1467], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 1596], 4),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 1248], 5),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 678], 5),
        ]
    ]

    for actual_chunk, expected_chunk in itertools.zip_longest(
        flattened_chunks, expected_flattened_chunks, fillvalue=None
    ):
        assert actual_chunk == expected_chunk, (actual_chunk, expected_chunk)


def test_get_chunks_fn_query_chunk_size_one_line_less_than_file_size(
    old_file1, modified_file1, random
):
    raw_difflines = [
        line
        for line in ndiff(old_file1, modified_file1)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)

    # query is all but last line: 13*4 + 7*5 - 5 = 82 tokens
    # key is last line
    args = SimpleNamespace(
        **{
            "output_path": TMP_DIR,
            "min_query_length": 80,
            "max_query_length": 84,
            "min_key_length": 3,
            "max_key_length": 15,
        }
    )

    working_diff = [*difflines]
    query_chunks, key_chunks = _get_chunks(working_diff, args)
    flattened_chunks = [
        chunk
        for chunk_pair in itertools.zip_longest(
            query_chunks, key_chunks, fillvalue=None
        )
        for chunk in chunk_pair
        if chunk is not None
    ]

    # Since we fix seed, this assert is invariant to the chunk length sampling code in _get_chunks
    expected_flattened_chunks = [
        [
            (LineType.CONTEXT, [1212, 318, 1627, 657], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 352], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 362], 4),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 513], 5),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 604], 5),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 642], 5),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 718], 5),
            (LineType.CONTEXT, [1212, 318, 1627, 767], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 807], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 860], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 838], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 1367], 4),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 1105], 5),
            (LineType.CONTEXT, [1212, 318, 1627, 1511], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 1478], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 1315], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 1467], 4),
            (LineType.CONTEXT, [1212, 318, 1627, 1596], 4),
            (LineType.ADDITION, [1212, 318, 3421, 1627, 1248], 5),
        ],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 678], 5)],
    ]

    for actual_chunk, expected_chunk in itertools.zip_longest(
        flattened_chunks, expected_flattened_chunks, fillvalue=None
    ):
        assert actual_chunk == expected_chunk, (actual_chunk, expected_chunk)


def test_get_chunks_fn_only_enough_lines_for_one_query_chunk(
    old_file1, modified_file1, random
):
    raw_difflines = [
        line
        for line in ndiff(old_file1, modified_file1)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)

    # query is single line at a time: 13*4 + 7*5 - 5 = 82 tokens
    # key is too large to grab any
    args = SimpleNamespace(
        **{
            "output_path": TMP_DIR,
            "min_query_length": 4,
            "max_query_length": 8,
            "min_key_length": 10000,
            "max_key_length": 10005,
        }
    )

    working_diff = [*difflines]
    query_chunks, key_chunks = _get_chunks(working_diff, args)

    assert len(query_chunks) == 7 and len(key_chunks) == 0, (
        len(query_chunks),
        len(key_chunks),
    )

    flattened_chunks = [
        chunk
        for chunk_pair in itertools.zip_longest(
            query_chunks, key_chunks, fillvalue=None
        )
        for chunk in chunk_pair
        if chunk is not None
    ]

    # Since we fix seed, this assert is invariant to the chunk length sampling code in _get_chunks
    expected_flattened_chunks = [
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 513], 5)],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 604], 5)],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 642], 5)],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 718], 5)],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 1105], 5)],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 1248], 5)],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 678], 5)],
    ]

    for actual_chunk, expected_chunk in itertools.zip_longest(
        flattened_chunks, expected_flattened_chunks, fillvalue=None
    ):
        assert actual_chunk == expected_chunk, (actual_chunk, expected_chunk)


def test_get_chunks_fn_only_enough_lines_for_one_key_chunk(
    old_file1, modified_file1, random
):
    raw_difflines = [
        line
        for line in ndiff(old_file1, modified_file1)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)

    # query is single line at a time: 13*4 + 7*5 - 5 = 82 tokens
    # key is too large to grab any
    args = SimpleNamespace(
        **{
            "output_path": TMP_DIR,
            "min_query_length": 10000,
            "max_query_length": 10005,
            "min_key_length": 4,
            "max_key_length": 8,
        }
    )

    working_diff = [*difflines]
    query_chunks, key_chunks = _get_chunks(working_diff, args)

    assert len(query_chunks) == 0 and len(key_chunks) == 7, (
        len(query_chunks),
        len(key_chunks),
    )

    flattened_chunks = [
        chunk
        for chunk_pair in itertools.zip_longest(
            query_chunks, key_chunks, fillvalue=None
        )
        for chunk in chunk_pair
        if chunk is not None
    ]

    # Since we fix seed, this assert is invariant to the chunk length sampling code in _get_chunks
    expected_flattened_chunks = [
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 513], 5)],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 604], 5)],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 642], 5)],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 718], 5)],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 1105], 5)],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 1248], 5)],
        [(LineType.ADDITION, [1212, 318, 3421, 1627, 678], 5)],
    ]

    for actual_chunk, expected_chunk in itertools.zip_longest(
        flattened_chunks, expected_flattened_chunks, fillvalue=None
    ):
        assert actual_chunk == expected_chunk, (actual_chunk, expected_chunk)

    # For debugging:
    # for i, chunk in enumerate(flattened_chunks):
    #    print(f'chunk {i}:')
    #    for line in chunk:
    #        print(line)
    #    assert False


###
# Testing function get_commit_chunk_pairs
###
def test_get_commit_chunk_pairs_fn_basic_functionality(
    old_file1,
    modified_file1,
    old_file2,
    modified_file2,
    old_file3,
    modified_file3,
    random,
):
    ###
    # Grab two python files (file1.py and file2.py) and one Java file (file3.java)
    ###

    file_path_to_diff = {}
    file_path_to_language = {}

    # Parse file 1
    raw_difflines = [
        line
        for line in ndiff(old_file1, modified_file1)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)
    fp = "file1.py"
    file_path_to_diff[fp] = difflines
    file_path_to_language[fp] = "Python"

    # Parse file 2
    raw_difflines = [
        line
        for line in ndiff(old_file2, modified_file2)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)
    fp = "file2.py"
    file_path_to_diff[fp] = difflines
    file_path_to_language[fp] = "Python"

    # Parse file 3
    raw_difflines = [
        line
        for line in ndiff(old_file3, modified_file3)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)
    fp = "file3.java"
    file_path_to_diff[fp] = difflines
    file_path_to_language[fp] = "Java"

    # query is 2-3 lines
    # key is 1 line

    args = SimpleNamespace(
        **{
            "output_path": TMP_DIR,
            "min_query_length": 8,
            "max_query_length": 12,
            "min_key_length": 3,
            "max_key_length": 7,
        }
    )

    all_chunks, pairs = get_commit_chunk_pairs(
        file_path_to_diff, file_path_to_language, args
    )

    # Since we fix seed, this assert is invariant to the chunk length sampling code in _get_chunks
    # observe we should be able to construct:
    # - 1 java pair
    # - 4 python pairs
    # We need to throw away 1 example due to having an uneven number of chunks.
    expected_chunks = [
        {
            "chunk": [1212, 318, 1627, 352, 26, 198, 1212, 318, 3421, 1627, 362, 26],
            "fp": "file3.java",
        },
        {"chunk": [1212, 318, 3421, 1627, 718, 26], "fp": "file3.java"},
        {
            "chunk": [1212, 318, 3421, 1627, 1248, 198, 1212, 318, 3421, 1627, 678],
            "fp": "file1.py",
        },
        {"chunk": [1212, 318, 3421, 1627, 642], "fp": "file1.py"},
        {
            "chunk": [1212, 318, 3421, 1627, 513, 198, 1212, 318, 3421, 1627, 604],
            "fp": "file1.py",
        },
        {"chunk": [1212, 318, 3421, 1627, 657], "fp": "file2.py"},
        {
            "chunk": [1212, 318, 3421, 1627, 718, 198, 1212, 318, 1627, 767],
            "fp": "file1.py",
        },
        {"chunk": [1212, 318, 3421, 1627, 1105], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 352], "fp": "file2.py"},
        {"chunk": [1212, 318, 3421, 1627, 362], "fp": "file2.py"},
        {
            "chunk": [1212, 318, 1627, 642, 198, 1212, 318, 3421, 1627, 718],
            "fp": "file2.py",
        },
    ]
    expected_pairs = [
        {
            "query_chunk": [
                1212,
                318,
                1627,
                352,
                26,
                198,
                1212,
                318,
                3421,
                1627,
                362,
                26,
            ],
            "query_fp": "file3.java",
            "key_chunk": [1212, 318, 3421, 1627, 718, 26],
            "key_fp": "file3.java",
        },
        {
            "query_chunk": [
                1212,
                318,
                3421,
                1627,
                1248,
                198,
                1212,
                318,
                3421,
                1627,
                678,
            ],
            "query_fp": "file1.py",
            "key_chunk": [1212, 318, 3421, 1627, 642],
            "key_fp": "file1.py",
        },
        {
            "query_chunk": [
                1212,
                318,
                3421,
                1627,
                513,
                198,
                1212,
                318,
                3421,
                1627,
                604,
            ],
            "query_fp": "file1.py",
            "key_chunk": [1212, 318, 3421, 1627, 657],
            "key_fp": "file2.py",
        },
        {
            "query_chunk": [1212, 318, 3421, 1627, 718, 198, 1212, 318, 1627, 767],
            "query_fp": "file1.py",
            "key_chunk": [1212, 318, 3421, 1627, 1105],
            "key_fp": "file1.py",
        },
        {
            "query_chunk": [1212, 318, 3421, 1627, 352],
            "query_fp": "file2.py",
            "key_chunk": [1212, 318, 3421, 1627, 362],
            "key_fp": "file2.py",
        },
    ]

    for actual_chunk, expected_chunk in itertools.zip_longest(
        all_chunks, expected_chunks, fillvalue=None
    ):
        assert actual_chunk == expected_chunk, (actual_chunk, expected_chunk)

    for actual_pair, expected_pair in itertools.zip_longest(
        pairs, expected_pairs, fillvalue=None
    ):
        assert actual_pair == expected_pair, (actual_pair, expected_pair)


def test_get_commit_chunk_pairs_fn_only_room_for_queries(
    old_file1,
    modified_file1,
    old_file2,
    modified_file2,
    old_file3,
    modified_file3,
    random,
):
    ###
    # Grab two python files (file1.py and file2.py) and one Java file (file3.java)
    ###

    file_path_to_diff = {}
    file_path_to_language = {}

    # Parse file 1
    raw_difflines = [
        line
        for line in ndiff(old_file1, modified_file1)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)
    fp = "file1.py"
    file_path_to_diff[fp] = difflines
    file_path_to_language[fp] = "Python"

    # Parse file 2
    raw_difflines = [
        line
        for line in ndiff(old_file2, modified_file2)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)
    fp = "file2.py"
    file_path_to_diff[fp] = difflines
    file_path_to_language[fp] = "Python"

    # Parse file 3
    raw_difflines = [
        line
        for line in ndiff(old_file3, modified_file3)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)
    fp = "file3.java"
    file_path_to_diff[fp] = difflines
    file_path_to_language[fp] = "Java"

    # no queries
    # keys are one line long

    args = SimpleNamespace(
        **{
            "output_path": TMP_DIR,
            "min_query_length": 3,
            "max_query_length": 7,
            "min_key_length": 10_000,
            "max_key_length": 10_005,
        }
    )

    all_chunks, pairs = get_commit_chunk_pairs(
        file_path_to_diff, file_path_to_language, args
    )

    # Since we fix seed, this assert is invariant to the chunk length sampling code in _get_chunks

    expected_chunks = [
        {"chunk": [1212, 318, 3421, 1627, 718, 26], "fp": "file3.java"},
        {"chunk": [1212, 318, 3421, 1627, 362, 26], "fp": "file3.java"},
        {"chunk": [1212, 318, 3421, 1627, 513], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 1248], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 718], "fp": "file2.py"},
        {"chunk": [1212, 318, 3421, 1627, 604], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 362], "fp": "file2.py"},
        {"chunk": [1212, 318, 3421, 1627, 642], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 678], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 1105], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 718], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 657], "fp": "file2.py"},
        {"chunk": [1212, 318, 3421, 1627, 352], "fp": "file2.py"},
    ]
    expected_pairs = [
        {
            "query_chunk": [1212, 318, 3421, 1627, 718, 26],
            "query_fp": "file3.java",
            "key_chunk": [1212, 318, 3421, 1627, 362, 26],
            "key_fp": "file3.java",
        },
        {
            "query_chunk": [1212, 318, 3421, 1627, 513],
            "query_fp": "file1.py",
            "key_chunk": [1212, 318, 3421, 1627, 1248],
            "key_fp": "file1.py",
        },
        {
            "query_chunk": [1212, 318, 3421, 1627, 718],
            "query_fp": "file2.py",
            "key_chunk": [1212, 318, 3421, 1627, 604],
            "key_fp": "file1.py",
        },
        {
            "query_chunk": [1212, 318, 3421, 1627, 362],
            "query_fp": "file2.py",
            "key_chunk": [1212, 318, 3421, 1627, 642],
            "key_fp": "file1.py",
        },
        {
            "query_chunk": [1212, 318, 3421, 1627, 678],
            "query_fp": "file1.py",
            "key_chunk": [1212, 318, 3421, 1627, 1105],
            "key_fp": "file1.py",
        },
        {
            "query_chunk": [1212, 318, 3421, 1627, 718],
            "query_fp": "file1.py",
            "key_chunk": [1212, 318, 3421, 1627, 657],
            "key_fp": "file2.py",
        },
    ]

    for actual_chunk, expected_chunk in itertools.zip_longest(
        all_chunks, expected_chunks, fillvalue=None
    ):
        assert actual_chunk == expected_chunk, (actual_chunk, expected_chunk)

    for actual_pair, expected_pair in itertools.zip_longest(
        pairs, expected_pairs, fillvalue=None
    ):
        assert actual_pair == expected_pair, (actual_pair, expected_pair)


def test_get_commit_chunk_pairs_fn_only_room_for_keys(
    old_file1,
    modified_file1,
    old_file2,
    modified_file2,
    old_file3,
    modified_file3,
    random,
):
    ###
    # Grab two python files (file1.py and file2.py) and one Java file (file3.java)
    ###

    file_path_to_diff = {}
    file_path_to_language = {}

    # Parse file 1
    raw_difflines = [
        line
        for line in ndiff(old_file1, modified_file1)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)
    fp = "file1.py"
    file_path_to_diff[fp] = difflines
    file_path_to_language[fp] = "Python"

    # Parse file 2
    raw_difflines = [
        line
        for line in ndiff(old_file2, modified_file2)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)
    fp = "file2.py"
    file_path_to_diff[fp] = difflines
    file_path_to_language[fp] = "Python"

    # Parse file 3
    raw_difflines = [
        line
        for line in ndiff(old_file3, modified_file3)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)
    fp = "file3.java"
    file_path_to_diff[fp] = difflines
    file_path_to_language[fp] = "Java"

    # no queries
    # keys are one line long

    args = SimpleNamespace(
        **{
            "output_path": TMP_DIR,
            "min_query_length": 10_000,
            "max_query_length": 10_005,
            "min_key_length": 3,
            "max_key_length": 7,
        }
    )

    all_chunks, pairs = get_commit_chunk_pairs(
        file_path_to_diff, file_path_to_language, args
    )

    # Since we fix seed, this assert is invariant to the chunk length sampling code in _get_chunks

    expected_chunks = [
        {"chunk": [1212, 318, 3421, 1627, 718, 26], "fp": "file3.java"},
        {"chunk": [1212, 318, 3421, 1627, 362, 26], "fp": "file3.java"},
        {"chunk": [1212, 318, 3421, 1627, 513], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 1248], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 718], "fp": "file2.py"},
        {"chunk": [1212, 318, 3421, 1627, 604], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 362], "fp": "file2.py"},
        {"chunk": [1212, 318, 3421, 1627, 642], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 678], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 1105], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 718], "fp": "file1.py"},
        {"chunk": [1212, 318, 3421, 1627, 657], "fp": "file2.py"},
        {"chunk": [1212, 318, 3421, 1627, 352], "fp": "file2.py"},
    ]
    expected_pairs = [
        {
            "query_chunk": [1212, 318, 3421, 1627, 718, 26],
            "query_fp": "file3.java",
            "key_chunk": [1212, 318, 3421, 1627, 362, 26],
            "key_fp": "file3.java",
        },
        {
            "query_chunk": [1212, 318, 3421, 1627, 513],
            "query_fp": "file1.py",
            "key_chunk": [1212, 318, 3421, 1627, 1248],
            "key_fp": "file1.py",
        },
        {
            "query_chunk": [1212, 318, 3421, 1627, 718],
            "query_fp": "file2.py",
            "key_chunk": [1212, 318, 3421, 1627, 604],
            "key_fp": "file1.py",
        },
        {
            "query_chunk": [1212, 318, 3421, 1627, 362],
            "query_fp": "file2.py",
            "key_chunk": [1212, 318, 3421, 1627, 642],
            "key_fp": "file1.py",
        },
        {
            "query_chunk": [1212, 318, 3421, 1627, 678],
            "query_fp": "file1.py",
            "key_chunk": [1212, 318, 3421, 1627, 1105],
            "key_fp": "file1.py",
        },
        {
            "query_chunk": [1212, 318, 3421, 1627, 718],
            "query_fp": "file1.py",
            "key_chunk": [1212, 318, 3421, 1627, 657],
            "key_fp": "file2.py",
        },
    ]

    for actual_chunk, expected_chunk in itertools.zip_longest(
        all_chunks, expected_chunks, fillvalue=None
    ):
        assert actual_chunk == expected_chunk, (actual_chunk, expected_chunk)

    for actual_pair, expected_pair in itertools.zip_longest(
        pairs, expected_pairs, fillvalue=None
    ):
        assert actual_pair == expected_pair, (actual_pair, expected_pair)


def test_get_commit_chunk_pairs_fn_giant_chunk_target_size(
    old_file1,
    modified_file1,
    old_file2,
    modified_file2,
    old_file3,
    modified_file3,
    old_file4,
    modified_file4,
    random,
):
    ###
    # Grab two python files (file1.py and file2.py) and one Java file (file3.java)
    ###

    file_path_to_diff = {}
    file_path_to_language = {}

    # Parse file 1
    raw_difflines = [
        line
        for line in ndiff(old_file1, modified_file1)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)
    fp = "file1.py"
    file_path_to_diff[fp] = difflines
    file_path_to_language[fp] = "Python"

    # Parse file 2
    raw_difflines = [
        line
        for line in ndiff(old_file2, modified_file2)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)
    fp = "file2.py"
    file_path_to_diff[fp] = difflines
    file_path_to_language[fp] = "Python"

    # Parse file 3
    raw_difflines = [
        line
        for line in ndiff(old_file3, modified_file3)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)
    fp = "file3.java"
    file_path_to_diff[fp] = difflines
    file_path_to_language[fp] = "Java"

    # Parse file 4
    raw_difflines = [
        line
        for line in ndiff(old_file4, modified_file4)
        if not line.startswith("-") and not line.startswith("?")
    ]
    difflines = []
    for line in raw_difflines:
        assert line.startswith("+") or line.startswith(
            " "
        ), f"We should be throwing away lines other than additions context. Things look bad in: {line}"
        tokenized_line = tokenizer.tokenize(line[2:])
        diffline = (
            LineType.ADDITION if line.startswith("+") else LineType.CONTEXT,
            tokenized_line,
            len(tokenized_line),
        )
        difflines.append(diffline)
    fp = "file4.py"
    file_path_to_diff[fp] = difflines
    file_path_to_language[fp] = "Python"

    # no queries
    # keys are one line long

    args = SimpleNamespace(
        **{
            "output_path": TMP_DIR,
            "min_query_length": 25,
            "max_query_length": 100_005,
            "min_key_length": 10_000,
            "max_key_length": 10_005,
        }
    )

    all_chunks, pairs = get_commit_chunk_pairs(
        file_path_to_diff, file_path_to_language, args
    )

    # Since we fix seed, this assert is invariant to the chunk length sampling code in _get_chunks
    expected_chunks = [
        {
            "chunk": [
                1212,
                318,
                1627,
                657,
                198,
                1212,
                318,
                1627,
                352,
                198,
                1212,
                318,
                1627,
                362,
                198,
                1212,
                318,
                3421,
                1627,
                513,
                198,
                1212,
                318,
                3421,
                1627,
                604,
                198,
                1212,
                318,
                3421,
                1627,
                642,
                198,
                1212,
                318,
                3421,
                1627,
                718,
                198,
                1212,
                318,
                1627,
                767,
                198,
                1212,
                318,
                1627,
                807,
                198,
                1212,
                318,
                1627,
                860,
                198,
                1212,
                318,
                1627,
                838,
                198,
                1212,
                318,
                1627,
                1367,
                198,
                1212,
                318,
                3421,
                1627,
                1105,
                198,
                1212,
                318,
                1627,
                1511,
                198,
                1212,
                318,
                1627,
                1478,
                198,
                1212,
                318,
                1627,
                1315,
                198,
                1212,
                318,
                1627,
                1467,
                198,
                1212,
                318,
                1627,
                1596,
                198,
                1212,
                318,
                3421,
                1627,
                1248,
                198,
                1212,
                318,
                3421,
                1627,
                678,
            ],
            "fp": "file1.py",
        },
        {
            "chunk": [
                1212,
                318,
                3421,
                1627,
                657,
                198,
                1212,
                318,
                3421,
                1627,
                352,
                198,
                1212,
                318,
                3421,
                1627,
                362,
                198,
                1212,
                318,
                1627,
                513,
                198,
                1212,
                318,
                1627,
                604,
                198,
                1212,
                318,
                1627,
                642,
                198,
                1212,
                318,
                3421,
                1627,
                718,
                198,
                1212,
                318,
                3421,
                1627,
                767,
            ],
            "fp": "file4.py",
        },
        {
            "chunk": [
                1212,
                318,
                1627,
                657,
                26,
                198,
                1212,
                318,
                1627,
                352,
                26,
                198,
                1212,
                318,
                3421,
                1627,
                362,
                26,
                198,
                1212,
                318,
                1627,
                513,
                26,
                198,
                1212,
                318,
                1627,
                604,
                26,
                198,
                1212,
                318,
                1627,
                642,
                26,
                198,
                1212,
                318,
                3421,
                1627,
                718,
                26,
            ],
            "fp": "file3.java",
        },
        {
            "chunk": [
                1212,
                318,
                3421,
                1627,
                657,
                198,
                1212,
                318,
                3421,
                1627,
                352,
                198,
                1212,
                318,
                3421,
                1627,
                362,
                198,
                1212,
                318,
                1627,
                513,
                198,
                1212,
                318,
                1627,
                604,
                198,
                1212,
                318,
                1627,
                642,
                198,
                1212,
                318,
                3421,
                1627,
                718,
            ],
            "fp": "file2.py",
        },
    ]
    expected_pairs = [
        {
            "query_chunk": [
                1212,
                318,
                1627,
                657,
                198,
                1212,
                318,
                1627,
                352,
                198,
                1212,
                318,
                1627,
                362,
                198,
                1212,
                318,
                3421,
                1627,
                513,
                198,
                1212,
                318,
                3421,
                1627,
                604,
                198,
                1212,
                318,
                3421,
                1627,
                642,
                198,
                1212,
                318,
                3421,
                1627,
                718,
                198,
                1212,
                318,
                1627,
                767,
                198,
                1212,
                318,
                1627,
                807,
                198,
                1212,
                318,
                1627,
                860,
                198,
                1212,
                318,
                1627,
                838,
                198,
                1212,
                318,
                1627,
                1367,
                198,
                1212,
                318,
                3421,
                1627,
                1105,
                198,
                1212,
                318,
                1627,
                1511,
                198,
                1212,
                318,
                1627,
                1478,
                198,
                1212,
                318,
                1627,
                1315,
                198,
                1212,
                318,
                1627,
                1467,
                198,
                1212,
                318,
                1627,
                1596,
                198,
                1212,
                318,
                3421,
                1627,
                1248,
                198,
                1212,
                318,
                3421,
                1627,
                678,
            ],
            "query_fp": "file1.py",
            "key_chunk": [
                1212,
                318,
                3421,
                1627,
                657,
                198,
                1212,
                318,
                3421,
                1627,
                352,
                198,
                1212,
                318,
                3421,
                1627,
                362,
                198,
                1212,
                318,
                1627,
                513,
                198,
                1212,
                318,
                1627,
                604,
                198,
                1212,
                318,
                1627,
                642,
                198,
                1212,
                318,
                3421,
                1627,
                718,
                198,
                1212,
                318,
                3421,
                1627,
                767,
            ],
            "key_fp": "file4.py",
        },
        {
            "query_chunk": [
                1212,
                318,
                1627,
                657,
                26,
                198,
                1212,
                318,
                1627,
                352,
                26,
                198,
                1212,
                318,
                3421,
                1627,
                362,
                26,
                198,
                1212,
                318,
                1627,
                513,
                26,
                198,
                1212,
                318,
                1627,
                604,
                26,
                198,
                1212,
                318,
                1627,
                642,
                26,
                198,
                1212,
                318,
                3421,
                1627,
                718,
                26,
            ],
            "query_fp": "file3.java",
            "key_chunk": [
                1212,
                318,
                3421,
                1627,
                657,
                198,
                1212,
                318,
                3421,
                1627,
                352,
                198,
                1212,
                318,
                3421,
                1627,
                362,
                198,
                1212,
                318,
                1627,
                513,
                198,
                1212,
                318,
                1627,
                604,
                198,
                1212,
                318,
                1627,
                642,
                198,
                1212,
                318,
                3421,
                1627,
                718,
            ],
            "key_fp": "file2.py",
        },
    ]

    for actual_chunk, expected_chunk in itertools.zip_longest(
        all_chunks, expected_chunks, fillvalue=None
    ):
        assert actual_chunk == expected_chunk, (actual_chunk, expected_chunk)

    for actual_pair, expected_pair in itertools.zip_longest(
        pairs, expected_pairs, fillvalue=None
    ):
        assert actual_pair == expected_pair, (actual_pair, expected_pair)


###
# End-to-end testing of _process_repo
###
diff_b_names_to_expected_chunks = {
    "HEAD~2": {
        ("This is line 2.\n", "This is changed line 3.\n"),
        ("This is changed line 11.\n",),
    },
    "HEAD~1": {
        ("This is line 1.\n", "This is line 2.\n", "This is line 3.\n"),
        ("This is line 4.\n",),
        ("This is line 5.\n", "This is line 6.\n", "This is line 7.\n"),
        ("This is line 8.\n",),
        ("This is line 9.\n", "This is line 10.\n"),
        ("This is line 11.\n",),
        ("This is line 12.\n", "This is line 13.\n"),
        ("This is line 14.\n",),
        ("This is line 15.\n", "This is line 16.\n"),
        ("This is line 17.\n",),
        ("This is line 18.\n", "This is line 19.\n"),
        ("This is line 20.\n",),
        ("This is line 21.\n", "This is line 22.\n"),
        ("This is line 23.\n",),
        ("This is line 24.\n", "This is line 25.\n"),
        ("This is line 26.\n",),
        ("This is line 27.\n", "This is line 28.\n"),
        ("This is line 29.\n",),
        ("This is line 30.\n", "This is line 31.\n"),
        ("This is line 32.\n",),
        ("This is line 33.\n", "This is line 34.\n", "This is line 35.\n"),
        ("This is line 36.\n",),
        ("This is line 37.\n", "This is line 38.\n"),
        ("This is line 39.\n",),
        ("This is line 40.\n", "This is line 41.\n", "This is line 42.\n"),
        ("This is line 43.\n",),
        ("This is line 44.\n", "This is line 45.\n"),
        ("This is line 46.\n",),
        ("This is line 47.\n", "This is line 48.\n"),
        ("This is line 49.\n",),
        ("This is line 50.\n", "This is line 51.\n", "This is line 52.\n"),
        ("This is line 53.\n",),
        ("This is line 54.\n", "This is line 55.\n", "This is line 56.\n"),
        ("This is line 57.\n",),
        ("This is line 58.\n", "This is line 59.\n"),
        ("This is line 60.\n",),
        ("This is line 61.\n", "This is line 62.\n"),
        ("This is line 63.\n",),
        ("This is line 64.\n", "This is line 65.\n"),
        ("This is line 66.\n",),
        ("This is line 67.\n", "This is line 68.\n"),
        ("This is line 69.\n",),
        ("This is line 70.\n", "This is line 71.\n", "This is line 72.\n"),
        ("This is line 73.\n",),
        ("This is line 74.\n", "This is line 75.\n"),
        ("This is line 76.\n",),
        ("This is line 77.\n", "This is line 78.\n", "This is line 79.\n"),
        ("This is line 80.\n",),
        ("This is line 81.\n", "This is line 82.\n"),
        ("This is line 83.\n",),
        ("This is line 84.\n", "This is line 85.\n"),
        ("This is line 86.\n",),
        ("This is line 87.\n", "This is line 88.\n"),
        ("This is line 89.\n",),
        ("This is line 90.\n", "This is line 91.\n"),
        ("This is line 92.\n",),
        ("This is line 93.\n", "This is line 94.\n"),
        ("This is line 95.\n",),
        ("This is line 96.\n", "This is line 97.\n"),
        ("This is line 98.\n",),
        ("This is line 99.\n",),
    },
    "HEAD~0": {
        ("This is line 3.\n", "This is changed line 4.\n"),
        ("This is changed line 84.\n",),
    },
}


def test_process_repo_basic_functionality(random):
    args = SimpleNamespace(
        **{
            "output_path": TMP_DIR,
            "min_query_length": 12,
            "max_query_length": 15,
            "min_key_length": 3,
            "max_key_length": 7,
            "num_commits_to_record": 10,
        }
    )

    pair_records = []
    all_chunks = []
    repo_metadata: RepoMetadata = {
        "tarball_path": TEST_REPO,
        "num_commits_processed": 0,
        "num_exceptions": 0,
        "num_timeouts": 0,
        "total_process_time": 0,
        "extraction_duration": 0,
        "repo_constructor_duration": 0,
        "timeout_process_repo_duration": 0,
        "timeout_delete_repo_duration": 0,
        "pairs_saved": 0,
        "chunks_saved": 0,
    }
    per_commit_metadata = []

    out = _process_repo(
        TEST_REPO, args, pair_records, all_chunks, repo_metadata, per_commit_metadata
    )

    diff_b_names = [f"HEAD~{i}" for i in reversed(range(3))]

    draft_diff_b_names_to_expected_chunks = {
        k: {item for item in v} for k, v in diff_b_names_to_expected_chunks.items()
    }
    for chunk in all_chunks:
        commit_name = chunk["commit_ref_relative_to_head"]
        chunk_text = tokenizer.detokenize(chunk["chunk"]).splitlines(1)
        chunk_text[-1] = chunk_text[-1] + "\n"
        draft_diff_b_names_to_expected_chunks[commit_name].remove(tuple(chunk_text))

    for commit_name in diff_b_names:
        assert (
            len(draft_diff_b_names_to_expected_chunks[commit_name]) == 0
        ), draft_diff_b_names_to_expected_chunks

    draft_diff_b_names_to_expected_chunks = {
        k: {item for item in v} for k, v in diff_b_names_to_expected_chunks.items()
    }
    for pair in pair_records:
        commit_name = pair["commit_ref_relative_to_head"]
        for chunk_type in ["query", "key"]:
            chunk_text = tokenizer.detokenize(pair[chunk_type]).splitlines(1)
            chunk_text[-1] = chunk_text[-1] + "\n"
            draft_diff_b_names_to_expected_chunks[commit_name].remove(tuple(chunk_text))

    # remove leftover chunk (it had no one to pair with)
    draft_diff_b_names_to_expected_chunks["HEAD~1"].remove(("This is line 53.\n",))

    for commit_name in diff_b_names:
        assert (
            len(draft_diff_b_names_to_expected_chunks[commit_name]) == 0
        ), draft_diff_b_names_to_expected_chunks

    assert out is True
