"""Rewrite a dataset."""
from pathlib import Path
from types import SimpleNamespace

import cfg
import numpy as np
import torch
from megatron.data import indexed_dataset
from megatron.data.indexed_dataset import make_dataset as make_indexed_dataset
from megatron.tokenizer import build_tokenizer


def rewrite(indir, outdir, prefix, sos_token):
    """Rewrite each sample in a dataset.

    Iterate through a dataset and perform a transformation on each
    document. Write out the document to a new dataset.
    The primary intention for this script is to transform a dataset
    of seq_length docs into one of seq_length+1 by inserting
    a <|startofsequence|> token at the beginning.

    Outputs a rewritten dataset to a new subdirectory "out".
    """
    idx_ds = make_indexed_dataset(str(indir / prefix), "mmap", skip_warmup=True)

    output_bin_files = f"{outdir / prefix}.bin"
    output_idx_files = f"{outdir / prefix}.idx"

    # Note the vocab_size is used to determine the smallest datatype
    # that will accomodate the integer data.
    builder = indexed_dataset.make_builder(
        output_bin_files, impl="mmap", vocab_size=52000
    )

    def transform(a):
        # Note, we use int32 for the ndarray because uint16 is incompatible
        # with the torch tensor we send to the builder.
        assert a.dtype == np.uint16
        out = np.zeros(len(a) + 1, dtype=np.int32)
        out[0] = sos_token
        out[1:] = a
        return out

    for doc in idx_ds:
        doc_out = transform(doc)
        builder.add_item(torch.IntTensor(doc_out))
        builder.end_document()
    builder.finalize(output_idx_files)
    print(f"Output rewritten files to {Path(output_bin_files).with_suffix('')}")


def main():
    in_path = Path(cfg.STEP_PATH_10)
    output = Path(cfg.STEP_PATH_11)
    output.mkdir()

    # create tokenizer to extract the startofsequence token
    args = SimpleNamespace(
        dataset_impl="mmap",
        rank=0,
        tokenizer_type="CodeGenTokenizer",
        make_vocab_size_divisible_by=128,
        model_parallel_size=1,
    )
    tokenizer = build_tokenizer(args)

    # Input is expected to be a directory of partitions, where each
    # partition contains a single indexed dataset.
    indirs = [x for x in in_path.iterdir() if x.is_dir()]
    indirs = sorted(indirs)
    for inpart in indirs:
        print("Processing:", inpart)

        bin_files = [x for x in inpart.glob("*.bin")]
        assert len(bin_files) == 1

        outpart = output / inpart.name
        outpart.mkdir()
        rewrite(inpart, outpart, bin_files[0].stem, tokenizer.sos)
    print("Done")


if __name__ == "__main__":
    main()
