"""Pipleine config file."""
languages = ["C", "C++", "Go", "Java", "JavaScript", "Python"]
prompt_filenames = [
    "api_level_completion_1k_context_codegen.test.jsonl",
    "api_level_completion_2k_context_codegen.test.jsonl",
    "line_level_completion_1k_context_codegen.test.jsonl",
    "line_level_completion_2k_context_codegen.test.jsonl",
    # Note there is not a codegen version of the function level completion
    # prompts. I believe it matters only in that the prompt will need
    # to be trunctated if we also want to add retrieved chunks, since
    # the seq_length for codegen is 2K.
    "function_level_completion_2k_context_codex.test.jsonl",
]

repos_filename = "repos.jsonl"
