java_projects/xebia-petclinic src/main/java/org/springframework/samples/petclinic/Owner.java
java_projects/xebia-petclinic src/main/java/org/springframework/samples/petclinic/package-info.java
java_projects/xebia-petclinic src/main/java/org/springframework/samples/petclinic/jpa/EntityManagerClinic.java
java_projects/xebia-petclinic src/main/java/org/springframework/samples/petclinic/Visit.java
java_projects/xebia-petclinic src/main/java/org/springframework/samples/petclinic/Specialty.java
java_projects/xebia-petclinic src/main/java/org/springframework/samples/petclinic/web/AddPetForm.java
java_projects/xebia-petclinic src/main/java/org/springframework/samples/petclinic/validation/package-info.java
java_projects/xebia-petclinic src/main/java/org/springframework/samples/petclinic/web/EditPetForm.java
java_projects/xebia-petclinic src/main/java/org/springframework/samples/petclinic/Person.java
java_projects/xebia-petclinic src/main/java/org/springframework/samples/petclinic/validation/OwnerValidator.java
java_projects/vraptor-dash src/main/java/br/com/caelum/vraptor/dash/hibernate/stats/HibernateStatsAwareUser.java
java_projects/vraptor-dash src/main/java/br/com/caelum/vraptor/dash/statement/StatementController.java
java_projects/vraptor-dash src/main/java/br/com/caelum/vraptor/dash/audit/Audit.java
java_projects/vraptor-dash src/test/java/br/com/caelum/vraptor/dash/matchers/IsEmptyMapMatcher.java
java_projects/vraptor-dash src/main/java/br/com/caelum/vraptor/dash/hibernate/HibernateStatisticsCollector.java
java_projects/vraptor-dash src/test/java/br/com/caelum/vraptor/dash/interceptor/ContentTypeInterceptorTest.java
java_projects/vraptor-dash src/main/java/br/com/caelum/vraptor/dash/uristats/BaseURIStatInterceptor.java
java_projects/vraptor-dash src/main/java/br/com/caelum/vraptor/dash/cache/CacheCheckFilter.java
java_projects/vraptor-dash src/test/java/br/com/caelum/vraptor/dash/audit/LogRequestTimeInterceptorTest.java
java_projects/vraptor-dash src/main/java/br/com/caelum/vraptor/dash/config/UserConfig.java
java_projects/vraptor-dash src/main/java/br/com/caelum/vraptor/dash/hibernate/stats/EntityStatsWrapper.java
java_projects/vraptor-dash src/main/java/br/com/caelum/vraptor/dash/monitor/BasicMonitor.java
java_projects/vraptor-dash src/main/java/br/com/caelum/vraptor/dash/hibernate/stats/EntityCacheStatsWrapper.java
java_projects/twitterdroid twitterdroid/src/jtwitter/TwitterEntry.java
java_projects/twitterdroid twitterdroid/src/com/fredbrunel/android/twitter/StatusAdapter.java
java_projects/twitterdroid twitterdroid/src/com/fredbrunel/android/twitter/AuthConstants.java
java_projects/twitterdroid twitterdroid/src/com/fredbrunel/android/twitter/ConfigActivity.java
java_projects/thucydides-showcase thucydides-showcase-simple-webtests/src/test/java/net/thucydides/showcase/simple/WhenViewingArtifactDetails.java
java_projects/thucydides-showcase thucydides-showcase-simple-webtests/src/main/java/net/thucydides/showcase/simple/pages/SearchPage.java
java_projects/thucydides-showcase thucydides-showcase-simple-webtests/src/main/java/net/thucydides/showcase/simple/pages/SearchResultsPage.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/plus/person/impl/OAuth2Draft10RequestInterceptor.java
java_projects/spring-social-google spring-social-google-quickstart/spring-social-quickstart/src/main/java/org/springframework/social/quickstart/config/WebMvcConfig.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/plus/person/impl/ContactQueryBuilderImpl.java
java_projects/spring-social-google spring-social-google-quickstart/spring-social-quickstart/src/main/java/org/springframework/social/quickstart/tasks/TaskForm.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/legacyprofile/impl/UserTemplate.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/plus/activity/Attachment.java
java_projects/spring-social-google spring-social-google-quickstart/spring-social-quickstart/src/main/java/org/springframework/social/quickstart/config/package-info.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/plus/activity/ActivityQueryBuilder.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/plus/person/impl/ContactEntryWrapper.java
java_projects/spring-social-google spring-social-google-quickstart/spring-social-quickstart/src/main/java/org/springframework/social/quickstart/user/SecurityContext.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/plus/activity/impl/ActivityQueryBuilderImpl.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/plus/person/Address.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/query/ApiQueryBuilder.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/legacyprofile/LegacyProfileOperations.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/tasks/impl/TaskTemplate.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/plus/activity/Article.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/query/impl/QueryBuilderImpl.java
java_projects/spring-social-google spring-social-google-quickstart/spring-social-quickstart/src/main/java/org/springframework/social/quickstart/config/EnvironmentInitializer.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/query/ApiPage.java
java_projects/spring-social-google spring-social-google-quickstart/spring-social-quickstart/src/main/java/org/springframework/social/quickstart/config/SocialConfig.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/tasks/TaskQueryBuilder.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/plus/person/package-info.java
java_projects/spring-social-google spring-social-google/spring-social-google/src/main/java/org/springframework/social/google/api/tasks/impl/TaskListQueryBuilderImpl.java
java_projects/servletjspdemo src/main/java/com/example/servletjspdemo/web/DataServlet.java
java_projects/servletjspdemo src/main/java/com/example/servletjspdemo/domain/Person.java
java_projects/postmark-java src/main/java/com/postmark/java/Attachment.java
java_projects/postmark-java src/main/java/com/postmark/java/SkipMe.java
java_projects/postmark-java src/main/java/com/postmark/java/PostmarkClient.java
java_projects/osgiutils_1 osgiutils.services/src/osgiutils/services/MultiServiceRunnable.java
java_projects/osgiutils_1 osgiutils.services/src/osgiutils/services/ServiceRunnableFallback.java
java_projects/naturvielfalt_android Naturwerk/src/info/naturwerk/app/NWDataBase.java
java_projects/naturvielfalt_android Naturwerk/src/info/naturwerk/app/NWBaseActivity.java
java_projects/naturvielfalt_android Naturwerk/src/info/naturwerk/app/NWCategoryActivity.java
java_projects/mod-installer src/mc/now/ui/Installer.java
java_projects/logmx src/logmx/parser/GlassfishLogFileParser.java
java_projects/koku-service-api personservice-api/src/main/java/fi/koku/services/entity/person/v1/PersonInfoProvider.java
java_projects/koku-service-api customerservice-api/src/main/java/fi/koku/services/entity/customerservice/model/CommunityRole.java
java_projects/koku-service-api customerservice-api/src/main/java/fi/koku/services/entity/customerservice/model/Message.java
java_projects/koku-service-api customerservice-api/src/main/java/fi/koku/services/entity/customerservice/model/Family.java
java_projects/koku-service-api customerservice-api/src/main/java/fi/koku/services/entity/customer/v1/CustomerServiceFactory.java
java_projects/koku-service-api authorizationinfoservice-api/src/main/java/fi/koku/services/utility/authorizationinfo/v1/AuthorizationInfoServiceFactory.java
java_projects/koku-service-api customerservice-api/src/main/java/fi/koku/services/entity/customerservice/model/FamilyIdAndFamilyMembers.java
java_projects/koku-service-api familyservice-api/src/main/java/fi/koku/services/entity/family/v1/FamilyService.java
java_projects/koku-service-api authorizationinfoservice-api/src/main/java/fi/koku/services/utility/authorizationinfo/v1/impl/AuthorizationInfoServiceDummyImpl.java
java_projects/koku-service-api personservice-api/src/main/java/fi/koku/services/entity/person/v1/PersonConstants.java
java_projects/koku-service-api authorizationinfoservice-api/src/main/java/fi/koku/services/utility/authorizationinfo/v1/model/Registry.java
java_projects/koku-service-api authorizationinfoservice-api/src/main/java/fi/koku/services/utility/authorizationinfo/util/AuthUtils.java
java_projects/koku-service-api personservice-api/src/main/java/fi/koku/services/entity/person/v1/impl/SaloImpl.java
java_projects/jbpm-plugin example-staging-workflow/src/main/java/hudson/stagingworkflow/CreateVotingTasksHandler.java
java_projects/jbpm-plugin jbpm/src/main/java/hudson/jbpm/ProcessClassLoaderCache.java
java_projects/jbpm-plugin jbpm/src/main/java/hudson/jbpm/workflow/StartProjectActionHandler.java
java_projects/jbpm-plugin jbpm/src/main/java/hudson/jbpm/model/Form.java
java_projects/jbpm-plugin example-staging-workflow/src/main/java/hudson/stagingworkflow/DeployStagedReleaseHandler.java
java_projects/jbpm-plugin jbpm/src/main/java/hudson/jbpm/workflow/FormActionHandler.java
java_projects/jbpm-plugin jbpm/src/main/java/hudson/jbpm/model/gpd/Label.java
java_projects/jbpm-plugin jbpm/src/main/java/hudson/jbpm/model/UserTasks.java
java_projects/ibatis-handling-joins src/com/loiane/model/Author.java
java_projects/ibatis-handling-joins src/com/loiane/data/BlogMapper.java
java_projects/ibatis-handling-joins src/com/loiane/model/Post.java
java_projects/hands-on-spring-batch 03-employees/src/main/java/handson/springbatch/tasklet/GenderTasklet.java
java_projects/hands-on-spring-batch 03-employees/src/main/java/handson/springbatch/reader/EmployeeFieldSetMapper.java
java_projects/hands-on-spring-batch 02-helloworld-chunk/src/test/java/handson/springbatch/HelloWorldJobTest.java
java_projects/hands-on-spring-batch 03-employees/src/test/java/handson/springbatch/EmployeeJobTest.java
java_projects/gat src/main/java/com/devtty/gat/data/MemberRepository.java
java_projects/gat src/main/java/com/devtty/gat/rest/JaxRsActivator.java
java_projects/gat src/main/java/com/devtty/gat/data/SeedDataImporter.java
java_projects/edsdk4j src/gettingstarted/E02_Simpler.java
java_projects/edsdk4j src/edsdk/EdsDirectoryItemInfo.java
java_projects/edsdk4j src/edsdk/EdsVolumeInfo.java
java_projects/edsdk4j src/edsdk/EdsRect.java
java_projects/edsdk4j src/edsdk/EdsRational.java
java_projects/edsdk4j src/edsdk/EdsSaveImageSetting.java
java_projects/edsdk4j src/edsdk/utils/commands/LiveViewTask.java
java_projects/edsdk4j src/edsdk/EdsUsersetData.java
java_projects/disruptor_1 code/src/perf/com/lmax/disruptor/support/ValueEntry.java
java_projects/disruptor_1 code/src/main/java/com/lmax/disruptor/LifecycleAware.java
java_projects/disruptor_1 code/src/test/java/com/lmax/disruptor/support/DaemonThreadFactory.java
java_projects/disruptor_1 code/src/perf/com/lmax/disruptor/support/FizzBuzzEntry.java
java_projects/disruptor_1 code/src/perf/com/lmax/disruptor/support/FunctionStep.java
java_projects/disruptor_1 code/src/perf/com/lmax/disruptor/support/ValueProducer.java
java_projects/disruptor_1 code/src/perf/com/lmax/disruptor/Sequencer3P1CPerfTest.java
java_projects/disruptor_1 code/src/test/java/com/lmax/disruptor/support/TestWaiter.java
java_projects/disruptor_1 code/src/perf/com/lmax/disruptor/AbstractPerfTestQueueVsDisruptor.java
java_projects/disruptor_1 code/src/main/java/com/lmax/disruptor/Util.java
java_projects/disruptor_1 code/src/main/java/com/lmax/disruptor/ForceFillProducerBarrier.java
java_projects/disruptor_1 code/src/main/java/com/lmax/disruptor/ConsumerBarrier.java
java_projects/disruptor_1 code/src/perf/com/lmax/disruptor/UniCast1P1CBatchPerfTest.java
java_projects/disruptor_1 code/src/main/java/com/lmax/disruptor/ClaimStrategy.java
java_projects/disruptor_1 code/src/perf/com/lmax/disruptor/support/LatencyStepHandler.java
java_projects/disruptor_1 code/src/perf/com/lmax/disruptor/support/ValueAdditionHandler.java
java_projects/confcaller gen/uk/me/sample/android/confcaller/R.java
java_projects/confcaller src/uk/me/sample/android/confcaller/ConfDbAdapter.java
java_projects/camel-aries-blueprint-tutorial reportincident.persistence-jpa-aries/src/main/java/org/apache/camel/example/reportincident/model/Incident.java
java_projects/camel-aries-blueprint-tutorial reportincident.web/src/main/java/org/apache/camel/example/reportincident/HomePage.java
java_projects/camel-aries-blueprint-tutorial reportincident.service-aries/src/main/java/org/apache/camel/example/reportincident/service/impl/IncidentServiceImpl.java
java_projects/camel-aries-blueprint-tutorial reportincident.service-aries/src/main/java/org/apache/camel/example/reportincident/service/IncidentService.java
java_projects/bioclipse.opentox plugins/net.bioclipse.opentox.qsar/src/net/bioclipse/opentox/qsar/OpenToxProviderDiscovery.java
java_projects/bioclipse.opentox plugins/net.bioclipse.opentox/src/net/bioclipse/opentox/business/IOpentoxManager.java
java_projects/bioclipse.opentox plugins/net.bioclipse.opentox.ds/src/net/bioclipse/opentox/ds/wizards/ServicesContentProvider.java
java_projects/bioclipse.opentox plugins/net.bioclipse.opentox/src/net/bioclipse/opentox/business/OpentoxManager.java
java_projects/bioclipse.opentox plugins/net.bioclipse.opentox.qsar/src/net/bioclipse/opentox/qsar/OpenToxDescriptorCalculator.java
java_projects/bioclipse.opentox plugins/net.bioclipse.opentox.ds/src/net/bioclipse/opentox/ds/OpenToxTestDiscovery.java
java_projects/bioclipse.opentox plugins/net.bioclipse.opentox.ui/src/net/bioclipse/opentox/ui/wizards/CreateDatasetPage.java
java_projects/bioclipse.opentox plugins/net.bioclipse.opentox/src/net/bioclipse/opentox/api/MolecularDescriptorAlgorithm.java
java_projects/bioclipse.opentox plugins/net.bioclipse.opentox.test/src/net/bioclipse/opentox/test/CoverageTest.java
java_projects/bioclipse.opentox plugins/net.bioclipse.opentox.ds/src/net/bioclipse/opentox/ds/OpenToxModel.java
java_projects/bioclipse.opentox plugins/net.bioclipse.opentox.test/src/net/bioclipse/opentox/test/AllOpentoxManagerPluginTests.java
java_projects/bioclipse.opentox plugins/net.bioclipse.opentox.ui/src/net/bioclipse/opentox/ui/handlers/CreateDatasetHandler.java
java_projects/android-tether src/og/android/tether/PostActivity.java
java_projects/android-tether src/og/android/tether/TetherIntent.java
java_projects/android-tether src/og/android/tether/system/WebserviceTask.java
java_projects/android-tether src/og/android/tether/AlarmReceiver.java
java_projects/android-tether src/og/android/tether/TetherService.java
java_projects/android-tether src/og/android/tether/SetupActivity.java
java_projects/android-tether src/og/android/tether/data/ClientData.java
java_projects/android-tether src/og/android/tether/AccessControlActivity.java
java_projects/android-tether facebook/src/com/facebook/android/AsyncFacebookRunner.java
java_projects/SnippingTool src/com/melloware/jintellitype/JIntellitype.java
java_projects/SnippingTool src/Preferences.java
java_projects/SnippingTool src/MultiUploaderThread.java
java_projects/SnippingTool src/MultiUploader.java
java_projects/SnippingTool src/OverlayPanel.java
java_projects/RPS RPS/src/net/heraan/Player/AI/Personality/Personality_Random.java
java_projects/RPS RPS/src/net/heraan/Player/Human/Human.java
java_projects/RPS RPS/src/net/heraan/Player/Player.java
java_projects/PipesJ2ME src/net/michaelkerley/Pipe.java
java_projects/MCBans src/com/mcbans/firestar/mcbans/org/json/JSONStringer.java
java_projects/MCBans src/com/mcbans/firestar/mcbans/org/json/JSONObject.java
java_projects/MCBans src/com/mcbans/firestar/mcbans/pluginInterface/Ban.java
java_projects/MCBans src/com/mcbans/firestar/mcbans/org/json/JSONTokener.java
java_projects/MCBans src/com/mcbans/firestar/mcbans/org/json/JSONArray.java
java_projects/MCBans src/com/mcbans/firestar/mcbans/pluginInterface/Kick.java
java_projects/MCBans src/com/mcbans/firestar/mcbans/commands/CommandHandler.java
java_projects/MCBans src/com/mcbans/firestar/mcbans/org/json/JSONWriter.java
java_projects/MCBans src/com/mcbans/firestar/mcbans/org/json/JSONException.java
java_projects/MCBans src/com/mcbans/firestar/mcbans/commands/Commands.java
java_projects/Journal.IO src/test/java/journal/io/api/JournalTest.java
java_projects/Journal.IO src/main/java/journal/io/api/WriteCallback.java
java_projects/Journal.IO src/main/java/journal/io/api/ReplicationTarget.java
java_projects/Journal.IO src/main/java/journal/io/api/DataFileAppender.java
java_projects/Clara clara/src/test/java/org/vaadin/teemu/clara/ClaraIntegrationTest.java
java_projects/Clara clara/src/main/java/org/vaadin/teemu/clara/AttributeInterceptor.java
java_projects/Clara clara/src/main/java/org/vaadin/teemu/clara/inflater/DefaultComponentManager.java
java_projects/Clara clara/src/main/java/org/vaadin/teemu/clara/inflater/ComponentInstantiationException.java
java_projects/Clara clara/src/main/java/org/vaadin/teemu/clara/AttributeContext.java
java_projects/Clara clara/src/main/java/org/vaadin/teemu/clara/inflater/VaadinAttributeParser.java
java_projects/AndProject src/bonsai/app/SelectBonsaiActivity.java
java_projects/AndProject src/bonsai/app/BonsaiDbUtil.java
java_projects/AndProject src/bonsai/app/alarm/NotificationService.java
java_projects/AndProject src/bonsai/app/weather/XmlHandler.java
java_projects/AndProject src/bonsai/app/EnvioMailActivity.java
java_projects/1160-Scouting-App src/com/team1160/scouting/frontend/elements/NumberDropDownElement.java
java_projects/1160-Scouting-App src/com/team1160/scouting/frontend/panels/GraphPanel.java
java_projects/1160-Scouting-App src/com/team1160/scouting/frontend/elements/JumpMenuItem.java
java_projects/1160-Scouting-App src/com/team1160/scouting/frontend/panels/InitialPanel.java
java_projects/1160-Scouting-App src/com/team1160/scouting/frontend/elements/MultiLineTableCellRenderer.java
java_projects/1160-Scouting-App src/com/team1160/scouting/frontend/ScoutingAppWindow.java
java_projects/1160-Scouting-App src/com/team1160/scouting/h2/CommentTable.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/sql/SQLIterator.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/server/DirectoryServlet.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/nodes/AllTests.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/algebra/RelationalOperators.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/expr/ConcatenationTest.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/engine/QueryIterTableSQL.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/algebra/Attribute.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/algebra/ExpressionProjectionSpec.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/sql/types/SQLBoolean.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/server/MetadataCreator.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/sql/ResultRowTest.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/expr/LessThanOrEqual.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/expr/LessThan.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/server/ClassMapServlet.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/SystemLoader.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/values/Column.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/server/NamespaceServlet.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/sql/types/SQLTime.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/server/D2RQDatasetDesc.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/mapgen/MappingGenerator.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/sql/types/SQLInterval.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/expr/BinaryOperator.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/sql/HSQLDBDatatypeTest.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/sql/ResultRow.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/optimizer/expr/TransformExprToSQLApplyer.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/jena/GraphD2RQ.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/examples/JenaGraphExample.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/map/ConstantValueClassMapTest.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/server/RequestParamHandler.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/algebra/JoinTest.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/functional_tests/AllTests.java
java_projects/d2rq src/d2rq/d2r_query.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/values/SQLExpressionValueMaker.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/csv/TranslationTableParserTest.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/map/ClassMap.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/expr/AllTests.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/vocab/VoID.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/expr/SQLExpression.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/algebra/CompatibleRelationGroup.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/sql/types/SQLApproximateNumeric.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/mapgen/FilterParserTest.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/expr/BooleanToIntegerCaseExpression.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/engine/AllTests.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/sql/vendor/Vendor.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/helpers/HSQLDatabase.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/sql/BeanCounter.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/map/MappingTest.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/engine/TripleRelationJoiner.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/server/ResourceServlet.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/values/ConstantValueMaker.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/vocab/D2RQ.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/sql/DatatypeTestBase.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/values/PatternTest.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/engine/TransformOpBGP.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/D2RQException.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/nodes/NodeMaker.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/ClassMapLister.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/sql/types/SQLCharacterString.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/expr/Conjunction.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/parser/URITest.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/algebra/RelationImpl.java
java_projects/d2rq src/de/fuberlin/wiwiss/d2rq/map/Configuration.java
java_projects/d2rq test/de/fuberlin/wiwiss/d2rq/values/AllTests.java
java_projects/jADT jADT-core/src/test/java/com/pogofish/jadt/printer/UserErrorPrinterTest.java
java_projects/jADT jADT-samples/src/main/java-gen/com/pogofish/jadt/samples/comments/data/CommentStyle1.java
java_projects/jADT jADT-core/src/test/java-gen/pogofish/jadt/sampleast/Function.java
java_projects/jADT jADT-core/src/main/java/com/pogofish/jadt/emitter/DataTypeEmitter.java
java_projects/jADT jADT-samples/src/main/java/com/pogofish/jadt/samples/visitor/Green.java
java_projects/jADT jADT-core/src/test/java/com/pogofish/jadt/sink/StringSinkFactoryFactoryTest.java
java_projects/jADT jADT-core/src/test/java/com/pogofish/jadt/parser/javacc/JavaCCTokenizerTest.java
java_projects/jADT jADT-core/src/main/java/com/pogofish/jadt/comments/BlockCommentParser.java
java_projects/jADT jADT-core/src/test/java-gen/pogofish/jadt/sampleast/SampleConsumer.java
java_projects/jADT jADT-core/src/main/java/com/pogofish/jadt/emitter/StandardConstructorEmitter.java
java_projects/jADT jADT-core/src/test/java/com/pogofish/jadt/checker/CheckerTest.java
java_projects/jADT jADT-core/src/test/java/com/pogofish/jadt/parser/DummyParserTest.java
java_projects/jADT jADT-core/src/main/java/com/pogofish/jadt/sink/FileSinkFactoryFactory.java
java_projects/jADT jADT-core/src/main/java/com/pogofish/jadt/sink/FileSink.java
java_projects/jADT jADT-samples/src/main/java-gen/com/pogofish/jadt/samples/ast/data/Function.java
java_projects/jADT jADT-samples/src/main/java/com/pogofish/jadt/samples/visitor/ColorEnum.java
java_projects/jADT jADT-samples/src/main/java/com/pogofish/jadt/samples/whathow/IntBinaryTreeUsage.java
java_projects/jADT jADT-core/src/main/java/com/pogofish/jadt/comments/CommentProcessor.java
java_projects/jADT jADT-core/src/test/java/com/pogofish/jadt/sink/FileSinkFactoryFactoryTest.java
java_projects/jADT jADT-core/src/main/java/com/pogofish/jadt/parser/Parser.java
java_projects/jADT jADT-samples/src/main/java-gen/com/pogofish/jadt/samples/ast/data/Arg.java
java_projects/jADT jADT-core/src/test/java/com/pogofish/jadt/parser/BaseTestParserImpl.java
java_projects/jADT jADT-samples/src/main/java/com/pogofish/jadt/samples/ast/Usage.java
java_projects/jADT jADT-core/src/main/java/com/pogofish/jadt/parser/javacc/JavaCCReader.java
java_projects/jADT jADT-samples/src/main/java-gen/com/pogofish/jadt/samples/whathow/data/Manager.java
java_projects/jADT jADT-samples/src/test/java/com/pogofish/jadt/samples/whathow/TPSReportStatusUsageTest.java
java_projects/jADT jADT-samples/src/main/java/com/pogofish/jadt/samples/whathow/TPSReportStatusUsage.java
java_projects/jADT jADT-core/src/test/java/com/pogofish/jadt/emitter/ConstructorEmitterTest.java
java_projects/jADT jADT-core/src/main/java/com/pogofish/jadt/emitter/StandardDocEmitter.java
java_projects/jADT jADT-samples/src/test/java/com/pogofish/jadt/samples/whathow/IntBinaryTreeUsageTest.java
java_projects/fasthat src/com/sun/tools/hat/internal/server/InstancesCountQuery.java
java_projects/fasthat src/com/sun/tools/hat/internal/model/JavaThing.java
java_projects/fasthat src/com/sun/tools/hat/internal/model/StackTrace.java
java_projects/fasthat src/com/sun/tools/hat/internal/model/JavaStatic.java
java_projects/fasthat src/com/sun/phobos/script/javascript/RhinoCompiledScript.java
java_projects/fasthat src/com/sun/tools/hat/internal/lang/jruby16/JRuby16.java
java_projects/fasthat src/com/sun/tools/hat/internal/model/JavaHeapObjectVisitor.java
java_projects/fasthat src/com/sun/tools/hat/internal/lang/common/HashCommon.java
java_projects/fasthat src/com/sun/tools/hat/internal/lang/guava/package-info.java
java_projects/fasthat src/com/sun/tools/hat/internal/lang/ScalarModel.java
java_projects/fasthat src/com/sun/tools/hat/internal/server/RootsQuery.java
java_projects/fasthat src/com/sun/tools/hat/internal/parser/MappedReadBuffer.java
java_projects/fasthat src/com/sun/tools/hat/internal/oql/OQLEngine.java
java_projects/fasthat src/com/sun/tools/hat/internal/parser/Reader.java
java_projects/fasthat src/com/sun/tools/hat/internal/model/JavaLong.java
java_projects/fasthat src/com/sun/tools/hat/internal/parser/ReadBuffer.java
java_projects/fasthat src/com/sun/tools/hat/internal/model/ReachableExcludesImpl.java
java_projects/fasthat src/com/sun/tools/hat/internal/model/JavaDouble.java
java_projects/fasthat src/com/sun/tools/hat/internal/lang/Models.java
java_projects/fasthat src/com/sun/phobos/script/util/ScriptEngineFactoryBase.java
java_projects/fasthat src/com/sun/tools/hat/internal/lang/jruby/package-info.java
java_projects/fasthat src/com/sun/tools/hat/internal/oql/OQLQuery.java
java_projects/fasthat src/com/sun/tools/hat/internal/lang/jruby12/JRubyObject.java
java_projects/fasthat src/com/sun/tools/hat/internal/model/JavaObject.java
java_projects/fasthat src/com/sun/tools/hat/internal/model/JavaObjectArray.java
java_projects/fasthat src/com/sun/tools/hat/internal/lang/CollectionModel.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/actions/OpenFileAction.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/graph/draw2d/LinkTooltip.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/graph/DotGraphCreator.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/menu/FileMenuManager.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/graph/draw2d/AbstractGraphTooltip.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/event/DispatcherAdapter.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/log/LogEventListener.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/widgets/CanvasScroller.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/widgets/property/PropertyList.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/graph/GraphNodeContentProvider.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/graph/FilterChainModelListener.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/graph/GraphListener.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/log/LogEventBuffer.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/widgets/OpenFileWizard.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/widgets/LogWindow.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/graph/GraphModelListener.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/actions/QuitAction.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/actions/AboutAction.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/event/EventManager.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/graph/draw2d/ConstrainedPageFlowLayout.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/widgets/property/PropertyPair.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/widgets/FileSelectionPage.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/actions/EditGraphPropertiesAction.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/graph/draw2d/Draw2dNode.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/actions/PreferenceAction.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/actions/GraphListenerAction.java
java_projects/GrandUI src/main/net/ggtools/grand/ui/log/LogEventDetailDialog.java
java_projects/oddjob test/java/org/oddjob/jmx/StatefulTest.java
java_projects/oddjob test/java/org/oddjob/state/EqualsStateTest.java
java_projects/oddjob src/java/org/oddjob/monitor/model/LogAction.java
java_projects/oddjob test/java/org/oddjob/designer/view/TextPseudoFormDummy.java
java_projects/oddjob test/java/org/oddjob/monitor/action/DesignInsideActionTest.java
java_projects/oddjob src/java/org/oddjob/jmx/server/ServerInterfaceHandler.java
java_projects/oddjob src/java/org/oddjob/jmx/client/Synchronizer.java
java_projects/oddjob test/java/org/oddjob/jmx/server/MockServerInterfaceManager.java
java_projects/oddjob src/java/org/oddjob/jmx/server/MBeanOperation.java
java_projects/oddjob src/java/org/oddjob/scheduling/OddjobServicesBean.java
java_projects/oddjob test/java/org/oddjob/jobs/job/StartJobTest.java
java_projects/oddjob test/java/org/oddjob/monitor/action/SetPropertyActionTest.java
java_projects/oddjob src/java/org/oddjob/schedules/schedules/AfterSchedule.java
java_projects/oddjob test/java/org/oddjob/jobs/CheckJobTest.java
java_projects/oddjob src/java/org/oddjob/swing/SwingInputHandler.java
java_projects/oddjob test/java/org/oddjob/designer/elements/schedule/DailyScheduleDETest.java
java_projects/oddjob test/java/org/oddjob/framework/RunnableProxyGeneratorTest.java
java_projects/oddjob src/java/org/oddjob/io/BufferType.java
java_projects/oddjob src/java/org/oddjob/state/JoinJob.java
java_projects/oddjob test/java/org/oddjob/framework/SimpleJobTest.java
java_projects/oddjob src/java/org/oddjob/designer/components/RepeatDC.java
java_projects/oddjob src/java/org/oddjob/persist/SilhouetteFactory.java
java_projects/oddjob src/java/org/oddjob/input/InputHandler.java
java_projects/oddjob test/java/org/oddjob/sql/DB2Test.java
java_projects/oddjob src/java/org/oddjob/values/properties/PropertiesJobArooa.java
java_projects/oddjob src/java/org/oddjob/jmx/VanillaInterfaceHandler.java
java_projects/oddjob src/java/org/oddjob/jmx/JMXServiceURLHelper.java
java_projects/oddjob src/java/org/oddjob/script/ScriptInvoker.java
java_projects/oddjob test/java/org/oddjob/persist/FilePersisterTest.java
java_projects/oddjob test/java/org/oddjob/MainTest.java
java_projects/oddjob src/java/org/oddjob/values/properties/PropertiesJobBase.java
java_projects/oddjob src/java/org/oddjob/logging/LogArchiver.java
java_projects/oddjob src/java/org/oddjob/monitor/action/UnloadAction.java
java_projects/oddjob test/java/org/oddjob/MockStructural.java
java_projects/oddjob src/java/org/oddjob/jmx/handlers/RunnableHandlerFactory.java
java_projects/oddjob src/java/org/oddjob/jmx/general/SimpleDomainNode.java
java_projects/oddjob tools/java/org/oddjob/doclet/PageData.java
java_projects/oddjob src/java/org/oddjob/scheduling/RunnableWrapper.java
java_projects/oddjob src/java/org/oddjob/values/properties/PropertiesTypeArooa.java
java_projects/oddjob src/java/org/oddjob/schedules/IntervalTo.java
java_projects/oddjob src/java/org/oddjob/jmx/server/AccumulatingFactoryProvider.java
java_projects/oddjob test/java/org/oddjob/tools/taglet/TagletsTest.java
java_projects/oddjob test/java/org/oddjob/monitor/action/SoftResetActionTest.java
java_projects/oddjob src/java/org/oddjob/jobs/job/StartJob.java
java_projects/oddjob test/java/org/oddjob/io/StdoutTypeTest.java
java_projects/oddjob test/java/org/oddjob/jobs/XSLTJobTest.java
java_projects/oddjob test/java/org/oddjob/describe/DescribeableDescriberTest.java
java_projects/oddjob src/java/org/oddjob/io/TeeType.java
java_projects/oddjob src/java/org/oddjob/values/properties/PropertiesDesFa.java
java_projects/oddjob src/java/org/oddjob/monitor/model/ExplorerContextImpl.java
java_projects/oddjob test/java/org/oddjob/launch/FileSpecTest.java
java_projects/oddjob test/java/org/oddjob/jobs/structural/ForEachJobTest.java
java_projects/oddjob src/java/org/oddjob/beanbus/EmptyDestination.java
java_projects/oddjob src/java/org/oddjob/input/requests/InputConfirm.java
java_projects/oddjob src/java/org/oddjob/Main.java
java_projects/oddjob src/java/org/oddjob/state/ParentState.java
java_projects/oddjob test/java/org/oddjob/tools/SomeJavaCode.java
java_projects/oddjob src/java/org/oddjob/beanbus/StageListener.java
java_projects/oddjob src/java/org/oddjob/designer/elements/schedule/MonthlyScheduleDE.java
java_projects/oddjob test/java/org/oddjob/launch/PathParserTest.java
java_projects/oddjob test/java/org/oddjob/framework/ServiceStrategiesTest.java
java_projects/oddjob src/java/org/oddjob/script/InvokeType.java
java_projects/oddjob src/java/org/oddjob/logging/log4j/ArchiveAppender.java
java_projects/oddjob src/java/org/oddjob/sql/ResultSetBeanFactory.java
java_projects/oddjob test/java/org/oddjob/sql/ConnectionTypeTest.java
java_projects/oddjob test/java/org/oddjob/jobs/structural/SequentialJobTest2.java
java_projects/oddjob src/java/org/oddjob/state/StateReflector.java
java_projects/oddjob test/java/org/oddjob/sql/GrabbingWithSQLTest.java
java_projects/oddjob src/java/org/oddjob/state/CascadeJob.java
java_projects/oddjob src/java/org/oddjob/jmx/server/LogArchiverHelper.java
java_projects/oddjob test/java/org/oddjob/logging/MockConsoleArchiver.java
java_projects/oddjob test/java/org/oddjob/designer/components/OddjobDCTest.java
java_projects/oddjob test/java/org/oddjob/jobs/WaitJobTest.java
java_projects/oddjob src/java/org/oddjob/jmx/general/DomainNode.java
java_projects/oddjob test/java/org/oddjob/io/ExistsJobTest.java
java_projects/oddjob test/java/org/oddjob/script/InvokeJobTest.java
java_projects/oddjob test/java/org/oddjob/designer/components/CopyDCTest.java
java_projects/oddjob src/java/org/oddjob/schedules/schedules/NowSchedule.java
java_projects/oddjob src/java/org/oddjob/jmx/general/SimpleMBeanNode.java
java_projects/oddjob test/java/org/oddjob/schedules/DateUtilsTest.java
java_projects/oddjob test/java/org/oddjob/designer/view/TypeSelectionDummy.java
java_projects/oddjob test/java/org/oddjob/state/JoinJobTest.java
java_projects/oddjob src/java/org/oddjob/logging/cache/AbstractArchiverCache.java
java_projects/oddjob tools/java/org/oddjob/tools/includes/PlainStreamToText.java
java_projects/oddjob test/java/org/oddjob/script/ScriptJobTest.java
java_projects/oddjob src/java/org/oddjob/schedules/schedules/TimeSchedule.java
java_projects/oddjob src/java/org/oddjob/monitor/view/JobTreeCellRenderer.java
java_projects/oddjob test/java/org/oddjob/sql/SQLJobTest.java
java_projects/oddjob test/java/org/oddjob/schedules/schedules/DailyScheduleTest.java
java_projects/oddjob test/java/org/oddjob/logging/cache/StructuralArchiverCacheTest.java
java_projects/oddjob src/java/org/oddjob/monitor/action/ForceAction.java
java_projects/oddjob src/java/org/oddjob/jobs/BeanReportJob.java
java_projects/oddjob test/java/org/oddjob/framework/BeanUtilsProviderTest.java
java_projects/oddjob test/java/org/oddjob/OddjobSerializeTest.java
java_projects/oddjob test/java/org/oddjob/schedules/schedules/DayAfterScheduleTest.java
java_projects/oddjob test/java/org/oddjob/designer/elements/FileDETest.java
java_projects/oddjob test/java/org/oddjob/ConsoleCapture.java
java_projects/oddjob test/java/org/oddjob/jobs/structural/RepeatJobTest.java
java_projects/oddjob test/java/org/oddjob/Helper.java
java_projects/oddjob test/java/org/oddjob/values/properties/PropertiesJobSystemTest.java
java_projects/oddjob src/java/org/oddjob/script/ScriptJob.java
java_projects/oddjob src/java/org/oddjob/monitor/action/LoadAction.java
java_projects/oddjob test/java/org/oddjob/scheduling/ExecutorThrottleTypeTest.java
java_projects/oddjob src/java/org/oddjob/structural/StructuralListener.java
java_projects/oddjob src/java/org/oddjob/designer/components/BaseDC.java
java_projects/oddjob test/java/org/oddjob/sql/HSQLAssumptionsTest.java
java_projects/oddjob src/java/org/oddjob/logging/LogListener.java
java_projects/oddjob src/java/org/oddjob/logging/cache/SimpleCounter.java
java_projects/oddjob src/java/org/oddjob/designer/elements/ConnectionDE.java
java_projects/oddjob src/java/org/oddjob/state/StateListener.java
java_projects/oddjob src/java/org/oddjob/scheduling/SimpleFuture.java
java_projects/oddjob test/java/org/oddjob/designer/components/ServerDCTest.java
java_projects/oddjob test/java/org/oddjob/state/ResetsTest.java
java_projects/oddjob src/java/org/oddjob/util/DefaultClock.java
java_projects/oddjob src/java/org/oddjob/util/SimpleThreadManager.java
java_projects/oddjob src/java/org/oddjob/jobs/structural/RepeatJob.java
java_projects/oddjob src/java/org/oddjob/designer/components/JustJobDC.java
java_projects/oddjob src/java/org/oddjob/jmx/handlers/DescribeableHandlerFactory.java
java_projects/oddjob test/java/org/oddjob/swing/SwingInputHandlerMain.java
java_projects/oddjob src/java/org/oddjob/OddjobRunner.java
java_projects/oddjob src/java/org/oddjob/logging/LoggingPrintStream.java
java_projects/oddjob src/java/org/oddjob/jmx/JMXServiceJob.java
java_projects/oddjob test/java/org/oddjob/jobs/structural/SequentialJobTest.java
java_projects/oddjob test/java/org/oddjob/monitor/action/UnloadActionTest.java
java_projects/oddjob test/java/org/oddjob/schedules/regression/ScheduleTester.java
java_projects/oddjob test/java/org/oddjob/framework/StructuralJobTest.java
java_projects/oddjob src/java/org/oddjob/state/AndStateOp.java
java_projects/oddjob test/java/org/oddjob/jobs/structural/JobFolderTest.java
java_projects/oddjob src/java/org/oddjob/scheduling/LoosingOutcome.java
java_projects/oddjob test/java/org/oddjob/designer/components/RootDCTest.java
java_projects/oddjob src/java/org/oddjob/schedules/Interval.java
java_projects/oddjob src/java/org/oddjob/jmx/handlers/ComponentOwnerHandlerFactory.java
java_projects/oddjob src/java/org/oddjob/monitor/action/SetPropertyAction.java
java_projects/oddjob src/java/org/oddjob/monitor/view/LogTextPanel.java
java_projects/oddjob src/java/org/oddjob/framework/BasePrimary.java
java_projects/oddjob test/oddballs/apple/src/fruit/Apple.java
java_projects/oddjob test/java/org/oddjob/ArooaDesignerMain.java
java_projects/oddjob test/java/org/oddjob/framework/StopWaitTest.java
java_projects/oddjob tools/java/org/oddjob/tools/includes/CompositeLoader.java
java_projects/oddjob src/java/org/oddjob/jobs/WaitJob.java
java_projects/oddjob src/java/org/oddjob/Iconic.java
java_projects/oddjob src/java/org/oddjob/util/ManagementDiagnostics.java
java_projects/oddjob src/java/org/oddjob/jmx/server/ServerInterfaceManager.java
java_projects/oddjob src/java/org/oddjob/oddballs/OddballFactory.java
java_projects/oddjob src/java/org/oddjob/persist/FilePersister.java
java_projects/oddjob test/java/org/oddjob/designer/view/TableWidget.java
java_projects/oddjob src/java/org/oddjob/jmx/server/JMXOperationFactory.java
java_projects/oddjob test/java/org/oddjob/values/ValueQueueServiceTest.java
java_projects/oddjob src/java/org/oddjob/script/NotPreCompiled.java
java_projects/oddjob src/java/org/oddjob/sql/ScriptParser.java
java_projects/oddjob test/java/org/oddjob/script/EchoService.java
java_projects/oddjob src/java/org/oddjob/sql/BadSQLHandler.java
java_projects/oddjob test/java/org/oddjob/jmx/server/MockServerInterfaceHandler.java
java_projects/oddjob test/java/org/oddjob/swing/ConfirmationJobMain.java
java_projects/oddjob test/java/org/oddjob/jmx/server/MockServerSideToolkit.java
java_projects/oddjob test/java/org/oddjob/OddjobTest.java
java_projects/oddjob src/java/org/oddjob/io/StdinType.java
java_projects/oddjob src/java/org/oddjob/Reserved.java
java_projects/oddjob src/java/org/oddjob/framework/PropertyChangeNotifier.java
java_projects/oddjob src/java/org/oddjob/state/ServiceState.java
java_projects/oddjob src/java/org/oddjob/designer/elements/schedule/BrokenScheduleDE.java
java_projects/oddjob test/java/org/oddjob/designer/elements/schedule/TimeScheduleDETest.java
java_projects/oddjob test/java/org/oddjob/beanbus/SimpleBusDriverTest.java
java_projects/oddjob src/java/org/oddjob/jmx/client/ClientDestroyed.java
java_projects/oddjob tools/java/org/oddjob/tools/taglet/PropertyTaglet.java
java_projects/oddjob src/java/org/oddjob/monitor/model/SelectedContextAware.java
java_projects/oddjob src/java/org/oddjob/schedules/schedules/LastSchedule.java
java_projects/oddjob test/java/org/oddjob/arooa/types/ImportTypeExamplesTest.java
java_projects/oddjob src/java/org/oddjob/monitor/MultiViewController.java
java_projects/oddjob test/java/org/oddjob/designer/components/SequentialDCTest.java
java_projects/oddjob src/java/org/oddjob/describe/UniversalDescriber.java
java_projects/oddjob src/java/org/oddjob/Stoppable.java
java_projects/oddjob src/java/org/oddjob/logging/log4j/Log4jPrintStream.java
java_projects/oddjob test/java/org/oddjob/monitor/model/ExplorerContextImplTest.java
java_projects/oddjob test/java/org/oddjob/schedules/schedules/DateScheduleTest.java
java_projects/oddjob src/java/org/oddjob/values/properties/PropertiesType.java
java_projects/oddjob src/java/org/oddjob/values/ValueQueueService.java
java_projects/oddjob test/java/org/oddjob/framework/ProxyGeneratorTest.java
java_projects/oddjob test/java/org/oddjob/values/CheckBasicSetters.java
java_projects/oddjob src/java/org/oddjob/sql/SQLJob.java
java_projects/oddjob test/java/org/oddjob/jmx/handlers/LogEnabledHandlerFactoryTest.java
java_projects/oddjob test/java/org/oddjob/schedules/schedules/MonthlyScheduleTest.java
java_projects/oddjob test/java/org/oddjob/launch/PathParserLaunchTest.java
java_projects/oddjob src/java/org/oddjob/structural/OddjobChildException.java
java_projects/oddjob src/java/org/oddjob/framework/StructuralJob.java
java_projects/oddjob test/java/org/oddjob/scheduling/DefaultOddjobServicesTest.java
java_projects/oddjob test/java/org/oddjob/OddjobLoadTest.java
java_projects/oddjob test/java/org/oddjob/jmx/handlers/ComponentOwnerHandlerFactoryTest.java
java_projects/oddjob test/java/org/oddjob/jobs/JobStateTest.java
java_projects/oddjob test/java/org/oddjob/monitor/view/ExplorerJobActionsTest.java
java_projects/oddjob test/java/org/oddjob/schedules/schedules/ParentChildScheduleTest.java
java_projects/oddjob src/java/org/oddjob/framework/SimpleJob.java
java_projects/oddjob test/java/org/oddjob/jobs/structural/ParallelJobTest.java
java_projects/oddjob src/java/org/oddjob/jmx/handlers/BeanDirectoryHandlerFactory.java
java_projects/oddjob test/java/org/oddjob/monitor/OddjobExplorerTest.java
java_projects/oddjob test/java/org/oddjob/jmx/SimpleSecurityTest.java
java_projects/oddjob src/java/org/oddjob/state/ParentStateConverter.java
java_projects/oddjob src/java/org/oddjob/persist/SerializeWithBinaryStream.java
java_projects/oddjob test/java/org/oddjob/designer/view/DummyStandardFormView.java
java_projects/oddjob test/java/org/oddjob/designer/view/DummyWidget.java
java_projects/oddjob src/java/org/oddjob/beanbus/Driver.java
java_projects/oddjob src/java/org/oddjob/schedules/AbstractSchedule.java
java_projects/oddjob src/java/org/oddjob/schedules/Schedule.java
java_projects/oddjob src/java/org/oddjob/logging/cache/LazyArchiverCache.java
java_projects/oddjob src/java/org/oddjob/jobs/structural/ParallelJob.java
java_projects/oddjob src/java/org/oddjob/framework/WrapDynaBean.java
java_projects/oddjob src/java/org/oddjob/framework/RunnableWrapper.java
java_projects/oddjob test/java/org/oddjob/jobs/job/DependsJobTest.java
java_projects/oddjob src/java/org/oddjob/jmx/server/ServerInterfaceHandlerFactory.java
java_projects/oddjob src/java/org/oddjob/persist/ArchiveBrowserJob.java
java_projects/oddjob src/java/org/oddjob/framework/Exportable.java
java_projects/oddjob test/java/org/oddjob/schedules/schedules/DailyScheduleExamplesTest.java
java_projects/oddjob test/java/org/oddjob/jmx/general/SimpleMBeanNodeTest.java
java_projects/oddjob src/java/org/oddjob/framework/Start.java
java_projects/oddjob src/java/org/oddjob/beanbus/BusEvent.java
java_projects/oddjob src/java/org/oddjob/jmx/handlers/LogPollableHandlerFactory.java
java_projects/oddjob src/java/org/oddjob/Loadable.java
java_projects/oddjob src/java/org/oddjob/io/Files.java
java_projects/oddjob test/java/org/oddjob/OddjobArooaSessionTest.java
java_projects/oddjob test/java/org/oddjob/jmx/handlers/IconicHandlerFactoryTest.java
java_projects/oddjob src/java/org/oddjob/OddjobDescriptorFactory.java
java_projects/oddjob test/java/org/oddjob/framework/BaseWrapperTest.java
java_projects/oddjob test/java/org/oddjob/examples/NonCachingPriceService.java
java_projects/oddjob src/java/org/oddjob/io/ResourceType.java
java_projects/oddjob test/java/org/oddjob/jmx/server/ObjectMBeanServerInfoTest.java
java_projects/oddjob src/java/org/oddjob/state/JobStateChanger.java
java_projects/oddjob test/java/org/oddjob/values/types/TokenizerTypeTest.java
java_projects/oddjob test/java/org/oddjob/persist/FileSilhouettesTest.java
java_projects/oddjob test/java/org/oddjob/jobs/SequenceJobTest.java
java_projects/oddjob src/java/org/oddjob/input/requests/BaseInputRequest.java
java_projects/oddjob src/java/org/oddjob/util/Clock.java
java_projects/oddjob test/java/org/oddjob/monitor/actions/ResourceActionProviderTest.java
java_projects/oddjob test/java/org/oddjob/designer/view/DummyFormView.java
java_projects/oddjob test/java/org/oddjob/jmx/handlers/DescribeableHandlerFactoryTest.java
java_projects/oddjob src/java/org/oddjob/jobs/EchoJob.java
java_projects/oddjob src/java/org/oddjob/describe/AccessorDescriber.java
java_projects/oddjob test/java/org/oddjob/arooa/types/ConvertTypeExamplesTest.java
java_projects/oddjob src/java/org/oddjob/designer/components/EchoDC.java
java_projects/oddjob src/java/org/oddjob/oddballs/OddballsDirDescriptorFactory.java
java_projects/oddjob tools/java/org/oddjob/tools/includes/JavaCodeResourceLoader.java
java_projects/oddjob test/java/org/oddjob/script/ScriptRunnerTest.java
java_projects/oddjob src/java/org/oddjob/monitor/control/PropertyChangeHelper.java
java_projects/oddjob src/java/org/oddjob/launch/Locator.java
java_projects/oddjob src/java/org/oddjob/monitor/model/ConfigContextInialiser.java
java_projects/oddjob src/java/org/oddjob/jmx/client/RemoteLogPoller.java
java_projects/oddjob src/java/org/oddjob/util/OddjobConfigException.java
java_projects/oddjob test/java/org/oddjob/persist/SilhouetteFactoryTest.java
java_projects/oddjob test/java/org/oddjob/framework/CallableProxyGeneratorTest.java
java_projects/oddjob src/java/org/oddjob/jobs/job/StopJob.java
java_projects/oddjob test/java/org/oddjob/jmx/client/RemoteLogPollerTest.java
java_projects/oddjob src/java/org/oddjob/designer/components/ResetJobDC.java
java_projects/oddjob src/java/org/oddjob/schedules/schedules/CountSchedule.java
java_projects/oddjob tools/java/org/oddjob/tools/doclet/utils/XMLResourceTagProcessor.java
java_projects/oddjob src/java/org/oddjob/framework/WrapDynaBeanOverview.java
java_projects/oddjob src/java/org/oddjob/jmx/server/SimpleServerSecurity.java
java_projects/oddjob test/java/org/oddjob/state/IfJobTest.java
java_projects/oddjob src/java/org/oddjob/values/types/TokenizerType.java
java_projects/oddjob tools/java/org/oddjob/doclet/IndexLine.java
java_projects/oddjob test/java/org/oddjob/state/FlagStateTest.java
java_projects/oddjob src/java/org/oddjob/logging/cache/LogArchiverCache.java
java_projects/oddjob test/java/org/oddjob/framework/RunnableWrapperTest.java
java_projects/oddjob src/java/org/oddjob/framework/ServiceAdaptor.java
java_projects/oddjob test/java/org/oddjob/values/properties/PropertiesEnvironmentTest.java
java_projects/oddjob src/java/org/oddjob/io/RenameJob.java
java_projects/oddjob test/java/org/oddjob/framework/ServiceProxyGeneratorTest.java
java_projects/oddjob src/java/org/oddjob/jmx/general/MBeanCacheMap.java
java_projects/oddjob test/java/org/oddjob/jmx/client/SimpleHandlerResolverTest.java
java_projects/oddjob src/java/org/oddjob/beanbus/BadBeanFilter.java
java_projects/oddjob tools/java/org/oddjob/doclet/ManualWriter.java
java_projects/oddjob src/java/org/oddjob/jobs/structural/ForEachJob.java
java_projects/oddjob test/java/org/oddjob/designer/components/DeleteDCTest.java
java_projects/oddjob src/java/org/oddjob/persist/SerializeWithBytes.java
java_projects/oddjob src/java/org/oddjob/schedules/schedules/DayAfterSchedule.java
java_projects/oddjob src/java/org/oddjob/jmx/server/OddjobJMXFileAccessController.java
java_projects/oddjob test/java/org/oddjob/jmx/general/MBeanCacheMapTest.java
java_projects/oddjob src/java/org/oddjob/OddjobServices.java
java_projects/oddjob src/java/org/oddjob/designer/elements/schedule/DayOfWeekScheduleDE.java
java_projects/oddjob src/java/org/oddjob/logging/OddjobNDC.java
java_projects/oddjob test/java/org/oddjob/jmx/handlers/StatefulHandlerFactoryTest.java
java_projects/oddjob test/java/org/oddjob/launch/ClassPathHelperTest.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/RubyCommentScanner.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/actions/FindWriteReferencesInWorkingSetAction.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/action/GenerateAccessorsAction.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/extractmethod/ExtractMethodRefactoring.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/internal/ti/CombinedTypeInferrerTest.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/Resources.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/CompletionProposalComputerRegistry.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/RubyElementAdapterFactory.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/workingsets/WorkingSetShowActionGroup.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/newsourcepage/DialogPackageExplorerActionGroup.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/template/contentassist/RubyTemplateAccess.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/util/NodeUtil.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/ti/DefaultReferenceFinder.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/pullup/UpPulledMethodsClass.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/convertlocaltofield/Messages.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/text/IColorManagerExtension.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/actions/RubyActionGroup.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/model/RubyModelProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/index/MemoryIndex.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/formatter/rewriter/MultipleAssignmentReWriteVisitor.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/breakpoints/RubyExceptionBreakpoint.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/viewsupport/ColoredRubyElementLabels.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/IMethod.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/internal/parser/warnings/RetryOutsideRescueBodyChecker.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/internal/ti/DataFlowTypeInferrerTest.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/search/SearchMatch.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/compiler/util/SimpleSet.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/search/matching/RubySearchPattern.java
java_projects/rdt tests/org.rubypeople.rdt.launching.tests/src/org/rubypeople/rdt/internal/launching/TestVMDebugger.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rubyeditor/GotoAnnotationAction.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/util/TC_NameHelper.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/preferences/EditEvaluationExpressionDialog.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/InstallStandardRubyWizard.java
java_projects/rdt plugins/org.rubypeople.rdt.astviewer/src/org/rubypeople/rdt/astviewer/views/AstUtility.java
java_projects/rdt plugins/com.aptana.rdt.ui/src/com/aptana/rdt/internal/ui/rspec/RSpecOutlinePage.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/FileTestCase.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/ImportContainer.java
java_projects/rdt tests/org.rubypeople.rdt.ui.tests/src/org/rubypeople/rdt/internal/ui/util/TwoArrayQuickSorterTest.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/launching/IRuntimeLoadpathEntryResolver.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/RubyCompletionProposal.java
java_projects/rdt plugins/org.rubypeople.rdt.testunit/src/org/rubypeople/rdt/internal/testunit/ui/OpenTestAction.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/builder/ERBBuildContext.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/typehierarchy/ToggleLinkingAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/RubyTokenCategories.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/internal/formatter/AbstractCodeFormatterTestCase.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/search/FieldDeclarationMatch.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/typehierarchy/MethodsLabelProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/internal/launching/JRubyVM.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/browsing/PatchedOpenInNewWindowAction.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/commands/GenericCommand.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/RubyType.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/ui/RubyRefactoringWizard.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rdocexport/CreateRdocActionDelegate.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/callhierarchy/CallHierarchyLabelProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/nodewrapper/AttrAccessorNodeWrapper.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/action/InlineClassAction.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/generateconstructor/ConstructorsGenerator.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/parser/warnings/Ruby19WhenStatements.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/DocumentationCommentRule.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/renameclass/ClassRenameTester.java
java_projects/rdt plugins/com.aptana.rdt.ui/src/com/aptana/rdt/internal/ui/text/correction/QuickFixProcessor.java
java_projects/rdt tests/com.aptana.rdt.tests/src/com/aptana/rdt/internal/core/parser/warnings/TC_ComparableInclusionVisitor.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/ti/util/MethodInvocationLocator.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/search/PatternStrings.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/LocalFileStorage.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/RubyIndenter.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/actions/SelectAllAction.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/internal/launching/JRubyVMRunner.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/DebuggerPreferencePage.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/formatsource/PreviewGeneratorImpl.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/dialogs/SelectionStatusDialog.java
java_projects/rdt tests/org.rubypeople.rdt.tests.all/src/org/rubypeople/rdt/tests/all/TS_RdtAllUnitTests.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/launcher/RubyArgumentsTab.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/workingsets/RemoveWorkingSetElementAction.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/compiler/parser/ScannerHelper.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/generateconstructor/Messages.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/preview/Messages.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/workingsets/WorkingSetConfigurationDialog.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/formatter/MidBlockMarker.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/TypedElementSelectionValidator.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/ti/Scope.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/action/InlineMethodAction.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/editprovider/FileMultiEditProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renamefield/RenameFieldConfig.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/text/ruby/IRubyCompletionProposal.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/pmd/RubyLanguage.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/display/DisplayView.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/core/gems/ContributedGemRegistry.java
java_projects/rdt plugins/com.aptana.rdt.ui/src/com/aptana/rdt/internal/ui/preferences/PreferenceInitializer.java
java_projects/rdt tests/org.rubypeople.rdt.debug.core.tests/src/org/rubypeople/rdt/debug/core/tests/TestRubyDebugTarget.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renameclass/ClassInstanciationFinder.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/compiler/IScanner.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/infoviews/GotoInputAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rubyeditor/OverrideIndicatorImageProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/codeassist/CompletionContext.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/core/gems/LocalFileGem.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/inlinemethod/SelectedCallFinder.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/compiler/util/SimpleSetOfCharArray.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/editprovider/IEditProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/RubyEditorAppearanceConfigurationBlock.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/editprovider/FileEditProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/action/RefactoringActionGroup.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/launching/IRuntimeLoadpathEntry2.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/action/MergeClassPartsInFileAction.java
java_projects/rdt tests/org.rubypeople.rdt.debug.core.tests/src/org/rubypeople/rdt/debug/core/tests/TestXmlStreamReader.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/CPListLabelProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/ui/pages/InlineClassPage.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/internal/core/parser/warnings/TS_InternalCoreParserWarnings.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/corext/util/MethodOverrideTester.java
java_projects/rdt tests/com.aptana.rdt.tests/src/com/aptana/rdt/internal/core/parser/warnings/DynamicVariableAliasesLocalTest.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/spelling/TaskTagDictionary.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rubyeditor/GotoMatchingBracketAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rubyeditor/RubyBreakpointMarkerUpdater.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/ExclusionInclusionEntryDialog.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/generateconstructor/GenerateConstructorRefactoring.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/AptanaProcessConsoleManager.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/actions/SelectionDispatchAction.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/internal/launching/StandardVMType.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/overridemethod/TC_OverridenMethodEditTest.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/parsing/BreakpointModificationReader.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/util/JRubyRefactoringUtils.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/callhierarchy/TreeTermination.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/FillMethodArgumentsProposal.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/corext/CorextMessages.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/preferences/RubyKeywordsPreferencePage.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/inlinemethod/ReturnStatementReplacer.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/internal/launching/VMListener.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/mergewithexternalclassparts/Messages.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/IProblem.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/actions/ValidBreakpointLocationLocator.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/ASTHolderCUInfo.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/IWorkingCopyManagerExtension.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rubyeditor/EditorUtility.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/correction/ProblemLocation.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/EditVariableEntryDialog.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/rubyeditor/ICustomRubyOutlinePage.java
java_projects/rdt plugins/org.rubypeople.rdt.testunit/src/org/rubypeople/rdt/internal/testunit/ui/ScrollLockAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/browsing/TypesContentProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/encapsulatefield/Messages.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/spelling/engine/ISpellChecker.java
java_projects/rdt tests/com.aptana.rdt.tests/src/com/aptana/rdt/internal/core/parser/warnings/TC_CodeComplexity.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/wizards/NewContainerWizardPage.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/internal/parser/warnings/MisspelledConstructorVisitor.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renamelocal/SingleLocalVariableEdit.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/CodeAssistAdvancedConfigurationBlock.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/callhierarchy/CallHierarchyFiltersActionGroup.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/debug/core/IRubyBreakpoint.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/RubyModelOperation.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/movemethod/conditionchecks/TS_MoveMethodChecks.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/console/URLConsoleLineTracker.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/internal/core/search/AllTests.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/search/MethodDeclarationMatch.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/newsourcepage/HintTextGroup.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/formatter/ProfileVersioner.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/documentprovider/StringDocumentProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/dialogfields/StringButtonDialogField.java
java_projects/rdt tests/org.rubypeople.rdt.ui.tests/src/org/rubypeople/rdt/internal/ui/search/TS_InternalUiRubySearch.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/formatsource/Messages.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/Messages.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renamemethod/RenameMethodConfig.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/internal/parser/warnings/MethodMissingWithoutRespondTo.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/search/IMatchPresentation.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/TabFolderLayout.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/callhierarchy/SearchScopeProjectAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/hover/RubyInformationProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/inlinelocal/MethodCallReplaceProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/typehierarchy/EnableMemberFilterAction.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/ui/pages/extractmethod/ParameterTextChanged.java
java_projects/rdt plugins/org.rubypeople.rdt.testunit/src/org/rubypeople/rdt/internal/testunit/ui/CompareResultsAction.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/search/RubyWorkspaceScope.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/filters/FilterDescriptor.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/viewsupport/RubyUILabelProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/ti/TypeInferenceHelper.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/nodewrapper/PartialClassNodeWrapper.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/inlinemethod/IReturnStatementReplacer.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/preferences/EvaluationExpressionsPreferencePage.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/parsing/AbstractReadStrategy.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/RubyElementProperties.java
java_projects/rdt plugins/org.rubypeople.rdt.testunit/src/org/rubypeople/rdt/internal/testunit/ui/TestUnitPreferencePage.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/launcher/RemoteRubyApplicationTabGroup.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/movemethod/InsertMethodEditProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/rubyvms/VMLibraryBlock.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/RubyDebugCommandFactory.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/action/ConvertTempToFieldAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/corext/buildpath/IPackageExplorerActionListener.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/search/FieldReferenceMatch.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/viewsupport/SourcePositionSorter.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/model/RubyVariable.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/util/TableLayoutComposite.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/editprovider/DeleteEditProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/search/CollectingSearchRequestor.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/builder/ResourceDeltaFormatter.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/TemplateCompletionProposalComputer.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/commands/ShowElementInTypeHierarchyViewHandler.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/launcher/InstallGemsJob.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/parsing/SingleReaderStrategy.java
java_projects/rdt tests/com.aptana.rdt.tests/src/com/aptana/rdt/internal/core/parser/warnings/TS_ParserWarnings.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/inlinemethod/RenameDuplicatedVariables.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/viewsupport/ProblemTreeViewer.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/newsourcepage/AddFolderToBuildpathAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/RubyElementImageDescriptor.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/core/gems/ILocalGemManager.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/filters/NonRubyProjectsFilter.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/folding/DefaultRubyFoldingStructureProvider.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/internal/core/parser/TC_TaskParser.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/RubyProjectAdapterFactory.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/inlinelocal/LocalInlinerTester.java
java_projects/rdt plugins/org.rubypeople.rdt.testunit/src/org/rubypeople/rdt/internal/testunit/ui/FailureTrace.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/actions/EvaluateAction.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/ui/ICheckboxListener.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/documentprovider/IDocumentProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/search/indexing/IndexManager.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/comment/CommentFormattingContext.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/internal/parser/warnings/ControlCouple.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/renamemethod/selection/RenameMethodSelectionTester.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/core/gems/Version.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/hierarchy/RegionBasedTypeHierarchy.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/AppearancePreferencePage.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/FileTestSuite.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/core/gems/AbstractGemManager.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/splitlocal/conditionchecks/SplitLocalConditionTester.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/ti/util/FirstPrecursorNodeLocator.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/RubyProjectWizard.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/search/indexing/ReadWriteMonitor.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/search/SearchEngine.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/CompletionProposalLabelProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renamefield/fielditems/AccessorFieldItem.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/classnodeprovider/ClassNodeProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/util/OpenTypeHierarchyUtil.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/RubyUIPreferenceInitializer.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/RubyImport.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rubyeditor/RubyDocumentProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/search/matching/PossibleMatchSet.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/util/RubyProjectSelector.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/launcher/LoadPathEntryLabelProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.astviewer/src/org/rubypeople/rdt/astviewer/preferences/PreferenceConstants.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/callhierarchy/CallHierarchyTransferDropAdapter.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/model/IRubyDebugTarget.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/internal/core/gems/RubyGemsInitializer.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/RubyDebugImages.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/corext/util/TypeFilter.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/LoadpathContainerDefaultPage.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/RubyTextMessages.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/inlinemethod/conditions/InlineMethodConditionTester.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/Member.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rubyeditor/SpecificContentAssistExecutor.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/SpellingPreferenceBlock.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/corext/template/ruby/RubyContextType.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/search/matching/MatchLocator.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/TwoLayerTreeEditProviderTester.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/ti/Variable.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/search/matching/OrLocator.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/parser/NextNodeFinder.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/corext/template/ruby/RubyScriptContext.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/AptanaRDTMessages.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/formatter/ASTBasedCodeFormatter.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renamelocal/RenameLocalConditionChecker.java
java_projects/rdt tests/org.rubypeople.eclipse.shams/src/org/rubypeople/eclipse/shams/resources/ShamContainer.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/LoadpathEntry.java
java_projects/rdt tests/com.aptana.rdt.tests/src/com/aptana/rdt/internal/core/parser/warnings/TC_SimilarVariableNameVisitor.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/overridemethod/OverrideMethodRefactoring.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/correction/AssistContext.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/internal/launching/RubiniusVM.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/text/ruby/IQuickAssistProcessor.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/evaluation/EvaluationExpression.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/actions/OccurrencesSearchGroup.java
java_projects/rdt plugins/com.aptana.rdt.ui/src/com/aptana/rdt/internal/ui/actions/CleanupGemsActionDelegate.java
java_projects/rdt tests/com.aptana.rdt.tests/src/com/aptana/rdt/internal/core/parser/warnings/WarningVisitorTest.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/action/FormatSourceAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/corext/callhierarchy/CallLocation.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/ProblemsLabelDecorator.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/compiler/CategorizedProblem.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/XMLWriter.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/overridemethod/TS_OverrideMethod.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/RubyModelException.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/HTMLTextPresenter.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/spelling/engine/RankedWordProposal.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/debug/core/model/IRubyExceptionBreakpoint.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/formatter/RubyScriptPreview.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/ToolFactory.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/NodeFactory.java
java_projects/rdt plugins/org.kxml2/samples/org/kobjects/xmlrpc/XmlRpcParser.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/convertlocaltofield/TS_LocalToField.java
java_projects/rdt plugins/org.rubypeople.rdt.testunit/src/org/rubypeople/rdt/testunit/launcher/TestUnitLaunchShortcut.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/IMember.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/RubyModel.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/ProposalInfo.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/commands/ClassicDebuggerConnection.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/actions/VariableFilterAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/filters/ImportDeclarationFilter.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/renamemodule/ModuleRenameTester.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/index/EntryResult.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/renamemethod/conditioncheck/TS_RenameMethodChecks.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/ti/TypeInferenceVisitor.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/compiler/util/HashtableOfObjectToInt.java
java_projects/rdt tests/com.aptana.rdt.tests/src/com/aptana/rdt/internal/core/gems/ShortListingGemParserTest.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/typehierarchy/HierarchyLabelProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/movemethod/ReplaceVisibilityEditProvider.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/internal/parser/warnings/RubyRedLint.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/formatter/CommentsTabPage.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/internal/core/parser/TS_InternalCoreParser.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/hover/AbstractAnnotationHover.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/ui/pages/encapsulatefield/IVisibilitySelectionListener.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/launching/AbstractRubyLaunchConfigurationDelegate.java
java_projects/rdt tests/org.rubypeople.rdt.debug.core.tests/src/org/rubypeople/rdt/debug/core/tests/FTC_Single.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/callhierarchy/SearchScopeAction.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/ICodeAssist.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/formatter/rewriter/Operators.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/filters/ClosedProjectFilter.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/actions/DeclarationsSearchGroup.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/dialogs/StatusInfo.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/internal/corext/refactoring/changes/RenameResourceChange.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/packageview/FileTransferDropAdapter.java
java_projects/rdt plugins/org.kxml2/src/org/kxml2/kdom/Node.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/internal/launching/PreferenceConstants.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/formatter/FormatterMessages.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/splitlocal/SplittedVariableRenamer.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/actions/WriteReferencesSearchGroup.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/parser/RdtWarnings.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/internal/core/parser/warnings/EmptyStatementVisitorTest.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/corext/util/ExternalFileTypeInfo.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/AbstractRubyTokenScanner.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/inlinelocal/conditionchecks/InlineLocalConditionTester.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/mergewithexternalclassparts/ClassPartTreeItem.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/workingsets/SelectWorkingSetAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/text/folding/IRubyFoldingStructureProvider.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/internal/core/parser/warnings/ConstantReassignmentVisitorTest.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/SetLoadpathOperation.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/search/GroupAction.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/parser/RubyParserWithComments.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/SharedImages.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/RubyElementDelta.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/compare/RubyTextViewerCreator.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/browsing/TopLevelTypeProblemsLabelDecorator.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renamemethod/MethodRenameEditProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/actions/ReadReferencesSearchGroup.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/formatter/CreateProfileDialog.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/actions/FindReadReferencesInProjectAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/RubyWorkbenchAdapter.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/browsing/RubyBrowsingPart.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/SmartTypingPreferencePage.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/correction/IgnoreWarningProposal.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/text/IColorManager.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/internal/parser/warnings/FeatureEnvy.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/CompletionRequestor.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/offsetprovider/MethodOffsetProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/launching/RubyRuntime.java
java_projects/rdt tests/org.rubypeople.rdt.debug.core.tests/src/org/rubypeople/rdt/debug/core/tests/FTS_Debug.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/ti/IReferenceFinder.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rubyeditor/OverrideIndicatorManager.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/launching/RubyLaunchDelegate.java
java_projects/rdt plugins/org.rubypeople.rdt.testunit/src/org/rubypeople/rdt/testunit/wizards/RubyNewTestCaseWizardPageTwo.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/inlineclass/InlineClassConditionChecker.java
java_projects/rdt tests/com.aptana.rdt.tests/src/com/aptana/rdt/internal/core/gems/AbstractGemParserTestCase.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/workingsets/WorkingSetFilterActionGroup.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/mergeclasspartsinfile/MergeClassPartInFileConfig.java
java_projects/rdt tests/org.rubypeople.rdt.debug.core.tests/src/org/rubypeople/rdt/debug/core/tests/TC_RubyDebugTarget.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/SelectionNodeProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/ILoadpathAttribute.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/search/matching/MethodPattern.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renamelocal/RenameLocalEditProvider.java
java_projects/rdt tests/org.rubypeople.rdt.debug.core.tests/src/org/rubypeople/rdt/debug/core/tests/TS_UnitTests.java
java_projects/rdt plugins/com.aptana.rdt.ui/src/com/aptana/rdt/ui/gems/GemsView.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/newsourcepage/LoadpathModifierOperation.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renamemodule/RenameModuleConfig.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/LibrariesWorkbookPage.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/nodewrapper/ModuleNodeWrapper.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/movefield/GenerateAccessorsAtTarget.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/workingsets/OpenCloseWorkingSetAction.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/search/SubTypeSearchJob.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/rubyvms/RubyVMsUpdater.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/parser/IgnoreMarkersContentHandler.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/inlineclass/InlineClassConfig.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renamefield/RenameFieldConditionChecker.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/filters/LibraryFilter.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/internal/launching/RuntimeLoadpathEntry.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/util/MementoTokenizer.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/viewsupport/IRichLabelProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/corext/buildpath/PackageExplorerActionEvent.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/launcher/RubyApplicationShortcut.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/RubyReconcilingStrategy.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/LoadpathOrderingWorkbookPage.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rdocexport/RdocListener.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/packageview/PackageExplorerLabelProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/editprovider/EditAndTreeContentProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/parser/Warning.java
java_projects/rdt plugins/org.rubypeople.rdt.testunit/src/org/rubypeople/rdt/testunit/ITestRunListener.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/commands/AbstractCommand.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/typehierarchy/ToggleOrientationAction.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/action/Messages.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/callhierarchy/ICallHierarchyViewPart.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/launcher/LoadPathContentProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/DownloadRubyWizardPage.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/ui/pages/ExtractMethodPage.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/inlineclass/InsertClassBuilder.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/RubyDynamicVar.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/extractmethod/conditionchecks/TS_ExtractMethodChecks.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/SuperModuleSelectionDialog.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/internal/core/gems/ShortListingGemParser.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/convertlocaltofield/LocalToFieldEditProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/parser/IgnoreMarker.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/internal/launching/RemoteRubyLaunchConfigurationDelegate.java
java_projects/rdt plugins/org.rubypeople.rdt.testunit/src/org/rubypeople/rdt/internal/testunit/ui/OpenEditorAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/dialogfields/TreeListDialogField.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/text/ruby/RubyCompletionProposalComputer.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/search/indexing/RemoveFolderFromindex.java
java_projects/rdt plugins/org.kxml2/src/org/kxml2/wap/WbxmlParser.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/SetFilterWizardPage.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/renamemethod/conditioncheck/RenameMethodConditionTester.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/util/FilteredList.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rubyeditor/RubyScriptAnnotationModelEvent.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/parsing/EvalReader.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/search/indexing/RemoveFromIndex.java
java_projects/rdt tests/org.rubypeople.eclipse.shams/src/org/rubypeople/eclipse/shams/resources/ShamFolder.java
java_projects/rdt plugins/org.kxml2/src/org/xmlpull/v1/XmlPullParser.java
java_projects/rdt tests/org.rubypeople.rdt.ui.tests/src/org/rubypeople/rdt/internal/ui/rubyeditor/TS_InternalUiRubyEditor.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/util/StackTraceLine.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/model/RubyStackFrame.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/internal/parser/warnings/RequireGemChecker.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/rubyvms/LibraryLabelProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/hover/RubyHoverMessages.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/ui/NewNameListener.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/util/ViewerPane.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/viewsupport/IProblemChangedListener.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/actions/FindAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/CombinedWordRule.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/text/ruby/hover/IRubyEditorTextHover.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rubyeditor/ExternalFileRubyAnnotationModel.java
java_projects/rdt plugins/com.aptana.rdt.ui/src/com/aptana/rdt/internal/ui/actions/RefreshGemsActionDelegate.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/SocketUtil.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/spelling/engine/ISpellEvent.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/IRubyModelMarker.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/convertlocaltofield/LocalToFieldConditionChecker.java
java_projects/rdt tests/org.rubypeople.rdt.debug.ui.tests/src/org/rubypeople/rdt/internal/debug/ui/launcher/TC_RubyArgumentsTab.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/parsing/MultiReaderStrategy.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/EditorInputAdapterFactory.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/formatsource/FormattedSourceEditProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/Openable.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/actions/FindReadReferencesAction.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/pushdown/MethodDownPusher.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/RubyContentAssistInvocationContext.java
java_projects/rdt tests/org.rubypeople.rdt.launching.tests/src/org/rubypeople/rdt/internal/launching/TC_RubyInterpreter.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/pushdown/DownPushedMethodsClass.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/debug/core/IRubyLineBreakpoint.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/text/RubyTextTools.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/PreferencesMessages.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/CompletionProposalCategory.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/RubySourceLocator.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/DynamicValidationStateChange.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renamefield/fielditems/FieldCallItem.java
java_projects/rdt plugins/org.kxml2/samples/Weblogs.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/typehierarchy/SortByDefiningTypeAction.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/util/Util.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/internal/core/search/MethodPatternParserTest.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/core/rspec/Behavior.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/TC_ModuleNodeProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/signatureprovider/MethodSignature.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/splitlocal/SplitLocalConfig.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/launching/IVMInstallChangedListener.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/convertlocaltofield/conditionchecks/LocalToFieldConditionTester.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/internal/core/parser/warnings/CoreClassReOpeningTest.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/debug/core/model/IRubyValue.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/compare/RubyMergeViewCreator.java
java_projects/rdt plugins/org.kxml2/src/org/xmlpull/v1/XmlSerializer.java
java_projects/rdt plugins/org.kxml2/src/org/kxml2/wap/WbxmlSerializer.java
java_projects/rdt plugins/org.rubypeople.rdt.astviewer/src/org/rubypeople/rdt/astviewer/preferences/PreferenceInitializer.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/debug/ui/InstallDeveloperToolsDialog.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/internal/core/parser/warnings/Ruby19WhenStatementsTest.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rubyeditor/DocumentAdapter.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/search/ColorDecoratingLabelProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/text/ruby/CompletionProposalCollector.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/inlineclass/ClassInliner.java
java_projects/rdt plugins/org.rubypeople.rdt.astviewer/src/org/rubypeople/rdt/astviewer/views/ViewLabelProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/search/RubyElementMatch.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/search/SearchRequestor.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/DeltaProcessor.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/documentprovider/DocumentProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/correction/QuickFixProcessor.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/classnodeprovider/TC_IncludedClassesProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ContentAssistPreference.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/search/SearchParticipantsExtensionPoint.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/dialogfields/IDialogFieldListener.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/newsourcepage/LoadpathModifierAction.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renamemodule/IncludeRenameEditProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/callhierarchy/RefreshAction.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/internal/launching/LibraryInfo.java
java_projects/rdt tests/org.rubypeople.rdt.ui.tests/src/org/rubypeople/rdt/internal/ui/util/InternalUIUtilTests.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/CreateRubyScriptOperation.java
java_projects/rdt plugins/com.aptana.rdt.rake/src/com/aptana/rdt/rake/PreferenceConstants.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/typehierarchy/FocusOnTypeAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/rubyeditor/RubyExternalEditorFactory.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/packageview/SelectionTransferDragAdapter.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/offsetprovider/OffsetProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/RubyConventions.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/hierarchy/TypeHierarchy.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/AptanaRDTPlugin.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/util/SWTUtil.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/internal/core/gems/GemOnePointTwoParser.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/TypeVector.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/internal/corext/refactoring/changes/CreateRubyScriptChange.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/formatter/NeutralMarker.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/codeassist/CodeResolver.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/IWorkingCopyManager.java
java_projects/rdt plugins/org.rubypeople.rdt.branding/src/org/rubypeople/rdt/internal/cheatsheets/webservice/CreateWsdlFileAction.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/search/matching/MatchingNodeSet.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/ui/pages/InlineLocalPage.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/RubyElementInfo.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/text/IAbstractManagedScanner.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/RdtDebugUiPlugin.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/workingsets/RubyWorkingSetUpdater.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/mergeclasspartsinfile/TS_MergeClassPartsInFile.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/launching/ITerminal.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/corext/buildpath/ILoadpathInformationProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/spelling/DefaultSpellingEngine.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/offsetprovider/AfterLastNodeInMethodOffsetProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/browsing/RubyBrowsingContentProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/internal/corext/refactoring/changes/MultiStateRubyScriptChange.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/search/matching/MethodLocator.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/workingsets/WorkingSetFilter.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/callhierarchy/CallHierarchyViewer.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/ui/pages/OverrideMethodSelectionPage.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/search/RubySearchScopeFactory.java
java_projects/rdt tests/org.rubypeople.rdt.launching.tests/src/org/rubypeople/rdt/internal/launching/TC_RunnerLaunching.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/TreeProviderTester.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/packageview/GotoResourceAction.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/model/RubyDebugElement.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/spelling/engine/ISpellEventListener.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/CodeReloadJob.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/newsourcepage/AddLibraryToBuildpathAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/dialogs/TypeInfoViewer.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/rubyvms/RubyVMMessages.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/IRubyModelStatus.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/viewsupport/MemberFilterAction.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/internal/core/TC_RubyCore.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/ruby/RubyFormattingStrategy.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/corext/buildpath/LoadpathModifier.java
java_projects/rdt plugins/org.rubypeople.rdt.launching/src/org/rubypeople/rdt/launching/StandardLoadpathProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/callhierarchy/CallHierarchyMessages.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/inlinemethod/ITargetClassFinder.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/viewsupport/SelectionProviderMediator.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/compiler/util/ObjectVector.java
java_projects/rdt plugins/com.aptana.rdt/src/com/aptana/rdt/internal/core/gems/GemManagerContentHandler.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renamefield/fielditems/ClassVarFieldItem.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/signatureprovider/IClassSignatureProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/core/pmd/Language.java
java_projects/rdt plugins/org.rubypeople.rdt.testunit/src/org/rubypeople/rdt/testunit/ITestUnitConstants.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renameclass/ConstructorCall.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/corext/util/LRUMap.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/actions/OpenTypeAction.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/ui/pages/ConverterPageParameters.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/movefield/MoveFieldConfig.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/wizards/buildpaths/newsourcepage/GenerateBuildPathActionGroup.java
java_projects/rdt plugins/org.rubypeople.rdt.testunit/src/org/rubypeople/rdt/internal/testunit/launcher/TestUnitTabGroup.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/inlinemethod/IMethodFinder.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/RubyPlugin.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/preferences/formatter/CodeFormatterConfigurationBlock.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/core/renamemethod/NodeSelector.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/nodewrapper/INodeWrapper.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/search/SearchParticipantDescriptor.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/ui/pages/FormatSourcePage.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/typehierarchy/MethodsContentProvider.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/actions/OpenTypeHierarchyAction.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.core/src/org/rubypeople/rdt/internal/debug/core/parsing/XmlStreamReaderException.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/FileTestData.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/actions/PasteAction.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/internal/ti/BasicTypeGuess.java
java_projects/rdt tests/org.rubypeople.rdt.core.tests/src/org/rubypeople/rdt/core/util/TS_CoreUtil.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/text/spelling/ChangeCaseProposal.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/ui/search/FiltersDialogAction.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/internal/corext/refactoring/changes/RubyScriptChange.java
java_projects/rdt plugins/org.rubypeople.rdt.core/src/org/rubypeople/rdt/core/search/TypeNameRequestor.java
java_projects/rdt plugins/org.rubypeople.rdt.ui/src/org/rubypeople/rdt/ui/ISharedImages.java
java_projects/rdt plugins/org.rubypeople.rdt.refactoring/src/org/rubypeople/rdt/refactoring/action/MoveFieldAction.java
java_projects/rdt plugins/org.rubypeople.rdt.debug.ui/src/org/rubypeople/rdt/internal/debug/ui/actions/ToggleBreakpointAdapter.java
java_projects/rdt tests/org.rubypeople.rdt.refactoring.tests/src/org/rubypeople/rdt/refactoring/tests/core/renamelocal/TS_RenameLocal.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/common/ExportTempTableStatus.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/semantics/type/BasicType.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/mapreduce/parallel/SlotResolver.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/flow/FlowClassEmitter.java
java_projects/asakusafw testing-project/asakusa-test-data-provider/src/test/java/com/asakusafw/testdriver/json/Simple.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/model/AstReferenceType.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/bean/ImportTargetTableBean.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/test/java/com/asakusafw/yaess/core/MockService.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/io/util/VoidInputStream.java
java_projects/asakusafw legacy-project/asakusa-test-tools/src/main/java/com/asakusafw/testtools/ColumnInfo.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/test/java/com/asakusafw/vocabulary/batch/ListBatch.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/batch/BatchCompilingEnvironment.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/operator/MasterCheckFlowFactory.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/io/TsvParser.java
java_projects/asakusafw legacy-project/asakusa-cleaner/src/main/java/com/asakusafw/cleaner/common/CleanerInitializer.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/ConditionalExpression.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/stage/CompiledShuffleFragment.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/PostfixOperator.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/operator/model/MockKeyValue1.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/ExportTempTest02ModelOutput.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/core/DataModelDefinition.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/AnnotationElementImpl.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/model/Aggregator.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/stage/FragmentConnection.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/parser/javadoc/AcceptableJavadocBlockParser.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-java/src/test/java/com/asakusafw/dmdl/java/emitter/driver/PropertyOrderDriverTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/com/asakusafw/bulkloader/common/ConfigurationLoaderTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-test-moderator/src/test/java/com/asakusafw/testdriver/bulkloader/TableOutputTest.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/test/java/com/asakusafw/windgate/file/resource/FileResourceMirrorTest.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/DocField.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/internal/parser/javadoc/ir/IrDocSimpleName.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/driver/CacheSupportEmitter.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/model/PropertyInfo.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-java/src/main/java/com/asakusafw/dmdl/java/util/NameUtil.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/main/java/com/asakusafw/windgate/file/resource/FileResourceMirror.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/util/LiteralToken.java
java_projects/asakusafw windgate-project/asakusa-windgate-stream/src/test/java/com/asakusafw/windgate/stream/StreamSourceDriverTest.java
java_projects/asakusafw core-project/asakusa-runtime/src/test/java/com/asakusafw/runtime/io/util/WritableUnionTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/processor/ExtendFlowProcessor.java
java_projects/asakusafw windgate-project/asakusa-windgate-bootstrap/src/test/java/com/asakusafw/windgate/bootstrap/CommandLineUtilTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/operator/UpdateFlow.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/testing/flow/InvalidFileNameOutputJob.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/value/DateUtil.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/GroupSortFlowProcessorTest.java
java_projects/asakusafw core-project/asakusa-runtime/src/test/java/com/asakusafw/runtime/flow/FileMapListBufferTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/flow/MasterJoinFlowSelection.java
java_projects/asakusafw testing-project/asakusa-test-driver/src/main/java/com/asakusafw/testdriver/TestExecutionPlan.java
java_projects/asakusafw windgate-project/asakusa-windgate-hadoopfs/src/main/java/com/asakusafw/windgate/hadoopfs/ssh/WindGateHadoopPut.java
java_projects/asakusafw utils-project/javadoc-parser/src/test/java/com/asakusafw/utils/java/parser/javadoc/FollowsNamedTypeBlockParserTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/mapreduce/parallel/SlotResolverTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/testing/external/Part1MockExporterDescription.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/stage/ShuffleEmiterUtil.java
java_projects/asakusafw testing-project/asakusa-test-data-provider/src/test/java/com/asakusafw/testdriver/excel/DefaultExcelRuleExtractorTest.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/ClassBody.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/semantics/trait/ReduceTerm.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-test-moderator/src/test/java/com/asakusafw/testdriver/bulkloader/CacheSupport.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/batch/package-info.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/driver/OriginalNameEmitter.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/util/CoreOperators.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/BalanceTranModelOutput.java
java_projects/asakusafw core-project/asakusa-runtime/src/test/java/com/asakusafw/runtime/value/LongOptionTest.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/operator/Empty.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/common/DBAccessUtil.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/core/ProfileContext.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/ForStatementImpl.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/internal/parser/javadoc/ir/IrDocBasicType.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/core/DifferenceSinkProvider.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/driver/OriginalNameTrait.java
java_projects/asakusafw testing-project/asakusa-test-driver/src/main/java/com/asakusafw/testdriver/OperatorTestEnvironment.java
java_projects/asakusafw testing-project/asakusa-test-driver/src/test/java/com/asakusafw/testdriver/temporary/TemporaryImporterPreparatorTest.java
java_projects/asakusafw yaess-project/asakusa-yaess-multidispatch/src/test/java/com/asakusafw/yaess/multidispatch/ExecutionScriptHandlerDispatcherTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-test-moderator/src/main/java/com/asakusafw/testdriver/bulkloader/TableInfo.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/stage/StageCompiler.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/core/CommandScriptHandler.java
java_projects/asakusafw legacy-project/asakusa-model-generator/src/main/java/com/asakusafw/modelgen/source/DatabaseSource.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/model/ExportTempPurchaseTran1Df.java
java_projects/asakusafw windgate-project/asakusa-windgate-stream/src/main/java/com/asakusafw/windgate/stream/file/FileResourceUtil.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/visualizer/VisualLabel.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/directio/AbstractDirectOutputMapper.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/test/java/com/asakusafw/yaess/basic/BasicMonitorProviderTest.java
java_projects/asakusafw windgate-project/asakusa-windgate-dmdl/src/main/java/com/asakusafw/dmdl/windgate/csv/driver/CsvLineNumberDriver.java
java_projects/asakusafw core-project/asakusa-runtime/src/test/java/com/asakusafw/runtime/io/util/ShuffleKeyTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/OperatorCompilerOptions.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/AssertStatementImpl.java
java_projects/asakusafw yaess-project/asakusa-yaess-tools/src/main/java/com/asakusafw/yaess/tools/CommandLineUtil.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/flow/package-info.java
java_projects/asakusafw windgate-project/asakusa-windgate-vocabulary/src/test/java/com/asakusafw/vocabulary/windgate/MockStreamSupport.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/test/java/com/asakusafw/yaess/basic/BasicHadoopScriptHandlerTest.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/EnumDeclaration.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/internal/parser/javadoc/ir/IrDocBlock.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-java/src/main/java/com/asakusafw/dmdl/java/emitter/CompositeDataModelDriver.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/operator/model/MockSummarized.java
java_projects/asakusafw directio-project/asakusa-directio-tools/src/test/java/com/asakusafw/directio/tools/DirectIoAbortTransactionTest.java
java_projects/asakusafw windgate-project/asakusa-windgate-dmdl/src/main/java/com/asakusafw/dmdl/windgate/csv/driver/CsvFieldTrait.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/Javadoc.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-vocabulary/src/main/java/com/asakusafw/vocabulary/bulkloader/ColumnOrder.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/bean/ImportBean.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/operator/MasterCheckFlow.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/operator/io/MockKeyOutput.java
java_projects/asakusafw testing-project/asakusa-test-data-provider/src/main/java/com/asakusafw/testdriver/excel/legacy/ConditionSheetItem.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/DataClassTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/plan/package-info.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/resource/StageResourceDriver.java
java_projects/asakusafw yaess-project/asakusa-yaess-multidispatch/src/main/java/com/asakusafw/yaess/multidispatch/package-info.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/util/tester/HadoopDriver.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/flow/SingleOutputJob.java
java_projects/asakusafw directio-project/asakusa-directio-dmdl/src/main/java/com/asakusafw/dmdl/directio/csv/driver/CsvFileNameDriver.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/CatchClauseImpl.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/FieldAccessExpression.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/io/csv/DateTimeFormatter.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/semantics/trait/NamespaceTrait.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/util/VariableTable.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/LockedTableModelInput.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/model/AstUnionExpression.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/util/AttributeUtil.java
java_projects/asakusafw legacy-project/asakusa-model-generator/src/test/java/com/asakusafw/modelgen/emitter/SummarizedModelEntityEmitterTest.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/core/FlowScript.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-java/src/main/java/com/asakusafw/dmdl/java/emitter/package-info.java
java_projects/asakusafw testing-project/asakusa-test-data-provider/src/main/java/com/asakusafw/testdriver/html/package-info.java
java_projects/asakusafw testing-project/asakusa-test-data-generator/src/main/java/com/asakusafw/testdata/generator/excel/WorkbookFormat.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-test-moderator/src/test/java/com/asakusafw/testdriver/bulkloader/H2Resource.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/input/StageInputDriver.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/model/TableModelDescription.java
java_projects/asakusafw directio-project/asakusa-directio-test-moderator/src/test/java/com/asakusafw/testdriver/directio/MockFileFormat.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/io/ExSummarizedOutput.java
java_projects/asakusafw legacy-project/asakusa-cleaner/src/main/java/com/asakusafw/cleaner/exception/CleanerSystemException.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/flow/MixedInputJob.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/importer/Importer.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/ExportTempImportTarget21DfModelOutput.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-vocabulary/src/test/java/com/asakusafw/vocabulary/bulkloader/MockDbExporterDescription.java
java_projects/asakusafw testing-project/asakusa-test-driver/src/main/java/com/asakusafw/testdriver/package-info.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/basic/YaessBasicLogger.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/operator/Confluent.java
java_projects/asakusafw windgate-project/asakusa-windgate-plugin/src/test/java/com/asakusafw/compiler/windgate/testing/io/SimpleOutput.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/common/EnumUtil.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-vocabulary/src/main/java/com/asakusafw/vocabulary/bulkloader/AttributeHelper.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/flow/FoldFlowSimple.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/FoldFlowProcessorTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-plugin/src/test/java/com/asakusafw/compiler/bulkloader/BulkLoaderIoProcessorRunTest.java
java_projects/asakusafw directio-project/asakusa-directio-test-moderator/src/test/java/com/asakusafw/testdriver/directio/ProfileContext.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/testing/flow/SingularOutputExporterDesc.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-test-moderator/src/test/java/com/asakusafw/testdriver/bulkloader/DupCheck.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/ExportTempTableModelInput.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/FlowCompilerOptions.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/input/ExtremeSplitCombiner.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-runtime/src/main/java/com/asakusafw/thundergate/runtime/cache/ThunderGateCacheSupport.java
java_projects/asakusafw utils-project/jsr269-bridge/src/main/java/com/asakusafw/utils/java/jsr269/bridge/Jsr269.java
java_projects/asakusafw distribution-project/asakusa-archetype-windgate/src/main/resources/archetype-resources/src/main/java/operator/CategorySummaryOperator.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/io/ModelOutput.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/BalanceTranModelInput.java
java_projects/asakusafw testing-project/asakusa-test-driver/src/main/java/com/asakusafw/testdriver/JobflowExecutor.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/Keyword.java
java_projects/asakusafw directio-project/asakusa-directio-dmdl/src/main/java/com/asakusafw/dmdl/directio/csv/driver/package-info.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/graph/FlowElementInput.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/operator/GroupSortFlow.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/common/ImportTableLockType.java
java_projects/asakusafw testing-project/asakusa-test-data-provider/src/main/java/com/asakusafw/testdriver/json/JsonObjectDriver.java
java_projects/asakusafw utils-project/jsr269-bridge/src/test/java/com/asakusafw/utils/java/jsr269/bridge/Callback.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/ExportTempPurchaseTran1DfModelInput.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/core/context/SimulationSupport.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/external/Ex1MockImporterDescription.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-plugin/src/main/java/com/asakusafw/compiler/bulkloader/package-info.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/testing/flow/IndependentOutExporterDesc.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/Util.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/model/ExportTempImportTarget21.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/operator/processor/FoldOperatorProcessorTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/ImportTarget2RlModelInput.java
java_projects/asakusafw windgate-project/asakusa-windgate-vocabulary/src/test/java/com/asakusafw/vocabulary/windgate/FsExporterDescriptionTest.java
java_projects/asakusafw directio-project/asakusa-directio-plugin/src/test/java/com/asakusafw/compiler/directio/testing/model/Line.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/repository/SpiWorkflowProcessorRepository.java
java_projects/asakusafw directio-project/asakusa-directio-plugin/src/test/java/com/asakusafw/compiler/directio/MockDataClass.java
java_projects/asakusafw yaess-project/asakusa-yaess-jobqueue/src/main/java/com/asakusafw/yaess/jobqueue/YaessJobQueueLogger.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/util/package-info.java
java_projects/asakusafw legacy-project/asakusa-model-generator/src/main/java/com/asakusafw/modelgen/package-info.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/model/PurchaseTran.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-runtime/src/main/java/com/asakusafw/thundergate/runtime/cache/mapreduce/CacheBuildClient.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/internal/parser/javadoc/ir/IrDocFragment.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/flow/BufferException.java
java_projects/asakusafw yaess-project/asakusa-yaess-tools/src/main/java/com/asakusafw/yaess/tools/package-info.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/JobflowCompilerTestRoot.java
java_projects/asakusafw utils-project/jsr199-testing/src/main/java/com/asakusafw/utils/java/jsr199/testing/VolatileJavaFile.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/collector/SystemOutManager.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/core/ServiceProfile.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/flow/LoggingFlowSimple.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/example/MultiPublicConstructor.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/graph/FlowElementKind.java
java_projects/asakusafw utils-project/simple-graph/src/main/java/com/asakusafw/utils/graph/HashGraph.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/io/TsvIoFactory.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/output/BridgeOutputFormat.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/LineComment.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/FlowGraphGenerator.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/operator/processor/MasterJoinOperatorProcessorTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/testing/external/Part2MockExporterDescription.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/test/java/com/asakusafw/windgate/core/GateScriptTest.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/core/ModelVerifier.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/logging/package-info.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/main/java/com/asakusafw/windgate/file/resource/LoadingObjectInputStream.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/core/Result.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/testing/io/Ex1Output.java
java_projects/asakusafw distribution-project/asakusa-archetype-windgate/src/main/resources/archetype-resources/src/main/java/jobflow/ErrorRecordToCsv.java
java_projects/asakusafw core-project/asakusa-runtime/src/test/java/com/asakusafw/runtime/value/ShortOptionTest.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/analyzer/DmdlSemanticException.java
java_projects/asakusafw directio-project/asakusa-directio-tools/src/test/java/com/asakusafw/directio/tools/DirectIoApplyTransactionTest.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/model/AbstractAstNode.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/operator/Logging.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/example/WithMissTypedOutput.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/example/CombineStage.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/parser/javadoc/JavadocParser.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/join/package-info.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/core/YaessLogger.java
java_projects/asakusafw core-project/asakusa-runtime/src/test/java/com/asakusafw/runtime/io/csv/CsvParserTest.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/io/ExJoined2Input.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/parser/javadoc/IllegalDocCommentFormatException.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/core/VerifierFactory.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/model/ModelOutputLocation.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/parser/javadoc/JavadocScanner.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/JobflowInstanceLockModelOutput.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/temporary/TemporaryStorage.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/model/ExportTempImportTarget13.java
java_projects/asakusafw yaess-project/asakusa-yaess-bootstrap/src/test/java/com/asakusafw/yaess/bootstrap/TrackingCommandScriptHandler.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-test-moderator/src/test/java/com/asakusafw/testdriver/bulkloader/TableSourceProviderTest.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/internal/parser/javadoc/ir/IrDocMethod.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/source/DatabaseSource.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/com/asakusafw/bulkloader/cache/DeleteCacheStorageLocalTest.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/model/AstRecord.java
java_projects/asakusafw legacy-project/asakusa-test-tools/src/test/java/test/modelgen/io/AllTypesWNoerrModelInput.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/flow/join/LookUpKey.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/testing/io/Part2Output.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/testing/external/Ex2MockImporterDescription.java
java_projects/asakusafw windgate-project/asakusa-windgate-bootstrap/src/main/java/com/asakusafw/windgate/bootstrap/CommandLineUtil.java
java_projects/asakusafw legacy-project/asakusa-model-generator/src/main/java/com/asakusafw/modelgen/emitter/ModelInputEmitter.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/internal/parser/javadoc/ir/IrDocField.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/flow/GroupSortFlowMin.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/semantics/trait/ProjectionsTrait.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/main/java/com/asakusafw/windgate/core/resource/ResourceMirror.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/flow/MultipleOutputJob.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/operator/GroupSortFlowFactory.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/model/AstSequenceType.java
java_projects/asakusafw legacy-project/asakusa-model-generator/src/main/java/com/asakusafw/modelgen/model/PropertyType.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/graph/FlowElementAttribute.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/OperatorProcessor.java
java_projects/asakusafw yaess-project/asakusa-yaess-bootstrap/src/test/java/com/asakusafw/yaess/bootstrap/CommandLineUtilTest.java
java_projects/asakusafw directio-project/asakusa-directio-tools/src/main/java/com/asakusafw/directio/tools/DirectIoListTransaction.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/flow/RestructureFlowSimple.java
java_projects/asakusafw windgate-project/asakusa-windgate-bootstrap/src/main/java/com/asakusafw/windgate/bootstrap/ExecutionKind.java
java_projects/asakusafw core-project/asakusa-runtime/src/test/java/com/asakusafw/runtime/io/util/WritableRawComparableTupleTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/batch/batch/Abstract.java
java_projects/asakusafw windgate-project/asakusa-windgate-dmdl/src/main/java/com/asakusafw/dmdl/windgate/csv/driver/CsvFileNameDriver.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/util/TableModelBuilder.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/directio/OutputAttemptContext.java
java_projects/asakusafw windgate-project/asakusa-windgate-dmdl/src/main/java/com/asakusafw/dmdl/windgate/csv/driver/CsvSupportDriver.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/FieldDeclaration.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/core/YaessCoreLogger.java
java_projects/asakusafw legacy-project/asakusa-test-tools/src/test/java/test/modelgen/io/BarModelOutput.java
java_projects/asakusafw yaess-project/asakusa-yaess-jobqueue/src/main/java/com/asakusafw/yaess/jobqueue/QueueHadoopScriptHandler.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/model/Util.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/source/DmdlSourceRepository.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/repository/SpiFlowElementProcessorRepository.java
java_projects/asakusafw windgate-project/asakusa-windgate-hadoopfs/src/main/java/com/asakusafw/windgate/hadoopfs/jsch/JschConnection.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/test/java/com/asakusafw/dmdl/thundergate/util/TableModelBuilderTest.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/test/java/com/asakusafw/windgate/core/GateTaskTest.java
java_projects/asakusafw core-project/asakusa-runtime/src/test/java/com/asakusafw/runtime/directio/DirectDataSourceRepositoryTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/batch/FirstJobFlow.java
java_projects/asakusafw windgate-project/asakusa-windgate-test-moderator/src/main/java/com/asakusafw/testdriver/windgate/WindGateSource.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/core/PhaseMonitor.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/plan/StageGraph.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/SingleElementAnnotation.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/emitter/package-info.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/graph/FlowElementResolver.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/testing/model/Ex2.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/main/java/com/asakusafw/windgate/core/WindGateCoreLogger.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/com/asakusafw/bulkloader/common/DBConnectionTest.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/core/ExecutionScriptHandler.java
java_projects/asakusafw legacy-project/asakusa-cleaner/src/main/java/com/asakusafw/cleaner/main/LocalFileCleaner.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/EnhancedForStatementImpl.java
java_projects/asakusafw directio-project/asakusa-directio-dmdl/src/main/java/com/asakusafw/dmdl/directio/sequencefile/driver/SequenceFileFormatDriver.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/DoStatementImpl.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/driver/OriginalNameDriver.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/view/model/Join.java
java_projects/asakusafw windgate-project/asakusa-windgate-stream/src/main/java/com/asakusafw/windgate/stream/file/FileProfile.java
java_projects/asakusafw yaess-project/asakusa-yaess-jsch/src/main/java/com/asakusafw/yaess/jsch/YaessJschLogger.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/util/ModelBuilder.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/model/ExportTempImportTarget11Df.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/core/util/PropertiesUtil.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/main/java/com/asakusafw/windgate/core/ProcessScript.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-plugin/src/test/java/com/asakusafw/compiler/bulkloader/testing/io/CachedOutput.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/common/JavaName.java
java_projects/asakusafw windgate-project/asakusa-windgate-plugin/src/test/java/com/asakusafw/compiler/windgate/testing/io/PairOutput.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/FlowCompiler.java
java_projects/asakusafw distribution-project/asakusa-archetype-directio/src/main/resources/archetype-resources/src/main/java/jobflow/SalesDetailFromCsv.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/SwitchLabel.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/stage/ShuffleKeyEmitterTest.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/semantics/ModelSymbol.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/model/LiteralKind.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/util/Emitter.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/core/SpiDataModelSourceProvider.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/SwitchStatementImpl.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/source/DmdlSourceFile.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/model/ModelRef.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/processor/FoldOperatorProcessor.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/mapreduce/parallel/ParallelSortClientEmitter.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/model/ImportTarget2.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/model/AstJoin.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/DataClass.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/test/java/com/asakusafw/vocabulary/batch/GraphBatch.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/emitter/JoinedModelGenerator.java
java_projects/asakusafw dsl-project/ashigel-compiler-bootstrap/src/main/java/com/asakusafw/compiler/bootstrap/OperatorCompilerDriver.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/ClassBodyImpl.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/semantics/type/package-info.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-plugin/src/test/java/com/asakusafw/compiler/bulkloader/testing/io/MockTableModelInput.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/util/hadoop/package-info.java
java_projects/asakusafw testing-project/asakusa-test-data-provider/src/main/java/com/asakusafw/testdriver/excel/legacy/RowMatchingCondition.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/ImportTarget1ErrorModelOutput.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/LabeledStatementImpl.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/main/java/com/asakusafw/windgate/core/package-info.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/flow/join/LookUpException.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/model/AstSimpleName.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/StageOutput.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/hadoop/package-info.java
java_projects/asakusafw testing-project/asakusa-test-data-generator/src/main/java/com/asakusafw/testdata/generator/excel/SheetBuilder.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/external/package-info.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/model/AstAttributeValueArray.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/DoStatement.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/testing/flow/DuplicateFragments.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/model/Part2.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/processor/SplitFlowProcessor.java
java_projects/asakusafw testing-project/asakusa-test-driver/src/main/java/com/asakusafw/testdriver/BatchTester.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-java/src/test/java/com/asakusafw/dmdl/java/emitter/driver/ObjectDriverTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/cache/LocalCacheInfoRepository.java
java_projects/asakusafw utils-project/javadoc-parser/src/test/java/com/asakusafw/utils/java/parser/javadoc/MockJavadocBlockParser.java
java_projects/asakusafw testing-project/asakusa-test-data-provider/src/main/java/com/asakusafw/testdriver/excel/ValueConditionKind.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/main/java/com/asakusafw/windgate/core/resource/DrainDriver.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/graph/PortConnection.java
java_projects/asakusafw legacy-project/asakusa-test-tools/src/test/java/test/modelgen/model/Foo.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/operator/io/MockKeyInput.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/core/BatchRuntime.java
java_projects/asakusafw windgate-project/asakusa-windgate-hadoopfs/src/main/java/com/asakusafw/windgate/hadoopfs/jsch/package-info.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/main/java/com/asakusafw/windgate/core/session/SessionProvider.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/test/java/com/asakusafw/testdriver/testing/model/Ordered.java
java_projects/asakusafw legacy-project/asakusa-test-tools/src/test/java/com/asakusafw/testtools/inspect/CauseTest.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/graph/OutputDescription.java
java_projects/asakusafw legacy-project/asakusa-cleaner/src/main/java/com/asakusafw/cleaner/bean/DFSCleanerBean.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/parser/javadoc/FollowsNamedTypeBlockParser.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/RestructureFlowProcessorTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/operator/Callback.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/main/java/com/asakusafw/windgate/core/util/package-info.java
java_projects/asakusafw yaess-project/asakusa-yaess-bootstrap/src/test/java/com/asakusafw/yaess/bootstrap/ExecutionTracker.java
java_projects/asakusafw yaess-project/asakusa-yaess-plugin/src/test/java/com/asakusafw/compiler/yaess/testing/flow/LastFlow.java
java_projects/asakusafw utils-project/jsr269-bridge/src/test/java/com/asakusafw/utils/java/jsr269/bridge/Jsr269Test.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/testing/DirectFlowCompilerTest.java
java_projects/asakusafw testing-project/asakusa-test-driver/src/test/java/com/asakusafw/testdriver/testing/batch/SimpleBatch.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/operator/ExtractFlow.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/rule/package-info.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/test/java/com/asakusafw/windgate/core/process/MockDriverFactory.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/MethodOrConstructorDeclaration.java
java_projects/asakusafw directio-project/asakusa-directio-plugin/src/test/java/com/asakusafw/compiler/directio/DirectFileIoProcessorTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/cache/BuildCache.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/operator/LoggingFlowFactory.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/semantics/Trait.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-java/src/main/java/com/asakusafw/dmdl/java/emitter/driver/JoinDriver.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/model/TempImportTarget1Df.java
java_projects/asakusafw legacy-project/asakusa-cleaner/src/main/java/com/asakusafw/cleaner/bean/LocalFileCleanerBean.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-plugin/src/test/java/com/asakusafw/compiler/bulkloader/testing/io/MockUnionModelOutput.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/common/FileNameUtil.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-vocabulary/src/test/java/com/asakusafw/vocabulary/bulkloader/InvalidDbExporterDescription.java
java_projects/asakusafw yaess-project/asakusa-yaess-flowlog/src/main/java/com/asakusafw/yaess/flowlog/FlowLogger.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/model/AstNode.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/io/util/VoidOutputStream.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/util/hadoop/ConfigurationProvider.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/model/PurchaseTranError.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-java/src/test/java/com/asakusafw/dmdl/java/emitter/driver/SummarizeDriverTest.java
java_projects/asakusafw dsl-project/ashigel-compiler-bootstrap/src/main/java/com/asakusafw/compiler/bootstrap/package-info.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/core/ExecutionContext.java
java_projects/asakusafw yaess-project/asakusa-yaess-jsch/src/test/java/com/asakusafw/yaess/jsch/SshHadoopScriptHandlerTest.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/test/java/com/asakusafw/testdriver/core/MockExporterRetriever.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/SynchronizedStatementImpl.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/test/java/com/asakusafw/vocabulary/flow/util/CoreOperatorFactoryTest.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/value/LongOption.java
java_projects/asakusafw windgate-project/asakusa-windgate-jdbc/src/test/java/com/asakusafw/windgate/jdbc/Pair.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/core/PropertyType.java
java_projects/asakusafw testing-project/asakusa-test-data-generator/src/main/java/com/asakusafw/testdata/generator/GenerateTask.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/TempImportTarget2ModelInput.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/CacheFilesModelInput.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/batch/batch/NoEmptyParameterConstructor.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/InitializerDeclarationImpl.java
java_projects/asakusafw legacy-project/asakusa-test-tools/src/test/java/test/modelgen/dummy/model/Bar.java
java_projects/asakusafw windgate-project/asakusa-windgate-vocabulary/src/test/java/com/asakusafw/vocabulary/windgate/MockJdbcSupport.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/model/DataModelClass.java
java_projects/asakusafw legacy-project/asakusa-fileio-test-moderator/src/main/java/com/asakusafw/testdriver/file/FileImporterPreparator.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/test/java/com/asakusafw/yaess/core/PrivateService.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/ExtractFlowProcessorTest.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/SwitchDefaultLabelImpl.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/semantics/PropertySymbol.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/cache/GetCacheInfoLocal.java
java_projects/asakusafw yaess-project/asakusa-yaess-multidispatch/src/main/java/com/asakusafw/yaess/multidispatch/HadoopScriptHandlerDispatcher.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/operator/CoGroupFlowFactory.java
java_projects/asakusafw testing-project/asakusa-test-data-provider/src/main/java/com/asakusafw/testdriver/excel/ExcelRuleExtractor.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/analyzer/driver/package-info.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/io/util/WritableRawComparable.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/common/ImportTableLockedOperation.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/batch/WorkflowProcessor.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/operator/io/MockFooOutput.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/flow/CoGroupFlowOp2.java
java_projects/asakusafw testing-project/asakusa-test-data-provider/src/main/java/com/asakusafw/testdriver/excel/legacy/package-info.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/io/util/Union.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/example/AbstractJobFlow.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/spi/TypeDriver.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/core/context/RuntimeContext.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/flow/SummarizeFlowRenameKey.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/join/JoinRewriter.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/main/java/com/asakusafw/windgate/file/resource/FileResourceProvider.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-java/src/test/java/com/asakusafw/dmdl/java/emitter/HelloDriver.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/collector/package-info.java
java_projects/asakusafw legacy-project/asakusa-model-generator/src/main/java/com/asakusafw/modelgen/emitter/JoinedModelEntityEmitter.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/PostfixExpressionImpl.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/testing/DirectFlowCompiler.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/view/model/CreateView.java
java_projects/asakusafw utils-project/jsr199-testing/src/main/java/com/asakusafw/utils/java/jsr199/testing/DirectClassLoader.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/output/TemporaryOutputFormat.java
java_projects/asakusafw core-project/asakusa-runtime/src/test/java/com/asakusafw/runtime/value/DoubleOptionTest.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/test/java/com/asakusafw/testdriver/testing/io/OrderedOutput.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/operator/MockHelper.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/example/InvalidTypeExporterDescription.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/operator/MasterJoinUpdateFlow.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/visualizer/VisualGraphEmitter.java
java_projects/asakusafw yaess-project/asakusa-yaess-plugin/src/test/java/com/asakusafw/compiler/yaess/testing/batch/DiamondBatch.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/internal/parser/javadoc/ir/IrDocMethodParameter.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/example/InvalidTypeImporterDescription.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/main/java/com/asakusafw/windgate/core/BaseProvider.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/processor/SummarizeOperatorProcessor.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/operator/io/MockKeyValue1Input.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/JavadocImpl.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/BaseStageClient.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/SplitFlowProcessorTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/model/ModelReference.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/test/java/com/asakusafw/dmdl/parser/DmdlParserTest.java
java_projects/asakusafw windgate-project/asakusa-windgate-jdbc/src/main/java/com/asakusafw/windgate/jdbc/JdbcResourceMirror.java
java_projects/asakusafw distribution-project/asakusa-archetype-windgate/src/main/resources/archetype-resources/src/test/java/jobflow/CategorySummaryJobTest.java
java_projects/asakusafw directio-project/asakusa-directio-tools/src/main/java/com/asakusafw/directio/tools/DirectIoDelete.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/IfStatementImpl.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/processor/BranchOperatorProcessor.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/join/JoinResourceDescription.java
java_projects/asakusafw directio-project/asakusa-directio-plugin/src/main/java/com/asakusafw/compiler/directio/emitter/Slot.java
java_projects/asakusafw testing-project/asakusa-test-driver/src/main/java/com/asakusafw/testdriver/TestDriverBase.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/FlowCompilerTest.java
java_projects/asakusafw yaess-project/asakusa-yaess-plugin/src/test/java/com/asakusafw/compiler/yaess/testing/io/DummyInput.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/main/java/com/asakusafw/windgate/core/resource/ResourceProfile.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/rule/Not.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/testing/external/Ex2MockExporterDescription.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/graph/ShuffleKey.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/DocMethod.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/model/AstModelMapping.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/test/java/com/asakusafw/vocabulary/batch/JobFlow2.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/parser/javadoc/package-info.java
java_projects/asakusafw dsl-project/ashigel-compiler-bootstrap/src/main/java/com/asakusafw/compiler/bootstrap/BatchCompilerDriver.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/parser/DmdlParser.java
java_projects/asakusafw directio-project/asakusa-directio-plugin/src/test/java/com/asakusafw/compiler/directio/DirectFileIoProcessorRunTest.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/ParameterizedTypeImpl.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/flow/SummarizeFlowTrivial.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/rule/DecimalRange.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/operator/ExOperatorImpl.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/testing/model/Part2.java
java_projects/asakusafw yaess-project/asakusa-yaess-plugin/src/test/java/com/asakusafw/compiler/yaess/testing/mock/MockImporterDescription.java
java_projects/asakusafw legacy-project/asakusa-model-generator/src/main/java/com/asakusafw/modelgen/model/ModelRepository.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-plugin/src/test/java/com/asakusafw/compiler/bulkloader/testing/model/Ex2.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/util/GeneratorUtil.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/parser/javadoc/JavadocBlockInfo.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/ConstructorDeclaration.java
java_projects/asakusafw core-project/asakusa-runtime/src/test/java/com/asakusafw/runtime/directio/keepalive/KeepAliveDataSourceTest.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-java/src/main/java/com/asakusafw/dmdl/java/Main.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/extractor/DfsFileImport.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/test/java/com/asakusafw/yaess/core/FlowScriptTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/batch/BatchCompiler.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/test/java/com/asakusafw/dmdl/thundergate/emitter/ThunderGateModelEmitterTest.java
java_projects/asakusafw core-project/asakusa-runtime/src/test/java/com/asakusafw/runtime/directio/hadoop/HadoopDataSourceCoreTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-test-moderator/src/test/java/com/asakusafw/testdriver/bulkloader/BulkLoadImporterPreparatorTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/join/operator/SideDataJoinUpdate.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/ClassInstanceCreationExpressionImpl.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/AlternateConstructorInvocationImpl.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/example/WithImportOutput.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/FlowElementProcessor.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/semantics/ModelDeclaration.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/internal/parser/javadoc/ir/IrDocText.java
java_projects/asakusafw utils-project/jsr199-testing/src/main/java/com/asakusafw/utils/java/jsr199/testing/VolatileClassFile.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/test/java/com/asakusafw/vocabulary/batch/BatchDescriptionTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/driver/PrimaryKeyTrait.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/operator/io/MockKeyValue2Output.java
java_projects/asakusafw windgate-project/asakusa-windgate-plugin/src/test/java/com/asakusafw/compiler/windgate/testing/io/SimpleInput.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/flow/Out3ExporterDesc.java
java_projects/asakusafw utils-project/simple-graph/src/test/java/com/asakusafw/utils/graph/GraphsTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/com/asakusafw/bulkloader/cache/GcCacheStorageTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/ExportTempImportTarget11ModelInput.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/operator/FoldFlowFactory.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/example/NoImportInput.java
java_projects/asakusafw legacy-project/asakusa-test-tools/src/main/java/com/asakusafw/testtools/inspect/AbstractInspector.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/directio/DirectDataSourceRepository.java
java_projects/asakusafw windgate-project/asakusa-windgate-hadoopfs/src/test/java/com/asakusafw/windgate/hadoopfs/HadoopFsMirrorTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/mapreduce/parallel/ParallelSortClientEmitterTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/operator/model/MockFoo.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/operator/ExOperatorFactory.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/com/asakusafw/bulkloader/exporter/LockReleaseTest.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/core/ResourceConfiguration.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/directio/hadoop/HadoopFileFormatAdapter.java
java_projects/asakusafw yaess-project/asakusa-yaess-jsch/src/test/java/com/asakusafw/yaess/jsch/JschProcessExecutorTest.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/parser/package-info.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/LocalClassDeclaration.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/testing/model/Ex1.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-test-moderator/src/main/java/com/asakusafw/testdriver/bulkloader/package-info.java
java_projects/asakusafw windgate-project/asakusa-windgate-plugin/src/test/java/com/asakusafw/compiler/windgate/WindGateIoProcessorTest.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/flow/TinyInputJob.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/stage/MapFragmentEmitterTest.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/io/Ex1Input.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/flow/ArrayListBuffer.java
java_projects/asakusafw legacy-project/asakusa-model-generator/src/test/java/com/asakusafw/modelgen/view/ViewParserTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/example/WithInvalidOutputName.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/internal/parser/javadoc/ir/IrDocNamedType.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/io/ExJoinedOutput.java
java_projects/asakusafw legacy-project/asakusa-model-generator/src/main/java/com/asakusafw/modelgen/util/package-info.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/SingleElementAnnotationImpl.java
java_projects/asakusafw legacy-project/asakusa-model-generator/src/main/java/com/asakusafw/modelgen/model/DecimalType.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/OperatorMethodDescriptor.java
java_projects/asakusafw yaess-project/asakusa-yaess-multidispatch/src/test/java/com/asakusafw/yaess/multidispatch/FailCommandScriptHandler.java
java_projects/asakusafw testing-project/asakusa-test-driver/src/test/java/com/asakusafw/testdriver/IdentityVerifier.java
java_projects/asakusafw testing-project/asakusa-test-data-provider/src/main/java/com/asakusafw/testdriver/json/JsonDataModelSource.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/directio/hadoop/HadoopDataSourceUtil.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/driver/CacheSupportTrait.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/SynchronizedStatement.java
java_projects/asakusafw windgate-project/asakusa-windgate-hadoopfs/src/main/java/com/asakusafw/windgate/hadoopfs/HadoopFsProfile.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/testing/io/ExSummarized2Input.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/util/ModelEmitter.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/graph/FlowElementOutput.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/test/java/com/asakusafw/testdriver/testing/io/VarietyInput.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/example/TopLevelJobFlow.java
java_projects/asakusafw testing-project/asakusa-test-data-provider/src/main/java/com/asakusafw/testdriver/excel/Util.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/collector/Collector.java
java_projects/asakusafw windgate-project/asakusa-windgate-jdbc/src/test/java/com/asakusafw/windgate/jdbc/JdbcSourceDriverTest.java
java_projects/asakusafw testing-project/asakusa-test-data-provider/src/test/java/com/asakusafw/testdriver/excel/legacy/LegacyExcelRuleExtractorTest.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/model/ExJoined2.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/PurchaseTranModelInput.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/test/java/com/asakusafw/testdriver/testing/model/Naming.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/processor/ConvertOperatorProcessor.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/ModelRoot.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/collector/SlotDistributor.java
java_projects/asakusafw testing-project/asakusa-test-data-provider/src/main/java/com/asakusafw/testdriver/json/JsonSourceProvider.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-vocabulary/src/test/java/com/asakusafw/vocabulary/bulkloader/NoTableModel.java
java_projects/asakusafw windgate-project/asakusa-windgate-jdbc/src/main/java/com/asakusafw/windgate/jdbc/JdbcProfile.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/DeductionTranModelInput.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/graph/FlowElementPort.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/ArrayCreationExpressionImpl.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/ExecutableAnalyzer.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/batch/Work.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/join/processor/SideDataJoinUpdateFlowProcessor.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/directio/package-info.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/operator/CoGroupFlow.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/ThrowStatement.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/common/MultiThreadedCopier.java
java_projects/asakusafw legacy-project/asakusa-test-tools/src/main/java/com/asakusafw/testtools/Constants.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/package-info.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/value/DoubleOption.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/bean/ExportTargetTableBean.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/tools/DBCleaner.java
java_projects/asakusafw directio-project/asakusa-directio-tools/src/main/java/com/asakusafw/directio/tools/DirectIoApplyTransaction.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/InitializerDeclaration.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/CatchClause.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/Import.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/importer/package-info.java
java_projects/asakusafw windgate-project/asakusa-windgate-dmdl/src/main/java/com/asakusafw/dmdl/windgate/csv/driver/CsvSupportTrait.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/ModifierImpl.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/jobflow/JobflowAnalyzer.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/CacheFilesModelOutput.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/join/operator/SideDataJoin.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/rule/PropertyCondition.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/test/java/com/asakusafw/dmdl/thundergate/emitter/RecordModelGeneratorTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/join/JoinResourceEmitter.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/ImportTarget2ErrorModelInput.java
java_projects/asakusafw windgate-project/asakusa-windgate-dmdl/src/main/java/com/asakusafw/dmdl/windgate/jdbc/driver/ColumnDriver.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/operator/Convert.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-java/src/main/java/com/asakusafw/dmdl/java/emitter/driver/WritableDriver.java
java_projects/asakusafw yaess-project/asakusa-yaess-jsch/src/main/java/com/asakusafw/yaess/jsch/SshHadoopScriptHandler.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/semantics/trait/MappingFactor.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/ExportTempTableModelOutput.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/core/package-info.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/ExportTempPurchaseTran1ModelOutput.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/directio/FilePattern.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/BranchStatement.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-vocabulary/src/main/java/com/asakusafw/vocabulary/bulkloader/DupCheckDbExporterDescription.java
java_projects/asakusafw legacy-project/asakusa-model-generator/src/test/java/com/asakusafw/modelgen/emitter/JavaNameTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/batch/BatchClass.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/value/DateTimeOption.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/io/util/WritableRawComparator.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/test/java/com/asakusafw/dmdl/thundergate/MainTest.java
java_projects/asakusafw yaess-project/asakusa-yaess-plugin/src/test/java/com/asakusafw/compiler/yaess/testing/mock/MockIoDescriptionProcessor.java
java_projects/asakusafw legacy-project/asakusa-cleaner/src/main/java/com/asakusafw/cleaner/log/LogMessageManager.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/model/DecimalType.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/batch/JoinJobFlow.java
java_projects/asakusafw legacy-project/asakusa-fileio-plugin/src/test/java/com/asakusafw/compiler/fileio/flow/IndependentOutExporterDesc.java
java_projects/asakusafw legacy-project/asakusa-cleaner/src/main/java/com/asakusafw/cleaner/log/package-info.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/LineCommentImpl.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/test/java/com/asakusafw/testdriver/core/SpiExporterRetrieverTest.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/basic/BasicCommandScriptHandler.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/com/asakusafw/bulkloader/exporter/ExportFileReceiveTest.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/ClassInstanceCreationExpression.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/model/AstDescription.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-vocabulary/src/test/java/com/asakusafw/vocabulary/bulkloader/DupCheckDbExporterDescriptionTest.java
java_projects/asakusafw windgate-project/asakusa-windgate-dmdl/src/main/java/com/asakusafw/dmdl/windgate/csv/driver/package-info.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/batch/processor/ScriptWorkDescriptionProcessor.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/test/java/com/asakusafw/yaess/core/task/ExecutionTaskTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/bean/ExportTempTableBean.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/testing/TemporaryInputDescription.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/directio/ResourceInfo.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/util/CoreOperatorFactory.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-java/src/test/java/com/asakusafw/dmdl/java/GeneratorTesterRoot.java
java_projects/asakusafw directio-project/asakusa-directio-test-moderator/src/test/java/com/asakusafw/testdriver/directio/MockStreamFormat.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/test/java/com/asakusafw/dmdl/thundergate/util/JoinedModelBuilderTest.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/basic/ExitCodeException.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/AbstractOperatorProcessor.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/main/java/com/asakusafw/dmdl/spi/ModelAttributeDriver.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/operator/MasterCheckFlowImpl.java
java_projects/asakusafw utils-project/jsr199-testing/src/test/java/com/asakusafw/utils/java/jsr199/testing/MockProcessor.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/testing/external/Ex1MockExporterDescription.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/output/LegacyBridgeOutputCommitter.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/util/package-info.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/view/model/Select.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/test/java/test/modelgen/table/io/LockedTableModelOutput.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/value/package-info.java
java_projects/asakusafw dmdl-project/asakusa-dmdl-core/src/test/java/com/asakusafw/dmdl/analyzer/DmdlAnalyzerTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/operator/processor/GroupSortOperatorProcessorTest.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/core/Difference.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/core/SpiVerifyRuleProvider.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/example/VolatileStage.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/flow/ConvertFlowSimple.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/OperatorClassCollector.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/common/StreamRedirectThread.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-test-moderator/src/test/java/com/asakusafw/testdriver/bulkloader/ConfigurationTest.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-vocabulary/src/test/java/com/asakusafw/vocabulary/bulkloader/MockDupCheckDbExporterDescription.java
java_projects/asakusafw windgate-project/asakusa-windgate-core/src/test/java/com/asakusafw/windgate/file/session/FileSessionProviderTest.java
java_projects/asakusafw legacy-project/asakusa-model-generator/src/main/java/com/asakusafw/modelgen/view/ViewAnalyzer.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-test-moderator/src/main/java/com/asakusafw/testdriver/bulkloader/Configuration.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/flow/ExtendFlowInvalid.java
java_projects/asakusafw yaess-project/asakusa-yaess-bootstrap/src/test/java/com/asakusafw/yaess/bootstrap/SerialExecutionTracker.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/IfStatement.java
java_projects/asakusafw distribution-project/asakusa-archetype-thundergate/src/main/resources/archetype-resources/src/main/java/jobflow/SalesDetailFromJdbc.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/EnhancedForStatement.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-plugin/src/test/java/com/asakusafw/compiler/bulkloader/testing/model/Ex1.java
java_projects/asakusafw yaess-project/asakusa-yaess-jobqueue/src/test/java/com/asakusafw/yaess/jobqueue/QueueHadoopScriptHandlerTest.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/internal/model/syntax/AnnotationElementDeclarationImpl.java
java_projects/asakusafw utils-project/javadoc-parser/src/main/java/com/asakusafw/utils/java/parser/javadoc/SerialFieldBlockParser.java
java_projects/asakusafw windgate-project/asakusa-windgate-stream/src/main/java/com/asakusafw/windgate/stream/CountingInputStream.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/test/java/com/asakusafw/yaess/basic/BasicCommandScriptHandlerTest.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/Literal.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/flow/mapreduce/parallel/ResolvedSlot.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/test/java/com/asakusafw/vocabulary/batch/InvalidNameBatch.java
java_projects/asakusafw yaess-project/asakusa-yaess-plugin/src/test/java/com/asakusafw/compiler/yaess/testing/model/Dummy.java
java_projects/asakusafw legacy-project/asakusa-test-tools/src/main/java/com/asakusafw/testtools/RowMatchingCondition.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/output/StageOutputFormat.java
java_projects/asakusafw legacy-project/asakusa-test-tools/src/test/java/com/asakusafw/testtools/inspect/DefaultInspectorTest.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/main/java/com/asakusafw/compiler/operator/flow/FlowPartClass.java
java_projects/asakusafw legacy-project/asakusa-test-tools/src/main/java/com/asakusafw/testtools/ColumnMatchingCondition.java
java_projects/asakusafw windgate-project/asakusa-windgate-jdbc/src/main/java/com/asakusafw/windgate/jdbc/JdbcLogger.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/core/DataModelSource.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/NormalAnnotation.java
java_projects/asakusafw yaess-project/asakusa-yaess-core/src/main/java/com/asakusafw/yaess/core/Job.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/graph/PortDirection.java
java_projects/asakusafw utils-project/java-dom/src/main/java/com/asakusafw/utils/java/model/syntax/VariableDeclarator.java
java_projects/asakusafw dsl-project/asakusa-dsl-vocabulary/src/main/java/com/asakusafw/vocabulary/flow/graph/FlowElementAttributeProvider.java
java_projects/asakusafw dsl-project/ashigel-compiler/src/test/java/com/asakusafw/compiler/flow/processor/operator/MasterJoinUpdateFlowImpl.java
java_projects/asakusafw thundergate-project/asakusa-thundergate-dmdl/src/main/java/com/asakusafw/dmdl/thundergate/driver/PrimaryKeyDriver.java
java_projects/asakusafw thundergate-project/asakusa-thundergate/src/main/java/com/asakusafw/bulkloader/importer/ImportFileDelete.java
java_projects/asakusafw windgate-project/asakusa-windgate-plugin/src/test/java/com/asakusafw/compiler/windgate/DualIdentityFlow.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/rule/IsNull.java
java_projects/asakusafw core-project/asakusa-runtime/src/main/java/com/asakusafw/runtime/stage/collector/package-info.java
java_projects/asakusafw testing-project/asakusa-test-moderator/src/main/java/com/asakusafw/testdriver/model/DefaultDataModelDefinition.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/scanner/SVCharacter.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBExpr.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/SVDBStringDocumentIndex.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/open_decl/TestOpenClass.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/wizards/templates/SVTemplateWizard.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBTypeInfoFwdDecl.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBTypeExpr.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVTaskFuncBodyParser.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/log/ILogLevel.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/svcp/SVDBFileDecorator.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/indent/TestAdaptiveIndent.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/wizards/BrowseClasses.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/templates/SVTUtils.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/editor/actions/SelectionConverter.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/persistence/DBWriteException.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/parser/TestParseSpecify.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/search/ISVDBPreProcIndexSearcher.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/indent/SVIndentLoopStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/index/cache/IndexCacheTests.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/diagrams/DiagModel.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/templates/TemplateCategory.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/stmt/SVDBAssignStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBModportTFPortsDecl.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/prop_pages/ISVProjectPropsPage.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/content_assist/TestContentAssistTypes.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVPropertyExprParser.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/index/TestOpencoresProjects.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBCoverpointBins.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBModportSimplePortsDecl.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/SVDBIndexRegistry.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/search/SVSearchResultsPage.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBItemType.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/evaluator/SVEvalGlobalContext.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/text/TagProcessor.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/persistence/JITPersistenceDelegateBase.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.doc.user/src/net/sf/sveditor/doc/user/tasks/AssembleTocTask.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/prop_pages/ProjectDirectoryDialog.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/SVDBFileOverrideIndex.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/parser/TestParseModuleBodyItems.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/editor/SVActionContributor.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/stmt/SVDBAssumeStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/SVDBIndexListIterator.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/search/SVSearchTableLabelProvider.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBStringExpr.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui.tests/src/net/sf/sveditor/ui/tests/editor/SVEditorTester.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/editor/actions/OverrideTaskFuncImpl.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/text/SVEditorFileProvider.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/parser/TestParseAssertions.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBBind.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/stmt/SVDBAssertStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBModportPortsDecl.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/utils/SVDBItemPrint.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVLexer.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/views/diagram/contributions/NewDiagramForClassHandler.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBIdentifierExpr.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/editor/SVCharacterPairMatcher.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/index/TestFileSystemIndexProviders.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVParameterDeclParser.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/views/diagram/contributions/NewDiagramForClassContributionItem.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVBlockItemDeclParser.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/srcgen/NewClassGenerator.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/pref/SVEditorPrefsInitialize.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBCoverBinsExpr.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/preproc/SVPreProcOutput.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/index/TestDeclCache.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/parser/TestParseClassBodyItems.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/ISVDBIncludeFileProvider.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBItem.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/views/diagram/LeaveEmBeLayoutAlgoritm.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/views/diagram/figures/CompartmentFigure.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVParserBase.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/wizards/templates/NewSVTDescriptorPage.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/job_mgr/JobMgr.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVProceduralBlockParser.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/preproc/SVPreProcessor.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBModIfcInstItem.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/explorer/LibIndexPath.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/editor/actions/AddBlockCommentAction.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/refs/SVDBRefCacheItem.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/SVDBIndexCollection.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/search/SVDBFindDocComment.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/SVTodoScanner.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/job_mgr/JobMgrTests.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/svt/editor/SVTLabelProvider.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/SVPerspectiveFactory.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBAssignmentPatternExpr.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVFatalParseException.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/refs/SVDBRefType.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/stmt/SVDBConfigCellClauseStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/SortUtils.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/editor/SVIndentingTemplateProposal.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/SVMarkerPropagationJob.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBChildItem.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/srcgen/OverrideMethodsFinder.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/search/SVDBIndexSearcher.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/project/SVDBProjectManager.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVPortListParser.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/prop_pages/LibraryPathsPage.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVCovergroupParser.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/templates/TemplateInfo.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/stmt/SVDBTypedefStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/docs/DocKeywordInfo.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/search/ISVDBIndexSearcher.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/docs/TestFindDocComments.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/index/TestThreadedSourceCollectionIndex.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/ISVDBFileSystemChangeListener.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBCrossBinsSelectConditionExpr.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/open_decl/TestOpenFile.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/hierarchy/HierarchyTreeNode.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBSequenceMatchItemExpr.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/scanner/FileContextSearchMacroProvider.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/indent/SVIndentTokenType.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/SVDBShadowIndex.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/stmt/SVDBAlwaysStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/log/ILogHandle.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/templates/FSInStreamProvider.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVExprParser.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/indent/IndentTests.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/log/LogFactory.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/content_assist/TestArrayContentAssist.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/prop_pages/AddSourceCollectionDialog.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/content_assist/TestContentAssistClass.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/editor/SVEditorTextHover.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/utils/BundleUtils.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBRandomizeCallExpr.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/scanner/ProgramBlockTests.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBParenExpr.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/search/SVDBStructFieldFinder.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/cache/ISVDBFS.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/editor/SVSourceViewerConfiguration.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/views/hierarchy/SVHierarchyViewerSorter.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/ISVDBIndex.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/docs/IDocTopicManager.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/WorkspaceFileDialog.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/expr_utils/SVExprScanner.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/fileset/SVFileSet.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBSequenceRepetitionExpr.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/index/TestVmmBasics.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/argfile/editor/SVArgFileCodeScanner.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/wizards/templates/TemplateFilesTableViewer.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/cache/SVDBFileFS.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVSpecifyBlockParser.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/svt/editor/FileBrowseDialog.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/expr/eval/SVDBIndexValueProvider.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/ISVDBChildItem.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/search/SVDBFindModuleMatcher.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/SVDBArgFileIndex.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/stmt/SVDBEventTriggerStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/expr_utils/SVExprUtilsParser.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/index/persistence/SrcCollectionPersistence.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/EditorInputUtils.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBCastExpr.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/log/ILogLevelListener.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/cache/BufferedRandomAccessFileWriter.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/SVDBArgFileIndexFactory.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/scanutils/SVDocumentTextScanner.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/index/TestIndexFileRefs.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/wizards/templates/TemplateParametersTableViewer.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/ISVDBProjectRefProvider.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBPropertyCaseItem.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/index/TestArgFileIndex.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBCoverCrossBinsSel.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBTypeInfoUnion.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/cache/MappedByteBufferOutputStream.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVDBPropertyIfStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/expr/eval/IValueProvider.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/search/BuiltinClassFactory.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/EmptySVDBChildItemIterable.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/TestNullIndexCacheFactory.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/svf_scanner/SVFCmdLineProcessor.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/search/SVDBPackageItemFinder.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/diagrams/DiagConnectionType.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBTypeInfoEnum.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.batch/src/net/sf/sveditor/core/batch/SVEditorVlogIndexFactory.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/stmt/SVDBWaitOrderStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui.tests/src/net/sf/sveditor/ui/tests/editor/TestAutoIndent.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/scanutils/ITextScanner.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/StringIterableIterator.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/templates/TemplateParameterType.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/SVEditorUtil.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/explorer/RebuildSvIndexAction.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/expr/SVExprParseException.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/indent/ISVIndentScanner.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVConstraintParser.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/log/ILogListener.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/FileIndexIterator.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/scanner/ISVPreProcScannerObserver.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/wizards/ISVSubWizard.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/templates/IExternalTemplatePathProvider.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/wizards/templates/NewSVTDescriptorWizard.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/SVDBLibIndex.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.doc.dev/src/net/sf/sveditor/doc/dev/BuildJavaDocTocTask.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/persistence/IDBWriter.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/SaveMarkersFileSystemProvider.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/stmt/SVDBConstraintIfStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/ISVDBDeclCache.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/explorer/SVExplorerDecoratingLabelProvider.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/search/SVSearchPage.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBClockingBlock.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/stmt/SVDBVarDeclStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/prop_pages/PluginLibPrefsPage.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/persistence/ISVDBPersistenceRWDelegateParent.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/argfile/editor/SVArgFileDocumentPartitions.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/editor/SVCompletionProcessor.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/scanutils/IScanLocation.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/stmt/SVDBNullStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/views/diagram/DiagContentProvider.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/InputStreamCopier.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/SVDBPersistenceDescriptor.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/SVScannerTests.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/expr/eval/SVExprComparator.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/stmt/SVDBStmt.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/parser/SVAssertionParser.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/diagrams/DiagNode.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/SVDBTypeInfo.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/argfile/editor/SVArgFileSourceViewerConfiguration.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/index/libIndex/WSArgFileIndexChanges.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/db/index/cache/SVDBDirFS.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.uvm.templates/src/net/sf/sveditor/core/uvm/templates/Activator.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/TextTagPosUtils.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core/src/net/sf/sveditor/core/scanutils/IRandomAccessTextScanner.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.core.tests/src/net/sf/sveditor/core/tests/project_settings/ProjectSettingsTests.java
java_projects/sveditor sveditor/plugins/net.sf.sveditor.ui/src/net/sf/sveditor/ui/views/objects/SVObjectsViewerFilter.java
