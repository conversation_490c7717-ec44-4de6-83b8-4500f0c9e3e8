-- impose schema on the issue comments datasaet

CREATE OR REPLACE TABLE
  github_events.issue_comments AS
WITH
  numbered AS (
  SELECT
    JSON_EXTRACT_SCALAR(entry.content, '$.id') AS id,
    entry.associated_id AS issue_id,
    repo_name,
    actor,
    J<PERSON><PERSON>_EXTRACT_SCALAR(entry.content, '$.body') AS body,
    STRUCT( JSON_EXTRACT_SCALAR(entry.content, '$.user.login') AS login,
      JSON_EXTRACT_SCALAR(entry.content, '$.user.id') AS id,
      JSON_EXTRACT_SCALAR(entry.content, '$.user.type') AS type,
      JSON_EXTRACT_SCALAR(entry.content, '$.user.site_admin') AS site_admin ) AS user,
    JSON_EXTRACT_SCALAR(entry.content, '$.author_association') AS author_association,
    JSON_EXTRACT_SCALAR(entry.content, '$.created_at') AS created_at,
    JSON_EXTRACT_SCALAR(entry.content, '$.updated_at') AS updated_at,
    JSON_EXTRACT_SCALAR(entry.content, '$.url') AS url,
    JSON_EXTRACT_SCALAR(entry.content, '$.issue_url') AS issue_url,
    JSON_EXTRACT(entry.content, '$.reactions') AS reactions,
    ROW_NUMBER() OVER (PARTITION BY JSON_EXTRACT_SCALAR(entry.content, '$.id')
    ORDER BY
      JSON_EXTRACT_SCALAR(entry.content, '$.updated_at') DESC) AS rn
  FROM
    github_events.issue_and_commit_comments
  WHERE
    entry.type = 'issue_comment' )
SELECT
  * EXCEPT(rn)
FROM
  numbered
WHERE
  rn = 1
