import json
import argparse
from typing import Any

from anthropic.types import (
    ToolResultBlockParam,
    ToolParam,
    <PERSON>lUseBlock,
    MessageParam,
)
from pydantic import BaseModel, Field

from research.core.constants import GCP_PROJECT_ID, GCP_VERTEX_REGION
from research.data.external_context.search_glean import (
    GleanSearchClient,
    GleanSearchResult,
)
from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser(description="Glean Retrieval Evaluation")
    parser.add_argument(
        "--input", type=str, default="glean_examples.json", help="Input JSON file"
    )
    parser.add_argument(
        "--output", type=str, default="output.json", help="Output JSON file"
    )
    parser.add_argument(
        "--url", type=str, default="https://augment-be.glean.com", help="Augment URL"
    )
    parser.add_argument(
        "--max_results", type=int, default=12, help="Maximum number of search results"
    )
    parser.add_argument(
        "--chunk_size", type=int, default=2000, help="Maximum chunk size"
    )
    parser.add_argument(
        "--model", type=str, default="claude-3-5-sonnet-v2@20241022", help="Model name"
    )
    parser.add_argument(
        "--temperature", type=float, default=0, help="Temperature for model inference"
    )
    return parser.parse_args()


class GleanSearch(BaseModel):
    query: str = Field(..., description="The search query")


GLEAN_SEARCH_INSTRUCTION = """Search in company documentations using Glean.
Use this tool to find relevant information only when necessary.
This search is based on keyword matching; start with a broad search and narrow down as needed.
Perform additional search based on previous results if necessary.
"""


class ReportResult(BaseModel):
    answer: str = Field(..., description="The answer to the question")
    chunk_ids: list[str] = Field(
        ...,
        description="List of at most 10 chunk ids relevant to the question, most relevant ones first.",
    )


GLEAN_RESULT_INSTRUCTION = """Use this tool to report the final answer and relevant chunks.
ALL chunks that are in any way relevant to the question should be reported.
"""


class GleanTestCase:
    def __init__(
        self,
        question: str,
        reference_answer: str,
        reference: str,
        model: str,
        temperature: float,
        anthropic_client: AnthropicVertexAiClient,
        glean_search_client: GleanSearchClient,
        max_results: int,
        chunk_size: int,
    ):
        self.question = question
        self.reference_answer = reference_answer
        self.reference = reference
        self.anthropic_client = anthropic_client.client
        self.glean_search_client = glean_search_client
        self.max_results = max_results
        self.chunk_size = chunk_size
        self.model = model
        self.temperature = temperature
        # Define all the tools here
        self.tools = [
            ToolParam(
                name="glean_search",
                description=GLEAN_SEARCH_INSTRUCTION,
                input_schema=GleanSearch.model_json_schema(),
            ),
            ToolParam(
                name="report_result",
                description=GLEAN_RESULT_INSTRUCTION,
                input_schema=ReportResult.model_json_schema(),
            ),
        ]
        self.chunk_cache = {}

    def run(self) -> dict[str, Any]:
        logger.info("Starting run method")
        # Send the initial question
        messages = [MessageParam(role="user", content=self.question)]
        search_performed = []
        known_doc_ids = set()
        while True:
            # Let the model decide if it wants to search or just generate results.
            # We can either add the repo retrieval before this, or set it up as a
            # a tool too
            logger.info("Sending request to Anthropic client")
            response = self.anthropic_client.messages.create(
                model=self.model,
                max_tokens=1024,
                system="You are an AI assistant with access to a Glean search tool.",
                messages=messages,
                temperature=self.temperature,
                tool_choice={"type": "any"},
                tools=self.tools,
            )
            logger.info("Received response with stop_reason: %s", response.stop_reason)

            if response.stop_reason != "tool_use":
                raise RuntimeError("Did not use a tool as expected")
            # Validate tool use and add it to message list
            logger.info("Processing tool use")
            tool_use = response.content[-1]
            assert isinstance(tool_use, ToolUseBlock)

            # Search, or reporting results
            if tool_use.name == "report_result":
                # We're done
                results = ReportResult.model_validate(tool_use.input)
                generated_answer = results.answer
                chunk_ids = results.chunk_ids
                break
            if tool_use.name == "glean_search":
                messages.append(MessageParam(role="assistant", content=[tool_use]))

                # Perform the search and add the results to the message list
                search_input = GleanSearch.model_validate(tool_use.input)
                logger.info(
                    "Performing Glean search with query: %s", search_input.query
                )
                search_results = self.glean_search_client.search(
                    search_input.query,
                    max_results=self.max_results,
                    max_snippet_size=self.chunk_size,
                    content=True,
                )
                new_docs: list[GleanSearchResult] = []
                old_docs = 0
                for item in search_results:
                    if item.document_id in known_doc_ids:
                        old_docs += 1
                    else:
                        new_docs.append(item)
                        known_doc_ids.add(item.document_id)
                if old_docs > 0:
                    search_content = [f"Omitted {old_docs} repeated search results"]
                else:
                    search_content = []
                if new_docs:
                    for result in new_docs:
                        new_chunks = result.chunk_content(self.chunk_size)
                        if not new_chunks:
                            continue
                        self.chunk_cache.update(
                            {chunk.chunk_id: chunk for chunk in new_chunks}
                        )

                        new_content = f"\n{new_chunks[0].document.title}\n====\n"
                        for chunk in new_chunks:
                            new_content += f"Chunk #{chunk.chunk_id}\n{chunk.text}\n"
                        search_content.append(new_content)
                else:
                    search_content.append("No new search results found")

                result_str = "\n".join(search_content)
                logger.info(
                    "Search results has %d characters in %d documents.",
                    len(result_str),
                    len(new_docs),
                )
                tool_result = ToolResultBlockParam(
                    tool_use_id=tool_use.id, type="tool_result", content=result_str
                )
                messages.append(MessageParam(role="user", content=[tool_result]))
                search_performed.append(search_input.model_dump())
                logger.info("Search performed and results added to messages")

        logger.info("Returning results")
        return {
            "question": self.question,
            "generated_answer": generated_answer,
            "reference_answer": self.reference_answer,
            "reference_url": self.reference,
            "search_performed": search_performed,
            "chunk_ids": chunk_ids,
        }


def main():
    args = parse_args()

    region = GCP_VERTEX_REGION
    project_id = GCP_PROJECT_ID
    max_output_tokens = 1024 * 8
    anthropic_client = AnthropicVertexAiClient(
        project_id=project_id,
        region=region,
        model_name=args.model,
        temperature=args.temperature,
        max_output_tokens=max_output_tokens,
    )
    glean_search_client = GleanSearchClient(args.url)

    with open(args.input) as examples_file:
        reference = json.load(examples_file)

    results = []
    for example in reference:
        gle = GleanTestCase(
            example["question"],
            example["answer"],
            example["reference"],
            args.model,
            args.temperature,
            anthropic_client=anthropic_client,
            glean_search_client=glean_search_client,
            chunk_size=args.chunk_size,
            max_results=args.max_results,
        )
        logger.info("Question: %s", example["question"])
        logger.info("----------------")
        result = gle.run()
        results.append(result)
        logger.info("Search performed: %s", result["search_performed"])
        logger.info("----------------")
        logger.info("Generated answer: %s", result["generated_answer"])
        logger.info("----------------")
        logger.info("Reference URL: %s", example["reference"])
        logger.info("Reference Answer: %s", example["answer"])
        logger.info("----------------")
        logger.info("Relevant chunks:")
        total_size = 0
        for chunk_id in result["chunk_ids"]:
            if chunk_id not in gle.chunk_cache:
                logger.warning("WARNING!!!! Chunk %s not found", chunk_id)
                continue
            logger.info(
                "Chunk %s with %d characters",
                chunk_id,
                len(gle.chunk_cache[chunk_id].text),
            )
            total_size += len(gle.chunk_cache[chunk_id].text)
        logger.info("Total size of chunks: %d characters", total_size)
        logger.info("================")
    with open(args.output, "w") as f:
        json.dump(results, f, indent=2)


if __name__ == "__main__":
    main()
