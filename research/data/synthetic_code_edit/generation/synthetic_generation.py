"""Synthetic data generation script."""

import argparse
import dataclasses
import json
import logging
from multiprocessing import Pool
from pathlib import Path
from typing import Any, Optional

from tqdm import tqdm

from research.core.utils_for_log import create_logger
from research.data.synthetic_code_edit.generation.pipelines import PipelineV1
from research.data.synthetic_code_edit.types import CodeEditData
from research.data.synthetic_code_edit.util_lib import (
    read_input_raw_edit_scopes,
    read_input_raw_samples,
)

PIPELINES = {
    "v1": PipelineV1,
}


def create_pipeline(
    pipeline_name: str,
    logger: logging.Logger,
    pipeline_kwargs: Optional[dict[str, str]] = None,
) -> Any:
    if pipeline_kwargs is None:
        pipeline_kwargs = {}
    return PIPELINES[pipeline_name](logger, **pipeline_kwargs)  # type: ignore


# We expect the arguments of generate_sample to be:
# - pipeline name: str
# - sample: CodeEditData
# - output directory path: Path
# - pipeline_kwargs: Optional[dict[str, str]] (used to initialize the pipeline)
GenerationArgType = tuple[Any, CodeEditData, str, Path, Optional[dict[str, str]]]


def generate_sample(generate_args: GenerationArgType) -> None:
    pipeline, input_sample, sample_name, output_dir, pipeline_kwargs = generate_args
    output_dir.mkdir(exist_ok=True, parents=False)
    logger = create_logger(sample_name, output_dir / f"{sample_name}.log")

    pipeline = create_pipeline(pipeline, logger, pipeline_kwargs)

    try:
        output_sample, meta_info = pipeline.generate_sample(input_sample)
    except KeyboardInterrupt:  # pylint: disable=try-except-raise
        raise
    except Exception as e:  # pylint: disable=broad-exception-caught
        logger.error(f"Generation failed: {e}")
        output_sample = None
        meta_info = {"fail_reason": f"exception with message:\n{e}"}

    if output_sample is not None:
        # generation successfull
        meta_file_name = f"{sample_name}_meta.json"
        with (output_dir / f"{sample_name}.json").open("w") as f:
            json.dump(dataclasses.asdict(output_sample), f, indent=2)
    else:
        # generation failed
        meta_file_name = f"{sample_name}_meta_failed.json"

    meta_info.update(pipeline.gpt.get_stats())  # Compute GPT stats (money spent, etc)
    with (output_dir / meta_file_name).open("w") as f:
        json.dump(meta_info, f, indent=2)


def main(
    input_file: str,
    use_edit_scopes: bool,
    output_dir: str,
    num_samples: int,
    pipeline: str,
    pipeline_kwargs: Optional[dict[str, str]],
    num_processes: int = -1,
):
    if use_edit_scopes:
        input_samples = read_input_raw_edit_scopes(input_file)
    else:
        input_samples = read_input_raw_samples(input_file)
    save_dir = Path(output_dir)
    max_num_samples = min(len(input_samples), num_samples)
    tasks: list[GenerationArgType] = []
    for index in range(max_num_samples):
        tasks.append(
            (
                pipeline,
                input_samples[index],
                f"{index:03d}",
                save_dir,
                pipeline_kwargs,
            )
        )

    if num_processes <= 1:
        for task in tqdm(tasks):
            generate_sample(task)
    else:
        with Pool(min(num_processes, max_num_samples)) as pool:
            _ = list(tqdm(pool.imap(generate_sample, tasks), total=len(tasks)))


def parse_pipeline_kwargs(string: str) -> dict[str, str]:
    kwargs = {}
    for pair in string.split(","):
        key, value = pair.split("=")
        kwargs[key] = value
    return kwargs


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for generating code edits synthetic data.",
    )
    parser.add_argument(
        "--input_file",
        "-i",
        type=Path,
        required=True,
        help="Input file with raw code samples",
    )
    parser.add_argument(
        "--output_dir",
        "-o",
        type=Path,
        required=True,
        help="Output directory for saving the data",
    )
    parser.add_argument(
        "--num_samples",
        "-n",
        type=int,
        required=True,
        help="Number of samples to generate",
    )
    parser.add_argument(
        "--pipeline",
        "-p",
        required=True,
        help="Number of samples to generate",
    )
    parser.add_argument(
        "--pipeline_kwargs",
        type=parse_pipeline_kwargs,
        help="Kwargs to pass to pipeline constructor",
    )
    parser.add_argument(
        "--num_processes",
        "-np",
        type=int,
        default=-1,
        help="Number of parallel processes",
    )
    parser.add_argument(
        "--use_edit_scopes",
        action="store_true",
        help="Use edit scopes data instead of CodeEditData",
    )
    args = parser.parse_args()

    return args


if __name__ == "__main__":
    xargs = parse_args()
    main(
        input_file=xargs.input_file,
        use_edit_scopes=xargs.use_edit_scopes,
        output_dir=xargs.output_dir,
        num_samples=xargs.num_samples,
        pipeline=xargs.pipeline,
        pipeline_kwargs=xargs.pipeline_kwargs,
        num_processes=xargs.num_processes,
    )
