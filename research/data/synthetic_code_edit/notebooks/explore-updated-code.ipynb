#%% md
# Explore the sampled updated_code for code edit
#%%
import random
import collections
from research.core import constants
from research.data.synthetic_code_edit import sampling_lib
#%%
file_path = (
    constants.AUGMENT_ROOT
    / "research"
    / "data"
    / "synthetic_code_edit"
    / "seeds"
    / "Code-Formatting"
    / "001.txt"
)
content = file_path.read_text()
lranges = sampling_lib.find_lrange_via_consecutive_sibling_nodes(
    content,
    max_consecutive_nodes=8,
    max_lines=40,
)
lines = content.splitlines(keepends=True)
#%%
counts = collections.defaultdict(lambda: 0)
for x in lranges:
    counts[x.stop - x.start] += 1
keys = sorted(counts.keys())
for key in keys:
    print(f"The amount of data with #lines = {key} is {counts[key]}")
#%%
index = random.randint(0, len(lranges) - 1)
lrange = lranges[index]
print("".join(lines[lrange.start : lrange.stop]))