"""Data pipeline to repartition/reshard parquet files.

Parquet partition size proportionally affects the memory usage of Spark executors.

Usage:
python research/data/rag/run_repartition.py \
--input /mnt/efs/spark-data/shared/repo/2024-0608_55k/ \
--output_dir /mnt/efs/spark-data/shared/repo/2024-0608_55k_repartitioned/ \
--rows_per_partition 20

"""

import argparse

from research.data.rag.utils import repartition_and_shuffle
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet


def main(inputs: list[str], output: str, rows_per_partition: int):
    spark = k8s_session(
        name="repartition",
        max_workers=32,
        conf={
            "spark.executor.pyspark.memory": "512G",
            "spark.executor.memory": "256G",
            "spark.sql.parquet.columnarReaderBatchSize": "16",
            "spark.task.cpus": "1",
            "spark.executor.cores": "1",
        },
        ephemeral_storage_gb=512,
    )
    all_inputs: list[str] = []
    for input_dir in inputs:
        xlist = map_parquet.list_files(
            spark, input_dir, suffix="parquet", include_path=True
        )
        all_inputs.extend(xlist)
        print(f"Read {len(xlist)} files from {input_dir}.")
    df = spark.read.parquet(*all_inputs)
    spark.sparkContext.setJobDescription(f"Shuffling dataset with {df.count()} rows.")
    df = repartition_and_shuffle(None, df, rows_per_indexed_dataset=rows_per_partition)
    df.write.parquet(output, mode="overwrite")
    df.printSchema()
    spark.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input",
        type=str,
        required=True,
        nargs="+",
        help="Input path to the stack dataset.",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="Output directory for the sampled repos.",
    )
    parser.add_argument(
        "--rows_per_partition",
        type=int,
        required=True,
        help="Number of rows per partition.",
    )
    args = parser.parse_args()
    main(args.input, args.output_dir, args.rows_per_partition)
