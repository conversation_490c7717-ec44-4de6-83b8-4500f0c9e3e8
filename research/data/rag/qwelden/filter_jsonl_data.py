"""
Filter hindsight data for <PERSON>wen training. The filtering is applied in the following order:

1. Model filtering: Keep only events for a specific model. If None, will not filter by model.
2. License filtering: Keep only events that are licensed. If False, will not filter by license.
3. Edit events filtering: Keep only events that have edit events. If False, will not filter by edit events.
4. Skip token filtering: Filter out events where the completion response contains a <|skip|> token.
5. Empty completion filtering: Filter out events where the completion response is empty.
6. Limit filtering: Limit the number of events to keep. If None, will not limit the number of events.

Usage:
python research/data/rag/qwelden/filter_jsonl_data.py \
    --input_datasets 2024-11-01_2024-11-14 2024-11-15_2024-11-30 2024-12-01_2024-12-14 2024-12-15_2024-12-31 2025-01-01_2025-01-14 \
    --tenant_name i0-vanguard0 \
    --output_dataset vanguard_2024-11-01_2025-01-14 \
    --model qweldenv1_14b \
    --license-filter \
    --edit-filter \
    --skip-token-filter \
    --empty-completion-filter \
    --limit-filter 1000000
"""

import argparse
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
from dataclasses import dataclass
import pandas as pd
from google.cloud import storage
from collections import Counter
import glob

from research.core.utils_for_file import read_jsonl_zst, write_jsonl_zst
from research.core.data_paths import canonicalize_path

BASE_DIR = Path(canonicalize_path("user/pranay/hindsight/"))
INPUT_DATASETS = [
    "2024-11-01_2024-11-14",
    "2024-11-15_2024-11-30",
    "2024-12-01_2024-12-14",
    "2024-12-15_2024-12-31",
    "2025-01-01_2025-01-14",
]

# License filter constants
LICENSE_BUCKET = "gcp-us1-spark-data"
LICENSE_NON_PERMISSIVE_PREFIX = (
    "shared/vanguard/license_filter/non_permissive_rids/completion_host_request/"
)
LICENSE_PERMISSIVE_PREFIX = (
    "shared/vanguard/license_filter/permissive_rids/completion_host_request/"
)
# TODO(pranay): Empty and Skip data resources require additional work to get the license data.
# These files are prepared from `experimental/pranay/empty_and_skip_completions/README.md` and are used here depending on the data source.
# LICENSE_NON_PERMISSIVE_PREFIX = (
#     "/mnt/efs/augment/user/pranay/hindsight/skip_2024-11-01_2025-01-14/non_permissive_rids/"
# )
# LICENSE_PERMISSIVE_PREFIX = (
#     "/mnt/efs/augment/user/pranay/hindsight/skip_2024-11-01_2025-01-14/permissive_rids/"
# )


@dataclass
class FilterStats:
    initial_count: int
    model_stats: Optional[Dict[str, int]] = None
    license_stats: Optional[Dict[str, int]] = None
    edit_events_stats: Optional[Dict[str, int]] = None
    skip_token_stats: Optional[Dict[str, int]] = None
    empty_completion_stats: Optional[Dict[str, int]] = None
    limit_stats: Optional[Dict[str, int]] = None
    final_count: int = 0

    def record_filter_stats(
        self, filter_name: str, before_count: int, after_count: int
    ) -> None:
        stats = {
            "total_before": before_count,
            "total_after": after_count,
            "total_filtered": before_count - after_count,
        }

        if filter_name == "model":
            self.model_stats = stats
        elif filter_name == "license":
            self.license_stats = stats
        elif filter_name == "edit_events":
            self.edit_events_stats = stats
        elif filter_name == "skip_token":
            self.skip_token_stats = stats
        elif filter_name == "empty_completion":
            self.empty_completion_stats = stats
        elif filter_name == "limit":
            self.limit_stats = stats


def apply_model_filter(data: List[dict], model: str) -> List[dict]:
    """Filter to keep only events for a specific model."""
    return [
        result for result in data if model in result["completion"]["response"]["model"]
    ]


def apply_license_filter(data: List[dict]) -> List[dict]:
    """Filter to keep only events that are licensed."""

    client = storage.Client()
    bucket = client.bucket(LICENSE_BUCKET)

    def read_parquet(paths: list[str]) -> pd.DataFrame:
        """Reads parquet data from paths using pandas."""
        dfs = [pd.read_parquet(file) for file in paths]
        return pd.concat(dfs, ignore_index=True)

    def get_all_parquet_files(prefix: str, gs: bool = True) -> list[str]:
        """Gets all parquet files from GCS path.

        Args:
            prefix: Path prefix within the bucket
        Returns:
            List of full GCS paths to parquet files
        """
        if gs:
            # List all blobs in the bucket with the given prefix
            blobs = bucket.list_blobs(prefix=prefix)

            # Filter for .parquet files and construct full paths
            parquet_files = [
                f"gs://{LICENSE_BUCKET}/{blob.name}"
                for blob in blobs
                if blob.name.endswith(".zstd.parquet")
            ]
        else:
            parquet_files = glob.glob(f"{prefix}/*.zstd.parquet")

        return sorted(parquet_files)

    def get_request_ids_from_parquet(prefix: str, gs: bool = True) -> set[str]:
        """Gets all request IDs from parquet files in GCS path."""
        parquet_files = get_all_parquet_files(prefix, gs)
        df = read_parquet(parquet_files)
        request_ids = set(df["request_id"])
        return request_ids

    # remove non-permissive requests from data
    non_permissive_requests = get_request_ids_from_parquet(
        LICENSE_NON_PERMISSIVE_PREFIX, gs=True
    )
    permissive_requests = get_request_ids_from_parquet(
        LICENSE_PERMISSIVE_PREFIX, gs=True
    )

    request_ids = set(result["completion"]["request_id"] for result in data)
    neither = request_ids - permissive_requests - non_permissive_requests
    if len(neither) > 0:
        print(
            f"Warning: {len(neither)} requests were not in the permissive list or the non-permissive list. Removing them to be safe."
        )

    data = [
        result
        for result in data
        if result["completion"]["request_id"] not in non_permissive_requests
        and result["completion"]["request_id"] in permissive_requests
    ]

    return data


def apply_edit_events_filter(data: List[dict]) -> List[dict]:
    """Filter to keep only events that have edit events."""
    return [result for result in data if result["completion"]["request"]["edit_events"]]


def apply_skip_token_filter(data: List[dict]) -> List[dict]:
    """Filter to keep only events that don't have a <|skip|> token in the response."""
    return [
        result
        for result in data
        if len(result["completion"]["response"]["skipped_suffix"]) == 0
        and len(result["completion"]["response"]["suffix_replacement_text"]) == 0
    ]


def apply_empty_completion_filter(data: List[dict]) -> List[dict]:
    """Filter to keep only events that have a non-empty completion response."""
    return [
        result
        for result in data
        if len(result["completion"]["response"]["token_ids"]) > 0
    ]


def load_jsonl_files(input_datasets: List[str], vendor: str) -> List[dict]:
    """Load and combine all JSONL files for given input_datasets and vendor."""
    jsonl_files = []
    for input_dataset in input_datasets:
        jsonl_files.append(BASE_DIR / input_dataset / vendor / "data.jsonl.zst")
        assert jsonl_files[-1].exists()

    results = []
    for file in jsonl_files:
        print(f"Reading {file}")
        results.extend(read_jsonl_zst(file))

    return results


def save_metadata(
    metadata_path: Path,
    source_files: List[Path],
    stats: FilterStats,
    filters_applied: Dict[str, bool],
    command: str,
    data: List[dict],
    model: Optional[str] = None,
) -> None:
    """Save metadata about the processing and filtering."""
    metadata = {
        "initial_count": stats.initial_count,
        "final_count": stats.final_count,
        "source_files": [str(f) for f in source_files],
        "command": command,
        "filters": {
            "model": {
                "applied": filters_applied["model"],
                "model_name": model if model else "N/A",
                **(stats.model_stats or {}),
            },
            "license": {
                "applied": filters_applied["license"],
                **(stats.license_stats or {}),
            },
            "edit_events": {
                "applied": filters_applied["edit_events"],
                **(stats.edit_events_stats or {}),
            },
            "skip_token": {
                "applied": filters_applied["skip_token"],
                **(stats.skip_token_stats or {}),
            },
            "empty_completion": {
                "applied": filters_applied["empty_completion"],
                **(stats.empty_completion_stats or {}),
            },
            "limit": {
                "applied": filters_applied["limit"],
                **(stats.limit_stats or {}),
            },
        },
    }

    # Model breakdown
    metadata["model_breakdown"] = Counter(
        result["completion"]["response"]["model"] for result in data
    )

    # Get top 10 most popular file extension breakdown and count
    metadata["file_breakdown"] = Counter(
        Path(result["completion"]["request"]["path"]).suffix for result in data
    )
    metadata["file_breakdown"] = dict(metadata["file_breakdown"].most_common(10))

    # Date breakdown, ordered by date
    metadata["date_breakdown"] = Counter(
        datetime.fromtimestamp(result["completion"]["request"]["timestamp"]).date()
        for result in data
    )
    metadata["date_breakdown"] = dict(
        sorted(metadata["date_breakdown"].items(), key=lambda item: item[0])
    )
    metadata["date_breakdown"] = {
        date.isoformat(): count for date, count in metadata["date_breakdown"].items()
    }

    print(json.dumps(metadata, indent=2))

    with open(metadata_path, "w") as f:
        json.dump(metadata, f, indent=2)


def process_data(
    input_datasets: List[str],
    tenant_name: str,
    output_dir: Path,
    model: Optional[str] = None,
    license_filter: bool = False,
    edit_filter: bool = False,
    skip_token_filter: bool = False,
    empty_completion_filter: bool = False,
    limit_filter: Optional[int] = None,
) -> None:
    """Process and filter data, saving results and metadata."""
    output_dir.mkdir(parents=True, exist_ok=True)

    # Load all data
    data = load_jsonl_files(input_datasets, tenant_name)
    stats = FilterStats(initial_count=len(data))

    # Apply filters in specified order
    if model:
        before_count = len(data)
        data = apply_model_filter(data, model)
        stats.record_filter_stats("model", before_count, len(data))

    if edit_filter:
        before_count = len(data)
        data = apply_edit_events_filter(data)
        stats.record_filter_stats("edit_events", before_count, len(data))

    if skip_token_filter:
        before_count = len(data)
        data = apply_skip_token_filter(data)
        stats.record_filter_stats("skip_token", before_count, len(data))

    if empty_completion_filter:
        before_count = len(data)
        data = apply_empty_completion_filter(data)
        stats.record_filter_stats("empty_completion", before_count, len(data))

    if license_filter:
        before_count = len(data)
        data = apply_license_filter(data)
        stats.record_filter_stats("license", before_count, len(data))

    if limit_filter:
        before_count = len(data)
        data = data[:limit_filter]
        stats.record_filter_stats("limit", before_count, len(data))

    stats.final_count = len(data)

    # Save metadata
    source_files = [
        BASE_DIR / input_dataset / tenant_name / "data.jsonl.zst"
        for input_dataset in input_datasets
    ]
    filters_applied = {
        "model": model is not None,
        "license": license_filter,
        "edit_events": edit_filter,
        "skip_token": skip_token_filter,
        "empty_completion": empty_completion_filter,
        "limit": limit_filter is not None,
    }
    command = f"python {' '.join(sys.argv)}"  # Get the exact command that was run
    save_metadata(
        output_dir / "metadata.json",
        source_files,
        stats,
        filters_applied,
        command,
        data,
        model,
    )

    # Save request IDs
    request_ids = set(result["completion"]["request_id"] for result in data)
    with open(output_dir / "request_ids.json", "w") as f:
        json.dump(list(request_ids), f)

    # Save the dataset
    output_file = output_dir / "data.jsonl.zst"
    print(f"Writing {len(data)} records to {output_file}")
    write_jsonl_zst(output_file, data)

    print(f"Saved all files to {output_dir}")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_datasets",
        type=str,
        nargs="+",
        default=INPUT_DATASETS,
        help="Input datasets to combine. Each dataset is a directory in <BASE_DIR>/<input_dataset>/<tenant_name>/data.jsonl.zst.",
    )
    parser.add_argument(
        "--tenant_name",
        type=str,
        required=True,
        choices=["dogfood-shard", "i0-vanguard0"],
    )
    parser.add_argument(
        "--output_dataset",
        type=str,
        required=True,
        help="Directory to save files in <BASE_DIR>/<dataset>/<tenant_name>/",
    )
    parser.add_argument(
        "--model",
        type=str,
        default=None,
        help="Model to filter by. If None, will not filter by model. (Eg. qweldenv1-1-14b)",
    )
    parser.add_argument(
        "--license-filter",
        action="store_true",
        help="Apply license filtering. If not specified, will not filter by license.",
    )
    parser.add_argument(
        "--edit-filter",
        action="store_true",
        help="Only keep events with edit events. If not specified, will not filter by edit events.",
    )
    parser.add_argument(
        "--skip-token-filter",
        action="store_true",
        help="Filter out events where the completion response contains a <|skip|> token. "
        "There may be some cases where we could not attribute the <|skip|> token to the suffix and these samples will still be kept, but they should be extremely rare.",
    )
    parser.add_argument(
        "--empty-completion-filter",
        action="store_true",
        help="Filter out events where the completion response is empty.",
    )
    parser.add_argument(
        "--limit-filter",
        type=int,
        default=None,
        help="Limit the number of events to keep. If None, will not limit the number of events.",
    )

    args = parser.parse_args()

    output_dir = BASE_DIR / args.output_dataset / args.tenant_name
    process_data(
        input_datasets=args.input_datasets,
        tenant_name=args.tenant_name,
        output_dir=output_dir,
        model=args.model,
        license_filter=args.license_filter,
        edit_filter=args.edit_filter,
        skip_token_filter=args.skip_token_filter,
        empty_completion_filter=args.empty_completion_filter,
        limit_filter=args.limit_filter,
    )


if __name__ == "__main__":
    main()
