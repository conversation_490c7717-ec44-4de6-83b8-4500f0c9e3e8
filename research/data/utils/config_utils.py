"""Utilities for working with config files."""

import json

import boto3

from research.core import augment_secrets


def save_config_s3(config: dict, bucket_name: str, file_name: str):
    """Save data generation config to S3."""
    cw_access_key_id, cw_secret_access_key = augment_secrets.get_coreweave_keys()
    config_json = json.dumps(config)
    s3 = boto3.client(
        "s3",
        aws_access_key_id=cw_access_key_id,
        aws_secret_access_key=cw_secret_access_key,
        endpoint_url="https://object.las1.coreweave.com",
    )
    s3.put_object(
        Bucket=bucket_name,
        Key=file_name,
        Body=config_json,
        ContentType="application/json",
    )
