"""Functions and utilities for running evaluation."""

from __future__ import annotations

import logging
import time
from functools import partial
from typing import Callable, Mapping, Optional

import torch
from megatron.data import indexed_dataset
from torch.types import Number
from torch.utils.data import DataLoader, Subset
from torch.utils.data.distributed import DistributedSampler

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward import metrics
from research.fastbackward.data import (
    LossMaskFn,
    RandomTokenDataset,
    make_indexed_dataset_collate_fn,
)
from research.fastbackward.distributed import is_main_process

EvalCallback = Callable[[str, torch.Tensor, torch.Tensor, torch.Tensor], None]
"""A function that is called after each evaluation step.

Args:
    name: The name of the evaluation dataset.
    x: The input data.
    y: The target data.
    y_hat: The model output.
"""

PartialEvalCallback = Callable[[torch.Tensor, torch.Tensor, torch.Tensor], None]
"""Partial of the callback function that is pushed down to single evals."""

logger = logging.getLogger(__name__)


def build_evaluation_datasets(
    dataset_raw_path: str,
    vocab_size: int,
    block_size: int,
    eval_items: int = 1000,
) -> dict[str, indexed_dataset.AugmentDataset]:
    """Build multiple datasets given a string.

    For simplicity, we allow the user to nest multiple dataset into a single string.
    Following the spec of
    - name_0@dataset_0;name_1@dataset_1;name_2@dataset_2;...
    - dataset_0;dataset_1;... (name will become 0, 1, ...)
    - path (single dataset, returning {set00: dataset})
    """
    dataset_by_name = {}
    parts = dataset_raw_path.split(";")
    for idx, raw_path in enumerate(parts):
        raw_parts = raw_path.split("@")
        if len(raw_parts) == 1:
            name, xpath = f"set{idx:02d}", raw_parts[0]
        elif len(raw_parts) == 2:
            name, xpath = raw_parts
        else:
            raise ValueError(f"Invalid dataset name: {raw_path} at position {idx}")
        if xpath == "RANDOM":
            dataset_by_name[name] = RandomTokenDataset(
                vocab_size=vocab_size,
                num_items=eval_items,
                seqlen=block_size,
                seed=2345,
            )
        else:
            assert indexed_dataset.MMapIndexedDataset.exists(
                xpath
            ), f"Requested evaluation dataset at {xpath} does not exist."
            eval_dataset = indexed_dataset.MMapIndexedDataset(xpath, skip_warmup=True)
            dataset_by_name[name] = eval_dataset
            logger.info(
                f"Eval dataset {name} (dtype={eval_dataset.dtype}) {len(eval_dataset)} items."
            )
    if len(dataset_by_name) == 1:
        # we don't need a dataset name in this case
        dataset_by_name = {"*": dataset_by_name["set00"]}
    return dataset_by_name


def build_evaluation_dataloaders(
    datasets_by_name: Mapping[str, indexed_dataset.AugmentDataset],
    eval_items: int,
    batch_size: int,
    seqlen: int,
    loss_mask_fn: Optional[LossMaskFn] = None,
) -> Mapping[str, DataLoader]:
    eval_loaders_by_name: dict[str, DataLoader] = {}
    for eval_name, eval_dataset in datasets_by_name.items():
        if eval_items > 0:
            # When eval_items is specified, use a (fixed) random subset of the eval set
            if len(eval_dataset) < eval_items:
                raise ValueError(
                    f"Requested {eval_items} items for {eval_name}, but only "
                    f"{len(eval_dataset)} items available."
                )
            g = torch.Generator()
            g.manual_seed(1337)
            subset_ids = torch.randperm(len(eval_dataset), generator=g)[:eval_items]
            eval_dataset = Subset(eval_dataset, subset_ids.tolist())
        # TODO: figure out if there is a way to do an *initial* shuffle and none after
        eval_sampler = DistributedSampler(
            eval_dataset,
            num_replicas=mpu.get_data_parallel_world_size(),
            rank=mpu.get_data_parallel_rank(),
            shuffle=False,
            drop_last=True,  # We need divisibility by dp_world_size
        )
        eval_loader = DataLoader(
            eval_dataset,
            batch_size=batch_size,
            sampler=eval_sampler,
            collate_fn=make_indexed_dataset_collate_fn(seqlen, loss_mask_fn),
            pin_memory=True,
            drop_last=True,  # TODO: check if we're OK having a smaller last batch
        )
        eval_loaders_by_name[eval_name] = eval_loader
    return eval_loaders_by_name


@torch.no_grad()
def run_single_evaluation(
    model: torch.nn.Module,
    metric_fn_by_name: Mapping[str, metrics.MetricFn],
    loader: DataLoader,
    dataset_name: str,
    callback: PartialEvalCallback | None,
    eval_log_interval,
) -> dict[str, Number]:
    """Run a set of metrics on a single dataset and returns the averages.

    Args:
        model: The model to evaluate.
        metric_fn_by_name: A mapping of metric names to metric functions.
        loader: The data loader.
        dataset_name: The name of the dataset.
        callback: A function to call after each evaluation step.
    """
    model.eval()
    total_batches = len(loader)
    aggregators_by_name: dict[str, metrics.Mean] = {
        name: metrics.Mean() for name in metric_fn_by_name.keys()
    }
    for i, (x, y, _) in enumerate(loader):
        x, y = x.to("cuda", non_blocking=True), y.to("cuda", non_blocking=True)
        y_hat = model(x)
        for metric_name, metric_fn in metric_fn_by_name.items():
            metric = metric_fn(x, y, y_hat)
            aggregators_by_name[metric_name].join(metric)
        if callable(callback):
            callback(x, y, y_hat)
        if is_main_process() and (i % eval_log_interval == 0 or i == total_batches - 1):
            logger.info(f"Eval progress ({dataset_name}): {i}/{total_batches} batches")

    model.train()
    metric_by_name: dict[str, Number] = {}
    for metric_name, metric in aggregators_by_name.items():
        value = metric.get()
        if value is None:  # TODO(markus): should we ignore these cases?
            logger.warning(f"Metric {metric_name} returned None. Converting to nan")
            value = float("nan")
        metric_by_name[metric_name] = value
    return metric_by_name


def run_all_evaluation(
    model: torch.nn.Module,
    metric_fn_by_name: Mapping[str, metrics.MetricFn],
    loaders_by_name: Mapping[str, DataLoader],
    callback: Optional[EvalCallback] = None,
    eval_log_interval: int = 200,
) -> Mapping[str, Number]:
    """Run a set of metrics on a set of datasets.

    The returned dict keys follow the format:
    {dataset_name}.{metric_name}
    or just
    {metric_name}
    if there is only a single dataset.
    """
    eval_start_time = time.time()

    metric_by_full_name: dict[str, Number] = {}
    for set_name, loader in loaders_by_name.items():
        if is_main_process():
            logger.info(f"Evaluating on dataset {set_name}...")
        eval_metric_by_name = run_single_evaluation(
            model,
            metric_fn_by_name,
            loader,
            set_name,
            partial(callback, set_name) if callable(callback) else None,
            eval_log_interval=eval_log_interval,
        )
        for metric_name, metric in eval_metric_by_name.items():
            metric_by_full_name[f"validation/{set_name}.{metric_name}"] = metric

    eval_total = time.time() - eval_start_time
    metric_by_full_name["runtime/eval_time"] = eval_total

    return metric_by_full_name
