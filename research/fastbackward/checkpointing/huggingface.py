"""Utilities for loading HuggingFace checkpoints."""

from __future__ import annotations

import glob
import json
import logging
from functools import partial
from pathlib import Path
from typing import Optional

import safetensors
from safetensors.torch import load_file
import torch

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward.checkpointing.utils import split_weight_for_model_parallel

ParameterRemapping = tuple[str, str, Optional[int]]
StateDict = dict[str, torch.Tensor]

logger = logging.getLogger(__name__)

# Checkpoint key mappings from HuggingFace versions of LLaMa 2/StarCoder
_LLAMA_REMAPS: list[ParameterRemapping] = [
    ("model.embed_tokens.weight", "tok_embeddings.weight", 1),
    ("model.norm.weight", "norm.weight", None),
    ("lm_head.weight", "output.weight", 0),
]
_LLAMA_LAYER_REMAPS: list[ParameterRemapping] = [
    ("model.layers.{}.self_attn.q_proj.weight", "layers.{}.attention.wq.weight", 0),
    ("model.layers.{}.self_attn.k_proj.weight", "layers.{}.attention.wk.weight", 0),
    ("model.layers.{}.self_attn.v_proj.weight", "layers.{}.attention.wv.weight", 0),
    ("model.layers.{}.self_attn.o_proj.weight", "layers.{}.attention.wo.weight", 1),
    ("model.layers.{}.mlp.gate_proj.weight", "layers.{}.feed_forward.w1.weight", 0),
    ("model.layers.{}.mlp.up_proj.weight", "layers.{}.feed_forward.w3.weight", 0),
    ("model.layers.{}.mlp.down_proj.weight", "layers.{}.feed_forward.w2.weight", 1),
    ("model.layers.{}.input_layernorm.weight", "layers.{}.attention_norm.weight", None),
    (
        "model.layers.{}.post_attention_layernorm.weight",
        "layers.{}.ffn_norm.weight",
        None,
    ),
]
_QWEN_ADDITIONAL_REMAPS: list[ParameterRemapping] = [
    ("model.layers.{}.self_attn.q_proj.bias", "layers.{}.attention.wq.bias", 0),
    ("model.layers.{}.self_attn.k_proj.bias", "layers.{}.attention.wk.bias", 0),
    ("model.layers.{}.self_attn.v_proj.bias", "layers.{}.attention.wv.bias", 0),
]
# Additional remappings for Qwen3 models with query and key normalization
_QWEN3_ADDITIONAL_REMAPS: list[ParameterRemapping] = [
    # Add mappings for query and key normalization layers
    # The normalization is applied on the head dimension, not the full hidden dimension
    (
        "model.layers.{}.self_attn.q_norm.weight",
        "layers.{}.attention.q_norm.weight",
        None,
    ),
    (
        "model.layers.{}.self_attn.k_norm.weight",
        "layers.{}.attention.k_norm.weight",
        None,
    ),
]
# Note: Qwen3 models use the same layer remapping as LLaMA models
# The only difference is that Qwen3 doesn't need the unpermutation step

# Remappings for Qwen3-Embedding models which have a different structure
_QWEN3_EMBEDDING_REMAPS: list[ParameterRemapping] = [
    ("embed_tokens.weight", "tok_embeddings.weight", 1),
    ("norm.weight", "norm.weight", None),
    # No lm_head.weight for embedding models
]


_STARCODER_REMAPS: list[ParameterRemapping] = [
    ("transformer.wte.weight", "tok_embeddings.weight", 1),
    ("transformer.wpe.weight", "pos_embeddings.weight", 1),
    ("transformer.ln_f.weight", "norm.weight", None),
    ("transformer.ln_f.bias", "norm.bias", None),
    ("lm_head.weight", "output.weight", 0),
]
_STARCODER_LAYER_REMAPS: list[ParameterRemapping] = [
    ("transformer.h.{}.ln_1.weight", "layers.{}.attention_norm.weight", None),
    ("transformer.h.{}.ln_1.bias", "layers.{}.attention_norm.bias", None),
    ("transformer.h.{}.attn.c_proj.weight", "layers.{}.attention.wo.weight", 1),
    ("transformer.h.{}.attn.c_proj.bias", "layers.{}.attention.wo.bias", None),
    ("transformer.h.{}.ln_2.weight", "layers.{}.ffn_norm.weight", None),
    ("transformer.h.{}.ln_2.bias", "layers.{}.ffn_norm.bias", None),
    ("transformer.h.{}.mlp.c_fc.weight", "layers.{}.feed_forward.w1.weight", 0),
    ("transformer.h.{}.mlp.c_fc.bias", "layers.{}.feed_forward.w1.bias", 0),
    ("transformer.h.{}.mlp.c_proj.weight", "layers.{}.feed_forward.w2.weight", 1),
    ("transformer.h.{}.mlp.c_proj.bias", "layers.{}.feed_forward.w2.bias", None),
]


# The HF checkpoints permute the QK matrices to match their (different) rotary
# embedding implementation.
# See: https://github.com/huggingface/transformers/blob/0c718f16d1e8b73ac637529f6328fd8cb378ce7e/src/transformers/models/llama/convert_llama_weights_to_hf.py#L149  # noqa: E501
# This function inverts that function to match the llama layout.
def unpermute_qk_weights_from_hf_to_llama(
    w: torch.Tensor, nheads: int, headdim: int, hidden_size: int
):
    """Unpermute the QK weights from HF to llama layout."""
    orig_shape = w.shape
    return (
        w.view(nheads, 2, headdim // 2, hidden_size).transpose(1, 2).reshape(orig_shape)
    )


def _load_hf_checkpoint(checkpoint_dir: str) -> tuple[dict, StateDict]:
    checkpoint_dir_ = Path(checkpoint_dir)
    config_file = checkpoint_dir_ / "config.json"
    with config_file.open(mode="r") as fh:
        config = json.load(fh)
    model_pattern = checkpoint_dir_ / "pytorch_model*.bin"
    files = glob.glob(model_pattern.as_posix())
    if len(files) > 0:
        load_fn = torch.load
    else:
        # No pytorch .bin files, try safetensors
        model_pattern = checkpoint_dir_ / "model*.safetensors"
        files = glob.glob(model_pattern.as_posix())
        load_fn = load_file
    if len(files) == 0:
        raise RuntimeError(
            f"Could not find any pytorch_model*.bin or model*.safetensors files at {checkpoint_dir}"
        )
    state_dict = load_fn(files[0])
    for f in files[1:]:
        state_dict.update(load_fn(f))
    return config, state_dict


def _validate_mp_params(mp_world_size: int | None, mp_rank: int | None):
    if mp_world_size is None and mp_rank is None:
        mp_world_size = mpu.get_model_parallel_world_size()
        mp_rank = mpu.get_model_parallel_rank()
    else:
        assert (
            mp_world_size is not None and mp_rank is not None
        ), "Must set both or neither of mp_{world_size,rank}"
    return mp_world_size, mp_rank


def _resize_vocab_weight(weight: torch.Tensor, new_vocab_size: int):
    old_vocab_size = weight.size(0)
    if old_vocab_size == new_vocab_size:
        return weight
    new_weight = torch.zeros(
        (new_vocab_size, weight.size(1)),
        device=weight.device,
        dtype=weight.dtype,
    )

    # If you really want to do this, you can:
    # new_weight.copy_(weight[:new_vocab_size, :])
    assert old_vocab_size <= new_vocab_size, "Cannot resize to a smaller vocab size"

    new_weight[:old_vocab_size, :].copy_(weight)
    return new_weight


def _remap_top_level_state_dict(
    source_dict: StateDict,
    target_dict: StateDict,
    mp_world_size: int,
    mp_rank: int,
    remaps: list[ParameterRemapping],
    embedding_key: str = "model.embed_tokens.weight",
    lm_head_key: str | None = "lm_head.weight",
):
    # Support tied embeddings -- the checkpoint may have only embeddings, not the output projection
    # Skip this check for embedding models (where lm_head_key is None)
    if lm_head_key is not None and lm_head_key not in source_dict:
        assert (
            embedding_key in source_dict
        ), f"Neither {lm_head_key} nor {embedding_key} found in checkpoint"
        source_dict[lm_head_key] = source_dict[embedding_key]

    for src, dst, split_dim in remaps:
        result = source_dict[src]
        target_dict[dst] = split_weight_for_model_parallel(
            result,
            split_dim,
            mp_world_size,
            mp_rank,
        )
        del source_dict[src]


def _remap_llama_hf_layer_state_dict(
    source_dict: StateDict,
    target_dict: StateDict,
    mp_world_size: int,
    mp_rank: int,
    config: dict,
    extra_remaps: list[ParameterRemapping] | None = None,
):
    """Remap layer weights from HuggingFace models to FastBackward format.
    Args:
        source_dict: Source state dictionary
        target_dict: Target state dictionary to populate
        mp_world_size: Model parallel world size
        mp_rank: Model parallel rank
        config: Model configuration
        extra_remaps: Additional parameter remappings to apply
    """
    nlayers = config["num_hidden_layers"]
    nheads = config["num_attention_heads"]
    n_kv_heads = config["num_key_value_heads"]
    hidden_size = config["hidden_size"]
    # Check if head_dim is in the config (for models like Qwen3)
    # Otherwise fall back to the calculation
    headdim = config.get("head_dim", hidden_size // nheads)
    all_remaps = _LLAMA_LAYER_REMAPS.copy()
    if extra_remaps is not None:
        all_remaps.extend(extra_remaps)
    # Map over per-layer parameter names
    for layernum in range(nlayers):
        for src_fmt, dst_fmt, split_dim in all_remaps:
            src = src_fmt.format(layernum)
            dst = dst_fmt.format(layernum)
            result = source_dict[src]
            # Unpermute QK weights and biases (if applicable) to match interleaved rotary.
            if src.endswith("q_proj.weight"):
                result = unpermute_qk_weights_from_hf_to_llama(
                    result, nheads, headdim, hidden_size
                )
            elif src.endswith("k_proj.weight"):
                result = unpermute_qk_weights_from_hf_to_llama(
                    result, n_kv_heads, headdim, hidden_size
                )
            elif src.endswith("q_proj.bias"):
                result = unpermute_qk_weights_from_hf_to_llama(
                    result, nheads, headdim, 1
                )
            elif src.endswith("k_proj.bias"):
                result = unpermute_qk_weights_from_hf_to_llama(
                    result, n_kv_heads, headdim, 1
                )
            elif src.endswith("self_attn.q_norm.weight"):
                result = unpermute_qk_weights_from_hf_to_llama(
                    result, nheads=1, headdim=headdim, hidden_size=1
                )
            elif src.endswith("self_attn.k_norm.weight"):
                result = unpermute_qk_weights_from_hf_to_llama(
                    result, nheads=1, headdim=headdim, hidden_size=1
                )
            # Regular weights that might need model-parallel splitting
            target_dict[dst] = split_weight_for_model_parallel(
                result,
                split_dim,
                mp_world_size,
                mp_rank,  # type: ignore
            )
            del source_dict[src]


def _remap_qwen3_embedding_layer_state_dict(
    source_dict: StateDict,
    target_dict: StateDict,
    mp_world_size: int,
    mp_rank: int,
    config: dict,
):
    """Remap layer weights from Qwen3-Embedding models to FastBackward format.

    This is a wrapper that adds "model." prefix to keys before calling the standard
    llama remap function, ensuring we get the unpermutation operations.
    """
    # Create a temporary dict with "model." prefix added to keys
    temp_source_dict = {}
    keys_to_rename = []

    for key in list(source_dict.keys()):
        if key.startswith("layers."):
            new_key = f"model.{key}"
            temp_source_dict[new_key] = source_dict[key]
            keys_to_rename.append(key)
        else:
            temp_source_dict[key] = source_dict[key]

    # Remove the original keys that we're renaming
    for key in keys_to_rename:
        del source_dict[key]

    # Add the renamed keys
    source_dict.update(temp_source_dict)

    # Now call the standard llama remap function which expects "model.layers" prefix
    _remap_llama_hf_layer_state_dict(
        source_dict,
        target_dict,
        mp_world_size,
        mp_rank,
        config,
        extra_remaps=_QWEN3_ADDITIONAL_REMAPS,
    )


def _remap_starcoder_hf_layer_state_dict(
    source_dict: StateDict,
    target_dict: StateDict,
    mp_world_size: int,
    mp_rank: int,
    config: dict,
):
    nlayers = config["n_layer"]
    nheads = config["n_head"]
    hidden_dim = config["n_embd"]
    # Check if head_dim is in the config (for models like Qwen3)
    # Otherwise fall back to the calculation
    headdim = config.get("head_dim", hidden_dim // nheads)
    for layernum in range(nlayers):
        for src_fmt, dst_fmt, split_dim in _STARCODER_LAYER_REMAPS:
            src = src_fmt.format(layernum)
            dst = dst_fmt.format(layernum)
            result = source_dict[src]
            target_dict[dst] = split_weight_for_model_parallel(
                result,
                split_dim,
                mp_world_size,
                mp_rank,
            )
            del source_dict[src]
        # Now we handle splitting the QKV matrix by hand
        # This is pretty gross.
        full_weight = source_dict[f"transformer.h.{layernum}.attn.c_attn.weight"]
        full_bias = source_dict[f"transformer.h.{layernum}.attn.c_attn.bias"]
        qkv = [  # Q_{weight, bias}, K_{weight, bias}, V_{weight_bias}
            full_weight[:hidden_dim, :].clone().detach(),
            full_bias[:hidden_dim].clone().detach(),
            full_weight[hidden_dim : hidden_dim + headdim, :].clone().detach(),
            full_bias[hidden_dim : hidden_dim + headdim].clone().detach(),
            full_weight[hidden_dim + headdim : hidden_dim + 2 * headdim, :]
            .clone()
            .detach(),
            full_bias[hidden_dim + headdim : hidden_dim + 2 * headdim].clone().detach(),
        ]
        # NOTE: only Q_{weight, bias} are MP-split, since starcoder has MQA, so K/V
        # aren't split.
        split_qkv = [
            split_weight_for_model_parallel(
                x,
                0,
                mp_world_size,
                mp_rank,
            )
            for x in qkv[:2]
        ] + qkv[2:]
        idx = 0
        for name in ["q", "k", "v"]:
            for kind in ["weight", "bias"]:
                key = f"layers.{layernum}.attention.w{name}.{kind}"
                target_dict[key] = split_qkv[idx]
                idx += 1


def load_huggingface_checkpoint(
    checkpoint_location: str,
    model_vocab_size: int | None = None,
    pos_embeddings_len: int | None = None,
    mp_world_size: int | None = None,
    mp_rank: int | None = None,
) -> StateDict:
    """Load a HuggingFace checkpoint into a state dict.

    Args:
        checkpoint_location: The path to the checkpoint.
        model_vocab_size: The new vocab size. If None, the vocab size is
            unchanged.
        pos_embeddings_len: The new length of the positional embeddings. If
            None, the positional embeddings are unchanged. Only for StarCoder.
        mp_world_size: The world size for model parallelism. If None, the
            world size is unchanged.
        mp_rank: The rank for model parallelism. If None, the rank is
            unchanged.

    Returns:
        A state dict.

    Note:
        For models like Qwen3 where num_head * head_dim != hidden_dim, the head_dim
        will be automatically extracted from the config.json file if it contains a
        "head_dim" field. Otherwise, it will be calculated as hidden_dim // n_heads.
    """
    config, state_dict = _load_hf_checkpoint(checkpoint_location)

    if "LlamaForCausalLM" in config["architectures"]:
        model_type = "llama"
        embedding_key = "model.embed_tokens.weight"
        lm_head_key = "lm_head.weight"
        top_level_remaps = _LLAMA_REMAPS
        layer_remap_fn = _remap_llama_hf_layer_state_dict
    elif "Qwen2ForCausalLM" in config["architectures"]:
        model_type = "qwen"
        embedding_key = "model.embed_tokens.weight"
        lm_head_key = "lm_head.weight"
        top_level_remaps = _LLAMA_REMAPS
        layer_remap_fn = partial(
            _remap_llama_hf_layer_state_dict,
            extra_remaps=_QWEN_ADDITIONAL_REMAPS,
        )
    elif "GPTBigCodeForCausalLM" in config["architectures"]:
        model_type = "starcoder"
        embedding_key = "transformer.wte.weight"
        lm_head_key = "lm_head.weight"
        top_level_remaps = _STARCODER_REMAPS
        layer_remap_fn = _remap_starcoder_hf_layer_state_dict
    elif "Qwen3ForCausalLM" in config["architectures"]:
        # Check if this is an embedding model by examining the state dict keys
        is_embedding_model = (
            "embed_tokens.weight" in state_dict and "lm_head.weight" not in state_dict
        )

        if is_embedding_model:
            model_type = "qwen3_embedding"
            embedding_key = "embed_tokens.weight"
            lm_head_key = None  # No lm_head for embedding models
            top_level_remaps = _QWEN3_EMBEDDING_REMAPS

            # Use the wrapper function that handles the key prefix difference
            # and ensures we get the unpermutation operations for Q/K weights
            layer_remap_fn = _remap_qwen3_embedding_layer_state_dict
        else:
            model_type = "qwen"
            embedding_key = "model.embed_tokens.weight"
            lm_head_key = "lm_head.weight"
            top_level_remaps = _LLAMA_REMAPS

            # Prepare extra remappings
            extra_remaps = list(
                _QWEN3_ADDITIONAL_REMAPS
            )  # Always include q_norm and k_norm

            # Use the consolidated function with Qwen3-specific remappings
            layer_remap_fn = partial(
                _remap_llama_hf_layer_state_dict,
                extra_remaps=extra_remaps,
            )
    else:
        raise ValueError("Unknown state dict format.")

    result_state_dict = {}
    mp_world_size, mp_rank = _validate_mp_params(mp_world_size, mp_rank)
    if model_vocab_size is not None:
        keys_to_resize = [embedding_key]
        if lm_head_key is not None:
            keys_to_resize.append(lm_head_key)
        for key in keys_to_resize:
            state_dict[key] = _resize_vocab_weight(state_dict[key], model_vocab_size)
    if pos_embeddings_len is not None and model_type == "starcoder":
        state_dict["transformer.wpe.weight"] = (
            state_dict["transformer.wpe.weight"][:pos_embeddings_len, :]
            .clone()
            .detach()
        )
    _remap_top_level_state_dict(
        state_dict,
        result_state_dict,
        mp_world_size,
        mp_rank,
        top_level_remaps,
        embedding_key=embedding_key,
        lm_head_key=lm_head_key,
    )
    layer_remap_fn(state_dict, result_state_dict, mp_world_size, mp_rank, config)
    return result_state_dict
