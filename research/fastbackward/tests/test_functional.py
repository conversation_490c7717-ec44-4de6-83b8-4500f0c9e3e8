"""Tests for fastbackward.functional."""

import pytest
import torch
import torch.distributed as dist

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward.functional import vocab_parallel_cross_entropy
from research.fastbackward.tests.fake_distributed_runner import distributed_runner


@pytest.mark.parametrize("world_size", [1, 4])
@pytest.mark.parametrize("use_ignore_index", [True, False])
def test_vocab_parallel_cross_entropy(world_size: int, use_ignore_index: bool):
    # Model-parallel across ranks.
    with distributed_runner(
        world_size, model_parallel_size=world_size, timeout_s=30.0
    ) as runner:
        runner.run(_test_vocab_parallel_cross_entropy, use_ignore_index)


def _test_vocab_parallel_cross_entropy(use_ignore_index):
    batch_size = 4
    seqlen = 1024
    vocab = 1000

    assert vocab % mpu.get_model_parallel_world_size() == 0
    vocab_per_rank = vocab // mpu.get_model_parallel_world_size()
    vocab_start_index = mpu.get_model_parallel_rank() * vocab_per_rank
    vocab_end_index = vocab_start_index + vocab_per_rank

    full_logits = torch.randn(batch_size, seqlen, vocab, device="cuda")
    targets = torch.randint(0, vocab, (batch_size, seqlen), device="cuda")
    # Broadcast random tensors from rank 0 so every rank has the same ones
    dist.broadcast(full_logits, 0)
    dist.broadcast(targets, 0)
    my_logits = full_logits[:, :, vocab_start_index:vocab_end_index].clone().detach()

    full_logits.requires_grad = True
    my_logits.requires_grad = True

    to_ignore_index = -3
    if use_ignore_index:
        to_ignore = torch.rand(batch_size, seqlen, device="cuda") < 0.5
        dist.broadcast(to_ignore, 0)
        targets[to_ignore] = to_ignore_index

    gt_loss = torch.nn.functional.cross_entropy(
        full_logits.view(-1, vocab), targets.view(-1), ignore_index=to_ignore_index
    )
    my_loss = vocab_parallel_cross_entropy(
        my_logits, targets, ignore_index=to_ignore_index
    )
    # All ranks should have the same loss and it matches the ground truth
    torch.testing.assert_close(my_loss, gt_loss)

    gt_loss.backward()
    my_loss.backward()

    # all_gather the parallel grads
    assert my_logits.grad is not None
    gather_list = [
        torch.empty_like(my_logits.grad) for _ in range(dist.get_world_size())
    ]
    dist.all_gather(gather_list, my_logits.grad)
    my_full_grad = torch.cat(gather_list, dim=-1)

    # All ranks should now have grads that match the ground truth
    torch.testing.assert_close(my_full_grad, full_logits.grad)
