"""Tests for sequence parallel support in fastbackward.

pytest --slow research/fastbackward/tests/test_sequence_parallel.py
"""

import pytest
import torch
from pytest import param
from torch.utils.data import DataLoader, TensorDataset, default_collate

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward import losses
from research.fastbackward.model import (
    ModelArgs,
    Transformer,
    configure_fsdp_optimizer,
)
from research.fastbackward.tests.fake_distributed_runner import distributed_runner
from research.fastbackward.training import TrainOptions, train_loop


@pytest.mark.parametrize(
    (
        "ffn_type,"
        "pos_embed_type,"
        "bias,"
        "norm_type,"
        "n_kv_heads,"
        "activation_checkpointing"
    ),
    [
        # First 3 cases always run, the remainder require `--slow` (no CI)
        ("glu", "rope", "none", "rmsnorm", 0, False),  # Llama 2
        ("glu", "rope", "none", "rmsnorm", 0, True),  # Activation checkpointing
        ("mlp", "absolute", "attn_mlp", "layernorm", 1, False),  # StarCoder
        param("mlp", "rope", "none", "rmsnorm", 0, False, marks=pytest.mark.slow),
        param("glu", "absolute", "none", "rmsnorm", 0, False, marks=pytest.mark.slow),
        param(
            "glu", "rope", "attn_mlp", "rmsnorm", 0, False, marks=pytest.mark.slow
        ),  # With bias
        param("glu", "rope", "none", "layernorm", 0, False, marks=pytest.mark.slow),
        param(
            "glu", "rope", "none", "rmsnorm", 1, False, marks=pytest.mark.slow
        ),  # MQA
        param(
            "mlp", "rope", "attn_mlp", "layernorm", 1, False, marks=pytest.mark.slow
        ),  # StarCoder 2
    ],
)
@pytest.mark.parametrize("world_size", [2, 4])
def test_sequence_parallel_model(
    world_size: int,
    ffn_type: str,
    pos_embed_type: str,
    bias: str,
    norm_type: str,
    n_kv_heads: int,
    activation_checkpointing: bool,
):
    """Test that sequence parallelism does not increase loss."""
    with distributed_runner(
        world_size=world_size,
        model_parallel_size=world_size,
        timeout_s=60.0,
    ) as runner:
        runner.run(
            _test_sequence_parallel_model,
            ffn_type,
            pos_embed_type,
            bias,
            norm_type,
            n_kv_heads,
            activation_checkpointing,
        )


def _test_sequence_parallel_model(
    ffn_type: str,
    pos_embed_type: str,
    bias: str,
    norm_type: str,
    n_kv_heads: int,
    activation_checkpointing: bool,
    rtol: float = 1e-4,
    atol: float = 1.2e-4,
):
    seed_offset = mpu.get_data_parallel_rank()
    torch.manual_seed(1234 + seed_offset)
    torch.cuda.manual_seed(1234 + seed_offset)

    dim = 512
    n_layers = 2
    n_heads = 8
    vocab_size = 1024

    # Construct a model with sequence parallel disabled
    model_args = ModelArgs(
        dim=dim,
        n_layers=n_layers,
        n_heads=n_heads,
        n_kv_heads=n_kv_heads,
        vocab_size=vocab_size,
        ffn_type=ffn_type,
        pos_embed_type=pos_embed_type,
        bias=bias,
        norm_type=norm_type,
        use_activation_checkpointing=activation_checkpointing,
        use_sequence_parallel=False,
    )
    mpu.initialize_model_parallel_subgroup(
        "kv", max(mpu.get_model_parallel_world_size() // model_args.n_kv_heads, 1)
    )
    model = Transformer(model_args)
    model.to(device=torch.device("cuda"), dtype=torch.float32)
    model.train()

    # Construct a model with sequence parallel enabled
    sp_model_args = ModelArgs(
        dim=dim,
        n_layers=n_layers,
        n_heads=n_heads,
        n_kv_heads=n_kv_heads,
        vocab_size=vocab_size,
        ffn_type=ffn_type,
        pos_embed_type=pos_embed_type,
        bias=bias,
        norm_type=norm_type,
        use_activation_checkpointing=activation_checkpointing,
        use_sequence_parallel=True,
    )
    sp_model = Transformer(sp_model_args)
    sp_model.to(device=torch.device("cuda"), dtype=torch.float32)
    sp_model.train()

    # Make sure the two models have the same params
    for p, sp_p in zip(model.parameters(), sp_model.parameters()):
        with torch.no_grad():
            sp_p.copy_(p)

    batch = 2
    seqlen = 1024
    data = torch.randint(
        vocab_size, (batch, seqlen + 1), dtype=torch.int64, device="cuda"
    )
    x = data[:, :-1].contiguous()
    y = data[:, 1:].contiguous()

    # Run forward+backward on both models with the same data
    loss = losses.cross_entropy_loss(x, y, model(x))
    loss.backward()

    sp_loss = losses.cross_entropy_loss(x, y, sp_model(x))
    sp_loss.backward()

    # Check that losses and all grads match
    torch.testing.assert_close(loss, sp_loss, rtol=rtol, atol=atol)
    for p, sp_p in zip(model.parameters(), sp_model.parameters()):
        torch.testing.assert_close(p.grad, sp_p.grad, rtol=rtol, atol=atol)

    # Run the non-sequence parallel model through one train step and check for reduced loss
    model.zero_grad()
    optimizer, fms = configure_fsdp_optimizer(
        model, weight_decay=0.1, learning_rate=0.1, betas=(0.9, 0.999)
    )
    train_loader = DataLoader(
        TensorDataset(x, y),
        batch_size=batch,
        shuffle=False,
        collate_fn=lambda batch: (*default_collate(batch), seqlen),
    )

    for _ in train_loop(
        model,
        loss_fn=losses.cross_entropy_loss,
        flat_model_state=fms,
        optimizer=optimizer,
        lr_schedule=lambda _: 1e-3,
        train_options=TrainOptions(
            batch_size=batch,
            block_size=seqlen,
            gradient_accumulation_steps=1,
            log_interval=1,
            max_iters=1,
        ),
        train_loader=train_loader,
    ):
        pass

    updated_loss = losses.cross_entropy_loss(x, y, model(x))

    # Run the sequence parallel model through one train step and check for reduced loss
    sp_model.zero_grad()
    sp_optimizer, sp_fms = configure_fsdp_optimizer(
        sp_model, weight_decay=0.1, learning_rate=0.1, betas=(0.9, 0.999)
    )
    train_loader = DataLoader(
        TensorDataset(x, y),
        batch_size=batch,
        shuffle=False,
        collate_fn=lambda batch: (*default_collate(batch), seqlen),
    )

    for _ in train_loop(
        sp_model,
        loss_fn=losses.cross_entropy_loss,
        flat_model_state=sp_fms,
        optimizer=sp_optimizer,
        lr_schedule=lambda _: 1e-3,
        train_options=TrainOptions(
            batch_size=batch,
            block_size=seqlen,
            gradient_accumulation_steps=1,
            log_interval=1,
            max_iters=1,
        ),
        train_loader=train_loader,
    ):
        pass

    sp_updated_loss = losses.cross_entropy_loss(x, y, sp_model(x))

    torch.testing.assert_close(updated_loss, sp_updated_loss, rtol=rtol, atol=atol)
    assert (
        sp_updated_loss < sp_loss
    ), f"Loss on the same data increased after 1 step: {sp_loss} -> {sp_updated_loss}."
