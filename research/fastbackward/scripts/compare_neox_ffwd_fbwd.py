"""Compare fastforward vs. fastbackward output for StarCoder models.

This script compares the forward-pass outputs of a model loaded in fastforward
and in fastbackward to validate our checkpoint loading and forward-pass logic.

Only works for StarCoder language models and embedding models.

Usage (lm):
    python3 scripts/compare_neox_ffwd_fbwd.py \
        --checkpoint_path="/mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint" \
        --data="/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_split/validation_dataset"

Expected result:
    ffwd_log_ppl_avg.value=1.456670 fbwd_log_ppl_avg.value=1.456680
    ppl_delta_max.value=0.000977 logits_delta_max.value=0.009377 token_delta_max.value=66.000000

Usage (embedder):
    python3 scripts/compare_neox_ffwd_fbwd.py -e \
        --checkpoint_path="/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000" \
        --data="/mnt/efs/augment/user/vzhao/data/starethanol6/ethanol6_16.1_mean_doc/validation_dataset"

Expected result:
    neox-vs-ffwd avg=0.008373 max=0.016769
    ffwd-vs-fbwd avg=0.001773 max=0.002472
    fbwd-vs-neox avg=0.008391 max=0.017014
"""

import argparse
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import cast

import torch
import tqdm
from megatron.data import indexed_dataset

# For NeoX
from megatron.inference.inference_model import InferenceModel
from megatron.inference.process_wrap import ProcessWrappedObject
from megatron.tokenizer.tokenizer import StarCoderTokenizer, get_tokenizer
from tensordict.tensordict import TensorDict
from torch.nn import functional as F

from base.fastforward import fwd_utils
from base.fastforward.fwd import OutputTensorType
from base.fastforward.fwd_utils import (
    get_model_spec_from_neox_checkpoint,
)
from base.fastforward.starcoder.fwd_starcoder import (
    StarcoderAttentionFactory,
    generate_step_fn,
)
from research.fastbackward import distributed
from research.fastbackward.checkpointing.neox import (
    load_starcoder_checkpoint,
    load_starethanol_checkpoint,
)
from research.fastbackward.model import Transformer


@dataclass
class Avg:
    """Track the incremental average of a value."""

    value: float = 0.0
    count: int = 0

    def update(self, value):
        self.value += (value - self.value) / (self.count + 1)
        self.count += 1


@dataclass
class Max:
    """Track the max of a value."""

    value: float = 0.0

    def update(self, value):
        self.value = max(self.value, value)


def load_model_neox(checkpoint_path: Path, is_embedding_model: bool) -> InferenceModel:
    assert (checkpoint_path.parent / "config.yml").exists()
    yaml_files = [checkpoint_path.parent / "config.yml"]
    overwrite_values = {
        "load": str(checkpoint_path.parent),
    }

    if is_embedding_model:
        overwrite_values |= {
            # For reasons, the perplexity loss doesn't support inference, so we switch
            # it out for a contrastive loss when loading for embeddings.
            "contrastive": True,
            "ppl_distill": False,
        }

    model = cast(
        InferenceModel,
        ProcessWrappedObject(
            InferenceModel,
            yaml_files=yaml_files,
            overwrite_values=overwrite_values,
            use_cache=False,
        ),
    )
    if is_embedding_model:
        model.contrastive_embed([], [])

    return model


def log_perplexity_ffwd(step, attn, input_: list[int]) -> tuple[float, torch.Tensor]:
    """Compute the log perplexity of the input.

    This is a variation of `fwd_utils.log_perplexity` that returns the logits, which
    we use for comparison purposes.
    """
    attn.reset()
    input_tokens = torch.tensor(input_, dtype=torch.int32).cuda()
    logits = step(input_tokens[:-1], attn).tensor
    # Logits represent next-token probabilities, so skip we have to first token.
    return F.cross_entropy(logits, input_tokens[1:].long()).item(), logits


@torch.no_grad
def log_perplexity_fbwd(model: Transformer, input_: list[int]):
    """Compute the log perplexity of the input."""
    input_tokens = torch.tensor(input_, dtype=torch.int32).long().cuda()

    logits = model(input_tokens[:-1].unsqueeze(0)).squeeze(0)
    # Logits represent next-token probabilities, so skip we have to first token.
    return F.cross_entropy(logits, input_tokens[1:].long()).item(), logits


def _first(it):
    return next(iter(it))


@torch.no_grad
def get_embeddings_fbwd(
    model: Transformer, input_: list[int], embedding_token_ids: list[int]
):
    """Compute the log perplexity of the input."""
    input_batch = TensorDict(
        {
            "tokens_BL": torch.tensor([input_]).cuda(),
            "emb_tokens_B": torch.tensor(
                [_first(tok_id for tok_id in embedding_token_ids if tok_id in input_)]
            ).cuda(),
        },
        batch_size=[1],
    )

    logits = model(input_batch).squeeze(0)
    return logits


def get_embeddings_neox(
    model: InferenceModel,
    input_: list[int],
    embedding_token_ids: list[int],
):
    query_token, doc_token = embedding_token_ids
    if query_token in input_:
        # Neox adds its own special tokens, so we need to remove them.
        input_ = input_[: input_.index(query_token)]
        query_emb, _, _ = model.contrastive_embed([input_], [])
        assert len(query_emb) == 1
        return query_emb[0]
    else:
        assert doc_token in input_
        # Neox adds its own special tokens, so we need to remove them.
        input_ = input_[: input_.index(doc_token)]
        _, doc_emb, _ = model.contrastive_embed([], [input_])
        assert len(doc_emb) == 1
        return doc_emb[0]


def compare_ppl(examples, ffwd_model, attn, fbwd_model):
    fbwd_log_ppl_avg, ffwd_log_ppl_avg, token_delta_avg = Avg(), Avg(), Avg()
    ppl_delta_max, logits_delta_max, token_delta_max = Max(), Max(), Max()

    with tqdm.tqdm(examples) as t:
        for example in t:
            ffwd_log_ppl_, ffwd_logits = log_perplexity_ffwd(ffwd_model, attn, example)
            fbwd_log_ppl_, fbwd_logits = log_perplexity_fbwd(fbwd_model, example)
            ppl_delta_ = abs(ffwd_log_ppl_ - fbwd_log_ppl_)
            logits_delta_ = torch.sqrt(
                F.mse_loss(ffwd_logits, fbwd_logits, reduction="mean")
            ).item()
            token_delta_ = ffwd_logits.argmax(dim=-1) != fbwd_logits.argmax(dim=-1)

            ffwd_log_ppl_avg.update(ffwd_log_ppl_)
            fbwd_log_ppl_avg.update(fbwd_log_ppl_)
            token_delta_avg.update(token_delta_.float().mean().item())
            ppl_delta_max.update(ppl_delta_)
            logits_delta_max.update(logits_delta_)
            token_delta_max.update(token_delta_.sum().item())

            t.set_description(
                f"{ffwd_log_ppl_avg.value=:0.6f} {fbwd_log_ppl_avg.value=:0.6f} {token_delta_avg.value=:0.6f}"
                f"{ppl_delta_max.value=:0.6f} {logits_delta_max.value=:0.6f} {token_delta_max.value=:0.6f}"
            )


def compare_emb(
    examples, neox_model, ffwd_model, attn, fbwd_model, embedding_token_ids
):
    emb_delta_avgs, emb_delta_maxs = (
        [Avg() for _ in range(3)],
        [Max() for _ in range(3)],
    )

    def compute_delta(embs_x: torch.Tensor, embs_y: torch.Tensor):
        # return torch.sqrt(F.mse_loss(embs_x, embs_y, reduction="mean"))
        return ((embs_x - embs_y).norm(dim=-1) / embs_y.norm(dim=-1)).max()

    with tqdm.tqdm(examples) as t:
        for example in t:
            neox_emb = get_embeddings_neox(neox_model, example, embedding_token_ids)
            ffwd_emb = fwd_utils.get_embeddings(
                ffwd_model, attn, example, embedding_token_ids
            )
            fbwd_emb = get_embeddings_fbwd(fbwd_model, example, embedding_token_ids)

            for i, delta in enumerate(
                [
                    compute_delta(ffwd_emb, neox_emb).item(),
                    compute_delta(fbwd_emb, ffwd_emb).item(),
                    compute_delta(fbwd_emb, neox_emb).item(),
                ]
            ):
                emb_delta_avgs[i].update(delta)
                emb_delta_maxs[i].update(delta)

            desc = ""
            for i, lbl in enumerate(["neox-vs-ffwd", "ffwd-vs-fbwd", "fbwd-vs-neox"]):
                desc += f"{lbl} avg={emb_delta_avgs[i].value:0.6f} max={emb_delta_maxs[i].value:0.6f} "

            t.set_description(desc)


def main():
    logging.basicConfig(level=logging.INFO)

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-cp", "--checkpoint_path", type=Path, help="Path to the checkpoint dir."
    )
    parser.add_argument("--data", type=Path, required=True, help="data to use")
    parser.add_argument(
        "-e",
        "--embeddings",
        action="store_true",
        help="Is this an embedding model?",
    )
    parser.add_argument(
        "--limit", type=int, default=1024, help="limit the number of examples"
    )

    args = parser.parse_args()

    distributed.init_distributed_for_training(1)

    dataset = indexed_dataset.make_dataset(str(args.data), "mmap", skip_warmup=True)
    assert dataset is not None
    examples = [dataset.get(i).tolist() for i in range(args.limit or len(dataset))]

    tokenizer = get_tokenizer("StarCoderTokenizer")
    assert isinstance(tokenizer, StarCoderTokenizer)

    neox_model = load_model_neox(args.checkpoint_path, args.embeddings)

    ffwd_model_spec = get_model_spec_from_neox_checkpoint(args.checkpoint_path)
    attn = StarcoderAttentionFactory(ffwd_model_spec)(8192)
    ffwd_model = generate_step_fn(
        ffwd_model_spec,
        auto_capture_graphs=False,
        output_type=OutputTensorType.EMBEDDING
        if args.embeddings
        else OutputTensorType.VOCAB_LOGITS,
    )

    if args.embeddings:
        fbwd_model = load_starethanol_checkpoint(args.checkpoint_path)
    else:
        fbwd_model = load_starcoder_checkpoint(args.checkpoint_path)
    fbwd_model.to(torch.float16).cuda()

    if args.embeddings:
        compare_emb(
            examples,
            neox_model,
            ffwd_model,
            attn,
            fbwd_model,
            [tokenizer.ret_endofquery_id, tokenizer.ret_endofkey_id],
        )
    else:
        compare_ppl(examples, ffwd_model, attn, fbwd_model)


if __name__ == "__main__":
    main()
