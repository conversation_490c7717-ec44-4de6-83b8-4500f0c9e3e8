"""Implementation of embedding models with neox."""

from __future__ import annotations

from collections.abc import Sequence
from pathlib import Path
from typing import cast

import torch
from megatron.inference.inference_model import InferenceModel

from research.models.embedding_model import EmbeddingModel


class NeoxEmbeddingModel(EmbeddingModel):
    def __init__(
        self,
        inference_model: InferenceModel,
        embedding_token_id: int,
        embedding_dim: int,
        end_of_query_token_id: int,
        end_of_key_token_id: int,
    ):
        """Create a neox embedding model.

        Args:
            inference_model: The model to use to generate embeddings. This can be a
                ProcessWrappedObject.
            embedding_token_id: The token id to use for extracting embeddings.
            embedding_dim: The dimensionality of the embeddings.
            query_and_doc_token_ids: The token ids to use for extracting query and
                document embeddings.
        """
        assert (
            embedding_token_id
            in [
                end_of_query_token_id,
                end_of_key_token_id,
            ]
        ), f"{embedding_token_id=} must be one of {end_of_query_token_id=} or {end_of_key_token_id=}."

        self.inference_model = inference_model
        self._embedding_token_id = embedding_token_id
        self._embedding_dim = embedding_dim
        self.end_of_query_token_id = end_of_query_token_id
        self.end_of_key_token_id = end_of_key_token_id

    @property
    def embedding_token_id(self) -> int:
        return self._embedding_token_id

    @property
    def embedding_dim(self) -> int:
        return self._embedding_dim

    def embed_batch(self, tokens_batch: Sequence[list[int]]) -> torch.Tensor:
        # neox adds its own special tokens, so we need to remove them if they're
        # present in the tokens.
        tokens_batch = [
            tokens[: tokens.index(self.embedding_token_id)]
            if self.embedding_token_id in tokens
            else tokens
            for tokens in tokens_batch
        ]

        if self.embedding_token_id == self.end_of_query_token_id:
            embs, _, _ = self.inference_model.contrastive_embed(
                queries=tokens_batch, documents=[]
            )
        else:
            _, embs, _ = self.inference_model.contrastive_embed(
                queries=[], documents=tokens_batch
            )

        return embs


def create_from_neox_checkpoint(
    checkpoint_path: Path | str,
) -> tuple[NeoxEmbeddingModel, NeoxEmbeddingModel]:
    """Create a neox embedding model from a checkpoint.

    Currently, all neox models are joint query and document models, so we load the model
    once and set it up the query and document encoders with different embedding tokens.

    Args:
        checkpoint_path: The path to the checkpoint.

    Returns:
        A tuple of query and document embedders.
    """
    import yaml
    from megatron.inference.process_wrap import ProcessWrappedObject
    from megatron.tokenizer.tokenizer import get_tokenizer

    checkpoint_path = Path(checkpoint_path)

    # The fastforward and fastbackward models use the exact checkpoint directory, while
    # the neox models use the parent directory. This code lets users use the first case.
    if (
        not (checkpoint_path / "config.yml").exists()
        and (checkpoint_path.parent / "config.yml").exists()
    ):
        checkpoint_path = checkpoint_path.parent

    assert (
        checkpoint_path.exists() and (checkpoint_path / "config.yml").exists()
    ), f"{checkpoint_path=} doesn't exist or doesn't contain a config.yml."

    model = cast(
        InferenceModel,
        ProcessWrappedObject(
            InferenceModel,
            yaml_files=[str(checkpoint_path / "config.yml")],
            overwrite_values={
                "load": str(checkpoint_path),
                # We rely on some special magic in the contrastive loss to actually
                # extract embeddings for inference, so we need to switch out any ppl
                # distillation loss for a contrastive loss.
                "contrastive": True,
                "ppl_distill": False,
            },
            use_cache=False,
        ),
    )

    with (checkpoint_path / "config.yml").open() as f:
        config = yaml.safe_load(f)

    if "contrastive_proj_dim" in config:
        embedding_dim = config["contrastive_proj_dim"]
    else:
        embedding_dim = config["hidden_size"]

    tokenizer = get_tokenizer(config["tokenizer_type"])
    end_of_query_token_id: int = tokenizer.ret_endofquery_id  # type: ignore[attr-defined]
    end_of_key_token_id: int = tokenizer.ret_endofkey_id  # type: ignore[attr-defined]

    query_model = NeoxEmbeddingModel(
        model,
        embedding_token_id=end_of_query_token_id,
        embedding_dim=embedding_dim,
        end_of_query_token_id=end_of_query_token_id,
        end_of_key_token_id=end_of_key_token_id,
    )

    doc_model = NeoxEmbeddingModel(
        model,
        embedding_token_id=end_of_key_token_id,
        embedding_dim=embedding_dim,
        end_of_query_token_id=end_of_query_token_id,
        end_of_key_token_id=end_of_key_token_id,
    )

    return query_model, doc_model
