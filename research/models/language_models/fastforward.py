"""Implementation of language models in fastforward."""

import json
import logging
import threading
from collections.abc import Sequence
from pathlib import Path

import torch
from typing_extensions import override

from base.fastforward import cached_attention, fwd_utils
from base.fastforward.fwd import AttentionFactory, ForwardStepFn, ModelSpec
from research.models.language_model import LanguageModel, LogitsGenerator, Tokens

logger = logging.getLogger(__file__)


class FastForwardLanguageModel(LanguageModel):
    def __init__(
        self,
        model_spec: ModelSpec,
        step_fn: ForwardStepFn,
        attention_factory: AttentionFactory,
        dtype: torch.dtype = torch.bfloat16,
    ):
        """Create a fastforward language model.

        Args:
            model_spec: The model specification.
            step_fn: The FastForward step funciton to use to generate embeddings.
            attention_factory: The attention factory to use.
            dtype: The dtype of model logits.
        """
        self.model_spec = model_spec
        self._step_fn = step_fn
        self._attn_cache = attention_factory(
            max_length=model_spec.max_position_embeddings
        )
        # Cache the logits for the previous tokens.
        self._logits_cache = torch.zeros(
            size=(self.max_sequence_length, model_spec.vocab_size),
            dtype=dtype,
            device="cuda",
        )
        self._tokens_cache: list[int] = []
        self._lock = threading.RLock()
        self._generator_open = False

    @property
    def vocab_size(self) -> int:
        return self.model_spec.vocab_size

    @property
    def max_sequence_length(self) -> int:
        return self.model_spec.max_position_embeddings

    def _find_common_prefix(self, tokens: list[int], start_idx: int) -> int:
        """Find the common prefix index of `tokens` with the cached tokens list.

        Returns:
            The index after the last common token in `tokens`. If there is no common
            prefix, returns 0.
        """
        for i, token in enumerate(tokens):
            if (
                start_idx + i >= len(self._tokens_cache)
            ) or token != self._tokens_cache[start_idx + i]:
                return i
        return len(tokens)

    @override
    def generate_logits(
        self,
        tokens: Tokens,
    ) -> LogitsGenerator:
        # NOTE(arun): The correctness of the below code depends on having exclusive
        # access to the attention, logits and tokens cache. We only allow a single
        # generator at a time and will raise an error if multiple threads attempt to
        # create one. If callers want to support multi-threading, please guard the
        # LanguageModel with a lock.
        with self._lock:
            if self._generator_open:
                raise RuntimeError(
                    "A generator is already open; you must close it first."
                )
            self._generator_open = True

        try:
            prev_idx = 0
            while True:
                # Loop invariant: The first prev_idx entries of the token, attention and
                # logit cache are the first prev_idx tokens of the current input stream,
                # and `tokens` refers to the remaining tokens.
                if prev_idx + len(tokens) > self.max_sequence_length:
                    raise IndexError(
                        "Received a token sequence that's too long: "
                        f"{prev_idx=} + {len(tokens)=} > {self.max_sequence_length=}."
                    )
                # We only need to look for matches in previous_tokens after prev_idx.
                idx = self._find_common_prefix(tokens, prev_idx)
                # Skip computation (and resetting the attention cache) if possible.
                next_idx = prev_idx + len(tokens)
                if idx < len(tokens):
                    logger.debug("Resetting attn_cache to: %d", prev_idx + idx)
                    self._attn_cache.reset(prev_idx + idx)
                    # NOTE(arun): the step function updates the attention cache to
                    # include the tokens from idx onwards.
                    step_logits = self._step_fn(
                        tokens[idx:], self._attn_cache
                    ).checked_cast(torch.Tensor)
                    self._logits_cache[prev_idx + idx : next_idx] = step_logits
                    self._tokens_cache[prev_idx + idx :] = tokens[idx:]
                tokens_or_none = yield self._logits_cache[prev_idx:next_idx]
                if tokens_or_none is None:
                    break
                tokens = tokens_or_none
                prev_idx = next_idx
        finally:
            with self._lock:
                self._generator_open = False


def create_from_starcoder2_checkpoint(
    checkpoint_path: Path | str,
    checkpoint_sha256: str | None = None,
    round_sizes: Sequence[int] = (32, 64, 128, 256, 512, cached_attention.MAX_Q_LEN),
    use_fp8: bool = False,
) -> FastForwardLanguageModel:
    """Create a fastforward language model from a StarCoder2 checkpoint.

    Args:
        checkpoint_path: The path to the checkpoint.
        checkpoint_sha256: If provided, a SHA256 checksum used to verify the checkpoint.
            Defaults to the checksum provided in {checkpoint_path}/params.json
        round_sizes: Round sizes we will compile cuda graphs for. Input is automatically
            padded to these sizes.
        use_fp8: If true, use the FP8 model.

    Returns:
        The corresponding LanguageModel.
    """
    # Keep StarCoder2 imports local to this function to only pull in what you need.
    from base.fastforward.starcoder.fwd_starcoder2 import (
        StarCoder2AttentionFactory,
        StarCoder2ModelSpec,
    )

    if use_fp8:
        from base.fastforward.starcoder.fwd_starcoder2_fp8 import (
            StarCoder2,
            generate_step_fn,
        )
    else:
        from base.fastforward.starcoder.fwd_starcoder2 import (
            StarCoder2,
            generate_step_fn,
        )

    checkpoint_path = Path(checkpoint_path)

    spec_file = checkpoint_path / "params.json"
    spec_params = json.loads(spec_file.read_text())
    # We don't use this key any more.
    spec_params.pop("rotary_cast_sincos_as_input", None)
    spec_params.pop("uses_yarn", None)

    spec_params["checkpoint_path"] = str(checkpoint_path)
    if checkpoint_sha256 is not None:
        spec_params["checkpoint_sha256"] = checkpoint_sha256

    spec_params["attn_split_head_mode"] = cached_attention.SplitHeadModes(
        spec_params["attn_split_head_mode"],
    )

    model_spec = StarCoder2ModelSpec(**spec_params)

    ffwd_model = generate_step_fn(
        model_spec,
        auto_capture_graphs=True,
        batch_sizes=round_sizes,
    )
    assert isinstance(ffwd_model, StarCoder2)
    # Prepare the model for evaluation.
    ffwd_model.eval()
    ffwd_model.requires_grad_(False)

    step_fn = fwd_utils.pad_and_step(ffwd_model, round_sizes=round_sizes)

    attn_factory = StarCoder2AttentionFactory(
        model_spec,
        attention_impl=cached_attention.AttentionImpl.MULTI_REQUEST_FLASH,
    )

    return FastForwardLanguageModel(
        model_spec,
        step_fn,
        attn_factory,
        dtype=torch.bfloat16,
    )


def create_from_llama3_checkpoint(
    checkpoint_path: Path | str,
    checkpoint_sha256: str | None = None,
    round_sizes: Sequence[int] = (32, 64, 128, 256, 512, cached_attention.MAX_Q_LEN),
    use_fp8: bool = False,
) -> FastForwardLanguageModel:
    """Create a fastforward language model from a LLaMA3/LLaMA3.1 checkpoint.

    Args:
        checkpoint_path: The path to the checkpoint.
        checkpoint_sha256: If provided, a SHA256 checksum used to verify the checkpoint.
            Defaults to the checksum provided in {checkpoint_path}/params.json
        round_sizes: Round sizes we will compile cuda graphs for. Input is automatically
            padded to these sizes.
        use_fp8: If true, use the FP8 model.

    Returns:
        The corresponding LanguageModel.
    """
    # Keep imports local to this function to only pull in what you need.
    from base.fastforward.llama.fwd_llama import LlamaAttentionFactory
    from base.fastforward.llama.model_specs import LlamaModelSpec

    if use_fp8:
        from base.fastforward.llama.fwd_llama_fp8 import (
            Llama,
            generate_step_fn,
        )
    else:
        from base.fastforward.llama.fwd_llama import (
            Llama,
            generate_step_fn,
        )

    checkpoint_path = Path(checkpoint_path)

    spec_file = checkpoint_path / "params.json"
    spec_params = json.loads(spec_file.read_text())

    spec_params["checkpoint_path"] = str(checkpoint_path)
    if checkpoint_sha256 is not None:
        spec_params["checkpoint_sha256"] = checkpoint_sha256

    spec_params["attn_split_head_mode"] = cached_attention.SplitHeadModes(
        spec_params["attn_split_head_mode"],
    )

    model_spec = LlamaModelSpec(**spec_params)

    ffwd_model = generate_step_fn(
        model_spec,
        auto_capture_graphs=True,
        batch_sizes=round_sizes,
    )
    assert isinstance(ffwd_model, Llama)
    # Prepare the model for evaluation.
    ffwd_model.eval()
    ffwd_model.requires_grad_(False)

    step_fn = fwd_utils.pad_and_step(ffwd_model, round_sizes=round_sizes)

    attn_factory = LlamaAttentionFactory(
        model_spec,
        attention_impl=cached_attention.AttentionImpl.MULTI_REQUEST_FLASH,
    )

    return FastForwardLanguageModel(
        model_spec,
        step_fn,
        attn_factory,
        dtype=torch.bfloat16,
    )
