"""Convert Qwen3 models from HuggingFace format to FastBackward format.

This script converts a Qwen3 model from HuggingFace format to FastBackward format,
handling model parallelism internally as a single-process operation.

Supports both base Qwen3 models and Qwen3-Embedding models.

Note: For most use cases, it's recommended to use the batch_hf2fbw_qwen3.py script
instead, which provides better error handling, logging, and support for converting
multiple model sizes and parallelism configurations in one command.

Usage:
    python research/tools/ckp_converter/hf2fbw_qwen3.py \
        --input_path /path/to/huggingface/model \
        --output_path /path/to/output/directory \
        --size 8b \
        --mp_size 4

    # For embedding models:
    python research/tools/ckp_converter/hf2fbw_qwen3.py \
        --input_path /path/to/huggingface/embedding/model \
        --output_path /path/to/output/directory \
        --size embedding-8b \
        --mp_size 1
"""

from research.fastbackward.checkpointing.huggingface import load_huggingface_checkpoint
from research.fastbackward.model import ModelArgs, GenericAttnSpec, correct_model_args

from dataclasses import dataclass
import torch
import json
import argparse
from pathlib import Path


@dataclass
class Qwen3Config:
    hidden_size: int
    num_layers: int
    num_attention_heads: int
    num_kv_heads: int
    head_dim: int
    intermediate_size: int
    max_position_embeddings: int
    multiple_of: int
    ffn_dim_multiplier: float
    vocab_size: int


# Configuration for Qwen3 models based on model specs
qwen3_config: dict[str, Qwen3Config] = {
    "32b": Qwen3Config(
        hidden_size=5120,
        num_layers=64,
        num_attention_heads=64,
        num_kv_heads=8,
        head_dim=128,
        intermediate_size=25600,
        max_position_embeddings=40960,
        multiple_of=256,
        ffn_dim_multiplier=1.87,
        vocab_size=151936,
    ),
    "14b": Qwen3Config(
        hidden_size=5120,
        num_layers=40,
        num_attention_heads=40,
        num_kv_heads=8,
        head_dim=128,
        intermediate_size=17408,
        max_position_embeddings=40960,
        multiple_of=256,
        ffn_dim_multiplier=1.27,
        vocab_size=151936,
    ),
    "8b": Qwen3Config(
        hidden_size=4096,
        num_layers=36,
        num_attention_heads=32,
        num_kv_heads=8,
        head_dim=128,
        intermediate_size=12288,
        max_position_embeddings=40960,
        multiple_of=256,
        ffn_dim_multiplier=1.12,
        vocab_size=151936,
    ),
    "4b": Qwen3Config(
        hidden_size=2560,
        num_layers=36,
        num_attention_heads=32,
        num_kv_heads=8,
        head_dim=128,
        intermediate_size=9728,
        max_position_embeddings=40960,
        multiple_of=256,
        ffn_dim_multiplier=1.42,
        vocab_size=151936,
    ),
    "1.7b": Qwen3Config(
        hidden_size=2048,
        num_layers=28,
        num_attention_heads=16,
        num_kv_heads=8,
        head_dim=128,
        intermediate_size=6144,
        max_position_embeddings=40960,
        multiple_of=256,
        ffn_dim_multiplier=1.1,
        vocab_size=151936,
    ),
    "0.6b": Qwen3Config(
        hidden_size=1024,
        num_layers=28,
        num_attention_heads=16,
        num_kv_heads=8,
        head_dim=128,
        intermediate_size=3072,
        max_position_embeddings=40960,
        multiple_of=256,
        ffn_dim_multiplier=1.1,
        vocab_size=151936,
    ),
    # Qwen3-Embedding model configurations
    "embedding-0.6b": Qwen3Config(
        hidden_size=1024,
        num_layers=28,
        num_attention_heads=16,
        num_kv_heads=8,
        head_dim=128,
        intermediate_size=3072,
        max_position_embeddings=32768,  # Different from base model
        multiple_of=256,
        ffn_dim_multiplier=1.1,
        vocab_size=151669,  # Different vocab size for embedding model
    ),
    "embedding-4b": Qwen3Config(
        hidden_size=2560,
        num_layers=36,
        num_attention_heads=32,
        num_kv_heads=8,
        head_dim=128,
        intermediate_size=9728,
        max_position_embeddings=40960,
        multiple_of=256,
        ffn_dim_multiplier=1.42,
        vocab_size=151665,  # Different vocab size for embedding model
    ),
    "embedding-8b": Qwen3Config(
        hidden_size=4096,
        num_layers=36,
        num_attention_heads=32,
        num_kv_heads=8,
        head_dim=128,
        intermediate_size=12288,
        max_position_embeddings=40960,
        multiple_of=256,
        ffn_dim_multiplier=1.12,
        vocab_size=151665,  # Different vocab size for embedding model
    ),
}


def main(
    path: str,
    output_path: str,
    size: str,
    mp_size: int = 1,
):
    """
    Convert a Qwen3 model from HuggingFace format to FastBackward format.
    Args:
        path: Path to the HuggingFace model
        output_path: Path to save the FastBackward model
        size: Model size (e.g., "32b")
        mp_size: Model parallel size
    """
    # Ensure the model size is supported
    if size.lower() not in qwen3_config:
        raise ValueError(
            f"Unsupported model size: {size}. Available sizes: {list(qwen3_config.keys())}"
        )

    # Get the configuration for the specified model size
    config = qwen3_config[size.lower()]

    # Create attention configuration
    attn_config = GenericAttnSpec(
        hidden_dim=config.hidden_size,
        n_heads=config.num_attention_heads,
        n_kv_heads=config.num_kv_heads,
        norm_type="rmsnorm",
        pos_embed_type="rope",
        bias=False,
        qkv_bias=False,
        use_qk_norm=True,  # Enable query and key normalization for Qwen3
        head_dim=config.head_dim,
    )

    # Create model arguments
    model_args = ModelArgs(
        ffn_type="glu",  # Qwen3 uses SwiGLU activation (implemented as "glu" in our codebase)
        bias="none",
        norm_type="rmsnorm",
        pos_embed_type="rope",
        dim=config.hidden_size,
        n_layers=config.num_layers,
        n_heads=config.num_attention_heads,
        n_kv_heads=config.num_kv_heads,
        vocab_size=config.vocab_size,
        multiple_of=config.multiple_of,
        ffn_dim_multiplier=config.ffn_dim_multiplier,
        norm_eps=1e-06,
        rope_theta=1000000.0,
        attn_config=attn_config,
        max_seq_len=config.max_position_embeddings,
    )

    # Create output directory if it doesn't exist
    Path(output_path).mkdir(parents=True, exist_ok=True)

    # Convert and save model for each MP rank
    for mp_rank in range(mp_size):
        state_dict = load_huggingface_checkpoint(
            path, mp_world_size=mp_size, mp_rank=mp_rank
        )
        torch.save(state_dict, f"{output_path}/consolidated.{mp_rank:02d}.pth")

    # Save model args
    model_args_json = ModelArgs.schema().dump(correct_model_args(model_args))
    with open(f"{output_path}/params.json", "w") as f:
        json.dump(model_args_json, f, indent=2)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Convert Qwen3 model to FastBackward format"
    )
    parser.add_argument(
        "--input_path", type=str, required=True, help="Path to HuggingFace model"
    )
    parser.add_argument(
        "--output_path", type=str, required=True, help="Path to save FastBackward model"
    )
    parser.add_argument(
        "--size",
        type=str,
        required=True,
        help="Model size (e.g., '32b', '8b', 'embedding-0.6b', 'embedding-4b', 'embedding-8b')",
    )
    parser.add_argument("--mp_size", type=int, default=1, help="Model parallel size")

    args = parser.parse_args()
    main(
        path=args.input_path,
        output_path=args.output_path,
        size=args.size,
        mp_size=args.mp_size,
    )
