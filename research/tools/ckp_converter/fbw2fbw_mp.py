"""Convert a FastBackward checkpoint from n-way model parallelism to a $MP_SIZE-way model parallelism checkpoint.

Usage:
    python research/tools/ckp_converter/fbw2fbw_mp.py --i $FBWD_CKPT_DIR --o $FBWD_CKPT_DIR_NEW_MP --mp $MP_SIZE
"""

import argparse
import json
import pathlib

import torch
from tqdm import tqdm

from research.core.utils_for_file import read_json
from research.fastbackward.checkpointing.utils import (
    merge_model_parallel_consolidated_checkpoints,
    split_consolidated_checkpoint,
)
from research.fastbackward.model import ModelArgs


def load_fastbackward_model(fb_ckpt_dir):
    """Loads the state dicts from a model-parallel FastBackward checkpoint."""
    weight_paths = sorted(fb_ckpt_dir.glob("consolidated.*.pth"))
    print(f"Loading weights for {len(weight_paths)} MP ranks.")
    state_dicts = []
    for weight_path in tqdm(weight_paths, desc="Loading weights"):
        state_dict = torch.load(weight_path, map_location="cpu", weights_only=True)
        state_dicts.append(state_dict)
    return state_dicts


def main(input_ckpt_dir: pathlib.Path, output_ckpt_dir: pathlib.Path, mp_size: int):
    """Converts a FastBackward checkpoint to FastForward."""
    if str(input_ckpt_dir) == str(output_ckpt_dir):
        raise ValueError(
            "The input and output checkpoint directories must be different."
        )
    dct = read_json(input_ckpt_dir / "params.json")
    dct.pop("dropout", None)
    model_args = ModelArgs.load_from_dict(dct)

    state_dicts = load_fastbackward_model(input_ckpt_dir)
    print("Finished loading state dicts.")
    merged_state_dict = merge_model_parallel_consolidated_checkpoints(
        model_args, state_dicts
    )
    output_ckpt_dir.mkdir(parents=False, exist_ok=True)
    with (output_ckpt_dir / "params.json").open("w") as f:
        f.write(
            json.dumps(
                ModelArgs.schema().dump(model_args),
                indent=2,
            )
        )
    for rank in range(mp_size):
        cur_state_dict = split_consolidated_checkpoint(
            model_args, merged_state_dict, mp_size, rank
        )
        torch.save(cur_state_dict, output_ckpt_dir / f"consolidated.{rank:02d}.pth")
        print(f"Saving the rank-{rank} weights...")
    print("Finished saving.")


def parse_args():
    """Parse command-line arguments for the script."""
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for converting FastBackward checkpoints into different model parallel sizes.",
    )
    parser.add_argument(
        "--i",
        type=pathlib.Path,
        required=True,
        help="Path to directory containing fastbackward checkpoint",
    )
    parser.add_argument(
        "--o",
        type=pathlib.Path,
        required=True,
        help="Path to directory where to store fastforward checkpoint",
    )
    parser.add_argument(
        "--mp",
        type=int,
        required=True,
        help="The number of model parallel sizes of the output checkpoint.",
    )
    return parser.parse_args()


if __name__ == "__main__":
    cli_args = parse_args()
    main(cli_args.i, cli_args.o, cli_args.mp)
