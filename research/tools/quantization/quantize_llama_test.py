r"""Test LLAMA quantization by quantizing a LLAMA-350M model."""

import pytest
import torch

from base.fastforward import (
    fwd_utils,
    quantize_utils,
)
from research.core.data_paths import canonicalize_path
from base.fastforward.llama import (
    fwd_llama,
    fwd_llama_fp8,
    model_specs,
)


@pytest.fixture(scope="module")
def model_fixture():
    """Load the weights of a llama-350m model to use for all tests."""
    ms = model_specs.get_llama_model_spec(
        model_name="llama-350m",
        checkpoint_path=canonicalize_path(
            "/mnt/efs/augment/checkpoints/v2/llama/fastforward/llama-350m"
        ),
        checkpoint_sha256="e22e039ec50c175821833e5b7110e6ffb2ee881a310bccc13527f85861593b5e",
    )
    bf16_model = fwd_llama.generate_step_fn(ms)
    attn_factory = fwd_llama.LlamaAttentionFactory(
        ms=ms,
        capture_attn_maxes_for_quantization=True,
        pre_attention_kernel_fusion=False,
    )
    yield ms, bf16_model, attn_factory


def test_quantize_model(model_fixture):
    torch.set_printoptions(sci_mode=False, precision=5, linewidth=200)

    ms, bf16_model, attn_factory = model_fixture
    attn = attn_factory(64)
    # Note: want a non-attn-max-capturing attention factory for the FP8 model.
    attn_factory_fp8 = fwd_llama.LlamaAttentionFactory(
        ms=ms, capture_attn_maxes_for_quantization=False
    )
    attn_fp8 = attn_factory_fp8(64)

    assert isinstance(bf16_model, fwd_llama.Llama)

    num_output_tokens = 5

    calibration_data = [
        {
            "tokens": [
                21,
                12000,
                5788,
                3460,
                328,
                4865,
                203,
                589,
                2575,
                2262,
                203,
                202,
                1216,
                440,
                8279,
                5788,
            ]
        }
    ]

    attn.reset()
    bf16_logits = bf16_model.forward(calibration_data[0]["tokens"], attn).checked_cast(
        torch.Tensor
    )
    bf16_tokens = torch.topk(bf16_logits[-num_output_tokens:], k=2).indices

    attn.reset()
    weights_e4m3, _ = quantize_utils.quantize_layer(
        layer_name="llama",
        layer_inp=bf16_model,
        calibration_data=calibration_data,
        reset_fn=attn.reset,
        attn=attn,
    )
    quantize_utils.update_weights_with_attn_scales(
        weights_e4m3, attn.captured_attn_maxes, ms.num_layers
    )

    fp8_model = fwd_llama_fp8.Llama(ms=ms)
    fp8_model.load_state_dict(weights_e4m3)

    attn_fp8.reset()
    fp8_step_fn = fwd_utils.pad_and_step(fp8_model, round_sizes=[8])
    fp8_logits = fp8_step_fn(calibration_data[0]["tokens"], attn_fp8).checked_cast(
        torch.Tensor
    )
    fp8_top_k_tokens = torch.topk(fp8_logits[-num_output_tokens:], k=2).indices

    assert (
        (bf16_tokens == fp8_top_k_tokens).any(dim=-1).all()
    ), f"{bf16_tokens=} but {fp8_top_k_tokens=}."
