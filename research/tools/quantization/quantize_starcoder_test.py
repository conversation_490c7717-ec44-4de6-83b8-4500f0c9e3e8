"""Tests for base.fastforward.quantize_starcoder."""

import pytest
import torch

from base.fastforward import fwd_utils
from base.fastforward.fwd import AttentionFactory, ForwardStepFn
from base.fastforward.starcoder import fwd_starcoder
from research.core.data_paths import canonicalize_path
from research.tools.quantization import quantize_starcoder


def _model_spec_1b():
    ms = fwd_utils.get_model_spec_from_neox_checkpoint(
        checkpoint_path=canonicalize_path(
            "/mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint_v2"
        )
    )
    ms.checkpoint_sha256 = (
        "8a26c397f2c233ec1c0cac92e66b85def17f8322bc0f53f84c6233fc57c6a220"
    )
    return ms


def create_1b_fwd() -> tuple[ForwardStepFn, AttentionFactory]:
    """Creates the forward and attention factory for starcoderbase 1B."""
    model_spec = _model_spec_1b()
    return (
        fwd_starcoder.generate_step_fn(model_spec, auto_capture_graphs=False),
        fwd_starcoder.StarcoderAttentionFactory(model_spec),
    )


@pytest.mark.parametrize("smooth_quant", [True, False])
def test_quantize_model(smooth_quant: bool):
    torch.set_printoptions(sci_mode=False, precision=5, linewidth=200)
    fp16_model, attn_factory = create_1b_fwd()
    assert isinstance(fp16_model, fwd_starcoder.StarCoder)

    attn = attn_factory(128)
    calibration_data = [[589, 17964, 81, 5860, 2262, 203]]

    fp16_tokens, fp16_score = fwd_utils.generate(
        fp16_model, attn, calibration_data[0], 16
    )
    attn.reset()
    print(f"FP16 score: {fp16_score:.4f}")

    fp8_model, fp8_attn_factory = quantize_starcoder.quantize_model(
        fp16_model,
        attn,
        calibration_data,
        _model_spec_1b(),
        smooth_quant=smooth_quant,
    )
    attn.reset()

    fp8_attn = fp8_attn_factory(128)
    fp8_step_fn = fwd_utils.pad_and_step(fp8_model, round_sizes=[8])
    fp8_tokens, fp8_score = fwd_utils.generate(
        fp8_step_fn, fp8_attn, calibration_data[0], 16
    )
    print(f"FP8 score (w/smooth_quant={smooth_quant}): {fp8_score:.4f}")
    assert fp16_tokens == fp8_tokens
