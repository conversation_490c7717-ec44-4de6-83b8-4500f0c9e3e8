{"port": 8888, "mtls": false, "server_cert_path": "", "server_key_path": "", "root_cert_path": "", "client_mtls": false, "client_cert_path": "", "client_key_path": "", "client_ca_path": "", "max_seq_length": 8192, "round_sizes": [32, 64, 128, 256, 512], "cache_size": 4, "model_arch": {"arch_type": "LLAMA_FP8", "num_layers": 62, "vocab_size": 32256, "emb_dim": 7168, "num_heads": 8, "head_dim": 128, "num_queries_per_head": 7, "mlp_dim_divisible_by": 128, "ffn_dim_multiplier": 1.0, "attn_split_head_mode": "KV_HEADS", "norm_eps": 1e-05, "rotary_pct": 1.0, "rotary_theta": 100000.0, "rotary_scaling_factor": 4.0, "max_position_embeddings": 8192}, "model_name": "droid_187_33B_FP8_edit_deploy", "weights_path": "/mnt/efs/augment/checkpoints/code-edit/droid/droid-187_33B_FP8", "max_rpc_threads": 32, "speculation_models": [{"model_name": "droid-1b-mqa-187-fp8", "model_arch": {"arch_type": "LLAMA_FP8", "num_layers": 24, "vocab_size": 32256, "emb_dim": 2048, "num_heads": 1, "head_dim": 128, "num_queries_per_head": 16, "mlp_dim_divisible_by": 128, "ffn_dim_multiplier": 1.0, "attn_split_head_mode": "Q_PER_HEADS", "norm_eps": 1e-06, "rotary_pct": 1.0, "rotary_theta": 100000.0, "rotary_scaling_factor": 4.0, "max_position_embeddings": 8192}, "weights_path": "/mnt/efs/augment/checkpoints/yury/droid-1b-mqa-187-ff-fp8", "weights_sha256": "a4e93f3c84a72ac4ef3872d0798f066aa93f3f7120752aefa2886e9175269a49", "round_sizes": [32, 64, 128, 256, 512, 1024, 2048]}], "model_parallelism": 2, "non_neural_speculation_model": "longest_overlap"}