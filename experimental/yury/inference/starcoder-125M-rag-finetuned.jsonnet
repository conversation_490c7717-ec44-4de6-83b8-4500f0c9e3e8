// a rogue 1B model
//
// Improve code completion quality by fine-tuning StarCoder model to better use retrieval
//
// Model Card: https://www.notion.so/Q3-2023-Rogue-models-71771c1ae50446fd9c96a8e721c2168e?pvs=4
local lang_presets = (import 'models/inference/configs/languages.jsonnet').presets;
{
  name: 'rogue.1B',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/yury/starcoder-125M/python-only-dp0.1-rag-finetuned/global_step2500',  // pragma: allowlist secret
  model_type: 'INFERENCE',
  inference: {
    tokenizer_name: 'rogue',
    retrieval_type: 'DENSE',
    prompt_formatter_name: 'rogue',
    languages: lang_presets.starcoder,
    max_context_length: 4 * 1024,
    extra_stop_tokens: ['<|skip|>', '<|pause|>'],
  },
  model_arch: {
    arch_type: 'STARCODER',
    num_layers: 12,
    vocab_size: 51200,
    emb_dim: 1024,
    num_heads: 8,
    head_dim: 128,
    norm_eps: 1e-5,
  },
}
