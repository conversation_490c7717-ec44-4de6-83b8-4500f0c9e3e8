import pytest
from pathlib import Path
from experimental.yury.smart_paste_eval.html_viewer import (
    save_comparison_html,
    format_diff,
    format_code,
)
from experimental.yury.smart_paste_eval.utils import (
    ComparisonResult,
    ModelEvaluationResults,
    DiffResult,
)


def test_save_comparison_html(tmp_path: Path):
    # Create test data
    ref_results = ModelEvaluationResults(
        sample_results={},
        category_stats={"test": {"correct": 1, "total": 2}},
        total_correct=1,
        total_processed=2,
        total_skipped=0,
        model_name="model1",
        url="http://test1",
    )

    results = ModelEvaluationResults(
        sample_results={},
        category_stats={"test": {"correct": 2, "total": 2}},
        total_correct=2,
        total_processed=2,
        total_skipped=0,
        model_name="model2",
        url="http://test2",
    )

    diff = DiffResult(
        uuid="test1",
        request_message="Test request",
        code_block="def test():\n    pass",
        ref_correct=True,
        output_correct=False,
        ref_diff_against_original="- old\n+ new",
        ref_diff_against_expected="",
        output_diff_against_original="- old\n+ different",
        output_diff_against_expected="- expected\n+ got",
    )

    comparison = ComparisonResult(
        ref_results=ref_results,
        results=results,
        num_different=1,
        num_regressions=1,
        num_improvements=0,
        diffs=[diff],
    )

    # Save HTML
    output_path = tmp_path / "comparison.html"
    save_comparison_html(comparison, output_path)

    # Verify file exists and contains expected content
    assert output_path.exists()
    content = output_path.read_text()

    # Check that all important elements are present
    assert "model1" in content
    assert "model2" in content
    assert "Test request" in content
    # Look for code parts that should be present regardless of syntax highlighting
    assert "test" in content
    assert "pass" in content
    # Check correctness indicators
    assert "✓ Correct" in content  # ref_correct=True
    assert "✗ Incorrect" in content  # output_correct=False
    # Check for the diff content
    assert "- expected" in content  # From output_diff_against_expected
    assert "+ got" in content  # From output_diff_against_expected
    # Check for section headers
    assert "Regressions (1 cases)" in content


def test_format_diff():
    diff_text = """
    + added line
    - removed line
    context line
    @@ -1,3 +1,3 @@
    """
    formatted = format_diff(diff_text)
    assert 'class="diff-add"' in formatted
    assert 'class="diff-remove"' in formatted
    assert 'class="diff-context"' in formatted
    assert 'class="diff-info"' in formatted


def test_format_code():
    code = "def test():\n    pass"
    formatted = format_code(code)
    assert "def" in formatted  # Should be syntax highlighted
    assert "test" in formatted
    assert "pass" in formatted
