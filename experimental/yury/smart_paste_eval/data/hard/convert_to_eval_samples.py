import dataclasses
import json
from pathlib import Path
from base.prompt_format_chat.smart_paste_prompt_formatter import SmartPastePromptInput
from base.prompt_format.common import Exchange
from experimental.yury.smart_paste_eval.data.eval_sample import EvalSample


@dataclasses.dataclass(frozen=True)
class SmartPastePromptInputWithRID(SmartPastePromptInput):
    request_id: str = ""
    """Original request ID."""

    @classmethod
    def from_dict(cls, d: dict) -> "SmartPastePromptInputWithRID":
        d = d.copy()
        d["chat_history"] = [
            Exchange(
                request_message=exchange["request_message"],
                response_text=exchange["response_text"],
                request_id=exchange.get("request_id"),
            )
            for exchange in d["chat_history"]
        ]
        return cls(**d)

    @classmethod
    def from_json(cls, json_str: str) -> "SmartPastePromptInputWithRID":
        return cls.from_dict(json.loads(json_str))


def convert_samples(input_path: Path, output_path: Path) -> None:
    with input_path.open("r") as f_in, output_path.open("w") as f_out:
        for line in f_in:
            smart_paste_input_with_rid = SmartPastePromptInputWithRID.from_json(line)
            request_id = smart_paste_input_with_rid.request_id
            smart_paste_input_with_rid.__dict__.pop("request_id")
            smart_paste_input = SmartPastePromptInput(
                **smart_paste_input_with_rid.__dict__
            )
            eval_sample = EvalSample(
                uuid=request_id,
                chat_request_id=request_id,
                prompt_input=smart_paste_input,
            )

            json.dump(dataclasses.asdict(eval_sample), f_out)
            f_out.write("\n")


if __name__ == "__main__":
    input_path = Path("experimental/yury/smart_paste_eval/data/hard/raw_data.jsonl")
    output_path = Path(
        "experimental/yury/smart_paste_eval/data/hard/raw_data_eval_samples.jsonl"
    )

    convert_samples(input_path, output_path)
