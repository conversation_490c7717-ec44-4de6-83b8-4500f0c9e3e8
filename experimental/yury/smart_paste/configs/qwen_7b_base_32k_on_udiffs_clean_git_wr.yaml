determined:
  description: "Smart paste model training on udiffs."
  workspace: Dev
  project: yury

augment:
  project_group: finetuning
  podspec_path: "8xH100.yaml"
  gpu_count: 128

fastbackward_configs:
 - configs/qwen25coder_7b.py

fastbackward_args:
  loss_mask_policy: negative_tokens
  batch_size: 1
  gradient_accumulation_steps: 4
  warmup_iters: 100
  learning_rate: 1e-5
  min_lr: 1e-6
  decay_lr: True
  max_epochs: 1
  eval_interval: 100
  block_size: 32768
  use_activation_checkpointing: True

  train_data_path: /mnt/efs/augment/user/yury/smart_paste/data_new/v2/06_1_training_data_stage_32k_udiff_clean_git_wr/train
  eval_data_path: valid_non_emptysc@/mnt/efs/augment/user/yury/smart_paste/data_new/v2/06_1_training_data_stage_32k_udiff_clean_git_wr/valid_non_emptysc;valid_emptysc@/mnt/efs/augment/user/yury/smart_paste/data_new/v2/06_1_training_data_stage_32k_udiff_clean_git_wr/valid_emptysc

  checkpoint_optimizer_state: False
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-7B

  tokenizer_name: qwen25coder
  use_research_tokenizer: false
  visualize_logits_samples: 32
  model_parallel_size: 4
  use_sequence_parallel: True

  run_name: smartpaste_qwen_7b_datav2_1_32k_udiff_clean_git_wr
  wandb_project: yury-smart-paste
