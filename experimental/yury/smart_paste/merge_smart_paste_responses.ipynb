#%%
%load_ext autoreload
%autoreload 2
#%%
from pathlib import Path

OUTPUT_DIR = Path(
    "/mnt/efs/augment/user/yury/smart_paste/data_new/v2/05_1_merged_responses_fixnewlinesend"
)
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

OUTPUT_FILE = OUTPUT_DIR / "merged_smart_paste_responses.jsonl"


smart_paste_inputs = {}
claude_smart_paste_responses = {}
llama_smart_paste_responses = {}
#%%
import json
import tqdm
from pathlib import Path
import numpy as np
from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput
from base.prompt_format.common import Exchange
from experimental.yury.smart_paste_lib.claude_utils import (
    extract_from_dict_and_apply_tool_calls,
    LineNotFound,
    InvalidToolCall,
    InvalidLineNumber,
)

DATA_V1 = [
    (
        "/mnt/efs/augment/user/yury/smart_paste/data_new/v1/diffs_with_claude_edit_responses.claude_smart_paste_responses.v3.jsonl",
        "/mnt/efs/augment/user/yury/smart_paste/data_new/v1/04_llama_smart_paste_stage/smart_paste_responses.jsonl",
    )
]

n_success, n_apply_failed = 0, 0

for smart_paste_inputs_path, llama_smart_paste_responses_path in DATA_V1:
    with open(smart_paste_inputs_path, "r") as f:
        for line in tqdm.tqdm(f, desc=f"Loading {smart_paste_inputs_path}"):
            datum = json.loads(line)

            request_id = datum["uuid"]
            before_file_contents = datum["before_file"]["contents"]
            selected_code_crange = datum["selected_code_crange"]

            datum["aux"]["commit"] = {
                "after_file": datum["aux"]["original_commit_after_file"]
            }

            smart_paste_input = ResearchSmartPastePromptInput(
                request_id=request_id,
                path=datum["before_file"]["path"],
                prefix=before_file_contents[: selected_code_crange["start"]],
                selected_code=before_file_contents[
                    selected_code_crange["start"] : selected_code_crange["stop"]
                ],
                suffix=before_file_contents[selected_code_crange["stop"] :],
                code_block=datum["codeblock"],
                chat_history=[
                    Exchange(
                        request_id=request_id,
                        request_message=datum["instruction"],
                        response_text=datum["chat_response"],
                    )
                ],
                prefix_begin=0,
                suffix_end=len(before_file_contents),
                retrieved_chunks=[],
                context_code_exchange_request_id=request_id,
                target_path=datum["before_file"]["path"],
                target_file_content=before_file_contents,
                aux=datum["aux"],
            )
            claude_smart_paste_response = datum["after_file"]["contents"]
            claude_smart_paste_response_redo = extract_from_dict_and_apply_tool_calls(
                datum["before_file"]["contents"],
                datum["aux"]["claude_smart_paste_response"],
            )
            if claude_smart_paste_response != claude_smart_paste_response_redo:
                n_apply_failed += 1
                continue
            smart_paste_inputs[request_id] = smart_paste_input
            claude_smart_paste_responses[request_id] = claude_smart_paste_response
            n_success += 1

    with open(llama_smart_paste_responses_path, "r") as f:
        for line in tqdm.tqdm(f, desc=f"Loading {llama_smart_paste_responses_path}"):
            datum = json.loads(line)
            if datum["status"] != "success":
                continue
            llama_smart_paste_responses[datum["request_id"]] = datum["modified_file"]


print(
    f"Loaded {n_success} smart_paste_inputs, {n_apply_failed} failed to apply tool calls"
)
#%%
import json
import tqdm
from pathlib import Path
from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput
from base.prompt_format.common import Exchange

DATA_V2 = [
    (
        "/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs.jsonl",
        "/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses.jsonl",
        "/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses.jsonl",
    ),
    (
        "/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs_iter2.jsonl",
        "/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_claude_smart_paste_stage/smart_paste_responses_iter2.jsonl",
        "/mnt/efs/augment/user/yury/smart_paste/data_new/v2/04_llama_smart_paste_stage/smart_paste_responses_iter2.jsonl",
    ),
]

for (
    smart_paste_inputs_path,
    claude_smart_paste_responses_path,
    llama_smart_paste_responses_path,
) in DATA_V2:
    with open(smart_paste_inputs_path, "r") as f:
        for line in tqdm.tqdm(f, desc=f"Loading {smart_paste_inputs_path}"):
            smart_paste_input = ResearchSmartPastePromptInput.from_json(line)
            assert (
                smart_paste_input.request_id
                == smart_paste_input.context_code_exchange_request_id
            )
            assert (
                smart_paste_input.request_id
                == smart_paste_input.chat_history[-1].request_id
            )
            assert len(smart_paste_input.chat_history) in [1, 2], len(
                smart_paste_input.chat_history
            )
            smart_paste_inputs[smart_paste_input.request_id] = smart_paste_input

    with open(claude_smart_paste_responses_path, "r") as f:
        for line in tqdm.tqdm(f, desc=f"Loading {claude_smart_paste_responses_path}"):
            datum = json.loads(line)
            claude_smart_paste_responses[datum["request_id"]] = datum["modified_file"]

    with open(llama_smart_paste_responses_path, "r") as f:
        for line in tqdm.tqdm(f, desc=f"Loading {llama_smart_paste_responses_path}"):
            try:
                datum = json.loads(line)
            except json.decoder.JSONDecodeError:
                continue
            if datum["status"] != "success":
                continue
            llama_smart_paste_responses[datum["request_id"]] = datum["modified_file"]
#%%
print(f"Loaded {len(smart_paste_inputs)} smart_paste_inputs")
print(f"Loaded {len(claude_smart_paste_responses)} claude_smart_paste_responses")
print(f"Loaded {len(llama_smart_paste_responses)} llama_smart_paste_responses")
#%%
import pandas as pd

df = []

for request_id in smart_paste_inputs:
    if request_id not in claude_smart_paste_responses:
        continue
    claude_response = claude_smart_paste_responses[request_id]

    datum = {
        "request_id": request_id,
        "len_target_file": len(smart_paste_inputs[request_id].target_file_content),
    }

    original_commit = smart_paste_inputs[request_id].aux["commit"]["after_file"][
        "contents"
    ]

    if claude_response.strip() == original_commit.strip():
        datum["keep"] = True
    else:
        if request_id not in llama_smart_paste_responses:
            datum["unsure"] = True
        else:
            llama_response = llama_smart_paste_responses[request_id]
            if llama_response.strip() == claude_response.strip():
                datum["keep"] = True
            elif llama_response.strip() == original_commit.strip():
                datum["keep"] = True
            else:
                datum["throw_away"] = True
    df.append(datum)

df = pd.DataFrame(df)
df.describe()
#%%
import pandas as pd

# 1. Convert NaN to False for boolean columns
bool_columns = ["keep", "throw_away", "unsure"]
df[bool_columns] = df[bool_columns].fillna(False)

# 2. Compute distributions for True values in each boolean column
metrics = []

# Add stats for all rows
stats_all = (
    df["len_target_file"]
    .agg(
        {
            "mean": "mean",
            "p50": lambda x: x.quantile(0.5),
            "p75": lambda x: x.quantile(0.75),
            "p90": lambda x: x.quantile(0.90),
            "max": "max",
            "count": "count",
        }
    )
    .round(2)
)
stats_all = pd.DataFrame([stats_all])
stats_all["group"] = "all_rows"
metrics.append(stats_all)

# Add stats for keep OR unsure
keep_or_unsure = (
    df[df["keep"] | df["unsure"]]["len_target_file"]
    .agg(
        {
            "mean": "mean",
            "p50": lambda x: x.quantile(0.5),
            "p75": lambda x: x.quantile(0.75),
            "p90": lambda x: x.quantile(0.90),
            "max": "max",
            "count": "count",
        }
    )
    .round(2)
)
keep_or_unsure = pd.DataFrame([keep_or_unsure])
keep_or_unsure["group"] = "keep_or_unsure"
metrics.append(keep_or_unsure)

# Original per-column stats
for bool_col in bool_columns:
    stats = (
        df[df[bool_col] == True]["len_target_file"]
        .agg(
            {
                "mean": "mean",
                "p50": lambda x: x.quantile(0.5),
                "p75": lambda x: x.quantile(0.75),
                "p90": lambda x: x.quantile(0.90),
                "max": "max",
                "count": "count",
            }
        )
        .round(2)
    )

    stats = pd.DataFrame([stats])
    stats["group"] = bool_col
    metrics.append(stats)

# Combine all statistics into a single DataFrame
result = pd.concat(metrics)

# Reorder columns to put group first
cols = ["group"] + [col for col in result.columns if col != "group"]
result = result[cols]
result
#%%
from research.core.str_diff import build_str_diff, StrDiff, NoopSpan, AddedSpan, ModSpan

NEWLINES = "\n\r"


def last_span_only_adds_newline(str_diff: StrDiff):
    if len(str_diff.spans) == 0:
        # no changes, so no newline added
        return False
    other_spans_exist = False
    for span in str_diff.spans[:-1]:
        if not isinstance(span, NoopSpan):
            other_spans_exist = True
            break
    if not other_spans_exist:
        # There's only a single Add/Delete/Mod span,
        # so we must respect it even if it adds a newline
        return False

    last_span = str_diff.spans[-1]
    if isinstance(last_span, AddedSpan) and len(last_span.after.strip(NEWLINES)) == 0:
        # last span only adds a newline
        return True
    if (
        isinstance(last_span, ModSpan)
        and last_span.after.startswith(last_span.before)
        and last_span.after.rstrip(NEWLINES) == last_span.before.rstrip(NEWLINES)
    ):
        # last span only modifies code and adds a newline
        return True
    return False
#%%
import tqdm
import dataclasses
from base.diff_utils.diff_utils import File
from experimental.yury.smart_paste.augmentations import (
    BeforeAfterFiles,
)

request_ids_to_save = df[(df["keep"] == True) | (df["unsure"] == True)][
    "request_id"
].to_list()

print(f"Preparing to save {len(request_ids_to_save)} samples")


def has_trailing_newline(s: str) -> bool:
    return s.endswith("\n")


counter = 0
n_skip = 0
n_empty_code_block = 0
n_no_claude_response = 0
n_chose_llama_response, n_chose_claude_response = 0, 0
n_original_no_new_lines, n_claude_changed_new_lines = 0, 0
n_last_span_only_adds_newline = 0

with OUTPUT_FILE.open("w") as f:
    for request_id in tqdm.tqdm(smart_paste_inputs):
        prompt_input = smart_paste_inputs[request_id]
        if len(prompt_input.code_block) == 0:
            n_empty_code_block += 1
            continue

        if request_id not in claude_smart_paste_responses:
            n_no_claude_response += 1
            continue

        original_commit = smart_paste_inputs[request_id].aux["commit"]["after_file"][
            "contents"
        ]
        claude_response = claude_smart_paste_responses[request_id]

        if claude_response.strip() == original_commit.strip():
            response = claude_response
            n_chose_claude_response += 1
        elif request_id in llama_smart_paste_responses:
            llama_response = llama_smart_paste_responses[request_id]
            if llama_response.strip() == original_commit.strip():
                response = llama_response
                n_chose_llama_response += 1
            elif llama_response.strip() == claude_response.strip():
                response = claude_response
                n_chose_claude_response += 1
            else:
                n_skip += 1
                continue
        else:
            response = claude_response
            n_chose_claude_response += 1

        if not has_trailing_newline(prompt_input.target_file_content):
            n_original_no_new_lines += 1
            if has_trailing_newline(response):
                n_claude_changed_new_lines += 1
                before_after_file = BeforeAfterFiles(
                    before_file=File(
                        prompt_input.target_path, prompt_input.target_file_content
                    ),
                    after_file=File(prompt_input.target_path, response),
                )
                if last_span_only_adds_newline(before_after_file.str_diff):
                    n_last_span_only_adds_newline += 1
                    response = response.rstrip(NEWLINES)

        output = {
            "request_id": request_id,
            "input": dataclasses.asdict(prompt_input),
            "response": response,
        }
        json.dump(output, f)
        f.write("\n")

        counter += 1

print(
    f"Saved {counter} samples, skipped {n_empty_code_block} with empty code blocks, {n_no_claude_response} no claude response, {n_skip} samples with no agreement"
)
print(
    f"{n_chose_llama_response} chose llama response, {n_chose_claude_response} chose claude response"
)
print(
    f"{n_original_no_new_lines} original no new lines, {n_claude_changed_new_lines} modified last line"
)
print(f"{n_last_span_only_adds_newline} last span only adds newline")
#%%
