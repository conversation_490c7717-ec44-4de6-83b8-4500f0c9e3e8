# Config for evaluating DeepSeek on the Hydra 2-3 lines task without retrieval.
#
# Run it locally
# $ cd augment/research/eval
# $ python3 eval.py --local --v2 /home/<USER>/augment/experimental/yury/configs/eval/23lines/deepseek_ft_rogue.yml

systems:
  - name: basic_rag
    model:
      name: deeprogue
      checkpoint_path: /mnt/efs/augment/user/yury/logs/deepseek_ft/deepseek-5.7bmqa_20231121_v2/checkpoint_llama_iteration_500
      enable_evaluation_mode: true
      seq_length: 7700
      prompt:
        max_prefix_tokens: 2048
        max_suffix_tokens: 1280
        preamble: <｜begin▁of▁sentence｜>
        max_retrieved_chunk_tokens: -1
        max_prompt_tokens: 7372
        always_use_suffix_token: True
        only_truncate_true_prefix: True
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      chunker:
        max_lines_per_chunk: 30
        name: line_level
      document_formatter:
        add_path: true
        name: simple_document
      query_formatter:
        add_path: true
        max_tokens: 1023
        name: ethanol3_query
        retokenize: true
      scorer:
        checkpoint_path: ethanol/ethanol5-01
        name: ethanol
    experimental:
      retriever_top_k: 25
      trim_on_dedent: False
      trim_on_max_lines: null
      remove_suffix: False

task:
  name: hydra
  dataset: repoeval_2-3lines

import_modules: experimental.igor.systems.ethanol

# Cannot be executed via Determined since requires launch llama.cpp server
