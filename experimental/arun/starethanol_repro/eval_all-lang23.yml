determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: >-
    diffb1m_16b_alphal_fixtoken, starethanol6_16.1_mean_proj_512_2000, 31136,
    all_languages_2-3lines
  project: arun
  workspace: Dev
  tags: starethanol-repro, eval
podspec: 1xA100.yaml
system:
  experimental:
    remove_suffix: false
    retriever_top_k: 100
    trim_on_dedent: false
    trim_on_max_lines: null
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
    name: rogue
    prompt:
      max_number_chunks: 32
      max_prefix_tokens: 1280
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
  name: basic_rag
  retriever:
    chunker:
      max_lines_per_chunk: 40
      name: line_level
    document_formatter:
      add_path: true
      add_prefix: false
      add_suffix: false
      max_tokens: 999
      name: ethanol6_document
      tokenizer_name: StarCoderTokenizer
    query_formatter:
      add_path: true
      add_suffix: true
      max_tokens: 1023
      name: ethanol6_query
      prefix_ratio: 0.9
      tokenizer_name: StarCoderTokenizer
    scorer:
        name: dense_scorer_v2_ffwd
        checkpoint_path: /mnt/efs/augment/checkpoints/star_ethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000
        # dtype: bfloat16

        # name: dense_scorer_v2
        # lr=2e-05 -- with learnable scale
        # checkpoint_path: /mnt/efs/augment/checkpoints/arun/starethanol-fbwd/seth6.16-fbwd-bs2x8x8-lr2e-05-iters2000-K128

        # lr=2e-05 -- without learnable scale
        # checkpoint_path: /mnt/efs/augment/checkpoints/arun/starethanol-fbwd/seth6.16-fbwd-bs1x16x32-lr2e-05-iters2000-K128

        # lr=2e-06 -- without learnable scale
        # checkpoint_path: /mnt/efs/augment/checkpoints/arun/starethanol-repo/seth6.16-fbwd-bs1x32x16-lr2e-06-iters2000-K128-step1700

        # lr=2e-06 -- with learnable scale
        # checkpoint_path: /mnt/efs/augment/checkpoints/arun/starethanol-repo/seth6.16-fbwd-lls-bs1x32x16-lr2e-06-iters2000-K128

        tokenizer_name: starcoder
        query_prompt_formatter_name: ""  # ethanol6.16.1-query-embedding
        doc_prompt_formatter_name: ""  # ethanol6-embedding-with-path-key
    # scorer:
    #   additional_yaml_files:
    #     - >-
    #       /home/<USER>/augment/experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml
    #   checkpoint_path: star_ethanol/starethanol6_16.1_mean_proj_512_2000
    #   name: starcoder_1b
task:
  dataset: all_languages_2-3lines_medium_to_hard.v1.0
  hydra_block_resource_internet_access: true
  name: hydra
