#!/usr/bin/env python3
import argparse
from dataclasses import dataclass
import math
from pathlib import Path
import yaml
from collections import defaultdict
from models.retrieval.chunking import signature_chunker
from models.retrieval.chunking import chunking


languages_path = Path(__file__).parent.parent.parent / "base/languages/languages.yaml"
with languages_path.open("r") as f:
    languages = yaml.safe_load(f)
# print(languages)
extensions = [ext for lang in languages.values() for ext in lang["extensions"]]
extensions = set(extensions)

MAX_FILE_SIZE = 128 * 1024

extra_extensions = set(
    line.strip()
    for line in """
.erb
.toml
.diff
.patch
.dockerfile
.ini
.v
.ps1
.tpl
.gradle
.http
.ino
.zsh
.bash
.m
.json5
.s
.tcl
.mk
.cmake
.groovy
.vue
.mm
.phtml
.nix
.g4
.jinja
.mustache
.hs
.haml
.tmpl
.gitignore
.env
.csv
""".strip().splitlines()
)

# Don't use the extra extensions yet
extensions = set()
# extensions = extensions - extra_extensions
# extensions = extensions.union(extra_extensions)
# assert extensions.intersection(extra_extensions) == set(), f"{extensions.intersection(extra_extensions)}"

stats = defaultdict(list)

# Walk the directory

ignore_list = [".git", "node_modules", "third_party", "external"]


def signature_chunking(path: Path):
    text = path.read_text(encoding="utf-8")
    doc = chunking.Document(
        blob_name="test",
        text=text,
        path=str(path),
    )

    chunker = signature_chunker.SignatureChunker()
    chunks = chunker.split_into_chunks(doc)
    return len(list(chunks))


@dataclass
class Rec:
    file_path: Path
    lines: int
    chunks: int
    sig_chunks: int
    file_size: int


def main(
    root_dir: Path, max_file_size: int, chunk_size: int, extra: bool = False, dump=False
):
    all_files: list[Rec] = []
    for file_path in root_dir.rglob("*"):
        file_path_rel = file_path.relative_to(root_dir)

        # Filter ignored paths and symlinks
        if any(part in ignore_list for part in file_path_rel.parts):
            continue
        if file_path.is_symlink():
            continue
        if not file_path.is_file():
            continue

        assert file_path.is_file()
        file_size = file_path.stat().st_size
        file_ext = file_path.suffix
        # if there is an allow list of extensions, check against those.
        if extensions and file_ext not in extensions:
            continue
        try:
            text = file_path.read_text(encoding="utf-8")
        except UnicodeDecodeError:
            continue

        if file_size > max_file_size:
            continue

        lines = len(text.splitlines())
        chunks = math.ceil(lines / chunk_size)
        sig_chunks = signature_chunking(file_path)
        rec = Rec(file_path, lines, chunks, sig_chunks, file_size)
        all_files.append(rec)

    for rec in all_files:
        bucket = math.ceil(math.log2(rec.file_size)) if rec.file_size else 0
        stats[bucket].append(rec)

    if dump:
        print("File-path\tLines\tChunks\tSigChunks\tSize")
        for rec in all_files:
            print(
                f"{rec.file_path}\t{rec.lines}\t{rec.chunks}\t{rec.sig_chunks}\t{rec.file_size}"
            )

    keys = sorted(stats.keys(), reverse=True)
    from collections import Counter
    import humanfriendly as huf

    all_size = sum(rec.file_size for k in stats for rec in stats[k])
    all_count = sum(1 for k in stats for _ in stats[k])
    all_lines = sum(rec.lines for k in stats for rec in stats[k])
    all_chunks = sum(rec.chunks for k in stats for rec in stats[k])
    all_sigchunks = sum(rec.sig_chunks for k in stats for rec in stats[k])
    count_large_chunks = sum(rec.chunks > 512 for k in stats for rec in stats[k])
    count_large_sigchunks = sum(rec.sig_chunks > 512 for k in stats for rec in stats[k])
    print(
        "Dir\tFiles\tLines\tSize\tChunks\tSigChunks\tLargeChunkCount\tLargeSigChunkCount"
    )
    print(
        f"{root_dir}\t{all_count}\t{all_lines}\t{huf.format_size(all_size, binary=True)}\t{all_chunks}\t{all_sigchunks}\t{count_large_chunks}\t{count_large_sigchunks}"
    )
    print()
    if not extra:
        return

    for k in keys:
        c = Counter([rec.file_path.suffix for rec in stats[k]])
        total_size = sum(rec.file_size for rec in stats[k])
        print(f"Bucket: {huf.format_size(2**k, binary=True)}")
        print(f" Size: {total_size/all_size * 100: 0.1f}%")
        print(" Extensions:", c)
        for rec in stats[k][:10]:
            print(rec.file_path, rec.lines, rec.chunks, rec.size, rec.sig_chunks)
        print()

    # All files without extension
    print("Files without extension")
    file_list = sorted(
        [
            (rec.file_path, rec.file_size)
            for k in stats
            for rec in stats[k]
            if not rec.file_path.suffix
        ],
        key=lambda x: x[1],
        reverse=True,
    )
    local_size = sum(v for x, v in file_list)
    print(f"{len(file_list) / all_count: 0.1f}% of all files")
    print(f"{local_size / all_size * 100: 0.1f}% of all size")
    print(f"{huf.format_size(local_size, binary=True)} Total Size")
    for file_path, file_size in file_list[:50]:
        print(f"{file_path}: {(file_size+1023)//1024} KB")

    # Print the ordered list
    # for file_path, file_size in file_list[:top_k]:
    #     print(f"{file_path}: {(file_size+1023)//1024} KB")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("root_dir", help="Path to the root directory")
    parser.add_argument(
        "--max-file-size", type=int, default=MAX_FILE_SIZE, help="Max file size"
    )
    parser.add_argument("--extra", action="store_true", help="Print extra diagnostics")
    parser.add_argument(
        "--chunk-size", type=int, default=30, help="Chunk size for processing"
    )
    parser.add_argument("--dump", action="store_true", help="Dump stats for each file")
    args = parser.parse_args()

    root_dir = Path(args.root_dir)
    if not root_dir.is_dir():
        print(f"Skipping {root_dir}")
        exit(1)

    if args.extra:
        print("Extra diagnostics enabled")

    main(
        root_dir,
        max_file_size=args.max_file_size,
        chunk_size=args.chunk_size,
        extra=args.extra,
        dump=args.dump,
    )
