"""Repro the CUBLAS_NOT_SUPPORTED bug with cuda-11.6.

This issue was posted in the pytorch discussion forum under:
CUDA error: CUBLAS_STATUS_NOT_SUPPORTED when calling cublasLtMatmul from torch.nn.functional.linear()
https://discuss.pytorch.org/t/cuda-error-cublas-status-not-supported-when-calling-cublasltmatmul-from-torch-nn-functional-linear/170214

The script is the minimal reproduction of the issue.
"""

import torch
import torch.nn.functional as F
from torch._utils import _flatten_dense_tensors, _unflatten_dense_tensors

pad = torch.rand((1), dtype=torch.half, device="cuda")
A = torch.rand((5120, 2560), dtype=torch.half, device="cuda")
pad_A = [pad, A]
new_tensors = _unflatten_dense_tensors(
    _flatten_dense_tensors([p.clone().detach() for p in pad_A]), pad_A
)
pad, A = new_tensors

X = torch.rand((26, 1, 2560), dtype=torch.half, device="cuda")
B = torch.rand((5120), dtype=torch.half, device="cuda")
out = F.linear(X, A, B)
print(out)
