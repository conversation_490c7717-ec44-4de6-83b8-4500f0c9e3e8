# python research/eval/eval.py --v2 experimental/pranay/hindsight_training/hindsight_v1-1-data_on_v3.1-model.yml

augment:
  dai_gcp_service_accounts:
  - mountpoint: /mnt/augment/secrets/cw-ri-importer
    secret: aug-prod-cw-ri-importer # pragma: allowlist secret
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: dogfood-shard, v1-1 data on qweldenv3.1-14b, hindsight
  project: Eval
  workspace: Dev
podspec: gpu-small.yaml
system:
  client:
    retry_count: 5
    retry_sleep: 10.0
    timeout: 60
    url: https://staging-shard-0.api.augmentcode.com
  completion:
    retry_count: 8
  model_name: qweldenv3-1-14b
  name: remote_completion
  retriever:
    disable_extension_filtering: true
    wait_indexing_retry_count: 256
    wait_indexing_retry_sleep_secs: 4
task:
  blob_limit: 15000
  dataset: "v1_1_fake_hindsight"
  dataset_base_dir: /mnt/efs/augment/user/pranay/requests/
  name: hindsight
  order: session_time
  service_account_file: /mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json
  tenant_name: dogfood-shard
