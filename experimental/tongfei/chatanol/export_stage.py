import os
from pathlib import Path

from experimental.tongfei.chatanol.common import DatasetConfig
from research.core.tokenizers import get_tokenizer
from research.data.spark import k8s_session
from research.data.spark.pipelines.pipeline_utils import ObjectDict
from research.data.spark.pipelines.stages.common import export_indexed_dataset


dataset_config = DatasetConfig()


def export_stage(input_path, output_path, tokenizer_name, small_cluster):
    spark = k8s_session(
        name="tongfei-distill-export_indexed_dataset",
        max_workers=32 if not small_cluster else 2,
    )
    export_indexed_dataset(
        config=ObjectDict(
            {
                "name": "export-dataset",
                "input": input_path,
                "output": Path(output_path),
                "samples_column": "prompt_tokens",
            }
        ),
        spark=spark,
        tokenizer=get_tokenizer(tokenizer_name),
    )
    spark.stop()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument("--input", type=str, help="stage6 uri")
    parser.add_argument("--output", type=str, help="output path")
    parser.add_argument("--tokenizer", type=str, help="tokenizer")
    args = parser.parse_args()
    args.input = os.path.abspath(os.path.realpath(args.input))
    args.output = os.path.abspath(os.path.realpath(args.output))
    export_stage(args.input, args.output, args.tokenizer, False)
