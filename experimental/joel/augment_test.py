"""Unit tests for augment.py."""

from unittest.mock import MagicMock

import augment


def test_handle_file(tmp_path):
    file_path = tmp_path / "test_file.txt"
    file_path.write_text("This is some test content.")
    expected = "This is some example content."
    augment.query_model = MagicMock(return_value=expected)
    augment.upload_blobs = MagicMock(return_values=[])

    augment.handle_file(
        query_type="edit",
        file=str(file_path),
        instruction="Replace 'test' with 'example'.",
        model_name="",
        blob_info=augment.BlobInfo(None, None, []),
        output_type=augment.OutputType("overwrite", path=None),
        print_type="full",
        fake_model=False,
    )

    assert file_path.read_text() == expected


def test_handle_file_line(tmp_path):
    file_path = tmp_path / "test_file.txt"
    file_path.write_text(
        "This is some test content.\nThis is another line of test content."
    )
    expected = "This is some example content.\nThis is another line of example content."
    augment.query_model = MagicMock(return_value=expected)
    augment.upload_blobs = MagicMock(return_values=[])

    augment.handle_file_line(
        query_type="edit",
        file=str(file_path),
        line=0,
        instruction="Replace 'test' with 'example'.",
        model_name="",
        blob_info=augment.BlobInfo(None, None, []),
        output_type=augment.OutputType("overwrite", path=None),
        print_type="full",
        fake_model=False,
    )

    assert file_path.read_text() == expected


def test_handle_file_line_with_gap(tmp_path):
    file_path = tmp_path / "test_file.txt"
    file_path.write_text(
        "This is some test content.\n\nThis is another line of test content."
    )
    augment.query_model = MagicMock(return_value="This is some example content.")
    augment.upload_blobs = MagicMock(return_values=[])

    augment.handle_file_line(
        query_type="edit",
        file=str(file_path),
        line=0,
        instruction="Replace 'test' with 'example'.",
        model_name="",
        blob_info=augment.BlobInfo(None, None, []),
        output_type=augment.OutputType("overwrite", path=None),
        print_type="full",
        fake_model=False,
    )

    assert (
        file_path.read_text()
        == "This is some example content.\nThis is another line of test content."
    )


def test_split_and_query():
    augment.MAX_EDIT_QUERY_CHARS = 15
    augment.NUM_PARALLEL_REQUESTS = 1
    augment.query_model = MagicMock(return_value="")
    augment.upload_blobs = MagicMock(return_values=[])
    text = "012345678\n012345678\n012345678\n"

    augment.split_and_query(
        query_type="edit",
        instruction="",
        text=text,
        file="file.py",
        model_name="",
        blob_info=augment.BlobInfo(None, None, []),
        fake_model=False,
    )

    assert len(augment.query_model.call_args_list) == 3
    print(augment.query_model.call_args_list[0].kwargs["selected_text"])
    assert (
        augment.query_model.call_args_list[0].kwargs["selected_text"] == "012345678\n"
    )
    assert augment.query_model.call_args_list[0].kwargs["prefix"] == ""
    assert (
        augment.query_model.call_args_list[0].kwargs["suffix"]
        == "012345678\n012345678\n"
    )
    assert (
        augment.query_model.call_args_list[1].kwargs["selected_text"] == "012345678\n"
    )
    assert augment.query_model.call_args_list[1].kwargs["prefix"] == "012345678\n"
    assert augment.query_model.call_args_list[1].kwargs["suffix"] == "012345678\n"
    assert (
        augment.query_model.call_args_list[2].kwargs["selected_text"] == "012345678\n"
    )
    assert (
        augment.query_model.call_args_list[2].kwargs["prefix"]
        == "012345678\n012345678\n"
    )
    assert augment.query_model.call_args_list[2].kwargs["suffix"] == ""


def test_split_and_query_smart_chunking():
    augment.MAX_EDIT_QUERY_CHARS = 42
    augment.NUM_PARALLEL_REQUESTS = 1
    augment.query_model = MagicMock(return_value="")
    augment.upload_blobs = MagicMock(return_values=[])
    text = """\
class A:
    def m():
        pass


class B:
    def m():
        pass


class C:
    def m1():
        pass

    def m2():
        pass

def m():
    pass
"""

    augment.split_and_query(
        query_type="edit",
        instruction="",
        text=text,
        file="file.py",
        model_name="",
        blob_info=augment.BlobInfo(None, None, []),
        fake_model=False,
    )

    assert augment.query_model.call_count == 5
    assert (
        augment.query_model.call_args_list[0].kwargs["selected_text"]
        == """\
class A:
    def m():
        pass
"""
    )
    assert augment.query_model.call_args_list[0].kwargs["prefix"] == ""
    assert augment.query_model.call_args_list[0].kwargs["suffix"].startswith("\n\n")
    assert (
        augment.query_model.call_args_list[1].kwargs["selected_text"]
        == """\
class B:
    def m():
        pass
"""
    )
    assert augment.query_model.call_args_list[1].kwargs["prefix"].endswith("\n\n")
    assert augment.query_model.call_args_list[1].kwargs["suffix"].startswith("\n\n")
    assert (
        augment.query_model.call_args_list[2].kwargs["selected_text"]
        == """\
class C:
    def m1():
        pass
"""
    )
    assert augment.query_model.call_args_list[2].kwargs["prefix"].endswith("\n\n")
    assert augment.query_model.call_args_list[2].kwargs["suffix"].startswith("\n")
    assert (
        augment.query_model.call_args_list[3].kwargs["selected_text"]
        == """\
    def m2():
        pass
"""
    )
    assert augment.query_model.call_args_list[2].kwargs["prefix"].endswith("\n")
    assert augment.query_model.call_args_list[3].kwargs["suffix"].startswith("\n")
    assert (
        augment.query_model.call_args_list[4].kwargs["selected_text"]
        == """\
def m():
    pass
"""
    )
    assert augment.query_model.call_args_list[1].kwargs["prefix"].endswith("\n")
    assert augment.query_model.call_args_list[4].kwargs["suffix"] == ""
