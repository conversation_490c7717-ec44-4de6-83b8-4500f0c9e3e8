#%%
import gzip
import jsonlines
from megatron.inference.inference_model import InferenceModel
from megatron.inference.process_wrap import ProcessWrappedObject

yaml_files=[
    "/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml",
    "/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml",
]
overwrite_values={
    "load": "/mnt/efs/augment/checkpoints/5df89921-5f51-4af1-abf9-dccb0996aa09",
}

def json_to_pairs(jsonl_gz_path, query_key, doc_key):
    pairs = []
    with gzip.open(jsonl_gz_path, 'rb') as fp:
        for obj in jsonlines.Reader(fp):
            pairs.append((obj[query_key], obj[doc_key]))
    return pairs

try:
    model = ProcessWrappedObject(
        InferenceModel,
        yaml_files=yaml_files,
        overwrite_values=overwrite_values,
        use_cache=False
    )

    results = {}

    for lang in ["go", "java", "javascript", "php", "python", "ruby"]:
        codesearch_root = f"/mnt/efs/augment/data/eval/retrieval/CodeSearchNet"
        jsonl_gz_path = f"{codesearch_root}/{lang}/final/jsonl/test/{lang}_test_0.jsonl.gz"
        pairs = json_to_pairs(jsonl_gz_path, "docstring", "code")
        
        # Evaluate one batch -- comment out next line to evaluate all
        pairs = pairs[:1000]

        results[lang] = model.contrastive_eval(pairs, 1000, 10)

        # Evaluate one language -- comment out next line to evaluate all
        break
finally:
    del model

results