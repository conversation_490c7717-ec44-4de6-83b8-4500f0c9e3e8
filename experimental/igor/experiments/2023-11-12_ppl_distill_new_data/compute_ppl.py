"""Perplexity distillation."""

from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet


def stage5a(input_path, output_path, model_config, score_batch_size):
    from experimental.vzhao.data import pandas_functions

    spark_gpu = k8s_session(
        max_workers=96,
        conf={
            "spark.executor.pyspark.memory": "1000G",
            "spark.executor.memory": "32G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        gpu_type="A100_NVLINK_80GB",
    )

    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                # NOTE: This assumes that the current `retrieved_chunks` are sorted in
                # the order of retriever scores.
                pandas_functions.add_retriever_rank,
                # This will prepend `EMPTY_CHUNK` to `retrieved_chunks`.
                pandas_functions.prepend_empty_chunk,
                # NOTE: Set `reduction='none'` to return per-token losses/scores.
                pandas_functions.ComputePPL(
                    model_config,
                    score_batch_size=score_batch_size,
                    output_col_name="token_ppl",
                    reduction="none",
                ),
            ]
        ),
        input_path=input_path,
        output_path=output_path,
        batch_size=8,
        timeout=7200,
    )

    spark_gpu.stop()

    for e in result["task_info"]["stderr"]:
        print(e)
        break
    return result


ETHANOL_VERSION_IN = "ethanol6/ethanol6-02"
ETHANOL_VERSION = "ethanol6/ethanol6-14"
TEMP_BUCKET_URI = "s3a://augment-temporary/igor/"
BUCKET_URI = "s3a://igor-dev-bucket/"

MODEL_CONFIG = {
    "checkpoint_path": "rogue/diffb1m_7b_alphal_fixtoken",
    "name": "rogue",
    "prompt": {
        "max_prefix_tokens": 250,
        "max_suffix_tokens": 250,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 1050,
    },
}

stage5a(
    model_config=MODEL_CONFIG,
    input_path=f"{BUCKET_URI}{ETHANOL_VERSION_IN}/03_processed/",
    output_path=f"{BUCKET_URI}{ETHANOL_VERSION}/05_with_ppl_scores/",
    score_batch_size=8,
)
