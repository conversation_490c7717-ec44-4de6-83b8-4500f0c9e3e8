#!/usr/bin/env python3.11
"""
Command-line tool for interacting with remote agents.

This script provides a command-line interface for creating, listing, chatting with,
and deleting remote agents. It supports both streaming and non-streaming chat.
"""

import argparse
import enum
import json
import logging
import os
import sys
import time
import traceback
from google.protobuf.json_format import Parse<PERSON>ict
from google.protobuf import timestamp_pb2
from requests.exceptions import ConnectionError, Timeout as TimeoutError
from typing import List, Optional

from services.api_proxy import public_api_pb2

# Add the repository root to the Python path
sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../.."))
)

from base.augment_client.client import (
    AugmentClient,
    CreateRemoteAgentResponse,
    AgentWorkspaceUpdateType,
    AgentListUpdateType,
    RemoteAgentWorkspaceSetup,
)
from base.prompt_format.common import (
    ChatResultNode,
    ChatResultNodeType,
    ChatRequestNodeType,
)


class AgentStatus(enum.IntEnum):
    """Enum for agent status values."""

    UNSPECIFIED = 0
    STARTING = 1
    RUNNING = 2
    IDLE = 3
    FAILED = 4


class WorkspaceSetupStepStatus(enum.IntEnum):
    """Enum for workspace setup step status values."""

    UNKNOWN = 0
    RUNNING = 1
    SUCCESS = 2
    FAILURE = 3


class FileChangeType(enum.IntEnum):
    """Enum for file change type values."""

    ADDED = 0
    DELETED = 1
    MODIFIED = 2
    RENAMED = 3


class UpdateType(enum.IntEnum):
    """Enum for update type values in streaming responses."""

    UNSPECIFIED = 0
    AGENT_HISTORY_EXCHANGE = 1
    AGENT_HISTORY_EXCHANGE_UPDATE = 2
    AGENT_HISTORY_AGENT_STATUS = 3


# Helper function to convert node type to string
def enum_value_to_string(enum_class, value: int) -> str:
    """Convert a numeric node type to a human-readable string."""
    try:
        return enum_class(value).name
    except ValueError:
        return f"UNKNOWN({value})"


def format_timestamp(timestamp) -> str:
    """Format a timestamp into a human-readable string.

    Args:
        timestamp: The timestamp to format. Can be a string, datetime, or other object with __str__.

    Returns:
        A human-readable formatted timestamp string.
    """
    if not timestamp or str(timestamp) == "Unknown":
        return "Unknown"

    try:
        from datetime import datetime

        # Convert to string if it's not already
        timestamp_str = str(timestamp)

        # Parse the timestamp
        dt = datetime.fromisoformat(timestamp_str.replace("Z", "+00:00"))

        # Format the timestamp
        return dt.strftime("%b %d, %Y at %I:%M %p")
    except Exception:
        # Return the original timestamp if parsing fails
        return str(timestamp)


# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def get_username_from_config() -> str:
    """Get the username from the user config file.

    Returns:
        The username from the config file.

    Raises:
        ValueError: If the username cannot be found in the config file.
    """
    user_config_path = os.path.expanduser("~/.augment/user.json")

    if not os.path.exists(user_config_path):
        raise ValueError(
            "User config file not found. Create ~/.augment/user.json with your username."
        )

    try:
        with open(user_config_path, "r") as f:
            user_config = json.load(f)

        if "name" not in user_config:
            raise ValueError("Username not found in config file.")

        return user_config["name"]
    except json.JSONDecodeError:
        raise ValueError("Invalid JSON in user config file.")
    except Exception as e:
        raise ValueError(f"Error reading username from config: {e}")


def get_api_url(api_url: Optional[str] = None) -> str:
    """Get the API URL from the provided URL or construct it from the username.

    Args:
        api_url: The API URL provided by the user. If None, construct from username.

    Returns:
        The API URL to use.

    Raises:
        ValueError: If the API URL cannot be constructed.
    """
    if api_url:
        return api_url

    try:
        username = get_username_from_config()
        namespace = f"dev-{username}"
        return f"https://{namespace}.us-central.api.augmentcode.com"
    except ValueError as e:
        logger.warning(f"Could not construct API URL from username: {e}")
        logger.warning("Using default API URL: https://api.augment.dev")
        return "https://api.augment.dev"


def get_api_token(token_file: Optional[str] = None) -> str:
    """Get the API token from the token file, environment, or default locations.

    Args:
        token_file: The path to the token file provided by the user.

    Returns:
        The API token to use.

    Raises:
        ValueError: If the API token cannot be found.
    """
    # Check if token file is provided
    if token_file and os.path.exists(token_file):
        with open(token_file, "r") as f:
            return f.read().strip()

    # Check environment variable
    token = os.environ.get("AUGMENT_API_TOKEN")
    if token:
        return token

    # Check dev-deploy token file (new default)
    dev_deploy_token_path = os.path.expanduser("~/.augment/dev-deploy-oauth2-token.txt")
    if os.path.exists(dev_deploy_token_path):
        with open(dev_deploy_token_path, "r") as f:
            return f.read().strip()

    # Check default token file
    token_path = os.path.expanduser("~/.augment/token")
    if os.path.exists(token_path):
        with open(token_path, "r") as f:
            return f.read().strip()

    raise ValueError(
        "API token not found. Set AUGMENT_API_TOKEN environment variable, provide --token-file argument, "
        "or create ~/.augment/dev-deploy-oauth2-token.txt file using dev_deploy_signin.js."
    )


def print_nodes(nodes: List[ChatResultNode]) -> None:
    """Print the nodes in a human-readable format."""
    for node in nodes:
        if node.type == ChatResultNodeType.RAW_RESPONSE:
            print(f"TEXT: {node.content}")
        elif node.type == ChatResultNodeType.TOOL_USE:
            if node.tool_use:
                print(f"TOOL CALL: {node.tool_use.name}")
                print(f"  Input: {node.tool_use.input}")
            else:
                print(f"TOOL CALL: {node.content}")
        else:
            print(f"NODE TYPE {node.type}: {node.content}")


def list_remote_agents(client: AugmentClient) -> None:
    """List all remote agents."""
    logger.info("Listing remote agents...")
    try:
        response = client.list_remote_agents()

        logger.debug(f"Response type: {type(response)}")
        logger.debug(f"Response content: {response}")

        if not response.remote_agents:
            logger.info("No remote agents found.")
            return

        logger.info(f"Found {len(response.remote_agents)} remote agents:")
        for agent in response.remote_agents:
            status_code = agent.status
            status_name = enum_value_to_string(AgentStatus, status_code)

            logger.info(f"  ID: {agent.remote_agent_id}")
            logger.info(f"  Status: {status_name} ({status_code})")
            logger.info(f"  Started: {format_timestamp(agent.started_at)}")
            logger.info(f"  Summary: {agent.session_summary}")
            logger.info("  ---")
    except Exception as e:
        logger.error(f"Error listing remote agents: {e}")
        logger.error(traceback.format_exc())


def get_workspace_setup_status(client: AugmentClient, agent_id: str) -> dict:
    """Get the workspace setup status for a remote agent.

    Args:
        client: The AugmentClient instance.
        agent_id: The ID of the remote agent.

    Returns:
        A dictionary containing the workspace setup status with at least a 'steps' key.
        If an error occurs, returns a dictionary with an empty 'steps' list.
    """
    try:
        # Since we can't modify the client directly, we'll use a workaround by sending the request manually
        response, _ = client._post(
            "remote-agents/logs",
            json={
                "remote_agent_id": agent_id,
            },
        )

        if not response.ok:
            status_code = response.status_code
            error_text = response.text
            logger.error(
                f"HTTP error getting workspace setup status: {status_code} {error_text}"
            )

            if status_code == 404:
                logger.error(f"Agent {agent_id} not found")
            elif status_code == 401:
                logger.error("Authentication error. Check your API token")
            elif status_code == 403:
                logger.error(
                    "Authorization error. You may not have permission to access this agent"
                )
            elif status_code >= 500:
                logger.error("Server error. The server may be experiencing issues")

            return {"steps": []}

        try:
            # Parse the response JSON into a dictionary
            j = response.json()

            # Return the workspace setup status
            if "workspace_setup_status" in j and j["workspace_setup_status"]:
                return j["workspace_setup_status"]
            else:
                logger.debug("No workspace_setup_status found in response")
                return {"steps": []}
        except ValueError as json_error:
            logger.error(f"Error parsing JSON response: {json_error}")
            logger.debug(f"Response content: {response.text[:200]}...")
            return {"steps": []}

    except ConnectionError as conn_error:
        logger.error(f"Connection error getting workspace setup status: {conn_error}")
        return {"steps": []}
    except TimeoutError as timeout_error:
        logger.error(f"Timeout error getting workspace setup status: {timeout_error}")
        return {"steps": []}
    except Exception as e:
        logger.error(f"Unexpected error getting workspace setup status: {e}")
        logger.error(traceback.format_exc())
        return {"steps": []}


def wait_for_agent_ready(
    client: AugmentClient,
    agent_id: str,
    timeout_seconds: int = 300,
    poll_interval: int = 5,
) -> bool:
    """Wait for an agent to be ready (IDLE or RUNNING).

    Args:
        client: The AugmentClient instance.
        agent_id: The ID of the remote agent.
        timeout_seconds: Maximum time to wait in seconds.
        poll_interval: Time between status checks in seconds.

    Returns:
        True if the agent is ready, False if it timed out.
    """
    import sys

    logger.info(f"Waiting for agent {agent_id} to be ready...")
    start_time = time.time()
    elapsed_time = 0

    # Define ready states
    ready_states = [AgentStatus.RUNNING, AgentStatus.IDLE]

    # Last step we've seen
    last_step_number = -1

    while elapsed_time < timeout_seconds:
        try:
            # Get the current status
            response = client.list_remote_agents()

            # Find our agent
            agent_found = False
            for agent in response.remote_agents:
                if agent.remote_agent_id == agent_id:
                    agent_found = True
                    status_code = agent.status
                    status_name = enum_value_to_string(AgentStatus, status_code)

                    # Get elapsed time
                    elapsed = int(elapsed_time)

                    # Default status message
                    status_message = f"Status: {status_name}"

                    # If the agent is starting, get more detailed progress information
                    if status_code == AgentStatus.STARTING:
                        setup_status = get_workspace_setup_status(client, agent_id)

                        # If we have setup steps, show the current step
                        if "steps" in setup_status and setup_status["steps"]:
                            steps = setup_status["steps"]

                            # Find the latest running step
                            current_step = None
                            for step in steps:
                                step_number = step.get("step_number", -1)
                                if step_number > last_step_number:
                                    last_step_number = step_number

                                if (
                                    step.get("status")
                                    == WorkspaceSetupStepStatus.RUNNING
                                ):
                                    current_step = step
                                    break

                            # If we found a running step, include it in the status message
                            if current_step:
                                step_desc = current_step.get(
                                    "step_description", "Unknown step"
                                )
                                status_message = f"Status: {status_name} - {step_desc}"

                    # Display the progress
                    sys.stdout.write(
                        f"\rWaiting for agent to be ready... "
                        f"{status_message} ({elapsed}s)"
                    )
                    sys.stdout.flush()

                    # Check if agent is ready
                    if status_code in ready_states:
                        sys.stdout.write(
                            f"\rAgent is ready! Final status: {status_name}"
                            + " " * 50
                            + "\n"
                        )
                        return True

                    # Check for failure
                    if status_code == AgentStatus.FAILED:
                        sys.stdout.write(
                            f"\rAgent failed to start. Status: {status_name}"
                            + " " * 50
                            + "\n"
                        )
                        return False

                    break

            if not agent_found:
                # Agent not found
                sys.stdout.write(
                    f"\rWarning: Agent {agent_id} not found in list" + " " * 50 + "\n"
                )

        except Exception as e:
            logger.error(f"Error checking agent status: {e}")

        # Sleep and update elapsed time
        time.sleep(poll_interval)
        elapsed_time = time.time() - start_time

    # Timeout
    sys.stdout.write(
        f"\rTimeout waiting for agent to be ready after {int(elapsed_time)}s"
        + " " * 50
        + "\n"
    )
    return False


def create_remote_agent(
    client: AugmentClient,
    system_prompt: str,
    initial_message: str,
    repository_url: str,
    git_ref: str,
    model: str = "claude-3-5-sonnet",
    wait: bool = False,
    wait_timeout: int = 300,
) -> CreateRemoteAgentResponse:
    """Create a remote agent.

    Args:
        client: The AugmentClient instance.
        system_prompt: The system prompt for the agent.
        initial_message: The initial message for the agent.
        repository_url: The repository URL for the agent.
        git_ref: The git reference for the agent.
        model: The model to use for the agent.
        wait: Whether to wait for the agent to be ready.
        wait_timeout: Maximum time to wait in seconds (default: 5 minutes).

    Returns:
        The CreateRemoteAgentResponse.
    """
    logger.info("Creating remote agent...")

    try:
        response = client.create_remote_agent(
            workspace_setup=RemoteAgentWorkspaceSetup(
                starting_files={
                    "github_commit_ref": {
                        "repository_url": repository_url,
                        "git_ref": git_ref,
                    }
                }
            ),
            initial_request_nodes=[
                {
                    "id": 1,
                    "type": ChatRequestNodeType.TEXT,
                    "text_node": {"content": initial_message},
                }
            ],
            model=model,
        )

        logger.info("Remote agent created successfully:")
        logger.info(f"  ID: {response.remote_agent_id}")
        logger.info(
            f"  Status: {enum_value_to_string(AgentStatus, int(response.status))}"
        )

        # Wait for the agent to be ready if requested
        if wait:
            wait_for_agent_ready(client, response.remote_agent_id, wait_timeout)

        return response
    except Exception as e:
        logger.error(f"Error creating remote agent: {e}")
        logger.error(traceback.format_exc())
        raise


def get_chat_history(
    client: AugmentClient, agent_id: str, last_processed_sequence_id: int = 0
) -> None:
    """Get the chat history for a remote agent.

    Args:
        client: The AugmentClient instance.
        agent_id: The ID of the remote agent.
        last_processed_sequence_id: The sequence ID of the last processed exchange.
            Use 0 to get all chat history.
    """
    logger.info(f"Getting chat history for agent {agent_id}...")
    try:
        # First, we need to modify the client method to include the last_processed_sequence_id parameter
        # Since we can't modify the client directly, we'll use a workaround by sending the request manually

        response = client.get_remote_agent_history(agent_id, last_processed_sequence_id)

        # Print the raw response for debugging
        logger.debug(f"Response type: {type(response)}")
        logger.debug(f"Response content: {response}")

        # Check if the response has a remote_agent field
        if response.remote_agent:
            # Print agent information
            agent = response.remote_agent
            logger.info(f"Remote Agent ID: {agent.remote_agent_id}")
            logger.info(f"Status: {agent.status}")
            logger.info(f"Started at: {format_timestamp(agent.started_at)}")
            if agent.session_summary:
                logger.info(f"Session summary: {agent.session_summary}")
        else:
            logger.info("No remote agent information found.")

        # Check if the response has chat history
        if response.chat_history:
            logger.info("Chat history:")
            for chat_history in response.chat_history:
                if chat_history.exchange:
                    exchange = chat_history.exchange
                    print_agent_history_exchange(exchange)
                    logger.info("  ---")
        else:
            logger.info("No chat history found.")
    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        logger.error(traceback.format_exc())


def print_agent_history_exchange(
    exchange: public_api_pb2.Exchange,
    sequence_id: int | None = None,
    finished_at: timestamp_pb2.Timestamp | None = None,
    changed_files: list | None = None,
):
    """Process an agent history exchange update and print it to the console.

    Args:
        exchange: The inner exchange dictionary containing request/response information.
        sequence_id: The sequence ID of the exchange.
        finished_at: The timestamp when the exchange was finished.
        changed_files: List of changed files in the exchange, if any.

    Returns:
        The sequence ID of the processed exchange.
    """
    from colorama import Fore, Style
    import json

    details = []
    if sequence_id:
        details.append(f"Sequence id: {sequence_id}")
    if finished_at:
        details.append(f"Finished at: {format_timestamp(finished_at)}")
    details_str = "(" + ", ".join(details) + ")" if details else ""
    print(f"\n{Fore.BLUE}[Update] Exchange{details_str}:{Style.RESET_ALL}")

    print(f"{Fore.YELLOW}Request:{Style.RESET_ALL}")

    # Process and display tool results if present
    if exchange.request_nodes:
        for node in exchange.request_nodes:
            # Tool Result node (ChatRequestNodeType.TOOL_RESULT = 1)
            if node.type == ChatRequestNodeType.TOOL_RESULT and node.tool_result_node:
                tool_res_node = node.tool_result_node
                result_type = (
                    f"{Fore.RED}Error"
                    if tool_res_node.is_error
                    else f"{Fore.GREEN}Result"
                )
                print(
                    f"  {result_type} (ID: {tool_res_node.tool_use_id}):{Style.RESET_ALL}"
                )
                print(f"  {tool_res_node.content}")

    if exchange.request_message:
        print(exchange.request_message)

    print(f"{Fore.GREEN}Response:{Style.RESET_ALL}")

    # Process and display tool calls if present
    if exchange.response_nodes:
        for node in exchange.response_nodes:
            # Tool Use node (ChatResultNodeType.TOOL_USE = 5)
            if node.type == ChatResultNodeType.TOOL_USE and node.tool_use:
                tool_name = node.tool_use.tool_name
                input_json = node.tool_use.input_json
                tool_id = node.tool_use.tool_use_id

                try:
                    tool_input = json.loads(input_json)
                except json.JSONDecodeError:
                    tool_input = input_json

                print(
                    f"  {Fore.BLUE}Tool Call:{Style.RESET_ALL} {tool_name} (ID: {tool_id})"
                )
                print(f"  {Fore.BLUE}Input:{Style.RESET_ALL}")
                print(f"{json.dumps(tool_input, indent=2)}")

    if exchange.response_text:
        print(exchange.response_text, end="", flush=True)

    # If there are changed files, print them
    if changed_files:
        print(f"{Fore.MAGENTA}Changed files:{Style.RESET_ALL}")
        for file in changed_files:
            old_path = file.old_path
            new_path = file.new_path
            change_type = enum_value_to_string(FileChangeType, file.change_type)

            if old_path and new_path:
                print(f"  {change_type}: {old_path} -> {new_path}")
            elif new_path:
                print(f"  {change_type}: {new_path}")
            elif old_path:
                print(f"  {change_type}: {old_path}")


def get_agent_history_stream(
    client: AugmentClient,
    agent_id: str,
    last_processed_sequence_id: int = 0,
    debug: bool = False,
    timeout_seconds: int | None = None,
) -> None:
    """Get the agent history for a remote agent with streaming.

    Args:
        client: The AugmentClient instance.
        agent_id: The ID of the remote agent.
        last_processed_sequence_id: The sequence ID of the last processed exchange.
            Use 0 to get all agent history.
        debug: Whether to print debug information.
        timeout_seconds: Maximum time to wait for the stream in seconds.
    """
    import json
    from colorama import Fore, Style, init

    # Initialize colorama
    init()

    logger.info(f"Getting streaming agent history for agent {agent_id}...")
    try:
        # Use the client's streaming method
        response = client.get_remote_agent_history_stream(
            agent_id,
            last_processed_sequence_id=last_processed_sequence_id,
            timeout=timeout_seconds,
        )

        logger.info("Streaming response started...")
        print(f"{Fore.CYAN}Streaming updates for agent {agent_id}...{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Press Ctrl+C to stop streaming{Style.RESET_ALL}")
        print("-" * 80)

        # Process the streaming response
        buffer = ""
        current_sequence_id = None

        for chunk in response.iter_content(chunk_size=1024):
            if chunk:
                # Decode the chunk with error handling
                try:
                    # Try UTF-8 decoding first
                    chunk_str = chunk.decode("utf-8")
                except UnicodeDecodeError:
                    # Fall back to a more lenient decoding if UTF-8 fails
                    logger.warning(
                        "UTF-8 decoding failed, trying with 'utf-8', 'replace' error handling"
                    )
                    chunk_str = chunk.decode("utf-8", errors="replace")

                buffer += chunk_str

                # Process complete JSON objects
                while "\n" in buffer:
                    line, buffer = buffer.split("\n", 1)
                    if debug:
                        print(
                            f"{Fore.CYAN}[DEBUG] Received line: {line}{Style.RESET_ALL}"
                        )
                    if line.strip():
                        try:
                            data = json.loads(line)

                            # Parse the JSON data into an AgentHistoryStreamResponse protobuf
                            data = ParseDict(
                                data,
                                public_api_pb2.GetRemoteAgentHistoryStreamResponse(),
                                ignore_unknown_fields=True,
                            )

                            # Handle updates in the response (new format)
                            if data.updates:
                                for update in data.updates:
                                    update_type = update.type

                                    if update_type == UpdateType.AGENT_HISTORY_EXCHANGE:
                                        exchange = update.exchange
                                        print_agent_history_exchange(
                                            exchange.exchange,
                                            exchange.sequence_id,
                                            exchange.finished_at,
                                            list(exchange.changed_files),
                                        )
                                        current_sequence_id = exchange.sequence_id

                                    elif (
                                        update_type
                                        == UpdateType.AGENT_HISTORY_EXCHANGE_UPDATE
                                    ):
                                        exchange_update = update.exchange_update
                                        request_id = exchange_update.request_id
                                        seq_id = exchange_update.sequence_id
                                        appended_text = exchange_update.appended_text
                                        appended_nodes = (
                                            exchange_update.appended_nodes
                                            if hasattr(
                                                exchange_update, "appended_nodes"
                                            )
                                            else []
                                        )
                                        appended_changed_files = (
                                            exchange_update.appended_changed_files
                                            if hasattr(
                                                exchange_update,
                                                "appended_changed_files",
                                            )
                                            else []
                                        )

                                        # If this is a new sequence, print an error
                                        if current_sequence_id != seq_id:
                                            print(
                                                f"\n{Fore.RED}[ERROR] Expected sequence ID {current_sequence_id} but got {seq_id}{Style.RESET_ALL}"
                                            )

                                        # For text updates, we always print the update directly
                                        if debug:
                                            print(
                                                f"{Fore.CYAN}[DEBUG] Received exchange update: {appended_text} (Request ID: {request_id}){Style.RESET_ALL}",
                                                flush=True,
                                            )
                                            if appended_nodes:
                                                print(
                                                    f"{Fore.CYAN}[DEBUG] Received {len(appended_nodes)} appended nodes{Style.RESET_ALL}",
                                                    flush=True,
                                                )
                                            if appended_changed_files:
                                                print(
                                                    f"{Fore.CYAN}[DEBUG] Received {len(appended_changed_files)} appended changed files{Style.RESET_ALL}",
                                                    flush=True,
                                                )

                                        # Print the actual text update
                                        print(appended_text, end="", flush=True)

                                        # Process any appended nodes
                                        if appended_nodes:
                                            for node in appended_nodes:
                                                if (
                                                    node.type
                                                    == ChatResultNodeType.TOOL_USE
                                                    and node.tool_use
                                                ):
                                                    tool_name = node.tool_use.tool_name
                                                    input_json = (
                                                        node.tool_use.input_json
                                                    )
                                                    tool_id = node.tool_use.tool_use_id

                                                    try:
                                                        tool_input = json.loads(
                                                            input_json
                                                        )
                                                    except json.JSONDecodeError:
                                                        tool_input = input_json

                                                    print(
                                                        f"\n  {Fore.BLUE}Tool Call:{Style.RESET_ALL} {tool_name} (ID: {tool_id})"
                                                    )
                                                    print(
                                                        f"  {Fore.BLUE}Input:{Style.RESET_ALL}"
                                                    )
                                                    print(
                                                        f"{json.dumps(tool_input, indent=2)}"
                                                    )

                                        # Process any appended changed files
                                        if appended_changed_files:
                                            print(
                                                f"\n{Fore.MAGENTA}Changed files:{Style.RESET_ALL}"
                                            )
                                            for file in appended_changed_files:
                                                old_path = file.old_path
                                                new_path = file.new_path
                                                change_type = enum_value_to_string(
                                                    FileChangeType, file.change_type
                                                )

                                                if old_path and new_path:
                                                    if old_path == new_path:
                                                        print(
                                                            f"  {Fore.YELLOW}Modified:{Style.RESET_ALL} {new_path}"
                                                        )
                                                    else:
                                                        print(
                                                            f"  {Fore.BLUE}Renamed:{Style.RESET_ALL} {old_path} → {new_path}"
                                                        )
                                                elif new_path:
                                                    print(
                                                        f"  {Fore.GREEN}Added:{Style.RESET_ALL} {new_path}"
                                                    )
                                                elif old_path:
                                                    print(
                                                        f"  {Fore.RED}Deleted:{Style.RESET_ALL} {old_path}"
                                                    )
                                                else:
                                                    print(
                                                        f"  {Fore.CYAN}{change_type}:{Style.RESET_ALL} Unknown file"
                                                    )

                                    elif (
                                        update_type
                                        == UpdateType.AGENT_HISTORY_AGENT_STATUS
                                    ):
                                        status = update.agent.status
                                        status_str = enum_value_to_string(
                                            AgentStatus, status
                                        )

                                        print(
                                            f"\n{Fore.CYAN}[Update] Agent status: {status_str} ({status}){Style.RESET_ALL}"
                                        )

                        except json.JSONDecodeError as e:
                            logger.error(f"Error decoding JSON: {e}")
                            logger.error(f"Problematic line: {line}")

        # Process any remaining data in the buffer
        if buffer.strip():
            try:
                data = json.loads(buffer)

                # Parse the JSON data into an AgentHistoryStreamResponse protobuf
                data = ParseDict(
                    data,
                    public_api_pb2.GetRemoteAgentHistoryStreamResponse(),
                    ignore_unknown_fields=True,
                )

                # Process the data if it contains updates
                for update in data.updates:
                    update_type = update.type
                    if update_type == UpdateType.AGENT_HISTORY_EXCHANGE_UPDATE:
                        print(f"\n{Fore.GREEN}Final exchange update:{Style.RESET_ALL}")
                        print(update.exchange_update.appended_text)
            except json.JSONDecodeError:
                logger.error(f"Error decoding final JSON: {buffer}")

        print("-" * 80)
        logger.info("Streaming response completed")
        print(f"{Fore.CYAN}Streaming completed{Style.RESET_ALL}")
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Streaming interrupted by user{Style.RESET_ALL}")
    except Exception as e:
        logger.error(f"Error getting streaming agent history: {e}")
        logger.error(traceback.format_exc())


def chat_with_agent(
    client: AugmentClient,
    agent_id: str,
    message: str,
) -> None:
    """Send a chat message to a remote agent.

    Args:
        client: The AugmentClient instance.
        agent_id: The ID of the remote agent.
        message: The message to send.
    """
    logger.info(f"Sending message to agent {agent_id}: {message}")
    try:
        response = client.remote_agent_chat(agent_id, message)

        # Print the entire response for debugging
        logger.debug(f"Raw response: {response}")

        # Check if we have nodes in the response
        if hasattr(response, "nodes") and response.nodes:
            logger.info("Response nodes found:")
            print_nodes(response.nodes)
        else:
            logger.info("No response nodes found in initial response")

            # Try to access other fields for debugging
            for field in ["nodes", "message_id", "request_id", "response_text"]:
                if hasattr(response, field):
                    value = getattr(response, field)
                    logger.info(f"Response.{field}: {value}")
    except Exception as e:
        logger.error(f"Error chatting with agent: {e}")
        logger.error(traceback.format_exc())


def delete_remote_agent(client: AugmentClient, agent_id: str) -> None:
    """Delete a remote agent."""
    logger.info(f"Deleting remote agent {agent_id}...")
    try:
        client.delete_remote_agent(agent_id)
        logger.info("Remote agent deleted successfully.")
    except Exception as e:
        logger.error(f"Error deleting remote agent: {e}")
        logger.error(traceback.format_exc())


def interrupt_remote_agent(client: AugmentClient, agent_id: str) -> None:
    """Interrupt a remote agent."""
    logger.info(f"Interrupting remote agent {agent_id}...")
    try:
        status = client.interrupt_remote_agent(agent_id)
        logger.info(f"Remote agent interrupted successfully. New status: {status}")
    except Exception as e:
        logger.error(f"Error interrupting remote agent: {e}")
        logger.error(traceback.format_exc())


def get_agent_workspace_stream(
    client: AugmentClient,
    agent_id: str,
    last_processed_sequence_id: int = 0,
    debug: bool = False,
    timeout_seconds: int | None = None,
) -> None:
    """Get the workspace updates for a remote agent with streaming.

    Args:
        client: The AugmentClient instance.
        agent_id: The ID of the remote agent.
        last_processed_sequence_id: The sequence ID of the last processed update.
            Use 0 to get all workspace updates.
        debug: Whether to print debug information.
        timeout_seconds: Maximum time to wait for the stream in seconds.
    """
    import json
    from colorama import Fore, Style, init

    # Initialize colorama
    init()

    logger.info(f"Getting streaming workspace updates for agent {agent_id}...")
    try:
        # Use the client's streaming method
        response = client.get_agent_workspace_stream(
            remote_agent_id=agent_id,
            last_processed_sequence_id=last_processed_sequence_id,
            timeout=timeout_seconds,
        )

        logger.info("Workspace streaming response started...")
        print(
            f"{Fore.CYAN}Streaming workspace updates for agent {agent_id}...{Style.RESET_ALL}"
        )
        print(f"{Fore.YELLOW}Press Ctrl+C to stop streaming{Style.RESET_ALL}")
        print("-" * 80)

        # Process the streaming response
        for update in client.parse_agent_workspace_stream(
            response, warn_on_parse_error=debug
        ):
            if debug:
                print(f"{Fore.CYAN}[DEBUG] Received update: {update}{Style.RESET_ALL}")

            for workspace_update in update.updates:
                update_type = workspace_update.type
                sequence_id = workspace_update.sequence_id

                if (
                    update_type
                    == AgentWorkspaceUpdateType.AGENT_WORKSPACE_UPDATE_INTERRUPT
                ):
                    print(
                        f"{Fore.RED}[Update] Interrupt request (Sequence ID: {sequence_id}){Style.RESET_ALL}"
                    )

                elif (
                    update_type
                    == AgentWorkspaceUpdateType.AGENT_WORKSPACE_UPDATE_CHAT_REQUEST
                ):
                    print(
                        f"{Fore.GREEN}[Update] Chat request (Sequence ID: {sequence_id}){Style.RESET_ALL}"
                    )

                    if (
                        workspace_update.chat_request
                        and workspace_update.chat_request.request_details
                    ):
                        request_details = workspace_update.chat_request.request_details

                        # Print request nodes if available
                        if "request_nodes" in request_details:
                            request_nodes = request_details["request_nodes"]
                            print(f"{Fore.YELLOW}Request nodes:{Style.RESET_ALL}")

                            for node in request_nodes:
                                node_type = node.get("type", 0)
                                node_id = node.get("id", 0)

                                if node_type == ChatRequestNodeType.TEXT:
                                    text_content = node.get("text_node", {}).get(
                                        "content", ""
                                    )
                                    print(
                                        f"  {Fore.BLUE}Text Node (ID: {node_id}):{Style.RESET_ALL}"
                                    )
                                    print(f"  {text_content}")

                                elif node_type == ChatRequestNodeType.TOOL_RESULT:
                                    tool_result = node.get("tool_result_node", {})
                                    tool_use_id = tool_result.get("tool_use_id", "")
                                    is_error = tool_result.get("is_error", False)
                                    content = tool_result.get("content", "")

                                    result_type = (
                                        f"{Fore.RED}Error"
                                        if is_error
                                        else f"{Fore.GREEN}Result"
                                    )
                                    print(
                                        f"  {result_type} (ID: {tool_use_id}):{Style.RESET_ALL}"
                                    )
                                    print(f"  {content}")

                                else:
                                    print(f"  Unknown node type: {node_type}")

                else:
                    print(
                        f"{Fore.BLUE}[Update] Unknown update type: {update_type} (Sequence ID: {sequence_id}){Style.RESET_ALL}"
                    )

        print("-" * 80)
        logger.info("Workspace streaming response completed")
        print(f"{Fore.CYAN}Streaming completed{Style.RESET_ALL}")

    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Streaming interrupted by user{Style.RESET_ALL}")
    except Exception as e:
        logger.error(f"Error getting streaming workspace updates: {e}")
        logger.error(traceback.format_exc())


def get_agent_list_stream(
    client: AugmentClient,
    last_update_timestamp: Optional[str] = None,
    debug: bool = False,
    timeout_seconds: int = 300,
) -> None:
    """Get streaming agent list updates for all remote agents.

    Args:
        client: The AugmentClient instance.
        last_update_timestamp: The timestamp of the last update the client has seen.
            If not provided, the server will send the current state of all agents
            and then stream subsequent updates.
        debug: Whether to print debug information.
        timeout_seconds: Maximum time to wait for the stream in seconds.
    """
    from datetime import datetime
    from colorama import Fore, Style, init

    # Initialize colorama
    init()

    logger.info("Starting agent list streaming...")
    print(f"{Fore.CYAN}Starting agent list streaming...{Style.RESET_ALL}")
    print("-" * 80)

    try:
        # Parse the timestamp if provided
        parsed_timestamp = None
        if last_update_timestamp:
            try:
                parsed_timestamp = datetime.fromisoformat(
                    last_update_timestamp.replace("Z", "+00:00")
                )
            except ValueError as e:
                logger.error(f"Invalid timestamp format: {e}")
                return

        # Get the streaming response
        for response in client.stream_agent_list_updates(
            last_update_timestamp=parsed_timestamp,
            timeout=timeout_seconds,
            warn_on_parse_error=True,
        ):
            if debug:
                logger.debug(f"Received agent list stream response: {response}")

            # Process each update in the response
            for update in response.updates:
                update_type = update.type
                update_timestamp = update.update_timestamp

                # Format timestamp
                timestamp_str = "Unknown"
                if update_timestamp:
                    try:
                        dt = datetime.fromtimestamp(
                            update_timestamp.seconds + update_timestamp.nanos / 1e9
                        )
                        timestamp_str = dt.strftime("%Y-%m-%d %H:%M:%S")
                    except Exception:
                        timestamp_str = str(update_timestamp)

                if update_type == AgentListUpdateType.AGENT_LIST_ALL_AGENTS:
                    print(
                        f"{Fore.GREEN}[Initial State] Received current agent list ({timestamp_str}){Style.RESET_ALL}"
                    )
                    if update.all_agents:
                        print(f"  Found {len(update.all_agents)} agents:")
                        for agent in update.all_agents:
                            status_name = enum_value_to_string(
                                AgentStatus, agent.status
                            )
                            print(f"    - {agent.remote_agent_id}: {status_name}")
                    else:
                        print("  No agents found")

                    if update.max_agents is not None:
                        print(f"  Max agents: {update.max_agents}")
                    if update.max_active_agents is not None:
                        print(f"  Max active agents: {update.max_active_agents}")

                elif update_type == AgentListUpdateType.AGENT_LIST_AGENT_ADDED:
                    print(
                        f"{Fore.GREEN}[Agent Added] ({timestamp_str}){Style.RESET_ALL}"
                    )
                    if update.agent:
                        agent = update.agent
                        status_name = enum_value_to_string(AgentStatus, agent.status)
                        print(f"  ID: {agent.remote_agent_id}")
                        print(f"  Status: {status_name}")
                        print(f"  Started: {format_timestamp(agent.started_at)}")
                        if agent.session_summary:
                            print(f"  Summary: {agent.session_summary}")

                elif update_type == AgentListUpdateType.AGENT_LIST_AGENT_UPDATED:
                    print(
                        f"{Fore.YELLOW}[Agent Updated] ({timestamp_str}){Style.RESET_ALL}"
                    )
                    if update.agent:
                        agent = update.agent
                        status_name = enum_value_to_string(AgentStatus, agent.status)
                        print(f"  ID: {agent.remote_agent_id}")
                        print(f"  Status: {status_name}")
                        print(f"  Started: {format_timestamp(agent.started_at)}")
                        if agent.session_summary:
                            print(f"  Summary: {agent.session_summary}")

                elif update_type == AgentListUpdateType.AGENT_LIST_AGENT_DELETED:
                    print(
                        f"{Fore.RED}[Agent Deleted] ({timestamp_str}){Style.RESET_ALL}"
                    )
                    if update.deleted_agent_id:
                        print(f"  ID: {update.deleted_agent_id}")

                else:
                    print(
                        f"{Fore.BLUE}[Unknown Update] Type: {update_type} ({timestamp_str}){Style.RESET_ALL}"
                    )

                print()  # Add spacing between updates

        print("-" * 80)
        logger.info("Agent list streaming response completed")
        print(f"{Fore.CYAN}Streaming completed{Style.RESET_ALL}")

    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Streaming interrupted by user{Style.RESET_ALL}")
    except Exception as e:
        logger.error(f"Error getting streaming agent list updates: {e}")
        logger.error(traceback.format_exc())


def main():
    """Main function."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    parser = argparse.ArgumentParser(
        description="Command-line tool for interacting with remote agents"
    )

    # Common arguments
    parser.add_argument(
        "--api-url",
        help="API URL (default: constructed from username in ~/.augment/user.json)",
    )
    parser.add_argument("--token-file", help="File containing the API token")
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging",
    )

    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # List command
    subparsers.add_parser("list", help="List all remote agents")

    # Create command
    create_parser = subparsers.add_parser("create", help="Create a new remote agent")
    create_parser.add_argument(
        "--system-prompt",
        default="You are a helpful assistant.",
        help="System prompt for the new agent",
    )
    create_parser.add_argument(
        "--initial-message",
        default="Hello, agent!",
        help="Initial message for the new agent",
    )
    create_parser.add_argument(
        "--repository-url",
        default="https://github.com/igor0/augment",
        help="Repository URL for the new agent",
    )
    create_parser.add_argument(
        "--git-ref", default="main", help="Git reference for the new agent"
    )
    create_parser.add_argument(
        "--model", default="claude-3-5-sonnet", help="Model for the new agent"
    )
    create_parser.add_argument(
        "--wait",
        action="store_true",
        help="Wait for the agent to be ready before returning",
    )
    create_parser.add_argument(
        "--wait-timeout",
        type=int,
        default=300,
        help="Maximum time to wait for the agent to be ready (in seconds, default: 5 minutes)",
    )

    # History command
    history_parser = subparsers.add_parser(
        "history", help="Get chat history for a remote agent"
    )
    history_parser.add_argument("agent_id", help="Remote agent ID")
    history_parser.add_argument(
        "--last-sequence-id",
        type=int,
        default=0,
        help="Last processed sequence ID for chat history",
    )

    # Stream history command
    stream_parser = subparsers.add_parser(
        "stream", help="Get streaming agent history for a remote agent"
    )
    stream_parser.add_argument("agent_id", help="Remote agent ID")
    stream_parser.add_argument(
        "--last-sequence-id",
        type=int,
        default=0,
        help="Last processed sequence ID for agent history",
    )

    # Workspace stream command
    workspace_stream_parser = subparsers.add_parser(
        "workspace-stream", help="Get streaming workspace updates for a remote agent"
    )
    workspace_stream_parser.add_argument("agent_id", help="Remote agent ID")
    workspace_stream_parser.add_argument(
        "--last-sequence-id",
        type=int,
        default=0,
        help="Last processed sequence ID for workspace updates",
    )

    # Agent list stream command
    agent_list_stream_parser = subparsers.add_parser(
        "list-stream", help="Get streaming agent list updates for all remote agents"
    )
    agent_list_stream_parser.add_argument(
        "--last-update-timestamp",
        help="Last update timestamp (ISO format, e.g., '2023-12-01T10:00:00Z')",
    )

    # Chat command
    chat_parser = subparsers.add_parser("chat", help="Send a message to a remote agent")
    chat_parser.add_argument("agent_id", help="Remote agent ID")
    chat_parser.add_argument("message", help="Message to send to the remote agent")

    # Delete command
    delete_parser = subparsers.add_parser("delete", help="Delete a remote agent")
    delete_parser.add_argument("agent_id", help="Remote agent ID")

    # Interrupt command
    interrupt_parser = subparsers.add_parser(
        "interrupt", help="Interrupt a remote agent"
    )
    interrupt_parser.add_argument("agent_id", help="Remote agent ID")

    args = parser.parse_args()

    # Set debug logging if requested
    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")

    try:
        # Get the API URL
        api_url = get_api_url(args.api_url)
        logger.info(f"Using API URL: {api_url}")

        # Create the client
        client = AugmentClient(url=api_url, token=get_api_token(args.token_file))

        if args.command == "list":
            list_remote_agents(client)
            return 0

        elif args.command == "create":
            create_remote_agent(
                client,
                system_prompt=args.system_prompt,
                initial_message=args.initial_message,
                repository_url=args.repository_url,
                git_ref=args.git_ref,
                model=args.model,
                wait=args.wait,
                wait_timeout=args.wait_timeout,
            )
            return 0

        elif args.command == "history":
            get_chat_history(client, args.agent_id, args.last_sequence_id)
            return 0

        elif args.command == "stream":
            get_agent_history_stream(
                client, args.agent_id, args.last_sequence_id, debug=args.debug
            )
            return 0

        elif args.command == "workspace-stream":
            get_agent_workspace_stream(
                client, args.agent_id, args.last_sequence_id, debug=args.debug
            )
            return 0

        elif args.command == "list-stream":
            get_agent_list_stream(
                client,
                last_update_timestamp=args.last_update_timestamp,
                debug=args.debug,
            )
            return 0

        elif args.command == "chat":
            chat_with_agent(
                client,
                args.agent_id,
                args.message,
            )
            return 0

        elif args.command == "delete":
            delete_remote_agent(client, args.agent_id)
            return 0

        elif args.command == "interrupt":
            interrupt_remote_agent(client, args.agent_id)
            return 0

        else:
            # If no valid command was provided, show help
            parser.print_help()
            return 0

    except Exception as e:
        logger.error(f"Error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
