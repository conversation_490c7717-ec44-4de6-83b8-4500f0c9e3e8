# Implementation Plan for Remote Agent Chat History Streaming

This document outlines the implementation plan for moving the polling of chat history from the client to the server, with the server streaming updates to the client as they become available. The implementation includes support for incremental updates similar to how the chat system handles RAW_RESPONSE nodes, allowing for real-time updates of agent responses as they are generated.

## Phase 1: Backend Changes

### 1. Define Protocol Messages

Add new protocol messages to `services/remote_agents/remote_agents.proto`:
```protobuf
message ChatHistoryStreamRequest {
  string remote_agent_id = 1;
  uint32 last_processed_sequence_id = 2;
}

// Define node types for streaming responses
enum ChatHistoryNodeType {
  // The main chat history exchange
  CHAT_HISTORY_EXCHANGE = 0;
  // Indication that streaming of the current exchange is complete
  EXCHANGE_COMPLETE = 1;
  // Agent status update
  AGENT_STATUS = 2;
}

message ChatHistoryNode {
  uint32 id = 1;
  ChatHistoryNodeType type = 2;
  // For CHAT_HISTORY_EXCHANGE nodes
  optional ChatHistoryExchange exchange = 3;
  // For AGENT_STATUS nodes
  optional Agent agent = 4;
  // Flag to indicate if this is a resent exchange (for reconnection scenarios)
  optional bool is_resent = 5;
}

message ChatHistoryStreamResponse {
  // Incremental text update for the current exchange
  optional string text_update = 1;
  // The sequence ID of the exchange being updated
  optional uint32 sequence_id = 2;
  // Structured nodes for different types of updates
  repeated ChatHistoryNode nodes = 3;
  // Session summary
  optional string session_summary = 4;
}
```

Update the `ChatHistoryExchange` message to include a `status` field:
```protobuf
message ChatHistoryExchange {
  optional chat.Exchange exchange = 1;
  repeated ChangedFile changed_files = 2;
  uint32 sequence_id = 3;
  optional string turn_summary = 4;
  google.protobuf.Timestamp finished_at = 5;
  optional string status = 6; // "complete", "interrupted", or "in_progress"
}
```

Add a new RPC method to the `RemoteAgents` service:
```protobuf
service RemoteAgents {
  // Existing methods...
  rpc ChatHistoryStream(ChatHistoryStreamRequest) returns (stream ChatHistoryStreamResponse) {}
}
```

### 2. Implement Missing Helper Functions

1. Add `readExchangeBySequenceId` function to read a specific exchange by its sequence ID:
   - Create a row key for the specific exchange
   - Read the row from BigTable
   - Extract and return the exchange

2. Create an alias for `readRemoteAgent` as `getAgent` for consistency with the implementation plan.

### 3. Implement Server-Side Polling

Implement the `ChatHistoryStream` handler in `services/remote_agents/server/server.go`:
- Validate user authorization
- Set up polling with a 1-second interval
- Track the last processed sequence ID
- Handle reconnection scenarios by checking if the last processed exchange is incomplete
- Poll BigTable for new exchanges and send incremental updates
- Track the last sent content for each exchange to support incremental updates
- Send completion notifications when an exchange is finished
- Include robust error handling and retry logic

### 4. Add API Proxy Endpoint

1. Add a new endpoint in `services/api_proxy/server/src/handlers_remote_agents.rs`:
   - Implement `EndpointHandler` for `GetRemoteAgentChatHistoryStreamRequest`
   - Convert the streaming response to an HTTP streaming response
   - Add proper error handling and timeout configuration

2. Add a new feature flag for the timeout:
   ```rust
   pub const CHAT_HISTORY_STREAM_REMOTE_AGENT_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
       feature_flags::IntFlag::new("chat_history_stream_remote_agent_timeout_ms", 30 * 60 * 1000);
   ```

3. Add the endpoint to the API proxy routes.

### 5. Testing in Development Environment

1. Deploy the changes to the development environment:
   ```bash
   bazel run //services/deploy:dev_deploy -- --services chat agents default remote_agents_all --operation Apply
   ```

2. Extend the `remote_agent_cli.py` to support streaming:
   - Add a new command to test the streaming endpoint
   - Implement a streaming client that connects to the new endpoint
   - Display incremental updates as they arrive

3. Test the streaming functionality with the CLI:
   ```bash
   # First, create a test agent if needed
   ./remote_agent_cli.py create --repository-url https://github.com/your-test-repo --git-ref main --initial-message "Hello"

   # Then test the streaming functionality
   ./remote_agent_cli.py stream-history <agent-id>
   ```

4. Verify the following test cases:
   - Initial connection and receiving complete history
   - Incremental updates as the agent responds
   - Reconnection after disconnection
   - Handling of exchange completion events
   - Performance with large chat histories

5. Monitor server logs for any errors:
   ```bash
   kubectl -n dev-igor logs -f deployment/remote-agents-server
   ```

## Phase 2: Frontend Changes

### 1. Add Client API Methods

Add a new method to the `AugmentAPI` class in `clients/vscode/src/augment-api.ts`:
```typescript
async *getRemoteAgentChatHistoryStream(
  agentId: string,
  lastProcessedSequenceId: number = 0,
): AsyncGenerator<RemoteAgentChatHistoryStreamResponse> {
  const url = `${this.apiUrl}/remote-agents/chat-history-stream`;
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`,
    },
    body: JSON.stringify({
      remote_agent_id: agentId,
      last_processed_sequence_id: lastProcessedSequenceId,
    }),
  });

  if (!response.ok) {
    throw new Error(`Failed to get remote agent chat history stream: ${response.statusText}`);
  }

  if (!response.body) {
    throw new Error('Response body is null');
  }

  const reader = response.body.getReader();
  const decoder = new TextDecoder();

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n').filter(line => line.trim() !== '');

      for (const line of lines) {
        try {
          const data = JSON.parse(line);
          yield data;
        } catch (e) {
          console.error('Failed to parse JSON:', e);
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
}
```

Add new message types to the VSCode extension:
```typescript
// Define node types for streaming responses
export enum ChatHistoryNodeType {
  // The main chat history exchange
  CHAT_HISTORY_EXCHANGE = 0,
  // Indication that streaming of the current exchange is complete
  EXCHANGE_COMPLETE = 1,
  // Agent status update
  AGENT_STATUS = 2,
}

export interface ChatHistoryNode {
  id: number;
  type: ChatHistoryNodeType;
  // For CHAT_HISTORY_EXCHANGE nodes
  exchange?: ChatHistoryExchange;
  // For AGENT_STATUS nodes
  agent?: RemoteAgent;
  // Flag to indicate if this is a resent exchange (for reconnection scenarios)
  is_resent?: boolean;
}

export interface RemoteAgentChatHistoryStreamRequest {
  type: WebViewMessageType.remoteAgentChatHistoryStreamRequest;
  data: {
    agentId: string;
    lastProcessedSequenceId: number;
  };
}

export interface RemoteAgentChatHistoryStreamResponse {
  type: WebViewMessageType.remoteAgentChatHistoryStreamResponse;
  data: {
    // Incremental text update for the current exchange
    text_update?: string;
    // The sequence ID of the exchange being updated
    sequence_id?: number;
    // Structured nodes for different types of updates
    nodes?: ChatHistoryNode[];
    // Session summary
    session_summary?: string;
  };
}
```

### 2. Update RemoteAgentsModel

Update the `RemoteAgentsModel` class to use streaming for chat history:
- Replace polling timers with a streaming connection
- Implement reconnection logic with exponential backoff
- Handle different types of streamed updates:
  - Incremental text updates for existing exchanges
  - New exchange nodes
  - Exchange completion notifications
  - Agent status updates
- Update state management for incremental updates
- Maintain backward compatibility

Update the `startPolling`, `stopPolling`, and `setCurrentAgent` methods to use streaming instead of polling for chat history.

## Phase 3: Testing and Stabilization

### 1. Test Streaming Functionality

Test various scenarios:
- Different agents and sequence IDs
- Long-running connections
- Disconnections and reconnections
- Interrupt and resume scenarios:
  - Stream breaks during an exchange
  - Stream breaks between exchanges
  - Client explicitly interrupts agent
  - Agent continues execution while client is disconnected

### 2. Monitor Performance and Resource Usage

Add monitoring for:
- Active streaming connections
- Messages per connection
- Latency and resource usage
- Connection establishment and closure events

### 3. Gradual Rollout

Implement a feature flag for enabling/disabling streaming:
```typescript
const ENABLE_CHAT_HISTORY_STREAMING = feature_flags::BoolFlag::new("enable_chat_history_streaming", false);
```

Roll out gradually, starting with a small percentage of users and monitoring for issues.
