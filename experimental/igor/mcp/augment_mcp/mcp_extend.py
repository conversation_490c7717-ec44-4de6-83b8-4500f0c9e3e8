"""Extensions to the MCP (Model Control Protocol) server and types.

This module extends the MCP Python SDK, adding support for tools to be marked with
different safety levels and provide a mechanism to verify tool call safety before
execution.
"""

from typing import Awaitable, Callable, Literal
from pydantic import RootModel

import mcp.types
from mcp.types import (
    CallToolRequestParams,
    Request,
    Result,
    Tool,
)

ToolSafety = Literal["unsafe", "safe", "check"]


class ExtendedTool(Tool):
    def __init__(
        self,
        name: str,
        description: str,
        inputSchema: dict,
        toolSafety: ToolSafety = "unsafe",
    ):
        super().__init__(name=name, description=description, inputSchema=inputSchema)
        self.toolSafety = toolSafety


class CheckSafeResult(Result):
    """The server's response to a tool call."""

    isSafe: bool


class CheckSafeRequest(Request):
    """Used by the client to invoke a tool provided by the server."""

    method: Literal["tools/check-safe"]
    params: CallToolRequestParams


class ExtendedClientRequest(
    RootModel[
        mcp.types.PingRequest
        | mcp.types.InitializeRequest
        | mcp.types.CompleteRequest
        | mcp.types.SetLevelRequest
        | mcp.types.GetPromptRequest
        | mcp.types.ListPromptsRequest
        | mcp.types.ListResourcesRequest
        | mcp.types.ListResourceTemplatesRequest
        | mcp.types.ReadResourceRequest
        | mcp.types.SubscribeRequest
        | mcp.types.UnsubscribeRequest
        | mcp.types.CallToolRequest
        | mcp.types.ListToolsRequest
        | CheckSafeRequest
    ]
):
    pass


class ExtendedServerResult(
    RootModel[
        mcp.types.EmptyResult
        | mcp.types.InitializeResult
        | mcp.types.CompleteResult
        | mcp.types.GetPromptResult
        | mcp.types.ListPromptsResult
        | mcp.types.ListResourcesResult
        | mcp.types.ListResourceTemplatesResult
        | mcp.types.ReadResourceResult
        | mcp.types.CallToolResult
        | mcp.types.ListToolsResult
        | CheckSafeResult
    ]
):
    pass


mcp.types.ClientRequest = ExtendedClientRequest
mcp.types.ServerResult = ExtendedServerResult

import mcp.types as mcp_types  # noqa: E402
import mcp.server as mcp_server  # noqa: E402


def check_safe(s: mcp_server.Server):
    def decorator(
        func: Callable[..., Awaitable[bool]],
    ):
        async def handler(req: CheckSafeRequest):
            is_safe: bool = await func(req.params.name, (req.params.arguments or {}))
            return ExtendedServerResult(CheckSafeResult(isSafe=is_safe))

        s.request_handlers[CheckSafeRequest] = handler
        return func

    return decorator
