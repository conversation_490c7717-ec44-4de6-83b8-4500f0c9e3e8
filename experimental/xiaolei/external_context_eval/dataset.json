[{"id": 1, "message": "How can you modify a dependency in your Bun project without forking or vendoring it, ensuring that your changes persist and can be shared?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bun~1.1.21"], "answer": "You can use `bun patch` to modify a dependency without forking or vendoring it. Run `bun patch <pkg>`, make your changes in the cloned package within `node_modules`, and then run `bun patch --commit <pkg>` to save your changes into a `.patch` file that persists and can be shared.", "context": ["## `bun patch`\nBun v1.1.14 introduces a new subcommand for <PERSON><PERSON>'s package manager:\n`bun patch`\n\n``` shiki\nbun patch <pkg>\n```\n\nSometimes, dependencies have bugs or missing features. You could fork\nthe package, make your changes, and publish it. But that's a lot of work\nfor a small change. And what if you don't want to maintain a fork? What\nif you want to continue to receive updates from the original package,\nbut with your changes?\n\n`bun patch` makes it easy to modify dependencies without forking or\nvendoring. Changes persist into a `.patch` file which can be reviewed,\nshared, versioned, and reused in other projects. These `.patch` files\nare automatically applied on `bun install`, with the results cached in\n<PERSON><PERSON>'s Global Cache.\n\nTo get started, run `bun patch <pkg>` to patch a package.\n\n<div class=\"CodeBlock\">\n\n``` shiki\nbun patch is-even\n```\n\n``` shiki\nbun patch v1.1.14\n\n+ is-even@1.0.0\n\n5 packages installed [3.00ms]\n\nTo patch is-even, edit the following folder:\n\n  node_modules/is-even\n\nOnce you're done with your changes, run:\n\n  bun patch --commit 'node_modules/is-even'\n```\n\n</div>\n\nInternally, this clones the package in node_modules with a fresh copy of\nitself independent from the shared cache. This allows you to safely make\nedits to files in the package's directory without impacting the Global\nCache.\n\nSince we clone the package, you can test your patches locally in your\nproject's node_modules folder. This makes it easy to iterate on your\nchanges and verify they work as expected.\n\nOnce you're happy with your changes, run `bun patch --commit <pkg>` to\nsave your changes.\n\n``` shiki\nbun patch --commit is-even\n```\n"], "note": ""}, {"id": 2, "message": "In Bun, how can you create a lazily-loaded file reference and read its contents in different formats?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bun~1.1.21"], "answer": "You can use `Bun.file(path)` to create a lazily-loaded `BunFile` reference. Then, you can read its contents using methods like `await foo.text()`, `await foo.stream()`, `await foo.arrayBuffer()`, or `await foo.bytes()` to get the data in different formats.", "context": ["## Reading files (`Bun.file()`)\n\n`Bun.file(path): BunFile`\n\nCreate a `BunFile` instance with the `Bun.file(path)` function. A\n`BunFile` represents a lazily-loaded file; initializing it does not\nactually read the file from disk.\n\n``` shiki\nconst foo = Bun.file(\"foo.txt\"); // relative to cwd\nfoo.size; // number of bytes\nfoo.type; // MIME type\n```\n\nThe reference conforms to the `Blob` interface, so the contents can be\nread in various formats.\n\n``` shiki\nconst foo = Bun.file(\"foo.txt\");\n\nawait foo.text(); // contents as a string\nawait foo.stream(); // contents as ReadableStream\nawait foo.arrayBuffer(); // contents as ArrayBuffer\nawait foo.bytes(); // contents as Uint8Array\n```\n\nFile references can also be created using numerical file descriptors or\n`file://` URLs.\n\n``` shiki\nBun.file(1234);\nBun.file(new URL(import.meta.url)); // reference to the current file\n```\n\nA `BunFile` can point to a location on disk where a file does not exist.\n\n``` shiki\nconst notreal = Bun.file(\"notreal.txt\");\nnotreal.size; // 0\nnotreal.type; // \"text/plain;charset=utf-8\"\nconst exists = await notreal.exists(); // false\n```\n\nThe default MIME type is `text/plain;charset=utf-8`, but it can be\noverridden by passing a second argument to `Bun.file`.\n\n``` shiki\nconst notreal = Bun.file(\"notreal.json\", { type: \"application/json\" });\nnotreal.type; // => \"application/json;charset=utf-8\"\n```\n\nFor convenience, Bun exposes `stdin`, `stdout` and `stderr` as instances\nof `BunFile`.\n\n``` shiki\nBun.stdin; // readonly\nBun.stdout;\nBun.stderr;\n```\n"], "note": ""}, {"id": 3, "message": "How can you enable lifecycle scripts like 'postinstall' for specific packages in Bun while maintaining security?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bun~1.1.21"], "answer": "By adding the package names to the `trustedDependencies` array in your `package.json`, you can enable lifecycle scripts like `postinstall` for specific packages while maintaining Bun's secure default configuration.", "context": ["Packages on `npm` can define *lifecycle scripts* in their\n`package.json`. Some of the most common are below, but there are many\nothers.\n\n- `preinstall`: Runs before the package is installed\n- `postinstall`: Runs after the package is installed\n- `preuninstall`: Runs before the package is uninstalled\n- `prepublishOnly`: Runs before the package is published\n\nThese scripts are arbitrary shell commands that the package manager is\nexpected to read and execute at the appropriate time. But executing\narbitrary scripts represents a potential security risk, so—unlike other\n`npm` clients—<PERSON><PERSON> does not execute arbitrary lifecycle scripts by\ndefault.\n\n## `postinstall`\n\nThe `postinstall` script is particularly important. It's widely used to\nbuild or install platform-specific binaries for packages that are\nimplemented as native Node.js add-ons. For example, `node-sass` is a\npopular package that uses `postinstall` to build a native binary for\nSass.\n\n``` shiki\n{\n  \"name\": \"my-app\",\n  \"version\": \"1.0.0\",\n  \"dependencies\": {\n    \"node-sass\": \"^6.0.1\"\n  }\n}\n```\n\n## `trustedDependencies`\n\nInstead of executing arbitrary scripts, <PERSON><PERSON> uses a \"default-secure\"\napproach. You can add certain packages to an allow list, and <PERSON><PERSON> will\nexecute lifecycle scripts for those packages. To tell <PERSON><PERSON> to allow\nlifecycle scripts for a particular package, add the package name to\n`trustedDependencies` array in your `package.json`.\n\n``` shiki\n{\n  \"name\": \"my-app\",\n  \"version\": \"1.0.0\",\n  \"trustedDependencies\": [\"node-sass\"]\n}\n```\n\nOnce added to `trustedDependencies`, install/re-install the package. Bun\nwill read this field and run lifecycle scripts for `my-trusted-package`.\n\nAs of Bun v1.0.16, the top 500 npm packages with lifecycle scripts are\nallowed by default. You can see the full list here.\n\n## `--ignore-scripts`\n\nTo disable lifecycle scripts for all packages, use the\n`--ignore-scripts` flag.\n\n``` shiki\nbun install --ignore-scripts\n```\n"], "note": ""}, {"id": 4, "message": "Regarding the following source code:\n```\nimport { test, expect } from \"bun:test\";\n\nexpect.extend({\n  toBeWithinRange(received, floor, ceiling) {\n    const pass = received >= floor && received <= ceiling;\n    if (pass) {\n      return {\n        message: () =>\n          `Expected ${received} not to be within range ${floor} - ${ceiling}`,\n        pass: true,\n      };\n    } else {\n      return {\n        message: () =>\n          `Expected ${received} to be within range ${floor} - ${ceiling}`,\n        pass: false,\n      };\n    }\n  },\n});\n\ntest(\"toBeWithinRange()\", () => {\n  expect(1).toBeWithinRange(1, 99); // ✅\n  expect(100).toBeWithinRange(1, 99); // ❌ Expected 100 to be within range 1 - 99\n});\n```\nHow can you create custom test matchers in <PERSON><PERSON>'s test runner to extend the functionality of 'expect'?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bun~1.1.21"], "answer": "You can create custom test matchers by using `expect.extend()` in <PERSON><PERSON>'s test runner. This allows you to add new matchers to `expect` for use in your tests.", "context": ["## Bun is a test runner\n### Matchers\n\nMatchers are assertions that you can use to test your code. Since Bun\n1.0, we've added dozens of new `expect()` matchers, including:\n\n``` shiki\nimport { expect } from \"bun:test\";\n\nexpect.hasAssertions();\nexpect.assertions(9);\nexpect({}).toBeObject();\nexpect([{ foo: \"bar\" }]).toContainEqual({ foo: \"bar\" });\nexpect(\" foo \").toEqualIgnoringWhitespace(\"foo\");\nexpect(\"foo\").toBeOneOf([\"foo\", \"bar\"]);\nexpect({ foo: Math.PI }).toEqual({ foo: expect.closeTo(3.14) });\nexpect({ a: { b: 1 } }).toEqual({ a: expect.objectContaining({ b: 1 }) });\nexpect({ a: Promise.resolve(\"bar\") }).toEqual({ a: expect.resolvesTo(\"bar\") });\nexpect({ b: Promise.reject(\"bar\") }).toEqual({ b: expect.rejectsTo(\"bar\") });\nexpect.unreachable();\n```\n\n### Custom matchers with `expect.extend()`\n\nIf there's a matcher that <PERSON><PERSON> doesn't support, you can create your own\nusing `expect.extend()`. This is useful when you want to define a custom\nmatcher that is reusable across multiple tests.\n\n``` shiki\nimport { test, expect } from \"bun:test\";\n\nexpect.extend({\n  toBeWithinRange(received, floor, ceiling) {\n    const pass = received >= floor && received <= ceiling;\n    if (pass) {\n      return {\n        message: () =>\n          `Expected ${received} not to be within range ${floor} - ${ceiling}`,\n        pass: true,\n      };\n    } else {\n      return {\n        message: () =>\n          `Expected ${received} to be within range ${floor} - ${ceiling}`,\n        pass: false,\n      };\n    }\n  },\n});\n\ntest(\"toBeWithinRange()\", () => {\n  expect(1).toBeWithinRange(1, 99); // ✅\n  expect(100).toBeWithinRange(1, 99); // ❌ Expected 100 to be within range 1 - 99\n});\n```\n"], "note": ""}, {"id": 5, "message": "What is the updated behavior of 'import.meta.resolve()' in Bun 1.1 compared to previous versions?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bun~1.1.21"], "answer": "In Bun 1.1, `import.meta.resolve()` now synchronously returns a URL string (e.g., 'file:///path/to/foo.js') instead of asynchronously resolving to an absolute file path as it did in previous versions.", "context": ["## Behaviour changes\n\n### `import.meta.resolve()`\n\nIn Bun 1.0, `import.meta.resolve()` would asynchronously resolve to an\nabsolute file path.\n\nThis matched the behavior of Node.js' original implementation. However,\nfor Web API compatibility reasons, Node.js changed the API to be\nsynchronous. And so, <PERSON><PERSON> has done the same.\n\n``` shiki\nimport.meta.resolve(\"./foo.js\"); // Before: Promise { \"/path/to/foo.js\" }\nimport.meta.resolve(\"./foo.js\"); // After: \"file:///path/to/foo.js\"\n```\n"], "note": ""}, {"id": 6, "message": "How can you import the 'LayoutPlugin' from BootstrapVue in the preferred way according to the changes made in version 2.0.0-rc.22?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bootstrap_vue"], "answer": "By importing 'LayoutPlugin' as a named export from 'bootstrap-vue' and using `Vue.use(LayoutPlugin)`.", "context": ["### <span class=\"bd-content-title\"> Importing as a Vue.js plugin </span>\n<span class=\"badge small badge-info\">CHANGED in 2.0.0-rc.22</span>\nImporting plugins has been simplified.\n\nThis plugin includes all of the above listed individual components.\nPlugins also include any component aliases.\n\n| Named Export                                                             | Import Path                          |\n|--------------------------------------------------------------------------|--------------------------------------|\n| `LayoutPlugin` <span class=\"badge small badge-success\">PREFERRED</span>  | `bootstrap-vue`                      |\n| `LayoutPlugin` <span class=\"badge small badge-warning\">DEPRECATED</span> | `bootstrap-vue/es/components`        |\n| `default` <span class=\"badge small badge-warning\">DEPRECATED</span>      | `bootstrap-vue/es/components/layout` |\n\nThe plugin can be imported via several methods\n\n**Example:**\n\n``` hljs\n// Importing the named export\nimport { LayoutPlugin } from 'bootstrap-vue'\nVue.use(LayoutPlugin)\n```"], "note": ""}, {"id": 7, "message": "In BootstrapVue, how do you set the 'switch' styling for a form checkbox input?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bootstrap_vue"], "answer": "By adding the `switch` prop to the `<b-form-checkbox>` component and setting it to true.", "context": ["here is a chunk from form-checkbox.md:\n```\nForm Checkbox Inputs\n#### <span class=\"bd-content-title\"> Properties </span>\n\nProperty\n\nType\n\nDefault Value\n\n`value`\n\nObject\n\n`true`\n\n`checked`\n\nString or Number or Object or Array or Boolean\n\n`inline`\n\nBoolean\n\n`false`\n\n`plain`\n\n<PERSON>olean\n\n`false`\n\n`button`\n\nBoolean\n\n`false`\n\n`button-variant`\n\nString\n\n`aria-label`\n\nString\n\n`aria-labelledby`\n\nString\n\n`id`\n\nString\n\n`name`\n\nString\n\n`disabled`\n\nBoolean\n\n`required`\n\nBoolean\n\n`false`\n\n`form`\n\nString\n\n`autofocus`\n\n<PERSON>olean\n\n`false`\n\n`size`\n\nString\n\n`state`\n\nString or Boolean\n\n`unchecked-value`\n\nObject\n\n`false`\n\n`indeterminate`\n\nBoolean\n\n`false`\n\n`switch`\n\n<PERSON><PERSON>an\n\n`false`\n```"], "note": ""}, {"id": 8, "message": "How can you customize the default breakpoint names in BootstrapVue to include 'xxl', and ensure components like 'b-col' use them?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bootstrap_vue"], "answer": "By passing a configuration object with the `breakpoints` array including `'xxl'` to `Vue.use(BootstrapVue)`, ensuring the breakpoint names match your custom Bootstrap SCSS.", "context": ["here is a chunk from settings.md:\n```\nSettings\n# <span class=\"bd-content-title\">Settings</span>\n## <span class=\"bd-content-title\">Configuring defaults</span>\n### <span class=\"bd-content-title\">Setting new configuration values</span>\n<span class=\"badge badge-info small\">ENHANCED in v2.0.0-rc.22</span>\n\nWhen you `Vue.use(BootstrapVue)`, you can optionally pass a\nconfiguration object which specifies new values to replace the default\nvalues. For example if you wish to define new breakpoint names (which\nwill generate appropriate properties on components such as `<b-col>` and\n`<b-form-group>`), so that the new breakpoints are\n`['aa', 'bb', 'cc', 'dd']` then `<b-col>` will now have `bb`, `cc`, and\n`dd` props instead of `sm`, `md`, `lg` and `xl` props (Similar for the\n`label-cols-{breakpoint}` and `label-align-{breakpoint}`props on\n`<b-form-group>`):\n\n``` hljs\nimport BootstrapVue from 'bootstrap-vue'\nVue.use(BootstrapVue, {\n  breakpoints: [`xs`, 'sm', 'md', 'lg', 'xl', 'xxl']\n})\n```\n\nOr if changing the default variants for `<b-button>` and `<b-alert>`:\n\n``` hljs\nimport BootstrapVue from 'bootstrap-vue'\nVue.use(BootstrapVue, {\n  BAlert: { variant: 'danger' },\n  BButton: { variant: 'primary' }\n})\n```\n\nThe values provided as the config option to `Vue.use` will be merged\nwith the default values.\n\n**Note:** When defining custom breakpoints, keep the names short (2 to 3\ncharacters). At least two breakpoint names must be defined. The\nbreakpoint names **must** match the breakpoint names defined in your\ncustom Bootstrap SCSS. Breakpoint names must not conflict with\nnon-breakpoint prop names used on various components (i.e. avoid `to`,\n`col`, etc)\n```"], "note": ""}, {"id": 9, "message": "When importing individual components from BootstrapVue, how should you import them to avoid deprecation warnings?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bootstrap_vue"], "answer": "By importing components directly from 'bootstrap-vue' using the named exports, rather than importing from deprecated paths like 'bootstrap-vue/es/components'.", "context": ["here is a chunk from input-group.md:\n```\n### <span class=\"bd-content-title\"> Importing as a Vue.js plugin </span>\n<span class=\"badge small badge-info\">CHANGED in 2.0.0-rc.22</span>\nImporting plugins has been simplified.\n\nThis plugin includes all of the above listed individual components.\nPlugins also include any component aliases.\n\n| Named Export                                                                 | Import Path                               |\n|------------------------------------------------------------------------------|-------------------------------------------|\n| `InputGroupPlugin` <span class=\"badge small badge-success\">PREFERRED</span>  | `bootstrap-vue`                           |\n| `InputGroupPlugin` <span class=\"badge small badge-warning\">DEPRECATED</span> | `bootstrap-vue/es/components`             |\n| `default` <span class=\"badge small badge-warning\">DEPRECATED</span>          | `bootstrap-vue/es/components/input-group` |\n\nThe plugin can be imported via several methods\n\n**Example:**\n\n``` hljs\n// Importing the named export\nimport { InputGroupPlugin } from 'bootstrap-vue'\nVue.use(InputGroupPlugin)\n```\n```\n\nhere is a chunk from dropdown.md:\n```\n### <span class=\"bd-content-title\"> Importing individual components </span>\n<span class=\"badge small badge-info\">CHANGED in 2.0.0-rc.22</span> You\ncan import individual components into your project via the following\nnamed exports:\n\n| Component                  | Named Export          | Import Path     |\n|----------------------------|-----------------------|-----------------|\n| `<b-dropdown>`             | `BDropdown`           | `bootstrap-vue` |\n| `<b-dropdown-item>`        | `BDropdownItem`       | `bootstrap-vue` |\n| `<b-dropdown-item-button>` | `BDropdownItemButton` | `bootstrap-vue` |\n| `<b-dropdown-divider>`     | `BDropdownDivider`    | `bootstrap-vue` |\n| `<b-dropdown-form>`        | `BDropdownForm`       | `bootstrap-vue` |\n| `<b-dropdown-text>`        | `BDropdownText`       | `bootstrap-vue` |\n| `<b-dropdown-group>`       | `BDropdownGroup`      | `bootstrap-vue` |\n| `<b-dropdown-header>`      | `BDropdownHeader`     | `bootstrap-vue` |\n\n**Example:**\n\n``` hljs\nimport { BDropdown } from 'bootstrap-vue'\nVue.component('b-dropdown', BDropdown)\n```\n```"], "note": ""}, {"id": 10, "message": "According to the BootstrapVue changelog for v2.0.0-rc.19, what change was made to the 'b-dropdown' component regarding its HTML structure?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bootstrap_vue"], "answer": "The 'b-dropdown' component was updated to use semantic `<ul>` and `<li>` HTML markup in its structure.", "context": ["here is a chunk from changelog.md:\n```\nChangelog\n# <span class=\"bd-content-title\">Changelog</span>\n## <span class=\"bd-content-title\">v2.0.0-rc.19</span>\n\n### <span class=\"bd-content-title\">Features v2.0.0-rc.19</span>\n\n**b-dropdown:** use semantic `<ul>` and `<li>` markup (closes \\#3072)\n(#3087 58ad66b)\n```"], "note": ""}, {"id": 11, "message": "How can I make a View accessible to assistive technologies, specify its current state as selected and disabled, and represent it as a 'button'?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["react_native~0.61.0"], "answer": "By setting the 'accessibilityRole' prop to 'button' on the View, and providing an 'accessibilityState' object with 'selected' and 'disabled' set to true.", "context": ["### `accessibilityRole`\n\n`'tablist'` - Used to represent a list of tabs.\n\n`'timer'` - Used to represent a timer.\n\n`'toolbar'` - Used to represent a tool bar (a container of action buttons or components).\n\n\nType\n\nRequired\n\nstring\n\nNo\n\n\n### `accessibilityState`\n\nDescribes the current state of a component to the user of an assistive technology.\n\nSee the Accessibility guide for more information.\n\n| Type                                                                                           | Required |\n|------------------------------------------------------------------------------------------------|----------|\n| object: {disabled: bool, selected: bool, checked: bool or 'mixed', busy: bool, expanded: bool} | No       |"], "note": ""}, {"id": 12, "message": "How can I render an inline image to the left of a TextInput component on Android in React Native?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["react_native~0.61.0"], "answer": "By using the 'inlineImageLeft' prop on the TextInput and specifying the image resource name located in the drawable folder.", "context": ["### `inlineImageLeft`\n\nIf defined, the provided image resource will be rendered on the left.\nThe image resource must be inside `/android/app/src/main/res/drawable`\nand referenced like\n\n``` hljs\n<TextInput\n inlineImageLeft='search_icon'\n/>\n```\n\n| Type   | Required | Platform |\n|--------|----------|----------|\n| string | No       | Android  |"], "note": ""}, {"id": 13, "message": "How can I enable nested scrolling in a ScrollView on Android devices running API level 21 and above?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["react_native~0.61.0"], "answer": "By setting the 'nestedScrollEnabled' prop to true on the ScrollView component for Android.", "context": ["### `nestedScrollEnabled`\n\nEnables nested scrolling for Android API level 21+. Nested scrolling is supported by default on iOS.\n\n| Type | Required | Platform |\n|------|----------|----------|\n| bool | No       | Android  |"], "note": ""}, {"id": 14, "message": "How can I configure a FlatList to consider an item as viewable only when 95% of it is visible in React Native?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["react_native~0.61.0"], "answer": "By setting the 'viewabilityConfig' prop with 'viewAreaCoveragePercentThreshold' set to 95 on the FlatList.", "context": ["### `viewabilityConfig`\nSee `ViewabilityHelper.js` for flow type and further documentation.\n\n| Type              | Required |\n|-------------------|----------|\n| ViewabilityConfig | No       |\n\n`viewabilityConfig` takes a type `ViewabilityConfig` an object with following properties\n\n| Property                         | Required | Type    |\n|----------------------------------|----------|---------|\n| minimumViewTime                  | No       | number  |\n| viewAreaCoveragePercentThreshold | No       | number  |\n| itemVisiblePercentThreshold      | No       | number  |\n| waitForInteraction               | No       | boolean |\n\nAt least one of the `viewAreaCoveragePercentThreshold` or `itemVisiblePercentThreshold` is required. This needs to be done in the `constructor` to avoid following error (ref):\n\n``` hljs\n  Error: Changing viewabilityConfig on the fly is not supported`\n```\n\n``` hljs\nconstructor (props) {\n  super(props)\n\n  this.viewabilityConfig = {\n      waitForInteraction: true,\n      viewAreaCoveragePercentThreshold: 95\n  }\n}\n```\n\n``` hljs\n<FlatList\n    viewabilityConfig={this.viewabilityConfig}\n  ...\n```\n\n#### viewAreaCoveragePercentThreshold\n\nPercent of viewport that must be covered for a partially occluded item to count as \"viewable\", 0-100. Fully visible items are always considered viewable. A value of 0 means that a single pixel in the viewport makes the item viewable, and a value of 100 means that an item must be either entirely visible or cover the entire viewport to count as viewable."], "note": ""}, {"id": 15, "message": "How can I create an ImageBackground component with a text overlay in React Native, and what must I include in its style?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["react_native~0.61.0"], "answer": "By using an ImageBackground component wrapping a Text component for the overlay, and specifying width and height in its style.", "context": ["Note that you must specify some width and height style attributes.\n\n## Example\n\n``` hljs\nreturn (\n  <ImageBackground source={...} style={{width: '100%', height: '100%'}}>\n    <Text>Inside</Text>\n  </ImageBackground>\n);\n```"], "note": ""}, {"id": 16, "message": "In MongoDB, can you use both Client-Side Field Level Encryption and Queryable Encryption to encrypt different fields in the same collection?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["mongodb~7.0.9"], "answer": "No, you cannot use both Client-Side Field Level Encryption and Queryable Encryption to encrypt different fields in the same collection.", "context": ["Features\n# Features.css-134mg1q{-webkit-align-self:center;-ms-flex-item-align:center;align-self:center;padding:0 10px;visibility:hidden;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}.css-6vrlzm{border-radius:0!important;display:initial!important;margin:initial!important;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}.css-1l4s55v{margin-top:-175px;position:absolute;padding-bottom:2px;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}\n## Comparison of Features\nEncryption at Rest, Transport Encryption, and optionally, the In-Use Encryption security mechanisms together. Please note that you cannot use both CSFLE and Queryable Encryption to encrypt different fields in the same collection.\n\nTo learn more about Queryable Encryption, see Queryable Encryption Features.\n\n</div>\n\n</div>\n\n</div>"], "note": ""}, {"id": 17, "message": "How can you clear all index filters on a specific collection in MongoDB?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["mongodb~7.0.9"], "answer": "By running the `planCacheClearFilters` command with the collection name, you can clear all index filters on that collection.", "context": ["planCacheClearFilters\n# planCacheClearFilters.css-134mg1q{-webkit-align-self:center;-ms-flex-item-align:center;align-self:center;padding:0 10px;visibility:hidden;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}.css-6vrlzm{border-radius:0!important;display:initial!important;margin:initial!important;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}.css-1l4s55v{margin-top:-175px;position:absolute;padding-bottom:2px;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}\n## Examples\n\n### Clear all Index Filters on a Collection\n\nThe following example clears all index filters on the `orders` collection:\n\n``` leafygreen-ui-vbfbru\ndb.runCommand(\n   {\n      planCacheClearFilters: \"orders\"\n   }\n)\n```"], "note": ""}, {"id": 18, "message": "Which methods allow specifying the `upsert` option when using the `$setOnInsert` update operator in MongoDB?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["mongodb~7.0.9"], "answer": "You can specify the `upsert` option with `db.collection.updateOne()`, `db.collection.updateMany()`, and `db.collection.findAndModify()` methods when using `$setOnInsert`.", "context": ["$setOnInsert\n# $setOnInsert.css-134mg1q{-webkit-align-self:center;-ms-flex-item-align:center;align-self:center;padding:0 10px;visibility:hidden;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}.css-6vrlzm{border-radius:0!important;display:initial!important;margin:initial!important;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}.css-1l4s55v{margin-top:-175px;position:absolute;padding-bottom:2px;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}\n## Definition\n!important}`$setOnInsert`.css-1ylug12{margin-top:-150px;position:absolute;padding-bottom:2px;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}\n\nYou can specify the `upsert` option for:\n\n.leafygreen-ui-rioki0{margin-top:0px;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}\n\n- `db.collection.updateOne()`\n\n- `db.collection.updateMany()`\n\n- `db.collection.findAndModify()`\n\n.css-9g5lg9{display:table;margin:24px 0;min-width:150px;table-layout:fixed;width:100%;}.css-9g5lg9 button>div>div{font-size:13px;}.css-9g5lg9 >div>div{display:grid;grid-template-columns:code panel;}.css-9g5lg9"], "note": ""}, {"id": 19, "message": "In MongoDB, how can you calculate rolling statistics for time series data using the `$percentile` operator?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["mongodb~7.0.9"], "answer": "By using the `$percentile` operator within a `$setWindowFields` stage in an aggregation pipeline, you can calculate rolling statistics for time series data.", "context": ["$percentile\n# $percentile (aggregation).css-134mg1q{-webkit-align-self:center;-ms-flex-item-align:center;align-self:center;padding:0 10px;visibility:hidden;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}.css-6vrlzm{border-radius:0!important;display:initial!important;margin:initial!important;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}.css-1l4s55v{margin-top:-175px;position:absolute;padding-bottom:2px;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}\n## Behavior\n\n### Window Functions\n\nA window function lets you calculate results over a moving \"window\" of neighboring documents. As each document passes though the pipeline, the `$setWindowFields` stage:\n\n- Recomputes the set of documents in the current window\n\n- calculates a value for all documents in the set\n\n- returns a single value for that document\n\nYou can use `$percentile` in a `$setWindowFields` stage to calculate rolling statistics for time series or other related data.\n\nWhen you use `$percentile` in a `$setWindowField` stage, the `input` value must be a field name. If you enter an array instead of a field name, the operation fails."], "note": ""}, {"id": 20, "message": "What is the purpose of the `cursor.returnKey()` method in MongoDB, and how does it affect the query results?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["mongodb~7.0.9"], "answer": "The `cursor.returnKey()` method modifies the query so that it returns only the index keys used to execute the query, instead of the full documents.", "context": ["cursor.returnKey()\n# cursor.returnKey().css-134mg1q{-webkit-align-self:center;-ms-flex-item-align:center;align-self:center;padding:0 10px;visibility:hidden;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}.css-6vrlzm{border-radius:0!important;display:initial!important;margin:initial!important;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}.css-1l4s55v{margin-top:-175px;position:absolute;padding-bottom:2px;}[class*=side-nav], #side-nav-null, header, #gatsby-announcer, body:after, .headerlink, footer, .main-column ~ div {display:none !important} .main-column {max-width: none !important} pre td {white-space:pre !important}\n## Example\n\nThe collection has two indexes in addition to the default `_id` index:\n\n``` leafygreen-ui-vbfbru\n{\n   \"v\" : 1,\n   \"key\" : {\n      \"_id\" : 1\n   },\n   \"name\" : \"_id_\",\n   \"ns\" : \"guidebook.restaurant\"\n},\n{\n   \"v\" : 1,\n   \"key\" : {\n      \"cuisine\" : 1\n   },\n   \"name\" : \"cuisine_1\",\n   \"ns\" : \"guidebook.restaurant\"\n},\n{\n   \"v\" : 1,\n   \"key\" : {\n      \"_fts\" : \"text\",\n      \"_ftsx\" : 1\n   },\n   \"name\" : \"name_text\",\n   \"ns\" : \"guidebook.restaurant\",\n   \"weights\" : {\n      \"name\" : 1\n   },\n   \"default_language\" : \"english\",\n   \"language_override\" : \"language\",\n   \"textIndexVersion\" : 3\n}\n```\n\nThe following code uses the `cursor.returnKey()` method to return only the indexed fields used for executing the query:\n\n``` leafygreen-ui-vbfbru\nvar csr = db.restaurant.find( { \"cuisine\" : \"Japanese\" } )\ncsr.returnKey()\n```\n\nThis returns the following:\n\n``` leafygreen-ui-vbfbru\n{ \"cuisine\" : \"Japanese\" }\n{ \"cuisine\" : \"Japanese\" }\n{ \"cuisine\" : \"Japanese\" }\n{ \"cuisine\" : \"Japanese\" }\n...\n```"], "note": ""}, {"id": 21, "message": "How can I generate both LTR and RTL versions of my Bootstrap project from the same codebase, and what tool does Bootstrap use to handle the RTL conversion?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bootstrap~5"], "answer": "By using the RTLCSS project, you can generate both LTR and RTL versions from the same Bootstrap codebase. Wrap your `@import`s with a class and set a custom rename rule for RTLCSS, which will prepend selectors with `.ltr` and `.rtl`, allowing you to use both versions on the same page.", "context": ["Our approach to building RTL support into Bootstrap comes with two important decisions that impact how we write and use our CSS:\n\n1.  **First, we decided to build it with the RTLCSS project.** This gives us some powerful features for managing changes and overrides when moving from LTR to RTL. It also allows us to build two versions of Bootstrap from one codebase.\n\n2.  **Second, we’ve renamed a handful of directional classes to adopt a logical properties approach.** Most of you have already interacted with logical properties thanks to our flex utilities—they replace direction properties like `left` and `right` in favor `start` and `end`. That makes the class names and values appropriate for LTR and RTL without any overhead.\n\nFor example, instead of `.ml-3` for `margin-left`, use `.ms-3`.\n\nWorking with RTL, through our source Sass or compiled CSS, shouldn’t be much different from our default LTR though.", "### LTR and RTL at the same time\n\nNeed both LTR and RTL on the same page? Thanks to RTLCSS String Maps, this is pretty straightforward. Wrap your `@import`s with a class, and set a custom rename rule for RTLCSS:\n\n``` highlight\n/* rtl:begin:options: {\n  \"autoRename\": true,\n  \"stringMap\":[ {\n    \"name\": \"ltr-rtl\",\n    \"priority\": 100,\n    \"search\": [\"ltr\"],\n    \"replace\": [\"rtl\"],\n    \"options\": {\n      \"scope\": \"*\",\n      \"ignoreCase\": false\n    }\n  } ]\n} */\n.ltr {\n  @import \"../node_modules/bootstrap/scss/bootstrap\";\n}\n/*rtl:end:options*/\n```\n\nAfter running Sass then RTLCSS, each selector in your CSS files will be prepended by `.ltr`, and `.rtl` for RTL files. Now you’re able to use both files on the same page, and simply use `.ltr` or `.rtl` on your components wrappers to use one or the other direction."], "note": ""}, {"id": 22, "message": "How can I create a custom button size in Bootstrap using Sass mixins?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bootstrap~5"], "answer": "You can use the `@mixin button-size($padding-y, $padding-x, $font-size, $border-radius)` mixin to create a custom button size by specifying custom values for padding, font size, and border radius in your Sass code.", "context": ["@mixin button-size($padding-y, $padding-x, $font-size, $border-radius) {\n  --#{$prefix}btn-padding-y: #{$padding-y};\n  --#{$prefix}btn-padding-x: #{$padding-x};\n  @include rfs($font-size, --#{$prefix}btn-font-size);\n  --#{$prefix}btn-border-radius: #{$border-radius};\n}"], "note": ""}, {"id": 23, "message": "How can I customize the appearance of a `<select>` element in Bootstrap using Sass variables, and what limitations exist when using floating labels with selects?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bootstrap~5"], "answer": "You can customize the appearance of a `<select>` element by adjusting Sass variables like `$form-select-padding-y`, `$form-select-font-size`, and others. When using floating labels with selects, note that they always display the label in its floated state, and selects with `size` and `multiple` attributes are not supported.", "context": ["Other than `.form-control`, floating labels are only available on `.form-select`s. They work in the same way, but unlike `<input>`s, they’ll always show the `<label>` in its floated state. **Selects with `size` and `multiple` are not supported.**\n\n``` highlight\n<div class=\"form-floating\">\n  <select class=\"form-select\" id=\"floatingSelect\" aria-label=\"Floating label select example\">\n    <option selected>Open this select menu</option>\n    <option value=\"1\">One</option>\n    <option value=\"2\">Two</option>\n    <option value=\"3\">Three</option>\n  </select>\n  <label for=\"floatingSelect\">Works with selects</label>\n</div>\n```\n", "### Sass variables\n``` highlight\n$form-select-padding-y:             $input-padding-y;\n$form-select-padding-x:             $input-padding-x;\n$form-select-font-family:           $input-font-family;\n$form-select-font-size:             $input-font-size;\n$form-select-indicator-padding:     $form-select-padding-x * 3; // Extra padding for background-image\n$form-select-font-weight:           $input-font-weight;\n$form-select-line-height:           $input-line-height;\n$form-select-color:                 $input-color;\n$form-select-bg:                    $input-bg;\n$form-select-disabled-color:        null;\n$form-select-disabled-bg:           $input-disabled-bg;\n$form-select-disabled-border-color: $input-disabled-border-color;\n$form-select-bg-position:           right $form-select-padding-x center;\n$form-select-bg-size:               16px 12px; // In pixels because image dimensions\n$form-select-indicator-color:       $gray-800;\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>\");\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding;\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding;\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half;\n\n$form-select-border-width:        $input-border-width;\n$form-select-border-color:        $input-border-color;\n$form-select-border-radius:       $input-border-radius;\n$form-select-box-shadow:          $box-shadow-inset;\n\n$form-select-focus-border-color:  $input-focus-border-color;\n$form-select-focus-width:         $input-focus-width;\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color;\n\n$form-select-padding-y-sm:        $input-padding-y-sm;\n```", "For custom Bootstrap form validation messages, you’ll need to add the `novalidate` boolean attribute to your `<form>`. This disables the browser default feedback tooltips, but still provides access to the form validation APIs in JavaScript. Try to submit the form below; our JavaScript will intercept the submit button and relay feedback to you. When attempting to submit, you’ll see the `:invalid` and `:valid` styles applied to your form controls.\n\nCustom feedback styles apply custom colors, borders, focus styles, and background icons to better communicate feedback. Background icons for `<select>`s are only available with `.form-select`, and not `.form-control`."], "note": ""}, {"id": 24, "message": "Regarding the following source code:\n```\nconst toastTrigger = document.getElementById('liveToastBtn')\nconst toastLiveExample = document.getElementById('liveToast')\n\nif (toastTrigger) {\n  toastTrigger.addEventListener('click', () => {\n    toastLiveExample.show()\n  })\n}\n```\nGiven the above code snippet, why does the toast not appear when clicking the button, and how can I fix it?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bootstrap~5"], "answer": "The toast does not appear because `toastLiveExample.show()` is not a valid method; you need to create a Bootstrap Toast instance and call `show()` on that instance. You can fix it by creating a Toast instance using `bootstrap.Toast.getOrCreateInstance(toastLiveExample)` and then calling `show()` on it.", "context": ["We use the following JavaScript to trigger our live toast demo:\n\n``` highlight\nconst toastTrigger = document.getElementById('liveToastBtn')\nconst toastLiveExample = document.getElementById('liveToast')\n\nif (toastTrigger) {\n  const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)\n  toastTrigger.addEventListener('click', () => {\n    toastBootstrap.show()\n  })\n}\n```\n"], "note": ""}, {"id": 25, "message": "How can I change the opacity of a background color utility class in Bootstrap, and what are two methods to achieve this?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["bootstrap~5"], "answer": "You can change the opacity of a background color utility class by either overriding the `--bs-bg-opacity` CSS variable via inline or custom styles, or by using the predefined `.bg-opacity-*` utility classes provided by Bootstrap.", "context": ["As of v5.1.0, `background-color` utilities are generated with Sass using CSS variables. This allows for real-time color changes without compilation and dynamic alpha transparency changes.\n\n### How it works\n\nConsider our default `.bg-success` utility.\n\n``` highlight\n.bg-success {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important;\n}\n```\n\nWe use an RGB version of our `--bs-success` (with the value of `25, 135, 84`) CSS variable and attached a second CSS variable, `--bs-bg-opacity`, for the alpha transparency (with a default value `1` thanks to a local CSS variable). That means anytime you use `.bg-success` now, your computed `color` value is `rgba(25, 135, 84, 1)`. The local CSS variable inside each `.bg-*` class avoids inheritance issues so nested instances of the utilities don’t automatically have a modified alpha transparency.\n\n### Example\n\nTo change that opacity, override `--bs-bg-opacity` via custom styles or inline styles.\n\n<div class=\"bd-example-snippet bd-code-snippet\">\n\n``` highlight\n<div class=\"bg-success p-2 text-white\">This is default success background</div>\n<div class=\"bg-success p-2\" style=\"--bs-bg-opacity: .5;\">This is 50% opacity success background</div>\n```\n\n</div>\n\nOr, choose from any of the `.bg-opacity` utilities:\n\n<div class=\"bd-example-snippet bd-code-snippet\">\n\n``` highlight\n<div class=\"bg-success p-2 text-white\">This is default success background</div>\n<div class=\"bg-success p-2 text-white bg-opacity-75\">This is 75% opacity success background</div>\n<div class=\"bg-success p-2 text-dark bg-opacity-50\">This is 50% opacity success background</div>\n<div class=\"bg-success p-2 text-dark bg-opacity-25\">This is 25% opacity success background</div>\n<div class=\"bg-success p-2 text-dark bg-opacity-10\">This is 10% opacity success background</div>\n```\n\n</div>"], "note": ""}, {"id": 26, "message": "When using the `timeout` function in actix-web, under what circumstances can it panic, and how can you ensure it does not?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["actix~4.8.0"], "answer": "The `timeout` function can panic if there is no current timer set. This occurs when `Builder::enable_time` or `Builder::enable_all` are not included in the Tokio runtime builder, or if the timer is created outside of a Tokio runtime. To prevent the panic, ensure that you are running within a Tokio runtime with time enabled.", "context": ["fn.timeout\n# Function actix_web::rt::time::timeout\nRequires a `Future` to complete before the specified duration has\nelapsed.\n\nIf the future completes before the duration has elapsed, then the\ncompleted value is returned. Otherwise, an error is returned and the\nfuture is canceled.\n\nNote that the timeout is checked before polling the future, so if the\nfuture does not yield during execution then it is possible for the\nfuture to complete and exceed the timeout *without* returning an error.\n\nThis function returns a future whose return type is\n`Result``<T,``Elapsed``>`, where `T` is the return type of the provided\nfuture.\n\nIf the provided future completes immediately, then the future returned\nfrom this function is guaranteed to complete immediately with an `Ok`\nvariant no matter the provided duration.\n\n## §Cancellation\n\nCancelling a timeout is done by dropping the future. No additional\ncleanup or other work is required.\n\nThe original future may be obtained by calling `Timeout::into_inner`.\nThis consumes the `Timeout`.\n\n## §Examples\n\nCreate a new `Timeout` set to expire in 10 milliseconds.\n\n``` rust\nuse tokio::time::timeout;\nuse tokio::sync::oneshot;\n\nuse std::time::Duration;\n\nlet (tx, rx) = oneshot::channel();\n\n// Wrap the future with a `Timeout` set to expire in 10 milliseconds.\nif let Err(_) = timeout(Duration::from_millis(10), rx).await {\n    println!(\"did not receive value within 10 ms\");\n}\n```\n\n## §Panics\n\nThis function panics if there is no current timer set.\n\nIt can be triggered when `Builder::enable_time` or `Builder::enable_all`\nare not included in the builder.\n\nIt can also panic whenever a timer is created outside of a Tokio\nruntime. That is why `rt.block_on(sleep(...))` will panic, since the\nfunction is executed outside of the runtime. Whereas\n`rt.block_on(async {sleep(...).await})` doesn’t panic. And this is\nbecause wrapping the function on an async makes it lazy, and so gets\nexecuted inside the runtime successfully without panicking."], "note": ""}, {"id": 27, "message": "Regarding the following source code:\n```\n```rust\nuse tokio::net::TcpStream;\nuse std::error::Error;\nuse std::io;\n\n#[tokio::main]\nasync fn main() -> Result<(), Box<dyn Error>> {\n    // Connect to a peer\n    let stream = TcpStream::connect(\"127.0.0.1:8080\").await?;\n\n    loop {\n        // Wait for the socket to be writable\n        stream.writable().await?;\n\n        // Try to write data, this may still fail with `WouldBlock`\n        // if the readiness event is a false positive.\n        match stream.try_write(b\"hello world\") {\n            Ok(n) => {\n                break;\n            }\n            Err(ref e) if e.kind() == io::ErrorKind::WouldBlock => {\n                continue;\n            }\n            Err(e) => {\n                return Err(e.into());\n            }\n        }\n    }\n\n    Ok(())\n}\n```\n```\nHow can you send data over a TcpStream in actix-web by using the writable and try_write methods, and how should you handle potential WouldBlock errors?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["actix~4.8.0"], "answer": "You can use `stream.writable().await?` to wait until the `TcpStream` is writable. Then, use `stream.try_write(data)` to attempt to send data. If `try_write` returns an error with `io::Error<PERSON>ind::WouldBlock`, you should handle it by continuing the loop and waiting again, as the readiness event might be a false positive. Other errors should be handled appropriately.", "context": ["struct.TcpStream\n# Struct actix_web::rt::net::TcpStream\n## Implementations\n<div id=\"method.writable\" class=\"section method\">\n\n#### pub async fn writable(&self) -> Result<(), Error>\n\n</div>\n\n<div class=\"docblock\">\n\nWaits for the socket to become writable.\n\nThis function is equivalent to `ready(Interest::WRITABLE)` and is\nusually paired with `try_write()`.\n\n##### §Cancel safety\n\nThis method is cancel safe. Once a readiness event occurs, the method\nwill continue to return immediately until the readiness event is\nconsumed by an attempt to write that fails with `WouldBlock` or\n`Poll::Pending`.\n\n##### §Examples\n\n``` rust\nuse tokio::net::TcpStream;\nuse std::error::Error;\nuse std::io;\n\n#[tokio::main]\nasync fn main() -> Result<(), Box<dyn Error>> {\n    // Connect to a peer\n    let stream = TcpStream::connect(\"127.0.0.1:8080\").await?;\n\n    loop {\n        // Wait for the socket to be writable\n        stream.writable().await?;\n\n        // Try to write data, this may still fail with `WouldBlock`\n        // if the readiness event is a false positive.\n        match stream.try_write(b\"hello world\") {\n            Ok(n) => {\n                break;\n            }\n            Err(ref e) if e.kind() == io::ErrorKind::WouldBlock => {\n                continue;\n            }\n            Err(e) => {\n                return Err(e.into());\n            }\n        }\n    }\n\n    Ok(())\n}\n```\n\n</div>", "struct.TcpStream\n# Struct actix_web::rt::net::TcpStream\n## Implementations\n<div id=\"method.poll_peek\" class=\"section method\">\n\n#### pub fn poll_peek( &self, cx: &mut Context<'_>, buf: &mut ReadBuf<'_>, ) -> Poll<Result<usize, Error>>\n\n</div>\n\n<div class=\"docblock\">\n\nAttempts to receive data on the socket, without removing that data from\nthe queue, registering the current task for wakeup if data is not yet\navailable.\n\nNote that on multiple calls to `poll_peek`, `poll_read` or\n`poll_read_ready`, only the `Waker` from the `Context` passed to the\nmost recent call is scheduled to receive a wakeup. (However,\n`poll_write` retains a second, independent waker.)\n\n##### §Return value\n\nThe function returns:\n\n- `Poll::Pending` if data is not yet available.\n- `Poll::Ready(Ok(n))` if data is available. `n` is the number of bytes\n  peeked.\n- `Poll::Ready(Err(e))` if an error is encountered.\n\n##### §Errors\n\nThis function may encounter any standard I/O error except `WouldBlock`.\n\n##### §Examples\n\n``` rust\nuse tokio::io::{self, ReadBuf};\nuse tokio::net::TcpStream;\n\nuse futures::future::poll_fn;\n\n#[tokio::main]\nasync fn main() -> io::Result<()> {\n    let stream = TcpStream::connect(\"127.0.0.1:8000\").await?;\n    let mut buf = [0; 10];\n    let mut buf = ReadBuf::new(&mut buf);\n\n    poll_fn(|cx| {\n        stream.poll_peek(cx, &mut buf)\n    }).await?;\n\n    Ok(())\n}\n```\n\n</div>\n\n\npub async fn ready(&self, interest: Interest) -> Result<Ready, Error>"], "note": ""}, {"id": 28, "message": "How does the tail segment `{name}*` in actix-web's `ResourceDef` differ from a standard dynamic segment, and when would you use it?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["actix~4.8.0"], "answer": "The tail segment `{name}*` captures all remaining parts of the URL path, including slashes, unlike a standard dynamic segment which stops at the next `/`. You would use it when you need to match and capture the rest of the path beyond a certain point, such as file paths or nested routes.", "context": ["struct.ResourceDef\n# Struct actix_web::dev::ResourceDef\n## §Custom Regex Segments\n\nDynamic segments can be customised to only match a specific regular\nexpression. It can be helpful to do this if resource definitions would\notherwise conflict and cause one to be inaccessible.\n\nThe regex used when capturing segment values can be specified explicitly\nusing this syntax: `{name:regex}`. For example, `/user/{id:\\d+}` will\nonly match paths where the user ID is numeric.\n\nThe regex could potentially match multiple segments. If this is not\nwanted, then care must be taken to avoid matching a slash `/`. It is\nguaranteed, however, that the match ends at a segment boundary; the\npattern `r\"(/|$)` is always appended to the regex.\n\nBy default, dynamic segments use this regex: `[^/]+`. This shows why it\nis the case, as shown in the earlier section, that segments capture a\nslice of the path up to the next `/` character.\n\nCustom regex segments can be used in static and prefix resource\ndefinition variants.\n\n### §Examples\n\n``` rust\nlet resource = ResourceDef::new(r\"/user/{id:\\d+}\");\nassert!(resource.is_match(\"/user/123\"));\nassert!(resource.is_match(\"/user/314159\"));\nassert!(!resource.is_match(\"/user/abc\"));\n```\n\n\n## §Tail Segments\n\nAs a shortcut to defining a custom regex for matching *all* remaining\ncharacters (not just those up until a `/` character), there is a special\npattern to match (and capture) the remaining path portion.\n\nTo do this, use the segment pattern: `{name}*`. Since a tail segment\nalso has a name, values are extracted in the same way as non-tail\ndynamic segments.\n\n### §Examples\n\n``` rust\nlet resource = ResourceDef::new(\"/blob/{tail}*\");\nassert!(resource.is_match(\"/blob/HEAD/Cargo.toml\"));\nassert!(resource.is_match(\"/blob/HEAD/README.md\"));\n\nlet mut path = Path::new(\"/blob/main/LICENSE\");\nresource.capture_match_info(&mut path);\nassert_eq!(path.get(\"tail\").unwrap(), \"main/LICENSE\");\n```"], "note": ""}, {"id": 29, "message": "Regarding the following source code:\n```\n```rust\nuse bytes::Buf;\n\nlet mut buf: &[u8] = match cfg!(target_endian = \"big\") {\n    true => b\"\\x3F\\x99\\x99\\x9A hello\",\n    false => b\"\\x9A\\x99\\x99\\x3F hello\",\n};\nassert_eq!(1.2f32, buf.get_f32_ne());\n```\n```\nHow do you use the `get_f32_ne` method from the `Buf` trait in actix-web to read a 32-bit floating-point number, and what precautions should you take?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["actix~4.8.0"], "answer": "You can use `get_f32_ne` to read a 32-bit floating-point number from a buffer in native-endian order. It advances the buffer's position by 4 bytes. You should ensure that the buffer has at least 4 bytes remaining before calling this method to avoid a panic.", "context": ["trait.Buf\n# Trait actix_web::web::Buf\n## Provided Methods\n<div id=\"method.get_f32_ne\" class=\"section method\">\n\n#### fn get_f32_ne(&mut self) -> f32\n\n</div>\n\n<div class=\"docblock\">\n\nGets an IEEE754 single-precision (4 bytes) floating point number from\n`self` in native-endian byte order.\n\nThe current position is advanced by 4.\n\n##### §Examples\n\n``` rust\nuse bytes::Buf;\n\nlet mut buf: &[u8] = match cfg!(target_endian = \"big\") {\n    true => b\"\\x3F\\x99\\x99\\x9A hello\",\n    false => b\"\\x9A\\x99\\x99\\x3F hello\",\n};\nassert_eq!(1.2f32, buf.get_f32_ne());\n```\n\n##### §Panics\n\nThis function panics if there is not enough remaining data in `self`.\n\n</div>"], "note": ""}, {"id": 30, "message": "How can you prevent a page from being embedded in an iframe using actix-web, and which header constant should you use?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["actix~4.8.0"], "answer": "You can prevent a page from being embedded in an iframe by setting the `X_FRAME_OPTIONS` header in your response. This header indicates to browsers that the page should not be framed, helping to protect against clickjacking attacks.", "context": ["# Constant actix_web::http::header::X_FRAME_OPTIONS\n``` rust\npub const X_FRAME_OPTIONS: HeaderName;\n```\n\n<div class=\"docblock\">\n\nIndicates whether or not a browser should be allowed to render a page in\na frame.\n\nSites can use this to avoid clickjacking attacks, by ensuring that their\ncontent is not embedded into other sites.\n\nThe added security is only provided if the user accessing the document\nis using a browser supporting `x-frame-options`.\n\n</div>"], "note": ""}, {"id": 31, "message": "What are the differences between `KeepAlive::Timeout`, `KeepAlive::Os`, and `KeepAlive::Disabled` in actix-web, and how do they affect TCP connections?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["actix~4.8.0"], "answer": "`KeepAlive::Timeout(Duration)` sets a specific keep-alive timeout duration for connections. `KeepAlive::Os` relies on the operating system's default timeout to close idle TCP connections, which may be very long. `KeepAlive::Disabled` disables keep-alive, causing connections to close immediately after a response.", "context": ["enum.KeepAlive\n# Enum actix_web::http::KeepAlive\n``` rust\npub enum KeepAlive {\n    Timeout(Duration),\n    Os,\n    Disabled,\n}\n```\n\n<div class=\"docblock\">\n\nConnection keep-alive config.\n\n</div>\n\n\n## Variants\n\n<div id=\"variant.Timeout\" class=\"section variant\">\n\n### Timeout(Duration)\n\n</div>\n\n<div class=\"docblock\">\n\nKeep-alive duration.\n\n`KeepAlive::Timeout(Duration::ZERO)` is mapped to `KeepAlive::Disabled`.\n\n</div>\n\n<div id=\"variant.Os\" class=\"section variant\">\n\n### Os\n\n</div>\n\n<div class=\"docblock\">\n\nRely on OS to shutdown TCP connection.\n\nSome defaults can be very long, check your OS documentation.\n\n</div>\n\n<div id=\"variant.Disabled\" class=\"section variant\">\n\n### Disabled\n\n</div>\n\n<div class=\"docblock\">\n\nKeep-alive is disabled.\n\nConnections will be closed immediately.\n\n</div>"], "note": ""}, {"id": 32, "message": "Regarding the following source code:\n```\n$('.toggle.button')\n  .api({\n    loadingDuration: 500,\n    mockResponse: { success: true }\n  })\n  .state({\n    text: {\n      inactive : 'Off',\n      active : 'On'\n    }\n  });\n```\nGiven the above code, how can you simulate a delayed successful response using Semantic UI's API module?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["semantic_ui~2.5.0"], "answer": "You can simulate a delayed successful response by using the 'mockResponse' and 'loadingDuration' settings in the API module.", "context": ["New in 2.4\n<div class=\"mock example\">\n\nAPI now supports mocked responses, letting you specify how responses are\nreturned in advance.\n\n\\$('.toggle.button')\n  .api({\n    // lets pretend this took a while\n    loadingDuration: 500,\n    // lets treat this button as requesting this JSON\n    mockResponse: { success: true }\n  })\n  // successful responses will trigger a text state change\n  .state({\n    text: {\n      inactive : 'Off',\n      active : 'On'\n    }\n  });\n\n</div>"], "note": ""}, {"id": 33, "message": "Under what governing law is the Semantic UI Contributor License Agreement, and does the UN Convention on Contracts for the International Sale of Goods apply?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["semantic_ui~2.5.0"], "answer": "The agreement is governed by the laws of the United States, and the UN Convention is entirely excluded from applying to it.", "context": ["Contributor License Agreement\n# Semantic UI Individual Contributor License Agreement\n### 2.3 Outbound License\n6.1 This Agreement will be governed by and construed in accordance with\nthe laws of United States of America excluding its conflicts of law\nprovisions. Under certain circumstances, the governing law in this\nsection might be superseded by the United Nations Convention on\nContracts for the International Sale of Goods (\"UN Convention\") and the\nparties intend to avoid the application of the UN Convention to this\nAgreement and, thus, exclude the application of the UN Convention in its\nentirety to this Agreement."], "note": ""}, {"id": 34, "message": "Regarding the following source code:\n```\n$('.overlay')\n  .visibility({\n    type : 'fixed',\n    offset : 15\n  });\n```\nHow can you make an element stick to the top of the viewport with a 15px offset using Semantic UI, ensuring that other content does not shift when it becomes fixed?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["semantic_ui~2.5.0"], "answer": "By using the Visibility API with 'type' set to 'fixed' and an 'offset' of 15, Semantic UI will automatically make the element stick to the top with a placeholder to prevent content shifting.", "context": ["New in 2.4\n<div class=\"visibility example\">\n\nVisibility APIs now include useful shortcuts for sticky content. A\nplaceholder will automatically be added when an element is passed making\nsure other content does not shift position.\n\nAdditionally visibility and sticky have been rewritten to use a pub/sub\npattern which is much more performant than `1.0`\n\n\\$('.overlay')\n  .visibility({\n    type : 'fixed',\n    offset : 15\n    // give some space from top of screen\n  });\n.overlay {\n  background-color: \\#FFFFFF;\n  padding: 0.5em;\n  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0);\n  transition: all 0.5s ease;\n  background: transparent;\n}\n/* change style */\n.fixed.overlay {\n  position: fixed;\n  padding: 1em;\n  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.2);\n}\n\n</div>"], "note": ""}, {"id": 35, "message": "Regarding the following source code:\n```\n$('.length.example form')\n  .form({\n    on: 'blur',\n    fields: {\n      exactLength: {\n        identifier : 'exactLength',\n        rules: [\n          {\n            type : 'exactLength[6]',\n            prompt : 'Please enter exactly 6 characters'\n          }\n        ]\n      }\n    }\n  });\n```\nHow can you use Semantic UI's form validation to ensure an input field contains exactly six characters?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["semantic_ui~2.5.0"], "answer": "By using the 'exactLength[6]' validation rule in the form's field definition for that input.", "context": ["Form Validation\n<div class=\"length example\">\n\nInputs can match against length of content\n\n\\$('.length.example form')\n  .form({\n    on: 'blur',\n    fields: {\n      minLength: {\n        identifier : 'minLength',\n        rules: [\n          {\n            type : 'minLength[100]',\n            prompt : 'Please enter at least 100 characters'\n          }\n        ]\n      },\n      exactLength: {\n        identifier : 'exactLength',\n        rules: [\n          {\n            type : 'exactLength[6]',\n            prompt : 'Please enter exactly 6 characters'\n          }\n        ]\n      },\n      maxLength: {\n        identifier : 'maxLength',\n        rules: [\n          {\n            type : 'maxLength[100]',\n            prompt : 'Please enter at most 100 characters'\n          }\n        ]\n      },\n    }\n  });\n\n</div>"], "note": ""}, {"id": 36, "message": "Regarding the following source code:\n```\n$('.green.leaf')\n  .transition('horizontal flip', '500ms')\n  .transition('horizontal flip', 500, function() {\n    alert('done!');\n  });\n```\nHow can you queue multiple animations on an element so that transition directions are automatically determined in Semantic UI?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["semantic_ui~2.5.0"], "answer": "By chaining multiple .transition() calls without specifying directions, queued animations will automatically determine the transition direction.", "context": ["Transition\nUI Transitions provide a wrapper for using CSS transitions in Javascript\nproviding cross-browser callbacks, advanced queuing, and feature\ndetection.\n\nTransitions are loosely coupled with other ui modules like dropdowns and\nmodals to provide element transitions\n\nTransitions are separated into three categories. **Inward transitions**,\n**outward transitions**, and **static transitions**. These three\ncategories determine the visibility of the element after the animation\ncompletes.\n\nIf a transition is called without any arguments all default settings\nwill be used.\n\n\\$('.green.leaf')\n  // default everything\n  .transition();\n\nTransitions use similar argument shorthand as animate. You can specify\ncallback functions, animation duration, and other settings using the\nsame arguments. Durations can be specified as strings with CSS\nshorthand, or with numbers.\n\n\\$('.green.leaf')\n  .transition({\n    animation : 'scale',\n    duration : '2s',\n    onComplete : function() {\n      alert('done');\n    }\n  });\n\nAnimations can be used on any display type not just block level\nelements. For example you can animate a button while preserving its\n`inline-block` status.\n\n\\$('.test.button')\n  .transition('horizontal flip', '500ms');\n\nAnimations called in sequence will be queued. Any queued animation will\nautomatically determine the transition direction if none is specified.\n\n\\$('.green.leaf')\n  .transition('horizontal flip', '500ms')\n  .transition('horizontal flip', 500, function() {\n    alert('done!');\n  });\n\nAnimations can be stopped using three methods. `stop` will end the\ncurrent animation, `stop all` will end animation and remove queued\nanimations, and `clear queue` will continue the current playback but\nremove queued animations."], "note": ""}, {"id": 37, "message": "Regarding the following source code:\n```\n\nconst { EventEmitter, once } = require('node:events');\n\nconst myEE = new EventEmitter();\n\nasync function foo() {\n  await once(myEE, 'bar');\n  console.log('bar');\n\n  // This Promise will never resolve because the 'foo' event will\n  // have already been emitted before the Promise is created.\n  await once(myEE, 'foo');\n  console.log('foo');\n}\n\nprocess.nextTick(() => {\n  myEE.emit('bar');\n  myEE.emit('foo');\n});\n\nfoo().then(() => console.log('done'));\n\n```\nWhy does the above code never log 'foo' or 'done', and how can you modify it so that it works as intended?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["node~18_lts"], "answer": "Because the 'foo' event is emitted before the 'once' listener for 'foo' is set up, so the Promise returned by 'once' never resolves, causing the code to hang. To fix it, you need to ensure that the 'foo' event is emitted after the listener is set up, for example, by moving the event emission inside a `setImmediate` callback or after the `foo()` function is called.", "context": ["events\n## Events\n### `events.once(emitter, name[, options])`\n#### Awaiting multiple events emitted on `process.nextTick()`\nThere is an edge case worth noting when using the `events.once()`\nfunction to await multiple events emitted on in the same batch of\n`process.nextTick()` operations, or whenever multiple events are emitted\nsynchronously. Specifically, because the `process.nextTick()` queue is\ndrained before the `Promise` microtask queue, and because `EventEmitter`\nemits all events synchronously, it is possible for `events.once()` to\nmiss an event.\n\nMJS modules\n\n```\nimport { EventEmitter, once } from 'node:events';\nimport process from 'node:process';\n\nconst myEE = new EventEmitter();\n\nasync function foo() {\n  await once(myEE, 'bar');\n  console.log('bar');\n\n  // This Promise will never resolve because the 'foo' event will\n  // have already been emitted before the Promise is created.\n  await once(myEE, 'foo');\n  console.log('foo');\n}\n\nprocess.nextTick(() => {\n  myEE.emit('bar');\n  myEE.emit('foo');\n});\n\nfoo().then(() => console.log('done'));\n```\n"], "note": ""}, {"id": 38, "message": "When using Node-API to create a buffer in native code, which function should you use, what does it return upon success, and why might you prefer using a `TypedArray` over a `node::Buffer`?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["node~18_lts"], "answer": "You should use `napi_create_buffer` to allocate a buffer object, which returns `napi_ok` upon success. However, it's generally recommended to use a `TypedArray` instead of a `node::Buffer` because `TypedArray` is sufficient in most cases and may offer better performance or compatibility.", "context": ["n-api\n## Node-API\n### Working with JavaScript values\n#### Object creation functions\n##### `napi_create_buffer`\n<div class=\"api_metadata\">\n\nAdded in: v8.0.0 N-API version: 1\n\n</div>\n\n```\nnapi_status napi_create_buffer(napi_env env,\n                               size_t size,\n                               void** data,\n                               napi_value* result) copy\n```\n\n- `[in] env`: The environment that the API is invoked under.\n- `[in] size`: Size in bytes of the underlying buffer.\n- `[out] data`: Raw pointer to the underlying buffer. `data` can\n  optionally be ignored by passing `NULL`.\n- `[out] result`: A `napi_value` representing a `node::Buffer`.\n\nReturns `napi_ok` if the API succeeded.\n\nThis API allocates a `node::<PERSON>uffer` object. While this is still a\nfully-supported data structure, in most cases using a `TypedArray` will\nsuffice.\n\n", "n-api\n## Node-API\n### Basic Node-API data types\n#### `napi_status`\n\n<div class=\"api_metadata\">\n\nAdded in: v8.0.0 N-API version: 1\n\n</div>\n\nIntegral status code indicating the success or failure of a Node-API\ncall. Currently, the following status codes are supported.\n\n```\ntypedef enum {\n  napi_ok,\n  napi_invalid_arg,\n  napi_object_expected,\n  napi_string_expected,\n  napi_name_expected,\n  napi_function_expected,\n  napi_number_expected,\n  napi_boolean_expected,\n  napi_array_expected,\n  napi_generic_failure,\n  napi_pending_exception,\n  napi_cancelled,\n  napi_escape_called_twice,\n  napi_handle_scope_mismatch,\n  napi_callback_scope_mismatch,\n  napi_queue_full,\n  napi_closing,\n  napi_bigint_expected,\n  napi_date_expected,\n  napi_arraybuffer_expected,\n  napi_detachable_arraybuffer_expected,\n  napi_would_deadlock,  /* unused */\n  napi_no_external_buffers_allowed,\n  napi_cannot_run_js\n} napi_status; copy\n```\n\nIf additional information is required upon an API returning a failed\nstatus, it can be obtained by calling `napi_get_last_error_info`.\n\n"], "note": ""}, {"id": 39, "message": "How can you collect V8 coverage data on demand in Node.js, and what must you do to allow V8 to release execution count records and optimize code afterward?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["node~18_lts"], "answer": "You can use `v8.takeCoverage()` to collect V8 coverage data on demand by writing it to disk. To allow V8 to release execution count records and optimize code afterward, you need to call `v8.stopCoverage()` to stop the coverage collection.", "context": ["v8\n## V8\n<div class=\"section\">\n\n### `v8.stopCoverage()`\n\n<div class=\"api_metadata\">\n\nAdded in: v15.1.0, v14.18.0, v12.22.0\n\n</div>\n\nThe `v8.stopCoverage()` method allows the user to stop the coverage\ncollection started by `NODE_V8_COVERAGE`, so that V8 can release the\nexecution count records and optimize code. This can be used in\nconjunction with `v8.takeCoverage()` if the user wants to collect the\ncoverage on demand.\n\n</div>\n\n<div class=\"section\">\n\n### `v8.takeCoverage()`\n\n<div class=\"api_metadata\">\n\nAdded in: v15.1.0, v14.18.0, v12.22.0\n\n</div>\n\nThe `v8.takeCoverage()` method allows the user to write the coverage\nstarted by `NODE_V8_COVERAGE` to disk on demand. This method can be\ninvoked multiple times during the lifetime of the process. Each time the\nexecution counter will be reset and a new coverage report will be\nwritten to the directory specified by `NODE_V8_COVERAGE`.\n\nWhen the process is about to exit, one last coverage will still be\nwritten to disk unless `v8.stopCoverage()` is invoked before the process\nexits.\n\n</div>\n\n"], "note": ""}, {"id": 40, "message": "In Node.js, how does the behavior of `Buffer.prototype.slice()` differ from `TypedArray.prototype.slice()`, and what method should you use to achieve consistent behavior across both `Buffer`s and other `TypedArray`s?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["node~18_lts"], "answer": "`Buffer.prototype.slice()` creates a view over the existing Buffer without copying the data, whereas `TypedArray.prototype.slice()` creates a copy of the data. To achieve consistent behavior across both Buffers and other TypedArrays, you should use `TypedArray.prototype.subarray()`, which behaves like `Buffer.prototype.slice()`.", "context": ["buffer\n## Buffer\n### Buffers and TypedArrays\n<div class=\"api_metadata\">\n\nHistory\n\n|         |                                                     |\n|---------|-----------------------------------------------------|\n| Version | Changes                                             |\n| v3.0.0  | The `<PERSON><PERSON><PERSON>`s class now inherits from `Uint8Array`. |\n\n</div>\n\n`Buffer` instances are also JavaScript `Uint8Array` and `TypedArray`\ninstances. All `TypedArray` methods are available on `Buffer`s. There\nare, however, subtle incompatibilities between the `Buffer` API and the\n`TypedArray` API.\n\nIn particular:\n\n- While `TypedArray.prototype.slice()` creates a copy of part of the\n  `TypedArray`, `Buffer.prototype.slice()` creates a view over the\n  existing `<PERSON>uffer` without copying. This behavior can be surprising,\n  and only exists for legacy compatibility.\n  `TypedArray.prototype.subarray()` can be used to achieve the behavior\n  of `Buffer.prototype.slice()` on both `<PERSON>uffer`s and other\n  `TypedArray`s and should be preferred.\n- `buf.toString()` is incompatible with its `TypedArray` equivalent.\n- A number of methods, e.g. `buf.indexOf()`, support additional\n  arguments.\n\n"], "note": ""}, {"id": 41, "message": "Regarding the following source code:\n```\n\nconst util = require('node:util');\nconst arr = Array(101).fill(0);\n\nconsole.log(arr); // Logs the truncated array\nutil.inspect.defaultOptions.maxArrayLength = null;\nconsole.log(arr); // Logs the full array\n\n```\nHow can you modify the default options used by `util.inspect` so that `console.log` prints the full content of large arrays instead of truncating them?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["node~18_lts"], "answer": "By setting `util.inspect.defaultOptions.maxArrayLength` to `null`, you modify the default options used by `util.inspect`, allowing `console.log` to print the full content of large arrays instead of truncating them.", "context": ["util\n## Util\n### `util.inspect(object[, showHidden[, depth[, colors]]])`\n\n#### `util.inspect.defaultOptions`\n\n<div class=\"api_metadata\">\n\nAdded in: v6.4.0\n\n</div>\n\nThe `defaultOptions` value allows customization of the default options\nused by `util.inspect`. This is useful for functions like `console.log`\nor `util.format` which implicitly call into `util.inspect`. It shall be\nset to an object containing one or more valid `util.inspect()` options.\nSetting option properties directly is also supported.\n\n```\nconst util = require('node:util');\nconst arr = Array(101).fill(0);\n\nconsole.log(arr); // Logs the truncated array\nutil.inspect.defaultOptions.maxArrayLength = null;\nconsole.log(arr); // logs the full array copy\n```\n"], "note": ""}, {"id": 42, "message": "How can you specify in a formula that the bottle should only be used when Homebrew is installed into the default prefix?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["homebrew"], "answer": "By adding `pour_bottle? only_if: :default_prefix` to the formula, which ensures the bottle is only used if Homebrew is installed in the default prefix.", "context": ["bottles\n# Bottles (Binary Packages)\n## Formula DSL\nAn additional bottle-related method is available in the formula DSL.\n\n### Pour bottle (`pour_bottle?`)\n\nOptionally returns a boolean to indicate whether a bottle should be used\nwhen installing this formula.\n\nFor example a bottle may break if a related formula has been compiled\nwith non-default options, so this method could check for that case and\nreturn `false`.\n\nA full example:\n\n```\npour_bottle? do\n  reason \"The bottle needs to be installed into #{Homebrew::DEFAULT_PREFIX}.\"\n  satisfy { HOMEBREW_PREFIX.to_s == Homebrew::DEFAULT_PREFIX }\nend\n```\n\nCommonly used `pour_bottle?` conditions can be added as preset symbols\nto the `pour_bottle?` method, allowing them to be specified like this:\n\n```\npour_bottle? only_if: :default_prefix\npour_bottle? only_if: :clt_installed\n```\n\n"], "note": ""}, {"id": 43, "message": "When writing a cask that installs a package using `pkg` or `installer manual:`, why is it necessary to include an `uninstall` stanza, and what key-value pairs can you use in it?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["homebrew"], "answer": "Because without an `uninstall` stanza, Homebrew will not know how to properly uninstall the package installed by `pkg` or `installer manual:`, leading to incomplete uninstalls. Key-value pairs that can be used in `uninstall` include `pkgutil:`, `launchctl:`, `script:`, and others.", "context": ["cask-cookbook\n# Cask Cookbook\n## Stanza descriptions\n### Stanza: `uninstall`\n#### `uninstall` Is Required for Casks That Install a pkg or installer manual\n\nFor most Casks, uninstall actions are determined automatically, and an\nexplicit `uninstall` stanza is not needed. However, a Cask which uses\nthe `pkg` or `installer manual:` stanzas will **not** know how to\nuninstall correctly unless an `uninstall` stanza is given.\n\nSo, while the Cask DSL does not enforce the requirement, it is much\nbetter for end-users if every `pkg` and `installer manual:` has a\ncorresponding `uninstall`.\n\nThe `uninstall` stanza is available for non-`pkg` Casks, and is useful\nfor a few corner cases. However, the documentation below concerns the\ntypical case of using `uninstall` to define procedures for a `pkg`.\n\n\n#### There Are Multiple Uninstall Techniques\n\nSince `pkg` installers can do arbitrary things, different techniques are\nneeded to uninstall in each case. You may need to specify one, or\nseveral, of the following key/value pairs as arguments to `uninstall`.\n\n\n\n"], "note": ""}, {"id": 44, "message": "How can you generate a Brewfile that includes all installed casks, formulae, images, and taps into the current directory?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["homebrew"], "answer": "By running `brew bundle dump`, which writes all installed casks, formulae, images, and taps into a `Brewfile` in the current directory.", "context": ["manpage\n# brew(1) – The Missing Package Manager for macOS (or Linux)\n## OFFICIAL EXTERNAL COMMANDS\n### `bundle` \\[*`subcommand`*\\]\nBundler for non-Ruby dependencies from Homebrew, Homebrew Cask, Mac App\nStore and Whalebrew.\n\n`brew bundle` \\[`install`\\] Install and upgrade (by default) all\ndependencies from the `Brewfile`.\n\nYou can specify the `Brewfile` location using `--file` or by setting the\n`HOMEBREW_BUNDLE_FILE` environment variable.\n\nYou can skip the installation of dependencies by adding space-separated\nvalues to one or more of the following environment variables:\n`HOMEBREW_BUNDLE_BREW_SKIP`, `HOMEBREW_BUNDLE_CASK_SKIP`,\n`HOMEBREW_BUNDLE_MAS_SKIP`, `HOMEBREW_BUNDLE_WHALEBREW_SKIP`,\n`HOMEBREW_BUNDLE_TAP_SKIP`.\n\n`brew bundle` will output a `Brewfile.lock.json` in the same directory\nas the `Brewfile` if all dependencies are installed successfully. This\ncontains dependency and system status information which can be useful in\ndebugging `brew bundle` failures and replicating a “last known good\nbuild” state. You can opt-out of this behaviour by setting the\n`HOMEBREW_BUNDLE_NO_LOCK` environment variable or passing the\n`--no-lock` option. You may wish to check this file into the same\nversion control system as your `Brewfile` (or ensure your version\ncontrol system ignores it if you’d prefer to rely on debugging\ninformation from a local machine).\n\n`brew bundle dump` Write all installed casks/formulae/images/taps into a\n`Brewfile` in the current directory.\n\n`brew bundle cleanup` Uninstall all dependencies not listed from the\n`Brewfile`.\n\nThis workflow is useful for maintainers or testers who regularly install\nlots of formulae.\n\n`brew bundle check` Check if all dependencies are installed from the\n`Brewfile`.\n\nThis provides a successful exit code if everything is up-to-date, making\nit useful for scripting.\n\n`brew bundle list` List all dependencies present in the `Brewfile`.\n\nBy default, only Homebrew dependencies are listed.\n\n"], "note": ""}, {"id": 45, "message": "Regarding the following source code:\n```\nsystem \"npm\", \"install\", *Language::Node.std_npm_install_args(libexec)\n```\nIn a Homebrew formula that installs a Node.js module globally, how can you install it into `libexec` and ensure the module's executables are available to users?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["homebrew"], "answer": "By using `system \"npm\", \"install\", *Language::Node.std_npm_install_args(libexec)` to install the module into `libexec`, and then symlinking the executables from `libexec/bin` to `bin` using `bin.install_symlink Dir[\"#{libexec}/bin/*\"]`, ensuring they are available to users.", "context": ["node-for-formula-authors\n# Node for Formula Authors\n## Installation\n### Installing global style modules with `std_npm_install_args` to `libexec`\n\nIn your formula’s `install` method, simply `cd` to the top level of your\nNode module if necessary and then use `system` to invoke `npm install`\nwith `Language::Node.std_npm_install_args` like:\n\n```\nsystem \"npm\", \"install\", *Language::Node.std_npm_install_args(libexec)\n```\n\nThis will install your Node module in npm’s global module style with a\ncustom prefix to `libexec`. All your modules’ executables will be\nautomatically resolved by npm into `libexec/bin` for you, which is not\nsymlinked into Homebrew’s prefix. We need to make sure these are\ninstalled. To do this we need to symlink all executables to `bin` with:\n\n```\nbin.install_symlink Dir[\"#{libexec}/bin/*\"]\n```\n\n"], "note": ""}, {"id": 46, "message": "Regarding the following source code:\n```\nversion \"1.2.3-beta,abcd1234\"\nurl \"https://example.com/download/#{version.before_comma.dots_to_hyphens}.zip\"\n```\nGiven a cask with version '1.2.3-beta,abcd1234', how can you transform the version string to replace dots with hyphens in the part before the comma for use in the URL?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["homebrew"], "answer": "By using `version.before_comma.dots_to_hyphens`, which converts the part before the comma ('1.2.3-beta') by replacing dots with hyphens, resulting in '1-2-3-beta' for use in the URL.", "context": ["cask-cookbook\n# Cask Cookbook\n## `uninstall` key `login_item:`\n### Stanza: `version`\n#### version methods\nThe examples above can become hard to read, however. Since many of these\nchanges are common, we provide a number of helpers to clearly interpret\notherwise obtuse cases:\n\n| Method              | Input              | Output             |\n|---------------------|--------------------|--------------------|\n| `major`             | `1.2.3-a45,ccdd88` | `1`                |\n| `minor`             | `1.2.3-a45,ccdd88` | `2`                |\n| `patch`             | `1.2.3-a45,ccdd88` | `3-a45`            |\n| `major_minor`       | `1.2.3-a45,ccdd88` | `1.2`              |\n| `major_minor_patch` | `1.2.3-a45,ccdd88` | `1.2.3-a45`        |\n| `minor_patch`       | `1.2.3-a45,ccdd88` | `2.3-a45`          |\n| `before_comma`      | `1.2.3-a45,ccdd88` | `1.2.3-a45`        |\n| `after_comma`       | `1.2.3-a45,ccdd88` | `ccdd88`           |\n| `dots_to_hyphens`   | `1.2.3-a45,ccdd88` | `1-2-3-a45,ccdd88` |\n| `no_dots`           | `1.2.3-a45,ccdd88` | `123-a45,ccdd88`   |\n\nSimilar to `dots_to_hyphens`, we provide all logical permutations of\n`{dots,hyphens,underscores}_to_{dots,hyphens,underscores}`. The same\napplies to `no_dots` in the form of `no_{dots,hyphens,underscores}`,\nwith an extra `no_dividers` that applies all of those at once.\n\nFinally, there is `csv` that returns an array of comma-separated values.\n`csv`, `before_comma` and `after_comma` are extra special to allow for\notherwise complex cases, and should be used sparingly. There should be\nno more than two of `,` per `version`.\n\n"], "note": ""}, {"id": 47, "message": "Regarding the following source code:\n```\nclass UserViewSet(ModelViewSet):\n    @action(methods=['post'], detail=True)\n    def set_password(self, request, pk=None):\n        # ...\n```\nIn the code above, how can you change the URL path and URL name of the 'set_password' action to 'change-password' and 'change_password' respectively?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["django_rest_framework~3.9.1"], "answer": "By adding the 'url_path' and 'url_name' arguments to the @action decorator with the desired values 'change-password' and 'change_password'.", "context": ["\nA viewset may mark extra actions for routing by decorating a method with the `@action` decorator. These extra actions will be included in the generated routes. For example, given the `set_password` method on the `UserViewSet` class:\n\n    from myapp.permissions import IsAdminOrIsSelf\n    from rest_framework.decorators import action\n\n    class UserViewSet(ModelViewSet):\n        ...\n\n        @action(methods=['post'], detail=True, permission_classes=[IsAdminOrIsSelf])\n        def set_password(self, request, pk=None):\n            ...\n\nThe following route would be generated:\n\n- URL pattern: `^users/{pk}/set_password/$`\n- URL name: `'user-set-password'`\n\nBy default, the URL pattern is based on the method name, and the URL name is the combination of the `ViewSet.basename` and the hyphenated method name. If you don't want to use the defaults for either of these values, you can instead provide the `url_path` and `url_name` arguments to the `@action` decorator.\n\n\nFor example, if you want to change the URL for our custom action to `^users/{pk}/change-password/$`, you could write:\n\n    from myapp.permissions import IsAdminOrIsSelf\n    from rest_framework.decorators import action\n\n    class UserViewSet(ModelViewSet):\n        ...\n\n        @action(methods=['post'], detail=True, permission_classes=[IsAdminOrIsSelf],\n                url_path='change-password', url_name='change_password')\n        def set_password(self, request, pk=None):\n            ...\n\nThe above example would now generate the following URL pattern:\n\n- URL path: `^users/{pk}/change-password/$`\n- URL name: `'user-change_password'`\n\nThis router includes routes for the standard set of `list`, `create`, `retrieve`, `update`, `partial_update` and `destroy` actions. The viewset can also mark additional methods to be routed, using the `@action` decorator.\n\n\n\n"], "note": ""}, {"id": 48, "message": "Regarding the following source code:\n```\nclass PurchaseList(generics.ListAPIView):\n    serializer_class = PurchaseSerializer\n    queryset = Purchase.objects.all()\n```\nIn the code above, how can you modify the view to filter purchases by a 'username' query parameter if it is provided in the request?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["django_rest_framework~3.9.1"], "answer": "By overriding the get_queryset method to check for 'username' in request.query_params and filtering the queryset based on it.", "context": ["\nFiltering\nWe can override `.get_queryset()` to deal with URLs such as `http://example.com/api/purchases?username=denvercoder9`, and filter the queryset only if the `username` parameter is included in the URL:\n\n    class PurchaseList(generics.ListAPIView):\n        serializer_class = PurchaseSerializer\n\n        def get_queryset(self):\n            \"\"\"\n            Optionally restricts the returned purchases to a given user,\n            by filtering against a `username` query parameter in the URL.\n            \"\"\"\n            queryset = Purchase.objects.all()\n            username = self.request.query_params.get('username', None)\n            if username is not None:\n                queryset = queryset.filter(purchaser__username=username)\n            return queryset\n\nAs well as being able to override the default queryset, REST framework also includes support for generic filtering backends that allow you to easily construct complex searches and filters.\n\nGeneric filters can also present themselves as HTML controls in the browsable API and admin API.\n\nThe default filter backends may be set globally, using the `DEFAULT_FILTER_BACKENDS` setting. For example.\n\n    REST_FRAMEWORK = {\n        'DEFAULT_FILTER_BACKENDS': ('django_filters.rest_framework.DjangoFilterBackend',)\n    }\n\nYou can also set the filter backends on a per-view, or per-viewset basis, using the `GenericAPIView` class-based views.\n\n    import django_filters.rest_framework\n    from django.contrib.auth.models import User\n    from myapp.serializers import UserSerializer\n    from rest_framework import generics\n\n    class UserListView(generics.ListAPIView):\n        queryset = User.objects.all()\n        serializer_class = UserSerializer\n        filter_backends = (django_filters.rest_framework.DjangoFilterBackend,)\n\n"], "note": ""}, {"id": 49, "message": "When using SessionAuthentication in Django REST Framework, how can you ensure that unsafe HTTP methods like POST require CSRF tokens?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["django_rest_framework~3.9.1"], "answer": "By ensuring that CSRF tokens are included in the HTTP requests, as SessionAuthentication requires valid CSRF tokens for unsafe methods.", "context": ["\nAuthentication\nThis authentication scheme uses Django's default session backend for authentication. Session authentication is appropriate for AJAX clients that are running in the same session context as your website.\n\nIf successfully authenticated, `SessionAuthentication` provides the following credentials.\n\n- `request.user` will be a Django `User` instance.\n- `request.auth` will be `None`.\n\nUnauthenticated responses that are denied permission will result in an `HTTP 403 Forbidden` response.\n\nIf you're using an AJAX style API with SessionAuthentication, you'll need to make sure you include a valid CSRF token for any \"unsafe\" HTTP method calls, such as `PUT`, `PATCH`, `POST` or `DELETE` requests. See the Django CSRF documentation for more details.\n\n**Warning**: Always use Django's standard login view when creating login pages. This will ensure your login views are properly protected.\n\nCSRF validation in REST framework works slightly differently to standard Django due to the need to support both session and non-session based authentication to the same views. This means that only authenticated requests require CSRF tokens, and anonymous requests may be sent without CSRF tokens. This behaviour is not suitable for login views, which should always have CSRF validation applied.\n\n"], "note": ""}, {"id": 50, "message": "Regarding the following source code:\n```\nclass Snippet(models.Model):\n    code = models.TextField()\n    # other fields...\n```\nIn the code above, how can you modify the Snippet model to automatically create a highlighted HTML version of the code when saving?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["django_rest_framework~3.9.1"], "answer": "By adding a 'highlighted' TextField to the model and overriding the save method to generate the highlighted HTML using Pygments and save it to the 'highlighted' field.", "context": ["\n4 - Authentication and permissions\nWe'll need some extra imports:\n\n    from pygments.lexers import get_lexer_by_name\n    from pygments.formatters.html import HtmlFormatter\n    from pygments import highlight\n\nAnd now we can add a `.save()` method to our model class:\n\n    def save(self, *args, **kwargs):\n        \"\"\"\n        Use the `pygments` library to create a highlighted HTML\n        representation of the code snippet.\n        \"\"\"\n        lexer = get_lexer_by_name(self.language)\n        linenos = 'table' if self.linenos else False\n        options = {'title': self.title} if self.title else {}\n        formatter = HtmlFormatter(style=self.style, linenos=linenos,\n                                  full=True, **options)\n        self.highlighted = highlight(self.code, lexer, formatter)\n        super(Snippet, self).save(*args, **kwargs)\n\nWhen that's all done we'll need to update our database tables. Normally we'd create a database migration in order to do that, but for the purposes of this tutorial, let's just delete the database and start again.\n\n    rm -f db.sqlite3\n    rm -r snippets/migrations\n    python manage.py makemigrations snippets\n    python manage.py migrate\n\nYou might also want to create a few different users, to use for testing the API. The quickest way to do this will be with the `createsuperuser` command.\n\n    python manage.py createsuperuser\n\n"], "note": ""}, {"id": 51, "message": "Regarding the following source code:\n```\ndef get(self, request):\n    queryset = Booking.objects.all()\n    serializer = BookingSerializer(queryset, many=True)\n    return Response(serializer.data)\n```\nIn the code above, when using URL-based versioning and hyperlinked serializers, why might the serialized URLs not include the version, and how can you fix it?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["django_rest_framework~3.9.1"], "answer": "Because the serializer lacks the request context needed to include versioned URLs; you can fix it by passing 'context={'request': request}' when instantiating the serializer.", "context": ["\nVersioning\nHow you vary the API behavior is up to you, but one example you might typically want is to switch to a different serialization style in a newer version. For example:\n\n    def get_serializer_class(self):\n        if self.request.version == 'v1':\n            return AccountSerializerVersion1\n        return AccountSerializer\n\nThe `reverse` function included by REST framework ties in with the versioning scheme. You need to make sure to include the current `request` as a keyword argument, like so.\n\n    from rest_framework.reverse import reverse\n\n    reverse('bookings-list', request=request)\n\nThe above function will apply any URL transformations appropriate to the request version. For example:\n\n- If `NamespaceVersioning` was being used, and the API version was 'v1', then the URL lookup used would be `'v1:bookings-list'`, which might resolve to a URL like `http://example.org/v1/bookings/`.\n- If `QueryParameterVersioning` was being used, and the API version was `1.0`, then the returned URL might be something like `http://example.org/bookings/?version=1.0`\n\nWhen using hyperlinked serialization styles together with a URL based versioning scheme make sure to include the request as context to the serializer.\n\n    def get(self, request):\n        queryset = Booking.objects.all()\n        serializer = BookingsSerializer(queryset, many=True, context={'request': request})\n        return Response({'all_bookings': serializer.data})\n\nDoing so will allow any returned URLs to include the appropriate versioning.\n\nThe versioning scheme is defined by the `DEFAULT_VERSIONING_CLASS` settings key.\n\n    REST_FRAMEWORK = {\n        'DEFAULT_VERSIONING_CLASS': 'rest_framework.versioning.NamespaceVersioning'\n    }\n\nUnless it is explicitly set, the value for `DEFAULT_VERSIONING_CLASS` will be `None`. In this case the `request.version` attribute will always return `None`.\n\n", "\nSerializers\nCalling `.save()` will either create a new instance, or update an existing instance, depending on if an existing instance was passed when instantiating the serializer class:\n\n    # .save() will create a new instance.\n    serializer = CommentSerializer(data=data)\n\n    # .save() will update the existing `comment` instance.\n    serializer = CommentSerializer(comment, data=data)\n\nBoth the `.create()` and `.update()` methods are optional. You can implement either neither, one, or both of them, depending on the use-case for your serializer class.\n\nSometimes you'll want your view code to be able to inject additional data at the point of saving the instance. This additional data might include information like the current user, the current time, or anything else that is not part of the request data.\n\n\nYou can do so by including additional keyword arguments when calling `.save()`. For example:\n\n    serializer.save(owner=request.user)\n\nAny additional keyword arguments will be included in the `validated_data` argument when `.create()` or `.update()` are called.\n\nIn some cases the `.create()` and `.update()` method names may not be meaningful. For example, in a contact form we may not be creating new instances, but instead sending an email or other message.\n\nIn these cases you might instead choose to override `.save()` directly, as being more readable and meaningful.\n\nFor example:\n\n    class ContactForm(serializers.Serializer):\n        email = serializers.EmailField()\n        message = serializers.CharField()\n\n        def save(self):\n            email = self.validated_data['email']\n            message = self.validated_data['message']\n            send_email(from=email, message=message)\n\nNote that in the case above we're now having to access the serializer `.validated_data` property directly.\n\n\n\n"], "note": ""}, {"id": 52, "message": "How can you permanently remove a large file from a Git repository's history to reduce the repository size, and what are the potential consequences of doing so?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["git~2"], "answer": "You can use Git's history-rewriting tools, such as filtering with 'git filter-branch' or tools like 'BFG Repo-Cleaner', to remove the large file from all commits. This process permanently deletes the file from the repository's history, reducing its size. However, it rewrites the commit history, which is destructive and requires all contributors to rebase their work onto the new history.", "context": ["# 10.7 Git Internals - Maintenance and Data Recovery\n## Maintenance and Data Recovery\n### Removing Objects\n\nThere are a lot of great things about Git, but one feature that can cause issues is the fact that a `git clone` downloads the entire history of the project, including every version of every file. This is fine if the whole thing is source code, because Git is highly optimized to compress that data efficiently. However, if someone at any point in the history of your project added a single huge file, every clone for all time will be forced to download that large file, even if it was removed from the project in the very next commit. Because it’s reachable from the history, it will always be there.\n\nThis can be a huge problem when you’re converting Subversion or Perforce repositories into Git. Because you don’t download the whole history in those systems, this type of addition carries few consequences. If you did an import from another system or otherwise find that your repository is much larger than it should be, here is how you can find and remove large objects.\n\n**Be warned: this technique is destructive to your commit history.** It rewrites every commit object since the earliest tree you have to modify to remove a large file reference. If you do this immediately after an import, before anyone has started to base work on the commit, you’re fine – otherwise, you have to notify all contributors that they must rebase their work onto your new commits.\n\nTo demonstrate, you’ll add a large file into your test repository, remove it in the next commit, find it, and remove it permanently from the repository. First, add a large object to your history:"], "note": ""}, {"id": 53, "message": "Regarding the following source code:\n```\n$ git branch -vv\n  feature    5e3ee11 [origin/feature: ahead 2] Add new feature\n  master     1ae2a45 [origin/master] Update README\n* develop    f8674d9 [origin/develop: ahead 3, behind 1] Work in progress\n  release    5ea463a Prepare for release\n```\nBased on the output above, how many commits is the 'develop' branch ahead and behind its tracking branch, and what does that indicate?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["git~2"], "answer": "The 'develop' branch is ahead by 3 commits and behind by 1 commit relative to its tracking branch 'origin/develop'. This means there are 3 commits locally that haven't been pushed to the remote repository and 1 commit on the remote that hasn't been fetched and merged locally.", "context": ["# 3.5 Git Branching - Remote Branches\n## Remote Branches\n### Tracking Branches\n\n``` highlight\n$ git branch -vv\n  iss53     7e424c3 [origin/iss53: ahead 2] Add forgotten brackets\n  master    1ae2a45 [origin/master] Deploy index fix\n* serverfix f8674d9 [teamone/server-fix-good: ahead 3, behind 1] This should do it\n  testing   5ea463a Try something new\n```\n\nSo here we can see that our `iss53` branch is tracking `origin/iss53` and is “ahead” by two, meaning that we have two commits locally that are not pushed to the server. We can also see that our `master` branch is tracking `origin/master` and is up to date. Next we can see that our `serverfix` branch is tracking the `server-fix-good` branch on our `teamone` server and is ahead by three and behind by one, meaning that there is one commit on the server we haven’t merged in yet and three commits locally that we haven’t pushed. Finally we can see that our `testing` branch is not tracking any remote branch.\n\nIt’s important to note that these numbers are only since the last time you fetched from each server. This command does not reach out to the servers, it’s telling you about what it has cached from these servers locally. If you want totally up to date ahead and behind numbers, you’ll need to fetch from all your remotes right before running this. You could do that like this:\n\n``` highlight\n$ git fetch --all; git branch -vv\n```"], "note": ""}, {"id": 54, "message": "Regarding the following source code:\n```\n$ git push origin :obsolete-branch\n```\nWhat does the above Git command accomplish, and how does it function in terms of refspec syntax?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["git~2"], "answer": "The command deletes the 'obsolete-branch' from the remote repository 'origin'. It uses the refspec syntax ':obsolete-branch', where an empty source ref and a destination ref indicate that the remote branch should be deleted.", "context": ["# 10.5 Git Internals - The Refspec\n## The Refspec\n### Deleting References\n\nYou can also use the refspec to delete references from the remote server by running something like this:\n\n``` highlight\n$ git push origin :topic\n```\n\nBecause the refspec is `<src>:<dst>`, by leaving off the `<src>` part, this basically says to make the `topic` branch on the remote nothing, which deletes it.\n\nOr you can use the newer syntax (available since Git v1.7.0):\n\n``` highlight\n$ git push origin --delete topic\n```"], "note": ""}, {"id": 55, "message": "Regarding the following source code:\n```\n$ git tag -v v1.4.2.1\ngpg: Signature made Wed Sep 13 02:08:25 2006 PDT using DSA key ID F3119B9A\ngpg: Can't check signature: public key not found\nerror: could not verify the tag 'v1.4.2.1'\n```\nWhen attempting to verify a signed Git tag, the above output was received. What does this mean, and how can you successfully verify the tag?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["git~2"], "answer": "The output indicates that GPG cannot verify the tag because the signer's public key is not in your keyring. To successfully verify the tag, you need to import the signer's public GPG key into your keyring.", "context": ["# 7.4 Git Tools - Signing Your Work\n## Signing Your Work\n### Verifying Tags\n\nTo verify a signed tag, you use `git tag -v <tag-name>`. This command uses GPG to verify the signature. You need the signer’s public key in your keyring for this to work properly:\n\n``` highlight\n$ git tag -v v1.4.2.1\nobject 883653babd8ee7ea23e6a5c392bb739348b1eb61\ntype commit\ntag v1.4.2.1\ntagger <PERSON><PERSON> <<EMAIL>> 1158138501 -0700\n\nGIT 1.4.2.1\n\nMinor fixes since 1.4.2, including git-mv and git-http with alternates.\ngpg: Signature made Wed Sep 13 02:08:25 2006 PDT using DSA key ID F3119B9A\ngpg: Good signature from \"<PERSON><PERSON> <<EMAIL>>\"\ngpg:                 aka \"[jpeg image of size 1513]\"\nPrimary key fingerprint: 3565 2A26 2040 E066 C9A7  4A7D C0C6 D9A4 F311 9B9A\n```\n\nIf you don’t have the signer’s public key, you get something like this instead:\n\n``` highlight\ngpg: Signature made Wed Sep 13 02:08:25 2006 PDT using DSA key ID F3119B9A\ngpg: Can't check signature: public key not found\nerror: could not verify the tag 'v1.4.2.1'\n```"], "note": ""}, {"id": 56, "message": "Regarding the following source code:\n```\n$ git fetch --all; git branch -vv\n```\nWhy is it recommended to run the above command before checking the ahead and behind numbers of branches, and what do these numbers represent?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["git~2"], "answer": "Running 'git fetch --all' updates your local cache with the latest commits from all remotes. This ensures that the ahead and behind numbers shown by 'git branch -vv' are accurate. These numbers represent how many commits your local branch is ahead of or behind its tracking branch on the remote.", "context": ["# 3.5 Git Branching - Remote Branches\n## Remote Branches\n### Tracking Branches\n\nIt's important to note that these numbers are only since the last time you fetched from each server. This command does not reach out to the servers, it's telling you about what it has cached from these servers locally. If you want totally up to date ahead and behind numbers, you'll need to fetch from all your remotes right before running this. You could do that like this:\n\n``` highlight\n$ git fetch --all; git branch -vv\n```"], "note": ""}, {"id": 57, "message": "How can you enable automatic memory reassignment in memcached, and what options are required to activate slab automove at startup and at runtime?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["memcached"], "answer": "You can enable automatic memory reassignment in memcached by using the startup options `-o slab_reassign,slab_automove`. Slab automove requires slab reassignment to be enabled first. At startup, include these options when launching memcached. Additionally, slab automove can be enabled or disabled at runtime using the command `echo \"slabs automove 0\" | nc localhost 11211`.", "context": ["Once all memory has been assigned and used by items, you may use a command to\nreassign memory.\n\n```$ echo \"slabs reassign 1 4\" | nc localhost 11211}}}\n\nThat will return an error code indicating success, or a need to retry later.\nSuccess does not mean that the slab was moved, but that a background thread\nwill attempt to move the memory as quickly as it can.\n\n=== Slab Automove ===\n\nWhile slab reassign is a manual feature, there is also the start of an\nautomatic memory reassignment algorithm.\n\n```$ memcached -o slab_reassign,slab_automove}}}\n\nThe above enables it on startup. slab_automove requires slab_reassign first be\nenabled.\n\nautomove itself may also be enabled or disabled at runtime:\n\n```$ echo \"slabs automove 0\" | nc localhost 11211}}}\n\nThe algorithm is slow and conservative. If a slab class is seen as having the\nhighest eviction count 3 times 10 seconds apart, it will take a page from a\nslab class which has had zero evictions in the last 30 seconds and move the\nmemory.\n\nThere are lots of cases where this will not be sufficient, and we invite the\ncommunity to help improve upon the algorithm. Included in the source directory\nis `scripts/mc_slab_mover`. See perldoc for more information:\n\n```$ perldoc ./scripts/mc_slab_mover}}}\n\nIt implements the same algorithm as built into memcached, and you may modify\nit to better suit your needs and improve on the script or port it to other\nlanguages. Please provide patches!\n\n=== Slab Reassign Implementation ===\n\nSlab page reassignment requires some tradeoffs:\n\n  * All items larger than 500k (even if they're under 730k) take 1MB of space\n\n  * When memory is reassigned, all items that were in the 1MB page are evicted\n\n  * When slab reassign is enabled, an extra background thread is used\n\nThe first item will be improved in later releases, and is avoided if you start\nmemcached without the -o slab_reassign option.\n\n=== New Stats ===", "Slab automover has gotten a very large update. The wider discussion can\nbe found in the pull request.\n\nAs data is stored into memcached, it pre-allocates pages of memory into\nslab classes of a particular size (ie: 90 bytes, 120 bytes). If you fill\nyour cache with 90 byte objects, and then start writing 120 byte\nobjects, there will be much less space available for 120 byte objects.\n\nWith the slab automover improvements, freed memory can be reclaimed back\ninto a global pool and reassigned to new slab classes. You can also\nstill manually move slab pages between classes with your own external\nprocess if the automover does not fit your needs (see doc/protocol.txt\nfor full details).\n\nThe automover now attempts to rescue items which are still valid when\nmoving a page from one class to another, or from one class into the\nglobal page pool. This makes it much less destructive.\n\nTo get all of the benefits of the last few releases, we recommend adding\nthe following startup options:\n\n`-o slab_reassign,slab_automove,lru_crawler,lru_maintainer`\n\nA modern start line includes a few other items:\n\n`-o slab_reassign,slab_automove,lru_crawler,lru_maintainer,maxconns_fast,hash_algorithm=murmur3`\n\nMany of these options are likely to become defaults in the future."], "note": ""}, {"id": 58, "message": "What is the effect of using the '-o expirezero_does_not_evict' option in memcached, and what are the potential caveats of enabling it?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["memcached"], "answer": "Using the '-o expirezero_does_not_evict' option in memcached makes items with an expiration time of 0 unevictable, preventing them from being removed from the cache. However, this can reduce the memory available for other items, potentially leading to memory crowding. The caveats include hardcoded tunables and a reduced maximum number of slab classes.", "context": ["An extra option: -o expirezero_does_not_evict (when used with\nlru_maintainer) will make items with an expiration time of 0\nunevictable. Take caution as this will crowd out memory available for\nother evictable items.\n\nSome caveats exist:\n\n- Some possible tunables are currently hardcoded.\n- Max number of slab classes is now 62, instead of 200. The default slab\n  factor gives 42 classes.\n\nThis is loosely inspired by the 2Q algorithm. More specifically the\nOpenBSD variant of it:\nhttp://www.tedunangst.com/flak/post/2Q-buffer-cache-algorithm\n\nIt's then extended to cope with the fact that memcached items do not\nbehave the same way as a buffer pool. TTL's mean extra\nscanning/shuffling is done to improve memory efficiency for valid items."], "note": ""}, {"id": 59, "message": "How can you transmit binary encoded keys using the memcached meta protocol, and which flag should you include in the command?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["memcached"], "answer": "You can transmit binary encoded keys in the memcached meta protocol by encoding the key in base64 and including the 'b' flag in your command. This allows you to use binary keys, which can reduce key size and save memory.", "context": ["The meta protocol can now transmit binary encoded keys by encoding the key in base64 and sending a 'b' flag with the command. If for example your key composes of several large numerics (userids and so on), these can be encoded directly as binary. This shortens the key which can save some memory on the server. See `doc/protocol.txt` for more details."], "note": ""}, {"id": 60, "message": "How can you configure memcached to store items larger than 1MB, and what is the recommended maximum slab chunk size when doing so?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["memcached"], "answer": "To configure memcached to store items larger than 1MB, you can increase the item size limit using the `-I` option, such as `-I 2m` for 2MB. Additionally, it's recommended to set the maximum slab chunk size using `-o slab_chunk_max=16384` to optimize memory efficiency.", "context": ["After over a decade of having an item size target of a single megabyte,\nitems can now safely raise above that. Up to hundreds of megabytes,\npotentially.\n\nThis feature automatically enables if you raise the item size limit\nabove 1mb. IE: `-I 2m`.\n\nYou can also enable it manually with a limit of 1mb (the default of\n`-I 1m`) or less (like `-I 512k`) by setting `-o slab_chunk_max=16384`\n(16384 is the recommended default).\n\nFull details in the pull request:\nhttps://github.com/memcached/memcached/pull/181\n\nIn summary:\n\n- Memcached uses a slab allocator, which splits up memory into chunks of\n  specific sizes.\n- The \"item size limit\" has always been the largest possible slab chunk.\n- Increasing the size limit thus spreads out available slab classes,\n  reducing memory efficiency.\n\nNow:\n\n- The maximum slab chunk size is no longer tied to the maximum item\n  size.\n- Items larger than the slab chunk size now comprise of multiple chunks\n  of the largest size."], "note": ""}, {"id": 61, "message": "Regarding the following source code:\n```\nlocal router = mcp.router_new({ map = { foo = handler1, bar = handler2 } })\nmcp.attach(mcp.CMD_ANY_STORAGE, router)\n```\nIn memcached proxy configuration, how can you set up a router to route requests based on key prefixes, and how do you attach it to handle all storage commands?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["memcached"], "answer": "You can set up a router in the memcached proxy configuration by creating a router object with a map of key prefixes to handlers using `mcp.router_new`. To route based on key prefixes, specify the `mode` as \"prefix\" and define the `stop` character. Attach the router to handle all storage commands using `mcp.attach(mcp.CMD_ANY_STORAGE, router)`.", "context": ["Lets say we want to route requests to different pools of memcached\ninstances based on part of the key in the request: for this we use\nrouter objects.\n\nRouters can achieve this matching efficiently without having to examine\nthe key inside Lua, which would result in many copies and regular\nexpressions.\n\n    -- Minimal example of a router. Here:\n    -- \"get foo/etc\" would be handled by the \"funcgen_foo\" handler\n    -- \"get bar/etc\" would be handled by the \"funcgen_bar\" handler\n    -- By default the router checks up to a \"/\" character for the map key.\n    local m = {\n        foo = funcgen_foo,\n        bar = funcgen_bar,\n    }\n    local router = mcp.router_new({ map = m, mode = \"prefix\", stop == \"/\" })\n    mcp.attach(mcp.CMD_GET, router)\n\nExplanation of router options:", "local r = mcp.router_new({\n        -- a table of route handlers to requests to\n        -- see \"command maps\" below for more info.\n        map = m,\n        -- mode can be (default \"prefix\"):\n        -- \"prefix\": we check the prefix of the key against the map.\n        -- stop matching when the \"stop\" character is seen.\n        -- \"anchor\": we check for and skip characters in \"start\", then match until\n        -- \"stop\" is seen.\n        mode = \"etc\",\n        -- start looks for these characters at the start of a key and skips them\n        -- before finding a sub string to match against. It is \"\" by default\n        -- if start is a single character, and optimized algorithm is used.\n        -- it must be 5 characters or less.\n        start = \"_\",\n        -- stop will stop matching at this character, and what came before this\n        -- string to check against the map. It is \"/\" by default.\n        -- It follows the same rules as \"start\": single characters are faster, max\n        -- is 5.\n        stop = \"/\"\n        -- If the request does not match against the map, use this route handler\n        -- by default.\n        default = funcgen\n    })\n\n    -- command maps\n\n    -- A router map entry may either reference a funcgen handler directly, or\n    -- another table which further maps commands to funcgen handlers\n    local m = {\n        -- any \"foo/etc\" key for get/set/touch will route here\n        foo = handler1,\n        bar = {\n            -- only \"get bar/etc\" will use handler2.\n            [mcp.CMD_GET] = handler2,\n            -- if a CMD_ANY_STORAGE entry is also provided, use if no exact match\n            [mcp.CMD_ANY_STORAGE] = handler3,\n            -- if no CMD_ANY_STORAGE is provided, and no exact CMD match, the\n            -- router's default entry is used.\n        }\n    }\n    local r = mcp.router_new({ map = m })\n    mcp.attach(mcp.CMD_ANY_STORAGE, r)"], "note": ""}, {"id": 62, "message": "When configuring an AWS DataSync HDFS location with Kerberos authentication, what are the required parameters, and how should the Kerberos keytab be provided if it is not a valid UTF-8 string?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["terraform~1.8.3.240519"], "answer": "You must set `authentication_type` to `KERBEROS` and provide `kerberos_principal`, and either `kerberos_keytab` or `kerberos_keytab_base64`, as well as `kerberos_krb5_conf` or `kerberos_krb5_conf_base64`. If the Kerberos keytab is not a valid UTF-8 string, it should be provided using the `kerberos_keytab_base64` parameter with base64-encoded binary data.", "context": ["aws_datasync_location_hdfs\n# Resource: aws_datasync_location_hdfs\n## Argument Reference\n`agent_arns` - (Required) A list of DataSync Agent ARNs with which this location will be associated.\n\n`authentication_type` - (Required) The type of authentication used to determine the identity of the user. Valid values are `SIMPLE` and `KERBEROS`.\n\n`kerberos_keytab` - (Optional) The Kerberos key table (keytab) that contains mappings between the defined Kerberos principal and the encrypted keys. Use `kerberos_keytab_base64` instead whenever the value is not a valid UTF-8 string. If `KERBEROS` is specified for `authentication_type`, this parameter (or `kerberos_keytab_base64`) is required.\n\n`kerberos_keytab_base64` - (Optional) Use instead of `kerberos_keytab` to pass base64-encoded binary data directly. If `KERBEROS` is specified for `authentication_type`, this parameter (or `kerberos_keytab`) is required.\n\n`kerberos_krb5_conf` - (Optional) The krb5.conf file that contains the Kerberos configuration information. Use `kerberos_krb5_conf_base64` instead whenever the value is not a valid UTF-8 string. If `KERBEROS` is specified for `authentication_type`, this parameter (or `kerberos_krb5_conf_base64`) is required.\n\n`kerberos_principal` - (Optional) The Kerberos principal with access to the files and folders on the HDFS cluster. If `KERBEROS` is specified for `authentication_type`, this parameter is required."], "note": ""}, {"id": 63, "message": "Regarding the following source code:\n```\nresource \"oci_core_instance_configuration\" \"example\" {\n  instance_options {\n    are_legacy_imds_endpoints_disabled = true\n  }\n}\n```\nIn an OCI Core Instance Configuration, which attribute should be set to disable the legacy instance metadata service endpoints, and what value should it have?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["terraform~1.8.3.240519"], "answer": "You should set the `are_legacy_imds_endpoints_disabled` attribute within the `instance_options` block to `true` to disable the legacy instance metadata service endpoints.", "context": ["oci_core_instance_configuration\n# oci_core_instance_configuration\n## Argument Reference\n`instance_options` - Optional mutable instance options. As a part of Instance Metadata Service Security Header, This allows user to disable the legacy imds endpoints.\n\n- `are_legacy_imds_endpoints_disabled` - Whether to disable the legacy (/v1) instance metadata service endpoints. Customers who have migrated to /v2 should set this to true for added security. Default is false.", "oci_core_instance_configuration\n# oci_core_instance_configuration\n## Attributes Reference\n`instance_options` - Optional mutable instance options. As a part of Instance Metadata Service Security Header, This allows user to disable the legacy imds endpoints.\n\n- `are_legacy_imds_endpoints_disabled` - Whether to disable the legacy (/v1) instance metadata service endpoints. Customers who have migrated to /v2 should set this to true for added security. Default is false."], "note": ""}, {"id": 64, "message": "According to Terraform's credentials helper guidelines, how should a credentials helper behave if it is asked to store a credentials object with unsupported properties other than 'token'?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["terraform~1.8.3.240519"], "answer": "The credentials helper must treat the object as unstorable and return an error; it should not store only the `token` and discard other properties, as this could alter the credentials' meaning.", "context": ["Credentials Helpers\n# Credentials Helpers\n## Handling Unsupported Credentials Object Properties\n\nTerraform defines only the `token` property within JSON credentials objects.\n\nIf a credentials helper is asked to store an object that has any properties other than `token` and if it is not able to faithfully retain them then it must behave as if the object is unstorable, returning an error. It must *not* store the `token` value in isolation and silently drop other properties, as that might change the meaning of the credentials object."], "note": ""}, {"id": 65, "message": "When using the Terraform function 'lookup', what will be the result of the expression 'lookup({a=\"apple\", b=\"banana\"}, \"c\", \"default\")', and why?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["terraform~1.8.3.240519"], "answer": "The result will be \"default\" because the key \"c\" does not exist in the map, so the default value is returned.", "context": ["# `lookup` Function\n`lookup` retrieves the value of a single element from a map, given its key. If the given key does not exist, the given default value is returned instead.\n\n    lookup(map, key, default)\n\n## Examples\n\n    > lookup({a=\"ay\", b=\"bee\"}, \"a\", \"what?\")\n    ay\n    > lookup({a=\"ay\", b=\"bee\"}, \"c\", \"what?\")\n    what?"], "note": ""}, {"id": 66, "message": "How can you import an AWS API Gateway Integration resource into Terraform, and what identifier should be used if the REST API ID is 'abc123', the Resource ID is 'def456', and the HTTP method is 'GET'?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["terraform~1.8.3.240519"], "answer": "You should use the identifier in the format `REST-API-ID/RESOURCE-ID/HTTP-METHOD`. The import command would be: `terraform import aws_api_gateway_integration.example abc123/def456/GET`.", "context": ["aws_api_gateway_integration\n# Resource: aws_api_gateway_integration\n## Import\nIn Terraform v1.5.0 and later, use an `import` block to import `aws_api_gateway_integration` using `REST-API-ID/RESOURCE-ID/HTTP-METHOD`. For example:\n\n    import {\n      to = aws_api_gateway_integration.example\n      id = \"12345abcde/67890fghij/GET\"\n    }\n\nUsing `terraform import`, import `aws_api_gateway_integration` using `REST-API-ID/RESOURCE-ID/HTTP-METHOD`. For example:\n\n    % terraform import aws_api_gateway_integration.example 12345abcde/67890fghij/GET"], "note": ""}, {"id": 67, "message": "Regarding the following source code:\n```\nlet source = Infallible<Int>.from([1, 2, 3])\nlet second = Infallible<String>.never()\nlet result = source.withLatestFrom(second)\n```\nIn RxSwift, when using 'withLatestFrom' with two Infallible sequences, will the elements emitted by the source sequence before the second sequence has emitted any values be included in the result?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["rxswift~6.7.1"], "answer": "No, elements emitted by the source sequence before the second sequence has emitted any values will be omitted when using 'withLatestFrom' with Infallible sequences.", "context": ["Note\n\nElements emitted by self before the second source has emitted any values will be omitted."], "note": ""}, {"id": 68, "message": "Regarding the following source code:\n```\nlet completable1 = Completable.create { observer in\n    observer(.completed)\n    return Disposables.create()\n}\nlet completable2 = Completable.create { observer in\n    observer(.completed)\n    return Disposables.create()\n}\nlet combined = Completable.zip(completable1, completable2)\n```\nIn RxSwift, when zipping multiple Completable sequences using 'Completable.zip', how does it combine them, and what is the relationship between 'zip' and 'merge' for Completable?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["rxswift~6.7.1"], "answer": "When using 'Completable.zip', it merges the completion of all Completable sequences into a single Completable. For Completable, 'zip' is an alias for 'merge', so it behaves the same as 'merge'.", "context": ["Merges the completion of all Completables into a single Completable.\n\nNote\n\nFor `Completable`, `zip` is an alias for `merge`.", "Merges the completion of all Completables from a collection into a single Completable.\n\nNote\n\nFor `Completable`, `zip` is an alias for `merge`."], "note": ""}, {"id": 69, "message": "In RxSwift, if I have a Single sequence and want to subscribe with 'onSuccess' and 'onError' handlers, which method should I use given that 'subscribe(onSuccess:onError:onDisposed:)' is deprecated?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["rxswift~6.7.1"], "answer": "You should use 'subscribe(onSuccess:onFailure:onDisposed:)' as the replacement method since 'subscribe(onSuccess:onError:onDisposed:)' is deprecated and renamed.", "context": ["@available(*, deprecated, renamed: \"subscribe(onSuccess:onFailure:onDisposed:﹚\")\npublic func subscribe(onSuccess: ((Element) -> Void)? = nil,\n                      onError: @escaping ((Swift.Error) -> Void),\n                      onDisposed: (() -> Void)? = nil) -> Disposable"], "note": ""}, {"id": 70, "message": "In RxSwift, how does a 'Binder' ensure thread safety when binding UI elements, and what happens if the target object is released?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["rxswift~6.7.1"], "answer": "A 'Binder' ensures thread safety by performing bindings on a specific scheduler, the main scheduler by default. It doesn't retain the target, so if the target object is released, the elements are not bound.", "context": ["Observer that enforces interface binding rules:\n\n- can’t bind errors (in debug builds binding of errors causes `fatalError` in release builds errors are being logged)\n- ensures binding is performed on a specific scheduler\n\n`Binder` doesn’t retain target and in case target is released, element isn’t bound.\n\nBy default it binds elements on main scheduler."], "note": ""}, {"id": 71, "message": "Regarding the following source code:\n```\ninfallibleSequence.subscribe(with: self, onNext: { object, element in\n    object.handle(element)\n}, onCompleted: { object in\n    object.completed()\n}, onDisposed: { object in\n    object.disposed()\n})\n```\nIn RxSwift, when using 'subscribe(with:onNext:onCompleted:onDisposed:)' on an Infallible sequence, how is the provided object reference handled, and what occurs if the object cannot be retained?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["rxswift~6.7.1"], "answer": "The 'subscribe(with:onNext:onCompleted:onDisposed:)' method provides an unretained, safe reference to the object. If the object can't be retained, none of the event closures will be invoked.", "context": ["Subscribes an element handler, a completion handler and disposed handler to an observable sequence.\n\nError callback is not exposed because `Infallible` can’t error out.\n\nAlso, take in an object and provide an unretained, safe to use (i.e. not implicitly unwrapped), reference to it along with the events emitted by the sequence.\n\nNote\n\nIf `object` can’t be retained, none of the other closures will be invoked."], "note": ""}, {"id": 72, "message": "Regarding the following source code:\n```\n<?php\n$application = new Application();\n$application->setDefaultCommand('my_command', true);\n```\nIn Symfony, how can you set a default command for your console application and make it run in single command mode?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["symfony~4.1"], "answer": "You can use the `setDefaultCommand` method of the `Application` class, passing the command name and setting the `$isSingleCommand` parameter to `true`. This sets the default command and makes the application run in single command mode.", "context": ["application\n# Application\n## Details\n### Application setDefaultCommand(string $commandName, bool $isSingleCommand = false)\nSets the default Command name.\n\n#### Parameters\n\n|        |                   |                                                              |\n|--------|-------------------|--------------------------------------------------------------|\n| string | $commandName     | The Command name                                             |\n| bool   | $isSingleCommand | Set to true if there is only one command in this application |\n\n#### Return Value\n\n|             |\n|-------------|\n| Application |"], "note": ""}, {"id": 73, "message": "Regarding the following source code:\n```\n<?php\n$command = new MyCommand();\n$command->setCode(function (InputInterface $input, OutputInterface $output) {\n    // custom code\n});\n$application = new Application();\n$application->add($command);\n$application->run();\n```\nHow can you set custom code to execute when running a command in Symfony, overriding the execute() method, and how does the Application class handle running the command?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["symfony~4.1"], "answer": "You can use the `setCode` method on your Command instance to set a callable that will be executed when the command runs, overriding the `execute()` method. The `Application` class handles running the command using its `run()` method, which internally calls `doRun()` to execute the command.", "context": ["cachepooldeletecommand\n# CachePoolDeleteCommand\n## Details\n### $this setCode(callable $code)\n\nSets the code to execute when running this command.\n\nIf this method is used, it overrides the code defined in the execute()\nmethod.\n\n#### Parameters\n\n|          |        |                                                              |\n|----------|--------|--------------------------------------------------------------|\n| callable | $code | A callable(InputInterface $input, OutputInterface $output) |\n\n#### Return Value\n\n|        |\n|--------|\n| $this |\n\n#### Exceptions\n\n|                          |\n|--------------------------|\n| InvalidArgumentException |\n\n#### See also\n\n|           |\n|-----------|\n| execute() |", "application\n# Application\n## Details\n### int run(InputInterface $input = null, OutputInterface $output = null)\n\nRuns the current application.\n\n#### Parameters\n\n|                 |          |\n|-----------------|----------|\n| InputInterface  | $input  |\n| OutputInterface | $output |\n\n#### Return Value\n\n|     |                                             |\n|-----|---------------------------------------------|\n| int | 0 if everything went fine, or an error code |\n\n#### Exceptions\n\n|           |                                                                    |\n|-----------|--------------------------------------------------------------------|\n| Exception | When running fails. Bypass this when {@link setCatchExceptions()}. |\n\n\n### int doRun(InputInterface $input, OutputInterface $output)\n\nRuns the current application.\n\n#### Parameters\n\n|                 |          |\n|-----------------|----------|\n| InputInterface  | $input  |\n| OutputInterface | $output |\n\n#### Return Value\n\n|     |                                             |\n|-----|---------------------------------------------|\n| int | 0 if everything went fine, or an error code |"], "note": ""}, {"id": 74, "message": "In Symfony, is it possible to set a new service in the ContainerBuilder after it has been compiled, and what happens if you try?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["symfony~4.1"], "answer": "No, you cannot set a new service in the `ContainerBuilder` after it has been compiled. If you try to do so, a `BadMethodCallException` is thrown indicating that the `ContainerBuilder` is compiled.", "context": ["containerbuilder\n# ContainerBuilder\n## Details\n### set(string $id, object $service)\n\nSets a service.\n\n#### Parameters\n\n|        |           |                        |\n|--------|-----------|------------------------|\n| string | $id      | The service identifier |\n| object | $service | The service instance   |\n\n#### Exceptions\n\n|                        |                                        |\n|------------------------|----------------------------------------|\n| BadMethodCallException | When this ContainerBuilder is compiled |"], "note": ""}, {"id": 75, "message": "How can you remove all CSRF tokens from the SessionTokenStorage in Symfony, and how can you check if a specific token exists before removing it?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["symfony~4.1"], "answer": "You can call the `clear()` method on the `SessionTokenStorage` instance to remove all CSRF tokens. To check if a specific token exists before removing it, use the `hasToken($tokenId)` method.", "context": ["sessiontokenstorage\n# SessionTokenStorage\n## Details\n### clear()\n\nRemoves all CSRF tokens.\n\n\n### bool hasToken(string $tokenId)\n\nChecks whether a token with the given token ID exists.\n\n#### Parameters\n\n|        |           |              |\n|--------|-----------|--------------|\n| string | $tokenId | The token ID |\n\n#### Return Value\n\n|      |                                          |\n|------|------------------------------------------|\n| bool | Whether a token exists with the given ID |"], "note": ""}, {"id": 76, "message": "How can you retrieve the list of event listeners that were not called during an event dispatch in Symfony, and how can you check if an event has any registered listeners?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["symfony~4.1"], "answer": "You can use the `getNotCalledListeners()` method of the `TraceableEventDispatcher` to retrieve the list of event listeners that were not called during an event dispatch. To check if an event has any registered listeners, use the `hasListeners($eventName)` method.", "context": ["traceableeventdispatcher\n# TraceableEventDispatcher\n## Details\n### bool hasListeners(string $eventName = null)\n\nChecks whether an event has any registered listeners.\n\n#### Parameters\n\n|        |             |                       |\n|--------|-------------|-----------------------|\n| string | $eventName | The name of the event |\n\n#### Return Value\n\n|      |                                                                |\n|------|----------------------------------------------------------------|\n| bool | true if the specified event has any listeners, false otherwise |\n\n\n### array getNotCalledListeners()\n\nGets the not called listeners.\n\n#### Return Value\n\n|       |                                  |\n|-------|----------------------------------|\n| array | An array of not called listeners |"], "note": ""}, {"id": 77, "message": "Regarding the following source code:\n```\n// In a Laravel controller, I have the following validation:\n$request->validate([\n    'name' => ['required', 'string', /* custom rule here */],\n]);\n```\nHow can you define and use a custom validation rule in Laravel that checks whether a given string is entirely uppercase?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["laravel~9"], "answer": "You can create a custom validation rule by running the Artisan command `php artisan make:rule Uppercase --invokable`, which generates a rule object. Implement the `__invoke` method to define the validation logic that checks if the string is uppercase. Then, use the new rule in your validator by passing an instance of it alongside other validation rules.", "context": ["validation\n# Validation\n## Custom Validation Rules\n### Using Rule Objects\n<PERSON><PERSON> provides a variety of helpful validation rules; however, you may\nwish to specify some of your own. One method of registering custom\nvalidation rules is using rule objects. To generate a new rule object,\nyou may use the `make:rule` Artisan command. Let's use this command to\ngenerate a rule that verifies a string is uppercase. <PERSON><PERSON> will place\nthe new rule in the `app/Rules` directory. If this directory does not\nexist, <PERSON><PERSON> will create it when you execute the Artisan command to\ncreate your rule:\n\n```\nphp artisan make:rule Uppercase --invokable\n```\n\nOnce the rule has been created, we are ready to define its behavior. A\nrule object contains a single method: `__invoke`. This method receives\nthe attribute name, its value, and a callback that should be invoked on\nfailure with the validation error message:\n\n```\n<?php\n\nnamespace App\\Rules;\n\nuse Illuminate\\Contracts\\Validation\\InvokableRule;\n\nclass Uppercase implements InvokableRule\n{\n    /**\n     * Run the validation rule.\n     *\n     * @param  string  \\$attribute\n     * @param  mixed  \\$value\n     * @param  \\Closure  \\$fail\n     * @return void\n     */\n    public function __invoke(\\$attribute, \\$value, \\$fail)\n    {\n        if (strtoupper(\\$value) !== \\$value) {\n            \\$fail('The :attribute must be uppercase.');\n        }\n    }\n}\n```\n\nOnce the rule has been defined, you may attach it to a validator by\npassing an instance of the rule object with your other validation rules:\n\n```\nuse App\\Rules\\Uppercase;\n\n\\$request->validate([\n    'name' => ['required', 'string', new Uppercase],\n]);\n```\n"], "note": ""}, {"id": 78, "message": "Regarding the following source code:\n```\n<?php\n\nnamespace App\\Models;\n\nuse App\\Contracts\\Publisher;\nuse Illuminate\\Database\\Eloquent\\Model;\n\nclass Podcast extends Model\n{\n    public function publish()\n    {\n        \\$this->update(['publishing' => now()]);\n\n        Publisher::publish(\\$this);\n    }\n}\n```\nHow can you modify this code to use real-time facades in Laravel to simplify static method calls and make it easier to mock during testing?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["laravel~9"], "answer": "You can modify the code by importing the class with the 'Facades' prefix, like `use Facades\\App\\Contracts\\Publisher;`. This allows you to call `Publisher::publish($this);` statically. During testing, you can mock these static method calls using Laravel's facade testing helpers.", "context": ["facades\n# Facades\n## Real-Time Facades\n\nInjecting a publisher implementation into the method allows us to easily\ntest the method in isolation since we can mock the injected publisher.\nHowever, it requires us to always pass a publisher instance each time we\ncall the `publish` method. Using real-time facades, we can maintain the\nsame testability while not being required to explicitly pass a\n`Publisher` instance. To generate a real-time facade, prefix the\nnamespace of the imported class with `Facades`:\n\n```\n<?php\n\nnamespace App\\Models;\n\nuse Facades\\App\\Contracts\\Publisher;\nuse Illuminate\\Database\\Eloquent\\Model;\n\nclass Podcast extends Model\n{\n    /**\n     * Publish the podcast.\n     *\n     * @return void\n     */\n    public function publish()\n    {\n        \\$this->update(['publishing' => now()]);\n\n        Publisher::publish(\\$this);\n    }\n}\n```\n\n\nWhen the real-time facade is used, the publisher implementation will be\nresolved out of the service container using the portion of the interface\nor class name that appears after the `Facades` prefix. When testing, we\ncan use <PERSON><PERSON>'s built-in facade testing helpers to mock this method\ncall:\n\n```\n<?php\n\nnamespace Tests\\Feature;\n\nuse App\\Models\\Podcast;\nuse Facades\\App\\Contracts\\Publisher;\nuse Illuminate\\Foundation\\Testing\\RefreshDatabase;\nuse Tests\\TestCase;\n\nclass PodcastTest extends TestCase\n{\n    use RefreshDatabase;\n\n    /**\n     * A test example.\n     *\n     * @return void\n     */\n    public function test_podcast_can_be_published()\n    {\n        \\$podcast = Podcast::factory()->create();\n\n        Publisher::shouldReceive('publish')->once()->with(\\$podcast);\n\n        \\$podcast->publish();\n    }\n}\n```\n"], "note": ""}, {"id": 79, "message": "Regarding the following source code:\n```\n// In the DatabaseNotification model:\nclass DatabaseNotification extends Model\n{\n    // How can we customize the date format for 'created_at'?\n}\n```\nHow can you retrieve all unread notifications for a user in Laravel and customize the serialization format of the 'created_at' date attribute to 'Y-m-d'?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["laravel~9"], "answer": "You can retrieve unread notifications using the `unreadNotifications` relationship. To customize the 'created_at' date format, you can override the `serializeDate` method in the `DatabaseNotification` model or specify the format in the `$casts` property like `'created_at' => 'datetime:Y-m-d'`.", "context": ["notifications\n# Notifications\n## Database Notifications\n### Accessing The Notifications\nOnce notifications are stored in the database, you need a convenient way\nto access them from your notifiable entities. The\n`Illuminate\\Notifications\\Notifiable` trait, which is included on\n<PERSON><PERSON>'s default `App\\Models\\User` model, includes a `notifications`\nEloquent relationship that returns the notifications for the entity. To\nfetch notifications, you may access this method like any other Eloquent\nrelationship. By default, notifications will be sorted by the\n`created_at` timestamp with the most recent notifications at the\nbeginning of the collection:\n\n```\n\\$user = App\\Models\\User::find(1);\n\nforeach (\\$user->notifications as \\$notification) {\n    echo \\$notification->type;\n}\n```\n\nIf you want to retrieve only the \"unread\" notifications, you may use the\n`unreadNotifications` relationship. Again, these notifications will be\nsorted by the `created_at` timestamp with the most recent notifications\nat the beginning of the collection:\n\n```\n\\$user = App\\Models\\User::find(1);\n\nforeach (\\$user->unreadNotifications as \\$notification) {\n    echo \\$notification->type;\n}\n```\n\n> **Note** To access your notifications from your JavaScript client, you\n> should define a notification controller for your application which\n> returns the notifications for a notifiable entity, such as the current\n> user. You may then make an HTTP request to that controller's URL from\n> your JavaScript client.\n\n", "eloquent-serialization\n# Eloquent: Serialization\n#### Customizing The Default Date Format\nYou may customize the default serialization format by overriding the\n`serializeDate` method. This method does not affect how your dates are\nformatted for storage in the database:\n\n```\n/**\n * Prepare a date for array / JSON serialization.\n *\n * @param  \\DateTimeInterface  \\$date\n * @return string\n */\nprotected function serializeDate(DateTimeInterface \\$date)\n{\n    return \\$date->format('Y-m-d');\n}\n```\n\n#### Customizing The Date Format Per Attribute\n\nYou may customize the serialization format of individual Eloquent date\nattributes by specifying the date format in the model's cast\ndeclarations:\n\n```\nprotected \\$casts = [\n    'birthday' => 'date:Y-m-d',\n    'joined_at' => 'datetime:Y-m-d H:00',\n];\n```\n"], "note": ""}, {"id": 80, "message": "How can you define a relationship in Laravel Eloquent to retrieve the most recent related model of a one-to-many relationship, such as a user's latest order?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["laravel~9"], "answer": "You can define a `hasOne` relationship and chain the `latestOfMany` method to retrieve the most recent related model. For example, in your User model, define a method like `latestOrder()` that returns `$this->hasOne(Order::class)->latestOfMany();`.", "context": ["eloquent-relationships\n# Eloquent: Relationships\n## Defining Relationships\n### Has One Of Many\nSometimes a model may have many related models, yet you want to easily\nretrieve the \"latest\" or \"oldest\" related model of the relationship. For\nexample, a `User` model may be related to many `Order` models, but you\nwant to define a convenient way to interact with the most recent order\nthe user has placed. You may accomplish this using the `hasOne`\nrelationship type combined with the `ofMany` methods:\n\n```\n/**\n * Get the user's most recent order.\n */\npublic function latestOrder()\n{\n    return \\$this->hasOne(Order::class)->latestOfMany();\n}\n```\n\nLikewise, you may define a method to retrieve the \"oldest\", or first,\nrelated model of a relationship:\n\n```\n/**\n * Get the user's oldest order.\n */\npublic function oldestOrder()\n{\n    return \\$this->hasOne(Order::class)->oldestOfMany();\n}\n```\n\nBy default, the `latestOfMany` and `oldestOfMany` methods will retrieve\nthe latest or oldest related model based on the model's primary key,\nwhich must be sortable. However, sometimes you may wish to retrieve a\nsingle model from a larger relationship using a different sorting\ncriteria.\n\nFor example, using the `ofMany` method, you may retrieve the user's most\nexpensive order. The `ofMany` method accepts the sortable column as its\nfirst argument and which aggregate function (`min` or `max`) to apply\nwhen querying for the related model:\n\n```\n/**\n * Get the user's largest order.\n */\npublic function largestOrder()\n{\n    return \\$this->hasOne(Order::class)->ofMany('price', 'max');\n}\n```\n\n> **Warning** Because PostgreSQL does not support executing the `MAX`\n> function against UUID columns, it is not currently possible to use\n> one-of-many relationships in combination with PostgreSQL UUID columns.\n\n"], "note": ""}, {"id": 81, "message": "Regarding the following source code:\n```\n$collection = collect([\n    ['name' => '<PERSON>', 'score' => 50],\n    ['name' => '<PERSON>', 'score' => 75],\n    ['name' => '<PERSON>', 'score' => 80],\n    ['name' => '<PERSON>', 'score' => 60],\n]);\n\n$sorted = $collection->sortBy(/* sorting logic here */);\n```\nHow can you sort this collection by 'name' ascending and 'score' descending using closures to define custom sort operations?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["laravel~9"], "answer": "You can use the `sortBy` method and pass an array of closures that define each sort operation. For example:\n\n```php\n$sorted = $collection->sortBy([\n    fn($a, $b) => $a['name'] <=> $b['name'],\n    fn($a, $b) => $b['score'] <=> $a['score'],\n]);\n```", "context": ["collections\n# Collections\n## Method Listing\n#### `sortBy()`\n\nWhen sorting a collection by multiple attributes, you may also provide\nclosures that define each sort operation:\n\n```\n\\$collection = collect([\n    ['name' => '<PERSON>', 'age' => 34],\n    ['name' => '<PERSON>', 'age' => 30],\n    ['name' => '<PERSON>', 'age' => 36],\n    ['name' => '<PERSON>', 'age' => 32],\n]);\n\n\\$sorted = \\$collection->sortBy([\n    fn (\\$a, \\$b) => \\$a['name'] <=> \\$b['name'],\n    fn (\\$a, \\$b) => \\$b['age'] <=> \\$a['age'],\n]);\n\n\\$sorted->values()->all();\n\n/*\n    [\n        ['name' => '<PERSON>', 'age' => 32],\n        ['name' => '<PERSON>', 'age' => 30],\n        ['name' => '<PERSON> Otwell', 'age' => 36],\n        ['name' => '<PERSON>', 'age' => 34],\n    ]\n*/\n```\n\n\n#### `sortByDesc()`\n\nThis method has the same signature as the `sortBy` method, but will sort\nthe collection in the opposite order.\n\n\n#### `sortDesc()`\n\nThis method will sort the collection in the opposite order as the `sort`\nmethod:\n\n```\n\\$collection = collect([5, 3, 1, 2, 4]);\n\n\\$sorted = \\$collection->sortDesc();\n\n\\$sorted->values()->all();\n\n// [5, 4, 3, 2, 1]\n```\n\nUnlike `sort`, you may not pass a closure to `sortDesc`. Instead, you\nshould use the `sort` method and invert your comparison.\n\n\n#### `sortKeys()`\n\nThe `sortKeys` method sorts the collection by the keys of the underlying\nassociative array:\n\n```\n\\$collection = collect([\n    'id' => 22345,\n    'first' => 'John',\n    'last' => 'Doe',\n]);\n\n\\$sorted = \\$collection->sortKeys();\n\n\\$sorted->all();\n\n/*\n    [\n        'first' => 'John',\n        'id' => 22345,\n        'last' => 'Doe',\n    ]\n*/\n```\n\n\n#### `sortKeysDesc()`\n\nThis method has the same signature as the `sortKeys` method, but will\nsort the collection in the opposite order.\n\n\n\n"], "note": ""}, {"id": 82, "message": "Regarding the following source code:\n```\nFoo* getOrCreateFoo(const Foo<PERSON>ey &key) {\n    // Efficiently get or create a Foo object\n}\n```\nWhat data structure provided by LLVM allows efficient implementation of getOrCreateFoo without constructing a new object if it already exists, especially for complex or polymorphic objects, and how does it achieve this?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["llvm~12.0.0"], "answer": "LLVM's FoldingSet class allows efficient implementation of getOrCreateFoo by performing a query with a FoldingSetNodeID, avoiding unnecessary object construction. It supports polymorphic objects using intrusive links, provides stable pointers, and helps in uniquing expensive-to-create objects.", "context": ["FoldingSet is an aggregate class that is really good at uniquing expensive-to-create or polymorphic objects. It is a combination of a chained hash table with intrusive links (uniqued objects are required to inherit from FoldingSetNode) that uses SmallVector as part of its ID process.\n\nConsider a case where you want to implement a “getOrCreateFoo” method for a complex object (for example, a node in the code generator). The client has a description of **what** it wants to generate (it knows the opcode and all the operands), but we don’t want to ‘new’ a node, then try inserting it into a set only to find out it already exists, at which point we would have to delete it and return the node that already exists.\n\nTo support this style of client, FoldingSet perform a query with a FoldingSetNodeID (which wraps SmallVector) that can be used to describe the element that we want to query for. The query either returns the element matching the ID or it returns an opaque ID that indicates where insertion should take place. Construction of the ID usually does not require heap traffic.\n\nBecause FoldingSet uses intrusive links, it can support polymorphic objects in the set (for example, you can have SDNode instances mixed with LoadSDNodes). Because the elements are individually allocated, pointers to the elements are stable: inserting or removing elements does not invalidate any pointers to other elements."], "note": ""}, {"id": 83, "message": "What are the drawbacks of using long-term development branches in LLVM, and why does LLVM prefer incremental changes instead?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["llvm~12.0.0"], "answer": "Long-term branches can cause merge conflicts, are ignored by the community, make code review difficult, are not routinely tested, and large monolithic changes may not work until fully complete. LLVM prefers incremental development to avoid these issues, facilitate easier code reviews, continuous testing, and smoother integration.", "context": ["In the LLVM project, we do all significant changes as a series of incremental patches. We have a strong dislike for huge changes or long-term development branches. Long-term development branches have a number of drawbacks:\n\n1.  Branches must have mainline merged into them periodically. If the branch development and mainline development occur in the same pieces of code, resolving merge conflicts can take a lot of time.\n2.  Other people in the community tend to ignore work on branches.\n3.  Huge changes (produced when a branch is merged back onto mainline) are extremely difficult to code review.\n4.  Branches are not routinely tested by our nightly tester infrastructure.\n5.  Changes developed as monolithic large changes often don’t work until the entire set of changes is done. Breaking it down into a set of smaller changes increases the odds that any of the work will be committed to the main repository.\n\nTo address these problems, LLVM uses an incremental development style and we require contributors to follow this practice when making a large/invasive change. Some tips:\n\n"], "note": ""}, {"id": 84, "message": "Regarding the following source code:\n```\nMachineBasicBlock *MBB = ...;\n// How to get the LLVM BasicBlock from MBB?\n```\nIn LLVM's code generator, how can you retrieve the LLVM BasicBlock associated with a given MachineBasicBlock?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["llvm~12.0.0"], "answer": "By calling the getBasicBlock() method on the MachineBasicBlock instance, you can obtain the corresponding LLVM BasicBlock it originates from.", "context": ["The `MachineBasicBlock` class contains a list of machine instructions (\nMachineInstr instances). It roughly corresponds to the LLVM code input\nto the instruction selector, but there can be a one-to-many mapping\n(i.e. one LLVM basic block can map to multiple machine basic blocks).\nThe `MachineBasicBlock` class has a “`getBasicBlock`” method, which\nreturns the LLVM basic block that it comes from.\n\n"], "note": ""}, {"id": 85, "message": "What are the conditions under which tail call optimization and sibling call optimization can be performed in LLVM, and how do they differ?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["llvm~12.0.0"], "answer": "Tail call optimization requires specific conventions and the `-tailcallopt` option, whereas sibling call optimization is a restricted form that can occur automatically without special flags. Sibling call optimization on x86/x86-64 can be performed when the caller and callee use the same calling convention (`c` or `fastcc`), the call is in tail position, return types match or the callee's result is unused, and stack-passed arguments are in the same frame offsets.", "context": ["Tail calls can onlybe optimized when this, the GHC or the HiPE convention isused.\n# The LLVM Target-Independent Code Generator¶\n## ¶\n### ¶\nSibling call optimization is a restricted form of tail call\noptimization. Unlike tail call optimization described in the previous\nsection, it can be performed automatically on any tail calls when\n`-tailcallopt` option is not specified.\n\nSibling call optimization is currently performed on x86/x86-64 when the\nfollowing constraints are met:\n\n- Caller and callee have the same calling convention. It can be either\n  `c` or `fastcc`.\n- The call is a tail call - in tail position (ret immediately follows\n  call and ret uses value of call or is void).\n- Caller and callee have matching return type or the callee result is\n  not used.\n- If any of the callee arguments are being passed in stack, they must be\n  available in caller’s own incoming argument stack and the frame\noffsets must be the same.\n\nExample:\n\n<div class=\"highlight-llvm notranslate\">\n\n    declare i32 @bar(i32, i32)\n\n    define i32 @foo(i32 %a, i32 %b, i32 %c) {\n    entry:\n      %0 = tail call i32 @bar(i32 %a, i32 %b)\n      ret i32 %0\n    }\n\n</div>\n\n"], "note": ""}, {"id": 86, "message": "Regarding the following source code:\n```\n; CHECK: load r[[#REG:]], [r0]\n; CHECK: load r[[#REG+1]], [r1]\n; CHECK: Loading from 0x[[#%x,ADDR:]]\n; CHECK-SAME: to 0x[[#ADDR + 7]]\n```\nIn an LLVM test using FileCheck, how can you use numeric expressions to verify that consecutive registers are used and specific address calculations are correct in the test output?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["llvm~12.0.0"], "answer": "By defining numeric variables and using expressions like [[#REG+1]] and [[#ADDR + 7]], FileCheck allows you to verify that registers and addresses follow specific numeric patterns, ensuring that consecutive registers are used and address calculations in the output are correct.", "context": ["The supported operators are:\n\n> <div>\n>\n> - `+` - Returns the sum of its two operands.\n> - `-` - Returns the difference of its two operands.\n>\n> </div>\n\nThe syntax of a function call is `<name>(<arguments>)` where:\n\n- `name` is a predefined string literal. Accepted values are:\n  - add - Returns the sum of its two operands.\n  - div - Returns the quotient of its two operands.\n  - max - Returns the largest of its two operands.\n  - min - Returns the smallest of its two operands.\n  - mul - Returns the product of its two operands.\n  - sub - Returns the difference of its two operands.\n- `<arguments>` is a comma separated list of expressions.\n\nFor example:\n\n<div class=\"highlight-llvm notranslate\">\n\n    ; CHECK: load r[[#REG:]], [r0]\n    ; CHECK: load r[[#REG+1]], [r1]\n    ; CHECK: Loading from 0x[[#%x,ADDR:]]\n    ; CHECK-SAME: to 0x[[#ADDR + 7]]\n\n</div>\n\nThe above example would match the text:\n\n<div class=\"highlight-gas notranslate\">\n\n    load r5, [r0]\n    load r6, [r1]\n    Loading from 0xa0463440 to 0xa0463447\n\n</div>\n\n"], "note": ""}, {"id": 87, "message": "Regarding the following source code:\n```\nCREATE OR REPLACE TABLE my_table (...) ENGINE=InnoDB;\n```\nIf you execute the above statement and the server crashes during the CREATE TABLE operation, what happens to the original 'my_table' in MariaDB when using InnoDB with file-per-table tablespaces?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["ma<PERSON>b"], "answer": "The original 'my_table' will be dropped even if the new table was not successfully created. During 'CREATE OR REPLACE TABLE', MariaDB first drops the existing table before attempting to create the new one. If a crash occurs during 'CREATE TABLE', the original table remains dropped.", "context": ["# Atomic DDL\n## Which DDL Operations are Now Crash Safe\n### CREATE OR <PERSON><PERSON><PERSON>CE TABLE\n\nCREATE OR REPLACE TABLE foo is implemented as:\n\n``` fixed\nDROP TABLE IF EXISTS foo;\nCREATE TABLE foo ...\n```\n\nThis means that if there is a crash during `CREATE TABLE` then the original table 'foo' will be dropped even if the new table was not created. If the table was not re-created, the binary log will contain the `DROP TABLE`.", "# InnoDB File-Per-Table Tablespaces\nWhen you create a table using the InnoDB storage engine, data written to that table is stored on the file system in a data file called a tablespace. Tablespace files contain both the data and indexes.\n\nWhen innodb_file_per_table=ON is set, InnoDB uses one tablespace file per InnoDB table. These tablespace files have the `.ibd` extension. When innodb_file_per_table=OFF is set, InnoDB stores all tables in the InnoDB system tablespace."], "note": ""}, {"id": 88, "message": "Regarding the following source code:\n```\nSELECT name, MEDIAN(score) OVER (PARTITION BY name) FROM exam_scores;\n```\nHow is the MEDIAN() window function in MariaDB equivalent to using PERCENTILE_CONT, and how can you write the equivalent expression?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["ma<PERSON>b"], "answer": "In MariaDB, MEDIAN() is equivalent to using PERCENTILE_CONT(0.5) WITHIN GROUP. The equivalent expression is: PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY score) OVER (PARTITION BY name).", "context": ["# MEDIAN\n\n## Syntax\n\n``` fixed\nMEDIAN(median expression) OVER (\n  [ PARTITION BY partition_expression ] \n)\n```\n\n## Description\n\nMEDIAN() is a window function that returns the median value of a range of values.\n\nIt is a specific case of PERCENTILE_CONT, with an argument of 0.5 and the ORDER BY column the one in `MEDIAN`'s argument.\n\n``` fixed\nMEDIAN(<median-arg>) OVER ( [ PARTITION BY partition_expression] )\n```\n\nIs equivalent to:\n\n``` fixed\nPERCENTILE_CONT(0.5) WITHIN \n  GROUP (ORDER BY <median-arg>) OVER ( [ PARTITION BY partition_expression ])\n```\n"], "note": ""}, {"id": 89, "message": "In MariaDB, does the expression 'NULL AND x()' utilize short-circuit evaluation to avoid evaluating 'x()'?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["ma<PERSON>b"], "answer": "No, in MariaDB, the expression 'NULL AND x()' does not use short-circuit evaluation. Even if the first operand is NULL, 'x()' will still be evaluated.", "context": ["# Operator Precedence\n## Short-circuit evaluation\n\nThe `AND`, `OR`, `&&` and `||` operators support short-circuit evaluation. This means that, in some cases, the expression on the right of those operators is not evaluated, because its result cannot affect the result. In the following cases, short-circuit evaluation is used and `x()` is not evaluated:\n\n- `FALSE AND x()`\n- `FALSE && x()`\n- `TRUE OR x()`\n- `TRUE || x()`\n- `NULL BETWEEN x() AND x()`\n\nNote however that the short-circuit evaluation does *not* apply to `NULL AND x()`. Also, `BETWEEN`'s right operands are not evaluated if the left operand is `NULL`, but in all other cases all the operands are evaluated.\n\nThis is a speed optimization. Also, since functions can have side-effects, this behavior can be used to choose whether execute them or not using a concise syntax:\n\n``` fixed\nSELECT some_function() OR log_error();\n```\n"], "note": ""}, {"id": 90, "message": "Regarding the following source code:\n```\nSELECT * FROM large_table WHERE condition LIMIT 10 ROWS EXAMINED 1000;\n```\nDoes the 'LIMIT ROWS EXAMINED' clause in MariaDB affect query optimization, and in what situations is it ignored?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["ma<PERSON>b"], "answer": "No, the 'LIMIT ROWS EXAMINED' clause does not affect query optimization in MariaDB. It is only considered during query execution and is ignored during query optimization, when explaining queries, and during auxiliary operations.", "context": ["# LIMIT ROWS EXAMINED\n## Syntax\n\n``` fixed\nSELECT ... FROM ... WHERE ...\n[group_clause] [order_clause]\nLIMIT [[offset,] row_count] ROWS EXAMINED rows_limit;\n```\n\nSimilar to the parameters of `LIMIT`, *`rows_limit`* can be both a prepared statement parameter, or a stored program parameter.\n\n## Description\n\nThe purpose of this optimization is to provide the means to terminate the execution of `SELECT` statements which examine too many rows, and thus use too many resources. This is achieved through an extension of the `LIMIT` clause — `LIMIT ROWS EXAMINED number_of_rows `. Whenever possible the semantics of `LIMIT ROWS EXAMINED` is the same as that of normal `LIMIT` (for instance for aggregate functions).\n\nThe `LIMIT ROWS EXAMINED` clause is taken into account by the query engine only during query execution. Thus the clause is ignored in the following cases:\n\n- If a query is `EXPLAIN`-ed.\n- During query optimization.\n- During auxiliary operations such as writing to system tables (e.g. logs).\n\nThe clause is not applicable to `DELETE` or `UPDATE` statements, and if used in those statements produces a syntax error.\n"], "note": ""}, {"id": 91, "message": "Regarding the following source code:\n```\nSELECT INET6_NTOA(UNHEX('20010DB8000000000000000000000001'));\n```\nHow can you convert a binary IPv6 address to its textual representation in MariaDB, and what is the maximum length of the returned string?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["ma<PERSON>b"], "answer": "You can use the INET6_NTOA(expr) function to convert a binary IPv6 address to its textual representation in MariaDB. The maximum length of the returned string is 39 characters.", "context": ["# INET6_NTOA\n\n## Syntax\n\n``` fixed\nINET6_NTOA(expr)\n```\n\n## Description\n\nGiven an IPv6 or IPv4 network address as a numeric binary string, returns the address as a nonbinary string in the connection character set.\n\nThe return string is lowercase, and is platform independent, since it does not use functions specific to the operating system. It has a maximum length of 39 characters.\n\nReturns NULL if the argument is not understood.\n\n## Examples\n\n``` fixed\nSELECT INET6_NTOA(UNHEX('0A000101'));\n+-------------------------------+\n| INET6_NTOA(UNHEX('0A000101')) |\n+-------------------------------+\n| ********                      |\n+-------------------------------+\n\nSELECT INET6_NTOA(UNHEX('48F3000000000000D4321431BA23846F'));\n+-------------------------------------------------------+\n| INET6_NTOA(UNHEX('48F3000000000000D4321431BA23846F')) |\n+-------------------------------------------------------+\n| 48f3::d432:1431:ba23:846f                             |\n+-------------------------------------------------------+\n```\n\n## See Also\n\n- INET6_ATON()\n- INET_NTOA()\n"], "note": ""}, {"id": 92, "message": "How do you format a batch file in JSONL format for batch processing with the OpenAI API, and what unique identifier should you include for each request?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["openai_cookbook~20240909"], "answer": "You should format the batch file in JSONL where each line is a JSON object representing a request. Each request must include a unique \"custom_id\" to identify it, as responses may not be returned in order.", "context": ["### Creating the batch file\nThe batch file, in the `jsonl` format, should contain one line (json object) per request. Each request is defined as such:\n\n```\n{\n    \"custom_id\": <REQUEST_ID>,\n    \"method\": \"POST\",\n    \"url\": \"/v1/chat/completions\",\n    \"body\": {\n        \"model\": <MODEL>,\n        \"messages\": <MESSAGES>,\n        // other parameters\n    }\n}\n```\n\nNote: the request ID should be unique per batch. This is what you can use to match results to the initial input files, as requests will not be returned in the same order.\n"], "note": ""}, {"id": 93, "message": "Regarding the following source code:\n```\ndef search_functions(data, code_query, top_n=5):\n    # Function implementation here\n```\nIn the context of searching code repositories using embeddings, how does the 'search_functions' method utilize cosine similarity to find relevant code snippets based on a natural language query?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["openai_cookbook~20240909"], "answer": "The 'search_functions' method embeds the natural language query and computes the cosine similarity between this embedding and the embeddings of code snippets in the database. It then sorts the code snippets based on similarity scores to find and return the most relevant ones.", "context": ["### Testing\nLet's test our endpoint with some simple queries. If you're familiar with the `openai-python` repository, you'll see that we're able to easily find functions we're looking for only a simple English description.\n\nWe define a search_functions method that takes our data that contains our embeddings, a query string, and some other configuration options. The process of searching our database works like such:\n\n1.  We first embed our query string (code_query) with\n    `text-embedding-3-small`. The reasoning here is that a query string\n    like 'a function that reverses a string' and a function like 'def\n    reverse(string): return string[::-1]' will be very similar when\n    embedded.\n2.  We then calculate the cosine similarity between our query string\n    embedding and all data points in our database. This gives a distance\n    between each point and our query.\n3.  We finally sort all of our data points by their distance to our\n    query string and return the number of results requested in the\n    function parameters.\n"], "note": ""}, {"id": 94, "message": "What steps are necessary to connect to a Weaviate instance and test the client connection when preparing for hybrid search with OpenAI?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["openai_cookbook~20240909"], "answer": "To connect to a Weaviate instance, first ensure the 'OPENAI_API_KEY' environment variable is set. Then, use this API key to configure and initialize the Weaviate client. Finally, test the client connection to confirm it's properly set up for subsequent operations.", "context": ["## Connect to your Weaviate instance\nIn this section, we will:\n\n1.  test env variable `OPENAI_API_KEY` – **make sure** you completed the\n    step in \\#Prepare-your-OpenAI-API-key\n2.  connect to your Weaviate your `OpenAI API Key`\n3.  and test the client connection\n\n### The client\n\nAfter this step, the `client` object will be used to perform all\nWeaviate-related operations.\n"], "note": ""}, {"id": 95, "message": "When creating a vector table in Supabase to store OpenAI embeddings, why is the 'vector(1536)' data type used in the table schema?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["openai_cookbook~20240909"], "answer": "The 'vector(1536)' data type specifies that each embedding stored in the table has 1536 dimensions, matching the dimensionality of the embeddings generated by OpenAI's model. This ensures compatibility between the stored embeddings and the model's output.", "context": ["## Create a vector table\nNext we'll create a table to store documents and embeddings. In the SQL Editor, run:\n\n```\ncreate table documents (\n  id bigint primary key generated always as identity,\n  content text not null,\n  embedding vector (1536) not null\n);\n```\n\nSince Supabase is built on Postgres, we're just using regular SQL here. You can modify this table however you like to better fit your application. If you have existing database tables, you can simply add a new `vector` column to the appropriate table.\n\nThe important piece to understand is the `vector` data type, which is a new data type that became available when we enabled the pgvector extension earlier. The size of the vector (1536 here) represents the number of dimensions in the embedding. Since we're using OpenAI's `text-embedding-3-small` model in this example, we set the vector size to 1536.\n"], "note": ""}, {"id": 96, "message": "How can GPT-4's visual capabilities be used to generate tags for product images and perform image searches by comparing embeddings of generated keywords and captions?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["openai_cookbook~20240909"], "answer": "By using GPT-4's visual capabilities, you can generate descriptive tags and captions for product images. Embeddings of these tags and captions are created and used to perform image searches by comparing them with embeddings of input text or other images, enabling semantic search based on visual content.", "context": ["## Tag images\nIn this section, we'll use GPT-4o mini to generate relevant tags for our products.\n\nWe'll use a simple zero-shot approach to extract keywords, and deduplicate those keywords using embeddings to avoid having multiple keywords that are too similar.\n\nWe will use a combination of an image and the product title to avoid extracting keywords for other items that are depicted in the image - sometimes there are multiple items used in the scene and we want to focus on just the one we want to tag.\n", "## Image search\nIn this section, we will use generated keywords and captions to search items that match a given input, either text or image.\n\nWe will leverage our embeddings model to generate embeddings for the keywords and captions and compare them to either input text or the generated caption from an input image.\n"], "note": ""}, {"id": 97, "message": "Regarding the following source code:\n```\nt = Thread.new { raise 'Error in thread' }\n```\nGiven the code snippet, how can you ensure that the exception raised inside the thread is automatically propagated to the main thread?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["ruby~3.3"], "answer": "By setting `Thread.abort_on_exception = true` before starting the thread, the unhandled exception will automatically be re-raised in the main thread.", "context": ["# class Thread\n### `Exception` handling\n\nWhen an unhandled exception is raised inside a thread, it will\nterminate. By default, this exception will not propagate to other\nthreads. The exception is stored and when another thread calls `value`\nor `join`, the exception will be re-raised in that thread.\n\n``` ruby\nt = Thread.new{ raise 'something went wrong' }\nt.value #=> RuntimeError: something went wrong\n```\n\nAn exception can be raised from outside the thread using the\n`Thread#raise` instance method, which takes the same parameters as\n`Kernel#raise`.\n\nSetting `Thread.abort_on_exception` = true, `Thread#abort_on_exception`\n= true, or \\$DEBUG = true will cause a subsequent unhandled exception\nraised in a thread to be automatically re-raised in the main thread.\n\nWith the addition of the class method `::handle_interrupt`, you can now\nhandle exceptions asynchronously with threads."], "note": ""}, {"id": 98, "message": "Regarding the following source code:\n```\ndata = \"\\xFF\\xFF\\xFF\"\nencoded = Base64.urlsafe_encode64(data)\n```\nHow can you modify the code so that the Base64-encoded string does not contain padding characters?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["ruby~3.3"], "answer": "By passing `padding: false` to `urlsafe_encode64`, like `Base64.urlsafe_encode64(data, padding: false)`, you can omit the padding characters.", "context": ["# module Base64\n<div class=\"method-detail\">\n\n<div class=\"method-header\">\n\n<div id=\"method-i-urlsafe_encode64\" class=\"method-heading\">\n\n<span class=\"method-name\">urlsafe_encode64</span><span\nclass=\"method-args\">(bin, padding: true)</span> Show source\n\n</div>\n\n</div>\n\n<div class=\"method-description\">\n\n<div id=\"urlsafe_encode64-source\" class=\"method-source-code\">\n\n``` ruby\n# File lib/base64.rb, line 328\ndef urlsafe_encode64(bin, padding: true)\n  str = strict_encode64(bin)\n  str.chomp!(\"==\") or str.chomp!(\"=\") unless padding\n  str.tr!(\"+/\", \"-_\")\n  str\nend\n```\n\n</div>\n\nReturns the RFC-4648-compliant Base64-encoding of `bin`.\n\nPer RFC 4648, the returned string will not contain the URL-unsafe\ncharacters `+` or `/`, but instead may contain the URL-safe characters\n`-` and `_`; see Encoding Character Set above:\n\n``` ruby\nBase64.urlsafe_encode64(\"\\xFB\\xEF\\xBE\") # => \"----\"\nBase64.urlsafe_encode64(\"\\xFF\\xFF\\xFF\") # => \"____\"\n```\n\nBy default, the returned string may have padding; see Padding, above:\n\n``` ruby\nBase64.urlsafe_encode64('*') # => \"Kg==\"\n```\n\nOptionally, you can suppress padding:\n\n``` ruby\nBase64.urlsafe_encode64('*', padding: false) # => \"Kg\"\n```\n\nThe returned string will have no newline characters, regardless of its\nlength; see Newlines above:\n\n``` ruby\nBase64.urlsafe_encode64('*') # => \"Kg==\"\nBase64.urlsafe_encode64('*' * 46)\n# => \"KioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKg==\"\n```\n\n</div>\n\n</div>"], "note": ""}, {"id": 99, "message": "Regarding the following source code:\n```\nip = IPAddr.new(\"2001:db8::1\")\nnetwork_order = ip.hton\n```\nWhat does the `network_order` variable contain after executing the code, and how is it obtained?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["ruby~3.3"], "answer": "It contains the IPv6 address represented as a binary string in network byte order, obtained by calling the `hton` method on the IPAddr object.", "context": ["# class IPAddr\n<div class=\"method-header\">\n\n<div id=\"method-i-hton\" class=\"method-heading\">\n\n<span class=\"method-name\">hton</span><span class=\"method-args\">()</span>\nShow source\n\n</div>\n\n</div>\n\n<div class=\"method-description\">\n\n<div id=\"hton-source\" class=\"method-source-code\">\n\n``` ruby\n# File lib/ipaddr.rb, line 226\ndef hton\n  case @family\n  when Socket::AF_INET\n    return [@addr].pack('N')\n  when Socket::AF_INET6\n    return (0..7).map { |i|\n      (@addr >> (112 - 16 * i)) & 0xffff\n    }.pack('n8')\n  else\n    raise AddressFamilyError, \"unsupported address family\"\n  end\nend\n```\n\n</div>\n\nReturns a network byte ordered string form of the IP address.\n\n</div>\n\n"], "note": ""}, {"id": 100, "message": "Regarding the following source code:\n```\nmodule Mod\n  # shareable_constant_value: literal\n  A = [1, 2, 3]\n  module Sub\n    B = [4, 5]\n  end\nend\nC = [6, 7, 8]\n```\nConsidering the code snippet, which of the constants `A`, `B`, and `C` are frozen, and why?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["ruby~3.3"], "answer": "Constant `A` is frozen because the directive affects subsequent constants in its scope. Constants `B` and `C` are not frozen because the directive does not affect nested modules or constants outside its scope.", "context": ["comments_rdoc\n# Code Comments\n## Magic Comments\n### `shareable_constant_value` Directive\n#### Scope\nThis directive can be used multiple times in the same file:\n\n``` ruby\n# shareable_constant_value: none\nA = {foo: []}\nA.frozen? # => false\nRactor.new { puts A } # => can not access non-shareable objects by non-main Ractor.\n\n# shareable_constant_value: literal\nB = {foo: []}\nB.frozen? # => true\nB[:foo].frozen? # => true\n\nC = [Object.new] # => cannot assign unshareable object to C (Ractor::IsolationError)\n\nD = [Object.new.freeze]\nD.frozen? # => true\n\n# shareable_constant_value: experimental_everything\nE = Set[1, 2, Object.new]\nE.frozen? # => true\nE.all?(&:frozen?) # => true\n```\n\nThe directive affects only subsequent constants and only for the current\nscope:\n\n``` ruby\nmodule Mod\n  # shareable_constant_value: literal\n  A = [1, 2, 3]\n  module Sub\n    B = [4, 5]\n  end\nend\n\nC = [4, 5]\n\nmodule Mod\n  D = [6]\nend\np Mod::A.frozen?, Mod::Sub::B.frozen? # => true, true\np C.frozen?, Mod::D.frozen? # => false, false\n```\n\n"], "note": ""}, {"id": 101, "message": "Regarding the following source code:\n```\nparser = URI::RFC2396_Parser.new\nstr = \"Visit http://example.com and https://secure.example.com\"\n```\nHow can you extract all HTTPS URLs from the given string using `URI::RFC2396_Parser`?", "path": "", "content": "", "selected_code": "", "original_rid": "", "docsets": ["ruby~3.3"], "answer": "By using `parser.extract(str, ['https'])`, which extracts all URLs with the 'https' scheme from the string.", "context": ["rfc2396_parser\n# class URI::RFC2396_Parser\n<div class=\"method-header\">\n\n<div id=\"method-i-make_regexp\" class=\"method-heading\">\n\n<span class=\"method-name\">make_regexp</span><span\nclass=\"method-args\">(schemes = nil)</span> Show source\n\n</div>\n\n</div>\n\n<div class=\"method-description\">\n\n<div id=\"make_regexp-source\" class=\"method-source-code\">\n\n``` ruby\n# File lib/uri/rfc2396_parser.rb, line 262\ndef make_regexp(schemes = nil)\n  unless schemes\n    @regexp[:ABS_URI_REF]\n  else\n    /(?=#{Regexp.union(*schemes)}:)#{@pattern[:X_ABS_URI]}/x\n  end\nend\n```\n\n</div>\n\nReturns `Regexp` that is default `self.regexp[:ABS_URI_REF]`, unless\n`schemes` is provided. Then it is a `Regexp.union` with\n`self.pattern[:X_ABS_URI]`.\n\n</div>\n\n</div>\n\n<div class=\"method-header\">\n\n<div id=\"method-i-extract\" class=\"method-heading\">\n\n<span class=\"method-callseq\"> extract( str ) </span> Show source\n\n</div>\n\n<div class=\"method-heading\">\n\n<span class=\"method-callseq\"> extract( str, schemes ) </span>\n\n</div>\n\n<div class=\"method-heading\">\n\n<span class=\"method-callseq\"> extract( str, schemes ) {\\|item\\| block }\n</span>\n\n</div>\n\n</div>\n\n<div class=\"method-description\">\n\n<div id=\"extract-source\" class=\"method-source-code\">\n\n``` ruby\n# File lib/uri/rfc2396_parser.rb, line 249\ndef extract(str, schemes = nil)\n  if block_given?\n    str.scan(make_regexp(schemes)) { yield $& }\n    nil\n  else\n    result = []\n    str.scan(make_regexp(schemes)) { result.push $& }\n    result\n  end\nend\n```\n\n</div>\n\n## Args\n\n`str`  \n`String` to search\n\n`schemes`  \nPatterns to apply to `str`\n\n## Description\n\nAttempts to parse and merge a set of URIs. If no `block` given, then\nreturns the result, else it calls `block` for each element in result.\n\nSee also `URI::Parser.make_regexp`.\n\n</div>\n\n"], "note": ""}]