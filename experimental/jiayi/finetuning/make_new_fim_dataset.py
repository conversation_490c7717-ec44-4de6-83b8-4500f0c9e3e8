"""This script generates a new FIM dataset from the Stack using FimSampler."""
# pylint: disable=no-name-in-module
# %%

import json
from pathlib import Path
from typing import Sequence

from tqdm import tqdm

from research.core.utils import PickleCache
from research.fim.fim_prompt import FimPromptFormatter, FormattedFimProblem
from research.static_analysis.common import AllSupportedLanguages, LanguageID
from research.utils.data_utils import langs_tag, prob_statistics, stack_lang_name
from research.utils.generate_fim_data import (
    FimDataProcessor,
    IntRange,
)
from research.utils.token_array_utils import split_shuffle_pack_dataset

# %%
seq_length = 1024 * 8
files_per_lang = 500_000

langs_to_process: Sequence[LanguageID] = AllSupportedLanguages
to_remove_skip_pauses = False

processor = FimDataProcessor(
    FimPromptFormatter.for_star_coder, seq_len_range=IntRange(1024, seq_length)
)
stack_path = Path("/mnt/efs/augment-lga1/data/raw/the-stack-dedup.2023-02-04/data/")
dataroot = Path("/mnt/efs/augment/data/processed/")
lang_tag = langs_tag(langs_to_process)
cache_dir = dataroot / f"new-fim/starcoder-v{processor.VERSION}-{files_per_lang//1000}K"
cache = PickleCache(cache_dir)
# (temporarily) uncomment below to clear cache
# cache.clear()
lang2probs = dict[LanguageID, Sequence[FormattedFimProblem]]()

for lang in langs_to_process:
    print("Processing:", lang)
    stack_name = stack_lang_name(lang)
    problems, errors = cache.cached_call(
        f"fim_data_from_the_stack({lang})",
        processor.fim_data_from_the_stack,
        (stack_path, {stack_name: lang}, files_per_lang),
    )
    print(f"{len(problems)=}, {len(errors)=}")
    lang2probs[lang] = problems
    del errors
    del problems

all_problems = [p for probs in lang2probs.values() for p in probs]
formatter = processor.get_formatter()
dataset_dir = cache_dir / langs_tag(list(lang2probs.keys()))
dataset_dir.mkdir(exist_ok=True)

if to_remove_skip_pauses:
    all_problems = [
        formatter.remove_skip_pauses(p)
        for p in tqdm(all_problems, "remove_skip_pauses")
    ]
    dataset_dir = dataset_dir.with_name(dataset_dir.name + "-no-skips")

print(stats := prob_statistics(all_problems))
(dataset_dir / "problem_stats.txt").write_text(json.dumps(stats))

splits_dir = dataset_dir / "splits"
split_shuffle_pack_dataset(
    [p.tokens for p in all_problems],
    splits_dir,
    split_sizes={"train": 0.95, "valid": 0.05, "test": 0.05},
    seq_len=seq_length + 1,
    tokenizer=formatter.tokenizer,
)
print(f"Splits saving to: {splits_dir}")
print(stats)
