from pathlib import Path
import shutil
from typing import Literal, assert_never

from base.prompt_format_next_edit.gen_prompt_formatter import (
    EditGenFormatterConfig,
    section_budgets_10k,
)
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import (
    NEXT_EDIT_SPARK_ROOT,
    FromGitRepos,
    FromOrderedChanges,
    FromPRsV2,
    FromPRsV1,
    edit_ethanol_config,
    get_all_stage_output_paths,
    run_cached_stages,
    get_initial_stage_input_path,
    create_stage0,
    create_stage1,
    create_stage2,
    create_stage3,
    create_final_stage,
    download_raw_repo_data,
    round_up_seq_length,
)
from research.data.spark.pipelines.stages.next_edit_location_pipelines import (
    PRToRepoChangeConfig,
)
from research.next_edits.edit_gen_sampler import EditGenSampler
from research.next_edits.edit_gen_stages import (
    PromptConfig,
    RetrievalConfig,
    SamplingConfig,
)
from research.utils.inspect_indexed_dataset import print_yellow


def get_dataset_config(
    data_name: Literal["pr_v2", "pr_v1", "raw_repos", "ordered_pr_v1"],
):
    # Note that we use different sampler parameters for different datasets
    # to ensure that we reach a desired positive:negative example ratio.
    # Otherwise, we may lose too much data during the rebalancing step.
    match data_name:
        case "pr_v2":
            data_source = FromPRsV2(
                pr_data_path=Path("/mnt/efs/spark-data/shared/pr_v2/pr_grouped_10k")
            )
            synth_instruct_path = None
            sampler = EditGenSampler(
                random_edit_region_rate=0.4, random_target_file_rate=0.125
            )
        case "pr_v1":
            data_source = FromPRsV1(
                pr_data_path=Path(
                    "/mnt/efs/spark-data/shared/gh_pr_train_repartitioned"
                ),
                pr_config=PRToRepoChangeConfig(),
            )
            synth_instruct_path = Path(
                "/mnt/efs/spark-data/user/guy/data-pipeline/pr_instructions_v2"
            )
            sampler = EditGenSampler()
        case "ordered_pr_v1":
            data_source = FromOrderedChanges(
                pr_data_path=Path(
                    "/mnt/efs/spark-data/shared/next-edit/stage0/gh_pr_train_repartitioned/repo_changes"
                ),
                grouping_data_path=Path(
                    "/mnt/efs/spark-data/shared/next-edit-grouping/G1.0_hunks.lt30.v2"
                ),
            )
            synth_instruct_path = None
            sampler = EditGenSampler(
                random_edit_region_rate=0.3, random_target_file_rate=0.1
            )
        case "raw_repos":
            data_source = FromGitRepos(
                repo_list_path=download_raw_repo_data(
                    NEXT_EDIT_SPARK_ROOT / "300K_repos", 300_000
                )
            )
            synth_instruct_path = None
            sampler = EditGenSampler()
        case _:
            assert_never(data_name)
    return data_source, synth_instruct_path, sampler


def make_edit_gen_dataset(
    data_name: Literal["pr_v2", "pr_v1", "raw_repos", "ordered_pr_v1"],
    skip_retrieval: bool,
    max_workers: int,
    clear_existing: bool = False,
    print_paths_only: bool = False,
):
    """Generate the Next Edit dataset for the generation model."""
    data_source, synth_instruct_path, sampler = get_dataset_config(data_name)

    # --------------------------------------------------------
    # Begin specifying the stage parameters.

    # Params for stage 1.
    sampling_config_name = "16000p_small1000"
    sampling_config = SamplingConfig(
        sampler=sampler,
        max_problems_per_repo=16_000,
        timeout_per_repo=3600,
        max_problems_per_commit=20,
    )

    # Params for stage 2.
    retrieval_config_name = "no_retrieval" if skip_retrieval else "ethanol-K120"
    retrieval_config = RetrievalConfig(
        retriever_config=edit_ethanol_config,
        num_retrieved_chunks=120,
        timeout_per_repo=1200 if skip_retrieval else 4800,
        synthetic_instructions_path=synth_instruct_path,
        skip_dense_retrieval=skip_retrieval,
    )

    # Params for stage 3.
    prompt_config_name = "star2_seq12k_pause500_out600"
    prompt_config = PromptConfig(
        tokenizer_name="starcoder2",
        formatter_config=EditGenFormatterConfig(
            diff_context_lines=12,
            max_prompt_tokens=10_200,
            section_budgets=section_budgets_10k(),
            diff_chunk_size_chars=500,
        ),
        max_output_tokens=600,
    )

    # Params for final stage.
    positive_ratio = 0.70
    pad_to_length = round_up_seq_length(prompt_config.max_sequence_length()) + 1

    # --------------------------------------------------------
    # End specifying the stage parameters.

    stage0_input_path = get_initial_stage_input_path(data_source)
    stage_output_paths = get_all_stage_output_paths(
        data_source=data_source,
        sampling_config_str=sampling_config_name,
        retrieval_config_str=retrieval_config_name,
        prompt_config_str=prompt_config_name,
        idata_config_str=f"pos_{positive_ratio:.2f}-pad_{pad_to_length}",
        is_quicktest=False,
    )
    if print_paths_only:
        print("Exiting early since print_paths_only=True.")
        return

    if clear_existing:
        # Note that we do not clear stage 0 results since they rarely require a change
        for path in stage_output_paths[1:]:
            if path.exists():
                response = input(f"Delete existing data at: {path}?\n(y/n): ")
                if response.lower() != "y":
                    print(f"Skip clearing: {path}")
                    continue
                shutil.rmtree(path, ignore_errors=True)

    # Runs all stages
    run_cached_stages(
        stages=[
            create_stage0(
                max_workers=max_workers,
                data_source=data_source,
                is_quicktest=False,
            ),
            create_stage1(
                sampling_config,
                max_workers=max_workers,
                data_source=data_source,
            ),
            create_stage2(
                retrieval_config,
                max_workers=max_workers,
            ),
            create_stage3(prompt_config, max_workers=max_workers),
            create_final_stage(
                pad_to_length=pad_to_length,
                positive_ratio=positive_ratio,
                tokenizer_name=prompt_config.tokenizer_name,
            ),
        ],
        result_paths=[stage0_input_path, *stage_output_paths],
    )


if __name__ == "__main__":
    make_edit_gen_dataset(
        data_name="pr_v2",
        skip_retrieval=False,
        max_workers=128,
    )
