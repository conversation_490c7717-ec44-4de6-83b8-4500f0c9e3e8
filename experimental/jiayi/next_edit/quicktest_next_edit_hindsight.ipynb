#%% md
This Notebook uses in-memory blob caches to help you quickly look up the Hindsight data from recent edit history.
#%%
%load_ext autoreload
%autoreload 2
#%%
from base.datasets.hindsight_next_edit_intermediate_dataset import (
    NextEditIntermediateType,
    NextEditRawDataQueryArgs,
    NextEditIntermediateErrorDetails,
)
from experimental.jiayi.next_edit.download_hindsight_data import (
    IntermediateDataDownloader,
)
from datetime import datetime, timedelta
import pytz


california_tz = pytz.timezone("America/Los_Angeles")


all_data: list[NextEditIntermediateType]
error_collector: NextEditIntermediateErrorDetails
downloader = IntermediateDataDownloader()  # Blobs are cached inside this object
#%%
# NOTE: can no longer look up by user id
query_args = NextEditRawDataQueryArgs(
    min_request_interval=timedelta(seconds=10), user_ids=["jiayi"]
)
end_time = california_tz.localize(datetime.fromisoformat("2025-01-27T14:40:00.0"))
total_duration = timedelta(hours=3)

all_data, error_collector = downloader.download_intermediate_data(
    end_time,
    total_duration,
    future_events_duration=timedelta(hours=1),
    query_args=query_args,
)
print(error_collector.summary())
#%%
error_collector.some_edited_files_not_found
#%%
import collections
from research.next_edits.next_hunk_heuristic import (
    HeuristicStateRecorder,
    NextHunkHeuristic,
    HeuristicFailed,
)
from tqdm import tqdm

heuristic = NextHunkHeuristic()
successful_examples = list[tuple[int, HeuristicStateRecorder]]()
errors = list[tuple[int, HeuristicFailed]]()

for i, datum in enumerate(tqdm(all_data, smoothing=0)):
    analyzer = HeuristicStateRecorder(heuristic.args, datum)
    try:
        if heuristic.get_gold_edits(datum, analyzer) is not None:
            successful_examples.append((i, analyzer))
    except HeuristicFailed as e:
        errors.append((i, e))

print(f"{len(successful_examples)=}")
print(f"A total of {len(errors)} ({len(errors) / len(all_data):.1%}) examples failed.")
print("Top errors:")
counter = collections.Counter([e.category for _, e in errors])
for category, count in counter.most_common(10):
    print(f"\t{category}: {count}")
#%%
from research.utils.notebook_uis.edit_gen import render_next_hunk_heuristic_analyzer
from research.utils.notebook_uis.notebook_uis import render_item_browser


def inspect_example(i: int):
    _, analyzer = successful_examples[i]
    return render_next_hunk_heuristic_analyzer(analyzer)


render_item_browser(len(successful_examples), inspect_example, initial_item=0)