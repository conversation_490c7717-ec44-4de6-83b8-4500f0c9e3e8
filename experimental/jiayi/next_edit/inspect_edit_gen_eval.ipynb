#%%
%load_ext autoreload
%autoreload 2
#%%
from pathlib import Path

from research.eval.harness.tasks.next_edit_gen_eval_task import NextEditGenEvalInfo


def is_correct(info: NextEditGenEvalInfo) -> bool:
    return info.predicted_change == info.gold_change


eval1_name = "S24-original"
eval1_path = Path("/mnt/efs/augment/eval/jobs/A89FFS4G")
eval2_name = "S26-original"
eval2_path = Path("/mnt/efs/augment/eval/jobs/3Xcrmtdp")
eval1_info_list = [
    NextEditGenEvalInfo.load_from_dir(path)
    for path in sorted(eval1_path.glob("000__no_instruct_problem_*"))
]
eval2_info_list = [
    NextEditGenEvalInfo.load_from_dir(path)
    for path in sorted(eval2_path.glob("000__no_instruct_problem_*"))
]

eval2_gain_examples = [
    (info1, info2)
    for info1, info2 in zip(eval1_info_list, eval2_info_list)
    if not is_correct(info1) and is_correct(info2)
]
eval2_lost_examples = [
    (info1, info2)
    for info1, info2 in zip(eval1_info_list, eval2_info_list)
    if is_correct(info1) and not is_correct(info2)
]
print(f"Number of gain examples: {len(eval2_gain_examples)}")
print(f"Number of lost examples: {len(eval2_lost_examples)}")
#%%
def item_display_fn(i: int, show_gain: bool):
    if show_gain:
        info1, info2 = eval2_gain_examples[i]
    else:
        info1, info2 = eval2_lost_examples[i]
    file_names = ["prompt_tokens.txt", "output_tokens.txt"]
    print(f"{eval1_name} Files:")
    for rel_path in file_names:
        print(f"\t{Path(info1.save_path) / rel_path}")
    print(f"{eval2_name} Files:")
    for rel_path in file_names:
        print(f"\t{Path(info2.save_path) / rel_path}")
    print("~=" * 40)
    print("Diff in context:")
    diff_start_pos = info2.prompt_tokens.index("<pr_diff>")
    diff_end_pos = info2.prompt_tokens.index("<pr_base>")
    print(info2.prompt_tokens[diff_start_pos:diff_end_pos])
    print("~=" * 40)
    print("Selected code:")
    selection_begin = info2.prompt_tokens.index("[[To Replace]]")
    selection_end = info2.prompt_tokens.index("[[Begin Replacement]]")
    print(info2.prompt_tokens[selection_begin:selection_end])
    print("~=" * 40)
    print(
        f"{eval1_name} prediction (correct={info1.predicted_change == info1.gold_change}):"
    )
    print(info1.predicted_change)
    print("~=" * 40)
    print(
        f"{eval2_name} prediction (correct={info2.predicted_change == info2.gold_change}):"
    )
    print(info2.predicted_change)


item_display_fn(3, show_gain=True)
#%%
from research.eval.harness.tasks.next_edit_gen_eval_task import (
    load_edit_problems_from_next_edit_data,
)

dataset_path = "/mnt/efs/augment/data/eval/next_edits/prs.v7.jsonl.zst"
diffs_path = dataset_path.replace(".jsonl.zst", ".diffs.jsonl.zst")
files_path = dataset_path.replace(".jsonl.zst", ".files.jsonl.zst")
problems = load_edit_problems_from_next_edit_data(
    diffs_path, files_path, limit_examples=500
)
print("Total number of problems: ", len(problems))
#%%
from research.utils.notebook_uis.edit_gen import display_edit_gen_viewer

display_edit_gen_viewer(problems)