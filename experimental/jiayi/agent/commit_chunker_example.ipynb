#%%
%load_ext autoreload
%autoreload 2
#%%
from tqdm import tqdm
from experimental.jiayi.agent.commit_fetcher import get_main_branch_commits_data
from research.core.constants import AUGMENT_ROOT


n_commits_to_index = 20

# Get recent commits from main branch
recent_commits = list(
    tqdm(
        get_main_branch_commits_data(
            count=n_commits_to_index,
            # author="Jiayi Wei",
            project_root=AUGMENT_ROOT,
        ),
        smoothing=0,
        total=n_commits_to_index,
        desc="Fetching commits",
    )
)
print("Oldest commit date:", recent_commits[-1].date_str)
print("Newest commit date:", recent_commits[0].date_str)
#%%
import json
from base.blob_names.python.blob_names import get_blob_name
from experimental.jiayi.agent.commit_fetcher import CommitData
from models.retrieval.chunking import chunking


def commit_to_document(commit: CommitData) -> chunking.Document:
    json_text = json.dumps(commit.to_json(), indent=2)
    path = f"{commit.hash}.gitcommit"
    blob_name = get_blob_name(path, json_text)
    return chunking.Document(blob_name=blob_name, text=json_text, path=path)


commit_doc = commit_to_document(recent_commits[0])
#%%
from models.retrieval.chunking.commit_summary_chunker import CommitSummaryChunker
from google.genai import Client as genai_Client

# build a google genai client
region="us-central1"
project_id="augment-research-gsc"
client = genai_Client(vertexai=True, project=project_id, location=region)
chunker = CommitSummaryChunker(client=client)
chunks = list(chunker.split_into_chunks(commit_doc))
#%%
chunk = chunks[0]
print(chunk.text)
#%%
print(chunk.parent_doc.text)