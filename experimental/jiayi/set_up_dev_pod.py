"""Install common utilities dependencies for a new dev pod."""

import subprocess


def install_all():
    """Install all dependencies."""

    # bazel run -c opt //base:install
    subprocess.check_call(["bazel", "run", "-c", "opt", "//base:install"])

    # pip3 install -e .
    subprocess.check_call(["pip3", "install", "-e", "."])

    subprocess.check_call("pip install delta-spark==2.4.0 s3cmd", shell=True)
    subprocess.check_call("bash deploy/dev/dev_container/install_spark.sh", shell=True)
    subprocess.check_call(
        'echo "export SPARK_HOME=/opt/spark"  >> /home/<USER>/.bashrc', shell=True
    )
    subprocess.check_call(
        'echo "export PATH=$PATH:$SPARK_HOME/bin" >> /home/<USER>/.bashrc', shell=True
    )


if __name__ == "__main__":
    install_all()
