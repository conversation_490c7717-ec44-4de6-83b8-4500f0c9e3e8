#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to extract str_replace_editor test cases from agent chat requests.

This script:
1. Downloads chat_host_request and chat_host_response events
2. Parses str_replace_editor tool inputs from response
3. Finds latest view response for the file being edited in chat_history
4. Parses original file content
5. Creates a folder for this request using test_name
6. Puts tool inputs and original file into this folder
"""

import argparse
import json
import logging
import os
from typing import Dict, List, Any, Optional, Tuple

from base.datasets.gcs_client import GCSRequestInsightFetcher
from services.chat_host.chat_pb2 import Exchange
from services.request_insight import request_insight_pb2

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Default tenant name
DOGFOOD_SHARD = "dogfood-shard"

# Output directory for test cases
TEST_DATA_DIR = "clients/sidecar/libs/src/tools/sidecar-tools/str-replace-editor-tool/__tests__/test_data"


def get_request_events(
    request_id: str, tenant_name: str = DOGFOOD_SHARD
) -> Tuple[request_insight_pb2.RIChatRequest, request_insight_pb2.RIChatResponse, Any]:
    """
    Fetch chat_host_request and chat_host_response events for a request.

    Args:
        request_id: The request ID to fetch events for
        tenant_name: The tenant name to query for

    Returns:
        Tuple of (chat_host_request, chat_host_response) events
    """
    logger.debug(f"Fetching events for request {request_id} from {tenant_name}")

    # Get request events
    result = GCSRequestInsightFetcher.from_tenant_name(tenant_name).get_request(
        request_id=request_id,
        request_event_names=frozenset({"chat_host_request", "chat_host_response"}),
    )

    chat_request = None
    chat_response = None

    # Extract chat_host_request and chat_host_response events
    for event in result.events:
        if event.HasField("chat_host_request"):
            chat_request = event.chat_host_request
        elif event.HasField("chat_host_response"):
            chat_response = event.chat_host_response

    if not chat_request:
        raise ValueError(f"No chat_host_request found for request {request_id}")
    if not chat_response:
        raise ValueError(f"No chat_host_response found for request {request_id}")

    return chat_request, chat_response, result


def parse_tool_inputs(
    chat_response: request_insight_pb2.RIChatResponse,
    result,
) -> List[Dict[str, Any]]:
    """
    Parse str_replace_editor tool inputs from chat_host_response and tool_use_data events.

    Args:
        chat_response: The chat_host_response event
        result: The full request result containing all events

    Returns:
        List of str_replace_editor tool inputs
    """
    logger.debug(
        "Parsing str_replace_editor tool inputs from response and tool_use_data events"
    )

    tool_inputs = []

    # Debug: Print information about the response nodes
    logger.debug(f"Number of response nodes: {len(chat_response.response.nodes)}")
    for i, node in enumerate(chat_response.response.nodes):
        logger.debug(f"Node {i} type: {node.type}")
        if hasattr(node, "tool_use") and node.tool_use:
            logger.debug(
                f"Node {i} tool_name: {node.tool_use.tool_name if node.tool_use else 'None'}"
            )

    # Extract tool uses from response nodes
    for i, node in enumerate(chat_response.response.nodes):
        logger.debug(f"Processing node {i} with type {node.type}")
        # Check if the node has a tool_use attribute with str-replace-editor
        if (
            hasattr(node, "tool_use")
            and node.tool_use
            and node.tool_use.tool_name == "str-replace-editor"
        ):
            tool_use = node.tool_use
            logger.debug(f"Found str-replace-editor tool use in node {i}")
            try:
                logger.debug(
                    f"Tool input_json: {tool_use.input_json[:100] if len(tool_use.input_json) > 100 else tool_use.input_json}"
                )
                tool_input = json.loads(tool_use.input_json)
                tool_inputs.append(
                    {"tool_use_id": tool_use.tool_use_id, "input": tool_input}
                )
                logger.debug(
                    f"Found str-replace-editor tool input in response: {tool_input.get('command')} for path: {tool_input.get('path')}"
                )
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse tool input JSON: {e}")

    # Also check tool_use_data events
    for event in result.events:
        if event.HasField("tool_use_data"):
            tool_use_data = event.tool_use_data
            if tool_use_data.tool_name == "str-replace-editor":
                try:
                    tool_input = json.loads(tool_use_data.tool_input)
                    if tool_input.get("command") == "str_replace":
                        tool_inputs.append(
                            {
                                "tool_use_id": tool_use_data.tool_use_id,
                                "input": tool_input,
                            }
                        )
                        logger.debug(
                            f"Found str-replace-editor tool input in tool_use_data: {tool_input.get('command')} for path: {tool_input.get('path')}"
                        )
                except json.JSONDecodeError:
                    logger.warning(
                        f"Failed to parse tool input JSON from tool_use_data: {tool_use_data.tool_input}"
                    )

    return tool_inputs


def find_latest_view_response(
    chat_request: request_insight_pb2.RIChatRequest, file_path: str
) -> Optional[str]:
    """
    Find the latest view response for the file being edited in chat_history.

    Args:
        chat_request: The chat_host_request event
        file_path: The path of the file being edited

    Returns:
        The original file content or None if not found
    """
    logger.debug(f"Finding latest view response for file: {file_path}")

    # Extract chat history from request
    chat_history = chat_request.request.chat_history
    chat_history.append(
        Exchange(
            request_message=chat_request.request.message,
            request_nodes=chat_request.request.nodes,
            response_text="",
        )
    )

    # Iterate through chat history in reverse order to find the latest view response
    for exchange in reversed(chat_history):
        # Check response_nodes
        for node in exchange.response_nodes:
            if node.type == 3:  # TOOL_USE type
                tool_use = node.tool_use
                if tool_use.tool_name == "str-replace-editor":
                    try:
                        tool_input = json.loads(tool_use.input_json)
                        if (
                            tool_input.get("command") == "view"
                            and tool_input.get("path") == file_path
                        ):
                            # Found a view response for the file
                            logger.debug(
                                f"Found view response for file: {file_path} in response_nodes"
                            )
                            return node.content
                    except json.JSONDecodeError:
                        continue

        # Check request_nodes for tool_result_node
        for node in exchange.request_nodes:
            if hasattr(node, "tool_result_node") and node.tool_result_node:
                content = node.tool_result_node.content
                # Check if this is a view response for the file we're looking for
                expected_header = (
                    f"Here's the result of running `cat -n` on {file_path}:"
                )
                if content and content.startswith(expected_header):
                    logger.debug(
                        f"Found view response for file: {file_path} in request_nodes"
                    )
                    # Extract just the file content without the header and line numbers
                    lines = content.splitlines()
                    if lines:
                        # Skip the header line
                        file_content = "\n".join(lines[1:])
                        return file_content

    logger.warning(f"No view response found for file: {file_path}")
    return None


def parse_original_content(view_response: Optional[str]) -> str:
    """
    Parse original file content from view response.

    Args:
        view_response: The view response content, can be None

    Returns:
        The original file content
    """
    logger.debug("Parsing original file content from view response")

    # The view response typically contains line numbers followed by content
    # We need to strip the line numbers and reconstruct the original content

    if not view_response:
        return ""

    # Split the response into lines
    lines = view_response.splitlines()

    # Skip the header line that starts with "Here's the result of running `cat -n`"
    if lines and lines[0].startswith("Here's the result of running `cat -n`"):
        lines = lines[1:]

    # Remove the footer line that shows "Total lines in file: X"
    if lines and lines[-1].startswith("Total lines in file:"):
        lines = lines[:-1]

    result_lines = []
    for line in lines:
        # For empty lines with just a line number, add an empty line
        if (
            line.strip()
            and line.strip()[0].isdigit()
            and len(line.strip().split()) == 1
        ):
            result_lines.append("")
            continue

        # The format is typically "     1\t" where the line number is followed by a tab
        # Split by tab character
        parts = line.split("\t", 1)
        if len(parts) > 1:
            # Skip the line number part
            result_lines.append(parts[1])

    return "\n".join(result_lines)


def create_test_data(
    test_name: str, tool_input: Dict[str, Any], original_content: Optional[str] = None
) -> None:
    """
    Create test data folder and save files.

    Args:
        test_name: The name of the test
        tool_input: The str_replace_editor tool input
        original_content: The original file content, can be None
    """
    logger.debug(f"Creating test data for test: {test_name}")

    # Create test directory
    test_dir = os.path.join(TEST_DATA_DIR, test_name)
    os.makedirs(test_dir, exist_ok=True)

    # Save tool inputs
    tool_inputs_path = os.path.join(test_dir, "tool_inputs.json")
    with open(tool_inputs_path, "w") as f:
        json.dump(tool_input, f, indent=2)

    # If original content is empty, try to read from filesystem
    file_path = tool_input.get("path")
    if not original_content and file_path:
        try:
            logger.debug(
                f"Trying to read original content from filesystem: {file_path}"
            )
            with open(file_path, "r") as f:
                original_content = f.read()
            logger.debug("Successfully read original content from filesystem")
        except Exception as e:
            logger.warning(f"Failed to read original content from filesystem: {e}")

    # Save original file content
    original_path = os.path.join(test_dir, "original.txt")
    with open(original_path, "w") as f:
        f.write(original_content or "")

    expected_path = os.path.join(test_dir, "expected.txt")
    with open(expected_path, "w") as f:
        f.write(original_content or "")

    logger.debug(f"Test data created in: {test_dir}")


def extract_tool_input_and_content(request_id: str, tenant: str = DOGFOOD_SHARD):
    """
    Extract tool_input and original_content from a request.

    Args:
        request_id: The request ID to extract data from
        tenant: The tenant name to query for (default: DOGFOOD_SHARD)

    Returns:
        Tuple of (tool_input, original_content) or (None, None) if extraction fails
    """
    try:
        # Fetch request events
        chat_request, chat_response, result = get_request_events(request_id, tenant)

        # Parse tool inputs
        tool_inputs = parse_tool_inputs(chat_response, result)

        if not tool_inputs:
            logger.error("No str_replace_editor tool inputs found in response")
            return None, None

        # Process each tool input
        for tool_data in tool_inputs:
            tool_input = tool_data["input"]

            # Only process str_replace commands
            if tool_input.get("command") != "str_replace":
                logger.debug(
                    f"Skipping non-str_replace command: {tool_input.get('command')}"
                )
                continue

            file_path = tool_input.get("path")
            if not file_path:
                logger.warning("Tool input missing path, skipping")
                continue

            # Find latest view response
            view_response = find_latest_view_response(chat_request, file_path)

            # Parse original content
            original_content = (
                parse_original_content(view_response) if view_response else ""
            )

            # We only process the first str_replace command for simplicity
            return tool_input, original_content

        # If we get here, we didn't find a valid str_replace command
        logger.warning("No valid str_replace commands found")
        return None, None

    except Exception as e:
        logger.exception(f"Error extracting tool input and content: {e}")
        return None, None


def main():
    """Main function to extract test case from request."""
    parser = argparse.ArgumentParser(
        description="Extract str_replace_editor test case from request"
    )
    parser.add_argument("request_id", help="The request ID to extract test case from")
    parser.add_argument("test_name", help="The name of the test to create")
    parser.add_argument(
        "--tenant-name",
        default=DOGFOOD_SHARD,
        help=f"The tenant name to query for (default: {DOGFOOD_SHARD})",
    )

    args = parser.parse_args()

    try:
        # Extract tool input and original content
        tool_input, original_content = extract_tool_input_and_content(
            args.request_id, args.tenant_name
        )

        if tool_input is None:
            logger.error("Failed to extract tool input and content")
            return

        # Create test data
        create_test_data(args.test_name, tool_input, original_content)

    except Exception as e:
        logger.exception(f"Error extracting test case: {e}")


if __name__ == "__main__":
    main()
