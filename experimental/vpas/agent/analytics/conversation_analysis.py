import re
from collections.abc import Callable
from dataclasses import dataclass

import dataclasses_json
import pandas as pd

from experimental.vpas.agent.analytics.conversation import AgentTurn, Conversation
from experimental.vpas.agent.analytics.str_replace_editor_tool_analysis import (
    classify_error_message as classify_str_replace_editor_error,
)


@dataclass
class TurnRow(dataclasses_json.DataClassJsonMixin):
    request_id: str
    last_request_id: str
    agent_round_index: int
    message: str | None = None
    message_type: str | None = None
    tool_name: str | None = None
    tool_command: str | None = None
    tool_use_id: str | None = None
    tool_input: str | None = None
    tool_output: str | None = None
    tool_output_is_error: bool | None = None
    next_tool_name: str | None = None
    next_tool_command: str | None = None
    next_tool_use_id: str | None = None
    next_tool_input: str | None = None
    next_tool_output: str | None = None
    next_tool_output_is_error: bool | None = None


def update_row_current_turn(row: TurnRow, turn: AgentTurn):
    if not turn.tool_call:
        return
    row.tool_name = turn.tool_call.tool_use.name
    if turn.tool_call.tool_use.name == "str-replace-editor":
        row.tool_command = f"{turn.tool_call.tool_use.input['command']}"

    if turn.tool_call.tool_result:
        row.tool_output = turn.tool_call.tool_result.content
        row.tool_output_is_error = turn.tool_call.tool_result.is_error

    row.tool_use_id = turn.tool_call.tool_use.tool_use_id
    row.tool_input = str(turn.tool_call.tool_use.input)


def update_row_next_turn(row: TurnRow, turn: AgentTurn):
    if not turn.tool_call:
        return
    row.next_tool_name = turn.tool_call.tool_use.name
    if turn.tool_call.tool_use.name == "str-replace-editor":
        row.next_tool_command = f"{turn.tool_call.tool_use.input['command']}"

    if turn.tool_call.tool_result:
        row.next_tool_output = turn.tool_call.tool_result.content
        row.next_tool_output_is_error = turn.tool_call.tool_result.is_error

    row.next_tool_use_id = turn.tool_call.tool_use.tool_use_id
    row.next_tool_input = str(turn.tool_call.tool_use.input)


@dataclass
class RegexClassifier:
    patterns: list[str]
    error_type: str

    def __call__(self, tool_output: str):
        for pattern in self.patterns:
            if re.search(pattern, tool_output, re.IGNORECASE):
                return self.error_type
        return None


ERROR_CLASSIFIERS: list[tuple[str | None, Callable[[str], str | None]]] = [
    (
        None,
        RegexClassifier(
            [r"Tool did not run. User clicked `Skip` to cancel."], "user_cancelled"
        ),
    ),
    ("str-replace-editor", classify_str_replace_editor_error),
    (
        None,
        RegexClassifier(
            [r"File not found", r"No such file or directory", r"directory not found"],
            "file_not_found",
        ),
    ),
    (
        "launch-process",
        RegexClassifier(
            [
                r"Here are the results from executing the command.\n<return-code>\n1\n</return-code>\n<output>\n\n</output>\n"
            ],
            "failed_no_output",
        ),
    ),
    (
        "save-file",
        RegexClassifier([r"File already exists"], "file_already_exists"),
    ),
    (
        "launch-process",
        RegexClassifier(
            [
                r"Cannot launch another waiting process while another waiting process is running"
            ],
            "concurrent_wait_processes",
        ),
    ),
    (
        "launch-process",
        RegexClassifier([r"(Test|Build) failed"], "test_failed"),
    ),
    (
        "launch-process",
        RegexClassifier([r"command not found"], "command_not_found"),
    ),
    (
        "launch-process",
        RegexClassifier([r"Program exited with error code"], "program_error"),
    ),
    (
        "launch-process",
        lambda tool_output: "program_error" if tool_output.count("\n") > 10 else None,
    ),
]


def classify_tool_error(
    tool_name: str, tool_output_is_error: bool | None, tool_output: str | None
) -> str | None:
    if not tool_output_is_error:
        return None
    if not tool_output:
        return "null_output"
    for tool_name_pattern, classifier in ERROR_CLASSIFIERS:
        if tool_name_pattern is None or tool_name == tool_name_pattern:
            error_type = classifier(tool_output)
            if error_type is not None:
                return error_type
    return "other"


def make_tool_calls_df(conversations: list[Conversation]) -> pd.DataFrame:
    rows = []
    for conv in conversations:
        for round_index, agent_round in enumerate(conv.agent_rounds):
            rows.append(
                TurnRow(
                    request_id=agent_round.user_message_request_id,
                    last_request_id=conv.last_request_id,
                    agent_round_index=round_index,
                    message=agent_round.user_message,
                    message_type="user_message",
                )
            )
            for i, turn in enumerate(agent_round.agent_turns):
                row = TurnRow(
                    request_id=turn.request_id,
                    last_request_id=conv.last_request_id,
                    agent_round_index=round_index,
                    message=turn.message,
                    message_type="agent_message",
                )

                update_row_current_turn(row, turn)
                if i < len(agent_round.agent_turns) - 1:
                    update_row_next_turn(row, agent_round.agent_turns[i + 1])
                rows.append(row)

    json_rows = TurnRow.schema().dump(rows, many=True)
    df = pd.DataFrame(json_rows)
    df = df.assign(
        round_id=lambda x: x.last_request_id + x.agent_round_index.astype(str)
    )
    df = df.assign(
        tool_full_name=lambda x: x[["tool_name", "tool_command"]].apply(
            lambda s: s.str.cat(sep="-") or None,
            axis=1,
        ),
        next_tool_full_name=lambda x: x[["next_tool_name", "next_tool_command"]].apply(
            lambda s: s.str.cat(sep="-") or None,
            axis=1,
        ),
    )
    df = df.assign(
        error_type=lambda x: x.apply(
            lambda row: classify_tool_error(
                row.tool_name, row.tool_output_is_error, row.tool_output
            ),
            axis=1,
        )
    )
    return df


def make_action_df(df: pd.DataFrame):
    tool_calls_df = df.dropna(subset=["tool_name"])
    invoke_df = tool_calls_df.assign(
        action=lambda x: x.tool_full_name + "-invoke",
        chars=lambda x: x.tool_input.str.len() + x.tool_name.str.len(),
    )
    response_df = tool_calls_df.assign(
        action=lambda x: x.tool_full_name
        + x.tool_output_is_error.replace({True: "-error", False: "-response"}),
        chars=lambda x: x.tool_output.str.len() + x.tool_name.str.len(),
    )
    message_df = df.dropna(subset=["message_type"]).assign(
        action=lambda x: x.message_type,
        chars=lambda x: x.message.str.len(),
    )

    action_df = pd.concat([invoke_df, response_df, message_df], axis=0)
    return action_df


def make_success_rate_df(df: pd.DataFrame, groupby: list[str], error_column: str):
    return (
        df.groupby(groupby)[error_column]
        .value_counts(dropna=False, ascending=False)
        .unstack(fill_value=0)
        .rename(columns=str)
        .rename(columns={"True": "Error", "False": "Success", "nan": "N/A"})
        .assign(
            Total=lambda x: x.sum(axis=1),
            Rate=lambda x: (x["Success"] / (x["Success"] + x["Error"])).apply(
                lambda x: f"{x:.1%}" if not pd.isna(x) else "N/A"
            ),
        )
        .sort_values(by="Total", ascending=False)
    )


def print_error_stats(df: pd.DataFrame):
    df = df[df.tool_output_is_error == True]  # noqa: E712
    print("\nCalls same tool on error:")
    print(
        make_success_rate_df(
            df.assign(different_tool=lambda x: x.tool_name != x.next_tool_name),
            ["tool_name"],
            "different_tool",
        )
    )
    print("\nDifferent tool errors:")
    print(
        make_success_rate_df(
            df[df.tool_name != df.next_tool_name],
            ["tool_name"],
            "next_tool_output_is_error",
        )
    )
    print("\nSame tool errors:")
    print(
        make_success_rate_df(
            df[df.tool_name == df.next_tool_name],
            ["tool_name"],
            "next_tool_output_is_error",
        )
    )
    print("\nNext tool call after error:")
    print(
        df.groupby(["tool_name"])["next_tool_name"].value_counts(
            dropna=False, ascending=False
        )
    )


def print_results(df: pd.DataFrame, total_processed=0, error_count=0):
    print("\n===== Conversation Analysis =====")
    num_conversations = df.last_request_id.nunique()
    print(f"Total conversations: {num_conversations}")

    if total_processed > 0:
        error_rate = (error_count / total_processed * 100) if total_processed > 0 else 0
        print(
            f"Request processing success rate: {(total_processed - error_count) / total_processed:.2%}"
        )
        print(
            f"Request processing errors: {error_count}/{total_processed} ({error_rate:.2f}%)"
        )

    num_rounds = df.round_id.nunique()
    num_turns = len(df[df["message_type"] != "user_message"])
    print(f"Total rounds: {num_rounds}")
    print(f"Total turns: {num_turns}")
    print(f"Average rounds per conversation: {num_rounds / num_conversations:.2f}")
    print(f"Average turns per round: {num_turns / num_rounds:.2f}")

    print("\nTool usage statistics:")
    df_tool_calls = df[df.tool_name.notnull()]
    convs_with_tool_calls = df_tool_calls.last_request_id.nunique()
    print(
        f"Conversations with tool calls: {convs_with_tool_calls} ({convs_with_tool_calls / num_conversations * 100:.1f}%)"
    )
    rounds_with_tool_calls = df_tool_calls.round_id.nunique()
    print(
        f"Rounds with tool calls: {rounds_with_tool_calls} ({rounds_with_tool_calls / num_rounds * 100:.1f}%)"
    )

    print("\nTool call distribution:")
    print(make_success_rate_df(df_tool_calls, ["tool_name"], "tool_output_is_error"))

    print("\nCharacter statistics per action (top-20):")
    action_df = make_action_df(df)
    avg_tool_chars = (
        action_df.groupby("action")["chars"].sum() / df.round_id.nunique()
    ).sort_values(ascending=False)
    print(avg_tool_chars.head(20))

    print("\nHow often tools are called at least once per round (top-20):")
    overall_tool_call_counts = df_tool_calls.round_id.nunique() / num_rounds
    print(f"Overall tool call rate: {overall_tool_call_counts:.2%}")
    nonnull_tool_calls = df_tool_calls[(df_tool_calls.tool_output_is_error.notnull())]
    print(
        f"At least one non-NA tool call: {nonnull_tool_calls.round_id.nunique() / num_rounds:.2%}"
    )
    successful_tool_calls = df_tool_calls[df_tool_calls.tool_output_is_error == False]  # noqa: E712
    print(
        f"At least one successful tool call: {successful_tool_calls.round_id.nunique() / num_rounds:.2%}"
    )
    tool_call_counts = action_df.groupby("action").round_id.nunique() / num_rounds
    print(tool_call_counts.sort_values(ascending=False).head(20))

    print_error_stats(df_tool_calls)
