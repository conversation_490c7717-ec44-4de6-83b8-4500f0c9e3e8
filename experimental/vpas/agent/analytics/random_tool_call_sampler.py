#!/usr/bin/env python3
"""
Random Tool Call Sampler

This script selects random tool calls from agent conversations based on configurable filters.
Supports multiple filter types that can be specified via CLI arguments.

To add a new filter:
1. Create a filter function that takes a ToolCall and returns bool
2. Add it to the FILTER_REGISTRY dictionary with a descriptive name
3. The filter will automatically be available via the --filters CLI argument

Current filters:
- large_view: File view calls that read whole files or at least 500 lines (excludes directory views)
- retrieval: Codebase-retrieval tool calls
- large_launch_process: First launch-process tool call in each conversation with more than 200 lines in tool result
- search_view: View calls that have search_query_regex parameter set
- grep: Grep-search tool calls
- launch_process_with_cwd: Launch-process tool calls that have the cwd parameter set
"""

import argparse
import logging
import random
from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Callable

from experimental.vpas.agent.analytics.conversation import Conversation, ToolCall
from experimental.vpas.agent.analytics.run_analysis import (
    fetch_conversations,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def parse_date_args(args):
    """Parse date arguments from command line args."""
    # Get current time
    now = datetime.now()

    # Parse dates
    to_date = args.to_date if args.to_date is not None else now

    # Determine from_date based on arguments
    if args.last_hours is not None:
        from_date = now - timedelta(hours=args.last_hours)
        logger.info(f"Processing data from the last {args.last_hours} hours")
    elif args.last_days is not None:
        from_date = now - timedelta(days=args.last_days)
        logger.info(f"Processing data from the last {args.last_days} days")
    elif args.from_date is not None:
        from_date = args.from_date
    else:
        # Default: process the last 1 hour
        from_date = now - timedelta(hours=1)
        logger.info("No time range specified, defaulting to the last 1 hour")

    return from_date, to_date


def large_view_filter(tool_call: ToolCall) -> bool:
    """
    Filter for large view calls.

    A qualifying view call is one that:
    1. Is a dedicated view tool
    2. Has type == 'file' (not directory)
    3. Either reads the whole file (no view_range) or reads at least 500 lines
    """
    if not tool_call.tool_use or tool_call.tool_use.name != "view":
        return False

    tool_input = tool_call.tool_use.input
    if not isinstance(tool_input, dict):
        return False

    # Check if it's a file view (not directory)
    if tool_input.get("type") != "file":
        return False

    # Check if it reads the whole file (no view_range)
    view_range = tool_input.get("view_range")
    if not view_range:
        return True

    # Check if it reads at least 500 lines
    if isinstance(view_range, list) and len(view_range) >= 2:
        start_line, end_line = view_range[0], view_range[1]
        if end_line == -1:  # Reads to end of file
            return True
        if isinstance(start_line, int) and isinstance(end_line, int):
            lines_read = end_line - start_line + 1
            return lines_read >= 500

    return False


def retrieval_filter(tool_call: ToolCall) -> bool:
    """Filter for codebase-retrieval calls."""
    return bool(tool_call.tool_use and tool_call.tool_use.name == "codebase-retrieval")


def search_view_filter(tool_call: ToolCall) -> bool:
    """
    Filter for view calls with search_query_regex parameter.

    A qualifying view call is one that:
    1. Is a view tool
    2. Has search_query_regex parameter set
    """
    if not tool_call.tool_use or tool_call.tool_use.name != "view":
        return False

    tool_input = tool_call.tool_use.input
    if not isinstance(tool_input, dict):
        return False

    # Check if search_query_regex parameter is present and not empty
    search_query_regex = tool_input.get("search_query_regex")
    return bool(search_query_regex)


def grep_filter(tool_call: ToolCall) -> bool:
    """Filter for grep-search tool calls."""
    return bool(tool_call.tool_use and tool_call.tool_use.name == "grep-search")


def launch_process_with_cwd_filter(tool_call: ToolCall) -> bool:
    """
    Filter for launch-process calls with cwd parameter set.

    A qualifying launch-process call is one that:
    1. Is a launch-process tool
    2. Has the cwd parameter set in the tool input
    """
    if not tool_call.tool_use or tool_call.tool_use.name != "launch-process":
        return False

    tool_input = tool_call.tool_use.input
    if not isinstance(tool_input, dict):
        return False

    # Check if cwd parameter is present and not empty
    cwd = tool_input.get("cwd")
    return bool(cwd)


def large_launch_process_filter(tool_call: ToolCall) -> bool:
    """
    Filter for large launch-process calls.

    A qualifying launch-process call is one that:
    1. Is a launch-process tool
    2. Has a tool result with more than 200 lines

    Note: This filter is applied with special logic in extract_qualifying_tool_calls()
    to only include the first launch-process call in each conversation.
    """
    if not tool_call.tool_use or tool_call.tool_use.name != "launch-process":
        return False

    # Check if we have a tool result
    if not tool_call.tool_result or not tool_call.tool_result.content:
        return False

    # Count the number of lines in the tool result
    lines = tool_call.tool_result.content.split("\n")
    return len(lines) > 200


# Filter registry: maps filter names to filter functions
# To add a new filter:
# 1. Create a filter function that takes a ToolCall and returns bool
# 2. Add it to this registry with a descriptive name
# 3. The filter will automatically be available via CLI
#
# Example of adding a new filter:
# def str_replace_filter(tool_call: ToolCall) -> bool:
#     """Filter for str-replace-editor calls."""
#     return bool(tool_call.tool_use and tool_call.tool_use.name == "str-replace-editor")
#
# Then add to registry:
# "str_replace": str_replace_filter,
FILTER_REGISTRY: Dict[str, Callable[[ToolCall], bool]] = {
    "large_view": large_view_filter,
    "retrieval": retrieval_filter,
    "large_launch_process": large_launch_process_filter,
    "search_view": search_view_filter,
    "grep": grep_filter,
    "launch_process_with_cwd": launch_process_with_cwd_filter,
}


def get_available_filters() -> List[str]:
    """Get list of available filter names."""
    return list(FILTER_REGISTRY.keys())


def apply_filters(tool_call: ToolCall, filter_names: List[str]) -> bool:
    """
    Apply the specified filters to a tool call.

    Returns True if the tool call passes any of the specified filters.
    """
    if not filter_names:
        return False

    for filter_name in filter_names:
        if filter_name in FILTER_REGISTRY:
            if FILTER_REGISTRY[filter_name](tool_call):
                return True

    return False


def get_tool_description(tool_call: ToolCall) -> str:
    """Get a descriptive string for a tool call."""
    if not tool_call.tool_use:
        return "unknown tool"

    tool_name = tool_call.tool_use.name
    tool_input = tool_call.tool_use.input

    if tool_name == "view" and isinstance(tool_input, dict):
        view_range = tool_input.get("view_range")
        path = tool_input.get("path", "unknown")
        search_query_regex = tool_input.get("search_query_regex")

        # Handle search_view calls
        if search_query_regex:
            # Truncate long regex patterns for readability
            if len(search_query_regex) > 50:
                search_query_regex = search_query_regex[:47] + "..."
            if view_range:
                start_line, end_line = view_range[0], view_range[1]
                if end_line == -1:
                    return f"view (search '{search_query_regex}' from line {start_line} to end): {path}"
                else:
                    return f"view (search '{search_query_regex}' in lines {start_line}-{end_line}): {path}"
            else:
                return f"view (search '{search_query_regex}'): {path}"

        # Handle regular view calls
        if not view_range:
            return f"view (whole file): {path}"
        else:
            start_line, end_line = view_range[0], view_range[1]
            if end_line == -1:
                return f"view (lines {start_line} to end): {path}"
            else:
                lines_read = end_line - start_line + 1
                return f"view ({lines_read} lines, {start_line}-{end_line}): {path}"

    elif tool_name == "codebase-retrieval" and isinstance(tool_input, dict):
        info_request = tool_input.get("information_request", "")
        # Truncate long requests for readability
        if len(info_request) > 100:
            info_request = info_request[:97] + "..."
        return f"codebase-retrieval: {info_request}"

    elif tool_name == "launch-process" and isinstance(tool_input, dict):
        command = tool_input.get("command", "")
        wait = tool_input.get("wait", False)
        cwd = tool_input.get("cwd")
        # Truncate long commands for readability
        if len(command) > 80:
            command = command[:77] + "..."

        # Include cwd in description if present
        if cwd:
            return f"launch-process (wait={wait}, cwd={cwd}): {command}"
        else:
            return f"launch-process (wait={wait}): {command}"

    elif tool_name == "grep-search" and isinstance(tool_input, dict):
        pattern = tool_input.get("pattern", "")
        path = tool_input.get("path", ".")
        # Truncate long patterns for readability
        if len(pattern) > 50:
            pattern = pattern[:47] + "..."
        return f"grep-search (pattern: '{pattern}'): {path}"

    else:
        return f"{tool_name}: {str(tool_input)[:100]}..."


def extract_qualifying_tool_calls(
    conversations: List[Conversation],
    filter_names: List[str],
) -> List[Tuple[str, str, str, str]]:
    """
    Extract qualifying tool calls from conversations based on specified filters.

    Args:
        conversations: List of conversations to process
        filter_names: List of filter names to apply

    Returns a list of tuples: (tool_use_request_id, tool_result_request_id, tool_name, description)
    """
    qualifying_calls = []

    for conversation in conversations:
        # Track if we've seen a launch-process call in this conversation
        seen_launch_process = False

        for agent_round in conversation.agent_rounds:
            for agent_turn in agent_round.agent_turns:
                if not agent_turn.tool_call:
                    continue

                tool_call = agent_turn.tool_call

                # Special handling for large_launch_process filter
                if "large_launch_process" in filter_names:
                    # Check if this is a launch-process call
                    if (
                        tool_call.tool_use
                        and tool_call.tool_use.name == "launch-process"
                    ):
                        # If we've already seen a launch-process call in this conversation,
                        # skip this one for the large_launch_process filter
                        if seen_launch_process:
                            # Apply other filters (excluding large_launch_process)
                            other_filters = [
                                f for f in filter_names if f != "large_launch_process"
                            ]
                            if other_filters and apply_filters(
                                tool_call, other_filters
                            ):
                                tool_use_request_id = tool_call.tool_use_request_id
                                tool_result_request_id = (
                                    tool_call.tool_result_request_id or "N/A"
                                )
                                tool_name = (
                                    tool_call.tool_use.name
                                    if tool_call.tool_use
                                    else "unknown"
                                )
                                description = get_tool_description(tool_call)
                                qualifying_calls.append(
                                    (
                                        tool_use_request_id,
                                        tool_result_request_id,
                                        tool_name,
                                        description,
                                    )
                                )
                            continue
                        else:
                            # This is the first launch-process call in the conversation
                            seen_launch_process = True
                            # Apply the large_launch_process filter
                            if large_launch_process_filter(tool_call):
                                tool_use_request_id = tool_call.tool_use_request_id
                                tool_result_request_id = (
                                    tool_call.tool_result_request_id or "N/A"
                                )
                                tool_name = (
                                    tool_call.tool_use.name
                                    if tool_call.tool_use
                                    else "unknown"
                                )
                                description = get_tool_description(tool_call)
                                qualifying_calls.append(
                                    (
                                        tool_use_request_id,
                                        tool_result_request_id,
                                        tool_name,
                                        description,
                                    )
                                )
                                continue
                            # If it doesn't pass the large_launch_process filter,
                            # still check other filters
                            other_filters = [
                                f for f in filter_names if f != "large_launch_process"
                            ]
                            if other_filters and apply_filters(
                                tool_call, other_filters
                            ):
                                tool_use_request_id = tool_call.tool_use_request_id
                                tool_result_request_id = (
                                    tool_call.tool_result_request_id or "N/A"
                                )
                                tool_name = (
                                    tool_call.tool_use.name
                                    if tool_call.tool_use
                                    else "unknown"
                                )
                                description = get_tool_description(tool_call)
                                qualifying_calls.append(
                                    (
                                        tool_use_request_id,
                                        tool_result_request_id,
                                        tool_name,
                                        description,
                                    )
                                )
                            continue

                # Apply regular filters for non-launch-process calls or when large_launch_process is not in filter_names
                if apply_filters(tool_call, filter_names):
                    tool_use_request_id = tool_call.tool_use_request_id
                    tool_result_request_id = tool_call.tool_result_request_id or "N/A"
                    tool_name = (
                        tool_call.tool_use.name if tool_call.tool_use else "unknown"
                    )
                    description = get_tool_description(tool_call)

                    qualifying_calls.append(
                        (
                            tool_use_request_id,
                            tool_result_request_id,
                            tool_name,
                            description,
                        )
                    )

    return qualifying_calls


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Select random tool calls based on configurable filters"
    )

    # Date range arguments (reuse from run_analysis.py)
    parser.add_argument(
        "--from-date",
        type=lambda s: datetime.fromisoformat(s),
        help="Start date in ISO format (YYYY-MM-DDTHH:MM:SS)",
    )
    parser.add_argument(
        "--to-date",
        type=lambda s: datetime.fromisoformat(s),
        help="End date in ISO format (YYYY-MM-DDTHH:MM:SS)",
    )
    parser.add_argument(
        "-lh",
        "--last-hours",
        type=int,
        help="Process data from the last N hours",
    )
    parser.add_argument(
        "-ld",
        "--last-days",
        type=int,
        help="Process data from the last N days",
    )

    # Filter arguments
    parser.add_argument(
        "--filters",
        nargs="+",
        default=["large_view", "retrieval"],
        choices=get_available_filters(),
        help=f"Filter types to apply (default: large_view retrieval). Available filters: {', '.join(get_available_filters())}",
    )

    # Sampling arguments
    parser.add_argument(
        "--count",
        type=int,
        default=30,
        help="Number of random tool calls to select (default: 30)",
    )

    # Other arguments
    parser.add_argument(
        "--tenant-name",
        type=str,
        default="dogfood-shard",
        help="Tenant name to filter by (default: dogfood-shard)",
    )
    parser.add_argument(
        "--thread-count",
        type=int,
        default=20,
        help="Number of threads to use for processing (default: 20)",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Limit the number of conversations to process (default: None)",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging",
    )
    parser.add_argument(
        "--model",
        type=str,
        default="claude",
        choices=["claude", "gemini"],
        help="Filter conversations by model name (default: claude)",
    )
    parser.add_argument(
        "--seed",
        type=int,
        default=None,
        help="Random seed for reproducible results",
    )

    return parser.parse_args()


def main():
    """Main function."""
    args = parse_args()

    # Set up logging
    if args.debug:
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)

    # Set random seed if provided
    if args.seed is not None:
        random.seed(args.seed)
        logger.info(f"Using random seed: {args.seed}")

    # Parse date arguments
    from_date, to_date = parse_date_args(args)

    logger.info(f"Fetching conversations from {from_date} to {to_date}")
    logger.info(f"Tenant: {args.tenant_name}")
    logger.info(f"Model: {args.model}")
    logger.info(f"Filters: {', '.join(args.filters)}")

    # Validate filters
    invalid_filters = [f for f in args.filters if f not in get_available_filters()]
    if invalid_filters:
        logger.error(f"Invalid filters: {', '.join(invalid_filters)}")
        logger.error(f"Available filters: {', '.join(get_available_filters())}")
        return

    # Fetch conversations
    conversations, _, _ = fetch_conversations(
        from_date=from_date,
        to_date=to_date,
        tenant_name=args.tenant_name,
        thread_count=args.thread_count,
        limit=args.limit,
        model=args.model,
    )

    if not conversations:
        logger.warning("No conversations found")
        return

    # Extract qualifying tool calls
    logger.info("Extracting qualifying tool calls...")
    qualifying_calls = extract_qualifying_tool_calls(conversations, args.filters)

    logger.info(f"Found {len(qualifying_calls)} qualifying tool calls")

    if not qualifying_calls:
        logger.warning("No qualifying tool calls found")
        return

    # Randomly sample the requested number of calls
    sample_size = min(args.count, len(qualifying_calls))
    sampled_calls = random.sample(qualifying_calls, sample_size)

    logger.info(f"Randomly selected {sample_size} tool calls")

    # Print results
    print("\n===== Random Tool Call Sample =====")
    print(f"Sample size: {sample_size} out of {len(qualifying_calls)} qualifying calls")
    print(f"Date range: {from_date} to {to_date}")
    print(f"Tenant: {args.tenant_name}")
    print(f"Model: {args.model}")
    print(f"Filters: {', '.join(args.filters)}")
    if args.seed is not None:
        print(f"Random seed: {args.seed}")
    print()

    print("Request IDs:")
    for i, (
        tool_use_request_id,
        tool_result_request_id,
        tool_name,
        description,
    ) in enumerate(sampled_calls, 1):
        print(
            f"{i:2d}. Tool Use: {tool_use_request_id} | Tool Result: {tool_result_request_id} | {tool_name}: {description}"
        )

    print()
    print("Tool Use Request IDs only (for easy copying):")
    for tool_use_request_id, _, _, _ in sampled_calls:
        print(tool_use_request_id)

    print()
    print("Tool Result Request IDs only (for easy copying):")
    for _, tool_result_request_id, _, _ in sampled_calls:
        if tool_result_request_id != "N/A":
            print(tool_result_request_id)


if __name__ == "__main__":
    main()
