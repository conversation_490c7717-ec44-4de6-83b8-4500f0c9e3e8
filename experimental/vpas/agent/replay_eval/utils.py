import json
from pathlib import Path
from typing import Any, <PERSON><PERSON>

import jsonschema

from base.prompt_format.common import ChatResultNodeType, ChatResultToolUse
from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON>ounter
from experimental.vpas.agent.replay_eval.eval_sample import EvalSample
from experimental.vpas.agent.replay_eval.model_config import ModelConfig, ModelResponse
from experimental.vpas.utils.pickle_cache import pickle_cache
from research.tools.chat_replay.replay_utils import run_model, run_model_v2

REPLAY_CACHE_DIRECTORY = "/mnt/efs/augment/user/vpas/replay_cache"


def extract_tool_calls(response: ModelResponse) -> list[ChatResultToolUse]:
    """Extract tool calls from a model response.

    Args:
        response: The model response to extract tool calls from

    Returns:
        A list of dictionaries containing tool call information
    """
    tool_calls = []

    for node in response:
        if node.type == ChatResultNodeType.TOOL_USE:
            tool_calls.append(node.tool_use)

    return tool_calls


def extract_tool_call(
    response: ModelResponse, tool_name: str
) -> Tuple[ChatResultToolUse | None, str]:
    tool_calls = extract_tool_calls(response)
    if len(tool_calls) == 0:
        return None, "No tool calls found"
    if len(tool_calls) > 1:
        return None, "Too many tool calls found"
    tool_call = tool_calls[0]
    if tool_call.name != tool_name:
        return None, f"Expected tool call: {tool_name}, found: {tool_call.name}"
    return tool_call, ""


def extract_text(response: ModelResponse) -> str:
    """Extract text from a model response.

    Args:
        response: The model response to extract text from

    Returns:
        A string containing the text
    """
    text = ""

    for node in response:
        if node.type == ChatResultNodeType.RAW_RESPONSE:
            text += node.content

    return text


def load_model_config(path: str | Path) -> ModelConfig:
    if isinstance(path, str):
        path = Path(path)

    # Check if the path exists
    if not path.exists():
        # Try appending ~/augment to the path
        home_path = Path.home() / "augment" / path
        if home_path.exists():
            path = home_path
        else:
            # Try appending current script directory + model_configs to the path
            script_dir = Path(__file__).parent
            model_configs_path = script_dir / "model_configs" / path
            if model_configs_path.exists():
                path = model_configs_path
            else:
                raise FileNotFoundError(
                    f"Could not find model config at {path}, {home_path}, or {model_configs_path}"
                )

    # Load the module
    import importlib.util
    import sys

    module_name = path.stem
    spec = importlib.util.spec_from_file_location(module_name, path)
    if spec is None or spec.loader is None:
        raise ImportError(f"Could not load module from {path}")

    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)

    # Return the model_config from the module
    if not hasattr(module, "model_config"):
        raise AttributeError(
            f"Module {module_name} does not have a 'model_config' attribute"
        )

    return module.model_config


def _load_samples_from_file(file_path: Path) -> list[EvalSample]:
    """Load samples from a single Python file.

    Args:
        file_path: Path to the Python file to load samples from

    Returns:
        List of EvalSample objects from the file
    """
    import importlib.util
    import sys

    # Generate a unique module name based on the path
    module_name = f"samples_{file_path.stem}_{hash(str(file_path))}"

    # Load the module
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    if spec is not None and spec.loader is not None:
        module = importlib.util.module_from_spec(spec)
        sys.modules[module_name] = module
        spec.loader.exec_module(module)

        # Return the samples from the module if it has a samples attribute
        if hasattr(module, "samples"):
            return module.samples

    return []


def load_samples(
    path: str,
    limit_per_category: int | None = None,
    request_ids: list[str] | None = None,
) -> list[EvalSample]:
    """Load samples from a file or directory.

    Args:
        path: Path to a Python file or directory containing Python files
             Relative to the script's eval_samples directory
        limit_per_category: Maximum number of samples per category
        request_ids: List of request IDs to filter samples by

    Returns:
        List of EvalSample objects

    Raises:
        FileNotFoundError: If the path doesn't exist
    """
    # Prefix path with current script directory + eval_samples/
    script_dir = Path(__file__).parent
    target_path = script_dir / "eval_samples" / path

    if not target_path.exists():
        raise FileNotFoundError(f"Path not found: {target_path}")

    all_samples = []

    # If path is a file, load samples only from this file
    if target_path.is_file():
        if target_path.suffix == ".py":
            all_samples.extend(_load_samples_from_file(target_path))
        else:
            raise ValueError(f"File must be a Python file (.py): {target_path}")

    # If path is a directory, load samples from all .py files in this dir and subdirs
    elif target_path.is_dir():
        # Find all .py files in the directory and its subdirectories
        for py_file in target_path.glob("**/*.py"):
            all_samples.extend(_load_samples_from_file(py_file))

    # Filter samples by request ID if specified
    if request_ids is not None and len(request_ids) > 0:
        all_samples = [
            sample for sample in all_samples if sample.request_id in request_ids
        ]
        if not all_samples:
            print(
                f"Warning: No samples found with the specified request IDs: {request_ids}"
            )

    # Apply category limit if specified
    if limit_per_category is not None and limit_per_category > 0:
        all_samples = _limit_samples_by_category(all_samples, limit_per_category)

    return all_samples


def _limit_samples_by_category(
    samples: list[EvalSample], limit: int
) -> list[EvalSample]:
    """Limit the number of samples per category.

    Args:
        samples: List of samples to limit
        limit: Maximum number of samples per category

    Returns:
        Limited list of samples
    """
    # Group samples by category
    categories = {}
    for sample in samples:
        category = sample.category or "Uncategorized"
        if category not in categories:
            categories[category] = []
        categories[category].append(sample)

    # Limit samples in each category
    limited_samples = []
    for category, category_samples in categories.items():
        limited_samples.extend(category_samples[:limit])

    return limited_samples


def extract_system_prompt(model_config) -> str:
    if hasattr(model_config, "prompt_formatter") and hasattr(
        model_config.prompt_formatter, "system_prompt_formatter"
    ):
        if hasattr(
            model_config.prompt_formatter.system_prompt_formatter,
            "parts_or_placeholders",
        ):
            return "".join(
                model_config.prompt_formatter.system_prompt_formatter.parts_or_placeholders
            )

    # If we can't find the system prompt, return a message
    return "System prompt not available for this model configuration"


def validate_tool_input_with_schema(
    tool_input: dict[str, Any], schema: dict[str, Any]
) -> Tuple[bool, str]:
    try:
        # Check if schema has required 'type' field
        if "type" not in schema:
            return False, "Invalid schema: missing 'type' field"

        jsonschema.validate(instance=tool_input, schema=schema)
        return True, ""
    except Exception as e:
        return False, f"Schema validation error: {str(e)}"


claude_token_counter = ClaudeTokenCounter()


def count_tokens(response: ModelResponse) -> int:
    total_tokens = 0
    for node in response:
        raw_text = ""
        if node.type == ChatResultNodeType.RAW_RESPONSE:
            raw_text = node.content
        elif node.type == ChatResultNodeType.TOOL_USE:
            assert node.tool_use
            raw_text = node.tool_use.name + " " + json.dumps(node.tool_use.input)
        total_tokens += claude_token_counter.count_tokens(raw_text)
    return total_tokens


@pickle_cache(REPLAY_CACHE_DIRECTORY)
def run_model_v2_with_cache(
    prompt_output,
    tool_definitions,
    base_model_version,
    prefill,
    client_type,
    tool_choice=None,
    attempt_index=0,  # added here to affect the cache key
    yield_final_parameters=False,
):
    try:
        return run_model_v2(
            prompt_output=prompt_output,
            tool_definitions=tool_definitions,
            base_model_version=base_model_version,
            prefill=prefill,
            client_type=client_type,
            tool_choice=tool_choice,
            yield_final_parameters=yield_final_parameters,
        )
    except Exception as e:
        print(f"Failed to run claude with cache: {e}")
        return None


@pickle_cache(REPLAY_CACHE_DIRECTORY)
def run_model_with_cache(
    prompt_output,
    tool_definitions,
    base_model_version,
    prefill,
    client_type,
    tool_choice=None,
    attempt_index=0,  # added here to affect the cache key
    yield_final_parameters=False,
):
    try:
        return run_model(
            prompt_output=prompt_output,
            tool_definitions=tool_definitions,
            base_model_version=base_model_version,
            prefill=prefill,
            client_type=client_type,
            tool_choice=tool_choice,
            yield_final_parameters=yield_final_parameters,
        )
    except Exception as e:
        print(f"Failed to run model with cache: {e}")
        return None


def add_line_numbers(file_content: str) -> str:
    return "\n".join(
        [f"{i:6}\t{line}" for i, line in enumerate(file_content.split("\n"), start=1)]
    )


def split_into_chunks_by_lines(text: str, chunk_size: int) -> list[str]:
    lines = text.split("\n")
    return [
        "\n".join(lines[i : i + chunk_size]) for i in range(0, len(lines), chunk_size)
    ]
