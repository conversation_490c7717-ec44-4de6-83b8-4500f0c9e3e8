import logging

from tqdm import tqdm

from experimental.vpas.agent.replay_eval.eval_output import (
    <PERSON><PERSON><PERSON>,
    <PERSON>lR<PERSON><PERSON>,
    EvalSummary,
)
from experimental.vpas.agent.replay_eval.eval_sample import EvalSample
from experimental.vpas.agent.replay_eval.model_config import ModelConfig
from experimental.vpas.agent.replay_eval.utils import (
    run_model_v2_with_cache,
)
from research.tools.chat_replay.replay_infra import (
    get_input_and_documents,
)
from research.tools.chat_replay.replay_utils import run_model_v2

logger = logging.getLogger(__name__)


class EvalHarness:
    def __init__(
        self, model_config: ModelConfig, num_attempts: int = 3, no_cache: bool = False
    ):
        self.model_config = model_config
        self.num_attempts = num_attempts
        self.no_cache = no_cache

    def run_single_sample_eval(self, sample: EvalSample) -> EvalResult:
        logger.info(f"Running eval for sample with request_id {sample.request_id}")
        chat_prompt_input, _ = get_input_and_documents(sample.request_id, sample.tenant)
        chat_prompt_input = self.model_config.preprocess_chat_prompt_input(
            chat_prompt_input
        )
        assert self.model_config.prompt_formatter is not None
        prompt_output = self.model_config.prompt_formatter.format_prompt(
            chat_prompt_input
        )
        tool_definitions = self.model_config.get_tool_definitions(
            chat_prompt_input.tool_definitions or []
        )

        if self.model_config.add_system_prompt_to_prefill:
            prefill = f"""\
<hidden_from_user>
Reminding myself my system prompt:
{prompt_output.system_prompt}
</hidden_from_user>
"""
        else:
            prefill = ""

        prefill += self.model_config.get_additional_prefill()

        prefill += sample.get_assistant_message_prefill()
        prefill = prefill.strip()

        attempts: list[Attempt] = []
        for attempt_idx in range(self.num_attempts):
            if self.no_cache:
                result = run_model_v2(
                    prompt_output,
                    tool_definitions=tool_definitions,
                    base_model_version=self.model_config.base_model,
                    prefill=prefill,
                    client_type=self.model_config.client_type,
                    tool_choice=sample.get_tool_choice(),
                    yield_final_parameters=True,
                )
            else:
                result = run_model_v2_with_cache(
                    prompt_output,
                    tool_definitions=tool_definitions,
                    base_model_version=self.model_config.base_model,
                    prefill=prefill,
                    client_type=self.model_config.client_type,
                    tool_choice=sample.get_tool_choice(),
                    attempt_index=attempt_idx,
                    yield_final_parameters=True,
                )

            if result is not None:
                response = result.response_nodes
                end_of_stream = result.end_of_stream
            else:
                response = None
                end_of_stream = None

            # evaluate using sample eval func
            sample_is_correct, sample_explanation = sample.eval_response(
                response, tool_definitions
            )
            # evaluate using model config eval func
            model_is_correct, model_explanation = self.model_config.eval_response(
                response
            )

            is_correct = sample_is_correct and model_is_correct
            if model_explanation:
                explanation = (
                    f"Sample: {sample_explanation}.\nModel: {model_explanation}"
                )
            else:
                explanation = sample_explanation

            attempts.append(
                Attempt(
                    response=response,
                    is_correct=is_correct,
                    explanation=explanation,
                    stats=sample.get_gen_stats(response, tool_definitions),
                    end_of_stream=end_of_stream,
                )
            )

        return EvalResult(
            sample=sample,
            attempts=attempts,
            sample_tool_definitions=chat_prompt_input.tool_definitions or [],
            model_tool_definitions=tool_definitions,
        )

    def run_eval(self, samples: list[EvalSample], dataset_path: str) -> EvalSummary:
        eval_results = dict()
        for sample in tqdm(
            samples, desc=f"Evaluating {self.model_config.name}", unit="sample"
        ):
            eval_results[sample.get_unique_name()] = self.run_single_sample_eval(sample)

        stats = dict()
        for eval_result in eval_results.values():
            for attempt in eval_result.attempts:
                for stat_name, stat_value in attempt.stats.items():
                    stat_sum_name = f"{stat_name}_sum"
                    stat_count_name = f"{stat_name}_count"
                    stats[stat_sum_name] = stats.get(stat_sum_name, 0) + stat_value
                    stats[stat_count_name] = stats.get(stat_count_name, 0) + 1
        for stat_name_sum in list(stats.keys()):
            if stat_name_sum.endswith("_sum"):
                stat_name = stat_name_sum[: -len("_sum")]
                count_name = stat_name + "_count"
                avg_name = stat_name + "_avg"
                stats[avg_name] = stats[stat_name_sum] / stats[count_name]

        return EvalSummary(self.model_config, dataset_path, eval_results, stats)
