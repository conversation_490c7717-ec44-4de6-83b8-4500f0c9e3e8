"""Tests for eval_output.py."""

import io
import pickle
from unittest.mock import MagicMock

from base.prompt_format.common import Chat<PERSON><PERSON>ultNode, ChatResultNodeType
from base.prompt_format_chat.structured_binks_agent_prompt_formatter import (
    StructuredBinksAgentPromptFormatter,
)
from base.third_party_clients.third_party_model_client import ToolC<PERSON><PERSON>, ToolChoiceType
from experimental.vpas.agent.replay_eval.eval_output import (
    Attempt,
    ComparisonSummary,
    EvalResult,
    EvalSummary,
)
from experimental.vpas.agent.replay_eval.eval_sample import EvalSample
from experimental.vpas.agent.replay_eval.model_config import ModelConfig


class TestEvalSummary:
    """Tests for the EvalSummary class."""

    def setup_method(self):
        """Set up test fixtures."""
        # Create a mock prompt formatter for use in tests
        self.mock_prompt_formatter = MagicMock(spec=StructuredBinksAgentPromptFormatter)

    def test_pickle_serialization(self):
        """Test that EvalSummary can be serialized to and deserialized from pickle."""
        # Create a real EvalSample
        sample = EvalSample(
            request_id="test-request-id",
            name="test_sample_1",
            assistant_message_prefill="",
            eval_response_func=lambda _: (True, ""),
        )

        # Create a real ChatResultNode for the ModelResponse
        response = [
            ChatResultNode(
                id=1, type=ChatResultNodeType.RAW_RESPONSE, content="Test response"
            )
        ]

        # Create an Attempt
        attempt = Attempt(response=response, is_correct=True, explanation="")

        # Create an EvalResult
        eval_result = EvalResult(sample=sample, attempts=[attempt])

        # Create a ModelConfig
        model_config = ModelConfig(
            name="test_model",
            base_model="claude-3-5-sonnet-v2",
            prompt_formatter=self.mock_prompt_formatter,
        )

        # Create an EvalSummary
        eval_summary = EvalSummary(
            model_config=model_config,
            dataset_path="test_samples",
            eval_results={sample.get_unique_name(): eval_result},
        )

        # Serialize to pickle
        pickle_buffer = io.BytesIO()
        pickle.dump(eval_summary, pickle_buffer)
        pickle_buffer.seek(0)

        # Deserialize from pickle
        deserialized_summary = pickle.load(pickle_buffer)

        # Check that the deserialized object has the expected values
        assert deserialized_summary.model_config.name == "test_model"
        assert (
            deserialized_summary.model_config.anthropic_model == "claude-3-5-sonnet-v2"
        )
        assert sample.get_unique_name() in deserialized_summary.eval_results

    def test_pickle_serialization_with_multiple_results(self):
        """Test pickle serialization with multiple eval results."""
        # Create two different samples
        sample1 = EvalSample(
            request_id="test-request-id-1",
            name="test_sample_1",
            assistant_message_prefill="",
            eval_response_func=lambda _: (True, ""),
        )

        sample2 = EvalSample(
            request_id="test-request-id-2",
            name="test_sample_2",
            assistant_message_prefill="",
            eval_response_func=lambda _: (True, ""),
        )

        # Create responses
        response1 = [
            ChatResultNode(
                id=1, type=ChatResultNodeType.RAW_RESPONSE, content="Test response 1"
            )
        ]
        response2 = [
            ChatResultNode(
                id=2, type=ChatResultNodeType.RAW_RESPONSE, content="Test response 2"
            )
        ]

        # Create attempts
        attempt1 = Attempt(response=response1, is_correct=True, explanation="")
        attempt2 = Attempt(
            response=response2,
            is_correct=False,
            explanation="Failed to use the correct tool",
        )

        # Create eval results
        eval_result1 = EvalResult(sample=sample1, attempts=[attempt1])
        eval_result2 = EvalResult(sample=sample2, attempts=[attempt2])

        # Create a ModelConfig
        model_config = ModelConfig(
            name="test_model",
            base_model="claude-3-5-sonnet-v2",
            prompt_formatter=self.mock_prompt_formatter,
        )

        # Create an EvalSummary with multiple results
        eval_summary = EvalSummary(
            model_config=model_config,
            dataset_path="test_samples",
            eval_results={
                sample1.get_unique_name(): eval_result1,
                sample2.get_unique_name(): eval_result2,
            },
        )

        # Serialize to pickle
        pickle_buffer = io.BytesIO()
        pickle.dump(eval_summary, pickle_buffer)
        pickle_buffer.seek(0)

        # Deserialize from pickle
        deserialized_summary = pickle.load(pickle_buffer)

        # Check that both results are in the deserialized object
        assert sample1.get_unique_name() in deserialized_summary.eval_results
        assert sample2.get_unique_name() in deserialized_summary.eval_results

        # Check correctness values were properly preserved
        assert (
            deserialized_summary.eval_results[
                sample1.get_unique_name()
            ].num_correct_attempts
            == 1
        )
        assert (
            deserialized_summary.eval_results[
                sample2.get_unique_name()
            ].num_correct_attempts
            == 0
        )

    def test_round_trip_pickle_serialization(self):
        """Test that pickle serializing and deserializing preserves all data."""
        # Create a sample with multiple attempts
        sample = EvalSample(
            request_id="test-request-id",
            name="test_sample_1",
            assistant_message_prefill="",
            eval_response_func=lambda _: (True, ""),
        )

        # Create multiple responses and attempts
        response1 = [
            ChatResultNode(
                id=1, type=ChatResultNodeType.RAW_RESPONSE, content="First attempt"
            )
        ]
        response2 = [
            ChatResultNode(
                id=2, type=ChatResultNodeType.RAW_RESPONSE, content="Second attempt"
            )
        ]
        response3 = [
            ChatResultNode(
                id=3, type=ChatResultNodeType.RAW_RESPONSE, content="Third attempt"
            )
        ]

        attempt1 = Attempt(
            response=response1, is_correct=False, explanation="First attempt failed"
        )
        attempt2 = Attempt(response=response2, is_correct=True, explanation="")
        attempt3 = Attempt(
            response=response3, is_correct=False, explanation="Third attempt failed"
        )

        # Create an EvalResult with multiple attempts
        eval_result = EvalResult(sample=sample, attempts=[attempt1, attempt2, attempt3])

        # Create a ModelConfig
        model_config = ModelConfig(
            name="test_model",
            base_model="claude-3-5-sonnet-v2",
            prompt_formatter=self.mock_prompt_formatter,
        )

        # Create an EvalSummary
        original_summary = EvalSummary(
            model_config=model_config,
            dataset_path="test_samples",
            eval_results={sample.get_unique_name(): eval_result},
        )

        # Serialize to pickle and back
        pickle_buffer = io.BytesIO()
        pickle.dump(original_summary, pickle_buffer)
        pickle_buffer.seek(0)
        deserialized_summary = pickle.load(pickle_buffer)

        # Check that the model config was preserved
        assert (
            deserialized_summary.model_config.name == original_summary.model_config.name
        )
        assert (
            deserialized_summary.model_config.anthropic_model
            == original_summary.model_config.base_model
        )

        # Check that the eval results were preserved
        assert sample.get_unique_name() in deserialized_summary.eval_results

        # Check that the attempts were preserved
        original_result = original_summary.eval_results[sample.get_unique_name()]
        deserialized_result = deserialized_summary.eval_results[
            sample.get_unique_name()
        ]

        assert len(deserialized_result.attempts) == len(original_result.attempts)
        assert (
            deserialized_result.num_correct_attempts
            == original_result.num_correct_attempts
        )
        assert (
            deserialized_result.num_correct_attempts == 1
        )  # Only the second attempt is correct

    def test_json_serialization(self):
        """Test that EvalSummary can be serialized to and deserialized from JSON."""
        # Create a real EvalSample with all fields filled in
        sample = EvalSample(
            request_id="test-request-id",
            name="test_sample_1",
            assistant_message_prefill="This is a prefill message",
            assistant_message_prefill_from_response=False,
            eval_response_func=lambda _: (True, ""),
            category="test_category",
            tool_choice=ToolChoice(type=ToolChoiceType.TOOL, name="test-tool"),
            linear_url="https://linear.app/augmentcode/issue/AU-1234/test-issue",
        )

        # Create multiple ChatResultNodes for a more complex ModelResponse
        response = [
            ChatResultNode(
                id=1, type=ChatResultNodeType.RAW_RESPONSE, content="Test response"
            ),
            ChatResultNode(
                id=2, type=ChatResultNodeType.TOOL_USE, content="Tool use content"
            ),
            ChatResultNode(
                id=3,
                type=ChatResultNodeType.MAIN_TEXT_FINISHED,
                content="Main text finished content",
            ),
        ]

        # Create multiple Attempts with different correctness values
        attempt1 = Attempt(
            response=response, is_correct=True, explanation="First attempt succeeded"
        )
        attempt2 = Attempt(
            response=[
                ChatResultNode(
                    id=4, type=ChatResultNodeType.RAW_RESPONSE, content="Second attempt"
                )
            ],
            is_correct=False,
            explanation="Second attempt failed",
        )

        # Create an EvalResult with multiple attempts
        eval_result = EvalResult(sample=sample, attempts=[attempt1, attempt2])

        # Create a ModelConfig with all fields
        model_config = ModelConfig(
            name="test_model",
            base_model="claude-3-5-sonnet-v2",
            prompt_formatter=self.mock_prompt_formatter,
        )

        # Create a second sample for testing multiple results
        sample2 = EvalSample(
            request_id="test-request-id-2",
            name="test_sample_2",
            assistant_message_prefill="Another prefill message",
            eval_response_func=lambda _: (True, ""),
            category="another_category",
            tool_choice=ToolChoice(type=ToolChoiceType.ANY, name=None),
            linear_url=None,
        )

        # Create a simple response and attempt for the second sample
        response2 = [
            ChatResultNode(
                id=5, type=ChatResultNodeType.RAW_RESPONSE, content="Sample 2 response"
            )
        ]
        attempt3 = Attempt(response=response2, is_correct=True, explanation="")
        eval_result2 = EvalResult(sample=sample2, attempts=[attempt3])

        # Create an EvalSummary with multiple results
        eval_summary = EvalSummary(
            model_config=model_config,
            dataset_path="test_samples",
            eval_results={
                sample.get_unique_name(): eval_result,
                sample2.get_unique_name(): eval_result2,
            },
        )

        # Serialize to JSON
        json_str = eval_summary.to_json()

        # Deserialize from JSON
        deserialized_summary = EvalSummary.from_json(json_str)

        # Check that the deserialized object has the expected values
        assert deserialized_summary.model_config.name == "test_model"
        assert deserialized_summary.model_config.base_model == "claude-3-5-sonnet-v2"

        # Verify both samples are in the deserialized results
        assert sample.get_unique_name() in deserialized_summary.eval_results
        assert sample2.get_unique_name() in deserialized_summary.eval_results

        # Verify the first eval result was properly deserialized
        deserialized_result = deserialized_summary.eval_results[
            sample.get_unique_name()
        ]
        assert deserialized_result.num_correct_attempts == 1
        assert len(deserialized_result.attempts) == 2
        assert deserialized_result.attempts[0].is_correct is True
        assert deserialized_result.attempts[1].is_correct is False
        assert deserialized_result.attempts[0].explanation == "First attempt succeeded"
        assert deserialized_result.attempts[1].explanation == "Second attempt failed"

        # Verify the sample properties were properly deserialized
        deserialized_sample = deserialized_result.sample
        assert deserialized_sample.request_id == "test-request-id"
        assert deserialized_sample.name == "test_sample_1"
        assert (
            deserialized_sample.assistant_message_prefill == "This is a prefill message"
        )
        assert deserialized_sample.category == "test_category"
        assert (
            deserialized_sample.linear_url
            == "https://linear.app/augmentcode/issue/AU-1234/test-issue"
        )
        assert deserialized_sample.tool_choice is not None
        assert deserialized_sample.tool_choice.type == ToolChoiceType.TOOL
        assert deserialized_sample.tool_choice.name == "test-tool"

        # Verify the second eval result was properly deserialized
        deserialized_result2 = deserialized_summary.eval_results[
            sample2.get_unique_name()
        ]
        assert deserialized_result2.num_correct_attempts == 1
        assert len(deserialized_result2.attempts) == 1

        # Verify the second sample properties were properly deserialized
        deserialized_sample2 = deserialized_result2.sample
        assert deserialized_sample2.request_id == "test-request-id-2"
        assert deserialized_sample2.name == "test_sample_2"
        assert (
            deserialized_sample2.assistant_message_prefill == "Another prefill message"
        )
        assert deserialized_sample2.category == "another_category"
        assert deserialized_sample2.linear_url is None
        assert deserialized_sample2.tool_choice is not None
        assert deserialized_sample2.tool_choice.type == ToolChoiceType.ANY
        assert deserialized_sample2.tool_choice.name is None

    def test_comparison_summary_json_serialization(self):
        """Test that ComparisonSummary can be serialized to and deserialized from JSON."""
        # Create a reference EvalSample
        ref_sample = EvalSample(
            request_id="ref-request-id",
            name="ref_sample",
            assistant_message_prefill="Reference prefill message",
            assistant_message_prefill_from_response=False,
            eval_response_func=lambda _: (True, ""),
            category="reference_category",
            tool_choice=ToolChoice(type=ToolChoiceType.TOOL, name="reference-tool"),
            linear_url="https://linear.app/augmentcode/issue/AU-1000/reference-issue",
        )

        # Create a response and attempt for the reference sample
        ref_response = [
            ChatResultNode(
                id=1, type=ChatResultNodeType.RAW_RESPONSE, content="Reference response"
            )
        ]
        ref_attempt = Attempt(response=ref_response, is_correct=True, explanation="")
        ref_eval_result = EvalResult(sample=ref_sample, attempts=[ref_attempt])

        # Create a reference ModelConfig
        ref_model_config = ModelConfig(
            name="reference_model",
            base_model="claude-3-5-sonnet-v1",
            prompt_formatter=self.mock_prompt_formatter,
            supervisor_message="Reference supervisor message",
            system_prompt="Reference system prompt",
        )

        # Create a reference EvalSummary
        ref_eval_summary = EvalSummary(
            model_config=ref_model_config,
            dataset_path="reference_samples",
            eval_results={ref_sample.get_unique_name(): ref_eval_result},
            stats={"accuracy": 1.0, "total_samples": 1},
        )

        # Create a new model EvalSample
        new_sample = EvalSample(
            request_id="new-request-id",
            name="new_sample",
            assistant_message_prefill="New model prefill message",
            assistant_message_prefill_from_response=False,
            eval_response_func=lambda _: (True, ""),
            category="new_category",
            tool_choice=ToolChoice(type=ToolChoiceType.ANY, name=None),
            linear_url=None,
        )

        # Create a response and attempt for the new model sample
        new_response = [
            ChatResultNode(
                id=2, type=ChatResultNodeType.RAW_RESPONSE, content="New model response"
            )
        ]
        new_attempt = Attempt(
            response=new_response, is_correct=False, explanation="Failed attempt"
        )
        new_eval_result = EvalResult(sample=new_sample, attempts=[new_attempt])

        # Create a new ModelConfig
        new_model_config = ModelConfig(
            name="new_model",
            base_model="claude-3-5-sonnet-v2",
            prompt_formatter=self.mock_prompt_formatter,
            supervisor_message="New model supervisor message",
            system_prompt="New model system prompt",
        )

        # Create a new EvalSummary
        new_eval_summary = EvalSummary(
            model_config=new_model_config,
            dataset_path="new_samples",
            eval_results={new_sample.get_unique_name(): new_eval_result},
            stats={"accuracy": 0.0, "total_samples": 1},
        )

        # Create a ComparisonSummary
        comparison_summary = ComparisonSummary(
            ref_summary=ref_eval_summary,
            new_model_summaries={"new_model": new_eval_summary},
        )

        # Serialize to JSON
        json_str = comparison_summary.to_json()

        # Deserialize from JSON
        deserialized_summary = ComparisonSummary.from_json(json_str)

        # Check that the deserialized object has the expected values
        # Reference model checks
        assert deserialized_summary.ref_summary.model_config.name == "reference_model"
        assert (
            deserialized_summary.ref_summary.model_config.base_model
            == "claude-3-5-sonnet-v1"
        )
        # The extract_system_prompt function returns a default message if it can't find the system prompt
        # in the expected location (prompt_formatter.system_prompt_formatter.parts_or_placeholders)
        assert (
            deserialized_summary.ref_system_prompt
            == "System prompt not available for this model configuration"
        )
        assert (
            deserialized_summary.ref_supervisor_message
            == "Reference supervisor message"
        )

        # Check reference model eval results
        assert (
            ref_sample.get_unique_name()
            in deserialized_summary.ref_summary.eval_results
        )
        ref_result = deserialized_summary.ref_summary.eval_results[
            ref_sample.get_unique_name()
        ]
        assert ref_result.num_correct_attempts == 1
        assert len(ref_result.attempts) == 1
        assert ref_result.attempts[0].is_correct is True

        # Check reference model eval stats
        assert deserialized_summary.ref_summary.stats["accuracy"] == 1.0
        assert deserialized_summary.ref_summary.stats["total_samples"] == 1

        # New model checks
        assert "new_model" in deserialized_summary.new_model_summaries
        new_model_summary = deserialized_summary.new_model_summaries["new_model"]
        assert new_model_summary.model_config.name == "new_model"
        assert new_model_summary.model_config.base_model == "claude-3-5-sonnet-v2"

        # Check system prompts and supervisor messages
        # The extract_system_prompt function returns a default message if it can't find the system prompt
        assert (
            deserialized_summary.model_system_prompts["new_model"]
            == "System prompt not available for this model configuration"
        )
        assert (
            deserialized_summary.model_supervisor_messages["new_model"]
            == "New model supervisor message"
        )

        # Check new model eval results
        assert new_sample.get_unique_name() in new_model_summary.eval_results
        new_result = new_model_summary.eval_results[new_sample.get_unique_name()]
        assert new_result.num_correct_attempts == 0
        assert len(new_result.attempts) == 1
        assert new_result.attempts[0].is_correct is False
        assert new_result.attempts[0].explanation == "Failed attempt"

        # Check new model eval stats
        assert new_model_summary.stats["accuracy"] == 0.0
        assert new_model_summary.stats["total_samples"] == 1
