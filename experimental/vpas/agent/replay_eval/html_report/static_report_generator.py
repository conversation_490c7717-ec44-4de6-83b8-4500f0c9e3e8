"""Static HTML report generator for replay evaluation.

This module provides functions for generating a fully static HTML report
that includes all information without interactive elements. The report is
intended for language model consumption rather than human consumption.
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional

from base.prompt_format.common import ChatResultNodeType
from experimental.vpas.agent.replay_eval.eval_output import (
    Attempt,
    ComparisonSummary,
    EvalResult,
    EvalSummary,
)
from experimental.vpas.agent.replay_eval.html_report.react_report_generator import (
    generate_comparison_json_str,
)


def format_code(code: str, language: str = "") -> str:
    """Format code with HTML syntax highlighting.

    Args:
        code: The code to format
        language: The language of the code

    Returns:
        HTML formatted code
    """
    # Simple HTML escaping for code
    import html

    escaped_code = html.escape(code)
    return f'<pre class="code {language}">{escaped_code}</pre>'


def format_node(node: Dict[str, Any]) -> str:
    """Format a chat result node as HTML.

    Args:
        node: The node to format

    Returns:
        HTML representation of the node
    """
    node_type = node.get("type")
    html_parts = []

    if node_type == ChatResultNodeType.RAW_RESPONSE:
        content = node.get("content", "")
        html_parts.append(
            f'<div class="node raw-response"><h4>Raw Response</h4>{format_code(content, "markdown")}</div>'
        )

    elif node_type == ChatResultNodeType.TOOL_USE:
        tool_use = node.get("tool_use", {})
        tool_name = tool_use.get("name", "Unknown Tool")
        tool_input = json.dumps(tool_use.get("input", {}), indent=2)
        html_parts.append(
            f'<div class="node tool-use"><h4>Tool Use: {tool_name}</h4>{format_code(tool_input, "json")}</div>'
        )

    # Tool result is not a specific node type in ChatResultNodeType
    # We'll check for content that looks like a tool result
    elif "tool_result" in str(node_type).lower() or (
        node.get("content", "").startswith("Result:")
        and node_type == ChatResultNodeType.RAW_RESPONSE
    ):
        content = node.get("content", "")
        html_parts.append(
            f'<div class="node tool-result"><h4>Tool Result</h4>{format_code(content, "markdown")}</div>'
        )

    elif node_type == ChatResultNodeType.FINAL_PARAMETERS:
        final_params = node.get("final_parameters", {})
        # Format all parameters, including nested ones
        params_json = json.dumps(final_params, indent=2)
        html_parts.append(
            f'<div class="node final-parameters"><h4>Final Parameters</h4>{format_code(params_json, "json")}</div>'
        )

        # Also expand specific important parameters
        if "messages" in final_params and isinstance(final_params["messages"], list):
            html_parts.append('<div class="messages-section"><h4>Messages</h4>')
            for i, msg in enumerate(final_params["messages"]):
                role = msg.get("role", "unknown")
                content = msg.get("content", "")
                if isinstance(content, str):
                    html_parts.append(
                        f'<div class="message {role}"><h5>Message {i+1} ({role})</h5>{format_code(content, "markdown")}</div>'
                    )
                elif isinstance(content, list):
                    # Handle content as a list of parts
                    parts_content = []
                    for part in content:
                        if isinstance(part, dict) and "text" in part:
                            parts_content.append(part["text"])
                        else:
                            parts_content.append(str(part))
                    joined_content = "\n".join(parts_content)
                    html_parts.append(
                        f'<div class="message {role}"><h5>Message {i+1} ({role})</h5>{format_code(joined_content, "markdown")}</div>'
                    )
            html_parts.append("</div>")

    return "\n".join(html_parts)


def format_attempt(attempt: Dict[str, Any], index: int) -> str:
    """Format an evaluation attempt as HTML.

    Args:
        attempt: The attempt to format
        index: The attempt index

    Returns:
        HTML representation of the attempt
    """
    is_correct = attempt.get("is_correct", False)
    status_class = "correct" if is_correct else "incorrect"
    status_text = "CORRECT" if is_correct else "INCORRECT"

    html_parts = [
        '<div class="attempt">',
        '<div class="attempt-header">',
        f"<h3>Attempt {index + 1}</h3>",
        f'<span class="{status_class}">{status_text}</span>',
        "</div>",
    ]

    # Add explanation if available
    explanation = attempt.get("explanation", "")
    if explanation:
        html_parts.append(
            f'<div class="explanation"><h4>Explanation</h4><pre>{explanation}</pre></div>'
        )

    # Add response nodes
    response = attempt.get("response", [])
    if response:
        html_parts.append('<div class="response"><h4>Response</h4>')
        for node in response:
            html_parts.append(format_node(node))
        html_parts.append("</div>")
    else:
        html_parts.append(
            '<div class="response"><h4>Response</h4><p>No response data available</p></div>'
        )

    html_parts.append("</div>")  # Close attempt div
    return "\n".join(html_parts)


def format_eval_result(eval_result: Dict[str, Any], model_name: str) -> str:
    """Format an evaluation result as HTML.

    Args:
        eval_result: The evaluation result to format
        model_name: The name of the model

    Returns:
        HTML representation of the evaluation result
    """
    sample = eval_result.get("sample", {})
    sample_name = sample.get("name", "Unknown")
    request_id = sample.get("request_id", "Unknown")
    category = sample.get("category", "Unknown")

    # Calculate correctness stats
    attempts = eval_result.get("attempts", [])
    total_attempts = len(attempts)
    correct_attempts = sum(1 for a in attempts if a.get("is_correct", False))
    is_correct = correct_attempts > 0
    status_class = "correct" if is_correct else "incorrect"
    status_text = "PASS" if is_correct else "FAIL"

    html_parts = [
        f'<div class="eval-result" id="{sample_name}" data-category="{category}">',
        '<div class="eval-result-header">',
        f"<h2>{sample_name}</h2>",
        f'<span class="{status_class}">{status_text}</span>',
        "</div>",
        '<div class="model-details">',
        f"<div><strong>Model:</strong> {model_name}</div>",
        f"<div><strong>Request ID:</strong> {request_id}</div>",
        f"<div><strong>Correct Attempts:</strong> {correct_attempts}/{total_attempts}</div>",
        "</div>",
    ]

    # Add all attempts
    html_parts.append('<div class="eval-result-attempts">')
    for i, attempt in enumerate(attempts):
        html_parts.append(format_attempt(attempt, i))
    html_parts.append("</div>")

    html_parts.append("</div>")  # Close eval-result div
    return "\n".join(html_parts)


def generate_static_html_report(comparison_summary: ComparisonSummary) -> str:
    """Generate a static HTML report from a comparison summary.

    Args:
        comparison_summary: The comparison summary to generate a report for

    Returns:
        HTML string containing the report
    """
    # Get model names
    ref_model_name = comparison_summary.ref_summary.model_config.name
    model_names = [ref_model_name] + list(comparison_summary.new_model_summaries.keys())
    dataset_path = comparison_summary.ref_summary.dataset_path

    # Convert to JSON and back to ensure all objects are serializable
    json_str = generate_comparison_json_str(comparison_summary)
    data = json.loads(json_str)

    # Start building HTML
    html_parts = [
        "<!DOCTYPE html>",
        "<html>",
        "<head>",
        '<meta charset="UTF-8">',
        "<title>Static Evaluation Report</title>",
        "<style>",
        # Basic styling
        "body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; }",
        "h1, h2, h3, h4, h5 { margin-top: 0; }",
        ".report-container { max-width: 1200px; margin: 0 auto; }",
        ".correct { color: green; }",
        ".incorrect { color: red; }",
        ".eval-result { border: 1px solid #ddd; margin-bottom: 20px; padding: 15px; border-radius: 5px; }",
        ".eval-result-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }",
        ".model-details { margin-bottom: 15px; }",
        ".attempt { border: 1px solid #eee; margin-bottom: 15px; padding: 15px; border-radius: 5px; }",
        ".attempt-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }",
        ".node { border: 1px solid #eee; margin-bottom: 10px; padding: 10px; border-radius: 5px; }",
        "pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }",
        ".message { border: 1px solid #eee; margin-bottom: 10px; padding: 10px; border-radius: 5px; }",
        ".user { background-color: #f0f8ff; }",
        ".assistant { background-color: #f5f5f5; }",
        ".system { background-color: #fff8dc; }",
        "</style>",
        "</head>",
        "<body>",
        '<div class="report-container">',
        "<h1>Static Evaluation Report</h1>",
        f"<p><strong>Dataset:</strong> {dataset_path}</p>",
        f"<p><strong>Reference Model:</strong> {ref_model_name}</p>",
        f'<p><strong>Comparison Models:</strong> {", ".join(model_names[1:]) if len(model_names) > 1 else "None"}</p>',
    ]

    # Add summary statistics
    ref_results = data["comparison_summary"]["ref_summary"]["eval_results"]
    total_samples = len(ref_results)
    ref_correct = sum(
        1
        for r in ref_results.values()
        if any(a.get("is_correct", False) for a in r.get("attempts", []))
    )
    ref_success_rate = ref_correct / total_samples if total_samples > 0 else 0

    html_parts.append("<h2>Summary Statistics</h2>")
    html_parts.append(
        '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">'
    )
    html_parts.append(
        "<tr><th>Model</th><th>Success Rate</th><th>Correct Samples</th><th>Total Samples</th></tr>"
    )
    html_parts.append(
        f"<tr><td>{ref_model_name}</td><td>{ref_success_rate:.2%}</td><td>{ref_correct}</td><td>{total_samples}</td></tr>"
    )

    # Add comparison model stats
    for model_name, summary in data["comparison_summary"][
        "new_model_summaries"
    ].items():
        model_results = summary["eval_results"]
        model_total = len(model_results)
        model_correct = sum(
            1
            for r in model_results.values()
            if any(a.get("is_correct", False) for a in r.get("attempts", []))
        )
        model_success_rate = model_correct / model_total if model_total > 0 else 0
        html_parts.append(
            f"<tr><td>{model_name}</td><td>{model_success_rate:.2%}</td><td>{model_correct}</td><td>{model_total}</td></tr>"
        )

    html_parts.append("</table>")

    # Add all reference model results
    html_parts.append("<h2>Reference Model Results</h2>")
    for sample_name, result in ref_results.items():
        html_parts.append(format_eval_result(result, ref_model_name))

    # Add all comparison model results
    for model_name, summary in data["comparison_summary"][
        "new_model_summaries"
    ].items():
        html_parts.append(f"<h2>{model_name} Results</h2>")
        for sample_name, result in summary["eval_results"].items():
            html_parts.append(format_eval_result(result, model_name))

    # Close HTML
    html_parts.append("</div>")
    html_parts.append("</body>")
    html_parts.append("</html>")

    return "\n".join(html_parts)


def save_static_comparison_html_report(
    comparison_summary: ComparisonSummary, output_dir: Path
) -> Optional[str]:
    """Generate a static comparison HTML report and save it to the output directory.

    Args:
        comparison_summary: The comparison summary to generate a report for
        output_dir: The directory where the report should be saved

    Returns:
        The path to the HTML report file
    """
    print(f"Generating static HTML report in {output_dir}")
    # Create the output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)

    # Generate the HTML
    html_content = generate_static_html_report(comparison_summary)

    # Save the HTML to a file
    html_path = output_dir / "static_report.html"
    with open(html_path, "w") as f:
        f.write(html_content)

    return str(html_path)
