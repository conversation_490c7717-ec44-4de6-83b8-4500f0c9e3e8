#!/usr/bin/env python3
"""
Script to regenerate a replay evaluation report using the latest version of run_replay_eval.py.

This script takes a URL to an existing comparison report, extracts the parameters used to generate it,
and runs the latest version of run_replay_eval.py with those parameters to generate a new report.

Example usage:
    python -u experimental/vpas/agent/replay_eval/regenerate_report.py \
        --report-url https://webserver.gcp-us1.r.augmentcode.com/cmsflash99/agent_replay_eval/comparison_claude-sonnet-3-7-200k-v2-agent_vs_claude-linen-200k-v2-agent_all_samples_attempts_5_dcc8/index.html

Note: The -u flag is recommended to ensure unbuffered output.
"""

import argparse
import re
import subprocess
import sys
from urllib.parse import urlparse


def extract_params_from_url(url):
    """
    Extract parameters from a comparison report URL.

    Args:
        url: The URL of the comparison report

    Returns:
        A dictionary containing the extracted parameters
    """
    # Parse the URL
    parsed_url = urlparse(url)

    # Extract the path
    path = parsed_url.path.lstrip("/")
    print(f"Parsed path: {path}")

    # Split the path into components
    path_parts = path.split("/")
    print(f"Path parts: {path_parts}")

    # The relevant part is typically the last directory in the path
    # Format: comparison_{ref_model}_vs_{target_models}_{dataset}_attempts_{num_attempts}_{random_suffix}
    comparison_dir = (
        path_parts[-2] if path_parts[-1] in ["index.html", ""] else path_parts[-1]
    )
    print(f"Comparison directory: {comparison_dir}")

    # Extract parameters using regex
    params = {}

    # Extract reference model and target models
    model_match = re.search(r"comparison_([^_]+)_vs_([^_]+)_", comparison_dir)
    if model_match:
        params["ref_model"] = model_match.group(1)
        params["target_models"] = model_match.group(2).split("_vs_")
        print(f"Extracted ref_model: {params['ref_model']}")
        print(f"Extracted target_models: {params['target_models']}")

    # Extract dataset
    dataset_match = re.search(r"_vs_[^_]+_([^_]+)_attempts_", comparison_dir)
    if dataset_match:
        params["dataset"] = dataset_match.group(1)
        print(f"Extracted dataset: {params['dataset']}")

    # Extract number of attempts
    attempts_match = re.search(r"_attempts_(\d+)", comparison_dir)
    if attempts_match:
        params["num_attempts"] = int(attempts_match.group(1))
        print(f"Extracted num_attempts: {params['num_attempts']}")

    # Extract limit per category if present
    limit_match = re.search(r"_limit_(\d+)", comparison_dir)
    if limit_match:
        params["limit_per_category"] = int(limit_match.group(1))
        print(f"Extracted limit_per_category: {params['limit_per_category']}")

    # Check if specific request IDs were used
    if "_specific_request_ids" in comparison_dir:
        params["specific_request_ids"] = True
        print("Detected specific_request_ids flag")

    print(f"Final extracted parameters: {params}")
    return params


def build_command(params):
    """
    Build the command to run the latest version of run_replay_eval.py with the extracted parameters.

    Args:
        params: Dictionary of parameters extracted from the URL

    Returns:
        A list containing the command and its arguments
    """
    cmd = ["python", "experimental/vpas/agent/replay_eval/run_replay_eval.py"]

    # Add reference model config
    if "ref_model" in params:
        ref_model = params["ref_model"]
        # Check if it ends with .py, if not add it
        if not ref_model.endswith(".py"):
            ref_model += ".py"
        cmd.extend(["--ref-model-config", ref_model])

    # Add target model configs
    if "target_models" in params and params["target_models"]:
        target_models = []
        for model in params["target_models"]:
            if not model.endswith(".py"):
                model += ".py"
            target_models.append(model)
        cmd.extend(["--model-configs"] + target_models)

    # Add dataset if it's not 'all_samples'
    if "dataset" in params and params["dataset"] != "all_samples":
        cmd.extend(["--samples", params["dataset"]])

    # Add number of attempts
    if "num_attempts" in params:
        cmd.extend(["--num-attempts", str(params["num_attempts"])])

    # Add limit per category if present
    if "limit_per_category" in params:
        cmd.extend(["--limit-per-category", str(params["limit_per_category"])])

    # Add no-cache flag to ensure fresh results
    cmd.append("--no-cache")

    return cmd


def main():
    print("Starting regenerate_report.py script...", flush=True)
    parser = argparse.ArgumentParser(
        description="Regenerate a replay evaluation report using the latest version of run_replay_eval.py"
    )
    parser.add_argument(
        "--report-url", required=True, help="URL of the existing comparison report"
    )
    parser.add_argument(
        "--dry-run", action="store_true", help="Print the command without executing it"
    )

    args = parser.parse_args()

    # Extract parameters from the URL
    params = extract_params_from_url(args.report_url)

    if not params:
        print(f"Error: Could not extract parameters from URL: {args.report_url}")
        sys.exit(1)

    # Build the command
    cmd = build_command(params)

    # Print the command
    print("Command to regenerate report:", flush=True)
    print(" ".join(cmd), flush=True)

    # Execute the command if not a dry run
    if not args.dry_run:
        print("\nExecuting command...", flush=True)
        subprocess.run(cmd)
    else:
        print("\nDry run - command not executed", flush=True)


if __name__ == "__main__":
    main()
