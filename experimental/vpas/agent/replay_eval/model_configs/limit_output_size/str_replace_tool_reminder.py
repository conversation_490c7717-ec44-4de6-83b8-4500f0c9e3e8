from typing import Tu<PERSON>
from base.prompt_format.common import ChatResultNodeType
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.vpas.agent.replay_eval.model_config import (
    ModelResponse,
    from_prod_model_name,
)
from experimental.vpas.agent.replay_eval.utils import extract_tool_call
from experimental.vpas.agent.str_replace_editor.utils import (
    extract_str_replace_entries_from_flat_input,
)
from experimental.vpas.agent.replay_eval.str_replace_editor_utils import (
    convert_history_to_flat_schema,
)

MAX_LINES = 200
INSTRUCTION = (
    f"ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST {MAX_LINES} LINES EACH."
)

model_config = from_prod_model_name()
model_config.name = "str_replace_tool_reminder"
model_config.override_tool_map["str-replace-editor"] = dict(
    description=f"""\
Custom editing tool for viewing, creating and editing files
* `path` is a file path relative to the workspace root
* command `view` displays the result of applying `cat -n`.
* If a `command` generates a long output, it will be truncated and marked with `<response clipped>`
* `insert` and `str_replace` commands output a snippet of the edited section for each entry. This snippet reflects the final state of the file after all edits and IDE auto-formatting have been applied.

Notes for using the `str_replace` command:
* Generate `str_replace_instruction_reminder` first to remind yourself to limit the edits to at most {MAX_LINES} lines.
* Specify `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2` and `old_str_end_line_number_2` for the second replacement, and so on
* The `old_str_start_line_number_1` and `old_str_end_line_number_1` parameters are 1-based line numbers
* Both `old_str_start_line_number_1` and `old_str_end_line_number_1` are INCLUSIVE
* The `old_str_1` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!
* Empty `old_str_1` is allowed only when the file is empty or contains only whitespaces
* It is important to specify `old_str_start_line_number_1` and `old_str_end_line_number_1` to disambiguate between multiple occurrences of `old_str_1` in the file
* Make sure that `old_str_start_line_number_1` and `old_str_end_line_number_1` do not overlap with other `old_str_start_line_number_2` and `old_str_end_line_number_2` entries
* The `new_str_1` parameter should contain the edited lines that should replace the `old_str_1`. Can be an empty string to delete content
* To make multiple replacements in one tool call add multiple sets of replacement parameters. For example, `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2`, `old_str_end_line_number_2` for the second replacement, etc.

Notes for using the `insert` command:
* Specify `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, and so on
* The `insert_line_1` parameter specifies the line number after which to insert the new string
* The `insert_line_1` parameter is 1-based line number
* To insert at the very beginning of the file, use `insert_line_1: 0`
* To make multiple insertions in one tool call add multiple sets of insertion parameters. For example, `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, etc.

Notes for using the `view` command:
* Strongly prefer to use larger ranges of at least 500 lines when scanning through files. One call with large range is much more efficient than many calls with small ranges

IMPORTANT:
* This is the only tool you should use for editing files.
* If it fails try your best to fix inputs and retry.
* DO NOT fall back to removing the whole file and recreating it from scratch.
* DO NOT use sed or any other command line tools for editing files.
* Try to fit as many edits in one tool call as possible
* Use view command to read the file before editing it.
""",
    schema={
        "type": "object",
        "properties": {
            "command": {
                "type": "string",
                "enum": ["view", "str_replace", "insert"],
                "description": "The commands to run. Allowed options are: 'view', 'str_replace', 'insert'.",
            },
            "path": {
                "description": "Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.",
                "type": "string",
            },
            "view_range": {
                "description": "Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [501, 1000] will show lines from 501 to 1000. Indices are 1-based and inclusive. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.",
                "items": {"type": "integer"},
                "type": "array",
            },
            "insert_line_1": {
                "description": "Required parameter of `insert` command. The line number after which to insert the new string. This line number is relative to the state of the file before any insertions in the current tool call have been applied.",
                "type": "integer",
            },
            "str_replace_instruction_reminder": {
                "description": f"Reminder to limit edits to at most {MAX_LINES} lines. Should be exactly this string: '{INSTRUCTION}'",
                "type": "string",
            },
            "new_str_1": {
                "description": "Required parameter of `str_replace` command containing the new string. Can be an empty string to delete content. Required parameter of `insert` command containing the string to insert.",
                "type": "string",
            },
            "old_str_1": {
                "description": "Required parameter of `str_replace` command containing the string in `path` to replace.",
                "type": "string",
            },
            "old_str_start_line_number_1": {
                "description": "The line number of the first line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.",
                "type": "integer",
            },
            "old_str_end_line_number_1": {
                "description": "The line number of the last line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.",
                "type": "integer",
            },
        },
        "required": ["command", "path", "str_replace_instruction_reminder"],
    },
)


def update_str_replace_inputs_retroactively(
    chat_prompt_input: ChatPromptInput,
) -> ChatPromptInput:
    for exchange in chat_prompt_input.chat_history:
        for node in exchange.response_nodes:
            if (
                node.type != ChatResultNodeType.TOOL_USE
                or not node.tool_use
                or node.tool_use.name != "str-replace-editor"
            ):
                continue
            tool_input = node.tool_use.input
            if tool_input.get("command") in ["str_replace", "insert", "view"]:
                new_tool_input = dict()
                new_tool_input["command"] = tool_input["command"]
                new_tool_input["path"] = tool_input["path"]
                new_tool_input["str_replace_instruction_reminder"] = INSTRUCTION
                for k, v in tool_input.items():
                    if k not in ["command", "path"]:
                        new_tool_input[k] = v
                node.tool_use.input = new_tool_input
    return chat_prompt_input


model_config.add_chat_prompt_input_preprocessor(update_str_replace_inputs_retroactively)
model_config.add_chat_prompt_input_preprocessor(convert_history_to_flat_schema)


def eval_response(response: ModelResponse) -> Tuple[bool, str]:
    tool_call, _ = extract_tool_call(response, "str-replace-editor")
    if tool_call is None or tool_call.input.get("command") not in [
        "str_replace",
        "insert",
    ]:
        return True, ""

    if "str_replace_instruction_reminder" not in tool_call.input:
        return False, "Missing str_replace_instruction_reminder parameter"

    if tool_call.input["str_replace_instruction_reminder"] != INSTRUCTION:
        return False, "Incorrect str_replace_instruction_reminder value"

    return True, ""


model_config.eval_func = eval_response
