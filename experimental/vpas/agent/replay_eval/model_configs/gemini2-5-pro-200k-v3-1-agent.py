from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name
from base.prompt_format_chat.prompt_formatter import (
    ChatTokenApportionment,
)
from base.prompt_format_chat.structured_binks_agent_prompt_formatter import (
    StructuredBinksAgentPromptFormatter,
)
from experimental.vpas.agent.replay_eval.model_config import ModelConfig

token_apportionment = ChatTokenApportionment(
    prefix_len=1024 * 2,
    suffix_len=1024 * 2,
    path_len=256,
    message_len=0,
    selected_code_len=8_192,
    chat_history_len=0,
    retrieval_len_per_each_user_guided_file=0,
    retrieval_len_for_user_guided=0,
    retrieval_len=0,
    max_prompt_len=1024 * 200,
    tool_results_len=1024 * 120,
    token_budget_to_trigger_truncation=1024 * 120,
)
prompt_formatter = get_structured_chat_prompt_formatter_by_name(
    "agent-binks-claude-v3",
    token_apportionment,
)
assert isinstance(prompt_formatter, StructuredBinksAgentPromptFormatter)

model_config = ModelConfig(
    name="gemini2-5-pro-200k-v3-1-agent",
    base_model="gemini2.5-pro-prev",
    client_type="google_genai",
    prompt_formatter=prompt_formatter,
)
