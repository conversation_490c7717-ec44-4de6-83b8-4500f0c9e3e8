#
# python research/fastbackward/determined/launch.py experimental/jeff/fb_configs/import-exps/sc2-bs1k-s1k-lr1e6to1e7-resumev4-import-mix-v2.yml
#
# Global batch size is 64 * 8 * 4 / 2 = 1024.
#
# Take about 12 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/85631
# bash research/utils/download_checkpoint.sh c25b4058-8660-4766-84ec-8e4b2e80b382 jeff/eldenv4-plus-imports-v2
#
# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt="/mnt/efs/augment/checkpoints/jeff/eldenv4-plus-imports-v2" \
#     --out-ckpt="/mnt/efs/augment/checkpoints/jeff/eldenv4-plus-imports-v2-ffw" \
#     --model-name="starcoder2-15b"
#
# Quantize the FFW checkpoint from BF16 to FP8
# python base/fastforward/starcoder/quantize_starcoder2.py \
#     --calibration-steps 300 \
#     --log-to-stdout \
#     --ckpt-path "/mnt/efs/augment/checkpoints/jeff/eldenv4-plus-imports-v2-ffw" \
#     --out-path "/mnt/efs/augment/checkpoints/jeff/eldenv4-plus-imports-v2-ffw-fp8" \
#     -d "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/import-mix-v2-dataset-elden-sc2/dataset"


determined:
  description: null
  workspace: Dev
  project: jeff
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 4
  batch_size: 8
  max_iters: 1000
  warmup_iters: 0
  lr_decay_iters: 1000
  block_size: 7936
  min_lr: 1.0e-7
  learning_rate: 1.0e-6
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/import-mix-v2-dataset-elden-sc2/dataset
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/import-mix-v2-dataset-elden-sc2/validation_dataset
  model_vocab_size: 49176
  checkpoint_optimizer_state: False
  checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp2/
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: SC2-15B-IMPORT-MIX-V2-BS1K-S1K-LR1e6to1e7-ResumeV4
  wandb_project: jeff-rag
