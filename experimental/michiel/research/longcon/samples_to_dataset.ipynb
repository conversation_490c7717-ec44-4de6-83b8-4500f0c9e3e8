#%%
%load_ext autoreload
%autoreload 2
#%%
"""Converts Rogue long context samples into indexed dataset for training."""

import json
from functools import partial
from types import SimpleNamespace
from typing import Any, Dict, List
from datetime import datetime

from research.data.spark.pipelines.stages.common import (
    export_indexed_dataset_impl,
)

from experimental.michiel.research.longcon.data_library import generate_prompt
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet
from research.data.rag.utils import pad_pack_tokens, repartition_and_shuffle
from megatron.tokenizer.tokenizer import StarCoderTokenizer

PROMPT_COLUMN = "prompt_tokens"
spark = k8s_session()
#%%
config_dict = {
    # "input": "s3a://augment-temporary/ragdata/test/",
    # "output": "/mnt/efs/augment/data/processed/rag/dataset/test_retrieval/",
    "input": "s3a://michiel-dev-bucket/ragdata/eth64m_fixfim/",
    "output": "/mnt/efs/augment/data/processed/rag/longcon/4k5far1k5near",
    "random_seed": 74912,
    # "seq_len": 1792,
    "seq_len": 6400,
    "max_prefix_tokens": 512,
    "max_suffix_tokens": 256,
    "max_total_near_tokens": 1500,
    # "max_far_tokens": 0,
    "max_far_tokens": 4500,
    "max_prompt_tokens": 7932,
    "max_noisy_far_tokens": 0,
    "max_target_tokens": 256,
    "component_order": ["far_retrieval", "path", "near_retrieval", "prefix", "suffix"],
    "samples_column": "prompt_tokens",
    "num_validation_samples": 50000,
}

config = SimpleNamespace(**config_dict)
#%%
now = datetime.now()
formatted_time = now.strftime('%Y-%m-%d_%H-%M-%S')
step1_uri = f"s3a://augment-temporary/michiel/{formatted_time}/step1/"
step2_uri = f"s3a://augment-temporary/michiel/{formatted_time}/step2/"
input_columns=["prefix", "middle", "suffix", "suffix_offset", "middle_char_start", "middle_char_end", "file_path", "retrieved_chunks"]

tokenizer = StarCoderTokenizer()

result = map_parquet.apply(
    spark,
    partial(
        generate_prompt,
        tokenizer=tokenizer,
        config=config,
    ),
    input_path=config.input,
    output_path=step1_uri,
    input_columns=input_columns,
    output_column=PROMPT_COLUMN,
    ignore_error=True,
    drop_original_columns=True,
)
#%%
result
print(result["status_count"])
print(result["task_info"]["stderr"][0])
#%%
result2 = map_parquet.apply(
    spark,
    partial(
        pad_pack_tokens, seq_len=config.seq_len, tokenizer=tokenizer
    ),
    input_path=step1_uri,
    output_path=step2_uri,
    input_columns=[PROMPT_COLUMN],
    output_column=config.samples_column,
    drop_original_columns=True,
)
#%%
df = spark.read.parquet(step2_uri).select(config.samples_column)
spark.sparkContext.setJobDescription("Shuffling dataset")
df = repartition_and_shuffle(config.random_seed, df)

# # TODO(michiel) fix partition naming
spark.sparkContext.setJobDescription("Creating indexed dataset")
export_indexed_dataset_impl(df, config=config, tokenizer=tokenizer)
with open(config.output + 'config.json', 'w') as f:
    json.dump(config_dict, f, indent=4)
#%%
spark.stop()