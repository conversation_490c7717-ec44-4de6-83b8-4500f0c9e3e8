"""Tools according to the tool abstractions in research.agents.tools."""

import dataclasses
import functools
from collections import OrderedDict
from fnmatch import fnmatch
from pathlib import Path
from textwrap import dedent
from typing import Any, Optional
from unittest.mock import Mock

from base.prompt_format.common import PromptChunk
from base.prompt_format_chat.lib.abstract_formatted_file import AbstractFormattedFile
from base.prompt_format_chat.lib.chat_history_builder import (
    format_chat_history,
    inject_selected_code_into_chat_history,
)
from base.prompt_format_chat.lib.formatted_file_v2 import FormattedFileV2
from base.prompt_format_chat.lib.token_counter import RoughTokenCounter, TokenCounter
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    StructuredChatPromptOutput,
    TokenizedChatPromptOutput,
)
from base.prompt_format_chat.tokenized_qwen_prompt_formatter import (
    StructToTokensQwenPromptFormatter,
)
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from experimental.guy.agent_qa.builtin_tools import QueryOnlyDocumentIndex
from experimental.michiel.research.agentqa.lib.utils import interleave_sequences
from experimental.michiel.research.agentqa.tools import (
    DirectorySubTreeTool as LegacyDirectorySubTreeTool,
)
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    Tool,
    ToolCallLogger,
    ToolImplOutput,
)
from research.core.types import Document
from research.llm_apis.llm_client import (
    LLMClient,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
    ToolParam,
)
from research.models import (
    GenerationOptions,
)
from research.models.fastforward_llama_models import (
    FastForwardQwen25Coder_32B,
)
from research.retrieval.types import Chunk


def create_directory_structure(chunks: list[Chunk]):
    """Create a directory structure from a list of Chunks.

    This function takes a list of Chunks and creates a directory structure
    similar to the one in the DirectorySubTreeTool class. It extracts the file paths
    from the Chunks and builds a hierarchical structure of directories and files.

    Args:
        chunks: List of Chunk objects.

    Returns:
        A dictionary representing the directory structure, with the following format:
        {
            "directory_path": {
                "files": [list of Path objects for files],
                "dirs": [list of Path objects for subdirectories],
                "dir_name_set": {set of directory names}
            },
            ...
        }
    """
    directory_structure = {"": {"files": [], "dirs": [], "dir_name_set": set()}}
    file_paths = [Path(chunk.path) for chunk in chunks if chunk.path]

    # Remove duplicates while preserving order
    seen = set()
    unique_file_paths = []
    for path in file_paths:
        if str(path) not in seen:
            seen.add(str(path))
            unique_file_paths.append(path)

    for path in unique_file_paths:
        # For files with no directory (e.g., 'test.py'), add them to the root directory
        if path.parent == Path(".") or path.parent == Path(""):
            if path.name not in directory_structure[""]["dir_name_set"]:
                directory_structure[""]["files"].append(path)
            continue

        original_path = path  # Keep the original path for file entries
        is_file = True
        while path.name != "":
            parent = path.parent
            parent_str = str(parent) if parent != Path(".") else ""

            if parent_str not in directory_structure:
                directory_structure[parent_str] = {
                    "files": [],
                    "dirs": [],
                    "dir_name_set": set(),
                }

            # Check if already added
            if path.name not in directory_structure[parent_str]["dir_name_set"]:
                if is_file:
                    directory_structure[parent_str]["files"].append(original_path)
                else:
                    directory_structure[parent_str]["dirs"].append(path)
                    directory_structure[parent_str]["dir_name_set"].add(path.name)
            path = parent
            is_file = False

            # If we've reached the root directory, stop
            if parent == Path(".") or parent == Path(""):
                break

    # Sort alphabetically
    for key in directory_structure:
        directory_structure[key]["files"].sort()
        directory_structure[key]["dirs"].sort()

    return directory_structure


def format_directory_subtree(
    directory_structure: dict[str, dict[str, Any]],
    starting_dir_str: str,
    max_directory_chars: int,
    max_file_chars: int,
    max_depth: int = 50,
) -> tuple[str, dict[str, bool]]:
    """Format a directory structure into a readable string representation.

    Args:
        directory_structure: A dictionary representing the directory structure, with the format:
            {
                "directory_path": {
                    "files": [list of Path objects for files],
                    "dirs": [list of Path objects for subdirectories],
                    "dir_name_set": {set of directory names}
                },
                ...
            }
        starting_dir_str: The directory to start from.
        max_directory_chars: Maximum number of characters for directory names.
        max_file_chars: Maximum number of characters for file names.
        max_depth: Maximum depth to traverse.

    Returns:
        A tuple containing:
        - The formatted directory structure as a string.
        - A dictionary with metadata about the formatting, including whether it was truncated.
    """
    spaces_per_level = 2

    # Find max level
    if starting_dir_str not in directory_structure:
        return f"Directory {starting_dir_str} not found.", {"truncated": False}

    # Create a simplified version of the directory structure for easier traversal
    simplified_structure = {}
    for path, content in directory_structure.items():
        simplified_structure[path] = {
            "files": [file.name for file in content["files"]],
            "dirs": [dir.name for dir in content["dirs"]],
            "dir_paths": {},
        }
        # Create a mapping from directory name to full path
        for dir in content["dirs"]:
            if path:
                simplified_structure[path]["dir_paths"][dir.name] = f"{path}/{dir.name}"
            else:
                simplified_structure[path]["dir_paths"][dir.name] = dir.name

    current_path = starting_dir_str
    current_level_idx = 0
    total_dir_char_count = 0
    total_file_char_count = 0

    max_dirs_reached = False
    max_files_reached = False

    max_dir_level = -1
    max_file_level = -1

    # First pass: determine the maximum depth we can display
    paths_to_process = [current_path]
    for idx in range(max_depth):
        if not paths_to_process:
            break

        # Reset for next level
        current_dir_char_count = 0
        current_file_char_count = 0
        next_paths = []

        for path in paths_to_process:
            subtree = simplified_structure[path]
            files = subtree["files"]
            dirs = subtree["dirs"]
            dir_paths = subtree["dir_paths"]

            for file in files:
                current_file_char_count += (
                    len(file) + spaces_per_level * current_level_idx
                )

            for dir in dirs:
                current_dir_char_count += (
                    len(dir) + spaces_per_level * current_level_idx
                )
                next_paths.append(dir_paths[dir])

        if (
            not max_dirs_reached
            and total_dir_char_count + current_dir_char_count > max_directory_chars
        ):
            max_dir_level = current_level_idx - 1
            max_dirs_reached = True
        if (
            not max_files_reached
            and total_file_char_count + current_file_char_count > max_file_chars
        ):
            max_file_level = current_level_idx - 1
            max_files_reached = True

        if max_dirs_reached:
            break
        elif not next_paths:
            if not max_dirs_reached:
                max_dir_level = current_level_idx
            if not max_files_reached:
                max_file_level = current_level_idx
        else:
            current_level_idx += 1
            paths_to_process = next_paths
            total_dir_char_count += current_dir_char_count
            total_file_char_count += current_file_char_count

    # Second pass: create the formatted string
    text = ""

    def format_directory(path, level):
        nonlocal text
        if level > max_dir_level:
            return

        subtree = simplified_structure[path]
        files = subtree["files"]
        dirs = subtree["dirs"]
        dir_paths = subtree["dir_paths"]

        # Add files if we're within the file level limit
        if level <= max_file_level:
            for file in sorted(files):
                indent = " " * spaces_per_level * level
                text += f"{indent}{file}\n"

        # Add directories
        for dir in sorted(dirs):
            indent = " " * spaces_per_level * level
            text += f"{indent}{dir}\n"

            # If we've reached the max directory level, add ellipsis
            if level >= max_dir_level:
                indent = " " * spaces_per_level * (level + 1)
                text += f"{indent}...\n"
            else:
                # Otherwise, recursively format the subdirectory
                format_directory(dir_paths[dir], level + 1)

    # Start formatting from the root directory
    # Special case for the root directory - don't print its name
    subtree = simplified_structure[starting_dir_str]
    files = subtree["files"]
    dirs = subtree["dirs"]
    dir_paths = subtree["dir_paths"]

    # Add directories in the root directory first (to match expected output in tests)
    for dir in sorted(dirs):
        text += f"{dir}\n"
        if max_dir_level > 0:
            format_directory(dir_paths[dir], 1)
        else:
            indent = " " * spaces_per_level
            text += f"{indent}...\n"

    # Add files in the root directory
    if max_file_level >= 0:
        for file in sorted(files):
            text += f"{file}\n"

    format_metadata = {
        "truncated": max_dirs_reached or max_files_reached,
    }
    return text, format_metadata


def format_dialog_as_string(dialog_messages: DialogMessages, char_budget: int) -> str:
    """Format the dialog messages as a string instead of a list of messages."""

    messages_list_of_lists = dialog_messages.get_messages_for_llm_client()
    # flatten
    messages = [message for sublist in messages_list_of_lists for message in sublist]

    used_chars = 0
    message_strings = []
    for message in reversed(messages):
        if isinstance(message, TextPrompt):
            message_str = f"User message:\n{message.text}\n"
        elif isinstance(message, TextResult):
            message_str = f"Assistant message:\n{message.text}\n"
        elif isinstance(message, ToolCall):
            message_str = f"Tool call:{message.tool_name}:\n"
            message_str += str(message.tool_input) + "\n"
        elif isinstance(message, ToolFormattedResult):
            message_str = f"Tool result:\n{message.tool_name}:\n"
            message_str += message.tool_output + "\n"
        else:
            raise ValueError(f"Unknown message type: {type(message)}")

        if used_chars + len(message_str) > char_budget:
            break
        used_chars += len(message_str)
        message_strings.append(message_str)

    return "\n".join(reversed(message_strings))


class CodebaseRetrievalQueryGenerationTool(Tool):
    """A tool that generates one or more structured queries to a codebase retriever."""

    name = "ask_for_codebase_snippets"

    description = dedent(
        """\
        When asked about a particular object or code section, this will generate one or more queries to retrieve relevant codebase information.
        Make sure to ask for concrete sections of code, do not ask high-level questions.
        If you need to know how multiple parts of the codebase interact, ask about each part separately.
        Ask for at most three sections at one time.
        """
    )

    request_code_description = dedent(
        """\
        Description of the codebase section or snippet to ask for.
        Favor short low-level descriptions over high-level questions.
        Do not put paths in here - use the path field instead if you want to specify a path.

        Examples of good uses of description:
        {'description': 'inherits from foo class'}
        {'description': 'test for MainClass'}
        {'description': 'display of purchase button'}

        Examples of bad uses of description:
        Too high level
        {'description': 'code that deals with customers'}
        Describes multiple sections likely to be in different parts of code
        {'description': 'code that deals with customers and orders'}
        """
    )

    request_code_path = dedent(
        """\
        Optional path to the codebase section or snippet to ask for.
        May be a full path or a partial path (e.g. "**/foo.*").
        Do NOT specify unless you are completely certain the path exists in the codebase, either from previously seen snippets or from the subtree directory tool.
        If you are not certain, just leave out this field.

        Examples of good uses of path:
        You have seen that foo class is in a folder foo with subfolders and files.
        {'description': 'inherits from foo class', 'path': '**/foo/**'}
        The user asked "foo in src".
        {'description': 'foo', 'path': '**/src/**'}
        The user asked a question about a jsonnet config file.
        {'description': 'jsonnet config file', 'path': '**/*.jsonnet'}

        Examples of bad uses of path:
        You are not sure the path exists in the codebase.
        {'description': 'foo', 'path': '**/foo/**'}
        The user asked "foo in src", but you are not sure if src exists.
        {'description': 'foo', 'path': '**/src/**'}
        """
    )

    request_code_contains_string = dedent(
        """\
        Optional field to specify a string that should be present in the snippet or the symbol name.
        If specified, will prioritize snippets which either contain this string or are part of a symbol with this name.
        The main reason to use this field is when you are looking for a symbol (like class or function) with a specific name.
        The other reason to use this field is when the user explicitly asks for a specific string, e.g. a variable or an error.

        Examples of good uses of contains_string:
        You are looking for a class with a specific name.
        {'description': 'foo class', 'contains_string': 'Foo'}
        You are looking for a specific log line.
        {'description': 'baz exception log line', 'contains_string': 'type not found'}
        The user asked "foo_var in src".
        {'description': 'foo_var', 'path': '**/src/**', 'contains_string': 'foo_var'}

        Examples of bad uses of contains_string:
        The user asked a general question: "test of MainClass", but you don't know what this test is called. Don't guess, leave out the contains_string.
        {'description': 'test of MainClass', 'contains_string': 'MainClassTest'}
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "code_section_requests": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "description": {
                            "type": "string",
                            "description": request_code_description,
                        },
                        "path": {
                            "type": "string",
                            "description": request_code_path,
                        },
                        "contains_string": {
                            "type": "string",
                            "description": request_code_contains_string,
                        },
                    },
                    "required": [
                        "description",
                    ],
                },
            },
        },
        "required": ["code_section_requests"],
    }


class FinalRoundQueryGenerationTool(Tool):
    """A tool that generates one or more structured queries to a codebase retriever."""

    name = "ask_for_codebase_snippets"

    description = dedent(
        """\
        When asked about a particular object or code section, this will generate one or more queries to retrieve relevant codebase information.
        Make sure to ask for concrete sections of code, do not ask high-level questions.
        If you need to know how multiple parts of the codebase interact, ask about each part separately.
        The number of queries you generate will be used to determine the amount of resources allocated to each query.
        Most commonly you should generate 2-3 queries.
        Generate a single query if you need a highly targeted piece of information.
        Generate more than 3 queries only rarely if you know for sure that you need a lot of different information.
        """
    )

    request_code_description = dedent(
        """\
        Description of the codebase section or snippet to ask for.
        Favor short low-level descriptions over high-level questions.
        Do not put paths in here - use the path field instead if you want to specify a path.

        Examples of good uses of description:
        {'description': 'inherits from foo class'}
        {'description': 'test for MainClass'}
        {'description': 'display of purchase button'}

        Examples of bad uses of description:
        Too high level
        {'description': 'code that deals with customers'}
        Describes multiple sections likely to be in different parts of code
        {'description': 'code that deals with customers and orders'}
        """
    )

    request_code_path = dedent(
        """\
        Optional path to the codebase section or snippet to ask for.
        May be a full path or a partial path (e.g. "**/foo.*").
        Do NOT specify unless you are completely certain the path exists in the codebase, either from previously seen snippets or from the subtree directory tool.
        If you are not certain, just leave out this field.

        Examples of good uses of path:
        You have seen that foo class is in a folder foo with subfolders and files.
        {'description': 'inherits from foo class', 'path': '**/foo/**'}
        The user asked "foo in src".
        {'description': 'foo', 'path': '**/src/**'}
        The user asked to see a class in a particular file.
        {'description': 'baz implementation', 'path': 'bar/foo.py'}

        Examples of bad uses of path:
        You are not sure the path exists in the codebase.
        {'description': 'foo', 'path': '**/foo/**'}
        The user asked "foo in src", but you are not sure if src exists.
        {'description': 'foo', 'path': '**/src/**'}
        """
    )

    request_code_contains_string = dedent(
        """\
        Optional field to specify a string that should be present in the snippet or the symbol name.
        If specified, will prioritize snippets which either contain this string or are part of a symbol with this name.
        The main reason to use this field is when you are looking for a symbol (like class or function) with a specific name.
        The other reason to use this field is when the user explicitly asks for a specific string, e.g. a variable or an error.

        Examples of good uses of contains_string:
        You are looking for a class with a specific name.
        {'description': 'foo class', 'contains_string': 'Foo'}
        You are looking for a specific log line.
        {'description': 'baz exception log line', 'contains_string': 'type not found'}
        The user asked "foo_var in src".
        {'description': 'foo_var', 'path': '**/src/**', 'contains_string': 'foo_var'}

        Examples of bad uses of contains_string:
        The user asked a general question: "test of MainClass", but you don't know what this test is called. Don't guess, leave out the contains_string.
        {'description': 'test of MainClass', 'contains_string': 'MainClassTest'}
        """
    )

    is_critical_description = dedent(
        """\
        Optional field to specify whether a query is critical to the answer.
        If specified, will heavily prioritize snippets that result from this query.
        For example, if a question is primarily about a specific file, you should mark the query that asks for that file as critical.
        Since resources are limited, you should only mark a query as critical if it is critical to the answer.
        The more queries you mark as critical, the more resources will be consumed and the less it is any critical query will receive enough resources.
        Be very conservative about marking queries as critical.
        Typically, only one of the queries should be marked as critical.
        More than one critical query is allowed but should be uncommon.
        If you are not sure, leave this field out.
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "code_section_requests": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "description": {
                            "type": "string",
                            "description": request_code_description,
                        },
                        "path": {
                            "type": "string",
                            "description": request_code_path,
                        },
                        "contains_string": {
                            "type": "string",
                            "description": request_code_contains_string,
                        },
                        "critical": {
                            "type": "boolean",
                            "description": is_critical_description,
                        },
                    },
                    "required": [
                        "description",
                    ],
                },
            },
        },
        "required": ["code_section_requests"],
    }


class HighLevelCodebaseRetrievalAgent(LLMTool):
    """An agent that performs codebase retrieval."""

    name = "request_codebase_information"

    description = dedent(
        """\
        Use this tool to request information from the codebase.
        It will return relevant snippets for the requested information.
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "information_request": {
                "type": "string",
                "description": "A description of the information you need.",
            },
        },
        "required": ["information_request"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        retriever: QueryOnlyDocumentIndex,
        llm_client: LLMClient,
        max_tool_chars: int = 50000,
    ):
        """
        Args:
            tool_call_logger: The tool call logger to use.
            retriever: The tool will use this retriever to search the codebase.
            max_tool_chars: The tool will return at most this many characters.
        """
        super().__init__(tool_call_logger)
        self.retriever = retriever
        self.llm_client = llm_client
        self.max_tool_chars = max_tool_chars

        # Hardcoded things that may need to be configured later
        self.max_num_chunks = 500

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the tool."""

        # Get low level queries
        retriever_queries = self.get_retriever_queries(
            tool_input, dialog_messages=dialog_messages
        )

        # Retrieve for each query
        query_result_list = []
        for retriever_query in retriever_queries:
            chunks, scores = retrieve(
                self.retriever,
                request_description=retriever_query["description"],
                request_path=retriever_query.get("path", ""),
                request_contains_string=retriever_query.get("contains_string", ""),
                max_num_chunks=self.max_num_chunks,
            )
            query_result_list.append((retriever_query, chunks, scores))

        # Format the results
        tool_output_str, included_chunks = self.format_retrieval(query_result_list)

        auxiliary_data = {
            "retrieved_chunks": included_chunks,
        }

        return ToolImplOutput(
            tool_output=tool_output_str,
            tool_result_message="Codebase information retrieved",
            auxiliary_data=auxiliary_data,
        )

    def get_retriever_queries(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> list[dict[str, Any]]:
        """Generate the queries to the retriever."""

        MAX_DIALOG_CHARS = 30000

        information_request = tool_input["information_request"]

        query_tool_param = ToolParam(
            name=CodebaseRetrievalQueryGenerationTool.name,
            description=CodebaseRetrievalQueryGenerationTool.description,
            input_schema=CodebaseRetrievalQueryGenerationTool.input_schema,
        )

        QUERY_GENERATION_SYSTEM_PROMPT = dedent(
            f"""\
            You are a tool-calling agent that generates queries for a dense retrieval tool to gather information from a codebase to help answer a request for information.
            Call the {query_tool_param.name} tool to generate queries.
            Please pay careful attention to the tool description to optimally use the tool.
            """
        )

        message_str = ""
        message_str += QUERY_GENERATION_SYSTEM_PROMPT
        message_str += f"The information request is: {information_request}\n"

        DIALOG_PROMPT = dedent(
            """\
            The information request is in the context of an agent attempting to execute a task.
            Below are some messages of the dialogue of this agent and the user:
            """
        )

        if dialog_messages is not None:
            message_str += DIALOG_PROMPT
            message_str += format_dialog_as_string(dialog_messages, MAX_DIALOG_CHARS)

        message_str += (
            f"Once again, the information request is: {information_request}\n"
        )

        messages = []
        messages.append(TextPrompt(text=message_str))

        response_messages, _ = self.llm_client.generate(
            messages=[messages],
            max_tokens=2048,
            temperature=0.0,
            tools=[query_tool_param],
            tool_choice={"type": "tool", "name": query_tool_param.name},
        )
        tool_calls = [
            message for message in response_messages if isinstance(message, ToolCall)
        ]
        assert (
            len(tool_calls) == 1
        ), f"Expected exactly one tool call, got {len(tool_calls)} tool calls in {response_messages}"
        response = tool_calls[0]
        return response.tool_input["code_section_requests"]

    def format_retrieval(
        self, query_result_list: list[tuple[dict[str, Any], list[Chunk], list[float]]]
    ) -> tuple[str, list[Chunk]]:
        """Format the retrieval results."""

        tool_output_text = "The following code sections were retrieved:\n"
        unneeded_token_counter = RoughTokenCounter()
        formatted_files: dict[str, AbstractFormattedFile] = OrderedDict()
        char_count = 0

        # We want to provide each generated query with equal budget, so we interleave
        # chunks from each query and stop when we reach the budget.
        chunk_sequences = [chunks for _, chunks, _ in query_result_list]
        interleaved_chunks = interleave_sequences(chunk_sequences)
        included_chunks = []

        for chunk in interleaved_chunks:
            prompt_chunk = PromptChunk(
                text=chunk.text,
                path=chunk.path or "",
                unique_id=chunk.id,
                origin="dense_retriever",
                char_start=chunk.char_offset,
                char_end=chunk.char_offset + chunk.length,
                blob_name=chunk.parent_doc.id,
            )
            file_id = chunk.parent_doc.id
            if file_id not in formatted_files:
                old_formatted_file = FormattedFileV2(unneeded_token_counter)
            else:
                old_formatted_file = formatted_files[file_id]

            new_formatted_file = old_formatted_file.add_chunk(prompt_chunk)
            new_len = len(new_formatted_file.get_file_str_and_tok_ct()[0])
            old_len = len(old_formatted_file.get_file_str_and_tok_ct()[0])
            added_len = new_len - old_len
            if char_count + added_len > self.max_tool_chars:
                break

            char_count += added_len
            formatted_files[file_id] = new_formatted_file
            included_chunks.append(chunk)

        for formatted_file in formatted_files.values():
            file_str, _ = formatted_file.get_file_str_and_tok_ct()
            file_path = formatted_file.sorted_chunks[0].path
            tool_output_text += f"Path: {file_path}\n{file_str}\n"

        return tool_output_text, included_chunks

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Retrieving codebase information: {tool_input['information_request']}"


def build_query_rewriter_prompt_given_initial_retrieval(
    information_request: str,
    initial_retrieval_chunks: list[Chunk] | None = None,
    dialog_messages: Optional[DialogMessages] = None,
    max_dialog_chars: int = 30000,
    max_initial_retrieval_chars: int = 10_000,
    max_directory_chars: int = 10_000,
    formatted_directory_output: str | None = None,
) -> tuple[list[dict[str, Any]], list[Chunk]]:
    """Generate the queries to the retriever."""

    query_tool_param = ToolParam(
        name=CodebaseRetrievalQueryGenerationTool.name,
        description=CodebaseRetrievalQueryGenerationTool.description,
        input_schema=CodebaseRetrievalQueryGenerationTool.input_schema,
    )

    formatted_initial_retrieval: str | None = None

    if initial_retrieval_chunks is not None:
        formatted_initial_retrieval, included_chunks = format_retrieval(
            initial_retrieval_chunks, max_initial_retrieval_chars
        )
        if max_directory_chars > 0:
            directory_structure = create_directory_structure(initial_retrieval_chunks)

            formatted_directory_output, _ = format_directory_subtree(
                directory_structure,
                "",
                max_directory_chars // 2,
                max_directory_chars // 2,
            )

            formatted_directory_output = f"""\
The directory structure is:\n{formatted_directory_output}

Keep in mind that this may not be a full directory subtree, only up to a character limit.
"""

        else:
            formatted_directory_output = None
    else:
        included_chunks = []

    if formatted_initial_retrieval and formatted_directory_output:
        extra_instruction = "\nMake sure to also look at the directory tree and previous codebase snippets to guide your search."
    elif formatted_initial_retrieval:
        extra_instruction = "\nMake sure to also look at the previous codebase snippets to guide your search."
    elif formatted_directory_output:
        extra_instruction = (
            "\nMake sure to also look at the directory tree to guide your search."
        )
    else:
        extra_instruction = ""

    message_str = f"""\
The information request is: {information_request}
{formatted_directory_output or ""}
{formatted_initial_retrieval or ""}
You are a tool-calling agent that generates queries for a dense retrieval tool to gather information from a codebase to help answer a request for information.
Call the {query_tool_param.name} tool to generate queries.
Please pay careful attention to the tool description to optimally use the tool.
Look at the information gathered already, and determine what additional information is missing.{extra_instruction}
"""

    DIALOG_PROMPT = """\
The information request is in the context of an agent attempting to execute a task.
Below are some messages of the dialogue of this agent and the user:
"""

    if dialog_messages is not None:
        message_str += DIALOG_PROMPT
        message_str += format_dialog_as_string(dialog_messages, max_dialog_chars)

    message_str += f"Once again, the information request is: {information_request}\n"

    messages = []
    messages.append(TextPrompt(text=message_str))
    return messages, included_chunks


def retrieve(
    retriever: QueryOnlyDocumentIndex,  # type: ignore
    request_description: str,
    request_path: str,
    request_contains_string: str,
    max_num_chunks: int = 1024,
) -> tuple[list[Chunk], list[float]]:
    """Perform retrieval for the current state."""
    query = ChatRetrieverPromptInput(
        prefix="",
        suffix="",
        path=request_path,
        message=request_description,
        selected_code="",
        chat_history=[],
    )
    chunks, scores = retriever.query(query, top_k=max_num_chunks)
    filtered_chunks = []
    filtered_scores = []
    path_filtered_chunks = []
    path_filtered_scores = []
    string_filtered_chunks = []
    string_filtered_scores = []
    for idx, chunk in enumerate(chunks):
        if (
            chunk.path is not None
            and request_path != ""
            and not fnmatch(chunk.path, request_path)
            and not fnmatch("/" + chunk.path, request_path)
        ):
            path_filtered_chunks.append(chunk)
            path_filtered_scores.append(scores[idx])
            continue  # Skip this chunk

        # Check if the chunk or its header (e.g. class/function name) contains the string
        if request_contains_string != "":
            chunk_header = chunk.header
            if (
                request_contains_string not in chunk.text
                and request_contains_string not in chunk_header
            ):
                string_filtered_chunks.append(chunk)
                string_filtered_scores.append(scores[idx])
                continue  # Skip this chunk
        filtered_chunks.append(chunk)  # Add the chunk to the filtered list
        filtered_scores.append(scores[idx])  # Add the score to the filtered list

    # Combine the filtered chunks and scores, prioritizing fully matching, then string matching, then path matching
    combined_chunks = filtered_chunks + string_filtered_chunks + path_filtered_chunks
    combined_scores = filtered_scores + string_filtered_scores + path_filtered_scores
    output_chunks = combined_chunks[:max_num_chunks]
    output_scores = combined_scores[:max_num_chunks]
    return output_chunks, output_scores


def format_retrieval(chunks, max_char_count: int) -> tuple[str, list[Chunk]]:
    """Format the retrieval results."""

    # We want to provide each generated query with equal budget, so we interleave
    # chunks from each query and stop when we reach the budget.
    tool_output_text = "The following code sections were retrieved:\n"
    unneeded_token_counter = RoughTokenCounter()
    formatted_files: dict[str, AbstractFormattedFile] = OrderedDict()
    char_count = 0
    used_chunks = []

    for chunk in chunks:
        prompt_chunk = PromptChunk(
            text=chunk.text,
            path=chunk.path or "",
            unique_id=chunk.id,
            origin="dense_retriever",
            char_start=chunk.char_offset,
            char_end=chunk.char_offset + chunk.length,
            blob_name=chunk.parent_doc.id,
        )
        file_id = chunk.parent_doc.id
        if file_id not in formatted_files:
            old_formatted_file = FormattedFileV2(unneeded_token_counter)
        else:
            old_formatted_file = formatted_files[file_id]

        new_formatted_file = old_formatted_file.add_chunk(prompt_chunk)
        new_len = len(new_formatted_file.get_file_str_and_tok_ct()[0])
        old_len = len(old_formatted_file.get_file_str_and_tok_ct()[0])
        added_len = new_len - old_len
        if char_count + added_len > max_char_count:
            break

        char_count += added_len
        formatted_files[file_id] = new_formatted_file
        used_chunks.append(chunk)

    if len(used_chunks) == 0:
        return "", []

    # Convert formatted files to output string
    for formatted_file in formatted_files.values():
        file_str, _ = formatted_file.get_file_str_and_tok_ct()
        file_path = formatted_file.sorted_chunks[0].path
        tool_output_text += f"Path: {file_path}\n\n```\n{file_str}\n```\n\n"

    return tool_output_text, used_chunks


def followup_query_rewrites_given_initial_retrieval(
    information_request: str,
    llm_client: LLMClient,
    initial_retrieval_chunks: list[Chunk],
    dialog_messages: Optional[DialogMessages] = None,
    max_dialog_chars: int = 30000,
    max_initial_retrieval_chars: int = 10000,
    max_directory_chars: int = 10000,
) -> tuple[list[dict[str, Any]], list[Chunk]]:
    """Generate the queries to the retriever given the initial retrieval."""

    query_tool_param = ToolParam(
        name=CodebaseRetrievalQueryGenerationTool.name,
        description=CodebaseRetrievalQueryGenerationTool.description,
        input_schema=CodebaseRetrievalQueryGenerationTool.input_schema,
    )

    messages, used_initial_retrieval_chunks = (
        build_query_rewriter_prompt_given_initial_retrieval(
            information_request,
            initial_retrieval_chunks,
            dialog_messages=dialog_messages,
            max_dialog_chars=max_dialog_chars,
            max_initial_retrieval_chars=max_initial_retrieval_chars,
            max_directory_chars=max_directory_chars,
        )
    )

    response_messages, _ = llm_client.generate(
        messages=[messages],
        max_tokens=2048,
        temperature=0.0,
        tools=[query_tool_param],
        tool_choice={"type": "tool", "name": query_tool_param.name},
    )
    tool_calls = [
        message for message in response_messages if isinstance(message, ToolCall)
    ]
    assert (
        len(tool_calls) == 1
    ), f"Expected exactly one tool call, got {len(tool_calls)} tool calls in {response_messages}"
    response = tool_calls[0]
    return response.tool_input["code_section_requests"], used_initial_retrieval_chunks


class HighLevelCodebaseMultiRetrievalAgent(LLMTool):
    """An agent that performs codebase retrieval."""

    name = "request_codebase_information"

    description = dedent(
        """\
        Use this tool to request information from the codebase.
        It will return relevant snippets for the requested information.
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "information_request": {
                "type": "string",
                "description": "A description of the information you need.",
            },
        },
        "required": ["information_request"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        retriever: QueryOnlyDocumentIndex,
        llm_client: LLMClient,
        max_tool_chars: int = 50000,
        max_intermediate_tool_chars: int = 100000,
    ):
        """
        Args:
            tool_call_logger: The tool call logger to use.
            retriever: The tool will use this retriever to search the codebase.
            max_tool_chars: The tool will return at most this many characters.
        """
        super().__init__(tool_call_logger)
        self.retriever = retriever
        self.llm_client = llm_client
        self.max_tool_chars = max_tool_chars
        self.max_intermediate_tool_chars = max_intermediate_tool_chars

        # Hardcoded things that may need to be configured later
        self.max_num_chunks = 1024

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the tool."""
        # First round retrieval
        retriever_queries = self.get_retriever_queries(
            tool_input, dialog_messages=dialog_messages
        )

        query_result_list = []
        for retriever_query in retriever_queries:
            chunks, scores = retrieve(
                self.retriever,
                request_description=retriever_query["description"],
                request_path=retriever_query.get("path", ""),
                request_contains_string=retriever_query.get("contains_string", ""),
                max_num_chunks=self.max_num_chunks,
            )
            query_result_list.append((retriever_query, chunks, scores))

        # Final round retrieval
        final_round_queries = self.get_final_round_retriever_queries(
            tool_input,
            query_result_list,
            dialog_messages=dialog_messages,
        )

        final_round_results = []
        for retriever_query in final_round_queries:
            chunks, scores = retrieve(
                self.retriever,
                request_description=retriever_query["description"],
                request_path=retriever_query.get("path", ""),
                request_contains_string=retriever_query.get("contains_string", ""),
                max_num_chunks=self.max_num_chunks,
            )
            final_round_results.append((retriever_query, chunks, scores))

        # Format the results
        tool_output_str, include_chunks = self.format_retrieval_for_output(
            final_round_results, self.max_tool_chars
        )

        auxiliary_data = {
            "retriever_queries": retriever_queries,
            "final_round_queries": final_round_queries,
            "retrieved_chunks": include_chunks,
        }

        return ToolImplOutput(
            tool_output=tool_output_str,
            tool_result_message="Codebase information retrieved",
            auxiliary_data=auxiliary_data,
        )

    def get_retriever_queries(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> list[dict[str, Any]]:
        """Generate the queries to the retriever."""

        MAX_DIALOG_CHARS = 30000

        information_request = tool_input["information_request"]

        query_tool_param = ToolParam(
            name=CodebaseRetrievalQueryGenerationTool.name,
            description=CodebaseRetrievalQueryGenerationTool.description,
            input_schema=CodebaseRetrievalQueryGenerationTool.input_schema,
        )

        QUERY_GENERATION_SYSTEM_PROMPT = dedent(
            f"""\
            You are a tool-calling agent that generates queries for a dense retrieval tool to gather information from a codebase to help answer a request for information.
            Call the {query_tool_param.name} tool to generate queries.
            Please pay careful attention to the tool description to optimally use the tool.
            """
        )

        message_str = ""
        message_str += QUERY_GENERATION_SYSTEM_PROMPT
        message_str += f"The information request is: {information_request}\n"

        DIALOG_PROMPT = dedent(
            """\
            The information request is in the context of an agent attempting to execute a task.
            Below are some messages of the dialogue of this agent and the user:
            """
        )

        if dialog_messages is not None:
            message_str += DIALOG_PROMPT
            message_str += format_dialog_as_string(dialog_messages, MAX_DIALOG_CHARS)

        message_str += (
            f"Once again, the information request is: {information_request}\n"
        )

        messages = []
        messages.append(TextPrompt(text=message_str))

        response_messages, _ = self.llm_client.generate(
            messages=[messages],
            max_tokens=2048,
            temperature=0.0,
            tools=[query_tool_param],
            tool_choice={"type": "tool", "name": query_tool_param.name},
        )
        tool_calls = [
            message for message in response_messages if isinstance(message, ToolCall)
        ]
        assert (
            len(tool_calls) == 1
        ), f"Expected exactly one tool call, got {len(tool_calls)} tool calls in {response_messages}"
        return tool_calls[0].tool_input["code_section_requests"]

    def get_final_round_retriever_queries(
        self,
        tool_input: dict[str, Any],
        query_result_list: list[tuple[dict[str, Any], list[Chunk], list[float]]],
        dialog_messages: Optional[DialogMessages] = None,
        used_initial_retrieval_chunks: Optional[list[Chunk]] = None,
    ) -> list[dict[str, Any]]:
        """Generate queries for final round retrieval using first round results as context."""
        information_request = tool_input["information_request"]

        context = self.format_retrieval_for_intermediate(
            query_result_list,
            max_char_count=self.max_intermediate_tool_chars,
            used_initial_retrieval_chunks=used_initial_retrieval_chunks,
        )

        query_tool_param = ToolParam(
            name=FinalRoundQueryGenerationTool.name,
            description=FinalRoundQueryGenerationTool.description,
            input_schema=FinalRoundQueryGenerationTool.input_schema,
        )

        FINAL_ROUND_SYSTEM_PROMPT = dedent(
            f"""\
            You are a tool-calling agent that generates queries for a final round of dense retrieval.
            Your task is to analyze the code snippets from the first round and generate a final set of queries to:
            1. Condense and expand the most relevant information from the first query.
              We will not show any results of the first query to the user, all the information has to be contained in this round.
              Typically, if one particular file was extremely relevant, there should be a query to retrieve the entire file.
            2. Find any missing information that was not found in the first round.

            Call the {query_tool_param.name} tool to generate these queries.
            Please pay careful attention to the tool description to optimally use the tool.
            """
        )

        message_str = ""
        message_str += FINAL_ROUND_SYSTEM_PROMPT
        message_str += f"The original information request is: {information_request}\n\n"
        message_str += context

        if dialog_messages is not None:
            message_str += "\nThis request is in the context of an agent dialogue:\n"
            message_str += format_dialog_as_string(dialog_messages, 30000)

        message_str += f"\nBased on these code snippets and the original request: {information_request}\n"
        message_str += "Please generate queries to find related and referenced code that would help understand these snippets better.\n"

        messages = []
        messages.append(TextPrompt(text=message_str))

        response_messages, _ = self.llm_client.generate(
            messages=[messages],
            max_tokens=2048,
            temperature=0.0,
            tools=[query_tool_param],
            tool_choice={"type": "tool", "name": query_tool_param.name},
        )
        tool_calls = [
            message for message in response_messages if isinstance(message, ToolCall)
        ]
        assert (
            len(tool_calls) == 1
        ), f"Expected exactly one tool call, got {len(tool_calls)} tool calls in {response_messages}"
        response = tool_calls[0]
        return response.tool_input["code_section_requests"]

    def format_retrieval_for_intermediate(
        self,
        query_result_list: list[tuple[dict[str, Any], list[Chunk], list[float]]],
        max_char_count: int,
        used_initial_retrieval_chunks: Optional[list[Chunk]] = None,
    ) -> str:
        """Format the retrieval results."""

        # We want to provide each generated query with equal budget, so we interleave
        # chunks from each query and stop when we reach the budget.
        tool_output_text = "The following code sections were retrieved:\n"
        unneeded_token_counter = RoughTokenCounter()
        formatted_files: dict[str, AbstractFormattedFile] = OrderedDict()
        char_count = 0
        chunk_sequences = [chunks for _, chunks, _ in query_result_list]

        interleaved_chunks = list(interleave_sequences(chunk_sequences))
        if used_initial_retrieval_chunks is not None:
            interleaved_chunks = interleave_sequences(
                [
                    used_initial_retrieval_chunks,
                    interleaved_chunks,
                ]
            )

        for chunk in interleaved_chunks:
            prompt_chunk = PromptChunk(
                text=chunk.text,
                path=chunk.path or "",
                unique_id=chunk.id,
                origin="dense_retriever",
                char_start=chunk.char_offset,
                char_end=chunk.char_offset + chunk.length,
                blob_name=chunk.parent_doc.id,
            )
            file_id = chunk.parent_doc.id
            if file_id not in formatted_files:
                old_formatted_file = FormattedFileV2(unneeded_token_counter)
            else:
                old_formatted_file = formatted_files[file_id]

            new_formatted_file = old_formatted_file.add_chunk(prompt_chunk)
            new_len = len(new_formatted_file.get_file_str_and_tok_ct()[0])
            old_len = len(old_formatted_file.get_file_str_and_tok_ct()[0])
            added_len = new_len - old_len
            if char_count + added_len > max_char_count:
                break

            char_count += added_len
            formatted_files[file_id] = new_formatted_file

        # Convert formatted files to output string
        for formatted_file in formatted_files.values():
            file_str, _ = formatted_file.get_file_str_and_tok_ct()
            file_path = formatted_file.sorted_chunks[0].path
            tool_output_text += f"Path: {file_path}\n{file_str}\n"

        return tool_output_text

    def format_retrieval_for_output(
        self,
        query_result_list: list[tuple[dict[str, Any], list[Chunk], list[float]]],
        max_char_count: int,
        used_initial_retrieval_chunks: Optional[list[Chunk]] = None,
    ) -> tuple[str, list[Chunk]]:
        """Format the retrieval results."""

        tool_output_text = "The following code sections were retrieved:\n"
        unneeded_token_counter = RoughTokenCounter()
        formatted_files: dict[str, AbstractFormattedFile] = OrderedDict()

        include_chunks = []
        # If `used_initial_retrieval_chunks` is provided, we include those chunks first.
        # We adjust the remaining budget accordingly.
        if used_initial_retrieval_chunks is not None:
            for chunk in used_initial_retrieval_chunks:
                chunk_chars = len(chunk.text) + len(chunk.path or "")
                if chunk_chars <= max_char_count:
                    max_char_count -= chunk_chars
                    include_chunks.append(chunk)
                else:
                    break
            used_initial_retrieval_chunks_set = set(include_chunks)
        else:
            used_initial_retrieval_chunks_set = set()

        # We allow the query generation call to specify certain queries as 'critical'.
        # Critical queries get a configurably increased budget.
        critical_weight = 2.0
        non_critical_weight = 1.0

        # Calculate the budget for each query based on the number of critical and non-critical queries.
        num_critical_queries = 0
        num_non_critical_queries = 0
        for query, _, _ in query_result_list:
            critical = query.get("critical", False)
            if critical:
                num_critical_queries += 1
            else:
                num_non_critical_queries += 1

        denominator = (
            critical_weight * num_critical_queries
            + non_critical_weight * num_non_critical_queries
        )
        critical_budget = (critical_weight / denominator) * max_char_count
        non_critical_budget = (non_critical_weight / denominator) * max_char_count

        # We fill each budget separately without overflowing
        # This means we may not use all the budget if we don't have enough chunks.
        for query, chunks, scores in query_result_list:
            critical = query.get("critical", False)
            budget = critical_budget if critical else non_critical_budget
            char_count = 0
            for chunk in chunks:
                if chunk in used_initial_retrieval_chunks_set:
                    continue
                chars_used = len(chunk.text) + len(chunk.path or "")
                if char_count + chars_used > budget:
                    break
                char_count += chars_used
                include_chunks.append(chunk)

        for chunk in include_chunks:
            prompt_chunk = PromptChunk(
                text=chunk.text,
                path=chunk.path or "",
                unique_id=chunk.id,
                origin="dense_retriever",
                char_start=chunk.char_offset,
                char_end=chunk.char_offset + chunk.length,
                blob_name=chunk.parent_doc.id,
            )
            file_id = chunk.parent_doc.id
            if file_id not in formatted_files:
                old_formatted_file = FormattedFileV2(unneeded_token_counter)
            else:
                old_formatted_file = formatted_files[file_id]

            new_formatted_file = old_formatted_file.add_chunk(prompt_chunk)
            formatted_files[file_id] = new_formatted_file

        for formatted_file in formatted_files.values():
            file_str, _ = formatted_file.get_file_str_and_tok_ct()
            file_path = formatted_file.sorted_chunks[0].path
            tool_output_text += f"Path: {file_path}\n{file_str}\n"

        return tool_output_text, include_chunks

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        print("\n")
        return f"Retrieving codebase information: {tool_input['information_request']}"


class HighLevelCodebase3StepRetrievalAgent(HighLevelCodebaseMultiRetrievalAgent):
    """An agent that performs codebase retrieval."""

    name = "request_codebase_information"

    description = dedent(
        """\
        Use this tool to request information from the codebase.
        It will return relevant snippets for the requested information.
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "information_request": {
                "type": "string",
                "description": "A description of the information you need.",
            },
        },
        "required": ["information_request"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        retriever: QueryOnlyDocumentIndex,
        llm_client: LLMClient,
        max_tool_chars: int = 50000,
        max_intermediate_tool_chars: int = 100000,
        max_initial_retrieval_chars: int = 10_000,
        max_directory_chars: int = 10_000,
        verbose: bool = False,
    ):
        """
        Args:
            tool_call_logger: The tool call logger to use.
            retriever: The tool will use this retriever to search the codebase.
            max_tool_chars: The tool will return at most this many characters.
        """
        super().__init__(
            tool_call_logger,
            retriever,
            llm_client,
            max_tool_chars,
            max_intermediate_tool_chars,
        )
        # Hardcoded things that may need to be configured later
        self.max_num_chunks = 1024
        self.max_initial_retrieval_chars = max_initial_retrieval_chars
        self.max_directory_chars = max_directory_chars
        self.verbose = verbose

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the tool."""

        if self.verbose:
            print(
                "[HighLevelCodebase3StepRetrievalAgent] original request: ",
                tool_input["information_request"],
            )

        # First round initial retrieval -- just vanilla chatanol
        initial_retrievals, initial_scores = retrieve(
            self.retriever,
            tool_input["information_request"],
            "",  # request_path
            "",  # request_contains_string
            self.max_num_chunks,
        )

        # First round retrieval
        retriever_queries, used_initial_retrieval_chunks = (
            followup_query_rewrites_given_initial_retrieval(
                tool_input["information_request"],
                self.llm_client,
                initial_retrievals,
                dialog_messages=dialog_messages,
                max_initial_retrieval_chars=self.max_initial_retrieval_chars,
                max_directory_chars=self.max_directory_chars,
            )
        )

        if self.verbose:
            print(
                "[HighLevelCodebase3StepRetrievalAgent] query rewrites: ",
                retriever_queries,
            )

        query_result_list = []
        for retriever_query in retriever_queries:
            chunks, scores = retrieve(
                self.retriever,
                request_description=retriever_query["description"],
                request_path=retriever_query.get("path", ""),
                request_contains_string=retriever_query.get("contains_string", ""),
                max_num_chunks=self.max_num_chunks,
            )
            query_result_list.append((retriever_query, chunks, scores))

        if self.max_intermediate_tool_chars == 0:
            # No reranking to be done -- early exit
            tool_output_str, include_chunks = self.format_retrieval_for_output(
                query_result_list,
                self.max_tool_chars,
                used_initial_retrieval_chunks=used_initial_retrieval_chunks,
            )

            auxiliary_data = {
                "retriever_queries": retriever_queries,
                "final_round_queries": query_result_list,
                "retrieved_chunks": include_chunks,
            }

            return ToolImplOutput(
                tool_output=tool_output_str,
                tool_result_message="Codebase information retrieved",
                auxiliary_data=auxiliary_data,
            )

        # Final round retrieval
        final_round_queries = self.get_final_round_retriever_queries(
            tool_input,
            query_result_list,
            dialog_messages=dialog_messages,
            used_initial_retrieval_chunks=used_initial_retrieval_chunks,
        )

        if self.verbose:
            print(
                "[HighLevelCodebase3StepRetrievalAgent] reranking: ",
                final_round_queries,
            )

        final_round_results = []
        for retriever_query in final_round_queries:
            chunks, scores = retrieve(
                self.retriever,
                request_description=retriever_query["description"],
                request_path=retriever_query.get("path", ""),
                request_contains_string=retriever_query.get("contains_string", ""),
                max_num_chunks=self.max_num_chunks,
            )
            final_round_results.append((retriever_query, chunks, scores))

        # Format the results
        tool_output_str, include_chunks = self.format_retrieval_for_output(
            final_round_results, self.max_tool_chars
        )

        auxiliary_data = {
            "retriever_queries": retriever_queries,
            "final_round_queries": final_round_queries,
            "retrieved_chunks": include_chunks,
        }

        return ToolImplOutput(
            tool_output=tool_output_str,
            tool_result_message="Codebase information retrieved",
            auxiliary_data=auxiliary_data,
        )


class ProduceAnswerTool(LLMTool):
    """A tool that produces a final answer based on available information."""

    name = "produce_answer"
    description = dedent(
        """\
        Use tool to produce a final answer to the user's question.

        There are two types of question: general programming questions, and questions that relate to the specific codebase.
        If the question is general, please provide an answer in the form of a chain of thought with supporting facts.
            Do not use the provided code snippets or directory tree.

        If the question is specific to the codebase, please provide an answer in the form of a chain of thought that relates to the codebase.
            For each step in the chain of thought, please include:
            - The full file path of a supporting documentation or code snippet.
            - The content of the supporting documentation or code snippet.
            - How the supporting documentation or code snippet supports the step.
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "answer": {
                "type": "string",
                "description": "The final answer to provide to the user.",
            }
        },
        "required": ["answer"],
    }

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the tool implementation.

        Args:
            tool_input: The tool input containing the answer.
            dialog_messages: Optional dialog messages for context.

        Returns:
            ToolImplOutput containing the answer.
        """
        answer = tool_input["answer"]
        return ToolImplOutput(
            tool_output=answer,
            tool_result_message="Final answer produced",
            auxiliary_data={},
        )


class HighLevelCodebaseRetrievalRerankAgent(HighLevelCodebaseRetrievalAgent):
    """An agent that performs codebase retrieval."""

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        retriever: QueryOnlyDocumentIndex,
        llm_client: LLMClient,
        rerank_client: LLMClient,
        max_tool_chars: int = 50000,
        max_rerank_input_chars: int = 100000,
    ):
        """
        Args:
            tool_call_logger: The tool call logger to use.
            retriever: The tool will use this retriever to search the codebase.
            max_tool_chars: The tool will return at most this many characters.
        """
        super().__init__(tool_call_logger, retriever, llm_client, max_tool_chars)
        self.rerank_client = rerank_client
        self.max_rerank_input_chars = max_rerank_input_chars

        # Hardcoded things that may need to be configured later
        self.max_num_chunks = 500

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the tool."""

        # Get low level queries
        retriever_queries = self.get_retriever_queries(
            tool_input, dialog_messages=dialog_messages
        )

        # Retrieve for each query
        query_result_list = []
        for retriever_query in retriever_queries:
            chunks, scores = retrieve(
                self.retriever,
                request_description=retriever_query["description"],
                request_path=retriever_query.get("path", ""),
                request_contains_string=retriever_query.get("contains_string", ""),
                max_num_chunks=self.max_num_chunks,
            )
            query_result_list.append((retriever_query, chunks, scores))

        # Rerank the results
        reranked_query_result_list = []
        print("Reranking...")
        for retriever_query, chunks, scores in query_result_list:
            print(f"Reranking for query: {retriever_query['description']}")
            reranked_chunks, reranked_scores = self.rerank(
                tool_input=tool_input,
                dialog_messages=dialog_messages,
                retriever_query=retriever_query,
                chunks=chunks,
                scores=scores,
            )
            reranked_query_result_list.append(
                (retriever_query, reranked_chunks, reranked_scores)
            )

        # Format the results
        tool_output_str = self.format_retrieval(reranked_query_result_list)

        auxiliary_data = {}
        auxiliary_data["retriever_queries"] = retriever_queries

        return ToolImplOutput(
            tool_output=tool_output_str,
            tool_result_message="Codebase information retrieved",
            auxiliary_data=auxiliary_data,
        )

    def rerank(
        self,
        tool_input: dict[str, Any],
        retriever_query: dict[str, Any],
        chunks: list[Chunk],
        scores: list[float],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> tuple[list[Chunk], list[float]]:
        """Rerank the retrieved chunks."""

        MAX_DIALOG_CHARS = 30000

        # Call LLM to identify relevant files

        RERANK_SYSTEM_PROMPT = dedent(
            """\
            We are trying to gather relevant context from a codebase to help an agent achieve a task.
            Your task is to identify the most relevant files for a given query.
            Identify between 3 and 15 relevant files.
            For each file, output the full file path on a separate line.
            Only output the file paths, nothing else.
            """
        )
        message_str = ""
        message_str += RERANK_SYSTEM_PROMPT
        message_str += f"The particular query being reranked is: {retriever_query['description']}\n"

        DIALOG_PROMPT = dedent(
            """\
            The query is in the context of an agent attempting to execute a task.
            Below are some messages of the dialogue of this agent and the user:
            """
        )

        if dialog_messages is not None:
            message_str += DIALOG_PROMPT
            message_str += format_dialog_as_string(dialog_messages, MAX_DIALOG_CHARS)

        rerank_chars = 0
        message_str += "Here are some code snippets retrieved in response to the query that you should use to identify relevant files:\n"
        for chunk in chunks:
            if rerank_chars + len(chunk.text) > self.max_rerank_input_chars:
                break
            rerank_chars += len(chunk.text) + 1
            message_str += f"Path: {chunk.path}\n"
            message_str += f"{chunk.text}\n"

        message_str += f"Once again, the particular query being reranked is: {retriever_query['description']}\n"
        message_str += "Please write a list of full file paths that are most relevant to the query with one file per line, and no other text."

        messages = []
        messages.append(TextPrompt(text=message_str))

        print(len(message_str))

        response_messages, _ = self.rerank_client.generate(
            messages=[messages],
            max_tokens=2048,
            temperature=0.0,
        )
        text_responses = [
            message for message in response_messages if hasattr(message, "text")
        ]
        assert (
            len(text_responses) >= 1
        ), f"Expected at least one text response, got {response_messages}"
        response = text_responses[0]
        response_string = response.text
        relevant_files = response_string.split("\n")
        print("Relevant files:")
        print(relevant_files)

        # Filter chunks based on the relevant files + some heuristics
        reranked_chunks = []
        reranked_scores = []

        leftover_chunks = []
        leftover_scores = []

        for chunk_idx, chunk in enumerate(chunks):
            if chunk.path in relevant_files:
                reranked_chunks.append(chunk)
                reranked_scores.append(scores[chunk_idx])
            else:
                leftover_chunks.append(chunk)
                leftover_scores.append(scores[chunk_idx])

        # Add all the other chunks in order afterwards
        reranked_chunks.extend(leftover_chunks)
        reranked_scores.extend(leftover_scores)

        return reranked_chunks, reranked_scores


class ChatanolLLMTool(LLMTool):
    """An agent that performs codebase retrieval."""

    name = "request_codebase_information"

    description = dedent(
        """\
        Use this tool to request information from the codebase.
        It will return relevant snippets for the requested information.
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "information_request": {
                "type": "string",
                "description": "A description of the information you need.",
            },
        },
        "required": ["information_request"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        retriever: QueryOnlyDocumentIndex,
        max_tool_chars: int = 50000,
    ):
        """
        Args:
            tool_call_logger: The tool call logger to use.
            retriever: The tool will use this retriever to search the codebase.
            max_tool_chars: The tool will return at most this many characters.
        """
        super().__init__(tool_call_logger)
        self.retriever = retriever
        self.max_tool_chars = max_tool_chars

        # Hardcoded things that may need to be configured later
        self.max_num_chunks = 500

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the tool."""

        chunks, scores = self.retrieve(
            request_description=tool_input["information_request"],
        )

        # Format the results
        tool_output_str, included_chunks = self.format_retrieval(
            [(tool_input, chunks, scores)]
        )

        auxiliary_data = {
            "retrieved_chunks": included_chunks,
        }

        return ToolImplOutput(
            tool_output=tool_output_str,
            tool_result_message="Codebase information retrieved",
            auxiliary_data=auxiliary_data,
        )

    def retrieve(  # type: ignore
        self, request_description: str
    ) -> tuple[list[Chunk], list[float]]:
        """Perform retrieval for the current state."""
        query = ChatRetrieverPromptInput(
            prefix="",
            suffix="",
            path="",
            message=request_description,
            selected_code="",
            chat_history=[],
        )
        chunks, scores = self.retriever.query(query, top_k=None)
        output_chunks = chunks[: self.max_num_chunks]
        output_scores = scores[: self.max_num_chunks]
        return output_chunks, output_scores

    def format_retrieval(
        self, query_result_list: list[tuple[dict[str, Any], list[Chunk], list[float]]]
    ) -> tuple[str, list[Chunk]]:
        """Format the retrieval results."""

        tool_output_text = "The following code sections were retrieved:\n"
        unneeded_token_counter = RoughTokenCounter()
        formatted_files: dict[str, AbstractFormattedFile] = OrderedDict()
        char_count = 0

        # We want to provide each generated query with equal budget, so we interleave
        # chunks from each query and stop when we reach the budget.
        chunk_sequences = [chunks for _, chunks, _ in query_result_list]
        interleaved_chunks = interleave_sequences(chunk_sequences)
        included_chunks = []

        for chunk in interleaved_chunks:
            prompt_chunk = PromptChunk(
                text=chunk.text,
                path=chunk.path or "",
                unique_id=chunk.id,
                origin="dense_retriever",
                char_start=chunk.char_offset,
                char_end=chunk.char_offset + chunk.length,
                blob_name=chunk.parent_doc.id,
            )
            file_id = chunk.parent_doc.id
            if file_id not in formatted_files:
                old_formatted_file = FormattedFileV2(unneeded_token_counter)
            else:
                old_formatted_file = formatted_files[file_id]

            new_formatted_file = old_formatted_file.add_chunk(prompt_chunk)
            new_len = len(new_formatted_file.get_file_str_and_tok_ct()[0])
            old_len = len(old_formatted_file.get_file_str_and_tok_ct()[0])
            added_len = new_len - old_len
            if char_count + added_len > self.max_tool_chars:
                break

            char_count += added_len
            formatted_files[file_id] = new_formatted_file
            included_chunks.append(chunk)

        for formatted_file in formatted_files.values():
            file_str, _ = formatted_file.get_file_str_and_tok_ct()
            file_path = formatted_file.sorted_chunks[0].path
            tool_output_text += f"Path: {file_path}\n{file_str}\n"

        return tool_output_text, included_chunks

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Retrieving codebase information: {tool_input['information_request']}"


class DirectorySubTreeTool(LLMTool):
    """A tool that returns the directory structure of a given directory."""

    name = "ask_for_directory_subtree"

    description = dedent(
        """\
        When asked about a directory, this will return the names of all subdirectories and files in that directory, recursively, until a character limit is reached.
        Use when you need to know what's in a directory or repository.
        Provide the directory to ask about.
        Lean towards being less specific rather than more specific.
        Instead of asking about a specific file or directory, ask about the parent directory or even higher level directory.
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "directories": {
                "type": "array",
                "items": {
                    "type": "string",
                },
                "description": "Directories to ask about.",
            },
        },
        "required": ["directories"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        files,
        max_directory_chars,
        max_file_chars,
    ):
        """Initialize the tool.

        Args:
            tool_call_logger: The tool call logger to use.
            files: List of Document objects representing files in the codebase.
            max_directory_chars: Maximum number of characters for directory names.
            max_file_chars: Maximum number of characters for file names.
        """
        super().__init__(tool_call_logger)
        self.max_directory_chars = max_directory_chars
        self.max_file_chars = max_file_chars
        self.set_files(files)

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the tool implementation.

        Args:
            tool_input: Dictionary containing the tool input parameters.
            dialog_messages: Optional dialog messages for context.

        Returns:
            ToolImplOutput containing the formatted directory structure.
        """
        tool_output_text = ""
        for directory in tool_input["directories"]:
            if directory == "":
                tool_output_text += "The directory subtree is:\n"
            else:
                tool_output_text += (
                    f"The directory subtree starting from {directory} is:\n"
                )

            format_directory_text, format_metadata = self.format_directory_subtree(
                starting_dir_str=directory,
                max_directory_chars=self.max_directory_chars,
                max_file_chars=self.max_file_chars,
            )
            tool_output_text += format_directory_text
            truncated = format_metadata["truncated"]
            if truncated:
                tool_output_text += "\n"
                tool_output_text += dedent(
                    """\
                    Keep in mind that this may not be a full directory subtree, only up to a character limit.
                    If a character limit was reached such that not all files or subdirectories can be displayed, that is indicated with '...'.
                    If there are no ... under a directory, that means that all subdirectories and files were displayed.
                    """
                )
            else:
                tool_output_text += "\n"
                tool_output_text += "All subdirectories and files were displayed.\n"

        return ToolImplOutput(
            tool_output=tool_output_text,
            tool_result_message="Directory structure retrieved",
            auxiliary_data={},
        )

    def set_files(self, files):
        self.directory_structure = self.create_directory_structure(files)

    @staticmethod
    def create_directory_structure(files: list[Document]):
        directory_structure = {"": {"files": [], "dirs": [], "dir_name_set": set()}}
        file_paths = [Path(file.path) for file in files]

        for path in file_paths:
            is_file = True
            while path.name != "":
                parent = path.parent
                if str(parent) not in directory_structure:
                    directory_structure[str(parent)] = {
                        "files": [],
                        "dirs": [],
                        "dir_name_set": set(),
                    }

                # Check if already added
                if path.name not in directory_structure[str(parent)]["dir_name_set"]:
                    if is_file:
                        directory_structure[str(parent)]["files"].append(path)
                    else:
                        directory_structure[str(parent)]["dirs"].append(path)
                        directory_structure[str(parent)]["dir_name_set"].add(path.name)
                path = parent
                is_file = False

        # Sort alphabetically
        for key in directory_structure:
            directory_structure[key]["files"].sort()
            directory_structure[key]["dirs"].sort()

        return directory_structure

    def format_directory_subtree(
        self,
        starting_dir_str: str,
        max_directory_chars: int,
        max_file_chars: int,
        max_depth: int = 50,
    ):
        # Use the top-level format_directory_subtree function
        return format_directory_subtree(
            self.directory_structure,
            starting_dir_str,
            max_directory_chars,
            max_file_chars,
            max_depth,
        )


class CharCounter(TokenCounter):
    """A token counter that counts characters."""

    @functools.lru_cache(maxsize=128)
    def count_tokens(self, prompt_chars: str) -> int:
        return len(prompt_chars)


def format_chat_input(chat_input: ChatPromptInput, chat_history_len: int = 0) -> str:
    """Format a chat input for use in a prompt."""
    cur_message = chat_input.message

    # Clip chat history to keep it within a budget
    chat_history, _ = format_chat_history(chat_input, CharCounter(), chat_history_len)

    # Handle selected code in chat history.
    # Following the chat prompt formatter pattern, we insert selected code
    # at the point where it was first selected in the conversation.
    # This is typically specified by `context_code_exchange_request_id`.
    # There are two special cases:
    # 1. Code selected in current turn: add to current message
    # 2. Selected code's original turn was trimmed from chat history:
    #    add as new turn at start of history
    if len(chat_input.selected_code) > 0:
        selected_code_section = f"""\
File {chat_input.path} is open in the editor and the following code is selected:

```
{chat_input.selected_code}
```
"""
        # See https://github.com/augmentcode/augment/blob/main/base/prompt_format_chat/lib/selected_code_prompt_formatter_v2.py#L130
        selected_code_response_message = "Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction."
        context_code_exchange_request_id = chat_input.context_code_exchange_request_id
        cur_message, chat_history = inject_selected_code_into_chat_history(
            cur_message,
            chat_history,
            selected_code_section,
            context_code_exchange_request_id,
            selected_code_response_message,
        )

    formatted_message = ""
    if len(chat_history) > 0:
        formatted_message += "Chat history:\n"
        for exchange in chat_history:
            formatted_message += f"User: {exchange.request_message}\n"
            formatted_message += f"Assistant: {exchange.response_text}\n"

    formatted_message += f"User: {cur_message}\n"
    return formatted_message


def parse_tool_call(output: str):
    code_section_requests = []
    lines = output.splitlines()
    for index in range(0, len(lines), 3):
        code_section_requests.append(
            {
                "description": lines[index],
                "path": lines[index + 1] if index + 1 < len(lines) else "",
                "contains_string": lines[index + 2] if index + 2 < len(lines) else "",
            }
        )

    tool_call = ToolCall(
        tool_name="ask_for_codebase_snippets",
        tool_input={"code_section_requests": code_section_requests},
        tool_call_id="",
    )
    return tool_call


class QwenQueryRewriterTool(LLMTool):
    """An agent that performs codebase retrieval."""

    name = "request_codebase_information"

    description = dedent(
        """\
        Use this tool to request information from the codebase.
        It will return relevant snippets for the requested information.
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "information_request": {
                "type": "string",
                "description": "A description of the information you need.",
            },
        },
        "required": ["information_request"],
    }

    SYSTEM_PROMPT = """\
You are a codebase search expert. Generate multiple targeted queries to find relevant code based on user conversations, refining searches iteratively."""

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        retriever: QueryOnlyDocumentIndex,
        max_tool_chars: int,
        max_init_retrieval_chars: int,
        max_chat_history_chars: int = 4000,
    ):
        """
        Args:
            tool_call_logger: The tool call logger to use.
            retriever: The tool will use this retriever to search the codebase.
            max_tool_chars: The tool will return at most this many characters.
        """
        super().__init__(tool_call_logger)
        self.retriever = retriever
        self.max_tool_chars = max_tool_chars
        self.max_init_retrieval_chars = max_init_retrieval_chars
        self.max_chat_history_chars = max_chat_history_chars

        # Hardcoded things that may need to be configured later
        self.max_num_chunks = 1024
        self.max_directory_tool_chars = 10_000

        self.tokenizer = Qwen25CoderTokenizer()
        self.tokenized_prompt_formatter = StructToTokensQwenPromptFormatter(
            self.tokenizer
        )

        self.formatted_directory_output = None

        # https://determined.gcp-us1.r.augmentcode.com/det/experiments/11587/trials/11587/checkpoints
        self.model = FastForwardQwen25Coder_32B(
            checkpoint_path=Path(
                "/mnt/efs/augment/checkpoints/query_rewriter/qrv2_qwen32b_binksv4_bsz64_ff"
            ),
            checkpoint_sha256="a56a93901971a22e5ab51c86c9dacf1388bd0da506dea1745c61448a8b1c1931",
            sequence_length=16384,
        )
        self.model.load()
        self.generation_options = GenerationOptions(
            temperature=0,
            top_k=0,
            top_p=0,
            max_generated_tokens=1024,
            stop_tokens=[151645],
        )

    def remove_docs(self, docs: list[Document]):
        self.formatted_directory_output = None

    def add_docs(self, docs: list[Document]):
        directory_subtree_tool = LegacyDirectorySubTreeTool(
            files=docs,
            max_directory_chars=self.max_directory_tool_chars,
            max_file_chars=self.max_directory_tool_chars,
        )
        directory_tool_call = ToolCall(
            tool_name=directory_subtree_tool.name,
            tool_input={"directories": [""]},
            tool_call_id="",
        )
        directory_tool_output, _ = directory_subtree_tool.activate_tool(
            directory_tool_call
        )
        formatted_directory_output, _ = directory_subtree_tool.format_tool_result(
            directory_tool_output
        )
        self.formatted_directory_output = formatted_directory_output.tool_output

    def format_query_rewriter_prompt(
        self,
        information_request: str,
        chunks: list[Chunk],
    ) -> tuple[StructuredChatPromptOutput, list[Chunk]]:
        """Format the prompt for the Query Rewriter model."""
        formatted_initial_retrieval, used_chunks = self.format_retrieval(
            chunks,
            self.max_init_retrieval_chars,
        )

        formatted_chat_input = format_chat_input(
            ChatPromptInput(
                message=information_request,
                path="",
                prefix="",
                selected_code="",
                suffix="",
                chat_history=[],
                prefix_begin=0,
                suffix_end=0,
                retrieved_chunks=[],
            ),
            chat_history_len=self.max_chat_history_chars,
        )

        assert self.formatted_directory_output is not None
        user_message_string = [
            f"{self.formatted_directory_output}\n",
            f"{formatted_initial_retrieval}\n",
        ]
        user_message_string.append(
            f"The user query we are trying to help with is:\n{formatted_chat_input}"
        )
        user_message_string = "".join(user_message_string)
        return (
            StructuredChatPromptOutput(
                system_prompt=self.SYSTEM_PROMPT,
                message=user_message_string,
                chat_history=[],
                retrieved_chunks_in_prompt=[],
                retrieval_as_tool=True,
            ),
            used_chunks,
        )

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the tool."""

        # Initial retrieval
        initial_retrieval_chunks, scores = self.get_initial_retrieval(
            request_description=tool_input["information_request"],
        )
        query_rewriter_prompt, used_initial_retrieval_chunks = (
            self.format_query_rewriter_prompt(
                tool_input["information_request"],
                initial_retrieval_chunks,
            )
        )
        used_initial_retrieval_chunks_set = set(used_initial_retrieval_chunks)
        prompt_tokens = self.tokenized_prompt_formatter.format_prompt(
            query_rewriter_prompt
        ).tokens

        output = self.model.raw_generate(prompt_tokens, self.generation_options)
        tool_call = parse_tool_call(output)

        final_round_results = []
        for retriever_query in tool_call.tool_input["code_section_requests"]:
            chunks, scores = retrieve(
                self.retriever,
                request_description=retriever_query["description"],
                request_path=retriever_query.get("path", ""),
                request_contains_string=retriever_query.get("contains_string", ""),
                max_num_chunks=self.max_num_chunks,
            )
            deduplicated_chunks = []
            for chunk in chunks:
                if chunk not in used_initial_retrieval_chunks_set:
                    deduplicated_chunks.append(chunk)
            final_round_results.append(deduplicated_chunks)

        final_round_results = list(interleave_sequences(final_round_results))

        # Format the results
        tool_output_str, include_chunks = self.format_retrieval(
            used_initial_retrieval_chunks + final_round_results,
            self.max_tool_chars,
        )

        auxiliary_data = {
            "retriever_queries": dataclasses.asdict(tool_call),
            "retrieved_chunks": include_chunks,
        }

        return ToolImplOutput(
            tool_output=tool_output_str,
            tool_result_message="Codebase information retrieved",
            auxiliary_data=auxiliary_data,
        )

    def get_initial_retrieval(  # type: ignore
        self, request_description: str
    ) -> tuple[list[Chunk], list[float]]:
        """Perform retrieval for the current state."""
        query = ChatRetrieverPromptInput(
            prefix="",
            suffix="",
            path="",
            message=request_description,
            selected_code="",
            chat_history=[],
        )
        chunks, scores = self.retriever.query(query, top_k=None)
        output_chunks = chunks[: self.max_num_chunks]
        output_scores = scores[: self.max_num_chunks]
        return output_chunks, output_scores

    def format_retrieval(
        self, chunks: list[Chunk], char_budget: int
    ) -> tuple[str, list[Chunk]]:
        retrieval_string = ""
        used_chunks: list[Chunk] = []
        unneeded_token_counter = RoughTokenCounter()
        formatted_files: dict[str, AbstractFormattedFile] = OrderedDict()
        char_count = 0
        for chunk in chunks:
            # construct PromptChunk
            prompt_chunk = PromptChunk(
                text=chunk.text,
                path=chunk.path or "",
                unique_id=chunk.id,
                origin="dense_retriever",
                char_start=chunk.char_offset,
                char_end=chunk.char_offset + chunk.length,
                blob_name=chunk.parent_doc.id,
            )
            file_id = chunk.parent_doc.id
            if file_id not in formatted_files:
                old_formatted_file = FormattedFileV2(unneeded_token_counter)
            else:
                old_formatted_file = formatted_files[file_id]

            new_formatted_file = old_formatted_file.add_chunk(prompt_chunk)
            new_len = len(new_formatted_file.get_file_str_and_tok_ct()[0])
            old_len = len(old_formatted_file.get_file_str_and_tok_ct()[0])
            added_len = new_len - old_len
            if char_count + added_len > char_budget:
                break

            char_count += added_len
            formatted_files[file_id] = new_formatted_file
            used_chunks.append(chunk)

        for formatted_file in formatted_files.values():
            file_str, _ = formatted_file.get_file_str_and_tok_ct()
            file_path = formatted_file.sorted_chunks[0].path
            retrieval_string += f"""\
Path: {file_path}

```
{file_str}
```

"""

        return retrieval_string, used_chunks

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        print("\n")
        return f"Retrieving codebase information: {tool_input['information_request']}"


class UserQueryAnnotatorTool(LLMTool):
    """A tool that analyzes and annotates user queries with metadata."""

    name = "annotate_user_query"

    description = dedent(
        """\
        Use this tool to analyze a user query and annotate it with metadata.
        It will identify key aspects of the query such as intent, entities, and required tools.
        """
    )

    requires_codebase_description = dedent(
        """\
        Whether the query requires information from the codebase to be answered properly.

        Set to true if:
        - The query is about understanding code in the codebase
        - The query is about modifying code in the codebase
        - The query is requesting information about the structure or architecture of the codebase
        - The query mentions specific files, classes, functions, or other elements in the codebase

        Set to false if:
        - The query is about a programming concept, language feature, or external library that doesn't require looking at the codebase
        - The query is asking for general advice that doesn't depend on the specific codebase
        - The query is requesting actions that do not directly require codebase information, such as looking up pull requests, tickets, etc.

        Be conservative and assume the query requires codebase information (true) unless you are certain it does not.
        """
    )

    requires_action_description = dedent(
        """\
        Whether the query requires non-read actions from an agent.

        Set to true if:
        - The query asks to modify, create, or delete code
        - The query asks to run commands or execute code
        - The query asks to install packages or dependencies
        - The query asks to commit, push, or perform other version control operations
        - The query asks to deploy code or perform other system operations

        Set to false if:
        - The query is just asking for information or explanation
        - The query is asking for code to be read or analyzed, but not modified
        - The query is asking for advice or suggestions, but not asking for actions to be taken

        This distinguishes between queries that only need information retrieval (false) versus queries that need the agent to take action (true).
        """
    )

    about_specific_file_description = dedent(
        """\
        If the query clearly primarily relates to one or a few specific files, please specify the FULL ABSOLUTE PATHs to these files. Otherwise, just write empty list.
        Some examples where files should be specified:
        - The user asks for the contents of a file. Make sure you find the full path to the file.
        - The user asks to summarize a file
        - The user asks to write tests for a class (both the file and another file with existing test should be specified)
        - The user asks a question about a symbol and you can see from the context that the symbol is defined in a specific file
        - The user asks to rewrite a function that calls an API in a specific file (both the file and another file with the API should be specified)
        - The user asks to edit a function in a specific file

        Some examples where the file should not be specified:
        - The user asks for the contents of a directory
        - You know the user is asking about a file, but you don't see the file name in the context. Don't guess!
        - The user has the file open, but the question is not related to that file.
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "requires_codebase": {
                "type": "boolean",
                "description": requires_codebase_description,
            },
            "requires_action": {
                "type": "boolean",
                "description": requires_action_description,
            },
            "about_specific_file": {
                "type": "array",
                "items": {
                    "type": "string",
                },
                "description": about_specific_file_description,
            },
        },
        "required": [
            "requires_codebase",
            "requires_action",
            "about_specific_file",
        ],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        llm_client: Optional[LLMClient] = None,
    ):
        """
        Args:
            tool_call_logger: The tool call logger to use.
            llm_client: Optional LLM client to use for analysis.
        """
        super().__init__(tool_call_logger)
        self.llm_client = llm_client

    # This tool is only used to create a tool input within the RouterTool
    # It doesn't have its own run_impl method


class RouterTool(LLMTool):
    """A tool that updates a user's query with additional instructions and information from the codebase as appropriate."""

    name = "router"

    description = dedent(
        """\
        Use this tool to route requests to the appropriate tool or service.
        It will analyze the request and determine the best destination.
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "message": {
                "type": "string",
                "description": "The user message to be routed.",
            },
        },
        "required": ["message"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        retriever: QueryOnlyDocumentIndex,
        llm_client: LLMClient,
        max_input_retrieval_chars: int = 20000,
        max_output_retrieval_chars: int = 20000,
        max_input_dialog_chars: int = 30000,
    ):
        """
        Args:
            tool_call_logger: The tool call logger to use.
            retriever: Retriever to use for context lookup.
            llm_client: LLM client to use for routing decisions.
            max_input_retrieval_chars: Maximum number of characters for input retrieval.
            max_output_retrieval_chars: Maximum number of characters for output retrieval.
            max_input_dialog_chars: Maximum number of characters for dialog history.
        """
        super().__init__(tool_call_logger)
        self.retriever = retriever
        self.llm_client = llm_client
        self.max_input_retrieval_chars = max_input_retrieval_chars
        self.max_output_retrieval_chars = max_output_retrieval_chars
        self.max_input_dialog_chars = max_input_dialog_chars
        self.max_num_chunks = 1024

    def _perform_llm_call(
        self,
        user_message: str,
        context_chunks: list[Chunk],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> dict[str, Any]:
        """Perform LLM call with forced tool use of UserQueryAnnotatorTool.

        Args:
            user_message: The user message to analyze.
            context_chunks: Retrieved context chunks to include in the prompt.
            dialog_messages: Optional dialog messages for additional context.

        Returns:
            The query string extracted from the tool call.
        """
        # Create the tool parameter for UserQueryAnnotatorTool
        annotator_tool_param = ToolParam(
            name="annotate_user_query",
            description=UserQueryAnnotatorTool.description,
            input_schema=UserQueryAnnotatorTool.input_schema,
        )

        # Create system prompt without context from retrieval
        system_prompt = dedent("""\
            You are an AI assistant that analyzes user queries to determine their intent and required tools.
            Analyze the user query carefully and provide detailed annotations.
            """)

        # Create a single message string for the LLM
        message_str = ""

        # Add context from retrieval if available
        if context_chunks:
            message_str += dedent("""\
            Here is some context from the codebase that might be relevant to the user query:
            """)

            # Use the factored out format_retrieval function
            formatted_retrieval, _ = format_retrieval(
                context_chunks, self.max_output_retrieval_chars
            )
            if formatted_retrieval:
                message_str += formatted_retrieval + "\n\n"

        # Add dialog history if available
        if dialog_messages is not None:
            # Format dialog history for context using max_input_dialog_chars
            DIALOG_PROMPT = dedent(
                """\
                The query is in the context of an agent attempting to execute a task.
                Below are some messages of the dialogue of this agent and the user:
                """
            )
            dialog_str = format_dialog_as_string(
                dialog_messages, self.max_input_dialog_chars
            )
            if dialog_str:
                message_str += DIALOG_PROMPT + "\n" + dialog_str + "\n\n"

        # Add the current user message
        message_str += f"User query: {user_message}"

        # Create a single TextPrompt from the message string
        messages = [TextPrompt(text=message_str)]

        # Make the LLM call with forced tool use
        response_messages, _ = self.llm_client.generate(
            messages=[messages],  # Wrap in a list as expected by the LLM client
            max_tokens=2048,
            system_prompt=system_prompt,
            temperature=0.0,
            tools=[annotator_tool_param],
            tool_choice={"type": "tool", "name": annotator_tool_param.name},
        )

        # Process the response
        tool_calls = [
            message for message in response_messages if isinstance(message, ToolCall)
        ]
        assert (
            len(tool_calls) == 1
        ), f"Expected exactly one tool call, got {len(tool_calls)} tool calls in {response_messages}"
        response = tool_calls[0]

        # Extract the annotations from the tool call
        tool_input = response.tool_input

        # Get the annotation values
        requires_codebase = tool_input["requires_codebase"]
        requires_action = tool_input["requires_action"]
        about_specific_file = tool_input["about_specific_file"]

        # Create annotations dictionary
        annotations = {
            "requires_codebase": requires_codebase,
            "requires_action": requires_action,
            "about_specific_file": about_specific_file,
        }

        return annotations

    def format_tool_output(
        self,
        user_message: str,
        annotations: dict[str, Any],
        context_chunks: list[Chunk],
    ) -> str:
        """Format the tool output according to the specified format.

        Args:
            user_message: The user message.
            annotations: The annotations from the UserQueryAnnotatorTool.
            context_chunks: Retrieved context chunks to include in the output.

        Returns:
            The formatted tool output with the structure:
            1. The user message
            2. If the request doesn't require actions, a statement in <supervisor> tags
            3. The retrieval information in <supporting_info> tags
        """
        # Start with the user message
        output_parts = [user_message]

        # Add supervisor message if request doesn't require actions
        requires_action = annotations.get("requires_action", False)
        if not requires_action:
            supervisor_msg = dedent(
                """\
                <supervisor>
                This request should use only read-only actions - looking at the codebase or other sources of information.
                Do not edit or create files, or perform any other write actions, such as editing or creating tickets or PRs.
                Don't tell the user about this text.
                </supervisor>
                """
            )
            output_parts.append(supervisor_msg)

        # Add retrieval information in supporting_info tags if we have context chunks
        if context_chunks:
            # Use the factored out format_retrieval function
            formatted_retrieval, _ = format_retrieval(
                context_chunks, self.max_output_retrieval_chars
            )

            supporting_info = f"""\
<supporting_info>
Here is some context from the codebase that might be relevant to the user query:
{formatted_retrieval}
</supporting_info>
"""
            output_parts.append(supporting_info)

        # Join all parts with newlines
        return "\n\n".join(output_parts)

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the tool implementation.

        Args:
            tool_input: Dictionary containing the tool input parameters.
            dialog_messages: Optional dialog messages for context.

        Returns:
            ToolImplOutput containing the routing decision based on UserQueryAnnotatorTool output.
        """
        user_message = tool_input["message"]

        # Step 1: Perform retrieval given the user message and history
        context_chunks, scores = retrieve(
            self.retriever,
            request_description=user_message,
            request_path="",
            request_contains_string="",
            max_num_chunks=self.max_num_chunks,
        )

        # Step 2: Perform LLM call with forced tool use of UserQueryAnnotatorTool
        annotations = self._perform_llm_call(
            user_message, context_chunks, dialog_messages
        )

        # If request is about specific files, retrieve from those files
        about_specific_file = annotations.get("about_specific_file", [])
        if about_specific_file:
            user_guided_chunk_lists = []
            for file_path in about_specific_file:
                user_guided_chunks, scores = retrieve(
                    self.retriever,
                    request_description=user_message,
                    request_path=file_path,
                    request_contains_string="",
                    max_num_chunks=self.max_num_chunks,
                )
                user_guided_chunk_lists.append(user_guided_chunks)
            context_chunks = list(interleave_sequences(user_guided_chunk_lists))

        formatted_retrieval, _ = format_retrieval(
            context_chunks, self.max_output_retrieval_chars
        )

        # Format the tool output
        result_text = self.format_tool_output(user_message, annotations, context_chunks)

        # Create auxiliary data with both annotations and context chunks
        auxiliary_data = {
            "annotations": annotations,
        }

        # Return the formatted result
        return ToolImplOutput(
            tool_output=result_text,
            tool_result_message="Query analyzed and routed",
            auxiliary_data=auxiliary_data,
        )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Routing message: {tool_input['message']}"
