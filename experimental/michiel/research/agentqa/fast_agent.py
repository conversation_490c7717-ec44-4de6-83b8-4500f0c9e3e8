from __future__ import annotations

import traceback
from dataclasses import dataclass, field
from textwrap import dedent
from typing import Any, Optional, cast

from dataclasses_json import DataClassJsonMixin

from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.michiel.research.agentqa.agent import (
    AgentFunction,
    AgentLog,
    ModelCall,
    ToolCallEntry,
    format_chat_input,
)
from experimental.michiel.research.agentqa.tools import (
    Tool,
)
from research.llm_apis.llm_client import (
    AssistantContentBlock,
    GeneralContentBlock,
    LLMClient,
    TextPrompt,
    ToolCall,
)


@dataclass
class FastQuestionAnsweringAgentResult(DataClassJsonMixin):
    """Result of the top level agent function."""

    answer: Optional[str] = None
    """The answer."""


class FastQuestionAnsweringAgent(AgentFunction):
    """The top level agent function."""

    def __init__(
        self,
        max_turns: int,
        router_tool: Tool,
        router_client: LLMClient,
        produce_answer_agent: <PERSON><PERSON>unction,
        information_agent: AgentFunction,
        initial_information_agent: AgentFunction,
        use_routing: bool = True,
        chat_history_len: int = 0,
    ):
        self.max_turns = max_turns
        self.router_tool = router_tool
        self.router_client = router_client
        self.produce_answer_agent = produce_answer_agent
        self.information_agent = information_agent
        self.initial_information_agent = initial_information_agent
        self.use_routing = use_routing
        self.chat_history_len = chat_history_len

    def __call__(
        self, empty_log: AgentLog, chat_input: ChatPromptInput
    ) -> FastQuestionAnsweringAgentResult:
        """Run the top level agent function."""

        # empty_log.register_child(current_agent_log)
        current_agent_log = AgentLog(name="FastQuestionAnsweringAgent")
        empty_log.register_child(current_agent_log)
        current_agent_log.agent_metadata = {
            "max_turns": self.max_turns,
        }
        try:
            result = self.run_agent(
                chat_input=chat_input,
                current_agent_log=current_agent_log,
            )
        except Exception as e:
            current_agent_log.error_state = {
                "error": str(e),
                "traceback": traceback.format_exc(),
            }
            result = FastQuestionAnsweringAgentResult(answer=None)
        return result

    def run_agent(
        self,
        chat_input: ChatPromptInput,
        current_agent_log: AgentLog,
    ) -> FastQuestionAnsweringAgentResult:
        current_agent_log.relevant_inputs["chat_input"] = chat_input
        information_call_list: list[ToolCallEntry] = []

        if self.use_routing:
            # First, route to see if it's a general question or a codebase question with a tool with a cheap model
            # If it's a general question, directly answer with best model
            messages = self.format_router_prompt(chat_input)
            call_result, call_metadata = self.router_client.generate(
                messages=messages,
                max_tokens=2048,
                tools=[self.router_tool.get_tool_param()],
                tool_choice={"type": "tool", "name": self.router_tool.name},
            )
            model_call = ModelCall(
                model_input=messages,
                model_response=call_result,
            )
            current_agent_log.register_model_call(model_call)
            assert (
                len(call_result) == 1
            ), f"Expected one message but got {len(call_result)}"
            assert str(type(call_result[0])) == str(
                ToolCall
            ), f"Expected tool call but got: {type(call_result[0])}"
            tool_call = cast(ToolCall, call_result[0])

            is_general_question = tool_call.tool_input["is_general_question"]

            # Answer general answer directly
            if is_general_question:
                print("Answering general question directly")
                answer_result = self.produce_answer_agent(
                    agent_log=current_agent_log,
                    chat_input=chat_input,
                    information_call_list=information_call_list,
                )
                print(f"Answer: {answer_result.answer_text}")
                return FastQuestionAnsweringAgentResult(
                    answer=answer_result.answer_text
                )

        # Initial directory search and codebase retrieval
        information_state = self.initial_information_agent(
            agent_log=current_agent_log,
            chat_input=chat_input,
        )
        first_information_calls = information_state.information_call_result
        information_call_list.extend(first_information_calls)

        # Outer loop of answering (currently single turn)
        answer_result = None
        for turn in range(1):
            # Inner loop of exploring codebase
            information_state = self.information_agent(
                agent_log=current_agent_log,
                chat_input=chat_input,
                information_call_list=information_call_list,
            )
            information_call_result = information_state.information_call_result
            information_call_list.extend(information_call_result)

            # Answer question
            print("Attempting to answer question")
            answer_result = self.produce_answer_agent(
                agent_log=current_agent_log,
                chat_input=chat_input,
                information_call_list=information_call_list,
            )
            print(f"Candidate answer: {answer_result.answer_text}")
            break

        assert answer_result is not None, "No answer found"
        result = FastQuestionAnsweringAgentResult(answer=answer_result.answer_text)
        return result

    def format_router_prompt(
        self, chat_input: ChatPromptInput
    ) -> list[list[GeneralContentBlock]]:
        """Format the prompt for the router agent function."""
        formatted_chat_input = format_chat_input(
            chat_input,
            chat_history_len=self.chat_history_len,
        )

        router_prompt = dedent(
            """\
            You are a tool-calling agent that classifies the nature of a query.
            Please decide whether the query is a general programming question or a question specific to a particular codebase.
            """
        )

        user_message_string = ""
        user_message_string += router_prompt
        user_message_string += f"The user query is:\n{formatted_chat_input}"

        messages = []
        messages.append([TextPrompt(text=user_message_string)])
        return messages


@dataclass
class InitialInformationAgentResult(DataClassJsonMixin):
    """Result of the information agent function."""

    information_exploration_result: Optional[str] = None
    """The information exploration result."""

    information_call_result: list[ToolCallEntry] = field(default_factory=list)
    """The new information."""


class InitialInformationAgent(AgentFunction):
    """Agent that carries out initial information turn for latency-constrained agent."""

    def __init__(
        self,
        codebase_retrieval_tool: Tool,
        directory_subtree_tool: Tool,
    ):
        self.codebase_retrieval_tool = codebase_retrieval_tool
        self.directory_subtree_tool = directory_subtree_tool

    def __call__(
        self,
        agent_log: AgentLog,
        chat_input: ChatPromptInput,
    ) -> InitialInformationAgentResult:
        """Produce new information."""

        current_agent_log = AgentLog(name="InitialInformationAgent")
        agent_log.register_child(current_agent_log)

        # Perform top level directory search
        directory_tool_call = ToolCall(
            tool_name=self.directory_subtree_tool.name,
            tool_input={"directories": [""]},
            tool_call_id="",
        )
        directory_tool_output, _ = self.directory_subtree_tool.activate_tool(
            directory_tool_call
        )
        formatted_directory_tool_output, _ = (
            self.directory_subtree_tool.format_tool_result(
                tool_result=directory_tool_output
            )
        )
        directory_tool_entry = ToolCallEntry(
            tool_call=directory_tool_call,
            tool_result=directory_tool_output,
            formatted_tool_output=formatted_directory_tool_output,
        )
        current_agent_log.register_tool_call(directory_tool_entry)

        # Perform retrieval with chat query
        retrieval_tool_call = ToolCall(
            tool_name=self.codebase_retrieval_tool.name,
            tool_input={"code_section_requests": [{"description": chat_input.message}]},
            tool_call_id="",
        )
        retrieval_tool_output, _ = self.codebase_retrieval_tool.activate_tool(
            retrieval_tool_call
        )
        formatted_retrieval_tool_output, _ = (
            self.codebase_retrieval_tool.format_tool_result(
                tool_result=retrieval_tool_output
            )
        )
        retrieval_tool_entry = ToolCallEntry(
            tool_call=retrieval_tool_call,
            tool_result=retrieval_tool_output,
            formatted_tool_output=formatted_retrieval_tool_output,
        )
        current_agent_log.register_tool_call(retrieval_tool_entry)

        result = InitialInformationAgentResult(
            information_call_result=[
                directory_tool_entry,
                retrieval_tool_entry,
            ],
        )
        current_agent_log.register_result(result)

        return result


@dataclass
class FastInformationAgentResult(DataClassJsonMixin):
    """Result of the information agent function."""

    information_exploration_result: Optional[str] = None
    """The information exploration result."""

    information_call_result: list[ToolCallEntry] = field(default_factory=list)
    """The new information."""


class FastInformationAgent(AgentFunction):
    """The information agent function for latency-constrained agent."""

    def __init__(
        self,
        codebase_retrieval_tool: Tool,
        codebase_retrieval_client: LLMClient,
        directory_subtree_tool: Tool,
        directory_subtree_client: LLMClient,
        chat_history_len: int = 0,
    ):
        self.codebase_retrieval_tool = codebase_retrieval_tool
        self.directory_subtree_tool = directory_subtree_tool
        self.codebase_retrieval_client = codebase_retrieval_client
        self.directory_subtree_client = directory_subtree_client
        self.chat_history_len = chat_history_len

    def __call__(
        self,
        agent_log: AgentLog,
        chat_input: ChatPromptInput,
        information_call_list: list[ToolCallEntry],
    ) -> FastInformationAgentResult:
        """Produce new information."""

        current_agent_log = AgentLog(name="InformationAgent")
        agent_log.register_child(current_agent_log)
        current_agent_log.relevant_inputs = {
            "chat_input": chat_input,
            "information_call_list": information_call_list,
        }

        # Call directory tool
        directory_tool_messages = self.format_directory_tool_prompt(
            chat_input=chat_input,
            information_call_list=information_call_list,
        )
        directory_tool_call_result, directory_tool_call_metadata = (
            self.directory_subtree_client.generate(
                messages=directory_tool_messages,
                max_tokens=2048,
                tools=[
                    self.directory_subtree_tool.get_tool_param(),
                ],
                tool_choice={"type": "tool", "name": self.directory_subtree_tool.name},
            )
        )
        directory_tool_model_call = ModelCall(
            model_input=directory_tool_messages,
            model_response=directory_tool_call_result,
        )
        current_agent_log.register_model_call(directory_tool_model_call)
        assert (
            len(directory_tool_call_result) == 1
        ), f"Expected one message but got {len(directory_tool_call_result)}"
        assert str(type(directory_tool_call_result[0])) == str(ToolCall)
        directory_tool_call = cast(ToolCall, directory_tool_call_result[0])
        print("Information agent has decided to call directory tool with inputs:")
        print(directory_tool_call.tool_input, "\n")
        directory_tool_output, _ = self.directory_subtree_tool.activate_tool(
            directory_tool_call
        )
        formatted_directory_tool_output, _ = (
            self.directory_subtree_tool.format_tool_result(
                tool_result=directory_tool_output
            )
        )
        directory_tool_entry = ToolCallEntry(
            tool_call=directory_tool_call,
            tool_result=directory_tool_output,
            formatted_tool_output=formatted_directory_tool_output,
        )
        current_agent_log.register_tool_call(directory_tool_entry)

        # Call retrieval tool
        retrieval_tool_messages = self.format_retrieval_tool_prompt(
            chat_input=chat_input,
            information_call_list=information_call_list,
        )
        retrieval_tool_call_result, retrieval_tool_call_metadata = (
            self.codebase_retrieval_client.generate(
                messages=retrieval_tool_messages,
                max_tokens=2048,
                tools=[
                    self.codebase_retrieval_tool.get_tool_param(),
                ],
                tool_choice={"type": "tool", "name": self.codebase_retrieval_tool.name},
            )
        )
        retrieval_tool_model_call = ModelCall(
            model_input=retrieval_tool_messages,
            model_response=retrieval_tool_call_result,
        )
        current_agent_log.register_model_call(retrieval_tool_model_call)
        assert (
            len(retrieval_tool_call_result) == 1
        ), f"Expected one message but got {len(retrieval_tool_call_result)}"
        assert str(type(retrieval_tool_call_result[0])) == str(ToolCall)
        retrieval_tool_call = cast(ToolCall, retrieval_tool_call_result[0])
        print("Information agent has decided to call retrieval tool with inputs:")
        print(retrieval_tool_call.tool_input, "\n")
        retrieval_tool_output, _ = self.codebase_retrieval_tool.activate_tool(
            retrieval_tool_call
        )
        formatted_retrieval_tool_output, _ = (
            self.codebase_retrieval_tool.format_tool_result(
                tool_result=retrieval_tool_output
            )
        )
        retrieval_tool_entry = ToolCallEntry(
            tool_call=retrieval_tool_call,
            tool_result=retrieval_tool_output,
            formatted_tool_output=formatted_retrieval_tool_output,
        )
        current_agent_log.register_tool_call(retrieval_tool_entry)

        result = FastInformationAgentResult(
            information_call_result=[
                directory_tool_entry,
                retrieval_tool_entry,
            ],
        )
        return result

    def format_directory_tool_prompt(
        self,
        chat_input: ChatPromptInput,
        information_call_list: list[ToolCallEntry],
    ) -> list[list[GeneralContentBlock]]:
        """Format the prompt for the information agent function."""

        formatted_chat_input = format_chat_input(
            chat_input,
            chat_history_len=self.chat_history_len,
        )

        directory_tool_prompt = dedent(
            """\
            You are a tool-calling agent that calls the directory subtree tool to gather information to help answer a coding question.
            Look at the user question and the information you have seen so far to decide which directory or directories to explore to best answer the question.
            Please pay careful attention to the tool description to optimally use the tool.
            Please call the tool with the directory or directories you want to explore.
            Only use a path that you are certain exists because you see it:
             - it's in the user query, or
             - you have seen it in a prior directory search or in a prior codebase snippet search.
            If you are not certain a path exists, be more conservative and ask for a higher-level directory.
            """
        )

        messages = []
        user_message_string = ""
        user_message_string += (
            f"The user query we are trying to help with is:\n{formatted_chat_input}"
        )
        user_message_string += "The following information has been gathered so far:\n"
        for tool_call_entry in information_call_list:  # type: ignore
            user_message_string += (
                f"{tool_call_entry.formatted_tool_output.tool_output}\n"  # type: ignore
            )
        user_message_string += directory_tool_prompt
        user_message_string += (
            f"As a reminder, the user query is:\n{formatted_chat_input}"
        )
        messages.append([TextPrompt(text=user_message_string)])
        return messages

    def format_retrieval_tool_prompt(
        self,
        chat_input: ChatPromptInput,
        information_call_list: list[ToolCallEntry],
    ) -> list[list[GeneralContentBlock]]:
        """Format the prompt for the information agent function."""

        formatted_chat_input = format_chat_input(
            chat_input,
            chat_history_len=self.chat_history_len,
        )

        retrieval_tool_prompt = dedent(
            """\
            You are a tool-calling agent that calls a specific tool to gather information to help answer a coding question.
            Please pay careful attention to the tool description to optimally use the tool.
            Try to look at the information gathered already, and determine what information is missing.
            Make sure to also look at the directory tree and previous codebase snippets to guide your search.

            Some additional guidance for using this tool:
            - If the user gives you a log line or variable name, use the contains_string field to look for that exact string. For example, if user asks "error: xyz".
            - Otherwise, almost never use the contains_string field, only when you are absolutely certain the right snippet should contain this string.
            """
        )

        messages = []
        user_message_string = ""
        user_message_string += (
            f"The user query we are trying to help with is:\n{formatted_chat_input}"
        )
        user_message_string += "The following information has been gathered so far:\n"
        for tool_call_entry in information_call_list:  # type: ignore
            user_message_string += (
                f"{tool_call_entry.formatted_tool_output.tool_output}\n"  # type: ignore
            )
        user_message_string += retrieval_tool_prompt
        user_message_string += (
            f"As a reminder, the user query is:\n{formatted_chat_input}"
        )
        messages.append([TextPrompt(text=user_message_string)])
        return messages
