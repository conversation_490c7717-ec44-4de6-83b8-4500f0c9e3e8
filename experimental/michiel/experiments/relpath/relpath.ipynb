#%%
from megatron.tokenizer.tokenizer import StarCoderTokenizer
from megatron.data import indexed_dataset
import pathlib


path = "/mnt/efs/augment/data/processed/rag/dataset/eth61m_depause_prefretsuf_npref100_olap0_quant20/dataset"
tokenizer = StarCoderTokenizer()

ds = indexed_dataset.make_dataset(path, impl="mmap", skip_warmup=True)
#%%
import os
#%%
path_token = tokenizer.filename_id
body_token = tokenizer.retrieval_body_id
sample = ds[1]

absolute_path_num_tokens = 0
relative_path_num_tokens = 0
num_paths = 0

for sample in ds[:500]:

    sample = sample.tolist()


    # Find main path
    path_tokens = sample[1:sample.index(tokenizer.fim_prefix_id)]
    path = tokenizer.detokenize(path_tokens)

    # Collect all paths
    retrieval_paths_tokens = []
    active_path = False
    for idx, token in enumerate(sample):
        if token == path_token:
            active_path = True
            active_idx = idx
        elif token == body_token:
            active_path = False
            retrieval_paths_tokens.append(sample[active_idx + 1:idx])

    retrieval_paths = [
        tokenizer.detokenize(retrieval_path_tokens)
        for retrieval_path_tokens in retrieval_paths_tokens
    ]

    # Get retrieval paths relative to main path
    relative_retrieval_paths = [
        os.path.relpath(retrieval_path, path)
        for retrieval_path in retrieval_paths
    ]

    relative_retrieval_paths_tokens = [
        tokenizer.tokenize(relative_retrieval_path)
        for relative_retrieval_path in relative_retrieval_paths
    ]

    num_paths += len(relative_retrieval_paths)

    for relative_retrieval_path_tokens in relative_retrieval_paths_tokens:
        relative_path_num_tokens += len(relative_retrieval_path_tokens)

    for retrieval_path_tokens in retrieval_paths_tokens:
        absolute_path_num_tokens += len(retrieval_path_tokens)


print(f"average number of tokens in a path: {absolute_path_num_tokens / num_paths}")
print(f"ratio of relative path tokens to absolute path tokens: {relative_path_num_tokens / absolute_path_num_tokens}")
#%%
relative_retrieval_paths
#%%
