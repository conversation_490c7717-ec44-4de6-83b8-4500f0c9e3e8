#!/usr/bin/env python3
"""
Beachhead Failure Log Analyzer

A command line tool to query and analyze beachhead failure logs from Google Cloud Logging.
Provides insights into failure patterns, pod counts, timing, and cluster information.
"""

import argparse
import json
import sys
import subprocess
from collections import Counter
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Analyze beachhead logs from Google Cloud Logging",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Analyze failures from the last 24 hours (default)
  %(prog)s failures

  # Analyze failures from the last 6 hours
  %(prog)s failures --hours 6

  # Analyze restarts from the last 24 hours
  %(prog)s restarts

  # Get stderr logs for a specific pod
  %(prog)s logs raws-12345678-1234-1234-1234-123456789abc-0
  %(prog)s logs 12345678-1234-1234-1234-123456789abc

  # Analyze failures from a specific time range
  %(prog)s failures --start "2024-01-15T10:00:00Z" --end "2024-01-15T18:00:00Z"

  # Include detailed log entries
  %(prog)s failures --verbose

  # Export to CSV format
  %(prog)s failures --format csv
        """,
    )

    # Add subcommands
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Failures subcommand
    failures_parser = subparsers.add_parser(
        "failures", help="Analyze beachhead failure logs"
    )
    add_common_args(failures_parser)

    # Restarts subcommand
    restarts_parser = subparsers.add_parser(
        "restarts", help="Analyze beachhead restart logs"
    )
    add_common_args(restarts_parser)

    # Logs subcommand
    logs_parser = subparsers.add_parser(
        "logs", help="Get stderr logs for a specific pod"
    )
    logs_parser.add_argument(
        "pod_name", help="Pod name or UUID (e.g., raws-uuid-0 or just uuid)"
    )
    add_common_args(logs_parser, include_format=False)

    return parser.parse_args()


def add_common_args(parser, include_format=True):
    """Add common arguments to a subparser."""
    # Time range options
    time_group = parser.add_mutually_exclusive_group()
    time_group.add_argument(
        "--hours",
        type=int,
        default=24,
        help="Number of hours to look back from now (default: 24)",
    )
    time_group.add_argument(
        "--start",
        type=str,
        help="Start time in ISO format (e.g., 2024-01-15T10:00:00Z)",
    )

    parser.add_argument(
        "--end", type=str, help="End time in ISO format (only used with --start)"
    )

    # Query options
    parser.add_argument(
        "--limit",
        type=int,
        default=1000,
        help="Maximum number of log entries to retrieve (default: 1000)",
    )

    # Output options
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Include detailed log entries in output",
    )

    if include_format:
        parser.add_argument(
            "--format",
            choices=["summary", "json", "csv"],
            default="summary",
            help="Output format (default: summary)",
        )


def get_time_range(args) -> tuple[datetime, datetime]:
    """Get the time range for the query based on arguments."""
    if args.start:
        start_time = datetime.fromisoformat(args.start.replace("Z", "+00:00"))
        if args.end:
            end_time = datetime.fromisoformat(args.end.replace("Z", "+00:00"))
        else:
            end_time = datetime.now(timezone.utc)
    else:
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=args.hours)

    return start_time, end_time


def get_projects_to_query() -> List[str]:
    """Get the list of GCP projects to query."""
    # Default to production agent cluster only
    return ["agent-sandbox-prod"]


def format_pod_name(pod_input: str) -> str:
    """Format pod name from input. If input looks like a UUID, format as raws-uuid-0."""
    import re

    # Remove any existing hyphens to check if it's a pure UUID
    clean_input = pod_input.replace("-", "")

    # Check if input looks like a UUID (32 hex characters)
    uuid_pattern = r"^[0-9a-f]{32}$"

    if re.match(uuid_pattern, clean_input.lower()):
        # It's a UUID, format it properly with hyphens
        formatted_uuid = f"{clean_input[:8]}-{clean_input[8:12]}-{clean_input[12:16]}-{clean_input[16:20]}-{clean_input[20:]}"
        return f"raws-{formatted_uuid}-0"

    # Check if it's already a properly formatted UUID with hyphens
    uuid_with_hyphens_pattern = (
        r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
    )
    if re.match(uuid_with_hyphens_pattern, pod_input.lower()):
        return f"raws-{pod_input}-0"

    # Not a UUID, return as-is
    return pod_input


def build_log_filter(
    start_time: datetime, end_time: datetime, event_type: str = "beachhead_failure"
) -> str:
    """Build the Google Cloud Logging filter for beachhead events."""
    start_str = start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
    end_str = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")

    return f"""
        jsonPayload._augment_event="{event_type}"
        AND timestamp >= "{start_str}"
        AND timestamp <= "{end_str}"
    """.strip()


def build_stderr_log_filter(
    start_time: datetime, end_time: datetime, pod_name: str
) -> str:
    """Build the Google Cloud Logging filter for stderr logs from a specific pod."""
    start_str = start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
    end_str = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")

    return f"""
        jsonPayload._augment_innie_stderr=true
        AND resource.labels.pod_name="{pod_name}"
        AND timestamp >= "{start_str}"
        AND timestamp <= "{end_str}"
    """.strip()


def query_logs(project: str, log_filter: str, limit: int) -> List[Dict[str, Any]]:
    """Query logs from a specific project using gcloud command."""
    try:
        # Build gcloud command
        cmd = [
            "gcloud",
            "logging",
            "read",
            log_filter,
            f"--project={project}",
            f"--limit={limit}",
            "--format=json",
            "--freshness=1d",  # Only look at recent logs for performance
        ]

        # Execute gcloud command
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60,  # 60 second timeout
        )

        if result.returncode != 0:
            print(f"Error querying project {project}: {result.stderr}", file=sys.stderr)
            return []

        # Parse JSON output
        if not result.stdout.strip():
            return []

        log_entries = json.loads(result.stdout)

        # Convert to our format
        results = []
        for entry in log_entries:
            log_data = {
                "timestamp": entry.get("timestamp", ""),
                "project": project,
                "severity": entry.get("severity", ""),
                "resource": entry.get("resource", {}),
                "labels": entry.get("labels", {}),
                "payload": entry.get("jsonPayload", {}),
            }
            results.append(log_data)

        return results

    except subprocess.TimeoutExpired:
        print(f"Timeout querying project {project}", file=sys.stderr)
        return []
    except json.JSONDecodeError as e:
        print(
            f"Error parsing JSON response from project {project}: {e}", file=sys.stderr
        )
        return []
    except Exception as e:
        print(f"Error querying project {project}: {e}", file=sys.stderr)
        return []


def query_beachhead_failures(
    project: str, log_filter: str, limit: int
) -> List[Dict[str, Any]]:
    """Query beachhead failure logs from a specific project using gcloud command."""
    return query_logs(project, log_filter, limit)


def extract_pod_info(log_entry: Dict[str, Any]) -> Dict[str, str]:
    """Extract pod and cluster information from a log entry."""
    info = {
        "pod_name": "unknown",
        "namespace": "unknown",
        "cluster": "unknown",
        "location": "unknown",
        "staging": "unknown",
    }

    # Extract from resource labels (gcloud JSON format)
    resource = log_entry.get("resource", {})
    if isinstance(resource, dict):
        labels = resource.get("labels", {})
        if isinstance(labels, dict):
            info["pod_name"] = labels.get("pod_name", "unknown")
            info["namespace"] = labels.get("namespace_name", "unknown")
            info["cluster"] = labels.get("cluster_name", "unknown")
            info["location"] = labels.get("location", "unknown")

    # Also check top-level labels
    top_labels = log_entry.get("labels", {})
    if isinstance(top_labels, dict):
        if info["pod_name"] == "unknown":
            info["pod_name"] = top_labels.get("pod_name", "unknown")
        if info["namespace"] == "unknown":
            info["namespace"] = top_labels.get("namespace_name", "unknown")
        if info["cluster"] == "unknown":
            info["cluster"] = top_labels.get("cluster_name", "unknown")
        if info["location"] == "unknown":
            info["location"] = top_labels.get("location", "unknown")

    # Determine if staging based on various indicators
    pod_name = info["pod_name"]
    namespace = info["namespace"]
    cluster = info["cluster"]

    staging_indicators = ["staging", "dev", "test"]
    if any(indicator in pod_name.lower() for indicator in staging_indicators):
        info["staging"] = "staging"
    elif any(indicator in namespace.lower() for indicator in staging_indicators):
        info["staging"] = "staging"
    elif any(indicator in cluster.lower() for indicator in staging_indicators):
        info["staging"] = "staging"
    else:
        info["staging"] = "production"

    return info


def analyze_failures(log_entries: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze the beachhead failure log entries."""
    return analyze_events(log_entries, "failures")


def analyze_restarts(log_entries: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze the beachhead restart log entries."""
    return analyze_events(log_entries, "restarts")


def analyze_events(
    log_entries: List[Dict[str, Any]], event_type: str
) -> Dict[str, Any]:
    """Analyze beachhead event log entries (failures or restarts)."""
    if not log_entries:
        return {
            f"total_{event_type}": 0,
            "pod_counts": {},
            "cluster_counts": {},
            "namespace_counts": {},
            "staging_counts": {},
            "hourly_distribution": {},
            f"{event_type}_details": [],
        }

    pod_counts = Counter()
    cluster_counts = Counter()
    namespace_counts = Counter()
    staging_counts = Counter()
    hourly_distribution = Counter()
    event_details = []

    for entry in log_entries:
        pod_info = extract_pod_info(entry)
        timestamp = datetime.fromisoformat(entry["timestamp"].replace("Z", "+00:00"))

        # Count by various dimensions
        pod_with_namespace = f"{pod_info['pod_name']} ({pod_info['namespace']})"
        pod_counts[pod_with_namespace] += 1
        cluster_counts[pod_info["cluster"]] += 1
        namespace_counts[pod_info["namespace"]] += 1
        staging_counts[pod_info["staging"]] += 1

        # Hourly distribution
        hour_key = timestamp.strftime("%Y-%m-%d %H:00")
        hourly_distribution[hour_key] += 1

        # Extract event details
        payload = entry.get("payload", {})
        restart_count = "unknown"
        return_code = "unknown"
        message = "unknown"

        if isinstance(payload, dict):
            restart_count = payload.get("restart_count", "unknown")
            return_code = payload.get("rc", "unknown")
            message = payload.get("MESSAGE", "unknown")

        event_details.append(
            {
                "timestamp": entry["timestamp"],
                "pod_name": pod_info["pod_name"],
                "namespace": pod_info["namespace"],
                "cluster": pod_info["cluster"],
                "location": pod_info["location"],
                "staging": pod_info["staging"],
                "restart_count": restart_count,
                "return_code": return_code,
                "message": message,
                "project": entry["project"],
            }
        )

    return {
        f"total_{event_type}": len(log_entries),
        "pod_counts": dict(pod_counts.most_common()),
        "cluster_counts": dict(cluster_counts.most_common()),
        "namespace_counts": dict(namespace_counts.most_common()),
        "staging_counts": dict(staging_counts.most_common()),
        "hourly_distribution": dict(sorted(hourly_distribution.items())),
        f"{event_type}_details": event_details,
    }


def analyze_stderr_logs(log_entries: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze stderr log entries for a specific pod."""
    if not log_entries:
        return {
            "total_logs": 0,
            "pod_name": "unknown",
            "log_entries": [],
        }

    log_details = []
    pod_name = "unknown"

    for entry in log_entries:
        pod_info = extract_pod_info(entry)
        if pod_name == "unknown":
            pod_name = pod_info["pod_name"]

        # Extract log message and additional fields
        payload = entry.get("payload", {})
        message = "unknown"
        restart_count = "unknown"
        stderr_line_number = 0
        augment_event = "unknown"
        augment_img = "unknown"

        if isinstance(payload, dict):
            message = payload.get("MESSAGE", payload.get("message", "unknown"))
            restart_count = payload.get("restart_count", "unknown")
            stderr_line_number = payload.get("stderr_line_number", 0)
            augment_event = payload.get("_augment_event", "unknown")
            augment_img = payload.get("_augment_img", "unknown")

        log_details.append(
            {
                "timestamp": entry["timestamp"],
                "pod_name": pod_info["pod_name"],
                "namespace": pod_info["namespace"],
                "cluster": pod_info["cluster"],
                "location": pod_info["location"],
                "message": message,
                "project": entry["project"],
                "severity": entry.get("severity", "unknown"),
                "restart_count": restart_count,
                "stderr_line_number": stderr_line_number,
                "augment_event": augment_event,
                "augment_img": augment_img,
                "raw_payload": payload,  # Include raw payload for verbose output
            }
        )

    # Sort log entries by restart_count and stderr_line_number
    def sort_key(entry):
        restart_count = entry["restart_count"]
        stderr_line_number = entry["stderr_line_number"]

        # Convert restart_count to int for sorting, use 0 if unknown
        try:
            restart_count_int = int(restart_count) if restart_count != "unknown" else 0
        except (ValueError, TypeError):
            restart_count_int = 0

        # Convert stderr_line_number to int for sorting
        try:
            stderr_line_number_int = int(stderr_line_number)
        except (ValueError, TypeError):
            stderr_line_number_int = 0

        return (restart_count_int, stderr_line_number_int)

    log_details.sort(key=sort_key)

    return {
        "total_logs": len(log_entries),
        "pod_name": pod_name,
        "log_entries": log_details,
    }


def format_summary_output(
    analysis: Dict[str, Any],
    start_time: datetime,
    end_time: datetime,
    event_type: str = "failures",
):
    """Format the analysis results as a human-readable summary."""
    title = f"Beachhead {event_type.title()} Analysis"
    print(title)
    print(
        f"Time Range: {start_time.strftime('%Y-%m-%d %H:%M:%S UTC')} to {end_time.strftime('%Y-%m-%d %H:%M:%S UTC')}"
    )

    total_key = f"total_{event_type}"
    total_count = analysis.get(total_key, 0)
    print(f"Total {event_type.title()}: {total_count}")
    print()

    if total_count == 0:
        print(f"No beachhead {event_type} found in the specified time range.")
        return

    # Pod counts
    print(f"{event_type.title()} by Pod:")
    for pod, count in analysis["pod_counts"].items():
        print(f"  {pod}: {count}")
    print()

    # Cluster counts
    print(f"{event_type.title()} by Cluster:")
    for cluster, count in analysis["cluster_counts"].items():
        print(f"  {cluster}: {count}")
    print()

    # Namespace counts
    print(f"{event_type.title()} by Namespace:")
    for namespace, count in analysis["namespace_counts"].items():
        print(f"  {namespace}: {count}")
    print()


def format_stderr_logs_output(
    analysis: Dict[str, Any],
    start_time: datetime,
    end_time: datetime,
    verbose: bool = False,
):
    """Format stderr logs as human-readable output."""
    print("Beachhead Stderr Logs")
    print(
        f"Time Range: {start_time.strftime('%Y-%m-%d %H:%M:%S UTC')} to {end_time.strftime('%Y-%m-%d %H:%M:%S UTC')}"
    )
    print(f"Pod: {analysis['pod_name']}")
    print(f"Total Log Entries: {analysis['total_logs']}")
    print()

    if analysis["total_logs"] == 0:
        print("No stderr logs found for the specified pod in the time range.")
        return

    if verbose:
        print("Raw Log Entries:")
        for log_entry in analysis["log_entries"]:
            timestamp = datetime.fromisoformat(
                log_entry["timestamp"].replace("Z", "+00:00")
            )
            print(f"Timestamp: {timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}")
            print(f"Pod: {log_entry['pod_name']}")
            print(f"Namespace: {log_entry['namespace']}")
            print(f"Cluster: {log_entry['cluster']}")
            print(f"Location: {log_entry['location']}")
            print(f"Project: {log_entry['project']}")
            print(f"Severity: {log_entry.get('severity', 'unknown')}")
            print(f"Restart Count: {log_entry.get('restart_count', 'unknown')}")
            print(
                f"Stderr Line Number: {log_entry.get('stderr_line_number', 'unknown')}"
            )
            print(f"Augment Event: {log_entry.get('augment_event', 'unknown')}")
            print(f"Augment Image: {log_entry.get('augment_img', 'unknown')}")
            print(f"Message: {log_entry['message']}")

            # Show raw payload data
            raw_payload = log_entry.get("raw_payload", {})
            if raw_payload and isinstance(raw_payload, dict):
                print("Raw Payload:")
                import json

                print(json.dumps(raw_payload, indent=2))

            print("-" * 80)
    else:
        print("Log Entries:")
        print("  Timestamp               | Rstrt# | Event    | Image    | Message")
        print("  " + "-" * 80)
        for log_entry in analysis["log_entries"]:
            timestamp = datetime.fromisoformat(
                log_entry["timestamp"].replace("Z", "+00:00")
            )
            restart_count = str(log_entry.get("restart_count", "unknown"))[
                :6
            ]  # Truncate to 6 chars for "Rstrt#"

            augment_event = str(log_entry.get("augment_event", "unknown"))
            # Event will be "beachhead_${event}_logging" and we only need the middle
            augment_event = augment_event.replace("beachhead_", "").replace(
                "_logging", ""
            )
            augment_event = augment_event[:8]  # Truncate to 8 chars

            augment_img = str(log_entry.get("augment_img", "unknown"))[
                :8
            ]  # Truncate to 8 chars
            message = log_entry["message"]

            print(
                f"  {timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')} | {restart_count:>6} | {augment_event:<8} | {augment_img:<8} | {message}"
            )
    print()


def format_json_output(analysis: Dict[str, Any]):
    """Format the analysis results as JSON."""
    print(json.dumps(analysis, indent=2, default=str))


def format_csv_output(analysis: Dict[str, Any], event_type: str = "failures"):
    """Format the event details as CSV."""
    details_key = f"{event_type}_details"
    details = analysis.get(details_key, [])

    if not details:
        print(
            "timestamp,pod_name,namespace,cluster,location,staging,restart_count,return_code,message,project"
        )
        return

    # CSV header
    print(
        "timestamp,pod_name,namespace,cluster,location,staging,restart_count,return_code,message,project"
    )

    # CSV rows
    for detail in details:
        # Escape commas and quotes in message
        message = str(detail["message"]).replace('"', '""')
        if "," in message:
            message = f'"{message}"'

        print(
            f"{detail['timestamp']},{detail['pod_name']},{detail['namespace']},"
            f"{detail['cluster']},{detail['location']},{detail['staging']},"
            f"{detail['restart_count']},{detail['return_code']},{message},{detail['project']}"
        )


def handle_failures_command(args):
    """Handle the failures subcommand."""
    start_time, end_time = get_time_range(args)
    projects = get_projects_to_query()
    log_filter = build_log_filter(start_time, end_time, "beachhead_failure")

    if args.verbose:
        print(f"Querying projects: {', '.join(projects)}", file=sys.stderr)
        print(f"Time range: {start_time} to {end_time}", file=sys.stderr)
        print(f"Log filter: {log_filter}", file=sys.stderr)
        print(file=sys.stderr)

    # Query all projects
    all_entries = []
    for project in projects:
        if args.verbose:
            print(f"Querying project: {project}...", file=sys.stderr)

        entries = query_logs(project, log_filter, args.limit)
        all_entries.extend(entries)

        if args.verbose:
            print(f"Found {len(entries)} entries in {project}", file=sys.stderr)

    # Analyze the results
    analysis = analyze_failures(all_entries)

    # Output results
    if args.format == "json":
        format_json_output(analysis)
    elif args.format == "csv":
        format_csv_output(analysis, "failures")
    else:
        format_summary_output(analysis, start_time, end_time, "failures")

        if args.verbose and analysis.get("failures_details"):
            print("\nDetailed Failure Entries:")
            for detail in analysis["failures_details"][:20]:  # Show first 20
                print(
                    f"  {detail['timestamp']} | {detail['pod_name']} | {detail['cluster']} | "
                    f"{detail['staging']} | RC:{detail['return_code']} | {detail['message']}"
                )
            if len(analysis["failures_details"]) > 20:
                print(
                    f"  ... and {len(analysis['failures_details']) - 20} more entries"
                )


def handle_restarts_command(args):
    """Handle the restarts subcommand."""
    start_time, end_time = get_time_range(args)
    projects = get_projects_to_query()
    log_filter = build_log_filter(start_time, end_time, "beachhead_restart")

    if args.verbose:
        print(f"Querying projects: {', '.join(projects)}", file=sys.stderr)
        print(f"Time range: {start_time} to {end_time}", file=sys.stderr)
        print(f"Log filter: {log_filter}", file=sys.stderr)
        print(file=sys.stderr)

    # Query all projects
    all_entries = []
    for project in projects:
        if args.verbose:
            print(f"Querying project: {project}...", file=sys.stderr)

        entries = query_logs(project, log_filter, args.limit)
        all_entries.extend(entries)

        if args.verbose:
            print(f"Found {len(entries)} entries in {project}", file=sys.stderr)

    # Analyze the results
    analysis = analyze_restarts(all_entries)

    # Output results
    if args.format == "json":
        format_json_output(analysis)
    elif args.format == "csv":
        format_csv_output(analysis, "restarts")
    else:
        format_summary_output(analysis, start_time, end_time, "restarts")

        if args.verbose and analysis.get("restarts_details"):
            print("\nDetailed Restart Entries:")
            for detail in analysis["restarts_details"][:20]:  # Show first 20
                print(
                    f"  {detail['timestamp']} | {detail['pod_name']} | {detail['cluster']} | "
                    f"{detail['staging']} | RC:{detail['return_code']} | {detail['message']}"
                )
            if len(analysis["restarts_details"]) > 20:
                print(
                    f"  ... and {len(analysis['restarts_details']) - 20} more entries"
                )


def handle_logs_command(args):
    """Handle the logs subcommand."""
    start_time, end_time = get_time_range(args)
    projects = get_projects_to_query()

    # Format the pod name
    pod_name = format_pod_name(args.pod_name)
    log_filter = build_stderr_log_filter(start_time, end_time, pod_name)

    if args.verbose:
        print(f"Querying projects: {', '.join(projects)}", file=sys.stderr)
        print(f"Time range: {start_time} to {end_time}", file=sys.stderr)
        print(f"Pod name: {pod_name}", file=sys.stderr)
        print(f"Log filter: {log_filter}", file=sys.stderr)
        print(file=sys.stderr)

    # Query all projects
    all_entries = []
    for project in projects:
        if args.verbose:
            print(f"Querying project: {project}...", file=sys.stderr)

        entries = query_logs(project, log_filter, args.limit)
        all_entries.extend(entries)

        if args.verbose:
            print(f"Found {len(entries)} entries in {project}", file=sys.stderr)

    # Analyze the results
    analysis = analyze_stderr_logs(all_entries)

    # Output results (logs subcommand doesn't support CSV/JSON format)
    format_stderr_logs_output(analysis, start_time, end_time, args.verbose)


def main():
    """Main function."""
    args = parse_args()

    # Check if a command was provided
    if not args.command:
        print(
            "Error: Please specify a command (failures, restarts, or logs)",
            file=sys.stderr,
        )
        print("Use --help for more information.", file=sys.stderr)
        sys.exit(1)

    # Route to appropriate handler
    if args.command == "failures":
        handle_failures_command(args)
    elif args.command == "restarts":
        handle_restarts_command(args)
    elif args.command == "logs":
        handle_logs_command(args)
    else:
        print(f"Error: Unknown command '{args.command}'", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
