#!/bin/bash

# Script to analyze running agent images and their git refs
# Usage: ./analyze_agent_images.sh [kubectl-context] [--dry-run]

set -euo pipefail

# Parse arguments
DRY_RUN=false
KUBECTL_CONTEXT=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [kubectl-context] [--dry-run]"
            echo "  kubectl-context: Kubernetes context to use (default: prod-agent0)"
            echo "  --dry-run: Show what would be done without making API calls"
            exit 0
            ;;
        *)
            if [[ -z "${KUBECTL_CONTEXT}" ]]; then
                KUBECTL_CONTEXT="$1"
            else
                echo "Error: Unknown argument $1"
                exit 1
            fi
            shift
            ;;
    esac
done

# Set default context if not provided
KUBECTL_CONTEXT="${KUBECTL_CONTEXT:-prod-agent0}"

# Configuration
KUBECTL_CONTEXT="${1:-prod-agent0}"
STAGING_REPO="us-central1-docker.pkg.dev/agent-sandbox-prod/agents-staging-us-central1/augment-remote-agent-virt"
PROD_REPO="us-central1-docker.pkg.dev/agent-sandbox-prod/agents-prod-us-central1/augment-remote-agent-virt"

# Temporary files
TEMP_DIR=$(mktemp -d)
PODS_YAML="${TEMP_DIR}/pods.yaml"
IMAGE_DATA="${TEMP_DIR}/image_data.txt"
STAGING_RESULTS="${TEMP_DIR}/staging_results.txt"
PROD_RESULTS="${TEMP_DIR}/prod_results.txt"

# Cleanup function
cleanup() {
    rm -rf "${TEMP_DIR}"
}
trap cleanup EXIT

# Check required tools
check_requirements() {
    local missing_tools=()

    if ! command -v kubectl &> /dev/null; then
        missing_tools+=("kubectl")
    fi

    if [[ "${DRY_RUN}" == "false" ]] && ! command -v gcloud &> /dev/null; then
        missing_tools+=("gcloud")
    fi

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        echo "Error: Missing required tools: ${missing_tools[*]}"
        echo "Please install the missing tools and try again."
        exit 1
    fi
}

check_requirements

echo "Analyzing agent images in context: ${KUBECTL_CONTEXT}" >&2
if [[ "${DRY_RUN}" == "true" ]]; then
    echo "(DRY RUN MODE - no gcloud API calls will be made)" >&2
fi
echo "==========================================" >&2

# Get pods and extract image information
echo "Fetching pod information from all namespaces..." >&2
kubectl --context "${KUBECTL_CONTEXT}" get pods -l aug.remote-agent-workspace.id --all-namespaces -o yaml > "${PODS_YAML}"

# Extract imageID (SHA) and image name from the YAML
echo "Extracting image information..." >&2

# More robust extraction that handles the YAML structure better
python3 -c "
import yaml
import sys
import re

with open('${PODS_YAML}', 'r') as f:
    data = yaml.safe_load(f)

if 'items' in data:
    for pod in data['items']:
        if 'status' in pod and 'containerStatuses' in pod['status']:
            for container in pod['status']['containerStatuses']:
                if 'image' in container and 'imageID' in container:
                    image = container['image']
                    image_id = container['imageID']
                    # Extract SHA from imageID (format: docker-pullable://repo@sha256:hash)
                    sha_match = re.search(r'sha256:([a-f0-9]{64})', image_id)
                    if sha_match and 'augment-remote-agent-virt' in image:
                        sha = sha_match.group(1)
                        print(f'{image},{sha}')
" > "${IMAGE_DATA}" 2>/dev/null || {
    # Fallback to grep method if python/yaml not available
    echo "Python/YAML not available, using grep fallback..." >&2
    grep -E "(imageID|image):" "${PODS_YAML}" | \
        grep -v "imagePullPolicy" | \
        sed 's/^[[:space:]]*//' | \
        paste - - | \
        awk '{
            # Extract image name and SHA
            if ($1 ~ /^image:/) {
                image = $2
            } else if ($1 ~ /^imageID:/) {
                sha = $2
                gsub(/.*sha256:/, "", sha)
                if (image ~ /augment-remote-agent-virt/) {
                    print image "," sha
                }
            }
        }' > "${IMAGE_DATA}"
}

echo "Found $(wc -l < "${IMAGE_DATA}") running agent containers" >&2

# Function to lookup git ref from SHA
lookup_git_ref() {
    local sha="$1"
    local repo="$2"

    # Use gcloud to lookup tags for this SHA
    local tags
    tags=$(gcloud container images list-tags "${repo}" \
        --filter="digest:sha256:${sha}" \
        --format="value(tags)" 2>/dev/null || echo "")

    if [[ -n "${tags}" ]]; then
        # Extract git ref from STAGING:$ref or STAGING-$ref format
        local git_ref
        git_ref=$(echo "${tags}" | tr ',' '\n' | grep "^STAGING[:-]" | head -1 | sed 's/^STAGING[:-]//' || echo "")
        if [[ -n "${git_ref}" ]]; then
            echo "${git_ref}"
        else
            # If no STAGING: or STAGING- tag found, try to find any tag that looks like a git ref
            git_ref=$(echo "${tags}" | tr ',' '\n' | grep -E '^[a-f0-9]{7,40}$|^[a-zA-Z0-9_-]+/[a-zA-Z0-9_-]+$' | head -1 || echo "")
            if [[ -n "${git_ref}" ]]; then
                echo "${git_ref}"
            else
                echo "unknown"
            fi
        fi
    else
        echo "unknown"
    fi
}

# Function to get commit date from git ref
get_commit_date() {
    local git_ref="$1"

    if [[ "${git_ref}" == "unknown" || "${git_ref}" == "<dry-run-placeholder>" ]]; then
        echo "unknown"
        return
    fi

    # Try to get commit date using git log with timeout
    local commit_date
    commit_date=$(timeout 10 git log -1 --format="%ci" "${git_ref}" 2>/dev/null || echo "")

    if [[ -n "${commit_date}" ]]; then
        # Convert to UTC format (YYYY-MM-DD HH:MM:SS UTC)
        date -u -d "${commit_date}" "+%Y-%m-%d %H:%M:%S UTC" 2>/dev/null || echo "unknown"
    else
        echo "unknown"
    fi
}

# Function to process images and count by ref
process_images() {
    local repo_pattern="$1"
    local lookup_repo="$2"
    local output_file="$3"
    local repo_name="$4"

    echo "Processing ${repo_name} images..." >&2

    # Filter images for this repository and count occurrences
    grep "${repo_pattern}" "${IMAGE_DATA}" | \
        cut -d',' -f2 | \
        sort | uniq -c | \
        while read -r count sha; do
            echo "  Looking up git ref for SHA: ${sha:0:12}..." >&2
            if [[ "${DRY_RUN}" == "true" ]]; then
                git_ref="<dry-run-placeholder>"
                commit_date="<dry-run-placeholder>"
            else
                git_ref=$(lookup_git_ref "${sha}" "${lookup_repo}")
                echo "  Getting commit date for ref: ${git_ref}..." >&2
                commit_date=$(get_commit_date "${git_ref}")
            fi
            echo "${sha},${git_ref},${count},${commit_date}"
        done > "${output_file}"
}

# Process staging images
process_images "agents-staging-us-central1" "${STAGING_REPO}" "${STAGING_RESULTS}" "STAGING"

# Process prod images
process_images "agents-prod-us-central1" "${STAGING_REPO}" "${PROD_RESULTS}" "PROD"

# Function to display results
display_results() {
    local file="$1"
    local title="$2"

    if [[ -s "${file}" ]]; then
        echo
        echo "${title} Images:"
        echo "Count | Commit Date             | Git Ref    | SHA"
        echo "------|-------------------------|------------|----------------------------------------------------------------"

        # Sort by count (numerically, descending)
        sort -t',' -k3 -nr "${file}" | while IFS=',' read -r sha git_ref count commit_date; do
            printf "%5s | %-23s | %-10s | %s\n" "${count}" "${commit_date}" "${git_ref:0:10}" "${sha}"
        done
    else
        echo
        echo "${title} Images: None found"
    fi
}

# Display results
display_results "${STAGING_RESULTS}" "STAGING"
display_results "${PROD_RESULTS}" "PROD"

# Summary
echo
echo "Summary:"
echo "--------"
staging_total=$(awk -F',' '{sum += $3} END {print sum+0}' "${STAGING_RESULTS}")
prod_total=$(awk -F',' '{sum += $3} END {print sum+0}' "${PROD_RESULTS}")
echo "Total STAGING agents: ${staging_total}"
echo "Total PROD agents: ${prod_total}"
echo "Total agents: $((staging_total + prod_total))"
