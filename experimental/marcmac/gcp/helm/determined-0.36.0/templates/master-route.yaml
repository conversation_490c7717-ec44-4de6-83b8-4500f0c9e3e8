{{- if .Values.openshiftRoute }}
{{- if .Values.openshiftRoute.enabled }}
apiVersion: route.openshift.io/v1
kind: Route
metadata:
  namespace: {{ .Release.Namespace }}
  labels:
    app: determined-master-{{ .Release.Name }}
    release: {{ .Release.Name }}
  name: determined-master-route-{{ .Release.Name }}
spec:
  {{- if .Values.openshiftRoute.host }}
  host: {{ .Values.openshiftRoute.host }}
  {{- end }}
  tls:
    termination: {{ .Values.openshiftRoute.termination }}
  to:
    kind: Service
    name: determined-master-service-{{ .Release.Name }}
    weight: 100
  wildcardPolicy: None
{{- end }}
{{- end }}
