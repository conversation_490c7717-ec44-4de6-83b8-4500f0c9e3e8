import subprocess
import tempfile

from unidiff import PatchSet

from base.diff_utils.diff_utils import File
from research.core.diff_utils import Repository
from research.core.next_edit_location_prompt_input import (
    FileLocation,
    NextEditLocationSystemInput,
)
from research.core.types import Document, Scored
from research.static_analysis.common import Char<PERSON><PERSON><PERSON>, Path
from research.utils import repo_change_utils
import yaml
from research.eval.harness.systems.next_edit_location_system import (
    BasicNextEditLocationSystem,
)
from research.eval.harness.factories import create_retriever


SYSTEM_CONFIG = """
retriever:
    scorer:
        name: dense_scorer_v2_fbwd
        checkpoint_path: /mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.S1.2_prs_2k.keepmost.filter.empty10,R1.2_v13-128.30lines,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50
        tokenizer_name: starcoder
        cache_dir: /tmp/augment/cache
    chunker:
        name: smart_line_level
        max_chunk_chars: 1280
    query_formatter:
        name: next_edit_location_query
        tokenizer: starcoder
    document_formatter:
        name: base:ethanol6-embedding-with-path-key
        tokenizer: starcoder
        max_tokens: 999
"""

RETRIEVER_CONFIG = {
    "scorer": {
        "name": "dense_scorer_v2_fbwd",
        "checkpoint_path": "/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3",
    },
    "chunker": {"name": "line_level", "max_lines_per_chunk": 30},
    "query_formatter": {
        "name": "base:chatanol6",
        "tokenizer_name": "rogue",
        "max_tokens": 1024,
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "tokenizer_name": "rogue",
        "add_path": True,
        "max_tokens": 1024,
    },
}


class LocationEval:
    def __init__(self):
        self._system = BasicNextEditLocationSystem.from_yaml_config(
            yaml.safe_load(SYSTEM_CONFIG)
        )
        self._retrieval_database = create_retriever(RETRIEVER_CONFIG)

    def generate_location_candidates(
        self,
        git_remote_url: str,
        sha: str,
        stacktrace: str,
        break_diff: str,
        top_k: int = 32,
    ) -> list[Scored[FileLocation]]:
        with tempfile.TemporaryDirectory() as tmpdir:
            # checkout the sha
            subprocess.run(["git", "init", "-b", "main", tmpdir], check=True)
            subprocess.run(
                ["git", "remote", "add", "origin", git_remote_url],
                check=True,
                cwd=tmpdir,
            )
            subprocess.run(
                ["git", "fetch", "--quiet", "--depth", "1", "origin", sha],
                check=True,
                cwd=tmpdir,
            )
            subprocess.run(
                ["git", "-c", "advice.detachedHead=false", "checkout", "FETCH_HEAD"],
                check=True,
                cwd=tmpdir,
            )
            sha_repo = Repository.load(Path(tmpdir))

        self._system._retriever.scorer.query_formatter.max_instruction_tokens = 4096  # type: ignore
        self._system._retriever.scorer.query_formatter.max_prompt_tokens = 8192  # type: ignore

        rc = repo_change_utils.repo_change_from_patchset(
            sha_repo,
            PatchSet(break_diff),
            reverse=True,
        )

        documents = [
            Document.new(text=code, path=str(path))
            for path, code in rc.after_files.items()
        ]

        self._system.add_docs(documents)

        input_ = NextEditLocationSystemInput(
            current_file=File("", ""),
            edit_region=CharRange(0, 0),
            instruction=stacktrace,
            recent_changes=tuple(
                f.map(lambda x: x.to_file()) for f in rc.changed_files
            ),
            doc_ids=frozenset([doc.id for doc in documents]),
            past_diff=break_diff,
            top_k=top_k,
        )

        output = self._system.generate(input_)
        return list(output.scored_candidates)
