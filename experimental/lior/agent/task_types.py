"""Type definitions for task execution."""

from typing import TypedDict, Optional


class TaskResult(TypedDict, total=False):
    """Result of a task execution.

    All fields are optional to allow for different states:
    - Pending: Only status and task_id are set
    - Running: status, task_id, and start_time are set
    - Completed: All fields except error are set
    - Failed: status, task_id, error, and returncode are set
    """

    task_id: str  # Unique task identifier
    status: str  # pending, running, completed, failed, cancelled
    command: str  # The command that was executed
    output: Optional[str]  # Command output if any
    error: Optional[str]  # Error message if failed
    returncode: Optional[int]  # Process return code
    start_time: Optional[float]  # When the task started
    end_time: Optional[float]  # When the task completed/failed/cancelled
    duration: Optional[float]  # Duration in seconds
    branch: Optional[str]  # Git branch used
