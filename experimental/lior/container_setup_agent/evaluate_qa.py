#!/usr/bin/env python3
"""
Evaluate question-answer pairs against knowledge base using perplexity scores and keyword recall.

This script evaluates question-answer pairs in two ways:
1. Perplexity: Calculates the average perplexity score for answers given the questions
   and the knowledge base content using a language model from the transformers library.
   Lower perplexity scores indicate better alignment between the answers and the knowledge base.
2. Keyword Recall: Checks if the required keyword appears in each answer and calculates
   keyword recall metrics. Higher recall indicates better answer accuracy.

The combined evaluation provides a comprehensive assessment of answer quality.
"""

import json
import concurrent.futures
from pathlib import Path
from typing import List, Dict, Any
import argparse
import re

from tqdm import tqdm
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.box import ROUNDED
from research.llm_apis.llm_client import get_client, TextPrompt, TextResult

# Initialize rich console
console = Console(highlight=False)


def load_knowledge_base(knowledge_file: Path) -> str:
    """Load knowledge from a single file.

    Args:
        knowledge_file: Path to the knowledge file

    Returns:
        Content of the knowledge file
    """
    if not knowledge_file.exists():
        console.print(
            f"[yellow]Warning: Knowledge file {knowledge_file} does not exist[/]"
        )
        return ""

    return knowledge_file.read_text()


def load_qa_pairs(qa_file: Path) -> List[Dict[str, Any]]:
    """Load question-answer pairs with keywords from a JSON file.

    Args:
        qa_file: Path to the JSON file containing question-answer pairs with keywords

    Returns:
        List of question-answer pairs with keywords
    """
    with open(qa_file, "r") as f:
        data = json.load(f)

    # Handle both formats: direct list or nested under "questions" key
    if isinstance(data, list):
        return data
    elif isinstance(data, dict) and "questions" in data:
        return data["questions"]
    else:
        raise ValueError(f"Unexpected format in {qa_file}")


# Using get_client directly instead of a wrapper function


def generate_answer_with_claude(client, question: str, knowledge_base: str) -> str:
    """Generate an answer to a question using Claude Sonnet based on the knowledge base.

    Args:
        client: Claude client instance from get_client
        question: The question to answer
        knowledge_base: The knowledge base content

    Returns:
        Generated answer from Claude

    Raises:
        AssertionError: If the response format is not as expected
        Exception: If there's an error generating the answer
    """
    prompt = f"""
You are an expert on our codebase. Answer the following question based ONLY on the provided knowledge base content.
Keep your answer concise and to the point - ideally one sentence.

Knowledge Base:
{knowledge_base}

Question: {question}

Answer:
"""
    # Use the client to generate a response
    response, _ = client.generate(
        messages=[[TextPrompt(text=prompt)]],
        temperature=0.0,
        max_tokens=1024,
    )

    # Assert that the response is a list (the expected type from Claude API)
    assert isinstance(
        response, list
    ), f"Expected list response from Claude, got {type(response)}"
    assert len(response) > 0, "Expected non-empty list response from Claude"

    # Get the first item in the list
    first_item = response[0]

    # Assert that the first item is a TextResult or a string (the expected content types)
    if isinstance(first_item, TextResult):
        return first_item.text.strip()
    elif isinstance(first_item, str):
        return first_item.strip()
    else:
        raise AssertionError(
            f"Expected TextResult or string in Claude response, got {type(first_item)}"
        )


def check_keyword_in_answer(answer: str, keyword: str) -> bool:
    """Check if a keyword appears in an answer, surrounded by spaces or punctuation.

    Args:
        answer: The answer text
        keyword: The keyword to check for

    Returns:
        True if the keyword is found in the answer, False otherwise
    """

    if not answer or not keyword:
        return False

    keyword_escaped = re.escape(keyword)
    boundary_chars = r'\s.,;:!?()[\]{}\'"/\-'
    pattern = (
        r"(^|["
        + boundary_chars
        + r"])"
        + keyword_escaped
        + r"($|["
        + boundary_chars
        + r"])"
    )
    return bool(re.search(pattern, answer, re.IGNORECASE))


def process_qa_pair(args):
    """Process a single QA pair in parallel.

    Args:
        args: Tuple containing (pair, knowledge_base, claude_client)

    Returns:
        Dictionary with evaluation results for this pair
    """
    pair, knowledge_base, claude_client = args

    question = pair["question"]
    original_answer = pair["answer"]
    category = pair.get("category", "unknown")
    keyword = pair.get("keyword", "")

    # Generate a new answer using Claude (silently)
    try:
        claude_answer = generate_answer_with_claude(
            claude_client, question, knowledge_base
        )

        # Check if keyword appears in the Claude-generated answer
        keyword_found = check_keyword_in_answer(claude_answer, keyword)
    except Exception as e:
        # Only print errors to avoid cluttering the output
        console.print(
            f"[bold red]Error processing question '{question}':[/] {e}", soft_wrap=True
        )
        # Re-raise the exception to be handled by the caller
        raise

    return {
        "question": question,
        "original_answer": original_answer,
        "claude_answer": claude_answer,
        "category": category,
        "keyword": keyword,
        "keyword_found": keyword_found,
    }


def evaluate_qa_pairs(
    qa_pairs: List[Dict[str, Any]], knowledge_base: str, batch_size: int = 10
) -> Dict[str, Any]:
    """Evaluate question-answer pairs against the knowledge base using keyword recall.

    Uses Claude Sonnet to generate new answers based on the knowledge base and questions,
    then calculates keyword recall on these generated answers.

    Args:
        qa_pairs: List of question-answer pairs with keywords
        knowledge_base: Combined content of all knowledge files
        batch_size: Number of QA pairs to process in parallel

    Returns:
        Dictionary with evaluation results including keyword recall
    """
    results = {
        "overall": {
            "total_pairs": len(qa_pairs),
            "keyword_recall": 0.0,
            "keywords_found": 0,
            "keywords_total": 0,
        },
        "by_category": {},
        "pairs": [],
    }

    # Initialize Claude client - throw exception if it fails
    claude_client = get_client(
        "anthropic-direct", model_name="claude-3-5-sonnet-20241022", max_retries=3
    )

    # Process QA pairs in parallel
    pair_results = []

    console.print(
        "[blue]Generating answers with Claude Sonnet and evaluating in parallel...[/]"
    )

    # Prepare arguments for parallel processing
    process_args = [(pair, knowledge_base, claude_client) for pair in qa_pairs]

    # Process in parallel with progress bar and controlled concurrency
    with concurrent.futures.ThreadPoolExecutor(max_workers=batch_size) as executor:
        # Submit all tasks and get future objects
        futures = [executor.submit(process_qa_pair, arg) for arg in process_args]

        # Process results as they complete with tqdm for progress tracking
        for future in tqdm(
            concurrent.futures.as_completed(futures),
            total=len(futures),
            desc="Evaluating QA pairs",
        ):
            try:
                pair_result = future.result()
                pair_results.append(pair_result)
            except Exception as e:
                console.print(f"[bold red]Error processing QA pair:[/] {e}")

    # Process results
    total_keywords_found = 0
    total_keywords_total = 0

    for pair_result in pair_results:
        category = pair_result["category"]
        keyword = pair_result["keyword"]
        keyword_found = pair_result["keyword_found"]

        # Add to results
        results["pairs"].append(pair_result)

        # Update category stats
        if category not in results["by_category"]:
            results["by_category"][category] = {
                "count": 0,
                "keywords_found": 0,
                "keywords_total": 0,
                "keyword_recall": 0.0,
            }

        results["by_category"][category]["count"] += 1

        # Update keyword stats if a keyword was provided
        if keyword:
            results["by_category"][category]["keywords_total"] += 1
            total_keywords_total += 1

            if keyword_found:
                results["by_category"][category]["keywords_found"] += 1
                total_keywords_found += 1

    # Calculate recall
    results["overall"]["keywords_found"] = total_keywords_found
    results["overall"]["keywords_total"] = total_keywords_total
    results["overall"]["keyword_recall"] = (
        total_keywords_found / total_keywords_total if total_keywords_total > 0 else 0.0
    )

    for category, stats in results["by_category"].items():
        stats["keyword_recall"] = (
            stats["keywords_found"] / stats["keywords_total"]
            if stats["keywords_total"] > 0
            else 0.0
        )

    return results


def main():
    """Main function to evaluate question-answer pairs."""
    parser = argparse.ArgumentParser(
        description="Evaluate QA pairs against knowledge base using keyword recall"
    )
    parser.add_argument(
        "--knowledge-file",
        type=Path,
        default=Path("knowledge.md"),
        help="File containing the knowledge base",
    )
    parser.add_argument(
        "--qa-file",
        type=Path,
        default=Path("codebase_qa_pairs.json"),
        help="JSON file containing question-answer pairs",
    )
    parser.add_argument(
        "--output-file",
        type=Path,
        default=Path("evaluation_results.json"),
        help="Output file for evaluation results",
    )

    parser.add_argument(
        "--batch-size",
        type=int,
        default=10,
        help="Number of QA pairs to process in parallel",
    )
    parser.add_argument(
        "--show-examples",
        type=int,
        default=5,
        help="Number of wrong answer examples to show",
    )

    args = parser.parse_args()

    # Load knowledge base
    console.print(f"[blue]Loading knowledge base from {args.knowledge_file}...[/]")
    knowledge_base = load_knowledge_base(args.knowledge_file)

    # Load QA pairs
    console.print(f"[blue]Loading QA pairs from {args.qa_file}...[/]")
    qa_pairs = load_qa_pairs(args.qa_file)

    # Evaluate QA pairs
    console.print("[blue]Evaluating QA pairs against knowledge base...[/]")
    results = evaluate_qa_pairs(qa_pairs, knowledge_base, batch_size=args.batch_size)

    # Save results
    with open(args.output_file, "w") as f:
        json.dump(results, f, indent=2)

    # Print summary in a nice format using rich
    console.print("\n")
    console.rule("[bold green]📊 EVALUATION RESULTS SUMMARY", style="green")

    # Overall statistics
    total_pairs = results["overall"]["total_pairs"]
    keywords_found = results["overall"]["keywords_found"]
    keywords_total = results["overall"]["keywords_total"]
    keyword_recall = results["overall"]["keyword_recall"]

    # Create a panel for overall statistics
    recall_style = (
        "green"
        if keyword_recall >= 0.8
        else "yellow"
        if keyword_recall >= 0.5
        else "red"
    )

    stats_text = Text()
    stats_text.append("\n")
    stats_text.append(f"Total QA pairs evaluated: {total_pairs}\n", style="blue")
    stats_text.append(
        f"Keywords found: {keywords_found}/{keywords_total}\n", style="blue"
    )
    stats_text.append(
        f"Overall keyword recall: {keyword_recall:.2%}\n", style=f"bold {recall_style}"
    )

    overall_panel = Panel(
        stats_text,
        title="[bold blue]OVERALL STATISTICS",
        border_style="blue",
        box=ROUNDED,
        expand=False,
        padding=(1, 2),
    )

    console.print(overall_panel)

    # Category results in a table format
    category_table = Table(
        title="[bold blue]RESULTS BY CATEGORY",
        box=ROUNDED,
        border_style="blue",
        header_style="bold yellow",
        expand=False,
    )

    # Add columns
    category_table.add_column("Category", style="dim", width=30)
    category_table.add_column("Found", justify="center")
    category_table.add_column("Total", justify="center")
    category_table.add_column("Recall", justify="center")

    # Sort categories by recall rate (descending)
    for category, stats in sorted(
        results["by_category"].items(),
        key=lambda x: x[1]["keyword_recall"],
        reverse=True,
    ):
        cat_found = stats["keywords_found"]
        cat_total = stats["keywords_total"]
        cat_recall = stats["keyword_recall"]

        # Truncate long category names
        display_category = category[:28] + ".." if len(category) > 30 else category

        # Color the recall percentage based on its value
        recall_style = (
            "green" if cat_recall >= 0.8 else "yellow" if cat_recall >= 0.5 else "red"
        )

        # Format the recall text
        recall_text = f"[bold {recall_style}]{cat_recall:.2%}[/]"

        # Add row to table
        category_table.add_row(
            display_category, str(cat_found), str(cat_total), recall_text
        )

    console.print(category_table)

    # Print examples of answers missing their required keywords
    missing_keywords = [
        pair
        for pair in results["pairs"]
        if pair.get("keyword") and not pair.get("keyword_found")
    ]

    if missing_keywords:
        # Randomly sample from all failed examples
        import random

        num_examples = min(args.show_examples, len(missing_keywords))
        sampled_examples = random.sample(missing_keywords, num_examples)

        console.print(
            f"\n[bold red]RANDOMLY SAMPLED EXAMPLES OF ANSWERS MISSING KEYWORDS ({len(missing_keywords)} total)[/]"
        )

        # Group by category for better understanding of distribution
        by_category = {}
        for pair in sampled_examples:
            category = pair.get("category", "unknown")
            if category not in by_category:
                by_category[category] = []
            by_category[category].append(pair)

        # Print category distribution of sampled examples
        category_counts = {cat: len(pairs) for cat, pairs in by_category.items()}
        console.print(f"[dim]Category distribution in sample: {category_counts}[/]")

        for i, pair in enumerate(sampled_examples):
            question = pair["question"]
            answer = pair.get("claude_answer", "No answer available")
            keyword = pair["keyword"]
            category = pair.get("category", "unknown")

            # Create a panel for each example
            example_text = Text()
            example_text.append("\n")
            example_text.append("Question:\n", style="bold blue")
            example_text.append(f"{question}\n\n")
            example_text.append("Claude Answer:\n", style="bold blue")
            example_text.append(f"{answer}\n\n")
            example_text.append("Expected Keyword: ", style="bold red")
            example_text.append(keyword, style="bold red")
            example_text.append(f"\n\nCategory: {category}", style="dim")

            example_panel = Panel(
                example_text,
                title=f"[bold yellow]Example {i+1}/{num_examples}",
                border_style="yellow",
                box=ROUNDED,
                expand=False,
                padding=(0, 1),
            )

            console.print(example_panel)

        if len(missing_keywords) > num_examples:
            console.print(
                f"[yellow]... and {len(missing_keywords) - num_examples} more examples not shown[/]"
            )

    console.print(f"\n[bold green]✅ Detailed results saved to {args.output_file}[/]")


if __name__ == "__main__":
    main()
