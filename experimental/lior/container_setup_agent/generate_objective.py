"""Generate test command objectives for a specific language using the knowledge document."""

import os
import logging
from pathlib import Path
from typing import Optional
from .knowledge_generator import ExpertAgent

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def generate_test_command(
    knowledge_path: Optional[Path] = None,
    language: str = "python",
    verbose: bool = True,
) -> str:
    """Generate a test command for the specified language.

    Args:
        knowledge_path: Path to the knowledge.md file. If None, uses default path.
        language: Programming language to find test commands for.
        verbose: Whether to enable verbose mode in the ExpertAgent.

    Returns:
        A working test command for the specified language.

    Raises:
        FileNotFoundError: If the knowledge file cannot be found.
        Exception: If there's an error generating the test command.
    """
    try:
        # Use default path if none provided
        if knowledge_path is None:
            knowledge_path = Path("/home/<USER>/augment/knowledge.md")

        # Ensure the knowledge file exists
        if not knowledge_path.exists():
            raise FileNotFoundError(f"Knowledge file not found at {knowledge_path}")

        # Read the knowledge file
        logger.info(f"Reading knowledge file from {knowledge_path}")
        with open(knowledge_path) as f:
            knowledge = f.read()

        prompt = f"""
        Below is a complete documentation of the codebase.
        {knowledge}

        Task: Find an examplary test command for {language} code in this project.

        PROCESS:
        1. First, carefully analyze the documentation to identify:
           - Testing frameworks used for {language} in this project
           - Build tools or test runners specific to {language}
           - Project conventions for running tests for {language}

        2. Search the codebase to find:
           - Test files and directories for {language} code
           - Configuration files for the testing frameworks
           - Examples of test commands in scripts or documentation

        3. Create a test command that runs a suite of tests for {language} code.

        4. Validate that the command produces no errors or test failures with bash_tool, repeating steps 2-4 until you have a validated test command.

        5. After successfully validating that the command runs a suite of tests for {language} code and produces no errors or failing tests, use the complete tool to submit your final answer.

        Your answer must only include the final working test command including cd commands if needed.
        """

        # Initialize the expert agent and get the answer
        logger.info(f"Generating test command for {language}")
        expert = ExpertAgent(verbose=verbose)
        command = expert.answer_question(prompt)

        logger.info(f"Generated command: {command}")
        return command

    except FileNotFoundError as e:
        logger.error(f"File error: {e}")
        raise
    except Exception as e:
        logger.error(f"Error generating test command: {e}")
        raise


def main():
    """Main entry point for the script."""
    try:
        command = generate_test_command()
        print(f"Running: {command}")
        exit_code = os.system(command)

        if exit_code != 0:
            logger.warning(f"Command exited with non-zero status: {exit_code}")

        return exit_code
    except Exception as e:
        logger.error(f"Failed to execute test command: {e}")
        return 1


if __name__ == "__main__":
    main()
