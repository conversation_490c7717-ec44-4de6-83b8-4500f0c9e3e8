#!/usr/bin/env python3
"""
Generate challenging questions, answers, and keywords about the codebase.

This script uses the ExpertAgent to generate a list of challenging questions,
answers, and unique keywords about the codebase based on the knowledge stored in <PERSON>'s knowledge base.
Each question has a unique keyword that must appear in the answer to be considered correct.
It runs each category in parallel to speed up the process.
"""

import json
import argparse
import concurrent.futures
from pathlib import Path
from typing import List, Dict, Any, Tuple

from tqdm import tqdm
from termcolor import colored

from experimental.lior.container_setup_agent.knowledge_generator import ExpertAgent


def generate_category_questions(
    expert: ExpertAgent, category: str, questions_per_category: int = 10
) -> List[Dict[str, str]]:
    """Generate questions, answers, and keywords for a specific category.

    Args:
        expert: ExpertAgent instance
        category: The category name
        questions_per_category: Number of questions to generate per category

    Returns:
        List of dictionaries containing questions, answers, and keywords
    """
    import re

    # Create a prompt for this category with XML formatting
    category_prompt = f"""
    I need you to generate {questions_per_category} clear, focused questions about the "{category}" category in our codebase.

    First, explore the knowledge base directory at /mnt/efs/augment/user/guy/bob/knowledge/ to find relevant information about this category.

    For each question:
    1. Make it assess SPECIFIC knowledge about OUR codebase's tools and practices, NOT general best practices
    2. Questions should test knowledge unique to our implementation, configuration, and conventions
    3. Provide a SHORT, CLEAR, ONE-SENTENCE answer based on the knowledge you find
    4. Provide a unique keyword that must appear in the answer to be considered correct

    Format your response using XML tags as follows:

    <qa_item>
      <question>What naming convention is used for test files in our codebase?</question>
      <answer>In our codebase, test files follow the 'test_filename.py' naming pattern rather than 'filename_test.py'.</answer>
      <keyword>test_filename.py</keyword>
    </qa_item>

    Provide exactly {questions_per_category} qa_items.

    IMPORTANT:
    - Questions must assess SPECIFIC knowledge of OUR codebase, NOT general best practices
    - Focus on our unique implementation details, not industry standards
    - Keep answers short and to the point - ONE SENTENCE ONLY
    - The keyword should be a technical term or specific concept that is crucial to the answer
    - The keyword MUST appear in the answer text
    - Use proper XML formatting with opening and closing tags
    - AVOID questions about specific version numbers (like Python 3.11, Node.js 20.13.1, etc.) as these change frequently
    - Focus instead on architectural patterns, file locations, naming conventions, and workflow processes that are more stable
    """

    print(colored(f"Generating questions for category: {category}...", "blue"))

    # Ask the expert to generate questions and answers
    try:
        response = expert.answer_question(category_prompt)

        # Parse the response using regex to extract questions, answers, and keywords
        qa_pairs = []

        # Define regex patterns for XML tags
        pattern = re.compile(
            r"<qa_item>\s*"
            r"<question>(.*?)</question>\s*"
            r"<answer>(.*?)</answer>\s*"
            r"<keyword>(.*?)</keyword>\s*"
            r"</qa_item>",
            re.DOTALL,
        )

        # Find all matches
        matches = pattern.findall(response)

        # Process each match
        for question, answer, keyword in matches:
            question = question.strip()
            answer = answer.strip()
            keyword = keyword.strip()

            # Ensure the keyword is found in the answer
            if keyword in answer:
                qa_pairs.append(
                    {
                        "question": question,
                        "answer": answer,
                        "keyword": keyword,
                        "category": category,
                    }
                )
                # Print validation success for visibility

        # Debug output if needed
        if len(qa_pairs) < questions_per_category:
            print(
                colored(
                    f"Warning: Only generated {len(qa_pairs)} valid QA pairs for {category}, expected {questions_per_category}",
                    "yellow",
                )
            )

    except Exception as e:
        print(colored(f"Error generating questions for {category}: {e}", "red"))
        return []

    return qa_pairs[:questions_per_category]


def process_category(args: Tuple[str, int]) -> Tuple[str, List[Dict[str, str]]]:
    """Process a single category in parallel.

    Args:
        args: Tuple containing (category, questions_per_category)

    Returns:
        Tuple of (category, list of QA pairs)
    """
    category, questions_per_category = args

    # Create a new ExpertAgent instance for this thread
    expert = ExpertAgent(verbose=False)  # Set verbose to False to avoid output clutter

    # Generate questions for this category
    qa_pairs = generate_category_questions(expert, category, questions_per_category)

    return category, qa_pairs


def generate_questions_and_answers(
    questions_per_category: int = 10, max_workers: int = 4
) -> List[Dict[str, str]]:
    """Generate questions, answers, and keywords about the codebase for multiple categories in parallel.

    Args:
        questions_per_category: Number of questions to generate per category
        max_workers: Maximum number of parallel workers (default: 4)

    Returns:
        List of dictionaries containing questions, answers, and keywords
    """
    # Define categories focused on tools and codebase structure
    categories = [
        "Source Control",
        "Testing Frameworks",
        "Linters and Code Quality",
        "Build Systems",
        "Logging and Monitoring",
        "Cloud Infrastructure",
        "Codebase Structure",
        "Dependency Management",
        "CI/CD Pipelines",
        "Development Environment Setup",
    ]

    # Prepare arguments for parallel processing
    category_args = [(category, questions_per_category) for category in categories]

    # Generate questions for each category in parallel
    all_qa_pairs = []

    print(colored(f"Processing {len(categories)} categories in parallel...", "blue"))

    with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_category = {
            executor.submit(process_category, args): args[0] for args in category_args
        }

        # Process results as they complete
        for future in tqdm(
            concurrent.futures.as_completed(future_to_category),
            total=len(categories),
            desc="Categories",
        ):
            category = future_to_category[future]
            try:
                _, category_qa_pairs = future.result()
                all_qa_pairs.extend(category_qa_pairs)
                print(
                    colored(
                        f"✓ Completed category: {category} with {len(category_qa_pairs)} questions",
                        "green",
                    )
                )
            except Exception as e:
                print(colored(f"✗ Error processing category {category}: {e}", "red"))

    return all_qa_pairs


def main():
    """Main function to generate questions, answers, and keywords."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Generate codebase QA pairs")
    parser.add_argument(
        "--questions", type=int, default=10, help="Number of questions per category"
    )
    parser.add_argument(
        "--workers", type=int, default=10, help="Maximum number of parallel workers"
    )
    parser.add_argument(
        "--dry-run", action="store_true", help="Run without saving output"
    )
    parser.add_argument(
        "--output", type=str, default="codebase_qa_pairs.json", help="Output file path"
    )
    args = parser.parse_args()

    # Initialize parallel processing
    print(colored("Initializing parallel processing...", "blue"))

    # Set parameters from command line arguments
    questions_per_category = args.questions
    max_workers = args.workers

    # Generate questions, answers, and keywords in parallel
    print(
        colored(
            "Generating questions, answers, and keywords about the codebase...", "blue"
        )
    )
    qa_pairs = generate_questions_and_answers(
        questions_per_category=questions_per_category, max_workers=max_workers
    )

    # Prepare structured output with metadata
    output_data = {
        "metadata": {
            "total_questions": len(qa_pairs),
            "categories": list(set(pair["category"] for pair in qa_pairs)),
            "questions_per_category": questions_per_category,
            "generation_date": __import__("datetime").datetime.now().isoformat(),
            "knowledge_base_path": "/mnt/efs/augment/user/guy/bob/knowledge",
        },
        "questions": qa_pairs,
    }

    # Save to file if not a dry run
    output_file = Path(args.output)
    if not args.dry_run:
        with open(output_file, "w") as f:
            json.dump(output_data, f, indent=2, sort_keys=True)
        print(colored(f"Saved to {output_file}", "green"))
    else:
        print(colored("Dry run - output not saved", "yellow"))

    # Print summary
    print(
        colored(
            f"\nGenerated {len(qa_pairs)} question-answer-keyword triplets across 10 categories",
            "green",
        )
    )

    # Print category statistics
    categories = {}
    for pair in qa_pairs:
        category = pair["category"]
        if category not in categories:
            categories[category] = 0
        categories[category] += 1

    print(colored("\nQuestions per category:", "blue"))
    for category, count in categories.items():
        print(colored(f"- {category}: {count} questions", "cyan"))

    # Print a few examples
    print(colored("\nExample questions:", "blue"))
    shown_categories = set()
    example_count = 0

    # Try to show one example from each category
    for pair in qa_pairs:
        if pair["category"] not in shown_categories and example_count < 5:
            shown_categories.add(pair["category"])
            example_count += 1
            print(colored(f"Category: {pair['category']}", "magenta"))
            print(colored(f"Q: {pair['question']}", "cyan"))
            print(colored(f"A: {pair['answer'][:150]}...", "yellow"))
            print(colored(f"K: {pair['keyword']}", "green"))
            print()


if __name__ == "__main__":
    main()
