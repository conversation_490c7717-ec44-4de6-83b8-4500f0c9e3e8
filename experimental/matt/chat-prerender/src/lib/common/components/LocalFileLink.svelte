<script lang="ts">
    import type { IRange } from "monaco-editor";
    import type { FileDetails } from "../../../../../../../../clients/vscode/src/webview-providers/webview-messages";

    export let repoRoot: string;
    export let path: string;
    export let range: IRange | undefined = undefined;
    export let onClick: (details: FileDetails) => void;

    // Styling props
    export let nowrap: boolean = false;

    function rangeToString(range: IRange) {
        return `${range.startLineNumber}-${range.endLineNumber}`;
    }

    function _onClick() {
        let r = undefined;
        if (range) {
            r = {
                start: range.startLineNumber,
                stop: range.endLineNumber,
            };
        }
        onClick({
            repoRoot,
            pathName: path,
            range: r,
        });
    }
</script>

<button class="c-local-file-link" class:c-local-file-link--nowrap={nowrap} on:click={_onClick}>
    <span class="c-local-file-link__path">&lrm;{path}</span>
    {#if range !== undefined}
        <span class="c-local-file-link__range">{rangeToString(range)}</span>
    {/if}
</button>

<style>
    .c-local-file-link {
        width: 100%;

        display: flex;
        flex-direction: row;
        justify-content: space-between;
        gap: var(--p-2);

        /* Remove button styles and make it look like an anchor */
        padding: 0;
        background: none;
        border: none;
        color: var(--vscode-textLink-foreground);
        font-size: inherit;
        line-height: inherit;
        text-decoration: underline;
        cursor: pointer;
    }

    .c-local-file-link:hover {
        color: var(--vscode-textLink-activeForeground);
    }

    .c-local-file-link.c-local-file-link--nowrap .c-local-file-link__path,
    .c-local-file-link.c-local-file-link--nowrap .c-local-file-link__range {
        white-space: nowrap;
        word-break: unset;
    }

    .c-local-file-link__path {
        white-space: pre-wrap;
        word-break: break-all;

        /* Make the path ellipsis on the left side (See &lrm; which fixes */
        /* paths that have punctuation in them i.e. 0002-example/file.ts) */
        overflow: hidden;
        text-overflow: ellipsis;
        direction: rtl;
        text-align: left;
    }

    .c-local-file-link__range {
        white-space: pre-wrap;
        word-break: break-all;

        overflow: hidden;
        text-overflow: ellipsis;
        text-align: right;

        /* These follow flex values allow the range to show ellipsis and */
        /* become hidden before the path begins to shrink with ellipsis */

        /* Make the selection range have zero width */
        flex-basis: 0;
        /* Allow the selection to grow to fill available space */
        flex-grow: 1;
    }
</style>
