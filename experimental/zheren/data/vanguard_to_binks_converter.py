#!/usr/bin/env python3
"""Convert Vanguard user chat data to Binks dataset format for chatanol training.

This script processes real user chat interactions from Vanguard tenants and
transforms them into the Binks dataset format suitable for training the
chatanol retriever model using the Ray-based training pipeline.

Example usage:
    python research/data/vanguard_to_binks_converter.py \
        --date-from 2025-01-01 \
        --date-to 2025-02-01 \
        --output-path /path/to/output.jsonl \
        --limit 1000
"""

import argparse
import base64
import json
import logging
import uuid
from collections import defaultdict
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Optional, Any, Iterable, Sequence

import google.auth
from google.cloud import bigquery, storage
from tqdm import tqdm

# Import official Binks schemas
from experimental.tongfei.data.binks_schemas import (
    Repository,
    File,
    DocumentWithQuestionsV2,
)

# Import dataset infrastructure
from base.datasets.gcs_blob_cache import GCSBlobCache, GCSCheckpointCache
from base.datasets.pipeline import Pipeline
from base.datasets.itertools import batched
from base.datasets.tenants import DatasetTenant, get_tenant
from base.datasets.gcp_creds import get_gcp_creds

# Import Vanguard utilities
from experimental.zheren.data.vanguard_data_utils import (
    get_vanguard_tenants,
    query_chat_requests,
    get_chat_request_from_gcs,
    VanguardChatRequest,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@dataclass
class ProcessingConfig:
    """Configuration for the processing pipeline."""

    batch_size: int = 128
    queue_size: int = 10
    blob_cache_size_bytes: int = 2**30  # 1GB
    blob_cache_num_threads: int = 32
    checkpoint_cache_size_bytes: int = 2**29  # 512MB
    checkpoint_cache_num_threads: int = 16


@dataclass
class WorkspaceGroup:
    """Groups chat requests by workspace."""

    workspace_key: str
    requests: list[VanguardChatRequest] = field(default_factory=list)
    blob_ids: set[str] = field(default_factory=set)
    file_paths: dict[str, list[str]] = field(default_factory=lambda: defaultdict(list))


class VanguardToBinksConverter:
    """Converter that processes Vanguard data into Binks format."""

    def __init__(
        self,
        tenant: DatasetTenant,
        config: ProcessingConfig,
        credentials: Optional[Any] = None,
    ):
        self.tenant = tenant
        self.config = config
        self.credentials = credentials or google.auth.default()[0]

        # Initialize caches
        storage_client = storage.Client(
            project=tenant.project_id, credentials=self.credentials
        )

        blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
        self.blob_cache = GCSBlobCache(
            blob_bucket,
            tenant.blob_bucket_prefix,
            config.blob_cache_size_bytes,
            num_threads=config.blob_cache_num_threads,
        )

        checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
        self.checkpoint_cache = GCSCheckpointCache(
            checkpoint_bucket,
            tenant.checkpoint_bucket_prefix,
            config.checkpoint_cache_size_bytes,
            num_threads=config.checkpoint_cache_num_threads,
        )

    def process_requests(
        self,
        date_from: str,
        date_to: str,
        limit: Optional[int] = None,
    ) -> Iterable[Repository]:
        """Process chat requests into Repository objects."""

        # Query for request IDs
        request_rows = query_chat_requests(
            self.tenant, date_from, date_to, limit, self.credentials
        )

        if not request_rows:
            logger.warning(f"No requests found for tenant {self.tenant.name}")
            return []

        logger.info(f"Processing {len(request_rows)} requests from {self.tenant.name}")

        # Create pipeline for processing
        pipeline = (
            Pipeline.from_source(
                batched(request_rows, self.config.batch_size),
                name=f"requests_{self.tenant.name}",
            )
            .and_then(self._fetch_chat_requests, name="fetch_requests")
            .and_then(self._group_by_workspace, name="group_workspaces")
            .and_then(self._create_repositories, name="create_repos")
            .run(max_queue_size=self.config.queue_size)
        )

        # Yield repositories as they're processed
        for repo_batch in pipeline:
            yield from repo_batch

    def _fetch_chat_requests(
        self, request_batch: Sequence[dict]
    ) -> list[VanguardChatRequest]:
        """Fetch chat request details from GCS."""
        chat_requests = []

        for row in request_batch:
            request = get_chat_request_from_gcs(
                self.tenant, row["tenant_id"], row["request_id"], self.credentials
            )
            if request and request.blob_names:
                chat_requests.append(request)

        return chat_requests

    def _transform_blob_id(self, base64_blob_id: str) -> str:
        """Transform base64-encoded blob ID to hex format for GCS lookup."""
        try:
            # Decode base64 to bytes
            blob_bytes = base64.b64decode(base64_blob_id)
            # Convert to hex string
            return blob_bytes.hex()
        except Exception as e:
            logger.warning(f"Failed to transform blob ID {base64_blob_id}: {e}")
            return base64_blob_id  # Return original if transformation fails

    def _group_by_workspace(
        self, requests: Sequence[VanguardChatRequest]
    ) -> list[WorkspaceGroup]:
        """Group requests by workspace based on blob paths."""

        # Collect all blob IDs and transform them
        all_blob_ids = set()
        blob_id_mapping = {}  # Maps original base64 ID to hex ID

        for request in requests:
            for blob_id in request.blob_names:
                hex_id = self._transform_blob_id(blob_id)
                blob_id_mapping[blob_id] = hex_id
                all_blob_ids.add(hex_id)

        # Fetch blob paths using hex IDs
        blob_paths = {}
        hex_blob_ids = list(all_blob_ids)
        blob_results = self.blob_cache.get(hex_blob_ids)

        for hex_id, result in zip(hex_blob_ids, blob_results):
            if result:
                blob_paths[hex_id] = str(result.path)

        # Group by workspace
        workspace_groups = {}

        for request in requests:
            # Find workspace key based on common path prefix
            request_paths = []
            for blob_id in request.blob_names:
                hex_id = blob_id_mapping.get(blob_id)
                if hex_id and hex_id in blob_paths:
                    request_paths.append(blob_paths[hex_id])

            if not request_paths:
                continue

            workspace_key = self._find_workspace_key(request_paths)

            # Create group if it doesn't exist
            if workspace_key not in workspace_groups:
                workspace_groups[workspace_key] = WorkspaceGroup(
                    workspace_key=workspace_key
                )

            group = workspace_groups[workspace_key]
            group.requests.append(request)
            group.blob_ids.update(request.blob_names)
            group.file_paths[request.request_id] = request_paths

        return list(workspace_groups.values())

    def _find_workspace_key(self, paths: list[str]) -> str:
        """Find common workspace key from paths."""
        if len(paths) == 1:
            return str(Path(paths[0]).parent)

        # Find common prefix
        common_parts = Path(paths[0]).parts
        for path in paths[1:]:
            path_parts = Path(path).parts
            common_parts = common_parts[: min(len(common_parts), len(path_parts))]
            for i, (a, b) in enumerate(zip(common_parts, path_parts)):
                if a != b:
                    common_parts = common_parts[:i]
                    break

        return str(Path(*common_parts)) if common_parts else "root"

    def _create_repositories(
        self, workspace_groups: Sequence[WorkspaceGroup]
    ) -> list[Repository]:
        """Create Repository objects from workspace groups."""
        repositories = []

        for group in workspace_groups:
            # Transform blob IDs for this workspace
            hex_blob_ids = []
            original_to_hex = {}
            for blob_id in group.blob_ids:
                hex_id = self._transform_blob_id(blob_id)
                hex_blob_ids.append(hex_id)
                original_to_hex[blob_id] = hex_id

            # Fetch blob contents for this workspace
            blob_contents = {}
            blob_results = self.blob_cache.get(hex_blob_ids)

            for hex_id, result in zip(hex_blob_ids, blob_results):
                if result:
                    # Find original blob ID for this hex ID
                    original_id = None
                    for orig, hx in original_to_hex.items():
                        if hx == hex_id:
                            original_id = orig
                            break

                    if original_id:
                        blob_contents[original_id] = {
                            "blob_id": hex_id,  # Store hex ID for reference
                            "path": str(result.path),
                            "content": result.content,
                            "size": len(result.content.encode("utf-8")),
                        }

            if not blob_contents:
                continue

            # Create repository
            repo = self._create_repository_from_group(group, blob_contents)
            if repo and repo.file_list and repo.documents_with_questions:
                repositories.append(repo)
            else:
                logger.debug(
                    f"Skipping repository for workspace {group.workspace_key}: "
                    f"repo={repo is not None}, "
                    f"files={len(repo.file_list) if repo else 0}, "
                    f"questions={len(repo.documents_with_questions) if repo else 0}"
                )

        return repositories

    def _create_repository_from_group(
        self, group: WorkspaceGroup, blob_contents: dict[str, dict]
    ) -> Optional[Repository]:
        """Create a Repository object from a workspace group."""

        repo_name = (
            f"vanguard_{self.tenant.name}_{group.workspace_key.replace('/', '_')}"
        )
        repo_uuid = str(uuid.uuid4())

        # Create File objects with minimal required fields
        file_list = []
        lang_counts = defaultdict(int)
        lang_sizes = defaultdict(int)
        total_size = 0

        for blob_id, blob_data in blob_contents.items():
            file_obj = self._create_file_object(blob_data, repo_name)
            file_list.append(file_obj)

            lang = file_obj.langpart
            lang_counts[lang] += 1
            lang_sizes[lang] += file_obj.size
            total_size += file_obj.size

        # Create DocumentWithQuestionsV2 objects
        documents_with_questions = []
        for request in group.requests:
            paths = group.file_paths.get(request.request_id, [])
            valid_paths = [
                p
                for p in paths
                if any(
                    blob_contents.get(bid, {}).get("path") == p
                    for bid in request.blob_names
                )
            ]

            if valid_paths:
                doc = DocumentWithQuestionsV2(
                    question=request.message,
                    paths=valid_paths,
                    answer="",  # To be filled by downstream processing
                )
                documents_with_questions.append(doc)
            else:
                logger.debug(
                    f"No valid paths for request {request.request_id}: "
                    f"paths={len(paths)}, blob_names={len(request.blob_names)}"
                )

        if not file_list or not documents_with_questions:
            logger.info(
                f"Skipping repository creation: "
                f"files={len(file_list)}, questions={len(documents_with_questions)}"
            )
            return None

        # Find max language stats
        if lang_counts:
            max_file_lang_name, max_file_count = max(
                lang_counts.items(), key=lambda x: x[1]
            )
        else:
            max_file_lang_name, max_file_count = "unknown", 0

        if lang_sizes:
            max_size_lang_name, max_size_total = max(
                lang_sizes.items(), key=lambda x: x[1]
            )
        else:
            max_size_lang_name, max_size_total = "unknown", 0

        return Repository(
            max_stars_repo_name=repo_name,
            max_file_lang={
                "file_count": max_file_count,
                "langpart": max_file_lang_name,
            },
            max_size_lang={
                "total_size": max_size_total,
                "langpart": max_size_lang_name,
            },
            total_size=total_size,
            file_list=file_list,
            documents_with_questions=documents_with_questions,
            repo_uuid=repo_uuid,
        )

    def _create_file_object(self, blob_data: dict, repo_name: str) -> File:
        """Create a File object from blob data with minimal required fields."""
        path = blob_data["path"]
        content = blob_data["content"]

        # Calculate file statistics
        lines = content.split("\n")
        line_lengths = [len(line) for line in lines if line]

        avg_line_length = sum(line_lengths) / len(line_lengths) if line_lengths else 0
        max_line_length = max(line_lengths) if line_lengths else 0

        # Calculate alphanum fraction
        alphanum_chars = sum(1 for c in content if c.isalnum())
        total_chars = len(content)
        alphanum_fraction = alphanum_chars / total_chars if total_chars > 0 else 0

        # Determine file extension and language
        ext = Path(path).suffix.lower() if Path(path).suffix else None
        lang = self._guess_language(path)

        # Create File object with all required fields
        # For Vanguard data, we use the same values for all repo variants
        return File(
            hexsha=blob_data["blob_id"],
            size=blob_data["size"],
            ext=ext,
            max_stars_repo_path=path,
            max_stars_repo_name=repo_name,
            max_stars_repo_licenses=[],  # No license info available
            max_stars_count=None,
            max_stars_repo_stars_event_min_datetime=None,
            max_stars_repo_stars_event_max_datetime=None,
            max_issues_repo_path=path,
            max_issues_repo_name=repo_name,
            max_issues_repo_licenses=[],
            max_issues_count=None,
            max_issues_repo_issues_event_min_datetime=None,
            max_issues_repo_issues_event_max_datetime=None,
            max_forks_repo_path=path,
            max_forks_repo_name=repo_name,
            max_forks_repo_licenses=[],
            max_forks_count=None,
            max_forks_repo_forks_event_min_datetime=None,
            max_forks_repo_forks_event_max_datetime=None,
            content=content,
            avg_line_length=avg_line_length,
            max_line_length=max_line_length,
            alphanum_fraction=alphanum_fraction,
            langpart=lang,
        )

    def _guess_language(self, path: str) -> str:
        """Guess programming language from file path."""
        ext_to_lang = {
            ".py": "python",
            ".js": "javascript",
            ".ts": "typescript",
            ".java": "java",
            ".cpp": "cpp",
            ".cc": "cpp",
            ".c": "c",
            ".h": "c",
            ".hpp": "cpp",
            ".cs": "csharp",
            ".go": "go",
            ".rs": "rust",
            ".rb": "ruby",
            ".php": "php",
            ".swift": "swift",
            ".kt": "kotlin",
            ".scala": "scala",
            ".r": "r",
            ".sh": "bash",
            ".sql": "sql",
            ".html": "html",
            ".css": "css",
            ".json": "json",
            ".yaml": "yaml",
            ".yml": "yaml",
            ".md": "markdown",
            ".mdx": "markdown",
            ".txt": "text",
            ".xml": "xml",
            ".jsx": "javascript",
            ".tsx": "typescript",
            ".vue": "javascript",
            ".svelte": "javascript",
            ".scss": "css",
            ".sass": "css",
            ".less": "css",
            ".toml": "toml",
            ".ini": "ini",
            ".cfg": "ini",
            ".conf": "ini",
            ".dockerfile": "dockerfile",
            ".makefile": "makefile",
            ".cmake": "cmake",
            ".gradle": "gradle",
            ".pom": "xml",
            ".ipynb": "jupyter",
            ".R": "r",
            ".m": "matlab",
            ".tex": "latex",
            ".rst": "rst",
            ".adoc": "asciidoc",
            ".pod": "perl",
            ".pl": "perl",
            ".pm": "perl",
            ".lua": "lua",
            ".dart": "dart",
            ".elm": "elm",
            ".clj": "clojure",
            ".cljs": "clojure",
            ".ex": "elixir",
            ".exs": "elixir",
            ".erl": "erlang",
            ".hrl": "erlang",
            ".fs": "fsharp",
            ".fsx": "fsharp",
            ".ml": "ocaml",
            ".mli": "ocaml",
            ".pas": "pascal",
            ".pp": "pascal",
            ".nim": "nim",
            ".nims": "nim",
            ".zig": "zig",
            ".v": "verilog",
            ".vhd": "vhdl",
            ".vhdl": "vhdl",
            ".proto": "protobuf",
            ".thrift": "thrift",
            ".graphql": "graphql",
            ".gql": "graphql",
        }

        ext = Path(path).suffix.lower()
        return ext_to_lang.get(ext, "unknown")


def main():
    """Main function to orchestrate the conversion process."""
    parser = argparse.ArgumentParser(
        description="Convert Vanguard user chat data to Binks dataset format"
    )
    parser.add_argument(
        "--date-from",
        type=str,
        required=True,
        help="Start date for data collection (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--date-to",
        type=str,
        required=True,
        help="End date for data collection (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--output-path", type=str, required=True, help="Output path for the JSONL file"
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Limit number of requests per tenant (for testing)",
    )
    parser.add_argument(
        "--tenants",
        type=str,
        default=None,
        help="Comma-separated list of specific tenants to process",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=128,
        help="Batch size for processing (default: 128)",
    )
    parser.add_argument(
        "--queue-size",
        type=int,
        default=10,
        help="Queue size for pipeline (default: 10)",
    )
    parser.add_argument(
        "--blob-cache-gb",
        type=float,
        default=1.0,
        help="Blob cache size in GB (default: 1.0)",
    )
    parser.add_argument(
        "--service-account-file",
        type=str,
        default=None,
        help="Path to service account JSON file for GCP authentication",
    )

    args = parser.parse_args()

    # Validate dates
    try:
        datetime.strptime(args.date_from, "%Y-%m-%d")
        datetime.strptime(args.date_to, "%Y-%m-%d")
    except ValueError:
        logger.error("Invalid date format. Use YYYY-MM-DD")
        return 1

    # Set up credentials
    credentials, _ = get_gcp_creds(args.service_account_file)

    # Get tenants to process
    if args.tenants:
        tenant_names = args.tenants.split(",")
        tenants = []
        for name in tenant_names:
            try:
                tenants.append(get_tenant(name.strip()))
            except ValueError:
                logger.error(f"Unknown tenant: {name}")
                return 1
    else:
        tenants = get_vanguard_tenants()

    logger.info(f"Processing {len(tenants)} tenants")

    # Create processing config
    config = ProcessingConfig(
        batch_size=args.batch_size,
        queue_size=args.queue_size,
        blob_cache_size_bytes=int(args.blob_cache_gb * 2**30),
    )

    # Process each tenant
    output_path = Path(args.output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    total_repos = 0
    total_files = 0
    total_questions = 0

    with open(output_path, "w") as f:
        for tenant in tenants:
            try:
                converter = VanguardToBinksConverter(tenant, config, credentials)

                for repo in converter.process_requests(
                    args.date_from, args.date_to, args.limit
                ):
                    # Write repository to output using official to_json method
                    f.write(repo.to_json() + "\n")

                    # Update statistics
                    total_repos += 1
                    total_files += len(repo.file_list)
                    total_questions += len(repo.documents_with_questions)

                    if total_repos % 100 == 0:
                        logger.info(f"Processed {total_repos} repositories...")

            except Exception as e:
                logger.error(f"Error processing tenant {tenant.name}: {e}")
                continue

    logger.info("Conversion complete!")
    logger.info(f"  Total repositories: {total_repos}")
    logger.info(f"  Total files: {total_files}")
    logger.info(f"  Total questions: {total_questions}")
    logger.info(f"Output written to {output_path}")

    return 0


if __name__ == "__main__":
    exit(main())
