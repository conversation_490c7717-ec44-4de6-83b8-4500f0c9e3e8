#!/usr/bin/env python3
"""Modular Ray-based Vanguard to Binks conversion pipeline.

This script implements a fine-grained, modular pipeline that addresses
scalability bottlenecks through parallel BigQuery querying and separated
processing stages.
"""

import argparse
import json
import logging
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Optional
import shutil

from base.datasets.tenants import DATASET_TENANTS
from base.datasets.gcp_creds import get_gcp_creds
from research.data.ray.ray_utils import Ray<PERSON>unner

# Import modular pipeline components
from experimental.zheren.data.vanguard_to_binks_modular_data_structures import (
    PipelineConfig,
)
from experimental.zheren.data.vanguard_to_binks_bigquery_actors import (
    TenantDateRangeSplitActor,
    BigQueryRequestFetcherActor,
)
from experimental.zheren.data.vanguard_to_binks_blob_fetcher_actor import (
    BlobContentFetcherActor,
)
from experimental.zheren.data.vanguard_to_binks_file_processing_actors import (
    FileProcessorActor,
    RepositoryAssemblerActor,
)
from experimental.zheren.data.vanguard_to_binks_classification_actor import (
    SingleRequestClassificationActor,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def create_pipeline_config(args) -> dict:
    """Create pipeline configuration from command-line arguments."""
    return {
        "stages": {
            "bigquery_split": {
                "actor_cls": TenantDateRangeSplitActor,
                "actor_args": {
                    "chunk_days": args.chunk_days,
                    "max_requests_per_chunk": args.max_requests_per_chunk,
                },
                "num_workers": 1,  # Single actor for splitting
                "num_cpu_per_worker": 1,
                "num_gpu_per_worker": 0,
            },
            "bigquery_fetch": {
                "actor_cls": BigQueryRequestFetcherActor,
                "actor_args": {
                    "service_account_json": args.service_account_file,
                    "batch_size": args.bigquery_batch_size,
                },
                "num_workers": args.num_bigquery_workers,
                "num_cpu_per_worker": 2,
                "num_gpu_per_worker": 0,
            },
            "classification": {
                "actor_cls": SingleRequestClassificationActor,
                "actor_args": {
                    "pleasehold_checkpoint": args.pleasehold_checkpoint,
                    "pleasehold_sha256": args.pleasehold_sha256,
                    "docsets": args.docsets.split(",") if args.docsets else None,
                    "service_account_json": args.service_account_file,
                },
                "num_workers": args.num_classifier_workers,
                "num_cpu_per_worker": args.num_cpu_per_worker,
                "num_gpu_per_worker": args.num_gpu_per_worker,
            },
            "blob_fetch": {
                "actor_cls": BlobContentFetcherActor,
                "actor_args": {
                    "blob_cache_gb": args.blob_cache_gb,
                    "service_account_json": args.service_account_file,
                },
                "num_workers": args.num_blob_workers,
                "num_cpu_per_worker": 2,
                "num_gpu_per_worker": 0,
            },
            "file_process": {
                "actor_cls": FileProcessorActor,
                "actor_args": {},
                "num_workers": args.num_processor_workers,
                "num_cpu_per_worker": 4,
                "num_gpu_per_worker": 0,
            },
            "repository_assembly": {
                "actor_cls": RepositoryAssemblerActor,
                "actor_args": {},
                "num_workers": args.num_assembler_workers,
                "num_cpu_per_worker": 2,
                "num_gpu_per_worker": 0,
            },
        }
    }


def run_pipeline_stage(
    stage_name: str,
    stage_config: dict,
    input_path: str,
    output_path: str,
    mode: str = "local",
) -> int:
    """Run a single pipeline stage."""
    logger.info(f"Running stage: {stage_name}")
    logger.info(f"Input: {input_path}")
    logger.info(f"Output: {output_path}")

    # Create output directory if needed
    output_dir = Path(output_path)
    if not output_dir.exists():
        output_dir.mkdir(parents=True, exist_ok=True)

    with RayRunner(
        actor_cls=stage_config["actor_cls"],
        actor_args=stage_config["actor_args"],
        num_workers=stage_config["num_workers"],
        num_cpu_per_worker=stage_config["num_cpu_per_worker"],
        num_gpu_per_worker=stage_config["num_gpu_per_worker"],
        local=mode == "local",
    ) as runner:
        runner.process_jsonl(input_path, output_path)

    # Count output records
    if mode == "local":
        output_dir = Path(output_path)
        if output_dir.exists():
            total_count = 0
            for output_file in output_dir.glob("*.jsonl"):
                with open(output_file, "r") as f:
                    count = sum(1 for _ in f)
                    total_count += count
            logger.info(f"Stage {stage_name} produced {total_count} records")
            return total_count
        else:
            logger.warning(f"Output directory {output_path} does not exist")

    return 0


def main():
    """Main function to orchestrate the modular pipeline."""
    parser = argparse.ArgumentParser(
        description="Modular Ray-based Vanguard to Binks dataset conversion"
    )

    # Data selection arguments
    parser.add_argument(
        "--date-from",
        type=str,
        required=True,
        help="Start date for data collection (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--date-to",
        type=str,
        required=True,
        help="End date for data collection (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--output-path", type=str, required=True, help="Output path for the JSONL file"
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Limit number of requests per tenant (for testing)",
    )
    parser.add_argument(
        "--tenants",
        type=str,
        default=None,
        help="Comma-separated list of specific tenants to process",
    )

    # BigQuery configuration
    parser.add_argument(
        "--chunk-days",
        type=int,
        default=7,
        help="Number of days per BigQuery chunk (default: 7)",
    )
    parser.add_argument(
        "--max-requests-per-chunk",
        type=int,
        default=None,
        help="Maximum requests per chunk",
    )
    parser.add_argument(
        "--bigquery-batch-size",
        type=int,
        default=1000,
        help="BigQuery result batch size (default: 1000)",
    )

    # Processing configuration
    parser.add_argument(
        "--blob-cache-gb",
        type=float,
        default=1.0,
        help="Blob cache size in GB (default: 1.0)",
    )
    parser.add_argument(
        "--service-account-file",
        type=str,
        default=None,
        help="Path to service account JSON file for GCP authentication",
    )

    # PleaseHold configuration
    parser.add_argument(
        "--pleasehold-checkpoint",
        type=str,
        default="/mnt/efs/augment/checkpoints/pleasehold/pleasehold_v2_qwen14b_bsz128_ff_fp8",
        help="Path to PleaseHold model checkpoint",
    )
    parser.add_argument(
        "--pleasehold-sha256",
        type=str,
        default="301cb2137636abaee6a0dfb08c7e50d95aca9677cb33a7ccc72858c02938cad6",
        help="SHA256 hash of PleaseHold checkpoint",
    )
    parser.add_argument(
        "--docsets",
        type=str,
        default=None,
        help="Comma-separated list of docsets for PleaseHold",
    )

    # Ray configuration
    parser.add_argument(
        "--mode",
        type=str,
        default="local",
        choices=["local", "ray"],
        help="Execution mode: local (for testing) or ray (distributed)",
    )
    parser.add_argument(
        "--num-cpu-per-worker",
        type=int,
        default=8,
        help="Number of CPUs per worker (default: 8)",
    )
    parser.add_argument(
        "--num-gpu-per-worker",
        type=int,
        default=0,
        help="Number of GPUs per worker (default: 0, set to 1 for GPU mode)",
    )

    # Worker configuration for each stage
    parser.add_argument(
        "--num-bigquery-workers",
        type=int,
        default=10,
        help="Number of BigQuery fetcher workers (default: 10)",
    )
    parser.add_argument(
        "--num-classifier-workers",
        type=int,
        default=4,
        help="Number of classifier workers (default: 4)",
    )
    parser.add_argument(
        "--num-blob-workers",
        type=int,
        default=20,
        help="Number of blob fetcher workers (default: 20)",
    )
    parser.add_argument(
        "--num-processor-workers",
        type=int,
        default=16,
        help="Number of file processor workers (default: 16)",
    )
    parser.add_argument(
        "--num-assembler-workers",
        type=int,
        default=8,
        help="Number of repository assembler workers (default: 8)",
    )

    # Pipeline control
    parser.add_argument(
        "--keep-intermediate",
        action="store_true",
        help="Keep intermediate files for debugging",
    )

    args = parser.parse_args()

    # Validate dates
    try:
        datetime.strptime(args.date_from, "%Y-%m-%d")
        datetime.strptime(args.date_to, "%Y-%m-%d")
    except ValueError:
        logger.error("Invalid date format. Use YYYY-MM-DD")
        return 1

    # Validate service account file if provided
    if args.service_account_file:
        get_gcp_creds(args.service_account_file)

    # Get tenants to process
    if args.tenants:
        tenant_names = [t.strip() for t in args.tenants.split(",")]
        # Validate tenant names
        invalid_tenants = [name for name in tenant_names if name not in DATASET_TENANTS]
        if invalid_tenants:
            logger.error(f"Invalid tenant names: {invalid_tenants}")
            logger.error(f"Available tenants: {sorted(DATASET_TENANTS.keys())}")
            return 1
    else:
        tenant_names = list(DATASET_TENANTS.keys())

    logger.info(
        f"Processing {len(tenant_names)} tenants in {args.mode} mode "
        f"with modular pipeline"
    )

    # Create pipeline configuration
    pipeline_config = create_pipeline_config(args)

    # Create temporary directory for intermediate files
    temp_dir = tempfile.mkdtemp(prefix="vanguard_binks_")
    logger.info(f"Using temporary directory: {temp_dir}")

    try:
        # Stage 0: Create initial work items
        initial_config = PipelineConfig(
            tenant_names=tenant_names,
            date_from=args.date_from,
            date_to=args.date_to,
            limit=args.limit,
        )

        initial_file = Path(temp_dir) / "initial_config.jsonl"
        with open(initial_file, "w") as f:
            f.write(initial_config.to_json() + "\n")

        # Run pipeline stages
        stage_files = {
            "initial": str(initial_file),
            "split": str(Path(temp_dir) / "stage0_split"),
            "fetch": str(Path(temp_dir) / "stage1_fetch"),
            "classify": str(Path(temp_dir) / "stage2_classify"),
            "blob": str(Path(temp_dir) / "stage3_blob"),
            "process": str(Path(temp_dir) / "stage4_process"),
            "final": args.output_path,
        }

        # Execute pipeline stages in sequence
        stages = [
            ("bigquery_split", "initial", "split"),
            ("bigquery_fetch", "split", "fetch"),
            ("classification", "fetch", "classify"),
            ("blob_fetch", "classify", "blob"),
            ("file_process", "blob", "process"),
            ("repository_assembly", "process", "final"),
        ]

        for stage_name, input_key, output_key in stages:
            stage_config = pipeline_config["stages"][stage_name]
            input_path = stage_files[input_key]
            output_path = stage_files[output_key]

            count = run_pipeline_stage(
                stage_name, stage_config, input_path, output_path, args.mode
            )

            if count == 0 and stage_name != "bigquery_split":
                logger.warning(f"Stage {stage_name} produced no output")

        logger.info(f"Pipeline complete! Output written to {args.output_path}")

        return 0

    finally:
        # Clean up temporary files unless requested to keep
        if not args.keep_intermediate:
            logger.info(f"Cleaning up temporary directory: {temp_dir}")
            shutil.rmtree(temp_dir, ignore_errors=True)
        else:
            logger.info(f"Keeping intermediate files in: {temp_dir}")


if __name__ == "__main__":
    exit(main())
