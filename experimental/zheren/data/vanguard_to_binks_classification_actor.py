#!/usr/bin/env python3
"""Classification actor for the modular Vanguard to Binks pipeline.

This module provides an actor for classifying requests using PleaseHold
to determine if they are codebase-related.
"""

import logging
from typing import Optional

# Import Ray utilities
from research.data.ray.ray_utils import AbstractRayActor

# Import data structures
from experimental.zheren.data.vanguard_to_binks_modular_data_structures import (
    VanguardSingleRequest,
    FilteredVanguardSingleRequest,
)

# Import PleaseHold classifier
from experimental.zheren.data.pleasehold_classifier_actor import (
    PleaseHoldClassifierActor,
    ClassificationRequest,
)

# Import dataset infrastructure
from base.datasets.tenants import get_tenant

# Import Vanguard utilities
from experimental.zheren.data.vanguard_data_utils import (
    get_chat_request_from_gcs,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class SingleRequestClassificationActor(
    AbstractRayActor[VanguardSingleRequest, FilteredVanguardSingleRequest]
):
    """Ray actor that classifies single Vanguard requests and filters for codebase-related ones.

    This actor:
    1. Takes a single Vanguard request
    2. Fetches chat request details from GCS
    3. Classifies the request using PleaseHold
    4. Returns filtered request with classification result
    """

    def __init__(
        self,
        pleasehold_checkpoint: str = "/mnt/efs/augment/checkpoints/pleasehold/pleasehold_v2_qwen14b_bsz128_ff_fp8",
        pleasehold_sha256: Optional[
            str
        ] = "301cb2137636abaee6a0dfb08c7e50d95aca9677cb33a7ccc72858c02938cad6",
        docsets: Optional[list[str]] = None,
        service_account_json: Optional[str] = None,
    ):
        super().__init__(
            input_cls=VanguardSingleRequest, output_cls=FilteredVanguardSingleRequest
        )

        # Initialize PleaseHold classifier
        self.classifier = PleaseHoldClassifierActor(
            checkpoint_path=pleasehold_checkpoint,
            checkpoint_sha256=pleasehold_sha256,
            docsets=docsets or [],
            num_gpus=1,  # Use single GPU per actor
        )

        # Store credentials
        if service_account_json:
            import google.auth
            from google.oauth2 import service_account

            self.credentials = service_account.Credentials.from_service_account_file(
                service_account_json
            )
        else:
            import google.auth

            self.credentials = google.auth.default()[0]

        logger.info("Initialized SingleRequestClassificationActor with PleaseHold")

    def process(
        self, row: VanguardSingleRequest
    ) -> list[FilteredVanguardSingleRequest]:
        """Process a single request through classification and filtering."""

        tenant = get_tenant(row.tenant_name)

        # Fetch chat request from GCS
        request = get_chat_request_from_gcs(
            tenant,
            row.tenant_id,
            row.request_id,
            self.credentials,
        )

        if not request or not request.message:
            logger.debug(f"No valid request found for {row.request_id}")
            return []

        # Create classification request
        classification_req = ClassificationRequest(
            request_id=row.request_id,
            message=request.message,
            tenant_name=row.tenant_name,
            blob_names=request.blob_names if request else None,
            file_paths=None,  # Will be populated later if needed
        )

        # Classify the request
        results = self.classifier.process(classification_req)

        if results and results[0].is_codebase_related:
            logger.debug(
                f"Request {row.request_id} classified as category {results[0].category} - keeping"
            )

            return [
                FilteredVanguardSingleRequest(
                    tenant_name=row.tenant_name,
                    request_id=row.request_id,
                )
            ]
        else:
            logger.debug(
                f"Request {row.request_id} not codebase-related - filtering out"
            )
            return []
