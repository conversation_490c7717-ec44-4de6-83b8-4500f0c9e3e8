# Vanguard to Binks Modular Pipeline

This document provides a comprehensive overview of the modular Vanguard to Binks data processing pipeline. The pipeline transforms raw Vanguard chat requests into Binks-format repositories for retrieval model training through a series of distributed processing stages.

## Overview

The modular pipeline processes user chat requests from the Vanguard system and converts them into structured repositories suitable for training retrieval models. It filters for codebase-related queries, fetches associated file contents, and assembles them into the Binks format with proper metadata and language detection.

## Architecture

The pipeline consists of 6 sequential stages implemented as Ray actors, enabling distributed processing and independent scaling:

```
PipelineConfig → TenantDateRangeSplitActor → TenantDateRange
TenantDateRange → BigQueryRequestFetcherActor → VanguardSingleRequest
VanguardSingleRequest → SingleRequestClassificationActor → FilteredVanguardSingleRequest
FilteredVanguardSingleRequest → BlobContentFetcherActor → RequestWithBlobContents
RequestWithBlobContents → FileProcessorActor → ProcessedRequest
ProcessedRequest → RepositoryAssemblerActor → Repository
```

Each stage has well-defined input/output interfaces and can be scaled independently based on processing requirements.

## Pipeline Flow Diagram

```
┌─────────────────────────────────────────────────────────────────────────┐
│                          Input: PipelineConfig                          │
│                    (tenants, date range, limits)                        │
└─────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                    Stage 0: Tenant Date Range Split                     │
│                                                                         │
│  ┌─────────────────────────────────────────────────┐                    │
│  │         TenantDateRangeSplitActor               │                    │
│  └─────────────────────────────────────────────────┘                    │
│                                                                         │
│  Input: PipelineConfig                                                  │
│  Output: TenantDateRange                                                │
│  - Splits work by tenant and date chunks                                │
│  - Distributes limits proportionally                                    │
└─────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                    Stage 1: BigQuery Request Fetch                      │
│                                                                         │
│  ┌─────────────────────────────────────────────────┐                    │
│  │      BigQueryRequestFetcherActor (N workers)    │                    │
│  └─────────────────────────────────────────────────┘                    │
│                                                                         │
│  Input: TenantDateRange                                                 │
│  Output: VanguardSingleRequest                                          │
│    - tenant_name, tenant_id, request_id                                 │
│  - Parallel BigQuery queries                                            │
│  - Streaming results with pagination                                    │
└─────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                Stage 2: Request Classification (GPU)                    │
│                                                                         │
│  ┌──────────────────────────────────────────────────┐                   │
│  │    SingleRequestClassificationActor (PleaseHold) │                   │
│  └──────────────────────────────────────────────────┘                   │
│                                                                         │
│  Input: VanguardSingleRequest                                           │
│  Output: FilteredVanguardSingleRequest                                  │
│    - tenant_name, request_id                                            │
│  - Filters for codebase-related queries only                            │
│  - Uses PleaseHold classifier                                           │
└─────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                    Stage 3: Blob Content Fetch (I/O)                    │
│                                                                         │
│  ┌─────────────────────────────────────────────────┐                    │
│  │         BlobContentFetcherActor                 │                    │
│  └─────────────────────────────────────────────────┘                    │
│                                                                         │
│  Input: FilteredVanguardSingleRequest                                   │
│  Output: RequestWithBlobContents                                        │
│    - tenant_name, request_id, message                                   │
│    - blob_contents: dict[blob_id → {path, content, size, hex_id}]       │
│  - Parallel GCS blob fetching with thread pools                         │
│  - Configurable blob cache (LRU)                                        │
└─────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                    Stage 4: File Processing (CPU)                       │
│                                                                         │
│  ┌─────────────────────────────────────────────────┐                    │
│  │            FileProcessorActor                   │                    │
│  └─────────────────────────────────────────────────┘                    │
│                                                                         │
│  Input: RequestWithBlobContents                                         │
│  Output: ProcessedRequest                                               │
│    - tenant_name, request_id, message                                   │
│    - files: list[ProcessedFile]                                         │
│    - language_stats, workspace_key, max_file/size_language              │
│  - Language detection per file                                          │
│  - Workspace key generation (MD5 hash)                                  │
│  - Aggregate statistics computation                                     │
└─────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                    Stage 5: Repository Assembly                         │
│                                                                         │
│  ┌─────────────────────────────────────────────────┐                    │
│  │         RepositoryAssemblerActor                │                    │
│  └─────────────────────────────────────────────────┘                    │
│                                                                         │
│  Input: ProcessedRequest                                                │
│  Output: Repository (Binks format)                                      │
│  - Repository name: vanguard_{tenant}_{workspace}_{request_id[:8]}      │
│  - Creates File objects with full metadata                              │
│  - Generates DocumentWithQuestionsV2                                    │
│  - Filters out requests with no files                                   │
└─────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                         Output: Binks Repositories                      │
│                    Ready for retrieval model training                   │
└─────────────────────────────────────────────────────────────────────────┘
```

## Pipeline Stages

### Stage 0: Tenant Date Range Split
**Actor**: `TenantDateRangeSplitActor`
**Input**: `PipelineConfig`
**Output**: `TenantDateRange`

Divides the overall processing job into manageable chunks for parallel execution.

**Functionality**:
- Splits date ranges into configurable chunks (default: 7 days)
- Distributes request limits proportionally across date chunks
- Creates independent work units for each tenant-date combination
- Enables parallel processing of large time periods

**Configuration**:
- `chunk_days`: Size of date chunks (default: 7)
- Automatically calculates proportional limits per chunk

### Stage 1: BigQuery Request Fetch
**Actor**: `BigQueryRequestFetcherActor`
**Input**: `TenantDateRange`
**Output**: `VanguardSingleRequest`

Retrieves chat request metadata from Vanguard's BigQuery tables.

**Functionality**:
- Executes SQL queries against tenant-specific BigQuery datasets
- Filters for CHAT request types within specified date ranges
- Uses streaming with configurable batch sizes for memory efficiency
- Caches BigQuery clients per project for performance optimization

**Query Structure**:
```sql
SELECT tenant_id, tenant, request_id, user_id, time
FROM `{project}.{dataset}.request_metadata`
WHERE tenant = '{tenant_name}' AND request_type = 'CHAT'
  AND time >= '{date_from}' AND time < '{date_to}'
ORDER BY time DESC
LIMIT {limit}
```

### Stage 2: Request Classification
**Actor**: `SingleRequestClassificationActor`
**Input**: `VanguardSingleRequest`
**Output**: `FilteredVanguardSingleRequest`

Filters requests to include only codebase-related queries using PleaseHold classification.

**Functionality**:
- Fetches complete chat request details from GCS
- Uses PleaseHold classifier to determine codebase relevance
- Only passes through requests classified as codebase-related
- Reduces downstream processing load by early filtering

**Classification Process**:
1. Retrieve chat request from GCS using tenant and request IDs
2. Create classification request with message and blob information
3. Run PleaseHold classifier to determine codebase relevance
4. Filter out non-codebase-related requests

### Stage 3: Blob Content Fetch
**Actor**: `BlobContentFetcherActor`
**Input**: `FilteredVanguardSingleRequest`
**Output**: `RequestWithBlobContents`

Retrieves actual file contents from Google Cloud Storage.

**Functionality**:
- Transforms blob IDs to hex format for GCS compatibility
- Uses GCSBlobCache with configurable cache size for efficient retrieval
- Leverages internal thread pools for parallel blob fetching
- Logs detailed statistics for monitoring and debugging

**Blob Processing**:
1. Convert blob IDs to hex format using consistent transformation
2. Fetch all blobs for a request in parallel via GCSBlobCache
3. Return blob contents with metadata (path, content, size)
4. Log fetch statistics (successful/failed counts)

### Stage 4: File Processing
**Actor**: `FileProcessorActor`
**Input**: `RequestWithBlobContents`
**Output**: `ProcessedRequest`

Processes individual files with language detection and statistical analysis.

**Functionality**:
- Detects programming language using `guess_language()` function
- Calculates file metrics (size, line counts, alphanumeric ratios)
- Extracts file extensions and generates workspace identifiers
- Computes aggregate language statistics across all files
- Determines dominant languages by file count and total size

**File Analysis**:
- Language detection for each file
- Calculation of average/max line lengths and alphanumeric fractions
- Workspace key generation using MD5 hash of all file paths
- Aggregation of language statistics (counts and sizes)

### Stage 5: Repository Assembly
**Actor**: `RepositoryAssemblerActor`
**Input**: `ProcessedRequest`
**Output**: `Repository`

Assembles the final Binks-format repository from processed files.

**Functionality**:
- Creates repositories with standardized naming convention
- Builds File objects with complete metadata
- Generates DocumentWithQuestionsV2 linking queries to files
- Includes comprehensive language and size statistics
- Filters out requests with no files

**Repository Structure**:
- Name: `vanguard_{tenant_name}_{workspace_key}_{request_id[:8]}`
- UUID: Randomly generated unique identifier
- Files: Complete file objects with content and metadata
- Document: User query linked to relevant file paths

## Data Structures

### Primary Data Types

```python
@dataclass
class VanguardSingleRequest:
    """Single request from BigQuery with essential identifiers."""
    tenant_name: str
    tenant_id: str
    request_id: str

@dataclass
class FilteredVanguardSingleRequest:
    """Codebase-related request after PleaseHold filtering."""
    tenant_name: str
    request_id: str

@dataclass
class RequestWithBlobContents:
    """Request with fetched file contents from GCS."""
    tenant_name: str
    request_id: str
    message: str
    blob_contents: dict[str, dict]  # blob_id -> {path, content, size, hex_id}

@dataclass
class ProcessedRequest:
    """Request with processed files and computed statistics."""
    tenant_name: str
    request_id: str
    message: str
    files: list[ProcessedFile]
    total_size: int
    language_stats: dict[str, LanguageStats]
    workspace_key: str
    max_file_language: str
    max_size_language: str
```

### File Representation

```python
@dataclass
class ProcessedFile:
    """Individual file with language detection and metadata."""
    blob_id: str           # Original blob identifier
    hex_id: str           # Hex-formatted blob ID for GCS
    path: str             # File path within repository
    content: str          # File content as string
    size: int             # File size in bytes
    language: str         # Detected programming language
    langpart: str         # Language partition for Binks format
    extension: Optional[str]  # File extension

@dataclass
class LanguageStats:
    """Statistics for a specific programming language."""
    file_count: int       # Number of files in this language
    total_size: int       # Total size of files in this language
```

## Configuration and Usage

### Command Line Interface

```bash
python vanguard_to_binks_modular_pipeline.py [OPTIONS]
```

### Essential Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `--tenants` | Comma-separated tenant names | Required |
| `--date-from` | Start date (YYYY-MM-DD) | Required |
| `--date-to` | End date (YYYY-MM-DD) | Required |
| `--limit` | Max requests per tenant | Required |
| `--output-path` | Output directory path | Required |

### Performance Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `--chunk-days` | Days per processing chunk | 7 |
| `--blob-cache-gb` | GCS blob cache size (GB) | 1.0 |
| `--num-bigquery-workers` | BigQuery fetch workers | 2 |
| `--num-classification-workers` | Classification workers | 4 |
| `--num-blob-workers` | Blob fetch workers | 4 |
| `--num-processor-workers` | File processing workers | 8 |
| `--num-assembler-workers` | Repository assembly workers | 2 |

### Example Usage

**Development/Testing**:
```bash
python vanguard_to_binks_modular_pipeline.py \
    --tenants augment \
    --date-from 2024-01-01 \
    --date-to 2024-01-07 \
    --limit 100 \
    --output-path ./test_output
```

**Production Processing**:
```bash
python vanguard_to_binks_modular_pipeline.py \
    --tenants tenant1,tenant2,tenant3 \
    --date-from 2024-01-01 \
    --date-to 2024-12-31 \
    --limit 50000 \
    --output-path /mnt/efs/binks_repositories \
    --blob-cache-gb 20.0 \
    --chunk-days 3 \
    --num-bigquery-workers 6 \
    --num-classification-workers 12 \
    --num-blob-workers 8 \
    --num-processor-workers 16 \
    --num-assembler-workers 4
```

## Performance Optimizations

### Parallelization Strategy
- **Horizontal scaling**: Each stage scales independently with configurable worker counts
- **Temporal parallelization**: Date range splitting enables concurrent processing of time periods
- **I/O optimization**: Blob fetching uses thread pools for concurrent GCS operations

### Memory Management
- **Streaming processing**: BigQuery results streamed with configurable batch sizes
- **Efficient caching**: GCSBlobCache with LRU eviction and configurable size limits
- **Minimal data passing**: Intermediate structures contain only essential fields

### Resource Optimization
- **Client caching**: BigQuery and GCS clients cached per project/tenant
- **Batch processing**: Efficient batching at each stage to minimize overhead
- **Early filtering**: Non-codebase requests filtered early to reduce downstream load

## Monitoring and Observability

### Logging Strategy
- **Stage-level logging**: Comprehensive logs at each processing stage
- **Progress tracking**: Periodic progress reports for long-running operations
- **Performance metrics**: Timing and throughput statistics
- **Error reporting**: Detailed error messages with context

### Key Metrics
- **Throughput**: Requests processed per minute at each stage
- **Success rates**: Successful vs failed operations (blob fetches, classifications)
- **Resource utilization**: Cache hit rates, memory usage, worker utilization
- **Data quality**: File counts, language distributions, repository sizes

## Output Format

The pipeline produces Repository objects in Binks format with the following structure:

### Repository Metadata
- **Name**: `vanguard_{tenant_name}_{workspace_key}_{request_id[:8]}`
- **UUID**: Unique identifier for the repository
- **Language statistics**: File counts and sizes by programming language
- **Creation timestamp**: Processing completion time

### File Objects
- **Complete content**: Full file text with metadata
- **Language detection**: Programming language and extension information
- **Path information**: Original file paths within the repository
- **Size metrics**: File sizes and line statistics

### Document Linkage
- **User query**: Original chat request message
- **File associations**: List of relevant file paths
- **Answer placeholder**: Empty field for future training data

Each repository represents a complete user request context, ready for retrieval model training with proper file associations and metadata.

## File Structure

```
experimental/zheren/data/
├── vanguard_to_binks_modular_pipeline.py          # Main pipeline orchestrator
├── vanguard_to_binks_modular_data_structures.py   # Shared data structures
├── vanguard_to_binks_bigquery_actors.py           # Stage 0-1: BigQuery operations
├── vanguard_to_binks_classification_actor.py      # Stage 2: PleaseHold classification
├── vanguard_to_binks_blob_fetcher_actor.py        # Stage 3: GCS blob fetching
├── vanguard_to_binks_file_processing_actors.py    # Stage 4-5: File processing & assembly
└── README_modular_pipeline.md                     # This documentation
```

## Key Design Principles

1. **Separation of Concerns**: Each stage handles a specific aspect of processing
2. **Minimal Data Transfer**: Only essential fields passed between stages
3. **Scalable Architecture**: Independent scaling of I/O, CPU, and GPU operations
4. **Fault Tolerance**: Stage-level error handling and recovery
5. **Observability**: Comprehensive logging and monitoring at each stage
