#!/usr/bin/env python3
"""Ray actor for PleaseHold classification of chat requests.

This actor loads the PleaseHold model and classifies chat requests to determine
if they are codebase-related queries that should be processed for retrieval training.
"""

import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Optional, Sequence

import torch
from dataclasses_json import DataClassJsonMixin

# Import Ray utilities
from research.data.ray.ray_utils import AbstractRayActor

# Import model and tokenizer components
from research.models import GenerationOptions
from research.models.all_models import get_model

# Import FastForward models explicitly (needed for CUDA initialization)
from research.models import fastforward_llama_models  # noqa: F401
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from base.prompt_format_router.pleasehold_prompt_formatter import (
    PleaseHoldPromptFormatter,
)
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@dataclass
class ClassificationRequest(DataClassJsonMixin):
    """Input data for classification - a batch of chat requests."""

    request_id: str
    message: str
    tenant_name: str
    # Optional fields for context
    blob_names: Optional[list[str]] = None
    file_paths: Optional[list[str]] = None


@dataclass
class ClassificationResult(DataClassJsonMixin):
    """Output of classification for a single request."""

    request_id: str
    message: str
    tenant_name: str
    is_codebase_related: bool
    category: int  # 0-4 as per PleaseHold output
    relevant_files: list[str]
    external_library: Optional[str]
    # Pass through original data
    blob_names: Optional[list[str]] = None
    file_paths: Optional[list[str]] = None


class PleaseHoldClassifierActor(
    AbstractRayActor[ClassificationRequest, ClassificationResult]
):
    """Ray actor that classifies chat requests using PleaseHold model.

    This actor:
    1. Loads the quantized PleaseHold model
    2. Formats requests using PleaseHoldPromptFormatter
    3. Runs inference to classify requests
    4. Parses the 3-line output format
    5. Determines if requests are codebase-related
    """

    def __init__(
        self,
        checkpoint_path: str = "/mnt/efs/augment/checkpoints/pleasehold/pleasehold_v2_qwen14b_bsz128_ff_fp8",
        checkpoint_sha256: Optional[
            str
        ] = "301cb2137636abaee6a0dfb08c7e50d95aca9677cb33a7ccc72858c02938cad6",
        device: str = "cuda",
        docsets: Optional[list[str]] = None,
        num_gpus: int = 1,
    ):
        super().__init__(
            input_cls=ClassificationRequest, output_cls=ClassificationResult
        )

        self.checkpoint_path = Path(checkpoint_path)
        self.checkpoint_sha256 = checkpoint_sha256
        self.device = device
        self.docsets = docsets or []
        self.num_gpus = num_gpus

        # Model will be loaded lazily
        self.model = None
        self.tokenizer = None
        self.prompt_formatter = None

        logger.info(
            f"Initialized PleaseHoldClassifierActor with checkpoint: {checkpoint_path}"
        )

    def _load_model(self):
        """Load the PleaseHold model and tokenizer."""
        if self.model is not None:
            return

        logger.info(f"Loading PleaseHold FP8 model with {self.num_gpus} GPU(s)...")

        # Load the quantized Qwen model
        # Using FastForward Qwen 14B FP8 model
        # The checkpoint contains FP8 quantized weights, so we need to use use_fp8=True
        self.model = get_model(
            "fastforward_qwen25coder_14b",
            checkpoint_path=self.checkpoint_path,
            checkpoint_sha256=self.checkpoint_sha256,
            num_gpus=self.num_gpus,  # Specify number of GPUs
            use_fp8=True,  # Enable FP8 mode for quantized model
        )
        self.model.load()

        # Initialize tokenizer
        self.tokenizer = Qwen25CoderTokenizer()

        # Initialize prompt formatter
        self.prompt_formatter = PleaseHoldPromptFormatter(
            tokenizer=self.tokenizer,
            docsets=self.docsets,
        )

        logger.info("Model loaded successfully")

    def _parse_pleasehold_output(
        self, output: str
    ) -> tuple[int, list[str], Optional[str]]:
        """Parse the 3-line PleaseHold output format.

        Returns:
            category: 0-4 classification
            relevant_files: List of relevant files (empty if none)
            external_library: External library name or None
        """
        lines = output.strip().split("\n")

        # Default values
        category = 0
        relevant_files = []
        external_library = None

        # Parse line 1: category
        if len(lines) > 0:
            try:
                category = int(lines[0].strip())
            except ValueError:
                logger.warning(f"Failed to parse category from: {lines[0]}")

        # Parse line 2: relevant files
        if len(lines) > 1 and lines[1].strip():
            # Split by comma or semicolon
            files = lines[1].strip().replace(";", ",").split(",")
            relevant_files = [f.strip() for f in files if f.strip()]

        # Parse line 3: external library
        if len(lines) > 2 and lines[2].strip().lower() != "none":
            external_library = lines[2].strip()

        return category, relevant_files, external_library

    def _is_codebase_related(self, category: int) -> bool:
        """Determine if a category indicates a codebase-related query.

        Categories:
        0: General programming question (not codebase-specific)
        1: Edit request (codebase-related)
        2: Question about current file (codebase-related)
        3: Codebase question (codebase-related)
        4: Overview question (codebase-related)
        """
        return category in [3]

    def process(self, row: ClassificationRequest) -> list[ClassificationResult]:
        """Process a single classification request."""
        # Ensure model is loaded
        self._load_model()

        try:
            # Create chat prompt input
            # ChatPromptInput expects all fields to be provided
            chat_input = ChatPromptInput(
                message=row.message,  # This is the user's message
                path=row.file_paths[0] if row.file_paths else "",
                prefix="",  # Empty for classification
                selected_code="",  # Empty for classification
                suffix="",  # Empty for classification
                chat_history=[],  # No history needed for classification
                prefix_begin=0,
                suffix_end=0,
                retrieved_chunks=[],  # No retrieval for classification
            )

            # Format prompt
            formatted_prompt = self.prompt_formatter.format_prompt(chat_input)

            # Run inference using raw_generate
            # The model expects prompt tokens and returns generated text
            generated_text = self.model.raw_generate(
                formatted_prompt.tokens,
                GenerationOptions(
                    max_generated_tokens=50,  # 3 lines should be much less than 50 tokens
                    temperature=0.0,  # Deterministic classification
                ),
            )

            # Parse output
            category, relevant_files, external_library = self._parse_pleasehold_output(
                generated_text
            )
            logger.info(
                f"Classifying request {row.request_id} with message: {row.message}"
            )
            logger.info(f"Generated text: {generated_text}")
            logger.info(f"Category: {category}")
            logger.info(f"Relevant files: {relevant_files}")
            logger.info(f"External library: {external_library}")

            # Create result
            result = ClassificationResult(
                request_id=row.request_id,
                message=row.message,
                tenant_name=row.tenant_name,
                is_codebase_related=self._is_codebase_related(category),
                category=category,
                relevant_files=relevant_files,
                external_library=external_library,
                blob_names=row.blob_names,
                file_paths=row.file_paths,
            )

            return [result]

        except Exception as e:
            logger.error(f"Error classifying request {row.request_id}: {e}")
            # Return a default result marking it as not codebase-related
            return [
                ClassificationResult(
                    request_id=row.request_id,
                    message=row.message,
                    tenant_name=row.tenant_name,
                    is_codebase_related=False,
                    category=0,
                    relevant_files=[],
                    external_library=None,
                    blob_names=row.blob_names,
                    file_paths=row.file_paths,
                )
            ]
