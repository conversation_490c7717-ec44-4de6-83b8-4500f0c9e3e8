"""Dataset loader for Vanguard-converted Binks data compatible with Ray pipeline.

This module provides utilities to load and process the converted Vanguard data
for use with the chatanol retriever training pipeline.
"""

import json
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Iterator, Optional, Sequence

import ray
from ray.data import Dataset

# Import official Binks schemas
from experimental.tongfei.data.binks_schemas import (
    Repository,
    GeneratedRetrievalTrainingExample,
    SilverRetrievalTrainingExample,
)
from research.core.types import Chunk

logger = logging.getLogger(__name__)


@dataclass
class VanguardBinksDataset:
    """Dataset wrapper for Vanguard-converted Binks data."""

    data_path: Path
    """Path to the JSONL file containing converted data."""

    limit: Optional[int] = None
    """Optional limit on number of repositories to load."""

    def load_repositories(self) -> list[Repository]:
        """Load repositories from the JSONL file."""
        repositories = []

        with open(self.data_path, "r") as f:
            for i, line in enumerate(f):
                if self.limit and i >= self.limit:
                    break

                try:
                    # Use the official from_json method
                    repo = Repository.from_json(line.strip())
                    repositories.append(repo)
                except Exception as e:
                    logger.error(f"Error loading repository at line {i}: {e}")
                    continue

        logger.info(f"Loaded {len(repositories)} repositories from {self.data_path}")
        return repositories

    def to_ray_dataset(self) -> Dataset:
        """Convert to Ray Dataset for distributed processing."""
        repositories = self.load_repositories()
        return ray.data.from_items(repositories)

    def iter_qa_pairs(self) -> Iterator[tuple[str, str, list[str]]]:
        """Iterate over all question-answer pairs in the dataset.

        Yields:
            Tuples of (question, answer, paths)
        """
        repositories = self.load_repositories()

        for repo in repositories:
            for doc in repo.documents_with_questions:
                for qa_pair in doc.qa_pairs():
                    yield (qa_pair.question, qa_pair.answer, qa_pair.paths)

    def get_statistics(self) -> dict:
        """Get dataset statistics."""
        repositories = self.load_repositories()

        total_files = sum(len(repo.file_list) for repo in repositories)
        total_questions = sum(
            len(repo.documents_with_questions) for repo in repositories
        )

        # Language distribution
        lang_counts = {}
        for repo in repositories:
            for file in repo.file_list:
                lang = file.langpart
                lang_counts[lang] = lang_counts.get(lang, 0) + 1

        # File size distribution
        file_sizes = []
        for repo in repositories:
            for file in repo.file_list:
                file_sizes.append(file.size)

        avg_file_size = sum(file_sizes) / len(file_sizes) if file_sizes else 0

        return {
            "num_repositories": len(repositories),
            "num_files": total_files,
            "num_questions": total_questions,
            "avg_files_per_repo": total_files / len(repositories)
            if repositories
            else 0,
            "avg_questions_per_repo": total_questions / len(repositories)
            if repositories
            else 0,
            "language_distribution": lang_counts,
            "avg_file_size_bytes": avg_file_size,
            "total_size_bytes": sum(file_sizes),
        }


class VanguardRetrievalDataGenerator:
    """Generates retrieval training examples from Vanguard data.

    This class is designed to work with the existing chatanol training pipeline.
    """

    def __init__(self, data_path: Path, limit: Optional[int] = None):
        self.dataset = VanguardBinksDataset(data_path, limit)

    def generate_training_examples(
        self,
        retriever,
        num_chunks: int = 128,
    ) -> Iterator[GeneratedRetrievalTrainingExample]:
        """Generate retrieval training examples.

        Args:
            retriever: The retrieval system to use for getting chunks
            num_chunks: Number of chunks to retrieve per question

        Yields:
            GeneratedRetrievalTrainingExample objects
        """
        repositories = self.dataset.load_repositories()

        for repo in repositories:
            # Add repository files to retriever
            retriever.remove_all_docs()
            for file in repo.file_list:
                retriever.add_doc(
                    text=file.content,
                    path=file.max_stars_repo_path,
                    metadata={"repo": repo.max_stars_repo_name},
                )

            # Generate examples for each question
            for doc in repo.documents_with_questions:
                for qa_pair in doc.qa_pairs():
                    # Retrieve chunks for the question
                    chunks, scores = retriever.query(qa_pair.question, top_k=num_chunks)

                    yield GeneratedRetrievalTrainingExample(
                        question=qa_pair.question,
                        answer=qa_pair.answer,
                        paths=qa_pair.paths,
                        retrieved_chunks=chunks,
                    )

    def generate_silver_examples(
        self,
        retriever,
        scorer,
        num_chunks: int = 128,
    ) -> Iterator[SilverRetrievalTrainingExample]:
        """Generate silver training examples with perplexity scores.

        Args:
            retriever: The retrieval system to use
            scorer: The scoring model for perplexity calculation
            num_chunks: Number of chunks to retrieve

        Yields:
            SilverRetrievalTrainingExample objects
        """
        for example in self.generate_training_examples(retriever, num_chunks):
            # Score each chunk
            perplexities = []
            for chunk in example.retrieved_chunks:
                score = scorer.score_chunk(example.question, chunk)
                perplexities.append(score)

            yield SilverRetrievalTrainingExample(
                question=example.question,
                chunks=example.retrieved_chunks,
                perplexities=perplexities,
            )


@ray.remote
class VanguardDataProcessor:
    """Ray actor for processing Vanguard data in parallel."""

    def __init__(self, retriever_config: dict, scorer_config: dict):
        # Initialize retriever and scorer based on configs
        # This would be implemented based on your specific retriever/scorer setup
        self.retriever = None  # Initialize based on retriever_config
        self.scorer = None  # Initialize based on scorer_config

    def process_repository(
        self, repo: Repository
    ) -> list[SilverRetrievalTrainingExample]:
        """Process a single repository to generate training examples."""
        examples = []

        # Add files to retriever
        if self.retriever:
            self.retriever.remove_all_docs()
            for file in repo.file_list:
                self.retriever.add_doc(
                    text=file.content,
                    path=file.max_stars_repo_path,
                )

        # Generate examples
        for doc in repo.documents_with_questions:
            for qa_pair in doc.qa_pairs():
                # Mock implementation - replace with actual retrieval
                chunks = []  # self.retriever.query(qa_pair.question)
                perplexities = []  # self.scorer.score_chunks(chunks)

                examples.append(
                    SilverRetrievalTrainingExample(
                        question=qa_pair.question,
                        chunks=chunks,
                        perplexities=perplexities,
                    )
                )

        return examples


def create_ray_pipeline(
    data_path: Path,
    retriever_config: dict,
    scorer_config: dict,
    num_workers: int = 4,
) -> Dataset:
    """Create a Ray pipeline for processing Vanguard data.

    Args:
        data_path: Path to the converted JSONL data
        retriever_config: Configuration for the retriever
        scorer_config: Configuration for the scorer
        num_workers: Number of parallel workers

    Returns:
        Ray Dataset of training examples
    """
    # Load repositories
    dataset = VanguardBinksDataset(data_path)
    repositories = dataset.load_repositories()

    # Create Ray dataset
    ray_dataset = ray.data.from_items(repositories)

    # Create processor actors
    # TODO: Implement processor actors when needed
    # processors = [
    #     VanguardDataProcessor.remote(retriever_config, scorer_config)
    #     for _ in range(num_workers)
    # ]

    # Process in parallel
    def process_batch(batch: dict) -> dict:
        # This would be implemented to distribute work to actors
        # For now, returning a placeholder
        return {"examples": []}

    # Apply processing
    processed = ray_dataset.map_batches(
        process_batch,
        batch_size=10,
        num_cpus=1,
    )

    return processed


# Integration with existing chatanol pipeline
def integrate_with_chatanol_pipeline(data_path: Path) -> dict:
    """Create configuration for integrating with chatanol training.

    Returns a configuration dict that can be used with the existing
    chatanol training pipeline.
    """
    return {
        "dataset_type": "vanguard_binks",
        "data_path": str(data_path),
        "dataset_class": "experimental.zheren.data.vanguard_binks_dataset.VanguardBinksDataset",
        "generator_class": "experimental.zheren.data.vanguard_binks_dataset.VanguardRetrievalDataGenerator",
        "preprocessing": {
            "batch_size": 128,
            "num_workers": 8,
            "cache_size_gb": 2.0,
        },
        "training": {
            "num_retrieved_chunks": 128,
            "num_retrieved_extra": 128,
            "prioritize_gold_chunks": True,
        },
    }


if __name__ == "__main__":
    # Example usage
    import sys

    if len(sys.argv) != 2:
        print("Usage: python vanguard_binks_dataset.py <data_path>")
        sys.exit(1)

    data_path = Path(sys.argv[1])
    dataset = VanguardBinksDataset(data_path)

    # Print statistics
    stats = dataset.get_statistics()
    print("\nDataset Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")

    # Show integration config
    config = integrate_with_chatanol_pipeline(data_path)
    print("\nChatanol Integration Config:")
    print(json.dumps(config, indent=2))
