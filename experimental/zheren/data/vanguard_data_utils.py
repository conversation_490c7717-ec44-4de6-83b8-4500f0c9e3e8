"""Utility functions for retrieving and processing Vanguard user data."""

import base64
import json
import logging
from dataclasses import dataclass
from typing import Optional, Any

import google.auth
from google.cloud import bigquery, storage
from google.protobuf.json_format import MessageToDict

from base.datasets.tenants import get_tenant, DatasetTenant
from services.request_insight import request_insight_pb2
from services.chat_host import chat_pb2

logger = logging.getLogger(__name__)


@dataclass
class VanguardChatRequest:
    """Parsed chat request from Vanguard user."""

    request_id: str
    tenant_id: str
    tenant_name: str
    user_id: str
    message: str
    blob_names: list[str]
    path: Optional[str] = None
    selected_code: Optional[str] = None
    prefix: Optional[str] = None
    suffix: Optional[str] = None
    chat_history: Optional[list[dict]] = None


def query_chat_requests(
    tenant: DatasetTenant,
    date_from: str,
    date_to: str,
    limit: Optional[int] = None,
    credentials: Optional[Any] = None,
) -> list[dict]:
    """Query BigQuery for chat request IDs."""
    if credentials is None:
        credentials, _ = google.auth.default()

    client = bigquery.Client(project=tenant.project_id, credentials=credentials)

    query = f"""
    SELECT
        tenant_id,
        tenant,
        request_id,
        user_id,
        time
    FROM `{tenant.project_id}.{tenant.search_dataset_name}.request_metadata`
    WHERE
        tenant = '{tenant.name}'
        AND request_type = 'CHAT'
        AND time >= '{date_from}'
        AND time < '{date_to}'
    ORDER BY time DESC
    """

    if limit:
        query += f" LIMIT {limit}"

    logger.info(f"Querying BigQuery for tenant {tenant.name}")
    job = client.query(query)
    results = list(job.result())
    logger.info(f"Found {len(results)} chat requests for tenant {tenant.name}")

    return [dict(row) for row in results]


def get_chat_request_from_gcs(
    tenant: DatasetTenant,
    tenant_id: str,
    request_id: str,
    credentials: Optional[Any] = None,
) -> Optional[VanguardChatRequest]:
    """Retrieve and parse a chat request from GCS."""
    if credentials is None:
        credentials, _ = google.auth.default()

    storage_client = storage.Client(project=tenant.project_id, credentials=credentials)

    # Chat requests are stored in the events bucket with event type 'chat_host_request'
    bucket = storage_client.bucket(tenant.events_bucket_name)
    prefix = f"{tenant_id}/request/{request_id}/chat_host_request"

    blobs = list(bucket.list_blobs(prefix=prefix))
    if not blobs:
        logger.warning(f"No chat request found for {request_id}")
        return None

    # Parse the request event
    for blob in blobs:
        try:
            blob_bytes = blob.download_as_bytes()
            request_event = request_insight_pb2.RequestEvent()
            request_event.ParseFromString(blob_bytes)

            # Convert to dict for easier access
            event_dict = MessageToDict(request_event, preserving_proto_field_name=True)

            # For chat_host_request events, the structure is different
            if "chat_host_request" not in event_dict:
                continue

            chat_data = event_dict["chat_host_request"]

            # The chat request data might be in 'request' field
            if "request" in chat_data:
                chat_request = chat_data["request"]
            else:
                # Or it might be directly in chat_data
                chat_request = chat_data

            # Extract blob names using proper set operations
            blob_names = []
            if "blobs" in chat_request:
                for blobs_obj in chat_request["blobs"]:
                    # Start with checkpoint blobs as a set
                    checkpoint_blob_names = set()
                    if "baseline_checkpoint_id" in blobs_obj:
                        # Need to retrieve checkpoint blob IDs
                        checkpoint_blob_ids = get_checkpoint_blob_ids(
                            tenant, blobs_obj["baseline_checkpoint_id"], credentials
                        )
                        checkpoint_blob_names = set(checkpoint_blob_ids)

                    # Get added and deleted blobs as sets
                    added_blob_names = set()
                    if "added" in blobs_obj:
                        added_blob_names = set(blobs_obj["added"])

                    deleted_blob_names = set()
                    if "deleted" in blobs_obj:
                        # Convert deleted blob IDs to hex format to match checkpoint format
                        for deleted_id in blobs_obj["deleted"]:
                            # Try to decode base64 to hex
                            try:
                                blob_bytes = base64.b64decode(deleted_id)
                                hex_id = blob_bytes.hex()
                                deleted_blob_names.add(hex_id)
                            except Exception:
                                # If not base64, add as-is
                                deleted_blob_names.add(deleted_id)

                    # Apply set operations: (checkpoint | added) - deleted
                    resolved_blobs = (
                        checkpoint_blob_names | added_blob_names
                    ) - deleted_blob_names

                    # Convert back to sorted list
                    blob_names = sorted(list(resolved_blobs))

            # Parse chat history
            chat_history = []
            if "chat_history" in chat_request:
                for exchange in chat_request["chat_history"]:
                    chat_history.append(
                        {
                            "request": exchange.get("request_message", ""),
                            "response": exchange.get("response_text", ""),
                        }
                    )

            # Extract user_id - it might be at different levels
            user_id = chat_data.get("user_id", "") or event_dict.get("user_id", "")

            return VanguardChatRequest(
                request_id=request_id,
                tenant_id=tenant_id,
                tenant_name=tenant.name,
                user_id=user_id,
                message=chat_request.get("message", ""),
                blob_names=blob_names,
                path=chat_request.get("path"),
                selected_code=chat_request.get("selected_code"),
                prefix=chat_request.get("prefix"),
                suffix=chat_request.get("suffix"),
                chat_history=chat_history if chat_history else None,
            )

        except Exception as e:
            logger.error(f"Error parsing chat request {request_id}: {e}")
            continue

    return None


def get_checkpoint_blob_ids(
    tenant: DatasetTenant,
    checkpoint_id: str,
    credentials: Optional[Any] = None,
) -> list[str]:
    """Get blob IDs from a checkpoint."""
    if credentials is None:
        credentials, _ = google.auth.default()

    storage_client = storage.Client(project=tenant.project_id, credentials=credentials)

    bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
    blob_name = f"{tenant.checkpoint_bucket_prefix}/{checkpoint_id}"
    blob = bucket.blob(blob_name)

    if not blob.exists():
        logger.warning(f"Checkpoint {checkpoint_id} not found")
        return []

    try:
        checkpoint_text = blob.download_as_text()
        return json.loads(checkpoint_text)
    except Exception as e:
        logger.error(f"Error loading checkpoint {checkpoint_id}: {e}")
        return []


def get_blob_content(
    tenant: DatasetTenant,
    blob_id: str,
    credentials: Optional[Any] = None,
) -> Optional[dict]:
    """Retrieve blob content and metadata from GCS."""
    if credentials is None:
        credentials, _ = google.auth.default()

    storage_client = storage.Client(project=tenant.project_id, credentials=credentials)

    bucket = storage_client.bucket(tenant.blob_bucket_name)
    blob_name = f"{tenant.blob_bucket_prefix}/{blob_id}"
    blob = bucket.blob(blob_name)

    if not blob.exists():
        logger.warning(f"Blob {blob_id} not found")
        return None

    try:
        # Reload to get metadata
        blob.reload()

        content = blob.download_as_text()
        metadata = blob.metadata or {}

        # Extract file path from metadata
        file_path = metadata.get("path", f"unknown/{blob_id}")

        return {
            "blob_id": blob_id,
            "path": file_path,
            "content": content,
            "size": blob.size,
            "metadata": metadata,
        }
    except Exception as e:
        logger.error(f"Error loading blob {blob_id}: {e}")
        return None


def batch_get_blob_contents(
    tenant: DatasetTenant,
    blob_ids: list[str],
    credentials: Optional[Any] = None,
    max_workers: int = 10,
) -> dict[str, dict]:
    """Retrieve multiple blob contents in parallel."""
    from concurrent.futures import ThreadPoolExecutor, as_completed

    blob_contents = {}

    def fetch_blob(blob_id):
        return blob_id, get_blob_content(tenant, blob_id, credentials)

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_blob = {
            executor.submit(fetch_blob, blob_id): blob_id for blob_id in blob_ids
        }

        for future in as_completed(future_to_blob):
            blob_id, content = future.result()
            if content:
                blob_contents[blob_id] = content

    return blob_contents
