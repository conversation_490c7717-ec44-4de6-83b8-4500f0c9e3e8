#!/usr/bin/env python3
"""Comprehensive data inventory script for Vanguard dataset analysis."""

import json
import logging
import random
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

import google.auth
from google.cloud import bigquery, storage
import numpy as np

from base.datasets.tenants import get_tenant
from experimental.zheren.data.vanguard_data_utils import (
    get_chat_request_from_gcs,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class VanguardDataInventory:
    """Analyze Vanguard data across all tenants."""

    def __init__(self):
        self.credentials, _ = google.auth.default()
        self.bq_client = bigquery.Client(credentials=self.credentials)
        self.all_tenants = [
            "i0-vanguard0",
            "i0-vanguard1",
            "i0-vanguard2",
            "i0-vanguard3",
            "i0-vanguard4",
            "i0-vanguard5",
            "i0-vanguard6",
            "i0-vanguard7",
            "i1-vanguard0",
            "i1-vanguard1",
            "i1-vanguard2",
            "i1-vanguard3",
            "i1-vanguard4",
            "i1-vanguard5",
            "i1-vanguard6",
            "i1-vanguard7",
        ]
        self.results = {}

    def run_full_inventory(self):
        """Run complete inventory analysis."""
        logger.info("=== VANGUARD DATA INVENTORY ===")
        logger.info(f"Analyzing {len(self.all_tenants)} tenants")

        # 1. Query BigQuery for aggregate statistics
        self.analyze_bigquery_stats()

        # 2. Analyze workspace/repository diversity
        self.analyze_workspace_diversity()

        # 3. Analyze data quality
        self.analyze_data_quality()

        # 4. Estimate conversion potential
        self.estimate_conversion_potential()

        # 5. Generate summary report
        self.generate_summary_report()

    def analyze_bigquery_stats(self):
        """Query BigQuery for aggregate statistics across all tenants."""
        logger.info("\n=== BIGQUERY AGGREGATE STATISTICS ===")

        # Build query for all tenants
        tenant_conditions = []
        for tenant_name in self.all_tenants:
            try:
                tenant = get_tenant(tenant_name)
                tenant_conditions.append(f"tenant_id = '{tenant.tenant_id}'")
            except Exception as e:
                logger.warning(f"Failed to get tenant {tenant_name}: {e}")

        if not tenant_conditions:
            logger.error("No valid tenants found")
            return

        where_clause = " OR ".join(tenant_conditions)

        # Query 1: Overall statistics
        # Use the first tenant's dataset as they all share the same data
        sample_tenant = get_tenant(self.all_tenants[0])
        overall_query = f"""
        SELECT
            COUNT(*) as total_requests,
            COUNT(DISTINCT user_id) as unique_users,
            MIN(time) as earliest_request,
            MAX(time) as latest_request,
            COUNT(DISTINCT tenant_id) as active_tenants
        FROM `{sample_tenant.project_id}.{sample_tenant.search_dataset_name}.request_metadata`
        WHERE ({where_clause})
            AND request_type = 'CHAT'
        """

        try:
            overall_results = self.bq_client.query(overall_query).result()
            for row in overall_results:
                self.results["total_requests"] = row.total_requests
                self.results["unique_users"] = row.unique_users
                self.results["earliest_request"] = row.earliest_request
                self.results["latest_request"] = row.latest_request
                self.results["active_tenants"] = row.active_tenants

                logger.info(f"Total requests: {row.total_requests:,}")
                logger.info(f"Unique users: {row.unique_users:,}")
                logger.info(
                    f"Date range: {row.earliest_request} to {row.latest_request}"
                )
                logger.info(f"Active tenants: {row.active_tenants}")
        except Exception as e:
            logger.error(f"Failed to query overall stats: {e}")

        # Query 2: Distribution by tenant
        tenant_query = f"""
        SELECT
            tenant_id,
            COUNT(*) as request_count,
            COUNT(DISTINCT user_id) as user_count
        FROM `{sample_tenant.project_id}.{sample_tenant.search_dataset_name}.request_metadata`
        WHERE ({where_clause})
            AND request_type = 'CHAT'
        GROUP BY tenant_id
        ORDER BY request_count DESC
        """

        try:
            tenant_results = self.bq_client.query(tenant_query).result()
            tenant_dist = {}
            logger.info("\nRequests by tenant:")
            for row in tenant_results:
                tenant_dist[row.tenant_id] = {
                    "requests": row.request_count,
                    "users": row.user_count,
                }
                # Find tenant name
                tenant_name = next(
                    (
                        t
                        for t in self.all_tenants
                        if get_tenant(t).tenant_id == row.tenant_id
                    ),
                    "unknown",
                )
                logger.info(
                    f"  {tenant_name}: {row.request_count:,} requests, {row.user_count} users"
                )
            self.results["tenant_distribution"] = tenant_dist
        except Exception as e:
            logger.error(f"Failed to query tenant distribution: {e}")

        # Query 3: Time distribution (monthly)
        time_query = f"""
        SELECT
            DATE_TRUNC(time, MONTH) as month,
            COUNT(*) as request_count
        FROM `{sample_tenant.project_id}.{sample_tenant.search_dataset_name}.request_metadata`
        WHERE ({where_clause})
            AND request_type = 'CHAT'
        GROUP BY month
        ORDER BY month
        """

        try:
            time_results = self.bq_client.query(time_query).result()
            time_dist = {}
            logger.info("\nRequests by month:")
            for row in time_results:
                month_str = row.month.strftime("%Y-%m")
                time_dist[month_str] = row.request_count
                logger.info(f"  {month_str}: {row.request_count:,}")
            self.results["monthly_distribution"] = time_dist
        except Exception as e:
            logger.error(f"Failed to query time distribution: {e}")

    def analyze_workspace_diversity(self):
        """Analyze workspace and repository diversity using real data."""
        logger.info("\n=== WORKSPACE/REPOSITORY DIVERSITY ===")

        # First, get actual unique user counts and workspace estimates from BigQuery
        sample_tenant = get_tenant(self.all_tenants[0])

        # Query for actual workspace diversity metrics
        workspace_query = f"""
        WITH user_workspace_stats AS (
            SELECT
                tenant_id,
                user_id,
                COUNT(DISTINCT request_id) as request_count,
                MIN(time) as first_request,
                MAX(time) as last_request
            FROM `{sample_tenant.project_id}.{sample_tenant.search_dataset_name}.request_metadata`
            WHERE request_type = 'CHAT'
                AND tenant_id IN ({','.join([f"'{get_tenant(t).tenant_id}'" for t in self.all_tenants])})
            GROUP BY tenant_id, user_id
        )
        SELECT
            COUNT(DISTINCT CONCAT(tenant_id, '_', user_id)) as unique_workspaces,
            AVG(request_count) as avg_requests_per_workspace,
            MAX(request_count) as max_requests_per_workspace,
            APPROX_QUANTILES(request_count, 100)[OFFSET(50)] as median_requests,
            APPROX_QUANTILES(request_count, 100)[OFFSET(95)] as p95_requests
        FROM user_workspace_stats
        """

        try:
            workspace_results = self.bq_client.query(workspace_query).result()
            for row in workspace_results:
                self.results["actual_workspace_count"] = row.unique_workspaces
                self.results["avg_requests_per_workspace"] = (
                    row.avg_requests_per_workspace
                )
                self.results["max_requests_per_workspace"] = (
                    row.max_requests_per_workspace
                )
                self.results["median_requests_per_workspace"] = row.median_requests
                self.results["p95_requests_per_workspace"] = row.p95_requests

                logger.info("\nActual workspace statistics:")
                logger.info(
                    f"  Unique workspaces (tenant_user combinations): {row.unique_workspaces:,}"
                )
                logger.info(
                    f"  Average requests per workspace: {row.avg_requests_per_workspace:.1f}"
                )
                logger.info(f"  Median requests per workspace: {row.median_requests}")
                logger.info(f"  95th percentile requests: {row.p95_requests}")
        except Exception as e:
            logger.error(f"Failed to query workspace stats: {e}")

        # Now sample some requests to analyze blob counts
        # We'll sample more aggressively since we want real data
        sample_size_per_tenant = 100
        blob_count_distribution = []
        unique_blob_ids = set()
        all_workspace_keys = set()

        for tenant_name in self.all_tenants[:8]:  # Sample first 8 tenants (half)
            try:
                tenant = get_tenant(tenant_name)
                logger.info(f"\nAnalyzing blob counts from {tenant_name}...")

                # Query sample requests from last 3 months for relevance
                sample_query = f"""
                SELECT request_id, tenant_id, user_id, time
                FROM `{tenant.project_id}.{tenant.search_dataset_name}.request_metadata`
                WHERE tenant_id = '{tenant.tenant_id}'
                    AND request_type = 'CHAT'
                    AND time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 90 DAY)
                ORDER BY RAND()
                LIMIT {sample_size_per_tenant}
                """

                results = self.bq_client.query(sample_query).result()

                for row in results:
                    # Get chat request
                    request = get_chat_request_from_gcs(
                        tenant, row.tenant_id, row.request_id, self.credentials
                    )

                    if request and request.blob_names:
                        # Track blob counts
                        blob_count = len(request.blob_names)
                        blob_count_distribution.append(blob_count)

                        # Track unique blobs (sample first 10)
                        unique_blob_ids.update(request.blob_names[:10])

                        # Estimate workspace key (simplified)
                        workspace_key = f"{tenant_name}_{row.user_id[:8]}"
                        all_workspace_keys.add(workspace_key)

            except Exception as e:
                logger.error(f"Failed to sample from {tenant_name}: {e}")

        # Analyze results
        if blob_count_distribution:
            self.results["workspace_count_estimate"] = len(all_workspace_keys)
            self.results["blob_count_stats"] = {
                "min": min(blob_count_distribution),
                "max": max(blob_count_distribution),
                "mean": np.mean(blob_count_distribution),
                "median": np.median(blob_count_distribution),
                "p95": np.percentile(blob_count_distribution, 95),
                "p99": np.percentile(blob_count_distribution, 99),
            }

            logger.info(f"\nEstimated unique workspaces: {len(all_workspace_keys)}")
            logger.info("Blob count statistics:")
            logger.info(f"  Min: {self.results['blob_count_stats']['min']}")
            logger.info(f"  Max: {self.results['blob_count_stats']['max']}")
            logger.info(f"  Mean: {self.results['blob_count_stats']['mean']:.1f}")
            logger.info(f"  Median: {self.results['blob_count_stats']['median']:.1f}")
            logger.info(
                f"  95th percentile: {self.results['blob_count_stats']['p95']:.1f}"
            )

            # Get more accurate blob statistics
            total_sampled = len(blob_count_distribution)
            unique_sampled = len(unique_blob_ids)

            # Query for total blob count estimate
            # blob_estimate_query = f"""
            # SELECT
            #     COUNT(DISTINCT user_id) as unique_users_with_data,
            #     AVG(CAST(JSON_EXTRACT_SCALAR(event_data, '$.blob_count') AS INT64)) as avg_blobs_per_request
            # FROM `{sample_tenant.project_id}.{sample_tenant.search_dataset_name}.request_metadata`
            # WHERE request_type = 'CHAT'
            #     AND time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 90 DAY)
            # """

            # Since we sampled 8/16 tenants with 100 requests each
            sampling_rate = (8 * sample_size_per_tenant) / self.results.get(
                "total_requests", 1
            )
            estimated_unique_blobs = int(unique_sampled / sampling_rate)
            self.results["estimated_unique_blobs"] = estimated_unique_blobs
            self.results["sampling_stats"] = {
                "requests_sampled": total_sampled,
                "unique_blobs_in_sample": unique_sampled,
                "sampling_rate": sampling_rate,
            }

            logger.info("\nBlob statistics:")
            logger.info(f"  Sampled {total_sampled} requests")
            logger.info(f"  Found {unique_sampled} unique blobs in sample")
            logger.info(f"  Sampling rate: {sampling_rate:.2%}")
            logger.info(f"  Estimated total unique blobs: {estimated_unique_blobs:,}")

    def analyze_data_quality(self):
        """Analyze data quality through sampling."""
        logger.info("\n=== DATA QUALITY ANALYSIS ===")

        # Sample for quality analysis
        quality_sample_size = 100
        success_count = 0
        message_lengths = []
        failed_retrievals = []

        # Sample from a recent time period
        sample_tenant = get_tenant("i0-vanguard0")
        quality_query = f"""
        SELECT request_id, tenant_id, user_id, time
        FROM `{sample_tenant.project_id}.{sample_tenant.search_dataset_name}.request_metadata`
        WHERE tenant_id = '{sample_tenant.tenant_id}'
            AND request_type = 'CHAT'
            AND time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
        ORDER BY RAND()
        LIMIT {quality_sample_size}
        """

        try:
            results = self.bq_client.query(quality_query).result()
            total_sampled = 0

            for row in results:
                total_sampled += 1
                request = get_chat_request_from_gcs(
                    sample_tenant, row.tenant_id, row.request_id, self.credentials
                )

                if request:
                    success_count += 1
                    if request.message:
                        message_lengths.append(len(request.message))
                else:
                    failed_retrievals.append(row.request_id)

            # Calculate quality metrics
            if total_sampled > 0:
                success_rate = success_count / total_sampled
                self.results["data_quality"] = {
                    "retrieval_success_rate": success_rate,
                    "failed_count": len(failed_retrievals),
                    "message_length_stats": {
                        "min": min(message_lengths) if message_lengths else 0,
                        "max": max(message_lengths) if message_lengths else 0,
                        "mean": np.mean(message_lengths) if message_lengths else 0,
                    },
                }

                logger.info("\nData quality metrics:")
                logger.info(f"  Retrieval success rate: {success_rate:.1%}")
                logger.info(f"  Failed retrievals: {len(failed_retrievals)}")
                if message_lengths:
                    logger.info(
                        f"  Message length - Min: {min(message_lengths)}, Max: {max(message_lengths)}, Avg: {np.mean(message_lengths):.0f}"
                    )

        except Exception as e:
            logger.error(f"Failed to analyze data quality: {e}")

    def estimate_conversion_potential(self):
        """Estimate the potential size of converted dataset using real statistics."""
        logger.info("\n=== CONVERSION POTENTIAL ESTIMATION ===")

        if "total_requests" not in self.results:
            logger.error("Missing total requests data")
            return

        # Use real data from our analysis
        # total_requests = self.results["total_requests"]
        actual_workspaces = self.results.get("actual_workspace_count", 0)
        avg_requests_per_workspace = self.results.get("avg_requests_per_workspace", 50)

        # File statistics from sampling
        blob_stats = self.results.get("blob_count_stats", {})
        median_blobs = blob_stats.get("median", 100)

        # After filtering (cap at 30 files, remove cache dirs)
        # Estimate 70% of files will be filtered out based on our analysis
        avg_files_after_filtering = min(30, median_blobs * 0.3)

        # Query for actual file sizes from a sample
        # sample_tenant = get_tenant(self.all_tenants[0])
        # file_size_query = f"""
        # WITH recent_requests AS (
        #     SELECT request_id
        #     FROM `{sample_tenant.project_id}.{sample_tenant.search_dataset_name}.request_metadata`
        #     WHERE request_type = 'CHAT'
        #         AND time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
        #     LIMIT 100
        # )
        # SELECT
        #     AVG(CAST(JSON_EXTRACT_SCALAR(event_data, '$.avg_file_size_bytes') AS FLOAT64)) as avg_file_size,
        #     COUNT(*) as sample_count
        # FROM `{sample_tenant.project_id}.{sample_tenant.analytics_dataset_name}.chat_host_request` chr
        # JOIN recent_requests r ON chr.request_id = r.request_id
        # WHERE JSON_EXTRACT_SCALAR(event_data, '$.avg_file_size_bytes') IS NOT NULL
        # """

        avg_file_size_kb = 5  # Default
        # try:
        #     # This query might not work due to schema differences, so we'll use defaults if it fails
        #     pass
        # except:
        #     logger.info("Using default file size estimate")

        # Calculate estimates based on real data
        estimated_repositories = actual_workspaces
        estimated_questions = int(
            actual_workspaces * avg_requests_per_workspace * 0.8
        )  # 80% will have valid data
        estimated_file_associations = int(
            estimated_questions * avg_files_after_filtering
        )
        estimated_size_gb = (estimated_file_associations * avg_file_size_kb) / (
            1024 * 1024
        )

        self.results["conversion_estimates"] = {
            "repositories": estimated_repositories,
            "questions": estimated_questions,
            "file_associations": estimated_file_associations,
            "size_gb": estimated_size_gb,
        }

        logger.info("\nConversion estimates (with 30-file limit and filtering):")
        logger.info(f"  Estimated repositories: {estimated_repositories:,}")
        logger.info(f"  Estimated questions: {estimated_questions:,}")
        logger.info(f"  Estimated file associations: {estimated_file_associations:,}")
        logger.info(f"  Estimated dataset size: {estimated_size_gb:.1f} GB")

        # Compare with binks5
        logger.info("\nComparison with binks5:")
        logger.info("  Binks5 has ~100K repositories with ~900K questions")
        logger.info(f"  Vanguard potential: {estimated_questions:,} questions")
        logger.info(
            f"  This represents {estimated_questions/900000:.0%} of binks5 size"
        )

        # Recommendations
        logger.info("\nRecommendations:")
        logger.info("  - Focus on recent 3-6 months of data for freshness")
        logger.info("  - Target tenants with highest activity")
        logger.info("  - Apply 30-file limit to match binks5 distribution")
        logger.info("  - Filter out cache/build directories")

    def generate_summary_report(self):
        """Generate final summary report."""
        logger.info("\n" + "=" * 60)
        logger.info("VANGUARD DATA INVENTORY SUMMARY")
        logger.info("=" * 60)

        summary = f"""
Total Chat Requests: {self.results.get('total_requests', 'N/A'):,}
Unique Users: {self.results.get('unique_users', 'N/A'):,}
Date Range: {self.results.get('earliest_request', 'N/A')} to {self.results.get('latest_request', 'N/A')}
Active Tenants: {self.results.get('active_tenants', 'N/A')}

Workspace Diversity:
  Actual Unique Workspaces: {self.results.get('actual_workspace_count', 'N/A'):,}
  Average Requests per Workspace: {self.results.get('avg_requests_per_workspace', 'N/A'):.1f}
  Median Blob Count per Request: {self.results.get('blob_count_stats', {}).get('median', 'N/A')}
  Estimated Unique Blobs: {self.results.get('estimated_unique_blobs', 'N/A'):,}

Data Quality:
  Retrieval Success Rate: {self.results.get('data_quality', {}).get('retrieval_success_rate', 0):.1%}

Conversion Potential (with filtering):
  Estimated Questions: {self.results.get('conversion_estimates', {}).get('questions', 'N/A'):,}
  Estimated File Associations: {self.results.get('conversion_estimates', {}).get('file_associations', 'N/A'):,}
  Estimated Dataset Size: {self.results.get('conversion_estimates', {}).get('size_gb', 'N/A'):.1f} GB
  Percentage of Binks5 Size: {self.results.get('conversion_estimates', {}).get('questions', 0)/900000:.1%}

Key Insights:
1. Vanguard contains {self.results.get('total_requests', 0):,} real developer interactions
2. Median request has {self.results.get('blob_count_stats', {}).get('median', 'many')} files (needs filtering to ~30)
3. Dataset represents significant real-world usage from {self.results.get('unique_users', 0):,} developers
4. Recent months show strong growth: {self.results.get('monthly_distribution', {}).get('2025-05', 0):,} requests in May 2025
"""
        logger.info(summary)

        # Save detailed results
        with open("vanguard_inventory_results.json", "w") as f:
            json.dump(self.results, f, indent=2, default=str)
        logger.info("\nDetailed results saved to vanguard_inventory_results.json")


def main():
    """Run the inventory analysis."""
    inventory = VanguardDataInventory()
    inventory.run_full_inventory()


if __name__ == "__main__":
    main()
