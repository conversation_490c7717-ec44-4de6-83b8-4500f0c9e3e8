#!/usr/bin/env python3
"""Data structures for the modular Vanguard to Binks conversion pipeline.

This module defines intermediate data structures used between different stages
of the modular pipeline for better parallelization and debugging.
"""

from dataclasses import dataclass
from typing import Optional
from dataclasses_json import DataClassJsonMixin


@dataclass
class VanguardSingleRequest(DataClassJsonMixin):
    """Input data structure for single request processing.

    Contains only the essential fields needed for processing a single request.
    """

    tenant_name: str
    tenant_id: str
    request_id: str


@dataclass
class FilteredVanguardSingleRequest(DataClassJsonMixin):
    """Single Vanguard request that has been filtered by PleaseHold.

    All requests at this stage are codebase-related (non-codebase requests
    are filtered out by the classification actor).
    """

    tenant_name: str
    request_id: str  # Extracted from request_row for direct access


@dataclass
class PipelineConfig(DataClassJsonMixin):
    """Initial configuration for the pipeline."""

    tenant_names: list[str]
    date_from: str  # ISO format YYYY-MM-DD
    date_to: str  # ISO format YYYY-MM-DD
    limit: Optional[int] = None


@dataclass
class TenantDateRange(DataClassJsonMixin):
    """Input for BigQuery fetching - represents a chunk of work.

    This structure allows splitting BigQuery queries by tenant and date range
    for parallel processing.
    """

    tenant_name: str
    tenant_id: str
    date_from: str  # ISO format YYYY-MM-DD
    date_to: str  # ISO format YYYY-MM-DD
    limit: Optional[int] = None

    def __str__(self) -> str:
        return f"{self.tenant_name}[{self.date_from} to {self.date_to}]"


@dataclass
class RequestWithBlobContents(DataClassJsonMixin):
    """Intermediate result after blob fetching.

    Contains the original request information along with fetched blob contents
    from GCS. This separates I/O-intensive blob fetching from CPU-intensive
    processing.
    """

    # Request identification
    tenant_name: str
    request_id: str

    # Chat request details
    message: str

    # Fetched blob contents
    blob_contents: dict[str, dict]  # blob_id -> {path, content, size, hex_id}


@dataclass
class ProcessedFile(DataClassJsonMixin):
    """Processed file information after language detection."""

    blob_id: str
    hex_id: str
    path: str
    content: str
    size: int
    language: str
    langpart: str  # Language partition for Binks
    extension: Optional[str]


@dataclass
class LanguageStats(DataClassJsonMixin):
    """Language statistics for a repository."""

    file_count: int
    total_size: int
    percentage_of_files: float
    percentage_of_size: float


@dataclass
class ProcessedRequest(DataClassJsonMixin):
    """Intermediate result after file processing.

    Contains processed files with language detection and statistics.
    This is the input to the final repository assembly stage.
    """

    # Request metadata
    tenant_name: str
    request_id: str
    message: str

    # Processed files
    files: list[ProcessedFile]

    # Aggregate statistics
    total_size: int
    language_stats: dict[str, LanguageStats]

    # Repository metadata
    workspace_key: str
    max_file_language: str
    max_size_language: str


@dataclass
class PipelineMetrics(DataClassJsonMixin):
    """Metrics for monitoring pipeline performance."""

    stage_name: str
    actor_id: str
    start_time: str  # ISO format
    end_time: str  # ISO format
    items_processed: int
    items_succeeded: int
    items_failed: int
    average_processing_time_ms: float
    errors: Optional[list[str]] = None


@dataclass
class BatchProcessingResult(DataClassJsonMixin):
    """Result of processing a batch of items through an actor."""

    input_count: int
    output_count: int
    failed_count: int
    processing_time_seconds: float
    error_messages: Optional[list[str]] = None


__all__ = [
    # New data structures
    "PipelineConfig",
    "TenantDateRange",
    "RequestWithBlobContents",
    "ProcessedFile",
    "LanguageStats",
    "ProcessedRequest",
    "PipelineMetrics",
    "BatchProcessingResult",
    # Re-exported structures
    "VanguardSingleRequest",
    "FilteredVanguardSingleRequest",
]
