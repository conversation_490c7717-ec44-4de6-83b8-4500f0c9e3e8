#!/usr/bin/env python3
"""Blob content fetching actor for the modular Vanguard to Binks pipeline.

This module provides an actor for parallel GCS blob fetching, separating
I/O-intensive operations from CPU-intensive processing.
"""

import base64
import logging
from typing import Optional, Any
import google.auth
from google.cloud import storage
from google.oauth2 import service_account

from research.data.ray.ray_utils import AbstractRayActor
from base.datasets.tenants import get_tenant
from base.datasets.gcs_blob_cache import GCSBlobCache
from experimental.zheren.data.vanguard_data_utils import (
    get_chat_request_from_gcs,
)
from experimental.zheren.data.vanguard_to_binks_modular_data_structures import (
    FilteredVanguardSingleRequest,
    RequestWithBlobContents,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class BlobContentFetcherActor(
    AbstractRayActor[FilteredVanguardSingleRequest, RequestWithBlobContents]
):
    """Fetches blob contents from GCS with caching.

    This actor handles the I/O-intensive operation of fetching blob contents
    from GCS, utilizing caching to improve performance for repeated accesses.
    """

    def __init__(
        self,
        blob_cache_gb: float = 1.0,
        service_account_json: Optional[str] = None,
    ):
        """Initialize the blob fetcher actor.

        Args:
            blob_cache_gb: Size of blob cache in GB
            service_account_json: Path to service account JSON file
        """
        super().__init__(
            input_cls=FilteredVanguardSingleRequest, output_cls=RequestWithBlobContents
        )

        # Initialize credentials
        if service_account_json:
            self.credentials = service_account.Credentials.from_service_account_file(
                service_account_json
            )
        else:
            self.credentials = google.auth.default()[0]

        # Cache configuration
        self.blob_cache_size_bytes = int(blob_cache_gb * 2**30)

        # Tenant and cache management
        self._tenant_cache = {}
        self._blob_cache = {}

        logger.info(f"Initialized BlobContentFetcherActor with {blob_cache_gb}GB cache")

    def _get_or_create_blob_cache(self, tenant_name: str) -> tuple[Any, GCSBlobCache]:
        """Get or create tenant and blob cache."""
        if tenant_name not in self._tenant_cache:
            tenant = get_tenant(tenant_name)
            self._tenant_cache[tenant_name] = tenant

            # Create storage client and bucket
            storage_client = storage.Client(
                project=tenant.project_id, credentials=self.credentials
            )
            blob_bucket = storage_client.bucket(tenant.blob_bucket_name)

            # Create blob cache for this tenant
            self._blob_cache[tenant_name] = GCSBlobCache(
                bucket=blob_bucket,
                bucket_prefix=tenant.blob_bucket_prefix,
                max_size_bytes=self.blob_cache_size_bytes,
                num_threads=32,  # Default from other implementations
            )
            logger.info(f"Created blob cache for tenant {tenant_name}")

        return self._tenant_cache[tenant_name], self._blob_cache[tenant_name]

    def _transform_blob_id(self, blob_id: str) -> str:
        """Transform blob ID to hex format for GCS lookup.

        Blob IDs can be in two formats:
        1. Already hex-encoded (64 chars, 0-9a-f)
        2. Base64-encoded (needs to be decoded to hex)
        """
        # Check if it's already a hex string
        if len(blob_id) == 64 and all(c in "0123456789abcdef" for c in blob_id.lower()):
            return blob_id

        # Otherwise, try to decode from base64
        try:
            blob_bytes = base64.b64decode(blob_id)
            return blob_bytes.hex()
        except Exception as e:
            logger.warning(f"Failed to transform blob ID {blob_id}: {e}")
            return blob_id

    def process(
        self, row: FilteredVanguardSingleRequest
    ) -> list[RequestWithBlobContents]:
        """Fetch blob contents for a filtered request.

        Args:
            row: FilteredVanguardSingleRequest with classification results

        Returns:
            List containing RequestWithBlobContents with fetched blobs
        """
        tenant, blob_cache = self._get_or_create_blob_cache(row.tenant_name)

        # Fetch chat request details from GCS
        chat_request = get_chat_request_from_gcs(
            tenant,
            tenant.tenant_id,  # Use tenant_id from tenant object
            row.request_id,
            self.credentials,
        )

        if not chat_request or not chat_request.blob_names:
            logger.warning(
                f"No valid chat request or blobs found for request {row.request_id}"
            )
            return []

        # Transform blob IDs to hex format
        hex_blob_ids = []
        original_to_hex = {}
        for blob_id in chat_request.blob_names:
            hex_id = self._transform_blob_id(blob_id)
            hex_blob_ids.append(hex_id)
            original_to_hex[blob_id] = hex_id

        logger.debug(
            f"Request {row.request_id} has {len(chat_request.blob_names)} blobs"
        )

        # Fetch all blobs at once (GCSBlobCache handles parallelization internally)
        blob_contents = {}
        successful_fetches = 0
        failed_fetches = 0

        blob_results = blob_cache.get(hex_blob_ids)

        for hex_id, result in zip(hex_blob_ids, blob_results):
            if result:
                successful_fetches += 1
                # Find original blob ID
                original_id = None
                for orig, hx in original_to_hex.items():
                    if hx == hex_id:
                        original_id = orig
                        break

                if original_id:
                    blob_contents[original_id] = {
                        "blob_id": hex_id,  # Use hex_id as blob_id to match original implementation
                        "path": str(result.path),
                        "content": result.content,
                        "size": len(result.content.encode("utf-8")),
                    }
            else:
                failed_fetches += 1
                logger.debug(f"Failed to fetch blob {hex_id}")

        # Log the statistics for monitoring/debugging
        total_blobs = len(chat_request.blob_names)
        logger.info(
            f"Fetched blobs for request {row.request_id}: "
            f"{successful_fetches} successful, {failed_fetches} failed out of {total_blobs} total"
        )

        # Create result object with only necessary fields
        result = RequestWithBlobContents(
            tenant_name=row.tenant_name,
            request_id=row.request_id,
            message=chat_request.message,
            blob_contents=blob_contents,
        )

        return [result]
