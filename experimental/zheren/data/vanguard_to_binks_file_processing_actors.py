#!/usr/bin/env python3
"""File processing actors for the modular Vanguard to Binks pipeline.

This module provides actors for CPU-intensive file processing operations
including language detection, validation, and repository assembly.
"""

import logging
import uuid
from collections import defaultdict
from pathlib import Path
import hashlib

from research.data.ray.ray_utils import AbstractRayActor
from base.languages import guess_language
from experimental.tongfei.data.binks_schemas import (
    Repository,
    File,
    DocumentWithQuestionsV2,
)
from experimental.zheren.data.vanguard_to_binks_modular_data_structures import (
    RequestWithBlobContents,
    ProcessedRequest,
    ProcessedFile,
    LanguageStats,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class FileProcessorActor(AbstractRayActor[RequestWithBlobContents, ProcessedRequest]):
    """Processes files for language detection and metadata extraction.

    This actor handles CPU-intensive operations like language detection,
    file validation, and statistics calculation.
    """

    def __init__(self):
        """Initialize the file processor actor."""
        super().__init__(input_cls=RequestWithBlobContents, output_cls=ProcessedRequest)

        logger.info("Initialized FileProcessorActor")

    def _extract_workspace_key(self, paths: list[str]) -> str:
        """Extract a workspace identifier from file paths."""
        if not paths:
            return "unknown"

        # Try to find common prefix or use hash of paths
        path_str = "|".join(sorted(paths))
        return hashlib.md5(path_str.encode()).hexdigest()[:8]

    def _process_file(self, blob_id: str, blob_data: dict) -> ProcessedFile:
        """Process a single file with language detection."""
        path = blob_data.get("path", "")
        content = blob_data.get("content", "")
        size = blob_data.get("size", 0)

        # Detect language
        language_info = guess_language(path)
        language = language_info if language_info else "unknown"
        langpart = language_info if language_info else "unknown"

        # Extract extension
        extension = Path(path).suffix.lstrip(".") if path else None
        if not extension:
            extension = "txt"

        return ProcessedFile(
            blob_id=blob_id,  # This is the original blob_id (key in blob_contents)
            hex_id=blob_data.get(
                "blob_id", blob_id
            ),  # blob_data["blob_id"] is actually hex_id
            path=path,
            content=content,
            size=size,
            language=language,
            langpart=langpart,
            extension=extension,
        )

    def process(self, row: RequestWithBlobContents) -> list[ProcessedRequest]:
        """Process blob contents to extract file metadata and statistics.

        Args:
            row: RequestWithBlobContents with fetched blobs

        Returns:
            List containing ProcessedRequest with file metadata
        """
        if not row.blob_contents:
            logger.warning(f"No blob contents to process for request {row.request_id}")
            return []

        # Process each file
        processed_files = []
        lang_counts = defaultdict(int)
        lang_sizes = defaultdict(int)

        for blob_id, blob_data in row.blob_contents.items():
            processed_file = self._process_file(blob_id, blob_data)
            processed_files.append(processed_file)

            # Count all files for statistics
            lang = processed_file.langpart  # Use langpart, not language
            lang_counts[lang] += 1
            lang_sizes[lang] += processed_file.size

        # Calculate total size
        total_size = sum(f.size for f in processed_files)
        total_files = len(processed_files)

        # Calculate language statistics
        language_stats = {}
        for lang, count in lang_counts.items():
            language_stats[lang] = LanguageStats(
                file_count=count,
                total_size=lang_sizes[lang],
                percentage_of_files=(count / total_files * 100)
                if total_files > 0
                else 0,
                percentage_of_size=(lang_sizes[lang] / total_size * 100)
                if total_size > 0
                else 0,
            )

        # Find max language by file count and size
        max_file_lang = max(
            lang_counts.items(), key=lambda x: x[1], default=("unknown", 0)
        )[0]
        max_size_lang = max(
            lang_sizes.items(), key=lambda x: x[1], default=("unknown", 0)
        )[0]

        # Extract workspace key
        paths = [f.path for f in processed_files if f.path]
        workspace_key = self._extract_workspace_key(paths)

        # Log statistics for monitoring/debugging
        logger.info(
            f"Processed request {row.request_id}: {total_files} files, "
            f"{total_size} bytes total"
        )

        # Create result with only necessary fields
        result = ProcessedRequest(
            tenant_name=row.tenant_name,
            request_id=row.request_id,
            message=row.message,
            files=processed_files,
            total_size=total_size,
            language_stats=language_stats,
            workspace_key=workspace_key,
            max_file_language=max_file_lang,
            max_size_language=max_size_lang,
        )

        return [result]


class RepositoryAssemblerActor(AbstractRayActor[ProcessedRequest, Repository]):
    """Assembles final Repository objects in Binks format.

    This actor performs the final formatting and validation to create
    Repository objects compatible with the Binks schema.
    """

    def __init__(self):
        """Initialize the repository assembler actor."""
        super().__init__(input_cls=ProcessedRequest, output_cls=Repository)

        logger.info("Initialized RepositoryAssemblerActor")

    def _create_file_object(
        self, processed_file: ProcessedFile, repo_name: str
    ) -> File:
        """Create a Binks File object from processed file data."""
        # Calculate file statistics
        lines = processed_file.content.splitlines()
        line_lengths = [len(line) for line in lines]

        avg_line_length = sum(line_lengths) / len(line_lengths) if line_lengths else 0.0
        max_line_length = max(line_lengths) if line_lengths else 0

        # Calculate alphanum fraction
        alphanum_chars = sum(1 for c in processed_file.content if c.isalnum())
        total_chars = len(processed_file.content)
        alphanum_fraction = alphanum_chars / total_chars if total_chars > 0 else 0.0

        # Create File object with all required fields
        # For Vanguard data, we use the same values for all repo variants
        return File(
            hexsha=processed_file.hex_id,
            size=processed_file.size,
            ext=processed_file.extension,
            max_stars_repo_path=processed_file.path,
            max_stars_repo_name=repo_name,
            max_stars_repo_licenses=[],  # No license info available
            max_stars_count=None,
            max_stars_repo_stars_event_min_datetime=None,
            max_stars_repo_stars_event_max_datetime=None,
            max_issues_repo_path=processed_file.path,
            max_issues_repo_name=repo_name,
            max_issues_repo_licenses=[],
            max_issues_count=None,
            max_issues_repo_issues_event_min_datetime=None,
            max_issues_repo_issues_event_max_datetime=None,
            max_forks_repo_path=processed_file.path,
            max_forks_repo_name=repo_name,
            max_forks_repo_licenses=[],
            max_forks_count=None,
            max_forks_repo_forks_event_min_datetime=None,
            max_forks_repo_forks_event_max_datetime=None,
            content=processed_file.content,
            avg_line_length=avg_line_length,
            max_line_length=max_line_length,
            alphanum_fraction=alphanum_fraction,
            langpart=processed_file.langpart,
        )

    def process(self, row: ProcessedRequest) -> list[Repository]:
        """Assemble a Repository object from processed request data.

        Args:
            row: ProcessedRequest with processed files and statistics

        Returns:
            List containing Repository object (empty if insufficient valid files)
        """
        # Check if we have any files
        if not row.files:
            logger.warning(f"Request {row.request_id} has no files to process")
            return []

        # Generate repository name
        repo_name = (
            f"vanguard_{row.tenant_name}_{row.workspace_key}_{row.request_id[:8]}"
        )
        repo_uuid = str(uuid.uuid4())

        # Create File objects for all files
        file_list = []
        for processed_file in row.files:
            file_obj = self._create_file_object(processed_file, repo_name)
            file_list.append(file_obj)

        # Create DocumentWithQuestionsV2 object
        paths = [f.path for f in row.files if f.path]
        document = DocumentWithQuestionsV2(
            question=row.message,
            paths=paths,
            answer="",  # Empty answer for retrieval training
        )

        # Create language statistics for Binks format
        # max_file_lang and max_size_lang should contain ONLY the language with max count/size
        # not all languages - this matches the original implementation
        max_file_lang = {
            "file_count": row.language_stats[row.max_file_language].file_count
            if row.max_file_language in row.language_stats
            else 0,
            "langpart": row.max_file_language,
        }
        max_size_lang = {
            "total_size": row.language_stats[row.max_size_language].total_size
            if row.max_size_language in row.language_stats
            else 0,
            "langpart": row.max_size_language,
        }

        # Create Repository object
        repository = Repository(
            max_stars_repo_name=repo_name,
            max_file_lang=max_file_lang,
            max_size_lang=max_size_lang,
            total_size=row.total_size,
            file_list=file_list,
            documents_with_questions=[document],
            repo_uuid=repo_uuid,
        )

        logger.info(
            f"Created repository {repo_name} with {len(file_list)} files "
            f"for request {row.request_id}"
        )

        return [repository]
