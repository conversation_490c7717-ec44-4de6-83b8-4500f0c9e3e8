# Chat Request Data

Central RI proto:

- `services/request_insight/request_insight.proto`

```json
message RIChatRequest {
  chat.ChatRequest request = 1;
  ...
```

ChatRequest:

- `services/chat_host/chat.proto`

```json
message ChatRequest {
  string model_name = 1;
  ...
```

- User input:
    - `string message = 6 [debug_redact = true];`
- User workspace:
    - `repeated base.blob_names.Blobs blobs = 12;`
- Also includes opened file, selected code, chat history, etc

To get chat request IDs:

- BigQuery table:
    - `system-services-prod.us_prod_request_insight_search_nonenterprise_dataset.request_metadata`
- Example query:
    - `research/vanguard/license_filter/get_request_ids_from_search_db.py`
        - [fetch_request_ids_on_bq](https://github.com/augmentcode/augment/blob/d2f3a4062510eb8301c96d5d3004be39669d8dfa/research/vanguard/license_filter/get_request_ids_from_search_db.py#L64)

To get blob IDs:

- Each request is saved as a single object on GCS
- Example code:
    - `research/vanguard/license_filter/get_blob_licenses.py`
    - [get_request_event_field_udf](https://github.com/augmentcode/augment/blob/d2f3a4062510eb8301c96d5d3004be39669d8dfa/research/vanguard/license_filter/get_blob_licenses.py#L178)

To get blob data (file content):

- Each blob (only in certain tenants like vanguard) is also saved as a single object on GCS
- Example code:
    - `research/vanguard/license_filter/get_blob_licenses.py`
        - [get_license_info](https://github.com/augmentcode/augment/blob/d2f3a4062510eb8301c96d5d3004be39669d8dfa/research/vanguard/license_filter/get_blob_licenses.py#L323)
    - `research/vanguard/license_filter/utils.py`
        - get_storage_blob
        - get_blob_file_name
