#!/usr/bin/env python3
"""Test script for the modular Vanguard to Binks pipeline.

This script tests the modular pipeline with a small dataset to verify
all components work correctly.
"""

import subprocess
import sys
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import shutil


def run_test():
    """Run a test of the modular pipeline."""
    # Test parameters - use dates that likely have data
    # Using dates from early 2024 which should have dogfood data
    date_from = "2024-01-01"
    date_to = "2024-01-03"

    # Create temporary output directory
    temp_dir = tempfile.mkdtemp(prefix="test_modular_pipeline_")
    output_path = Path(temp_dir) / "output"
    output_path.mkdir(exist_ok=True)

    print("Running modular pipeline test...")
    print(f"Date range: {date_from} to {date_to}")
    print(f"Output directory: {output_path}")

    # Build command
    cmd = [
        sys.executable,
        "experimental/zheren/data/vanguard_to_binks_modular_pipeline.py",
        "--date-from",
        date_from,
        "--date-to",
        date_to,
        "--output-path",
        str(output_path),
        "--mode",
        "local",
        "--limit",
        "10",  # Small limit for testing
        "--tenants",
        "dogfood",  # Single tenant for testing
        "--chunk-days",
        "3",  # Smaller chunks for testing
        "--keep-intermediate",  # Keep files for debugging
        # Use minimal workers in local mode
        "--num-bigquery-workers",
        "1",
        "--num-classifier-workers",
        "1",
        "--num-blob-workers",
        "1",
        "--num-processor-workers",
        "1",
        "--num-assembler-workers",
        "1",
    ]

    print(f"Running command: {' '.join(cmd)}")

    try:
        # Run the pipeline
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)

        print("\n=== STDOUT ===")
        print(result.stdout)

        if result.stderr:
            print("\n=== STDERR ===")
            print(result.stderr)

        # Check output
        output_files = list(output_path.glob("*.jsonl"))
        print(f"\nOutput files: {output_files}")

        if output_files:
            # Count records in output
            total_records = 0
            for output_file in output_files:
                with open(output_file, "r") as f:
                    count = sum(1 for _ in f)
                    total_records += count
                    print(f"{output_file.name}: {count} records")

            print(f"\nTotal output records: {total_records}")

            # Show sample output
            if total_records > 0:
                print("\nSample output (first record):")
                with open(output_files[0], "r") as f:
                    first_line = f.readline()
                    if len(first_line) > 200:
                        print(first_line[:200] + "...")
                    else:
                        print(first_line)

        print("\nTest completed successfully!")

    except subprocess.CalledProcessError as e:
        print(f"\nError running pipeline: {e}")
        print(f"Return code: {e.returncode}")
        print(f"\n=== STDOUT ===\n{e.stdout}")
        print(f"\n=== STDERR ===\n{e.stderr}")
        return 1

    finally:
        # Clean up
        print(f"\nCleaning up temporary directory: {temp_dir}")
        shutil.rmtree(temp_dir, ignore_errors=True)

    return 0


def run_comparison_test():
    """Run a comparison test between old and new pipelines."""
    print("\n" + "=" * 60)
    print("Running comparison test between old and new pipelines")
    print("=" * 60 + "\n")

    # Test parameters
    date_from = (datetime.now() - timedelta(days=3)).strftime("%Y-%m-%d")
    date_to = datetime.now().strftime("%Y-%m-%d")

    # Create temporary directories
    temp_dir = tempfile.mkdtemp(prefix="pipeline_comparison_")
    old_output = Path(temp_dir) / "old_pipeline"
    new_output = Path(temp_dir) / "new_pipeline"
    old_output.mkdir(exist_ok=True)
    new_output.mkdir(exist_ok=True)

    try:
        # Run old pipeline
        print("Running OLD pipeline...")
        old_cmd = [
            sys.executable,
            "experimental/zheren/data/vanguard_to_binks_single_request_with_pleasehold.py",
            "--date-from",
            date_from,
            "--date-to",
            date_to,
            "--output-path",
            str(old_output),
            "--mode",
            "local",
            "--limit",
            "5",
            "--tenants",
            "dogfood",
            "--two-stage",
        ]

        old_result = subprocess.run(old_cmd, capture_output=True, text=True)
        print(f"Old pipeline return code: {old_result.returncode}")

        # Run new pipeline
        print("\nRunning NEW modular pipeline...")
        new_cmd = [
            sys.executable,
            "experimental/zheren/data/vanguard_to_binks_modular_pipeline.py",
            "--date-from",
            date_from,
            "--date-to",
            date_to,
            "--output-path",
            str(new_output),
            "--mode",
            "local",
            "--limit",
            "5",
            "--tenants",
            "dogfood",
            "--chunk-days",
            "1",
        ]

        new_result = subprocess.run(new_cmd, capture_output=True, text=True)
        print(f"New pipeline return code: {new_result.returncode}")

        # Compare outputs
        print("\n" + "-" * 40)
        print("Comparison Results:")
        print("-" * 40)

        old_files = list(old_output.glob("*.jsonl"))
        new_files = list(new_output.glob("*.jsonl"))

        print(f"Old pipeline output files: {len(old_files)}")
        print(f"New pipeline output files: {len(new_files)}")

        # Count records
        old_count = sum(sum(1 for _ in open(f)) for f in old_files)
        new_count = sum(sum(1 for _ in open(f)) for f in new_files)

        print(f"Old pipeline records: {old_count}")
        print(f"New pipeline records: {new_count}")

        print("\nComparison test completed!")

    finally:
        # Clean up
        shutil.rmtree(temp_dir, ignore_errors=True)

    return 0


if __name__ == "__main__":
    print("Testing Modular Vanguard to Binks Pipeline")
    print("==========================================\n")

    # Run basic test
    ret_code = run_test()

    # Optionally run comparison test
    # Uncomment to compare with old pipeline
    # if ret_code == 0:
    #     ret_code = run_comparison_test()

    sys.exit(ret_code)
