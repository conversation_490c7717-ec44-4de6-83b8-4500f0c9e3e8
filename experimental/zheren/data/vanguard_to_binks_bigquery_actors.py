#!/usr/bin/env python3
"""BigQuery parallelization actors for the modular Vanguard to Binks pipeline.

This module provides actors for parallel BigQuery querying to address the
bottleneck of sequential tenant and date range processing.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional
import google.auth
from google.cloud import bigquery
from google.oauth2 import service_account

from research.data.ray.ray_utils import AbstractRayActor
from base.datasets.tenants import get_tenant, DATASET_TENANTS
from experimental.zheren.data.vanguard_to_binks_modular_data_structures import (
    PipelineConfig,
    TenantDateRange,
    VanguardSingleRequest,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class TenantDateRangeSplitActor(AbstractRayActor[PipelineConfig, TenantDateRange]):
    """Splits work into manageable chunks for parallel BigQuery processing.

    This actor takes a PipelineConfig and generates TenantDateRange
    objects that represent chunks of work for parallel processing.
    """

    def __init__(
        self,
        chunk_days: int = 7,
        max_requests_per_chunk: Optional[int] = None,
    ):
        """Initialize the splitter actor.

        Args:
            chunk_days: Number of days per chunk (default: 7)
            max_requests_per_chunk: Optional limit on requests per chunk
        """
        super().__init__(input_cls=PipelineConfig, output_cls=TenantDateRange)
        self.chunk_days = chunk_days
        self.max_requests_per_chunk = max_requests_per_chunk

        logger.info(
            f"Initialized TenantDateRangeSplitActor with chunk_days={chunk_days}, "
            f"max_requests_per_chunk={max_requests_per_chunk}"
        )

    def process(self, row: PipelineConfig) -> list[TenantDateRange]:
        """Split the work configuration into chunks.

        Args:
            row: PipelineConfig with tenant names, date range, and limit

        Returns:
            List of TenantDateRange objects for parallel processing
        """
        tenant_names = row.tenant_names
        if not tenant_names:
            # Use all available tenants
            tenant_names = list(DATASET_TENANTS.keys())

        date_from = datetime.strptime(row.date_from, "%Y-%m-%d")
        date_to = datetime.strptime(row.date_to, "%Y-%m-%d")
        limit = row.limit

        chunks = []

        # First, calculate total number of chunks to properly distribute the limit
        total_chunks = 0
        valid_tenants = []
        for tenant_name in tenant_names:
            try:
                tenant = get_tenant(tenant_name)
                valid_tenants.append((tenant_name, tenant))
                current_date = date_from
                while current_date < date_to:
                    chunk_end = min(
                        current_date + timedelta(days=self.chunk_days), date_to
                    )
                    total_chunks += 1
                    current_date = chunk_end
            except ValueError:
                logger.warning(f"Skipping unknown tenant: {tenant_name}")
                continue

        # Calculate per-chunk limit
        if limit and total_chunks > 0:
            # Distribute limit across chunks, with at least 1 per chunk
            per_chunk_limit = max(1, limit // total_chunks)
            # Give any remainder to the first chunks
            remainder = limit % total_chunks
        else:
            per_chunk_limit = self.max_requests_per_chunk
            remainder = 0

        chunk_index = 0
        for tenant_name, tenant in valid_tenants:
            # Split date range into chunks
            current_date = date_from
            while current_date < date_to:
                chunk_end = min(current_date + timedelta(days=self.chunk_days), date_to)

                # Calculate this chunk's limit
                if limit and per_chunk_limit is not None:
                    chunk_limit = per_chunk_limit + (
                        1 if chunk_index < remainder else 0
                    )
                    if self.max_requests_per_chunk:
                        chunk_limit = min(chunk_limit, self.max_requests_per_chunk)
                else:
                    chunk_limit = self.max_requests_per_chunk

                chunk = TenantDateRange(
                    tenant_name=tenant_name,
                    tenant_id=tenant.tenant_id,
                    date_from=current_date.strftime("%Y-%m-%d"),
                    date_to=chunk_end.strftime("%Y-%m-%d"),
                    limit=chunk_limit,
                )
                chunks.append(chunk)
                chunk_index += 1

                current_date = chunk_end

        logger.info(
            f"Split work into {len(chunks)} chunks across {len(valid_tenants)} tenants"
            + (
                f" with total limit {limit} (per-chunk: ~{per_chunk_limit})"
                if limit
                else ""
            )
        )
        return chunks


class BigQueryRequestFetcherActor(
    AbstractRayActor[TenantDateRange, VanguardSingleRequest]
):
    """Fetches requests from BigQuery in parallel.

    This actor processes TenantDateRange chunks and queries BigQuery to fetch
    request metadata, converting them into VanguardSingleRequest objects.
    """

    def __init__(
        self,
        service_account_json: Optional[str] = None,
        batch_size: int = 1000,
    ):
        """Initialize the BigQuery fetcher actor.

        Args:
            service_account_json: Path to service account JSON file
            batch_size: Number of rows to fetch per BigQuery page
        """
        super().__init__(input_cls=TenantDateRange, output_cls=VanguardSingleRequest)

        # Initialize credentials
        if service_account_json:
            self.credentials = service_account.Credentials.from_service_account_file(
                service_account_json
            )
        else:
            self.credentials = google.auth.default()[0]

        self.batch_size = batch_size
        self._client_cache = {}  # Cache BigQuery clients per project

        logger.info(
            f"Initialized BigQueryRequestFetcherActor with batch_size={batch_size}"
        )

    def _get_bigquery_client(self, project_id: str) -> bigquery.Client:
        """Get or create a BigQuery client for the project."""
        if project_id not in self._client_cache:
            self._client_cache[project_id] = bigquery.Client(
                project=project_id, credentials=self.credentials
            )
        return self._client_cache[project_id]

    def process(self, row: TenantDateRange) -> list[VanguardSingleRequest]:
        """Fetch requests from BigQuery for the given chunk.

        Args:
            row: TenantDateRange specifying the chunk to process

        Returns:
            List of VanguardSingleRequest objects
        """
        tenant = get_tenant(row.tenant_name)
        client = self._get_bigquery_client(tenant.project_id)

        # Build query
        query = f"""
        SELECT
            tenant_id,
            tenant,
            request_id,
            user_id,
            time
        FROM `{tenant.project_id}.{tenant.search_dataset_name}.request_metadata`
        WHERE
            tenant = '{tenant.name}'
            AND request_type = 'CHAT'
            AND time >= '{row.date_from}'
            AND time < '{row.date_to}'
        ORDER BY time DESC
        """

        if row.limit:
            query += f" LIMIT {row.limit}"

        logger.info(
            f"Querying BigQuery for {row.tenant_name} "
            f"[{row.date_from} to {row.date_to}]"
        )

        # Execute query with pagination
        job_config = bigquery.QueryJobConfig(
            use_query_cache=True,
            use_legacy_sql=False,
        )

        query_job = client.query(query, job_config=job_config)

        requests = []
        rows_processed = 0

        # Process results in batches
        for row_batch in query_job.result(page_size=self.batch_size):
            # Convert BigQuery row to serializable dict
            serializable_row = {}
            for key, value in row_batch.items():
                if isinstance(value, datetime):
                    serializable_row[key] = value.isoformat()
                else:
                    serializable_row[key] = value

            # Create VanguardSingleRequest with only essential fields
            single_request = VanguardSingleRequest(
                tenant_name=row.tenant_name,
                tenant_id=row.tenant_id,
                request_id=serializable_row["request_id"],
            )
            requests.append(single_request)
            rows_processed += 1

            # Log progress periodically
            if rows_processed % 10000 == 0:
                logger.info(
                    f"Processed {rows_processed} rows for {row.tenant_name} "
                    f"[{row.date_from} to {row.date_to}]"
                )

        logger.info(
            f"Fetched {len(requests)} requests for {row.tenant_name} "
            f"[{row.date_from} to {row.date_to}]"
        )

        return requests
