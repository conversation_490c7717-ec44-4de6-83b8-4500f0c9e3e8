/**********************************
 * Messages from webview to vscode.
 **********************************/

// Enum value is useful for debugging
export enum MessageFromWebviewAction {
  SEND_CHAT_MESSAGE = 'SEND_CHAT_MESSAGE',
  SEND_TASK_CHAT = 'SEND_TASK_CHAT',
  SEND_TASK_SCRATCHPAD = 'SEND_TASK_SCRATCHPAD',
  REQUEST_SELECTED_TEXT = 'REQUEST_SELECTED_TEXT',
  SHOW_INFO_BOX = 'SHOW_INFO_BOX',
  WRITE_TO_EDITOR = 'WRITE_TO_EDITOR',
  SAVE_TO_VSCODE_STORE = 'SAVE_TO_VSCODE_STORE',
  REQUEST_VSCODE_STORE = 'REQUEST_VSCODE_STORE',
  REQUEST_FILE_LIST = 'REQUEST_FILE_LIST',
  REQUEST_FILE_CONTENT = 'REQUEST_FILE_CONTENT',
  REQUEST_CURRENT_FILE = 'REQUEST_CURRENT_FILE',
}

export type MessageFromWebview =
  {requestMessageId?: string} & (
    {action: MessageFromWebviewAction.SEND_CHAT_MESSAGE; payload: MessageTextPayload}
  | {action: MessageFromWebviewAction.SEND_TASK_CHAT; payload: MessageFromWebviewTaskChatPayload}
  | {action: MessageFromWebviewAction.SEND_TASK_SCRATCHPAD; payload: MessageFromWebviewTaskScratchpadPayload}
  | {action: MessageFromWebviewAction.SHOW_INFO_BOX; payload: MessageTextPayload}
  | {action: MessageFromWebviewAction.WRITE_TO_EDITOR; payload: MessageTextPayload}
  | {action: MessageFromWebviewAction.SAVE_TO_VSCODE_STORE; payload: Task[]}
  | {action: MessageFromWebviewAction.REQUEST_VSCODE_STORE; payload?: undefined}
  | {action: MessageFromWebviewAction.REQUEST_SELECTED_TEXT; payload?: undefined}
  | {action: MessageFromWebviewAction.REQUEST_FILE_LIST; payload: {path: string}}
  | {action: MessageFromWebviewAction.REQUEST_FILE_CONTENT; payload: {path: string}}
  | {action: MessageFromWebviewAction.REQUEST_CURRENT_FILE; payload?: undefined}
  );

export interface MessageTextPayload {
  content: string;
}

export interface MessageFromWebviewTaskChatPayload extends MessageTextPayload {
  taskId: string;  // Used for the response, to know where to put the response.
  attachedFiles: string[];
}

export interface MessageFromWebviewTaskScratchpadPayload {
  instruction: string;
  previousCode: string;
  // Used for the response, to know where to put the response.
  taskId: string;
  messageId: string;
  id: string;
  attachedFiles: string[];
}


/**********************************
 * Messages from vscode to webview.
 **********************************/

// Enum value is useful for debugging
export enum MessageToWebviewAction {
  CLEAR_ALL = 'CLEAR_ALL',
  PRINT_CHAT_MESSAGE = 'PRINT_CHAT_MESSAGE',
  PRINT_TASK_CHAT = 'PRINT_TASK_CHAT',
  PRINT_TASK_RETRIEVAL = 'PRINT_TASK_RETRIEVAL',
  PRINT_TASK_SCRATCHPAD = 'PRINT_TASK_SCRATCHPAD',
  START_SCRATCHPAD_DIRECTLY = 'START_SCRATCHPAD_DIRECTLY',
  SELECTED_TEXT = 'SELECTED_TEXT',
  RECEIVE_VSCODE_STORE = 'RECEIVE_VSCODE_STORE',
  RECEIVE_FILE_LIST = 'RECEIVE_FILE_LIST',
  RECEIVE_FILE_CONTENT = 'RECEIVE_FILE_CONTENT',
}

export type MessageToWebview =
  {requestMessageId?: string} & (
    { action: MessageToWebviewAction.CLEAR_ALL; payload?: undefined}
  | { action: MessageToWebviewAction.PRINT_CHAT_MESSAGE; payload: MessageToWebviewChatMessagePayload}
  | { action: MessageToWebviewAction.PRINT_TASK_CHAT; payload: MessageToWebviewTaskChatPayload}
  | { action: MessageToWebviewAction.PRINT_TASK_RETRIEVAL; payload: MessageToWebviewTaskChatPayload}
  | { action: MessageToWebviewAction.PRINT_TASK_SCRATCHPAD; payload: MessageToWebviewTaskScratchpadPayload}
  | { action: MessageToWebviewAction.START_SCRATCHPAD_DIRECTLY; payload: MessageTextPayload}
  | { action: MessageToWebviewAction.SELECTED_TEXT; payload: MessageToWebviewSelectedTextPayload}
  | { action: MessageToWebviewAction.RECEIVE_VSCODE_STORE; payload: Task[]}
  | { action: MessageToWebviewAction.RECEIVE_FILE_LIST; payload: MessageToWebviewListFilePayload}
  | { action: MessageToWebviewAction.RECEIVE_FILE_CONTENT; payload: {path: string; content: string}}
  );

export interface ChatContent {
  id: string;  // for streaming and logging
  content: string;
  fromUser?: boolean;
  action?: string;
  model?: string;
  status?: string;
}

export interface MessageToWebviewListFilePayload {
  dir: string;
  items: FileListItem[];
}

export interface FileListItem {
  name: string;  // not the full path.
  isDir: boolean;
}

export interface MessageToWebviewChatMessagePayload extends ChatContent {
}

export interface MessageToWebviewTaskChatPayload extends MessageToWebviewChatMessagePayload {
  taskId: string;
}

export interface MessageToWebviewTaskScratchpadPayload extends CodeVersionContent {
  taskId: string;
  messageId: string;
  id: string;  // code version piece id.
}

export interface MessageToWebviewSelectedTextPayload extends MessageTextPayload {
  appendToInput?: boolean;
  // The content below are not saved, nor used.
  // It could be useful for the case - "user moved selection / focus while generating inline edit".
  prefix: string;
  suffix: string;
  range: [{line: number; char: number}, {line: number; char: number}];
  filepath: string;
}

/**********************************
 * UI
 **********************************/

export interface ChatStoreState {
  messages: ChatContent[];
}

export interface TasksStoreState {
  query: string;
  tasks: Task[];
  activeTaskIndex: number;
  activeScratchpadId: string|null;
  temporaryScratchpad?: {
    id: string;
    taskIndex: number;
    messageIndex: number;
    messageBlockIndex: number;
  };
}

export interface Task {
  taskId: string;
  title: string;
  messages: TaskMessageContent[];
  attachedFiles: string[];
}

export type TaskMessageContent = TaskChatContent|TaskScratchpadContent;
export enum TaskMessageType {
  CHAT = 'chat',  // TaskChatContent
  SCRATCH_PAD = 'scratchpad',  // TaskScratchpadContent
  RETREIVAL = 'retrieval',
}

export interface TaskChatContent extends ChatContent {
  type: TaskMessageType.CHAT|TaskMessageType.RETREIVAL;
}

export interface CodeVersionContent extends ChatContent {
  instruction: string;
  previousCode: string;
}

export interface TaskScratchpadContent {
  id: string;
  type: TaskMessageType.SCRATCH_PAD;
  codeVersions: CodeVersionContent[];
  activeCodeVersionIndex: number;
  // TODO: also save where is this scratch pad coming from
}
