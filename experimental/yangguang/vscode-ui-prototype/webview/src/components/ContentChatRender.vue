<script setup lang="ts">
import {marked} from "marked";
import { useTasksStore } from '@/shared/stores';
import { type ChatContent, TaskMessageType, type TaskScratchpadContent } from "@/shared/types";
import Scratchpad from "./Scratchpad.vue";
const taskStore = useTasksStore();

const props = defineProps<{
  message: ChatContent;
  retrievalMessage?: boolean;
  allowScratchpad?: boolean;
  messageIndex?: number;
}>();

function parseMarkdownWithCode(markdown: string) {
  if (props.retrievalMessage) {
    return [{markdown}];
  }

  const parts = markdown.split('```');
  // If there's only one code block, prepend a empty part to make template loop render the editor.
  if (parts.length === 1 && _checkIsCode(parts[0])) {
    parts.unshift('');
  }
  return parts.map((part, index) => {
    if (index % 2 === 0) {
      return {markdown: part};
    } else {
      // Passing in `id`, used as part of the render key for `<Monaco>` in side `<Scratchpad>`
      // The goal is keep each block unique, but not re-render the editor for each stream chunk.
      return _parseMarkdownCodeBlock(part, `${props.message.id}-${index}`);
    }
  });
}

function _parseMarkdownCodeBlock(textBetweenBackticks: string, id: string): TaskScratchpadContent {
  return {
    id,
    activeCodeVersionIndex: 0,
    type: TaskMessageType.SCRATCH_PAD,
    codeVersions: [
      {
        id: '',
        content: textBetweenBackticks,
        instruction: '',
        previousCode: '',
      }
    ],
  };
}

function _checkIsCode(content: string): boolean {
  // This is a very hacky way to detect code. Not reliable obviously. Good enough for demo.
  return content.includes(' = ') || !!content.match(/\w\.\w/);
}

function matchTemporaryScratchpad(partIndex: number): boolean {
  return props.allowScratchpad && !!taskStore.temporaryScratchpad
    && taskStore.activeTaskIndex === taskStore.temporaryScratchpad.taskIndex
    && props.messageIndex === taskStore.temporaryScratchpad.messageIndex
    && partIndex === taskStore.temporaryScratchpad?.messageBlockIndex;
}
</script>

<template>
  <template v-for="(part, index) of parseMarkdownWithCode(message.content)">
    <template v-if="'markdown' in part">
      <div v-if="part.markdown" class="text-message _markdown-content" :class="{'-status': retrievalMessage, '-user': message.fromUser, '-bot': !retrievalMessage && !message.fromUser}"
          v-html="marked.parse(part.markdown)">
      </div>
    </template>
    <!-- This active state is the temporary scratchpad for the initial version from a chat response. -->
    <Scratchpad v-else
        :isActive="matchTemporaryScratchpad(index)"
        :scratchpad="part"
        :activateFn="() => taskStore.createScratchpad(part.codeVersions[0].content, messageIndex, index)" />
  </template>
  <details v-if="message.action">
    <summary>debug info</summary>
    <p>
      Action: <code>{{message.action}}</code>,&nbsp;
      Model: <code>{{message.model}}</code>,&nbsp;
      <template v-if="message.status">API status: {{message.status}},&nbsp;</template>
      <a :href="`https://supabase.com/dashboard/project/jojultdzeeymbwqvhivt/editor/79538?sort=created:desc&filter=id:eq:${message.id}`" target="_blank">Log link</a>
    </p>
  </details>
</template>

<style lang="scss" scoped>
.text-message {
  margin: 1.5em 0;
  min-height: 2em;  // Avoid the icon get cut off

  &.-status {
    opacity: .7;
  }

  &.-bot,
  &.-user {
    background-repeat: no-repeat;
    background-position: top left 0.5em;
    background-size: 1.6em;
    padding-left: 3em;
  }

  &.-bot {
    background-image: url(https://jojultdzeeymbwqvhivt.supabase.co/storage/v1/object/public/augment-prototype/icon-bot.svg);
  }

  &.-user {
    background-image: url(https://jojultdzeeymbwqvhivt.supabase.co/storage/v1/object/public/augment-prototype/icon-user.svg);
  }
}

details {
  text-align: right;
  margin-bottom: 2em;
}
</style>
