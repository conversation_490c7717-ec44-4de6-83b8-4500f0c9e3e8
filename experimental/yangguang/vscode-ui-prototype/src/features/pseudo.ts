import * as vscode from 'vscode';
import { pseudoCodeSummary, pesudoCodeByLine } from '../lib/api/openai';
import type { PseudoCodeByLinePiece } from '../lib/types';

export default function (name: string) {
    return vscode.commands.registerCommand(`extension.${name}`, async () => {
        await pseudo();
    });
}

async function pseudo() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showErrorMessage('No active editor.');
        return;
    }

    const selectedText = editor.document.getText(editor.selection);
    // Selected, or the whole file
    const code = selectedText || editor.document.getText();
    try {
        const panel = vscode.window.createWebviewPanel(
            'pseudo-code',
            'Pseudo Code',
            vscode.ViewColumn.Two,
            { enableScripts: true }
        );
        panel.webview.html = getWebviewContent('Reading the code...', [], false);

        let summary = '';
        let detail: PseudoCodeByLinePiece[] = [];
        pseudoCodeSummary(code, (content) => {
            summary = content;
            panel.webview.html = getWebviewContent(summary, detail, false);
        }, (_) => {
            panel.webview.html = getWebviewContent(summary, detail, true);
        });
        detail = await pesudoCodeByLine(code);
        console.debug('pseudo code detail', detail);
        panel.webview.html = getWebviewContent(summary, detail, true);
        panel.webview.onDidReceiveMessage(message => {
            if (message.command === 'highlightCode') {
                const range = message.range;
                highlightCodeRange(range);
            }
        });
    } catch (e) {
        console.error(e);
        vscode.window.showErrorMessage(e.message);
    }
}

function getWebviewContent(summary: string, details: PseudoCodeByLinePiece[], showDetailLoading: boolean): string {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Pseudo code</title>
      <style>
        p {
          white-space: pre-wrap;
        }
        button {
          all: unset;
          cursor: pointer;
        }

        input {
          width: 100%;
        }
        li.view input,
        li.edit button {
          display: none;
        }
      </style>
    </head>
    <body>
      <h1>Pseudo code</h1>
      <main>
        <p>${summary}</p>
        <ul class="detail-wrapper">
          ${details.map((part) => `
            <li class="view" data-start="${part.startLine}" data-end="${part.endLine}">
              <button>L${part.startLine} - ${part.endLine}: ${part.explanation}</button>
              <input value="${part.explanation}">
            </li>
          `).join('')}
        </ul>
        ${!details.length ? (showDetailLoading ? 'Loading details...' : '') : ''}
      </main>
      <script>
        const vscode = acquireVsCodeApi();

        for (const el of document.querySelectorAll('.detail-wrapper button')) {
          el.addEventListener('mouseenter', (e) => {
            highlightCode(e.target.parentNode);
          });
          el.addEventListener('mouseleave', (e) => {
            highlightCode();
          });
          el.addEventListener('click', (e) => {
            e.target.parentNode.classList.remove('view');
            e.target.parentNode.classList.add('edit');
          });
        }

        for (const el of document.querySelectorAll('.detail-wrapper input')) {
          el.addEventListener("keydown", function (e) {
            if (e.key === "Enter") {
              e.target.parentNode.querySelector('button').textContent = e.target.value;
              e.target.parentNode.classList.remove('edit');
              e.target.parentNode.classList.add('view');
              editCode(e.target.parentNode);
            }
          });
        }

        function highlightCode(liElement) {
          const start = liElement ? Number(liElement.dataset.start) : 0;
          const end = liElement ? Number(liElement.dataset.end) : 0;
          const range = {
            start: { line: start, character: 0 },
            end: { line: end + 1, character: 0 }
          };
          vscode.postMessage({ command: 'highlightCode', range });
        }

        function editCode(liElement) {
          const start = liElement ? Number(liElement.dataset.start) : 0;
          const end = liElement ? Number(liElement.dataset.end) : 0;
          // TODO: add commands for editing.
          // const range = {
          //   start: { line: start, character: 0 },
          //   end: { line: end + 1, character: 0 }
          // };
          // vscode.postMessage({ command: '', range });
        }
      </script>
    </body>
    </html>
  `;
}

let decoration;
function highlightCodeRange(range: { start: vscode.Position, end: vscode.Position }) {
    // TODO: find the correct way to locate the editor (maybe save it when pseudo is triggered)
    // const editor = vscode.window.activeTextEditor;
    const editor = vscode.window.visibleTextEditors[0];

    if (!editor) {
        return; // No active text editor
    }

    // Clear current light
    if (decoration) {
        editor.setDecorations(decoration, []);
        decoration.dispose(); // Dispose the decoration type to release resources
    }

    // Create a decoration type with the desired styling
    decoration = vscode.window.createTextEditorDecorationType({
        backgroundColor: 'rgba(255, 255, 0, 0.5)' // Yellowish highlight color
    });

    // Create the range from the received data
    const startPosition = new vscode.Position(range.start.line, range.start.character);
    const endPosition = new vscode.Position(range.end.line, range.end.character);
    const rangeInstance = new vscode.Range(startPosition, endPosition);

    // Apply the decoration to the editor
    editor.setDecorations(decoration, [rangeInstance]);

    if (!isRangeInsideView(editor, rangeInstance)) {
        editor.revealRange(rangeInstance, vscode.TextEditorRevealType.InCenter);
    }
}

function isRangeInsideView(editor: vscode.TextEditor, range: vscode.Range): boolean {
    const editorVisibleRanges = editor.visibleRanges;

    for (const visibleRange of editorVisibleRanges) {
        // Check if the range intersects with any of the visible ranges
        if (range.intersection(visibleRange)) {
            return true;
        }
    }

    return false;
}
