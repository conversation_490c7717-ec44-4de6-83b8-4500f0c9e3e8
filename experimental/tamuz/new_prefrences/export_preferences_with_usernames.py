#!/usr/bin/env python3

"""
Script to export preference samples with additional metadata.
Includes username mapping from user lookup table.
"""

import argparse
import json
import logging
import os
import re
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from collections import Counter

from aitutor_data_retrieval import AITutorDataRetriever
from google.protobuf.json_format import MessageToDict

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def get_preference_events(
    retriever: AITutorDataRetriever, days: int = 14, limit: int = 1000
) -> List[Dict[str, Any]]:
    """Get preference events from the last N days.

    Args:
        retriever: The AITutorDataRetriever instance.
        days: Number of days to look back.
        limit: Maximum number of events to return.

    Returns:
        A list of preference events.
    """

    logger.info(f"Retrieving preference events from the last {days} days...")

    # Use a larger limit for the query to ensure we get enough events
    query_limit = limit * 2

    # Execute a direct BigQuery query to get preference events
    query = f"""
    SELECT
        re.request_id as preference_request_id,
        re.tenant,
        re.tenant_id,
        re.event_type,
        re.event_id,
        re.time,
        rm.user_id
    FROM `{retriever.project_id}.{retriever.tenant.search_dataset_name}.request_event` re
    LEFT JOIN `{retriever.project_id}.{retriever.tenant.search_dataset_name}.request_metadata` rm
        ON re.request_id = rm.request_id
    WHERE
        re.event_type = 'preference_sample'
        AND re.tenant = '{retriever.tenant_name}'
        AND re.time BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL {days} DAY) AND CURRENT_TIMESTAMP()
    ORDER BY re.time DESC
    LIMIT {query_limit}
    """

    # Debug the query
    logger.info(f"Query: {query}")

    # Execute the query
    logger.info("Executing direct BigQuery query for preference events")
    query_job = retriever.bq_client.query(query)
    rows = list(query_job.result())

    # Convert rows to dictionaries
    preference_events = [dict(row.items()) for row in rows]

    # Debug: Check if user_id is present in the events
    user_ids = [
        event.get("user_id") for event in preference_events if event.get("user_id")
    ]
    logger.info(
        f"Found {len(preference_events)} preference events, {len(user_ids)} with user_id"
    )
    if user_ids:
        logger.info(f"Sample user_ids: {user_ids[:5]}")

    # Limit the results if needed
    if len(preference_events) > limit:
        preference_events = preference_events[:limit]
        logger.info(f"Limited to {len(preference_events)} preference events")

    return preference_events


def get_full_preference_event(
    preference_events: List[Dict[str, Any]], retriever: AITutorDataRetriever
) -> List[Dict[str, Any]]:
    """Extract full preference events with improved error handling and metadata.

    Args:
        preference_events: List of preference events.
        retriever: The AITutorDataRetriever instance.

    Returns:
        A list of full preference events.
    """
    if not preference_events:
        logger.info("No preference events to process")
        return []

    # Convert to list for the get_requests method
    request_ids_list = [event["preference_request_id"] for event in preference_events]

    # Use the GCSRequestInsightFetcher directly for batch retrieval
    logger.info(f"Using parallel retrieval for {len(request_ids_list)} request IDs")

    # Filter for preference_sample events only
    request_event_names = frozenset(["preference_sample"])

    # Get requests in parallel with a reasonable batch size
    batch_size = min(32, (os.cpu_count() or 1) + 4)
    requests_iterator = retriever.gcs_fetcher.get_requests(
        request_ids=request_ids_list,
        request_event_names=request_event_names,
        batch_size=batch_size,
    )
    all_preference_events = []

    # Create a mapping of request_id to original event for faster lookups
    request_id_to_event = {
        event["preference_request_id"]: event for event in preference_events
    }

    # Process the results
    for result in requests_iterator:
        if isinstance(result, Exception):
            logger.warning(f"Error retrieving request: {result}")
            continue

        # Process the events in this request
        for event in result.events:
            try:
                event_dict = MessageToDict(event)

                # Check if this is a preference_sample event
                if "preferenceSample" in event_dict:
                    # Add metadata from the original event
                    orig_event = request_id_to_event.get(result.request_id)
                    if orig_event:
                        event_dict["preference_request_id"] = orig_event[
                            "preference_request_id"
                        ]
                        event_dict["tenant"] = orig_event["tenant"]
                        event_dict["tenant_id"] = orig_event["tenant_id"]
                        event_dict["event_type"] = orig_event["event_type"]
                        event_dict["user_id"] = orig_event.get("user_id")

                    all_preference_events.append(event_dict)

            except Exception as e:
                logger.warning(
                    f"Error processing event for request {result.request_id}: {e}"
                )

    logger.info(f"Found {len(all_preference_events)} preference events")

    return all_preference_events


def extract_model_names(feedback: str) -> Tuple[str, str]:
    """Extract model names from the feedback text.

    Args:
        feedback: The feedback text.

    Returns:
        A tuple of two model names (model A, model B).
    """
    model_pattern = r"MODEL_IDS_START_LABEL\s*(.+?)\s*MODEL_IDS_END_LABEL"
    model_match = re.search(model_pattern, feedback, re.DOTALL)
    if model_match:
        models = model_match.group(1).strip().split("\n")
        if len(models) == 2:
            return models[0], models[1]
        return models[0], "Unknown"
    return "Unknown", "Unknown"


def get_chat_requests(
    retriever: AITutorDataRetriever, request_ids: List[str]
) -> Dict[str, Dict[str, Any]]:
    """Retrieve chat requests and responses for a list of request IDs.

    Args:
        retriever: The AITutorDataRetriever instance.
        request_ids: List of request IDs to fetch chat requests for.

    Returns:
        A dictionary mapping request IDs to their chat requests and responses.
    """
    if not request_ids:
        logger.info("No request IDs provided for chat request retrieval")
        return {}

    # First, get metadata for these request IDs to get user_agent information
    logger.info(f"Retrieving metadata for {len(request_ids)} request IDs")

    # Format request IDs for SQL query
    formatted_request_ids = ", ".join([f"'{rid}'" for rid in request_ids])

    # Query to get metadata including user_agent
    metadata_query = f"""
    SELECT
        request_id,
        user_id,
        request_type,
        user_agent,
        time
    FROM `{retriever.project_id}.{retriever.tenant.search_dataset_name}.request_metadata`
    WHERE request_id IN ({formatted_request_ids})
    """

    # Execute the query
    metadata_job = retriever.bq_client.query(metadata_query)
    metadata_rows = list(metadata_job.result())

    # Create a dictionary mapping request IDs to metadata
    metadata_dict = {row.request_id: dict(row.items()) for row in metadata_rows}
    logger.info(
        f"Retrieved metadata for {len(metadata_dict)} out of {len(request_ids)} request IDs"
    )

    # Use the GCSRequestInsightFetcher to get requests and responses from GCS
    logger.info(
        f"Using GCS fetcher to retrieve {len(request_ids)} chat requests and responses"
    )

    # Get requests in parallel with a reasonable batch size
    batch_size = min(32, (os.cpu_count() or 1) + 4)

    # We're interested in both chat_host_request and chat_host_response events
    request_event_names = frozenset(["chat_host_request", "chat_host_response"])

    # Use the GCS fetcher to get the requests
    requests_iterator = retriever.gcs_fetcher.get_requests(
        request_ids=request_ids,
        request_event_names=request_event_names,
        batch_size=batch_size,
    )

    # Process the results and build the chat_requests dictionary
    chat_requests = {}
    for result in requests_iterator:
        try:
            request_id = result.request_id
            if request_id not in chat_requests:
                chat_requests[request_id] = {
                    "request": None,
                    "response": None,
                    "metadata": metadata_dict.get(request_id, {}),
                }

            # Process each event
            for event in result.events:
                # Extract chat request
                if hasattr(event, "chat_host_request") and event.chat_host_request:
                    chat_requests[request_id]["request"] = MessageToDict(
                        event.chat_host_request
                    )

                # Extract chat response
                if hasattr(event, "chat_host_response") and event.chat_host_response:
                    chat_requests[request_id]["response"] = MessageToDict(
                        event.chat_host_response
                    )
        except Exception as e:
            logger.warning(
                f"Error processing chat request/response for {result.request_id}: {e}"
            )

    # Count how many have both request and response
    complete_chats = sum(
        1
        for data in chat_requests.values()
        if data.get("request") is not None and data.get("response") is not None
    )

    logger.info(
        f"Retrieved {len(chat_requests)} chat entries, {complete_chats} with both request and response"
    )

    return chat_requests


def process_preference_events(
    preference_events: List[Dict[str, Any]], retriever: AITutorDataRetriever
) -> List[Dict[str, Any]]:
    """Process preference events to extract and enrich with chat requests and responses.

    Args:
        preference_events: List of preference events.
        retriever: The AITutorDataRetriever instance.

    Returns:
        A list of processed preference events with chat requests, responses, and metadata.
    """
    # Extract all request IDs from preference events
    all_request_ids = []
    for event in preference_events:
        if "preferenceSample" in event and "requestIds" in event["preferenceSample"]:
            all_request_ids.extend(event["preferenceSample"]["requestIds"])

    # Remove duplicates
    unique_request_ids = list(set(all_request_ids))
    logger.info(
        f"Found {len(unique_request_ids)} unique request IDs in {len(preference_events)} preference events"
    )

    # Get chat requests, responses, and metadata for these IDs
    chat_data = get_chat_requests(retriever, unique_request_ids)

    # Enrich preference events with chat data
    processed_events = []
    for event in preference_events:
        processed_event = event.copy()

        # Add model names if feedback is available
        if (
            "preferenceSample" in processed_event
            and "feedback" in processed_event["preferenceSample"]
        ):
            processed_event["model_a"], processed_event["model_b"] = (
                extract_model_names(processed_event["preferenceSample"]["feedback"])
            )

        # Add chat data if available
        if (
            "preferenceSample" in processed_event
            and "requestIds" in processed_event["preferenceSample"]
        ):
            request_ids = processed_event["preferenceSample"]["requestIds"]

            # Create option_a and option_b
            if len(request_ids) >= 1 and request_ids[0] in chat_data:
                processed_event["option_a"] = chat_data[request_ids[0]]
                processed_event["option_a"]["datetime"] = (
                    datetime.now()
                )  # Will be converted to ISO format later

                # Get user_id from metadata if available
                if (
                    "metadata" in processed_event["option_a"]
                    and "user_id" in processed_event["option_a"]["metadata"]
                ):
                    processed_event["user_id"] = processed_event["option_a"][
                        "metadata"
                    ]["user_id"]

            if len(request_ids) >= 2 and request_ids[1] in chat_data:
                processed_event["option_b"] = chat_data[request_ids[1]]
                processed_event["option_b"]["datetime"] = (
                    datetime.now()
                )  # Will be converted to ISO format later

                # If we don't have a user_id yet, try to get it from option_b
                if (
                    "user_id" not in processed_event
                    and "metadata" in processed_event["option_b"]
                    and "user_id" in processed_event["option_b"]["metadata"]
                ):
                    processed_event["user_id"] = processed_event["option_b"][
                        "metadata"
                    ]["user_id"]

            # Add scores
            if "scores" in processed_event["preferenceSample"]:
                processed_event["scores"] = processed_event["preferenceSample"][
                    "scores"
                ]

            # Add feedback
            if "feedback" in processed_event["preferenceSample"]:
                processed_event["feedback"] = processed_event["preferenceSample"][
                    "feedback"
                ]

        processed_events.append(processed_event)

    # Count how many events have chat data
    events_with_chats = sum(
        1 for event in processed_events if "option_a" in event and "option_b" in event
    )
    logger.info(
        f"Enriched {events_with_chats} out of {len(processed_events)} preference events with chat data"
    )

    # Count how many events have user_id
    events_with_user_id = sum(
        1 for event in processed_events if "user_id" in event and event["user_id"]
    )
    logger.info(
        f"Found {events_with_user_id} out of {len(processed_events)} preference events with user_id"
    )

    return processed_events


def export_samples(
    processed_events: List[Dict[str, Any]],
    tenant_name: str,
    start_day: str,
    user_lookup: Dict[str, Dict[str, Any]] = None,
    output_dir: str = "/mnt/efs/augment/user/tamuz/annotations",
) -> str:
    """Export processed events to a JSONL file.

    Args:
        processed_events: List of processed preference events.
        tenant_name: Name of the tenant.
        start_day: Start day in YYYY-MM-DD format.
        user_lookup: Dictionary mapping user IDs to user metadata.
        output_dir: Directory to save the output file.

    Returns:
        Path to the output file.
    """
    # Create the output directory if it doesn't exist
    tenant_dir = os.path.join(output_dir, tenant_name)
    os.makedirs(tenant_dir, exist_ok=True)

    # Format the output file name
    start_date = datetime.strptime(start_day, "%Y-%m-%d")
    current_date = datetime.now()
    output_file = os.path.join(
        tenant_dir,
        f"{start_date.strftime('%m-%d')}_{current_date.strftime('%m-%d')}-dump-w-timestamps.jsonl",
    )

    # Create a mapping of user_id to username and latest userAgent
    username_mapping = {}
    user_agent_mapping = {}
    if user_lookup:
        for user_id, data in user_lookup.items():
            username_mapping[user_id] = data.get("user_name", "unknown")
            user_agent_mapping[user_id] = data.get("user_agent", "unknown")

    # Custom JSON encoder to handle datetime objects
    class DateTimeEncoder(json.JSONEncoder):
        def default(self, obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            return super().default(obj)

    # Process and export the samples
    exported_samples = []
    for sample in processed_events:
        if "option_a" not in sample or "option_b" not in sample:
            continue

        try:
            # Get the user_id and look up the username and userAgent
            user_id = sample.get("user_id", "")
            username = username_mapping.get(user_id, "unknown")

            # Get userAgent from lookup table first (most recent), then from metadata
            user_agent = user_agent_mapping.get(user_id, "unknown")
            if (
                user_agent == "unknown"
                and "metadata" in sample["option_a"]
                and "user_agent" in sample["option_a"]["metadata"]
            ):
                user_agent = sample["option_a"]["metadata"]["user_agent"]

            # Convert datetime to ISO format string if it's a datetime object
            if isinstance(sample["option_a"].get("datetime"), datetime):
                datetime_str = sample["option_a"]["datetime"].isoformat()
            else:
                datetime_str = datetime.now().isoformat()

            exported_sample = {
                "preference_request_id": sample.get("preference_request_id", ""),
                "user_id": user_id,
                "username": username,
                "user_agent": user_agent,
                "chat_history": sample["option_a"]["request"].get("chat_history", []),
                "message": sample["option_a"]["request"].get("message", ""),
                "path": sample["option_a"]["request"].get("path", ""),
                "prefix": sample["option_a"]["request"].get("prefix", ""),
                "selected_code": sample["option_a"]["request"].get("selected_code", ""),
                "suffix": sample["option_a"]["request"].get("suffix", ""),
                "option_a": sample["option_a"]["response"],
                "option_b": sample["option_b"]["response"],
                "scores": sample.get("scores", {}),
                "feedback": sample.get("feedback", ""),
                "metadata": sample["option_a"].get("metadata", {}),
                "datetime": datetime_str,
                "model_a": sample.get("model_a", "Unknown"),
                "model_b": sample.get("model_b", "Unknown"),
            }
            exported_samples.append(exported_sample)
        except Exception as e:
            logger.warning(f"Error exporting sample: {e}")

    # Write the samples to the output file
    with open(output_file, "w") as f:
        for sample in exported_samples:
            f.write(json.dumps(sample, cls=DateTimeEncoder))
            f.write("\n")

    logger.info(f"Exported {len(exported_samples)} samples to {output_file}")

    return output_file


def generate_user_summary(
    processed_events: List[Dict[str, Any]],
    user_lookup: Dict[str, Dict[str, Any]] = None,
) -> Dict[str, Dict[str, Any]]:
    """Generate a summary of the number of preferences per user.

    Args:
        processed_events: List of processed preference events.
        user_lookup: Dictionary mapping user IDs to user metadata.

    Returns:
        A dictionary mapping user IDs to summary information.
    """
    user_counts = Counter()
    user_models = {}
    user_scores = {}

    for event in processed_events:
        user_id = event.get("user_id")
        if not user_id:
            continue

        # Count preferences per user
        user_counts[user_id] += 1

        # Track models used by each user
        if user_id not in user_models:
            user_models[user_id] = Counter()

        model_a = event.get("model_a", "Unknown")
        model_b = event.get("model_b", "Unknown")
        user_models[user_id][model_a] += 1
        user_models[user_id][model_b] += 1

        # Track scores given by each user
        if user_id not in user_scores:
            user_scores[user_id] = []

        if "scores" in event and "overallRating" in event["scores"]:
            user_scores[user_id].append(event["scores"]["overallRating"])

    # Create the summary
    user_summary = {}
    for user_id, count in user_counts.most_common():
        username = "unknown"
        if user_lookup and user_id in user_lookup:
            username = user_lookup[user_id].get("user_name", "unknown")

        # Calculate average score if available
        avg_score = None
        if user_id in user_scores and user_scores[user_id]:
            avg_score = sum(user_scores[user_id]) / len(user_scores[user_id])

        # Get top models
        top_models = []
        if user_id in user_models:
            top_models = user_models[user_id].most_common(3)

        user_summary[user_id] = {
            "username": username,
            "count": count,
            "avg_score": avg_score,
            "top_models": top_models,
        }

    logger.info(f"Generated summary for {len(user_summary)} users")

    return user_summary


def main():
    parser = argparse.ArgumentParser(
        description="Export preference samples with additional metadata"
    )
    parser.add_argument(
        "--tenant", type=str, default="aitutor-mercor", help="Tenant name"
    )
    parser.add_argument(
        "--days", type=int, default=8, help="Number of days to look back"
    )
    parser.add_argument(
        "--limit", type=int, default=100000, help="Maximum number of events to return"
    )
    parser.add_argument(
        "--start-day",
        type=str,
        default=None,
        help="Start day in YYYY-MM-DD format (default: days ago)",
    )
    parser.add_argument("--output-dir", type=str, default=".", help="Output directory")
    parser.add_argument(
        "--user-lookup", type=str, default=None, help="Path to user lookup table (JSON)"
    )
    args = parser.parse_args()

    # Set the start day if not provided
    if args.start_day is None:
        start_day = (datetime.now() - timedelta(days=args.days)).strftime("%Y-%m-%d")
    else:
        start_day = args.start_day

    # Initialize the retriever
    retriever = AITutorDataRetriever(tenant_name=args.tenant)

    # Load user lookup table if provided
    user_lookup = None
    if args.user_lookup:
        try:
            with open(args.user_lookup, "r") as f:
                user_lookup = json.load(f)
            logger.info(f"Loaded user lookup table with {len(user_lookup)} users")

            # Make sure user_name exists in the lookup table
            for user_id in user_lookup:
                if "user_name" not in user_lookup[user_id]:
                    user_lookup[user_id]["user_name"] = "unknown"  # Default value

            # Mercor username mapping - using actual user emails from the lookup table
            username_mapping = {
                "Kb2mjANN5Eg1ESnC7QHqwcVu02XtD1fUA2L9i3f69lU=": user_lookup[
                    "Kb2mjANN5Eg1ESnC7QHqwcVu02XtD1fUA2L9i3f69lU="
                ].get("user_name", "unknown"),
                "gE+GTuLcpbRqVqaD+XchXVDxK/5Iw9MuFtTFjmDaOy4=": user_lookup[
                    "gE+GTuLcpbRqVqaD+XchXVDxK/5Iw9MuFtTFjmDaOy4="
                ].get("user_name", "unknown"),
                "J9GNPioe96f6IlzxcO3qXsOKcq9juKo7l5ORQ+1cGhQ=": user_lookup[
                    "J9GNPioe96f6IlzxcO3qXsOKcq9juKo7l5ORQ+1cGhQ="
                ].get("user_name", "unknown"),
                "CfO1vnzQUqfC8jcM6O+SvIhK8WQxRVMhsKv40RdgDT8=": user_lookup[
                    "CfO1vnzQUqfC8jcM6O+SvIhK8WQxRVMhsKv40RdgDT8="
                ].get("user_name", "unknown"),
                "Klui5mwubOCu86+spqRKdhAjU2PImc4Bl5L5wr0jC0c=": user_lookup[
                    "Klui5mwubOCu86+spqRKdhAjU2PImc4Bl5L5wr0jC0c="
                ].get("user_name", "unknown"),
                # Add more mappings as needed
            }

            # Update the user lookup table with usernames
            for user_id, username in username_mapping.items():
                if user_id in user_lookup:
                    user_lookup[user_id]["user_name"] = username

            logger.info(f"Added username mappings for {len(username_mapping)} users")
        except Exception as e:
            logger.warning(f"Error loading user lookup table: {e}")

    # Get preference events
    preference_events = get_preference_events(
        retriever=retriever, days=args.days, limit=args.limit
    )
    print(f"Found {len(preference_events)} preference events")

    # Get full preference events
    full_events = get_full_preference_event(
        preference_events=preference_events, retriever=retriever
    )
    print(f"Loaded {len(full_events)} preference events")

    # Process preference events to get chat data
    processed_events = process_preference_events(full_events, retriever)
    print(f"Processed {len(processed_events)} preference events")

    # Export the samples
    output_file = export_samples(
        processed_events=processed_events,
        tenant_name=args.tenant,
        start_day=start_day,
        user_lookup=user_lookup,
        output_dir=args.output_dir,
    )
    print(f"Exported samples to {output_file}")

    # Generate user summary
    user_summary = generate_user_summary(processed_events, user_lookup)

    # Save user summary to file
    summary_file = os.path.join(args.output_dir, f"{args.tenant}_user_summary.json")
    with open(summary_file, "w") as f:
        json.dump(user_summary, f, indent=2, cls=DateTimeEncoder)
    print(f"Saved user summary to {summary_file}")

    # Print user summary
    print("\nPreferences per user:")
    for i, (user_id, data) in enumerate(list(user_summary.items())[:10]):
        username = data["username"]
        count = data["count"]
        avg_score = data["avg_score"]
        avg_score_str = f"{avg_score:.2f}" if avg_score is not None else "N/A"

        print(f"{i+1}. {username} (ID: {user_id})")
        print(f"   Preferences: {count}, Avg Score: {avg_score_str}")

        if data["top_models"]:
            print(
                f"   Top Models: {', '.join([f'{model} ({count})' for model, count in data['top_models']])}"
            )
        print()

    if len(user_summary) > 10:
        print(f"... and {len(user_summary) - 10} more users")

    return processed_events, user_summary


# Custom JSON encoder to handle datetime objects
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


if __name__ == "__main__":
    main()
