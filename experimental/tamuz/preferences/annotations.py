# %%

import matplotlib.pyplot as plt
from collections import Counter
from tqdm import tqdm
import pandas as pd
from copy import deepcopy
import datetime
import re
import numpy as np
import math
import plotly.express as px
from sklearn.linear_model import LogisticRegression
from sklearn.multiclass import OneVsRestClassifier
from collections import defaultdict
import signal
from contextlib import contextmanager
from typing import Dict, List, NamedTuple
from dataclasses import dataclass
from sklearn.preprocessing import LabelEncoder

from experimental.tamuz.preferences.data_retrieval import AnnotationsRetriever
from experimental.tamuz.preferences.renderer import SampleRenderer
from experimental.tamuz.preferences.html_report_utils import (
    style_dataframe,
    generate_elo_table,
    generate_elo_plot,
    generate_category_tables,
    remove_model_names,
    generate_model_comparison_tables,
    wrap_html,
)

pd.options.display.float_format = "{:.2f}".format


# %%
PROJECT_ID = "system-services-prod"
DATASET_NAME = "prod_request_insight_full_export_dataset"

MERCOR_TENANT_NAME = "aitutor-mercor"
TURING_BUCKET_NAME = "augment_ai"
TURING_TENANT_NAME = "aitutor-turing"
TURING_PREFIX = "to_augment_ai"
START_DAY = "2024-11-15"  # Must be in date format ex: YYYY-MM-DD
HISTORY_LIMIT = 3
NUM_SAMPLES_TO_SHOW = 5
ANNOTATIONS_DIR = "/mnt/efs/augment/user/tamuz/annotations"
MODEL_ELO_DIR = f"{ANNOTATIONS_DIR}/model-elo"

pd.options.display.float_format = "{:.2f}".format


# %%
def get_data_df(samples):
    all_data = []
    for sample in samples:
        cur_scores = deepcopy(sample["scores"])

        cur_scores["user_id"] = sample["option_a"]["user_id"]
        all_data.append(cur_scores)
    return pd.DataFrame(all_data)


def request_id_to_link(request_id, tenant_name):
    return f"https://support.{tenant_name}.t.us-central1.prod.augmentcode.com/t/{tenant_name}/request/{request_id}"


# %%
retriever = AnnotationsRetriever(
    project_id=PROJECT_ID, dataset_name=DATASET_NAME, start_day=START_DAY
)

mercor_samples = retriever.get_enriched_samples(tenant_name=MERCOR_TENANT_NAME)
turing_samples = retriever.get_enriched_samples(tenant_name=TURING_TENANT_NAME)
samples = mercor_samples + turing_samples
# %%
analysis_df = get_data_df(samples)
samples_per_user = Counter(analysis_df.user_id)

for name, num in samples_per_user.most_common(100):
    print(f"{name}: {num}")

# %%
label_names = list(
    ["formattingRating", "instructionFollowingRating", "isHighQuality", "overallRating"]
)
user_names = list(map(lambda x: x[0], samples_per_user.most_common(100)))


# %%
def clean_text(text):
    # Remove any leading/trailing whitespace, newlines, and colons
    text = text.strip().strip(":").strip()
    # Replace multiple spaces with a single space
    text = re.sub(r"\s+", " ", text)
    return text


def extract_model_name(sample):
    return sample["option_a"]["request"]["model_name"], sample["option_b"]["request"][
        "model_name"
    ]


def extract_categories(text):
    category_pattern = r"\[Category\]\s*(.+?)(?=\[|$)"
    subcategory_pattern = r"\[Sub Category\]\s*(.+?)(?=\[|$)"
    type_pattern = r"\[Type\]\s*(.+?)(?=\[|$)"

    category_match = re.search(category_pattern, text, re.DOTALL)
    subcategory_match = re.search(subcategory_pattern, text, re.DOTALL)
    type_match = re.search(type_pattern, text, re.DOTALL)

    category = clean_text(category_match.group(1)) if category_match else "Unknown"
    subcategory = (
        clean_text(subcategory_match.group(1))
        if subcategory_match
        else "No Subcategory"
    )
    type_ = clean_text(type_match.group(1)) if type_match else "No Type"

    return category, subcategory, type_


def extract_implicit_external_sources(text):
    start_labels = ["IMPLICIT_EXTERNAL_SOURCES_START_LABEL"]
    end_labels = ["IMPLICIT_EXTERNAL_SOURCES_END_LABEL"]

    for start_label in start_labels:
        for end_label in end_labels:
            pattern = f"{re.escape(start_label)}(.*?){re.escape(end_label)}"
            match = re.search(pattern, text, re.DOTALL)
            if match:
                return clean_text(match.group(1))

    return ""


# %%


# %%


custom_metrics = defaultdict(dict)

for user_name in user_names:
    print(f"{user_name}")
    cur_samples = [
        sample for sample in samples if sample["option_a"]["user_id"] == user_name
    ]
    print(
        "\n".join(
            map(
                str,
                Counter(list(map(lambda x: x["user_agent"], cur_samples))).most_common(
                    100
                ),
            )
        )
    )
    print("#" * 20)

    num_w_selected_code = len(
        list(
            filter(
                lambda x: len(x["option_a"]["request"].get("selected_code", "")) > 0,
                cur_samples,
            )
        )
    )
    num_wo_selected_code = len(cur_samples) - num_w_selected_code

    custom_metrics[user_name]["num_w_selected_code"] = num_w_selected_code
    custom_metrics[user_name]["num_wo_selected_code"] = num_wo_selected_code

    for sample in cur_samples:
        category, sub_category, type_ = extract_categories(sample.get("feedback", ""))
        implicit_external_sources = extract_implicit_external_sources(
            sample.get("feedback", "")
        )
        model_a, model_b = extract_model_name(sample)
        sub_category = f"{category}_{sub_category}"
        type_ = f"{sub_category}_{type_}"
        sample["option_a"]["model_name"] = model_a
        sample["option_b"]["model_name"] = model_b
        sample["implicit_external_sources"] = implicit_external_sources
        sample["winning_model_name"] = (
            model_a
            if sample["scores"]["overallRating"] < 0
            else sample["option_b"]["model_name"]
            if sample["scores"]["overallRating"] > 0
            else "Tie"
        )

        custom_metrics[user_name].setdefault("categories", defaultdict(int))[
            category
        ] += 1
        custom_metrics[user_name].setdefault("subcategories", defaultdict(int))[
            sub_category
        ] += 1
        custom_metrics[user_name].setdefault("types", defaultdict(int))[type_] += 1

# %%
for user_name in user_names:
    print(
        f"{user_name}: {custom_metrics[user_name]['num_w_selected_code'] / (custom_metrics[user_name]['num_w_selected_code'] + custom_metrics[user_name]['num_wo_selected_code']):.2f}   {custom_metrics[user_name]['categories']}"
    )


# %%

slow_samples = []


# Add timeout context manager at the top
@contextmanager
def timeout(seconds):
    def signal_handler(signum, frame):
        raise TimeoutError(f"Timed out after {seconds} seconds")

    signal.signal(signal.SIGALRM, signal_handler)
    signal.alarm(seconds)
    try:
        yield
    finally:
        signal.alarm(0)


def generate_report(internal: bool):
    multisample_html = ""
    if internal:
        # Create DataFrame
        df = pd.DataFrame(
            [
                {
                    "overallRating": sample["scores"]["overallRating"],
                    "user_id": sample["option_a"]["user_id"],
                    "model_a": sample["option_a"]["model_name"],
                    "model_b": sample["option_b"]["model_name"],
                    "winning_model": sample["winning_model_name"],
                    "datetime": sample["option_a"]["datetime"],
                }
                for sample in samples
                if sample["option_a"]["model_name"]
                not in ["undefined", "Undefined", "Unknown"]
            ]
        )
        model_name_mapping = {
            "binks-l3-70B-FP8-ug-chatanol1-16-3-chat": "Chatanol-16-3",
            "binks-l3-w-refinement-v1": "Refinement-v1",
        }
        # Apply the mapping to the relevant columns
        columns_to_shorten = ["model_a", "model_b", "winning_model"]
        for col in columns_to_shorten:
            df[col] = df[col].replace(model_name_mapping)
        elo_table = generate_elo_table(df)
        elo_table_html = style_dataframe(elo_table).to_html(index=False)

    for user_id in user_names:
        cur_samples = [
            sample for sample in samples if sample["option_a"]["user_id"] == user_id
        ]
        cur_samples = list(
            reversed(sorted(cur_samples, key=lambda x: x["option_a"]["datetime"]))
        )

        cur_user_html = ""

        for i, sample in tqdm(
            enumerate(cur_samples[0 : min(NUM_SAMPLES_TO_SHOW, len(cur_samples))])
        ):
            try:
                # Handles previous samples that don't have ratings
                if "formattingRating" not in sample["scores"]:
                    sample["scores"]["formattingRating"] = -100

                if "instructionFollowingRating" not in sample["scores"]:
                    sample["scores"]["instructionFollowingRating"] = -100

                if "hallucinationRating" not in sample["scores"]:
                    sample["scores"]["hallucinationRating"] = -100
                sample["feedback"] = remove_model_names(sample["feedback"])

                cur_user_html += f"<h1>Chain {i}</h1>"
                cur_user_html += f"<div>User ID: {user_id}</div>"
                cur_user_html += f"<div>Time: {sample['option_a']['datetime'].strftime('%m-%d-%Y %H:%M:%S')}</div>"
                cur_user_html += f'<div>Request ID (Preference): {sample["preference_request_id"]}</div>'
                cur_user_html += (
                    "<div>____________________________________________________</div>"
                )
                cur_user_html += (
                    f'<div>Model A: {sample["option_a"]["model_name"]}</div>'
                )
                cur_user_html += f'<div>Request ID (A): <a href="{request_id_to_link(sample["option_a"]["request_id"], sample["tenant_name"])}">{sample["option_a"]["request_id"]}</a></div>'
                cur_user_html += (
                    f'<div>Model B: {sample["option_b"]["model_name"]}</div>'
                )
                cur_user_html += f'<div>Request ID (B): <a href="{request_id_to_link(sample["option_b"]["request_id"],  sample["tenant_name"])}">{sample["option_b"]["request_id"]}</a></div>'
                with timeout(1):
                    history_html = SampleRenderer(sample).render()

                cur_user_html += history_html
                cur_user_html += "<hr>"

            except TimeoutError:
                print(
                    f"TIMEOUT: Sample {sample['preference_request_id']} took too long to render"
                )
                history_html = (
                    "<div class='error'>Rendering timed out after 5 seconds</div>"
                )
        multisample_html += f"<details><summary>User ID: {user_id} -- {len(cur_samples)} samples</summary>{cur_user_html}</details>"
    total_categories = Counter()
    total_subcategories = Counter()
    total_samples = 0

    for user_id in user_names:
        total_categories.update(custom_metrics[user_id]["categories"])
        total_subcategories.update(custom_metrics[user_id]["subcategories"])
        total_samples += len(
            [sample for sample in samples if sample["option_a"]["user_id"] == user_id]
        )

    # Create summary HTML
    summary_html = "<h2>Overall Summary</h2>"
    summary_html += f"<div>Total Users: {len(user_names)}</div>"
    summary_html += f"<div>Total Samples: {total_samples}</div>"
    summary_html += generate_category_tables(total_categories, total_subcategories)
    if internal:
        # Generate overall plot
        overall_plot_html = generate_elo_plot(df)
        summary_html += f"<h2>Overall Score Distribution</h2><img src='data:image/png;base64,{overall_plot_html}'>"

        summary_html += "<h2>Elo Ratings</h2>"
        summary_html += elo_table_html
        # Add overall model comparison tables
        summary_html += generate_model_comparison_tables(df)

    # Add the summary to the main HTML
    multisample_html += summary_html
    whole_html = wrap_html(multisample_html)

    with open(
        f"{MODEL_ELO_DIR}/{datetime.datetime(*map(int, START_DAY.split('-'))).strftime('%m-%d')}_{datetime.datetime.now().strftime('%m-%d')}.html",
        "w",
    ) as f:
        f.write(whole_html)
    return f"{MODEL_ELO_DIR}/{datetime.datetime(*map(int, START_DAY.split('-'))).strftime('%m-%d')}_{datetime.datetime.now().strftime('%m-%d')}.html"


generate_report(internal=True)


# %%
def get_turn_num(request):
    if len(request.get("chat_history", "")) > 0:
        return len(request["chat_history"])
    return 0


# %%
battles = pd.DataFrame(
    [
        {
            "overallRating": sample["scores"]["overallRating"],
            "formattingRating": sample["scores"]["formattingRating"],
            "instructionFollowingRating": sample["scores"][
                "instructionFollowingRating"
            ],
            "isHighQuality": sample["scores"]["isHighQuality"],
            "user_id": sample["option_a"]["user_id"],
            "model_a": sample["option_a"]["model_name"],
            "model_b": sample["option_b"]["model_name"],
            "winner": "model_a"
            if sample["winning_model_name"] == sample["option_a"]["model_name"]
            else "model_b"
            if sample["winning_model_name"] == sample["option_b"]["model_name"]
            else "Tie",
            "turn": get_turn_num(sample["option_a"]["request"]),
            "datetime": sample["option_a"]["datetime"],
        }
        for sample in samples
        if sample["option_a"]["model_name"] not in ["undefined", "Undefined", "Unknown"]
    ]
)

model_name_mapping = {
    "binks-l3-70B-FP8-ug-chatanol1-16-3-chat": "Chatanol-16-3",
    "binks-l3-w-refinement-v1": "Refinement-v1",
}
# Apply the mapping to the relevant columns
columns_to_shorten = ["model_a", "model_b", "winner"]
for col in columns_to_shorten:
    battles[col] = battles[col].replace(model_name_mapping)
battles.head()

# %%
print(f"Total samples: {len(battles)}")
battles = battles[battles.model_a != battles.model_b]
print(f"Total head to head battles: {len(battles)}")


# %%
fig = px.bar(
    battles["winner"].value_counts(),
    title="Counts of Battle Outcomes",
    text_auto=True,
    height=400,
)
fig.update_layout(xaxis_title="Battle Outcome", yaxis_title="Count", showlegend=False)

# %%


# %%
# Filter out models with less then 50 battles
model_counts = pd.concat([battles["model_a"], battles["model_b"]]).value_counts()
model_counts = model_counts[model_counts >= 50]
model_counts.sort_values(ascending=False)
battles = battles[
    battles["model_a"].isin(model_counts.index)
    & battles["model_b"].isin(model_counts.index)
]

# %%
battles_no_ties = battles[~battles["winner"].str.contains("Tie")]
fig = px.bar(
    pd.concat([battles["model_a"], battles["model_b"]]).value_counts(),
    title="Battle Count for Each Model",
    text_auto=True,
)
fig.update_layout(
    xaxis_title="model", yaxis_title="Battle Count", height=400, showlegend=False
)


# %%
def visualize_battle_count(battles, title, show_num_models=30):
    ptbl = pd.pivot_table(
        battles, index="model_a", columns="model_b", aggfunc="size", fill_value=0
    )
    battle_counts = ptbl + ptbl.T
    ordering = battle_counts.sum().sort_values(ascending=False).index
    ordering = ordering[:show_num_models]
    fig = px.imshow(battle_counts.loc[ordering, ordering], title=title, text_auto=True)
    fig.update_layout(
        xaxis_title="Model B",
        yaxis_title="Model A",
        xaxis_side="top",
        height=800,
        width=800,
        title_y=0.07,
        title_x=0.5,
        font=dict(size=10),
    )
    fig.update_traces(
        hovertemplate="Model A: %{y}<br>Model B: %{x}<br>Count: %{z}<extra></extra>"
    )
    return fig


fig = visualize_battle_count(
    battles, title="Battle Count of Each Combination of Models", show_num_models=30
)
fig

# %%
visualize_battle_count(
    battles_no_ties, "Battle Count for Each Combination of Models (without Ties)"
)

# %%
visualize_battle_count(
    battles[battles["winner"].str.contains("Tie")],
    "Tie Count for Each Combination of Models",
)

# %%
fig = px.histogram(
    battles["turn"],
    title="Number of Conversation Turns",
    text_auto=True,
    height=400,
    log_y=True,
)
fig.update_layout(xaxis_title="Turns", yaxis_title="Count", showlegend=False)
fig


# %%
def compute_pairwise_win_fraction(battles, max_num_models=30):
    # Times each model wins as Model A
    a_win_ptbl = pd.pivot_table(
        battles[battles["winner"] == "model_a"],
        index="model_a",
        columns="model_b",
        aggfunc="size",
        fill_value=0,
    )  # type: ignore

    # Table counting times each model wins as Model B
    b_win_ptbl = pd.pivot_table(
        battles[battles["winner"] == "model_b"],
        index="model_a",
        columns="model_b",
        aggfunc="size",
        fill_value=0,
    )  # type: ignore

    # Table counting number of A-B pairs
    num_battles_ptbl = pd.pivot_table(
        battles, index="model_a", columns="model_b", aggfunc="size", fill_value=0
    )  # type: ignore

    # Computing the proportion of wins for each model as A and as B
    # against all other models
    row_beats_col_freq = (a_win_ptbl + b_win_ptbl.T) / (
        num_battles_ptbl + num_battles_ptbl.T
    )

    # Arrange ordering according to proprition of wins
    prop_wins = row_beats_col_freq.mean(axis=1).sort_values(ascending=False)
    prop_wins = prop_wins[:max_num_models]
    model_names = list(prop_wins.keys())
    row_beats_col = row_beats_col_freq.loc[model_names, model_names]
    return row_beats_col


def visualize_pairwise_win_fraction(battles, title, max_num_models=30):
    row_beats_col = compute_pairwise_win_fraction(battles, max_num_models)
    fig = px.imshow(
        row_beats_col, color_continuous_scale="RdBu", text_auto=".2f", title=title
    )
    fig.update_layout(
        xaxis_title=" Model B: Loser",
        yaxis_title="Model A: Winner",
        xaxis_side="top",
        height=900,
        width=900,
        title_y=0.07,
        title_x=0.5,
    )
    fig.update_traces(
        hovertemplate="Model A: %{y}<br>Model B: %{x}<br>Fraction of A Wins: %{z}<extra></extra>"
    )

    return fig


# %%
fig = visualize_pairwise_win_fraction(
    battles_no_ties, title="Fraction of Model A Wins for All Non-tied A vs. B Battles"
)
fig


# %% [markdown]
# ### Maximum Likelihood Estimation with [Bradley-Terry model](https://en.wikipedia.org/wiki/Bradley%E2%80%93Terry_model)
#
# In the context of LLM evaluation, models can be assumed to be static. In this case, we can directly fit the ratings by maximum likelihood estimation method (aka Bradley-Terry model), which produce significantly stable ratings.

# %%


def compute_mle_elo(df, scale=400, BASE=10, INIT_RATING=1500):
    df = df.reset_index(drop=True)

    # Create a mapping for the score
    score_map = {"model_a": 1, "model_b": 0, "Tie": 0.5}
    df["score"] = df["winner"].map(score_map)

    # Create a list of all unique models
    all_models = sorted(set(df["model_a"].unique()) | set(df["model_b"].unique()))
    model_to_index = {model: i for i, model in enumerate(all_models)}

    # Prepare the feature matrix and target vector
    n_comparisons = len(df)
    n_models = len(all_models)
    X = np.zeros((n_comparisons, n_models))

    # Use vectorized operations instead of loop
    X[np.arange(n_comparisons), df["model_a"].map(model_to_index)] = 1 + math.log(BASE)
    X[np.arange(n_comparisons), df["model_b"].map(model_to_index)] = -1 - math.log(BASE)

    # Convert scores to discrete classes
    le = LabelEncoder()
    y = le.fit_transform(df["score"])

    # Fit the logistic regression model
    lr = OneVsRestClassifier(
        LogisticRegression(fit_intercept=False, solver="lbfgs", max_iter=1000)
    )
    lr.fit(X, y)

    # Convert coefficients to Elo ratings
    model_a_win_class = np.where(le.classes_ == 1)[0][0]
    elo_ratings = (scale) * lr.estimators_[model_a_win_class].coef_[0] + INIT_RATING
    # Create a Series with model names and their Elo ratings
    elo_series = pd.Series(elo_ratings, index=all_models)

    return elo_series.sort_values(ascending=False)


def preety_print_model_ratings(ratings):
    df = (
        pd.DataFrame(
            [[n, ratings[n]] for n in ratings.keys()], columns=["Model", "Elo rating"]
        )
        .sort_values("Elo rating", ascending=False)
        .reset_index(drop=True)
    )
    # df["Elo rating"] = (df["Elo rating"] + 0.5).astype(int)
    df.index = df.index + 1
    return df


# %% [markdown]
#


# %%
def get_bootstrap_result(battles, func_compute_elo, num_round):
    rows = []
    for i in tqdm(range(num_round), desc="bootstrap"):
        rows.append(func_compute_elo(battles.sample(frac=1.0, replace=True)))
    df = pd.DataFrame(rows)
    return df[df.median().sort_values(ascending=False).index]


BOOTSTRAP_ROUNDS = 500

np.random.seed(42)
bootstrap_elo_lu = get_bootstrap_result(battles, compute_mle_elo, BOOTSTRAP_ROUNDS)


# %%
def visualize_bootstrap_scores(df, title):
    bars = (
        pd.DataFrame(
            dict(
                lower=df.quantile(0.025),
                rating=df.quantile(0.5),
                upper=df.quantile(0.975),
            )
        )
        .reset_index(names="model")
        .sort_values("rating", ascending=False)
    )
    bars["error_y"] = bars["upper"] - bars["rating"]
    bars["error_y_minus"] = bars["rating"] - bars["lower"]
    bars["rating_rounded"] = np.round(bars["rating"], 2)
    fig = px.scatter(
        bars,
        x="model",
        y="rating",
        error_y="error_y",
        error_y_minus="error_y_minus",
        text="rating_rounded",
        title=title,
    )
    fig.update_layout(xaxis_title="Model", yaxis_title="Rating", height=600)
    return fig


# %%

# %%
visualize_bootstrap_scores(bootstrap_elo_lu, "Bootstrap of MLE Elo Rating Estimates")


# %%
px.violin(bootstrap_elo_lu.melt(), x="variable", y="value")


# %%
# def predict_win_rate(elo_ratings, SCALE=300, BASE=15):
#     names = sorted(list(elo_ratings.keys()))
#     wins = defaultdict(lambda: defaultdict(lambda: 0))
#     for a in names:
#         for b in names:
#             ea = 1 / (1 + BASE ** ((elo_ratings[b] - elo_ratings[a]) / SCALE))
#             wins[a][b] = ea
#             wins[b][a] = 1 - ea

#     data = {
#         a: [wins[a][b] if a != b else np.NAN for b in names]
#         for a in names
#     }

#     df = pd.DataFrame(data, index=names)
#     df.index.name = "model_a"
#     df.columns.name = "model_b"
#     return df.T

# %%


# %%


# %%
# win_rate = predict_win_rate(dict(bootstrap_elo_lu.quantile(0.5)))
# ordered_models = win_rate.mean(axis=1).sort_values(ascending=False).index
# ordered_models = ordered_models[:30]
# fig = px.imshow(win_rate.loc[ordered_models, ordered_models], # type: ignore
#                 color_continuous_scale='RdBu', text_auto=".2f",
#                 title="Predicted Win Rate Using Elo Ratings for Model A in an A vs. B Battle")
# fig.update_layout(xaxis_title="Model B",
#                   yaxis_title="Model A",
#                   xaxis_side="top", height=900, width=900,
#                   title_y=0.07, title_x=0.5)
# fig.update_traces(hovertemplate=
#                   "Model A: %{y}<br>Model B: %{x}<br>Win Rate: %{z}<extra></extra>")
# fig

# # %%
# battles.describe()

# %%


class EloStats(NamedTuple):
    median: float
    lower_ci: float
    upper_ci: float
    num_battles: int


@dataclass
class BootstrapEloResults:
    ratings: Dict[str, EloStats]
    bootstrap_samples: Dict[str, List[float]]


def compute_single_elo_run(
    battles_df: pd.DataFrame,
    rating_column: str,
    weights: List[float],
    k_factor: float = 32,
    initial_rating: float = 1500,
) -> Dict[str, float]:
    """Compute single Elo rating run with weighted discrete scores."""
    ratings = defaultdict(lambda: initial_rating)

    # Score mapping dictionary using weights
    score_mapping = {
        -3: weights[0] * 1.0,  # Strong A win
        -2: weights[1] * 1.0,  # Medium A win
        -1: weights[2] * 1.0,  # Weak A win
        0: 0.5,  # Tie
        1: weights[2] * 0.0,  # Weak B win
        2: weights[1] * 0.0,  # Medium B win
        3: weights[0] * 0.0,  # Strong B win
    }

    for _, battle in battles_df.iterrows():
        model_a = battle["model_a"]
        model_b = battle["model_b"]
        raw_score = battle[rating_column]

        if pd.isna(raw_score):
            continue

        # Convert raw score to integer and get weighted score
        score = int(raw_score)
        weighted_score = score_mapping[score]

        # Calculate expected scores
        rating_diff = ratings[model_b] - ratings[model_a]
        expected_score_a = 1 / (1 + 10 ** (rating_diff / 400))

        # Update ratings with weighted score
        rating_change = k_factor * (weighted_score - expected_score_a)
        ratings[model_a] += rating_change
        ratings[model_b] -= rating_change

    return dict(ratings)


def bootstrap_elo_ratings(
    battles_df: pd.DataFrame,
    rating_column: str,
    weights: List[float],
    n_bootstrap: int = 1000,
    k_factor: float = 32,
    initial_rating: float = 1500,
    ci_width: float = 0.95,
) -> BootstrapEloResults:
    """Modified bootstrap function to include weights for discrete scores"""
    # Count battles per model
    model_battles = defaultdict(int)
    for _, row in battles_df.iterrows():
        model_battles[row["model_a"]] += 1
        model_battles[row["model_b"]] += 1

    # Store bootstrap results
    bootstrap_ratings = defaultdict(list)

    # Run bootstrap iterations
    for _ in tqdm(range(n_bootstrap), desc=f"Bootstrap {rating_column}"):
        # Sample with replacement
        bootstrap_sample = battles_df.sample(n=len(battles_df), replace=True)
        # Pass weights to compute_single_elo_run
        ratings = compute_single_elo_run(
            bootstrap_sample,
            rating_column,
            weights=weights,
            k_factor=k_factor,
            initial_rating=initial_rating,
        )

        for model, rating in ratings.items():
            bootstrap_ratings[model].append(rating)

    # Compute statistics
    alpha = (1 - ci_width) / 2
    percentiles = [alpha * 100, 50, (1 - alpha) * 100]

    ratings_stats = {}
    for model, ratings in bootstrap_ratings.items():
        lower, median, upper = np.percentile(ratings, percentiles)
        ratings_stats[model] = EloStats(
            median=median,
            lower_ci=lower,
            upper_ci=upper,
            num_battles=model_battles[model],
        )

    return BootstrapEloResults(ratings_stats, bootstrap_ratings)


# Example weight configurations for discrete scores
weight_configs = {
    "Equal": [1, 1, 1],  # All wins weighted equally
    "Extreme Emphasis": [3, 2, 1],  # Stronger wins matter more
    "Mild Emphasis": [1.5, 1.25, 1],  # Slightly stronger emphasis on decisive wins
    "Middle Emphasis": [1, 2, 1],  # Medium wins matter more
    "Ignore week wins": [1, 1, 0],  # Ignore weak wins
    "Binary": [1, 1, 1],  # Treats all wins the same
}


def analyze_with_weights(
    battles_df: pd.DataFrame, weight_configs: Dict[str, List[float]], rating_column: str
):
    """Analyze ratings with different weight configurations"""
    results = {}
    for weight_name, weights in weight_configs.items():
        print(f"Computing ratings for {weight_name} weights...")
        results[weight_name] = bootstrap_elo_ratings(
            battles_df, rating_column, weights=weights, n_bootstrap=200
        )
    return results


# Visualization function remains the same
def visualize_weight_comparisons(results: Dict[str, BootstrapEloResults]):
    plt.figure(figsize=(15, 8))

    x_positions = np.arange(len(results))
    width = 0.15

    # Get unique models across all results
    all_models = set()
    for result in results.values():
        all_models.update(result.ratings.keys())

    for i, model in enumerate(sorted(all_models)):
        medians = []
        errors = []

        for weight_name, result in results.items():
            if model in result.ratings:
                stats = result.ratings[model]
                medians.append(stats.median)
                errors.append(
                    [stats.median - stats.lower_ci, stats.upper_ci - stats.median]
                )
            else:
                medians.append(0)
                errors.append([0, 0])

        plt.errorbar(
            x_positions + i * width,
            medians,
            yerr=np.array(errors).T,
            label=model,
            fmt="o",
            capsize=5,
        )

    plt.xlabel("Weight Configuration")
    plt.ylabel("Elo Rating")
    plt.title("Comparison of Elo Ratings with Different Weight Configurations")
    plt.xticks(
        x_positions + width * len(all_models) / 2, list(results.keys()), rotation=45
    )
    plt.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
    plt.tight_layout()
    plt.grid(True, alpha=0.3)

    return plt


# Usage:
results = analyze_with_weights(battles, weight_configs, "overallRating")
visualize_weight_comparisons(results)


# %%


# %%


# %%
