import json

from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name
from experimental.zhuoran.prompt.xml.request_ids import (
    FIRST_FAIL_REQUEST_IDS,
)
from experimental.zhuoran.prompt.xml.utils import (
    POSTPROCESSING_PROMPT,
    get_example_result,
)
from experimental.zhuoran.prompt.xml.xml_replay_infra import get_input_and_documents
from research.tools.chat_replay.replay_utils import (
    TOKEN_APPORTIONMENT,
    chat_prompt_dict_from_input,
    fix_input,
    render_prompt,
    run_claude,
)

eval_setups = {
    # model_name: base_model_version, prompt_formatter_name
    # "old_claude": ("sonnet3.5", "binks-claude-v4"),
    # "v8": ("sonnet3.5-v2", "binks-claude-v8"),
    "v11": ("sonnet3.5-v2", "binks-claude-v11"),
    "v14.1": ("sonnet3.7", "binks-claude-v14"),
}
prompt_formatters = {}
for model_name, (
    base_model_version,
    prompt_formatter_name,
) in eval_setups.items():
    prompt_formatter = get_structured_chat_prompt_formatter_by_name(
        prompt_formatter_name,  # type: ignore
        TOKEN_APPORTIONMENT,
    )
    prompt_formatters[model_name] = prompt_formatter

results = []


for example_index, request_id in enumerate(FIRST_FAIL_REQUEST_IDS):
    print("=" * 40)
    chat_prompt_input, _ = get_input_and_documents(request_id)
    print(chat_prompt_input.message.splitlines()[-1])
    print(f"Example {example_index}/{len(FIRST_FAIL_REQUEST_IDS)}")
    example_results = {}
    for model_name, (
        base_model_version,
        prompt_formatter_name,
    ) in eval_setups.items():
        prompt_formatter = prompt_formatters[model_name]
        chat_prompt_input = fix_input(chat_prompt_input, 4)
        prompt_output = prompt_formatter.format_prompt(chat_prompt_input)
        try:
            response, _ = run_claude(
                message_or_prompt=prompt_output,
                base_model_version=base_model_version,
                retry_limit=3,
            )
        except Exception as e:
            print(f"Error running {model_name}: {e}")

            # return {
            #     "opening_count": opening_count,
            #     "closing_count": closing_count,
            #     "accuracy": accuracy,
            #     "extra_diff_line_count": extra_diff_line_count,
            #     "no_extra_change": no_extra_change,
            #     "response": response,
            #     "prompt_input": chat_prompt_dict_from_input(chat_prompt_input),
            #     "prompt_output": render_prompt(prompt_output),
            # }
            example_results[model_name] = {
                "opening_count": 1,
                "closing_count": 0,
                "accuracy": 0,
                "extra_diff_line_count": 0,
                "no_extra_change": 0,
                "response": "",
                "prompt_input": chat_prompt_dict_from_input(chat_prompt_input),
                "prompt_output": render_prompt(prompt_output),
            }
            continue
        example_results[model_name] = get_example_result(
            response, model_name, chat_prompt_input, prompt_output
        )
    results.append(example_results)
    print(flush=True)
    print(f"Accuracies up to {example_index}/{len(FIRST_FAIL_REQUEST_IDS)}")
    for model_name in results[0].keys():
        print(
            f"{model_name}:"
            f" {sum([r[model_name]['accuracy'] for r in results]) / len(results):.1%}"
        )
    print(flush=True)
    print(
        f"No extra change up ratios up to {example_index}/{len(FIRST_FAIL_REQUEST_IDS)}"
    )
    for model_name in results[0].keys():
        print(
            f"{model_name}:"
            f" {sum([r[model_name]['no_extra_change'] for r in results]) / len(results):.1%}"
        )
    print(flush=True)
    print(
        f"Average extra diff lines up to {example_index}/{len(FIRST_FAIL_REQUEST_IDS)}"
    )
    for model_name in results[0].keys():
        print(
            f"{model_name}:"
            f" {sum([r[model_name]['extra_diff_line_count'] for r in results]) / len(results):.1f}"
        )
    with open("/mnt/efs/augment/user/zhuoran/prompt/eval_first8.json", "w") as f:
        json.dump(results, f, indent=4)
    print(flush=True)
