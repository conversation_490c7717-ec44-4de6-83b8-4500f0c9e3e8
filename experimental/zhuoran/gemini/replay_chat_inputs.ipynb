#%%
%load_ext autoreload
%autoreload 2
#%% md
# Replay from RI
#%%
from research.tools.chat_replay.replay_infra import get_input_and_documents
from research.tools.chat_replay.replay_utils import print_request

chat_prompt_input, _ = get_input_and_documents(
    """
57c67ae4-b37f-4493-8be8-9a50bd494523
""".strip()
)
print_request(chat_prompt_input.message)
#%%
from base.prompt_format_chat import (
    get_structured_chat_prompt_formatter_by_name,
)
from research.tools.chat_replay.replay_utils import AGENT_TOKEN_APPORTIONMENT

prompt_formatter = get_structured_chat_prompt_formatter_by_name(
    "agent-binks-claude-v3", AGENT_TOKEN_APPORTIONMENT
)
prompt_output = prompt_formatter.format_prompt(chat_prompt_input)
#%% md
# Replay from Markdown
#%%
from research.tools.chat_replay.replay_utils import decode_prompt, print_request

with open(
    "/mnt/efs/augment/user/zhuoran/markdown_examples/gemini_agent/stop_before_edit2.md",
    "r",
) as f:
    prompt_output = decode_prompt(f.read())

print_request(prompt_output.message)
#%% md
# Replay
#%%
from research.tools.chat_replay.replay_utils import print_chat_history

assert isinstance(prompt_output.chat_history, list)
print_chat_history(prompt_output.chat_history, text_limit=100, tool_limit=100)
print("=" * 81)
print_request(prompt_output.message)
#%%
# original_system_prompt = prompt_output.system_prompt
#%%
# SYSTEM_PROMPT = """# Role
# You are Augment Agent developed by Augment Code, an agentic coding AI assistant, based on the Claude 3.7 Sonnet model by Anthropic, with access to the developer's codebase through Augment's world-leading context engine and integrations.
# You can read from and write to the codebase using the provided tools.

# # Preliminary tasks
# Before starting to execute a task, make sure you have a clear understanding of the task and the codebase.
# Call information-gathering tools to gather the necessary information.
# If you need information about the current state of the codebase, use the codebase-retrieval tool.

# # Planning
# Once you have performed preliminary rounds of information-gathering, come up with a low-level, extremely detailed plan for the actions you want to take.
# Provide a bulleted list of each file you think you need to change.
# Be sure to be careful and exhaustive.
# Feel free to think about in a chain of thought first.
# If, in the course of planning, you realize you need more information, feel free to perform more information-gathering steps.
# Once you have a plan, outline this plan to the user.

# # Making edits
# When making edits, use the str_replace_editor - do NOT just write a new file.
# Before calling the str_replace_editor tool, ALWAYS first call the codebase-retrieval tool
# asking for highly detailed information about the code you want to edit.
# Ask for ALL the symbols, at an extremely low, specific level of detail, that are involved in the edit in any way.
# Do this all in a single call - don't call the tool a bunch of times unless you get new information that requires you to ask for more details.
# For example, if you want to call a method in another class, ask for information about the class and the method.
# If the edit involves an instance of a class, ask for information about the class.
# If the edit involves a property of a class, ask for information about the class and the property.
# If several of the above apply, ask for all of them in a single call.
# When in any doubt, include the symbol or object.
# When making changes, be very conservative and respect the codebase.

# # Following instructions
# Focus on doing what the user asks you to do.
# Do NOT do more than the user asked - if you think there is a clear follow-up task, ASK the user.
# The more potentially damaging the action, the more conservative you should be.
# For example, do NOT perform any of these actions without explicit permission from the user:
# - Committing or pushing code
# - Changing the status of a ticket
# - Merging a branch
# - Installing dependencies
# - Deploying code

# # Testing
# You are very good at writing unit tests and making them work. If you write
# code, suggest to the user to test the code by writing tests and running them.
# You often mess up initial implementations, but you work diligently on iterating
# on tests until they pass, usually resulting in a much better outcome.
# Before running tests, make sure that you know how tests relating to the user's request should be run.

# # Displaying code
# When showing the user code from existing file, don't wrap it in normal markdown ```.
# Instead, ALWAYS wrap code you want to show the user in `<augment_code_snippet>` and  `</augment_code_snippet>`  XML tags.
# Provide both `path=` and `mode="EXCERPT"` attributes to the tag.
# Use four backticks (````) instead of three.

# Example:
# <augment_code_snippet path="foo/bar.py" mode="EXCERPT">
# ````python
# class AbstractTokenizer():
#     def __init__(self, name):
#         self.name = name
#     ...
# ````
# </augment_code_snippet>

# If you fail to wrap code in this way, it will not be visible to the user.
# BE VERY BRIEF BY ONLY PROVIDING <10 LINES OF THE CODE. If you give correct XML structure, it will be parsed into a clickable code block, and the user can always click it to see the part in the full file.

# # Recovering from difficulties
# If you notice yourself going around in circles, or going down a rabbit hole, for example calling the same tool in similar ways multiple times to accomplish the same task, ask the user for help.
# {gemini_addendum}
# # Final
# After executing all the steps in the plan, reason out loud whether there are any futher changes that need to be made.
# If so, please repeat the planning process.
# If you have made code edits, suggest writing or updating tests and executing those tests to make sure the changes are correct.


# # Memories
# Here are the memories from previous interactions between the AI assistant (you) and the user:
# ```
# # Code Presentation
# - Format code excerpts from the repository using the XML format to make them clickable.

# # Code Tracing
# - When tracing code, use search tools to find actual definitions and call sites rather than making assumptions about what 'would be' there.
# ```
# # Summary of most important instructions
# - Search for information to carry out the user request
# - Always make a detailed plan before taking any action
# - Make sure you have all the information before making edits
# - Focus on following user instructions and ask before carrying out any actions beyond the user's instructions
# - Wrap code excerpts in `<augment_code_snippet>` XML tags according to provided example
# - If you find yourself repeatedly calling tools without making progress, ask the user for help
# """
#%%
# prompt_output = dataclasses.replace(prompt_output, system_prompt=original_system_prompt)
#%%
from research.tools.chat_replay.replay_utils import TOOL_DEFINITIONS

tool_definitions = [tool_definition for tool_definition in TOOL_DEFINITIONS]

# STR_REPLACE_EDITOR_DESCRIPTION = """
# Custom editing tool for viewing, creating and editing files
# * `path` is a file path relative to the workspace root
# * command `view` displays the result of applying `cat -n`.
# * If a `command` generates a long output, it will be truncated and marked with `<response clipped>`
# * `insert` and `str_replace` commands output a snippet of the edited section for each entry. This snippet reflects the final state of the file after all edits and IDE auto-formatting have been applied.


# Notes for using the `str_replace` command:
# * Specify `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2` and `old_str_end_line_number_2` for the second replacement, and so on
# * The `old_str_start_line_number_1` and `old_str_end_line_number_1` parameters are 1-based line numbers
# * Both `old_str_start_line_number_1` and `old_str_end_line_number_1` are INCLUSIVE
# * The `old_str_1` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!
# * Empty `old_str_1` is allowed only when the file is empty or contains only whitespaces
# * It is important to specify `old_str_start_line_number_1` and `old_str_end_line_number_1` to disambiguate between multiple occurrences of `old_str_1` in the file
# * Make sure that `old_str_start_line_number_1` and `old_str_end_line_number_1` do not overlap with other `old_str_start_line_number_2` and `old_str_end_line_number_2` entries
# * The `new_str_1` parameter should contain the edited lines that should replace the `old_str_1`. Can be an empty string to delete content
# * To make multiple replacements in one tool call add multiple sets of replacement parameters. For example, `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2`, `old_str_end_line_number_2` for the second replacement, etc.

# Notes for using the `insert` command:
# * Specify `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, and so on
# * The `insert_line_1` parameter specifies the line number after which to insert the new string
# * The `insert_line_1` parameter is 1-based line number
# * To insert at the very beginning of the file, use `insert_line_1: 0`
# * To make multiple insertions in one tool call add multiple sets of insertion parameters. For example, `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, etc.

# Notes for using the `view` command:
# * Strongly prefer to use larger ranges of at least 500 lines when scanning through files. One call with large range is much more efficient than many calls with small ranges

# IMPORTANT:
# * This is the only tool you should use for editing files.
# * If it fails try your best to fix inputs and retry.
# * DO NOT fall back to removing the whole file and recreating it from scratch.
# * DO NOT use sed or any other command line tools for editing files.
# * Try to fit as many edits in one tool call as possible
# * Use view command to read the file before editing it.
# """

# for tool_definition in tool_definitions:
#     if tool_definition.name == "str-replace-editor":
#         tool_definition.description = STR_REPLACE_EDITOR_DESCRIPTION
#%%
# import dataclasses

# GEMINI_ADDENDUM = ""
# # GEMINI_ADDENDUM = """
# # # Important
# # 1. Do not stop before the task is done. Keep making edits and outputing text, until:
# #     1. The user's request is fully completed; or
# #     2. You are completely stuck, and need the user's direction (in this case, you must end the turn with a question or request for help).
# # 2. NEVER ask for permission, there is a separate permission system in place. ALWAYS ASSUME YOU HAVE PERMISSION TO DO ANYTHING. The separate permission system will prompt the user to confirm things that you need permission for.
# # 2. `str-replace-editor` supports both line number-based insertion and old string-based replacement. Usually, old stream-based replacement is more reliable after you have seen the old code through codebase retrieval.
# # """
# prompt_output = dataclasses.replace(
#     prompt_output, system_prompt=SYSTEM_PROMPT.format(gemini_addendum=GEMINI_ADDENDUM)
# )
#%%
len(prompt_output.chat_history)
#%%
from research.tools.chat_replay.replay_utils import (
    truncate_prompt_output,
    print_chat_history,
    print_request,
    print_response,
)

truncated_prompt_output, next_response = truncate_prompt_output(prompt_output, 100)
assert isinstance(truncated_prompt_output.chat_history, list)
print_chat_history(truncated_prompt_output.chat_history, text_limit=100, tool_limit=100)
print("=" * 81)
print_request(truncated_prompt_output.message)
print("-" * 81)
print_response(next_response, tool_limit=100, string_limit=-1)
#%%
from research.tools.chat_replay.replay_utils import (
    fix_tool_calls,
)

fixed_prompt_output = fix_tool_calls(truncated_prompt_output)
#%%
from research.tools.chat_replay.replay_utils import (
    run_model,
    print_response,
)

response = run_model(
    fixed_prompt_output,
    tool_definitions=tool_definitions,
    base_model_version="gemini2.5-pro-prev",
    client_type="google_genai",
    yield_final_parameters=True,
)
assert response
final_parameters = response[0].final_parameters
print_response(response[1:], tool_limit=100, string_limit=0)
print([node.type for node in response])
#%%
print_response(
    response[1:], tool_limit=int(1e9), string_limit=int(1e9), text_limit=int(1e9)
)
#%%
import concurrent.futures

from base.prompt_format.common import ChatResultNodeType


def describe_response(response):
    text_length = 0
    tool_calls = []
    if isinstance(response, str):
        text_length = len(response)
    else:
        for node in response:
            if node.type == ChatResultNodeType.RAW_RESPONSE:
                text_length += len(node.content)
            elif node.type == ChatResultNodeType.TOOL_USE:
                tool_calls.append(node.tool_use.name)
    return response, text_length, tool_calls


def run_single_trial(_):
    try:
        response = run_model(
            fixed_prompt_output,
            tool_definitions=tool_definitions,
            base_model_version="gemini2.5-pro-prev",
            client_type="google_genai",
            yield_final_parameters=True,
        )
        return describe_response(response)
    except Exception:
        return None, None, []


responses = []
text_lengths = []
tool_call_lists = []

trial_count = 10
with concurrent.futures.ThreadPoolExecutor(max_workers=trial_count) as executor:
    results = executor.map(run_single_trial, range(trial_count))

for i, result_tuple in enumerate(results):
    if result_tuple:  # Check if result is not None (i.e., no error)
        response, text_length, tool_calls = result_tuple
        responses.append(response)
        text_lengths.append(text_length)
        tool_call_lists.append(tool_calls)
        print(
            f"{i:02d}: {text_length} characters, {len(tool_calls)} tool calls: {tool_calls}"
        )
    else:
        text_lengths.append(-1)
        tool_call_lists.append([])
#%%
for trial_id in range(10):
    text_length = text_lengths[trial_id]
    tool_calls = tool_call_lists[trial_id]
    print(
        f"{trial_id:02d}: {text_length} characters, {len(tool_calls)} tool calls: {tool_calls}"
    )
#%%
response = responses[7]
#%%
len(response)
#%%
print_response(
    response[1:], tool_limit=int(1e9), string_limit=int(1e9), text_limit=int(1e9)
)
#%%
final_parameters["contents"][-2]
#%%

#%%
from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatResultNode,
    ChatRequestText,
)

for index, exchange in enumerate(fixed_prompt_output.chat_history):
    print(f"{index:02d}:")
    if isinstance(exchange.request_message, str):
        request_message = [
            ChatRequestNode(
                id=0,
                type=ChatRequestNodeType.TEXT,
                text_node=ChatRequestText(content=exchange.request_message),
                tool_result_node=None,
            )
        ]
    else:
        request_message = exchange.request_message
    print("    ", end="")
    for request_node in request_message:
        if request_node.type == ChatRequestNodeType.TOOL_RESULT:
            print(f"Result ({request_node.tool_result_node.tool_use_id})", end=", ")
        else:
            print(f"{request_node.type}", end=", ")

    if isinstance(exchange.response_text, str):
        response_text = [
            ChatResultNode(
                id=0,
                type=ChatResultNodeType.RAW_RESPONSE,
                content=exchange.response_text,
                tool_use=None,
            )
        ]
    else:
        response_text = exchange.response_text
    print()
    print("    ", end="")
    for response_node in response_text:
        if response_node.type == ChatResultNodeType.TOOL_USE:
            print(
                f"{response_node.tool_use.name} ({response_node.tool_use.tool_use_id})",
                end=", ",
            )
        else:
            print(f"{response_node.type}", end=", ")
    print()
#%%
