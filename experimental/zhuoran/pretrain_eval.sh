JOB_NAME="1b_lr32_cd55_dep"
STEP=95000

SUMMARY_PATH=${HOME}/summaries/${JOB_NAME}.json
# SUMMARY_PATH=${HOME}/t.json
rm ${SUMMARY_PATH}
    # --checkpoint=/mnt/efs/augment/user/zhuoran/experiments/${JOB_NAME}/checkpoint_llama_iteration_${STEP} \
python /home/<USER>/augment/research/eval/eval.py \
    --skip_bazel \
    --podspec=ampere-small \
    --summary_path=${SUMMARY_PATH} \
    --wait_for_completion \
    --model_name=fastbackward_starcoder \
    --job_name_suffix=${JOB_NAME} \
    --checkpoint=4dd434cd-279c-4489-bc6e-28648d48abe5 \
    /home/<USER>/augment/research/eval/configs/pretrain/*.yml
