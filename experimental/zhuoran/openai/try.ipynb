#%%
%load_ext autoreload
%autoreload 2
#%% md
# Claude
#%%
import time
import random
import string

from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient

REGION = "us-east5"
PROJECT_ID = "augment-387916"
MODEL_NAME = "claude-3-5-sonnet@20240620"
TEMPERAURE = 0
MAX_OUTPUT_TOKENS = 1024 * 8

client = AnthropicVertexAiClient(
    region=REGION,
    project_id=PROJECT_ID,
    model_name=MODEL_NAME,
    temperature=TEMPERAURE,
    max_output_tokens=MAX_OUTPUT_TOKENS,
)
start_time = time.time()

tools = [
    {
        "name": "find_secret",
        "description": "Find the secret given a secret key.",
        "input_schema": {
            "type": "object",
            "properties": {
                "key": {"type": "string"},
            },
            "required": ["key"],
        },
    }
]

# Random key of length 10000
key = "".join(
    random.choice(string.ascii_lowercase + string.digits) for _ in range(9999)
)

with client.client.messages.stream(
    model=MODEL_NAME,
    max_tokens=MAX_OUTPUT_TOKENS,
    messages=[
        {
            "role": "user",
            "content": f"What's the secret for {key}",
        },
    ],
    tools=tools,
    system="",
    temperature=TEMPERAURE,
) as stream:
    for chunk in stream:
        current_time = time.time()
        delta = current_time - start_time
        if delta > 0.01:
            print(f"{delta * 1000:.1f}ms:", end=" ")
            start_time = current_time
            print(chunk)
#%%
import anthropic

from research.environments import get_eng_secret


anthropic_api_key = get_eng_secret("seal-research-anthropic-key")
client = anthropic.Anthropic(api_key=anthropic_api_key, max_retries=1)
#%%
from base.prompt_format_chat.lib.system_prompts import (
    get_agent_system_prompt_formatter_v2,
)

from base.prompt_format_chat.lib.token_counter_claude import ClaudeTokenCounter

token_counter = ClaudeTokenCounter()

system_prompt = get_agent_system_prompt_formatter_v2(
    token_counter=token_counter,
    model_name="Claude Sonnet 4",
    creator="Zhuoran",
).format({})

print(system_prompt)

messages = [
    {
        "role": "user",
        # "content": "Write a hello world program in Python.",
        "content": "What's your base model?",
    },
]
response = client.messages.create(  # type: ignore
    max_tokens=32_000,
    messages=messages,
    model="claude-sonnet-4-0",
    system=system_prompt,
    # model="claude-3-7-sonnet-20250219",
    # model="claude-lace-eap",
    temperature=1,
    # extra_body={
    #     "thinking": {
    #         "type": "enabled",
    #         "budget_tokens": 32_000,
    #     },
    # },
    stream=True,
)
response
#%%
chunks = [chunk for chunk in response]
#%%
# for chunk in chunks:
#     print(chunk.type, chunk)
#%%
for chunk in chunks:
    try:
        print(chunk.delta.text, end="")
    except AttributeError:
        pass
#%%
len(chunks)
#%%
index = -1
#%%
from anthropic import TextEvent

index += 1
chunk = chunks[index]
print(chunk)
print(isinstance(chunk, TextEvent))
#%%
print(response.content[0].text)
#%% md
# Grok
#%%
import openai

grok_api_key = "************************************************************************************"

client = openai.OpenAI(
    base_url="https://api.x.ai/v1",
    api_key=grok_api_key,
)
response = client.chat.completions.create(  # type: ignore
    model="grok-2",
    messages=[
        {"role": "user", "content": "Write a hello world program in Python."},
    ],
    stream=True,
)
for chunk in response:
    print(chunk.choices[0].delta.content, end="")
#%%
from openai import BadRequestError, NotFoundError

function_spec = {
    "name": "write_hello_world",
    "description": "Writes a hello world program.",
}

for model in [
    "grok-2-1212",
]:
    print(f'"{model}": {{')
    for feature, system_role, streaming, kwargs, additional_messages in [
        ("system_prompt", "system", False, {}, []),
        ("streaming", "user", True, {}, []),
        ("temperature", "user", False, {"temperature": 0.5}, []),
        (
            "function_calling",
            "user",
            False,
            {"functions": [function_spec]},
            [
                {
                    "role": "function",
                    "name": "write_hello_world",
                    "content": "print('Hello, world!')",
                }
            ],
        ),
    ]:
        try:
            response = client.chat.completions.create(  # type: ignore
                model=model,
                messages=[
                    {"role": system_role, "content": "You are a helpful assistant."},
                    {
                        "role": "user",
                        "content": "Write a hello world program in Python.",
                    },
                    *additional_messages,
                ],
                stream=streaming,
                **kwargs,
            )
            print(f'    "{feature}": True,')
        except (BadRequestError, NotFoundError):
            print(f'    "{feature}": False,')
    print("},")
#%% md
# OpenAI
#%%
import openai

openai_api_key = open("/mnt/efs/augment/user/zhuoran/.tokens/openai").read().strip()
client = openai.OpenAI(api_key=openai_api_key)
#%%
from openai import BadRequestError, NotFoundError

function_spec = {
    "name": "write_hello_world",
    "description": "Writes a hello world program.",
}

for model in [
    # "gpt-4o-2024-08-06",
    # "gpt-4o-2024-11-20",
    # "o1-preview-2024-09-12",
    # "o1-mini-2024-09-12",
    # "o1-2024-12-17",
    # "o3-mini-2025-01-31",
    "o3-2025-04-16",
    "o4-mini-2025-04-16",
    # "chatgpt-4o-latest",
    # "gpt-4.5-preview-2025-02-27",
]:
    print(f'"{model}": {{')
    for feature, system_role, streaming, kwargs, additional_messages in [
        ("system_prompt", "system", False, {}, []),
        ("streaming", "user", True, {}, []),
        ("temperature", "user", False, {"temperature": 0.5}, []),
        (
            "function_calling",
            "user",
            False,
            {"functions": [function_spec]},
            [
                {
                    "role": "function",
                    "name": "write_hello_world",
                    "content": "print('Hello, world!')",
                }
            ],
        ),
    ]:
        try:
            response = client.chat.completions.create(  # type: ignore
                model=model,
                messages=[
                    {"role": system_role, "content": "You are a helpful assistant."},
                    {
                        "role": "user",
                        "content": "Write a hello world program in Python.",
                    },
                    *additional_messages,
                ],
                stream=streaming,
                **kwargs,
            )
            print(f'    "{feature}": True,')
        except (BadRequestError, NotFoundError):
            print(f'    "{feature}": False,')
    print("},")
#%%
import json


def get_time():
    return "12:16"


function_spec = {
    "name": "get_time",
    "description": "Fetches the current time.",
}

model_name = "o1-preview"
response = client.chat.completions.create(
    model=model_name,
    messages=[
        {"role": "user", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What's the time now?"},
    ],
    functions=[function_spec],  # type: ignore
)

# # Check if a function call is needed
# if response.choices[0].finish_reason == "function_call":
#     function_name = response.choices[0].message.function_call.name
#     arguments = json.loads(response.choices[0].message.function_call.arguments)

#     if function_name == "get_time":
#         result = get_time()
#         # Send the result back to the model
#         follow_up_response = client.chat.completions.create(
#             model="o1",
#             messages=[
#                 {"role": "system", "content": "You are a helpful assistant."},
#                 {
#                     "role": "user",
#                     "content": "What's the time now?",
#                 },
#                 {"role": "function", "name": function_name, "content": result},
#             ],
#         )
#         print(follow_up_response.choices[0].message.content)
# else:
#     print(response.choices[0].message.content)
#%%
print(response)
#%%
print(response)
#%% md
# Gemini
#%%
import io

from google.genai import Client
from google.genai.types import (
    Content,
    FunctionCall,
    GenerateContentConfig,
    Part,
    ThinkingConfig,
)
from PIL import Image


def make_fake_image_bytes():
    img = Image.new("RGB", (60, 30), color="black")
    buffered = io.BytesIO()
    img.save(buffered, format="JPEG")
    return buffered.getvalue()


def format_preexisting_chat_history():
    image_data = make_fake_image_bytes()

    history = [
        Content(
            role="user",
            parts=[Part.from_text(text="What's the weather like in San Jose?")],
        ),
        Content(
            role="model",
            parts=[
                Part(
                    function_call=FunctionCall(
                        name="get_weather",
                        args={
                            "location": "San Jose, CA",
                        },
                    )
                )
            ],
        ),
        Content(
            role="function",
            parts=[
                Part.from_function_response(
                    name="get_weather",
                    response={
                        "content": {
                            "temperature": "70°F",
                            "condition": "Sunny",
                        }
                    },
                )
            ],
        ),
        Content(
            role="model",
            parts=[
                Part.from_text(
                    text="The weather in San Jose is currently sunny with a temperature of 70°F."
                )
            ],
        ),
        Content(
            role="user",
            parts=[
                Part.from_text(text="What is in this picture?"),
                Part.from_bytes(data=image_data, mime_type="image/jpeg"),
            ],
        ),
        Content(
            role="model",
            parts=[Part.from_text(text="The picture is black.")],
        ),
    ]
    return history


PROJECT_ID = "augment-research-gsc"
REGION = "us-central1"
model_name = "gemini-2.5-pro-preview-05-06"

client = Client(vertexai=True, project=PROJECT_ID, location=REGION)

history_list = format_preexisting_chat_history()

config = GenerateContentConfig(
    max_output_tokens=2048,
    temperature=0.2,
    thinking_config=ThinkingConfig(include_thoughts=True),
)

iterator = client.models.generate_content_stream(
    model=model_name,
    contents=history_list  # type: ignore
    + [
        Content(
            role="user",
            parts=[
                Part.from_text(text="Write a long story of Dinosaurs."),
                Part.from_text(
                    text="Then, summarize the our conversation again, very concisely."
                ),
                Part.from_text(text="Think before you answer."),
            ],
        )
    ],
    # + [
    #     Content(
    #         role="model",
    #         parts=[
    #             Part.from_text(text="In Jurassic World: Camp Cretaceous,"),
    #         ],
    #     )
    # ],
    config=config,
)
chunks = []
for chunk in iterator:
    print(chunk)
    chunks.append(chunk)
    # print(chunk.candidates[0].content.parts[0].text, end="")  # type: ignore
#%%
len(chunks)
#%%
from research.tools.chat_replay.replay_utils import get_attrs

# chunk = chunks[-1]
# print(get_attrs(chunk))
# chunk
response_ids = set()
for chunk in chunks:
    assert "response_id" in get_attrs(chunk)
    response_ids.add(chunk.response_id)
response_ids
#%%
from google.genai import Client
from google.genai.types import (
    Content,
    GenerateContentConfig,
    Part,
    ThinkingConfig,
)


PROJECT_ID = "augment-research-gsc"
REGION = "us-central1"
model_name = "gemini-2.5-pro-preview-03-25"

client = Client(vertexai=True, project=PROJECT_ID, location=REGION)

config = GenerateContentConfig(
    max_output_tokens=2048,
    temperature=0.2,
    thinking_config=ThinkingConfig(include_thoughts=True, thinking_budget=1000),
)

iterator = client.models.generate_content_stream(
    model=model_name,
    contents=[
        Content(
            role="user",
            parts=[
                Part.from_text(text="How many R's are there in strawberry?"),
            ],
        )
    ],
    config=config,
)
for chunk in iterator:
    print(chunk)
    # print(chunk.candidates[0].content.parts[0].text, end="")  # type: ignore
#%%
from google.genai import Client
from google.genai.types import (
    Content,
    GenerateContentConfig,
    Part,
    FunctionDeclaration,
    Schema,
)


PROJECT_ID = "augment-research-gsc"
REGION = "us-central1"
model_name = "gemini-2.5-pro-exp-03-25"

client = Client(vertexai=True, project=PROJECT_ID, location=REGION)

history_list = format_preexisting_chat_history()

config = GenerateContentConfig(
    max_output_tokens=2048,
    temperature=0.2,
    tools=[
        FunctionDeclaration(
            name="get_weather",
            description="Get current temperature for a given location.",
            parameters=Schema(
                type="OBJECT",
                properties={
                    "location": {
                        "type": "STRING",
                        "description": "City and country e.g. Bogotá, Colombia",
                    }
                },
                required=["location"],
            ),
        )
    ],
)

iterator = client.models.generate_content_stream(
    model=model_name,
    contents=[
        Content(
            role="user",
            parts=[
                Part.from_text("How's the weather in San Jose?"),
            ],
        )
    ],
    config=config,
)
for chunk in iterator:
    # print(chunk)
    print(chunk.candidates[0].content.parts[0].text, end="")  # type: ignore
#%% md
# Augment clients
#%%
%load_ext autoreload
%autoreload 2
#%%
import os

from base.augment_client.client import AugmentClient
from base.augment_client.client import AugmentModelClient

client = AugmentClient(
    url="https://staging-shard-0.api.augmentcode.com",
    token=os.environ["AUGMENT_TOKEN"],
)
model_client = AugmentModelClient(client, "")
stream = model_client.chat_stream(
    selected_code="",
    message="Write a quick sort, be brief",
    prefix="",
    suffix="",
    path="",
    user_guidelines="Answer in Chinese",
)
for r in stream:
    print(r.text)
#%%
from openai import OpenAI

openai_api_key = open("/mnt/efs/augment/user/zhuoran/.tokens/openai").read().strip()
client = OpenAI(api_key=openai_api_key)

tools = [
    {
        "type": "function",
        "function": {
            "name": "get_a",
            "description": "Get the value of a.",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": [],
                "additionalProperties": False,
            },
            "strict": True,
        },
    },
    {
        "type": "function",
        "function": {
            "name": "get_b",
            "description": "Get the value of b.",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": [],
                "additionalProperties": False,
            },
            "strict": True,
        },
    },
    {
        "type": "function",
        "function": {
            "name": "git",
            "description": "Use git.",
            "parameters": {
                "type": "object",
                "properties": {
                    "args": {
                        "type": "object",
                        "properties": {
                            "command": {
                                "type": "string",
                                "description": "The git command to run.",
                            },
                            "args": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "The arguments to pass to the git command.",
                            },
                        },
                        "description": "Arguments to pass to git.",
                    }
                },
                "required": ["args"],
            },
        },
    },
]

response = client.chat.completions.create(  # type: ignore
    model="o3-mini-2025-01-31",
    messages=[
        {
            "role": "system",
            "content": "You are a helpful assistant.",
        },
        {"role": "user", "content": "What's the sum of a and b?"},
    ],
    # stream=False,
    stream=True,
    temperature=1,
    tools=tools,
    # tool_choice={"type": "function", "function": {"name": "get_a"}},
    # tools=None,
)
response_list = list(response)
response_list
#%%
for r in response_list:
    print(r.choices[0].delta.tool_calls)
#%%
from openai import OpenAI

openai_api_key = open("/mnt/efs/augment/user/zhuoran/.tokens/openai").read().strip()
client = OpenAI(api_key=openai_api_key)

tools = [
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "Get current temperature for a given location.",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "City and country e.g. Bogotá, Colombia",
                    }
                },
                "required": ["location"],
                "additionalProperties": False,
            },
            "strict": True,
        },
    },
    {
        "type": "function",
        "function": {
            "name": "get_location",
            "description": "Get current location.",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": [],
                "additionalProperties": False,
            },
            "strict": True,
        },
    },
]

response = client.chat.completions.create(  # type: ignore
    model="o3-mini-2025-01-31",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "How is the weather here?"},
        {
            "role": "assistant",
            "content": "",
            "tool_calls": [
                {
                    "type": "function",
                    "id": "call_FMCknos0j6MWKIcSkpQ2Y5uy",
                    "function": {
                        "name": "get_weather",
                        "arguments": '{"location": "Bogotá, Colombia"}',
                    },
                }
            ],
        },
        {
            "role": "tool",
            "tool_call_id": "call_FMCknos0j6MWKIcSkpQ2Y5uy",
            # "name": "get_weather",
            "content": "It's 32 degrees Celsius.",
        },
        # {"role": "assistant", "content": "The current weather in Bogotá, Colombia is"},
    ],
    # stream=False,
    stream=True,
    temperature=1,
    tools=tools,
    # tools=None,
)
response_list = list(response)
response_list
#%%
for r in response_list:
    print(r.choices[0].delta.tool_calls)
#%%
print(response.choices[0].message.content)
#%%

#%% md
# Augment clients
#%%
%load_ext autoreload
%autoreload 2
#%%
from base.third_party_clients.vertexai_client import VertexAiClient

client = VertexAiClient(
    project_id="augment-research-gsc",
    region="us-central1",
    model_name="gemini-2.5-pro-exp-03-25",
    temperature=0.7,
    max_output_tokens=1024,
)
#%%
stream = client.generate_response_stream(
    model_caller="trial-notebook",
    messages=[],
    system_prompt="You are a helpful assistant.",
    cur_message="Write a hello world program in Python.",
)
for r in stream:
    print(r.text, r.replace_text_response, r.tool_use)
#%%

#%% md
# DeepSeek
#%%
import requests

fireworks_api_key = (
    open("/mnt/efs/augment/user/zhuoran/.tokens/fireworks").read().strip()
)

url = "https://api.fireworks.ai/inference/v1/chat/completions"
payload = {
    # "model": "accounts/fireworks/models/deepseek-v3",
    # "model": "accounts/fireworks/models/llama-v3p3-70b-instruct",
    "model": "accounts/fireworks/models/deepseek-r1",
    "max_tokens": 16384,
    "top_p": 1,
    "top_k": 40,
    "presence_penalty": 0,
    "frequency_penalty": 0,
    "temperature": 0.6,
    "messages": [
        {"role": "user", "content": "What's the date today?"},
        {
            "role": "assistant",
            "content": "The date is 2023-10-10.",
        },
    ],
    "tools": [
        {
            "type": "function",
            "function": {
                "name": "get_current_date",
                "description": "Get the current date",
                "parameters": {
                    "type": "object",
                    "properties": {},
                },
            },
        }
    ],
    # "stream": True,
}
headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "Authorization": f"Bearer {fireworks_api_key}",
}
response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
# print(response.json()["choices"][0]["message"]["content"])
response.json()
#%%
response.request.__dir__()
#%%
from fireworks.client import Fireworks

client = Fireworks(api_key=fireworks_api_key)
response = client.chat.completions.create(
    model="accounts/fireworks/models/deepseek-v3",
    messages=[
        {
            "role": "user",
            "content": "What functions are available for you to call?",
        }
    ],
    tools=[
        {
            "type": "function",
            "function": {
                "name": "get_current_date",
                "description": "Get the current date",
                "parameters": {
                    "type": "object",
                    "properties": {},
                },
            },
        }
    ],
)
# print(response.choices[0].message.content)
response
#%%
response
#%%
response.json()
#%%
import requests
import json

openai_api_key = open("/mnt/efs/augment/user/zhuoran/.tokens/openai").read().strip()
url = "https://api.openai.com/v1/chat/completions"
payload = {
    "model": "gpt-4o",
    "max_tokens": 16384,
    "top_p": 1,
    "presence_penalty": 0,
    "frequency_penalty": 0,
    "temperature": 0.6,
    "messages": [{"role": "user", "content": "Write a hello world in Python"}],
}
headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "Authorization": f"Bearer {openai_api_key}",
}
response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
print(response.json()["choices"][0]["message"]["content"])
#%%
import requests
import json

url = "https://api.openai.com/v1/chat/completions"
payload = {
    "model": "gpt-4o",
    "max_tokens": 16384,
    "top_p": 1,
    "presence_penalty": 0,
    "frequency_penalty": 0,
    "temperature": 0.6,
    "messages": [{"role": "user", "content": "Write a hello world in Python"}],
    "stream": True,  # Enable streaming
}
headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "Authorization": f"Bearer {openai_api_key}",
}

# Make streaming request
response = requests.request(
    "POST", url, headers=headers, data=json.dumps(payload), stream=True
)

buffer = ""
# Process the stream chunk by chunk
for chunk in response.iter_content(chunk_size=1):
    if chunk:
        buffer += chunk.decode("utf-8")
        if buffer.endswith("\n"):
            lines = buffer.split("\n")
            for line in lines[:-1]:  # Process all complete lines
                if line.startswith("data: "):
                    if line == "data: [DONE]":
                        break
                    try:
                        json_data = json.loads(line[6:])  # Skip "data: " prefix
                        content = json_data["choices"][0]["delta"].get("content", "")
                        if content:
                            print(content, end="\n", flush=True)
                    except json.JSONDecodeError:
                        continue
            buffer = lines[-1]  # Keep the incomplete line

# Process any remaining buffer content
if buffer:
    if buffer.startswith("data: ") and buffer != "data: [DONE]":
        try:
            json_data = json.loads(buffer[6:])
            content = json_data["choices"][0]["delta"].get("content", "")
            if content:
                print(content, end="\n", flush=True)
        except json.JSONDecodeError:
            pass

print()  # Final newline
#%%
import requests
import json

url = "https://api.openai.com/v1/chat/completions"
payload = {
    "model": "gpt-4o",
    "max_tokens": 16384,
    "top_p": 1,
    "presence_penalty": 0,
    "frequency_penalty": 0,
    "temperature": 0.6,
    "messages": [{"role": "user", "content": "What's today's date?"}],
    "tools": [
        {
            "type": "function",
            "function": {
                "name": "get_current_date",
                "description": "Get the current date",
                "parameters": {
                    "type": "object",
                    "properties": {},
                },
            },
        }
    ],
}
headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "Authorization": f"Bearer {openai_api_key}",
}
response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
# print(response.json()["choices"][0]["message"]["content"])
print(response.json())
#%%
%load_ext autoreload
%autoreload 2
#%%
from base.third_party_clients.fireworks_client import FireworksClient

fireworks_client = FireworksClient(
    api_key=fireworks_api_key,
    model_name="accounts/fireworks/models/deepseek-v3",
    temperature=0,
    max_output_tokens=8192,
)
response_iterator = fireworks_client.generate_response_stream(
    cur_message="Write a hello world in Python.",
)
text = ""
for chunk in response_iterator:
    print(chunk.text, end="\n", flush=True)
    text += chunk.text
#%%
print(text)
#%%
from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient

anthropic_client = AnthropicDirectClient(
    api_key="<API_KEY>",
    model_name="claude-3-5-sonnet-v2@********",
    temperature=0,
    max_output_tokens=8192,
)
#%%
from typing import Any, Dict
from unittest.mock import MagicMock

import pytest
import json

from base.prompt_format.common import Exchange
from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from base.third_party_clients.third_party_model_client import (
    ToolDefinition,
    ToolUseResponse,
)


def date_tool(args: Dict[str, Any]) -> str:
    """A simple tool that returns a fixed date."""
    return "2011-12-16"


input_schema = {
    "type": "object",
    "properties": {"debug": {"type": "boolean"}},
    "required": ["debug"],
}
tool_definitions = [
    ToolDefinition(
        name="get_date",
        description="Get the current date in YYYY-MM-DD format",
        input_schema_json=json.dumps(input_schema),
    )
]

client = AnthropicVertexAiClient(
    project_id="augment-387916",
    region="us-east5",
    model_name="claude-3-5-sonnet-v2@********",
    temperature=0.7,
    max_output_tokens=1024,
)

system_prompt = "You are a helpful assistant that can use tools. Always format dates as 'The date is MM-DD-YY'."
cur_message = "What's today's date?"
chat_history = [
    Exchange(
        request_message="What's your purpose?",
        response_text="I'm here to help!",
    ),
]

responses = []
tool_response = None
tool_used = False
response_stream = client.generate_response_stream(
    cur_message=cur_message,
    system_prompt=system_prompt,
    chat_history=chat_history,
    tool_definitions=tool_definitions,
    tool_choice={"type": "tool", "name": "get_date"},
)
#%%
chunks = [chunk for chunk in response_stream]
chunks
#%%
%load_ext autoreload
%autoreload 2
#%%
from base.prompt_format.common import (
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestToolResult,
    Exchange,
)

assert len(list(chunks)) == 1

tool_use = chunks[0].tool_use

assert tool_use
assert tool_use.tool_name == "get_date"

chat_history.append(
    Exchange(
        request_message=cur_message,
        response_text=[
            ChatResultNode(
                id=1,
                type=ChatResultNodeType.TOOL_USE,
                content="",
                tool_use=ChatResultToolUse(
                    name=tool_use.tool_name,
                    input=tool_use.input,
                    tool_use_id=tool_use.tool_use_id,
                ),
            )
        ],
    )
)

response_stream = client.generate_response_stream(
    cur_message=[
        ChatRequestNode(
            id=1,
            type=ChatRequestNodeType.TOOL_RESULT,
            text_node=None,
            tool_result_node=ChatRequestToolResult(
                tool_use_id=tool_use.tool_use_id,
                content="2011-12-16",
                is_error=False,
            ),
        )
    ],
    system_prompt=system_prompt,
    chat_history=chat_history,
    tool_definitions=tool_definitions,
)

chunks = [chunk for chunk in response_stream]
chunks
#%%
chunks
#%% md
# Research LLMs
#%%
from research.llm_apis.llm_client import OpenAIDirectClient

client = OpenAIDirectClient("gpt-4o")
#%%
from research.llm_apis.llm_client import TextPrompt

client.generate(
    messages=[[TextPrompt("What's today's date?")]],
    max_tokens=1024,
    # system_prompt="You are a helpful assistant.",
)
#%% md
