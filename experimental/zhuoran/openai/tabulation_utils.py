import json
import os

import pandas as pd

MODEL_ATTRIBUTES = {
    "claude": {
        1: (1, "Claude 3.5 Sonnet", "1.18.2", 0),
        1.1: (1, "<PERSON> 3.5 Sonnet", "1.18.3", 0),
        2: (2, "<PERSON> 3.5 Sonnet", "1.18.2", 0),
        2.1: (2, "Claude 3.5 Sonnet", "1.18.3", 0),
        3: (3, "Claude 3.5 Sonnet", "1.18.2", 0),
        3.1: (3, "Claude 3.5 Sonnet", "1.18.3", 0),
        4: (4, "Claude 3.5 Sonnet", "1.18.2", 0),
        4.1: (4, "Claude 3.5 Sonnet", "1.18.2", 0),
        4.2: (4, "Claude 3.5 Sonnet", "1.18.3", 0),
        5: (3, "<PERSON> 3.5 Sonnet V2", "1.18.2", 0),
        5.1: (3, "<PERSON> 3.5 Sonnet V2", "1.18.2", 0),
        5.2: (3, "Claude 3.5 Sonnet V2", "1.18.3", 0),
        6: (4, "Claude 3.5 Sonnet V2", "1.18.2", 0),
        6.1: (4, "<PERSON> 3.5 Sonnet V2", "1.18.2", 0),
        6.2: (4, "<PERSON> 3.5 Sonnet V2", "1.18.3", 0),
        7: (7, "<PERSON> 3.5 Sonnet V2", "1.18.2", 0),
        7.1: (7, "<PERSON> 3.5 Sonnet V2", "1.18.3", 0),
        8: (8, "<PERSON> 3.5 Sonnet V2", "1.18.2", 0),
        8.1: (8, "Claude 3.5 Sonnet V2", "1.18.3", 0),
        9: (8, "Claude 3.5 Sonnet V2", "1.18.2", 0),
        9.1: (8, "Claude 3.5 Sonnet V2", "1.18.3", 0),
        10: (4, "Claude 3.5 Sonnet", "1.18.3", 0),
        11: (11, "Claude 3.5 Sonnet V2", "1.18.3", 0),
        11.1: (11.1, "Claude 3.5 Sonnet V2", "1.18.3", 0),
        11.2: (11.2, "Claude 3.5 Sonnet V2", "1.18.3", 0),
        11.4: (11.1, "Claude 3.5 Sonnet V2", 4, 0),
        13: (13, "Claude 3.5 Sonnet V2", "1.18.3", 0),
        14: (14, "Claude 3.7 Sonnet", "1.18.3", 0),
        14.1: (14.1, "Claude 3.7 Sonnet", "1.18.3", 0),
        14.2: (14.2, "Claude 3.7 Sonnet", "1.18.3", 0),
        14.3: (14.3, "Claude 3.7 Sonnet", "1.18.3", 0),
        14.4: (14.4, "Claude 3.7 Sonnet", "1.18.3", 0),
        15: (15, "Claude 3.7 Sonnet", "1.18.3", 0),
        15.01: (15.01, "Claude 3.7 Sonnet", "1.18.3", 0),
        15.02: (15.02, "Claude 3.7 Sonnet", "1.18.3", 0),
        15.1: (15.1, "Claude 3.7 Sonnet", "1.18.3", 0),
        15.11: (15.11, "Claude 3.7 Sonnet", "3.1.1", 1),
        16: (16, "Claude 3.7 Sonnet", "3.1.1", 1),
        16.01: (16.01, "Claude 3.7 Sonnet", "3.1.1", 1),
        16.02: (16.02, "Claude 3.7 Sonnet", 4, 2),
        17: (17, "Claude 3.7 Sonnet", 4, 2),
        17.01: (17.01, "Claude 3.7 Sonnet", 4, 2),
        17.02: (17.02, "Claude 3.7 Sonnet", 4, 2),
    },
    "wool": {
        12.1: (12.1, "Claude Wool", "1.18.3", 0),
    },
    "flannel": {
        13: (13, "Claude Flannel", "1.18.3", 0),
    },
    "linen": {
        17: (17, "Claude Linen", 4, 2),
    },
    "lycra": {
        17: (17, "Claude Lycra", 4, 2),
    },
    "ombre": {
        16: (16, "Claude Ombre", 4, 2),
        17: (17, "Claude Ombre", 4, 2),
    },
    "lace": {
        16: (16, "Claude Lace", 4, 2),
        17: (17, "Claude Lace", 4, 2),
    },
    "o1_mini": {
        1: (1, "o1 mini", "1.18.2", 0),
        1.1: (1, "o1 mini", "1.18.3", 0),
        2: (3, "o1 mini", "1.18.2", 0),
        2.1: (3, "o1 mini", "1.18.3", 0),
        3: (4, "o1 mini", "1.18.2", 0),
        3.1: (4, "o1 mini", "1.18.3", 0),
        4: (7, "o1 mini", "1.18.2", 0),
        4.1: (7, "o1 mini", "1.18.3", 0),
        5: (8, "o1 mini", "1.18.2", 0),
        5.1: (8, "o1 mini", "1.18.3", 0),
    },
    "o1": {
        1: (1, "o1 preview", "1.18.2", 0),
        1.1: (1, "o1 preview", "1.18.3", 0),
        2: (3, "o1 preview", "1.18.2", 0),
        2.1: (3, "o1 preview", "1.18.3", 0),
        3: (4, "o1 preview", "1.18.2", 0),
        3.1: (4, "o1 preview", "1.18.3", 0),
        4: (7, "o1 preview", "1.18.2", 0),
        4.1: (7, "o1 preview", "1.18.3", 0),
        5: (8, "o1 preview", "1.18.2", 0),
        5.1: (8, "o1 preview", "1.18.3", 0),
        6: (4, "o1", "1.18.3", 0),
    },
    "o3_mini": {
        3: (4, "o3 mini", "1.18.3", 0),
    },
    "o3": {
        1: (3, "o3", None, None),
    },
    "o4_mini": {
        1: (3, "o4 mini", None, None),
    },
    "gpt4o_chatgpt": {
        1: (1, "ChatGPT latest", 4, 2),
        2: (1, "ChatGPT latest", "1.18.3", 0),
    },
    "gpt4o": {
        1: (1, "GPT-4o (2024-08-06)", "1.18.2", 0),
        1.1: (1, "GPT-4o (2024-08-06)", "1.18.3", 0),
        2: (1, "GPT-4o (2024-11-20)", "1.18.2", 0),
        2.1: (1, "GPT-4o (2024-11-20)", "1.18.3", 0),
        3: (3, "GPT-4o (2024-11-20)", "1.18.2", 0),
        3.1: (3, "GPT-4o (2024-11-20)", "1.18.3", 0),
    },
    "gpt4_1_mini": {
        1: (3, "GPT-4.1 mini", None, None),
    },
    "gpt4_1_nano": {
        1: (3, "GPT-4.1 nano", None, None),
    },
    "gpt4_1": {
        1: (3, "GPT-4.1", None, None),
    },
    "gpt4-5": {
        2: (3, "GPT-4.5 (2024-11-20)", "1.18.3", 0),
    },
    "gemini2_5_pro": {
        1: (0, "Gemini 2.5 Pro (exp)", 4, 2),
        2: (3, "Gemini 2.5 Pro (exp)", 4, 2),
        3: (0, "Gemini 2.5 Pro (preview)", 4, 2),
        4: (3, "Gemini 2.5 Pro (preview)", 4, 2),
        5: (0, "Gemini 2.5 Pro (preview 05-06)", 4, 2),
        6: (3, "Gemini 2.5 Pro (preview 05-06)", 4, 2),
    },
    "gemini2_5_flash": {
        1: (0, "Gemini 2.5 Flash (preview)", 4, 2),
        2: (3, "Gemini 2.5 Flash (preview)", 4, 2),
    },
    "gemini": {
        1: (0, "Gemini 2.0 Flash", "1.18.3", 0),
        2: (3, "Gemini 2.0 Flash", "1.18.3", 0),
        3: (3, "Gemini 2.0 Flash", "1.18.3", 0),
    },
    "deepseek_v3": {
        1: (3, "DeepSeek-V3", "1.18.3", 0),
        2: (3, "DeepSeek-V3 (03-24)", 4, 2),
    },
    "deepseek_r1": {
        1: (3, "DeepSeek-R1", "1.18.3", 0),
        2: (3, "DeepSeek-R1", "1.18.3", 0),
        2.1: (3, "DeepSeek-R1", 4, 2),
    },
    "qwen2-5": {
        1: (3, "Qwen2.5 Coder 32B", "1.18.3", 0),
    },
    "qwq": {
        1: (3, "QWQ 32B", "1.18.3", 0),
        2: (3, "QWQ 32B", "1.18.3", 0),
    },
    "grok2": {
        1: (3, "Grok 2", "1.18.3", 0),
    },
    "grok3_mini": {
        1: (3, "Grok 3 mini", None, None),
    },
    "grok3": {
        1: (3, "Grok 3", None, None),
    },
    "llama4_scout": {
        1: (3, "Llama 4 Scout", None, None),
    },
    "llama4_maverick": {
        1: (3, "Llama 4 Maverick", None, None),
    },
}


def reset_display():
    pd.reset_option("display.max_rows")
    pd.reset_option("display.max_columns")
    pd.reset_option("display.width")
    pd.reset_option("display.max_colwidth")


def set_unlimited_display():
    pd.set_option("display.max_rows", None)
    pd.set_option("display.max_columns", None)
    pd.set_option("display.width", None)
    pd.set_option("display.max_colwidth", None)


def read_results(result_paths):
    results_data = []
    for path in result_paths:
        label = os.path.splitext(os.path.basename(path))[0]
        with open(path) as file:
            result = json.load(file)
        run_result = list(result["runs"].values())[0]
        if "metrics" not in run_result["results"]:
            continue
        metrics = run_result["results"]["metrics"]
        # Add label to metrics dictionary
        metrics["label"] = label
        del metrics["samples"]
        metrics.pop("gold paths mrr", None)
        metrics.pop("gold_paths_mrr", None)
        if "keywords recall in answer" in metrics:
            metrics["answer_keyword_recall"] = metrics.pop("keywords recall in answer")
        if "keywords recall in retrievals" in metrics:
            metrics["retrievals_keyword_recall"] = metrics.pop(
                "keywords recall in retrievals"
            )
        if "gold paths recall" in metrics:
            metrics["gold_paths_recall"] = metrics.pop("gold paths recall")
        if "generation time" in metrics:
            metrics["generation_time"] = metrics.pop("generation time")
        # metrics["html_report_url"] = run_result["results"]["html_report_url"]
        results_data.append(metrics)

    # Create DataFrame from the collected data
    df = pd.DataFrame(results_data)
    # Reorder columns to put label first
    cols = ["label"] + [col for col in df.columns if col != "label"]
    df = df[cols]
    return df


def group_rows(df):
    # Extract base label by removing the _x suffix
    df["base_label"] = df["label"].str.replace(r"_\d+$", "", regex=True)

    # Group by base_label and calculate mean, std, and count
    # First limit rows per group, then perform aggregation
    df_limited = df.groupby("base_label").head(n=100)  # Adjust n as needed
    grouped_df = df_limited.groupby("base_label").agg(
        {
            "label": "count",
            **{
                col: ["mean", "std"]
                for col in df.columns
                if col not in ["label", "base_label"]
            },
        }
    )

    # Flatten column names
    grouped_df.columns = [
        f"{col[0]} {col[1]}" if col[1] != "count" else "count"
        for col in grouped_df.columns
    ]

    # Reset index to make base_label a regular column
    grouped_df = grouped_df.reset_index()

    # Filter out columns that are all zeros
    non_zero_cols = ["base_label", "count"] + [
        col
        for col in grouped_df.columns
        if col not in ["base_label", "count"] and not (grouped_df[col] == 0).all()
    ]
    grouped_df = grouped_df[non_zero_cols]
    return grouped_df


def combine_means_and_stds(data_frame):
    # Find pairs of "xxx mean" and "xxx std" columns
    mean_std_pairs = {}
    new_data_frame = data_frame.copy()
    for col in new_data_frame.columns:
        if col.endswith(" mean"):
            base_name = col[:-5]  # Remove " mean" suffix
            if f"{base_name} std" in new_data_frame.columns:
                mean_std_pairs[base_name] = (col, f"{base_name} std")
            else:
                new_data_frame[f"{base_name} std"] = 0
                mean_std_pairs[base_name] = (col, f"{base_name} std")

    # Create new columns with combined mean±std format
    for base_name, (mean_col, std_col) in mean_std_pairs.items():
        if base_name == "generation time":
            new_data_frame[base_name] = new_data_frame.apply(
                lambda row: f"{row[mean_col]:.01f}"
                if row[std_col] == 0
                else f"{row[mean_col]:.01f}±{row[std_col]:.01f}",
                axis=1,
            )
        else:
            new_data_frame[base_name] = new_data_frame.apply(
                lambda row: f"{row[mean_col]:.03f}"
                if row[std_col] == 0
                else f"{row[mean_col]:.03f}±{row[std_col]:.03f}",
                axis=1,
            )
    columns_to_drop = [col for pair in mean_std_pairs.values() for col in pair]
    new_data_frame = new_data_frame.drop(columns=columns_to_drop)

    # Extract Base model label using first matching key in ATTRIBUTES
    new_data_frame["Base model label"] = new_data_frame["base_label"].apply(
        lambda x: next((key for key in MODEL_ATTRIBUTES if key in x), None)
    )

    # Extract Context length
    new_data_frame["Context length"] = (
        new_data_frame["base_label"].str.extract(r"_(\d+)k").fillna("16")
    )

    # Extract Version - get the last match
    new_data_frame["Version"] = new_data_frame["base_label"].str.extract(
        r".*v([\d.-]+)"
    )
    # Replace hyphens with dots
    new_data_frame["Version"] = new_data_frame["Version"].str.replace("-", ".")
    new_data_frame["Version"] = pd.to_numeric(
        new_data_frame["Version"], errors="coerce"
    )
    # Extract Retriever - get the last match
    retriever_values = new_data_frame["base_label"].str.extract(r".*c(\d+)")
    # Convert to int only where we have matches, otherwise None
    new_data_frame["Retriever"] = retriever_values.apply(
        lambda x: int(x[0]) if pd.notna(x[0]) else None, axis=1
    )

    # Extract Router - get the last match
    router_values = new_data_frame["base_label"].str.extract(r".*p(\d+)")
    # Convert to int only where we have matches, otherwise None
    new_data_frame["Router"] = router_values.apply(
        lambda x: int(x[0]) if pd.notna(x[0]) else None, axis=1
    )

    # Extract Round - get the last match
    new_data_frame["Round"] = (
        new_data_frame["base_label"].str.extract(r".*r(\d+)").astype(int)
    )
    return new_data_frame


def rename_columns(data_frame):
    name_mapping = {
        "base_label": "Label",
        "count": "Trials",
        "answer_keyword_recall": "Answer score",
        "retrievals_keyword_recall": "Retrieval score",
        "gold_paths_recall": "File coverage",
        "generation_time": "Generation time",
        "answer_length": "Answer length",
    }

    new_data_frame = data_frame.copy()
    new_data_frame = new_data_frame.rename(columns=name_mapping)

    new_column_order = [
        "Label",
        "Base model label",
        "Context length",
        "Version",
        "Retriever",
        "Router",
        "Round",
        "Trials",
    ] + [
        col
        for col in new_data_frame.columns
        if col
        not in [
            "Label",
            "Base model label",
            "Context length",
            "Version",
            "Retriever",
            "Router",
            "Round",
            "Trials",
        ]
    ]
    new_data_frame = new_data_frame[new_column_order]
    return new_data_frame


def add_attributes(data_frame):
    def get_attributes(row):
        base_model = row["Base model label"]
        version = row["Version"]
        if base_model in MODEL_ATTRIBUTES and version in MODEL_ATTRIBUTES[base_model]:
            return MODEL_ATTRIBUTES[base_model][version]
        return None, None, None, None

    # Get attributes from MODEL_ATTRIBUTES
    attributes_df = data_frame.apply(get_attributes, axis=1, result_type="expand")
    attributes_df.columns = ["Prompt formatter", "Base model", "Retriever", "Router"]

    # Add Prompt formatter and Base model directly
    data_frame["Prompt formatter"] = attributes_df["Prompt formatter"]
    data_frame["Base model"] = attributes_df["Base model"]

    # Combine Retriever and Router columns, preferring data_frame's original values
    data_frame["Retriever"] = data_frame["Retriever"].combine_first(
        attributes_df["Retriever"]
    )
    data_frame["Router"] = data_frame["Router"].combine_first(attributes_df["Router"])

    columns = data_frame.columns.tolist()
    new_order = [
        "Label",
        "Base model label",
        "Base model",
        "Version",
        "Prompt formatter",
        "Retriever",
        "Router",
    ] + [
        col
        for col in columns
        if col
        not in [
            "Label",
            "Base model label",
            "Prompt formatter",
            "Base model",
            "Retriever",
            "Version",
            "Router",
        ]
    ]
    data_frame = data_frame[new_order]
    return data_frame


def filter_old_runs(data_frame, round_denylist=None):
    assert (
        not data_frame[["Base model", "Prompt formatter", "Retriever"]]
        .isnull()
        .values.any()
    ), f"Missing attributes in {data_frame[data_frame[['Base model', 'Prompt formatter', 'Retriever']].isnull().all(axis=1)]}"

    round_denylist = round_denylist or []

    grouped = data_frame.groupby(["Base model", "Version", "Context length"])

    def filter_group(group):
        max_trials = group["Trials"].max()
        max_trial_rows = group[group["Trials"] == max_trials]
        valid_rows = max_trial_rows[~max_trial_rows["Round"].isin(round_denylist)]
        if valid_rows.empty:
            return pd.DataFrame()
        latest_round = valid_rows["Round"].max()
        return valid_rows[valid_rows["Round"] == latest_round]

    latest_run_data_frame = grouped.apply(filter_group).reset_index(drop=True)
    return latest_run_data_frame


def filter_minor_versions(data_frame):
    data_frame["Version"] = pd.to_numeric(data_frame["Version"], errors="coerce")
    data_frame["Major_Version"] = data_frame["Version"].apply(lambda x: int(x))

    def keep_latest_minor(group):
        return group.loc[group["Version"].idxmax()]

    major_only_data_frame = (
        data_frame.groupby(["Base model", "Major_Version", "Context length"])
        .apply(keep_latest_minor)
        .reset_index(drop=True)
    )
    major_only_data_frame = major_only_data_frame.drop(columns=["Major_Version"])

    return major_only_data_frame


def beautify(data_frame):
    beautiful_data_frame = data_frame.copy()
    # Convert Version back to string without .0 for whole numbers
    beautiful_data_frame["Version"] = beautiful_data_frame["Version"].apply(
        lambda x: f"{x:.2f}".rstrip("0").rstrip(".")
    )
    beautiful_data_frame["Prompt formatter"] = beautiful_data_frame[
        "Prompt formatter"
    ].apply(lambda x: f"{x:.0f}")
    beautiful_data_frame["Context length"] = beautiful_data_frame[
        "Context length"
    ].apply(lambda x: f"{x}K")
    beautiful_data_frame["Round"] = beautiful_data_frame["Round"].apply(
        lambda x: f"{x:.0f}"
    )
    beautiful_data_frame["Trials"] = beautiful_data_frame["Trials"].apply(
        lambda x: f"{x:.0f}"
    )
    # Convert Retriever and Router to integers if they are numeric
    beautiful_data_frame["Retriever"] = beautiful_data_frame["Retriever"].apply(
        lambda x: f"{int(x)}" if pd.notna(x) and isinstance(x, (int, float)) else x
    )
    beautiful_data_frame["Router"] = beautiful_data_frame["Router"].apply(
        lambda x: f"{int(x)}" if pd.notna(x) and isinstance(x, (int, float)) else x
    )
    return beautiful_data_frame
