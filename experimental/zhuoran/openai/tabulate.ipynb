#%%
%load_ext autoreload
%autoreload 2
#%%
import glob
import os

result_paths = glob.glob("/mnt/efs/augment/user/zhuoran/summaries/openai/*.json")

for path in result_paths:
    print(os.path.basename(path))
#%%
from experimental.zhuoran.openai.tabulation_utils import (
    add_attributes,
    combine_means_and_stds,
    rename_columns,
    group_rows,
    read_results,
    reset_display,
    set_unlimited_display,
    filter_old_runs,
    filter_minor_versions,
    beautify,
)
#%%
df = read_results(result_paths)
reset_display()
df
#%%
grouped_df = group_rows(df)
reset_display()
grouped_df
#%%
combined_df = combine_means_and_stds(grouped_df)
renamed_df = rename_columns(combined_df)
print(renamed_df["Base model label"].unique())

reset_display()
renamed_df
#%%
attributed_df = add_attributes(renamed_df)
reset_display()
attributed_df
#%%
attributed_df[attributed_df["Base model"].isnull()]
#%%
from IPython.display import display, HTML


filtered_df = filter_old_runs(
    attributed_df,
    round_denylist=[
        10,
        # 12,
    ],
)
# filtered_df = filter_minor_versions(filtered_df)
# filtered_df = filtered_df[filtered_df["Prompt formatter"].isin([1, 3, 4, 7, 8])]
filtered_df = filtered_df[
    filtered_df["Base model label"].isin(
        [
            "claude",
            # "wool",
            # "flannel",
            # "linen",
            # "lycra",
            "ombre",
            "lace",
            # "o1_mini",
            # "o1",
            # "o3_mini",
            "o3",
            "o4_mini",
            # "gpt4o_chatgpt",
            # "gpt4o",
            "gpt4_1",
            # "gpt4_1_mini",
            # "gpt4_1_nano",
            # "gpt4-5",
            # "gemini",
            "gemini2_5_pro",
            # "deepseek_r1",
            "deepseek_v3",
            # "grok2",
            "grok3",
            # "grok3_mini",
            # "qwen2-5",
            # "qwq",
            # "llama4_scout",
            "llama4_maverick",
        ]
    )
]
# filtered_df = filtered_df[filtered_df["Context length"].isin(["16"])]
filtered_df = filtered_df[
    filtered_df["Retriever"].isin(
        [
            "1.18.3",
            "2.2",
            4,
        ]
    )
]
filtered_df = filtered_df[filtered_df["Round"].isin([11])]
# filtered_df = filtered_df[filtered_df["Prompt formatter"].isin([3, 3.1])]
filtered_df = filtered_df[
    ~filtered_df["Version"].isin([14.2, 14.3, 14.4, 17.01, 17.02])
]
filtered_df = filtered_df[filtered_df["Retriever"].isin([4])]


sorting_criteria = [
    # ("Base model label", False),
    # ("Prompt formatter", False),
    # ("Version", False),
    ("Answer score", False),
]
filtered_df = filtered_df.sort_values(
    by=[key for key, _ in sorting_criteria],  # type: ignore
    ascending=[ascending for _, ascending in sorting_criteria],
)
beautiful_df = beautify(filtered_df)
beautiful_df = beautiful_df.drop(
    columns=[
        "Label",
        "Base model label",
        "Context length",
        # "Retriever",
        # "Router",
        "Round",
        # "Trials",
    ]
)
set_unlimited_display()
display(HTML(beautiful_df.to_html(index=False)))

with open("/home/<USER>/results.csv", "w") as f:
    f.write(beautiful_df.to_csv(index=False))
#%%
import json

results_data = []
for path in result_paths:
    label = os.path.splitext(os.path.basename(path))[0]
    with open(path) as file:
        result = json.load(file)
    run_result = list(result["runs"].values())[0]
    if "html_report_url" not in run_result["results"]:
        print(label, "no html_report_url")
        continue
    print(label, run_result["results"]["html_report_url"])
#%%
import json

results_data = []
for path in result_paths:
    label = os.path.splitext(os.path.basename(path))[0]
    with open(path) as file:
        result = json.load(file)
    run_result = list(result["runs"].values())[0]
    if "artifact" not in run_result["results"]:
        print(label, "no artifact")
        continue
    print(label, run_result["results"]["artifact"])
#%%
