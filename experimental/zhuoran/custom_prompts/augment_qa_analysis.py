import argparse
import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List

import zstandard as zstd
from jinja2 import Environment
from tqdm import tqdm

from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from research.eval.harness.tasks.augment_qa_utils import AugmentQAOutput

# Vertex AI settings
REGION = "us-east5"
PROJECT_ID = "augment-387916"
MODEL_NAME = "claude-3-5-sonnet@20240620"
# MODEL_NAME = "claude-3-5-sonnet-v2@20241022"
TEMPERAURE = 0
MAX_OUTPUT_TOKENS = 1024 * 8


def get_vertex_ai_client():
    vertex_ai_client = AnthropicVertexAiClient(
        project_id=PROJECT_ID,
        region=REGION,
        model_name=MODEL_NAME,
        temperature=TEMPERAURE,
        max_output_tokens=MAX_OUTPUT_TOKENS,
    )
    return vertex_ai_client


def run_claude(vertex_ai_client, message_or_prompt, system_message):
    if isinstance(message_or_prompt, str):
        message = message_or_prompt
    else:
        print(type(message_or_prompt))
        message = message_or_prompt.message
        system_message = message_or_prompt.system_prompt
    response = vertex_ai_client.client.messages.create(
        model=MODEL_NAME,
        max_tokens=MAX_OUTPUT_TOKENS,
        messages=[{"role": "user", "content": message}],
        system=system_message,
        temperature=TEMPERAURE,
    )
    return response.content[0].text, response


def parse_arguments() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Compare 1 to 3 eval results and generate an HTML report."
    )
    parser.add_argument(
        "--input_paths",
        nargs="+",
        type=str,
        default=[],
        help="Paths to the input jsonl files (1 to 3) in the format 'model:path'",
    )
    parser.add_argument(
        "--output_path",
        type=str,
        default="",
        help="Path to the output html file",
    )
    parser.add_argument(
        "--translation_source",
        type=str,
        default="",
        help="Source language for translation",
    )
    return parser.parse_args()


def load_eval_results(file_path: Path) -> List[AugmentQAOutput]:
    if file_path.suffix == ".zst":
        with zstd.open(file_path, "rt", encoding="utf-8") as f:
            return [
                AugmentQAOutput(**json.loads(line.strip()), generation_time=0)
                for line in f
            ]
    elif file_path.suffix == ".jsonl":
        with open(file_path, "r", encoding="utf-8") as f:
            return [AugmentQAOutput(**json.loads(line.strip())) for line in f]
    else:
        raise ValueError(f"Unsupported file format: {file_path.suffix}")


def compute_metrics(results: List[AugmentQAOutput]) -> Dict[str, float]:
    if not results:
        return {}
    n_samples = float(len(results))
    metrics = {
        key: sum(r.metrics[key] for r in results) / n_samples
        for key in results[0].metrics.keys()
    }
    metrics["length"] = (
        sum(len(r.result["generated_text"]) / 3 for r in results) / n_samples
    )
    return metrics


input_paths = [
    ("Baseline", "/mnt/efs/augment/eval/jobs/mMEUvpYP/000__RemoteChatSystem_xe4v"),
    # (
    #     "Concise 0",
    #     "/mnt/efs/augment/eval/jobs/fr5VEdbs/000__RemoteChatSystem_iikl",
    # ),
    # (
    #     "Concise 1",
    #     "/mnt/efs/augment/eval/jobs/5hRsQLFp/000__RemoteChatSystem_mnov",
    # ),
    # (
    #     "Concise 2",
    #     "/mnt/efs/augment/eval/jobs/PyH8R8MK/000__RemoteChatSystem_zqmx",
    # ),
    # (
    #     "Code only",
    #     "/mnt/efs/augment/eval/jobs/4rM65rsZ/000__RemoteChatSystem_zvdt",
    # ),
    # nMmSXQYU/000__RemoteChatSystem_vfz8
    (
        "Code only 1",
        "/mnt/efs/augment/eval/jobs/nMmSXQYU/000__RemoteChatSystem_vfz8",
    ),
    # (
    #     "Detail 0",
    #     "/mnt/efs/augment/eval/jobs/cCB4d9Yd/000__RemoteChatSystem_ntuu",
    # ),
    # (
    #     "Detail 1",
    #     "/mnt/efs/augment/eval/jobs/eWDbGZ7y/000__RemoteChatSystem_9gv9",
    # ),
    # (
    #     "Detail 2",
    #     "/mnt/efs/augment/eval/jobs/jQcXwPAy/000__RemoteChatSystem_usjj",
    # ),
    # (
    #     "Detail 3",
    #     "/mnt/efs/augment/eval/jobs/7tk2HRRZ/000__RemoteChatSystem_vum9",
    # ),
    # (
    #     "More text",
    #     "/mnt/efs/augment/eval/jobs/niye3DrD/000__RemoteChatSystem_n2kl",
    # ),
    # 94auyAfT/000__RemoteChatSystem_zxe4
    (
        "More text 1",
        "/mnt/efs/augment/eval/jobs/94auyAfT/000__RemoteChatSystem_zxe4",
    ),
]
input_paths = [
    (model, os.path.join(path, "samples.jsonl.zst")) for model, path in input_paths
]
output_path = (
    "/mnt/efs/augment/public_html/zhuoran_compare/custom_prompts_code_only.html"
)

print(f"Input paths: {input_paths}")
print(f"Output path: {output_path}")

eval_results = []
eval_results_data = []
for model_index, (model, path) in enumerate(input_paths):
    print(f"Loading #{model_index}: {model}")
    file_path = Path(path)
    if not file_path.exists():
        raise FileNotFoundError(f"Path {path} does not exist")

    eval_result = load_eval_results(file_path)
    print(f"Loaded {len(eval_result)} results")
    eval_results.extend(eval_result)
    metrics = compute_metrics(eval_result)
    print("Computed metrics")
    print(json.dumps(metrics, indent=4))
    print()

    eval_results_data.append(
        {
            "header": model,
            "file_path": str(file_path),
            "metrics": metrics,
            "outputs": eval_result,
        }
    )
