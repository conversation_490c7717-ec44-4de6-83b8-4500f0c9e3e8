"""python experimental/dxy/xtry/create_retriever.py"""

from research.eval.harness.factories import create_retriever

METH<PERSON><PERSON>_CONFIG = dict(
    scorer={
        "checkpoint_path": "methanol/methanol_0416.4_1250",
        "name": "generic_neox",
    },
    chunker={
        "name": "signature",
    },
    document_formatter={
        "add_path": False,
        "name": "simple_document",
        "tokenizer_name": "StarCoderTokenizer",
    },
    query_formatter={
        "add_path": True,
        "add_suffix": True,
        "max_lines": -1,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "tokenizer_name": "StarCoderTokenizer",
    },
    max_num_chunks_per_file=1_000_000,
)

SETHANOL_CONFIG = dict(
    chunker={
        "max_lines_per_chunk": 40,
        "name": "line_level",
    },
    document_formatter={
        "add_path": True,
        "add_prefix": False,
        "add_suffix": False,
        "max_tokens": 999,
        "name": "ethanol6_document",
    },
    query_formatter={
        "add_path": True,
        "add_suffix": True,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "prefix_ratio": 0.9,
    },
    scorer={
        "name": "generic_neox",
        "checkpoint_path": "star_ethanol/starethanol6_16.1_mean_proj_512_2000",
    },
    max_num_chunks_per_file=1_000_000,
)

ret_db_configs = {
    "line": SETHANOL_CONFIG,
    "signature": METHANOL_CONFIG,
}
ret_dbs = {}
for key, config in ret_db_configs.items():
    ret_dbs[key] = create_retriever(config)
    ret_dbs[key].load()

print("Create retriever done.")
