#%%
%load_ext autoreload
%autoreload 2
#%%
import logging
import termcolor
import tqdm
import tree_sitter as ts
from research.static_analysis.usage_analysis import ParsedFile
from experimental.dxy.exps.generate_patches_api import (
    parse_docs_from_docker_image,
    CoverageElement,
)
from research.static_analysis.common import LanguageID, decode_bytes
from experimental.dxy.exps.patch_data_lib import (
    CoverageElement,
    filter_docs_by_node_lines,
    filter_docs_by_node_tokens,
    find_ts_parent_in_certain_type_range,
    parsedfile_to_dict,
    ts_node_to_dict,
    count_nodes_for_docs,
    doc_w_nodes_to_dict,
)
from research.core import utils_for_str
from research.core.utils import Timer
from research.core.types import Char<PERSON><PERSON><PERSON>


def filter_doc_nodes_by_coverage(
    documents: list[ParsedFile], filename2coverage: dict[str, CoverageElement]
) -> list[tuple[ParsedFile, list[ts.Node]]]:
    doc_nodes_pairs: list[tuple[ParsedFile, list[ts.Node]]] = []
    total_valid_nodes: int = 0
    for index, doc in tqdm.tqdm(enumerate(documents)):
        filtered_nodes__by_coverage = []
        for node in doc.ts_nodes:
            if doc.scope_tree.name not in filename2coverage:
                continue
            coverage_ele = filename2coverage[doc.scope_tree.name]
            covered_by_unit_test = False
            crange = doc.bmap.tsnode_to_crange(node)
            line_start = doc.lmap.get_line_number(crange.start)
            line_end = doc.lmap.get_line_number(crange.stop - 1)
            for line in range(line_start, line_end + 1):
                # lmap.get_line_number starts from 0-index but coverage_ele used 1-index for the line number
                line = line + 1
                if line in coverage_ele.line2hit and coverage_ele.line2hit[line] > 0:
                    covered_by_unit_test = True
            if covered_by_unit_test:
                filtered_nodes__by_coverage.append(node)
        print(
            f"[{index:4d}/{len(documents):4d}] {doc.scope_tree.name:20s}"
            f" : {len(doc.ts_nodes):3d} nodes -> {len(filtered_nodes__by_coverage):3d} nodes"
        )
        # Collect the useful data
        if filtered_nodes__by_coverage:
            doc_nodes_pairs.append((doc, filtered_nodes__by_coverage))
        total_valid_nodes += len(filtered_nodes__by_coverage)
    print(f"Left {len(doc_nodes_pairs)} documents with {total_valid_nodes} nodes.")
    return doc_nodes_pairs
#%%
documents, coverages = parse_docs_from_docker_image("google/pyglove:v1.0")
filename2doc: dict[str, ParsedFile] = {}
filename2coverage: dict[str, CoverageElement] = {}

for doc in documents:
    filename2doc[doc.scope_tree.name] = doc

for coverage in coverages:
    filename2coverage[coverage.filename] = coverage

doc_w_nodes = filter_doc_nodes_by_coverage(documents, filename2coverage)
#%% md
# Show API Calls
#%%
# Show the API calls
def show_code(prefix: str, suffix: str, middle: str):
    print(termcolor.colored(utils_for_str.get_last_n_lines(prefix, 8), color="green"), end="")
    print(termcolor.colored(middle, color="white", on_color="on_black"), end="")
    print(termcolor.colored(utils_for_str.get_first_n_lines(suffix, 8), color="yellow"), end="\n")

def show_node(doc_dict: dict, index: int):
    node = doc_dict["ts_node_dicts"][index]
    code: str = doc_dict["code"]
    crange: CharRange = node["node.char_range"]
    prefix: str = code[: crange.start]
    suffix: str = code[crange.stop :]
    middle: str = code[crange.start : crange.stop]
    print(termcolor.colored(utils_for_str.get_last_n_lines(prefix, 8), color="green"), end="")
    print(termcolor.colored(middle, color="white", on_color="on_black"), end="")
    print(termcolor.colored(utils_for_str.get_first_n_lines(suffix, 8), color="yellow"), end="\n")

def find_api_calls(
    doc_w_nodes: list[tuple[ParsedFile, list[ts.Node]]]
) -> list[tuple[ParsedFile, list[ts.Node]]]:
    valid_types_for_api_call = (
        "call",
        "pair",
        "assignment",
        "binary_operator",
        "return_statement",
        "expression_statement",
        "dictionary",
        "argument_list",
    )

    doc_nodes_pairs: list[tuple[ParsedFile, list[ts.Node]]] = []
    total_valid_nodes: int = 0
    for index, (doc, nodes) in enumerate(doc_w_nodes):
        target_nodes: list[ts.Node] = []
        for node in doc.ts_nodes:
            if node.type != "call":
                continue
            cur_node = find_ts_parent_in_certain_type_range(
                node, valid_types_for_api_call
            )
            if cur_node is None:
                raise ValueError(
                    f"node={node} did not successfully call find_ts_parent_in_certain_type_range"
                )
            target_nodes.append(cur_node)
        if target_nodes:
            doc_nodes_pairs.append((doc, target_nodes))
            total_valid_nodes += len(target_nodes)
    print(f"Find {len(doc_nodes_pairs)} documents with {total_valid_nodes} nodes.")
    return doc_nodes_pairs


api_doc_w_nodes = find_api_calls(doc_w_nodes)
api_doc_dicts = doc_w_nodes_to_dict(api_doc_w_nodes)
api_doc_dicts = filter_docs_by_node_lines(api_doc_dicts, min_lines=3, max_lines=8)


show_code(prefix=r'''class GreaterThan(BinaryOperator):
  """Greater than operator."""

  ORDER = 2
  OPERATOR_STR = '>'
  OPERATOR_FN = lambda cls, x, y: x > y

''',


middle=r'''class Equals(BinaryOperator):
  """Equals operation."""

  ORDER = 3
  OPERATOR_STR = '=='
  OPERATOR_FN = lambda cls, x, y: x == y''',


suffix=r'''class NotEquals(BinaryOperator):
  """Not Equals operator."""

  ORDER = 3
  OPERATOR_STR = '!='
  OPERATOR_FN = lambda cls, x, y: x != y''')

print('\n')
print('\n')
print('\n')

show_node(api_doc_dicts[0], 1)
print('\n')
context = r"""class _DetourContext:
  '''Context that sets/gets detoured class mappings under current thread.'''

  _DETOUR_STACK_KEY = 'detour_stack'
  _DETOUR_MAPPING_KEY = 'detour_map'
  _NEW_CALL_STACK = 'new_stack'
  
  def call_new(self, new_method, cls, *args, **kwargs):
    '''Call __new__ method with correctly handling super.__new__.'''
    try:
      self._new_stack.append(new_method)
      if new_method is object.__new__:
        return object.__new__(cls)
      else:
        return new_method(cls, *args, **kwargs)
    finally:
      self._new_stack.pop(-1)"""
print(f"----- Context within the file : {api_doc_dicts[0]['scope_tree'].name}-----\n")
print(context)
print('\n')
#%% md
# Show String-like Snippets
#%%
# Find the comments or docstring
# Similarity Matching
def is_comment_type(node: ts.Node):
    node_text = decode_bytes(node.text)
    if node.type == "block" and (
        node_text.startswith('"' * 3) and node_text.endswith('"' * 3) or node_text.startswith("'" * 3) and node_text.endswith("'" * 3)
    ):
        return True
    else:
        return False

def find_comments(
    doc_w_nodes: list[tuple[ParsedFile, list[ts.Node]]]
) -> list[tuple[ParsedFile, list[ts.Node]]]:

    doc_nodes_pairs: list[tuple[ParsedFile, list[ts.Node]]] = []
    total_valid_nodes: int = 0
    for index, (doc, nodes) in enumerate(doc_w_nodes):
        target_nodes: list[ts.Node] = []
        for node in doc.ts_nodes:
            if is_comment_type(node):
                target_nodes.append(node)
        if target_nodes:
            doc_nodes_pairs.append((doc, target_nodes))
            total_valid_nodes += len(target_nodes)
    print(f"Find {len(doc_nodes_pairs)} documents with {total_valid_nodes} nodes.")
    return doc_nodes_pairs

comment_doc_w_nodes = find_comments(doc_w_nodes)
comment_doc_w_nodes = doc_w_nodes_to_dict(comment_doc_w_nodes)
comment_doc_w_nodes = filter_docs_by_node_lines(comment_doc_w_nodes, min_lines=3, max_lines=8)
show_node(comment_doc_w_nodes[1], 0)
#%%
# Find the comments or docstring
# Exact Matching
def is_empty_char(x):
    return x in (" ", "\t", "\n")

def find_leftest_empty_index(code: str, index: int):
    while index >= 0:
        if is_empty_char(code[index]):
            index -= 1
        else:
            break
    return index + 1

def find_empty_string(
    doc_w_nodes: list[tuple[ParsedFile, list[ts.Node]]]
) -> list[dict]:
    
    doc_dict_list: list[dict] = []
    total_valid_nodes: int = 0
    for index, (doc, nodes) in enumerate(doc_w_nodes):
        doc_dict = parsedfile_to_dict(doc)
        target_nodes: list[dict] = []
        for node in doc.ts_nodes:
            if node.children is None or len(node.children) == 0:
                crange = doc.bmap.tsnode_to_crange(node)
                if crange.start == 0 or not is_empty_char(doc.code[crange.start-1]):
                    continue
                leftest_index = find_leftest_empty_index(doc.code, crange.start-1)
                empty_string = doc.code[leftest_index: crange.start-1]
                if " " in empty_string:
                    node_dict = dict()
                    node_dict["node.char_range"] = CharRange(start=leftest_index, stop=crange.start)
                    target_nodes.append(node_dict)
        if target_nodes:
            doc_dict["ts_node_dicts"] = target_nodes
            doc_dict_list.append(doc_dict)
            total_valid_nodes += len(target_nodes)
    print(f"Find {len(doc_dict_list)} documents with {total_valid_nodes} nodes.")
    return doc_dict_list

empty_doc_w_nodes = find_empty_string(doc_w_nodes)
# empty_doc_w_nodes = filter_docs_by_node_lines(empty_doc_w_nodes, min_lines=3, max_lines=8)
print(empty_doc_w_nodes[0]["ts_node_dicts"][0])
show_node(empty_doc_w_nodes[0], 0)
print('\n')
#%%
import functools
import json
import logging
import threading
from collections import defaultdict
from dataclasses import asdict, dataclass
from glob import glob
from pathlib import Path
from pprint import pformat
from typing import Optional, Union

import shortuuid
from tqdm import tqdm

from research.core.ui_sugar import UISugar
from research.core.artifacts import collect_artifacts
from research.core.model_input import ModelInput
from research.core.types import Chunk, Document
from research.core.utils_for_str import extract_colored_parts
from research.eval import hydra, patch_lib
from research.eval.harness import types, utils
from research.eval.harness.systems import AbstractSystem
from research.eval.harness.tasks.abs_task import AbstractTask, DocsType
from research.eval.hydra.driver import Driver
from research.retrieval.utils import Span

# class AbstractCategory(UISugar):

#     name: str
#     """The concrete category name."""

# class APICallCategory(AbstractCategory):
    

# class AugmentedHydraTask(AbstractTask):
#     """The Hydra Task."""

#     version: str = "1.0"

#     DEFAULT_DATASET = "repoeval_functions"
#     DATASETS_BASEDIR = "/mnt/efs/augment/data/eval/hydra/datasets/"

#     def __init__(self):
#         self.dataset_name = dataset
#         self.dataset_path = dataset_path
#         self.name = experiment_name


print("Result Analysis")
print("API Calls with reference found in the repo:")
print("\tPASS = 128 / 240 (vs. 128 / 240), Syntax Error = 32 / 240")
print("API Calls with reference found in the same file:")
print("\tPASS =  34 / 128 (vs.  32 / 128), Syntax Error = 32 / 240")
print("API Calls (others):")
print("\tPASS =  17 / 64  (vs.  16 /  64), Syntax Error = 32 / 240")

print("String-like completion")
print("\tSimilarity = 54% (vs. 49%), Syntax Error = 32 / 240")

print("Empty string-like completion")
print("\t Exact Match = 39 / 50 (vs. 29 / 50), Syntax Error = 32 / 240")

print("\n\n\n")

print("Breakdown by Structural Categorization")
print("\tAPI Calls with reference found in the repo:")
print("\t\tCursor-Loc (Beining of a line): PASS = 128 / 240 (vs. 128 / 240), Syntax Error = 32 / 240")
print("\t\tCursor-Loc (Middle of a line): PASS = 128 / 240 (vs. 128 / 240), Syntax Error = 32 / 240")

print("\n\n\n")
print("....")

print("Breakdown by Repo Name")
print("\tAPI Calls with reference found in the repo:")
print("\t\t Pydantic PASS = 128 / 240 (vs. baseline = 128 / 240)")
print("\t\t ...")