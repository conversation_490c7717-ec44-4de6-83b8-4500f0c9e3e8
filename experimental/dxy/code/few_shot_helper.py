import re
from dataclasses import asdict
from typing import Any, Callable, List, Optional

from experimental.dxy.code.str_helper import trim_tail_white_space_per_paragraph
from research.core.model_input import ModelInput
from research.eval.vulcan import VulcanTest


def dict_by_modelinput(input: ModelInput) -> dict:
    data = asdict(input)
    extra_data = data.pop("extra")
    for key, value in extra_data.items():
        data[f"extra.{key}"] = value
    return data


def dict_by_any(data) -> dict:
    if isinstance(data, dict):
        return data
    elif isinstance(data, ModelInput):
        return dict_by_modelinput(data)
    elif isinstance(data, VulcanTest):
        return {"prefix": data.prefix, "suffix": data.suffix, "expected": data.expected}
    else:
        raise TypeError(f"Unknown data type: {type(data)}")


STOP_PATTERNS = {
    "common": [
        #### new examaple
        r"\n{4,}",
        r"\[+ Exemplar-\d+ \]+",
        #### special symbols
        r"\[eod\]",
        # r"<fim\_prefix>",
        # r"<fim\_suffix>",
        # r"<fim\_middle>",
        # "<|endoftext|>",
    ],
    "coding": [
        #### code block
        r"\s*```.*",
        #### code docstring block
        r'\s*"{3,}.*',
        r"\s*'{3,}.*",
    ],
    "artifacts": [
        #### artifacts
        r"#{2,}.+#{2,}",
        r"%{2,}.+%{2,}",
        r"={3,}.+={3,}",
        r"\s*\[{2,}.+\]{2,}",
        r"#{3,}.+",
        r">{3,}.+<{3,}",
        #### Line starting with html url
        r"^http(s|)://[^\s]+",
        # #### punctuation-only line
        # r'^[{}\s]+$'.format(re.escape(string.punctuation)),
    ],
    "math": [
        #### latex (math)
        r"^\\begin\{.*",
        r"^\\end\{.*",
    ],
}


# pylint: disable=dangerous-default-value
class FewshotTemplate:
    """."""

    def __init__(
        self,
        input_keys: List[str],
        output_keys: List[str],
        input_template: str = ">>>>> {key} <<<<<\n{val}",
        output_template: Optional[str] = None,
        transfer_fn: Optional[Callable[[Any], dict]] = None,
        key_sep="\n",
        ex_sep="\n\n\n\n",
        counter_template: str = "[[[[ Exemplar-{count} ]]]]\n{val}",
        key_rename_map: Optional[dict] = None,
        task_prefix: str = "",
    ):
        self.input_keys = input_keys
        self.output_keys = output_keys
        self.input_template = input_template
        self.output_template = output_template or input_template
        self.transfer_fn = transfer_fn or (lambda x: x)
        self.key_sep = key_sep
        self.ex_sep = ex_sep
        self.counter_template = counter_template
        self.stop_patterns = STOP_PATTERNS["common"]
        self.task_prefix = task_prefix

    def _format_ex(self, key, val, is_input: bool = True):
        xkey = key.replace("_", " ").title()
        template = self.input_template if is_input else self.output_template
        return template.format(key=xkey, val=val)

    def get_prompt(self, test_ex: Any, exemplars: Optional[List[Any]] = None):
        """Get the prompt string."""
        test_ex = dict_by_any(self.transfer_fn(test_ex))
        exemplars = exemplars or []
        exemplars = [dict_by_any(self.transfer_fn(x)) for x in exemplars]

        ex_strs = []
        # exemplars
        for ex in exemplars:
            key_strs = []
            # > input_keys
            for key in self.input_keys:
                key_strs.append(self._format_ex(key=key, val=ex[key]))
            # > output_keys
            for key in self.output_keys:
                key_strs.append(self._format_ex(key=key, val=ex[key], is_input=False))
            ex_strs.append(self.key_sep.join(key_strs))

        #### test example
        # > input_keys
        key_strs = []
        for key in self.input_keys:
            key_strs.append(self._format_ex(key=key, val=test_ex[key]))
        # > output_keys[0]
        final_key_0 = self.output_keys[0]
        key_strs.append(self._format_ex(key=final_key_0, val="", is_input=False))
        ex_strs.append(self.key_sep.join(key_strs))

        if self.counter_template:
            for i in range(len(ex_strs)):
                ex_strs[i] = self.counter_template.format(count=i + 1, val=ex_strs[i])

        return self.task_prefix + self.ex_sep.join(ex_strs)

    def parse_response(self, response, category: Optional[str] = None):
        """."""
        # right strip any whilte space for each paragraph
        response = trim_tail_white_space_per_paragraph(response)

        stop_patterns = self.stop_patterns
        if category is not None:
            stop_patterns += STOP_PATTERNS[category]

        if stop_patterns:
            for p in stop_patterns:
                m = re.search(p, response, re.MULTILINE | re.IGNORECASE)
                if m:
                    response = response[: m.start()]
                    # print("-" * 100)
                    # print(f"!! find something about {p}")
                    # print(f"!! m={m}")
                    # print(response)
        # import pdb; pdb.set_trace()

        pattern = self.output_template.format(key=".*", val="")
        output_texts = re.split(pattern, response)

        if len(output_texts) < len(self.output_keys):
            raise ValueError(
                f"#output fields [{len(output_texts)}] < #output keys"
                f" [{len(self.output_keys)}]"
            )

        response = {}
        for _, (key, text) in enumerate(zip(self.output_keys, output_texts)):
            # # For idx = 0, the `prefix` is provided in the `prompt`.
            # # For idx > 0, the `prefix` is generated in the `response`.
            # if idx > 0 and self.output_options and key in self.output_options:
            #     prefix = f"Choose from {self.output_options[key]}:\n"
            #     text = text[len(prefix) :]
            # for p in STOP_PATTERNS["artifacts"]:
            #     m = re.search(p, text, re.MULTILINE | re.IGNORECASE)
            #     if m:
            #         text = text[: m.start()].strip()
            response[key] = text

        return response

    def parse_prompt(self, prompt):
        """."""
        input_pattern = self._format_ex(key=self.input_keys[-1], val="")
        input_pattern = re.escape(input_pattern.strip())
        output_pattern = self._format_ex(key=self.output_keys[-1], val="")
        output_pattern = re.escape(output_pattern.strip())

        starts, ends = [], []
        for m in re.finditer(input_pattern, prompt, flags=re.MULTILINE):
            starts.append(m.start())
        for m in re.finditer(output_pattern, prompt, flags=re.MULTILINE):
            ends.append(m.start())

        exemplars = []
        for i, (s, e) in enumerate(zip(starts[:-1], ends[:-1])):
            ex_text = prompt[s:e]
            ex_text = "\n".join(ex_text.split("\n")[1:])
            exemplars.append(f"[{i}] {ex_text}")
        prompt_text = "\n".join(exemplars)

        return prompt_text
