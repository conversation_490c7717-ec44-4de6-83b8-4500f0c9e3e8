#%%
%load_ext autoreload
%autoreload 2
#%%
from research.llm_apis.chat_utils import OpenAIAPIClient

deepseek_address = "*************:8000"
client = OpenAIAPIClient(address=deepseek_address)

prompt = "Write hello world in haskell"

# Without streaming
response = client.generate(messages=[prompt], max_tokens=256)
print(response)

print("--------------------------------------------------------------------------------")

# With streaming
response = ""
for response_elem in client.generate_stream(messages=[prompt], max_tokens=256):
    print(response_elem, end="")
    response += response_elem