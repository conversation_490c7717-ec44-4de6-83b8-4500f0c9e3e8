#!/bin/bash
#
# bash experimental/dxy/scripts/reformat.sh --exp
# bash experimental/dxy/scripts/reformat.sh --core
# bash experimental/dxy/scripts/reformat.sh ...
#
# pre-commit run -s origin/main -o HEAD

case $1 in
  --exp)
    echo "Reformat the experimental/dxy"
    pre-commit run --hook-stage manual --files \
	experimental/dxy/code/* \
	experimental/dxy/exps/* \
	experimental/dxy/misc/* \
	experimental/dxy/pipeline-scripts/* \
	experimental/dxy/edits/*.py
    ;;
  --edit)
    echo "Reformat research/core, research/models, and research/eval/harness/tasks/*"
    pre-commit run --hook-stage manual --files \
      research/core/* research/core/tests/* \
      research/data/synthetic_code_edit/*
    ;;
  --core)
    echo "Reformat research/core, research/models, and research/eval/harness/tasks/*"
    pre-commit run --hook-stage manual --files \
      research/core/* research/core/tests/* \
      research/models/* research/models/tests/* \
      research/eval/harness/tasks/* \
      research/eval/tests/tasks/* \
      research/eval/tests/hydra/* \
      research/eval/dataset_generation_lib/* \
      research/eval/dataset_generation_lib/tests/* \
      research/data/eval/repos/* \
      research/data/eval/repo_lib/tests/* \
      research/data/eval/repo_lib/* \
      research/static_analysis/* \
      research/static_analysis/tests/*
    ;;
    *)
    echo "Reformat $1"
    pre-commit run --hook-stage manual --files $1
    ;;
esac
