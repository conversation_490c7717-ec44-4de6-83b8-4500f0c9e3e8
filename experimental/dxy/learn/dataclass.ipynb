#%% md
The default api for DataClassJsonMixin will fail unexpectedly when the dataclass has a field with a union type. See details in the following code.
The obj1's `x` field is of type `B`, and after serialization and deserialization with the default api, `obj2`'s `x` field is of type `A`.
#%%
import dataclasses
from dataclasses_json import DataClassJsonMixin

@dataclasses.dataclass
class A(DataClassJsonMixin):
    a: int
    c: int

@dataclasses.dataclass
class B(DataClassJsonMixin):
    a: int
    b: int
    c: int

@dataclasses.dataclass
class X(DataClassJsonMixin):
    x: A | B | None = None

obj1 = X(x=B(a=1, b=2, c=3))
print(obj1)
json_dict = obj1.to_dict()
print(json_dict)

obj2 = X.from_dict(json_dict)
print(obj2)
#%% md
If we use the "schema" interface, the result will be correct as it also records the type of the field.
#%%
obj1 = X(x=B(a=1, b=2, c=3))
print(obj1)
json_dict = X.schema().dump(obj1)
print(json_dict)

obj2 = X.schema().load(json_dict)
print(obj2)