"""Prompt formatter for the code edit."""
from megatron.tokenizer.tokenizer import (
    AbstractTokenizer,
    DeepSeekCoderBaseTokenizer,
    DeepSeekCoderInstructTokenizer,
    DeepSeekTokenizer,
)

from research.core import utils_for_str
from research.core.model_input import ModelInput
from research.core.prompt_formatters import AbstractPromptFormatter


class DroidFormatter(AbstractPromptFormatter):
    """Prompt formatter for Droid, a code instruct model."""

    max_prefix_tokens: int = 1536
    """Maximum number of tokens in the prefix."""

    max_suffix_tokens: int = 1536
    """Maximum number of tokens in the prefix."""

    def create_default_tokenizer(self) -> AbstractTokenizer:
        """A function to create the default tokenizer."""
        return DeepSeekCoderBaseTokenizer()

    def prepare_prompt(
        self,
        model_input: ModelInput,
    ) -> tuple[list[int], dict]:
        if model_input.retrieved_chunks:
            raise NotImplementedError(f"Do not support retrieved chunks for {self}.")
        if self.preamble:
            raise NotImplementedError(f"Do not support preamble for {self}.")
        tokenizer = self.tokenizer
        assert isinstance(tokenizer, DeepSeekTokenizer)

        prompt = (
            [tokenizer.bos_id]
            + tokenizer.tokenize_safe(model_input.extra["instruction"])
            + [tokenizer.fim_prefix_id]
            + tokenizer.tokenize_safe(model_input.prefix)[-self.max_prefix_tokens :]
            + [tokenizer.fim_suffix_id]
            + tokenizer.tokenize_safe(model_input.suffix)[: self.max_suffix_tokens]
            + [tokenizer.fim_middle_id]
            + tokenizer.tokenize_safe(model_input.extra["selected_code"])
            + [tokenizer.pause_id]
        )

        return prompt, {}


class DSInstructNLPFormatter(AbstractPromptFormatter):
    """Prompt formatter for Droid, a code instruct model."""

    max_prefix_tokens: int = 1536
    """Maximum number of tokens in the prefix."""

    max_suffix_tokens: int = 1536
    """Maximum number of tokens in the prefix."""

    max_context_lines: int = 64
    """Maximum number of lines in the context."""

    def create_default_tokenizer(self) -> AbstractTokenizer:
        """A function to create the default tokenizer."""
        return DeepSeekCoderInstructTokenizer()

    def prepare_prompt(
        self,
        model_input: ModelInput,
    ) -> tuple[list[int], dict]:
        if model_input.retrieved_chunks:
            raise NotImplementedError(f"Do not support retrieved chunks for {self}.")
        if self.preamble:
            raise NotImplementedError(f"Do not support preamble for {self}.")
        tokenizer = self.tokenizer
        assert isinstance(tokenizer, DeepSeekTokenizer)
        prefix = utils_for_str.get_last_n_lines(
            model_input.prefix, self.max_context_lines
        )
        suffix = utils_for_str.get_first_n_lines(
            model_input.suffix, self.max_context_lines
        )

        instruction = model_input.extra["instruction"]
        selected_code = model_input.extra["selected_code"]

        prompt_text = f"""You are an AI programming assistant, and you only answer questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.

    ### Instruction:

Full Code:
```
{prefix}*********
{selected_code}*********
{suffix}
```

Edit Request: {instruction}

Please follow these instructions carefully and restrictly:
- The edit request is only applied to the code within these 9-times-repeated characters *********.
- Only output the updated code within the asterisks.
- Do not output any additional text, headers, comments, suggestions, formatting, or explanations.

    ### Response:
"""
        prompt = [tokenizer.bos_id] + tokenizer.tokenize(prompt_text)

        return prompt, {}
