"""Process Data for Edit.

python experimental/dxy/edits/process_data.py \
    --input_dir /mnt/efs/augment/user/dxy/datasets/edit/raw-le_25_25-2023-10-06/ \
    --output_dir /mnt/efs/augment/user/dxy/datasets/edit/2023-10-06/
"""
import argparse
import glob
import pathlib
import typing
from concurrent.futures import ThreadPoolExecutor

import pyglove as pg
import tqdm

# from megatron.tokenizer.tokenizer import CodeLLaMATokenizer
from experimental.dxy.edits import data_type


def save_data_into(data: typing.List[data_type.EditData], save_dir: pathlib.Path):
    save_dir.mkdir(exist_ok=True, parents=True)
    for idx, x in enumerate(data):
        save_path = save_dir / f"{idx:06d}-{len(data):06d}.json"
        pg.save(x, str(save_path), indent=2)
    print(f"Save {len(data)} data into {save_dir}")


def load_filter_save_data(
    json_files: typing.List[str],
    filter_fn: typing.Callable[[data_type.EditData], bool],
    save_dir: pathlib.Path,
):
    save_dir.mkdir(exist_ok=True, parents=True)
    pbar = tqdm.tqdm(total=len(json_files), desc=f"Save into {save_dir}")

    def _load_and_potential_save(json_file: str, index: int):
        data = pg.load(json_file)
        pbar.update()
        if filter_fn(data):
            save_path = save_dir / f"{index:06d}.json"
            pg.save(data, str(save_path), indent=2)

    with ThreadPoolExecutor(max_workers=32) as executor:
        for index, xfile in enumerate(json_files):
            executor.submit(_load_and_potential_save, xfile, index)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_dir",
        type=str,
        required=True,
        help="",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="",
    )
    args = parser.parse_args()

    input_dir = pathlib.Path(args.input_dir)
    assert input_dir.exists()
    output_dir = pathlib.Path(args.output_dir)
    json_files = []
    for x in glob.glob(str(input_dir / "*.json")):
        json_files.append(str(x))
    print(f"There are {len(json_files)} files in total.")

    load_filter_save_data(
        json_files,
        lambda x: x.get_diffs()[0] <= 10 and x.get_diffs()[1] <= 30,
        output_dir / "data-le_10_30",
    )
    load_filter_save_data(
        json_files,
        lambda x: x.get_diffs()[0] <= 30 and x.get_diffs()[1] <= 60,
        output_dir / "data-le_30_60",
    )

    # json_files = random.sample(json_files, 1000)
    # pbar = tqdm.tqdm(total=len(json_files))

    # def update(*_):
    #     pbar.update()

    # futures = []
    # with ThreadPoolExecutor(max_workers=32) as executor:
    #     for chunk in json_files:
    #         future = executor.submit(pg.load, chunk)
    #         future.add_done_callback(update)
    #         futures.append(future)
    # pbar.close()

    # all_data: typing.List[data_type.EditData] = [future.result() for future in futures]
    # print(f"There are {len(all_data)} examples in total.")

    # save_data_into(
    #     data_type.keep_editdata_via_fn(
    #         all_data, lambda x: x.get_diffs()[0] <= 10 and x.get_diffs()[1] <= 30
    #     ),
    #     output_dir / "data-le_10_30",
    # )

    # save_data_into(
    #     data_type.keep_editdata_via_fn(
    #         all_data, lambda x: x.get_diffs()[0] <= 30 and x.get_diffs()[1] <= 60
    #     ),
    #     output_dir / "data-le_30_60",
    # )

    # save_data_into(
    #     all_data,
    #     save_dir / "data-all",
    # )
    # save_data_into(
    #     data_type.remove_editdata_via_fn(
    #         all_data, lambda x: x.get_diffs()[0] >= 25 or x.get_diffs()[1] >= 25
    #     ),
    #     save_dir / "data-lt_25_25",
    # )
    # save_data_into(
    #     data_type.keep_editdata_via_fn(
    #         all_data,
    #         lambda x: 0 <= x.get_diffs()[0] <= 10 and 0 <= x.get_diffs()[1] <= 10,
    #     ),
    #     save_dir / "data-le_10_10",
    # )
    # save_data_into(
    #     data_type.keep_editdata_via_fn(
    #         all_data,
    #         lambda x: 0 <= x.get_diffs()[0] <= 5 and 0 <= x.get_diffs()[1] <= 5,
    #     ),
    #     save_dir / "data-le_5_5",
    # )
    # save_data_into(
    #     data_type.keep_editdata_via_fn(
    #         all_data,
    #         lambda x: 0 <= x.get_diffs()[0] <= 2 and 0 <= x.get_diffs()[1] <= 2,
    #     ),
    #     save_dir / "data-le_2_2",
    # )
    # save_data_into(
    #     data_type.keep_editdata_via_fn(
    #         all_data,
    #         lambda x: (0 <= x.get_diffs()[0] <= 10 and 0 <= x.get_diffs()[1] <= 10)
    #         and not (0 <= x.get_diffs()[0] <= 5 and 0 <= x.get_diffs()[1] <= 5),
    #     ),
    #     save_dir / "data-le_10_10-le_5_5",
    # )
    # save_data_into(
    #     data_type.keep_editdata_via_fn(
    #         all_data,
    #         lambda x: (0 <= x.get_diffs()[0] <= 5 and 0 <= x.get_diffs()[1] <= 5)
    #         and not (0 <= x.get_diffs()[0] <= 2 and 0 <= x.get_diffs()[1] <= 2),
    #     ),
    #     save_dir / "data-le_5_5-le_2_2",
    # )
