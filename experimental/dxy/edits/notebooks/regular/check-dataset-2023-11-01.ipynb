#%%
%load_ext autoreload
%autoreload 2
#%%
import re
import termcolor
import random
import numpy as np
from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder
from megatron.tokenizer.tokenizer import Llama2Tokenizer

tokenizer = Llama2Tokenizer()


def filter_string(s):
    return "".join([char for char in s.lower() if char.isalpha() or char == " "])


def extract_content(input_string):
    pattern = r"<INST>(.*?)<SELECTED>"
    matches = re.findall(pattern, input_string, re.DOTALL)

    # Strip each match of leading and trailing whitespaces
    return [match.strip() for match in matches]


def split_list(lst: list[int], delimiter: int):
    result = []
    current_subsequence = []
    for item in lst:
        if item == delimiter:
            if current_subsequence:  # Avoid appending empty lists
                result.append(current_subsequence)
                current_subsequence = []
        else:
            current_subsequence.append(item)
    if current_subsequence:  # Append the last subsequence if it's not empty
        result.append(current_subsequence)
    return result


def show_dataset_info(dataset: MMapIndexedDataset):
    print(f"There are {len(dataset)} examples")
    indexes = [0, random.randint(0, len(dataset) - 1), len(dataset) - 1]
    indexes = sorted(list(set(indexes)))
    for index in indexes:
        example = dataset[index]
        negative_num = np.sum(example < 0).item()  # type: ignore
        print(
            termcolor.colored(
                f"Index={index:5d}, negative_num={negative_num}", color="green"
            )
        )
        example: list[int] = np.abs(example).tolist()
        parts = split_list(example, tokenizer.eos_id)
        parts = [x for x in parts if x != [tokenizer.eos_id]]
        print(
            termcolor.colored(
                f"There are {len(parts)} unpacked examples.", color="green"
            )
        )
        for j, part in enumerate(parts):
            print(termcolor.colored(f"Part {j:5d}/{len(parts):5d}", color="blue"))
            results = tokenizer.detokenize(part)
            print(results)
            print("*" * 100 + "\n")
        print("-" * 100 + "\n")
#%%
dataset = MMapIndexedDataset(
    "/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/train-v1_r3n_s4096_full_pack"
)
instructions = []
for i in range(len(dataset)):
    example: list[int] = np.abs(dataset[i]).tolist()
    text: str = tokenizer.detokenize(example)
    for inst in extract_content(text):
        instructions.append(inst)
print(f"Get {len(instructions)} instructions.")
#%%
unique_instructions = set(instructions)
print(f"#unique_instructions = {len(unique_instructions)}")
unique_v2_instructions = list(set([filter_string(x) for x in unique_instructions]))
print(f"#unique_v2_instructions = {len(unique_v2_instructions)}")
#%%
for index in range(30):
    print(unique_v2_instructions[index])
#%%
dataset = MMapIndexedDataset(
    "/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/valid-v1_r1n_s4096_onlytgt_pad"
)

show_dataset_info(dataset)
#%%
dataset = MMapIndexedDataset(
    "/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/train-v2_r6n_s4096_onlytgt_pack"
)

show_dataset_info(dataset)
#%%
# dataset = MMapIndexedDataset("/mnt/efs/augment/user/dxy/datasets/xtry-rules/0_to_1e9/simple-valid")
dataset = MMapIndexedDataset(
    "/mnt/efs/augment/user/dxy/datasets/xtry-rules/0_to_1e9/structure-valid"
)
show_dataset_info(dataset)