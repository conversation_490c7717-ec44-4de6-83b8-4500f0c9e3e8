#%%
%load_ext autoreload
%autoreload 2
#%%
import pathlib
import tqdm
import json
import termcolor
from augment.research.core import utils_for_dataclass, utils_for_file, utils_for_str
from augment.experimental.dxy.edits.data_type import EditData, EditDataManager
from augment.experimental.dxy.edits.reverse_get_instruction import (
    predict_edit_data_v0,
    predict_edit_data_v1,
)

file_names = (
    "instruct-del_00_00-per40.json",
    "instruct-del_01_01-per40.json",
    "instruct-del_02_02-per40.json",
    "instruct-del_03_03-per40.json",
    "instruct-del_04_04-per40.json",
    "instruct-del_05_05-per40.json",
    "instruct-del_06_06-per40.json",
    "instruct-del_07_07-per40.json",
    "instruct-del_08_08-per40.json",
    "instruct-del_09_09-per40.json",
    "instruct-del_10_10-per40.json",
    "instruct-del_11_20-per10.json",
    "instruct-del_21_30-per10.json",
)
json_file_dir = (
    "/mnt/efs/augment/user/dxy/datasets/edit.local/organized-basic-instruct-2023-10-13/"
)


all_edit_data = []
skip_due_to_no_blocks = []
skip_due_to_multiple_blocks = []

for json_file in file_names:
    json_file_path = pathlib.Path(json_file_dir) / json_file
    with json_file_path.open("r") as f:
        for line in f:
            xdict = json.loads(line)
            data = utils_for_dataclass.create_from_dict(EditData, xdict["edit_data"])
            instruction = xdict["instruction"]
            blocks = utils_for_str.extract_code_within_backticks(instruction)
            if len(blocks) == 0:
                # print(f"No blocks for this instruction:\n{instruction}")
                skip_due_to_no_blocks.append((data, instruction))
                continue
            elif len(blocks) > 1:
                # print(f"Too many blocks for this instruction:\n{instruction}")
                skip_due_to_multiple_blocks.append((data, instruction))
                continue
            final_instruction = blocks[0]
            if final_instruction[0] == '"' and final_instruction[-1] == '"':
                final_instruction = final_instruction[1:-1]
            elif final_instruction[0] == "`" and final_instruction[-1] == "`":
                final_instruction = final_instruction[1:-1]
            data.instruction = final_instruction
            all_edit_data.append(data)
print(f"There are {len(all_edit_data)} edit data.")
print(f"skip_due_to_no_blocks={len(skip_due_to_no_blocks)}")
print(f"skip_due_to_multiple_blocks={len(skip_due_to_multiple_blocks)}")
#%%
import random

index = random.randint(0, len(all_edit_data) - 1)

all_edit_data[index].show_lines()
print(f"instruction: {all_edit_data[index].instruction}")