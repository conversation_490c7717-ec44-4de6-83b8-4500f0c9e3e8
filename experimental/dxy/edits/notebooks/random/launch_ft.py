"""Launch a FLASK server for the edit demo only!

Run on a 4-nodes H100 machine

python experimental/dxy/edits/notebooks/random/launch_ft.py --port 5005 \
    --edit_ckp "/mnt/efs/augment/user/dxy/logs/edit.12.21/DSC-B-ALL-S4K_WUP0_110-WD1_0-b4x8x2x1-lr_2e-5_constant/checkpoint_llama_iteration_120"

python experimental/dxy/edits/notebooks/random/launch_ft.py --port 5005 \
    --edit_ckp "/mnt/efs/augment/user/dxy/logs/edit.12.22.NLP/DSC-I-TRN-Instruct-E1-WD1_0-b4x8x2x1-lr_2e-5_constant/checkpoint_llama_iteration_80"
"""
import argparse
import logging
import pathlib
import threading
import time
import typing

from flask import Flask, jsonify, request

from experimental.dxy.edits.prompt_formatter import (  # DroidFormatter,
    DSInstructNLPFormatter,
)
from research.core import utils_for_file, utils_for_log, utils_for_str
from research.core.model_input import ModelInput
from research.models.llama2_models import FastBackwardDeepSeekModel
from research.models.meta_model import GenerationOptions

app = Flask(__name__)
GLOBAL_EDIT_LOG_DIR = pathlib.Path(
    "/mnt/efs/augment/user/dxy/datasets/edit.raw/dogfood_edit_logs"
)
GLOBAL_EDIT_LOG_DIR.mkdir(exist_ok=True, parents=True)
LOGGER_SAVE_DIR = GLOBAL_EDIT_LOG_DIR / "loggers"
LOGGER_SAVE_DIR.mkdir(exist_ok=True, parents=True)
logger = utils_for_log.create_logger(
    __file__,
    log_file=LOGGER_SAVE_DIR
    / f"{pathlib.Path(__file__).stem}-{utils_for_log.time_string()}.log",
    log_level=logging.INFO,
)
logger.info(f"__file__: {__file__}")
current_directory = pathlib.Path(__file__).parent
logger.info(f"The Current Directory: {current_directory}")
augment_directory = current_directory.parent.parent.parent.parent.parent
logger.info(f"The Augment Directory: {augment_directory}")
research_directory = augment_directory / "research"
logger.info(f"The Augment Research Directory: {research_directory}")
assert research_directory.exists(), f"{research_directory} must exist"
logger.info(f"The Edit Log Directory: {GLOBAL_EDIT_LOG_DIR}")


class EditModel:
    """The edit model with different versions of prompts."""

    ckp_path: pathlib.Path = pathlib.Path(
        # "/mnt/efs/augment/user/dxy/logs/edit.12.21/DSC-B-ALL-S4K_WUP0_110-WD1_0-b4x8x2x1-lr_2e-5_constant/checkpoint_llama_iteration_120"
        "/mnt/efs/augment/user/dxy/logs/edit.01.04/DSC-I-D0Vaug-ALL-S4K_WUP0_E1-WD1_0-b4x8x2x1-lr_1e-5_constant/checkpoint_llama_iteration_90",
    )

    # def __init__(self) -> None:
    #     self.ckp_path = pathlib.Path(
    #         "/mnt/efs/augment/user/dxy/logs/edit.12.21/DSC-B-ALL-S4K_WUP0_110-WD1_0-b4x8x2x1-lr_2e-5_constant/checkpoint_llama_iteration_120"
    #     )

    def load(self):
        assert self.ckp_path.exists()
        # self.prompt_formatter = DroidFormatter()
        self.prompt_formatter = DSInstructNLPFormatter()
        self.model = FastBackwardDeepSeekModel(
            self.ckp_path, seq_length=4096, model_parallel_size=4
        )
        self.model.load()
        self.model.prompt_formatter.tokenizer = self.prompt_formatter.tokenizer

    def __call__(
        self,
        selected_code: str,
        instruction: str,
        prefix: typing.Optional[str] = None,
        suffix: typing.Optional[str] = None,
        lines_in_prefix_suffix: int = 0,
        top_p: float = 0.0,
        temperature: float = 0.0,
    ) -> str:
        if lines_in_prefix_suffix <= 0 or prefix is None or suffix is None:
            prefix, suffix = "", ""
        else:
            prefix = utils_for_str.get_last_n_lines(prefix, lines_in_prefix_suffix)
            suffix = utils_for_str.get_first_n_lines(suffix, lines_in_prefix_suffix)
        minput = ModelInput(
            prefix=prefix,
            suffix=suffix,
            extra={
                "instruction": instruction,
                "selected_code": selected_code,
            },
        )
        prompt, _ = self.prompt_formatter.prepare_prompt(minput)
        print(f"The prompt length: {len(prompt)}")
        result = self.model.raw_generate(
            prompt,
            GenerationOptions(
                max_generated_tokens=512,
                top_k=None,
                top_p=top_p,
                temperature=temperature,
            ),
        )
        markdown_text = utils_for_str.extract_the_last_markdown_block(result)
        if markdown_text is None:
            return result
        else:
            return markdown_text


GLOBAL_EDIT_MODEL = EditModel()


def shorten(string: str):
    return string.lower().strip()


@app.route("/edit", methods=["POST"])
def edit():
    """The edit communication func.

    Required fields in the chat mode:
    - selected_code: str
    - instruction: str
    """
    data = request.get_json()
    logger.info(f"Receive data with keys: {data.keys()}")
    # As defined here https://github.com/augmentcode/augment/blob/main/clients/vscode/src/augment-api.ts#L140
    # our code edit feature accepts these inputs:
    #   - instruction: string;
    #   - selected_code: string;
    #   - prefix: string;
    #   - suffix: string;
    #   - top_k?: number;
    #   - top_p?: number;
    #   - temperature?: number;
    #   - lang?: string;
    #   - lines_in_prefix_suffix?: number;
    #
    request_id = data.get("request_id", None)
    selected_code = data.get("selected_code", None)
    instruction = data.get("instruction", None)
    original_prefix = data.get("prefix", None)
    original_suffix = data.get("suffix", None)
    top_k = data.get("top_k", 0)
    top_p = data.get("top_p", 0.0)
    temperature = data.get("temperature", 0)
    lines_in_prefix_suffix = data.get("lines_in_prefix_suffix", 0)
    language = data.get("lang", None)
    debug = data.get("debug", False)
    # Normalize top_k, top_p, temperature
    try:
        top_k = int(float(top_k))
    except (ValueError, TypeError):
        top_k = 0
    try:
        top_p = float(top_p)
    except (ValueError, TypeError):
        top_p = 0.0
    try:
        temperature = float(temperature)
    except (ValueError, TypeError):
        temperature = 0.0
    try:
        lines_in_prefix_suffix = int(float(lines_in_prefix_suffix))
    except (ValueError, TypeError):
        lines_in_prefix_suffix = 0

    thread_id = threading.get_ident()

    if selected_code is None or instruction is None:
        logger.info("Did not find selected_code or instruction")
        return jsonify({"response": "", "status": "missing-message-input"})

    try:
        instruction = instruction.strip()
        logger.info(f"instruction: {instruction}")
        logger.info(f"request_id: {request_id}")
        logger.info(
            "Generation hyper-paramters:\n"
            + f"top_k = {top_k}\n"
            + f"top_p = {top_p}\n"
            + f"temperature = {temperature}\n"
            + f"lines_in_prefix_suffix = {lines_in_prefix_suffix}"
        )
        if lines_in_prefix_suffix <= 0:
            prefix, suffix = None, None
        else:
            prefix, suffix = original_prefix, original_suffix
        start_time = time.time()
        response = GLOBAL_EDIT_MODEL(
            selected_code,
            instruction,
            prefix=prefix,
            suffix=suffix,
            lines_in_prefix_suffix=lines_in_prefix_suffix,
            top_p=top_p,
            temperature=temperature,
        )
        # Cache the results to a file
        cache_file_path = GLOBAL_EDIT_LOG_DIR / (
            utils_for_log.time_string().replace(":", "-")
            + f"-TID{thread_id}"
            + f"-{utils_for_str.get_random_str(8)}"
            + ".json"
        )
        time_cost = time.time() - start_time
        debug_json_data = {}
        try:
            debug_json_data = {
                "request_id": request_id,
                "selected_code": selected_code,
                "prefix": original_prefix,
                "suffix": original_suffix,
                "instruction": instruction,
                "response": response,
                "lines_in_prefix_suffix": lines_in_prefix_suffix,
                "language": language,
                "thread_id": thread_id,
                "sender_ip": request.remote_addr,
                "time_cost(seconds)": time_cost,
            }
            utils_for_file.write_json(
                cache_file_path,
                debug_json_data,
                indent=2,
            )
            logger.info(f"Time cost for model inference: {time_cost*1000:.1f} ms")
            logger.info(f"Cache file path: {cache_file_path}")
            logger.info(f"Model output:\n{response}")
        except BaseException as e:
            logger.info(f"Fail to write cache file ({cache_file_path}) due to {e}")
        # If it is the debug mode, return more information.
        if debug:
            return jsonify(
                {
                    "response": response,
                    "modified_code": "",
                    "status": "success",
                    **debug_json_data,
                }
            )
        else:
            return jsonify(
                {"response": response, "modified_code": "", "status": "success"}
            )
    except BaseException as e:
        print(f"Fail to call model.generate due to {e}")
        return jsonify({"response": "", "status": f"failed due to {e}"})


@app.route("/log_status", methods=["POST"])
def log_status():
    """Logging the status.

    - request_id: str
    - instruction: str
    """
    data = request.get_json()
    logger.info(f"Receive data with keys: {data.keys()}")

    request_id = data.get("request_id", None)
    instruction = data.get("instruction", None)
    human_annotated_text = data.get("human_annotated_text", None)
    human_annotated_instruction = data.get("human_annotated_instruction", None)
    status = data.get("status", None)
    debug = data.get("debug", False)

    if request_id is None or instruction is None:
        logger.info("Did not find the request_id or instruction")
        return jsonify({"status": "incorrect inputs"})

    try:
        logger.info(f"instruction: {instruction}")
        logger.info(f"request_id: {request_id}")
        logger.info(f"status: {status}")
        thread_id = threading.get_ident()
        # Cache the results to a file
        cache_file_path = GLOBAL_EDIT_LOG_DIR / (
            "FEEDBACK-"
            + utils_for_log.time_string().replace(":", "-")
            + f"-TID{thread_id}"
            + f"-{utils_for_str.get_random_str(8)}"
            + ".json"
        )
        debug_json_data = {}
        try:
            debug_json_data = {
                "instruction": instruction,
                "request_id": request_id,
                "thread_id": thread_id,
                "human_annotated_text": human_annotated_text,
                "human_annotated_instruction": human_annotated_instruction,
                "status": status,
                "sender_ip": request.remote_addr,
            }
            utils_for_file.write_json(
                cache_file_path,
                debug_json_data,
                indent=2,
            )
            logger.info(f"Cache file path: {cache_file_path}")
        except BaseException as e:
            logger.info(f"Fail to write cache file ({cache_file_path}) due to {e}")
        # If it is the debug mode, return more information.
        if debug:
            return jsonify({"status": "success", **debug_json_data})
        else:
            return jsonify({"status": "success"})
    except BaseException as e:
        print(f"Fail to call model.generate due to {e}")
        return jsonify({"response": "", "status": f"failed due to {e}"})


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--port",
        type=str,
        default="5005",
        help="The port to send request to this server.",
    )
    parser.add_argument(
        "--edit_ckp",
        type=str,
        default=None,
        required=True,
        help="The path to the checkpoint file.",
    )
    args = parser.parse_args()
    GLOBAL_EDIT_MODEL.ckp_path = pathlib.Path(args.edit_ckp)
    GLOBAL_EDIT_MODEL.load()
    app.run(host="0.0.0.0", processes=1, threaded=True, debug=False, port=args.port)
