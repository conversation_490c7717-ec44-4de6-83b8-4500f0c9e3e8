# python experimental/dxy/misc/test_2023_09_04.py
import collections
import logging
from pathlib import Path

import tree_sitter as ts
from termcolor import colored

from research.eval.dataset_generation_lib import (
    advanced_snippet_generators as patch_generate_lib,
)
from research.static_analysis.common import decode_bytes
from research.static_analysis.experimental_parsing import AugmentedParsedFile
from research.static_analysis.parsing import (
    ScopeOrSpan,
    ScopeTreeParser,
    SrcScope,
    SrcSpan,
)
from research.static_analysis.usage_analysis import ParsedFile, UsageIndex

global_logger = logging.Logger("notebook")
# Clear previous handlers
for handler in global_logger.handlers[:]:
    global_logger.removeHandler(handler)
# Create a console handler (ch) and set the level to DEBUG
ch = logging.StreamHandler()
# Create a formatter
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
# Add formatter to ch
ch.setFormatter(formatter)
# Add ch to logger
global_logger.addHandler(ch)
# Set the logging level
global_logger.setLevel(logging.INFO)
global_logger.info("Hello World")


colors = ["red", "green", "blue", "light_yellow", "cyan"]
color_index = 0


def get_color():
    global color_index
    color = colors[color_index % len(colors)]
    color_index += 1
    return color


def customized_find(root_node: ts.Node):
    target_nodes = []
    unique_types = set()
    queue: collections.deque[tuple[ts.Node, int]] = collections.deque([(root_node, 0)])
    while queue:
        (node, depth) = queue.popleft()
        target_nodes.append((node, depth))
        unique_types.add(node.type)
        for cur_node in node.children:
            queue.append((cur_node, depth + 1))
    unique_types = sorted(list(unique_types))
    print(f"unique types: {unique_types}")
    print(f"Find {len(target_nodes)} nodes.")
    return target_nodes


ex_file = """
@pg.members([
    ('seed', pg.typing.Int().noneable(), 'Random seed.')
])
class RandomScalar(base.Scalar):

  def _on_bound(self):
    self._random = x + 1  # hello-comment
    self.seed = 1
"""

# ex_file = """
# def parse_cmd():
#     parser = argparse.ArgumentParser()

#     # System configuration
#     parser.add_argument(
#         "--image_name",
#         type=str,
#         required=True,
#         help="The docker image name.",
#     )
#     parser.add_argument(
#         "--output_dir",
#         type=str,
#         required=True,
#         help="The output directory.",
#     )
#     return parser.parse_args()

# # hello
# # world
# # this is me!
# """

# # ex_file = """
# # @pg.members([])
# # class A:
# #   '''Class doc string.'''

# #   x: int = -1
# #   '''Attribute x.'''
# # """

doc = ParsedFile.parse(Path("file2.py"), "python", ex_file)

color_index = 0
target_nodes = customized_find(doc.ts_tree.root_node)

# , types=("!=", "call", "def", "dictionary")
for index, (node, depth) in enumerate(target_nodes):
    breaker = "-" * 8
    print(
        breaker
        + f" : [{index:05d}] {node.type:16s} : depth={depth} : {node.start_byte} - {node.end_byte}"
    )
    print(colored(decode_bytes(node.text), color=get_color()), end="\n")
    print(breaker * 4)

# print("")
# print("-" * 100)
# print("")


# doc = AugmentedParsedFile.from_code_str(ex_file)
# # [final_doc] = patch_generate_lib.find_all_api_calls([doc], logger=global_logger)
# [final_doc] = patch_generate_lib.find_all_meaningful_strs([doc], logger=global_logger)
# for index, node in enumerate(final_doc.ts_nodes):
#     crange = final_doc.tsnode_to_crange(node)
#     breaker = "-" * 8
#     print(breaker + f" : [{index:05d}] {node.type:16s} : crange = {crange}")
#     text = doc.doc.code[crange.start : crange.stop]
#     print(colored(text, color=get_color()), end="\n")


# text = """
#     Returns True if the passed email address is valid.

#     The local part of the email precedes the singular @ symbol and
#     is associated with a display-name. For example, "john.smith"
#     The domain is stricter than the local part and follows the @ symbol.

#     Global email checks:
#      1. There can only be one @ symbol in the email address. Technically if the
#         @ symbol is quoted in the local-part, then it is valid, however this
#         implementation ignores "" for now.
#         (See https://en.wikipedia.org/wiki/Email_address#:~:text=If%20quoted,)
#      2. The local-part and the domain are limited to a certain number of octets. With
#         unicode storing a single character in one byte, each octet is equivalent to
#         a character. Hence, we can just check the length of the string.
#     Checks for the local-part:
#     3. The local-part may contain: upper and lowercase latin letters, digits 0 to 9,
#         and printable characters (!#$%&'*+-/=?^_`{|}~)
#      4. The local-part may also contain a "." in any place that is not the first or
#         last character, and may not have more than one "." consecutively.

#     Checks for the domain:
#      5. The domain may contain: upper and lowercase latin letters and digits 0 to 9
#      6. Hyphen "-", provided that it is not the first or last character
#      7. The domain may also contain a "." in any place that is not the first or
#         last character, and may not have more than one "." consecutively.

#     >>> for email, valid in email_tests:
#     ...     assert is_valid_email_address(email) == valid
# """

# if True:
#     lines = text.split("\n")
#     lines = [x.replace("#", "") for x in lines]
#     lines = [x.replace("'", "") for x in lines]
#     lines = [x.replace('"', "") for x in lines]
#     lines = [x.replace(" ", "") for x in lines]
#     count = sum(x == "" for x in lines)
#     print(count)
