"""Create the rlhf dataset for completion models.

Be careful about data before 2024-08-06: https://www.notion.so/2024-08-13-Acceptance-Increase-283cf387c0d94ee5a654833072daab7f#b2aa9e41f1b24f33b097c68bf23b9d36

# Usage

python experimental/dxy/rag/rlhf/data/dump-user-events.py --date_range 20240801-20241029
python experimental/dxy/rag/rlhf/data/dump-user-events.py --date_range 20250116-20250119

The default output to /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114
"""

import argparse
import pathlib

from base.datasets import tenants
from base.datasets.user_event_lib import (
    CompletionRequestIdIssuedEvent,
    TextEditEvent,
    UserEventFilters,
    UserEventStream,
    UserEventUnion,
)
from experimental.dxy.rag.rlhf.shared_lib import traverse_date_range
from research.core import utils_for_file


def dump_raw_data(
    date_ranges: list[str],
    tenant_names: list[str],
    root_out_dir: pathlib.Path = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal"
    ),
):
    all_date_ranges = []
    for cur_date_range in date_ranges:
        start_date, end_date = cur_date_range.split("-")
        all_date_ranges.extend(traverse_date_range(start_date, end_date))
    print(f"There are {len(all_date_ranges)} date ranges.")
    for date, xstart, xend in all_date_ranges:
        print(f"{date}: {xstart} - {xend}")
    for index, (date, xstart, xend) in enumerate(all_date_ranges):
        output_dir = root_out_dir / f"{date}-user-event"
        print(f"Process {index:03d}/{len(all_date_ranges)}: {date}")
        filters = UserEventFilters(
            timestamp_begin=xstart,
            timestamp_end=xend,
            event_types=[
                "completion_request_id_issued",
                "text_edit",
            ],
        )
        for tenant_name in tenant_names:
            stream = UserEventStream.from_query(
                tenants.get_tenant(tenant_name), filters
            )
            events: list[UserEventUnion] = list(stream.events)
            events_json: list[dict] = []
            for event in events:
                if isinstance(event, TextEditEvent):
                    json_data = TextEditEvent.schema().dump(event)
                elif isinstance(event, CompletionRequestIdIssuedEvent):
                    json_data = CompletionRequestIdIssuedEvent.schema().dump(event)
                else:
                    raise TypeError(f"Unknown event type {type(event)}.")
                events_json.append(json_data)
            if len(events_json) == 0:
                print(f"Skip {date} {tenant_name} as there is no data.")
                continue
            output_dir.mkdir(exist_ok=True, parents=False)
            output_path = (
                output_dir / f"{tenant_name}-raw-data-{len(events_json)}-samples.jsonl"
            )
            utils_for_file.write_jsonl(output_path, events_json)
            print(f"Save {len(events_json)} items into {output_path}.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--date_range",
        type=str,
        required=True,
        help="The date range to process, e.g., 20240801-20240802, we accept multiple ranges.",
        nargs="+",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114",
    )
    args = parser.parse_args()

    dump_raw_data(
        date_ranges=args.date_range,
        tenant_names=["dogfood", "dogfood-shard", "i0-vanguard0", "aitutor-turing"],
        root_out_dir=pathlib.Path(args.output_dir),
    )
