#%%
%load_ext autoreload
%autoreload 2
#%%
import os
import pathlib
from research.eval.harness.factories import create_system
from experimental.dxy.rogue.exps.model_configs import model_config_dict
from experimental.michiel.configs.eval.retriever_configs import retriever_config_dict

system_config = {
    "name": "basic_rag",
    "model": model_config_dict["roguesl_4k"],
    "generation_options": {"max_generated_tokens": 280},
    "retriever": retriever_config_dict["ethanol616"],
    "experimental": {
        "remove_suffix": False,
        "trim_on_dedent": False,
        "retriever_top_k": 25,
    },
    "fim_gen_mode": "evaluation",
}
system_config["model"]["checkpoint_path"] = (
    "/mnt/efs/augment/checkpoints/dxy/sc2-3b-rogue/baseline-4k-eth6_morelang3-bs512s5k"
)
system_config["model"]["model_parallel_size"] = 1
system = create_system(system_config)
#%%
import research.eval.harness.metrics as metrics
from research.eval.harness.tasks.api_call_task import (
    ApiCallTask,
    hydra_lib,
    SUPPORTED_DATASET2DIR,
    HYDRA_SOFT_TIMEOUT_SECS,
    HYDRA_HARD_TIMEOUT_SECS,
)

driver = hydra_lib.Driver(
    driver_name="eval-augmented-hydra-test",
    local_timeout_secs=HYDRA_SOFT_TIMEOUT_SECS,
    global_timeout_secs=HYDRA_HARD_TIMEOUT_SECS,
    hydra_block_resource_internet_access=False,
)
task = ApiCallTask(
    SUPPORTED_DATASET2DIR["finegrained-python.large"],
)
#%%
system.load()
#%%
index = 512
xdata = task[index]
system.clear_retriever()
system.add_docs(xdata.repository)


completion = system.generate(xdata.model_input)
forward_metrics = metrics.safe_forward_metrics(system, xdata.model_input, completion)
prompt = system.get_model().tokenizer.detokenize(completion.prompt_tokens)
results = task.metric(
    xdata.model_input,
    xdata.patch,
    tag=xdata.tag,
    image_name=xdata.image_name,
    completion=completion.generated_text,
    driver=driver,
    forward_metrics=forward_metrics,
)
print(results)