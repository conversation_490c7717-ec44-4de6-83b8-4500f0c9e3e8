#%% md
# README

You need to run `experimental/jiayi/next_edit/inspect_next_edit_hindsight.ipynb` first to generate the dataset. Then run `experimental/vaibhav/convert_ground_truth_to_input.py` to convert the dataset to task inputs. Then run `experimental/vaibhav/location_retriever_eval.py` to evaluate the model. Finally, run this notebook to plot the metrics.

#%%
%load_ext autoreload
%autoreload 2
#%%
from research.utils.diagnostic_analysis_util import (
    calculate_diagnostic_metrics_at_most_l_swaps,
)

from pathlib import Path
#%%
import pickle

with open(
    "/mnt/efs/augment/user/vaibhav/dogfood-v2_2025_02_03_28days_10s_task_inputs_2000.pkl",
    "rb",
) as f:
    from research.eval.harness.tasks.next_edit_hindsight_location_eval_task import (
        NextEditHindsightTaskInput,
    )

    task_inputs: list[NextEditHindsightTaskInput] = pickle.load(f)
#%%
raven_eval_output_path = Path("~/tmp/raven_eval_curr_file_2000/next_edit_hindsight_location_NextEditHindsightLocationEvalTask_output.pkl").expanduser()
with open(raven_eval_output_path, "rb") as f:
    output_old = pickle.load(f)

raven_eval_output_path_new = Path("~/tmp/raven_distill_eval_curr_file_2000/next_edit_hindsight_location_NextEditHindsightLocationEvalTask_output.pkl").expanduser()

with open(raven_eval_output_path_new, "rb") as f:
    output_new = pickle.load(f)

#%%
raven_eval_output_path_v6 = Path("~/tmp/raven_v6_eval_2000/output.pkl").expanduser()

with open(raven_eval_output_path_v6, "rb") as f:
    output= pickle.load(f)
#%%
metrics = calculate_diagnostic_metrics_at_most_l_swaps(
    output, task_inputs, [1, 3, 8, 32], list(range(9))
)

metrics
#%%
metrics_old = calculate_diagnostic_metrics_at_most_l_swaps(
    output_old, task_inputs, [1, 3, 8, 32], list(range(9))
)

metrics_new = calculate_diagnostic_metrics_at_most_l_swaps(
    output_new, task_inputs, [1, 3, 8, 32], list(range(9))
)
#%%
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, Any

def plot_k0_comparison(
    metrics_old: Dict[str, Dict[int, Dict[int, float]]],
    metrics_new: Dict[str, Dict[int, Dict[int, float]]],
    metrics_third: Dict[str, Dict[int, Dict[int, float]]],
    figsize: tuple = (10, 6),
    save_path: str = "output/k0_comparison.png"
) -> None:
    """
    Plot k=0 values comparison between old, new, and third metrics for current_file_recall.
    
    Args:
        metrics_old: Dictionary containing old metrics
        metrics_new: Dictionary containing new metrics
        metrics_third: Dictionary containing third model metrics
        figsize: Figure size tuple (width, height)
        save_path: Optional path to save the plot
    """
    plt.style.use("bmh")
    fig, ax = plt.subplots(figsize=figsize)
    
    # Extract k=0 values for each top_k
    top_ks = sorted(metrics_old["current_file_recall"].keys())
    k0_values_old = [metrics_old["current_file_recall"][k][0] for k in top_ks]
    k0_values_new = [metrics_new["current_file_recall"][k][0] for k in top_ks]
    k0_values_third = [metrics_third["current_file_recall"][k][0] for k in top_ks]
    
    x = np.arange(len(top_ks))
    width = 0.25  # Reduced width to accommodate third bar
    
    # Create bars
    ax.bar(x - width, k0_values_old, width, label='Old Location Model', color='#2E86C1', alpha=0.8)
    ax.bar(x, k0_values_new, width, label='New Location Model', color='#27AE60', alpha=0.8)
    ax.bar(x + width, k0_values_third, width, label='Editor Model', color='#E74C3C', alpha=0.8)
    
    # Customize plot
    ax.set_ylabel('Recall at k=0')
    ax.set_xlabel('Top k')
    ax.set_title('Comparison Location Models for Current File Recall')
    ax.set_xticks(x)
    ax.set_xticklabels(top_ks)
    ax.legend()
    
    # Add value labels on bars
    def autolabel(rects):
        for rect in rects:
            height = rect.get_height()
            ax.annotate(f'{height:.3f}',
                       xy=(rect.get_x() + rect.get_width() / 2, height),
                       xytext=(0, 3),  # 3 points vertical offset
                       textcoords="offset points",
                       ha='center', va='bottom',
                       rotation=90)
    
    autolabel(ax.containers[0])
    autolabel(ax.containers[1])
    autolabel(ax.containers[2])
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches="tight")
    
    plt.show()

plot_k0_comparison(metrics_old, metrics_new, metrics, save_path="output/k0_comparison_same_chunks.png")
#%%
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, Any


def plot_recall_metrics(
    metrics: Dict[str, Any], figsize: tuple = (15, 7), save_path: str = None
) -> None:
    """
    Create professional plots showing recall metrics with baseline indicators.
    """
    plt.style.use("bmh")
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)

    k_colors = {
        1: "#2E86C1",  # Steel Blue
        3: "#27AE60",  # Emerald Green
        8: "#F39C12",  # Orange
        32: "#C0392B",  # Dark Red
    }

    def plot_recall_data(ax, data, title):
        # Plot baseline (k=0) lines and shading first
        baselines = {k: data[k][0] for k in metrics["top_ks"]}
        y_max = max(max(values.values()) for values in data.values())

        # Sort baselines for proper shading
        sorted_baselines = sorted(baselines.items(), key=lambda x: x[1])

        # Add shaded regions between baseline values
        prev_y = 0
        for k, baseline in sorted_baselines:
            ax.axhspan(prev_y, baseline, color=k_colors[k], alpha=0.05, zorder=1)
            prev_y = baseline

            # Add horizontal baseline lines
            ax.axhline(
                y=baseline,
                color=k_colors[k],
                linestyle="--",
                alpha=0.5,
                zorder=2,
                linewidth=1.5,
            )

            # Add baseline labels on the right
            ax.text(
                ax.get_xlim()[1] * 1.02,
                baseline,
                f"k=0: {baseline:.2f}",
                color=k_colors[k],
                verticalalignment="center",
                fontsize=9,
                fontweight="bold",
            )

        # Plot the actual data
        for k in metrics["top_ks"]:
            recall_data = data[k]
            x = list(recall_data.keys())
            y = list(recall_data.values())

            # Plot initial point
            ax.plot(
                [0],
                [recall_data[0]],
                "o",
                color=k_colors[k],
                markersize=12,
                label=f"k={k}",
                zorder=4,
                markeredgecolor="white",
                markeredgewidth=2,
            )

            # Plot line and points
            if len(x) > 1:
                ax.plot(
                    x[1:],
                    y[1:],
                    "-",
                    color=k_colors[k],
                    linewidth=2.5,
                    alpha=0.8,
                    zorder=3,
                )
                ax.plot(
                    x[1:],
                    y[1:],
                    "o",
                    color=k_colors[k],
                    markersize=8,
                    alpha=0.8,
                    zorder=3,
                    markeredgecolor="white",
                    markeredgewidth=1.5,
                )

            # Mark and annotate the highest point
            max_y = max(y)
            max_x = x[y.index(max_y)]
            ax.plot(
                max_x,
                max_y,
                "*",
                color=k_colors[k],
                markersize=15,
                zorder=5,
                markeredgecolor="white",
                markeredgewidth=1.5,
            )
            ax.annotate(
                f"{max_y:.2f}",
                xy=(max_x, max_y),
                xytext=(5, 5),
                textcoords="offset points",
                fontsize=9,
                fontweight="bold",
                color=k_colors[k],
                bbox=dict(
                    facecolor="white",
                    edgecolor=k_colors[k],
                    alpha=0.7,
                    pad=2,
                    boxstyle="round,pad=0.5",
                ),
                zorder=6,
            )

        # Customize appearance
        ax.set_title(title, fontsize=14, fontweight="bold", pad=20)
        ax.set_xlabel("Number of Swaps", fontsize=12)
        ax.set_ylabel("Recall", fontsize=12)
        ax.grid(True, alpha=0.3, linestyle="--", zorder=0)
        ax.spines["top"].set_visible(False)
        ax.spines["right"].set_visible(False)

        # Set axis limits with padding
        ax.set_ylim(0, y_max * 1.1)
        ax.set_xlim(
            ax.get_xlim()[0], ax.get_xlim()[1] * 1.15
        )  # Extra space for baseline labels

        # Add legend
        legend = ax.legend(
            bbox_to_anchor=(1.02, 1),
            loc="upper left",
            frameon=True,
            fancybox=True,
            shadow=True,
            fontsize=10,
        )
        legend.get_frame().set_alpha(0.9)

    # Plot both subplots
    plot_recall_data(ax1, metrics["current_file_recall"], "Current File Recall")
    plot_recall_data(ax2, metrics["non_current_file_recall"], "Non-Current File Recall")

    # Add subtle background color
    fig.patch.set_facecolor("#f8f9fa")
    ax1.set_facecolor("#ffffff")
    ax2.set_facecolor("#ffffff")

    # Adjust layout
    plt.tight_layout(w_pad=4)

    # Add a super title
    fig.suptitle("Recall Metrics Analysis", fontsize=16, fontweight="bold", y=1.05)

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches="tight", facecolor="white")

    plt.show()


# Call the function with your metrics
plot_recall_metrics(
    metrics_old, save_path="output/recall_metrics_at_most_l_swaps_chunked_no_cursor_overlap.png"
)
#%%
# Call the function with your metrics
plot_recall_metrics(
    metrics_new, save_path="output/recall_metrics_at_most_l_swaps_chunked_new_no_cursor_overlap.png"
)
#%%
def plot_diagnostic_proportions(
    metrics: Dict[str, Dict[str, float]],
    figsize: tuple = (12, 6),
    title: str = "Proportion of Diagnostic Coverage",
    save_path: str = None,
) -> None:
    """
    Create a professional bar plot showing the proportion of useful diagnostics.

    Args:
        metrics: Dictionary containing diagnostic metrics with keys:
                'covered_diagnostic_types' and 'total_diagnostics_types'
        figsize: Tuple specifying figure dimensions (width, height)
        title: Plot title string
        save_path: Optional path to save the plot

    Returns:
        None
    """
    if not isinstance(metrics, dict):
        raise TypeError("metrics must be a dictionary")

    required_keys = {"covered_diagnostic_types", "total_diagnostics_types"}
    if not all(key in metrics for key in required_keys):
        raise ValueError(f"metrics must contain keys: {required_keys}")

    covered = metrics["covered_diagnostic_types"]
    total = metrics["total_diagnostics_types"]

    # Validate data consistency
    if not covered.keys() == total.keys():
        raise ValueError(
            "Diagnostic types must match in covered and total dictionaries"
        )

    # Calculate proportions
    proportions = {
        type_: (count / total[type_] if total[type_] > 0 else 0)
        for type_, count in covered.items()
    }

    # Create figure
    fig, ax = plt.subplots(figsize=figsize)

    # Plot bars with custom colors
    x = np.arange(len(proportions))
    colors = ["#2ecc71", "#3498db", "#e74c3c"]  # Professional color scheme
    bars = ax.bar(
        x,
        proportions.values(),
        width=0.6,
        edgecolor="black",
        linewidth=1,
        color=colors[: len(proportions)],
    )

    # Customize appearance
    ax.set_title(title, pad=20, fontsize=14, fontweight="bold")
    ax.set_xlabel("Diagnostic Type", fontsize=12)
    ax.set_ylabel("Proportion of Diagnostics", fontsize=12)
    ax.set_xticks(x)
    ax.set_xticklabels(proportions.keys(), rotation=45, ha="right")
    ax.grid(True, axis="y", alpha=0.3)
    ax.set_ylim(0, 0.15)

    # Add value labels on bars
    for bar in bars:
        height = bar.get_height()
        ax.text(
            bar.get_x() + bar.get_width() / 2,
            height,
            f"{height:.1%}",
            ha="center",
            va="bottom",
        )

    # Adjust layout
    plt.tight_layout()

    # Save if path provided
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches="tight")

    plt.show()


plot_diagnostic_proportions(
    metrics, save_path="output/diagnostic_proportions_chunked_new.png"
)
#%%
def plot_helpful_messages_data(
    metrics: Dict[str, float],
    figsize: tuple = (12, 6),
    title: str = "Proportion of Useful Messages by File Category",
    save_path: str = None,
) -> None:
    """
    Create a professional bar plot showing the proportion of helpful messages.

    Args:
        metrics: Dictionary containing metrics with keys:
                'proportion_helpful_messages',
                'proportion_helpful_messages_current_file',
                'proportion_helpful_messages_non_current_file'
        figsize: Tuple specifying figure dimensions (width, height)
        title: Plot title string
        save_path: Optional path to save the plot

    Returns:
        None
    """
    # Input validation
    required_keys = {
        "proportion_helpful_messages",
        "proportion_helpful_messages_current_file",
        "proportion_helpful_messages_non_current_file",
    }
    if not all(key in metrics for key in required_keys):
        raise ValueError(f"metrics must contain keys: {required_keys}")

    # Create figure
    fig, ax = plt.subplots(figsize=figsize)
    plt.style.use("bmh")

    # Data preparation
    categories = ["All Files", "Current File", "Non-Current File"]
    values = [
        metrics["proportion_helpful_messages"],
        metrics["proportion_helpful_messages_current_file"],
        metrics["proportion_helpful_messages_non_current_file"],
    ]

    # Custom colors for professional look
    colors = ["#3498db", "#2ecc71", "#e74c3c"]

    # Plot bars
    bars = ax.bar(
        categories, values, width=0.6, color=colors, edgecolor="black", linewidth=1
    )

    # Customize appearance
    ax.set_title(title, pad=20, fontsize=14, fontweight="bold")
    ax.set_xlabel("File Category", fontsize=12)
    ax.set_ylabel("Proportion of Helpful Messages", fontsize=12)

    # Set y-axis limits with some padding
    ax.set_ylim(0, max(values) * 1.1)

    # Add grid for better readability
    ax.grid(True, axis="y", alpha=0.3)

    # Rotate labels if needed
    plt.xticks(rotation=0)

    # Add value labels on top of bars
    for bar in bars:
        height = bar.get_height()
        ax.text(
            bar.get_x() + bar.get_width() / 2,
            height,
            f"{height:.1%}",
            ha="center",
            va="bottom",
        )

    # Add subtle background color
    ax.set_facecolor("#f8f9fa")

    # Adjust layout
    plt.tight_layout()

    # Save if path provided
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches="tight")

    plt.show()


# Example usage:
"""
metrics = {
    'proportion_helpful_messages': 0.75,
    'proportion_helpful_messages_current_file': 0.85,
    'proportion_helpful_messages_non_current_file': 0.65
}
plot_helpful_messages_data(metrics)
"""

plot_helpful_messages_data(metrics, save_path="output/helpful_messages_data_new.png")
#%%
# Get top useful diagnostic messages


def print_top_useful_diagnostic_messages(metrics, k=10):
    sorted_messages = sorted(
        metrics["helpful_messages"].items(), key=lambda x: x[1], reverse=True
    )
    for message, count in sorted_messages[:k]:
        print(f"{message}: {count}")


print_top_useful_diagnostic_messages(metrics)
#%%
# Print total number of helpful messages
print(f"Total number of helpful messages: {metrics['total_helpful_messages']}")
#%%
print(f"Total number of diagnostics: {metrics['num_samples_with_diagnostic']}")
#%%
