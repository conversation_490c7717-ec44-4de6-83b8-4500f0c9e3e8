# Import necessary libraries and modules
from pathlib import Path
from tqdm import tqdm
from research.models.fastforward_models import StarCoder2_FastForward
from base.fastforward.fwd_utils import get_checkpoint_sha
import pickle
from base.prompt_format_next_edit.gen_prompt_formatter import EditGenPromptInput
from base.prompt_format_next_edit.gen_prompt_formatter import EditGen<PERSON>rompt<PERSON>ormatter
from research.eval.harness.tasks.next_edit_hindsight_location_eval_task import (
        NextEditHindsightTaskInput,
    )
from research.core.next_edit_location_prompt_input import FileLocation
from research.core.types import Scored
from base.diff_utils.diff_utils import File
from base.retrieval.chunking.line_based_chunking import LineChunkContents
from base.diff_utils.changes import Changed
from collections.abc import Sequence
import torch
from base.prompt_format_next_edit.gen_prompt_formatter import EditGenFormatterConfig
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
import argparse
from base.retrieval.chunking.smart_chunking import <PERSON><PERSON><PERSON><PERSON>
from research.eval.harness.tasks.next_edit_hindsight_location_eval_task import (
    NextEditHindsightLocationDiagnosticsOutput,
)

# Define paths for model checkpoints
AUGMENT_CHECKPOINTS_ROOT = Path("/mnt/efs/augment/checkpoints")
RAVEN_V6_CKPT = (
    AUGMENT_CHECKPOINTS_ROOT
    / "next-edit-gen"
    / "S28_wip0.6_small1000-R4_ethanol-P21_star2_seq12k_pause500_out600-pr_grouped_10k-starcoder2_15b-ffw"
)

def load_edit_gen():
    """
    Load and initialize the StarCoder2 FastForward model from checkpoint.
    Returns: Initialized model ready for inference
    """
    edit_model = StarCoder2_FastForward(
        model_path=RAVEN_V6_CKPT,
        checkpoint_path=RAVEN_V6_CKPT,
        checkpoint_sha256=get_checkpoint_sha(RAVEN_V6_CKPT),
    )
    edit_model.load()
    return edit_model

def get_edit_model_scores(
    current_file: File,
    chunks: list[LineChunkContents],
    recently_edited_files: Sequence[Changed[File]],
    edit_model: StarCoder2_FastForward,
    edit_gen_prompt_formatter: EditGenPromptFormatter,
) -> list[float]:
    """
    Calculate edit likelihood scores for each chunk in the file.
    
    Args:
        current_file: The file being analyzed
        chunks: List of code chunks to score
        recently_edited_files: Sequence of recently modified files
        edit_model: The loaded StarCoder2 model
        edit_gen_prompt_formatter: Formatter for model inputs
    
    Returns:
        List of log-probability scores for each chunk
    """
    scores = []
    for chunk in chunks:
        # Create input for the model
        edit_model_input = EditGenPromptInput(
            current_file=current_file,
            edit_region=chunk.crange(),
            instruction="",
            recent_changes=tuple(recently_edited_files),
            retrieval_chunks=[],
        )
        # Format input and get model predictions
        tokens = edit_gen_prompt_formatter.format_input_prompt(edit_model_input).tokens
        logits = edit_model.forward_pass_single_logits(torch.tensor(tokens))
        # Extract probability score for the "has_change" token
        score = (
            torch.log_softmax(logits[-1], dim=-1)[
                edit_gen_prompt_formatter.tokenizer.special_tokens.has_change
            ]
            .cpu()
            .tolist()
        )
        scores.append(score)
    return scores


def load_task_inputs(path: Path) -> list[NextEditHindsightTaskInput]:
    """Load evaluation task inputs from a pickle file"""
    with open(path, "rb") as f:
        return pickle.load(f)


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_path",
        type=Path,
        required=True,
        help="Path to input _task_inputs file.",
    )
    parser.add_argument(
        "--top_k", type=int, required=False, help="Top k chunks to consider.", default=32,
    )
    parser.add_argument(
        "--output_path", type=Path, required=True, help="Path to output directory."
    )
    args = parser.parse_args()

    # Initialize model and required components
    input_path = Path(args.input_path)
    output_dir = Path(args.output_path).expanduser()
    output_dir.mkdir(parents=True, exist_ok=True)
    output_path = Path(output_dir, "output.pkl")
    edit_model = load_edit_gen()
    task_inputs = load_task_inputs(input_path)

    sc2_tokenizer = StarCoder2Tokenizer()
    top_k = args.top_k
    
    # Configure the edit generation prompt formatter
    edit_gen_prompt_formatter = EditGenPromptFormatter(
        sc2_tokenizer,
        config=EditGenFormatterConfig(
            diff_context_lines=9,
            max_prompt_tokens=10200,
            section_budgets={
                "suffix_tks": 1200,
                "prefix_tks": 2800,
                "diff_tks": 3700,
                "filename_tks": 100,
                "instruction_tks": 200,
                "retrieval_tks": 2000,
            },
        ),
    )

    # Initialize chunker for splitting code into segments
    chunker = SmartChunker(max_chunk_chars=2000)
    output: list[NextEditHindsightLocationDiagnosticsOutput] = []
    metrics = []

    total_chunks = []
    
    # Process each task input
    for task_input in tqdm(task_inputs):
        # Split file into chunks
        chunks = chunker.split_chunks(task_input.prompt.current_file.contents, None)
        
        total_chunks.append(len(chunks))
        # Get edit likelihood scores for each chunk
        scores = get_edit_model_scores(
            task_input.prompt.current_file,
            chunks,
            task_input.prompt.recent_changes,
            edit_model,
            edit_gen_prompt_formatter,
        )

        # Create scored locations from chunks and their scores
        scored_Locations: list[Scored[FileLocation]] = []
        for score, chunk in zip(scores, chunks):
            scored_Locations.append(
                Scored(
                    FileLocation(
                        path=task_input.prompt.current_file.path,
                        range=chunk.lrange(),
                    ),
                    score,
                )
            )
        
        # Get top-k locations based on scores
        top_k_locations = sorted(scored_Locations, key=lambda x: x.score, reverse=True)[:top_k]

        # Create and store output for evaluations
        output.append(
            NextEditHindsightLocationDiagnosticsOutput(
                ground_truth=task_input.ground_truth,
                scored_candidates=top_k_locations,
                cursor_path=task_input.prompt.current_file.path,
                diagnostics=task_input.prompt.diagnostics,
                debug_info={},
            )
        )
    
    print(f"Avg chunks per file: {sum(total_chunks)/2000}")
    # Save results to output file
    with open(output_path, "wb") as f:
        pickle.dump(output, f)
        
