#%%

#%%
import matplotlib.pyplot as plt
import numpy as np

# Data for StarCoderBase (no retreival)'
x_a = np.array([1, 3, 7, 16])
passed_a = np.array([162, 311, 404, 436])
sum_a = np.array([1053, 1053, 1053, 1053])
pass_rate_a = (passed_a / sum_a) * 100

# Data for StarCoderBase with contrieve350m retrieval
x_b = np.array([1, 3, 7, 16])
passed_b = np.array([260, 369, 420, 456])
sum_b = np.array([1053, 1053, 1053, 1053])
pass_rate_b = (passed_b / sum_b) * 100

# Data for Rogue (no retrieval)
x_c = np.array([1,7,16])
passed_c = np.array([417, 587, 596])
sum_c = np.array([1053, 1053, 1053])
pass_rate_c= (passed_c / sum_c) * 100

# Data for Rogue with contrieve350m retrieval
x_d = np.array([1,7,16])
passed_d = np.array([615, 770, 752])
sum_d = np.array([1053, 1053, 1053])
pass_rate_d = (passed_d / sum_d) * 100

# Create the main plot
fig, ax1 = plt.subplots(figsize=(10, 6))

# Plotting the data
ax1.plot(x_a, pass_rate_a, label='StarCoderBase (no retreival)', marker='o')
ax1.plot(x_b, pass_rate_b, label='StarCoderBase with contrieve350m retrieval', marker='x')
ax1.plot(x_c, pass_rate_c, label='Rogue (no retrieval)', marker='o')
ax1.plot(x_d, pass_rate_d, label='Rogue (with contrieve350m retrieval)', marker='x')
ax1.set_xlabel('Model size')
ax1.set_ylabel('Pass Rate (%)')
ax1.tick_params('y', colors='b')
ax1.yaxis.label.set_color('b')
ax1.set_yticks(np.linspace(0, 100, 21))
ax1.set_xticks(x_a)
ax1.set_ylim(0, 100)

plt.legend()

# Create a second y-axis
ax2 = ax1.twinx()

# Plotting the number of passes
#ax2.plot(x_a - 0.5, passed_a,  alpha=0.5)
#ax2.plot(x_b, passed_b, alpha=0.5)

# Customize the second y-axis
ax2.set_ylabel('Number of Passes', color='g')
ax2.tick_params('y', colors='g')
ax2.set_yticks(np.linspace(0, 1053, 20))
ax2.set_ylim(0, 1053)

ax2.yaxis.label.set_color('g')


plt.legend()

plt.title('Hydra performance on all_languages_2-3lines_medium_to_hard.dev with and without retrieval.')

plt.show()

#%%
import matplotlib.pyplot as plt
import numpy as np

data = dict(reversed(list({
    'StarCoderBase (no retreival)': {
        "model_sizes": [1, 3, 7, 16],
        "pass_rates": [149, 270, 353, 393]
    },

    'StarCoderBase, (bm25)': {
        "model_sizes": [1, 3, 7, 16],
        "pass_rates": [250, 365, 402, 419]
    },
    'StarCoderBase, (contrieve350m)': {
        "model_sizes": [1, 3, 7, 16],
        "pass_rates": [243, 349, 386, 401]
    },
    'StarCoderBase, (diff_boykin)': {
        "model_sizes": [1, 3, 7, 16],
        "pass_rates": [243, 339, 393,409]
    },

    'StarCoderBase, (no retrieval + trimming heuristics)': {
        "model_sizes": [1,3,7,16],
        "pass_rates": [304,386,441,467]
    },

    'Rogue (no retreival)': {
        "model_sizes": [1, 7, 16],
        "pass_rates": [366, 514, 512]
    },

    'StarCoderBase, (diff_boykin + trimming heuristics)': {
        "model_sizes": [1,3,7,16],
        "pass_rates": [490,540,575,589]
    },

    'Rogue (bm25)': {
        "model_sizes": [1, 7, 16],
        "pass_rates": [570, 683, 696]
    },
    'Rogue (contrieve350m)': {
        "model_sizes": [1, 7, 16],
        "pass_rates": [555, 687, 685]
    },
    'Rogue (diff_boykin)': {
        "model_sizes": [1, 7, 16],
        "pass_rates": [562,676, 688]
    },
}.items())))

# Create the main plot
fig, ax1 = plt.subplots(figsize=(10, 6))

# Plotting the data
for name, item in data.items():
    sum_d = np.array([894] * len(item["model_sizes"]))
    pass_rates = (item["pass_rates"] / sum_d) * 100
    ax1.plot(item["model_sizes"], pass_rates, label=name, marker='o')

ax1.set_xlabel('Model size')
ax1.set_ylabel('Pass Rate (%)')
ax1.tick_params('y', colors='b')
ax1.yaxis.label.set_color('b')
ax1.set_yticks(np.linspace(0, 100, 21))
ax1.set_xticks([1, 3, 7, 16])
ax1.set_ylim(0, 100)

plt.legend()

# Create a second y-axis
ax2 = ax1.twinx()

# Plotting the number of passes
#ax2.plot(x_a - 0.5, passed_a,  alpha=0.5)
#ax2.plot(x_b, passed_b, alpha=0.5)

# Customize the second y-axis
ax2.set_ylabel('Number of Passes', color='g')
ax2.tick_params('y', colors='g')
ax2.set_yticks(np.linspace(0, 894, 20))
ax2.set_ylim(0, 894)

ax2.yaxis.label.set_color('g')


plt.legend()

plt.title('Hydra performance on all_languages_2-3lines_medium_to_hard.dev with and without retrieval.')

plt.show()

#%%
import matplotlib.pyplot as plt
import numpy as np

data = dict(reversed(list({
    #'StarCoderBase (no retreival)': {
    #    "model_sizes": [1, 3, 7, 16],
    #    "pass_rates": [224,354,457,503]
    #},
    #'StarCoderBase, (bm25)': {
    #    "model_sizes": [1, 3, 7, 16],
    #    "pass_rates": [350,451,499,514]
    #},
    #'StarCoderBase, (contrieve350m)': {
    #    "model_sizes": [1, 3, 7, 16],
    #    "pass_rates": [321,427,485,516]
    #},
    #'StarCoderBase, (diff_boykin)': {
    #    "model_sizes": [1, 3, 7, 16],
    #    "pass_rates": [322,431,487,524]
    #},

    'StarCoderBase (no retreival + trimming heuristics)': {
        "model_sizes": [1, 3, 7, 16],
        "pass_rates": [130,285,361,404]
    },
    'StarCoderBase, (diff_boykin + trimming heuristics)': {
        "model_sizes": [1,3,7,16],
        "pass_rates": [391,464,524,560]
    },


#    'Rogue (no retreival)': {
#        "model_sizes": [1, 7, 16],
#        "pass_rates": [380,608,615]
#    },
#    'Rogue (bm25)': {
#        "model_sizes": [1, 7, 16],
#        "pass_rates": [659,832,855]
#    },
#    'Rogue (contrieve350m)': {
#        "model_sizes": [1, 7, 16],
#        "pass_rates": [627,833,844]
#    },
    'Rogue (diff_boykin)': {
        "model_sizes": [1, 7, 16],
        "pass_rates": [639,807,841]
    },
}.items())))

num_patches = 1226

# Create the main plot
fig, ax1 = plt.subplots(figsize=(10, 7))

# Plotting the data
for name, item in data.items():
    sum_d = np.array([num_patches] * len(item["model_sizes"]))
    pass_rates = (item["pass_rates"] / sum_d) * 100
    ax1.plot(item["model_sizes"], pass_rates, label=name, marker='o')

ax1.set_xlabel('Model size')
ax1.set_ylabel('Pass Rate (%)')
ax1.tick_params('y', colors='b')
ax1.yaxis.label.set_color('b')
ax1.set_yticks(np.linspace(0, 100, 21))
ax1.set_xticks([1, 3, 7, 16])
ax1.set_ylim(0, 100)

# Place the legend below the plot
plt.legend(loc='upper center', bbox_to_anchor=(0.5, -0.2), ncol=2)

# Adjust layout to make room for the legend
plt.tight_layout()

# Create a second y-axis
ax2 = ax1.twinx()

# Plotting the number of passes
#ax2.plot(x_a - 0.5, passed_a,  alpha=0.5)
#ax2.plot(x_b, passed_b, alpha=0.5)

# Customize the second y-axis
ax2.set_ylabel('Number of Passes', color='g')
ax2.tick_params('y', colors='g')
ax2.set_yticks(np.linspace(0, num_patches, 20))
ax2.set_ylim(0, num_patches)

ax2.yaxis.label.set_color('g')


plt.legend()

plt.title('Hydra performance on all_languages_2-3lines_medium_to_hard.dev with and without retrieval.')

plt.show()
