#%%
from pathlib import Path
import json
import pickle
from pprint import pprint
from research.eval import patch_lib
from research.static_analysis.parsing import LineMap
from collections import Counter
from megatron.tokenizer.tokenizer import CodeGenTokenizer
import pandas as pd
import random
import csv
import numpy as np

tokenizer = CodeGenTokenizer()

#%%
base_path = Path('/mnt/efs/augment/user/colin/scratch/')
# project_name = 'evaluate_retrievers_2023_07_05'
# project_name = 'evaluate_retrievers_2023_07_05_no_retriever'
# project_name = 'evaluate_retrievers_2023_07_06'
# project_name = 'evaluate_retrievers_2023_07_06_attempt3'
# project_name = 'evaluate_indiana_retrievers_2023_07_07'
project_name = 'evaluate_indiana_retrievers_2023_07_08_1024localctx_linelevelchunker'
model_name = 'indiana_16B_retrieve10'
#exp_names = [
#    #'bm25', 
#    #'dense_retriever_diffs', 
#    'dense_retriever_samefile',
#    #'no_retriever',
#]
exp_name = 'dense_retriever_samefile'
ret_tok = tokenizer.vocab['<|ret-endofdoc|>']

print('-' * 80)
print(f'EXP NAME: {exp_name}')

hydra_out_noretrieval_fp = base_path / 'evaluate_indiana_retrievers_2023_07_07' / 'no_retriever' / f'{model_name}_no_retriever.jsonl'
hydra_out_fp = base_path / project_name / exp_name / f'{model_name}_{exp_name}.jsonl'
completed_patches_fp = base_path / project_name / exp_name / f'{model_name}_{exp_name}_completed_patches.pkl'

with open(hydra_out_fp, 'r') as f:
    hydra_out = [json.loads(line) for line in f.readlines()]

with open(hydra_out_noretrieval_fp, 'r') as f:
    hydra_out_noretrieval = [json.loads(line) for line in f.readlines()]

with open(completed_patches_fp, 'rb') as f:
    completed_patches = pickle.load(f)

all_patches =  [patch for patches in completed_patches.values() for patch in patches]

#%%
random.seed(10)
shuffled_patches = sorted(all_patches, key=lambda k: random.random())

column_names = tuple('prompt_id, repo_name, has_passed (with retrieval), has_passed (no retrieval), local_context_prompt + generation,  retrieval_prompt, generation'.split(', '))
patch_csvs = [column_names]

for i in range(50):
    patch = patch_lib.Patch.from_json(shuffled_patches[i]['patch'])
    repo_name = patch.repository
    prompt_id = patch.patch_id
    ground_truth = patch.original_patch_content
    generation = patch.patch_content

    prompt = tokenizer.detokenize(shuffled_patches[i]['prompt_toks'])
    local_context_prompt = prompt[prompt.rindex('<|ret-endofdoc|>'):] + '# start ground truth\n' + ground_truth + '# end ground truth\n' 
    retrieval_prompt = prompt[:prompt.rindex('<|ret-endofdoc|>')]
    
    has_passed = [item['_extra']['result'] for item in hydra_out if item['patch_id'] == prompt_id]
    has_passed_noretrieval = [item['_extra']['result'] for item in hydra_out_noretrieval if item['patch_id'] == prompt_id]

    patch_csvs.append((prompt_id, repo_name, has_passed, has_passed_noretrieval, local_context_prompt, retrieval_prompt, generation))


with open('/mnt/efs/augment/user/colin/scratch/50_hydra_generations.csv','w') as out:
    csv_out=csv.writer(out, delimiter=',')
    for row in patch_csvs:
        csv_out.writerow(row)
#%%
with open('/mnt/efs/augment/user/colin/scratch/analyze_hydra_patches_annotations_first25annotations.json') as f:
    annotations = json.load(f)

annotations.keys()
#%%
from collections import defaultdict
example_to_metadata = {example["example_id"]: {} for example in annotations['examples']}

print(f'Num examples: {len(annotations["examples"])}')
num_annotated_examples = 0

tag_to_count_rows = []

for example in annotations['examples']:
    example_to_metadata[example["example_id"]]['passed_with_retrieval'] = example['metadata']["has_passed (no retrieval)"]
    
    tag_to_values = defaultdict(set)
    for annotation in example['annotations']:
        tag = annotation['tag']
        annotated_text = annotation['value']
        tag_to_values[tag].add(annotated_text)

    tag_to_count = {tag: len(values) for tag, values in tag_to_values.items()}
    if len(tag_to_count) > 0:
        num_annotated_examples += 1
        tag_to_count_rows.append({"example_id": example["example_id"], **tag_to_count})


print(f'Num annotated examples: {num_annotated_examples}')

annotation_count_df = pd.DataFrame(tag_to_count_rows).replace(np. nan,0)

annotation_count_df.describe()

#%%
### Number of patches with truncated local context

sum(annotation_count_df['local_entity_truncated'] > 0) / annotation_count_df['local_entity_truncated'].shape[0]