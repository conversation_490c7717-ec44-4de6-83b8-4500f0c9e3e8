#%%
import glob
import pandas as pd
from git import Repo
import time
from pygit2 import Repository
import os
import sys
from typing import Optional
import chardet
from difflib import ndiff, unified_diff
from pathlib import Path

#%%
LANGUAGES = [
    "C",
    "C++",
    "Go",
    "Java",
    "Javascript",
    "Python"
]

LANGUAGE_TO_EXTENSION = {
    "C++": [".c", ".cpp", ".h"],
    "Go": [".go"],
    "Java": [".java", ".jav"],
    "Javascript": [".js"],
    "Python": [".py"],
}

BAD_EXTENSIONS = [
    ".app",
    ".bin",
    ".bmp",
    ".bz2",
    ".class",
    ".csv",
    ".dat",
    ".db",
    ".dll",
    ".dylib",
    ".egg",
    ".eot",
    ".exe",
    ".gif",
    ".gitignore",
    ".glif",
    ".gradle",
    ".gz",
    ".ico",
    ".jar",
    ".jpeg",
    ".jpg",
    ".lo",
    ".lock",
    ".log",
    ".tlog",
    ".mp3",
    ".mp4",
    ".nar",
    ".nes",
    ".o",
    ".ogg",
    ".otf",
    ".p",
    ".pdf",
    ".png",
    ".pickle",
    ".pkl",
    ".pyc",
    ".pyd",
    ".pyo",
    ".rkt",
    ".ser",
    ".so",
    ".ss",
    ".svg",
    ".tar",
    ".tsv",
    ".ttf",
    ".umap",
    ".war",
    ".webm",
    ".woff",
    ".woff2",
    ".xcuserstate",
    ".xz",
    ".zip",
    ".zst",
]
#%%
def _decode_byte_string(buf) -> Optional[str]:
    """Return the contents as a string.

    The contents is expected to contain text encoded in utf-8, but other encodings
    may also work if they can be detected automatically. Note, this
    determination may have false negatives, meaning that the actual contents encoding
    could not accurately be determined.

    Args:
        buf: byte string

    Returns:
        Decoded string upon success, None on failure.
    """
    # discerns filetype with mime and reads text from file if possible
    try:
        buf = buf.decode("UTF-8")
        return buf
    except UnicodeDecodeError:
        # bad encoding, try different encoding
        try:
            enc = chardet.detect(buf)
            if enc["encoding"] is None:
                return
            buf = buf.decode(enc["encoding"])
            return buf
        except UnicodeDecodeError:
            return
    except KeyboardInterrupt:
        sys.exit()

def fast_ndiff(prev_lines, new_lines):
    """unified_diff is way faster than ndiff because ignores but it grabs local diffs (eg changes with nearby context)
    rather than file-level diffs. By seeing the context size to 1e9
    """
    diff = list(unified_diff(prev_lines, new_lines, n=1e9))
    assert (
        diff[0].startswith('---')
        and diff[1].startswith('+++')
        and diff[2].startswith('@@')
    ), diff[:5]
    diff = diff[3:]
    return diff

def guess_lang_from_fp(fname):
    fname_extension = os.path.splitext(fname)[1].lower()

    for lang, valid_extensions in LANGUAGE_TO_EXTENSION.items():
        if fname_extension in valid_extensions:
            return lang

    return None

def _should_ignore_file(fname):
    """Determines what kinds of files we don't need to track (e.g. non-code files).

    Args:
        fname: Base filename

    Returns:
        Boolean that is True iff the file should not be tracked.
    """
    if (
        ".git" in fname
        or fname[0] == "."
        or "LICENSE" in fname
        or "node_modules" in fname
        or ".min." in fname
        or os.path.splitext(fname)[1].lower() in BAD_EXTENSIONS
    ):
        return True
    else:
        return False
#%% md
## /mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v3/logs/logs-*.log
#%%
###
# Compute time to process each repo (including deletion time)
###

log_files = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v3/logs/logs-*.log')
counts = []
for log_file in log_files:
    with open(log_file) as f:
        for line in f.readlines():
            if 'Repo processed' not in line:
                continue
            fp = line.split('Repo processed: ', 1)[1].split(' ',1)[0][:-1]
            count = line.split('chunks in ', 1)[1].split(' ', 1)[0]
            counts.append({'fp': fp, 'processs_time': float(count)})

pd.DataFrame(counts).describe()

#%%
df = pd.DataFrame(counts)
#print(df)

for i in range(10):
    print(df.loc[df['processs_time'] > 25].iloc[i]['fp'])
#%%
###
# Look at mean num_commits_processed
###
paths = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v3/metadata*.parquet')
paths = list(paths)
df = pd.read_parquet(paths[0])
for path in paths[1:]:
    df = pd.concat((df, pd.read_parquet(path)))

df['num_commits_processed'].describe()
#%%
###
# Compute num computes timed out
###
log_paths = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v3/logs/logs-*.log')
processed_repos = 0
timed_out = 0
for log_file in log_paths:
    with open(log_file) as f:
        for line in f.readlines():
            if 'Processing repo' in line:
                processed_repos += 1
            elif 'Timed out processing' in line:
                timed_out += 1

print(timed_out/processed_repos)
#%% md
## /mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v4_morelogs
#%%
###
# Commit level stats
###
fps = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v4_morelogs/commit_metadata_shard*.parquet')
df = pd.read_parquet(fps[0])
for fp in fps[1:]:
    df = pd.concat((df, pd.read_parquet(fp)))

# fractions of time
df['tokenizer_duration/commit_process_time'] = df['tokenizer_duration']/df['commit_process_time']
df['get_chunks_duration/commit_process_time'] = df['get_chunks_duration']/df['commit_process_time']
df['diffing_duration/commit_process_time'] = df['diffing_duration']/df['commit_process_time']
df['decoding_bytes_duration/commit_process_time'] = df['decoding_bytes_duration']/df['commit_process_time']
df['ndiff_duration/commit_process_time'] = df['ndiff_duration']/df['commit_process_time']

# tokens saved
df['tokens_saved/tokens_tokenized'] = df['tokens_saved']/df['tokens_tokenized']

print(df.describe())
#%%
###
# Compute repo-level statistics
###
fps = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v4_morelogs/repo_metadata_shard*.parquet')
df = pd.read_parquet(fps[0])
for fp in fps[1:]:
    df = pd.concat((df, pd.read_parquet(fp)))

df['leftover_commits'] = df['total_commits'] - df['num_commits_processed']

print(df.describe())
#%% md
## /mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v5_fastndiff
#%%
###
# Commit level stats for new fast ndiffing
###
fps = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v5_fastndiff/commit_metadata_shard*.parquet')
df = pd.read_parquet(fps[0])
for fp in fps[1:]:
    df = pd.concat((df, pd.read_parquet(fp)))

# fractions of time
df['tokenizer_duration/commit_process_time'] = df['tokenizer_duration']/df['commit_process_time']
df['get_chunks_duration/commit_process_time'] = df['get_chunks_duration']/df['commit_process_time']
df['diffing_duration/commit_process_time'] = df['diffing_duration']/df['commit_process_time']
df['decoding_bytes_duration/commit_process_time'] = df['decoding_bytes_duration']/df['commit_process_time']
df['ndiff_duration/commit_process_time'] = df['ndiff_duration']/df['commit_process_time']

# tokens saved
df['tokens_saved/tokens_tokenized'] = df['tokens_saved']/df['tokens_tokenized']

print(df.describe())
#%% md
### /mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v6_pygit2_60sec_timeout/
#%%
###
# Commit level stats for new fast diffing
###
fps = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v6_pygit2_60sec_timeout/commit_metadata_shard*.parquet')
df = pd.read_parquet(fps[0])
for fp in fps[1:]:
    df = pd.concat((df, pd.read_parquet(fp)))

# fractions of time
df['tokenizer_duration/commit_process_time'] = df['tokenizer_duration']/df['commit_process_time']
df['get_chunks_duration/commit_process_time'] = df['get_chunks_duration']/df['commit_process_time']
df['diffing_duration/commit_process_time'] = df['diffing_duration']/df['commit_process_time']
#df['decoding_bytes_duration/commit_process_time'] = df['decoding_bytes_duration']/df['commit_process_time']
#df['ndiff_duration/commit_process_time'] = df['ndiff_duration']/df['commit_process_time']

# tokens saved
df['tokens_saved/tokens_tokenized'] = df['tokens_saved']/df['tokens_tokenized']

print(df.describe())
#%% md
### Benchmarking GitPython (git) vs PyGit2
#%%
### GitPython, process newest to oldest

repo = Repo('/home/<USER>/augment')
times = []

for i in range(500):
    #if i % 50 == 0:
    #    print(f'Processed HEAD through HEAD~{i-1}')
    #    print(pd.Series(times).describe())
        #times = []
    diff_start = time.time()
    prior_commit = repo.commit(f"HEAD~{i+1}")
    diffs_objs = prior_commit.diff(f"HEAD~{i}")
    num_additions = 0
    skipped = 0
    num_modifications = 0
    try:
        for diff_obj in diffs_objs.iter_change_type("D"):
            continue
        for diff_obj in diffs_objs.iter_change_type("R"):
            continue
        for diff_obj in diffs_objs.iter_change_type("A"):
            num_additions += 1
            current_file_path = diff_obj.b_path

            fname = Path(current_file_path).name
            #file_language = guess.language_name(current_file_contents)
            file_language = guess_lang_from_fp(fname)
            if file_language is None:
                continue
            if _should_ignore_file(fname):
                continue


            current_file_contents = _decode_byte_string(
                diff_obj.b_blob.data_stream.read()
            )
            
            prior_file_contents = []
            if not prior_file_contents or not current_file_contents:
                skipped += 1
                continue
            current_file_contents = current_file_contents.splitlines(1)
            raw_difflines = [line for line in fast_ndiff(prior_file_contents, current_file_contents) if not line.startswith('-') and not line.startswith('?')]
        for diff_obj in diffs_objs.iter_change_type("M"):
            num_modifications += 1
            current_file_path = diff_obj.b_path
            current_file_contents = _decode_byte_string(
                diff_obj.b_blob.data_stream.read()
            )
            prior_file_contents = _decode_byte_string(
                diff_obj.a_blob.data_stream.read()
            )
            if not prior_file_contents or not current_file_contents:
                skipped += 1
                continue
            prior_file_contents = prior_file_contents.splitlines(1)
            current_file_contents = current_file_contents.splitlines(1)
            raw_difflines = [line for line in fast_ndiff(prior_file_contents, current_file_contents) if not line.startswith('-') and not line.startswith('?')]
    except Exception as e:
        continue
    #print(f'num_additions: {num_additions}, num_modifications: {num_modifications}, skipped: {skipped}')

    diff_end = time.time()
    times.append(diff_end - diff_start)

pd.Series(times).describe()
#%%
### pygit2, actually reading patches

repo = Repository('/home/<USER>/augment')

times = []

for i in range(500):
    #if i % 50 == 0:
    #    print(f'Processed HEAD through HEAD~{i-1}')
    #    print(pd.Series(times).describe())
        #times = []
    diff_start = time.time()
    diffs_objs = repo.diff(f"HEAD~{i+1}", f"HEAD~{i}", context_lines=int(1e9))
    for j, patch in enumerate(list(diffs_objs)):
        if i == 0 and j == 0:
                #print(patch.text)
                patch.text
    diff_end = time.time()
    times.append(diff_end - diff_start)

pd.Series(times).describe()

#%% md
### Messy scratchpad from exploring pygit2 after this: venture at your own risk!
#%%
# Links:
# - https://www.pygit2.org/diff.html#pygit2.Repository.diff


#diff = next(diffs_objs.deltas)
print(diffs_objs.stats.files_changed, diffs_objs.stats.insertions, diffs_objs.stats.deletions)

#%%
print(diff.status_char())

print(diffs_objs.patch)
#%%
next(diffs_objs.deltas).new_file.path
#%%
repo = Repository('/mnt/efs/augment-lga1-nvme/pytest/test_repo')
diffs_objs = repo.diff('HEAD~1', 'HEAD', context_lines=int(1e9))
#print(len(diffs_objs), 'num patches')
for patch in diffs_objs:
    print(patch.delta.status_char())
    print(patch.text.splitlines(1)[:10])

diffs_objs = repo.diff('HEAD~2', 'HEAD~1', context_lines=int(1e9))
#print(len(diffs_objs), 'num patches')
for patch in diffs_objs:
    print(patch.delta.status_char())
    for line in patch.text.splitlines(1)[:10]:
        print(line.rstrip('\n'))

    
#%%
dir(patch.delta.new_file)
#%%
for i in range(10):
    d = repo.revparse_single(f'HEAD~{i}')
    print(d)
#%%
d = repo.revparse_single(f'HEAD~{0}')

