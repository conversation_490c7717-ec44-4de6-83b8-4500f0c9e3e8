#%%
run_config = [
    ("multiline_retrieval_old_ckpt", "/mnt/efs/augment/eval/jobs/EpyXPYcm"),
    ("multiline_retrieval_new_ckpt", "/mnt/efs/augment/eval/jobs/eQ7Z3YHV"),
]
#%%
import difflib
import html
import json
import pandas as pd
import pickle

from research.eval.harness.utils import read_jsonl_zst
from IPython.core.display import display, HTML
from collections import defaultdict
from pathlib import Path
from types import MethodType

def read_artifacts(run_path):
    matching_files = list(Path(run_path).glob("*_hydra.jsonl"))
    if len(matching_files) != 1:
        raise ValueError(
            f"Expected 1 Hydra jsonl file under {run_path}, found {len(matching_files)}"
        )

    hydra_results_path = matching_files[0]
    hydra_patches_path = (
        hydra_results_path.parent / (hydra_results_path.stem + "_completed_patches.jsonl.zst")
    )

    with hydra_results_path.open("r") as f:
        hydra_result_records = [json.loads(x) for x in f]
    hydra_patch_records = read_jsonl_zst(hydra_patches_path)

    if len(hydra_patch_records) != len(hydra_result_records):
        raise ValueError("Inconsistent records!")
    
    return hydra_patch_records, hydra_result_records

def unpack_hydra_patch_results(a):
    run_metadata = {
        "prompt": a["prompt"],
        "generation": a["generation"],
        "completion": a["completion"],
        "filename": a["patch"]["file_name"],
        "file_content": a["patch"]["file_content"],
    }

    if "retrieval_metadata" in a and "retriever_prompt" in a["retrieval_metadata"]:
        run_metadata["retriever_prompt"] = a["retrieval_metadata"]["retriever_prompt"]
    elif "retrieval" in a and "retriever_prompt" in a["retrieval"]:
        run_metadata["retriever_prompt"] = a["retrieval"]["retriever_prompt"]
    elif "artifacts" in a:
        run_metadata["retriever_prompt"] = a["artifacts"][0]["retriever_prompt"]
    else:
        run_metadata["retriever_prompt"] = ""
    return run_metadata

def load(run_path):
    api_task_path = Path(run_path) / "000__RAGSystem_ApiCallTask"
    if api_task_path.exists():
        return load_api_task(api_task_path)

    hydra_patch_records, hydra_result_records = read_artifacts(run_path)
    if len(hydra_patch_records) != len(hydra_result_records):
        raise ValueError("Inconsistent records!")

    metadata = {}
    for a, b in zip(hydra_patch_records, hydra_result_records):
        assert a["completion"] == b["patch_content"]

        metadata[b["patch_id"]] = unpack_hydra_patch_results(a) | {
            "ground_truth": b["file_content"][b["char_start"]: b["char_end"]],
            "result": b["_extra"]["result"],
            "run_output": b["_extra"]["run_output"],
        }
    return metadata

def load_api_task(api_task_path):
    hydra_patch_records = read_jsonl_zst(api_task_path / "000_RAGSystem_ApiCallTaskcompleted_patches.jsonl.zst")

    api_result_files = sorted(e for e in (api_task_path / "cache").glob("*.json"))
    assert len(api_result_files) == len(hydra_patch_records)

    metadata = {}
    for hydra_patch_rec, api_result_file in zip(hydra_patch_records, api_result_files):
        with open(api_result_file) as fh:
            api_result_rec = json.load(fh)

        patch_id = hydra_patch_rec['patch']['patch_id']
        result_val = (
            ("metric@hydra-unit-test-pass" in api_result_rec) and api_result_rec["metric@hydra-unit-test-pass"]
        )
        metadata[patch_id] = unpack_hydra_patch_results(hydra_patch_rec) | {
            "ground_truth": api_result_rec["inputs"]["target"],
            "result": "PASSED" if result_val else "FAILED",
            "run_output": "<not available>",
        }
    return metadata


def diff_runs(results, patch_id):
    f"diff_runs(results[0]['name'], results[1]['name'])"
    run1 = results[0]['data'][patch_id]
    run2 = results[1]['data'][patch_id]
    run_names = [res_item['name'] for res_item in results]
    diff_obj = difflib.HtmlDiff()
    diff_obj._legend = ""

    markers = [
        '+' if res == "PASSED"
        else (
            '-' if res == "FAILED" else '^'
        )
        for res in (run1["result"], run2["result"])
    ]

    # Adjust colors
    orig_convert_flags = diff_obj._convert_flags
    def _convert_flags(self,fromlist,tolist,flaglist,context,numlines):
        def _swap_parity(items):
            return [
                item.replace('\0+', '\0*').replace('\0-', f'\0{markers[0]}').replace('\0*', f'\0{markers[1]}')
                for item in items
            ]
        fromlist = _swap_parity(fromlist)
        tolist = _swap_parity(tolist)
        return orig_convert_flags(fromlist, tolist, flaglist, context, numlines)

    diff_obj._convert_flags = MethodType(_convert_flags, diff_obj)

    display(HTML(f"""
    <style type="text/css">
        {difflib.HtmlDiff()._styles}
        td {{ text-align: left; }}
        :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td {{ text-align: left; }}
    </style>
    """))

    display(HTML(f"<h2>Patch {patch_id}</h2>"))

    display(HTML("<h3>Results</h3>"))
    display(HTML(diff_obj.make_table(
        [run1["result"]],
        [run2["result"]],
        fromdesc=run_names[0],
        todesc=run_names[1],
    )))

    if run1["result"] == run2["result"]:
        # No difference in the two runs, so let's showing the details.
        #return
        pass
    
    display(HTML("<h3>Prompt</h3>"))
    display(HTML(diff_obj.make_table(
        run1['prompt'].splitlines(),
        run2['prompt'].splitlines(),
        fromdesc=run_names[0],
        todesc=run_names[1],
    )))
    display(HTML("<h3>Retriever Prompt</h3>"))
    display(HTML(diff_obj.make_table(
        run1['retriever_prompt'].splitlines(),
        run2['retriever_prompt'].splitlines(),
        fromdesc=run_names[0],
        todesc=run_names[1],
    )))

    display(HTML("<h3>Completion</h3>"))
    display(HTML(diff_obj.make_table(
        run1['completion'].splitlines(),
        run2['completion'].splitlines(),
        fromdesc=run_names[0],
        todesc=run_names[1],
    )))
    
    display(HTML("<h3>Ground Truth</h3>"))
    display(HTML(
        "<table class='diff' summary='Ground Truth' style='border: 1px solid black;'><tr><td>"
        + "</tr></td><tr><td>".join(
            line.replace(" ", "&nbsp;")
            for line in run1['ground_truth'].splitlines()
        )
        + "</td></tr></table>"
    ))
#%% md
# Load results
#%%
results = [
    {
        "data": load(run_path),
        "name": run_name,
    }
    for run_name, run_path in run_config
]
#%% md
## Summarize the Results
#%%
if results[0]['data'].keys() != results[1]['data'].keys():
    print("ERROR: key sets don't match!")

result_stats = {}
for k in reversed(results[0]['data'].keys()):
    kk = tuple(results[i]['data'][k]["result"] for i in range(len(results)))
    if kk not in result_stats:
        result_stats[kk] = 0
    result_stats[kk] += 1

pd.DataFrame([
    {
        results[i]["name"]: item[0][i]
        for i in range(len(results))
    } | {
        "count": item[1]
    }
    for item in result_stats.items()
])
#%% md
## Check for Known Issues
#%%
issue_count = 0

for res in results:
    for k in reversed(res['data'].keys()):
        if "Checkout your internet" in res['data'][k]['run_output']:
            print(f"[{res['name']}, {k}]  HuggingFace error, presumed non-deterministic failure.")
            issue_count += 1
        if (res['data'])[k]['result'] not in ["PASSED", "FAILED"]:
            print(f"[{res['name']}, {k}]  Result other than pass/fail: {res['data'][k]['result']}.")
            issue_count += 1

if issue_count > 0:
    print(f"WARNING: {issue_count} issues found.")
else:
    print("All OK.")
#%% md
# Show Differences Between Runs
Note: Flip the if False guard to generate the cells. You can then restore the guard to avoid accidentally.
#%%
if True:
    def create_new_cell(contents):
        from IPython.core.getipython import get_ipython

        shell = get_ipython()

        payload = dict(
            source="set_next_input",
            text=contents,
            replace=False,
        )
        shell.payload_manager.write_payload(payload, single=False)

    for patch_id in reversed(list(results[0]["data"].keys())):
        passfail = [res_item["data"][patch_id]["result"] for res_item in results]
        if passfail[0] != passfail[1]:
            create_new_cell(
                f"# {patch_id}: {passfail[0]} {passfail[1]}\n"
                f"diff_runs(results, '{patch_id}')"
            )