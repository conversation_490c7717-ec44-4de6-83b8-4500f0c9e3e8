

import json
import logging

import pandas as pd
from experimental.colin.projects.autofix.training.data_pipelines.stages_reranker.stage2_run_auxiliary_retrieval import stage2_run_auxiliary_retrieval
from experimental.colin.projects.autofix.training.data_pipelines.stages_reranker.stage1_generate_problems import stage1_generate_problems
from experimental.colin.projects.autofix.training.data_pipelines.stages_reranker.stage3_formatting import stage3_format_as_tokens
from experimental.colin.projects.autofix.training.data_pipelines.stages_reranker.stage4_export import stage4_export
from experimental.colin.projects.autofix.training.data_pipelines.utils import configure_root_logger, create_cpu_spark, diff_dicts, file_exists, read_file, save_file
from pyspark.sql import functions as F
from base.prompt_format_next_edit.gen_prompt_formatter import (
    EditGenFormatterConfig,
    section_budgets_10k,
)
from research.next_edits.edit_gen_stages import PromptConfig



INPUT_DATA = "gs://gcp-us1-user/colin/bugfix_localization_model/v11/stage5_with_logs"

VERSION = "v2"
TRAINING_DATA_BASE = f"gs://gcp-us1-spark-data/user/colin/autofix_reranker/{VERSION}"
config_output_path = f"{TRAINING_DATA_BASE.rstrip('/')}/config.json"
logs_output_base = f"/mnt/efs/augment/user/colin/autofix_reranker/{VERSION}"
configure_root_logger(logs_output_base)
logger = logging.getLogger(__name__)

pipeline_config = {
    "VERSION": VERSION,
    "TRAINING_DATA_BASE": TRAINING_DATA_BASE,

    # Input data from localization model pipeline
    "INPUT_DATA": INPUT_DATA,
    "STAGE1_GENERATE_PROBLEMS_PATH": f"{TRAINING_DATA_BASE}/stage1_generate_problems",
    "STAGE2_RETRIEVAL_PATH": f"{TRAINING_DATA_BASE}/stage2_retrieval",
    "STAGE3_FORMAT_AS_TOKENS_PATH": f"{TRAINING_DATA_BASE}/stage3_format_as_tokens",
    "STAGE4_INDEXED_DATASET_PATH": f"/mnt/efs/augment/data/processed/autofix/{VERSION}/stage4_indexed_dataset",
    

    "stage_1_config": {
        "max_problems_per_commit": 20,
        "max_problems_per_repo": 16_000,
        "random_edit_region_rate": 0.45,
        "random_target_file_rate": 0.125,
    },

    "stage_2_config": {
        "skip_dense_retrieval": True,
        "timeout_per_repo": 1200,
        "synthetic_instructions_path": None,
        "num_retrieved_chunks": 128,
        "retriever_config": {
            "scorer": {
                "name": "dense_scorer_v2_fbwd",
                "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.rel.S1.2_prs_2k.keepmost.filter.empty10,R1.2_v13-128.30lines,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50",
                "tokenizer_name": "starcoder",
                "cache_dir": "/tmp/augment/cache",
            },
            "chunker": {
                "name": "smart_line_level",
                "max_chunk_chars": 2000,
            },
            "query_formatter": {
                "name": "next_edit_location_query",
                "tokenizer": "starcoder",
                "max_prompt_tokens": 8192,
                "max_instruction_tokens": 4096,
                "use_smart_header": True,
                "deduplicate_identical_paths": True,
                "truncate_instructions_tail": False,
            },
            "document_formatter": {
                "name": "base:ethanol6-embedding-with-path-key",
                "tokenizer": "starcoder",
                "max_tokens": 999,
            },
        },
    },

    "stage_3_config": {
        "prompt_config": {
            "tokenizer_name": "starcoder2",
            "formatter_config": {
                "diff_context_lines": 12,
                "max_prompt_tokens": 14_000,
                "output_instruction": False,
                "section_budgets": {
                    "suffix_tks": 1000,
                    "prefix_tks": 2200,
                    "diff_tks": 4500,
                    "filename_tks": 100,
                    "instruction_tks": 6000,
                    "retrieval_tks": 0,
                }
            },
            "drop_instruction_rate": 0.0,
            "max_output_tokens": 1800,
        },
        "eval_repo_names_path": "/mnt/efs/spark-data/user/colin/bugfix_localization_model/autofix_eval_repo_names_v3.txt",
    },
}

VALID_STAGES = set([1,2,3,4])

def run_pipeline(active_stages: set[int], analytics_only=False):
    assert active_stages.issubset(VALID_STAGES)

    logger.info(f"Active stages: {active_stages}")
    logger.info(f"Pipeline config: {pipeline_config}")
    logger.info(f"Version: {VERSION}")
    logger.info(f"Analytics only: {analytics_only}")

    if not analytics_only:
        if not file_exists(config_output_path):
            save_file(config_output_path, pipeline_config)
        else:
            saved_config = json.loads(read_file(config_output_path))
            assert saved_config == pipeline_config, f"Pipeline config has changed since last run. Please update the config file. Diff: {diff_dicts(saved_config, pipeline_config)}"
            logger.info(f"Pipeline config already exists but is unchanged. Continuing with initialization of pipeline.")
        
    gpu_spark = None
    cpu_spark = None

    if 1 in active_stages:
        if cpu_spark is None:
            cpu_spark = create_cpu_spark()
        stage1_generate_problems(
            cpu_spark,
            input_path=pipeline_config["INPUT_DATA"],
            stage1_path=pipeline_config["STAGE1_GENERATE_PROBLEMS_PATH"],
            logs_output_base=logs_output_base,
            **pipeline_config["stage_1_config"],
            analytics_only=analytics_only,
        )
    if 2 in active_stages:
        if cpu_spark is None:
            cpu_spark = create_cpu_spark()
        stage2_run_auxiliary_retrieval(
            cpu_spark,
            stage1_path=pipeline_config["STAGE1_GENERATE_PROBLEMS_PATH"],
            stage2_path=pipeline_config["STAGE2_RETRIEVAL_PATH"],
            logs_output_base=logs_output_base,
            retrieval_config=pipeline_config["stage_2_config"],
            analytics_only=analytics_only,
        ) 
    if 3 in active_stages:
        if cpu_spark is None:
            cpu_spark = create_cpu_spark()
        stage3_format_as_tokens(
            cpu_spark,
            stage2_path=pipeline_config["STAGE2_RETRIEVAL_PATH"],
            stage3_path=pipeline_config["STAGE3_FORMAT_AS_TOKENS_PATH"],
            logs_output_base=logs_output_base,
            prompt_config=pipeline_config["stage_3_config"]["prompt_config"],
            eval_repo_names_path=pipeline_config["stage_3_config"]["eval_repo_names_path"],
            analytics_only=analytics_only,
        )
    if 4 in active_stages:
        stage4_export(
            stage3_path=pipeline_config["STAGE3_FORMAT_AS_TOKENS_PATH"],
            stage4_path=pipeline_config["STAGE4_INDEXED_DATASET_PATH"],
            logs_output_base=logs_output_base,
            tokenizer_name=pipeline_config["stage_3_config"]["prompt_config"]["tokenizer_name"],
            analytics_only=analytics_only,
        )


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Run pipeline with specified active stages")
    parser.add_argument("--stages", nargs="+", type=int, help="List of active stages to run", required=True)
    parser.add_argument("--analytics-only", action="store_true", help="Only run analytics on existing data")
    args = parser.parse_args()

    active_stages = set(args.stages)
    run_pipeline(active_stages=active_stages, analytics_only=args.analytics_only)