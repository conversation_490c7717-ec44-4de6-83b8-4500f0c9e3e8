#%%
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark
import pyspark.sql.functions as F
from tqdm import tqdm

from research.utils.repo_change_utils import RepoChange

cpu_spark = create_cpu_spark()

dataset_path = 'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13-fixed/stage4_with_hydrated_commits_v3/'
#%%
ds_raw = cpu_spark.read.parquet(dataset_path)
#%%
from compress_pickle import dumps as compressed_dumps
from compress_pickle import loads as compressed_loads
from research.utils.repo_change_utils import Modified
from pyspark.sql.types import BinaryType
from pyspark.sql.types import IntegerType

def filter_problems_with_undecodable_files(problems_bytes):
    problems = compressed_loads(problems_bytes, compression='gzip')
    new_problems = []
    for problem in problems:
        found_undecodable_file = False
        for file in problem.fixing_change.changed_files:
            if isinstance(file, Modified) and file.after.code == file.before.code and file.after.code == "":
                found_undecodable_file = True
                break
        for file in problem.breaking_change.changed_files:
            if isinstance(file, Modified) and file.after.code == file.before.code and file.after.code == "":
                found_undecodable_file = True
                break
        if not found_undecodable_file:
            new_problems.append(problem)

    return compressed_dumps(new_problems, compression='gzip')
ds = ds_raw.withColumn('problems', F.udf(filter_problems_with_undecodable_files, BinaryType())(F.col('problems')))
ds_local = ds.limit(500).toPandas()

ds_local
#%%
problem = problems = compressed_loads(ds_local.iloc[0]['problems'], compression='gzip')[0]
problem.passing_child.sha
#%%
from compress_pickle import loads as compressed_loads
from research.utils.repo_change_utils import Modified

sha_to_logs = {}

i = 0
for row in ds.toLocalIterator(prefetchPartitions=True):
    i += 1
    if i < 50:
        continue
    if i > 500:
        break

    print(row['repo_id'])
    problems = compressed_loads(row['problems'], compression='gzip')
    for problem in problems:
        sha_to_logs[problem.passing_child.sha] = problem.logs
        print(problem.passing_child.sha)
    print('------------')

    # https://github.com/ToFuProject/spectrally/commit/177541ed53fddbca061acca9bdb89ea1b28850ab
#%%
print(sha_to_logs['e640f5507a2cf8546432c3e800fb5e581702df0a'][0])