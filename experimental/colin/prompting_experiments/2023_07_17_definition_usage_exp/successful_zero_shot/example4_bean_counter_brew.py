class Brew(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    grind_size = db.Column(db.SmallInteger)
    bean_dose = db.Column(db.Integer)
    water_dose = db.Column(db.Integer)
    extraction_time = db.Column(db.Integer)
    brew_method = db.Column(db.SmallInteger)
    filter_type = db.Column(db.SmallInteger)
    brew_date = db.Column(db.DateTime)
    notes = db.Column(db.Text)

    bean_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('bean.id'))
    user_id = db.Column(db.Integer, db.<PERSON>ey('user.id'))
    roast_id = db.Column(db.Integer, db.ForeignKey('roast.id'), default=None)

    def brew_method_str(self):
        return BREW_METHODS[self.brew_method]

    def filter_type_str(self):
        return FILTER_TYPES[self.filter_type]

    def grind_size_str(self):
        return GRIND_SIZES[self.grind_size]

    def remove_brew(self, commit=True):
        db.session.delete(self)
        if commit:
            db.session.commit()

    @staticmethod
    def add_brew(brew, commit=True):
        db.session.add(brew)
        if commit:
            db.session.commit()

def restart_brew_every_10_minutes():
    """Restart the brew every 10 minutes by:
    - removing the oldest brew,
    - and then adding a new brew.
    """
    # Begin Copilot (zero-shot)
    # Remove the oldest brew.
    oldest_brew = Brew.query.order_by(Brew.brew_date.asc()).first()
    oldest_brew.remove_brew(commit=False)

    # Add a new brew.
    new_brew = Brew(
        grind_size=random.randint(0, len(GRIND_SIZES) - 1),
        bean_dose=random.randint(0, 50),
        water_dose=random.randint(0, 50),
        extraction_time=random.randint(0, 50),
        brew_method=random.randint(0, len(BREW_METHODS) - 1),
        filter_type=random.randint(0, len(FILTER_TYPES) - 1),
        brew_date=datetime.now(),
        notes='This is a note.',
        bean_id=random.randint(0, 50),
        user_id=random.randint(0, 50),
        roast_id=random.randint(0, 50),
    )
    Brew.add_brew(new_brew, commit=False)

    # Commit the changes.
    db.session.commit()
    # End Copilot (zero-shot)

    # Begin StarCoder (zero-shot)
    # Get the oldest brew
    oldest_brew = Brew.query.order_by(Brew.brew_date).first()
    # Remove the oldest brew
    oldest_brew.remove_brew()
    # Add a new brew
    Brew.add_brew(Brew())
    # End StarCoder (zero-shot)

    # Begin StarCoder (few-shot with prompt1.py)
    # Get the oldest brew
    oldest_brew = Brew.query.order_by(Brew.brew_date).first()
    # Remove the oldest brew
    oldest_brew.remove_brew()
    # Add a new brew
    Brew.add_brew(Brew())
    # End StarCoder (few-shot with prompt1.py)

'''
Analysis:

This example is from https://github.com/BouncyNudibranch/bean-counter/blob/master/app/models.py.

Copilot passes.

StarCoder (with and without prompt1.py) does well.

'''
