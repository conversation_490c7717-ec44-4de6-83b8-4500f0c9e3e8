import os
import argparse
import asyncio
import openai
from termcolor import colored


async def stream_chat_completion(async_stream):
    reasoning_chunks = []
    response_chunks = []
    try:
        async for i in async_stream:
            print(i)
            if (
                hasattr(i.choices[0].delta, "reasoning_content")
                and i.choices[0].delta.reasoning_content
            ):
                if not reasoning_chunks:
                    print("-" * 100)
                    print("Reasoning content")
                    print("-" * 100)
                print(
                    colored(i.choices[0].delta.reasoning_content, "blue"),
                    end="",
                    flush=True,
                )
                reasoning_chunks.append(i.choices[0].delta.reasoning_content)
            elif i.choices[0].delta.content:
                if not response_chunks:
                    print()
                    print("-" * 100)
                    print("Response content")
                    print("-" * 100)
                print(colored(i.choices[0].delta.content, "green"), end="", flush=True)
                response_chunks.append(i.choices[0].delta.content)
            elif i.choices[0].delta.tool_calls:
                for tool_call in i.choices[0].delta.tool_calls:
                    if not response_chunks:
                        print()
                        print("-" * 100)
                        print("Response content")
                        print("-" * 100)
                    if tool_call.function.name:
                        print(
                            "tool name:",
                            colored(tool_call.function.name, "yellow"),
                            end="\n\n",
                            flush=True,
                        )
                        response_chunks.append(tool_call.function.name)
                    elif tool_call.function.arguments:
                        print(
                            colored(tool_call.function.arguments, "red"),
                            end="",
                            flush=True,
                        )
                        response_chunks.append(tool_call.function.arguments)
    except Exception:
        import traceback

        traceback.print_exc()


async def main(args):
    oai_compat_tools = [
        {
            "type": "function",
            "function": {
                "name": "python_code_interpreter",
                "description": "python code interpreter that is able to run python code",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "code": {
                            "type": "string",
                            "description": "The python code to run",
                        },
                    },
                    "required": ["code"],
                    "additionalProperties": False,
                },
            },
        }
    ]

    code = """\
def quicksort(arr):
    # Stack to simulate recursion
    stack = [(0, len(arr) - 1)]
    while stack:
        low, high = stack.pop()
        if low < high:
            # Partition the array
            pivot = arr[high]
            i = low - 1
            for j in range(low, high):
                if arr[j] <= pivot:
                    i += 1
                    arr[i], arr[j] = arr[j], arr[i]
            arr[i + 1], arr[high] = arr[high], arr[i + 1]
            pivot_idx = i + 1
            # Push subarrays to stack
            # Push larger subarray first to ensure O(log n) space
            if pivot_idx - low < high - pivot_idx:
                stack.append((pivot_idx + 1, high))
                stack.append((low, pivot_idx - 1))
            else:
                stack.append((low, pivot_idx - 1))
                stack.append((pivot_idx + 1, high))
    return arr"""

    messages = [
        {
            "role": "user",
            "content": f"Test whether the following code is correct:\n\n{code}",
        },
    ]
    base_url = "https://research-models.api.x.ai/research/swe"
    api_key = os.environ["XAI_API_KEY"]
    client = openai.AsyncOpenAI(api_key=api_key, base_url=base_url)
    try:
        completion = await client.chat.completions.create(
            messages=messages,
            model="swe-v6-checkpoint-05-23",
            max_tokens=4096,
            temperature=0.3,
            top_p=0.95,
            tools=oai_compat_tools,
            tool_choice="required",
            stream=args.stream,
        )
        if args.stream:
            await stream_chat_completion(completion)
        else:
            print("-" * 100)
            print("Response content")
            print("-" * 100)
            # print(colored(completion.choices[0].message.reasoning_content, "blue"))
            if completion.choices[0].message.content:
                print(colored(completion.choices[0].message.content, "green"))
            elif completion.choices[0].message.tool_calls:
                for tool_call in completion.choices[0].message.tool_calls:
                    print(
                        "tool name:",
                        colored(tool_call.function.name, "yellow"),
                        end="\n\n",
                    )
                    print(colored(tool_call.function.arguments, "red"))
    except Exception as e:
        print(f"Unexpected error in tool_call_test: {e}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--stream", default=False, action=argparse.BooleanOptionalAction
    )
    args = parser.parse_args()
    asyncio.run(main(args))


"""
Example usage:

python tool_call_test.py --stream
"""
