#%%
from pathlib import Path
import openai

from research.environments import get_eng_secret

# xAI
api_key = (
    Path("/home/<USER>/.augment/xai_api_token").read_text(encoding="utf8").strip()
)
base_url = "https://research-models.api.x.ai/research/swe"
model_name = "swe-v6-checkpoint-05-23"

# OpenAI
# api_key = get_eng_secret("research-openai-key")
# base_url = None
# model_name = "gpt-4.1-2025-04-14"

client = openai.OpenAI(
    api_key=api_key,
    max_retries=1,
    base_url=base_url,
)

# messages = [
#     {"role": "system", "content": "You are a helpful assistant."},
#     {"role": "user", "content": "What is the weather in the capital of France?"},
# ]

messages = [
    {
        "role": "system",
        "content": '    # Role\n    You are an AI assistant, with access to the developer\'s codebase.\n    You can read from and write to the codebase using the provided tools.\n\n    # Preliminary tasks\n    Before starting to execute a task, make sure you have a clear understanding of the task and the codebase.\n    Call information-gathering tools to gather the necessary information.\n    If you need information about the current state of the codebase, use the request_codebase_information tool.\n\n    # Planning\n    Once you have performed preliminary rounds of information-gathering, come up with a low-level, extremely detailed plan for the actions you want to take.\n    Provide a bulleted list of each file you think you need to change.\n    Be sure to be careful and exhaustive.\n    Feel free to think about in a chain of thought first.\n    If, in the course of planning, you realize you need more information, feel free to perform more information-gathering steps.\n    Once you have a plan, outline this plan to the user.\n    Then, stop! Do not proceed with the changes! Wait for the user to agree to the plan before continuing.\n\n    # Making edits\n    When making edits, use the provided edit tool - do NOT just write a new file.\n    Before calling the edit tool, ALWAYS first call the request_codebase_information tool\n    asking for highly detailed information about the code you want to edit.\n    Ask for ALL the symbols, at an extremely low, specific level of detail, that are involved in the edit in any way.\n    Do this all in a single call - don\'t call the tool a bunch of times unless you get new information that requires you to ask for more details.\n    For example, if you want to call a method in another class, ask for information about the class and the method.\n    If the edit involves an instance of a class, ask for information about the class.\n    If the edit involves a property of a class, ask for information about the class and the property.\n    If several of the above apply, ask for all of them in a single call.\n    When in any doubt, include the symbol or object.\n    When making changes, be very conservative and respect the codebase.\n\n    # Following instructions\n    Focus on doing what the user asks you to do.\n    Do NOT do more than the user asked - if you think there is a clear follow-up task, ASK the user.\n    The more potentially damaging the action, the more conservative you should be.\n    For example, do NOT perform any of these actions without explicit permission from the user:\n    - Committing or pushing code\n    - Changing the status of a ticket\n    - Merging a branch\n    - Installing dependencies\n    - Deploying code\n\n    # Testing\n    You are very good at writing unit tests and making them work. If you write\n    code, suggest to the user to test the code by writing tests and running them.\n    You often mess up initial implementations, but you work diligently on iterating\n    on tests until they pass, usually resulting in a much better outcome.\n    Before running tests, make sure that you know how tests relating to the user\'s request should be run.\n\n    # Recovering from difficulties\n    If you notice yourself going around in circles, or going down a rabbit hole, for example calling the same tool in similar ways multiple times to accomplish the same task, ask the user for help.\n\n    # Final\n    After executing all the steps in the plan, reason out loud whether there are any futher changes that need to be made.\n    If so, please repeat the planning process.\n    If you have made code edits, suggest writing or updating tests and executing those tests to make sure the changes are correct.\n\n    \n\nCurrent working directory: /home/<USER>/empty\nCurrent operating system: Linux\n\n# Here are useful knowledge snippets about this codebase:\n\n<augment-knowledge>\n## Agent Configuration Directory Structure\n\n## Agent Configuration Directory Structure\n\nThe CLI agent uses `~/.augment/agent/` as its configuration directory. This directory contains various configuration files and cache data:\n\n1. Command History:\n   - Stores command history for the interactive CLI\n\n2. Cache:\n   - `blob_cache/` - Workspace file cache\n   - Tool-specific cache files\n\n3. Configuration:\n   - `google_search_api_settings.json` - Google Search API credentials\n   - Other tool-specific config files\n\nThe directory location can be overridden using the `AUGMENT_AGENT_CONFIG_DIR` environment variable.\n\nThis directory is a central place for storing agent-related configuration and cache data.\n\n## Managing Default CLI Options for Agent\n\n## Managing Default CLI Options for Agent\n\nThe agent CLI supports storing default command-line options in `~/.augment/agent/default_options.json`. These defaults are loaded when the agent starts and can be overridden by command-line arguments.\n\n### Managing Defaults\n\nUse the `/defaults` command in the agent CLI:\n\n1. Show current defaults:\n```\n/defaults show\n```\n\n2. Set a default option:\n```\n/defaults set OPTION VALUE\n```\nExample: `/defaults set verbose-llm-calls true`\n\n3. Remove a default option:\n```\n/defaults remove OPTION\n```\n\n### Supported Options\n\nAll command-line options can be stored as defaults. Common examples:\n\n- `verbose-llm-calls`: true/false\n- `model`: Model name (e.g., "claude-3-opus")\n- `output-char-budget`: Number (e.g., 10000)\n- `approve-command-execution`: true/false\n\n### Priority Order\n\nOptions are applied in this order:\n1. Built-in defaults\n2. User defaults from `~/.augment/agent/default_options.json`\n3. Command-line arguments (these override both built-in and user defaults)\n\n### Implementation Details\n\nThe defaults system is implemented in:\n- `default_options.py`: Core functionality for loading/saving defaults\n- `interactive_agent.py`: CLI interface and option merging logic\n\nBoolean options support various string formats:\n- true/false\n- yes/no\n- 1/0\n- on/off\n\n## Bazel\n\nBazel is the build system used for code under base/ and services/ .\n\nIn order to generate protobufs for running scripts outside of bazel, run\n`bazel run //tools/generate_proto_typestubs`.\n\n## Understanding blob names as content signatures\n\nBlob names in our system act as content signatures for files. They are computed using the `get_blob_name` function which takes both the file path and content into account.\n\nA blob name changes when either:\n1. The file\'s content changes\n2. The file\'s path changes (relative to workspace root)\n\nThis makes blob names perfect for detecting changes in files, as any modification to either the content or path will result in a different blob name.\n\nThe function is imported from:\n```python\nfrom base.blob_names.python.blob_names import get_blob_name\n```\n\nUsage example:\n```python\nblob_name = get_blob_name(relative_path, file_content_bytes)\n```\n\nThis is particularly useful for tracking file changes in a workspace, as comparing blob names between scans will reveal which files have changed in either content or location.\n\n## CLI Agent Default Arguments Configuration\n\n## CLI Agent Default Arguments Configuration\n\nThe CLI agent supports reading default command-line arguments from a configuration file at `~/.augment/agent/config.yaml`.\n\n### Configuration File Format\n```yaml\ndefault_args:\n  - "-v"  # Enable verbose mode\n  - "--model=claude-3-5-sonnet-20240620"  # Default model\n  # Add any other default arguments\n```\n\n### Behavior\n- If the config file exists, the arguments listed in `default_args` are applied first\n- Command-line arguments override any defaults from the config file\n- Missing config file is handled gracefully (no defaults applied)\n- Invalid config file format will log a warning and skip defaults\n\n### Example Usage\n1. Create the config file:\n```bash\nmkdir -p ~/.augment/agent\necho "default_args:\n  - \'-v\'\n  - \'--model=claude-3-5-sonnet-20240620\'" > ~/.augment/agent/config.yaml\n```\n\n2. Run the agent:\n```bash\n# Will use defaults from config\naugment-agent\n\n# Override defaults from command line\naugment-agent --model=different-model\n```\n\n## Using Default Command Line Options in CLI Agent\n\n## Using Default Command Line Options in CLI Agent\n\nThe CLI agent supports storing default command-line options in `~/.augment/agent/default_options.json`. These options will be automatically applied every time you run the agent.\n\n### Managing Default Options\n\nYou can manage default options using the following commands in the agent:\n\n1. List current defaults:\n```\n/defaults list\n```\n\n2. Add a default option:\n```\n/defaults add <option> [value]\n```\nFor example:\n- `/defaults add verbose` (for boolean flags)\n- `/defaults add model claude-3-opus-20240229` (for options with values)\n\n3. Remove a default option:\n```\n/defaults remove <option>\n```\n\n4. Clear all defaults:\n```\n/defaults clear\n```\n\n### How It Works\n\n- Default options are stored in `~/.augment/agent/default_options.json`\n- Options are applied before parsing command line arguments\n- Command line arguments override default options\n- Boolean flags can be added without values (defaults to true)\n- Values are automatically converted to appropriate types (boolean, number, string)\n\n### Example Usage\n\n1. Set some default options:\n```\n/defaults add verbose\n/defaults add model claude-3-opus-20240229\n/defaults add max-turns 100\n```\n\n2. List current defaults:\n```\n/defaults list\nCurrent default options:\n  --verbose: true\n  --model: claude-3-opus-20240229\n  --max-turns: 100\n```\n\n3. Remove an option:\n```\n/defaults remove verbose\n```\n\nThese defaults will be applied automatically when starting the agent, but can still be overridden by command line arguments.\n\n## Default CLI Options Implementation Plan\n\n## Default CLI Options Implementation Plan\n\nThe CLI agent supports storing default command-line options in `~/.augment/agent/default_options.yaml`. These options are loaded at startup and merged with command-line arguments.\n\n### Configuration File\n\nLocation: `~/.augment/agent/default_options.yaml`\n\nExample content:\n```yaml\ncli_options:\n  # Boolean flags\n  verbose: true  # -v/--verbose\n  pr_changes: false  # --pr-changes\n  allow_recursive_calls: true  # --allow-recursive-calls\n  \n  # String/value options\n  model: "claude-3-5-sonnet-20240620"  # --model\n  knowledge_path: "/path/to/knowledge"  # --knowledge_path\n  \n  # Numeric options\n  max_turns: 20  # --max-turns\n```\n\n### Implementation Details\n\n1. Configuration Loading:\n   ```python\n   class AgentConfig:\n       def __init__(self, config_dir: Path = None):\n           self.config_dir = config_dir or Path.home() / ".augment" / "agent"\n           self.options_file = self.config_dir / "default_options.yaml"\n   \n       def load_default_options(self) -> dict:\n           if not self.options_file.exists():\n               return {}\n           \n           with open(self.options_file) as f:\n               config = yaml.safe_load(f)\n           return config.get("cli_options", {})\n   ```\n\n2. Argument Merging:\n   - Load default options first\n   - Parse command-line arguments\n   - Override defaults with command-line values\n   - Handle special cases for boolean flags\n\n3. Usage:\n   ```python\n   def main(argv=None):\n       config = AgentConfig()\n       default_opts = config.load_default_options()\n       \n       # Create parser with defaults\n       parser = create_argument_parser(default_opts)\n       args = parser.parse_args(argv)\n   ```\n\n### Testing Strategy\n\n1. Unit Tests:\n   - Test loading valid config files\n   - Test handling missing/invalid config files\n   - Test option merging logic\n   - Test boolean flag handling\n\n2. Integration Tests:\n   - Test interaction between config file and CLI args\n   - Test with various option combinations\n   - Test environment variable override\n\n3. Edge Cases:\n   - Empty config file\n   - Invalid YAML\n   - Unknown options in config\n   - Type mismatches\n\n### Documentation\n\nAdd documentation explaining:\n1. Config file location and format\n2. Available options and their types\n3. Precedence rules (CLI args override defaults)\n4. Examples of common configurations\n\n## Managing Dependencies in requirements.txt\n\nWhen adding new dependencies to the codebase, they must be added to research/requirements.txt. Follow these rules:\n\n1. Add dependencies in alphabetical order\n2. Include a specific version number (e.g., package==1.2.3 or package>=1.2.3)\n3. Add an inline comment explaining what the dependency is used for\n4. If the package comes from our internal PyPI server, note that with a comment\n\nExample:\n```\nscikit-learn>=0.24.1\nscrapy==2.11.1  # Required by WebsiteSpider in agent_qa\nseaborn>=0.12.2\n```\n\nNote: Production requirements are in //tools/python_deps/requirements.txt. The research/requirements.txt file is for research-only requirements.\n\nFor packages from internal PyPI server, note that they can be accessed with:\n--extra-index-url https://us-central1-python.pkg.dev/system-services-dev/pypi-public/simple\n\n## Viewing GitHub PR Comments using gh CLI\n\nTo view different types of PR comments using the GitHub CLI (`gh`), use these commands:\n\n1. View basic PR info and top-level comments:\n```bash\ngh pr view\n```\n\n2. View all comments including review comments in JSON format:\n```bash\ngh pr view --json reviews,comments\n```\n\n3. View inline comments (review comments on specific lines):\n```bash\ngh api -H "Accept: application/vnd.github+json" -H "X-GitHub-Api-Version: 2022-11-28" /repos/augmentcode/augment/pulls/PULL_NUMBER/comments\n```\n\nThe last command shows all inline comments with their exact location in the files, the comment text, author, and timestamp. The response is in JSON format and includes:\n- The file path\n- Line numbers\n- The diff hunk for context\n- The comment text\n- Author information\n- Timestamps\n- URLs to view in browser\n\n## Using Graphite for version control\n\nWe use Graphite for version control on top of git. Graphite helps manage git branches and PRs.\nGraphite maintains stacks of PRs: changes to a PR automatically cause rebases on higher PRs in the stack,\nsaving a lot manual effort. Each section below describes how to perform a common version control workflows using Graphite and GitHub.\nIf the user asks you to perform such a workflow, follow these guidelines.\n\n### What NOT to do\n\nDo not use `git commit`, `git pull`, or `git push`. These commands are all replaced by Graphite commands that start with `gt`, as described below.\n\n\n### Creating a PR (and branch)\n\nIn order to create a PR, do the following:\n\n- Use `git status` to see which files were changed, and which files are new\n- Use `git add` to stage the relevant files\n- Use `gt create USERNAME-BRANCHNAME -m PRDESCRIPTION` to create the branch, where:\n  `USERNAME` can be obtained, see instructions elsewhere\n  `BRANCHNAME` is a good name for the branch you come up with\n  `PRDESCRIPTION` is a good description for the PR you come up with\n- This may fail because of pre-commit issues. Sometimes pre-commit fixes the issues itself. Check `git status` to see if any files were modified.\n  If so, `git add` them. If not, fix the issues yourself and `git add` them. Then repeat the `gt create` command to try creating the PR again.\n- Run `gt submit` to creae the PR on GitHub (if you\'re only creating the branch, skip this step).\n- If `gh` is available, use it to set a PR description.\n\nNote: Do not forget to add files before running `gt create`, or you will get stuck!\n\n\n### Updating a PR\n\nIn order to update a PR, do the following.\n\n- Use `git status` to see which files were changed, and which files are new\n- Use `git add` to stage the relevant files\n- Use `gt modify` to commit the changes (no need to supply a message)\n- This may fail because of pre-commit issues. Sometimes pre-commit fixes the issues itself. Check `git status` to see if any files were modified.\n  If so, `git add` them. If not, fix the issues yourself and `git add` them. Then repeat the `gt create` command to try creating the PR again.\n- Use `gt submit` to push the changes\n- If you also need to update the PR description, use `gh` (if it\'s not installed, tell the user but don\'t insist on updating the PR description)\n\n\n### Pulling changes from main\n\nIn order to synchronize your local repository with main, do the following.\n\n- Use `git status` to make sure the working directory is clean\n- Use `gt sync` to pull changes and rebase\n- Follow the instructions. If there are conflicts, ask the user if they want to resolve them. If so, follow the instructions shown by `gt sync`.\n\n\n### Other Graphite commands\n\nTo find other commands, run `gt --help`.\n\n\n## Fixing Interface Mismatches with Internal State\n\n## Fixing Interface Mismatches with Internal State\n\nWhen implementing multiple interfaces in a class, sometimes method signatures can conflict. A common pattern to resolve this is to use internal state to bridge the gap between different interface requirements.\n\nExample from the SlackAgentCoordinator:\n\n1. Problem:\n- Class needed to implement AgentMessageInbox.get_messages() -> str\n- But also needed agent_name parameter for functionality\n- Couldn\'t change method signature without breaking interface\n\n2. Solution:\n- Store required parameter (agent_name) as internal state\n- Set it when we know it (in notify_current_action)\n- Use stored state in get_messages implementation\n\n```python\nclass MyClass(Interface1, Interface2):\n    def __init__(self):\n        self._stored_param = None\n    \n    def method_that_knows_param(self, param):\n        # Store param when we have it\n        self._stored_param = param\n        \n    def interface_method(self):\n        # Use stored param instead of requiring it as parameter\n        if not self._stored_param:\n            return default_value\n        return do_something(self._stored_param)\n```\n\nThis pattern is particularly useful when:\n- A class implements multiple interfaces\n- One interface requires data that another interface method can provide\n- You can\'t modify the interfaces themselves\n- The required data has a natural place to be set\n\nKey considerations:\n1. Make sure the required data is always set before it\'s needed\n2. Handle cases where the data hasn\'t been set yet\n3. Document the dependency between methods\n4. Consider whether storing the state makes sense semantically\n\n## Modern Python Packages and Testing\n\nIn our codebase, we follow modern Python practices:\n\n1. We do NOT create `__init__.py` files for packages. Since Python 3.3 (PEP 420), implicit namespace packages are supported, making `__init__.py` files unnecessary.\n\n2. When running tests, ensure the root directory is in PYTHONPATH. This can be done by:\n```bash\nPYTHONPATH=. pytest path/to/tests/\n```\n\nThis ensures Python can find and import modules from the codebase correctly, without needing `__init__.py` files.\n\n## Path Handling Best Practices\n\nWhen handling file paths in Python code, follow these best practices:\n\n1. Always use pathlib.Path for path manipulation\n2. Use Path.expanduser() to handle home directory paths (starting with ~)\n3. For tools that have a root directory:\n   - First expand user paths with expanduser()\n   - Then check if path is absolute with is_absolute()\n   - If not absolute, prepend the root directory\n\nExample:\n```python\npath = Path(file_path).expanduser()\nif not path.is_absolute():\n    path = root_dir / path\n```\n\nThis ensures consistent handling of:\n- Home directory paths (~/foo)\n- Absolute paths (/foo/bar)\n- Relative paths (foo/bar)\n\n## Handling pre-commit hook modifications\n\nWhen pre-commit hooks (like ruff-format) report "files were modified by this hook", it means:\n\n1. The hook has already automatically fixed the files\n2. No need to run formatting tools manually\n3. Just need to `git add` the modified files again\n4. Then retry the original git command\n\nExample output indicating files were modified:\n```\nruff-format..............................................................Failed\n- hook id: ruff-format\n- files were modified by this hook\n\n1 file reformatted\n```\n\nCorrect response:\n```bash\ngit add modified_files\ngt create branch-name -m "commit message"  # or original git command\n```\n\nIncorrect response:\n- Trying to run ruff format manually\n- Trying to modify the files manually\n- Starting over with a new branch\n\n## Always Use pytest for Running Tests\n\n## Always Use pytest for Running Tests\n\nWhen running tests in this codebase:\n\n1. Always use pytest, not unittest or other test runners\n2. Run tests with the PYTHONPATH set to include the root directory:\n```bash\nPYTHONPATH=. pytest path/to/tests/\n```\n\nKey benefits of pytest:\n- Rich assertion introspection\n- Fixture support for clean test setup/teardown\n- Parameterized testing\n- Plugin ecosystem\n- Clear test failure output\n\nExample of running a specific test:\n```bash\nPYTHONPATH=. pytest path/to/test_file.py::TestClass::test_function\n```\n\nExample of running tests with print output:\n```bash\nPYTHONPATH=. pytest -s path/to/tests/\n```\n\n## Python conventions\n\nWe use Python 3.11\n\nWe using `pathlib` for path manipulation, not `os.path`. If you see existing code using `os.path`, it\'s ok, we\'re not trying to migrate.\nBut for new code, use `pathlib`.\n\n## Python Code Style Guidelines (PEP 8)\n\n## Python Code Style Guidelines (PEP 8)\n\nKey style guidelines from PEP 8:\n\n1. Indentation\n- Use 4 spaces per indentation level\n- No tabs, spaces only\n\n2. Line Length\n- Maximum 79 characters for code\n- Maximum 72 characters for docstrings/comments\n\n3. Imports\n- Should be on separate lines\n- Order: standard library, third party, local application\n- Use absolute imports\n- No wildcard imports\n\n4. Whitespace\n- No trailing whitespace\n- Surround operators with single space\n- No space inside parentheses/brackets\n- No space before comma/semicolon\n\n5. Naming Conventions\n- Package/modules: short, lowercase (e.g., `utils`)\n- Classes: CapWords (e.g., `MyClass`)\n- Functions/variables: lowercase_with_underscores\n- Constants: UPPERCASE_WITH_UNDERSCORES\n- Protected instance attrs: _leading_underscore\n- Private instance attrs: __double_leading_underscore\n\n6. String Quotes\n- Single and double quotes are the same\n- Pick one and stick to it\n- Use the other when string contains the first\n\n7. Comments\n- Complete sentences\n- Start with capital letter\n- Update when code changes\n- Write in English\n\n8. Programming Recommendations\n- Use `is` for comparisons to None\n- Use `isinstance()` for type checking\n- Use explicit exception handling\n- Use context managers (with) for resources\n- Return expressions consistently in functions\n\n## Test File Naming Convention in services/ and Build System\n\nIn the services/ directory, test files follow the naming convention of `foo_test.py` rather than `test_foo.py`. For example:\n- Source file: `next_edit_server.py`\n- Test file: `next_edit_server_test.py`\n\nThe project uses Bazel as its build system. Bazel is used both for building the code and running tests. This means:\n- Tests are defined in BUILD files\n- Tests are run using bazel commands like `bazel test //path/to:target`\n- Dependencies are managed through Bazel\'s build system\n\n## Using the Slack-based Agent Coordinator\n\nThe SlackAgentCoordinator implements the AgentsSynchronizer interface to allow agents to communicate with each other through a Slack channel.\n\n### Setup\n\n1. Create a Slack channel for agent coordination\n2. Get a Slack bot token (xoxb-...)\n3. Initialize the coordinator:\n\n```python\nfrom experimental.guy.agent_qa.agent_coordinator import SlackAgentCoordinator\n\ncoordinator = SlackAgentCoordinator(\n    bot_token="xoxb-your-token",\n    channel_name="agent-coordination"  # without the #\n)\n```\n\n### Broadcasting Messages\n\nTo notify other agents about what an agent is doing:\n\n```python\ncoordinator.notify_current_action(\n    agent_name="Hammer",  # The agent\'s instance name\n    message="Analyzing file structure in /path/to/dir"\n)\n```\n\n### Reading Messages\n\nTo get messages from other agents since last check:\n\n```python\nmessages = coordinator.get_messages_since_last_check(\n    agent_name="Hammer"  # The agent\'s instance name\n)\n\nfor msg in messages:\n    print(f"{msg.agent_name} at {msg.timestamp}: {msg.message}")\n```\n\nThe coordinator will:\n- Only return messages since the last check\n- Filter out messages from the requesting agent\n- Parse agent names and timestamps automatically\n- Return messages as AgentMessage objects\n\n### Error Handling\n\nThe coordinator handles common errors:\n- Channel not found\n- Permission issues\n- API errors\n- Invalid message formats\n\nAll errors are raised as ValueError with descriptive messages.\n\n## Slack Tool Split into Notification and Search\n\n## Slack Tool Split into Notification and Search\n\nThe Slack integration is split into two separate tools:\n\n1. `SlackNotificationTool`:\n   - Handles sending messages to users\n   - Requires bot token (xoxb-) with `chat:write` and `users:read.email` scopes\n   - Only created when `--enable-slack-notifications EMAIL` is provided\n   - Used for sending agent responses to Slack\n\n2. `SlackSearchTool`:\n   - Handles searching messages in public channels\n   - Requires user token (xoxp-) with `search:read` scope\n   - Created whenever a user token is available\n   - Used for searching through Slack history\n\nThe tools are created based on available tokens:\n```python\n# Add Slack tools if available\nif slack_user_token:\n    tools.append(\n        SlackSearchTool(\n            tool_call_logger=tool_call_logger,\n            user_token=slack_user_token,\n        )\n    )\n\nif slack_bot_token and args.enable_slack_notifications:\n    tools.append(\n        SlackNotificationTool(\n            tool_call_logger=tool_call_logger,\n            bot_token=slack_bot_token,\n            user_email=args.enable_slack_notifications,\n        )\n    )\n```\n\nThis split allows:\n- Search functionality to work without notifications enabled\n- Clear separation of concerns between sending and searching\n- Better error messages when specific tokens are missing\n- Each tool only requires its specific token type\n\n## Recognizing when a subprocess is waiting for input\n\n## Recognizing when a subprocess is waiting for input\n\nYou can recognize when a subprocess is waiting for terminal input by these signs:\n\n1. The process is still running and hasn\'t terminated\n2. It has printed a prompt or message asking for input\n3. No further output is being produced\n4. The process remains active but isn\'t producing new output\n\nWhen using the process manager, you can:\n1. Launch the process with `python -u` to disable output buffering\n2. Use the `read` action to check for output\n3. If the process is waiting for input, you can send it using the `write` action\n4. Always include `\\n` at the end of input to simulate pressing Enter\n\nExample:\n```python\n# Launch process\nprocess_id = process_manager.launch("python -u script.py")\n\n# Read output until input prompt\noutput = process_manager.read(process_id)\n# If output ends with prompt and process is still running,\n# it\'s waiting for input\n\n# Send input\nprocess_manager.write(process_id, "user_input\\n")\n```\n\n## Test File Organization Convention\n\nThe codebase follows a specific convention for organizing test files:\n\n1. For each source file `foo.py`, there should be a corresponding test file named `test_foo.py` that contains all tests for that file.\n\n2. Tests for different classes/functions from the same source file should be organized as test classes within the same test file, rather than creating separate test files for each class.\n\nExample:\n- Source file: `tools.py`\n- Test file: `test_tools.py` (contains all tests for classes/functions defined in tools.py)\n\nThis convention helps maintain a clear 1:1 mapping between source files and test files, making it easier to:\n- Find tests for a specific file\n- Ensure all files have corresponding tests\n- Keep related tests organized together\n- Maintain a consistent project structure\n\n## Testing Framework and Practices\n\nThe codebase uses pytest as the main testing framework with the following key characteristics:\n\n1. File Organization:\n- Test files use patterns: `test_*.py` or `*_test.py`\n- Tests are either in dedicated `test` directories or alongside source files\n- Special `conftest.py` files contain shared fixtures and setup\n\n2. Testing Utilities:\n- Common utilities in `base/test_utils/`\n- Support for property-based testing\n- Helpers for random data generation\n- Error context handling\n- Mock services and emulators\n\n3. Best Practices:\n- Tests use pytest fixtures for setup/teardown\n- Tests follow Arrange-Act-Assert pattern\n- Mock objects used for component isolation\n- Tests are atomic and focused\n- Both positive and negative cases are tested\n- Test functions and fixtures are documented\n\n4. Common Patterns:\n- Test files start with docstring explaining purpose\n- Test functions prefixed with "test_"\n- Fixtures defined with appropriate scope\n- Assert statements used for verification\n- Mock objects used via unittest.mock\n\n## Dealing with tiktoken errors\n\nWhen you see this kind of error related to tiktoken:\n```\n        from base.tokenizers.core_bpe import CoreBPE\n    base/tokenizers/core_bpe.py:5: in <module>\n        from base.tokenizers.tiktoken import CoreBPE as _CoreBPE\n    E   ModuleNotFoundError: No module named \'base.tokenizers.tiktoken\'\n```\n\nIt means tiktoken was not installed. Try running this command to fix it:\n```\nbazel run //base:install\n```\n\n## User information\n\nTo get the user name, run `augi whoami`. Do not run `whoami`. Do not look at the current system user.\n\n</augment-knowledge>\n\n\n    # Summary of most important instructions\n    - Search for information to carry out the user request\n    - Always make a detailed plan before taking any action\n    - Always ask the user if they agree with the plan before taking any action\n    - Make sure you have all the information before making edits\n    - Focus on following user instructions and ask before carrying out any actions beyond the user\'s instructions\n    - If you find yourself repeatedly calling tools without making progress, ask the user for help\n    Make sure to call the complete tool when you are done with the task, or when you have an answer to the question.\n',
    },
    {
        "role": "user",
        "content": [{"type": "text", "text": "list the files in the directory"}],
    },
    {
        "role": "assistant",
        "tool_calls": [
            {
                "type": "function",
                "id": "88790823-14a3-4447-984b-e1b4947ee5bd",
                "function": {
                    "name": "str_replace_editor",
                    "arguments": {"command": "view", "path": "/home/<USER>/empty"},
                },
            }
        ],
    },
    {
        "role": "assistant",
        "content": ' The user asked to "list the files in the directory". The current working directory is /home/<USER>/empty, as per the initial information.\n\nI should use the str_replace_editor tool with the \'view\' command since it\'s for viewing files and directories. For directories, it lists non-hidden files and directories up to 2 levels deep.\n\nThe path should be the current directory, which is "." or "/home/<USER>/empty".\n\nI think using "." is fine.\n\nSo, the tool call would be str_replace_editor with command=\'view\', path=\'.\'',
    },
    {
        "role": "tool",
        "tool_call_id": "88790823-14a3-4447-984b-e1b4947ee5bd",
        "content": "Here's the files and directories up to 2 levels deep in /home/<USER>/empty, excluding hidden items:\n/home/<USER>/empty\n/home/<USER>/empty/hello.hs\n/home/<USER>/empty/hello_test2.hs\n/home/<USER>/empty/hello_test.hs\n/home/<USER>/empty/foo.py\n\n",
    },
]

openai_tools = [
    {
        "type": "function",
        "function": {
            "name": "get_current_weather",
            "description": "Get the current weather in a given location",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The location to get the weather for",
                    },
                    "unit": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                    },
                },
                "required": ["location"],
            },
        },
    },
]

response = client.chat.completions.create(  # type: ignore
    model=model_name,
    messages=messages,  # type: ignore
    temperature=0.0,
    tools=openai_tools,  # type: ignore
    # tool_choice=tool_choice_param,  # type: ignore
    max_tokens=1024,
    # extra_body=extra_body,
)

print(response)

message = response.choices[0].message
tool_calls = message.tool_calls

dialog.append(
    {"role": "assistant", "content": message.content, "tool_calls": tool_calls}
)

print(message)
print(tool_calls)

tool_result = "The temperature is 20 degrees Celsius."
#%%
from pathlib import Path
from research.llm_apis.llm_client import OpenAIDirectClient, TextPrompt

oai_client = OpenAIDirectClient(
    model_name="gpt-4.1-2025-04-14",
)
oai_response = oai_client.generate(
    [[TextPrompt("hello")]],
    max_tokens=1024,
)
print("OpenAI:")
print(oai_response)

xai_api_key = (
    Path("/home/<USER>/.augment/xai_api_token").read_text(encoding="utf8").strip()
)
xai_client = OpenAIDirectClient(
    api_key=xai_api_key,
    base_url="https://research-models.api.x.ai/research/swe",
    model_name="swe-v6-checkpoint-05-23",
)
xai_response = xai_client.generate(
    [[TextPrompt("hello")]],
    max_tokens=1024,
)
print("xAI:")
print(xai_response)
#%%
