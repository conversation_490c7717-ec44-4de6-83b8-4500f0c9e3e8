#%% md
## Setup
#%%
import json
from pathlib import Path
import numpy as np
from termcolor import colored
import re
import yaml

from utils import CompletionResponse, LlamaCppClient, print_prompt


HEADER = "=" * 80


clients = {
    "code_llama_34b": LlamaCppClient(address="**************:8080"),
    "llama_70b": LlamaCppClient(address="**************:8081"),
    "deepseek-coder-base-33b": LlamaCppClient(address="**************:8082"),
    "deepseek-coder-instruct-33b": LlamaCppClient(
        address="**************:8083",
        prompt_template="You are an AI programming assistant, utilizing the Deepseek Coder model, developed by Deepseek Company, and you only answer questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.\n### Instruction: {prompt}\n### Response:\n",
        ),
}


def load_samples(path: str) -> list[dict]:
    return yaml.safe_load(open(path))


def load_basic_samples() -> list[dict]:
    return load_samples("basic_samples.yaml")


def load_categories(path: Path):
    with path.open("r", encoding="utf8") as file:
        lines = file.read().splitlines()
        categories = [line for line in lines if not line.startswith("#")]
        return categories


basic_samples = load_basic_samples()

#%% md
## Example generation for testing and debugging
#%%
SYSTEM_PROMPT = "Translate the instruction to a valid bash shell command."
INSTRUCTION_HEADER = "Instruction:"
COMMAND_HEADER = "Bash command:"

def make_single_shot_prompt(instruction, shell_command):
    return f"""\
{SYSTEM_PROMPT}

{INSTRUCTION_HEADER}
{instruction}

{COMMAND_HEADER}
{shell_command}
"""

labelled_samples = [
    {
        "instruction": "print the second column of file.txt",
        "shell_command": "cat file.txt | awk '{print $2}'",
        "type": "files",
    },
    {
        "instruction": "rename train-* to valid-*",
        "shell_command": 'for f in train-*; do mv "$f" "valid-${f#train-}"; done',
        "type": "files",
    },
    {
        "instruction": "delete git branch fix_bug2",
        "shell_command": "git branch -d fix_bug2",
        "type": "git",
    },
]


def make_few_shot_prompt(num_shots, samples=labelled_samples, prepare_next_sample=True):
    shot_samples = np.random.choice(samples, size=num_shots, replace=False)
    assert len(shot_samples) == num_shots
    shot_prompts = [
        make_single_shot_prompt(
            instruction=sample["instruction"],
            shell_command=sample["shell_command"])
        for sample in shot_samples
    ]
    prompt = "\n\n".join(shot_prompts)
    if prepare_next_sample:
        prompt += f"\n\n{SYSTEM_PROMPT}\n\n{INSTRUCTION_HEADER}\n"
    return prompt


def extract_answer(generated_text: str) -> tuple[str, str]:
    """Returns the instruction, command from the output."""
    first_answer = generated_text.split(SYSTEM_PROMPT)[0]
    instruction, command = [elem.strip() for elem in first_answer.split(COMMAND_HEADER)]
    return instruction, command


preamble = make_few_shot_prompt(num_shots=2)
# print(f"Prompt:\n'''{prompt}'''\n\n")

for name, client in clients.items():
        response = client.generate(preamble, max_generated_tokens=128, temperature=0.8)
        print(colored(f"=== Generated from model {response.model}:", "blue"))
        print("'''" + response.content + "'''")
        instruction, command = extract_answer(response.content)
        snippet = "{" + f'''
    "instruction": "{instruction}",
    "shell_command": "{command}",
''' + "}"
        print(snippet)
#%% md
## Generate diverse instructions and commands

Use a pre-generated list of categories.
#%%
categories = load_categories(Path("categories.txt"))
print(f"Loaded {len(categories)} categories.")

SYSTEM_PROMPT = "Translate the instruction to a valid shell command."

labelled_samples = basic_samples

def make_prompt(samples: list[dict], include_type: bool, include_command: bool):
    prompt = ""

    for i, sample in enumerate(samples):
        if i > 0:
            prompt += "\n"
        
        if include_type:
            type_line  = f"type: {sample['type']}\n"
        else:
            type_line = ""

        instruction_line = f"instruction: {sample['instruction']}\n"

        if include_command:
            command_line = f"command: {sample['command']}\n"
        else:
            command_line = ""

        prompt += f"""\
{SYSTEM_PROMPT}

{type_line}{instruction_line}{command_line}"""

    # finally, add the next system prompt so the model doesn't need to generate it
    prompt += f"""\

{SYSTEM_PROMPT}
"""

    return prompt


client = clients["code_llama_34b"]
temperature = 0.8



def is_good_command(command: str):
    if command.startswith("Error:"):
        return False
    if re.search(r"<\S+>", command):
        return False
    return True


# SHOT_STOP_CONDITIONS = [f"\n{SYSTEM_PROMPT}", "\n```"]
num_skipped = 0
good_samples = []

while len(good_samples) < 100:
    print("=" * 80)

    # generate instruction in a sampled category
    category = np.random.choice(categories)
    preamble = make_prompt(labelled_samples, include_type=True, include_command=True)
    preamble = preamble + f"type: {category}\n"
    # print(colored("\nfirst prompt:", "green"))
    # print_prompt(prompt)
    # print("prompt:")
    # print(prompt)

    response = client.generate(preamble, max_generated_tokens=128, temperature=temperature, stop=["\n"])
    # print(f"type: {category}")
    # print(response.content)

    m = re.match(r"^instruction: (.*)", response.content)
    if not m:
        print("Could not find instruction:", response.content)
        continue

    instruction = m.group(1).strip()
    print(colored("sampled type: ", "green") + category)
    print(colored("generated instruction: ", "green") + instruction)

    # generate commands: show the model the instruction but not the category
    preamble = make_prompt(labelled_samples, include_type=False, include_command=True)
    prompt_with_instruction = preamble + f"instruction: {instruction}\n"
    # print(colored("\nsecond prompt:", "green"))
    # print_prompt(prompt_with_instruction)
    response = client.generate(prompt_with_instruction, max_generated_tokens=128, temperature=0., stop=["\n"])

    m = re.match(r"^command: (.*)", response.content)
    if not m:
        print("Could not find command:", response.content)
        continue
    command = m.group(1).strip()

    if not is_good_command(command):
        print("Skipping bad command:", command)
        num_skipped += 1
        continue

    generated_sample = {
        "type": category,
        "instruction": instruction,
        "command": command,
    }

    good_samples.append(generated_sample)

    print(colored("generated: ", "green") + instruction)
    print(json.dumps(generated_sample, indent=2))

    # print(colored("temperature:", 'green'), temperature)
    # for _ in range(3):
    #     response = client.generate(prompt_with_instruction, max_generated_tokens=128, temperature=temperature, stop=["\n"])
    #     command = response.content
    #     print(f'        "command": "{command}",')

total_generated = len(good_samples) + num_skipped
print(f"Generated {len(good_samples)} good samples, skipped {num_skipped} bad samples, which is {num_skipped/total_generated:.1%}")

with Path("good_samples_draft.txt").open("w", encoding="utf-8") as file:
    json.dump(good_samples, file, indent=2)
#%% md
## Increase the complexity

A pre-evolution experiment.
#%%
labelled_samples = load_basic_samples()

preamble = ""

def make_single_shot(sample):
    prompt = f"""\
Here is an instruction with a shell command, followed by a complicated version of the instruction with its command.

instruction: {sample["instruction"]}
command: {sample["command"]}
"""

    if "complicated_instruction" in sample:
        prompt += f"""\
complicated instruction: {sample["complicated_instruction"]}
complicated command: {sample["complicated_command"]}
"""

    return prompt

for sample in labelled_samples:
    if "complicated_instruction" in sample:
        assert "complicated_command" in sample
        preamble += "\n" + make_single_shot(sample)

client = clients["code_llama_34b"]

new_samples = load_samples("good_samples.yaml")

for sample in new_samples:
    assert "complicated_instruction" not in sample
    print(HEADER)
    prompt = preamble + "\n" + make_single_shot(sample)
    # print_prompt(prompt)
    print(f"original instruction: {sample['instruction']}")
    print(f"original command: {sample['command']}")
    response = client.generate(prompt, max_generated_tokens=64, temperature=0.0, stop=["\n\n"])
    print(response.content)


#%% md
## Evolution
#%%
from copy import copy, deepcopy
from dataclasses import dataclass


Sample = dict


def find_missing_fields(source: dict, target: dict) -> list[str]:
    """Returns the set of fields that exist in "source" but not in "target"."""
    return [field for field in source if field not in target]


@dataclass
class SampleTransformation:
    transformation_type: str
    sample_before: Sample
    sample_after: Sample


@dataclass
class SamplesWithTransformations:
    samples: list[Sample]
    transformations: list[SampleTransformation]

    def extend(self, other: "SamplesWithTransformations"):
        self.samples.extend(other.samples)
        self.transformations.extend(other.transformations)


def flatten_sample(sample: Sample) -> SamplesWithTransformations:
    """Takes a sample with a transformation tree and returns a flat list.

    The first returned sample is the root of the transformation tree.

    If the sample has fields not contained in a sub-sample, those fields are
    considered "inherited" and will be added to the sub-sample.

    The original sample object is unchanged.
    """
    sample = copy(sample)
    result = SamplesWithTransformations(samples=[sample], transformations=[])

    if "transformations" in sample:
        sample_transformations = sample["transformations"]
        del sample["transformations"]

        for transformed_sample in sample_transformations:
            transformed_sample = copy(transformed_sample)

            # Copy transformation type aside
            assert "transformation_type" in transformed_sample
            transformation_type = transformed_sample["transformation_type"]
            del transformed_sample["transformation_type"]

            # Add inherited fields
            inherited_fields = find_missing_fields(sample, transformed_sample)
            for field in inherited_fields:
                transformed_sample[field] = sample[field]

            # Flatten recursively
            recursive_samples: SamplesWithTransformations = flatten_sample(
                transformed_sample
            )
            result.extend(recursive_samples)

            # Record the transformation
            result.transformations.append(
                SampleTransformation(
                    transformation_type=transformation_type,
                    sample_before=sample,
                    sample_after=recursive_samples.samples[0],
                )
            )

    return result


def flatten_loaded_samples(loaded_samples: list[Sample]) -> SamplesWithTransformations:
    result = SamplesWithTransformations(samples=[], transformations=[])
    for sample in loaded_samples:
        flattened_sample = flatten_sample(sample)
        result.extend(flattened_sample)
    return result


samples: SamplesWithTransformations = flatten_loaded_samples(
    load_samples("basic_samples.yaml")
)


def select_few_shot_samples(
    samples: list[Sample],
    few_shot_fields: list[str],
    num_shots=3,
    ) -> list[Sample]:
    """Sample few-shot samples for the prompt."""
    few_shot_samples: list[Sample] = []

    def any_fields_repeat(sample: Sample) -> bool:
        for prompt_sample in few_shot_samples:
            for field in few_shot_fields:
                if prompt_sample[field] == sample[field]:
                    return True
        return False

    while len(few_shot_samples) < num_shots:
        candidate: Sample = np.random.choice(samples, size=1)[0]  # type: ignore
        # if any_fields_repeat(candidate):
        #     continue
        few_shot_samples.append(candidate)

    return few_shot_samples


def make_prompt(
    system_prompt: str,
    few_shot_samples: list[Sample],
    new_sample_input: dict,
    few_shot_fields: list[str],
    new_sample_fields: list[str],
) -> str:
    # Prepare the few-shot prompt string
    sample_prompts = []
    system_prompt_header = system_prompt + "\n\n"

    for sample in few_shot_samples:
        sample_prompt = system_prompt_header
        for field in few_shot_fields:
            sample_prompt += f"{field}: {sample[field]}\n"
        sample_prompts.append(sample_prompt)

    prompt = "\n".join(sample_prompts)

    # Add the question part of the prompt
    prompt += "\n" + system_prompt_header
    for field in new_sample_fields:
        prompt += f"{field}: {new_sample_input[field]}\n"
        # prompt += f"{field}: {question[field]}\n"

    return prompt


def make_prompt_with_sampled_shots(
    system_prompt: str,
    samples: list[Sample],
    new_sample_input: dict,
    common_fields: list[str],
    additional_few_shot_fields: list[str],
    num_shots=3,
) -> str:
    # Sample a question
    # samples = copy(samples)
    # question: Sample = np.random.choice(samples, size=1)[0]  # type: ignore
    # samples.remove(question)

    few_shot_fields = common_fields + additional_few_shot_fields
    few_shot_samples = select_few_shot_samples(
        samples=samples,
        few_shot_fields=few_shot_fields,
        num_shots=num_shots)

    prompt = make_prompt(
        system_prompt=system_prompt,
        few_shot_samples=few_shot_samples,
        new_sample_input=new_sample_input,
        few_shot_fields=few_shot_fields,
        new_sample_fields=common_fields,
    )
    
    return prompt
        
# np.random.seed(42)
system_prompt = "Translate the instruction to a valid bash shell command."
categories = load_categories(Path("categories.txt"))

new_sample_input = {
    "type": np.random.choice(categories, size=1)[0],
    "difficulty": np.random.choice(["easy", "medium", "hard"], size=1)[0],
}
print(colored("new_sample_input:", "green"))
print(json.dumps(new_sample_input, indent=2))

# prompt = make_prompt_with_sampled_shots(
#     system_prompt=system_prompt,
#     samples=samples.samples,
#     new_sample_input=new_sample_input,
#     common_fields=["type", "difficulty"],
#     additional_few_shot_fields=["instruction", "command"],
#     num_shots=5,
# )

print(colored("\nprompt:", "green"))
print(prompt)
client = clients["code_llama_34b"]
response = client.generate(prompt, max_generated_tokens=64, temperature=0.8, stop=[system_prompt])
print(colored("response:", "green"))
print(response.content)
#%%
from abc import abstractmethod


# class Sample:
#     category: str
#     instruction: str
#     command: str


# class SampleGenerator:
#     @abstractmethod
#     def prepare_prompt(seed_samples, question_sample) -> str:
#         pass

#     @abstractmethod
#     def extract_generated_sample(generated_text):
#         pass


transformation_prompts = {
    "clarify": "Clarify the instruction by adding more details.",
    "complicate": "Write a more complex version of the instruction.",
    "rephrase": "Rephrase the instruction while maintaining its meaning.",
    "terse": "Write a more terse version of the instruction.",
}

seed_samples = load_samples("basic_samples.yaml")
question_samples = load_samples("good_samples.yaml")


# What a sample looks like:
# {
#     "type": "images",
#     "instruction": "resize my_image.png to a width of 800 pixels",
#     "command": "convert my_image.png -resize 800x my_image_resized.png",
#     "transformations": [
#         {
#             "transformation_type": "complicate",
#             "instruction": "Find all JPEG and PNG files in the current directory and its subdirectories, resize them to a width of 800 pixels while maintaining aspect ratio, and save them with a \"_small\" suffix before the file extension.",
#             "command": "find . -type f \\( -iname \"*.jpg\" -o -iname \"*.jpeg\" -o -iname \"*.png\" \\) -exec convert {} -resize 800x \\>{}_small\\> \\;",
#         },
#     ]
# },


def get_evolution_prompt(
        labelled_samples: list[dict],
        question_sample: dict,
        transformation_type: str):
    assert transformation_type in transformation_prompts
    shots = 3

    # find all examples that have the transformation type
    # TODO do this hierarchically
    transformation_type_samples = []
    for sample in labelled_samples:
        if "transformations" in sample:
            for transformation in sample["transformations"]:
                if transformation["transformation_type"] == transformation_type:
                    transformation_type_samples.append({
                        "before": {
                            "instruction": sample["instruction"],
                            "command": sample["command"],
                        },
                        "after": {
                            "instruction": transformation["instruction"],
                            "command": transformation["command"],
                        },
                    })
                    break

    assert len(transformation_type_samples) >= shots

    prompt_samples: list[dict] = np.random.choice(transformation_type_samples, shots, replace=False)  # type: ignore
    sample_prompt = ""

    for sample in prompt_samples:
#         sample_prompt += f"""\

# {transformation_prompts[transformation_type]}

# original instruction: {sample["before"]["instruction"]}
# transformed instruction: {sample["after"]["instruction"]}
# """
        sample_prompt += f"""\

{transformation_prompts[transformation_type]}

original:
- instruction: {sample["before"]["instruction"]}
- command: {sample["before"]["command"]}
new:
- instruction: {sample["after"]["instruction"]}
- command: {sample["after"]["command"]}
"""

    sample_prompt += f"""\

{transformation_prompts[transformation_type]}

original:
- instruction: {question_sample["instruction"]}
- command: {question_sample["command"]}
new:
"""

    return sample_prompt


def evolve_a_sample(client: LlamaCppClient, seed_samples: list[dict], question_samples: list[dict], transformation_type: str):
    question_sample = np.random.choice(question_samples, 1)[0]  # type: ignore
    prompt = get_evolution_prompt(seed_samples, question_sample, transformation_type=transformation_type)
    # print(colored("prompt:", "green"))
    # print("'''" + prompt + "'''")
    response = client.generate(prompt, max_generated_tokens=64, temperature=0.8, stop=[transformation_prompts[transformation_type]])
    print(colored("question:", "green"))
    print("instruction:", question_sample["instruction"])
    print("command:", question_sample["command"])
    print(colored("response:", "green"))
    print(response.content)
    return response.content


for _ in range(10):
    answer = evolve_a_sample(clients["code_llama_34b"], seed_samples, question_samples, transformation_type="complicate")
    # print(answer)

#%% md
## Critique hallucinated examples (failed)
#%%
from typing import Optional

SYSTEM_PROMPT = """\
The following is an instruction translated to a shell command.
Does the command uses files, URLs, or other objects that were not specified in the insturction?
Specify which part was hallucinated and then answer True or False."""

hallucination_samples = [
    {
        "instruction": "sync the files in /home/<USER>",
        "command": "rsync -avz user@server:/home/<USER>/remote/path",
        "hallucinated": "/remote/path",
        "hallucinated_detailed": "'/home/<USER>' is specified and 'server' is specified, but target path '/remote/path' is not specified",
        "hallucinated_detailed2": "'rsync' is a general command, '-avz' is a flag of the command, 'user' is a username which was not specified in the instruction",
        "answer": True
    },
    {
        "instruction": "show the average number of users logged in per day",
        "command": "SELECT AVG(logins) AS logins FROM user_info;",
        "hallucinated": "logins",
        "hallucinated_detailed": "column 'logins' is not specified and table 'user_info' is not specified",
        "hallucinated_detailed2": "'AVG' is a function, 'logins' is a column name which was not specified in the instruction",
        "answer": True
    },
    {
        "instruction": "how much disk space",
        "command": "df -h",
        "hallucinated": "",
        "hallucinated_detailed": "no objects were used",
        "hallucinated_detailed2": "'df' is a general command, '-h' is a flag of the command",
        "answer": False
    },
    {
        "instruction": "show changes since commit 'foo'",
        "command": "git diff foo HEAD",
        "hallucinated": "",
        "hallucinated_detailed": "commit 'foo' is specified",
        "hallucinated_detailed2": "'git diff' is a general command, 'foo' is a commit which was specified in the instruction, 'HEAD' is a general git term",
        "answer": False
    },
    {
        "instruction": "move all .txt files from folder foo/bar to folder /tmp",
        "command": "mv foo/bar/*.txt /tmp/",
        "hallucinated": "",
        "hallucinated_detailed": "folder 'foo/bar' is specified, '/tmp' is specified, and file extension '.txt' is specified",
        "hallucinated_detailed2": "'mv' is a general command, 'foo/bar' folder was specified in the instruction, '*.txt' was specified in the instruction, '/tmp/' was specified in the instruction",
        "answer": False
    },
    {
        "instruction": "connect to *******",
        "command": "ftp *******",
        "hallucinated": "ftp",
        "hallucinated_detailed": "address '*******' was specified but the protocol 'ftp' was not specified",
        "hallucinated_detailed2": "the command 'ftp' refers to the ftp protocol which was not specified in the instruction",
        "answer": True
    },
    {
        "instruction": "list all lines with the word \"the\"",
        "command": "grep -i the file.txt",
        "hallucinated": "file.txt",
        "hallucinated_detailed": "the string 'the' was specified but filename 'file.txt' was not specified",
        "hallucinated_detailed2": "'grep -i' is a general command, 'the' was specified in the instruction, 'file.txt' was not specified in the instruction",
        "answer": True,
    },
]

validation_hallucination_samples = [
    {
        "instruction": "send <NAME_EMAIL> with subject test and body testing",
        "command": "echo 'testing' | mutt -s \"test\" -- <EMAIL>",
        "hallucinated": "",
        "answer": False,
    },
    {
        "instruction": "show database names",
        "command": "mysql --execute='show databases;'",
        "hallucinated": "",
        "answer": False,
    },
    {
        "instruction": "copy everything from directory1/directory2/file.txt to /home/<USER>",
        "command": "rsync -avzr --info=progress2 /tmp/directory1/directory2/file.txt /home/<USER>",
        "hallucinated": "/tmp/",
        "answer": True,
    },
    {
        "instruction": "add a red square on the image",
        "command": "convert -draw 'color 10,20,30,40 rectangle 0,0,640,480' input.jpg output.jpg",
        "hallucinated": "input.jpg",
        "answer": True,
    },
    {
        "instruction": "scan the network to find all running ssh servers",
        "command": "nmap -p 22 --open -n -oG -",
        "hallucinated": "",
        "answer": False,
    },
    {
        "instruction": "send an email to your friend",
        "command": 'echo "Hello, world!" | mail -s "Welcome" <EMAIL>',
        "hallucinated": "<EMAIL>",
        "answer": True,
    },
    {
        "instruction": "list running containers",
        "command": "docker ps",
        "hallucinated": "",
        "answer": False,
    },
    {
        "instruction": "compress file.txt with gzip",
        "command": "gzip -c file.txt > file.gz",
        "hallucinated": "",
        "answer": False,
    },
    {
        "instruction": "list all files in S3 bucket",
        "command": "aws s3 ls <bucket-name>",
        "hallucinated": "<bucket-name>",
        "answer": True,
    },
    {
        "instruction": "download file.txt from server",
        "command": "curl -O ftp://server/file.txt",
        "hallucinated": "ftp",
        "answer": True,
    },
    {
        "instruction": "count number of lines in file.txt",
        "command": "cat file.txt | wc -l",
        "hallucinated": "",
        "answer": False,
    },
    {
        "instruction": "what is my current working directory",
        "command": "pwd",
        "hallucinated": "",
        "answer": False,
    },
    {
        "instruction": "upload file.txt to server-1",
        "command": "scp file.txt root@server-1:/root/file.txt",
        "hallucinated": "root",
        "answer": True,
    },
    {
        "instruction": "list all the available files",
        "command": "wget --mirror --convert-links --adjust-extension -e robots=off -P /home/<USER>//files.katacoda.com/http/example",
        "hallucinated": "/home/<USER>",
        "answer": False,
    },
    {
        "instruction": "send <NAME_EMAIL>",
        "command": 'mail -s "Message from test" <EMAIL>',
        "hallucinated": "",
        "answer": False,
    },
    {
        "instruction": "download file.txt",
        "command": "ftpget /home/<USER>/file.txt",
        "hallucinated": "/home/<USER>",
        "answer": True,
    },
    {
        "instruction": "create a zip file",
        "command": "zip -r archive.zip folder/",
        "hallucinated": "folder/",
        "answer": True,
    },
    {
        "instruction": "send an email to alice",
        "command": 'echo "Hello" | mail -s "Hi Alice" <EMAIL>',
        "hallucinated": "example.com",
        "answer": True,
    },
    {
        "instruction": "get status of pods",
        "command": "kubectl get pods",
        "hallucinated": "",
        "answer": False,
    },
    {
        "instruction": "download the file.txt",
        "command": "wget http://localhost/file.txt",
        "hallucinated": "localhost",
        "answer": False,
    },
]

def make_single_hallucination_critique_prompt(sample, detailed=True):
    prompt = f"""\
{SYSTEM_PROMPT}

instruction: {sample["instruction"]}
command: {sample["command"]}
"""

    if "hallucinated" in sample and "answer" in sample:
        if detailed:
            hallucination = sample["hallucinated_detailed2"]
        else:
            hallucination = sample["hallucinated"]
        prompt += f"""\
hallucinated: {hallucination}
answer: {sample["answer"]}
"""

    return prompt


def get_model_answer_with_random_prompt(client, val_sample: dict) -> Optional[bool]:
    num_shots = 4
    samples_for_prompt = np.random.choice(hallucination_samples, size=num_shots)
    np.random.shuffle(samples_for_prompt)

    critique_prompt = "\n".join([make_single_hallucination_critique_prompt(sample) for sample in samples_for_prompt])

    val_sample_for_prompt = dict(val_sample)
    del val_sample_for_prompt["hallucinated"]
    del val_sample_for_prompt["answer"]
    prompt = critique_prompt + "\n" + make_single_hallucination_critique_prompt(val_sample_for_prompt)

    # print(colored("prompt:", "blue"))
    # print("'''" + prompt + "'''")

    print(colored("\nsample:", "blue"))
    print(make_single_hallucination_critique_prompt(samples_for_prompt[0]) + "\n" + make_single_hallucination_critique_prompt(val_sample_for_prompt))

    # print("val sample:")
    # print(json.dumps(val_sample, indent=2))

    stop_string = "The following is"
    assert SYSTEM_PROMPT.startswith(stop_string)

    response = client.generate(prompt, max_generated_tokens=64, temperature=0.0, stop=[stop_string])
    # print(f"answer generated by {response.model}")
    print(colored(f"generated by {response.model}:", "blue"))
    print(response.content)

    # parse the response
    m = re.match(r"^hallucinated:([^\n]*)\nanswer:([^\n]*)", response.content)
    if m:
        # hallucinated = m.group(1).strip()
        answer = m.group(2).strip()
        bool_answer: bool = answer.lower() == "true"
        # print(colored("model generated:", "green"))
        # print("hallucinated:", hallucinated)
        # print(f"answer: '''{answer}''' bool={bool_answer}")
        return bool_answer
    else:
        print("Invalid response:")
        print("'''" + response.content + "'''")
        return None
        # raise ValueError("Invalid response")


majority_voting_trials = 3
# majority_trials = 1
assert majority_voting_trials % 2 == 1

# client = clients["llama_70b"]
# client = clients["code_llama_34b"]

results = {}

for model_name, client in clients.items():
    if "instruct" in model_name:
        continue

    num_correct = 0
    for i, val_sample in enumerate(validation_hallucination_samples):
        print (f"\n=== model: {model_name} sample: {i}/{len(validation_hallucination_samples)} ===" + "=" * 80)
        model_answers = []
        while len(model_answers) < majority_voting_trials:
            model_answer = get_model_answer_with_random_prompt(client, val_sample)
            if model_answer is not None:
                model_answers.append(model_answer)
        avg_answer = np.mean(model_answers)
        model_answer = avg_answer > 0.5
        # print(f"model_answers={model_answers} avg_answer={avg_answer} model_answer={model_answer}")

        correct = (model_answer == val_sample["answer"])
        num_correct += int(correct)
        print("correct?", correct)

    print(f"num correct: {num_correct}")
    acc = num_correct / len(validation_hallucination_samples) * 100
    print(f"accuracy: {acc:.1f}%")
    results[model_name] = acc

print("All accuracies:")
print(json.dumps(results, indent=2))
#%% md
## Generate categories (succeeded)
#%%
shell_categories_prompt = (
    "A list of categories of things that users typically do in a shell terminal."
)

shell_seed_categories = [
    "file-manipulation",
    "systems",
    "docker",
    "process-management",
    "database-queries",
    "git",
    "k8s",
    "vim",
    "ssh",
    "networking",
    "file-transfer",
    "environment",
    "irc",
]

code_edit_categories_prompt = (
    "A list of categories of ways developers typically edit their code."
)

code_edit_seed_categories = [
    "formatting",
    "linting",
    "refactoring",
    "documenting",
    "debugging",
    "fixing",
    "performance",
    "optimization",
    "variable-renaming",
    "structure-data",
    "i18n",
    "translation",
]


def generate_categories_one_round(
    client: LlamaCppClient,
    seed_categories: list[str],
    categories_prompt: str,
    min_number: int,
    blacklist: list[str],
) -> list[str]:
    categories = []
    num_shots = 5
    item_prefix = "\n- "
    while len(categories) < min_number:
        prompt_categories = np.random.choice(seed_categories, num_shots, replace=False)
        prompt = categories_prompt + "\n"
        prompt += item_prefix + item_prefix.join(prompt_categories) + item_prefix
        # print(f"'''{prompt}'''")
        response = client.generate(prompt, max_generated_tokens=64, temperature=0.8)
        answers = response.content.split(item_prefix)

        def is_good_answer(answer):
            if not answer:
                return False
            if answer in seed_categories or answer in categories:
                return False
            if answer in blacklist:
                return False
            if "\n" in answer:
                return False
            if not re.match(r"^[a-zA-Z][a-zA-Z-]*$", answer):
                return False
            return True

        for answer in answers:
            answer = answer.strip()
            if is_good_answer(answer):
                categories.append(answer)
    return categories


def generate_categories(
    client: LlamaCppClient,
    seed_categories: list[str],
    categories_prompt: str,
    target_num_categories: int,
    blacklist: list[str] = [],
) -> list[str]:
    num_categories_per_round = 3
    categories = list(seed_categories)

    while len(categories) < target_num_categories:
        print(f"=== starting round with {len(categories)} categories ===")
        new_categories = generate_categories_one_round(
            client, categories, categories_prompt, num_categories_per_round, blacklist
        )
        print(f"generated {len(new_categories)} categories:")
        for cat in new_categories:
            print(f"\t{cat}")
        categories.extend(new_categories)

    return categories


def save_categories(path: str, categories: list[str]):
    with Path(path).open("w", encoding="utf-8") as file:
        file.write("\n".join(categories) + "\n")
    print(f"{len(categories)} categories saved to {path}")


# generated_categories = generate_categories(
#     clients["code_llama_34b"],
#     shell_seed_categories,
#     shell_categories_prompt,
#     target_num_categories=100,
#     blacklist=["irc"],
#     )
# save_categories("shell_categories_draft.txt", generated_categories)

generated_categories = generate_categories(
    clients["code_llama_34b"],
    code_edit_seed_categories,
    code_edit_categories_prompt,
    target_num_categories=80,
    blacklist=["syntax-highlighting"],
)
save_categories("code_edit_categories_draft.txt", generated_categories)
#%% md
## Directly ask DeepSeek Coder Instruct
#%%
client = clients["deepseek-coder-instruct-33b"]

SYSTEM_PROMPT = "Convert the following instruction to a one-line bash command:"

num_prompt_samples = 3
orig_prompt = ""
for sample in samples[:num_prompt_samples]:
    orig_prompt += f"""\

{SYSTEM_PROMPT}
instruction: {sample["instruction"]}
command: {sample["command"]}
"""

orig_prompt += f"\n{SYSTEM_PROMPT}\n"

instruction = "list all tables in personnel.sqlite"
prompt = orig_prompt + f"""\
instruction: {instruction}
"""
response = client.generate(prompt, max_generated_tokens=64, temperature=0., stop=[])
print(response.content)


# val_samples = samples[num_prompt_samples:]

# for sample in val_samples:
#     preamble = orig_prompt + f"""\
# instruction: {sample["instruction"]}
# """
#     # print(prompt)
#     response = client.generate(preamble, max_generated_tokens=64, temperature=0., stop=[])
#     print(json.dumps(sample, indent=2))
#     print("deepseek command:", response.content)
#%%
client = clients["deepseek-coder-instruct-33b"]

preamble = f"""\
The following is an instruction translated to a shell command.
Does the command uses files, URLs, or other objects that were not specified in the instruction?
Specify which part was not specified and then answer "yes" or "no".

instruction: sync the files in /home/<USER>
command: rsync -avz user@server:/home/<USER>/remote/path,
hallucination: user@server was not specified in the instruction
answer: yes

The following is an instruction translated to a shell command.
Does the command uses files, URLs, or other objects that were not specified in the instruction?
Specify which part was not specified and then answer "Yes" or "No".

instruction: count number of lines in file.txt
command: cat file.txt | wc -l
hallucination: file.txt was specified in the instruction
answer: no

The following is an instruction translated to a shell command.
Does the command uses files, URLs, or other objects that were not specified in the instruction?
Specify which part was not specified and then answer "Yes" or "No".

instruction: upload file.txt to server-1
command: scp file.txt root@server-1:/root/file.txt
hallucinated: root user was not specified
answer: yes

The following is an instruction translated to a shell command.
Does the command uses files, URLs, or other objects that were not specified in the instruction?
Specify which part was not specified and then answer "yes" or "no".
"""


# instruction: what is my current working directory
# command: pwd
# hallucinated: it uses the correct command
# answer: no

response = client.generate(preamble, max_generated_tokens=128)
print(response.content)
#%% md
## First attempt at code edits
#%%
# client = clients["code_llama_34b"]
# client = clients["deepseek-coder-instruct-33b"]
client = clients["llama_70b"]

prompt = """\
Edit the code according to the following instruction.

type: editing
instruction: change double quote to single
code:
```python
    with (Path(__file__).parent / "language_extensions.json").open(
        "r", encoding="utf8"
    ) as file:
        language_data = json.load(file)
        supported_languages = [
            {
                "name": lang["name"],
                "vscode_name": lang["vscode_name"],
                "extensions": lang["extensions"],
            }
            for lang in language_data
            if "vscode_name" in lang and is_language_supported(lang)
        ]
```
edited code:
```python
    with (Path(__file__).parent / 'language_extensions.json').open(
        'r', encoding='utf8'
    ) as file:
        language_data = json.load(file)
        supported_languages = [
            {
                'name': lang['name'],
                'vscode_name': lang['vscode_name'],
                'extensions': lang['extensions'],
            }
            for lang in language_data
            if 'vscode_name' in lang and is_language_supported(lang)
        ]
```

Edit the code according to the following instruction.

type: formatting
instruction: fix the formatting
code:
```python
def _get_generation_options(
    max_generated_tokens: int,
    request_content: Union[CompletionRequest, ReplacementRequest],
):
    def get(value, default):
        if value is None:
            return default
        return value

    temperature = get(
        request_content.temperature, default=GenerationOptions.temperature
    )
    top_k = get(request_content.top_k, default=GenerationOptions.top_k)
    top_p = get(request_content.top_p, default=GenerationOptions.top_p)
    max_tokens = get(request_content.max_tokens, default=max_generated_tokens)
```
edited code:
```python
def _get_generation_options( max_generated_tokens: int, request_content: Union[CompletionRequest, ReplacementRequest],
):
    def get(value, default):
        if value is None:
            return default
        return value

    temperature = get( request_content.temperature,
                            default=GenerationOptions.temperature
    )
    top_k = get(request_content.top_k, 
        default=GenerationOptions.top_k)
    top_p = get(request_content.top_p, default=GenerationOptions.top_p)
    max_tokens = get(request_content.max_tokens, default=max_generated_tokens)
```

Edit the code according to the following instruction.

type: refactoring
instruction: move the main logic to a function
code:
```python
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--storage_path",
        type=str,
        default=None,
        help="path to the file store",
    )
    args = parser.parse_args()

    file_store = FileStore(Path(args.storage_path))
    for doc in file_store.get_files():
        print(doc.path)
        print(doc.text)
```
edited code:
```python
def print_files(storage_path: str):
    file_store = FileStore(Path(storage_path))
    for doc in file_store.get_files():
        print(doc.path)
        print(doc.text)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--storage_path",
        type=str,
        default=None,
        help="path to the file store",
    )
    args = parser.parse_args()
    print_files(args.storage_path)
```

Edit the code according to the following instruction.

type: bugfix
instruction: fix the bug
code:
```python
def factorial(n):
    return n * factorial(n-1)
```
edited code:
```python
def factorial(n):
    if n == 0:
        return 1
    return n * factorial(n - 1)

Edit the code according to the following instruction.

type: translation
instruction: turn it into valid python
```python
import argparse

def main():
    parse --text flag
```
edited code:
```python
import argparse

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--text",
        type=str,
        default="Hello, World!",
        help="text to tokenize",
    )
    args = parser.parse_args()
```

Edit the code according to the following instruction.

type: format
instruction:"""

response = client.generate(prompt=prompt, max_generated_tokens=1024, temperature=0.0, stop=[])
print(response.content)
#%% md
## Scratchpad and old evol-instruct code
#%%
few_shot_preamble = """\
Please increase the difficulty of the given programming test question a bit.

You can increase the difficulty using, but not limited to, the following methods:
Add new constraints and requirements to the original problem, adding approximately 10 additional words.

Original question:
Create an array of length 5 which contains all even numbers between 1 and 10.

More difficult question:
Create an array of length k which contains all the even numbers between 1 and 2k.


Please increase the difficulty of the given programming test question a bit.

You can increase the difficulty using, but not limited to, the following methods:
If the original problem can be solved with only a few logical steps, please add more reasoning steps.

Original question:
Create an array of length 15 containing numbers divisible by 3 up to 45.

More difficult question:
Create an array of length 15 containing numbers divisible by 3 or 4 up to 60.
"""

preamble = get_evol_prompt(code_alpaca_data[5]["instruction"])
print("Prompt:\n'''")
print(preamble, end="")
print("'''\n")

full_prompt = few_shot_preamble + "\n\n" + preamble

max_generated = 64

for name, client in clients.items():
    response = client.generate(preamble, max_generated)
    # print(json.dumps(response.full_response, indent=2))
    print(f"Model: {response.model}")
    print(f"Generated:\n{response.content}")
    print("\n")
#%%

with Path("/mnt/efs/augment/user/guy/code_alpaca_20k.json").open("r", encoding="utf-8") as file:
    code_alpaca_data = json.load(file)

def get_evol_prompt(question):
    methods = [
        "Add new constraints and requirements to the original problem, adding approximately 10 additional words.",
        "Replace a commonly used requirement in the programming task with a less common and more specific one.",
        "If the original problem can be solved with only a few logical steps, please add more reasoning steps.",
        # "Provide a piece of erroneous code as a reference to increase misdirection.",
        "Propose more strict space complexity requirements.",
    ]
    method = np.random.choice(methods)
    prompt = f"""\
Please increase the difficulty of the given programming test question a bit.

You can increase the difficulty using, but not limited to, the following methods:
{method}

Original question:
{question}

More difficult question:
"""
    return prompt

# prompt = "def hello():"
# max_generated = 64

# for name, client in clients.items():
#     response = client.generate(prompt, max_generated)
#     # print(json.dumps(response.full_response, indent=2))
#     print("Model:", response.model)
#     print("Generated:", response.content)
#     print("\n")