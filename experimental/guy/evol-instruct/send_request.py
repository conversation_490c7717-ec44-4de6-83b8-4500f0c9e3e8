"""Send a request to a llama.cpp server."""

import argparse
from pathlib import Path

from utils import LlamaCppClient


def main():
    parser = argparse.ArgumentParser(description="")
    parser.add_argument("--prompt", type=Path, required=True)
    parser.add_argument("--client", type=str, default="deepseek-coder-instruct-33b")
    args = parser.parse_args()

    clients = {
        "code_llama_34b": LlamaCppClient(address="**************:8080"),
        "llama_70b": LlamaCppClient(address="**************:8081"),
        "deepseek-coder-base-33b": LlamaCppClient(address="**************:8082"),
        "deepseek-coder-instruct-33b": LlamaCppClient(
            address="**************:8083",
            prompt_template="You are an AI programming assistant, utilizing the Deepseek Coder model, developed by Deepseek Company, and you only answer questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.\n### Instruction: {prompt}\n### Response:\n",
            timeout=120,
        ),
    }

    client = clients[args.client]
    prompt = Path(args.prompt).read_text(encoding="utf8")
    print(prompt)
    print("\nGenerating ...\n")
    response = client.generate(
        prompt=prompt, max_generated_tokens=4096, temperature=0.0, stop=[]
    )
    print(response.content)


if __name__ == "__main__":
    main()
