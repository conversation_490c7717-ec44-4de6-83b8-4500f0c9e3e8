"""A simple client for sending code-edit requests."""

import argparse
import json
import uuid

import requests


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--url",
        default="https://igor-a100-i.tenant-augment-eng.las1.ingress.coreweave.cloud/5005/edit",
        help="code edit endpoint",
    )
    parser.add_argument("--token", required=True, help="authentication token")
    args = parser.parse_args()

    prefix = "# prefix\n"
    suffix = "# suffix\n"
    middle = "x = 1\n"

    payload = {
        "instruction": "rename x to y",
        "selected_code": middle,
        "prefix": prefix,
        "suffix": suffix,
        "top_k": 5,
        "top_p": 0.1,
        "temperature": 0.5,
        "blob_name": "",
        "request_id": str(uuid.uuid4()),
        "session_id": str(uuid.uuid4()),
        "completion_url": args.url,
        "prefix_begin": 0,
        "selection_begin": len(prefix),
        "selection_end": len(prefix) + len(middle),
        "suffix_end": len(prefix) + len(middle) + len(suffix),
        "lang": "python",
        "lines_in_prefix_suffix": 9999,
        "path": "scratch.py",
        "blobs": {"checkpoint_id": None, "added_blobs": [], "deleted_blobs": []},
    }

    print("Payload:")
    print(json.dumps(payload, indent=2))

    headers = {
        "Authorization": f"Bearer {args.token}",
        "Content-Type": "application/json",
    }

    response = requests.post(args.url, data=json.dumps(payload), headers=headers)

    print("HTTP status code:", response.status_code)
    print("HTTP response:")
    print(json.dumps(response.json(), indent=2))


if __name__ == "__main__":
    main()
