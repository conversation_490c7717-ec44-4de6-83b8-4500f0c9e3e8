## Using G<PERSON>hit<PERSON> for version control

We use <PERSON><PERSON><PERSON><PERSON> for version control on top of git. <PERSON><PERSON><PERSON><PERSON> helps manage git branches and PRs.
Graphite maintains stacks of PRs: changes to a PR automatically cause rebases on higher PRs in the stack,
saving a lot manual effort.

Some git commands stay the same, including:

git add
git status

The Graphite command-line command is `gt`. You can replace all git commands with gt commands: `git add` becomes `gt add` etc.

For the following commands, always call <PERSON><PERSON>hit<PERSON> instead of calling git.

Committing:
- Add files with `git add`
- Commit with `gt modify` (instead of `git commit`)
- Sometimes the pre-commit hook will edit files. They can then be added with `git add` and committed with `gt modify`
- By default, commit to the current branch instead of creating a new branch

Pulling: Instead of `git pull` use `gt sync`. This will "restack" the rest of the stack. If this fails, <PERSON><PERSON><PERSON><PERSON> will say that an explicit `gt restack` is needed to handle errors.

Creating a branch:
- Add your changes with `git add` and then use `gt create branch-name -m commit-message`. Make sure to supply a short commit message in the command line. Don't use `git checkout -b branch-name`.

Pushing: In a branch, instead of `git push` use `gt submit`. If there's no associated PR, <PERSON><PERSON><PERSON><PERSON> will create one.

Create a PR: Once a branch is created, use `gt submit` to create the PR.

Switching branches: Instead of `git checkout branch-name` use `gt checkout branch-name`.

To see the PR stack use `gt ls`.

To switch between branches:
`gt up` and `gt down` to move up and down the stack.
`gt top` and `gt bottom` to go to the top/bottom of the stack.

To rename a branch use `gt rename`.

### Branch names

We use the convention `$USER-branch-name` to name branches, namely every branch name starts with the username.
