"""Tests for NotionPageTool."""

import json
import types
from pathlib import Path
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest
import pytest_asyncio

from experimental.guy.agent_qa.notion_tools import NotionPageTool
from research.agents.tools import ToolCallLogger


@pytest.fixture
def cache_dir(tmp_path):
    """Create a temporary cache directory."""
    return tmp_path / "cache"


@pytest.fixture
def token_file(cache_dir):
    """Create a token file with a test token."""
    cache_dir.mkdir(parents=True, exist_ok=True)
    token_file = cache_dir / "notion_api_token"
    token_file.write_text("test-token")
    return token_file


@pytest.fixture
def tool(cache_dir, token_file, mock_notion_client):
    """Create a NotionPageTool instance."""
    writer = NotionPageTool(
        ToolCallLogger(),
        cache_dir,
        allowed_pages_to_modify=["parent-123", "page-123"],  # Add common test page IDs
        allow_create_pages=True,  # Allow creation for most tests
    )
    # Properly bind the mock method to the instance
    writer._resolve_page_id = types.MethodType(mock_resolve_page_id, writer)
    # Mock the _ensure_client method to return our mock client
    writer._ensure_client = lambda: mock_notion_client
    return writer


@pytest_asyncio.fixture
async def mock_notion_client():
    """Create a mock Notion client."""
    client_instance = MagicMock()

    # Mock the pages.create method
    client_instance.pages.create = AsyncMock(
        return_value={"id": "new-page-123", "url": "https://notion.so/new-page-123"}
    )

    # Mock the blocks.children.append method
    client_instance.blocks.children.append = AsyncMock(return_value={})

    # Mock the pages.update method
    client_instance.pages.update = AsyncMock(return_value={})

    # Mock the blocks.delete method
    client_instance.blocks.delete = AsyncMock(return_value={})

    # Mock the blocks.children.list method with next_cursor
    client_instance.blocks.children.list = AsyncMock(
        return_value={"results": [], "has_more": False, "next_cursor": None}
    )

    # Mock the pages.retrieve method
    client_instance.pages.retrieve = AsyncMock(
        return_value={
            "id": "page-123",
            "url": "https://notion.so/page-123",
            "properties": {
                "title": {"title": [{"plain_text": "Test Page"}]},
            },
        }
    )

    # Mock the search method
    client_instance.search = AsyncMock(
        return_value={
            "results": [
                {
                    "id": "page-123",
                    "url": "https://notion.so/page-123",
                    "properties": {
                        "title": {"title": [{"plain_text": "Test Page"}]},
                    },
                }
            ]
        }
    )

    return client_instance


def mock_resolve_page_id(self, page_id_or_path: str) -> str:
    """Mock the _resolve_page_id method."""
    if page_id_or_path == "parent-123":
        return "parent-123"
    elif page_id_or_path == "page-123":
        return "page-123"
    elif page_id_or_path == "new-page-123":
        return "new-page-123"
    elif page_id_or_path == "Engineering/Documentation/Setup Guide":
        return "page-123"
    elif page_id_or_path == "unauthorized-123":
        return "unauthorized-123"  # Return unauthorized page ID
    return ""


@pytest.mark.asyncio
async def test_create_page(tool, mock_notion_client):
    """Test creating a new page."""
    result = await tool.run_impl_async(
        {
            "mode": "create",
            "page_id_or_path": "parent-123",
            "title": "Test Page",
            "content": "# Test Page\n\nThis is a test page.",
        }
    )

    assert "Created new page" in result.tool_output
    assert "new-page-123" in result.tool_output


@pytest.mark.asyncio
async def test_update_page(tool, mock_notion_client):
    """Test updating an existing page."""
    result = await tool.run_impl_async(
        {
            "mode": "update",
            "page_id_or_path": "page-123",
            "content": "# Updated Page\n\nThis page has been updated.",
        }
    )

    assert "Updated page" in result.tool_output
    assert "page-123" in result.tool_output


@pytest.mark.asyncio
async def test_read_page(tool, mock_notion_client):
    """Test reading a page."""
    result = await tool.run_impl_async(
        {
            "mode": "read",
            "page_id_or_path": "page-123",
        }
    )

    assert "Test Page" in result.tool_output


@pytest.mark.asyncio
async def test_append_to_page(tool, mock_notion_client):
    """Test appending content to a page."""
    result = await tool.run_impl_async(
        {
            "mode": "append",
            "page_id_or_path": "page-123",
            "content": "This is new content to append.",
        }
    )

    assert "Successfully appended content" in result.tool_output
    assert "page-123" in result.tool_result_message


@pytest.mark.asyncio
async def test_search_pages(tool, mock_notion_client):
    """Test searching pages."""
    # Mock the search method
    mock_notion_client.search = AsyncMock(
        return_value={
            "results": [
                {
                    "id": "page-123",
                    "url": "https://notion.so/page-123",
                    "properties": {
                        "title": {"title": [{"plain_text": "Test Page"}]},
                    },
                }
            ]
        }
    )

    result = await tool.run_impl_async(
        {
            "mode": "search",
            "query": "test",
        }
    )

    assert "Test Page" in result.tool_output
    assert "page-123" in result.tool_output


@pytest.mark.asyncio
async def test_invalid_mode(tool):
    """Test invalid mode."""
    result = await tool.run_impl_async(
        {
            "mode": "invalid",
            "page_id_or_path": "page-123",
        }
    )

    assert "Error: invalid mode" in result.tool_output


@pytest.mark.asyncio
async def test_page_not_found(tool, mock_notion_client):
    """Test reading a non-existent page."""
    # Mock the pages.retrieve method to raise an error
    mock_notion_client.pages.retrieve = AsyncMock(
        side_effect=Exception("Could not find page")
    )

    result = await tool.run_impl_async(
        {
            "mode": "read",
            "page_id_or_path": "nonexistent-123",
        }
    )

    assert "Error" in result.tool_output
    assert "Could not find" in result.tool_output


@pytest.mark.asyncio
async def test_unauthorized_modification(tool):
    """Test modifying a page without permission."""
    # Create a new tool instance with no allowed pages
    restricted_tool = NotionPageTool(
        ToolCallLogger(),
        tool.cache_dir,
        allowed_pages_to_modify=[],  # No pages allowed
        allow_create_pages=False,
    )
    restricted_tool._resolve_page_id = types.MethodType(
        mock_resolve_page_id, restricted_tool
    )
    restricted_tool._ensure_client = lambda: tool._ensure_client()

    result = await restricted_tool.run_impl_async(
        {
            "mode": "update",
            "page_id_or_path": "unauthorized-123",
            "content": "This should fail.",
        }
    )

    assert "Error" in result.tool_output
    assert "Not allowed to modify" in result.tool_output


@pytest.mark.asyncio
async def test_create_without_permission(tool):
    """Test creating a page when creation is not allowed."""
    # Create a new tool instance with create_pages=False
    restricted_tool = NotionPageTool(
        ToolCallLogger(),
        tool.cache_dir,
        allowed_pages_to_modify=["parent-123"],
        allow_create_pages=False,
    )
    restricted_tool._resolve_page_id = types.MethodType(
        mock_resolve_page_id, restricted_tool
    )

    result = await restricted_tool.run_impl_async(
        {
            "mode": "create",
            "page_id_or_path": "parent-123",
            "title": "Test Page",
            "content": "This should fail.",
        }
    )

    assert "Error" in result.tool_output
    assert "Creating new pages is not allowed" in result.tool_output


@pytest.mark.asyncio
async def test_missing_required_fields(tool):
    """Test missing required fields."""
    # Test create mode without title
    result = await tool.run_impl_async(
        {
            "mode": "create",
            "page_id_or_path": "parent-123",
            "content": "Missing title.",
        }
    )

    assert "Error" in result.tool_output
    assert "title is required" in result.tool_output

    # Test create mode without content
    result = await tool.run_impl_async(
        {
            "mode": "create",
            "page_id_or_path": "parent-123",
            "title": "Test Page",
        }
    )

    assert "Error" in result.tool_output
    assert "content is required" in result.tool_output


@pytest.mark.asyncio
async def test_path_resolution(tool, mock_notion_client):
    """Test resolving page by path."""
    result = await tool.run_impl_async(
        {
            "mode": "read",
            "page_id_or_path": "Engineering/Documentation/Setup Guide",
        }
    )

    assert "Test Page" in result.tool_output
    assert "page-123" in result.tool_result_message
