"""Tests for the StateManager class."""

import os
import pickle
import tempfile
import unittest
from pathlib import Path

from experimental.guy.agent_qa.state_manager import StateManager


class StateManagerTest(unittest.TestCase):
    """Tests for the StateManager class."""

    def setUp(self):
        """Set up a temporary directory for test files."""
        self.temp_dir = tempfile.TemporaryDirectory()
        self.state_file = Path(self.temp_dir.name) / "test_state.pkl"

    def tearDown(self):
        """Clean up temporary directory."""
        self.temp_dir.cleanup()

    def test_init_without_file(self):
        """Test initializing without a state file."""
        manager = StateManager()
        self.assertEqual(manager.get_all(), {})

    def test_init_with_nonexistent_file(self):
        """Test initializing with a non-existent state file."""
        manager = StateManager(self.state_file)
        self.assertEqual(manager.get_all(), {})

    def test_init_with_existing_file(self):
        """Test initializing with an existing state file."""
        # Create a state file
        initial_state = {"key1": "value1", "key2": 42}
        with open(self.state_file, "wb") as f:
            pickle.dump(initial_state, f)

        # Initialize with the existing file
        manager = StateManager(self.state_file)
        self.assertEqual(manager.get_all(), initial_state)

    def test_get_existing_key(self):
        """Test getting an existing key."""
        manager = StateManager()
        manager.update("key", "value")
        self.assertEqual(manager.get("key", "default"), "value")

    def test_get_nonexistent_key(self):
        """Test getting a non-existent key."""
        manager = StateManager()
        self.assertEqual(manager.get("nonexistent", "default"), "default")

    def test_update(self):
        """Test updating a state entry."""
        manager = StateManager(self.state_file)
        manager.update("key", "value")
        self.assertEqual(manager.get("key", None), "value")

        # Check that the state was saved to the file
        with open(self.state_file, "rb") as f:
            saved_state = pickle.load(f)
        self.assertEqual(saved_state, {"key": "value"})

    def test_update_many(self):
        """Test updating multiple state entries at once."""
        manager = StateManager(self.state_file)
        manager.update_many({"key1": "value1", "key2": 42})
        self.assertEqual(manager.get("key1", None), "value1")
        self.assertEqual(manager.get("key2", None), 42)

        # Check that the state was saved to the file
        with open(self.state_file, "rb") as f:
            saved_state = pickle.load(f)
        self.assertEqual(saved_state, {"key1": "value1", "key2": 42})

    def test_delete(self):
        """Test deleting a state entry."""
        manager = StateManager(self.state_file)
        manager.update("key", "value")
        manager.delete("key")
        self.assertEqual(manager.get("key", "default"), "default")

        # Check that the state was saved to the file
        with open(self.state_file, "rb") as f:
            saved_state = pickle.load(f)
        self.assertEqual(saved_state, {})

    def test_clear(self):
        """Test clearing all state entries."""
        manager = StateManager(self.state_file)
        manager.update_many({"key1": "value1", "key2": 42})
        manager.clear()
        self.assertEqual(manager.get_all(), {})

        # Check that the state was saved to the file
        with open(self.state_file, "rb") as f:
            saved_state = pickle.load(f)
        self.assertEqual(saved_state, {})

    def test_get_all(self):
        """Test getting all state entries."""
        manager = StateManager()
        manager.update_many({"key1": "value1", "key2": 42})
        self.assertEqual(manager.get_all(), {"key1": "value1", "key2": 42})

    def test_complex_values(self):
        """Test storing and retrieving complex values."""
        manager = StateManager(self.state_file)
        complex_value = {
            "nested": {"a": 1, "b": 2},
            "list": [1, 2, 3],
            "tuple": (4, 5, 6),
        }
        manager.update("complex", complex_value)
        self.assertEqual(manager.get("complex", None), complex_value)

        # Check that the state was saved to the file
        with open(self.state_file, "rb") as f:
            saved_state = pickle.load(f)
        self.assertEqual(saved_state["complex"], complex_value)


if __name__ == "__main__":
    unittest.main()
