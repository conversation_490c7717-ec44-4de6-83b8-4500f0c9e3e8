"""Core functionality for fetching web pages and converting to markdown."""

import requests
from html2text import HTML2Text


class WebPageError(Exception):
    """Raised when there's an error fetching or processing a web page."""

    pass


def fetch_and_convert_to_markdown(url: str, timeout: int = 10) -> str:
    """Fetch a web page and convert it to markdown.

    Args:
        url: The URL to fetch
        timeout: Timeout in seconds

    Returns:
        The page content converted to markdown

    Raises:
        WebPageError: If there's an error fetching or processing the page
    """
    try:
        response = requests.get(url, timeout=timeout)
        response.raise_for_status()

        converter = HTML2Text()
        converter.ignore_links = False
        converter.ignore_images = False
        converter.body_width = 0  # Don't wrap lines

        markdown = converter.handle(response.text)
        return markdown

    except requests.RequestException as e:
        raise WebPageError(f"Failed to fetch page: {str(e)}") from e
    except Exception as e:
        raise WebPageError(f"Failed to convert page: {str(e)}") from e
