#!/bin/bash

set -e

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7

cd ~/vllm

CHAT_TEMPLATE=/mnt/efs/augment/user/guy/deepseek_coder_v2/vllm_chat_template.jinja

MODEL=/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct
#MODEL=/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct-FP8
#MODEL=/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Instruct
#MODEL=/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Instruct-AutoFP8

#SEQ_LEN=32768
SEQ_LEN=63000
#SEQ_LEN=100000

python -m vllm.entrypoints.openai.api_server \
    --model $MODEL \
    --chat-template $CHAT_TEMPLATE \
    --trust-remote-code \
    --tensor-parallel-size 16 \
    --max-model-len $SEQ_LEN \
    --enforce-eager \
    --kv-cache-dtype fp8
