"""<PERSON><PERSON><PERSON> to run all edit generation evaluation jobs.

This script should be manually edited and run to launch all the evaluation jobs.
"""

import argparse
from pathlib import Path

# from experimental.jiayi.evaluation.eval_config_tools import launch_eval
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import ethanol_config
from research.eval.harness.tasks import create_task
from research.eval.harness.harness import EvalHarness

# from research.eval import eval_lib

from experimental.guy.llama_next_edit.start_next_edit_combined_llama_server import (
    build_finetuned_generation_system,
)


def main():
    parser = argparse.ArgumentParser(description="Run all next edit eval jobs")
    parser.add_argument(
        "--generation_checkpoint",
        type=str,
        required=True,
        help="Checkpoint path to the generation model",
    )
    parser.add_argument(
        "--model",
        type=str,
        required=True,
        choices=["llama", "deepseek"],
        help="Model type",
    )
    parser.add_argument(
        "--dataset_path",
        type=str,
        required=True,
        choices=[
            "/mnt/efs/augment/data/eval/next_edits/manual.v3.jsonl.zst",
            "/mnt/efs/augment/data/eval/next_edits/prs.v7.jsonl.zst",
        ],
        help="Choose between manual or prs dataset",
    )

    args = parser.parse_args()

    task_configs = [
        {
            "name": "next_edit_gen",
            "dataset_path": args.dataset_path,
            "must_give_change": True,
            "limit_examples": 2000,
        }
    ]

    system = build_finetuned_generation_system(args.generation_checkpoint, args.model)

    for task_config in task_configs:
        print("Running task", task_config)
        task = create_task(task_config)
        harness = EvalHarness(
            system=system,
            task=task,
            output_path=Path("/mnt/efs/augment/user/guy/eval/next_edit_gen_eval")
            / Path(args.generation_checkpoint).name
            / Path(task_config["dataset_path"]).name,
        )
        harness.run()


if __name__ == "__main__":
    main()
