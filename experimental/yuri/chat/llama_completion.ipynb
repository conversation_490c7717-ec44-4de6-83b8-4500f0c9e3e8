#%%
import sys

sys.path.append("/home/<USER>/repos/augment")
sys.path.append("/home/<USER>/repos/augment/research/gpt-neox")

import os

os.environ["PYTHONPATH"] += ":/home/<USER>/repos/augment/research/gpt-neox:/home/<USER>/repos/augment"
#%%
import requests
import json
import random
import numpy as np
import torch

from base.tokenizers import create_tokenizer_by_name
from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder
from pathlib import Path
from tqdm import tqdm
from experimental.guy.apis.llama3_tokenizer import ChatFormat, Message, Tokenizer


TOKENIZER = create_tokenizer_by_name("llama3_instruct")
DATA_PATH = Path("/mnt/efs/augment/user/yuri/data/droid_llama/real/droid_clean_data_jun20_llama_v2")
OUTPUT_PATH = Path("/mnt/efs/augment/user/yuri/data/droid_llama/real/droid_clean_data_jun20_llama_v2_just_header")
SPLIT_NAME = "valid"
SEQ_LENGTH = 8192

OUTPUT_PATH.mkdir(exist_ok=True)

ALL_IPS = [
    "*************",
    "**************",
    "**************",
    "**************",
    "**************",
    "**************",
    "**************",
    "**************",

    "**************",
    # "*************",
]

PORTS = ["8000"]

ALL_URLS = [f"{ip}:{port}" for ip in ALL_IPS for port in PORTS]


def get_all_samples(path: Path):
    dataset = MMapIndexedDataset(str(path))
    all_samples = []
    for i in tqdm(range(len(dataset))):
        all_samples.append(dataset[i].tolist())
    return all_samples


def save_dataset(samples, output_path):
    random.shuffle(samples)
    if not output_path.parent.exists():
        output_path.parent.mkdir()

    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(".bin"), dtype=np.int32)
    for sample in tqdm(samples):
        builder.add_item(torch.tensor(sample, dtype=torch.int32))
        builder.end_document()
    builder.finalize(output_path.with_suffix(".idx"))


class TritonClient:
    def __init__(self, url: str):
        self.tokenizer = Tokenizer("/mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-70B-Instruct/tokenizer.model")
        self.prompt_formatter = ChatFormat(self.tokenizer)
        self.url = url

    def generate(self, user_message: str):
        dialog = [
            Message(role="user", content=user_message)
        ]

        full_prompt = self.tokenizer.decode(
            self.prompt_formatter.encode_dialog_prompt(dialog)
        )
        payload = self.get_payload(full_prompt)
        
        response_json = self.send_request(payload)

        return response_json["text_output"]
    
    def generate_raw(self, prompt: str):
        payload = self.get_payload(prompt)
        response_json = self.send_request(payload)

        return response_json["text_output"]

    def get_payload(self, full_prompt):
        payload = {
            "text_input": full_prompt,
            "max_tokens": 1000,
            "end_id": self.tokenizer.special_tokens["<|eot_id|>"],
            "stream": False,
            "temperature": 0.8,
            "top_k": 40,
            "top_p": 0.95,
            "random_seed": random.randint(0, int(2 ** 31)),
            "return_context_logits": False,
            "return_log_probs": False,
            "return_generation_logits": False,
        }

        return payload

    def send_request(self, payload):
        headers = {"Content-Type": "application/json"}

        response = requests.post(
            f"http://{self.url}/v2/models/ensemble/generate",
            headers=headers,
            data=json.dumps(payload),
            timeout=100,
        )
        response_json = response.json()

        return response_json
#%%
samples = get_all_samples(DATA_PATH / SPLIT_NAME)
#%%
from queue import Queue
import threading
from typing import Any

client_queue = Queue()

threads = []
lock = threading.Lock()
num_generated = 0

prefixes_tokens: list[Any] = [None] * len(samples)  # Common prefix for all samples
new_outputs_str: list[Any] = [None] * len(samples)  # New output for all samples (excluding completion)
completions_str: list[Any] = [None] * len(samples) # Completions generated by LLaMA


for url in tqdm(ALL_URLS):
    client_queue.put(TritonClient(url))



def process_sample(modified_prompt, i):
    client = client_queue.get()

    # completion = client.generate_raw(modified_prompt)
    completion = ""
    
    with lock:
        completions_str[i] = completion
        global num_generated
        num_generated += 1
        print(f"Generated {num_generated} samples")

    client_queue.put(client)
    

# for i, sample in enumerate(tqdm(samples)):
for i, sample in enumerate(samples):
    while abs(sample[-1]) == TOKENIZER.special_tokens.eos:
        sample = sample[:-1]
    
    full_prompt = TOKENIZER.detokenize(list(map(abs, sample))) # type: ignore
      
    last_header_idx = full_prompt.rfind("<|eot_id|><|start_header_id|>assistant<|end_header_id|>")
    code_section_start = full_prompt.find("```", last_header_idx)
    
    previous_output_tokenized = TOKENIZER.tokenize_safe(full_prompt[code_section_start:])
    assert sample[-len(previous_output_tokenized):] == previous_output_tokenized
    prefixes_tokens[i] = sample[:-len(previous_output_tokenized)]
    
    # new_output = "Here is the modified code:\n" + full_prompt[code_section_start:] + "\n"
    new_output = "Here is the modified code:\n" + full_prompt[code_section_start:]
    new_outputs_str[i] = new_output
    
    llama_input = TOKENIZER.detokenize(list(map(abs, prefixes_tokens[i]))) + new_output # type: ignore
    cur_thread = threading.Thread(
        target=process_sample,
        args=(llama_input, i)
    )
    threads.append(cur_thread)
    cur_thread.start()
    
for thread in threads:
    thread.join()

#%%
new_samples = []

for prefix, new_output, completion in tqdm(zip(prefixes_tokens, new_outputs_str, completions_str)):
    new_sample = prefix + TOKENIZER.tokenize_safe(new_output) + TOKENIZER.tokenize_safe(completion)
    new_sample += [TOKENIZER.special_tokens.eos]
    new_sample += [-1 * TOKENIZER.special_tokens.eos] * (SEQ_LENGTH - len(new_sample) + 1)    

    new_samples.append(new_sample)
    
#%%
import copy


to_check_samples = copy.deepcopy(samples)
to_check_new_samples = copy.deepcopy(new_samples)
#%%
save_dataset(new_samples, OUTPUT_PATH / SPLIT_NAME)
#%%

#%% md
# Check
#%%
CHECK_IDX = 5

assert len(to_check_samples[CHECK_IDX]) == len(to_check_new_samples[CHECK_IDX])

first_diff_ifx = 0
while to_check_samples[CHECK_IDX][first_diff_ifx] == to_check_new_samples[CHECK_IDX][first_diff_ifx]:
    first_diff_ifx += 1
    
print(first_diff_ifx)
#%%
print("The same part:")
print(TOKENIZER.detokenize(list(map(abs, to_check_samples[CHECK_IDX][:first_diff_ifx]))))

#%%
print(TOKENIZER.detokenize(list(map(abs, to_check_samples[CHECK_IDX][first_diff_ifx:]))))

#%%
print(TOKENIZER.detokenize(list(map(abs, to_check_new_samples[CHECK_IDX][first_diff_ifx:]))))
#%%
new_samples[CHECK_IDX][:first_diff_ifx]
#%%
new_samples[CHECK_IDX][first_diff_ifx:]
#%%
