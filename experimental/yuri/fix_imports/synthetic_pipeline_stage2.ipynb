#%%
import sys
import os


sys.path.append("/home/<USER>/repos/augment")
sys.path.append("/home/<USER>/repos/augment/research/gpt-neox")

os.environ["PYTHONPATH"] = "/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox"
os.environ["CUDA_VISIBLE_DEVICES"] = "1"
#%%
import json
import difflib
import random
import pandas as pd

from pathlib import Path
from tqdm import tqdm

from research.core.abstract_prompt_formatter import get_prompt_formatter
from research.eval.harness.factories import create_retriever
from research.core.model_input import ModelInput
from research.core.types import Document
from research.data.synthetic_code_edit.util_lib import get_unified_diff
from multiprocessing import Pool

from experimental.yuri.pr_edits.droid_data import save_dataset
from research.data.spark import k8s_session
#%%
def create_spark(job_name: str, max_workers: int):
    return k8s_session(
        name=job_name,
        gpu_type=["A40", "Quadro_RTX_5000", "RTX_A5000", "RTX_A6000"],
        conf={
            "spark.executor.pyspark.memory": "1000G",
            "spark.executor.memory": "30G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        max_workers=max_workers,
    )
#%%
GENERATED_DATA_PATH = Path("/mnt/efs/augment/user/yuri/data/fix_imports_mar30_v13_1k/")
#%%
data = []

for file in tqdm(GENERATED_DATA_PATH.glob("*.json")):
    with file.open("r") as f:
        data.append(json.load(f))
    if len(data) > 20:
        break
        

#%%
# v13_1k
SKIPPED = [
    0, 14, 28, 31, 34, 36, 49, 52, 55, 67, 73, 79, 81, 82, 89, 99, 100, 107, 111,
    114, 121, 129, 133, 137, 142, 149, 159, 163, 176, 182, 184, 185, 189, 196, 208,
    209, 218, 228, 230, 238, 245, 249, 250, 251, 256, 272, 279, 291, 295, 297, 304,
    305, 308, 310, 322, 332, 335, 348, 351, 359, 374, 380, 387, 395, 404, 428, 431,
    441, 448, 460, 469, 479, 480, 493, 503, 512, 516, 524, 528, 535, 545, 546, 548,
    549, 560, 567, 570, 574, 575, 593, 607, 612, 614, 618, 630, 635, 640, 641, 642,
    651, 654, 656, 658, 665, 673, 683, 684, 685, 688, 689, 692, 693, 699, 712
]

len(SKIPPED)
#%%
data_filtered = []
for sample_id, sample in enumerate(data):
    if sample_id in SKIPPED:
        continue
    data_filtered.append(sample)
data = data_filtered
#%%
len(data)
#%%
data_df = pd.DataFrame(data)
#%%
data_df.columns
#%%
data_df.shape
#%%
spark = create_spark("yuri-process", 32)
#%%

#%% md

# Visualize
#%%
HTML_START = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Code Visualization</title>
    <style>
        pre {{
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-left: 3px solid #f36d33;
            color: #666;
            page-break-inside: avoid;
            font-family: monospace;
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 1.6em;
            max-width: 100%;
            overflow: auto;
            padding: 1em 1.5em;
            display: block;
            word-wrap: break-word;
        }}
        .wide-line {{
            width: 100%; 
            margin-left: auto;|
            margin-right: auto;
            height: 20px;
            background-color: black;
        }}
        .instructions li {{
           color: gray; /* This makes all list items gray */
        }}

        .instructions li:first-child {{
            color: black; /* This changes the color of the first item to black */
        }}

    </style>
</head>
<body>
"""

HTML_END = """
<div id="checkedList"></div>

<script>
function updateCheckedList() {
  const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
  const checkedIds = Array.from(checkboxes).map(checkbox => checkbox.id);
  const listElement = document.getElementById('checkedList');
  
  // Create a string or list items from the checkedIds
  const listContent = checkedIds.length > 0 ? checkedIds.join(', ') : 'No checkboxes checked';
  
  // Update the div's content
  listElement.textContent = listContent;
}

// Initial update in case any are checked by default
updateCheckedList();

// Add event listener to checkboxes
document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
  checkbox.addEventListener('change', updateCheckedList);
});
</script>
</body>
</html>
"""

HTML_END = """
<div id="checkedList"></div>

<script>
function updateCheckedList() {
  const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
  const checkedIds = Array.from(checkboxes).map(checkbox => `"${checkbox.id}",`);
  
  // Update to display each entry on its own line, enclosed in quotation marks
  const listElement = document.getElementById('checkedList');
  const listContent = checkedIds.length > 0 ? checkedIds.join('<br>') : 'No checkboxes checked';
  
  // Use innerHTML since we're including HTML tags (e.g., <br>)
  listElement.innerHTML = listContent;
}

// Initial update in case any are checked by default
updateCheckedList();

// Add event listener to checkboxes
document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
  checkbox.addEventListener('change', updateCheckedList);
});
</script>
</body>
</html>
"""

def get_diff_html(input_code, output_code, comment, another_header=None):
    diff_obj = difflib.HtmlDiff()
    diff_obj._legend = ""


    diff_html = diff_obj.make_file(
        input_code.splitlines(),
        output_code.splitlines()
    )

    comment_html = f"<li><strong>{comment}</strong></li>"

    html = f"""
    <h4>Comment: {comment_html}</h4>
    <div id="code-diff">{diff_html}</div>
"""
    if another_header is not None:
        html = f"""<h4>{another_header}</h4>""" + html
    return html
#%%
MAIN_HTML = ""
for sample_id, sample in enumerate(data):
    MAIN_HTML += "<hr class=\"wide-line\">"
    MAIN_HTML += f"<h2>Code sample {sample_id} {sample['type']} {sample['N']}</h2><hr>"

    cur_diff = get_diff_html(
        sample["selected_code"],
        sample["updated_code"],
        "",
        "",
    )
    MAIN_HTML += f"{cur_diff}<hr>"

RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END
with open('./imports_mar30_v13_1k_after_filtering2.html', 'w') as f:
    f.write(RESULTING_HTML)
#%%
data[0].keys()
#%% md
# Tokenize
#%%
GOOD_INDICES = list(range(len(data)))
len(GOOD_INDICES)

#%%
POSSIBLE_INSTRUCTIONS = [
    "Fix imports",
    "Fix import statements.",
    "Check and fix imports.",
    "Adjust import statements.",
    "Review and correct imports.",
    "Update imports.",
    "Ensure imports are correct.",
    "Refine imports.",
    "Correct import errors.",
    "Address import issues.",
    "Streamline imports.",
    "Revisit import section.",
    "Repair import declarations.",
    "Solve import problems",
    "Tidy up imports.",
]


RETRIEVER_CONFIG = {
    "scorer": {
        "name": "ethanol",
        "checkpoint_path": "ethanol/ethanol6-16.1",
    },
    "chunker": {
        "name": "line_level",
        "max_lines_per_chunk": 30,
    },
    "query_formatter": {
        "name": "ethanol6_query",
        "add_path": True,
        "add_suffix": True,
        "max_tokens": 1023,
        "prefix_ratio": 0.9,
    },
    "document_formatter": {
        "name": "ethanol6_document",
        "add_path": True,
        "max_tokens": 999,
    },
}


RETRIEVER = create_retriever(RETRIEVER_CONFIG)
RETRIEVER.load()

TOKEN_APPORTIONMENT = {
    "path_len": 256,
    "instruction_len": 512,
    "prefix_len": 1536,
    "selected_code_len": 4096,
    "suffix_len": 1024,
    "max_prompt_tokens": 16384 - 4096,  # 4096 represents the max output tokens
}
SEQUENCE_LENGTH = 16384

PROMPT_FORMATTER = get_prompt_formatter("droid", **TOKEN_APPORTIONMENT)
#%%
def tokenize(sample):
    cur_docs = [Document(id=d["id"], text=d["text"], path=d["path"]) for d in sample["docs"]]
    RETRIEVER.add_docs(cur_docs)

    query_input = ModelInput(
        prefix=sample["prefix"],
        suffix=sample["suffix"],
        path=sample["path"],
    )
    retrieved_chunks, _ = RETRIEVER.query(query_input, top_k=128)
    # retrieved_chunks = []

    model_input = ModelInput(
        prefix=sample["prefix"],
        suffix=sample["suffix"],
        path=sample["path"],
        retrieved_chunks=retrieved_chunks,
        extra={
            "instruction": random.choice(POSSIBLE_INSTRUCTIONS),
            "selected_code": sample["selected_code"],
            "prefix_begin": 0,
            "suffix_end": len(sample["prefix"] + sample["selected_code"] + sample["suffix"]),
        }
    )

    tokenized_input, _ = PROMPT_FORMATTER.prepare_prompt(model_input)
    tokenized_output = PROMPT_FORMATTER.tokenizer.tokenize(sample["updated_code"] + "\n```\n") + [PROMPT_FORMATTER.tokenizer.eod_id]
    complete_prompt = [-1 * t for t in tokenized_input] + tokenized_output
    complete_prompt += [-1 * PROMPT_FORMATTER.tokenizer.eod_id] * (SEQUENCE_LENGTH - len(complete_prompt) + 1)  # +1 to make total prompt of length SEQUENCE_LENGTH + 1

    return complete_prompt


#%%
t = tokenize(data[GOOD_INDICES[0]])

#%%
print(PROMPT_FORMATTER.tokenizer.detokenize(list(map(abs, t))))
#%%
print(data[0]['suffix'])
#%%
FINAL_DATASET_DIR = Path("/mnt/efs/augment/user/yuri/data/fix_imports_mar30_v13_1k_noretrieval_tokenized")

tokenized_samples = []
for indx in tqdm(GOOD_INDICES):
    try:
        t = tokenize(data[indx])
    except Exception as e:
        print(f"Failed to process sample {indx}: {e}")
        continue
    tokenized_samples.append(t)

    
#%%
len(tokenized_samples)
#%%
FINAL_DATASET_DIR.mkdir(exist_ok=True)

save_dataset(tokenized_samples[:560], tokenized_samples[560:], FINAL_DATASET_DIR)

#%%
print(PROMPT_FORMATTER.tokenizer.detokenize(list(map(abs, tokenized_samples[1]))))
#%%
