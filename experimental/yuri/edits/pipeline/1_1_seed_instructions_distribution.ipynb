#%%
import json
import matplotlib.pyplot as plt

from collections import defaultdict

%matplotlib inline
#%%
with open('/home/<USER>/repos/augment/research/data/synthetic_code_edit/seeds/seed_instructions_with_inverse.json', 'r') as f:
    seed_instructions = json.load(f)

with open('/home/<USER>/repos/augment/research/data/synthetic_code_edit/categories.json', 'r') as f:
    categories = json.load(f)
#%%

len(seed_instructions), seed_instructions[0]
#%%
all_categories = set(categories.keys())
all_categories
#%%
samples_per_category = defaultdict(int)

for s_i in seed_instructions:
    for c in s_i[2]:  # iterate over categories
        assert c in categories.keys(), c
        samples_per_category[c] += 1
        

samples_per_category


#%%
_sorted_vs = [*sorted(samples_per_category.items(), key=lambda x: x[1])]
_cats, _num = [], []
for k, v in _sorted_vs:
    _cats.append(k)
    _num.append(v)

plt.bar(_cats, _num)
plt.xticks(rotation=75)

#%%
