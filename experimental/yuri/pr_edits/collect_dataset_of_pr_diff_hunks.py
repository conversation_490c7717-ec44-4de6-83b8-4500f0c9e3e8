import argparse
import base64
import json
import random
import re
import subprocess
import tempfile
from multiprocessing import Pool
from pathlib import Path

import requests
from tqdm import tqdm

GITHUB_TOKEN = ""


def read_data(path):
    data = []
    with path.open() as f:
        for line in tqdm(f, desc="Reading data"):
            cur_sample = json.loads(line)
            data.append(cur_sample)
    return data


def get_file_at_commit(owner, repo, path, commit_sha):
    url = f"https://api.github.com/repos/{owner}/{repo}/contents/{path}"
    headers = {
        "Accept": "application/vnd.github.v3+json",
        "Authorization": f"token {GITHUB_TOKEN}",
    }
    params = {"ref": commit_sha}

    response = requests.get(url, headers=headers, params=params)
    response.raise_for_status()
    data = response.json()

    content = base64.b64decode(data["content"]).decode("utf-8")

    return content


def get_comment(owner, repo, comment_id):
    url = f"https://api.github.com/repos/{owner}/{repo}/pulls/comments/{comment_id}"
    headers = {
        "Accept": "application/vnd.github.v3+json",
        "Authorization": f"token {GITHUB_TOKEN}",
    }

    response = requests.get(url, headers=headers)
    response.raise_for_status()
    data = response.json()

    return data


def get_git_diff(code_a: str, code_b: str) -> str:
    with (
        tempfile.NamedTemporaryFile(mode="w") as file_a,
        tempfile.NamedTemporaryFile(mode="w") as file_b,
    ):
        file_a.write(code_a)
        file_a.flush()
        file_b.write(code_b)
        file_b.flush()

        result = subprocess.run(
            ["git", "diff", "--unified=3", "--no-index", file_a.name, file_b.name],
            capture_output=True,
            text=True,
        )
        return result.stdout


def parse_diff_into_hunks(diff: str) -> list[str]:
    all_hunks = []
    current_hunk = []
    for line in diff.splitlines(True):
        if line.startswith("@@"):
            if len(current_hunk) > 0:
                all_hunks.append("".join(current_hunk))
                current_hunk = []
            indxs_line = line.split("@@")[1]
            cur_line = f"@@{indxs_line}@@\n"
            current_hunk.append(cur_line)
        else:
            current_hunk.append(line)
    if len(current_hunk) > 0:
        all_hunks.append("".join(current_hunk))
    return all_hunks


def simplify_hunk_header(hunk):
    all_lines = hunk.splitlines(True)
    first_line = all_lines[0]
    assert first_line.startswith("@@")
    indxs_line = first_line.split("@@")[1]
    first_line = f"@@{indxs_line}@@\n"

    return "".join([first_line, *all_lines[1:]])


def get_num_lines(s):
    return len(s.splitlines(True))


def get_first_n_lines(s, n):
    return "".join(s.splitlines(True)[:n])


def hunk_to_code(hunk, ignore_symbol):
    result = []
    for i, line in enumerate(hunk.splitlines(True)):
        if i == 0:  # For hunk header
            continue
        if line.startswith(ignore_symbol):
            continue
        line = line[1:]
        result.append(line)

    return "".join(result)


def parse_all_diff_numbers(diff_hunk: str):
    match = re.search(r"@@ -(\d+),(\d+) \+(\d+),(\d+) @@", diff_hunk)
    if match:
        return (
            int(match.group(1)),
            int(match.group(2)),
            int(match.group(3)),
            int(match.group(4)),
        )
    assert False  # noqa: B011


def mark_lines(code: str, line_range: list[int]) -> str:
    result = []
    for i, line in enumerate(code.splitlines(True)):
        if line_range[0] <= i <= line_range[1]:
            result.append(f"|>{line}")
        else:
            result.append(line)
    return "".join(result)


def get_full_diff_samples(sample):
    whole_samples = []

    comments = [*filter(lambda c: "in_reply_to_id" not in c, sample["comments_array"])]
    comments = comments[:10]  # To not get too much comments from a single PR
    for comment in comments:
        if "```suggestion" in comment["body"]:
            continue
        new_sample = {
            "comment": comment,
            "comment_url": f"{sample['url']}/files#r{comment['id']}",
        }

        try:
            comment_full_info = get_comment(
                sample["base"]["repo_owner"], sample["base"]["repo_name"], comment["id"]
            )
            original_content = get_file_at_commit(
                sample["base"]["repo_owner"],
                sample["base"]["repo_name"],
                comment["path"],
                comment["base_sha"],
            )
            updated_content = get_file_at_commit(
                sample["head"]["repo_owner"],
                sample["head"]["repo_name"],
                comment["path"],
                comment["original_commit_id"],
            )
        except Exception:
            # print(f"Error getting file for comment {comment['id']}: {e}")
            continue

        if comment_full_info["user"]["login"] == sample["user"]["login"]:
            continue
        if (
            comment_full_info["start_side"] == "LEFT"
            or comment_full_info["side"] == "LEFT"
        ):
            continue

        diff = get_git_diff(original_content, updated_content)
        hunks = parse_diff_into_hunks(diff)

        comment_diff_hunk = simplify_hunk_header(comment["diff_hunk"]).rstrip()

        new_sample["all_considered_hunks"] = hunks
        new_sample["comment_full_info"] = comment_full_info

        for hunk in hunks:
            whole_diff_hunk_prefix = get_first_n_lines(
                hunk, get_num_lines(comment_diff_hunk)
            ).rstrip()
            if whole_diff_hunk_prefix == comment_diff_hunk:
                new_sample["full_diff_hunk"] = hunk
                break

        if "full_diff_hunk" not in new_sample:
            # print(f"Cannot find hunk for comment {comment['id']}")
            continue

        whole_samples.append(new_sample)

    return whole_samples


def process_sample(task):
    pr_sample, output_dir = task
    full_diff_samples = get_full_diff_samples(pr_sample)

    sample_id = f"{pr_sample['base']['repo_owner']}_{pr_sample['base']['repo_name']}_{pr_sample['number']}"

    with open(output_dir / f"{sample_id}.json", "w") as f:
        json.dump(full_diff_samples, f, indent=2)


def main(input_json: Path, output_dir: Path, num_processes: int, num_samples: int):
    output_dir.mkdir(parents=False, exist_ok=True)
    data = read_data(input_json)
    random.shuffle(data)
    print(f"Read {len(data)} data from {input_json}")

    tasks = []
    for pr_sample in data[:num_samples]:
        tasks.append((pr_sample, output_dir))

    if num_processes <= 1:
        for task in tqdm(tasks):
            process_sample(task)
    else:
        with Pool(num_processes) as pool:
            _ = list(tqdm(pool.imap(process_sample, tasks), total=len(tasks)))


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for getting a dataset of pr diff hunks and comments",
    )
    parser.add_argument(
        "--input_json",
        "-i",
        type=Path,
        required=True,
        help="Input json file with data from BigQuery",
    )
    parser.add_argument(
        "--output_dir",
        "-o",
        type=Path,
        required=True,
        help="Output directory for saving the data",
    )
    parser.add_argument(
        "--num_processes",
        "-np",
        type=int,
        default=1,
        help="Number of parallel processes",
    )
    parser.add_argument(
        "--num_samples",
        "-n",
        type=int,
        required=True,
        help="Number of samples to generate",
    )
    args = parser.parse_args()

    return args


if __name__ == "__main__":
    xargs = parse_args()
    main(
        input_json=xargs.input_json,
        output_dir=xargs.output_dir,
        num_processes=xargs.num_processes,
        num_samples=xargs.num_samples,
    )
