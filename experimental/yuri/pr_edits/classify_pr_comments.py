import argparse
import json
from multiprocessing import Pool
import random
import requests
import base64
import re
import subprocess
import tempfile


from pathlib import Path
from tqdm import tqdm

from research.data.synthetic_code_edit.api_lib import GptWrapper


PROMPT = """
Here is code hunk from a GitHub PR:
```
{code_chunk}
```

And here is a comment that was left by a reviewer:
```
{comment_body}
```
Comment was attached to the lines marked with '|>'

Your task is to classify this review comment into one of two classes:
1. Actionable comment
    - This comment explicitly suggests a specific change to be made to the code hunk.
    - Comment doesn't initiates a discussion.
    - Comment doesn't contain any URLs, any references to other files, any usernames.
2. Non-actionable comment
    - If comment fails any of criterias of Actionable comment, then it's non-actionable.

Follow these steps to complete this task:
1. Analyze the provided code chunk and comment.
2. Describe your thinking process on how to complete this task.
3. Explicitly write YES (if comment is actionable) or NO (if comment is non-actionable).
"""


EXTRACT_PROMPT = """Return the result in a JSON with a single key:
- result

result should be true if comment is actionable, false if comment is non-actionable.
"""


def read_data(input_dir: Path):
    data = []
    for file in input_dir.glob("*.json"):
        with file.open() as f:
            data.append(json.load(f))
    return data


def hunk_to_code(hunk, ignore_symbol):
    result = []
    for i, line in enumerate(hunk.splitlines(True)):
        if i == 0: # For hunk header
            continue
        if line.startswith(ignore_symbol):
            continue
        line = line[1:]
        result.append(line)

    return "".join(result)

def parse_all_diff_numbers(diff_hunk: str):
    match = re.search(r'@@ -(\d+),(\d+) \+(\d+),(\d+) @@', diff_hunk)
    if match:
        return (int(match.group(1)), int(match.group(2)),
                int(match.group(3)), int(match.group(4)))
    assert False


def mark_lines(code: str, line_range: list[int]) -> str:
    result = []
    for i, line in enumerate(code.splitlines(True)):
        if line_range[0] <= i <= line_range[1]:
            result.append(f"|>{line}")
        else:
            result.append(line)
    return "".join(result)

def get_marked_code(sample):
    if sample["comment_full_info"]["start_side"] == "LEFT":
        return None
    if sample["comment_full_info"]["side"] == "LEFT":
        return None

    cur_code = hunk_to_code(sample["full_diff_hunk"], "-")

    last_commented_line = sample["comment_full_info"]["original_line"]
    first_commented_line = sample["comment_full_info"]["original_start_line"]
    if first_commented_line is None:
        first_commented_line = last_commented_line

    _, _, first_plus_line, _ = parse_all_diff_numbers(sample["full_diff_hunk"])

    first_commented_line -= first_plus_line
    last_commented_line -= first_plus_line

    marked_code = mark_lines(cur_code, [first_commented_line, last_commented_line])

    return marked_code


def classify_sample(comment_sample, gpt):
    marked_code = get_marked_code(comment_sample)

    prompt = PROMPT.format(code_chunk=marked_code, comment_body=comment_sample["comment"]["body"])
    messages = [{"role": "user", "content": prompt}]
    gpt4_response = gpt(messages, model="gpt-4-1106-preview", temperature=0)

    messages.append({"role": "assistant", "content": gpt4_response})
    messages.append({"role": "user", "content": EXTRACT_PROMPT})
    extracted_response = gpt(messages, model="gpt-3.5-turbo-1106", use_json=True, temperature=0)

    extracted_response["main_prompt"] = prompt

    return extracted_response

def process_sample(task):
    comment_sample, gpt, output_dir = task

    sample_idx = f"{comment_sample['comment_full_info']['commit_id']}_{comment_sample['comment_full_info']['pull_request_review_id']}_{comment_sample['comment_full_info']['id']}"
    for _dir in ["good", "bad", "failed"]:
        if (output_dir / _dir / f"{sample_idx}.json").exists():
            return

    try:
        cur_result = classify_sample(comment_sample, gpt)
    except Exception as e:
        print(f"Failed to classify sample {sample_idx}: {e}")
        with (output_dir / "failed" / f"{sample_idx}.json").open("w") as f:
            json.dump(comment_sample, f, indent=2)
        return

    comment_sample["classification_prompt"] = cur_result["main_prompt"]
    if cur_result["result"]:
        target_dir = output_dir / "good"
    else:
        target_dir = output_dir / "bad"

    with (target_dir / f"{sample_idx}.json").open("w") as f:
        json.dump(comment_sample, f, indent=2)

def main(
    input_dir: Path, output_dir: Path, num_processes: int, num_samples: int
):
    output_dir.mkdir(parents=False, exist_ok=True)
    (output_dir / "good").mkdir(exist_ok=True, parents=False)
    (output_dir / "bad").mkdir(exist_ok=True, parents=False)
    (output_dir / "failed").mkdir(exist_ok=True, parents=False)

    data = read_data(input_dir)
    print(f"Read {len(data)} data from {input_dir}")

    gpt = GptWrapper()
    tasks = []
    for pr_samples in data[:num_samples]:
        for comment_sample in pr_samples:
            tasks.append((comment_sample, gpt, output_dir))

    if num_processes <= 1:
        for task in tqdm(tasks):
            process_sample(task)
    else:
        with Pool(num_processes) as pool:
            _ = list(tqdm(pool.imap(process_sample, tasks), total=len(tasks)))


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for classifying PR comments into 'actionable'/'not actionable'.",
    )
    parser.add_argument(
        "--input_dir",
        "-i",
        type=Path,
        required=True,
        help="Folder with output from collect_dataset_of_pr_diff_hunks.py",
    )
    parser.add_argument(
        "--output_dir",
        "-o",
        type=Path,
        required=True,
        help="Output directory for saving the data",
    )
    parser.add_argument(
        "--num_processes",
        "-np",
        type=int,
        default=1,
        help="Number of parallel processes",
    )
    parser.add_argument(
        "--num_samples",
        "-n",
        type=int,
        required=True,
        help="Number of samples to generate",
    )
    args = parser.parse_args()

    return args


if __name__ == "__main__":
    xargs = parse_args()
    main(
        input_dir=xargs.input_dir,
        output_dir=xargs.output_dir,
        num_processes=xargs.num_processes,
        num_samples=xargs.num_samples,
    )
