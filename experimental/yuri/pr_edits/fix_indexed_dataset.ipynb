#%%
import pandas as pd
import json
import random
import torch
import numpy as np

from pathlib import Path
from megatron.data.indexed_dataset import MMapIndexedDataset
from research.core.abstract_prompt_formatter import get_prompt_formatter
from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder

# To initialise formatters registry
from research.eval.harness.factories import create_retriever
from tqdm import tqdm
#%%
RESULT_DIR = Path("/mnt/efs/augment/user/yuri/data/mar_7_75k_v4")
#%%
dataset = MMapIndexedDataset(str(RESULT_DIR / "train")) + MMapIndexedDataset(str(RESULT_DIR / "valid"))
#%%
len(dataset)
#%%
def find_substring(S, A, B):
    # Find the first occurrence of A in S
    start_index = S.find(A)
    if start_index == -1:
        # A is not found
        assert False
        return ""
    
    # Adjust start_index to the end of A to find the substring after A
    start_index += len(A)
    
    # Find the first occurrence of B in S after A
    end_index = S.find(B, start_index)
    if end_index == -1:
        # B is not found after A
        assert False
        return ""
    
    # Extract the substring between A and B
    return S[start_index:end_index]

    
def split_sample(sample_s: str):
    instruction = find_substring(sample_s, "Instruction: Fix PR comment: ", "\nPrefix:\n```")
    try:
        instruction2 = find_substring(sample_s, "Instruction: Fix PR comment: ", "\nSee (")
    except AssertionError:
        instruction2 = instruction
    if len(instruction2) < len(instruction):
        instruction = instruction2
    selected_code = find_substring(sample_s, "Selected Code:\n```\n", "```\n\nUpdated Code:\n```")
    updated_code = find_substring(sample_s, "Updated Code:\n```\n", "\n```\n<|EOT|><|EOT|>")
    return {"instruction": instruction, "selected_code": selected_code, "updated_code": updated_code}
                
def split_raw(sample, tokenizer):
    sample = list(map(abs, sample))
    sample_s = tokenizer.detokenize(sample)
    return split_sample(sample_s)


def save_dataset(train_dataset, eval_dataset, output_path):
    for f_name, dataset in [("train", train_dataset), ("valid", eval_dataset)]:
        cur_output_path = output_path / f_name
        builder = MMapIndexedDatasetBuilder(
            cur_output_path.with_suffix(".bin"), dtype=np.int32
        )
        for sample in tqdm(dataset):
            if len(sample) == 1:
                continue
            builder.add_item(torch.tensor(sample, dtype=torch.int32))
            builder.end_document()
        builder.finalize(cur_output_path.with_suffix(".idx"))

#%%
TOKEN_APPORTIONMENT = {
    "path_len": 256,
    "instruction_len": 512,
    "prefix_len": 1536,
    "selected_code_len": 4096,
    "suffix_len": 1024,
    "max_prompt_tokens": 16384 - 4096,  # 4096 represents the max output tokens
}

prompt_formatter = get_prompt_formatter("droid", **TOKEN_APPORTIONMENT)
tokenizer = prompt_formatter.tokenizer
#%%
dataset[0]
#%%
sample = list(map(abs, dataset[2]))
print(tokenizer.detokenize(sample))
#%%
split_sample(tokenizer.detokenize(sample))
#%%
from collections import defaultdict


instr2samples = defaultdict(list)
for sample in tqdm(dataset):
    sample_s = tokenizer.detokenize(list(map(abs, sample)))
    instr2samples[split_sample(sample_s)["instruction"]].append(sample)
#%%
good_samples = []
first_stage_bad_samples = []
#%%
for f_path in tqdm(Path("/mnt/efs/augment/user/yuri/data/cr_75k_filter_mar17_v1/good").glob("*.json")):
    with f_path.open() as f:
        sample = json.load(f)
    
    possible_samples = instr2samples[sample["comment"]]
    
    if len(possible_samples) == 0:
        first_stage_bad_samples.append(sample)
        continue

    possible_samples = [*filter(
        lambda s: split_raw(s, tokenizer)["updated_code"] == sample["ce_sample"]["updated_code"], possible_samples)]
    # possible_samples = [*filter(
    #     lambda s: split_raw(s, tokenizer)["selected_code"] in sample["ce_sample"]["selected_code"], possible_samples)]
    if len(possible_samples) == 0:
        print("Skipping second stage.")
        continue
    assert len(possible_samples) == 1


    found_sample = possible_samples[0]
    good_samples.append(found_sample)

#%%
len(good_samples)
#%%
len(first_stage_bad_samples)
#%%
first_stage_bad_samples[0]
#%%
# len(list(filter(lambda k: "I think it's easier to read this way: " in k, instr2samples.keys())))
# list(filter(lambda k: "I think we still want this check so that" in k, instr2samples.keys()))

# instr2samples["I think we still want this check so that disabled projects don't run old tasks wouldn't we?"]
#%%
good_samples[0]
#%%
random.shuffle(good_samples)
#%%
_o =  Path("/mnt/efs/augment/user/yuri/data/cr_75k_filter_mar17_v1_first40k")
_o.mkdir(exist_ok=False, parents=False)
save_dataset(good_samples[:-750], good_samples[-750:], _o)

#%%
