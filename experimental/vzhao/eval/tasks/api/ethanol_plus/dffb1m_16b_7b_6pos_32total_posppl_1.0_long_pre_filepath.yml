# Usage
# python research/eval/eval.py --v2 /home/<USER>/augment/experimental/vzhao/eval/tasks/api/ethanol_plus/dffb1m_16b_7b_6pos_32total_posppl_1.0_long_pre_filepath.yml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: diffb1m_16b_alphal_fimv2, 7b_6pos_32total_posppl_1.0_long_pre_filepath, 30LineChunk, finegrained-python.large
  workspace: Dev
  project: vzhao-eval
import_modules: experimental.igor.systems.ethanol
system:
  name: basic_rag
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fimv2
    name: rogue
    prompt:
      max_prefix_tokens: 1280
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
      # NOTE: Change this to control the max number of retrieved chunks in the prompt.
      max_number_chunks: 32
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    scorer:
      name: diff_boykin
      checkpoint: ethanol_plus/7b_6pos_32total_posppl_1.0_long_pre_filepath
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    query_formatter:
      name: ethanol3_query
      max_tokens: 1023
      max_lines: -1
      add_path: True
      retokenize: True
    document_formatter:
      name: simple_document
      add_path: True
      max_tokens: 1023
  experimental:
    remove_suffix: False
    retriever_top_k: 100
    trim_on_dedent: False
    trim_on_max_lines: null
task:
  name: api
  dataset: finegrained-python.large
podspec: 1xA100.yaml
