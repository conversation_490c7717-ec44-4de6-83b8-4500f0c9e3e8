#
# This file contains an example of an evaluation config
#
# Usage:
# python /home/<USER>/augment/research/eval/eval.py --v2 /home/<USER>/augment/experimental/vzhao/eval/tasks/multiline/starcoder_noretrieval.yml

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

systems:
  - name: basic_rag
    model:
      name: starcoderbase
      prompt:
        always_fim_style: true
        max_prefix_tokens: 2048
        max_prompt_tokens: 7912
        max_suffix_tokens: 2048
        retrieval_layout_style: "comment2"
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      name: null
      chunker: line_level
      max_chunk: 20
      max_query_lines: 15
    experimental:
      remove_suffix: False
      retriever_top_k: 50
      trim_on_dedent: True
      trim_on_max_lines: null
      trim_trailing_newline_on_prefix: True


# Tasks
#   specify the evaluation tasks for each checkpoint
#
tasks:
  - name: hydra
    dataset: repoeval_2-3lines
    # limit: 10
    # exec: False

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: 1xA100.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: Hydra, StarCoder, NoRetrieval, MultiLine
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
