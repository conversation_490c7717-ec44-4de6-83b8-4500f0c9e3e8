# Usage
# python research/eval/eval.py --v2 /home/<USER>/augment/experimental/vzhao/eval/tasks/multiline/scope_chunks/diffb1m_16b_bm25_signature_diffboykin_rerank.yml
# python research/eval/eval.py --v2 --local --dry_run /home/<USER>/augment/experimental/vzhao/eval/tasks/multiline/scope_chunks/diffb1m_16b_bm25_signature_diffboykin_rerank.yml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: diffb1m_16b, SignatureBM25, DiffBoykinReranking, repoeval_2-3lines, max_number_chunks=100, topk=32
  workspace: Dev
  project: Eval
system:
  name: rag_with_reranker
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
    name: rogue
    prompt:
      max_prefix_tokens: 1280
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
      # NOTE: Change this to control the max number of retrieved chunks in the prompt.
      max_number_chunks: 32
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    name: bm25
    chunker:
      name: scope_aware
      max_lines_per_chunk: 256
    query_formatter:
      name: signature_body_query
      max_lines: 256
    document_formatter:
      name: simple_document
  reranker:
    name: diff_boykin
    query_formatter:
      name: simple_query
      max_lines: 20
  experimental:
    remove_suffix: False
    retriever_top_k: 32
    trim_on_dedent: False
    trim_on_max_lines: null
task:
  name: hydra
  dataset: repoeval_2-3lines
podspec: 1xA100.yaml
