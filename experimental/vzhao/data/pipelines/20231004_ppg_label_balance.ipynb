#%%
# My custom library.
%load_ext autoreload
%autoreload 2


# Imports
import json
import os
import logging
from functools import partial
from types import SimpleNamespace
from typing import Any, Generator, List, Mapping, Sequence, Iterable
import random

import pandas as pd
import pyspark.sql.functions as F

from research.data.spark import k8s_session, get_session
from research.data.spark.pipelines.utils import map_parquet
from research.eval.harness.factories import create_retriever
from research.core.types import EMPTY_CHUNK

from research.retrieval.types import Chunk, Document
from research.static_analysis.file_language_estimator import guess_lang_from_fp
from research.static_analysis.fim_prompt import _format_middle
from research.static_analysis.fim_sampling import CSTFimSampler, FimProblem
from research.static_analysis.usage_analysis import ParsedFile
from research.core.model_input import ModelInput


from experimental.vzhao.data import common, constants, spark_stages
from experimental.vzhao.data import pandas_functions as vz_pdfn
#%% md
# CONFIG
#%%
# Data paths.
RAW_REPO_DATA = "s3a://the-stack-processed/by-repo"

# S3ROOT = "s3a://augment-temporary/vzhao/ppl_gain/test"
S3ROOT = "s3a://augment-temporary/vzhao/ppl_gain/1023_7b_6pos_32total_posppl_1.0"
VERSION_SUFFIX = "_long_pre_filepath"

# Path to the final dataset for training.
DATA_OUTPUT_PATH = "/mnt/efs/augment/user/vincent/data/ppl_gain/1022_6pos_32total_posppl_1.0_long_pre_long_suf_filepath"

CONFIG = dict(
    allowed_languages=["python", "go", "java", "javascript", "rust", "typescript"],
    random_seed=74912,
)
#%%
# CONFIG = dict(
#     allowed_languages=["python", "go", "java", "javascript", "rust", "typescript"],
#     random_seed=74912,
#     # === Stage 1 ===
#     stage_1=dict(
#         # Sampling Repositories.
#         repo_min_size=200000,
#         repo_max_size=5000000,
#         limit_repos=35000,
#         downsample_small=True,
#     ),
#     # === Stage 2 ===
#     stage_2=dict(
#         # FIM sampler Configs.
#         every_n_lines=150,
#         max_problems_per_file=4,
#         max_prefix_chars=8000,
#         max_suffix_chars=8000,
#         # Retrieval Configs.
#         num_retrieved_chunks=127,
#         retriever={
#             "name": "diff_boykin",
#             "chunker": {
#                 "name": "line_level",
#                 "max_lines_per_chunk": 40,
#             },
#             "query_formatter": {
#                 "name": "simple_query",
#                 "max_lines": 20,
#             },
#         },
#     ),
#     # === No Stage 3 ===
#     # === Stage 4 ===
#     stage_4=dict(
#         num_rows=None,
#         num_partitions=4096,
#     ),
#     # === Stage 5 ===
#     stage_5=dict(
#         language_model={
#             "checkpoint_path": "rogue/diffb1m_1b_alphal_fixtoken",
#             "name": "rogue",
#             "prompt": {
#                 "max_prefix_tokens": 100,
#                 "max_suffix_tokens": 100,
#                 "max_retrieved_chunk_tokens": -1,
#                 "max_prompt_tokens": 750,
#             },
#         },
#     ),
#     # === Stage 6 ===
#     stage_6=dict(
#         min_pos_ppg=0.12,
#         min_pos_ppl=-0.4,
#         max_neg_ppg=0.03,
#         num_positives=6,
#         total_chunks=32,
#     ),
#     # === Stage 7 ===
    # stage_7=dict(
    #     encoder_seq_length=1024,
    #     query_formatter={
    #         "name": "ethanol3_query",
    #         "max_tokens": 1024 - 1,
    #         "max_lines": 20,
    #         "add_path": True,
    #         "retokenize": True,
    #     },
    #     key_formatter={
    #         "name": "simple_document",
    #         "add_path": True,
    #         "max_tokens": 1024 - 1,
    #     },
    #     max_retrieved_docs=32 - 1,
    # ),
# )
#%% md
# Step 1: Sample and filters Repos
#%%
STAGE1_URI = os.path.join(S3ROOT, "stage_1_repo")
print(STAGE1_URI)
#%%
def stage1():
    spark = k8s_session(max_workers=64)

    print("Processing retrieval samples")
    df = spark.read.parquet(RAW_REPO_DATA)

    languages = CONFIG["allowed_languages"]
    if languages:
        languages = [lang.lower() for lang in languages]
        df = df.filter(
            df[constants.REPO_LANG_COLUMN][constants.REPO_LANG_SUBCOL].isin(languages)
        )

    # if hasattr(config, "retrieval_languages"):
    #     config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]

    df = common.filter_by_repo_size(
        df, min_size=CONFIG['stage_1']["repo_min_size"], max_size=CONFIG['stage_1']["repo_max_size"]
    )

    print(f"Processing {df.count()} repos", flush=True)
    df = df.limit(CONFIG['stage_1']["limit_repos"])

    # About 100 to 200 repos per partition.
    df = df.repartition(2000)
    # Perform repo-specific processing
    df.write.parquet(STAGE1_URI, mode="overwrite")
    spark.stop()


    for e in result["task_info"]["stderr"]:
        print(e)
        break
    return result


result = stage1()
#%% md
# Step 2: Run Retrieval Augmentation

This is one job per repo.
#%%
# STAGE1_URI = os.path.join("s3a://augment-temporary/vzhao/ethanol_rag/test", "stage_1")
STAGE2_URI = os.path.join(S3ROOT, "stage_2_retrieval")
#%%
import pandas as pd
from megatron.tokenizer.tokenizer import StarCoderTokenizer


# This processes one partition of the dataset.
# now we know that batch sizes really isn't that much a deal.
# most of the memory is used by treesitter for its leaks



def process_partition_pandas(
    batch: pd.DataFrame) -> Iterable[pd.Series]:
    """Process a single partition of the dataset.

    Args:
        batch: A single partition of the dataset.
        config: The configuration object.

    Returns:
        A generator of processed rows.
    """
    # TODO(michiel) update for retriever query formatting options
    retrieval_database = create_retriever(CONFIG['retriever'])

    if CONFIG['retriever']['name'] != "bm25":
        retrieval_database.scorer.load()

    sampler = CSTFimSampler()
    sampler.rng.seed(CONFIG['random_seed'])

    tokenizer = StarCoderTokenizer()

    for files in batch.file_list:
        yield from common.process_repo(
            files,
            sampler=sampler,
            retrieval_database=retrieval_database,
            tokenizer=tokenizer,
            allowed_languages=CONFIG['allowed_languages'],
            downsample_small=CONFIG['downsample_small'],
            every_n_lines=CONFIG['every_n_lines'],
            max_problems_per_file=CONFIG['max_problems_per_file'],
            random_seed=CONFIG['random_seed'],
            max_prefix_chars=CONFIG['max_prefix_chars'],
            max_suffix_chars=CONFIG['max_suffix_chars'],
            num_retrieved_chunks=CONFIG['num_retrieved_chunks'],
        )
#%%
def stage2():
    spark_conf = {
        "spark.executor.pyspark.memory": "50G",
        "spark.executor.memory": "30G",
        "spark.sql.parquet.columnarReaderBatchSize": "256",
        "spark.task.cpus": "5",
    }
    spark = k8s_session(
        max_workers=128,
        conf=spark_conf,
        gpu_type="RTX_A5000",
    )
    result = map_parquet.apply_pandas(
        spark,
        process_partition_pandas,
        input_path=STAGE1_URI,
        output_path=STAGE2_URI,
        timeout=3600,  # one hour timeout
        batch_size=100,
    )
    spark.stop()

    for e in result["task_info"]["stderr"]:
        print(e)
        break
    return result


result = stage2()
#%%
STAGE2_URI
#%% md
# Step 4: Reshuffle
#%%
!s3cmd ls s3://augment-temporary/vzhao/ppl_gain/
#%%
STAGE4_URI = os.path.join(S3ROOT, 'stage_4_reshuffle')
STAGE4_URI
#%%
common.reshuffle(
    STAGE2_URI,
    STAGE4_URI,
    num_rows=CONFIG['num_rows'],
    num_partitions=CONFIG['num_partitions'],
    columns=[
        "prefix",
        "suffix",
        "middle",
        "file_path",
        "retrieved_chunks",
    ],
    max_workers=32,
    override=True,
)
#%% md
# Stage 5a: Compute Perplexity

Stage 5a works on computing PPL for all retrieved chunks from scratch.
#%%
# CONFIG['language_model'] = {
#     "checkpoint_path": "rogue/diffb1m_1b_alphal_fixtoken",
#     "name": "rogue",
#     "prompt": {
#         "max_prefix_tokens": 100,
#         "max_suffix_tokens": 100,
#         "max_retrieved_chunk_tokens": -1,
#         "max_prompt_tokens": 750,
#     }
# }
#%%
STAGE5_URI = os.path.join(S3ROOT, 'stage_5_ppg')
STAGE5_URI
#%%
%load_ext autoreload
%autoreload 2
from research.data.spark.pipelines.utils import map_parquet
#%%
from research.eval.harness.factories import create_reranker, create_model
import datetime


@map_parquet.allow_unused_args
def prepend_empty_chunk(retrieved_chunks) -> pd.Series:
    retrieved_chunks = [EMPTY_CHUNK] + common.deserialize_retrieved_chunks(
        retrieved_chunks
    )
    return pd.Series(
        {"retrieved_chunks": common.serialize_retrieved_chunks(retrieved_chunks)}
    )


def compute_ppl(
    prefix,
    suffix,
    middle,
    file_path,
    retrieved_chunks,
) -> pd.Series:
    def print_with_time(*args):
        print(datetime.datetime.now().strftime("%d.%b %Y %H:%M:%S"), *args)

    global cached_reranker
    if "cached_reranker" not in globals():
        # Construct a reranker
        print_with_time("Constructing the model...")
        model = create_model(CONFIG["language_model"])

        print_with_time("Constructing the reranker...")
        cached_reranker = create_reranker(
            model,
            config={
                "name": "oracle_perplexity_reranker",
                "top_k": 256,
                "batchsize": 4,
            },
        )

        # Load the reranking model
        print_with_time("Loading the model...")
        model.load()

    model_input = ModelInput(
        prefix=prefix,
        suffix=suffix,
        retrieved_chunks=common.deserialize_retrieved_chunks(retrieved_chunks),
        path=file_path,
    )

    print_with_time(f"Reranking...")
    scores = cached_reranker._score(model_input, middle)
    assert len(scores) == len(
        common.deserialize_retrieved_chunks(retrieved_chunks)
    ), f"{len(scores)} {len(common.deserialize_retrieved_chunks(retrieved_chunks))}"
    return pd.Series({"ppl": json.dumps(scores)})


@map_parquet.allow_unused_args
def compute_ppl_gain(retrieved_chunks, ppl):
    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunks)
    ppl = json.loads(ppl)

    ppl_empty = ppl[0]
    ppg = [s - ppl_empty for s in ppl]
    retrieval_rank = list(range(len(retrieved_chunks)))
    retrieval_rank, ppg, ppl, retrieved_chunks = list(
        zip(
            *sorted(
                zip(retrieval_rank, ppg, ppl, retrieved_chunks),
                key=lambda x: x[1],
                reverse=True,
            )
        )
    )
    return pd.Series(
        {
            "ppg": json.dumps(ppg),
            "ppl": json.dumps(ppl),
            "retrieved_chunks": common.serialize_retrieved_chunks(retrieved_chunks),
            "retrieval_rank": json.dumps(retrieval_rank),
        }
    )


def stage5a():
    spark_gpu = k8s_session(
        max_workers=128,
        conf={
            "spark.executor.pyspark.memory": "50G",
            "spark.executor.memory": "30G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        gpu_type="RTX_A5000",
    )
    files = map_parquet.list_files(
        spark_gpu, STAGE4_URI, suffix="parquet", include_path=False
    )

    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                prepend_empty_chunk,
                compute_ppl,
                compute_ppl_gain,
            ]
        ),
        # input_path=STAGE4_URI,
        input_path=STAGE4_URI,
        output_path=STAGE5_URI,
        # timing_run=True,
        # profile=True,
        timeout=7200,
    )

    spark_gpu.stop()

    for e in result["task_info"]["stderr"]:
        print(e)
        break
    return result


result = stage5a()
#%% md
# Stage 5b: Add PPL of Empty Chunk.

Prepend ppl of empty chunk to the existing data.
#%% md
## Sanity Check: Rescore all chunks and compare to the existing ppl
#%%
# IGOR_STAGE5_URI = "s3a://igor-dev-bucket/perplexity_distill4.01/05_with_ppl_scores/"
# IGOR_STAGE5_URI = "s3a://igor-dev-bucket/ethanol5/ethanol5-01/05_with_ppl_scores/"
IGOR_STAGE5_URI = "s3a://augment-temporary/vzhao/ppl_gain/1027_1b_sample_llm_rescore/stage_7_rescore_7b/"
STAGE5_URI = os.path.join(S3ROOT, "stage_5b_debug")
print(IGOR_STAGE5_URI)
print(STAGE5_URI)
#%%
# !s3cmd ls {STAGE5_URI.replace('s3a', 's3')}
!s3cmd rm {STAGE5_URI.replace('s3a', 's3') + '/*'}
#%%
%load_ext autoreload
%autoreload 2
from typing import Iterator
from research.eval.harness.factories import create_reranker, create_model
import datetime
from experimental.vzhao.data import pandas_functions as vz_pdfn


def stage5b_debug():
    spark_gpu = k8s_session(
        name="vzhao-sanity",
        max_workers=16,
        conf={
            "spark.executor.pyspark.memory": "80G",
            # "spark.executor.pyspark.memory": "50G",
            "spark.executor.memory": "30G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        # gpu_type="A40",
        gpu_type="A100_NVLINK_80GB",
        gpu_count=1,
    )
    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                vz_pdfn.ComputeSequenceLength(),
                vz_pdfn.ComputePPL(
                    {
                        "checkpoint_path": "rogue/diffb1m_7b_alphal_fixtoken",
                        "name": "rogue",
                        "prompt": {
                            "max_prefix_tokens": 256,
                            "max_suffix_tokens": 256,
                            "max_retrieved_chunk_tokens": -1,
                            "max_prompt_tokens": 3072,
                        },
                    },
                    score_batch_size=2,
                    output_col_name="ppl_1",
                ),
                vz_pdfn.ComputePPL(
                    {
                        "checkpoint_path": "rogue/diffb1m_7b_alphal_fixtoken",
                        "name": "rogue",
                        "prompt": {
                            "max_prefix_tokens": 1280,
                            "max_suffix_tokens": 768,
                            "max_retrieved_chunk_tokens": -1,
                            "max_prompt_tokens": 3072,
                        },
                    },
                    score_batch_size=2,
                    output_col_name="ppl_2",
                ),
            ]
        ),
        input_path=IGOR_STAGE5_URI,
        output_path=STAGE5_URI,
        timing_run=True,
        timeout=7200,
        batch_size=4,
    )

    spark_gpu.stop()

    for e in result["task_info"]["stderr"]:
        print(e)
        break
    return result


result = stage5b_debug()
#%% md
## Main
#%%
%load_ext autoreload
%autoreload 2
import os
from research.data.spark.pipelines.utils import map_parquet
#%%
CONFIG["stage_5"] = dict(
    language_model={
        "checkpoint_path": "rogue/diffb1m_7b_alphal_fixtoken",
        "name": "rogue",
        "prompt": {
            "max_prefix_tokens": 256,
            "max_suffix_tokens": 256,
            "max_retrieved_chunk_tokens": -1,
            "max_prompt_tokens": 3072,
        },
    },
)


# IGOR_STAGE5_URI = "s3a://igor-dev-bucket/perplexity_distill4.01/05_with_ppl_scores/"
IGOR_STAGE5_URI = "s3a://igor-dev-bucket/ethanol5/ethanol5-01/05_with_ppl_scores/"
STAGE5_URI = os.path.join(S3ROOT, "stage_5_ppg")
print(IGOR_STAGE5_URI)
print(STAGE5_URI)
print(CONFIG["stage_5"])
#%%
!s3cmd ls {STAGE5_URI.replace('s3a', 's3')}
# !s3cmd rm {STAGE5_URI.replace('s3a', 's3') + '/*'}
#%%
from typing import Iterator
from research.eval.harness.factories import create_reranker, create_model
import datetime


class PrependScoreEmptyChunk:
    def __init__(self):
        self._reranker = None

    @map_parquet.allow_unused_args
    def __call__(
        self,
        prefix,
        suffix,
        middle,
        file_path,
        retrieved_chunks,
        ppl_scores,
        retrieval_rank,
    ) -> Iterator[pd.Series]:
        def print_with_time(*args):
            print(datetime.datetime.now().strftime("%d.%b %Y %H:%M:%S"), *args)

        if self._reranker is None:
            # Construct a reranker
            print_with_time("Constructing the model...")
            model = create_model(CONFIG["stage_5"]["language_model"])

            print_with_time("Constructing the reranker...")
            self._reranker = create_reranker(
                model,
                config={
                    "name": "oracle_perplexity_reranker",
                    "top_k": 256,
                    "batchsize": 2,
                },
            )
            # Load the reranking model
            print_with_time("Loading the model...")
            model.load()

        model_input = ModelInput(
            prefix=prefix,
            suffix=suffix,
            retrieved_chunks=[EMPTY_CHUNK],
            path=file_path,
        )
        print_with_time(f"Scoring...")
        
        middel_len = len(self._reranker.model.tokenizer.tokenize(middle))
        if middel_len > self._reranker.model.prompt_formatter.max_prompt_tokens:
            # Target too long.
            return
        scores = self._reranker._score(model_input, middle)
        assert len(scores) == 1, f"`scores` should contain only 1 score."
        yield pd.Series(
            {
                "ppl": json.dumps([scores[0]] + json.loads(ppl_scores)),
                "retrieved_chunks": common.serialize_retrieved_chunks(
                    [EMPTY_CHUNK]
                    + common.deserialize_retrieved_chunks(retrieved_chunks)
                ),
                'retrieval_rank': json.dumps([-1] + json.loads(retrieval_rank))
            }
        )


def stage5b():
    spark_gpu = k8s_session(
        name="vzhao-label-balance-stage5",
        max_workers=64,
        conf={
            "spark.executor.pyspark.memory": "50G",
            "spark.executor.memory": "30G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        gpu_type="A40",
        gpu_count=1,
    )
    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                vz_pdfn.add_retriever_rank,
                PrependScoreEmptyChunk(),
                vz_pdfn.compute_ppl_gain,
                vz_pdfn.sort_by_ppg,
            ]
        ),
        input_path=IGOR_STAGE5_URI,
        output_path=STAGE5_URI,
        timeout=7200,
        batch_size=512,
    )

    spark_gpu.stop()

    for e in result["task_info"]["stderr"]:
        print(e)
        break
    return result


result = stage5b()
#%% md
# Stage 6: Label Balancing
If change parameters, regenerate data from here.
#%%
%load_ext autoreload
%autoreload 2
import pandas as pd
import numpy as np
from typing import Iterator

from research.data.spark.pipelines.utils import map_parquet


def create_balance_labels_fn(
    min_pos_ppg: float,
    min_pos_ppl: float,
    max_neg_ppg: float,
    num_positives: int,
    total_chunks: int,
):
    """Returns a row wise function for hard example mining and label balancing.
    
    Logics:
      Take top `max_positives` chunks whose `ppg` > `min_pos_ppg`.
      Randomly sample  `total_chunks` - `num_positives` from chunks whose `ppg` < `max_neg_ppg`.
    
    """
    @map_parquet.passthrough_feature
    @map_parquet.allow_unused_args
    def balance_labels(
        retrieved_chunks, ppl, ppg, retrieval_rank
    ) -> Iterator[pd.Series]:
        """Balance the labels of retrieved chunks."""
        df = pd.DataFrame(
            {
                "retrieved_chunks": common.maybe_deserialize_retrieved_chunks(
                    retrieved_chunks
                ),
                "ppl": common.maybe_json_loads(ppl),
                "ppg": common.maybe_json_loads(ppg),
                "retrieval_rank": common.maybe_json_loads(retrieval_rank),
            }
        )
        # Discard empty chunk.
        df = df.loc[df['ppg']!=0, :]
        # Takes top `max_positives` as positives.
        candidates = df[(df['ppg'] > min_pos_ppg) & (df['ppl'] > min_pos_ppl)]
        df_positives = candidates.sort_values('ppg', ascending=False).iloc[:num_positives, :]
        if df_positives.empty:
            return
        assert df_positives.shape[0] <= num_positives, f'Bad number of positives: {df_positives.shape[0]}'

        # Randomly select nagatives.
        num_negatives = total_chunks - df_positives.shape[0]
        df_negatives = df.loc[np.logical_and(df['ppg'] < max_neg_ppg, df['ppg'] != 0), :]
        if df_negatives.shape[0] < num_negatives:
            return
        df_negatives = df_negatives.sample(n=num_negatives, random_state=0)
        df_negatives = df_negatives.sample(num_negatives)
        # All negatives should have ppg=0.
        df_negatives["ppg"] = 0

        df_concat = pd.concat([df_positives, df_negatives], axis=0)
        yield pd.Series(
            {
                "retrieved_chunks": common.serialize_retrieved_chunks(df_concat["retrieved_chunks"].to_list()),
                "ppl": json.dumps(df_concat["ppl"].to_list()),
                "ppg": json.dumps(df_concat["ppg"].to_list()),
                "retrieval_rank": json.dumps(df_concat["retrieval_rank"].to_list()),
            }
        )

    return balance_labels
#%%
CONFIG["stage_6"] = dict(
    min_pos_ppg=0.12,
    min_pos_ppl=-1.0,
    max_neg_ppg=0.03,
    num_positives=6,
    total_chunks=32,
)

STAGE5_URI = "s3a://augment-temporary/vzhao/ppl_gain/1011_6pos_32total_pos_ppl_0.4/stage_5_ppg"
# STAGE6_URI = os.path.join(S3ROOT, f"stage_6_label")
STAGE6_URI = os.path.join(S3ROOT, f"test")
print(STAGE5_URI)
print(STAGE6_URI)
display(CONFIG["stage_6"])
#%%
def stage6():
    spark_gpu = k8s_session(max_workers=64)

    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                create_balance_labels_fn(
                    min_pos_ppg=CONFIG["stage_6"]["min_pos_ppg"],
                    min_pos_ppl=CONFIG["stage_6"]["min_pos_ppl"],
                    max_neg_ppg=CONFIG["stage_6"]["max_neg_ppg"],
                    num_positives=CONFIG["stage_6"]["num_positives"],
                    total_chunks=CONFIG["stage_6"]["total_chunks"],
                )
            ]
        ),
        input_path=STAGE5_URI,
        output_path=STAGE6_URI,
        # timing_run=True,
        # profile=True,
        timeout=7200,
    )

    spark_gpu.stop()

    for e in result["task_info"]["stderr"]:
        print(e)
        break
    return result


result = stage6()
#%% md
# Stage 7: Tokenize
#%%
from research.core.abstract_prompt_formatter import get_prompt_formatter
from research.retrieval import utils
from research.data.spark.pipelines.stages import common as spark_common
import numpy as np


def create_prompt_formatter(formatter_config):
    from research.retrieval.prompt_formatters import SimpleQueryFormatter
    import experimental.igor.systems.ethanol
    cls_name, kwargs = utils.parse_yaml_config(formatter_config)
    return get_prompt_formatter(cls_name, **kwargs)


@map_parquet.allow_unused_args
def pack_prompts(
    prefix,
    suffix,
    file_path,
    retrieved_chunks,
    ppg,
) -> pd.Series:
    from research.retrieval import prompt_formatters

    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunks)
    # XXX temporary hack
    retrieved_chunks = retrieved_chunks[: CONFIG['stage_7']["max_retrieved_docs"]]
    ppg_scores = json.loads(ppg)

    global cached_query_prompt_formatter, cached_key_prompt_formatter
    if "cached_query_prompt_formatter" not in globals():
        cached_query_prompt_formatter = create_prompt_formatter(
            CONFIG['stage_7']["query_formatter"]
        )
    if "cached_key_prompt_formatter" not in globals():
        cached_key_prompt_formatter = create_prompt_formatter(CONFIG['stage_7']["key_formatter"])
    # NOTE: The special query token is NOT added by  `cached_query_prompt_formatter`.
    end_of_query_token = cached_query_prompt_formatter.tokenizer.vocab[
        "<|ret-endofquery|>"
    ]
    query_prompt = cached_query_prompt_formatter.prepare_prompt(
        ModelInput(prefix=prefix, path=file_path)
    )
    query_prompt.append(end_of_query_token)
    if len(query_prompt) > CONFIG['stage_7']["encoder_seq_length"]:
        raise ValueError(
            f"Query token length exceeds seq_len: {len(query_prompt)} > {CONFIG['stage_7']['encoder_seq_length']}"
        )

    prompt_list = [query_prompt]

    counter_prompts, counter_long_prompts = 0, 0
    end_of_key_token = cached_key_prompt_formatter.tokenizer.vocab["<|ret-endofkey|>"]
    pad_token = cached_key_prompt_formatter.tokenizer.pad_id
    for ppg_score, chunk in zip(ppg_scores, retrieved_chunks):
        counter_prompts += 1
        # Encode the perplexity score into tokens.
        ppg_tokens = cached_key_prompt_formatter.tokenizer.tokenize(f"{ppg_score}")
        # Format the suffix of the prompt
        suffix = [end_of_key_token] + ppg_tokens + [pad_token]

        # Format the prompt
        chunk_prompt = cached_key_prompt_formatter.prepare_prompt(
            ModelInput(retrieved_chunks=[chunk], path=chunk.parent_doc.path)
        )
        len_to_keep = CONFIG['stage_7']["encoder_seq_length"] - len(suffix)
        if len(chunk_prompt) > len_to_keep:
            counter_long_prompts += 1
        chunk_prompt = chunk_prompt[:len_to_keep]
        chunk_prompt.extend(suffix)
        prompt_list.append(chunk_prompt)

    for id, chunk_prompt in enumerate(prompt_list):
        if len(chunk_prompt) > CONFIG['stage_7']["encoder_seq_length"]:
            print("===================================================")
            print(cached_key_prompt_formatter.tokenizer.detokenize(chunk_prompt))
            print("===================================================")
            raise ValueError(
                f"{id} token length exceeds seq_len: {len(chunk_prompt)} > {CONFIG['stage_7']['encoder_seq_length']}"
            )

    print("LONG PROMPTS", counter_long_prompts, "/", counter_prompts)

    # This is consumed by `common.unpack_tokens`.
    all_tokens = [
        spark_common.pack_tokens(
            np.pad(prompt, (0, 1 + CONFIG['stage_7']["encoder_seq_length"] - len(prompt)))
        )
        for prompt in prompt_list
    ]
    return pd.Series({"prompt_tokens": all_tokens})
#%%
from experimental.igor.systems import ethanol

CONFIG["stage_7"] = dict(
    encoder_seq_length=1024,
    query_formatter={
        "name": "ethanol_plus_query_1",
        "max_tokens": 1024 - 1,
        "max_lines": -1,
        "add_path": True,
        'preamble': "# Piece of code for code completion.",
        "retokenize": True,
    },
    key_formatter={
        "name": "ethanol_plus_doc_1",
        "add_path": True,
        'preamble': "# Code chunk for retrieval augmentation.",
        "max_tokens": 1024 - 1,
    },
    max_retrieved_docs=32 - 1,
)
STAGE6_URI = 's3a://augment-temporary/vzhao/ppl_gain/1022_6pos_32total_posppl_1.0/stage_6_label'
STAGE7_URI = os.path.join(S3ROOT, f'stage_7_token{VERSION_SUFFIX}')
print(STAGE6_URI)
print(STAGE7_URI)
display(CONFIG['stage_7'])
#%%
def stage7():
    spark = k8s_session(max_workers=96)

    results = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                pack_prompts,
            ]
        ),
        input_path=STAGE6_URI,
        output_path=STAGE7_URI,
        timeout=7200,
    )
    spark.stop()

    for e in results["task_info"]["stderr"]:
        print(e)
        break


stage7()
#%% md
# Stage 8: Explode
#%%
import json
import pyspark.sql.functions as F
import pyspark.sql.types as T

def to_list(chunk_str: str) -> list[str]:
    return [json.dumps(c) for c in json.loads(chunk_str)]

ret_chunk_udf = F.udf(lambda x: to_list(x), T.ArrayType(T.StringType()))
float_array_udf = F.udf(lambda x: json.loads(x), T.ArrayType(T.DoubleType()))
int_array_udf = F.udf(lambda x: json.loads(x), T.ArrayType(T.IntegerType()))
#%%
STAGE8_URI = os.path.join(S3ROOT, f'stage_8_explode{VERSION_SUFFIX}')
print(STAGE7_URI)
print(STAGE8_URI)
print(CONFIG['stage_7'])
#%%
spark = k8s_session(max_workers=64)

df = spark.read.parquet(STAGE7_URI)
df = df.filter(F.size(F.col("prompt_tokens")) == CONFIG['stage_7']['max_retrieved_docs'] + 1)
df = df.withColumn("prompt_tokens", F.explode("prompt_tokens"))

df.write.parquet(STAGE8_URI)
spark.stop()
#%%
# spark = k8s_session(max_workers=128)

# df = spark.read.parquet(STAGE7_URI)
# df = df.filter(F.size(F.col("prompt_tokens")) == CONFIG["max_retrieved_docs"] + 1)

# df = df.select(
#     "prefix",
#     "suffix",
#     "middle",
#     "file_path",
#     F.explode(
#         F.arrays_zip(
#             ret_chunk_udf(F.col("retrieved_chunks")).alias("retrieved_chunks"),
#             float_array_udf(F.col("ppl_scores")).alias("ppl_scores"),
#             float_array_udf(F.col("ppl")).alias("ppl"),
#             float_array_udf(F.col("ppg")).alias("ppg"),
#             int_array_udf(F.col("retrieval_rank")).alias("retrieval_rank"),
#             "prompt_tokens",
#         )
#     ).alias("zipped"),
# ).select(
#     "prefix",
#     "suffix",
#     "middle",
#     "file_path",
#     "zipped.retrieved_chunks",
#     "zipped.ppl_scores",
#     "zipped.ppl",
#     "zipped.ppg",
#     "zipped.retrieval_rank",
#     "zipped.prompt_tokens",
# )

# df.write.parquet(STAGE8_URI)
# spark.stop()
#%%
# spark = k8s_session()

# df=spark.read.parquet(STAGE6_URI)
# print(df.filter(F.size(F.col("prompt_tokens")) < max_retrieved_docs + 1).count())

# spark.stop()
#%% md
# Stage 9: Convert to Dataset for Training
#%%
from research.data.spark.pipelines.stages.common import export_indexed_dataset

class ObjectDict(dict):
    """Provides both namespace-like and dict-like access to fields.

    Allows access to fields using both obj.name notation and obj["name"]
    notation. The latter is useful when "name" contains periods, for example.
    """

    def __getattr__(self, name: str):
        if name in self:
            return self[name]
        else:
            raise AttributeError("No such attribute: " + name)

    def __setattr__(self, name: str, value):
        self[name] = value

    def __delattr__(self, name: str):
        if name in self:
            del self[name]
        else:
            raise AttributeError("No such attribute: " + name)

print(STAGE8_URI)
print(DATA_OUTPUT_PATH)
print(CONFIG['stage_7'])
#%%
STAGE8_URI = 's3a://augment-temporary/vzhao/ppl_gain/1101_1b_128total_long_pre_filepath/stage_8_explode/'
DATA_OUTPUT_PATH = '/mnt/efs/augment/user/vincent/data/ppl_gain/1101_1b_128total_long_pre_filepath'

print(STAGE8_URI)
print(DATA_OUTPUT_PATH)
print(CONFIG['stage_7'])
#%%
import os

if not os.path.exists(DATA_OUTPUT_PATH):
    os.makedirs(DATA_OUTPUT_PATH)

spark = k8s_session(64)

export_indexed_dataset(
    config=ObjectDict(
        {
            "input": STAGE8_URI,
            "output": DATA_OUTPUT_PATH,
            "samples_column": "prompt_tokens",
            'num_validation_samples': 8192,
        }
    ),
    spark=spark,
    tokenizer=create_prompt_formatter(CONFIG['stage_7']['query_formatter']).tokenizer
)

spark.stop()
#%% md
# TO Kill a Running Spark Job

```bash
# Get appName
kubectl get pods | grep vzhao

```
#%%
from pyspark.sql import SparkSession

spark= SparkSession.builder.appName('vzhao-sanity-ea9cd08b72c21703').getOrCreate()
spark.stop()