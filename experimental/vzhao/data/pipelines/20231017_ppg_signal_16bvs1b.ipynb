#%%
# My custom library.
%load_ext autoreload
%autoreload 2


# Imports
import json
import os
import logging
from functools import partial
from types import SimpleNamespace
from typing import Any, Generator, List, Mapping, Sequence, Iterable
import random

import pandas as pd
import pyspark.sql.functions as F

from research.data.spark import k8s_session, get_session
from research.data.spark.pipelines.utils import map_parquet
from research.eval.harness.factories import create_retriever
from research.core.types import EMPTY_CHUNK
from research.retrieval.types import Chunk, Document
from research.static_analysis.file_language_estimator import guess_lang_from_fp
from research.static_analysis.fim_prompt import _format_middle
from research.static_analysis.fim_sampling import CSTFimSampler, FimProblem
from research.static_analysis.usage_analysis import ParsedFile
from research.core.model_input import ModelInput


from experimental.vzhao.data import common
from experimental.vzhao.data import constants
#%% md
# CONFIG
#%%
CONFIG = dict(
    allowed_languages=["python", "go", "java", "javascript", "rust", "typescript"],
    random_seed=74912,
    # === Stage 5 Compute Perplexity Signal ===
    stage_4=dict(
        num_rows=1024,
        num_partitions=16,
    ),
    # === Stage 5 Compute Perplexity Signal ===
    stage_5=dict(
        language_model={
            "checkpoint_path": "rogue/diffb1m_16b_alphal_fixtoken",
            "name": "rogue",
            "prompt": {
                "max_prefix_tokens": 100,
                "max_suffix_tokens": 100,
                "max_retrieved_chunk_tokens": -1,
                "max_prompt_tokens": 750,
            },
        },
    ),
)
#%%
# Data paths.
RAW_REPO_DATA = "s3a://the-stack-processed/by-repo"

# S3ROOT = "s3a://augment-temporary/vzhao/ppl_gain/test"
S3ROOT = "s3a://augment-temporary/vzhao/ppl_gain/1017_ppg_signal_16bvs1b"
VERSION_SUFFIX = "_rouge16b"

#%% md
# Step 4: Reshuffle
#%%
STAGE4_URI_OLD = "s3a://igor-dev-bucket/perplexity_distill4.01/04_subsampled/"
STAGE4_URI = os.path.join(S3ROOT, 'stage_4_reshuffle')
print(STAGE4_URI_OLD)
print(STAGE4_URI)
print(CONFIG['stage_4'])
#%%
common.reshuffle(
    STAGE4_URI_OLD,
    STAGE4_URI,
    num_rows=CONFIG['stage_4']['num_rows'],
    num_partitions=CONFIG['stage_4']['num_partitions'],
    columns=[
        "prefix",
        "suffix",
        "middle",
        "file_path",
        "retrieved_chunks",
    ],
    max_workers=32,
    override=True,
)
#%% md
# Stage 5a: Compute Perplexity

Stage 5a works on computing PPL for all retrieved chunks from scratch.
#%%
STAGE5_URI = os.path.join(S3ROOT, f'stage_5_ppg{VERSION_SUFFIX}')
print(STAGE4_URI)
print(STAGE5_URI)
#%%
%load_ext autoreload
%autoreload 2
from research.data.spark.pipelines.utils import map_parquet
from research.eval.harness.factories import create_reranker, create_model
import datetime


@map_parquet.allow_unused_args
def prepend_empty_chunk(retrieved_chunks) -> pd.Series:
    retrieved_chunks = [EMPTY_CHUNK] + common.deserialize_retrieved_chunks(
        retrieved_chunks
    )
    return pd.Series(
        {"retrieved_chunks": common.serialize_retrieved_chunks(retrieved_chunks)}
    )


def compute_ppl(
    prefix,
    suffix,
    middle,
    file_path,
    retrieved_chunks,
) -> pd.Series:
    def print_with_time(*args):
        print(datetime.datetime.now().strftime("%d.%b %Y %H:%M:%S"), *args)

    global cached_reranker
    if "cached_reranker" not in globals():
        # Construct a reranker
        print_with_time("Constructing the model...")
        model = create_model(CONFIG['stage_5']["language_model"])

        print_with_time("Constructing the reranker...")
        cached_reranker = create_reranker(
            model,
            config={
                "name": "oracle_perplexity_reranker",
                "top_k": 256,
                "batchsize": 4,
            },
        )

        # Load the reranking model
        print_with_time("Loading the model...")
        model.load()

    model_input = ModelInput(
        prefix=prefix,
        suffix=suffix,
        retrieved_chunks=common.deserialize_retrieved_chunks(retrieved_chunks),
        path=file_path,
    )

    print_with_time(f"Reranking...")
    scores = cached_reranker._score(model_input, middle)
    assert len(scores) == len(
        common.deserialize_retrieved_chunks(retrieved_chunks)
    ), f"{len(scores)} {len(common.deserialize_retrieved_chunks(retrieved_chunks))}"
    return pd.Series({"ppl": json.dumps(scores)})


@map_parquet.allow_unused_args
def compute_ppl_gain(retrieved_chunks, ppl):
    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunks)
    ppl = json.loads(ppl)

    ppl_empty = ppl[0]
    ppg = [s - ppl_empty for s in ppl]
    retrieval_rank = list(range(len(retrieved_chunks)))
    retrieval_rank, ppg, ppl, retrieved_chunks = list(
        zip(
            *sorted(
                zip(retrieval_rank, ppg, ppl, retrieved_chunks),
                key=lambda x: x[1],
                reverse=True,
            )
        )
    )
    return pd.Series(
        {
            "ppg": json.dumps(ppg),
            "ppl": json.dumps(ppl),
            "retrieved_chunks": common.serialize_retrieved_chunks(retrieved_chunks),
            "retrieval_rank": json.dumps(retrieval_rank),
        }
    )
#%%
def stage5a():
    spark_gpu = k8s_session(
        max_workers=16,
        conf={
            "spark.executor.pyspark.memory": "50G",
            "spark.executor.memory": "30G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        gpu_type="A40",
    )
    files = map_parquet.list_files(
        spark_gpu, STAGE4_URI, suffix="parquet", include_path=False
    )

    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                prepend_empty_chunk,
                compute_ppl,
                compute_ppl_gain,
            ]
        ),
        input_path=[os.path.join(STAGE4_URI, f) for f in files[:10]],
        output_path=STAGE5_URI,
        # timing_run=True,
        # profile=True,
        timeout=7200,
    )

    spark_gpu.stop()

    for e in result["task_info"]["stderr"]:
        print(e)
        break
    return result


result = stage5a()
#%%
