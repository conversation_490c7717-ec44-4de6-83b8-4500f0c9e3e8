#%%
# My custom library.
%load_ext autoreload
%autoreload 2


# Imports
import json
import os
import logging
from functools import partial
from types import SimpleNamespace
from typing import Any, Generator, List, Mapping, Sequence, Iterable
import random

import pandas as pd
import pyspark.sql.functions as F

from augment.research.data.spark import k8s_session, get_session
from augment.research.data.spark.pipelines.utils import map_parquet
from augment.research.eval.harness.factories import create_retriever
from augment.research.core.types import EMPTY_CHUNK
from augment.research.retrieval.types import Chunk, Document
from augment.research.static_analysis.file_language_estimator import guess_lang_from_fp
from augment.research.static_analysis.fim_prompt import _format_middle
from augment.research.static_analysis.fim_sampling import CSTFimSampler, FimProblem
from augment.research.static_analysis.usage_analysis import ParsedFile
from augment.research.core.model_input import ModelInput


from augment.experimental.vzhao.data import common
from augment.experimental.vzhao.data import constants
#%%
# Data paths.
RAW_REPO_DATA = "s3a://the-stack-processed/by-repo"

# S3ROOT = "s3a://augment-temporary/vzhao/ppl_gain/test"
# S3ROOT = "s3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hard"
S3ROOT = "s3a://augment-temporary/vzhao/ppl_gain/1012_6pos_32total_hardneg"

# Path to the final dataset for training.
DATA_OUTPUT_PATH = "/mnt/efs/augment/user/vincent/data/ppl_gain/1012_6pos_32total_hardneg"
#%%
CONFIG = dict(
    allowed_languages=["python", "go", "java", "javascript", "rust", "typescript"],
    random_seed=74912,
    # === Stage 1 ===
    # Sampling Repositories.
    repo_min_size=200000,
    repo_max_size=5000000,
    limit_repos=35000,
    downsample_small=True,
    # === Stage 2 ===
    # FIM sampler Configs.
    every_n_lines=150,
    max_problems_per_file=4,
    max_prefix_chars=8000,
    max_suffix_chars=8000,
    # Retrieval Configs.
    num_retrieved_chunks=127,
    retriever={
        "scorer": {
            "name": "diff_boykin",
        },
        "chunker": {
            "name": "line_level",
            "max_lines_per_chunk": 40,
        },
        "query_formatter": {
            "name": "simple_query",
            "max_lines": 20,
        },
    },
    # === No Stage 3 ===
    # === Stage 4 ===
    stage_4=dict(
        num_rows=None,
        num_partitions=4096,
    ),
    # === Stage 5 ===
    stage_5=dict(
        language_model={
            "checkpoint_path": "rogue/diffb1m_1b_alphal_fixtoken",
            "name": "rogue",
            "prompt": {
                "max_prefix_tokens": 100,
                "max_suffix_tokens": 100,
                "max_retrieved_chunk_tokens": -1,
                "max_prompt_tokens": 750,
            },
        },
    ),
    # === Stage 6 ===
    stage_6={
        # Stage 6:
        # args for positives.
        "min_pos_ppg": 0.12,
        "min_pos_ppl": -0.4,
        "num_positives": 6,
        "num_hard_positives": 1,
        "hard_pos_topk": 16,
        # args for negatives.
        "max_neg_ppg": 0.03,
        "num_hard_negatives": 0,
        "hard_neg_topk": 4,
        "total_chunks": 32,
    },
    # === Stage 7 ===
    stage_7=dict(
        encoder_seq_length=1024,
        query_formatter={
            "name": "simple_query",
            "max_tokens": 1024 - 1,
            "max_lines": -1,
            "add_path": False,
            "add_sos": False,
        },
        key_formatter={
            "name": "simple_document",
            "add_path": False,
            "max_tokens": 1024 - 1,
        },
        max_retrieved_docs=32 - 1,
    ),
)
#%% md
# Step 1: Sample and filters Repos
#%%
STAGE1_URI = os.path.join(S3ROOT, "stage_1")
#%%
def stage1():
    spark = k8s_session(max_workers=100)

    print("Processing retrieval samples")
    df = spark.read.parquet(RAW_REPO_DATA)

    languages = CONFIG["allowed_languages"]
    if languages:
        languages = [lang.lower() for lang in languages]
        df = df.filter(
            df[constants.REPO_LANG_COLUMN][constants.REPO_LANG_SUBCOL].isin(languages)
        )

    # if hasattr(config, "retrieval_languages"):
    #     config.retrieval_languages = [lang.lower() for lang in config.retrieval_languages]

    df = common.filter_by_repo_size(
        df, min_size=CONFIG["repo_min_size"], max_size=CONFIG["repo_max_size"]
    )

    print(f"Processing {df.count()} repos", flush=True)
    df = df.limit(CONFIG["limit_repos"])

    # About 100 to 200 repos per partition.
    df = df.repartition(2000)
    # Perform repo-specific processing
    df.write.parquet(STAGE1_URI, mode="overwrite")
    spark.stop()


    for e in result["task_info"]["stderr"]:
        print(e)
        break
    return result


result = stage1()
#%% md
# Step 2: Run Retrieval Augmentation

This is one job per repo.
#%%
# STAGE1_URI = os.path.join("s3a://augment-temporary/vzhao/ethanol_rag/test", "stage_1")
STAGE2_URI = os.path.join(S3ROOT, "stage_2_retrieval")
#%%
import pandas as pd
from megatron.tokenizer.tokenizer import StarCoderTokenizer


# This processes one partition of the dataset.
# now we know that batch sizes really isn't that much a deal.
# most of the memory is used by treesitter for its leaks



def process_partition_pandas(
    batch: pd.DataFrame) -> Iterable[pd.Series]:
    """Process a single partition of the dataset.

    Args:
        batch: A single partition of the dataset.
        config: The configuration object.

    Returns:
        A generator of processed rows.
    """
    # TODO(michiel) update for retriever query formatting options
    retrieval_database = create_retriever(CONFIG['retriever'])

    if CONFIG['retriever']['name'] != "bm25":
        retrieval_database.scorer.load()

    sampler = CSTFimSampler()
    sampler.rng.seed(CONFIG['random_seed'])

    tokenizer = StarCoderTokenizer()

    for files in batch.file_list:
        yield from common.process_repo(
            files,
            sampler=sampler,
            retrieval_database=retrieval_database,
            tokenizer=tokenizer,
            allowed_languages=CONFIG['allowed_languages'],
            downsample_small=CONFIG['downsample_small'],
            every_n_lines=CONFIG['every_n_lines'],
            max_problems_per_file=CONFIG['max_problems_per_file'],
            random_seed=CONFIG['random_seed'],
            max_prefix_chars=CONFIG['max_prefix_chars'],
            max_suffix_chars=CONFIG['max_suffix_chars'],
            num_retrieved_chunks=CONFIG['num_retrieved_chunks'],
        )
#%%
def stage2():
    spark_conf = {
        "spark.executor.pyspark.memory": "50G",
        "spark.executor.memory": "30G",
        "spark.sql.parquet.columnarReaderBatchSize": "256",
        "spark.task.cpus": "5",
    }
    spark = k8s_session(
        max_workers=128,
        conf=spark_conf,
        gpu_type="RTX_A5000",
    )
    result = map_parquet.apply_pandas(
        spark,
        process_partition_pandas,
        input_path=STAGE1_URI,
        output_path=STAGE2_URI,
        timeout=3600,  # one hour timeout
        batch_size=100,
    )
    spark.stop()

    for e in result["task_info"]["stderr"]:
        print(e)
        break
    return result


result = stage2()
#%%
STAGE2_URI
#%% md
# Step 4: Reshuffle
#%%
STAGE4_URI = os.path.join(S3ROOT, 'stage_4_reshuffle')
STAGE4_URI
#%%
common.reshuffle(
    STAGE2_URI,
    STAGE4_URI,
    num_rows=CONFIG['num_rows'],
    num_partitions=CONFIG['num_partitions'],
    columns=[
        "prefix",
        "suffix",
        "middle",
        "file_path",
        "retrieved_chunks",
    ],
    max_workers=32,
    override=True,
)
#%% md
# Stage 5a: Compute Perplexity

Stage 5a works on computing PPL for all retrieved chunks from scratch.
#%%
# CONFIG['language_model'] = {
#     "checkpoint_path": "rogue/diffb1m_1b_alphal_fixtoken",
#     "name": "rogue",
#     "prompt": {
#         "max_prefix_tokens": 100,
#         "max_suffix_tokens": 100,
#         "max_retrieved_chunk_tokens": -1,
#         "max_prompt_tokens": 750,
#     }
# }
#%%
STAGE5_URI = os.path.join(S3ROOT, 'stage_5_ppg')
STAGE5_URI
#%%
%load_ext autoreload
%autoreload 2
from augment.research.data.spark.pipelines.utils import map_parquet
#%%
from augment.research.eval.harness.factories import create_reranker, create_model
import datetime


@map_parquet.allow_unused_args
def prepend_empty_chunk(retrieved_chunks) -> pd.Series:
    retrieved_chunks = [EMPTY_CHUNK] + common.deserialize_retrieved_chunks(
        retrieved_chunks
    )
    return pd.Series(
        {"retrieved_chunks": common.serialize_retrieved_chunks(retrieved_chunks)}
    )


def compute_ppl(
    prefix,
    suffix,
    middle,
    file_path,
    retrieved_chunks,
) -> pd.Series:
    def print_with_time(*args):
        print(datetime.datetime.now().strftime("%d.%b %Y %H:%M:%S"), *args)

    global cached_reranker
    if "cached_reranker" not in globals():
        # Construct a reranker
        print_with_time("Constructing the model...")
        model = create_model(CONFIG["language_model"])

        print_with_time("Constructing the reranker...")
        cached_reranker = create_reranker(
            model,
            config={
                "name": "oracle_perplexity_reranker",
                "top_k": 256,
                "batchsize": 4,
            },
        )

        # Load the reranking model
        print_with_time("Loading the model...")
        model.load()

    model_input = ModelInput(
        prefix=prefix,
        suffix=suffix,
        retrieved_chunks=common.deserialize_retrieved_chunks(retrieved_chunks),
        path=file_path,
    )

    print_with_time(f"Reranking...")
    scores = cached_reranker._score(model_input, middle)
    assert len(scores) == len(
        common.deserialize_retrieved_chunks(retrieved_chunks)
    ), f"{len(scores)} {len(common.deserialize_retrieved_chunks(retrieved_chunks))}"
    return pd.Series({"ppl": json.dumps(scores)})


@map_parquet.allow_unused_args
def compute_ppl_gain(retrieved_chunks, ppl):
    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunks)
    ppl = json.loads(ppl)

    ppl_empty = ppl[0]
    ppg = [s - ppl_empty for s in ppl]
    retrieval_rank = list(range(len(retrieved_chunks)))
    retrieval_rank, ppg, ppl, retrieved_chunks = list(
        zip(
            *sorted(
                zip(retrieval_rank, ppg, ppl, retrieved_chunks),
                key=lambda x: x[1],
                reverse=True,
            )
        )
    )
    return pd.Series(
        {
            "ppg": json.dumps(ppg),
            "ppl": json.dumps(ppl),
            "retrieved_chunks": common.serialize_retrieved_chunks(retrieved_chunks),
            "retrieval_rank": json.dumps(retrieval_rank),
        }
    )


def stage5a():
    spark_gpu = k8s_session(
        max_workers=128,
        conf={
            "spark.executor.pyspark.memory": "50G",
            "spark.executor.memory": "30G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        gpu_type="RTX_A5000",
    )
    files = map_parquet.list_files(
        spark_gpu, STAGE4_URI, suffix="parquet", include_path=False
    )

    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                prepend_empty_chunk,
                compute_ppl,
                compute_ppl_gain,
            ]
        ),
        # input_path=STAGE4_URI,
        input_path=STAGE4_URI,
        output_path=STAGE5_URI,
        # timing_run=True,
        # profile=True,
        timeout=7200,
    )

    spark_gpu.stop()

    for e in result["task_info"]["stderr"]:
        print(e)
        break
    return result


result = stage5a()
#%% md
# Stage 5b: Add PPL of Empty Chunk.

Prepend ppl of empty chunk to the existing data.
#%%
%load_ext autoreload
%autoreload 2
from augment.research.data.spark.pipelines.utils import map_parquet

IGOR_STAGE5_URI = 's3a://igor-dev-bucket/perplexity_distill4.01/05_with_ppl_scores/'
STAGE5_URI = os.path.join(S3ROOT, 'stage_5_ppg')
STAGE5_URI
#%%
from augment.research.eval.harness.factories import create_reranker, create_model
import datetime

@map_parquet.allow_unused_args
def prepend_empty_chunk(retrieved_chunks) -> pd.Series:
    retrieved_chunks = [EMPTY_CHUNK] + common.deserialize_retrieved_chunks(
        retrieved_chunks
    )
    return pd.Series(
        {"retrieved_chunks": common.serialize_retrieved_chunks(retrieved_chunks)}
    )


@map_parquet.allow_unused_args
def prepend_score_empty_chunk(
    prefix, suffix, middle, file_path, retrieved_chunks, ppl_scores
) -> pd.Series:
    def print_with_time(*args):
        print(datetime.datetime.now().strftime("%d.%b %Y %H:%M:%S"), *args)

    global cached_reranker
    if "cached_reranker" not in globals():
        # Construct a reranker
        print_with_time("Constructing the model...")
        model = create_model(CONFIG["language_model"])

        print_with_time("Constructing the reranker...")
        cached_reranker = create_reranker(
            model,
            config={
                "name": "oracle_perplexity_reranker",
                "top_k": 256,
                "batchsize": 4,
            },
        )

        # Load the reranking model
        print_with_time("Loading the model...")
        model.load()

    model_input = ModelInput(
        prefix=prefix,
        suffix=suffix,
        retrieved_chunks=[EMPTY_CHUNK],
        path=file_path,
    )
    print_with_time(f"Scoring...")
    scores = cached_reranker._score(model_input, middle)
    assert len(scores) == 1, f"`scores` should contain only 1 score."
    return pd.Series(
        {
            "ppl": json.dumps([scores[0]] + json.loads(ppl_scores)),
            "retrieved_chunks": common.serialize_retrieved_chunks(
                [EMPTY_CHUNK] + common.deserialize_retrieved_chunks(retrieved_chunks)
            ),
        }
    )

@map_parquet.allow_unused_args
def compute_ppl_gain(retrieved_chunks, ppl):
    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunks)
    ppl = json.loads(ppl)

    ppl_empty = ppl[0]
    ppg = [s - ppl_empty for s in ppl]
    retrieval_rank = list(range(len(retrieved_chunks)))
    retrieval_rank, ppg, ppl, retrieved_chunks = list(
        zip(
            *sorted(
                zip(retrieval_rank, ppg, ppl, retrieved_chunks),
                key=lambda x: x[1],
                reverse=True,
            )
        )
    )
    return pd.Series(
        {
            "ppg": json.dumps(ppg),
            "ppl": json.dumps(ppl),
            "retrieved_chunks": common.serialize_retrieved_chunks(retrieved_chunks),
            "retrieval_rank": json.dumps(retrieval_rank),
        }
    )


def stage5b():
    spark_gpu = k8s_session(
        max_workers=160,
        conf={
            "spark.executor.pyspark.memory": "50G",
            "spark.executor.memory": "30G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        gpu_type="RTX_A5000",
    )
    files = map_parquet.list_files(
        spark_gpu, IGOR_STAGE5_URI, suffix="parquet", include_path=False
    )

    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                prepend_score_empty_chunk,
                compute_ppl_gain,
            ]
        ),
        input_path=IGOR_STAGE5_URI,
        # input_path=[os.path.join(IGOR_STAGE5_URI, files[0])],
        output_path=STAGE5_URI,
        # timing_run=True,
        # profile=True,
        timeout=7200,
    )

    spark_gpu.stop()

    for e in result["task_info"]["stderr"]:
        print(e)
        break
    return result


result = stage5b()
#%% md
# [start-from-here] Stage 6: Hard Example Mining and Label Balancing
#%%
CONFIG.update(
    {
        "stage_6": {
            # Stage 6:
            # args for positives.
            "min_pos_ppg": 0.12,
            "min_pos_ppl": -0.4,
            "num_positives": 6,
            "num_hard_positives": 0,
            "hard_pos_topk": 16,
            # args for negatives.
            "max_neg_ppg": 0.03,
            "num_hard_negatives": 8,
            "hard_neg_topk": 8,
            "total_chunks": 32,
        }
    }
)
#%%
%load_ext autoreload
%autoreload 2
import pandas as pd
import numpy as np
from typing import Iterator

from augment.research.data.spark.pipelines.utils import map_parquet
from scipy.stats import rankdata, kendalltau

def create_balance_labels_fn(
    *,
    # args for positives.
    min_pos_ppg: float,
    min_pos_ppl: float,
    num_positives: int,
    num_hard_positives: int,
    hard_pos_topk: int,
    # args for negatives.
    max_neg_ppg: float,
    num_hard_negatives: int,
    hard_neg_topk: int,
    total_chunks: int,
):
    """Returns a row wise function for hard example mining and label balancing.

    Logics:
      * Hard Positive Mining:
        * Find all chunks whose `ppg` > `min_pos_ppg` and `ppl` > `min_pos_ppl`.
        * Sort chunks by reverse `retrieval_rank`
        * Take top `max_positives`.
      * Hard Negative Mining:
        * Find all chunks whose `ppg` < `max_neg_ppg`.
        * Sort chunks by `retrieval_rank`
        * Take top `total_chunks` - `num_positives`.
    """
    if num_hard_positives > num_positives:
        raise ValueError(
            f"Number of hard positivies ({num_hard_positives}) should be less than or equal to number of positives ({num_positives})."
        )

    @map_parquet.allow_unused_args
    def balance_labels(
        retrieved_chunks, ppl, ppg, retrieval_rank
    ) -> Iterator[pd.Series]:
        """Balance the labels of retrieved chunks."""
        df = pd.DataFrame(
            {   
                # Only deserialize the first level.
                "retrieved_chunks": [json.dumps(c) for c in json.loads(retrieved_chunks)],
                "ppl": common.maybe_json_loads(ppl),
                "ppg": common.maybe_json_loads(ppg),
                "ppg_raw": common.maybe_json_loads(ppg),
                "retrieval_rank": common.maybe_json_loads(retrieval_rank),
            }
        )
        # Discard empty chunk.
        df = df.loc[df["ppg"] != 0, :]

        # === Positive Mining ===
        df_positives = df[(df["ppg"] > min_pos_ppg) & (df['ppl'] > min_pos_ppl)]
        if df_positives.empty:
            return
        # Candidates for hard positives.
        candidates = df_positives[
            df_positives["retrieval_rank"] > hard_pos_topk
        ].sort_values("retrieval_rank", ascending=False)
        df_hard_pos = candidates.iloc[:num_hard_positives, :]
        if num_hard_positives and df_hard_pos.empty:
            # No hard positives.
            return
        # Candidates for regular positives.
        candidates = df_positives.merge(df_hard_pos, how="left", indicator=True)
        candidates = candidates[candidates["_merge"] == "left_only"]
        df_other_pos = candidates.sort_values("ppg", ascending=False).iloc[
            : (num_positives - df_hard_pos.shape[0]), :
        ]
        # Final dataframe of positives.
        df_positives = pd.concat([df_other_pos, df_hard_pos], axis=0)
        assert (
            df_positives.shape[0] <= num_positives
        ), f"Invalid number of positives: {df_positives.shape[0]}"

        # === Negative Mining ===
        num_negatives = total_chunks - df_positives.shape[0]
        df_negatives = df[df["ppg"] < max_neg_ppg]
        if df_negatives.shape[0] < num_negatives:
            # Not enough negatives to sample from.
            return
        # Candidates for hard negatives.
        nonlocal num_hard_negatives
        num_hard_negatives = min(num_hard_negatives, num_negatives)
        candidates = df_negatives[
            df_negatives["retrieval_rank"] < hard_neg_topk
        ].sort_values("retrieval_rank", ascending=True)
        df_hard_neg = candidates.iloc[:num_hard_negatives, :]
        if num_hard_negatives and df_hard_neg.empty:
            return
        # Candidates for regular negatives.
        candidates = df_negatives.merge(df_hard_neg, how="left", indicator=True)
        candidates = candidates[candidates["_merge"] == "left_only"]
        df_rand_neg = candidates.sample(num_negatives - df_hard_neg.shape[0])
        # Final dataframe of negatives.
        df_negatives = pd.concat([df_rand_neg, df_hard_neg], axis=0)
        # All negatives should have ppg=0.
        df_negatives["ppg"] = 0

        df_concat = pd.concat([df_positives, df_negatives], axis=0)

        llm_ranking = rankdata(-df_concat["ppg"], method='min')
        ret_ranking = rankdata(df_concat["retrieval_rank"], method='min')
        tau = kendalltau(llm_ranking, ret_ranking, variant='b')



        yield pd.Series(
            {
                "retrieved_chunks": json.dumps([json.loads(c) for c in df_concat["retrieved_chunks"].to_list()]), 
                "ppl": json.dumps(df_concat["ppl"].to_list()),
                "ppg": json.dumps(df_concat["ppg"].to_list()),
                "ppg_raw": json.dumps(df_concat["ppg_raw"].to_list()),
                "retrieval_rank": json.dumps(df_concat["retrieval_rank"].to_list()),
                'kendalltau': tau.statistic,
            }
        )

    return balance_labels
#%%
STAGE5_URI = 's3a://augment-temporary/vzhao/ppl_gain/1009/stage_5_ppg/'
STAGE6_URI = os.path.join(S3ROOT, 'stage_6_mining')
print(STAGE6_URI)
display(CONFIG['stage_6'])
#%%
def stage6():
    spark_gpu = k8s_session(max_workers=128)

    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                create_balance_labels_fn(
                    min_pos_ppg=CONFIG['stage_6']["min_pos_ppg"],
                    min_pos_ppl=CONFIG['stage_6']['min_pos_ppl'],
                    num_positives=CONFIG['stage_6']['num_positives'],
                    num_hard_positives=CONFIG['stage_6']['num_hard_positives'],
                    hard_pos_topk=CONFIG['stage_6']['hard_pos_topk'],
                    max_neg_ppg=CONFIG['stage_6']['max_neg_ppg'],
                    num_hard_negatives=CONFIG['stage_6']['num_hard_negatives'],
                    hard_neg_topk=CONFIG['stage_6']["hard_neg_topk"],
                    total_chunks=CONFIG['stage_6']["total_chunks"],
                )
            ]
        ),
        input_path=STAGE5_URI,
        output_path=STAGE6_URI,
        # timing_run=True,
        # profile=True,
        timeout=7200,
    )

    spark_gpu.stop()

    for e in result["task_info"]["stderr"]:
        print(e)
        break
    return result


result = stage6()
#%% md
# Stage 7: Tokenize
#%%
# CONFIG.update(
#     dict(
#         encoder_seq_length=1024,
#         query_formatter={
#             "name": "simple_query",
#             "max_tokens": 1024 - 1,
#             "max_lines": -1,
#             "add_path": False,
#             "add_sos": False,
#         },
#         key_formatter={
#             "name": "simple_document",
#             "add_path": False,
#             "max_tokens": 1024 - 1,
#         },
#         max_retrieved_docs=32-1,
#     )
# )
#%%
from augment.research.retrieval.prompt_formatters import SimpleQueryFormatter
from augment.research.core.abstract_prompt_formatter import get_prompt_formatter
from augment.research.retrieval import utils
from augment.research.data.spark.pipelines.stages import common as spark_common


def create_prompt_formatter(formatter_config):
    cls_name, kwargs = utils.parse_yaml_config(formatter_config)
    return get_prompt_formatter(cls_name, **kwargs)


@map_parquet.allow_unused_args
def pack_prompts(
    prefix,
    suffix,
    file_path,
    retrieved_chunks,
    ppg,
) -> pd.Series:
    from augment.research.retrieval import prompt_formatters

    retrieved_chunks = common.deserialize_retrieved_chunks(retrieved_chunks)
    # XXX temporary hack
    retrieved_chunks = retrieved_chunks[: CONFIG["stage_7"]["max_retrieved_docs"]]
    ppg_scores = json.loads(ppg)

    global cached_query_prompt_formatter, cached_key_prompt_formatter
    if "cached_query_prompt_formatter" not in globals():
        cached_query_prompt_formatter = create_prompt_formatter(
            CONFIG["stage_7"]["query_formatter"]
        )
    if "cached_key_prompt_formatter" not in globals():
        cached_key_prompt_formatter = create_prompt_formatter(
            CONFIG["stage_7"]["key_formatter"]
        )

    end_of_query_token = cached_query_prompt_formatter.tokenizer.vocab[
        "<|ret-endofquery|>"
    ]
    end_of_key_token = cached_key_prompt_formatter.tokenizer.vocab["<|ret-endofkey|>"]
    pad_token = cached_key_prompt_formatter.tokenizer.pad_id

    query_prompt = cached_query_prompt_formatter.prepare_prompt(
        ModelInput(prefix=prefix, path=file_path)
    )
    query_prompt.append(end_of_query_token)
    if len(query_prompt) > CONFIG["stage_7"]["encoder_seq_length"]:
        raise ValueError(
            f"Query token length exceeds seq_len: {len(query_prompt)} > {CONFIG['stage_7']['encoder_seq_length']}"
        )

    prompt_list = [query_prompt]

    counter_prompts, counter_long_prompts = 0, 0
    for ppg_score, chunk in zip(ppg_scores, retrieved_chunks):
        counter_prompts += 1
        # Encode the perplexity score into tokens.
        ppg_tokens = cached_key_prompt_formatter.tokenizer.tokenize(f"{ppg_score}")
        # Format the suffix of the prompt
        suffix = [end_of_key_token] + ppg_tokens + [pad_token]

        # Format the prompt
        chunk_prompt = cached_key_prompt_formatter.prepare_prompt(
            ModelInput(retrieved_chunks=[chunk], path=chunk.parent_doc.path)
        )
        len_to_keep = CONFIG["stage_7"]["encoder_seq_length"] - len(suffix)
        if len(chunk_prompt) > len_to_keep:
            counter_long_prompts += 1
        chunk_prompt = chunk_prompt[:len_to_keep]
        chunk_prompt.extend(suffix)
        prompt_list.append(chunk_prompt)

    for id, chunk_prompt in enumerate(prompt_list):
        if len(chunk_prompt) > CONFIG["stage_7"]["encoder_seq_length"]:
            print("===================================================")
            print(cached_key_prompt_formatter.tokenizer.detokenize(chunk_prompt))
            print("===================================================")
            raise ValueError(
                f"{id} token length exceeds seq_len: {len(chunk_prompt)} > {CONFIG['stage_7']['encoder_seq_length']}"
            )

    print("LONG PROMPTS", counter_long_prompts, "/", counter_prompts)

    # This is consumed by `common.unpack_tokens`.
    all_tokens = [
        spark_common.pack_tokens(
            np.pad(
                prompt, (0, 1 + CONFIG["stage_7"]["encoder_seq_length"] - len(prompt))
            )
        )
        for prompt in prompt_list
    ]
    return pd.Series({"prompt_tokens": all_tokens})
#%%
STAGE7_URI = os.path.join(S3ROOT, 'stage_7_token')
display(CONFIG['stage_7'])
print(STAGE7_URI)
#%%
def stage7():
    spark = k8s_session(max_workers=64)

    mp_result2 = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors([pack_prompts,]),
        input_path=STAGE6_URI,
        output_path=STAGE7_URI,
        # timing_run=True,
        # profile=True,
        timeout=7200,
    )
    spark.stop()

    for e in mp_result2["task_info"]["stderr"]:
        print(e)
        break


stage7()
#%% md
# Stage 8: Explode
#%%
import json
import pyspark.sql.functions as F
import pyspark.sql.types as T

def to_list(chunk_str: str) -> list[str]:
    return [json.dumps(c) for c in json.loads(chunk_str)]

ret_chunk_udf = F.udf(lambda x: to_list(x), T.ArrayType(T.StringType()))
float_array_udf = F.udf(lambda x: json.loads(x), T.ArrayType(T.DoubleType()))
int_array_udf = F.udf(lambda x: json.loads(x), T.ArrayType(T.IntegerType()))
#%%
STAGE8_URI = os.path.join(S3ROOT, 'stage_8_explode')
print(STAGE8_URI)
#%%
# spark = k8s_session(max_workers=128)

# df = spark.read.parquet(STAGE7_URI)
# df = df.filter(
#     F.size(F.col("prompt_tokens")) == CONFIG["stage_7"]["max_retrieved_docs"] + 1
# )

# df = df.select(
#     "prefix",
#     "suffix",
#     "middle",
#     "file_path",
#     F.explode(
#         F.arrays_zip(
#             ret_chunk_udf(F.col("retrieved_chunks")).alias("retrieved_chunks"),
#             float_array_udf(F.col("ppl_scores")).alias("ppl_scores"),
#             float_array_udf(F.col("ppl")).alias("ppl"),
#             float_array_udf(F.col("ppg")).alias("ppg"),
#             int_array_udf(F.col("retrieval_rank")).alias("retrieval_rank"),
#             "prompt_tokens",
#         )
#     ).alias("zipped"),
# ).select(
#     "prefix",
#     "suffix",
#     "middle",
#     "file_path",
#     "zipped.retrieved_chunks",
#     "zipped.ppl_scores",
#     "zipped.ppl",
#     "zipped.ppg",
#     "zipped.retrieval_rank",
#     "zipped.prompt_tokens",
# )

# df.write.parquet(STAGE8_URI)
# spark.stop()
#%%
spark = k8s_session(max_workers=64)

df = spark.read.parquet(STAGE7_URI)
df = df.filter(F.size(F.col("prompt_tokens")) == CONFIG["stage_7"]['max_retrieved_docs'] + 1)
df = df.withColumn("prompt_tokens", F.explode("prompt_tokens"))

df.write.parquet(STAGE8_URI)
spark.stop()
#%% md
# Stage 9: Convert to Dataset for Training
#%%
from augment.research.data.spark.pipelines.stages.common import export_indexed_dataset

class ObjectDict(dict):
    """Provides both namespace-like and dict-like access to fields.

    Allows access to fields using both obj.name notation and obj["name"]
    notation. The latter is useful when "name" contains periods, for example.
    """

    def __getattr__(self, name: str):
        if name in self:
            return self[name]
        else:
            raise AttributeError("No such attribute: " + name)

    def __setattr__(self, name: str, value):
        self[name] = value

    def __delattr__(self, name: str):
        if name in self:
            del self[name]
        else:
            raise AttributeError("No such attribute: " + name)

print(DATA_OUTPUT_PATH)
#%%
import os

if not os.path.exists(DATA_OUTPUT_PATH):
    os.makedirs(DATA_OUTPUT_PATH)

spark = k8s_session()

export_indexed_dataset(
    config=ObjectDict(
        {
            "input": STAGE8_URI,
            "output": DATA_OUTPUT_PATH,
            "samples_column": "prompt_tokens",
            'num_validation_samples': 8192,
        }
    ),
    spark=spark,
    tokenizer=create_prompt_formatter(CONFIG['stage_7']['query_formatter']).tokenizer
)

spark.stop()
#%% md
# TO Kill a Running Spark Job

```bash
# Get appName
kubectl get pods | grep vzhao

```
#%%
from pyspark.sql import SparkSession

spark= SparkSession.builder.appName('vzhao-dev').getOrCreate()
spark.stop()