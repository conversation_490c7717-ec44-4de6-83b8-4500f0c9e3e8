#include "simplenccl.hpp"

#include <c10/cuda/CUDAGuard.h>
#include <c10/cuda/CUDAStream.h>

#include <cstdio>

#define NCCLCHECK(cmd)                                                    \
    do {                                                                  \
        ncclResult_t res = cmd;                                           \
        if (res != ncclSuccess) {                                         \
            printf("Failed, NCCL error %s:%d '%s'\n", __FILE__, __LINE__, \
                   ncclGetErrorString(res));                              \
            exit(EXIT_FAILURE);                                           \
        }                                                                 \
    } while (0)

namespace c10d {

SimpleNCCLBackend::SimpleNCCLBackend(const c10::intrusive_ptr<::c10d::Store>& store, int rank,
                                     int size)
    : Backend(rank, size) {
    const std::string ncclIdKey = "SimpleNCCLBackend.ncclId";
    ncclUniqueId id;
    if (rank == 0) {
        NCCLCHECK(ncclGetUniqueId(&id));
        auto vec = std::vector<uint8_t>(reinterpret_cast<uint8_t*>(&id),
                                        reinterpret_cast<uint8_t*>(&id) + sizeof(id));
        store->set(ncclIdKey, vec);
    } else {
        auto vec = store->get(ncclIdKey);
        TORCH_CHECK(vec.size() == sizeof(id), "Invalid NCCL ID");
        memcpy(&id, vec.data(), vec.size());
    }
    c10::cuda::CUDAGuard(static_cast<c10::DeviceIndex>(rank));
    NCCLCHECK(ncclCommInitRank(&ncclComm_, size, id, rank));
}

c10::intrusive_ptr<Work> SimpleNCCLBackend::allreduce(std::vector<at::Tensor>& tensors,
                                                      const AllreduceOptions& opts) {
    TORCH_CHECK(tensors.size() == 1, "SimpleNCCLBackend only supports 1 tensor");
    auto x = tensors[0];
    TORCH_CHECK(x.is_cuda());
    TORCH_CHECK(x.device().index() == rank_);

    // TODO: clean this up to a lookup table
    ncclDataType_t dtype = ncclFloat;
    if (x.scalar_type() == at::ScalarType::Half) {
        dtype = ncclHalf;
    } else if (x.scalar_type() == at::ScalarType::BFloat16) {
        dtype = ncclBfloat16;
    } else {
        TORCH_CHECK(x.scalar_type() == at::ScalarType::Float, "Unsupported dtype", x.scalar_type());
    }
    ncclRedOp_t reduceOp = ncclSum;
    if (opts.reduceOp == ReduceOp::AVG) {
        reduceOp = ncclAvg;
    } else if (opts.reduceOp == ReduceOp::MAX) {
        reduceOp = ncclMax;
    } else {
        TORCH_CHECK(opts.reduceOp == ReduceOp::SUM, "Unsupported reduce op", opts.reduceOp);
    }
    NCCLCHECK(ncclAllReduce(x.data_ptr(), x.data_ptr(), x.numel(), dtype, reduceOp, ncclComm_,
                            at::cuda::getCurrentCUDAStream().stream()));

    // What does this actually do?
    auto future =
        c10::make_intrusive<c10::ivalue::Future>(c10::ListType::create(c10::TensorType::get()));
    future->markCompleted(c10::IValue(tensors));
    return c10::make_intrusive<SimpleNCCLWork>(OpType::ALLREDUCE, std::move(future));
}

c10::intrusive_ptr<Backend> SimpleNCCLBackend::createSimpleNCCLBackend(
    const c10::intrusive_ptr<::c10d::Store>& store, int rank, int size,
    const std::chrono::duration<float>& timeout) {
    return c10::make_intrusive<SimpleNCCLBackend>(store, rank, size);
}

PYBIND11_MODULE(TORCH_EXTENSION_NAME, m) {
    m.def("createSimpleNCCLBackend", &SimpleNCCLBackend::createSimpleNCCLBackend);
}

bool SimpleNCCLWork::isCompleted() { return true; }

bool SimpleNCCLWork::isSuccess() const { return true; }

bool SimpleNCCLWork::wait(std::chrono::milliseconds /* unused */) { return true; }

c10::intrusive_ptr<c10::ivalue::Future> SimpleNCCLWork::getFuture() { return future_; }

}  // namespace c10d
