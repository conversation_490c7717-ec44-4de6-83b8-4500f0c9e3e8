"""Tests for the commit summary chunker."""

import json
import unittest
from typing import cast
from unittest import mock

import pytest

from models.retrieval.chunking.chunking import Document
from models.retrieval.chunking.commit_summary_chunker import (
    CommitData,
    CommitSummaryChunker,
    FileChange,
    format_commit_diffs,
    format_commit_status,
)


class MockThirdPartyModelResponse:
    """Mock response for the ThirdPartyModelClient."""

    def __init__(self, text):
        self.text = text
        self.end_of_stream = None


@pytest.fixture
def mock_google_genai_client():
    """Create a mock GoogleGenaiClient."""
    with mock.patch(
        "base.third_party_clients.google_genai_client.GoogleGenaiClient"
    ) as mock_client:
        # Mock the generate_response_stream method
        mock_instance = mock_client.return_value
        mock_generate = mock.MagicMock()
        mock_instance.generate_response_stream = mock_generate

        # Set up the mock to return a generator that yields a single response
        def mock_generator(*args, **kwargs):
            yield MockThirdPartyModelResponse("This is a mock summary of the commit.")

        mock_generate.side_effect = mock_generator
        yield mock_client


def test_commit_summary_chunker_with_commit_path(mock_google_genai_client):
    """Test the chunker with a document that has a commit path."""
    # Create a sample commit data
    commit_data = {
        "hash": "abcdef1234567890abcdef1234567890abcdef12",
        "authorName": "Test User",
        "authorEmail": "<EMAIL>",
        "timestamp": 1622548800,
        "subject": "Add new feature",
        "body": "This commit adds a new feature to the codebase.",
        "filesChanged": [
            {
                "changeType": "M",
                "filePath": "path/to/file.py",
                "diff": "--- a/path/to/file.py\n+++ b/path/to/file.py\n@@ -1,5 +1,7 @@\n def function():\n     return True\n+\n+def new_function():\n+    return False\n",
            }
        ],
    }

    # Create a document with the commit data and a commit path
    doc = Document(
        blob_name="commit_abcdef",
        text=json.dumps(commit_data),
        path="git://commit/abcdef1234567890abcdef1234567890abcdef12",
    )

    # Create the chunker
    chunker = CommitSummaryChunker(client=mock_google_genai_client.return_value)

    # Get the chunks
    chunks = list(chunker.split_into_chunks(doc))

    # Verify the results
    assert len(chunks) == 1
    chunk = chunks[0]
    assert commit_data["subject"] in chunk.text
    assert commit_data["hash"] in chunk.text
    assert commit_data["authorName"] in chunk.text
    assert commit_data["body"] not in chunk.text
    assert "This is a mock summary of the commit." in chunk.text
    assert chunk.parent_doc == doc
    assert chunk.char_offset == 0
    assert chunk.line_offset == 0
    assert chunk.length_in_lines == 11


def test_commit_summary_chunker_with_non_commit_path(mock_google_genai_client):
    """Test the chunker with a document that doesn't have a commit path."""
    # Create a sample commit data
    commit_data = {
        "hash": "abcdef1234567890",
        "authorName": "Test User",
        "authorEmail": "<EMAIL>",
        "timestamp": 1622548800,
        "subject": "Add new feature",
        "body": "This commit adds a new feature to the codebase.",
        "filesChanged": [
            {
                "changeType": "M",
                "filePath": "path/to/file.py",
                "diff": "--- a/path/to/file.py\n+++ b/path/to/file.py\n@@ -1,5 +1,7 @@\n def function():\n     return True\n+\n+def new_function():\n+    return False\n",
            }
        ],
    }

    # Create a document with the commit data but a non-commit path
    doc = Document(
        blob_name="commit_abcdef",
        text=json.dumps(commit_data),
        path="path/to/file.json",
    )

    # Create the chunker
    chunker = CommitSummaryChunker(client=mock_google_genai_client.return_value)

    # Get the chunks
    chunks = list(chunker.split_into_chunks(doc))

    # Verify that no chunks were returned for a non-commit document
    assert len(chunks) == 0


def test_commit_summary_chunker_invalid_json(mock_google_genai_client):
    """Test the chunker with invalid JSON data."""
    # Create a document with invalid JSON but with a commit path
    doc = Document(
        blob_name="invalid_commit",
        text="This is not valid JSON",
        path="git://commit/1234567890abcdef1234567890abcdef12345678",
    )

    # Create the chunker
    chunker = CommitSummaryChunker(client=mock_google_genai_client.return_value)

    # Verify that an exception is raised
    with pytest.raises(Exception):
        list(chunker.split_into_chunks(doc))


def test_format_commit_diffs():
    """Test the format_commit_diffs method."""

    # Test with a normal file
    files_changed = [
        FileChange(
            changeType="M",
            filePath="path/to/file.py",
            diff="--- a/path/to/file.py\n+++ b/path/to/file.py\n@@ -1,5 +1,7 @@\n def function():\n     return True\n+\n+def new_function():\n+    return False\n",
        )
    ]

    result = format_commit_diffs(files_changed, 1000)
    assert "--- M path/to/file.py ---" in result
    assert "def function():" in result
    assert "def new_function():" in result

    # Test with a deleted file
    files_changed = [
        FileChange(
            changeType="D",
            filePath="path/to/deleted.py",
            oldFilePath="path/to/deleted.py",
            diff="--- a/path/to/deleted.py\n+++ /dev/null\n@@ -1,10 +0,0 @@\n-def function():\n-    return True\n",
        )
    ]

    result = format_commit_diffs(files_changed, 1000)
    assert "--- D path/to/deleted.py ---" in result
    assert "[Deleted file diff skipped]" in result

    # Test with an added file
    files_changed = [
        FileChange(
            changeType="A",
            filePath="path/to/added_file.py",
            diff="--- /dev/null\n+++ b/path/to/added_file.py\n@@ -0,0 +1,3 @@\n+def new_added_function():\n+    return True\n",
        )
    ]

    result = format_commit_diffs(files_changed, 1000)
    assert "--- A path/to/added_file.py ---" in result
    assert "def new_added_function():" in result
    assert "return True" in result

    # Test with truncation
    files_changed = [
        FileChange(
            changeType="M",
            filePath="path/to/file.py",
            diff="A" * 2000,
        )
    ]

    result = format_commit_diffs(files_changed, 100)
    assert "--- M path/to/file.py ---" in result
    assert "[... File diff truncated ...]" in result
    assert len(result) < 2200  # Original diff (2000) + header + truncation message


def test_format_commit_status():
    """Test the format_commit_status function."""

    # Test with various file change types
    files_changed = [
        FileChange(
            changeType="A",
            filePath="path/to/added_file.py",
        ),
        FileChange(
            changeType="M",
            filePath="path/to/modified_file.py",
        ),
        FileChange(
            changeType="D",
            filePath="path/to/deleted_file.py",
        ),
        FileChange(
            changeType="R",
            filePath="path/to/new_name.py",
            oldFilePath="path/to/old_name.py",
        ),
        FileChange(
            changeType="C",
            filePath="path/to/copied_file.py",
            oldFilePath="path/to/original_file.py",
        ),
        FileChange(
            changeType="T",
            filePath="path/to/typechanged_file.py",
        ),
    ]

    result = format_commit_status(files_changed)
    expected_lines = [
        "A path/to/added_file.py",
        "M path/to/modified_file.py",
        "D path/to/deleted_file.py",
        "R path/to/old_name.py -> path/to/new_name.py",
        "C path/to/original_file.py -> path/to/copied_file.py",
        "T path/to/typechanged_file.py",
    ]

    assert result == "\n".join(expected_lines)

    # Test with empty list
    assert format_commit_status([]) == ""

    # Test with single file without oldFilePath
    single_file = [FileChange(changeType="M", filePath="single.py")]
    assert format_commit_status(single_file) == "M single.py"


def test_format_summary_method(mock_google_genai_client):
    """Test the _format_summary method."""
    # Create a chunker instance
    chunker = CommitSummaryChunker(client=mock_google_genai_client.return_value)

    # Create sample commit data
    commit_data_dict = {
        "hash": "abcdef1234567890abcdef1234567890abcdef12",
        "authorName": "Test User",
        "authorEmail": "<EMAIL>",
        "timestamp": 1622548800,
        "subject": "Add new feature to improve performance",
        "body": "This commit adds a new feature to the codebase.",
        "filesChanged": [
            {
                "changeType": "M",
                "filePath": "path/to/file.py",
            },
            {
                "changeType": "A",
                "filePath": "path/to/new_file.py",
            },
        ],
    }
    commit_data = cast(CommitData, commit_data_dict)

    summary = "Implemented caching mechanism to improve database query performance"

    # Call the _format_summary method
    result = chunker._format_summary(commit_data, summary)

    # Verify the formatted output contains expected elements
    assert "Subject: Add new feature to improve performance" in result
    assert "Commit SHA: abcdef1234567890abcdef1234567890abcdef12" in result
    assert "Author: Test User" in result
    assert "Date: 1622548800" in result
    assert "Summary:" in result
    assert (
        "Implemented caching mechanism to improve database query performance" in result
    )
    assert "Files changed:" in result
    assert "M path/to/file.py" in result
    assert "A path/to/new_file.py" in result

    # Test with long subject that should be truncated
    long_subject_commit_dict = commit_data_dict.copy()
    long_subject_commit_dict["subject"] = "A" * 250  # Longer than 200 char limit
    long_subject_commit = cast(CommitData, long_subject_commit_dict)

    result_long = chunker._format_summary(long_subject_commit, summary)
    # Should be truncated to around 200 chars
    subject_line = [
        line for line in result_long.split("\n") if line.startswith("Subject:")
    ][0]
    assert len(subject_line) < 250  # Should be shorter than original


if __name__ == "__main__":
    unittest.main()
