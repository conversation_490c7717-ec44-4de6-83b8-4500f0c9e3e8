"""Commit summary chunker implementation.

This chunker takes a document containing commit details and generates a summary
using Gemini, storing the commit metadata in the chunk.

It can be used directly in the chunker pipeline and will only process documents
with paths matching the pattern 'git://commit/<commit_sha>'.
"""

import json
import re
from collections.abc import Iterable
from dataclasses import dataclass
from typing import NotRequired, TypedDict

import structlog
from grpc import StatusCode

from base.ranges.string_utils import shorten_str
from base.third_party_clients.google_genai_client import GoogleGenaiClient
from models.retrieval.chunking import chunking

log = structlog.get_logger()

# Pattern for commit document paths: git://commit/<commit_sha>
COMMIT_PATH_PATTERN = r"^git://commit/([a-f0-9]{40})$"


def is_commit_path(path: str | None) -> bool:
    return path is not None and bool(re.match(COMMIT_PATH_PATTERN, path))


class FileChange(TypedDict):
    """Data class representing a file change in a commit."""

    changeType: str
    """
    git status	Short	What happened
    modified	M	    The contents of a tracked file changed.
    new file	A	    A brand-new file was added and staged.
    deleted	    D	    A previously-tracked file was removed.
    renamed	    R	    The file's path changed but Git detected it's the same content.
    copied	    C	    A new path has content identical (or very close) to an existing file, so Git flagged it as a copy.
    typechange 	T	    The “type” flipped—e.g. regular file → symlink, symlink → file, or file → submodule.
    """

    filePath: str
    """The new file path."""

    oldFilePath: NotRequired[str]
    """The old file path."""

    diff: NotRequired[str]
    """The diff for this specific file change."""


class CommitData(TypedDict):
    """TypedDict representing a Git commit."""

    hash: str
    authorName: str
    authorEmail: str
    timestamp: int
    subject: str
    body: str
    filesChanged: list[FileChange]


def format_commit_diffs(
    files_changed: Iterable[FileChange], max_diff_chars: int
) -> str:
    """Format the diffs for a commit.

    Args:
        files_changed: list of file changes.
        max_diff_chars: Maximum characters of diff to include per file.

    Returns:
        A formatted string containing all diffs.
    """
    # Process each file's diff individually with per-file truncation
    processed_diffs = list[str]()

    for file_change in files_changed:
        # Format the file status string for display
        if file_change["changeType"] == "D":
            file_status = f"{file_change['changeType']} {file_change['filePath']}"
        elif "oldFilePath" in file_change:
            file_status = f"{file_change['changeType']} {file_change['oldFilePath']} -> {file_change['filePath']}"
        else:
            file_status = f"{file_change['changeType']} {file_change['filePath']}"

        # Skip showing diffs for entirely deleted files
        if file_change["changeType"] == "D":
            processed_diffs.append(
                f"--- {file_status} ---\\n[Deleted file diff skipped]"
            )
            continue

        # Apply truncation per file
        file_diff = file_change.get("diff", "")
        if not file_diff.strip() and file_change["changeType"] != "R":
            log.warning("Empty diff for file that wasn't moved.")

        if len(file_diff) > max_diff_chars:
            truncated_file_diff = (
                file_diff[:max_diff_chars] + "\\n[... File diff truncated ...]"
            )
            processed_diffs.append(f"--- {file_status} ---\\n{truncated_file_diff}")
        else:
            processed_diffs.append(f"--- {file_status} ---\\n{file_diff}")

    # Join all processed diffs
    return "\\n\\n".join(processed_diffs)


def format_commit_status(files_changed: Iterable[FileChange]) -> str:
    """Format the changed files like:

    A path/to/added_file.py
    M path/to/modified_file.py
    D path/to/deleted_file.py
    R path/to/renamed_file.py -> path/to/new_file.py
    C path/to/copied_file.py -> path/to/new_file.py
    T path/to/typechanged_file.py

    Args:
        files_changed: list of file changes.

    Returns:
        A formatted string containing all diffs.
    """
    # Process each file's diff individually with per-file truncation
    files_status_parts = list[str]()
    for f in files_changed:
        if "oldFilePath" in f:
            files_status_parts.append(
                f"{f['changeType']} {f['oldFilePath']} -> {f['filePath']}"
            )
        else:
            files_status_parts.append(f"{f['changeType']} {f['filePath']}")
    return "\n".join(files_status_parts)


# IMPORTANT(arun): Don't change this prompt without bumping the transformation key
# in //services/deploy/commit_indexer_v1_deploy.jsonnet
SYSTEM_PROMPT_V1 = """\
You are an expert AI assistant specialized in analyzing code changes (Commits) to create concise,
information-rich summaries that will be used in a Retrieval-Augmented Generation (RAG) system.

Your task is to analyze the commit data and generate a concise summary of the changes or tasks performed.
This summary will be indexed and used to retrieve relevant past commits when users ask questions about
the codebase or request help with similar tasks.

**Instructions:**
1. Create a comprehensive summary that captures:
   * The primary purpose/goal of the changes (if obvious from the provided info)
   * Key components or systems that were modified
   * Specific functionality that was added, modified, or fixed
2. Include the most important technical terms, function names, class names, and concepts that would
   make this commit retrievable when users ask about similar tasks or components.
3. *Do not* list the list of file changes in your summary.
4. Make the summary 1-5 sentences long, information-dense.
5. Focus on making the summary USEFUL FOR RETRIEVAL--it should match when users ask about:
   * Similar tasks they want to accomplish
   * Where specific functionality lives in the codebase
   * Understanding the purpose of specific components
"""


@dataclass
class CommitSummaryChunker(chunking.Chunker):
    """Chunker that generates summaries of commits using Gemini.

    This chunker takes a document containing the detailed contents of a commit
    and calls Gemini to summarize it into a single chunk containing the summary,
    along with commit details stored as chunk metadata.

    It will only process documents with paths matching the pattern 'git://commit/<commit_sha>'.
    All other documents will be skipped.
    """

    client: GoogleGenaiClient
    max_diff_chars_per_file: int = 2_000
    max_total_diff_chars: int = 20_000
    system_prompt: str = SYSTEM_PROMPT_V1

    def split_into_chunks(self, doc: chunking.Document) -> Iterable[chunking.Chunk]:
        """Process a document and generate chunks.

        If the document path matches the commit pattern (git://commit/<commit_sha>),
        it will be processed as a commit document. Otherwise, it will be skipped.

        For commit documents, the document is expected to contain a JSON representation
        of a commit. The chunker will extract the commit details, format them, and generate
        a summary using Gemini.

        Args:
            doc: The document to process.

        Returns:
            A generator yielding chunks for commit documents, or an empty generator for non-commit documents.

        Raises:
            chunking.ChunkingException: If the document cannot be processed.
        """
        # Check if this is a commit document based on its path
        if not is_commit_path(doc.path):
            # Not a commit document, skip
            log.info("Skipping non-commit document", blob_name=doc.blob_name)
            return
        log.info("Processing commit document", blob_name=doc.blob_name)

        try:
            # Parse the commit data from the document
            commit_data_dict = json.loads(doc.text)
            # Validate the commit data against the TypedDict
            commit_data = CommitData(**commit_data_dict)
        except json.JSONDecodeError:
            log.error("Failed to parse commit data as JSON")
            raise chunking.ChunkingException(StatusCode.INVALID_ARGUMENT)
        except (KeyError, TypeError) as e:
            log.error(f"Invalid commit data format: {str(e)}")
            raise chunking.ChunkingException(StatusCode.INVALID_ARGUMENT)

        try:
            # Format the commit for summarization
            formatted_commit = self._format_commit(commit_data)

            # Generate the summary using Gemini
            summary = self._generate_summary(formatted_commit)

            # Format the summary
            summary = self._format_summary(commit_data, summary)

            # Create and yield the chunk
            yield chunking.Chunk(
                text=summary,
                parent_doc=doc,
                char_offset=0,
                line_offset=0,
                length_in_lines=summary.count("\n") + 1,
            )
        except Exception as e:
            log.exception("Error processing commit data", error=str(e))
            raise chunking.ChunkingException(StatusCode.INTERNAL)

    def _format_commit(self, commit_data: CommitData) -> str:
        """Format a commit into a standardized string representation.

        Args:
            commit_data: The commit data to format.

        Returns:
            A formatted string representation of the commit.
        """
        # Get the commit description
        description = shorten_str(commit_data["body"], max_len=1000, omit_mode="right")

        # Process diffs with per-file truncation
        all_diffs = format_commit_diffs(
            commit_data["filesChanged"], self.max_diff_chars_per_file
        )

        # Apply total diff truncation if needed
        if len(all_diffs) > self.max_total_diff_chars:
            all_diffs = (
                all_diffs[: self.max_total_diff_chars]
                + "\n[... Total diff truncated ...]"
            )

        files_status = format_commit_status(commit_data["filesChanged"])

        # Format the commit into a standardized representation
        return f"""\
Message: {commit_data["subject"]}
{description}

**Files Changed:**
{files_status}

{all_diffs}
"""

    def _generate_summary(self, formatted_commit: str) -> str:
        """Generate a summary of the commit using Gemini.

        Args:
            formatted_commit: The formatted commit data.

        Returns:
            The generated summary.
        """

        # User prompt for the Gemini model
        user_prompt = f"""\
Analyze the following commit data and generate a detailed task summary as instructed.
Directly start your response with the summary and *nothing else*.

**Input Commit Data:**

{formatted_commit}
"""

        try:
            # Generate the summary using the GoogleGenaiClient
            response_generator = self.client.generate_response_stream(
                model_caller="commit_summary_chunker",
                system_prompt=self.system_prompt,
                cur_message=user_prompt,
            )

            # Collect the response text
            summary_parts = []
            for response in response_generator:
                if response.text:
                    summary_parts.append(response.text)

            # Join all parts and strip any whitespace
            summary = "".join(summary_parts).strip()
            return summary
        except Exception as e:
            log.exception("Error generating summary with Gemini", error=str(e))
            raise chunking.ChunkingException(StatusCode.INTERNAL)

    def _format_summary(self, commit_data: CommitData, summary: str) -> str:
        """Format a commit + summary into a comprehensive description.

        Args:
            commit_data: The commit data to format.
            summary: The summary of the commit.

        Returns:
            A formatted string representation of the commit.
        """

        return f"""
Subject: {shorten_str(commit_data["subject"], max_len=200, omit_mode="right")}
Commit SHA: {commit_data["hash"]}
Author: {commit_data["authorName"]}
Date: {commit_data["timestamp"]}
Summary:
{summary}

Files changed:
{format_commit_status(commit_data["filesChanged"])}
"""
