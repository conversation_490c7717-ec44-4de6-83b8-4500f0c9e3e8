"""Tests for the retrieval section prompt formatter."""

from unittest.mock import <PERSON><PERSON>
from textwrap import dedent

import pytest

from base.tokenizers.tokenizer import Tokenizer
from base.prompt_format_chat.prompt_formatter import Prompt<PERSON>hunk, TokenList
from base.prompt_format_chat.legacy_binks.retrieval_section_prompt_formatter import (
    RetrievalSectionPromptFormatter,
)


def tokenize_safe(text: str) -> TokenList:
    """Mo<PERSON> tokenize a text string into ASCII codes."""
    return [ord(char) for char in text]


def detokenize(tokens: TokenList) -> str:
    """Mock detokenize a list of ASCII codes back to a string."""
    return "".join(chr(token) for token in tokens)


@pytest.fixture(name="formatter")
def fixture_formatter():
    """Setup the formatter."""
    single_file_template = dedent(
        """\
        I have file `{path}` open with its content:

        ```
        {content}
        ```

        """
    )

    # Mocking the Tokenizer interface
    tokenizer_mock = Mock(spec=Tokenizer)
    tokenizer_mock.tokenize_safe.side_effect = tokenize_safe
    tokenizer_mock.detokenize.side_effect = detokenize

    formatter = RetrievalSectionPromptFormatter(single_file_template, tokenizer_mock)
    return formatter


def test_format_with_a_single_chunk(formatter):
    """Test formatting with a single chunk."""
    chunk = PromptChunk(
        text="This is a test chunk.\n",
        path="file1.txt",
        char_start=0,
        char_end=22,
        blob_name="file1.txt",
        unique_id="file1.txt:1",
    )

    tokens, prompt_chunks = formatter.format(
        [chunk], max_path_tokens=10, max_total_tokens=100
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {"file1.txt:1"}
    detokenized_text = formatter.tokenizer.detokenize(tokens)

    expected_text = dedent(
        """\
        I have file `file1.txt` open with its content:

        ```
        This is a test chunk.
        ...
        ```

        """
    )

    assert detokenized_text == expected_text


def test_format_with_multiple_chunks(formatter):
    """Test formatting with multiple chunks."""
    chunk1 = PromptChunk(
        text="small\n",
        path="file1.txt",
        char_start=0,
        char_end=6,
        blob_name="file1.txt",
        unique_id="file1.txt:1",
    )
    chunk2 = PromptChunk(
        text="This is the second chunk.\n",
        path="file1.txt",
        char_start=6,
        char_end=32,
        blob_name="file1.txt",
        unique_id="file1.txt:2",
    )

    # Test case: budget is enough for both chunks.
    tokens, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=200
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:1",
        "file1.txt:2",
    }
    detokenized_text = formatter.tokenizer.detokenize(tokens)
    expected_text = dedent(
        """\
        I have file `file1.txt` open with its content:

        ```
        small
        This is the second chunk.
        ...
        ```

        """
    )
    assert detokenized_text == expected_text

    # Test case: budget is enough for a single chunk only
    tokens, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=92
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:2",
    }
    detokenized_text = formatter.tokenizer.detokenize(tokens)
    expected_text = dedent(
        """\
        I have file `file1.txt` open with its content:

        ```
        ...
        This is the second chunk.
        ...
        ```

        """
    )
    assert detokenized_text == expected_text

    # Test case: budget is enough for the first chunk
    tokens, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=90
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:1",
    }
    detokenized_text = formatter.tokenizer.detokenize(tokens)
    expected_text = dedent(
        """\
        I have file `file1.txt` open with its content:

        ```
        small
        ...
        ```

        """
    )
    assert detokenized_text == expected_text

    # Test case: budget is not enough for any chunks
    tokens, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=60
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == set()
    detokenized_text = formatter.tokenizer.detokenize(tokens)
    expected_text = ""
    assert detokenized_text == expected_text


def test_format_with_multiple_files(formatter):
    """Test formatting with multiple files."""
    chunk1 = PromptChunk(
        text="small.\n",
        path="file1.txt",
        char_start=0,
        char_end=7,
        blob_name="file1.txt",
        unique_id="file1.txt:1",
    )
    chunk2 = PromptChunk(
        text="This chunk is fairly long.\n",
        path="file2.txt",
        char_start=22,
        char_end=49,
        blob_name="file2.txt",
        unique_id="file2.txt:1",
    )

    # Test case: budget is enough for both chunks.
    tokens, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=200
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:1",
        "file2.txt:1",
    }
    detokenized_text = formatter.tokenizer.detokenize(tokens)
    expected_text = dedent(
        """\
        I have file `file2.txt` open with its content:

        ```
        ...
        This chunk is fairly long.
        ...
        ```

        I have file `file1.txt` open with its content:

        ```
        small.
        ...
        ```

        """
    )
    assert detokenized_text == expected_text

    # Test case: budget is enough only for a single chunk
    tokens, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=100
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file2.txt:1",
    }
    detokenized_text = formatter.tokenizer.detokenize(tokens)
    expected_text = dedent(
        """\
        I have file `file2.txt` open with its content:

        ```
        ...
        This chunk is fairly long.
        ...
        ```

        """
    )
    assert detokenized_text == expected_text

    # Test case: budget is enough only for the shortest chunk.
    tokens, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=90
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:1",
    }
    detokenized_text = formatter.tokenizer.detokenize(tokens)
    expected_text = dedent(
        """\
        I have file `file1.txt` open with its content:

        ```
        small.
        ...
        ```

        """
    )
    assert detokenized_text == expected_text

    # Test case: budget is not enough for any chunks.
    tokens, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=60
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == set()
    detokenized_text = formatter.tokenizer.detokenize(tokens)
    expected_text = ""
    assert detokenized_text == expected_text
