"""Utility functions for truncating text with token counter."""

import collections
import copy

from base.prompt_format_chat.lib.token_counter import TokenCounter


def trailing_n_lines(text: str, max_toks: int, token_counter: TokenCounter) -> str:
    """Return the last line-delimited up-to-N tokens of the given `text`."""
    lines = text.splitlines(keepends=True)
    last_n_lines = collections.deque([])  # deque has O(1) appendLeft
    last_n_lines_tok_ct = 0

    for line in reversed(lines):
        line_tok_ct = token_counter.count_tokens(line)
        if last_n_lines_tok_ct + line_tok_ct > max_toks:
            break
        last_n_lines.appendleft(line)
        last_n_lines_tok_ct += line_tok_ct

    return "".join(last_n_lines)


def head_n_lines(text: str, max_toks: int, token_counter: TokenCounter) -> str:
    """Return the first line-delimited up-to-N tokens of the given `text`."""
    lines = text.splitlines(keepends=True)
    first_n_lines = []
    first_n_lines_tok_ct = 0

    for line in lines:
        line_tok_ct = token_counter.count_tokens(line)
        if first_n_lines_tok_ct + line_tok_ct > max_toks:
            break
        first_n_lines.append(line)
        first_n_lines_tok_ct += line_tok_ct

    return "".join(first_n_lines)


def last_approx_tokens(
    text: str, max_toks: int, token_counter: TokenCounter, granularity=256
) -> str:
    """Return approx last up-to-N tokens of the given `text`, at a granularity of `granularity` chars at a time."""
    tokens_so_far = 0
    result = ""

    # Break up text into chunks
    chunks: list[str] = []
    text_iter = copy.copy(text)
    while len(text_iter) > 0:
        if len(text_iter) < granularity:
            chunk = text_iter
            text_iter = ""
        else:
            chunk = text_iter[-granularity:]
            text_iter = text_iter[:-granularity]
        chunks.append(chunk)

    for chunk in chunks:
        assert len(chunk) <= granularity, len(chunk)
        tokens_in_chunk = token_counter.count_tokens(chunk)
        if tokens_so_far + tokens_in_chunk > max_toks:
            break
        result = chunk + result
        tokens_so_far += tokens_in_chunk

    return result
