"""Test the DocumentationFormattedFile class."""

import pytest

from base.prompt_format.common import DocumentationMetadata, PromptChunk
from base.prompt_format_chat.lib.documentation_formatted_file import (
    DocumentationFormattedFile,
)
from base.prompt_format_chat.lib.token_counter import TokenizerBasedTokenCounter
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer

DUMMY_PATH = "file.txt"


@pytest.fixture(name="formatted_file")
def fixture_formatted_file():
    """Setup the formatted file with llama3 tokenizer."""
    tokenizer = Llama3InstructTokenizer()
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    return DocumentationFormattedFile(token_counter=token_counter)


def test_get_tokens_empty(formatted_file: DocumentationFormattedFile):
    """Test empty FormattedFile."""
    assert formatted_file.get_file_str_and_tok_ct() == (
        "...",
        formatted_file.token_counter.count_tokens("..."),
    )
    assert len(formatted_file) == formatted_file.token_counter.count_tokens("...")


def test_add_chunk(formatted_file: DocumentationFormattedFile):
    """Test adding a single chunk."""
    chunk = PromptChunk(
        text="Hello World!\n",
        char_start=0,
        char_end=12,
        path=DUMMY_PATH,
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git", name="Git", page_id="1", headers=[]
        ),
    )
    formatted_file = formatted_file.add_chunk(chunk)
    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "Hello World!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_documentation_empty_headers(formatted_file: DocumentationFormattedFile):
    """Test the documentation formatted file with empty headers."""
    chunk1 = PromptChunk(
        text="Hello World!\n",
        char_start=0,
        char_end=12,
        path=DUMMY_PATH,
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git", name="Git", page_id="1", headers=[]
        ),
    )
    chunk2 = PromptChunk(
        text="Foo Bar!\n",
        char_start=12,
        char_end=20,
        path=DUMMY_PATH,
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git", name="Git", page_id="1", headers=[]
        ),
    )
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "Hello World!\nFoo Bar!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_documentation_same_headers(formatted_file: DocumentationFormattedFile):
    """Test the documentation formatted file with the same headers."""
    chunk1 = PromptChunk(
        text="Hello World!\n",
        char_start=0,
        char_end=12,
        path=DUMMY_PATH,
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git",
            name="Git",
            page_id="1",
            headers=["#Header1", "##Header2"],
        ),
    )
    chunk2 = PromptChunk(
        text="Foo Bar!\n",
        char_start=12,
        char_end=20,
        path=DUMMY_PATH,
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git",
            name="Git",
            page_id="2",
            headers=["#Header1", "##Header2"],
        ),
    )
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "#Header1\n##Header2\nHello World!\nFoo Bar!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_documentation_sequential_headers(formatted_file: DocumentationFormattedFile):
    """Test the documentation formatted file with sequential headers."""
    chunk1 = PromptChunk(
        text="Hello World!\n",
        char_start=0,
        char_end=12,
        path=DUMMY_PATH,
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git",
            name="Git",
            page_id="1",
            headers=["#Header1", "##Header2"],
        ),
    )
    chunk2 = PromptChunk(
        text="Foo Bar!\n",
        char_start=12,
        char_end=20,
        path=DUMMY_PATH,
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git",
            name="Git",
            page_id="1",
            headers=["#Header1", "##Header2", "###Header3"],
        ),
    )
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "#Header1\n##Header2\nHello World!\n###Header3\nFoo Bar!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_documentation_different_headers(formatted_file: DocumentationFormattedFile):
    """Test the documentation formatted file with different headers."""
    chunk1 = PromptChunk(
        text="Hello World!\n",
        char_start=0,
        char_end=12,
        path=DUMMY_PATH,
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git",
            name="Git",
            page_id="1",
            headers=["#Header1", "##Header2"],
        ),
    )
    chunk2 = PromptChunk(
        text="Foo Bar!\n",
        char_start=12,
        char_end=20,
        path=DUMMY_PATH,
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git",
            name="Git",
            page_id="1",
            headers=["#Header3", "##Header4"],
        ),
    )
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert (
        file_str
        == "#Header1\n##Header2\nHello World!\n#Header3\n##Header4\nFoo Bar!\n..."
    )
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct
