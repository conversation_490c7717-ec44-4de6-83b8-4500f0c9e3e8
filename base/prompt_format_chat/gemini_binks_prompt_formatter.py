import dataclasses
from typing import Literal

from base.prompt_format.common import Exchange
from base.prompt_format_chat.lib.token_counter_vertex import Vert<PERSON><PERSON><PERSON><PERSON>ounter
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    StructuredChatPromptFormatter,
    StructuredChatPromptOutput,
)
from base.prompt_format_chat.structured_binks_prompt_formatter import (
    StructuredBinksPromptFormatter,
)


class GeminiBinksChatPromptFormatter(StructuredChatPromptFormatter):
    """The Gemini prompt formatter for the code chat."""

    def __init__(
        self,
        model_name: Literal[
            "gemini-1.5-flash-001",
            "gemini-1.5-pro-001",
            "gemini-2.0-flash-exp",
            "gemini-2.5-pro-exp-03-25",
            "gemini-2.5-flash-preview-05-20",
        ],
        token_apportionment: ChatTokenApportionment | None = None,
    ):
        self.token_counter = VertexTokenCounter(model_name)
        self.structured_binks_prompt_formatter = StructuredBinksPromptFormatter.create(
            self.token_counter,
            token_apportionment,
        )
        self.token_apportionment = (
            self.structured_binks_prompt_formatter.token_apportionment
        )

    def format_prompt(
        self,
        prompt_input: ChatPromptInput,
    ) -> StructuredChatPromptOutput:
        """Format prompt for Gemini.

        Colin: This uses the binks structured prompt formatter but it adds a filler message
        that helps a lot for Gemini.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            StructuredChatPromptOutput object
        """

        # Colin: Add filler message to chat history. For some reason this is needed to make the prompt formatter happy.
        # Without the model quality really degrads for long context (128k). It may not be needed for shorter context lengths,
        # but it won't hurt in those cases!
        prompt_input = dataclasses.replace(
            prompt_input,
            chat_history=list(prompt_input.chat_history)
            + [
                Exchange(
                    request_message="You have been so helpful!",
                    response_text="I'm glad to hear that! How can I help you further? 😊 ",
                )
            ],
        )

        return self.structured_binks_prompt_formatter.format_prompt(prompt_input)
