package blob_names

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"

	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
)

type BlobName string

func (b BlobName) String() string {
	return string(b)
}

// DecodeHexBlobName decodes a hex string into a byte slice.
func DecodeHexBlobName(blobName BlobName) ([]byte, error) {
	// A SHA-256 hash is 256 bits, which is 32 bytes.
	const sha256Size = 32

	// Decode the hex string.
	bytes, err := hex.DecodeString(string(blobName))
	if err != nil {
		return nil, fmt.Errorf("failed to decode hex string: %v", err)
	}

	// Ensure the decoded byte slice is exactly 32 bytes (256 bits).
	if len(bytes) != sha256Size {
		return nil, fmt.Errorf("decoded byte slice is not 256 bits (32 bytes), got %d bytes", len(bytes))
	}

	return bytes, nil
}

func GetBlobName(path string, contents []byte) BlobName {
	hash := sha256.New()
	hash.Write([]byte(path))
	hash.Write(contents)
	return BlobName(hex.EncodeToString(hash.Sum(nil)))
}

func NewBlobNameFromBytes(bytes []byte) BlobName {
	return BlobName(hex.EncodeToString(bytes))
}

func NewBlobNameProto(blobName BlobName, encoded bool) (*blobsproto.BlobName, error) {
	if !encoded {
		return &blobsproto.BlobName{
			Encoding: &blobsproto.BlobName_NameHex{
				NameHex: string(blobName),
			},
		}, nil
	}
	bytes, err := DecodeHexBlobName(blobName)
	if err != nil {
		return nil, err
	}
	return &blobsproto.BlobName{
		Encoding: &blobsproto.BlobName_Name{
			Name: bytes,
		},
	}, nil
}

func FromBlobNameProto(blobName *blobsproto.BlobName) (BlobName, error) {
	if v, ok := blobName.GetEncoding().(*blobsproto.BlobName_Name); ok {
		return BlobName(hex.EncodeToString(v.Name)), nil
	} else if v, ok := blobName.GetEncoding().(*blobsproto.BlobName_NameHex); ok {
		return BlobName(v.NameHex), nil
	}
	return "", fmt.Errorf("invalid blob name encoding")
}

type CheckpointID string

func (c CheckpointID) String() string {
	return string(c)
}

func CalculateCheckpointID(blobNames []BlobName) (CheckpointID, error) {
	if len(blobNames) == 0 {
		return "", fmt.Errorf("no blob names provided")
	}

	// Convert the blob names to byte slices.
	byteStrings := make([][]byte, len(blobNames))
	for i, blobName := range blobNames {
		bytes, err := DecodeHexBlobName(blobName)
		if err != nil {
			return "", fmt.Errorf("failed to decode blob name: %v", err)
		}
		byteStrings[i] = bytes
	}

	// The checkpoint ID is formed by XORing the byte strings together.
	result := byteStrings[0][:]
	for _, hashBytes := range byteStrings[1:] {
		// XOR each byte pair.
		for i := range result {
			result[i] ^= hashBytes[i]
		}
	}

	// Convert the result back to a hex string.
	return CheckpointID(hex.EncodeToString(result)), nil
}
