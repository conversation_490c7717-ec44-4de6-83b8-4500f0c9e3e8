load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "blobset_cache",
    srcs = [
        "blobset_cache.go",
    ],
    importpath = "github.com/augmentcode/augment/base/go/blobset_cache",
    visibility = BASE_VISIBILITY,
    deps = [
        "//base/blob_names:blob_names_go",
        "@com_github_rs_zerolog//log",
    ],
)

go_test(
    name = "blobset_cache_test",
    srcs = [
        "blobset_cache_test.go",
    ],
    embed = [":blobset_cache"],
    deps = [
        "//base/blob_names:blob_names_go",
        "@com_github_stretchr_testify//assert",
    ],
)
