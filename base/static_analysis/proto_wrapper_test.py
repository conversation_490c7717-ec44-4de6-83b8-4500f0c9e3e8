"""Tests for proto_wrapper."""

from pathlib import Path

import pytest

from base.ranges import <PERSON><PERSON><PERSON><PERSON><PERSON>, LineRange
from base.static_analysis import proto_wrapper
from base.static_analysis.signature_index import (
    BasicUsageReranker,
    FileSummaryWithSignatures,
    SignatureQueryState,
)
from base.static_analysis.signature_utils import FileSignatureInfo, SymbolSignature
from base.static_analysis.usage_analysis import (
    FileCharRange,
    FileSummary,
    LocalUsageAnalysis,
    ScopeTreeStructure,
    SymbolDefinition,
    SymbolNameUsage,
    VarOccurrence,
)


def ex_file_summary() -> FileSummary:
    """Make an example file summary."""
    defns = [
        SymbolDefinition(
            name="Foo",
            path=Path("foo.py"),
            name_crange=CharRange(start=1, stop=4),
            full_crange=CharRange(start=0, stop=10),
            prefix_crange=CharRange(start=0, stop=6),
            in_class=False,
            parent_id=None,
            kind="class",
            variable_summary="",
        ),
    ]
    defns.append(
        SymbolDefinition(
            name=".bar",
            path=Path("foo.py"),
            name_crange=CharRange(start=21, stop=24),
            full_crange=CharRange(start=20, stop=30),
            prefix_crange=CharRange(start=20, stop=26),
            in_class=True,
            parent_id=defns[0].id,
            kind="function",
            variable_summary="",
        )
    )
    return FileSummary(
        lang="python",
        path=Path("foo.py"),
        size_chars=30,
        size_lines=4,
        definitions=defns,
        local_analysis=LocalUsageAnalysis(
            name_usages={
                SymbolNameUsage(
                    name=".foo",
                    use_site=CharRange(start=25, stop=35),
                    def_range=FileCharRange(
                        path=Path("foo.py"), crange=CharRange(start=42, stop=137)
                    ),
                ),
            },
            var_occurrences={
                VarOccurrence("x", (CharRange(25, 26),)),
                VarOccurrence("y", (CharRange(25, 26), CharRange(27, 28))),
            },
        ),
        scope_structure=ScopeTreeStructure(
            range=CharRange(start=0, stop=35),
            children=[],
        ),
    )


def ex_file_summary_with_signatures(
    file_summary: FileSummary,
) -> FileSummaryWithSignatures:
    """Make an example file summary with signatures."""
    return FileSummaryWithSignatures(
        summary=file_summary,
        signature_info=FileSignatureInfo(
            module_signature=SymbolSignature(
                text="module_signature",
                path=Path("foo.py"),
                crange=CharRange(start=0, stop=10),
                lrange=LineRange(start=0, stop=10),
            ),
            symbol_signatures={
                defn.id: SymbolSignature(
                    text=f"symbol_signature_{defn.name}",
                    path=defn.path,
                    crange=defn.full_crange,
                    # TODO(arun): We really should have line range in the definition.
                    lrange=LineRange(start=0, stop=10),
                )
                for defn in file_summary.definitions
            },
        ),
    )


def ex_signature_query_state(file_summary: FileSummary) -> SignatureQueryState:
    """Make an example signature query state."""
    query_state = SignatureQueryState(
        file_path=Path("foo.py"),
        lang="python",
        est_prompt_range=CharRange(start=0, stop=10),
        reranker=BasicUsageReranker(
            current_file=Path("foo.py"),
        ),
    )
    query_state.reranker.record_context_bonus(
        {
            "Foo": [file_summary.definitions[0]],
            ".bar": [file_summary.definitions[1]],
        },
        {
            "@foo": [Path("foo.py"), Path("bar.py")],
        },
    )
    return query_state


@pytest.mark.parametrize(
    "py_obj",
    [
        ex_file_summary(),
        ex_file_summary_with_signatures(ex_file_summary()),
        ex_signature_query_state(ex_file_summary()),
    ],
)
def test_to_from_proto(py_obj):
    """Test that the object is the same after being serialized and deserialized.

    Note: we don't care how proto is encoded as long as it decodes to the same."""
    proto = proto_wrapper.to_proto(py_obj)
    example_back = proto_wrapper.from_proto(proto)
    assert example_back == py_obj
