"""Unit tests for parsing utilities."""

# pylint: disable=missing-function-docstring
# pylint: disable=protected-access

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from textwrap import dedent
from typing import Sequence

import pytest

from base.ranges import ByteRange
from base.static_analysis.common import (
    LanguageID,
    assert_eq,
    assert_str_eq,
    guess_lang_from_fp,
)
from base.test_utils.testing_utils import error_context
from base.static_analysis.parsing import (
    GlobalParser,
    ParsingFailedError,
    ScopeKind,
    ScopeOrSpan,
    SrcScope,
    SrcSpan,
    TreeSitterParser,
    compare_asts,
    find_syntax_errors,
    get_nodes_of_types,
    get_scope_by_range,
)

_TEST_DATA_ROOT = Path(__file__).parent / "testdata"


def _parse_tree(code: str, file_name: str, lang: LanguageID) -> SrcScope:
    assert_eq(guess_lang_from_fp(file_name), lang)
    return GlobalParser.parse(code, file_name, lang)


@dataclass
class ScopeCheck:
    """Specify a check to be performed on the content of `SrcScope`.

    Check the scope name, type, prefix and suffix.
    """

    name: str
    type: ScopeKind
    prefix: str
    suffix: str = ""
    docstr: str = ""
    parent_name_override: str | None = None

    def check(self, scope: SrcScope):
        """Perform the check on the given `SrcScope`."""
        assert_eq(scope.name, self.name)
        assert_eq(scope.kind, self.type)
        assert_str_eq(scope.prefix.code.strip(), self.prefix.strip())
        assert_str_eq(scope.docstr.code.strip(), self.docstr.strip())
        assert_str_eq(scope.suffix.code.strip(), self.suffix.strip())
        assert_eq(scope.parent_name_override, self.parent_name_override)


@dataclass
class IsSpan:
    """Specify a check to be performed on a `SrcSpan`.

    Check the span content against the specified prefix and suffix.
    Note that whitespaces are removed via `dedent(code).strip()`.
    """

    starts_with: str
    ends_with: "str | None" = None

    def check(self, span: SrcSpan):
        """Perform the check on the given `SrcSpan`."""
        code = dedent(span.code).strip()
        starts = dedent(self.starts_with).strip()
        if self.ends_with is None:
            ends = starts
        else:
            ends = dedent(self.ends_with).strip()
        start_code = code[: max(len(starts), 50)]
        if not start_code.startswith(starts):
            raise AssertionError(
                f"Expect to start with: {repr(starts)}, "
                f"actual starts with: {repr(start_code)}"
            )
        end_code = code[max(0, len(code) - max(50, len(ends))) :]
        if not end_code.endswith(ends):
            raise AssertionError(
                f"Expect to end with: {repr(ends)}, actual ends with: {repr(end_code)}"
            )


class IsScope:
    """Specify a check on the content and children of a `SrcScope`."""

    scope_check: ScopeCheck
    children: Sequence["IsSpan | IsScope"] = ()

    def __init__(self, check: ScopeCheck, *children: "IsSpan | IsScope"):
        self.scope_check = check
        self.children = children

    def check(self, scope: SrcScope):
        """Perform the check on the given `SrcSpan`."""
        with error_context(f"Checking scope {self.scope_check}"):
            self.scope_check.check(scope)
            assert_eq(len(scope.children), len(self.children))
            for child, expected in zip(scope.children, self.children):
                if isinstance(expected, IsSpan):
                    assert isinstance(child, SrcSpan), f"Expected span, got {child}"
                    expected.check(child)
                else:
                    assert isinstance(child, SrcScope), f"Expected scope, got {child}"
                    expected.check(child)


def test_assertion_tree():
    ex_code = """\
class Foo:
    x: int = 5

"""

    root = _parse_tree(ex_code, "test.py", "python")
    class0 = root.children[0]
    assert isinstance(class0, SrcScope)
    class0.pprint()

    IsScope(
        ScopeCheck("Foo", "class", "class Foo:"),
        IsSpan("x: int = 5", "x: int = 5"),
    ).check(class0)

    bad_class_name = IsScope(
        ScopeCheck("Bar", "class", "class Foo:"),
        IsSpan("x: int = 5", "x: int = 5"),
    )
    with pytest.raises(AssertionError):
        bad_class_name.check(class0)

    missing_children = IsScope(
        ScopeCheck("Foo", "class", "class Foo:"),
    )
    with pytest.raises(AssertionError):
        missing_children.check(class0)

    bad_span = IsScope(
        ScopeCheck("Foo", "class", "class Foo:"),
        IsSpan("x: int", "x: int = 6"),
    )
    with pytest.raises(AssertionError):
        bad_span.check(class0)


PYTHON_BASIC_EXAMPLE = """\

import package
from another.package import imported_f

glob_var = imported_f()
glob_var += 1;

def glob_f(x):
    "glob_f docstring"

    return x + glob_var

@dataclass
class DataClass:
    ''' DataClass docstring.
    '''
    x: int
    y: float

    def method(self, z: int):
        return self.x + self.y + z

    @staticmethod
    def smethod():
        pass

    class InnerClass:
        @annot('A')
        @annot('B')
        def inner_method(self):
            pass

        InnerFiled = 5
"""

# flake8: noqa
PYTHON_BROKEN_PARSABLE_1 = """\
# I'm a very broken code
#!/usr/bin/env python3.4
# coding: utf-8

from fruit.mixin.meta import MixinMeta

def broken_function(mth):

class A:
    x: int = 1
    def broken_method(self):

    y: int = 2


def resolve_defining_class(mth):
	qname = mth.__qualname__.split('.')

	if len(qname) < 2:
		raise ValueError(
			'Method {!r} is not defined in a class.'.format(mth

	cls_name = qname[-2]
	cls_mod = mth.__module__

"""

# NOTE(2024-07-08): This code failed to parse with tree-sitter-languages 0.7.0, but now
# parses successfully with non-root ERROR nodes.
PYTHON_BROKEN_PARSABLE_2 = """\
from git import Sequence

@dataclasses.dataclass
class Person:
    name: str
    dob: datetime.date

    def __str__(self):

        return f"{self.name} is {age} years old"

    def salary(self,
"""

JAVA_BASIC_EXAMPLE = """\
import package.*;
import static another.package.imported_f;

public class Main {
    static int globVar = imported_f() + 1;

    /** This is a docstring. */
    public static int glob_f(int x) {
        // this is just a comment
        return x + globVar;
    }
};

/** DataClass doc string */
@A
@B
class DataClass extends A {
    int x; // x is a field
    float y;

    public DataClass(int x, float y) {
        this.x = x;
        this.y = y;
    }

    public float method(int z) {
        return this.x + this.y + z;
    }

    public static void smethod() {
    }

    static class InnerClass {
        @Annot("C")
        public void innerMethod() {
        }

        static final int InnerField = 5;
    }

    abstract Set<Entry<K, V>> fromEntries(Entry<K, V>[] entries);
}

public enum Enum {
    E1, E2, E3
}
"""

CPP_BASIC_EXAMPLE = """\
#include <iostream>
#include <functional>
#include "package/package.h"
#include "another/package/imported_f.h"

/** Template docstr */
template <typename T>
class TemplateClass {
public:
    T value;

    TemplateClass(T value) : value(value) {}
};


int Main::globVar = imported_f() + 1;

/** DataClass docstr */
class DataClass : public A {
public:
    int x; // x is a field
    float y;

    DataClass(int x, float y) : x(x), y(y) {}

    /** This is a docstring. */
    float method(int z) {
        return this->x + this->y + z;
    }

    class InnerClass {
    public:
        static void innerMethod() {
        }

        static const int InnerField;
    };
};


template <typename... Args>
void variadicTemplateFunction(Args... args) {
    // Function implementation.
}

int main(){
    TemplateClass<int> obj(5);
    std::cout << obj.getValue() << std::endl;

    std::function<int(int, int)> lambdaFunction = [](int a, int b) {
        return a + b;
    };
    std::cout << lambdaFunction(3, 4) << std::endl;

    variadicTemplateFunction(1, 2, "test", 3.5);
}

namespace A {
    void foo() {}
}
"""

JS6_BASIC_EXAMPLE = """\
import * as package from './package';
import { imported_f } from './another/package';

class Main {
    static globVar = imported_f() + 1;

    /**
     * This is a docstring. */
    static glob_f(x) {
        // this is just a comment
        return x + Main.globVar;
    }
}

class DataClass extends A {
    constructor(x, y) {
        super();
        this.x = x; // x is a field
        this.y = y;
    }

    method(z) {
        return this.x + this.y + z;
    }

    static smethod() {
    }

    static InnerClass = class {
        innerMethod() {
        }

        static InnerField = 5;
    }
}
"""

JS5_BASIC_EXAMPLE = """\
var package = require('./package');
var imported_f = require('./another/package').imported_f;

var Main = (function () {
    function Main() {
    }

    Main.globVar = imported_f() + 1;

    /**
     * This is a docstring.
     */
    Main.glob_f = function (x) {
        // this is just a comment
        return x + Main.globVar;
    };

    return Main;
}());

function DataClass(x, y) {
    A.call(this);
    this.x = x; // x is a field
    this.y = y;
}

DataClass.prototype = Object.create(A.prototype);
DataClass.prototype.constructor = DataClass;

DataClass.prototype.method = function (z) {
    return this.x + this.y + z;
};

DataClass.smethod = function () {
};

DataClass.InnerClass = (function () {
    function InnerClass() {
    }

    InnerClass.prototype.innerMethod = function () {
    };

    InnerClass.InnerField = 5;

    return InnerClass;
}());

"""

TYPESCRIPT_BASIC_EXAMPLE = """\
import * as package from './package';
import { imported_f } from './another/package';

interface ICoordinate {
    x: number;
    y: number;
}

class Main {
    static globVar: number = imported_f() + 1;

    /**
     * This is a docstring. */
    static glob_f(x: number): number {
        // this is just a comment
        return x + Main.globVar;
    }
}

abstract class A {
    abstract method(z: number): number;
}

export class DataClass extends A implements ICoordinate {
    x: number; // x is a field
    private y: number;

    constructor(x: number, y: number) {
        super();
        this.x = x;
        this.y = y;
    }

    public method(z: number): number {
        return this.x + this.y + z;
    }

    static smethod(): void {
    }

    static InnerClass = class {
        innerMethod(): void {
        }

        static InnerField: number = 5;
    }
}

let mainInstance: Main = new Main();
let dataInstance: DataClass = new DataClass(5, 7);

enum Action {
    EDIT = 'edit',
    EXPLAIN = 'explain',
}

export namespace n {
    export function foo() {}
}
"""

GO_BASIC_EXAMPLE = """\
// Top comments
package main

import (
    "package"
    "another/package"
)

var globVar = package.ImportedF() + 1

/* glob_f doc string */
func glob_f(x int) int {
    // this is just a comment
    return x + globVar
}

/* DataClass doc string */
type DataClass struct {
    x int // x is a field
    y float32
}

/* type declaration */
type KeyValueStore interface {
	KeyValueReader
    Method() int
}

func NewDataClass(x int, y float32) DataClass {
    return DataClass{x: x, y: y}
}

const InnerField = 5
func (dc *DataClass) method(z int) float32 {
    return float32(dc.x) + dc.y + float32(z)
}


"""

RUST_BASIC_EXAMPLE = """\
mod package;
use crate::package::imported_f;

pub struct Main {}

/// Main doc 1
/// Main doc 2
impl Main {
    // just a comment
    pub const GLOBVAR: i32 = imported_f() + 1;

    pub fn glob_f(x: i32) -> i32 {
        // this is just a comment
        x + Self::GLOBVAR
    }
}

pub trait ICoordinate {
    fn new(x: i32, y: i32) -> Self;
    fn method(&self, z: i32) -> i32;
}

pub struct DataClass {
    x: i32, // x is a field
    y: i32,
}

impl ICoordinate for DataClass {
    fn new(x: i32, y: i32) -> Self {
        Self { x, y }
    }

    fn method(&self, z: i32) -> i32 {
        self.x + self.y + z
    }
}

impl DataClass {
    pub struct InnerClass {}

    impl InnerClass {
        pub const INNERFIELD: i32 = 5;
    }
}

fn main() {
    let main_instance = Main {};
    let data_instance = DataClass::new(5, 7);
}

mod tests {
    #[test]
    fn test_main() {
        assert_eq!(1, 1);
    }
}

"""


# Parsable examples
WELL_FORMED_EXAMPLES: list[tuple[str, LanguageID]] = [
    (PYTHON_BASIC_EXAMPLE, "python"),
    (PYTHON_BROKEN_PARSABLE_1, "python"),
    (PYTHON_BROKEN_PARSABLE_2, "python"),
    (JAVA_BASIC_EXAMPLE, "java"),
    (CPP_BASIC_EXAMPLE, "cpp"),
    (JS6_BASIC_EXAMPLE, "javascript"),
    (JS5_BASIC_EXAMPLE, "javascript"),
    (TYPESCRIPT_BASIC_EXAMPLE, "typescript"),
    (GO_BASIC_EXAMPLE, "go"),
    (RUST_BASIC_EXAMPLE, "rust"),
]

# Unparsable examples
BROKEN_EXAMPLES: list[tuple[str, LanguageID]] = []

ALL_EXAMPLES = WELL_FORMED_EXAMPLES + BROKEN_EXAMPLES


def test_scoping_python():
    ex_code = PYTHON_BASIC_EXAMPLE

    root = _parse_tree(ex_code, "test.py", "python")
    root.pprint()

    IsScope(
        ScopeCheck("test.py", "file", "", ""),
        IsSpan("import package", "glob_var += 1;"),
        IsScope(
            ScopeCheck(
                "glob_f", "function", "def glob_f(x):", docstr='"glob_f docstring"'
            ),
            IsSpan("return x + glob_var", "return x + glob_var"),
        ),
        IsScope(
            ScopeCheck(
                "DataClass",
                "class",
                "@dataclass\nclass DataClass:",
                "",
                docstr="''' DataClass docstring.\n    '''",
            ),
            IsSpan("x: int", "y: float"),
            IsScope(
                ScopeCheck(
                    "method",
                    "function",
                    "def method(self, z: int):",
                ),
                IsSpan("return self.x ", "+ self.y + z"),
            ),
            IsScope(
                ScopeCheck(
                    "smethod",
                    "function",
                    "@staticmethod\n    def smethod():",
                    "",
                ),
                IsSpan("pass", "pass"),
            ),
            IsScope(
                ScopeCheck("InnerClass", "class", "class InnerClass:", ""),
                IsScope(
                    ScopeCheck(
                        "inner_method",
                        "function",
                        (
                            "@annot('A')\n        @annot('B')\n"
                            "        def inner_method(self):"
                        ),
                    ),
                    IsSpan("pass", "pass"),
                ),
                IsSpan("InnerFiled = 5", "InnerFiled = 5"),
            ),
        ),
    ).check(root)

    assert_str_eq(root.get_code(), ex_code)


def test_inner_scopes():
    # Functions and classes inside a function are not considered as scopes

    ex_code = """\
def glob_f(x):
    class InnerClass:
        pass

    def inner_f():
        pass

    return inner_f()
"""
    root = _parse_tree(ex_code, "test.py", "python")
    root.pprint()

    # Now translate the above into an Assertion Tree
    IsScope(
        ScopeCheck("test.py", "file", "", ""),
        IsScope(
            ScopeCheck("glob_f", "function", "def glob_f(x):", ""),
            IsSpan("class InnerClass", "return inner_f()"),
        ),
    ).check(root)


def test_scoping_java():
    ex_code = JAVA_BASIC_EXAMPLE

    root = _parse_tree(ex_code, "test.java", "java")
    root.pprint()

    # Now translate the above into an Assertion Tree
    IsScope(
        ScopeCheck("test.java", "file", "", ""),
        IsSpan("import package.*", "package.imported_f;"),
        IsScope(
            ScopeCheck("Main", "class", "public class Main {", "};"),
            IsSpan("static int globVar", "imported_f() + 1;"),
            IsScope(
                ScopeCheck(
                    "glob_f",
                    "function",
                    "public static int glob_f(int x) {",
                    "}",
                    "/** This is a docstring. */",
                ),
                IsSpan("// this is just a comment", "return x + globVar;"),
            ),
        ),
        IsScope(
            ScopeCheck(
                "DataClass",
                "class",
                "@A\n@B\nclass DataClass extends A {",
                "}",
                docstr="/** DataClass doc string */",
            ),
            IsSpan("int x", "float y;"),
            IsScope(
                ScopeCheck(
                    "DataClass", "function", "public DataClass(int x, float y) {", "}"
                ),
                IsSpan("this.x = x;", "this.y = y;"),
            ),
            IsScope(
                ScopeCheck("method", "function", "public float method(int z) {", "}"),
                IsSpan("return this.x + ", "this.y + z;"),
            ),
            IsScope(
                ScopeCheck(
                    "smethod", "function", "public static void smethod() {", "}"
                ),
            ),
            IsScope(
                ScopeCheck("InnerClass", "class", "static class InnerClass {", "}"),
                IsScope(
                    ScopeCheck(
                        "innerMethod",
                        "function",
                        '@Annot("C")\n        public void innerMethod() {',
                        "}",
                    ),
                ),
                IsSpan("static final int InnerField = 5;", "InnerField = 5;"),
            ),
            IsScope(
                ScopeCheck(
                    "fromEntries",
                    "function",
                    "abstract Set<Entry<K, V>> fromEntries(Entry<K, V>[] entries);",
                )
            ),
        ),
        IsScope(
            ScopeCheck(
                "Enum",
                "class",
                "public enum Enum {",
                "}",
            ),
            IsSpan("E1, E2, E3"),
        ),
    ).check(root)

    assert_str_eq(root.get_code(), ex_code)


def test_nested_class_java():
    code = """
public class Main {
    public static final long ROOT_PID = 0L;
    public static final long MAX_PID = (long) Math.pow(2, 32);
    public static final long MAX_PORT = (long) Math.pow(2, 32);

    static class Record {
        long id;    // unique id
        long port;  // port id
        boolean alive; // True if record is alive, False if dead

        public Record(long id, long port, boolean alive) {
            this.id = id;
            this.port = port;
            this.alive = alive;
        }
    }
}
"""
    root = _parse_tree(code, "test.java", "java")
    root.pprint()

    IsScope(
        ScopeCheck("test.java", "file", "", ""),
        IsScope(
            ScopeCheck("Main", "class", "public class Main {", "}"),
            IsScope(
                ScopeCheck(
                    "Record",
                    "class",
                    "public static class Record {",
                    "}",
                ),
                IsSpan("long id;", "// True if record is alive, False if dead"),
                IsScope(
                    ScopeCheck(
                        "Record",
                        "function",
                        "public Record(long id, long port, boolean alive) {",
                        "}",
                    ),
                    IsSpan("this.id = id;", "this.alive = alive;"),
                ),
            ),
        ),
    )


def test_unicode_parsing():
    ex_code = """
# comment 1 😃...
class A:
    # 😃⭐️
    def f(): # å
        # ååå
        return 1

    # # 😃⭐️
    def bar():
        pass

"""
    root = _parse_tree(ex_code, "test.py", "python")
    root.pprint()

    def rec_check(s: ScopeOrSpan):
        if isinstance(s, SrcSpan):
            assert_str_eq(s.code, ex_code[s.range.to_slice()], lambda: f"{s=}")
        else:
            rec_check(s.prefix)
            rec_check(s.suffix)
            rec_check(s.docstr)
            for c in s.children:
                rec_check(c)

    rec_check(root)


def test_parsing_bad_code():
    for code, lang in BROKEN_EXAMPLES:
        GlobalParser.parse_errored_root = False
        with pytest.raises(ParsingFailedError):
            GlobalParser.parse(code, "test.py", lang)

    GlobalParser.parse_errored_root = True
    for code, lang in BROKEN_EXAMPLES:
        GlobalParser.parse(code, "test.py", lang)


def test_scoping_cpp():
    ex_code = CPP_BASIC_EXAMPLE
    root = _parse_tree(ex_code, "test.cpp", "cpp")
    root.pprint()

    IsScope(
        ScopeCheck("test.cpp", "file", "", ""),
        IsSpan("#include <iostream>", '#include "another/package/imported_f.h"'),
        IsScope(
            ScopeCheck(
                "TemplateClass",
                "class",
                "template <typename T>\nclass TemplateClass {",
                "};",
                docstr="/** Template docstr */",
            ),
            IsSpan("public:\n    T value;", "T value;"),
            IsScope(
                ScopeCheck(
                    "TemplateClass",
                    "function",
                    "TemplateClass(T value) : value(value) {",
                    "}",
                )
            ),
        ),
        IsSpan("int Main::globVar = imported_f() + 1;", "imported_f() + 1;"),
        IsScope(
            ScopeCheck(
                "DataClass",
                "class",
                "class DataClass : public A {",
                "};",
                docstr="/** DataClass docstr */",
            ),
            IsSpan("public:\n    int x;", "float y;"),
            IsScope(
                ScopeCheck(
                    "DataClass",
                    "function",
                    "DataClass(int x, float y) : x(x), y(y) {",
                    "}",
                )
            ),
            IsScope(
                ScopeCheck(
                    "method",
                    "function",
                    "float method(int z) {",
                    "}",
                    docstr="/** This is a docstring. */",
                ),
                IsSpan("return this->x + ", "this->y + z;"),
            ),
            IsScope(
                ScopeCheck("InnerClass", "class", "class InnerClass {", "};"),
                IsSpan("public:", "public:"),
                IsScope(
                    ScopeCheck(
                        "innerMethod",
                        "function",
                        "static void innerMethod() {",
                        "}",
                    )
                ),
                IsSpan("static const int InnerField;", "InnerField;"),
            ),
        ),
        IsScope(
            ScopeCheck(
                "variadicTemplateFunction",
                "function",
                (
                    "template <typename... Args>\n"
                    "void variadicTemplateFunction(Args... args) {"
                ),
                "}",
            ),
            IsSpan("// Function implementation.", "implementation."),
        ),
        IsScope(
            ScopeCheck("main", "function", "int main(){", "}"),
            IsSpan(
                "TemplateClass<int> obj(5);",
                'variadicTemplateFunction(1, 2, "test", 3.5);',
            ),
        ),
        IsScope(
            ScopeCheck("A", "class", "namespace A {", "}"),
            IsScope(
                ScopeCheck(
                    "foo",
                    "function",
                    "void foo() {",
                    "}",
                ),
            ),
        ),
    ).check(root)

    assert_str_eq(root.get_code(), ex_code)


def test_scoping_javascript_es6():
    """Test Javascript code in ES6 syntax."""
    ex_code = JS6_BASIC_EXAMPLE

    root = _parse_tree(ex_code, "test.js", "javascript")
    root.pprint()

    IsScope(
        ScopeCheck("test.js", "file", "", ""),
        IsSpan("import * as package", "from './another/package';"),
        IsScope(
            ScopeCheck("Main", "class", "class Main {", "}"),
            IsSpan("static globVar = imported_f() + 1;", "imported_f() + 1;"),
            IsScope(
                ScopeCheck(
                    "glob_f",
                    "function",
                    "static glob_f(x) {",
                    "}",
                    docstr="/**\n     * This is a docstring. */",
                ),
                IsSpan("// this is just a comment", "return x + Main.globVar;"),
            ),
        ),
        IsScope(
            ScopeCheck("DataClass", "class", "class DataClass extends A {", "}"),
            IsScope(
                ScopeCheck("constructor", "function", "constructor(x, y) {", "}"),
                IsSpan("super();", "this.y = y;"),
            ),
            IsScope(
                ScopeCheck("method", "function", "method(z) {", "}"),
                IsSpan("return this.x + ", "this.y + z;"),
            ),
            IsScope(ScopeCheck("smethod", "function", "static smethod() {", "}")),
            IsSpan("static InnerClass = class {", "static InnerField = 5;\n}"),
        ),
    ).check(root)

    assert_str_eq(root.get_code(), ex_code)


def test_scoping_javascript_es5():
    """Test Javascript code in ES5 syntax."""
    ex_code = JS5_BASIC_EXAMPLE

    root = _parse_tree(ex_code, "test.js", "javascript")
    root.pprint()

    # Unfortunately, in ES5, it's much harder to identify class scopes, so we
    # don't do much for now.
    IsScope(
        ScopeCheck("test.js", "file", "", ""),
        IsSpan("var package = require('./package');", "return Main;\n}());"),
        IsScope(
            ScopeCheck("DataClass", "function", "function DataClass(x, y) {", "}"),
            IsSpan("A.call(this);", "this.y = y;"),
        ),
        IsSpan(
            "DataClass.prototype = Object.create(A.prototype);",
            "return InnerClass;\n}());",
        ),
    ).check(root)

    assert_str_eq(root.get_code(), ex_code)


def test_scoping_typescript():
    ex_code = TYPESCRIPT_BASIC_EXAMPLE
    root = _parse_tree(ex_code, "test.ts", "typescript")
    root.pprint()

    IsScope(
        ScopeCheck("test.ts", "file", "", ""),
        IsSpan("import * as package", "from './another/package';"),
        IsScope(
            ScopeCheck("ICoordinate", "class", "interface ICoordinate {", "}"),
            IsSpan("x: number;", "y: number;"),
        ),
        IsScope(
            ScopeCheck("Main", "class", "class Main {", "}"),
            IsSpan("static globVar: number =", "imported_f() + 1;"),
            IsScope(
                ScopeCheck(
                    "glob_f",
                    "function",
                    "static glob_f(x: number): number {",
                    "}",
                    docstr="/**\n     * This is a docstring. */",
                ),
                IsSpan("// this is just a comment", "return x + Main.globVar;"),
            ),
        ),
        IsScope(
            ScopeCheck("A", "class", "abstract class A {", "}"),
            IsSpan("abstract method(z: number):", "number;"),
        ),
        IsScope(
            ScopeCheck(
                "DataClass",
                "class",
                "export class DataClass extends A implements ICoordinate {",
                "}",
            ),
            IsSpan("x: number; // x is a field", "private y: number;"),
            IsScope(
                ScopeCheck(
                    "constructor",
                    "function",
                    "constructor(x: number, y: number) {",
                    "}",
                ),
                IsSpan("super();", "this.y = y;"),
            ),
            IsScope(
                ScopeCheck(
                    "method", "function", "public method(z: number): number {", "}"
                ),
                IsSpan("return this.x +", "this.y + z;"),
            ),
            IsScope(
                ScopeCheck("smethod", "function", "static smethod(): void {", "}"),
            ),
            IsSpan("static InnerClass = class {", "}"),
        ),
        IsSpan("let mainInstance: Main =", "new DataClass(5, 7);"),
        IsScope(
            ScopeCheck("Action", "class", "enum Action {", "}"),
            IsSpan("EDIT = 'edit',\nEXPLAIN = 'explain',\n"),
        ),
        IsScope(
            ScopeCheck("n", "class", "export namespace n {", "}"),
            IsScope(
                ScopeCheck(
                    "foo",
                    "function",
                    "export function foo() {",
                    "}",
                ),
            ),
        ),
    ).check(root)

    assert_str_eq(root.get_code(), ex_code)


TSX_BASIC_EXAMPLE = """\
export default function BlobPageComponent() {
  const { blobName }: any = useParams();
  console.log(`blob name ${blobName}`)

  const children = (<BlobComponent blobName={blobName} title={"Blob Info"}></BlobComponent>);
  return (<>
    <LayoutComponent children={children} selectedMenuKey={"content"}
      breadcrumbs={[{ label: "", link: "/content" },
      { label: `Blob ${blobName}`, link: `/content/blob/${blobName}` }]} />
  </>
  );
}
export default App;
"""


def test_scoping_tsx():
    ex_code = TSX_BASIC_EXAMPLE
    root = _parse_tree(ex_code, "test.tsx", "typescript")
    root.pprint()
    assert_str_eq(root.get_code(), ex_code)

    IsScope(
        ScopeCheck("test.tsx", "file", "", ""),
        IsScope(
            ScopeCheck(
                "BlobPageComponent",
                "function",
                "export default function BlobPageComponent() {",
                "}",
            ),
            IsSpan("const { blobName }: any = useParams();", "</>\n);"),
        ),
        IsSpan("export default App;"),
    ).check(root)


@pytest.mark.parametrize("lang", ("typescript", "javascript"))
def test_ts_js_type_assertion_parsing(lang: LanguageID):
    sample = """
function getEls(elem) {
    getEls((<any> elem).contentWindow.document.body);
}"""
    path = "test.ts" if lang == "typescript" else "test.js"
    tree = GlobalParser.parse_ts_tree(sample, lang=lang, path=Path(path))
    error_nodes = get_nodes_of_types(tree.root_node, ("ERROR",))
    assert not error_nodes, f"{error_nodes=}"


def test_scoping_go():
    ex_code = GO_BASIC_EXAMPLE

    root = _parse_tree(ex_code, "test.go", "go")
    root.pprint()

    IsScope(
        ScopeCheck("test.go", "file", "", ""),
        IsSpan("// Top comments", "var globVar = package.ImportedF() + 1"),
        IsScope(
            ScopeCheck(
                "glob_f",
                "function",
                "func glob_f(x int) int {",
                "}",
                "/* glob_f doc string */",
            ),
            IsSpan("// this is just a comment", "return x + globVar"),
        ),
        IsScope(
            ScopeCheck(
                "DataClass",
                "class",
                "type DataClass struct {",
                "}",
                "/* DataClass doc string */",
            ),
            IsSpan("x int // x is a field", "y float32"),
        ),
        IsScope(
            ScopeCheck(
                "KeyValueStore",
                "class",
                "type KeyValueStore interface {",
                "}",
                "/* type declaration */",
            ),
            IsSpan("KeyValueReader"),
            IsScope(
                ScopeCheck(
                    "Method",
                    "function",
                    "Method() int",
                    "",
                ),
            ),
        ),
        IsScope(
            ScopeCheck(
                "NewDataClass",
                "function",
                "func NewDataClass(x int, y float32) DataClass {",
                "}",
            ),
            IsSpan("return DataClass{x: x, y: y}", ""),
        ),
        IsSpan("const InnerField = 5", "const InnerField = 5"),
        IsScope(
            ScopeCheck(
                "method",
                "function",
                "func (dc *DataClass) method(z int) float32 {",
                "}",
                parent_name_override="DataClass",
            ),
            IsSpan("return float32(dc.x) + dc.y + float32(z)", ""),
        ),
    ).check(root)

    assert_str_eq(root.get_code(), ex_code)


def test_scoping_rust():
    ex_code = RUST_BASIC_EXAMPLE

    root = _parse_tree(ex_code, "test.rs", "rust")
    root.pprint()

    IsScope(
        ScopeCheck("test.rs", "file", "", ""),
        IsScope(
            ScopeCheck(
                "package",
                "class",
                "mod package;",
                "",
            ),
        ),
        IsSpan("use crate::package::imported_f;"),
        IsScope(
            ScopeCheck(
                "Main",
                "class",
                "pub struct Main {",
                "}",
            ),
        ),
        IsScope(
            ScopeCheck(
                "Main",
                "class",
                "impl Main {",
                "}",
                "/// Main doc 1\n/// Main doc 2",
            ),
            IsSpan("// just a comment", "imported_f() + 1;"),
            IsScope(
                ScopeCheck(
                    "glob_f",
                    "function",
                    "pub fn glob_f(x: i32) -> i32 {",
                    "}",
                ),
                IsSpan("// this is just a comment\n", "x + Self::GLOBVAR"),
            ),
        ),
        IsScope(
            ScopeCheck(
                "ICoordinate",
                "class",
                "pub trait ICoordinate {",
                "}",
            ),
            IsScope(
                ScopeCheck("new", "function", "fn new(x: i32, y: i32) -> Self;"),
            ),
            IsScope(
                ScopeCheck("method", "function", "fn method(&self, z: i32) -> i32;"),
            ),
        ),
        IsScope(
            ScopeCheck(
                "DataClass",
                "class",
                "pub struct DataClass {",
                "}",
            ),
            IsSpan("    x: i32, // x is a field\n    y: i32,"),
        ),
        IsScope(
            ScopeCheck(
                "DataClass",
                "class",
                "impl ICoordinate for DataClass {",
                "}",
            ),
            IsScope(
                ScopeCheck("new", "function", "fn new(x: i32, y: i32) -> Self {", "}"),
                IsSpan("Self { x, y }"),
            ),
            IsScope(
                ScopeCheck(
                    "method", "function", "fn method(&self, z: i32) -> i32 {", "}"
                ),
                IsSpan("self.x + self.y + z"),
            ),
        ),
        IsScope(
            ScopeCheck(
                "DataClass",
                "class",
                "impl DataClass {",
                "}",
            ),
            IsScope(
                ScopeCheck(
                    "InnerClass",
                    "class",
                    "pub struct InnerClass {",
                    "}",
                ),
            ),
            IsScope(
                ScopeCheck(
                    "InnerClass",
                    "class",
                    "impl InnerClass {",
                    "}",
                ),
                IsSpan("pub const INNERFIELD: i32 = 5;"),
            ),
        ),
        IsScope(
            ScopeCheck("main", "function", "fn main() {", "}"),
            IsSpan(
                "let main_instance = Main {};",
                "let data_instance = DataClass::new(5, 7);",
            ),
        ),
        IsScope(
            ScopeCheck("tests", "class", "mod tests {", "}"),
            IsSpan("#[test]"),
            IsScope(
                ScopeCheck(
                    "test_main",
                    "function",
                    "fn test_main() {",
                    "}",
                ),
                IsSpan("assert_eq!(1, 1);"),
            ),
        ),
    ).check(root)

    assert_str_eq(root.get_code(), ex_code)


def test_traversal():
    ex_code = """
# span 1
import package
from another.package import imported_f

glob_var = imported_f()
glob_var += 1;

# span 2
def glob_f(x):
    "glob_f docstring"

    return x + glob_var

@dataclass
class DataClass:
    # span 3
    ''' DataClass docstring.
    '''
    x: int
    y: float

    def method(self, z: int):
        # span 4
        return self.x + self.y + z

    @staticmethod
    def smethod():
        # span 5
        pass

    class InnerClass:
        @annot('A')
        @annot('B')
        def inner_method(self):
            # span 6
            pass

        # span 7
        InnerFiled = 5

"""
    root = _parse_tree(ex_code, "test.py", "python")
    root.pprint()

    all_spans = list(root.get_all_spans())
    assert_eq(len(all_spans), 7)

    merged = SrcScope.merge_all(*all_spans)
    assert_eq(root, merged)


def test_node_range_coverage():
    """All tree nodes should cover the entire file completely without overlapping."""

    def rec_check(s: ScopeOrSpan):
        if isinstance(s, SrcScope):
            scope_range = s.range
            all_children = s.prefix_doc_pair() + tuple(s.children) + (s.suffix,)
            merged = ByteRange(scope_range.start, scope_range.start)
            for child in all_children:
                if not child.range:
                    continue
                assert_eq(
                    merged.stop, child.range.start, lambda: f"{s=},\n{all_children=}"
                )
                merged = merged.merge(child.range)
            assert_eq(merged, scope_range)

            for c in s.children:
                rec_check(c)

    for ex_code, lang in ALL_EXAMPLES:
        root = GlobalParser.parse(ex_code, "no file name", lang)
        rec_check(root)


def test_inner_range():
    """Inner ranges should not intersect with prefix or suffix."""

    def rec_check(s: ScopeOrSpan):
        if isinstance(s, SrcScope):
            assert s.range.contains(s.inner_range)
            assert not s.prefix.range.intersect(s.inner_range), s
            assert not s.suffix.range.intersect(s.inner_range), s
            for c in s.children:
                rec_check(c)

    for ex_code, lang in ALL_EXAMPLES:
        root = GlobalParser.parse(ex_code, "no file name", lang)
        rec_check(root)


def test_get_scope_by_range() -> None:
    """Test get_scope_by_range on scopes."""

    def rec_check(root: SrcScope, target: SrcScope):
        r = get_scope_by_range(root, target.range)
        assert isinstance(r, list)
        assert r[0] == target

        for child in target.children:
            if isinstance(child, SrcScope):
                rec_check(root, child)

    for ex_code, lang in ALL_EXAMPLES:
        root = GlobalParser.parse(ex_code, "no file name", lang)
        rec_check(root, root)


def test_find_syntax_errors():
    error_cases = [
        """
        class Foo:
            def f1(x):
                # not closed quote
                x = "
                return y
        """,
        """
        # extra closing bracket
        def f2():
            (a,))
        """,
        """
        # missing operator
        x = y z
        """,
    ]

    assert find_syntax_errors(PYTHON_BASIC_EXAMPLE, "python", Path("test.py")) == []

    for err_case in error_cases:
        assert find_syntax_errors(dedent(err_case), "python", Path("test.py"))


# TODO(AU-1350): Avoid failure by setting a maximum recursion depth.
@pytest.mark.xfail(
    raises=RecursionError,
    reason="Parser generates too big a tree to recurse through.",
)
def test_parse_deeply_nested_file():
    text = Path(_TEST_DATA_ROOT / "deep_recursion.cpp").read_text()
    assert find_syntax_errors(text, "cpp", Path("test.cpp")) == []


@pytest.mark.parametrize(
    "code1, code2, lang, path, ignore_comments, expected_result",
    [
        (
            "foo(x, y)",
            "foo(x, y)",
            "python",
            "ex.py",
            False,
            True,
        ),
        (
            "foo(x, y)",
            "bar(x, y)",
            "python",
            "ex.py",
            False,
            False,
        ),
        (
            "foo(x, y)",
            "foo(x)",
            "python",
            "ex.py",
            False,
            False,
        ),
        (
            "foo(x, y)",
            "foo(x,y)",
            "python",
            "ex.py",
            False,
            True,
        ),
        (
            "foo(x, y)",
            """foo(
                x,
                y
            )""",
            "python",
            "ex.py",
            False,
            True,
        ),
        (
            "foo(x, y)",
            """foo(
                x,
                y,
            )""",
            "python",
            "ex.py",
            False,
            False,
        ),
        (
            "[42]",
            "[42,]",
            "python",
            "ex.py",
            False,
            False,
        ),
        (
            "x + y",
            "x - y",
            "python",
            "ex.py",
            False,
            False,
        ),
        (
            "class C { int answer() { return 42; } }",
            """class C {
                int answer() {
                    return 42;
                }
            }""",
            "java",
            "ex.java",
            False,
            True,
        ),
        (
            "# a",
            "# b",
            "python",
            "ex.py",
            True,
            True,
        ),
        (
            """class C { int answer() { return "s"; } }  // comment""",
            """class C {
                int answer() {
                    return "s";
                }
            }""",
            "java",
            "ex.java",
            False,
            False,
        ),
        (
            """class C { int answer() { return "s"; } }  // comment""",
            """class C {
                int answer() {
                    /*
                    comment
                    */
                    return "s";
                }
            }""",
            "java",
            "ex.java",
            True,
            True,
        ),
        (
            "'a'",
            "'a'",
            "python",
            "ex.py",
            False,
            True,
        ),
        (
            "'a'",
            "'b'",
            "python",
            "ex.py",
            False,
            False,
        ),
        (
            "let s = 'a'",
            "let s = 'a'",
            "rust",
            "ex.rs",
            False,
            True,
        ),
        (
            "let s = 'a'",
            "let s = 'b'",
            "rust",
            "ex.rs",
            False,
            False,
        ),
        (
            "const s = 'a'",
            "const s = 'a'",
            "typescript",
            "ex.ts",
            False,
            True,
        ),
        (
            "const s = 'a'",
            "const s = 'b'",
            "typescript",
            "ex.ts",
            False,
            False,
        ),
        (
            "f(" * 1000 + "x" + ")" * 1000,
            "f(" * 1000 + "x" + ")" * 1000,
            "python",
            "ex.py",
            False,
            None,
        ),
        (
            "const s = `a`",
            "const s = `a`",
            "typescript",
            "ex.ts",
            False,
            True,
        ),
        (
            "const s = `a`",
            "const s = `b`",
            "typescript",
            "ex.ts",
            False,
            False,
        ),
        (
            "42",
            "42",
            "fake language",
            "ex.fake",
            False,
            None,
        ),
    ],
)
def test_compare_asts(
    code1: str,
    code2: str,
    lang: LanguageID,
    path: str,
    ignore_comments: bool,
    expected_result: bool,
):
    result = compare_asts(code1, code2, lang, Path(path), ignore_comments)
    assert result == expected_result


def count_scope_nodes(s: ScopeOrSpan) -> int:
    if isinstance(s, SrcScope):
        return 1 + sum(count_scope_nodes(c) for c in s.children)
    return 0


def test_many_functions():
    def build_fns(n: int) -> str:
        code = ""
        for i in range(n):
            code += f"\ndef fn{i}(): pass"
        return code

    scope = _parse_tree(build_fns(10000), "file1.py", "python")
    assert count_scope_nodes(scope) == 1_000


def test_many_methods():
    def build_fns(n: int) -> str:
        code = "class C:"
        for i in range(n):
            code += f"\n  def fn{i}(): pass"
        return code

    scope = _parse_tree(build_fns(10000), "file1.py", "python")
    assert count_scope_nodes(scope) == 1_000


def test_parsing_timeout():
    # NOTE(arun): This code takes ~10ms to parse on my CPU, so we test the timeout
    # behavior with a timeout of 10us to be really safe.
    code = Path(_TEST_DATA_ROOT / "deep_recursion.cpp").read_text()

    # 1s is definitely enough time.
    TreeSitterParser(timeout_us=1_000 * 1_000).parse_ts_tree(
        code,
        "cpp",
        Path("deep_recursion.cpp"),
    )

    with pytest.raises(ParsingFailedError):
        # 10us is definitely not enough time.
        TreeSitterParser(timeout_us=10).parse_ts_tree(
            code,
            "cpp",
            Path("deep_recursion.cpp"),
        )


def test_parsing_timeout_on_hanging_code():
    # NOTE(2024-07-08): This code hangs the parser for some reason and is here as an
    # example of some code that would otherwise timeout.
    code = """\
function f() {
  return (
        <A
          a-a={x ? '' : 42}
          b={() => c()}
          c={() => c()}
        />
  );
}
"""
    with pytest.raises(ParsingFailedError):
        TreeSitterParser(timeout_us=1_000 * 1_000).parse_ts_tree(
            code,
            "typescript",
            Path("test.ts"),
        )
