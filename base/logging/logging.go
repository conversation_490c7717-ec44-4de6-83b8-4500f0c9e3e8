package logging

import (
	"os"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// SetupConsoleLogging sets up logging for console applications.
func SetupConsoleLogging() {
	// Configure zerolog to use ConsoleWriter for CLI-friendly output
	log.Logger = zerolog.New(zerolog.ConsoleWriter{
		Out:        os.Stderr,
		TimeFormat: time.RFC3339,
	}).With().Timestamp().Logger()
	zerolog.SetGlobalLevel(zerolog.InfoLevel)
}

type LoggingOptions struct {
	// Debug enables debug logging
	Debug bool
}

// SetupServerLoggingWithOptions sets up logging for server applications with the given options.
func SetupServerLoggingWithOptions(options LoggingOptions) {
	// Hoping that nanosecond timestamps will help keep log messages ordered in Google Cloud Logging
	zerolog.TimeFieldFormat = time.RFC3339Nano
	zerolog.LevelFieldName = "severity"

	// Set log level based on debug parameter or LOG_DEBUG environment variable
	logLevel := zerolog.InfoLevel
	if options.Debug {
		logLevel = zerolog.DebugLevel
	}
	zerolog.SetGlobalLevel(logLevel)

	// Add file and line info
	log.Logger = log.With().Caller().Logger()
	// Ensure Ctx() calls pick up the global logger at least
	zerolog.DefaultContextLogger = &log.Logger
}

// SetupServerLogging sets up logging for server applications.
func SetupServerLogging() {
	debug := false
	if os.Getenv("LOG_DEBUG") != "" {
		debug = true
	}
	loggingOptions := LoggingOptions{
		Debug: debug,
	}
	SetupServerLoggingWithOptions(loggingOptions)
}
