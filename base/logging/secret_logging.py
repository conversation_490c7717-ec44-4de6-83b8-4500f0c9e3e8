from abc import ABC, abstractmethod
import logging
from typing import Literal


# Note that we didn't use the levels defined in `logging` because structlog uses
# a slightly different logging interface.
LogLevel = Literal["debug", "info", "warn", "error", "critical"]


class SecretLogger(ABC):
    """Interface for a dual logger able to handle both secret and non-secret messages.

    All implementations need to implement the `log_secret` and `log_nonsecret` methods.
    """

    @abstractmethod
    def log_secret(self, message: str, level: LogLevel) -> None:
        """Log a message containing sensitive information."""
        ...

    @abstractmethod
    def log_nonsecret(self, message: str, level: LogLevel) -> None:
        """Log a message that does not contain any sensitive information."""
        ...

    def info(self, message: str) -> None:
        self.log_nonsecret(message, level="info")

    def secret_info(self, message: str) -> None:
        self.log_secret(message, level="info")

    def warn(self, message: str) -> None:
        self.log_nonsecret(message, level="warn")

    def secret_warn(self, message: str) -> None:
        self.log_secret(message, level="warn")

    def error(self, message: str) -> None:
        self.log_nonsecret(message, level="error")

    def secret_error(self, message: str) -> None:
        self.log_secret(message, level="error")

    def critical(self, message: str) -> None:
        self.log_nonsecret(message, level="critical")

    def secret_critical(self, message: str) -> None:
        self.log_secret(message, level="critical")

    @classmethod
    def call_logger(cls, logger: logging.Logger, message: str, level: LogLevel):
        """A convenience method to call the logger on the correct log level.

        This works for both `logging` logger and `structlog` logger.
        """

        if level == "debug":
            logger.debug(message)
        elif level == "info":
            logger.info(message)
        elif level == "warn":
            logger.warning(message)
        elif level == "error":
            logger.error(message)
        elif level == "critical":
            logger.critical(message)
        else:
            logger.error(f"Invalid log level: {level}")


class IgnoreSecretLogger(SecretLogger):
    """Ignores secret messages and logs non-secrets to the provided logger.

    This is safe to use in production, but you may want to consider implementing a
    custom logger that logs secrets to a safe location.
    """

    def __init__(self, logger: logging.Logger | None = None):
        if logger is None:
            logger = logging.getLogger()
        self.logger = logger

    def log_secret(self, message: str, level: LogLevel) -> None:
        del message, level

    def log_nonsecret(self, message: str, level: LogLevel) -> None:
        self.call_logger(self.logger, message, level=level)


class IgnoreAllLogger(SecretLogger):
    """Ignores all messages. Useful for suppressing all logging."""

    def log_secret(self, message: str, level: LogLevel) -> None:
        del message, level

    def log_nonsecret(self, message: str, level: LogLevel) -> None:
        del message, level


class DebugSecretLogger(SecretLogger):
    """A logger will log all secrets to the debug level.

    This is safe to use in production because debug level logs are disabled there.
    """

    def __init__(self, logger: logging.Logger | None = None):
        if logger is None:
            logger = logging.getLogger()
        self.logger = logger

    def log_secret(self, message: str, level: LogLevel) -> None:
        prefix = f"[{level}]"
        self.call_logger(self.logger, f"{prefix} {message}", level="debug")

    def log_nonsecret(self, message: str, level: LogLevel) -> None:
        self.call_logger(self.logger, message, level=level)


class UnsafeLogger(SecretLogger):
    """A logger that logs everything to the provided logger.

    Do not use this in production.
    """

    def __init__(self, logger: logging.Logger | None = None):
        if logger is None:
            logger = logging.getLogger()
        self.logger = logger

    def log_secret(self, message: str, level: LogLevel) -> None:
        """We add [SECRET] to be able to verify the code is working properly in a deployment"""
        self.call_logger(self.logger, f"[SECRET] {message}", level=level)

    def log_nonsecret(self, message: str, level: LogLevel) -> None:
        self.call_logger(self.logger, message, level=level)


def get_safe_logger(logger: logging.Logger, secret_logs_enabled: bool) -> SecretLogger:
    return UnsafeLogger(logger) if secret_logs_enabled else IgnoreSecretLogger(logger)
