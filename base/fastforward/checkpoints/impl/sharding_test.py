"""Tests for sharding and unsharding weights."""

import pytest
import re
import safetensors.torch
import torch

from base.fastforward.checkpoints.impl import sharding

TEST_DATATYPES = [torch.uint8, torch.float16, torch.float32, torch.bfloat16]


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_shard_weights(dtype: torch.dtype):
    """Shard a tensor of random weights."""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (32, 32), dtype=dtype, device="cpu")
    w1_sharded = sharding.shard_single_weight(w1, sharding.ShardSaveArgs(shard_count=8))
    assert w1_sharded.split_dim == 0
    assert len(w1_sharded.shards) == 8
    assert w1_sharded.shards[0].shape == (4, 32)


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_shard_unshard_weights_noargs(dtype: torch.dtype):
    """Shard a tensor of random weights. Asserts that after unsharding with no args, they are the same."""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (32, 32), dtype=dtype, device="cpu")

    for dim in [0, 1]:
        w1_sharded = sharding.shard_single_weight(
            w1, sharding.ShardSaveArgs(shard_count=8, split_dim=dim)
        )
        assert w1_sharded.split_dim == dim
        assert len(w1_sharded.shards) == 8

        # Don't help by passing dim; should be sufficient information in the sharded form to unshard with
        # no arguments
        w1_unsharded = sharding.unshard_weights({"foo": w1_sharded}, {})["foo"]
        assert torch.equal(w1, w1_unsharded)


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_shard_unshard_weights_args(dtype: torch.dtype):
    """Shard a tensor of random weights. Asserts that after unsharding full args, they are the same."""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (32, 32), dtype=dtype, device="cpu")

    for dim in [0, 1]:
        w1_sharded = sharding.shard_single_weight(
            w1, sharding.ShardSaveArgs(shard_count=8, split_dim=dim)
        )
        assert w1_sharded.split_dim == dim
        assert len(w1_sharded.shards) == 8

        w1_unsharded = sharding.unshard_weights(
            {"foo": w1_sharded},
            {
                "foo": sharding.ShardLoadArgs(
                    shard_count=8, shard_idxs=tuple(range(8)), split_dim=dim
                )
            },
        )["foo"]
        assert torch.equal(w1, w1_unsharded)


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_shard_unshard_negative_dim(dtype: torch.dtype):
    """Asserts that sharding using negative dimensions (indexing from end of tensor shape) work as expected"""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (8, 8, 8), dtype=dtype, device="cpu")

    # Test sharding using negative dimension value
    w1_sharded_dim_neg_1 = sharding.shard_single_weight(
        w1, sharding.ShardSaveArgs(shard_count=8, split_dim=-1)
    )
    assert all(
        shard is not None and shard.shape == (8, 8, 1)
        for shard in w1_sharded_dim_neg_1.shards
    )
    assert torch.equal(w1.chunk(8, dim=-1)[4], w1_sharded_dim_neg_1.shards[4])  # type: ignore

    # Test unsharding using positive or negative dimension values
    target_shard = w1.chunk(8, dim=-1)[3]
    for dim_val in (2, -1):
        single_shard = sharding.unshard_weights(
            {"w1": w1_sharded_dim_neg_1},
            {
                "w1": sharding.ShardLoadArgs(
                    shard_idxs=(3,), shard_count=8, split_dim=dim_val
                )
            },
        )["w1"]
        assert torch.equal(single_shard, target_shard)


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_shard_unshard_weights_args_invalid_dim(dtype: torch.dtype):
    """Shard a tensor of random weights. Asserts that unsharding with the wrong dimension results in an error."""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (32, 32), dtype=dtype, device="cpu")

    for dim in [0, 1]:
        w1_sharded = sharding.shard_single_weight(
            w1, sharding.ShardSaveArgs(shard_count=8, split_dim=dim)
        )
        assert w1_sharded.split_dim == dim
        assert len(w1_sharded.shards) == 8

        args = sharding.ShardLoadArgs(
            shard_count=8, shard_idxs=tuple(range(8)), split_dim=1 - dim
        )
        with pytest.raises(ValueError):
            sharding.unshard_weights(
                {"foo": w1_sharded},
                {"foo": args},
            )


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_invalid_chunk_dims(dtype: torch.dtype):
    """Shard a dict of random weights. Asserts an error is thrown when sharding by non-divisor of shard axis."""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (32, 32), dtype=dtype, device="cpu")
    with pytest.raises(ValueError):
        # Cannot shard 32 into 7
        sharding.shard_single_weight(w1, sharding.ShardSaveArgs(shard_count=7))


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_invalid_load_shard_request(dtype: torch.dtype):
    """Shard a dict of random weights. Asserts an error is thrown when loading with an incompatible shard count."""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (32, 32), dtype=dtype, device="cpu")
    w1_sharded = sharding.shard_single_weight(w1, sharding.ShardSaveArgs(shard_count=8))
    with pytest.raises(ValueError):
        # Cannot load shard 0 of 7 when sharding is performed with 8
        sharding.unshard_weights(
            {"w1": w1_sharded}, {"w1": sharding.ShardLoadArgs((0,), shard_count=7)}
        )


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_non_increasing_idxs(dtype: torch.dtype):
    """Shard a dict of random weights. Asserts that sharding requests must be increasing."""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (32, 32), dtype=dtype, device="cpu")
    w1_sharded = sharding.shard_single_weight(w1, sharding.ShardSaveArgs(shard_count=8))
    with pytest.raises(ValueError):
        # Cannot load shards with out-of-order weights
        w1_unsharded = sharding.unshard_weights(
            {"w1": w1_sharded},
            {"w1": sharding.ShardLoadArgs((5, 4, 3, 2, 1), shard_count=8)},
        )

    with pytest.raises(ValueError):
        # Cannot load shards with non-contiguous weights
        w1_unsharded = sharding.unshard_weights(
            {"w1": w1_sharded},
            {"w1": sharding.ShardLoadArgs((2, 2), shard_count=8)},
        )

    with pytest.raises(ValueError):
        # Cannot load shards with non-contiguous out-of-order weights
        w1_unsharded = sharding.unshard_weights(
            {"w1": w1_sharded},
            {"w1": sharding.ShardLoadArgs((5, 3, 1), shard_count=8)},
        )

    w1_unsharded = sharding.unshard_weights(
        {"w1": w1_sharded},
        {"w1": sharding.ShardLoadArgs((0, 1, 2, 3), shard_count=8)},
    )["w1"]
    assert torch.equal(w1_unsharded, torch.concat(w1.chunk(8)[:4]))


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_non_contiguous_idxs(dtype: torch.dtype):
    """Shard a dict of random weights. Asserts that non-contiguous shards can be loaded together
    and are concatenated correctly."""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (32, 32), dtype=dtype, device="cpu")

    # Dimension 0
    w1_sharded = sharding.shard_single_weight(w1, sharding.ShardSaveArgs(shard_count=8))
    w1_unsharded = sharding.unshard_weights(
        {"w1": w1_sharded},
        {"w1": sharding.ShardLoadArgs((0, 2, 4, 6), shard_count=8)},
    )["w1"]
    validate = torch.concat(w1.chunk(8, dim=0)[::2], dim=0)
    assert torch.equal(validate, w1_unsharded)

    # Dimension 1, different stride
    w1_sharded = sharding.shard_single_weight(
        w1, sharding.ShardSaveArgs(shard_count=8, split_dim=1)
    )
    w1_unsharded = sharding.unshard_weights(
        {"w1": w1_sharded},
        {"w1": sharding.ShardLoadArgs((1, 4), shard_count=8, split_dim=1)},
    )["w1"]
    validate = torch.concat((w1.chunk(8, dim=1)[1], w1.chunk(8, dim=1)[4]), dim=1)
    assert torch.equal(validate, w1_unsharded)


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_select_shards_from_unsharded_weights(dtype: torch.dtype):
    """If a model desires sharded weights, but we have only persisted the full
    weight tensor, then unshard_weights should still chunk the tensor up and
    return only the desired shards.

    The alternative requires each model to detect failed unsharding, and
    specify its weight sharding twice:
    a) as an argument to load_weights
    b) in the fallback code in case load_weights returns a full weight tensor
    """
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (32, 32), dtype=dtype, device="cpu")
    w1_unsharded = sharding.unshard_weights(
        {"w1": w1}, {"w1": sharding.ShardLoadArgs(shard_idxs=(1, 2, 3), shard_count=8)}
    )["w1"]
    assert torch.equal(w1_unsharded, torch.concat(w1.chunk(8)[1:4]))


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_load_scaling_factor(dtype: torch.dtype):
    """Shard a dict of random weights. Asserts that different types of requests, scaled compatibly, still work."""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (32, 32), dtype=dtype, device="cpu")
    w1_sharded = sharding.shard_single_weight(w1, sharding.ShardSaveArgs(shard_count=8))
    assert len(w1_sharded.shards) == 8

    w1_unsharded = sharding.unshard_weights(
        {"w1": w1_sharded},
        # Since we sharded by 8, each shard with shard count 4 corresponds to 2 shards on-disk
        {"w1": sharding.ShardLoadArgs(shard_idxs=(0, 1, 2, 3), shard_count=4)},
    )["w1"]

    assert torch.equal(w1_unsharded, w1)


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_load_shard_out_of_range(dtype: torch.dtype):
    """Shard a dict of random weights. Assert that accessing shard out of range raises an error"""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (32, 32), dtype=dtype, device="cpu")
    w1_sharded = sharding.shard_single_weight(w1, sharding.ShardSaveArgs(shard_count=8))

    with pytest.raises(IndexError):
        sharding.unshard_weights(
            {"w1": w1_sharded},
            {
                "w1": sharding.ShardLoadArgs(
                    # Since we sharded by 8, each shard with shard count 4 corresponds to 2 shards on-disk
                    shard_idxs=(0, 1, 2, 3, 4),
                    shard_count=4,
                )
            },
        )["w1"]

    with pytest.raises(IndexError):
        sharding.unshard_weights(
            {"w1": w1_sharded},
            {
                "w1": sharding.ShardLoadArgs(
                    shard_idxs=(8,),
                    shard_count=8,
                )
            },
        )["w1"]


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_load_extra_shard_count(dtype: torch.dtype):
    """Shard a dict of random weights. Assert that requesting unsharding based on
    finer-grained shards than exist will throw an error"""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (32, 32), dtype=dtype, device="cpu")

    w1_sharded = sharding.shard_single_weight(w1, sharding.ShardSaveArgs(shard_count=8))

    with pytest.raises(ValueError):
        sharding.unshard_weights(
            {"w1": w1_sharded},
            {
                "w1": sharding.ShardLoadArgs(
                    # At this time, we choose not to support further sharding during the "unshard"
                    # operation. Requesting the data in 16 shards from existing 8 shards would
                    # require further sharding.
                    shard_idxs=(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15),
                    shard_count=16,
                )
            },
        )


def test_unshard_discard_input():
    """Assert that unsharding weights will not modify the input unless
    that behavior is requested.
    """
    torch.manual_seed(31415)

    layer_0_a = torch.randint(0, 16, (32, 32), device="cpu")
    score = torch.randint(0, 16, (32, 32), device="cpu")
    sharded = sharding.shard_weights(
        {"layer.0.a": layer_0_a, "score": score},
        {
            "layer.0.a": sharding.ShardSaveArgs(shard_count=2),
            "score": sharding.ShardSaveArgs(shard_count=4, split_dim=1),
        },
    )
    copy_sharded = dict(sharded)

    # Passing no keyword argument, the input weights should be unmodified
    sharding.unshard_weights(sharded, {})
    assert set(copy_sharded.keys()) == set(sharded.keys())
    for k in copy_sharded:
        # No references changed
        assert copy_sharded[k] is sharded[k]

    # Same behavior when explicitly requested
    sharding.unshard_weights(sharded, {}, discard_input_tensors=False)
    assert set(copy_sharded.keys()) == set(sharded.keys())
    for k in copy_sharded:
        # No references changed
        assert copy_sharded[k] is sharded[k]

    # Discard upon request
    sharding.unshard_weights(sharded, {}, discard_input_tensors=True)
    assert sharded == {}


def test_shard_arg_map():
    """Test defining shard args using both strings and patterns"""
    torch.manual_seed(31415)

    t = torch.randint(0, 16, (32, 32), device="cpu")
    weights = {
        "layer.0.a": t,
        "layer.0.b": t,
        "layer.0.cat": t,
        "layer.1.d": t,
        "score": t,
    }

    sharded = sharding.shard_weights(
        weights,
        {
            # All layer.0 should be sharded
            re.compile(r"layer\.0\."): sharding.ShardSaveArgs(shard_count=2),
            # Exact string matches have higher priority than pattern matches
            "layer.0.b": sharding.ShardSaveArgs(shard_count=4),
            re.compile(r"[sS]core$"): sharding.ShardSaveArgs(shard_count=8),
        },
    )

    assert set(weights.keys()) == set(sharded.keys())
    assert isinstance(sharded["layer.0.a"], sharding.ShardedTensor)
    assert isinstance(sharded["layer.0.b"], sharding.ShardedTensor)
    assert isinstance(sharded["layer.0.cat"], sharding.ShardedTensor)
    assert isinstance(sharded["layer.1.d"], torch.Tensor)
    assert isinstance(sharded["score"], sharding.ShardedTensor)

    assert len(sharded["layer.0.a"].shards) == 2
    assert len(sharded["layer.0.b"].shards) == 4
    assert len(sharded["layer.0.cat"].shards) == 2
    assert torch.equal(sharded["layer.1.d"], t)
    assert len(sharded["score"].shards) == 8
