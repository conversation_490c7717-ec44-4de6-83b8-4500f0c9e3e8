"""Test for the starcoder model."""

import time
from typing import Sequence

import pytest
import torch

from base.fastforward import (
    fwd_model_test_utils,
    fwd_utils,
)
from base.fastforward.fwd import AttentionFactory, ForwardStepFn
from base.fastforward.starcoder import fwd_starcoder_fp8

SC_KNOWN_SEQUENCE = fwd_starcoder_fp8.SC_KNOWN_SEQUENCE


@pytest.mark.parametrize("round_sizes", [(8,), (16,), (32,)])
def test_generate(
    starcoder_1b_fp8_fixture: tuple[ForwardStepFn, AttentionFactory],
    round_sizes: Sequence[int],
):
    step_fn, attn_factory = starcoder_1b_fp8_fixture
    step_fn = fwd_utils.pad_and_step(step_fn, round_sizes)
    attn_length = 128
    attn = attn_factory(attn_length)

    model_inputs = SC_KNOWN_SEQUENCE[:6]
    print(f"{model_inputs=}")
    tokens = list(model_inputs)

    latencies = list[float]()

    def run_step(model_inputs_list: list[int]) -> torch.Tensor:
        torch.cuda.synchronize()
        start_time = time.time()
        logits = step_fn(model_inputs_list, attn).checked_cast(torch.Tensor)
        torch.cuda.synchronize()
        step_time_ms = (time.time() - start_time) * 1e3
        latencies.append(step_time_ms)
        print(f"step executed in {step_time_ms:0.2f}ms.")
        return logits

    num_rounds = 4
    for _ in range(num_rounds):
        logits = run_step(model_inputs)
        next_tokens = torch.argmax(logits, dim=-1)
        next_token = int(next_tokens[-1])
        model_inputs = [next_token]
        tokens.append(int(next_token))

    print(f"{tokens=}")
    assert tuple(tokens) == tuple(SC_KNOWN_SEQUENCE[: 6 + num_rounds])

    median_latency_ms = sorted(latencies)[len(latencies) // 2]
    print(f"Median latency: {median_latency_ms:0.2f}ms")


def test_logits_are_close(starcoder_1b_fp8_fixture, starcoder_1b_fp16_fixture):
    step_fn_fp8, attn_factory_fp8 = starcoder_1b_fp8_fixture
    step_fn_fp16, attn_factory_fp16 = starcoder_1b_fp16_fixture
    # pad
    step_fn_fp8 = fwd_utils.pad_and_step(step_fn_fp8, [8])
    step_fn_fp16 = fwd_utils.pad_and_step(step_fn_fp16, [8])

    fwd_model_test_utils.check_logits_are_close(
        step_fn_fp8,
        step_fn_fp16,
        attn_factory_fp8,
        attn_factory_fp16,
        SC_KNOWN_SEQUENCE,
        extra_kv_len=128,  # to account for graph capturing
    )


def test_logits_are_close_graphed(
    starcoder_1b_fp8_graphed_fixture, starcoder_1b_fp16_fixture
):
    """Compare fp8 graphed vs fp16 ungraphed."""
    step_fn_fp8, attn_factory_fp8 = starcoder_1b_fp8_graphed_fixture
    step_fn_fp16, attn_factory_fp16 = starcoder_1b_fp16_fixture
    step_fn_fp16 = fwd_utils.PaddedStepFunction(step_fn_fp16, [8, 32])
    fwd_model_test_utils.check_logits_are_close(
        step_fn_fp8,
        step_fn_fp16,
        attn_factory_fp8,
        attn_factory_fp16,
        SC_KNOWN_SEQUENCE,
        extra_kv_len=128,  # to account for graph capturing
    )


@pytest.mark.parametrize(
    "prompt",
    [
        pytest.param([1], id="one"),
        pytest.param([1, 2], id="two"),
        pytest.param([1, 2, 3], id="three"),
        pytest.param(SC_KNOWN_SEQUENCE, id="known_sequence"),
    ],
)
def test_batched_equals_sequential(starcoder_1b_fp8_fixture, prompt: list[int]):
    step_fn, attn_factory = starcoder_1b_fp8_fixture
    step_fn = fwd_utils.pad_and_step(step_fn, [8])
    fwd_model_test_utils.check_batched_equals_sequential(
        step_fn,
        attn_factory,
        prompt,
        extra_kv_len=8,  # to account for padding
    )


@pytest.mark.parametrize(
    "prompt",
    [
        pytest.param([1], id="one"),
        pytest.param([1, 2], id="two"),
        pytest.param([1, 2, 3, 4, 5, 6, 7, 8, 9], id="nine"),
        pytest.param(SC_KNOWN_SEQUENCE, id="known_sequence"),
    ],
)
def test_batched_equals_sequential_graphed(
    starcoder_1b_fp8_graphed_fixture, prompt: list[int]
):
    # Avoiding the use of fixtures because cuda graphs are stateful
    # behavior in the step function.
    step_fn, attn_factory = starcoder_1b_fp8_graphed_fixture
    step_fn = fwd_utils.PaddedStepFunction(step_fn, [8, 32])
    fwd_model_test_utils.check_batched_equals_sequential(
        step_fn, attn_factory, prompt, kv_len=128
    )
