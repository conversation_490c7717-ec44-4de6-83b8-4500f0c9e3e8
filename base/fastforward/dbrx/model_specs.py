"""ModelSpecs for different models, such as LLaMA, DeepSeek, and DBRX."""

import dataclasses
import pathlib

from base.fastforward import cached_attention, fwd


@dataclasses.dataclass
class DbrxModelSpec(fwd.ModelSpec):
    """ModelSpec for DBRX models. Needs to specify num_queries_per_had for GQA."""

    num_queries_per_head: int = 1
    """Used for GQA / MQA models."""

    attn_split_head_mode: cached_attention.SplitHeadModes = (
        cached_attention.SplitHeadModes.NO_SPLIT
    )
    """Tensor parallelism in attention layers. None means to not use."""

    attn_clip_qkv: float | None = 8.0
    """The clip_qkv value for the Attention layer."""

    # The DbrxFFN hyper-parameters.
    mlp_hidden_dim: int = 10752
    """MLP's hidden_dim."""

    moe_num_experts: int = 16
    """The number of experts in DbrxFFN."""

    moe_top_k: int = 4
    """The top-k value for DbrxFFN."""

    moe_normalize_expert_weights: float | None = 1.0
    """The weight for normalizing the expert weights in DbrxFFN."""

    def __post_init__(self):
        super().__post_init__()
        self.num_heads_kv = self.num_heads
        self.num_heads_q = self.num_heads_kv * self.num_queries_per_head


_DBRX_MODEL_SPECS: dict[str, DbrxModelSpec] = {
    # The vocab size is set as 100352 following the HF config and it is larger than the vocab size of the tokenizer.
    # The head_dim is 128 = 6144 // 48.
    "dbrx-132b": DbrxModelSpec(
        name="dbrx-132b",
        checkpoint_path="",
        num_layers=40,
        vocab_size=100352,
        emb_dim=6144,
        num_heads=8,
        head_dim=128,
        rotary_theta=500000.0,
        rotary_scaling_factor=1.0,
        rotary_pct=1.0,
        rotary_interleave=False,
        max_position_embeddings=32768,
        unscaled_max_position_embeddings=32768,
        norm_eps=1e-05,
        checkpoint_sha256=None,
        num_queries_per_head=6,
        attn_split_head_mode=cached_attention.SplitHeadModes.KV_HEADS,
        mlp_hidden_dim=10752,
        moe_num_experts=16,
        moe_top_k=4,
        moe_normalize_expert_weights=1.0,
    ),
    # This is a super small DBRX model for testing purposes.
    "dbrx-nano": DbrxModelSpec(
        name="dbrx-nano",
        checkpoint_path="",
        num_layers=2,
        vocab_size=1024,
        emb_dim=256,
        num_heads=8,
        head_dim=32,
        rotary_theta=500000.0,
        rotary_scaling_factor=1.0,
        rotary_pct=1.0,
        rotary_interleave=False,
        max_position_embeddings=1024,
        unscaled_max_position_embeddings=1024,
        norm_eps=1e-05,
        checkpoint_sha256=None,
        num_queries_per_head=6,
        attn_split_head_mode=cached_attention.SplitHeadModes.KV_HEADS,
        mlp_hidden_dim=512,
        moe_num_experts=16,
        moe_top_k=4,
        moe_normalize_expert_weights=1.0,
    ),
}


def get_model_spec(
    model_name: str,
    checkpoint_path: pathlib.Path | str = "",
    checkpoint_sha256: str | None = None,
) -> DbrxModelSpec:
    """Returns a DBRX model spec."""
    ms = _DBRX_MODEL_SPECS[model_name]
    ms = dataclasses.replace(ms)
    if checkpoint_path is not None:
        if not isinstance(checkpoint_path, str):
            checkpoint_path = str(checkpoint_path.absolute())
        ms.checkpoint_path = checkpoint_path
    if checkpoint_sha256 is not None:
        ms.checkpoint_sha256 = checkpoint_sha256
    return ms
