load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("@python_pip//:requirements.bzl", "requirement")

py_library(
    name = "conftest",
    testonly = True,
    srcs = [
        "conftest.py",
    ],
    deps = [
        ":fwd_dbrx",
        ":model_specs",
        "//base/fastforward:fwd",
        requirement("pytest"),
        requirement("torch"),
    ],
)

py_library(
    name = "fwd_dbrx",
    srcs = ["fwd_dbrx.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":model_specs",
        "//base/fastforward:cached_attention",
        "//base/fastforward:cuda_graphs_attention",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_torch",
        "//base/fastforward:layers",
        "//base/fastforward:parallel_fwd",
        "//base/fastforward/checkpoints:save_load",
        requirement("torch"),
        requirement("numpy"),
    ],
)

py_library(
    name = "model_specs",
    srcs = ["model_specs.py"],
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:cached_attention",
        "//base/fastforward:fwd",
        requirement("torch"),
        requirement("numpy"),
    ],
)

pytest_test(
    name = "fwd_dbrx_test",
    size = "medium",
    srcs = [
        "fwd_dbrx_test.py",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        "//base/fastforward/dbrx:conftest",
        requirement("torch"),
    ],
)
