"""Contains the pytorch modules and function for the positional embeddings."""

import math
from dataclasses import dataclass
from typing import Op<PERSON>, <PERSON><PERSON>, Union

import numpy as np
import torch
from dataclasses_json import DataClassJsonMixin

try:
    from base.fastforward import compiled_rotary

    _compiled_rotary_exception = None
    _compiled_rotary_exception_msg = None
except ImportError as ex:
    msg = """There was an issue importing the `compiled_rotary` package. If you
             are outside a bazel-managed environment, you can build the package with:
             > bazel run //base/fastforward:install
          """
    compiled_rotary = None
    _compiled_rotary_exception = ex
    _compiled_rotary_exception_msg = msg


try:
    from base.fastforward import compiled_rotary_qkv

    _compiled_rotary_qkv_exception = None
    _compiled_rotary_qkv_exception_msg = None
except ImportError as ex:
    msg = """There was an issue importing the `compiled_rotary_qkv` package. If you
             are outside a bazel-managed environment, you can build the package with:
             > bazel run //base/fastforward:install
          """
    compiled_rotary_qkv = None
    _compiled_rotary_qkv_exception = ex
    _compiled_rotary_qkv_exception_msg = msg

Device = Union[torch.device, str]


def _find_dim_for_rotation_count(
    rotation_count: int,
    dim: int,
    base: float = 10000,
    max_position_embeddings: int = 2048,
):
    """Find the dimension index for a rotation count at maximum sequence length.

    I.e. at the dimension correspond to the return index, the RoPE frequencies will
    rotate `rotation_count` full cycles on a sequence of length
    `max_position_embeddings`, up to integer rounding.
    """
    return (
        dim * math.log(max_position_embeddings / (rotation_count * 2 * math.pi))
    ) / (2 * math.log(base))


def _find_dim_range_for_rotation_range(
    rotation_floor: int,
    rotation_ceiling: int,
    dim: int,
    base: float = 10000,
    max_position_embeddings: int = 2048,
):
    """Finds a range of dimension indices for a range of max rotation counts.

    The floor and ceiling of the dimension indices correspond to the ceiling and
    floor of the rotation counts, respectively.
    """
    low = math.floor(
        _find_dim_for_rotation_count(rotation_floor, dim, base, max_position_embeddings)
    )
    high = math.ceil(
        _find_dim_for_rotation_count(
            rotation_ceiling, dim, base, max_position_embeddings
        )
    )
    return max(low, 0), min(high, dim - 1)  # Clamp values just in case


def _get_linear_ramp_mask(begin: float, end: float, dim: int):
    """Creates a linear ramp mask."""
    if begin == end:
        end += 0.001  # Prevent singularity

    linear_func = (torch.arange(dim, dtype=torch.float32) - begin) / (end - begin)
    ramp_func = torch.clamp(linear_func, 0, 1)
    return ramp_func


def _get_yarn_extrapolation_ramp_mask(
    beta_fast: int,
    beta_slow: int,
    dim: int,
    base: float,
    unscaled_max_position_embeddings: int,
    device: Device,
):
    """Creates the YaRN extrapolation ramp mask."""
    ramp_start, ramp_end = _find_dim_range_for_rotation_range(
        beta_fast,
        beta_slow,
        dim,
        base,
        unscaled_max_position_embeddings,
    )
    # NOTE: The `1 -` here might look odd, as the call site uses both `mask` and
    # `1 - mask`. However, applying `1 - ` and sending to CUDA is not numerically
    # equivalent to sending the mask to CUDA first and then applying `1 -`. One must
    # maintain this order of operations for numeric equivalence with the official
    # reference implementation of YaRN.
    mask = 1 - _get_linear_ramp_mask(ramp_start, ramp_end, dim // 2)
    mask = mask.to(device=device, dtype=torch.float32)
    return mask


def get_yarn_temperature_scaling_factor(
    rotary_scaling_factor: float, mscale: float = 1.0
) -> float:
    """Gets the temperature scaling factor from the RoPE scaling factor."""
    if rotary_scaling_factor <= 1:
        return 1.0
    return 1.0 + 0.1 * math.log(rotary_scaling_factor) * mscale


def _apply_llama3_1_scaling(freqs: torch.Tensor, rotary_scaling_factor: float):
    """Applies LLaMa3.1 RoPE scaling (NTK-by-part).

    Args:
        freqs: RoPE frequencies of shape (rotary_dim // 2,).
        rotary_scaling_factor: The ratio of the new maximum sequence length to the
            original maximum sequence length before scaling.

    Returns:
        Scaled RoPE frequencies of shape (rotary_dim // 2,).
    """
    # Values obtained from grid search
    low_freq_factor = 1
    high_freq_factor = 4
    old_context_len = 8192  # original llama3 length

    low_freq_wavelen = old_context_len / low_freq_factor
    high_freq_wavelen = old_context_len / high_freq_factor
    new_freqs = []
    for freq in freqs:
        wavelen = 2 * math.pi / freq
        if wavelen < high_freq_wavelen:
            new_freqs.append(freq)
        elif wavelen > low_freq_wavelen:
            new_freqs.append(freq / rotary_scaling_factor)
        else:
            assert low_freq_wavelen != high_freq_wavelen
            smooth = (old_context_len / wavelen - low_freq_factor) / (
                high_freq_factor - low_freq_factor
            )
            new_freqs.append(
                (1 - smooth) * freq / rotary_scaling_factor + smooth * freq
            )
    return torch.tensor(new_freqs, dtype=freqs.dtype, device=freqs.device)


def _precompute_llama3_1_freqs_cis(
    dim: int,
    end: int,
    theta: float = 10000.0,
    rotary_scaling_factor: float = 1.0,
):
    """Precomputes the RoPE table for LLaMa3.1 (NTK-by-part scaling).

    Args:
        dim: RoPE dimension per head.
        end: Maximum sequence length.
        theta: The base power for the cos/sin tables.
        rotary_scaling_factor: Context extension factor.

    Returns:
        A complex64 RoPE table of shape (end, dim // 2), containing the cos/sin values
        as complex numbers.
    """
    freqs = 1.0 / (theta ** (torch.arange(0, dim, 2)[: (dim // 2)].float() / dim))
    t = torch.arange(end, device=freqs.device, dtype=torch.float32)
    if rotary_scaling_factor != 1.0:
        freqs = _apply_llama3_1_scaling(freqs, rotary_scaling_factor)
    freqs = torch.outer(t, freqs)
    freqs_cis = torch.polar(torch.ones_like(freqs), freqs)  # complex64
    return freqs_cis


@dataclass(frozen=True)
class DeepSeekV1ExtensionConfig(DataClassJsonMixin):
    """Configuration for the DeepSeek V1 rotary embeddings, which just applies the position interpolation."""

    rotary_scaling_factor: float
    """Scaling factor for RoPE extension."""


@dataclass(frozen=True)
class YaRNExtensionConfig(DataClassJsonMixin):
    """Configuration for the YaRN rotary embeddings."""

    rotary_scaling_factor: float
    """Scaling factor for RoPE extension."""

    unscaled_max_position_embeddings: int
    """Original max position size during training. This parameter is necessary to compute the ramp limits of YaRN."""

    beta_fast: int
    """Rotation count of the higher-frequency end of the YaRN ramp."""

    beta_slow: int
    """Rotation count of the lower-frequency end of the YaRN ramp."""

    mscale: float
    """Additional scaling factor for YaRN temperature calculation."""


@dataclass(frozen=True)
class Llama31ExtensionConfig(DataClassJsonMixin):
    """Configuration for the rotary embeddings."""

    rotary_scaling_factor: float
    """Scaling factor for RoPE extension."""

    def __post_init__(self):
        # NOTE(zhouran): LLaMa 3.1 is continue pre-trained with a scaling factor of 8.0.
        # If we start to post-train it with a different scaling factor, we can remove
        # this assertion.
        assert self.rotary_scaling_factor == 8.0, self.rotary_scaling_factor


@dataclass(frozen=True)
class RotaryConfig(DataClassJsonMixin):
    """Configuration for the rotary embeddings."""

    rotary_ratio: float
    """Ratio of head dimension to which we apply rotary embeddings. Also called `rotary_pct` in other locations, but it is always a value in [0, 1]."""

    rotary_theta: float
    """Base power for rope."""

    max_position_embeddings: int
    """Max position size used with the position embeddings."""

    rotary_interleave: bool = True
    """Whether to interleave the rotary embeddings."""

    ext_config: (
        YaRNExtensionConfig | Llama31ExtensionConfig | DeepSeekV1ExtensionConfig | None
    ) = None
    """The extension config that YaRN or LLAMA 3.1 or any other Rotary configs that is different than the basic RoPE.
    Please do not put the default values for these extended config definations. This aims to avoid DataClassJsonMixin.from_dict() to confuse.
    """

    def __post_init__(self):
        assert 0.0 <= self.rotary_ratio <= 1.0, self.rotary_ratio
        assert self.rotary_theta > 1.0, self.rotary_theta
        assert self.max_position_embeddings > 0, self.max_position_embeddings


@torch.inference_mode()
def calculate_rotary_freqs(
    rope_dim: int,
    max_seq_len: int,
    config: RotaryConfig,
    device: Device,
):
    """Calculates cosine and sine tables for different variants of RoPE."""
    if config.ext_config is None:  # the vanilla RoPE
        dim_range = torch.arange(0, rope_dim, 2, dtype=torch.float32, device=device)
        freqs_powers = dim_range[: (rope_dim // 2)] / rope_dim
        freqs = 1.0 / (config.rotary_theta**freqs_powers)
        t = torch.arange(max_seq_len, dtype=torch.float32, device=device)
        freqs = torch.outer(t, freqs)
        freqs_cos, freqs_sin = freqs.cos(), freqs.sin()
    elif isinstance(config.ext_config, DeepSeekV1ExtensionConfig):
        dim_range = torch.arange(0, rope_dim, 2, dtype=torch.float32, device=device)
        freqs_powers = dim_range[: (rope_dim // 2)] / rope_dim
        freqs = 1.0 / (
            config.ext_config.rotary_scaling_factor * config.rotary_theta**freqs_powers
        )
        t = torch.arange(max_seq_len, dtype=torch.float32, device=device)
        freqs = torch.outer(t, freqs)
        freqs_cos, freqs_sin = freqs.cos(), freqs.sin()
    elif isinstance(config.ext_config, YaRNExtensionConfig):
        dim_range = torch.arange(0, rope_dim, 2, dtype=torch.float32, device=device)
        freqs_powers = dim_range[: (rope_dim // 2)] / rope_dim
        freqs_extra = 1.0 / (config.rotary_theta**freqs_powers)
        freqs_inter = 1.0 / (
            config.ext_config.rotary_scaling_factor * config.rotary_theta**freqs_powers
        )
        extrapolation_ramp_mask = _get_yarn_extrapolation_ramp_mask(
            config.ext_config.beta_fast,
            config.ext_config.beta_slow,
            rope_dim,
            config.rotary_theta,
            config.ext_config.unscaled_max_position_embeddings,
            device=device,
        )
        freqs = freqs_inter * (1 - extrapolation_ramp_mask) + freqs_extra * (
            extrapolation_ramp_mask
        )
        t = torch.arange(max_seq_len, dtype=torch.float32, device=device)
        freqs = torch.outer(t, freqs)
        temperature = get_yarn_temperature_scaling_factor(
            config.ext_config.rotary_scaling_factor, config.ext_config.mscale
        )
        freqs_cos = freqs.cos() * temperature
        freqs_sin = freqs.sin() * temperature
    elif isinstance(config.ext_config, Llama31ExtensionConfig):
        freqs = _precompute_llama3_1_freqs_cis(
            rope_dim,
            max_seq_len,
            config.rotary_theta,
            config.ext_config.rotary_scaling_factor,
        )
        freqs_cos = freqs.real.to(device=device)
        freqs_sin = freqs.imag.to(device=device)
        return freqs_cos, freqs_sin
    else:
        raise TypeError(f"Unsupported extended config: {config.ext_config}")
    assert freqs_cos.shape == (max_seq_len, rope_dim // 2)
    assert freqs_sin.shape == (max_seq_len, rope_dim // 2)
    return freqs_cos, freqs_sin


# DEPRECATED in favor of `FusedRotaryEmbedding` below.
class RopeEmbedding(torch.nn.Module):
    """Re-implement ROPE embeddings to simplify the logic in `RotaryEmbedding`."""

    def __init__(
        self,
        head_dim: int,
        config: RotaryConfig,
        precision: torch.dtype = torch.float32,
        device: Device = "cuda",
    ):
        """Computes and caches the cos/sin tensors.

        Args:
            head_dim: used for the `q` and `k` tensors that will be used. Technically,
                this argument is not needed, but we require it ensure the intended
                shapes for `q` and `k` are known and fixed.
            config: the configuration for the rotary embeddings.
            precision: the ROPE cos/sin tensors will be stored in this format.
            device: the GPU / CPU to keep the cos/sin tensors.
        """
        super().__init__()
        rope_dim = int(config.rotary_ratio * head_dim)
        assert rope_dim % 2 == 0
        assert config.ext_config is None, "Only supports the vanilla RoPE."

        self.max_seq_len = config.max_position_embeddings
        self.head_dim = head_dim
        self.rope_dim = rope_dim
        self.device = device

        # compute rotation indices
        rot_inds = np.arange(0, rope_dim, dtype=np.int32)
        if config.rotary_interleave:
            rot_inds = rot_inds.reshape(-1, 2)[:, ::-1].reshape(-1).copy()
        else:
            rot_inds = rot_inds.reshape(2, -1)[::-1, :].reshape(-1).copy()

        # compute `cos` and `sin` using numpy, then move them into GPUs using torch.
        pwr = np.arange(0, rope_dim, 2, dtype=np.float32) / float(-rope_dim)
        inv_freq = np.float_power(config.rotary_theta, pwr).astype(np.float32)

        t = np.arange(config.max_position_embeddings, dtype=np.float32)
        emb = np.einsum("i,j->ij", t, inv_freq)[:, None, :]

        cos = np.cos(emb)
        sin = np.sin(emb)

        cos = cos.repeat(2, 2) if config.rotary_interleave else np.tile(cos, [1, 1, 2])
        sin = sin.repeat(2, 2) if config.rotary_interleave else np.tile(sin, [1, 1, 2])
        if config.rotary_interleave:
            sin[:, :, ::2] *= float(-1.0)
        else:
            sin[:, :, : rope_dim // 2] *= float(-1.0)

        rot_inds = torch.tensor(rot_inds, dtype=torch.int32).to(device=device)

        cos = torch.tensor(cos, dtype=precision).to(device=device)
        sin = torch.tensor(sin, dtype=precision).to(device=device)

        self.register_buffer("rot_inds", rot_inds, persistent=False)
        self.register_buffer("cos_cached", cos, persistent=False)
        self.register_buffer("sin_cached", sin, persistent=False)

    @torch.inference_mode()
    def forward(
        self,
        x: torch.Tensor,
        offset: Optional[torch.Tensor] = None,
        pos: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """Applies ROPE to tensor `x` according to `offset` or `pos`.

        Exactly one of `offset` and `pos` must be given to indicate the
        positions of the tokens in `x`.

        Args:
            x: the tensor to apply ROPE on.
            offset: if not None, both `q` and `k` start from this offset.
            pos: if not None, must have the shape `[q.size(0),]`, where each number
                indicates the position of the corresponding token in `q` and `k`.
        """
        assert (
            int(offset is None) + int(pos is None) == 1
        ), "Exactly one of `offset` and `pos` must be specified."

        assert x.size(0) <= self.max_seq_len, f"{x.size(0)=}, but {self.max_seq_len=}."
        assert x.size(-1) == self.head_dim, f"Expect {self.head_dim=}, got {x.size()=}."

        if pos is None:
            # this means that we are given `offset`. we will turn it into `pos`
            assert offset is not None
            pos = (
                torch.arange(x.size(0), dtype=torch.int32, device=self.device) + offset
            )

        cos = self.cos_cached[pos, :, :]
        sin = self.sin_cached[pos, :, :]
        ndim = x.ndim
        head_dim = self.head_dim
        rope_dim = self.rope_dim
        cos = cos.unsqueeze(2) if ndim == 4 else cos
        sin = sin.unsqueeze(2) if ndim == 4 else sin

        x_rope, x_pass = x.split([rope_dim, head_dim - rope_dim], ndim - 1)
        x_rope = x_rope.mul(cos).addcmul_(x_rope[..., self.rot_inds], sin)
        return torch.concat((x_rope, x_pass), dim=ndim - 1)


# NOTE: We comment out this function because it is not used everywhere and has some issues about documentation.
# Given it is still a good python reference implementation, we keep it here.
#
# # NOTE: we have disabled the use of `torch.compile` in production for now.
# # @torch.compile(dynamic=False)
# def indexed_rotary_embed(
#     x: torch.Tensor,
#     freqs_cos: torch.Tensor,
#     freqs_sin: torch.Tensor,
#     idxs: torch.Tensor,
# ):
#     """A standalone implementation of RoPE with position indices focused on compilation.

#     This is based on the complex number-based implementation from the upstream LLaMA repo.
#     See also `rotary_embed` from `research/fastbackward/rotary.py`.

#     NOTE: This is the LLaMA-style implementation, also called "interleaved" in (eg) Megatron.
#     Args:
#         x: Inpute activations of shape (ntokens, *, headdim). * is any number of dimensions for
#           RoPE to broadcast over.
#         freqs_{cos, sin}: Pre-computed cos/sin tables of shape (max_seqlen, headdim // 2).
#           These are computed in the `FusedRotaryEmbedding` class.
#         idxs: Sequence indices of shape (ntokens,) for each element of `x`. Each index must lie in
#           the range [0, max_seqlen).

#     Returns:
#         Result tensor of shape (ntokens, *, headdim).
#     """
#     x_ = x.float().reshape(*x.shape[:-1], -1, 2)
#     x_real = x_[..., 0]
#     x_imag = x_[..., 1]

#     cos = freqs_cos[idxs, ...]
#     sin = freqs_sin[idxs, ...]

#     cos_real = cos * x_real
#     sin_imag = sin * x_imag
#     cos_imag = cos * x_imag
#     sin_real = sin * x_real
#     real_part = (cos_real - sin_imag).type_as(x)
#     imag_part = (cos_imag + sin_real).type_as(x)
#     return torch.stack((real_part, imag_part), dim=-1).flatten(-2)


class FusedRotaryEmbedding(torch.nn.Module):
    """Another implementation of RoPE focused on fusing into a single kernel.

    NOTE(Xuanyi): be careful about the rotary_interleave argument. By default rotary_interleave=True.
    If you rotate a tensor with the last dimension as feature dimension, it should be like
    [x_1, y_1, x_2, y_2, ..., x_n, y_n] as every two features represent a complex number (or a vector) to
    be rotated. In our codebase, most implementations of RoPE assumes this interleaving format.
    However, in the HF implementation, they assume the tensor like [x_1, x_2, ..., x_n, y_1, y_2, ..., y_n].
    We support it by setting rotary_interleave=False in this module. If we do not support, it will cause a lot of
    pain when converting checkpoints from HF to our codebase, etc.
    """

    def __init__(
        self,
        head_dim: int,
        max_seq_len: int,
        config: RotaryConfig,
        device: Device = "cuda",
    ):
        super().__init__()
        self._config = config
        self.head_dim = head_dim
        self.rope_dim = int(config.rotary_ratio * head_dim)
        self.max_seq_len = max_seq_len
        self.rotary_interleave = config.rotary_interleave
        # Keep a vector of 3x 1s for "fp8 scales" when not scaling.
        self.ones = torch.ones(3, dtype=torch.float32, device=device)

        assert self.rope_dim % 2 == 0, f"{self.rope_dim=}, but must be even."

        self.freqs_cos, self.freqs_sin = calculate_rotary_freqs(
            rope_dim=self.rope_dim,
            max_seq_len=self.max_seq_len,
            config=config,
            device=device,
        )

    @torch.inference_mode()
    def forward(self, x: torch.Tensor, pos: torch.Tensor):
        # pos.max().item() should be smaller than self.max_seq_len.
        assert x.size(-1) == self.head_dim, f"Expect {self.head_dim=}, got {x.size()=}."
        assert pos.ndim == 1
        assert pos.size(0) == x.size(0)

        freqs_cos, freqs_sin = self.freqs_cos, self.freqs_sin

        # NOTE: we enforce that the compiled kernels are available. I have left the other branch
        # below for debugging or to move over to `torch.compile` once we can use it in production.
        if compiled_rotary is None:
            raise RuntimeError(
                _compiled_rotary_exception_msg
            ) from _compiled_rotary_exception
        assert compiled_rotary is not None

        # TODO(Xuanyi or Carl): support the rotary_interleave FLAG in the compiled kernel.
        if self.rotary_interleave:
            x = compiled_rotary.rotary_embed(x, freqs_cos, freqs_sin, pos)
        else:
            if x.size(-1) > self.rope_dim:
                x, others = x.split([self.rope_dim, x.size(-1) - self.rope_dim], dim=-1)
            else:
                x, others = x, None
            x = _not_to_interleave(x)
            x = compiled_rotary.rotary_embed(x, freqs_cos, freqs_sin, pos)
            x = _interleave_to_not(x)
            if others is not None:
                x = torch.cat((x, others), dim=-1)
        return x

    @torch.inference_mode()
    def rotary_qkv(
        self,
        q: torch.Tensor,
        pos_q: torch.Tensor,
        k: torch.Tensor,
        pos_kv: torch.Tensor,
        k_dest: torch.Tensor,
        v: torch.Tensor,
        v_dest: torch.Tensor,
        kv_cache_idxs: torch.Tensor,
        qkv_scales: torch.Tensor | None,
    ):
        """Integrated kernel for rotary embeddings for query and key; also copying keys and values into target tensors.

        Args:
            q: Activations of shape (ntokens_q, q_heads, headdim). This argument can be either
                query or key, as we have to apply the same rotary embeddings to both. For simplicity,
                we call it `q`, as this argument is intended to be used for the query if a key is
                also passed in as `k`.
            pos_q: Sequence indices of shape (ntokens_q,) for each element of `q`. Each index must lie
                in the range [0, max_seqlen).
            k: Activations of shape (ntokens_kv, kv_heads, headdim). We apply rotary embeddings to the
                keys and copy them into the destination tensor.
            pos_kv: Sequence indices of shape (ntokens_kv,) for each element of `k` and `v`. Each index must lie
                in the range [0, max_seqlen).
            k_dest: Destination activations of shape (ncaches, ntokens_kv, kv_heads, headdim).
            v: Activations of shape (ntokens_kv, kv_heads, headdim). We never apply rotary embeddings to
                the values. We only copy them into the destination tensor.
            v_dest: Destination activations of shape (ncaches, ntokens_kv, kv_heads, headdim).
            kv_cache_idxs: Indices of shape (ntokens_kv,) in the range [0, ncaches). Used to index into
                `k_dest` and `v_dest`.
            qkv_scales: 3-element tensor with scale factors for Q/K/V, respectively -- ie, the
                result `q` is multiplied by `qkv_scales[0]`, and so on. If None, then no scaling is
                applied.
        """
        assert q.size(0) <= self.max_seq_len, f"{q.size(0)=}, but {self.max_seq_len=}."
        assert q.size(-1) == self.head_dim, f"Expect {self.head_dim=}, got {q.size()=}."
        assert pos_q.ndim == 1
        assert pos_q.size(0) == q.size(0)
        assert pos_kv.ndim == 1
        assert pos_kv.size(0) == k.size(0)

        freqs_cos, freqs_sin = self.freqs_cos, self.freqs_sin

        assert k.shape == v.shape
        assert k.shape[1:] == k_dest.shape[2:]
        assert v.shape[1:] == v_dest.shape[2:]
        assert (
            pos_kv.shape == kv_cache_idxs.shape
        ), f"{pos_kv.shape=} != {kv_cache_idxs.shape=}"
        assert (
            kv_cache_idxs.shape[0] == k.shape[0]
        ), f"{kv_cache_idxs.shape[0]=} != {k.shape[0]=}"
        assert (
            kv_cache_idxs.shape[0] == v.shape[0]
        ), f"{kv_cache_idxs.shape[0]=} != {v.shape[0]=}"

        assert (
            self.rotary_interleave
        ), "rotary_qkv is only supported for interleaved rotary embeddings."

        if compiled_rotary_qkv is None:
            raise RuntimeError(
                _compiled_rotary_qkv_exception_msg
            ) from _compiled_rotary_qkv_exception
        assert compiled_rotary_qkv is not None
        return compiled_rotary_qkv.rotary_embed_qkv(
            q,
            pos_q,
            k,
            v,
            freqs_cos,
            freqs_sin,
            pos_kv,
            k_dest,
            v_dest,
            kv_cache_idxs,
            qkv_scales if qkv_scales is not None else self.ones,
        )


class RotaryEmbedding(torch.nn.Module):
    """Module to implement rotary position encoding.

    See https://blog.eleuther.ai/rotary-embeddings/ for an easy explanation.
    """

    def __init__(
        self,
        dim: int,
        config: RotaryConfig,
        precision: torch.dtype = torch.float,
        device=None,
    ):
        super().__init__()
        self.max_seq_length = config.max_position_embeddings
        self.precision = precision
        self.rotary_interleave = config.rotary_interleave
        self.rotary_ndims = int(config.rotary_ratio * dim)
        inv_freq = 1.0 / (
            config.rotary_theta ** (torch.arange(0, dim, 2).float().to(device) / dim)
        )
        self.register_buffer("inv_freq", inv_freq, persistent=False)
        # NOTE: it's important to create cached cos/sin tables here before the module gets cast to
        # a lower precision (which will cast `inv_freq` and by extension `emb` below).
        self._prepare_cache(device)

    def _is_bf16(self):
        return self.precision == torch.bfloat16

    @torch.inference_mode()
    def forward(
        self,
        x: torch.Tensor,
        seq_dim: int = 1,
        seq_len: Optional[int] = None,
        offset: int = 0,
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward call.

        Args:
            x: if seq_len is set, only used to set the device of the tensors created.
            Otherwise, use to find the seq_len
            seq_dim: if seq_len is set, not used. Otherwise, it is used to find the seq_len
            seq_len: The sequence length to use. If not set, the sequence length is calculated
                based on x and seq_dim
            offset: The offset to use.
        """
        if seq_len is None:
            seq_len = x.shape[seq_dim]
        if self.cos_cached is None or self.sin_cached is None:
            cos, sin = self._prepare_cache(device=x.device)
        else:
            cos = self.cos_cached
            sin = self.sin_cached

        # return tuple of tensors of size seq_len to pass the contract
        # of Module forward
        cos = cos[offset : offset + seq_len, :, :, :]
        sin = sin[offset : offset + seq_len, :, :, :]

        return cos, sin

    @torch.inference_mode()
    def _prepare_cache(self, device) -> Tuple[torch.Tensor, torch.Tensor]:
        """Prepares the cos/sin cache."""
        # pylint: disable=access-member-before-definition
        if str(device) != str(self.inv_freq.device):
            self.inv_freq = self.inv_freq.to(device)
        # pylint: enable=access-member-before-definition
        t = torch.arange(self.max_seq_length, device=device).type_as(self.inv_freq)
        freqs = torch.einsum("i,j->ij", t, self.inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        assert emb.dtype == torch.float32
        cos_cached = emb.cos()[:, None, None, :]
        sin_cached = emb.sin()[:, None, None, :]
        if self.rotary_interleave:
            # move the interleaving into the cache to not repeat
            # the operation multiple times
            cos_cached = cos_cached[
                :, :, :, : cos_cached.shape[3] // 2
            ].repeat_interleave(2, 3)
            sin_cached = sin_cached[
                :, :, :, : sin_cached.shape[3] // 2
            ].repeat_interleave(2, 3)
        cos_cached = cos_cached.to(self.precision)
        sin_cached = sin_cached.to(self.precision)
        self.register_buffer("cos_cached", cos_cached, persistent=False)
        self.register_buffer("sin_cached", sin_cached, persistent=False)
        return cos_cached, sin_cached

    def get_apply_fn(self):
        """Returns a function to apply the rotary.

        The second and third argument are assumed to be the cos/sin cache values
        returned from the last call to the rotary embedding.

        """
        if self.rotary_interleave:
            return _apply_rotary_pos_emb_interleave
        else:
            return (
                _apply_rotary_pos_emb_torch
                if self._is_bf16()
                else _apply_rotary_pos_emb
            )

    def embed(
        self,
        query_layer: torch.Tensor,  # q_len, ..., head_dim (depending on MQA / GQA)
        key_layer: torch.Tensor,  # kv_len, num_heads, head_dim
        num_past_keys: int,
    ) -> tuple[torch.Tensor, torch.Tensor]:
        seq_len = key_layer.shape[0]
        cos, sin = self(key_layer, seq_len=seq_len, offset=num_past_keys)
        if self.rotary_ndims is not None and self.rotary_interleave:
            # Special case
            (query_layer, query_pass, key_layer, key_pass) = _partial_rotary_interleave(
                dims=self.rotary_ndims,
                query_layer=query_layer,
                key_layer=key_layer,
                cos=cos,
                sin=sin,
                offset=num_past_keys,
            )
            # cat not part of _partial_rotary_interleave as torchscript generates
            # an invalid tensor output. Likely related to dim-1
            query_layer = torch.cat((query_layer, query_pass), dim=-1)
            key_layer = torch.cat((key_layer, key_pass), dim=-1)

            return query_layer, key_layer

        else:
            if self.rotary_ndims is not None:
                # partial rotary
                query_rot, query_pass = (
                    query_layer[..., : self.rotary_ndims],
                    query_layer[..., self.rotary_ndims :],
                )
                key_rot, key_pass = (
                    key_layer[..., : self.rotary_ndims],
                    key_layer[..., self.rotary_ndims :],
                )
            else:
                # full rotary
                query_rot, key_rot = query_layer, key_layer
                query_pass, key_pass = None, None

            apply_rotary_fn = self.get_apply_fn()
            query_layer = apply_rotary_fn(query_rot, cos, sin)
            key_layer = apply_rotary_fn(key_rot, cos, sin)

            if self.rotary_ndims is not None:
                assert query_pass is not None and key_pass is not None
                query_layer = torch.cat((query_layer, query_pass), dim=-1)
                key_layer = torch.cat((key_layer, key_pass), dim=-1)

            return query_layer, key_layer


# rotary pos emb helpers:


def _interleave_to_not(x: torch.Tensor):
    """Permute the tensor from [x_1, y_1, ..., x_n, y_n] to [x_1, ..., x_n, y_1, ..., y_n]."""
    x_aux = x.reshape(*x.shape[:-1], -1, 2).transpose(-2, -1)
    return x_aux.reshape(*x.shape)


def _not_to_interleave(x: torch.Tensor):
    """Permute the tensor from [x_1, ..., x_n, y_1, ..., y_n] to [x_1, y_1, ..., x_n, y_n]."""
    x_aux = x.reshape(*x.shape[:-1], 2, -1).transpose(-2, -1)
    return x_aux.reshape(*x.shape)


@torch.jit.script
def _expand_to_dim4(x: torch.Tensor) -> torch.Tensor:
    """Adds more dims to a tensor `x` to support broadcasting."""
    if x.ndim == 4:
        return x
    elif x.ndim == 3:
        return x[:, :, None, :]
    else:
        raise ValueError(f"Unsupported tensor size {x.size()}.")


@torch.jit.script
def _rotate_half(x):
    x1, x2 = x[..., : x.shape[-1] // 2], x[..., x.shape[-1] // 2 :]
    # dim=-1 triggers a bug in earlier torch versions
    return torch.cat((-x2, x1), dim=x1.ndim - 1)


@torch.jit.script
def _rotate_every_two(x: torch.Tensor) -> torch.Tensor:
    x1 = x[..., ::2]
    x2 = x[..., 1::2]
    x = torch.stack((-x2, x1), dim=-1)
    return x.flatten(-2)  # in einsum notation: rearrange(x, '... d j -> ... (d j)')


@torch.jit.script
def _apply_rotary_pos_emb(x, cos, sin):
    return (x * cos) + (_rotate_half(x) * sin)


@torch.jit.script
def _apply_rotary_pos_emb_interleave(
    x: torch.Tensor, cos: torch.Tensor, sin: torch.Tensor, offset: int = 0
) -> torch.Tensor:
    assert cos.shape[0] == sin.shape[0] == x.shape[0]
    inp_ndim = x.ndim
    if inp_ndim not in (3, 4):
        raise ValueError(f"Unsupportted input size {x.size()}.")
    x = _expand_to_dim4(x)
    assert cos.shape[0] <= x.shape[0] + offset
    x = (x * cos) + (_rotate_every_two(x) * sin)
    x = x.squeeze(2) if inp_ndim == 3 else x
    return x


@torch.jit.script
def _partial_rotary_interleave(
    dims: int,
    query_layer: torch.Tensor,
    key_layer: torch.Tensor,
    cos: torch.Tensor,
    sin: torch.Tensor,
    offset: int,
):
    """Jitted optimization of the partial rotate with interleave RoPE."""
    query_rot, query_pass = (query_layer[..., :dims], query_layer[..., dims:])
    key_rot, key_pass = (key_layer[..., :dims], key_layer[..., dims:])
    query_layer = _apply_rotary_pos_emb_interleave(query_rot, cos, sin, offset=offset)
    key_layer = _apply_rotary_pos_emb_interleave(key_rot, cos, sin, offset=offset)
    return query_layer, query_pass, key_layer, key_pass


def _apply_rotary_pos_emb_torch(x, cos, sin):
    # TODO: jitting fails with bf16
    return (x * cos) + (_rotate_half(x) * sin)
