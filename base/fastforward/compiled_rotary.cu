#include <c10/cuda/CUDAStream.h>
#include <cuda.h>
#include <cuda_bf16.h>
#include <cuda_fp16.h>
#include <torch/extension.h>

namespace compiled_rotary {

// PairT is a helper struct to load a pair of values in a single instruction.
// TODO: this could plausibly be pulled out into a helper library if we need it more.
template <typename T>
struct PairT {};

template <>
struct PairT<at::Half> {
    using type = __half2;
};

template <>
struct PairT<at::BFloat16> {
    using type = __nv_bfloat162;
};

template <>
struct PairT<float> {
    using type = float2;
};

template <>
struct PairT<double> {
    using type = double2;
};

// See `rotary_embed` in `base/fastforward/compiled_rotary.cpp`.
template <typename T>
__global__ void rotary_embed_kernel(const T* x, const float* freqs_cos, const float* freqs_sin,
                                    const int* pos, T* out, dim3 x_strides, dim3 result_strides,
                                    int ropedim) {
    int my_pos = pos[blockIdx.x];       // Actual position index.
    int head_offset = 2 * threadIdx.x;  // Per-thread offset into this head.

    int head_start = blockIdx.x * x_strides.x + blockIdx.y * x_strides.y + blockIdx.z * x_strides.z;

    float x_real, x_imag;
    constexpr int load_ratio = sizeof(typename PairT<T>::type) / sizeof(T);
    // Unfortuntely, kv-caching can give weird alignments, so we need to handle the case where a
    // coalesced pairwise load is invalid.
    if (reinterpret_cast<std::uintptr_t>(x) % sizeof(typename PairT<T>::type) != 0) {
        x_real = static_cast<float>(x[head_start + head_offset]);
        x_imag = static_cast<float>(x[head_start + head_offset + 1]);
    } else {
        // Load a single PairT, then pull out the two T's and cast them to float.
        typename PairT<T>::type x_pair = reinterpret_cast<const typename PairT<T>::type*>(
            x)[(head_start + head_offset) / load_ratio];
        x_real = static_cast<float>(x_pair.x);
        x_imag = static_cast<float>(x_pair.y);
    }

    float real_result = x_real;
    float imag_result = x_imag;
    // Branch on whether I'm within `ropedim` of this head.
    if (head_offset + 1 < ropedim) {
        int freqs_stride = ropedim / 2;
        float cos = freqs_cos[my_pos * freqs_stride + threadIdx.x];
        float sin = freqs_sin[my_pos * freqs_stride + threadIdx.x];

        float cos_real = cos * x_real;
        float sin_imag = sin * x_imag;
        float cos_imag = cos * x_imag;
        float sin_real = sin * x_real;
        real_result = cos_real - sin_imag;
        imag_result = cos_imag + sin_real;
    }

    int output_start = blockIdx.x * result_strides.x + blockIdx.y * result_strides.y +
                       blockIdx.z * result_strides.z;
    // Reverse of loading: fill a PairT, then store the two T's.
    // NOTE: this takes advantage of implicit type casting in assignment. If you add the explicit
    // casts, then you get some confusion about which operator= to use.
    typename PairT<T>::type result_pair;
    result_pair.x = real_result;
    result_pair.y = imag_result;
    reinterpret_cast<typename PairT<T>::type*>(out)[(output_start + head_offset) / load_ratio] =
        result_pair;
}

// Helper function to call into the rope kernel. The parallelism approach is:
//   - One thread block per token per head.
//   - Each thread reads two activations per head (real part and imaginary
//   part).
// Note that this means a lot of redundant reads of the cos/sin tables. If it
// matters, we could look at multiple heads per thread block.
torch::Tensor rotary_embed_cuda(torch::Tensor& x, torch::Tensor& freqs_cos,
                                torch::Tensor& freqs_sin, torch::Tensor& idxs) {
    auto result = torch::empty_like(x);
    TORCH_CHECK(x.dim() == 3 || x.dim() == 4);
    auto result_view = result;
    // If x has one head dimension, add a dummy dimension to make indexing easier.
    if (x.dim() == 3) {
        x = x.unsqueeze(2);  // Add the (optional) second head dimension.
        result_view = result.view_as(x);
    }
    TORCH_CHECK(x.dim() == 4);
    TORCH_CHECK(result_view.dim() == 4);
    dim3 x_strides(x.stride(0), x.stride(1), x.stride(2));
    dim3 result_strides(result_view.stride(0), result_view.stride(1), result_view.stride(2));
    TORCH_CHECK(x.stride(3) == 1);
    TORCH_CHECK(result_view.stride(3) == 1);

    int headdim = x.size(-1);
    TORCH_CHECK(headdim % 2 == 0, "headdim must be even.");
    int ropedim = freqs_cos.size(1) * 2;

    dim3 num_blocks(x.size(0), x.size(1), x.size(2));
    int num_threads = headdim / 2;
    // Instantiates kernel for fp64, fp32, fp16, and bf16.
    AT_DISPATCH_FLOATING_TYPES_AND2(
        at::ScalarType::Half, at::ScalarType::BFloat16, x.scalar_type(), "rotary_embed_kernel",
        ([&] {
            TORCH_CHECK(result.data_ptr<scalar_t>() == result_view.data_ptr<scalar_t>());
            rotary_embed_kernel<scalar_t>
                <<<num_blocks, num_threads, 0, at::cuda::getCurrentCUDAStream().stream()>>>(
                    x.data_ptr<scalar_t>(), freqs_cos.data_ptr<float>(),
                    freqs_sin.data_ptr<float>(), idxs.data_ptr<int>(), result.data_ptr<scalar_t>(),
                    x_strides, result_strides, ropedim);
        }));
    return result;
}

}  // namespace compiled_rotary
