"""Helper function for MultiCacheAttention.register_tokens_get_positions.

This function is used to register tokens and get their position indices.
It is called before the attention is called for the first time in the current round.
"""

import logging

import torch

Tensor = torch.Tensor
Device = torch.device | str

_LOGGING_USE_OF_KERNEL_DONE = False


def register_tokens_core(
    tokens: Tensor,
    cache_idxs: Tensor,
    positions: Tensor,
    tokens_cache: Tensor,
    num_caches: int,
    padding_cache_idx: int,
    padding_cache_pos: int,
    device: Device,
    device_idx: int,
    use_kernel: bool = False,
) -> Tensor:
    """Core logic for registering tokens and getting positions, without relying on self."""
    torch.cuda.nvtx.range_push("register_tokens_get_positions mcattn")
    if tokens_cache.device.index != 0:
        raise ValueError(
            f"tokens_cache.device.index={tokens_cache.device.index} != 0. "
            f"This is likely a bug."
        )
    if use_kernel:
        from base.fastforward.compiled_attention_utils import register_tokens_cpp

        global _LOGGING_USE_OF_KERNEL_DONE
        if not _LOGGING_USE_OF_KERNEL_DONE:
            logging.info("using register_tokens kernel")
            _LOGGING_USE_OF_KERNEL_DONE = True

        new_pos = register_tokens_cpp(
            tokens,
            cache_idxs,
            positions,
            tokens_cache if device_idx == 0 else None,
            padding_cache_idx,
        )
        torch.cuda.nvtx.range_pop()
        return new_pos

    num_tokens = tokens.numel()
    all_indices = torch.arange(num_caches + 1, dtype=torch.int32, device=device)

    # cnt_idxs: [num_tokens, num_caches]
    # cnt_idxs[i, j] = 1{ cache_idxs[i] == j }
    # this means all the rows i-th with cache_idxs[i] == -1 are all zeros.
    cnt_idxs = (cache_idxs[:, None] == all_indices[None, :]).to(torch.int32)

    # new_pos: [num_tokens, num_caches]
    # new_pos[i, j] = number of tokens before token i-th that have cache_idx == j
    new_pos = cnt_idxs.cumsum(0, dtype=torch.int32).sub_(1)

    # NOTE(hieu,markus): we cannot do new_pos.sum(1) here because the
    # new_pos.sub_(1) above introduces a bunch of -1 into the new_pos[i, j] where
    # cache_idxs[i] != j

    # NOTE: selects new_pos[i, cache_idxs[i]] for each row i-th in new_pos
    # new_pos: [num_tokens]
    new_pos = new_pos[
        torch.arange(num_tokens, dtype=torch.int32, device=device), cache_idxs
    ]

    new_pos.add_(positions[cache_idxs])
    new_pos.masked_fill_(cache_idxs == padding_cache_idx, padding_cache_pos)

    if device_idx == 0:
        # guarded by device_idx == 0 because the tokens cache exists only on device 0
        tokens_cache[cache_idxs, new_pos] = tokens
    positions.add_(cnt_idxs.sum(0, dtype=torch.int32))

    # Make sure the dummy memories stay "dummy"
    if device_idx == 0:
        tokens_cache[padding_cache_idx].fill_(-1)
    positions[padding_cache_idx].fill_(0)

    torch.cuda.nvtx.range_pop()
    return new_pos
