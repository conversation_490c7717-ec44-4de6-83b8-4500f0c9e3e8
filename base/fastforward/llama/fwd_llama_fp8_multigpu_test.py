"""Smoke test LLAMA models in e4m3."""

import pytest
import torch

from base.fastforward import fwd_model_test_utils, fwd_utils
from base.fastforward.all_reduce import AllReduceImpl
from base.fastforward.cached_attention import AttentionImpl, SplitHeadModes
from base.fastforward.llama import fwd_llama, fwd_llama_fp8, model_specs
from base.fastforward.parallel import ParallelConfig

_LLAMA_350M_PROMPT_TOKS = fwd_llama.LLAMA_350M_PROMPT_TOKS
_LLAMA_350M_OUTPUT_TOKS = fwd_llama.LLAMA_350M_OUTPUT_TOKS


def test_generate(
    llama_350m_fp8_multigpu_fixture,
    round_sizes: tuple[int, ...] = (8, 16),
):
    """Ensure that the model generates the correct outputs."""

    step_fn, attn_factory = llama_350m_fp8_multigpu_fixture
    step_fn = fwd_utils.PaddedStepFunction(step_fn, round_sizes=round_sizes)

    try:
        fwd_model_test_utils.check_if_model_generates_target_sequence(
            step_fn,
            attn_factory,
            _LLAMA_350M_PROMPT_TOKS,
            _LLAMA_350M_OUTPUT_TOKS,
            allowed_mismatches=2,
            extra_kv_len=max(round_sizes),  # to account for the padding
        )
    finally:
        del step_fn
        del attn_factory


def test_sequence_parallel(
    llama_350m_fp8_multigpu_sequence_parallel_fixture,
):
    step_fn, attn_factory, max_round_size = (
        llama_350m_fp8_multigpu_sequence_parallel_fixture
    )

    try:
        fwd_model_test_utils.check_if_model_generates_target_sequence(
            step_fn,
            attn_factory,
            _LLAMA_350M_PROMPT_TOKS,
            _LLAMA_350M_OUTPUT_TOKS,
            allowed_mismatches=2,
            extra_kv_len=max_round_size,  # to account for the padding
        )
    finally:
        del step_fn
        del attn_factory


def test_dynamic_sequence_parallel():
    ms = model_specs.get_llama_model_spec(
        model_name="llama-350m",
        checkpoint_path="/mnt/efs/augment/checkpoints/v2/llama/fastforward/llama-350m-fp8",
        checkpoint_sha256="3668939a42f0016ee16971b8cb17c4b5bbeb9e971ae66bb6507c5a6b990abc65",
    )
    attn_impl = AttentionImpl.MULTI_REQUEST_FLASH
    all_reduce_impl = AllReduceImpl.FASTFORWARD
    sp_config = ParallelConfig(num_processes=2, tp_size=1, sp_size=2)
    tp_config = ParallelConfig(num_processes=2, tp_size=2, sp_size=1)
    step_fn_single_gpu = fwd_llama_fp8.generate_step_fn(ms)
    padded_step_fn_single_gpu = fwd_utils.pad_and_step(
        step_fn_single_gpu, round_sizes=[16, 512]
    )
    attn_factory_single_gpu = fwd_llama.LlamaAttentionFactory(
        ms=ms,
        attention_impl=attn_impl,
        pre_attention_kernel_fusion=True,
    )
    step_fn_dynamic = fwd_llama_fp8.generate_step_fn(
        ms,
        parallel_config=sp_config,
        auto_capture_graphs=False,
        all_reduce_impl=all_reduce_impl,
        small_round_parallel_config=tp_config,
        small_round_token_cutoff=16,
    )
    padded_step_fn_dynamic = fwd_utils.pad_and_step(
        step_fn_dynamic, round_sizes=[16, 512]
    )
    attn_factory_dynamic = fwd_llama.LlamaAttentionFactory(
        ms=ms,
        parallel_config=sp_config,
        attention_impl=attn_impl,
        pre_attention_kernel_fusion=True,
    )
    input_tokens = list(_LLAMA_350M_PROMPT_TOKS) * 47
    assert len(input_tokens) > 512 and len(input_tokens) < (512 + 16)
    fwd_model_test_utils.check_logits_are_close(
        step_fn_1=padded_step_fn_single_gpu,
        step_fn_2=padded_step_fn_dynamic,
        attn_factory_1=attn_factory_single_gpu,
        attn_factory_2=attn_factory_dynamic,
        test_tokens=input_tokens,  # type: ignore
        extra_kv_len=512,
    )


def test_two_way_parallel(
    llama_350m_fp8_multigpu_two_way_parallel_fixture,
):
    step_fn, attn_factory, max_round_size = (
        llama_350m_fp8_multigpu_two_way_parallel_fixture
    )

    try:
        fwd_model_test_utils.check_if_model_generates_target_sequence(
            step_fn,
            attn_factory,
            _LLAMA_350M_PROMPT_TOKS,
            _LLAMA_350M_OUTPUT_TOKS,
            allowed_mismatches=2,
            extra_kv_len=max_round_size,  # to account for the padding
        )
    finally:
        del step_fn
        del attn_factory


@pytest.mark.parametrize("split_head_mode", SplitHeadModes)
def test_compare_split_head_mode_logits(
    llama_350m_gqa_model_spec_fixture, split_head_mode
):
    ms = llama_350m_gqa_model_spec_fixture
    round_sizes = [16, 512]
    max_seqlen = 8192
    ms.attn_split_head_mode = split_head_mode
    toks = list(range(1, 3000))

    step_1gpu = fwd_llama_fp8.generate_step_fn(
        ms=ms,
        batch_sizes=round_sizes,
        all_reduce_impl=AllReduceImpl.FASTFORWARD,
    )
    step_1gpu = fwd_utils.pad_and_step(step_1gpu, round_sizes=round_sizes)
    attn_factory_1gpu = fwd_llama.LlamaAttentionFactory(
        ms=ms,
        attention_impl=AttentionImpl.MULTI_REQUEST_FLASH,
    )
    attn_1gpu = attn_factory_1gpu(max_seqlen)
    logits_1gpu = step_1gpu(toks, attn_1gpu).checked_cast(torch.Tensor)

    parallel_config_2gpu = ParallelConfig(num_processes=2, sp_size=1, tp_size=2)
    step_2gpu = fwd_llama_fp8.generate_step_fn(
        ms=ms,
        parallel_config=parallel_config_2gpu,
        batch_sizes=round_sizes,
        all_reduce_impl=AllReduceImpl.FASTFORWARD,
    )
    step_2gpu = fwd_utils.pad_and_step(step_2gpu, round_sizes=round_sizes)
    attn_factory_2gpu = fwd_llama.LlamaAttentionFactory(
        ms=ms,
        parallel_config=parallel_config_2gpu,
        attention_impl=AttentionImpl.MULTI_REQUEST_FLASH,
    )
    attn_2gpu = attn_factory_2gpu(max_seqlen)
    logits_2gpu = step_2gpu(toks, attn_2gpu).checked_cast(torch.Tensor)

    # Compare the greedy tokens generated by the two models
    toks_1gpu = torch.argmax(logits_1gpu, dim=-1)
    toks_2gpu = torch.argmax(logits_2gpu, dim=-1)
    match_rate = (toks_1gpu == toks_2gpu).sum().item() / toks_1gpu.numel()
    # Match rate is ~86% for the input [1, 2, ..., 2999]. This seems low!
    assert match_rate > 0.85
