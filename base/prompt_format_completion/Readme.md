# Prompt Format

This module contains the prompt formatting logic for the various models we support.

Each LLM model is trained with a certain format how the various information (e.g. retrieval, prefix, suffix) are formatted into the prompt.
During inference, we need to mirror the formatting rules.

Each model is configured with a prompt formatter name (e.g. in //services/deploy/configs).
The implementation of these formatters can be found in this directory.
The supported formatters are:

- `codegen`: Formatter for Codegen based models
- `indiana`: Formatter for Indiana based models
- `starcoder`: Formatter for vanilla StarCoder models
- `rogue`: Formatter for Rogue models

Users of the module should use the `//base/prompt_format_completion` library and only create
instances via the get_prompt_formatter_by_name factory function.

## Prompt Formatter and Tokenizer Limitations

With the addition of the DeepSeek tokenizer into Rogue, we decided to verify the
concrete tokenizer types that the prompt formatter supports. This allows for
both static type checking and runtime protection when using the tokenizer. This
also means that if you want to add a new tokenizer to a prompt formatter, you
should:

- import the tokenizer class from the base/tokenizers directory,
- add the tokenizer class to the list of supported tokenizers, and
- ensure that the tokenizer's special_tokens value satisfies the requirements of the prompt formatter.

Tokenizers can not be imported from outside of base without additional changes
to make the import work. We decided this restriction was acceptable for now,
as we are not expecting to use a tokenizer outside of base in the near future.
If this need changes, we can revisit this decision.

## Special Tokens

We have defined several generic protocol for special tokens in `base/tokenizers/tokenizer.py`.
The required special tokens for "most" prompt formatters should be covered by
these protocols. So that,

- if possible, please do not define a new special token set in the prompt formatter.
- if impossible, let's discuss what we can do to make it work.
