import opentelemetry
import opentelemetry.exporter.otlp.proto.grpc.trace_exporter
import opentelemetry.sdk.trace
import opentelemetry.sdk.trace.export
import opentelemetry.trace


def setup_opentelemetry() -> opentelemetry.trace.Tracer:
    provider = opentelemetry.sdk.trace.TracerProvider()
    processor = opentelemetry.sdk.trace.export.BatchSpanProcessor(
        opentelemetry.exporter.otlp.proto.grpc.trace_exporter.OTLPSpanExporter()
    )
    provider.add_span_processor(processor)
    opentelemetry.trace.set_tracer_provider(provider)

    return opentelemetry.trace.get_tracer(__name__)
