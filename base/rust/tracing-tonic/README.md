# tracing-tonic

This library exists to make it easier to automatically propagate tracing parameters
across gRPC when using the tonic gRPC library.

As such, it holds glue code between tonic and the Rust tracing and opentelemetry crates.

The hope is to eventually get rid of such glue code at each call site. However, in this
first version, helper functions are provided to reduce code impact at each call site.

## client example

Note: we change the type of the client to use TracingService!

```
use tracing_tonic::client::TracingService;

pub struct RequestInsightClientImpl {
    client:
        // Without tracing service, the type parameter is often tonic::transport::Channel
        // insetad of TracingService.
        request_insight::request_insight_client::RequestInsightClient<TracingService>,

    ...
};

    pub async fn new(
        endpoint: &str,
        request_timeout: Duration,
    ) -> Result<RequestInsightClientImpl, tonic::transport::Error> {
        // Standard stuff for setting up a channel
        let channel = Channel::from_shared(endpoint.to_string())
            .expect("Failed to parse endpoint")
            .timeout(request_timeout)
            .connect()
            .await?

        // wrap Channel in a Tracing Service
        let channel = tower::ServiceBuilder::new()
            .layer_fn(TracingService::new)
            .service(channel);

        // More standard stuff
        let client = request_insight::request_insight_client::RequestInsightClient::new(channel);
        Ok(RequestInsightClientImpl { client })
    }
```

## server usage

`server::trace_fn` can be passed to tonic::transport::Server::trace_fn
