"""Prompt formatter to format prompts for Ethanol embedding keys and queries."""

from typing import Optional
from typing_extensions import override


from base.prompt_format.common import (
    ChatResultNodeType,
    Exchange,
    RequestMessage,
    ResponseMessage,
)
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format_retrieve.prompt_formatter import (
    ChatRetrieverPromptInput,
    DocumentRetrieverPromptInput,
    RetrieverPromptFormatter,
    PromptFormatterOutput,
)
from base.tokenizers import (
    RetrievalSpecialTokens,
    Tokenizer,
)
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderSpecialTokens


class Chatanol6QueryFormatter(RetrieverPromptFormatter[ChatRetrieverPromptInput]):
    """Query formatter for Chatanol1-16-3 models."""

    input_type = ChatRetrieverPromptInput

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
        single_turn_is_special: bool = False,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1024,
                input_fraction=0,
                prefix_fraction=0,
                max_path_tokens=0,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer
        self.single_turn_is_special = single_turn_is_special
        assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
        self.special_tokens = tokenizer.special_tokens
        self.preamble = (
            list(tokenizer.special_tokens.begin_sequence)
            if isinstance(tokenizer.special_tokens, DeepSeekCoderSpecialTokens)
            else []
        )

    def _format_user_message(
        self, message: str, add_newline: bool, is_single_turn: bool
    ):
        tokenize = self.tokenizer.tokenize_safe
        if self.single_turn_is_special and is_single_turn:
            tokens = []
        else:
            tokens = tokenize("### Instruction:\n")
        tokens += tokenize(message)
        if add_newline:
            assert not is_single_turn
            tokens += tokenize("\n")
        return tokens

    def _format_chat_message(self, message: str):
        tokenize = self.tokenizer.tokenize_safe
        tokens = tokenize("### Response:\n") + tokenize(message)
        # We always add a newline at the end of the response,
        # since we always expect to see a new instruction.
        tokens += tokenize("\n")
        return tokens

    def _format_exchange(self, exchange: Exchange):
        # To represent the request message, collect the text from all
        # text nodes.
        #
        # We ignore any tool results. (Often, chat will summarize the tool result in
        # the next response, so we may still see info about the tool result.)

        # Collect the content (text) from any text or tool use responses.
        # We ignore any of actual tool use calls (i.e., tool name + inputs)
        return self._format_user_message(
            self._format_request_message(exchange.request_message), True, False
        ) + self._format_chat_message(
            self._format_response_message(exchange.response_text),
        )

    def _format_request_message(self, request_message: RequestMessage) -> str:
        if isinstance(request_message, str):
            return request_message

        return "\n".join(
            (node.text_node.content if node.text_node is not None else "")
            + (
                node.tool_result_node.content
                if node.tool_result_node is not None
                else ""
            )
            for node in request_message
        )

    def _format_response_message(self, response_message: ResponseMessage) -> str:
        if isinstance(response_message, str):
            return response_message

        return "\n".join(
            node.content
            for node in response_message
            if node.type
            in [ChatResultNodeType.RAW_RESPONSE, ChatResultNodeType.TOOL_USE]
        )

    def format_prompt(
        self,
        prompt_input: ChatRetrieverPromptInput,
    ) -> PromptFormatterOutput:
        """Returns tokenized prompt and metadata."""
        token_budget = (
            self.apportionment_config.max_content_len
            - len(self.preamble)
            - 1  # reserve 1 token for end_of_key
        )

        # Start by adding the last user message
        message = self._format_request_message(prompt_input.message)
        is_single_turn = len(prompt_input.chat_history) == 0
        prompt_tokens = self._format_user_message(message, False, is_single_turn)[
            :token_budget
        ]
        token_budget -= len(prompt_tokens)

        # Prepend the conversation history if the budget allows it
        for exchange in reversed(prompt_input.chat_history):
            exchange_tokens = self._format_exchange(exchange)
            if len(exchange_tokens) <= token_budget:
                prompt_tokens = exchange_tokens + prompt_tokens
                token_budget -= len(exchange_tokens)
            else:
                break
        # Finally, we prepend the preamble.
        prompt_tokens = self.preamble + prompt_tokens
        prompt_tokens += [self.special_tokens.end_of_query]
        return PromptFormatterOutput([prompt_tokens])


class Chatanol6DocumentFormatter(
    RetrieverPromptFormatter[DocumentRetrieverPromptInput]
):
    """The document formatter for Chatonol6 embedding models.

    This is a fork of `Ethanol6DocumentFormatter` that supports additional types of chunks such as documentation.

    NOTE: The same prompt format is used for both code and docset chunks.
    """

    input_type = DocumentRetrieverPromptInput

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
        add_path: bool,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1000,
                input_fraction=0,
                prefix_fraction=0,
                max_path_tokens=128,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer
        self.add_path = add_path
        assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
        self.special_tokens = tokenizer.special_tokens

    @override
    def format_prompt(
        self,
        prompt_input: DocumentRetrieverPromptInput,
    ) -> PromptFormatterOutput:
        """Returns tokenized prompt."""
        if not self.apportionment_config:
            raise ValueError(
                "Apportionment configuration is required for Chatanol6 document formatter."
            )
        max_tokens = (
            self.apportionment_config.max_content_len
            - 1  # reserve 1 token for end_of_key
        )
        if max_tokens < 0:
            raise ValueError(
                "Inconsistent prompt configuration: "
                f"max_content_len={self.apportionment_config.max_content_len} "
            )

        # Format the prompt header consisting of the <|start-of-sequence|> token and
        # the path.
        header_tokens = []

        header_tokens.append(self.special_tokens.start_of_key)

        # If metadata is present, the chunk is a documentation chunk.
        metadata = prompt_input.metadata
        is_docset_chunk = metadata is not None

        if is_docset_chunk:
            # In this case, we use the name of the docset as the path
            tokenized_name = self.tokenizer.tokenize_safe(metadata.name)
            tokenized_name = tokenized_name[: self.apportionment_config.max_path_tokens]
            header_tokens.extend(tokenized_name)
        elif self.add_path:
            # Otherwise, the chunk is a code chunk and we use the path.
            tokenized_path = self.tokenizer.tokenize_safe(prompt_input.path)
            tokenized_path = tokenized_path[: self.apportionment_config.max_path_tokens]
            header_tokens.extend(tokenized_path)

        header_tokens.append(self.special_tokens.fim_middle)

        content_tokens = []

        # Add all heading titles for the chunk
        if is_docset_chunk:
            chunk_headers = [
                token
                for header in metadata.headers
                for token in self.tokenizer.tokenize_safe(header + "\n")
            ]
            content_tokens.extend(chunk_headers)

        # Format the document tokens.
        content_tokens.extend(self.tokenizer.tokenize_safe(prompt_input.text))

        # Trim the prompt to fit into max_tokens.
        if max_tokens >= len(header_tokens) + len(content_tokens):
            # No trimming is needed.
            pass
        else:
            if max_tokens > len(header_tokens):
                # The text itself doesn't fit. Trim the text from the right.
                content_tokens = content_tokens[: max_tokens - len(header_tokens)]
            else:
                # The header alone does not fit. Trim the header from the right.
                content_tokens = []
                header_tokens = header_tokens[:max_tokens]
            assert (len(header_tokens) + len(content_tokens)) == max_tokens

        prompt = header_tokens + content_tokens + [self.special_tokens.end_of_key]
        return PromptFormatterOutput([prompt])
