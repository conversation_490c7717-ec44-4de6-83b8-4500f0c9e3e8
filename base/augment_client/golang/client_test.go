package augmentclient

import (
	"fmt"
	"log"
	"net/http"
	"reflect"
	"testing"

	publicpb "github.com/augmentcode/augment/services/api_proxy/public_api"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/emptypb"
)

const ErrorMsgFmt string = `
The following error occured because the golang client (base/augment_client/golang/client.go) and the
public_api.proto gRPC REST-annotated service endpoints (services/api_proxy/public_api.proto) do not match.
Please ensure that the golang client is up to date with the gRPC REST-annotated service endpoints.

Expected gRPC method:
    Name: %s
    Input Type: %s
    Output Type: %s
Got:
    %s
Error: %s
`

// Wrap the client so we can define test methods on it
type TestClient struct {
	Client
	mock.Mock
	clientMethods map[string]reflect.Method

	// Source of truth from `apireflect.go` and `public_api.proto`
	grpcRegistry     map[string]endpointInfo
	endpointRegistry map[string]endpointInfo
}

func (tc *TestClient) CheckToolSafety(msg *publicpb.CheckToolSafetyRequest) (*publicpb.CheckToolSafetyResponse, error, *http.Response) {
	return tc.Client.CheckToolSafety(msg)
}

func (tc *TestClient) RevokeToolAccess(msg *publicpb.RevokeToolAccessRequest) (*publicpb.RevokeToolAccessResponse, error, *http.Response) {
	return tc.Client.RevokeToolAccess(msg)
}

func (tc *TestClient) GetRemoteAgentHistoryStream(msg *publicpb.GetRemoteAgentHistoryStreamRequest) (*publicpb.GetRemoteAgentHistoryStreamResponse, error, *http.Response) {
	return tc.Client.GetRemoteAgentHistoryStream(msg)
}

func (tc *TestClient) AgentWorkspaceStream(msg *publicpb.AgentWorkspaceStreamRequest) (*publicpb.AgentWorkspaceStreamResponse, error, *http.Response) {
	return tc.Client.AgentWorkspaceStream(msg)
}

func (tc *TestClient) TestToolConnection(msg *publicpb.TestToolConnectionRequest) (*publicpb.TestToolConnectionResponse, error, *http.Response) {
	return tc.Client.TestToolConnection(msg)
}

func NewTestClient() *TestClient {
	tc := &TestClient{
		clientMethods:    _clientMethods(),
		grpcRegistry:     _grpcMethodToEndpointInfo(),
		endpointRegistry: endpointRegistry,
	}
	return tc
}

// Sets up all the mock RPC calls according to the endpoint registry
// from the `apireflect.go` file. This ensures that the only allowed
// method calls are the ones that are documented in `public_api.proto`,
// as any non-expected calls will cause the mock to panic
func (tc *TestClient) SetupMockRpcs() *TestClient {
	log.Printf("Setting up mock RPCs")
	tc.Client.DoProto = tc._MockDoProto

	// Look through proto grpc registry and mock all of the calls, including the
	// correct types, for both the input and the output
	for _, info := range tc.grpcRegistry {
		log.Printf(
			"Mocking %s(%s) => %s",
			info.grpcName,
			info.reqT.Descriptor().FullName(),
			info.resT.Descriptor().FullName(),
		)
		reqType := reflect.TypeOf(info.reqT.New().Interface())
		tc.Mock.On(
			info.grpcName,
			info.endpoint,
			mock.AnythingOfType(reqType.Name()),
		).Return(
			info.resT.New().Interface(), nil, &http.Response{},
		).Once()
	}
	return tc
}

func (tc *TestClient) _MockDoProto(endpoint string, pb_msg proto.Message) (proto.Message, error, *http.Response) {
	grpcName := tc.endpointRegistry[endpoint].grpcName
	log.Printf("Mock calling %s: DoProto(\"%s\", %s)", grpcName, endpoint, pb_msg.ProtoReflect().Descriptor().FullName())

	// Type check input type
	reqTDesc := tc.endpointRegistry[endpoint].reqT.Descriptor()
	if err := checkProtoMessageType(reqTDesc, pb_msg); err != nil {
		panic(fmt.Sprintf("Invalid DoProto input type for endpoint %s: %s", endpoint, err))
	}

	args := tc.Mock.MethodCalled(grpcName, endpoint, pb_msg)
	return args.Get(0).(proto.Message), args.Error(1), args.Get(2).(*http.Response)
}

// ==========================
// Helper types and functions
// ==========================
var (
	emptyPbT = reflect.TypeOf((*emptypb.Empty)(nil)).Elem()
	pbMT     = reflect.TypeOf((*proto.Message)(nil)).Elem()
	httpReqT = reflect.TypeOf((*http.Request)(nil)).Elem()
	httpResT = reflect.TypeOf((*http.Response)(nil)).Elem()
	errT     = reflect.TypeOf((*error)(nil)).Elem()
)

func _grpcMethodToEndpointInfo() map[string]endpointInfo {
	methods := make(map[string]endpointInfo)
	for _, info := range endpointRegistry {
		methods[info.grpcName] = info
	}
	return methods
}

func _clientMethods() map[string]reflect.Method {
	methods := make(map[string]reflect.Method)
	c := reflect.TypeOf(&TestClient{})
	for i := 0; i < c.NumMethod(); i++ {
		m := c.Method(i)
		methods[m.Name] = m
	}
	return methods
}

// Helper function to construct arguments for a method from the methods
// implemented on the client
func makeArgsForMethod(tc *TestClient, m reflect.Method) []reflect.Value {
	args := make([]reflect.Value, m.Type.NumIn())
	args[0] = reflect.ValueOf(tc) // First arg of method is always receiver
	for i := 1; i < len(args); i++ {
		argT := m.Type.In(i)
		args[i] = reflect.New(argT).Elem()
	}
	return args
}

// This test checks two things:
// - All of the gRPC methods documented in `public_api.proto` are implemented in the client
// - For each documented method, have the correct types for their arguments and return values
func TestGoClientHasPublicApi(t *testing.T) {
	tc := NewTestClient().SetupMockRpcs()
	for k, info := range tc.grpcRegistry {
		// Check that the gRPC method from `public_api.proto` is implemented in the client
		if _, ok := tc.clientMethods[k]; !ok {
			t.Errorf("gRPC method %s is not implemented in the golang client", k)
			continue
		}

		// If we reach here, we have a method of the same name in both the
		// client implementation and the `public_api.proto` file. Now we need to
		// perform a type check

		m := tc.clientMethods[k]
		args := makeArgsForMethod(tc, m)

		// If this call fails, it means the client tried to call a method with arguments
		// that are not documented in `public_api.proto`, as we only mock
		// the function signatures that are documented there
		defer func() {
			if r := recover(); r != nil {
				t.Errorf(
					ErrorMsgFmt,
					k,
					info.reqT.Descriptor().FullName(),
					info.resT.Descriptor().FullName(),
					m.Func.Type().String(),
					r,
				)
			}
		}()
		m.Func.Call(args)
	}
}

func TestNoIncompleteRpcMethods(t *testing.T) {
	for k := range incompleteRpcMethods {
		t.Errorf("RPC method %s in public_api.proto is missing a complete `google.api.http` annotation.", k)
	}
}
