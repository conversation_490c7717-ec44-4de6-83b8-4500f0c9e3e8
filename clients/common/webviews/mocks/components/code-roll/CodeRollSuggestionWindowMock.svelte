<script lang="ts">
  import type { IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import { buildCodeActions } from "$common-webviews/src/common/components/code-roll/code-roll-util";
  import { setCodeRollSelection } from "$common-webviews/src/common/components/code-roll/code-roll-context";
  import Diff from "$common-webviews/src/common/components/code-roll/diff/Diff.svelte";
  import type { OnCodeAction } from "$common-webviews/src/common/components/code-roll/types";

  export let originalCode: string = "";
  export let activeSuggestion: IEditSuggestion | undefined;
  export let selectedSuggestion: IEditSuggestion | undefined;
  export let suggestions: IEditSuggestion[] = selectedSuggestion ? [selectedSuggestion] : [];
  export let nextSuggestion: IEditSuggestion | undefined;
  export let codeActions = buildCodeActions("active", "accept", "reject", "undo");
  export let language = "typescript";

  const ctx = setCodeRollSelection({
    activeSuggestion,
    selectedSuggestion: suggestions[0],
    nextSuggestion,
  });

  const onCodeAction: OnCodeAction = (action, suggestion) => {
    console.log("Code action:", action, suggestion);
  };
</script>

<div class="mock-container">
  <h3>CodeRollSuggestionWindow Mock</h3>
  <Diff
    path={$ctx.selectedSuggestion?.qualifiedPathName.relPath ?? "No Path?"}
    {originalCode}
    {language}
    {suggestions}
    {codeActions}
    {onCodeAction}
  />
</div>

<style>
  .mock-container {
    width: 100%;
    height: 300px;
    border: 1px solid var(--vscode-panel-border, #808080);
    border-radius: 4px;
    overflow: hidden;
  }

  h3 {
    margin: 0;
    padding: 8px;
    background-color: var(--vscode-panel-background);
    color: var(--vscode-foreground);
    font-size: 14px;
    border-bottom: 1px solid var(--vscode-panel-border, #808080);
  }
</style>
