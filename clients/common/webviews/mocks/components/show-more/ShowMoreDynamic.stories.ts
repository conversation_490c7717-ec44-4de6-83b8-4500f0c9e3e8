/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./ShowMoreDynamicStory.svelte";

const meta = {
  title: "components/ShowMore/Dynamic",
  component,
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component:
          "Demonstrates the ShowMore component with dynamic content that changes size after initial render.",
      },
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const DynamicContent: Story = {};

export const CustomHeight: Story = {
  args: {
    maxHeight: 50,
  } as any,
};

export const InitiallyExpanded: Story = {
  args: {
    expanded: true,
  } as any,
};
