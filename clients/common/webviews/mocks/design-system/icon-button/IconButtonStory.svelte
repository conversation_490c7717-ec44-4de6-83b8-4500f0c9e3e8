<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import type {
    ButtonColor,
    ButtonVariant,
    ButtonRadius,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import ArrowR<PERSON> from "$common-webviews/src/design-system/icons/arrow-right.svelte";
  import InfoCircled from "$common-webviews/src/design-system/icons/info-circled.svelte";
  import DotsHorizontal from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";

  const sizeVariants = [0, 0.5, 1, 2, 3, 4] as const;
  const variantVariants: ButtonVariant[] = [
    "classic",
    "solid",
    "soft",
    "surface",
    "outline",
    "ghost-block",
    "ghost",
  ];
  const colorVariants: ButtonColor[] = ["accent", "neutral", "error"];
  const highContrastVariants: boolean[] = [false, true];
  const radiusVariants: ButtonRadius[] = ["medium", "full"];

  const onClick = () => {
    console.log("Button clicked");
  };
</script>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <th></th>
        <th>Icon</th>
        <th>Icon</th>
        <th>Icon</th>
      </thead>
      <tbody>
        {#each variantVariants as variant}
          <tr>
            <td>{variant}</td>
            <td>
              <IconButtonAugment {variant} on:click={onClick}>
                <InfoCircled />
              </IconButtonAugment>
            </td>
            <td>
              <IconButtonAugment {variant} on:click={onClick}>
                <ArrowRight />
              </IconButtonAugment>
            </td>
            <td>
              <IconButtonAugment {variant} on:click={onClick}>
                <DotsHorizontal />
              </IconButtonAugment>
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <th></th>
        {#each colorVariants as color}
          <th>{color}</th>
        {/each}
      </thead>
      <tbody>
        {#each variantVariants as variant}
          <tr>
            <td>{variant}</td>
            {#each colorVariants as color}
              <td>
                <IconButtonAugment {color} {variant} on:click={onClick}>
                  <InfoCircled />
                </IconButtonAugment>
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <th></th>
        <th>Enabled</th>
        <th>Disabled</th>
        <th>Loading</th>
      </thead>
      <tbody>
        {#each variantVariants as variant}
          <tr>
            <td>{variant}</td>
            <td>
              <IconButtonAugment {variant} on:click={onClick}>
                <InfoCircled />
              </IconButtonAugment>
            </td>
            <td>
              <IconButtonAugment disabled {variant} on:click={onClick}>
                <InfoCircled />
              </IconButtonAugment>
            </td>
            <td>
              <IconButtonAugment loading {variant} on:click={onClick}>
                <InfoCircled />
              </IconButtonAugment>
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <th></th>
        {#each sizeVariants as size}
          <th>{size}</th>
        {/each}
      </thead>
      <tbody>
        {#each variantVariants as variant}
          <tr>
            <td>{variant}</td>
            {#each sizeVariants as size}
              <td>
                <IconButtonAugment {size} {variant} on:click={onClick}>
                  <InfoCircled />
                </IconButtonAugment>
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <th></th>
        <th>Normal</th>
        <th>High Contrast</th>
      </thead>
      <tbody>
        {#each variantVariants as variant}
          <tr>
            <td>{variant}</td>
            {#each highContrastVariants as highContrast}
              <td>
                <IconButtonAugment {highContrast} {variant} on:click={onClick}>
                  <InfoCircled />
                </IconButtonAugment>
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <th></th>
        {#each sizeVariants as size}
          <th>{size}</th>
        {/each}
      </thead>
      <tbody>
        {#each radiusVariants as radius}
          <tr>
            <td>{radius}</td>
            {#each sizeVariants as size}
              <td>
                <IconButtonAugment {size} {radius} on:click={onClick}>
                  <InfoCircled />
                </IconButtonAugment>
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <h1>Additional Properties</h1>
  <div>
    <IconButtonAugment title="Example Button with title" on:click={onClick}>
      <InfoCircled />
    </IconButtonAugment>
  </div>
</ColumnLayout>
