<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Link from "$common-webviews/src/apps/chat/components/markdown-ext/Link.svelte";
  import { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { SpecialContextInputModel } from "$common-webviews/src/apps/chat/models/context-model";
  import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
  import type { Tokens } from "marked";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { setChatModel } from "$common-webviews/src/apps/chat/chat-context";

  const chatModel = new ChatModel(
    new AsyncMsgSender(host.postMessage),
    host,
    new SpecialContextInputModel(),
  );
  setChatModel(chatModel);

  const urlToken: Tokens.Link = {
    type: "link",
    raw: "",
    href: "https://augmentcode.com",
    text: "augmentcode.com",
    title: "Link to augmentcode home page",
    tokens: [],
  };

  const augmentToken: Tokens.Link = {
    type: "link",
    raw: "",
    href: "augment://test",
    text: "example text",
    title: "example title",
    tokens: [],
  };

  const unknownFileToken: Tokens.Link = {
    type: "link",
    raw: "",
    href: "./example/unknown-file.ts",
    text: "unknown-file.ts",
    title: "unknown typescript file",
    tokens: [],
  };

  const knownFileToken: Tokens.Link = {
    type: "link",
    raw: "",
    href: "./example/known-file.ts",
    text: "known-file.ts",
    title: "known typescript file",
    tokens: [],
  };
</script>

<ColumnLayout>
  <Link token={urlToken} />
  <Link token={augmentToken} />
  <Link token={unknownFileToken} />
  <Link token={knownFileToken} />
</ColumnLayout>
