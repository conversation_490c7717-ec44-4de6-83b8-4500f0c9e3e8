<script lang="ts">
  import { MonacoContext } from "$common-webviews/src/design-system/components/MonacoProvider";
  import { onDestroy } from "svelte";
  import type { MonacoType } from "$common-webviews/src/design-system/components/MonacoProvider";

  export let code: string = "// Example code\nconsole.log('Hello, world!');";
  export let language: string = "javascript";

  let editorContainer: HTMLElement;
  export let editorInstance: any = undefined;

  function setupEditor() {
    // Get the Monaco context
    const monacoContext = MonacoContext.getContext();

    // Get the Monaco instance store
    const monaco = monacoContext.monaco;

    let currentEditor: any = null;

    // Subscribe to the Monaco store
    const unsubscribe = monaco.subscribe((monacoInstance: MonacoType | null) => {
      if (monacoInstance && editorContainer && !currentEditor) {
        // Create the editor
        currentEditor = monacoInstance.editor.create(editorContainer, {
          value: code,
          language: language,
          theme: "vs-dark",
          automaticLayout: true,
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
        });

        // Store the editor instance for external access
        editorInstance = currentEditor;
      }
    });

    // Clean up on component destroy
    onDestroy(() => {
      unsubscribe();
      if (currentEditor) {
        currentEditor.dispose();
      }
    });
  }
</script>

<div class="c-monaco-example">
  {setupEditor()}
  <div class="c-monaco-example__container" bind:this={editorContainer}></div>
</div>

<style>
  .c-monaco-example {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .c-monaco-example__container {
    flex: 1;
    min-height: 100px;
    border: 1px solid var(--ds-color-neutral-a6);
    border-radius: var(--ds-radius-2);
    overflow: hidden;
  }
</style>
