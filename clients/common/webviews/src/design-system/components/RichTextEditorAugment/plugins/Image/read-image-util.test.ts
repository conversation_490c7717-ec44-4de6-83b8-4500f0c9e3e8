import { readImageAsBlob } from "./read-image-util";
import { expect, describe, it, beforeEach, afterEach, vi } from "vitest";

describe("readImageAsBlob", () => {
  let canvasMock: HTMLCanvasElement;
  let contextMock: CanvasRenderingContext2D;

  beforeEach(() => {
    // Mock canvas and context
    contextMock = {
      drawImage: vi.fn(),
    } as unknown as CanvasRenderingContext2D;

    URL.createObjectURL = vi.fn().mockReturnValue("mock-object-url");
    canvasMock = {
      getContext: vi.fn().mockReturnValue(contextMock),
      toBlob: vi
        .fn()
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        .mockImplementation((callback) => callback(new Blob(["mock-blob"]))),
      width: 0,
      height: 0,
    } as unknown as HTMLCanvasElement;

    // Mock document.createElement
    vi.spyOn(document, "createElement").mockImplementation((tagName: string) => {
      if (tagName === "canvas") {
        return canvasMock;
      }
      return document.createElement(tagName);
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should scale down a large image while preserving aspect ratio", async () => {
    // Create a mock File
    const mockFile = new File([""], "test.png", { type: "image/png" });

    // Mock Image loading
    const mockImage = {
      width: 2048,
      height: 1536,
      onload: null as (() => void) | null,
      onerror: null as ((err: Error) => void) | null,
      src: "",
    };

    // Mock Image constructor
    global.Image = vi.fn().mockImplementation(() => mockImage) as unknown as typeof Image;

    // Start processing the image
    const promise = readImageAsBlob(mockFile);

    // Simulate successful image load
    mockImage.onload!();

    const result = await promise;

    // Verify canvas dimensions were set correctly
    expect(canvasMock.width).toBe(1024);
    expect(canvasMock.height).toBe(768);

    // Verify drawImage was called with correct parameters
    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(contextMock.drawImage).toHaveBeenCalledWith(mockImage, 0, 0, 1024, 768);

    // Verify blob was created
    expect(result).toBeInstanceOf(Blob);
  });

  it("should handle portrait images correctly", async () => {
    const mockFile = new File([""], "test.png", { type: "image/png" });

    const mockImage = {
      width: 1536,
      height: 2048,
      onload: null as (() => void) | null,
      onerror: null as ((err: Error) => void) | null,
      src: "",
    };

    global.Image = vi.fn().mockImplementation(() => mockImage) as unknown as typeof Image;

    const promise = readImageAsBlob(mockFile, 1024);
    mockImage.onload!();

    await promise;

    expect(canvasMock.width).toBe(768);
    expect(canvasMock.height).toBe(1024);
  });

  it("should handle canvas context error", async () => {
    const mockFile = new File([""], "test.png", { type: "image/png" });

    // Mock canvas to return null context
    canvasMock.getContext = vi.fn().mockReturnValue(null);

    const mockImage = {
      width: 1024,
      height: 768,
      onload: null as (() => void) | null,
      onerror: null as ((err: Error) => void) | null,
      src: "",
    };

    global.Image = vi.fn().mockImplementation(() => mockImage) as unknown as typeof Image;

    const promise = readImageAsBlob(mockFile);
    mockImage.onload!();

    await expect(promise).rejects.toThrow("Could not get canvas context");
  });

  it("should handle image load error", async () => {
    const mockFile = new File([""], "test.png", { type: "image/png" });

    const mockImage = {
      width: 1024,
      height: 768,
      onload: null as (() => void) | null,
      onerror: null as ((err: Error) => void) | null,
      src: "",
    };

    global.Image = vi.fn().mockImplementation(() => mockImage) as unknown as typeof Image;

    const promise = readImageAsBlob(mockFile);
    mockImage.onerror!(new Error("test error"));

    await expect(promise).rejects.toThrow("Failed to load image");
  });

  it("should handle blob creation error", async () => {
    const mockFile = new File([""], "test.png", { type: "image/png" });

    // Mock canvas to return null blob
    canvasMock.toBlob = vi
      .fn()
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      .mockImplementation((callback) => callback(null));

    const mockImage = {
      width: 1024,
      height: 768,
      onload: null as (() => void) | null,
      onerror: null as ((err: Error) => void) | null,
      src: "",
    };

    global.Image = vi.fn().mockImplementation(() => mockImage) as unknown as typeof Image;

    const promise = readImageAsBlob(mockFile);
    mockImage.onload!();

    await expect(promise).rejects.toThrow("Failed to create blob");
  });
});
