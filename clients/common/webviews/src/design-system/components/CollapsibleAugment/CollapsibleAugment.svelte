<script lang="ts">
  import { writable, derived } from "svelte/store";
  import { setCollapsibleContext } from "./context";
  import { detectStickyHeader } from "$common-webviews/src/apps/chat/components/actions/detectStickyHeader";

  export let collapsed: boolean = false;
  export let stickyHeader: boolean = false;
  export let expandable: boolean = true;
  export let isHeaderStuck = false;
  /**
   * The number of pixels from the top that the sticky header should be offset by
   * Defaults to -0.5 to be a little bit above the page
   */
  export let stickyHeaderTop = -0.5;

  const collapsedStore = writable(collapsed);
  const readableCollapsed = derived(collapsedStore, ($state) => $state);

  const expandableStore = writable(expandable);

  // Reset the last known child height when we expand
  $: expandableStore.set(expandable);
  $: collapsedStore.set(collapsed);
  $: collapsed = $collapsedStore;
  $: expandable = $expandableStore;
  $: {
    //If a component is not expandable, it should always be collapsed
    if (!expandable) {
      collapsedStore.set(true);
    }
  }
  let shouldRenderChild = false;
  let hideChildTimeout: ReturnType<typeof setTimeout> | undefined = undefined;
  function hideChild() {
    clearTimeout(hideChildTimeout);
    hideChildTimeout = setTimeout(() => {
      shouldRenderChild = false;
    }, 200);
  }
  function showChild() {
    clearTimeout(hideChildTimeout);
    shouldRenderChild = true;
  }
  $: collapsed ? hideChild() : showChild();

  function setCollapsed(value: boolean) {
    if (!expandable) {
      collapsedStore.set(true);
      return;
    }
    collapsedStore.set(value);
  }
  export const toggle = function toggle() {
    setCollapsed(!$collapsedStore);
  };

  setCollapsibleContext({
    collapsed: readableCollapsed,
    setCollapsed,
    toggle,
    expandable: expandableStore,
  });
  $: ({ class: className } = $$restProps);
</script>

<div
  class="c-collapsible {className}"
  class:is-collapsed={$collapsedStore}
  class:is-expandable={$expandableStore}
  style:--sticky-header-top={`${stickyHeaderTop}px`}
>
  <header
    class="c-collapsible__header"
    class:is-sticky={stickyHeader}
    use:detectStickyHeader={{
      offset: -stickyHeaderTop,
      onStuck: () => {
        isHeaderStuck = true;
      },
      onUnstuck: () => {
        isHeaderStuck = false;
      },
    }}
  >
    <div
      class="c-collapsible__header-inner"
      class:is-collapsed={$collapsedStore}
      class:is-header-stuck={isHeaderStuck}
      class:has-header-padding={stickyHeaderTop > 0}
    >
      <slot name="header" />
    </div>
  </header>
  <div class="c-collapsible__content" class:is-collapsed={$collapsedStore}>
    <div class="c-collapsible__content-inner">
      {#if $expandableStore && shouldRenderChild}
        <div class="c-collapsible__body">
          <slot />
        </div>
        {#if $$slots.footer}
          <footer class="c-collapsible__footer">
            <slot name="footer" />
          </footer>
        {/if}
      {/if}
    </div>
  </div>
</div>

<style>
  .c-collapsible {
    --collapsible-panel-background: var(--ds-color-neutral-a2);
    --collapsible-panel-border-color: var(--augment-border-color);
    --collapsible-panel-border: 1px solid var(--collapsible-panel-border-color);
    --collapsible-panel-radius: var(--ds-radius-2);
    display: flex;
    flex-direction: column;
  }

  .c-collapsible__header {
    display: flex;
    align-items: center;
  }
  .c-collapsible__header-inner {
    border-radius: var(--collapsible-panel-radius) var(--collapsible-panel-radius) 0 0;
    background: var(--collapsible-panel-background);
    border: var(--collapsible-panel-border);
    width: 100%;
    position: relative;
  }
  .c-collapsible__header-inner:not(.is-collapsed)::before {
    content: "";
    position: absolute;
    top: calc(-1 * var(--sticky-header-top));
    bottom: 0;
    left: calc(-1 * var(--ds-spacing-1));
    width: calc(100% + var(--ds-spacing-1) * 2);
    background: var(--augment-window-background);
    pointer-events: none;
    border: 0;
    z-index: -1; /* Ensure the overlay is behind the header */
  }
  .c-collapsible__header-inner.is-collapsed {
    border-radius: var(--collapsible-panel-radius);
  }

  .c-collapsible__header.is-sticky {
    position: sticky;
    top: var(--sticky-header-top);
    z-index: var(--z-block-container-sticky-header);
  }

  .c-collapsible__content {
    background: var(--collapsible-panel-background);
    display: grid;
    grid-template-rows: 1fr;
    transition: grid-template-rows 0.2s ease-in-out;
    overflow: clip;
    border-bottom-left-radius: var(--collapsible-panel-radius);
    border-bottom-right-radius: var(--collapsible-panel-radius);
  }
  .c-collapsible__content:not(.is-collapsed) {
    border: var(--collapsible-panel-border);
    border-top: 0; /* no duplicate borders with header */
  }

  .c-collapsible__content-inner {
    overflow: hidden;
    min-height: 0;
    display: flex;
    flex-direction: column;
  }

  .c-collapsible__content-inner-height-wrapper {
    min-height: var(--last-known-child-height, 0);
  }

  .c-collapsible__body {
    flex: 1;
  }

  .c-collapsible__footer {
    border-top: var(--collapsible-panel-border);
  }

  .c-collapsible.is-collapsed .c-collapsible__content {
    grid-template-rows: 0fr;
  }

  .c-collapsible.is-collapsed .c-collapsible__header {
    border-radius: var(--collapsible-panel-radius);
    border-bottom: none;
  }

  /* Only remove the footer border when fully collapsed */
  .c-collapsible.is-collapsed .c-collapsible__footer {
    border-top: none;
  }
  .c-collapsible:not(.is-expandable) {
    cursor: default;
  }
</style>
