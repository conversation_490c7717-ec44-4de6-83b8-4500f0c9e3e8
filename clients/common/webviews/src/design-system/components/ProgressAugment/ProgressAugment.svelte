<script lang="ts">
  import { dsColorAttribute } from "$common-webviews/src/design-system/_libs/component-utils";
  import {
    defaultGetValueLabel,
    getProgressState,
    isValidMaxNumber,
    isValidValueNumber,
    getInvalidMaxError,
    getInvalidValueError,
    PROGRESS_NAME,
    DEFAULT_MAX,
  } from "./progress-util";
  import type { ProgressColor, ProgressVariant, ProgressSize } from "./progress-util";

  export let value: number | undefined = undefined;
  export let max: number = DEFAULT_MAX;
  export let color: ProgressColor = "accent";
  export let variant: ProgressVariant = "soft";
  export let size: ProgressSize = 2;
  export let getValueLabel: (value: number, max: number) => string = defaultGetValueLabel;

  $: {
    if (!isValidMaxNumber(max)) {
      console.error(getInvalidMaxError(String(max), PROGRESS_NAME));
    }
    if (!isValidValueNumber(value, max)) {
      console.error(getInvalidValueError(String(value), PROGRESS_NAME));
    }
  }
</script>

<div
  {...dsColorAttribute(color)}
  style="--progress-width-internal:{defaultGetValueLabel(value ?? 0, max)}"
  aria-valuemax={max}
  aria-valuenow={Math.min(value ?? 0, max)}
  aria-valuetext={getValueLabel(value ?? 0, max)}
  role="progressbar"
  data-state="loading"
  data-value={value}
  data-max={max}
  class="c-progress-augment c-progress-augment__size-{size} c-progress-augment__color-{color} c-progress-augment__variant-{variant} "
>
  <div
    data-state={getProgressState(value, max)}
    data-value={value}
    data-max={max}
    class="c-progress-augment__indicator"
  ></div>
</div>

<style>
  .c-progress-augment {
    /** Allow for consumers to override the width */
    --progress-width: var(--progress-width-internal);
    --progress-background: var(--ds-color-a5);
    --progress-color: var(--ds-color-a10);
    position: relative;
    overflow: hidden;
    background: var(--progress-background);
    border-radius: 99999px;
    width: 100%;

    /* Fix overflow clipping in Safari */
    /* https://gist.github.com/domske/b66047671c780a238b51c51ffde8d3a0 */
    transform: translateZ(0);
  }

  .c-progress-augment__indicator {
    background-color: var(--progress-color);
    border-radius: inherit;
    width: var(--progress-width);
    height: 100%;
    transition: width 660ms cubic-bezier(0.65, 0, 0.35, 1);
  }
  .c-progress-augment__size-1 {
    height: 5px;
  }
  .c-progress-augment__size-2 {
    height: 10px;
  }
  .c-progress-augment__size-3 {
    height: 15px;
  }
  .c-progress-augment__color-accent {
    --progress-color: var(--ds-color-a7);
  }
  .c-progress-augment__color-neutral {
    --progress-color: var(--ds-color-a8);
  }
  .c-progress-augment__color-error {
    --progress-color: var(--ds-color-a9);
  }
  .c-progress-augment__color-success {
    --progress-color: var(--ds-color-a10);
  }
  .c-progress-augment__variant-soft {
    --progress-background: var(--ds-color-a4);
  }
  .c-progress-augment__variant-surface {
    --progress-background: var(--ds-color-4);
  }
  .c-progress-augment__variant-outline {
    --progress-background: transparent;
    --progress-color: var(--ds-color-a10);
    border: 1px solid var(--progress-color);
  }
</style>
