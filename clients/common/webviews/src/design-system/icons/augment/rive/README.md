# Using Rive Animations in Augment

This guide explains how to use Rive animations in the Augment codebase.

## What is Rive?

[Rive](https://rive.app/) is a real-time interactive design and animation tool that allows you to create and ship interactive animations for any platform. In Augment, we use Rive for creating engaging UI animations like the memories icon animation.

## Setup and Dependencies

Rive is integrated into our codebase via the `@rive-app/canvas` package. This dependency is already included in the `package.json` of the `clients/common/webviews` project.

```json
"dependencies": {
  "@rive-app/canvas": "^2.26.4",
  // other dependencies...
}
```

## File Structure

Rive animation files (`.riv`) are stored in the following directory:

```
clients/common/webviews/src/design-system/icons/augment/rive/
```

We typically maintain both light and dark theme versions of animations when needed (e.g., `memories-light.riv` and `memories-dark.riv`).

## Basic Usage

Here's a basic example of how to use a Rive animation in a Svelte component:

```svelte
<script lang="ts">
  import * as rive from "@rive-app/canvas";
  import lightAnimationSrc from "$common-webviews/src/design-system/icons/augment/rive/animation-light.riv?url";
  import darkAnimationSrc from "$common-webviews/src/design-system/icons/augment/rive/animation-dark.riv?url";
  import { onDestroy, onMount } from "svelte";

  // Animation source based on theme
  let animationSrc = lightAnimationSrc;

  // Canvas and Rive instance references
  let canvas: HTMLCanvasElement | undefined = undefined;
  let r: rive.Rive | undefined = undefined;

  // Initialize Rive animation
  function initRive() {
    // Clean up existing instance if any
    if (r) {
      r.cleanup();
      r = undefined;
    }

    // Create new Rive instance
    if (canvas && !r) {
      r = new rive.Rive({
        src: animationSrc,
        canvas,
        autoplay: false,
        onLoad: () => {
          r?.resizeDrawingSurfaceToCanvas();
          // Initial state setup if needed
          r?.play();
          r?.stop();
        },
      });
    }
  }

  // Initialize when canvas and source are available
  $: canvas && !r && animationSrc && initRive();

  // Animation control functions
  function startAnimation() {
    r?.reset();
    r?.play();
  }

  function stopAnimation() {
    r?.reset();
  }

  // Clean up on component destruction
  onDestroy(() => {
    r?.cleanup();
  });
</script>

<canvas bind:this={canvas} width="32" height="32" style="width: 14px; height: 14px;"></canvas>
```

## Animation Controls

Rive provides several methods to control animations:

- `play()`: Start the animation
- `pause()`: Pause the animation
- `stop()`: Stop the animation
- `reset()`: Reset the animation to its initial state
- `resizeDrawingSurfaceToCanvas()`: Resize the animation to fit the canvas

## Theme Integration

To support both light and dark themes, you can use the theme observer from our theme store:

```svelte
<script>
  import { attachAugmentThemeObserver } from "$common-webviews/src/common/hosts/user-themes/theme-store";
  import { UserThemeCategory } from "$common-webviews/src/common/hosts/user-themes/augment-theme-attributes";

  onMount(() => {
    attachAugmentThemeObserver((category: UserThemeCategory | undefined) => {
      animationSrc = category === UserThemeCategory.dark ? darkAnimationSrc : lightAnimationSrc;
    });
  });
</script>
```

## CSP Configuration (for VSCode)

When using Rive in VSCode webviews, you need to configure Content Security Policy (CSP) to allow loading Rive animations. This is already handled in our codebase through the `addRiveCdnRequirements` function in `clients/vscode/src/utils/webviews/csp.ts`:

```typescript
export function addRiveCdnRequirements(): CSPOptionOperator {
    return (opts: CSPOptions) => {
        // Add both jsdelivr and unpkg CDNs
        opts.connects.add("https://cdn.jsdelivr.net");
        opts.connects.add("https://unpkg.com");
        opts.scripts.add("https://cdn.jsdelivr.net");
        opts.scripts.add("https://unpkg.com");
        opts.scripts.add("'unsafe-eval'"); // Required for WebAssembly compilation
        opts.workers.add("blob:");
        // Allow loading local Rive files through vscode-resource scheme
        opts.connects.add("vscode-resource:");
        opts.connects.add("https://*.vscode-cdn.net");
    };
}
```

## Best Practices

1. **Memory Management**: Always clean up Rive instances in the `onDestroy` lifecycle hook to prevent memory leaks.

2. **Canvas Sizing**: Set explicit width and height attributes on the canvas element, and use CSS to control the displayed size.

3. **Animation Reset**: When starting an animation, call `reset()` first to ensure it starts from the beginning.

4. **Theme Support**: Create separate animation files for light and dark themes when necessary.

5. **Performance**: Be mindful of performance, especially when using multiple animations on a single page.

## Real-World Example

For a complete implementation example, see the `MemoriesChip.svelte` component:

```
clients/common/webviews/src/apps/chat/components/context/MemoriesChip.svelte
```

This component demonstrates how to:
- Initialize a Rive animation
- Handle theme changes
- Control animation playback
- Clean up resources properly

## Creating New Animations

1. Create your animation in the [Rive editor](https://rive.app/)
2. Export it as a `.riv` file
3. Add it to the `clients/common/webviews/src/design-system/icons/augment/rive/` directory
4. Import and use it in your component as shown in the examples above

## Troubleshooting

- If animations don't appear, check that the canvas element is properly sized and visible
- If animations don't play, ensure the Rive instance is properly initialized and the play method is called
- For CSP-related issues in VSCode, make sure the CSP configuration includes the necessary directives
