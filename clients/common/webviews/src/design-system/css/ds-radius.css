[data-ds-radius="none"] {
  --ds-internal-vars-radius-factor: 0;
  --ds-internal-vars-radius-full: 0px;
}

[data-ds-radius="small"] {
  --ds-internal-vars-radius-factor: 0.75;
  --ds-internal-vars-radius-full: 0px;
}

[data-ds-radius="medium"] {
  --ds-internal-vars-radius-factor: 1;
  --ds-internal-vars-radius-full: 0px;
}

[data-ds-radius="large"] {
  --ds-internal-vars-radius-factor: 1.5;
  --ds-internal-vars-radius-full: 0px;
}

[data-ds-radius="full"] {
  --ds-internal-vars-radius-factor: 1.5;
  --ds-internal-vars-radius-full: 9999px;
}

:root {
  --ds-radius-1: calc(3px * var(--ds-internal-vars-radius-factor, 1));
  --ds-radius-2: calc(4px * var(--ds-internal-vars-radius-factor, 1));
  --ds-radius-3: calc(6px * var(--ds-internal-vars-radius-factor, 1));
  --ds-radius-4: calc(8px * var(--ds-internal-vars-radius-factor, 1));
  --ds-radius-5: calc(12px * var(--ds-internal-vars-radius-factor, 1));
  --ds-radius-6: calc(16px * var(--ds-internal-vars-radius-factor, 1));
}
