import {
  type AutofixIteration,
  AutofixIterationStage,
  AutofixMessages,
} from "$vscode/src/autofix/autofix-state";

export function getIterationMessage(
  iteration: AutofixIteration | undefined,
  stage: AutofixIterationStage,
  stageCount: number | undefined,
  isCurrentStage: boolean | undefined,
): AutofixMessages | null {
  if (!iteration) return null;

  if (stage === AutofixIterationStage.runTest) {
    if (iteration.commandFailed === undefined && isCurrentStage) {
      return iteration.isFirstIteration ? AutofixMessages.testRunning : AutofixMessages.retesting;
    }
    if (iteration.commandFailed === true) {
      return AutofixMessages.testFailed;
    }
    return AutofixMessages.testPassed;
  }

  if (stage === AutofixIterationStage.applyFix) {
    const isLatestStage = stageCount === (iteration.suggestedSolutions?.length || 0);

    if (isLatestStage) {
      if (iteration.selectedSolutions) {
        return AutofixMessages.selectedSolutions;
      }

      return AutofixMessages.generatingSolutions;
    }

    return AutofixMessages.suggestedSolutions;
  }

  return null;
}
