import { host } from "$common-webviews/src/common/hosts/__mocks__/host";
import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import { get } from "svelte/store";
import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { ChatModeModel } from "./chat-mode-model";
import { ChatModel } from "./chat-model";
import { AgentConversationModel } from "./agent-conversation-model";
import { RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
import { SpecialContextInputModel } from "./context-model";
import { AgentExecutionMode } from "./chat-model";
import { tick } from "svelte";
import { ToolsWebviewModel } from "./tools-webview-model";
import { ChatMode } from "@augment-internal/sidecar-libs/src/chat/chat-types";

describe("ChatModeModel", () => {
  let chatModel: ChatModel;
  let agentConversationModel: AgentConversationModel;
  let remoteAgentsModel: RemoteAgentsModel;
  let chatModeModel: ChatModeModel;
  let messageBroker: MessageBroker;
  let contextModel: SpecialContextInputModel;
  let mockCheckpointStore: any;
  let mockToolsModel: any;

  beforeEach(() => {
    // Setup host mock with proper async message handling
    host.postMessage.mockImplementation((msg) => {
      // Handle async messages immediately
      if (msg.type === WebViewMessageType.asyncWrapper) {
        const baseMsg = msg.baseMsg;
        if (!baseMsg) return;

        // Mock responses for different message types - use immediate response
        const response: any = {
          type: WebViewMessageType.asyncWrapper,
          requestId: msg.requestId,
          error: null,
          baseMsg: null,
          destination: "webview",
        };

        switch (baseMsg.type) {
          case "get-remote-agent-notification-enabled-request":
            response.baseMsg = {
              type: "get-remote-agent-notification-enabled-response",
              data: { enabledAgentIds: [] },
            };
            break;
          case "get-remote-agent-pinned-status-request":
            response.baseMsg = {
              type: "get-remote-agent-pinned-status-response",
              data: { pinnedAgents: {} },
            };
            break;
          case "chat-mode-changed":
            response.baseMsg = {
              type: "empty",
              data: {},
            };
            break;
          case "agent-set-current-conversation":
            response.baseMsg = {
              type: "empty",
              data: {},
            };
            break;
          case "get-workspace-info-request":
            response.baseMsg = {
              type: "get-workspace-info-response",
              data: { workspacePath: "/test/workspace" },
            };
            break;
          case "check-agent-auto-mode-approval":
            response.baseMsg = {
              type: "check-agent-auto-mode-approval-response",
              data: { approved: false },
            };
            break;
          case "check-has-ever-used-agent":
            response.baseMsg = {
              type: "check-has-ever-used-agent-response",
              data: { hasUsed: false },
            };
            break;
          case "get-remote-agent-overviews-request":
            response.baseMsg = {
              type: "get-remote-agent-overviews-response",
              data: { agents: [] },
            };
            break;
          case "report-agent-session-event":
            response.baseMsg = {
              type: "empty",
              data: {},
            };
            break;
          case "get-remote-agent-status":
            response.baseMsg = {
              type: "remote-agent-status-response",
              data: {
                isRemoteAgentSshWindow: false,
                remoteAgentId: undefined,
              },
            };
            break;
          case "cancel-remote-agent-history-stream-request":
            response.baseMsg = {
              type: "empty",
              data: {},
            };
            break;
          case "cancel-remote-agents-stream-request":
            response.baseMsg = {
              type: "empty",
              data: {},
            };
            break;
          default:
            // For unhandled messages, just return
            return;
        }

        // Simulate the response being sent back immediately
        // Use queueMicrotask to ensure it's async but immediate
        queueMicrotask(() => {
          window.postMessage(response, "*");
        });
      }
    });

    // Initialize dependencies
    messageBroker = new MessageBroker(host);
    contextModel = new SpecialContextInputModel();

    chatModel = new ChatModel(messageBroker, host, contextModel, {
      initialFlags: {
        doUseNewDraftFunctionality: true,
        enableBackgroundAgents: true,
        enableAgentAutoMode: true,
      },
    });

    remoteAgentsModel = new RemoteAgentsModel({
      msgBroker: messageBroker,
      isActive: false,
      flagsModel: chatModel.flags,
      host,
    });

    // Create mock checkpoint store
    mockCheckpointStore = {
      registerAgentConversationModel: vi.fn(),
      targetCheckpointSummary: {
        subscribe: vi.fn(() => {
          return { unsubscribe: vi.fn() };
        }),
      },
      targetCheckpointHasChanges: {
        subscribe: vi.fn(() => {
          return { unsubscribe: vi.fn() };
        }),
      },
      currentCheckpoint: {
        subscribe: vi.fn(() => {
          return { unsubscribe: vi.fn() };
        }),
      },
      createNewCheckpoint: vi.fn(),
      maybeCreateOriginalCheckpoint: vi.fn(),
    };

    // Create mock tools model
    mockToolsModel = new ToolsWebviewModel(
      chatModel.currentConversationModel,
      chatModel.extensionClient,
      chatModel,
      undefined, // remoteAgentsModel not needed for these tests
    );

    agentConversationModel = new AgentConversationModel(
      chatModel,
      mockToolsModel,
      mockCheckpointStore,
    );

    chatModeModel = new ChatModeModel(chatModel, agentConversationModel, remoteAgentsModel);
  });

  afterEach(() => {
    // Clean up models to prevent timeout errors during test teardown
    if (remoteAgentsModel) {
      remoteAgentsModel.dispose();
    }
    if (chatModel) {
      // Clear any pending timeouts in the chat model
      vi.clearAllTimers();
    }
  });

  describe("Constructor", () => {
    test("should initialize with remote agent mode when isRemoteAgentSshWindow flag is true", async () => {
      // Set the remote agent window flag
      // there is no setter for this, so we have to mock the getter
      vi.spyOn(remoteAgentsModel, "isRemoteAgentSshWindow", "get").mockReturnValue(true);
      vi.spyOn(remoteAgentsModel, "remoteAgentSshWindowId", "get").mockReturnValue(
        "test-agent-123",
      );

      const remoteAgentChatModel = new ChatModel(messageBroker, host, contextModel, {
        initialFlags: {
          doUseNewDraftFunctionality: true,
          enableBackgroundAgents: true,
          enableAgentAutoMode: true,
        },
      });

      const remoteAgentToolsModel = new ToolsWebviewModel(
        remoteAgentChatModel.currentConversationModel,
        remoteAgentChatModel.extensionClient,
        remoteAgentChatModel,
        undefined, // remoteAgentsModel not needed for these tests
      );

      const remoteAgentConversationModel = new AgentConversationModel(
        remoteAgentChatModel,
        remoteAgentToolsModel,
        mockCheckpointStore,
      );

      new ChatModeModel(remoteAgentChatModel, remoteAgentConversationModel, remoteAgentsModel);

      await tick();

      expect(remoteAgentsModel.isActive).toBe(true);
    });
  });

  describe("toggleChatAgentMode", () => {
    describe("forward toggle", () => {
      test("should toggle from chat to agent manual mode", async () => {
        // Start in chat mode
        chatModeModel.setToChat();
        await tick();

        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(false);

        // Toggle forward
        await chatModeModel.toggleChatAgentMode(false);
        await tick();

        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
        expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.manual);
      });

      test("should toggle from agent manual to agent auto mode", async () => {
        // Start in agent manual mode
        await chatModeModel.setToAgent(AgentExecutionMode.manual);
        await tick();

        // Toggle forward
        await chatModeModel.toggleChatAgentMode(false);
        await tick();

        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
        expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.auto);
      });

      test("should toggle from agent auto to remote agent when enabled", async () => {
        // The enableBackgroundAgents flag is already true from the initial setup

        // Start in agent auto mode
        await chatModeModel.setToAgent(AgentExecutionMode.auto);
        await tick();

        // Toggle forward
        await chatModeModel.toggleChatAgentMode(false);
        await tick();

        expect(remoteAgentsModel.isActive).toBe(true);
        // Should also set agent execution mode to manual for next cycle
        expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.manual);
      });

      test("should toggle from agent auto to chat when remote agents disabled", async () => {
        // Create a new chat model with remote agents disabled
        const disabledChatModel = new ChatModel(messageBroker, host, contextModel, {
          initialFlags: {
            doUseNewDraftFunctionality: true,
            enableBackgroundAgents: false,
            enableAgentAutoMode: true,
          },
        });

        const disabledToolsModel = new ToolsWebviewModel(
          disabledChatModel.currentConversationModel,
          disabledChatModel.extensionClient,
          disabledChatModel,
          undefined, // remoteAgentsModel not needed for these tests
        );

        const disabledAgentConversationModel = new AgentConversationModel(
          disabledChatModel,
          disabledToolsModel,
          mockCheckpointStore,
        );

        const disabledChatModeModel = new ChatModeModel(
          disabledChatModel,
          disabledAgentConversationModel,
          remoteAgentsModel,
        );

        // Start in agent auto mode
        await disabledChatModeModel.setToAgent(AgentExecutionMode.auto);
        await tick();

        // Toggle forward
        await disabledChatModeModel.toggleChatAgentMode(false);
        await tick();

        expect(get(disabledAgentConversationModel.isCurrConversationAgentic)).toBe(false);
        expect(remoteAgentsModel.isActive).toBe(false);
      });

      test("should toggle from remote agent back to chat", async () => {
        // Start in remote agent mode
        chatModeModel.setToRemoteAgent();
        await tick();

        expect(remoteAgentsModel.isActive).toBe(true);

        // Toggle forward
        await chatModeModel.toggleChatAgentMode(false);
        await tick();

        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(false);
        expect(remoteAgentsModel.isActive).toBe(false);
      });
    });

    describe("reverse toggle", () => {
      test("should toggle from chat to remote agent when enabled", async () => {
        // The enableBackgroundAgents flag is already true from the initial setup

        // Start in chat mode
        chatModeModel.setToChat();
        await tick();

        // Toggle reverse
        await chatModeModel.toggleChatAgentMode(true);
        await tick();

        expect(remoteAgentsModel.isActive).toBe(true);
      });

      test("should toggle from chat to agent auto when remote agents disabled", async () => {
        // Create a new chat model with remote agents disabled
        const disabledChatModel = new ChatModel(messageBroker, host, contextModel, {
          initialFlags: {
            doUseNewDraftFunctionality: true,
            enableBackgroundAgents: false,
            enableAgentAutoMode: true,
          },
        });

        const disabledToolsModel = new ToolsWebviewModel(
          disabledChatModel.currentConversationModel,
          disabledChatModel.extensionClient,
          disabledChatModel,
          undefined, // remoteAgentsModel not needed for these tests
        );

        const disabledAgentConversationModel = new AgentConversationModel(
          disabledChatModel,
          disabledToolsModel,
          mockCheckpointStore,
        );

        const disabledChatModeModel = new ChatModeModel(
          disabledChatModel,
          disabledAgentConversationModel,
          remoteAgentsModel,
        );

        // Start in chat mode
        disabledChatModeModel.setToChat();
        await tick();

        // Toggle reverse
        await disabledChatModeModel.toggleChatAgentMode(true);
        await tick();

        expect(get(disabledAgentConversationModel.isCurrConversationAgentic)).toBe(true);
        expect(get(disabledChatModel.agentExecutionMode)).toBe(AgentExecutionMode.auto);
      });

      test("should toggle from agent manual to chat", async () => {
        // Start in agent manual mode
        await chatModeModel.setToAgent(AgentExecutionMode.manual);
        await tick();

        // Toggle reverse
        await chatModeModel.toggleChatAgentMode(true);
        await tick();

        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(false);
      });

      test("should toggle from agent auto to agent manual", async () => {
        // Start in agent auto mode
        await chatModeModel.setToAgent(AgentExecutionMode.auto);
        await tick();

        // Toggle reverse
        await chatModeModel.toggleChatAgentMode(true);
        await tick();

        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
        expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.manual);
      });

      test("should toggle from remote agent to agent auto", async () => {
        // Mock setToAgentic to properly update the conversation state
        const setToAgenticSpy = vi
          .spyOn(agentConversationModel, "setToAgentic")
          .mockImplementation(async () => {
            // Simulate what setToAgentic does - it updates the current conversation's extraData
            chatModel.currentConversationModel.extraData = {
              isAgentConversation: true,
              baselineTimestamp: 0,
            };
            // This will trigger the subscription that updates _isCurrConversationAgentic
            agentConversationModel["_isCurrConversationAgentic"].set(true);
          });

        // Start in remote agent mode
        // Note: setToRemoteAgent no longer calls setToAgentic(true) or sets execution mode
        chatModeModel.setToRemoteAgent();
        await tick();

        // Verify we're in remote agent mode
        expect(remoteAgentsModel.isActive).toBe(true);
        // setToRemoteAgent no longer calls setToAgentic(true), so conversation should not be agentic
        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(false);

        // Toggle reverse - from remote agent, it should go to agent auto mode
        await chatModeModel.toggleChatAgentMode(true);
        await tick();

        // Verify setToAgentic was called
        expect(setToAgenticSpy).toHaveBeenCalled();
        // The setToAgent method is called with auto mode
        expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
        expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.auto);
        expect(remoteAgentsModel.isActive).toBe(false);
      });
    });
  });

  describe("setToChat", () => {
    test("should set to chat mode", () => {
      chatModeModel.setToChat();

      expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(false);
      expect(remoteAgentsModel.isActive).toBe(false);
    });

    test("should update current chat mode to CHAT", () => {
      const setCurrentChatModeSpy = vi.spyOn(chatModel, "setCurrentChatMode");

      chatModeModel.setToChat();

      expect(setCurrentChatModeSpy).toHaveBeenCalledWith(ChatMode.chat);
    });

    test("should not change mode in remote agent window", async () => {
      // Set the remote agent window flag
      vi.spyOn(remoteAgentsModel, "isRemoteAgentSshWindow", "get").mockReturnValue(true);
      vi.spyOn(remoteAgentsModel, "remoteAgentSshWindowId", "get").mockReturnValue(
        "test-agent-123",
      );

      const remoteAgentChatModel = new ChatModel(messageBroker, host, contextModel, {
        initialFlags: {
          doUseNewDraftFunctionality: true,
          enableBackgroundAgents: true,
          enableAgentAutoMode: true,
        },
      });

      const remoteAgentToolsModel = new ToolsWebviewModel(
        remoteAgentChatModel.currentConversationModel,
        remoteAgentChatModel.extensionClient,
        remoteAgentChatModel,
        undefined, // remoteAgentsModel not needed for these tests
      );

      const remoteAgentConversationModel = new AgentConversationModel(
        remoteAgentChatModel,
        remoteAgentToolsModel,
        mockCheckpointStore,
      );

      const remoteAgentChatModeModel = new ChatModeModel(
        remoteAgentChatModel,
        remoteAgentConversationModel,
        remoteAgentsModel,
      );

      await tick();

      // Try to set to chat mode
      remoteAgentChatModeModel.setToChat();

      // Should remain in remote agent mode
      expect(remoteAgentsModel.isActive).toBe(true);
    });
  });

  describe("setToAgent", () => {
    test("should set to agent manual mode", async () => {
      await chatModeModel.setToAgent(AgentExecutionMode.manual);
      await tick();

      expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
      expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.manual);
      expect(remoteAgentsModel.isActive).toBe(false);
    });

    test("should set to agent auto mode", async () => {
      await chatModeModel.setToAgent(AgentExecutionMode.auto);
      await tick();

      expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
      expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.auto);
      expect(remoteAgentsModel.isActive).toBe(false);
    });

    test("should refresh auto mode acceptance", async () => {
      const refreshSpy = vi.spyOn(agentConversationModel, "refreshAutoModeAcceptance");

      await chatModeModel.setToAgent(AgentExecutionMode.auto);
      await tick();

      expect(refreshSpy).toHaveBeenCalled();
    });

    test("should reset total characters cache", async () => {
      const resetSpy = vi.spyOn(chatModel.currentConversationModel, "resetTotalCharactersCache");

      await chatModeModel.setToAgent(AgentExecutionMode.manual);
      await tick();

      expect(resetSpy).toHaveBeenCalled();
    });

    test("should update current chat mode to AGENT", async () => {
      const setCurrentChatModeSpy = vi.spyOn(chatModel, "setCurrentChatMode");

      await chatModeModel.setToAgent(AgentExecutionMode.manual);
      await tick();

      expect(setCurrentChatModeSpy).toHaveBeenCalledWith(ChatMode.agent);
    });

    test("should not change mode in remote agent window", async () => {
      // Set the remote agent window flag
      vi.spyOn(remoteAgentsModel, "isRemoteAgentSshWindow", "get").mockReturnValue(true);
      vi.spyOn(remoteAgentsModel, "remoteAgentSshWindowId", "get").mockReturnValue(
        "test-agent-123",
      );

      // Create instance with remote agent window flag
      const remoteAgentChatModel = new ChatModel(messageBroker, host, contextModel, {
        initialFlags: {
          doUseNewDraftFunctionality: true,
          enableBackgroundAgents: true,
          enableAgentAutoMode: true,
        },
      });

      const remoteAgentToolsModel = new ToolsWebviewModel(
        remoteAgentChatModel.currentConversationModel,
        remoteAgentChatModel.extensionClient,
        remoteAgentChatModel,
        undefined, // remoteAgentsModel not needed for these tests
      );

      const remoteAgentConversationModel = new AgentConversationModel(
        remoteAgentChatModel,
        remoteAgentToolsModel,
        mockCheckpointStore,
      );

      const remoteAgentChatModeModel = new ChatModeModel(
        remoteAgentChatModel,
        remoteAgentConversationModel,
        remoteAgentsModel,
      );

      await tick();

      // Try to set to agent mode
      await remoteAgentChatModeModel.setToAgent(AgentExecutionMode.manual);

      // Should remain in remote agent mode
      expect(remoteAgentsModel.isActive).toBe(true);
    });

    test("should handle missing agentConversationModel gracefully", async () => {
      // Create a ChatModeModel with null agentConversationModel
      const brokenChatModeModel = new ChatModeModel(
        chatModel,
        null as unknown as AgentConversationModel,
        remoteAgentsModel,
      );

      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      await brokenChatModeModel.setToAgent(AgentExecutionMode.manual);

      expect(consoleSpy).toHaveBeenCalledWith("AgentConversationModel is not initialized");
      consoleSpy.mockRestore();
    });
  });

  describe("setToRemoteAgent", () => {
    test("should set to remote agent mode with no agent", () => {
      chatModeModel.setToRemoteAgent();

      expect(remoteAgentsModel.isActive).toBe(true);
    });

    test("should set to remote agent mode with specific agent", async () => {
      const setCurrentAgentSpy = vi.spyOn(remoteAgentsModel, "setCurrentAgent");
      const agentId = "test-agent-123";

      chatModeModel.setToRemoteAgent(agentId);

      expect(remoteAgentsModel.isActive).toBe(true);
      expect(setCurrentAgentSpy).toHaveBeenCalledWith(agentId);
    });

    test("should set to remote agent mode with null", async () => {
      const clearCurrentAgentSpy = vi.spyOn(remoteAgentsModel, "clearCurrentAgent");

      // setToRemoteAgent with null calls clearCurrentAgent
      chatModeModel.setToRemoteAgent(null);

      expect(remoteAgentsModel.isActive).toBe(true);
      expect(clearCurrentAgentSpy).toHaveBeenCalled();
    });

    test("should update current chat mode to REMOTE_AGENT", () => {
      const setCurrentChatModeSpy = vi.spyOn(chatModel, "setCurrentChatMode");

      chatModeModel.setToRemoteAgent();

      expect(setCurrentChatModeSpy).toHaveBeenCalledWith(ChatMode.remoteAgent);
    });
  });

  describe("createNewChatThread", () => {
    test("should create new chat thread", async () => {
      const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");

      await chatModeModel.createNewChatThread();

      expect(setCurrentConversationSpy).toHaveBeenCalledWith(undefined, true, {
        noopIfSameConversation: true,
      });
      expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(false);
      expect(remoteAgentsModel.isActive).toBe(false);
    });
  });

  describe("createNewLocalAgentThread", () => {
    test("should create new local agent thread with manual mode", async () => {
      const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");

      await chatModeModel.createNewLocalAgentThread(AgentExecutionMode.manual);

      expect(setCurrentConversationSpy).toHaveBeenCalledWith(undefined, true, {
        noopIfSameConversation: true,
      });
      expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
      expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.manual);
    });

    test("should create new local agent thread with auto mode", async () => {
      const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");

      await chatModeModel.createNewLocalAgentThread(AgentExecutionMode.auto);

      expect(setCurrentConversationSpy).toHaveBeenCalledWith(undefined, true, {
        noopIfSameConversation: true,
      });
      expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
      expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.auto);
    });
  });

  describe("createNewRemoteAgentThread", () => {
    test("should create new remote agent thread with no agent", async () => {
      const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");

      await chatModeModel.createNewRemoteAgentThread();

      expect(setCurrentConversationSpy).not.toHaveBeenCalled();
      expect(remoteAgentsModel.isActive).toBe(true);
    });

    test("should create new remote agent thread with specific agent", async () => {
      const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");
      const setCurrentAgentSpy = vi.spyOn(remoteAgentsModel, "setCurrentAgent");
      const agentId = "test-agent-456";

      // createNewRemoteAgentThread doesn't take parameters, it uses setToRemoteAgent internally
      await chatModeModel.createNewRemoteAgentThread();
      // Then set the agent separately
      chatModeModel.setToRemoteAgent(agentId);

      expect(setCurrentConversationSpy).not.toHaveBeenCalled();
      expect(remoteAgentsModel.isActive).toBe(true);
      expect(setCurrentAgentSpy).toHaveBeenCalledWith(agentId);
    });
  });

  describe("setCurrentConversation behavior", () => {
    test("should call setCurrentConversation for chat and localAgent modes but never for remoteAgent", async () => {
      const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");

      // Test chat mode - should call setCurrentConversation
      setCurrentConversationSpy.mockClear();
      await chatModeModel.createNewChatThread();
      expect(setCurrentConversationSpy).toHaveBeenCalledWith(undefined, true, {
        noopIfSameConversation: true,
      });

      // Test localAgent mode - should call setCurrentConversation
      setCurrentConversationSpy.mockClear();
      await chatModeModel.createNewLocalAgentThread(AgentExecutionMode.manual);
      expect(setCurrentConversationSpy).toHaveBeenCalledWith(undefined, true, {
        noopIfSameConversation: true,
      });

      setCurrentConversationSpy.mockClear();
      await chatModeModel.createNewLocalAgentThread(AgentExecutionMode.auto);
      expect(setCurrentConversationSpy).toHaveBeenCalledWith(undefined, true, {
        noopIfSameConversation: true,
      });

      // Test remoteAgent mode - should NOT call setCurrentConversation
      setCurrentConversationSpy.mockClear();
      await chatModeModel.createNewRemoteAgentThread();
      expect(setCurrentConversationSpy).not.toHaveBeenCalled();

      // Test switchToThread for different modes
      setCurrentConversationSpy.mockClear();
      chatModeModel.switchToThread("chat", "test-chat-id");
      expect(setCurrentConversationSpy).toHaveBeenCalledWith("test-chat-id", true, {
        noopIfSameConversation: true,
      });

      setCurrentConversationSpy.mockClear();
      chatModeModel.switchToThread("localAgent", "test-agent-id", AgentExecutionMode.manual);
      expect(setCurrentConversationSpy).toHaveBeenCalledWith("test-agent-id", true, {
        noopIfSameConversation: true,
      });

      setCurrentConversationSpy.mockClear();
      chatModeModel.switchToThread("remoteAgent", "test-remote-agent-id");
      expect(setCurrentConversationSpy).not.toHaveBeenCalled();
    });
  });

  describe("switchToThread", () => {
    test("should switch to chat thread", () => {
      const threadId = "chat-thread-123";
      const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");

      chatModeModel.switchToThread("chat", threadId);

      expect(setCurrentConversationSpy).toHaveBeenCalledWith(threadId, true, {
        noopIfSameConversation: true,
      });
      expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(false);
      expect(remoteAgentsModel.isActive).toBe(false);
    });

    test("should switch to local agent thread with mode", async () => {
      const threadId = "agent-thread-456";
      const setCurrentConversationSpy = vi.spyOn(chatModel, "setCurrentConversation");

      chatModeModel.switchToThread("localAgent", threadId, AgentExecutionMode.auto);
      await tick();

      expect(setCurrentConversationSpy).toHaveBeenCalledWith(threadId, true, {
        noopIfSameConversation: true,
      });
      expect(get(agentConversationModel.isCurrConversationAgentic)).toBe(true);
      expect(get(chatModel.agentExecutionMode)).toBe(AgentExecutionMode.auto);
    });

    test("should switch to remote agent thread", () => {
      const threadId = "remote-thread-789";
      const setToRemoteAgentSpy = vi.spyOn(chatModeModel, "setToRemoteAgent");

      chatModeModel.switchToThread("remoteAgent", threadId);

      // For remote agent threads, setCurrentConversation is not called
      // Instead, setToRemoteAgent is called with the threadId
      expect(setToRemoteAgentSpy).toHaveBeenCalledWith(threadId);
      expect(remoteAgentsModel.isActive).toBe(true);
    });
  });

  describe("handleMessageFromExtension", () => {
    test("should handle remoteAgentSelectAgentId message", () => {
      const setToRemoteAgentSpy = vi.spyOn(chatModeModel, "setToRemoteAgent");
      const agentId = "selected-agent-123";

      const event = {
        data: {
          type: WebViewMessageType.remoteAgentSelectAgentId,
          data: {
            agentId,
          },
        },
      } as MessageEvent;

      const handled = chatModeModel.handleMessageFromExtension(event);

      expect(handled).toBe(true);
      expect(setToRemoteAgentSpy).toHaveBeenCalledWith(agentId);
    });

    test("should not handle other message types", () => {
      const event = {
        data: {
          type: WebViewMessageType.chatClearMetadata,
        },
      } as MessageEvent;

      const handled = chatModeModel.handleMessageFromExtension(event);

      expect(handled).toBe(false);
    });
  });

  describe("findLatestChatThread", () => {
    test("should find latest chat thread", () => {
      // Mock orderedConversations as a method
      // The method returns the first matching item, not the last
      vi.spyOn(chatModel, "orderedConversations").mockReturnValue([
        { id: "chat-1", extraData: { isAgentConversation: false } },
        { id: "agent-1", extraData: { isAgentConversation: true } },
        { id: "chat-2", extraData: { isAgentConversation: false } },
        { id: "chat-3", extraData: {} }, // No isAgentConversation means it's a chat
      ] as any);

      const result = chatModeModel["findLatestChatThread"]();

      // Returns the first chat thread found
      expect(result).toBe("chat-1");
    });

    test("should return undefined when no chat threads exist", () => {
      vi.spyOn(chatModel, "orderedConversations").mockReturnValue([
        { id: "agent-1", extraData: { isAgentConversation: true } },
        { id: "agent-2", extraData: { isAgentConversation: true } },
      ] as any);

      const result = chatModeModel["findLatestChatThread"]();

      expect(result).toBeUndefined();
    });
  });

  describe("findLatestLocalAgentThread", () => {
    test("should find latest local agent thread", () => {
      vi.spyOn(chatModel, "orderedConversations").mockReturnValue([
        { id: "chat-1", extraData: { isAgentConversation: false } },
        { id: "agent-1", extraData: { isAgentConversation: true } },
        { id: "agent-2", extraData: { isAgentConversation: true } },
      ] as any);

      const result = chatModeModel["findLatestLocalAgentThread"]();

      // Returns the first agent thread found
      expect(result).toBe("agent-1");
    });

    test("should return undefined when no agent threads exist", () => {
      vi.spyOn(chatModel, "orderedConversations").mockReturnValue([
        { id: "chat-1", extraData: { isAgentConversation: false } },
        { id: "chat-2", extraData: {} },
      ] as any);

      const result = chatModeModel["findLatestLocalAgentThread"]();

      expect(result).toBeUndefined();
    });
  });

  describe("handleSetToChat", () => {
    test("should switch to latest chat thread if exists", () => {
      const switchToThreadSpy = vi.spyOn(chatModeModel, "switchToThread");
      vi.spyOn(chatModel, "orderedConversations").mockReturnValue([
        { id: "chat-1", extraData: { isAgentConversation: false } },
      ] as any);

      chatModeModel.handleSetToChat();

      expect(switchToThreadSpy).toHaveBeenCalledWith("chat", "chat-1");
    });

    test("should create new chat thread if none exist", () => {
      const createNewChatThreadSpy = vi.spyOn(chatModeModel, "createNewChatThread");
      vi.spyOn(chatModel, "orderedConversations").mockReturnValue([]);

      chatModeModel.handleSetToChat();

      expect(createNewChatThreadSpy).toHaveBeenCalled();
    });
  });

  describe("handleSetToAgent", () => {
    test("should switch to latest agent thread if exists", async () => {
      const switchToThreadSpy = vi.spyOn(chatModeModel, "switchToThread");
      vi.spyOn(chatModel, "orderedConversations").mockReturnValue([
        { id: "agent-1", extraData: { isAgentConversation: true } },
      ] as any);

      await chatModeModel.handleSetToAgent(AgentExecutionMode.manual);

      expect(switchToThreadSpy).toHaveBeenCalledWith(
        "localAgent",
        "agent-1",
        AgentExecutionMode.manual,
      );
    });

    test("should create new agent thread if none exist", async () => {
      const createNewLocalAgentThreadSpy = vi.spyOn(chatModeModel, "createNewLocalAgentThread");
      vi.spyOn(chatModel, "orderedConversations").mockReturnValue([]);

      await chatModeModel.handleSetToAgent(AgentExecutionMode.auto);

      expect(createNewLocalAgentThreadSpy).toHaveBeenCalledWith(AgentExecutionMode.auto);
    });
  });

  describe("handleSetToBackgroundAgent", () => {
    test("should handle set to background agent", () => {
      const switchToThreadSpy = vi.spyOn(chatModeModel, "switchToThread");
      const createNewRemoteAgentThreadSpy = vi.spyOn(chatModeModel, "createNewRemoteAgentThread");

      // Mock no existing remote agents
      vi.spyOn(remoteAgentsModel, "currentAgent", "get").mockReturnValue(undefined);
      vi.spyOn(remoteAgentsModel, "agentOverviews", "get").mockReturnValue([]);

      chatModeModel.handleSetToBackgroundAgent();

      // When no remote agent thread exists, it creates a new one
      expect(createNewRemoteAgentThreadSpy).toHaveBeenCalled();
      expect(switchToThreadSpy).not.toHaveBeenCalled();
    });

    test("should switch to existing remote agent thread", () => {
      const switchToThreadSpy = vi.spyOn(chatModeModel, "switchToThread");
      const createNewRemoteAgentThreadSpy = vi.spyOn(chatModeModel, "createNewRemoteAgentThread");

      // Mock existing remote agent
      const mockAgentId = "existing-agent-123";
      vi.spyOn(remoteAgentsModel, "currentAgent", "get").mockReturnValue({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        remote_agent_id: mockAgentId,
      } as any);

      chatModeModel.handleSetToBackgroundAgent();

      // When a remote agent thread exists, it switches to it
      expect(switchToThreadSpy).toHaveBeenCalledWith("remoteAgent", mockAgentId);
      expect(createNewRemoteAgentThreadSpy).not.toHaveBeenCalled();
    });
  });
});
