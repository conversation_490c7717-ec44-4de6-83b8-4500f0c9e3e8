import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { CurrentConversationTaskStore, clearCancelledTasks } from "./task-store";
import {
  TaskState,
  TaskUpdatedBy,
  type HydratedTask,
} from "@augment-internal/sidecar-libs/src/agent/task/task-types";
import { get, writable } from "svelte/store";
import type { IExtensionClient } from "../extension-client";
import type { ConversationModel } from "./conversation-model";
import type { ChatModel } from "./chat-model";
import type { AgentConversationModel } from "./agent-conversation-model";

// Helper function to create a mock task
function createMockTask(
  uuid: string = "test-uuid",
  name: string = "Test Task",
  description: string = "Test Description",
  state: TaskState = TaskState.NOT_STARTED,
  subTasks: string[] = [],
  subTasksData: HydratedTask[] = [],
): HydratedTask {
  return {
    uuid,
    name,
    description,
    state,
    subTasks,
    subTasksData,
    lastUpdated: Date.now(),
    lastUpdatedBy: TaskUpdatedBy.USER,
  };
}

describe("CurrentConversationTaskStore", () => {
  describe("createReadOnlyStore", () => {
    it("should create a read-only store with undefined root task", async () => {
      const store = CurrentConversationTaskStore.createReadOnlyStore(undefined);

      // Check that the store has the expected properties
      expect(get(store.rootTask)).toBeUndefined();
      expect(get(store.rootTaskUuid)).toBeUndefined();
      expect(get(store.canShowTaskList)).toBe(true);
      expect(get(store.uuidToTask).size).toBe(0);

      // Check that the methods are no-ops
      await expect(store.createTask("test", "test")).resolves.toBe("");
      await expect(store.updateTask("test", {}, TaskUpdatedBy.USER)).resolves.toBeUndefined();
      await expect(store.getHydratedTask("test")).resolves.toBeUndefined();
      await expect(store.refreshTasks()).resolves.toBeUndefined();
      await expect(store.updateTaskListStatuses("test")).resolves.toBeUndefined();
      await expect(store.syncTaskListWithConversation("test")).resolves.toBeUndefined();

      // These methods don't return anything
      await expect(store.deleteTask("test")).resolves.toBeUndefined();
      await expect(store.saveHydratedTask({} as HydratedTask)).resolves.toBeUndefined();
      await expect(store.runHydratedTask({} as HydratedTask)).resolves.toBeUndefined();
      store.dispose();
      expect(store.handleMessageFromExtension({} as MessageEvent)).toBe(false);
    });

    it("should create a read-only store with a root task", async () => {
      const rootTask = createMockTask("root-uuid", "Root Task");
      const store = CurrentConversationTaskStore.createReadOnlyStore(rootTask);

      // Check that the store has the expected properties
      expect(get(store.rootTask)).toEqual(rootTask);
      expect(get(store.rootTaskUuid)).toBe("root-uuid");
      expect(get(store.canShowTaskList)).toBe(true);
      expect(get(store.uuidToTask).size).toBe(1);
      expect(get(store.uuidToTask).get("root-uuid")).toEqual(rootTask);
    });

    it("should create a read-only store with a task tree", async () => {
      const subTask1 = createMockTask("sub-1", "Sub Task 1");
      const subTask2 = createMockTask("sub-2", "Sub Task 2");
      const rootTask = createMockTask(
        "root-uuid",
        "Root Task",
        "Root Description",
        TaskState.NOT_STARTED,
        ["sub-1", "sub-2"],
        [subTask1, subTask2],
      );

      const store = CurrentConversationTaskStore.createReadOnlyStore(rootTask);

      // Check that the store has the expected properties
      expect(get(store.rootTask)).toEqual(rootTask);
      expect(get(store.rootTaskUuid)).toBe("root-uuid");

      // Check that the uuidToTask map contains all tasks
      const uuidToTask = get(store.uuidToTask);
      expect(uuidToTask.size).toBe(3);
      expect(uuidToTask.get("root-uuid")).toEqual(rootTask);
      expect(uuidToTask.get("sub-1")).toEqual(subTask1);
      expect(uuidToTask.get("sub-2")).toEqual(subTask2);
    });

    it("should create a read-only store with a deeply nested task tree", async () => {
      const subSubTask = createMockTask("sub-sub", "Sub Sub Task");
      const subTask1 = createMockTask(
        "sub-1",
        "Sub Task 1",
        "Sub Description",
        TaskState.IN_PROGRESS,
        ["sub-sub"],
        [subSubTask],
      );
      const subTask2 = createMockTask("sub-2", "Sub Task 2");
      const rootTask = createMockTask(
        "root-uuid",
        "Root Task",
        "Root Description",
        TaskState.NOT_STARTED,
        ["sub-1", "sub-2"],
        [subTask1, subTask2],
      );

      const store = CurrentConversationTaskStore.createReadOnlyStore(rootTask);

      // Check that the uuidToTask map contains all tasks
      const uuidToTask = get(store.uuidToTask);
      expect(uuidToTask.size).toBe(4);
      expect(uuidToTask.get("root-uuid")).toEqual(rootTask);
      expect(uuidToTask.get("sub-1")).toEqual(subTask1);
      expect(uuidToTask.get("sub-2")).toEqual(subTask2);
      expect(uuidToTask.get("sub-sub")).toEqual(subSubTask);
    });
  });

  describe("clearCancelledTasks", () => {
    it("should return null when the root task is cancelled", () => {
      const cancelledRootTask = createMockTask(
        "root",
        "Root Task",
        "Description",
        TaskState.CANCELLED,
      );

      const result = clearCancelledTasks(cancelledRootTask);

      expect(result).toBeNull();
    });

    it("should return the same task when no tasks are cancelled", () => {
      const subTask1 = createMockTask("sub-1", "Sub Task 1", "Description", TaskState.NOT_STARTED);
      const subTask2 = createMockTask("sub-2", "Sub Task 2", "Description", TaskState.COMPLETE);
      const rootTask = createMockTask(
        "root",
        "Root Task",
        "Description",
        TaskState.IN_PROGRESS,
        ["sub-1", "sub-2"],
        [subTask1, subTask2],
      );

      const result = clearCancelledTasks(rootTask);

      expect(result).not.toBeNull();
      expect(result!.uuid).toBe("root");
      expect(result!.state).toBe(TaskState.IN_PROGRESS);
      expect(result!.subTasks).toEqual(["sub-1", "sub-2"]);
      expect(result!.subTasksData).toHaveLength(2);
      expect(result!.subTasksData![0].uuid).toBe("sub-1");
      expect(result!.subTasksData![1].uuid).toBe("sub-2");
    });

    it("should remove cancelled tasks from a flat list of subtasks", () => {
      const subTask1 = createMockTask("sub-1", "Sub Task 1", "Description", TaskState.NOT_STARTED);
      const subTask2 = createMockTask("sub-2", "Sub Task 2", "Description", TaskState.CANCELLED);
      const subTask3 = createMockTask("sub-3", "Sub Task 3", "Description", TaskState.COMPLETE);
      const rootTask = createMockTask(
        "root",
        "Root Task",
        "Description",
        TaskState.IN_PROGRESS,
        ["sub-1", "sub-2", "sub-3"],
        [subTask1, subTask2, subTask3],
      );

      const result = clearCancelledTasks(rootTask);

      expect(result).not.toBeNull();
      expect(result!.subTasks).toEqual(["sub-1", "sub-3"]);
      expect(result!.subTasksData).toHaveLength(2);
      expect(result!.subTasksData![0].uuid).toBe("sub-1");
      expect(result!.subTasksData![1].uuid).toBe("sub-3");
    });

    it("should remove cancelled tasks from nested hierarchies", () => {
      const subSubTask1 = createMockTask(
        "sub-sub-1",
        "Sub Sub Task 1",
        "Description",
        TaskState.NOT_STARTED,
      );
      const subSubTask2 = createMockTask(
        "sub-sub-2",
        "Sub Sub Task 2",
        "Description",
        TaskState.CANCELLED,
      );
      const subTask1 = createMockTask(
        "sub-1",
        "Sub Task 1",
        "Description",
        TaskState.IN_PROGRESS,
        ["sub-sub-1", "sub-sub-2"],
        [subSubTask1, subSubTask2],
      );
      const subTask2 = createMockTask("sub-2", "Sub Task 2", "Description", TaskState.CANCELLED);
      const rootTask = createMockTask(
        "root",
        "Root Task",
        "Description",
        TaskState.NOT_STARTED,
        ["sub-1", "sub-2"],
        [subTask1, subTask2],
      );

      const result = clearCancelledTasks(rootTask);

      expect(result).not.toBeNull();
      expect(result!.subTasks).toEqual(["sub-1"]);
      expect(result!.subTasksData).toHaveLength(1);

      const remainingSubTask = result!.subTasksData![0];
      expect(remainingSubTask.uuid).toBe("sub-1");
      expect(remainingSubTask.subTasks).toEqual(["sub-sub-1"]);
      expect(remainingSubTask.subTasksData).toHaveLength(1);
      expect(remainingSubTask.subTasksData![0].uuid).toBe("sub-sub-1");
    });

    it("should handle cases where all subtasks are cancelled", () => {
      const subTask1 = createMockTask("sub-1", "Sub Task 1", "Description", TaskState.CANCELLED);
      const subTask2 = createMockTask("sub-2", "Sub Task 2", "Description", TaskState.CANCELLED);
      const rootTask = createMockTask(
        "root",
        "Root Task",
        "Description",
        TaskState.NOT_STARTED,
        ["sub-1", "sub-2"],
        [subTask1, subTask2],
      );

      const result = clearCancelledTasks(rootTask);

      expect(result).not.toBeNull();
      expect(result!.uuid).toBe("root");
      expect(result!.subTasks).toEqual([]);
      expect(result!.subTasksData).toEqual([]);
    });

    it("should preserve all other task properties and metadata", () => {
      const originalTask = createMockTask(
        "root",
        "Original Name",
        "Original Description",
        TaskState.IN_PROGRESS,
      );
      originalTask.lastUpdated = 1234567890;
      originalTask.lastUpdatedBy = TaskUpdatedBy.AGENT;

      const result = clearCancelledTasks(originalTask);

      expect(result).not.toBeNull();
      expect(result!.uuid).toBe("root");
      expect(result!.name).toBe("Original Name");
      expect(result!.description).toBe("Original Description");
      expect(result!.state).toBe(TaskState.IN_PROGRESS);
      expect(result!.lastUpdated).toBe(1234567890);
      expect(result!.lastUpdatedBy).toBe(TaskUpdatedBy.AGENT);
    });

    it("should return a new object (immutable operation)", () => {
      const subTask = createMockTask("sub-1", "Sub Task", "Description", TaskState.NOT_STARTED);
      const rootTask = createMockTask(
        "root",
        "Root Task",
        "Description",
        TaskState.NOT_STARTED,
        ["sub-1"],
        [subTask],
      );

      const result = clearCancelledTasks(rootTask);

      expect(result).not.toBeNull();
      expect(result).not.toBe(rootTask); // Different object reference
      expect(result!.subTasksData![0]).not.toBe(subTask); // Subtasks are also new objects

      // Original task should remain unchanged
      expect(rootTask.subTasks).toEqual(["sub-1"]);
      expect(rootTask.subTasksData).toHaveLength(1);
    });

    it("should handle tasks with no subtasks", () => {
      const leafTask = createMockTask("leaf", "Leaf Task", "Description", TaskState.COMPLETE);

      const result = clearCancelledTasks(leafTask);

      expect(result).not.toBeNull();
      expect(result!.uuid).toBe("leaf");
      expect(result!.subTasks).toEqual([]);
      expect(result!.subTasksData).toEqual([]);
    });

    it("should handle deeply nested cancelled tasks", () => {
      const deepTask = createMockTask("deep", "Deep Task", "Description", TaskState.CANCELLED);
      const midTask = createMockTask(
        "mid",
        "Mid Task",
        "Description",
        TaskState.NOT_STARTED,
        ["deep"],
        [deepTask],
      );
      const rootTask = createMockTask(
        "root",
        "Root Task",
        "Description",
        TaskState.NOT_STARTED,
        ["mid"],
        [midTask],
      );

      const result = clearCancelledTasks(rootTask);

      expect(result).not.toBeNull();
      expect(result!.subTasks).toEqual(["mid"]);
      expect(result!.subTasksData).toHaveLength(1);

      const remainingMidTask = result!.subTasksData![0];
      expect(remainingMidTask.uuid).toBe("mid");
      expect(remainingMidTask.subTasks).toEqual([]);
      expect(remainingMidTask.subTasksData).toEqual([]);
    });
  });

  describe("CurrentConversationTaskStore - Full Implementation", () => {
    let mockExtensionClient: IExtensionClient;
    let mockChatModel: ChatModel;
    let mockConversationModel: ConversationModel;
    let mockAgentConversationModel: AgentConversationModel;
    let store: CurrentConversationTaskStore;
    let flagsStore: any;
    let agenticStore: any;
    let conversationStore: any;

    beforeEach(() => {
      // Mock extension client
      mockExtensionClient = {
        createTask: vi.fn().mockResolvedValue("new-task-uuid"),
        updateTask: vi.fn().mockResolvedValue(undefined),
        getHydratedTask: vi.fn().mockResolvedValue(undefined),
        setCurrentRootTaskUuid: vi.fn(),
        updateHydratedTask: vi.fn().mockResolvedValue({ created: 1, updated: 0, deleted: 0 }),
        createFile: vi.fn(),
      } as any;

      // Mock conversation model
      conversationStore = writable({
        id: "conv-123",
        name: "Test Conversation",
        rootTaskUuid: undefined,
      });
      mockConversationModel = {
        ...conversationStore,
        onNewConversation: vi.fn().mockReturnValue(() => {}),
        sendSilentExchange: vi.fn().mockResolvedValue({
          responseText: "# Test Task\n[ ] UUID:test NAME:Test DESCRIPTION:Test",
        }),
        rootTaskUuid: undefined,
      } as any;

      // Mock chat model with writable stores
      flagsStore = writable({ enableTaskList: true });
      mockChatModel = {
        flags: flagsStore,
        get currentConversationId() {
          return "conv-123";
        },
        currentConversationModel: {
          sendExchange: vi.fn().mockResolvedValue(undefined),
          createStructuredRequestNodes: vi.fn().mockReturnValue([]),
          selectedModelId: "test-model",
        },
      } as any;

      // Mock agent conversation model
      agenticStore = writable(true);
      mockAgentConversationModel = {
        isCurrConversationAgentic: agenticStore,
        interruptAgent: vi.fn().mockResolvedValue(undefined),
      } as any;

      // Create store instance
      store = new CurrentConversationTaskStore(
        mockChatModel,
        mockExtensionClient,
        mockConversationModel,
        mockAgentConversationModel,
      );

      // Clear any calls made during construction
      vi.clearAllMocks();
    });

    afterEach(() => {
      store.dispose();
      vi.clearAllMocks();
      vi.restoreAllMocks();
    });

    describe("constructor", () => {
      it("should initialize with correct default values", () => {
        expect(get(store.rootTask)).toBeUndefined();
        expect(get(store.uuidToTask).size).toBe(0);
      });

      it("should set up derived stores correctly", () => {
        expect(get(store.canShowTaskList)).toBe(true);
      });

      it("should call _maybeInitializeConversationRootTask on construction", () => {
        // This is tested indirectly through the constructor behavior
        expect(mockExtensionClient.setCurrentRootTaskUuid).toHaveBeenCalled();
      });
    });

    describe("static properties", () => {
      it("should have correct static key", () => {
        expect(CurrentConversationTaskStore.key).toBe("currentConversationTaskStore");
      });

      it("should have correct refresh interval", () => {
        expect(CurrentConversationTaskStore.REFRESH_INTERVAL_MS).toBe(2000);
      });
    });

    describe("dispose", () => {
      it("should dispose all disposables", () => {
        const disposeSpy = vi.fn();
        (store as any)._disposables.push({ dispose: disposeSpy });

        store.dispose();

        expect(disposeSpy).toHaveBeenCalled();
        expect((store as any)._disposables).toEqual([]);
      });
    });

    describe("handleMessageFromExtension", () => {
      it("should return false for any message", () => {
        const result = store.handleMessageFromExtension({} as MessageEvent);
        expect(result).toBe(false);
      });
    });

    describe("createTask", () => {
      it("should create a task and refresh", async () => {
        const uuid = await store.createTask("Test Task", "Test Description", "parent-uuid");

        expect(mockExtensionClient.createTask).toHaveBeenCalledWith(
          "Test Task",
          "Test Description",
          "parent-uuid",
        );
        expect(uuid).toBe("new-task-uuid");
      });

      it("should create a task without parent", async () => {
        const uuid = await store.createTask("Test Task", "Test Description");

        expect(mockExtensionClient.createTask).toHaveBeenCalledWith(
          "Test Task",
          "Test Description",
          undefined,
        );
        expect(uuid).toBe("new-task-uuid");
      });
    });

    describe("updateTask", () => {
      it("should update a task and refresh", async () => {
        const updates = { name: "Updated Task" };

        await store.updateTask("task-uuid", updates, TaskUpdatedBy.USER);

        expect(mockExtensionClient.updateTask).toHaveBeenCalledWith(
          "task-uuid",
          updates,
          TaskUpdatedBy.USER,
        );
      });
    });

    describe("getHydratedTask", () => {
      it("should delegate to extension client", async () => {
        const mockTask = createMockTask("test-uuid");
        mockExtensionClient.getHydratedTask = vi.fn().mockResolvedValue(mockTask);

        const result = await store.getHydratedTask("test-uuid");

        expect(mockExtensionClient.getHydratedTask).toHaveBeenCalledWith("test-uuid");
        expect(result).toBe(mockTask);
      });
    });

    describe("refreshTasks", () => {
      it("should refresh tasks when root task UUID exists", async () => {
        const mockTask = createMockTask("root-uuid");
        mockExtensionClient.getHydratedTask = vi.fn().mockResolvedValue(mockTask);

        // Set the root task UUID in the conversation store
        conversationStore.set({
          id: "conv-123",
          name: "Test Conversation",
          rootTaskUuid: "root-uuid",
        });

        // Clear any calls made during construction
        vi.clearAllMocks();

        await store.refreshTasks();

        expect(mockExtensionClient.getHydratedTask).toHaveBeenCalledWith("root-uuid");
        expect(get(store.rootTask)).toBe(mockTask);
      });

      it("should clear root task when no UUID exists", async () => {
        // Set the conversation store to have no root task UUID
        conversationStore.set({
          id: "conv-123",
          name: "Test Conversation",
          rootTaskUuid: undefined,
        });

        await store.refreshTasks();

        expect(get(store.rootTask)).toBeUndefined();
      });
    });

    describe("syncTaskListWithConversation", () => {
      it("should sync task list with conversation", async () => {
        const mockRootTask = createMockTask("root-uuid");
        mockExtensionClient.getHydratedTask = vi.fn().mockResolvedValue(mockRootTask);
        (store as any)._rootTask.set(mockRootTask);

        await store.syncTaskListWithConversation("root-uuid");

        expect(mockConversationModel.sendSilentExchange).toHaveBeenCalled();
        expect(mockExtensionClient.updateHydratedTask).toHaveBeenCalled();
      });
    });

    describe("updateTaskListStatuses", () => {
      it("should update task list statuses", async () => {
        const mockRootTask = createMockTask("root-uuid");
        mockExtensionClient.getHydratedTask = vi.fn().mockResolvedValue(mockRootTask);
        (store as any)._rootTask.set(mockRootTask);

        await store.updateTaskListStatuses("root-uuid");

        expect(mockConversationModel.sendSilentExchange).toHaveBeenCalled();
        expect(mockExtensionClient.updateHydratedTask).toHaveBeenCalled();
      });
    });

    describe("_updateTaskList", () => {
      it("should call sendSilentExchange when root task exists", async () => {
        // Set up a root task so the method doesn't return early
        const mockRootTask = createMockTask("root-uuid");
        (store as any)._rootTask.set(mockRootTask);

        await (store as any)._updateTaskList("root-uuid", "test prompt", []);

        expect(mockConversationModel.sendSilentExchange).toHaveBeenCalled();
      });

      it("should not update if no root task", async () => {
        (store as any)._rootTask.set(undefined);

        await (store as any)._updateTaskList("root-uuid", "test prompt", []);

        expect(mockConversationModel.sendSilentExchange).not.toHaveBeenCalled();
      });

      it("should not update if task not found in tree", async () => {
        const mockRootTask = createMockTask("root-uuid");
        (store as any)._rootTask.set(mockRootTask);

        await (store as any)._updateTaskList("non-existent-uuid", "test prompt", []);

        expect(mockConversationModel.sendSilentExchange).not.toHaveBeenCalled();
      });

      it("should handle errors and reset enhancing state", async () => {
        const mockRootTask = createMockTask("root-uuid");
        (store as any)._rootTask.set(mockRootTask);
        mockConversationModel.sendSilentExchange = vi
          .fn()
          .mockRejectedValue(new Error("Test error"));

        await expect(
          (store as any)._updateTaskList("root-uuid", "test prompt", []),
        ).rejects.toThrow("Test error");
      });
    });

    describe("deleteTask", () => {
      it("should delete task from parent", async () => {
        const mockSubTask = createMockTask("sub-uuid");
        const mockParentTask = createMockTask(
          "parent-uuid",
          "Parent",
          "Description",
          TaskState.NOT_STARTED,
          ["sub-uuid"],
          [mockSubTask],
        );
        mockExtensionClient.getHydratedTask = vi.fn().mockResolvedValue(mockParentTask);
        const backlinks: Record<string, string> = {};
        backlinks["sub-uuid"] = "parent-uuid";
        (store as any)._backlinks.set(backlinks);

        await store.deleteTask("sub-uuid");

        expect(mockExtensionClient.updateTask).toHaveBeenCalledWith(
          "parent-uuid",
          { subTasks: [] },
          TaskUpdatedBy.USER,
        );
      });

      it("should delete top-level task from root task", async () => {
        const mockTopLevelTask = createMockTask("top-level-uuid");
        const mockRootTask = createMockTask(
          "root-uuid",
          "Root",
          "Root Description",
          TaskState.NOT_STARTED,
          ["top-level-uuid"],
          [mockTopLevelTask],
        );
        (store as any)._backlinks.set({}); // No parent for top-level task
        (store as any)._rootTask.set(mockRootTask);

        await store.deleteTask("top-level-uuid");

        expect(mockExtensionClient.updateTask).toHaveBeenCalledWith(
          "root-uuid",
          {
            subTasks: [],
          },
          TaskUpdatedBy.USER,
        );
      });

      it("should do nothing if no parent task UUID and no root task", async () => {
        (store as any)._backlinks.set({});
        (store as any)._rootTask.set(undefined);

        await store.deleteTask("orphan-uuid");

        expect(mockExtensionClient.updateTask).not.toHaveBeenCalled();
      });

      it("should do nothing if task is not a direct child of root task", async () => {
        const mockOtherTask = createMockTask("other-uuid");
        const mockRootTask = createMockTask(
          "root-uuid",
          "Root",
          "Root Description",
          TaskState.NOT_STARTED,
          ["other-uuid"],
          [mockOtherTask],
        );
        (store as any)._backlinks.set({});
        (store as any)._rootTask.set(mockRootTask);

        await store.deleteTask("non-existent-uuid");

        expect(mockExtensionClient.updateTask).not.toHaveBeenCalled();
      });

      it("should do nothing if parent task not found", async () => {
        mockExtensionClient.getHydratedTask = vi.fn().mockResolvedValue(undefined);
        const backlinks: Record<string, string> = {};
        backlinks["sub-uuid"] = "parent-uuid";
        (store as any)._backlinks.set(backlinks);

        await store.deleteTask("sub-uuid");

        expect(mockExtensionClient.updateTask).not.toHaveBeenCalled();
      });
    });

    describe("saveHydratedTask", () => {
      it("should save hydrated task", async () => {
        const mockTask = createMockTask("test-uuid");

        // Clear any calls made during construction
        vi.clearAllMocks();

        await store.saveHydratedTask(mockTask);

        expect(mockExtensionClient.updateHydratedTask).toHaveBeenCalledWith(
          mockTask,
          TaskUpdatedBy.USER,
        );
      });
    });

    describe("runHydratedTask", () => {
      it("should run hydrated task", async () => {
        const mockTask = createMockTask("test-uuid", "Test Task", "Test Description");

        await store.runHydratedTask(mockTask);

        expect(mockAgentConversationModel.interruptAgent).toHaveBeenCalled();
        expect(mockChatModel.currentConversationModel.sendExchange).toHaveBeenCalled();
      });

      it("should not send exchange if conversation changed", async () => {
        const mockTask = createMockTask("test-uuid", "Test Task", "Test Description");
        // Mock the getter to return a different conversation ID after interrupt
        let callCount = 0;
        Object.defineProperty(mockChatModel, "currentConversationId", {
          get: () => {
            callCount++;
            return callCount === 1 ? "conv-123" : "different-conv";
          },
        });

        await store.runHydratedTask(mockTask);

        expect(mockAgentConversationModel.interruptAgent).toHaveBeenCalled();
        expect(mockChatModel.currentConversationModel.sendExchange).not.toHaveBeenCalled();
      });
    });

    describe("getParentTask", () => {
      it("should return parent task for a child task", () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        const childTask = createMockTask("child-uuid");
        const parentTask = createMockTask(
          "parent-uuid",
          "Parent",
          "Description",
          TaskState.NOT_STARTED,
          ["child-uuid"],
          [childTask],
        );
        const rootTask = createMockTask(
          "root-uuid",
          "Root",
          "Description",
          TaskState.NOT_STARTED,
          ["parent-uuid"],
          [parentTask],
        );

        // Set up the root task
        (store as any)._rootTask.set(rootTask);

        // Force the uuidToTask derived store to compute, which will set up backlinks
        get(store.uuidToTask);

        const result = store.getParentTask("child-uuid");

        expect(result).toBe(parentTask);
      });

      it("should return undefined for root task", () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        const rootTask = createMockTask("root-uuid");
        (store as any)._rootTask.set(rootTask);

        // Force the uuidToTask derived store to compute, which will set up backlinks
        get(store.uuidToTask);

        const result = store.getParentTask("root-uuid");

        expect(result).toBeUndefined();
      });

      it("should return undefined for non-existent task", () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        const rootTask = createMockTask("root-uuid");
        (store as any)._rootTask.set(rootTask);

        // Force the uuidToTask derived store to compute, which will set up backlinks
        get(store.uuidToTask);

        const result = store.getParentTask("non-existent-uuid");

        expect(result).toBeUndefined();
      });
    });

    describe("addNewTaskAfter", () => {
      it("should add new task after specified task", async () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        const task1 = createMockTask("task-1", "Task 1");
        const task2 = createMockTask("task-2", "Task 2");
        const parentTask = createMockTask(
          "parent-uuid",
          "Parent",
          "Description",
          TaskState.NOT_STARTED,
          ["task-1", "task-2"],
          [task1, task2],
        );
        const rootTask = createMockTask(
          "root-uuid",
          "Root",
          "Description",
          TaskState.NOT_STARTED,
          ["parent-uuid"],
          [parentTask],
        );

        // Set up the root task
        (store as any)._rootTask.set(rootTask);

        // Force the uuidToTask derived store to compute, which will set up backlinks
        get(store.uuidToTask);

        // Mock the cloneHydratedTask method to return a cloned task
        const clonedTask = createMockTask("cloned-new-task", "New Task");
        const cloneTaskSpy = vi.spyOn(store, "cloneHydratedTask").mockResolvedValue(clonedTask);
        const saveHydratedTaskSpy = vi.spyOn(store, "saveHydratedTask").mockResolvedValue();

        const newTask = createMockTask("new-task", "New Task");
        const result = await store.addNewTaskAfter("task-1", newTask);

        // Verify the new task was cloned and returned
        expect(result).toBeDefined();
        expect(result!.name).toBe("New Task");
        expect(result!.uuid).toBe("cloned-new-task");
        expect(cloneTaskSpy).toHaveBeenCalledWith(newTask);
        expect(saveHydratedTaskSpy).toHaveBeenCalledWith(parentTask);

        // Verify the parent task was updated via saveHydratedTask
        expect(saveHydratedTaskSpy).toHaveBeenCalledWith(parentTask);

        // Verify the parent task's subTasks array was updated correctly
        expect(parentTask.subTasks).toEqual(["task-1", "cloned-new-task", "task-2"]);
      });

      it("should add new task at the end when target is last task", async () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        const task1 = createMockTask("task-1", "Task 1");
        const task2 = createMockTask("task-2", "Task 2");
        const parentTask = createMockTask(
          "parent-uuid",
          "Parent",
          "Description",
          TaskState.NOT_STARTED,
          ["task-1", "task-2"],
          [task1, task2],
        );
        const rootTask = createMockTask(
          "root-uuid",
          "Root",
          "Description",
          TaskState.NOT_STARTED,
          ["parent-uuid"],
          [parentTask],
        );

        // Set up the root task
        (store as any)._rootTask.set(rootTask);

        // Force the uuidToTask derived store to compute, which will set up backlinks
        get(store.uuidToTask);

        // Mock the cloneHydratedTask method to return a cloned task
        const clonedTask = createMockTask("cloned-new-task", "New Task");
        vi.spyOn(store, "cloneHydratedTask").mockResolvedValue(clonedTask);
        const saveHydratedTaskSpy = vi.spyOn(store, "saveHydratedTask").mockResolvedValue();

        const newTask = createMockTask("new-task", "New Task");
        const result = await store.addNewTaskAfter("task-2", newTask);

        // Verify the new task was added at the end
        expect(result).toBeDefined();
        expect(saveHydratedTaskSpy).toHaveBeenCalledWith(parentTask);

        // Verify the parent task's subTasks array was updated correctly
        expect(parentTask.subTasks).toEqual(["task-1", "task-2", "cloned-new-task"]);
      });

      it("should return undefined when target task has no parent", async () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        const rootTask = createMockTask("root-uuid", "Root");
        (store as any)._rootTask.set(rootTask);

        // Force the uuidToTask derived store to compute, which will set up backlinks
        get(store.uuidToTask);

        const saveHydratedTaskSpy = vi.spyOn(store, "saveHydratedTask").mockResolvedValue();

        const newTask = createMockTask("new-task", "New Task");
        const result = await store.addNewTaskAfter("root-uuid", newTask);

        expect(result).toBeUndefined();
        expect(mockExtensionClient.updateTask).not.toHaveBeenCalled();
        expect(saveHydratedTaskSpy).not.toHaveBeenCalled();
      });

      it("should return undefined when target task is not found in parent", async () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        const task1 = createMockTask("task-1", "Task 1");
        const parentTask = createMockTask(
          "parent-uuid",
          "Parent",
          "Description",
          TaskState.NOT_STARTED,
          ["task-1"],
          [task1],
        );
        const rootTask = createMockTask(
          "root-uuid",
          "Root",
          "Description",
          TaskState.NOT_STARTED,
          ["parent-uuid"],
          [parentTask],
        );

        // Set up the root task
        (store as any)._rootTask.set(rootTask);

        // Force the uuidToTask derived store to compute, which will set up backlinks
        get(store.uuidToTask);

        const saveHydratedTaskSpy = vi.spyOn(store, "saveHydratedTask").mockResolvedValue();

        const newTask = createMockTask("new-task", "New Task");
        // Try to add after a task that doesn't exist in the parent
        const result = await store.addNewTaskAfter("non-existent-task", newTask);

        expect(result).toBeUndefined();
        expect(mockExtensionClient.updateTask).not.toHaveBeenCalled();
        expect(saveHydratedTaskSpy).not.toHaveBeenCalled();
      });

      it("should handle parent task without subTasksData", async () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        const parentTask = createMockTask(
          "parent-uuid",
          "Parent",
          "Description",
          TaskState.NOT_STARTED,
          ["task-1"],
          // No subTasksData provided
        );
        const rootTask = createMockTask(
          "root-uuid",
          "Root",
          "Description",
          TaskState.NOT_STARTED,
          ["parent-uuid"],
          [parentTask],
        );

        // Set up the root task
        (store as any)._rootTask.set(rootTask);

        // Force the uuidToTask derived store to compute, which will set up backlinks
        get(store.uuidToTask);

        // Mock the cloneHydratedTask method to return a cloned task
        const clonedTask = createMockTask("cloned-new-task", "New Task");
        vi.spyOn(store, "cloneHydratedTask").mockResolvedValue(clonedTask);
        const saveHydratedTaskSpy = vi.spyOn(store, "saveHydratedTask").mockResolvedValue();

        const newTask = createMockTask("new-task", "New Task");
        const result = await store.addNewTaskAfter("task-1", newTask);

        // Should still work, just won't update subTasksData
        expect(result).toBeDefined();
        expect(saveHydratedTaskSpy).toHaveBeenCalledWith(parentTask);

        // Verify the parent task's subTasks array was updated correctly
        expect(parentTask.subTasks).toEqual(["task-1", "cloned-new-task"]);
      });

      it("should clone the new task to avoid modifying original", async () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        const task1 = createMockTask("task-1", "Task 1");
        const parentTask = createMockTask(
          "parent-uuid",
          "Parent",
          "Description",
          TaskState.NOT_STARTED,
          ["task-1"],
          [task1],
        );
        const rootTask = createMockTask(
          "root-uuid",
          "Root",
          "Description",
          TaskState.NOT_STARTED,
          ["parent-uuid"],
          [parentTask],
        );

        // Set up the root task
        (store as any)._rootTask.set(rootTask);

        // Force the uuidToTask derived store to compute, which will set up backlinks
        get(store.uuidToTask);

        // Mock the cloneHydratedTask method to return a cloned task
        const clonedTask = createMockTask("cloned-new-task", "New Task");
        const cloneTaskSpy = vi.spyOn(store, "cloneHydratedTask").mockResolvedValue(clonedTask);
        vi.spyOn(store, "saveHydratedTask").mockResolvedValue();

        const originalTask = createMockTask("new-task", "New Task");
        const result = await store.addNewTaskAfter("task-1", originalTask);

        // Verify the result is a different object (cloned)
        expect(result).toBeDefined();
        expect(result).not.toBe(originalTask);
        expect(result!.uuid).toBe("cloned-new-task");
        expect(result!.name).toBe("New Task");
        expect(cloneTaskSpy).toHaveBeenCalledWith(originalTask);
      });
    });

    describe("cloneHydratedTask", () => {
      it("should clone a task with new UUID", async () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        const originalTask = createMockTask(
          "original-uuid",
          "Original Task",
          "Original Description",
          TaskState.IN_PROGRESS,
          ["subtask-1"],
          [createMockTask("subtask-1", "Subtask", "Subtask Description", TaskState.NOT_STARTED)],
        );

        // Mock the extension client methods
        mockExtensionClient.createTask = vi.fn().mockResolvedValue("new-task-uuid");
        mockExtensionClient.updateHydratedTask = vi.fn().mockResolvedValue({
          created: [],
          updated: [],
          deleted: [],
        });
        mockExtensionClient.getHydratedTask = vi.fn().mockResolvedValue({
          ...originalTask,
          uuid: "new-task-uuid",
        });

        const result = await store.cloneHydratedTask(originalTask);

        // Verify the task was created with the correct properties
        expect(mockExtensionClient.createTask).toHaveBeenCalledWith(
          "Original Task",
          "Original Description",
          undefined,
        );

        // Verify the task hierarchy was updated
        expect(mockExtensionClient.updateHydratedTask).toHaveBeenCalled();

        // Verify the result
        expect(result).toBeDefined();
        expect(result?.uuid).toBe("new-task-uuid");
        expect(result?.name).toBe("Original Task");
        expect(result?.description).toBe("Original Description");
      });

      it("should handle errors during cloning", async () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        const originalTask = createMockTask("original-uuid", "Original Task");

        // Mock the extension client to fail
        mockExtensionClient.createTask = vi.fn().mockResolvedValue("");

        const result = await store.cloneHydratedTask(originalTask);

        // Verify the result is undefined when creation fails
        expect(result).toBeUndefined();
        expect(mockExtensionClient.updateHydratedTask).not.toHaveBeenCalled();
      });
    });

    describe("derived stores", () => {
      it("should update uuidToTask when root task changes", () => {
        const subTask = createMockTask("sub-uuid");
        const rootTask = createMockTask(
          "root-uuid",
          "Root",
          "Description",
          TaskState.NOT_STARTED,
          ["sub-uuid"],
          [subTask],
        );

        (store as any)._rootTask.set(rootTask);

        const uuidToTask = get(store.uuidToTask);
        expect(uuidToTask.size).toBe(2);
        expect(uuidToTask.get("root-uuid")).toBe(rootTask);
        expect(uuidToTask.get("sub-uuid")).toBe(subTask);
      });

      it("should update canShowTaskList based on flags and agentic status", () => {
        expect(get(store.canShowTaskList)).toBe(true);

        flagsStore.set({ enableTaskList: false });
        expect(get(store.canShowTaskList)).toBe(false);

        flagsStore.set({ enableTaskList: true });
        agenticStore.set(false);
        expect(get(store.canShowTaskList)).toBe(false);
      });
    });

    describe("refresh interval", () => {
      it("should start refresh interval when canShowTaskList is true", () => {
        const setIntervalSpy = vi.spyOn(global, "setInterval");

        // The interval should already be started during construction since both flags are true
        // Let's trigger it by changing the flags
        flagsStore.set({ enableTaskList: false });
        flagsStore.set({ enableTaskList: true });

        expect(setIntervalSpy).toHaveBeenCalled();

        setIntervalSpy.mockRestore();
      });

      it("should stop refresh interval when canShowTaskList is false", () => {
        const clearIntervalSpy = vi.spyOn(global, "clearInterval");

        // First enable to start interval
        flagsStore.set({ enableTaskList: true });
        agenticStore.set(true);

        // Then disable to stop interval
        flagsStore.set({ enableTaskList: false });

        expect(clearIntervalSpy).toHaveBeenCalled();

        clearIntervalSpy.mockRestore();
      });
    });

    describe("_maybeInitializeConversationRootTask", () => {
      it("should return existing root task UUID", async () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        // Set up conversation store with existing root task UUID
        conversationStore.set({
          id: "conv-123",
          name: "Test Conversation",
          rootTaskUuid: "existing-uuid",
        });

        const result = await (store as any)._maybeInitializeConversationRootTask();

        expect(result).toBe("existing-uuid");
        expect(mockExtensionClient.createTask).not.toHaveBeenCalled();
      });

      it("should create new root task if none exists", async () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        // Set up conversation store with no root task UUID
        conversationStore.set({
          id: "conv-123",
          name: "Test Conversation",
          rootTaskUuid: undefined,
        });
        mockExtensionClient.createTask = vi.fn().mockResolvedValue("new-root-uuid");

        const result = await (store as any)._maybeInitializeConversationRootTask();

        expect(mockExtensionClient.createTask).toHaveBeenCalledWith(
          "Conversation: Test Conversation",
          "Root task for conversation conv-123",
        );
        expect(mockConversationModel.rootTaskUuid).toBe("new-root-uuid");
        expect(mockExtensionClient.setCurrentRootTaskUuid).toHaveBeenCalledWith("new-root-uuid");
        expect(result).toBe("new-root-uuid");
      });

      it("should handle conversation without name", async () => {
        // Clear any previous calls from constructor
        vi.clearAllMocks();

        conversationStore.set({ id: "conv-123", name: undefined, rootTaskUuid: undefined });
        mockExtensionClient.createTask = vi.fn().mockResolvedValue("new-root-uuid");

        await (store as any)._maybeInitializeConversationRootTask();

        expect(mockExtensionClient.createTask).toHaveBeenCalledWith(
          "Conversation: New Chat",
          "Root task for conversation conv-123",
        );
      });
    });

    describe("_processImportedFileContent", () => {
      it("should append imported tasks to existing tasks with new UUIDs", async () => {
        // Set up existing root task with some subtasks
        const existingSubTask = createMockTask(
          "existing-1",
          "Existing Task",
          "Existing Description",
        );
        const rootTask = createMockTask(
          "root-uuid",
          "Root Task",
          "Root Description",
          TaskState.NOT_STARTED,
          ["existing-1"],
          [existingSubTask],
        );

        // Mock the root task in the store
        (store as any)._rootTask.set(rootTask);

        // Mock the parseMarkdownToTaskTree to return imported tasks
        const importedSubTask1 = createMockTask(
          "imported-1",
          "Imported Task 1",
          "Imported Description 1",
        );
        const importedSubTask2 = createMockTask(
          "imported-2",
          "Imported Task 2",
          "Imported Description 2",
        );
        const importedRootTask = createMockTask(
          "imported-root",
          "Imported Root",
          "Imported Root Description",
          TaskState.NOT_STARTED,
          ["imported-1", "imported-2"],
          [importedSubTask1, importedSubTask2],
        );

        // Mock parseMarkdownToTaskTree to return our test data
        vi.doMock("@augment-internal/sidecar-libs/src/agent/task/task-utils", async () => {
          const actual = await vi.importActual(
            "@augment-internal/sidecar-libs/src/agent/task/task-utils",
          );
          return {
            ...actual,
            parseMarkdownToTaskTree: vi.fn().mockReturnValue(importedRootTask),
          };
        });

        // Call the private method
        const fileContent =
          "[ ] UUID:imported-root NAME:Imported Root DESCRIPTION:Imported Root Description\n-[ ] UUID:imported-1 NAME:Imported Task 1 DESCRIPTION:Imported Description 1\n-[ ] UUID:imported-2 NAME:Imported Task 2 DESCRIPTION:Imported Description 2";
        await (store as any)._processImportedFileContent(fileContent);

        // Verify updateHydratedTask was called
        expect(mockExtensionClient.updateHydratedTask).toHaveBeenCalledTimes(1);

        // Get the actual call arguments
        const updateCall = (mockExtensionClient.updateHydratedTask as any).mock.calls[0][0];
        const mergedSubTasks = updateCall.subTasksData;

        // Verify basic structure
        expect(updateCall.uuid).toBe("root-uuid");
        expect(updateCall.name).toBe("Root Task");
        expect(updateCall.description).toBe("Root Description");

        // Should have 3 tasks total (1 existing + 2 imported)
        expect(mergedSubTasks).toHaveLength(3);
        expect(updateCall.subTasks).toHaveLength(3);

        // Verify existing task is preserved
        const existingTask = mergedSubTasks.find((task: any) => task.uuid === "existing-1");
        expect(existingTask).toBeDefined();
        expect(existingTask.name).toBe("Existing Task");

        // Find the imported tasks
        const importedTask1 = mergedSubTasks.find((task: any) => task.name === "Imported Task 1");
        const importedTask2 = mergedSubTasks.find((task: any) => task.name === "Imported Task 2");

        expect(importedTask1).toBeDefined();
        expect(importedTask2).toBeDefined();

        // Verify imported tasks have new UUIDs (not the original ones)
        expect(importedTask1.uuid).not.toBe("imported-1");
        expect(importedTask2.uuid).not.toBe("imported-2");
        expect(importedTask1.uuid).toMatch(
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
        );
        expect(importedTask2.uuid).toMatch(
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
        );

        // Verify imported tasks have correct content
        expect(importedTask1.description).toBe("Imported Description 1");
        expect(importedTask2.description).toBe("Imported Description 2");

        // Verify the subTasks array contains the new UUIDs
        expect(updateCall.subTasks).toContain("existing-1");
        expect(updateCall.subTasks).toContain(importedTask1.uuid);
        expect(updateCall.subTasks).toContain(importedTask2.uuid);
      });

      it("should handle empty file content", async () => {
        const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

        await (store as any)._processImportedFileContent("");

        expect(consoleSpy).toHaveBeenCalledWith("Empty file content");
        expect(mockExtensionClient.updateHydratedTask).not.toHaveBeenCalled();

        consoleSpy.mockRestore();
      });

      it("should handle missing root task", async () => {
        const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

        // Set root task to undefined
        (store as any)._rootTask.set(undefined);

        await (store as any)._processImportedFileContent(
          "[ ] UUID:test NAME:Test DESCRIPTION:Test",
        );

        expect(consoleSpy).toHaveBeenCalledWith("No root task found");
        expect(mockExtensionClient.updateHydratedTask).not.toHaveBeenCalled();

        consoleSpy.mockRestore();
      });
    });

    describe("exportTask", () => {
      it("should export a task with default options", async () => {
        const testTask = createMockTask("test-uuid", "Test Task", "Test Description");

        await store.exportTask(testTask);

        expect(mockExtensionClient.createFile).toHaveBeenCalledWith(
          expect.stringContaining("[ ] NAME:Test Task DESCRIPTION:Test Description"),
          expect.stringMatching(/^Test_Task_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.md$/),
        );
        expect(get(store.isImportingExporting)).toBe(false);
      });

      it("should export a task with custom file name", async () => {
        const testTask = createMockTask("test-uuid", "Test Task", "Test Description");

        await store.exportTask(testTask, {
          fileName: "custom-task.md",
        });

        expect(mockExtensionClient.createFile).toHaveBeenCalledWith(
          expect.stringContaining("[ ] NAME:Test Task DESCRIPTION:Test Description"),
          "custom-task.md",
        );
      });

      it("should export a task with custom base name", async () => {
        const testTask = createMockTask("test-uuid", "Test Task", "Test Description");

        await store.exportTask(testTask, {
          baseName: "CustomBase",
        });

        expect(mockExtensionClient.createFile).toHaveBeenCalledWith(
          expect.stringContaining("[ ] NAME:Test Task DESCRIPTION:Test Description"),
          expect.stringMatching(/^CustomBase_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.md$/),
        );
      });

      it("should export a task with shallow option", async () => {
        const subTask = createMockTask("sub-uuid", "Sub Task", "Sub Description");
        const testTask = createMockTask(
          "test-uuid",
          "Test Task",
          "Test Description",
          TaskState.NOT_STARTED,
          ["sub-uuid"],
          [subTask],
        );

        await store.exportTask(testTask, {
          shallow: true,
        });

        const expectedContent = (mockExtensionClient.createFile as any).mock.calls[0][0];
        expect(expectedContent).toContain("[ ] NAME:Test Task DESCRIPTION:Test Description");
        expect(expectedContent).not.toContain("Sub Task");
      });

      it("should export a task with subtasks by default", async () => {
        const subTask = createMockTask("sub-uuid", "Sub Task", "Sub Description");
        const testTask = createMockTask(
          "test-uuid",
          "Test Task",
          "Test Description",
          TaskState.NOT_STARTED,
          ["sub-uuid"],
          [subTask],
        );

        await store.exportTask(testTask);

        const expectedContent = (mockExtensionClient.createFile as any).mock.calls[0][0];
        expect(expectedContent).toContain("[ ] NAME:Test Task DESCRIPTION:Test Description");
        expect(expectedContent).toContain("-[ ] NAME:Sub Task DESCRIPTION:Sub Description");
      });

      it("should handle export errors gracefully", async () => {
        const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
        mockExtensionClient.createFile = vi.fn().mockImplementation(() => {
          throw new Error("File creation failed");
        });

        const testTask = createMockTask("test-uuid", "Test Task", "Test Description");

        await store.exportTask(testTask);

        expect(consoleSpy).toHaveBeenCalledWith("Error exporting task:", expect.any(Error));
        expect(get(store.isImportingExporting)).toBe(false);

        consoleSpy.mockRestore();
      });

      it("should sanitize file names", async () => {
        const testTask = createMockTask(
          "test-uuid",
          "Test/Task:With*Invalid?Chars",
          "Test Description",
        );

        await store.exportTask(testTask);

        expect(mockExtensionClient.createFile).toHaveBeenCalledWith(
          expect.any(String),
          expect.stringMatching(
            /^Test_Task_With_Invalid_Chars_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.md$/,
          ),
        );
      });

      it("should set isImportingExporting during export", async () => {
        const testTask = createMockTask("test-uuid", "Test Task", "Test Description");
        let isImportingExportingDuringCall = false;

        mockExtensionClient.createFile = vi.fn().mockImplementation(() => {
          isImportingExportingDuringCall = get(store.isImportingExporting);
        });

        await store.exportTask(testTask);

        expect(isImportingExportingDuringCall).toBe(true);
        expect(get(store.isImportingExporting)).toBe(false);
      });
    });
  });
});
