<script lang="ts">
  /**
   * Transforms markdown task lists into interactive tree views
   *
   * Core concept: Convert flat text into a structured UI that shows task hierarchies
   *
   * Component lifecycle:
   * [INIT]
   * - Get tool context and set up props
   * - Create state variables for content
   *
   * [REACTIVE]
   * - On tool state completed: Parse content and create task store (once)
   * - On successful parsing: Display interactive task tree
   */
  import {
    type ToolUseState,
    ToolUsePhase,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import { getToolUseContext } from "../../../tool-context";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import Markdown from "$common-webviews/src/common/components/markdown/Markdown.svelte";
  import TaskTree from "$common-webviews/src/apps/chat/components/tasks/task-tree/TaskTree.svelte";
  import { type HydratedTask } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import { parseMarkdownToTaskTree } from "@augment-internal/sidecar-libs/src/agent/task/task-utils";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "$common-webviews/src/apps/chat/models/task-store";
  import {
    preprocessTaskTreeVisibility,
    type VisibleHydratedTask,
  } from "$common-webviews/src/apps/chat/components/tasks/utils/task-visibility-utils";

  const ctx = getToolUseContext();

  export let toolUseInput: Record<string, unknown> = $ctx.toolUseInput;
  export let toolUseState: ToolUseState = $ctx.toolUseState;
  export let requestId: string = $ctx.requestId;
  export let editable: boolean = false; // When false, prevents editing of tasks

  // We don't use turnIndex but it's part of the component API
  export const turnIndex: number = $ctx.turnIndex;

  $: toolUseInput = $ctx.toolUseInput;
  $: toolUseState = $ctx.toolUseState;
  $: requestId = $ctx.requestId;

  // Track if the task tree has been initialized
  let initialized = false;

  // Track the completion state of the tool use
  $: completed = toolUseState.phase === ToolUsePhase.completed;

  // State for task list content and parsed task tree
  let taskListContent: string | undefined = undefined;
  let rootTask: HydratedTask | undefined = undefined;
  let preprocessedRootTask: VisibleHydratedTask | undefined = undefined;

  // We'll create a read-only task store when we have a task to display
  let taskStore: ICurrentConversationTaskStore | undefined = undefined;

  // Preprocess the task tree when rootTask changes
  $: if (rootTask) {
    preprocessedRootTask = preprocessTaskTreeVisibility(rootTask, new Set());
  }

  // Watch for tool use state completion and initialize the task tree once
  $: if (completed && !initialized && toolUseState.result?.text) {
    // Set initialized to true to prevent re-initialization
    initialized = true;

    // Get the task list content from the tool use result
    taskListContent = toolUseState.result.text;

    try {
      // Parse the markdown into a task tree
      rootTask = parseMarkdownToTaskTree(taskListContent);

      // Create a read-only task store with our parsed task
      taskStore = CurrentConversationTaskStore.createReadOnlyStore(rootTask);
    } catch (error) {
      console.error("Failed to parse task list markdown:", error);
    }
  }
</script>

<div class="c-task-list-tool-use-details">
  {#if taskStore !== undefined && preprocessedRootTask !== undefined && preprocessedRootTask.subTasksData && preprocessedRootTask.subTasksData.length > 0}
    <div class="c-task-list-tool-use-details__section">
      <div class="c-task-list-tool-use-details__section-header">
        <TextAugment size={1} weight="medium" color="primary">Task List</TextAugment>
      </div>
      <div class="c-task-list-tool-use-details__task-list">
        <TaskTree task={preprocessedRootTask} {taskStore} {editable} />
      </div>
    </div>
  {:else if taskStore !== undefined && rootTask !== undefined}
    <div class="c-task-list-tool-use-details__empty">
      <TextAugment size={1} color="neutral">No tasks to display</TextAugment>
    </div>
  {:else if taskListContent}
    <div class="c-task-list-tool-use-details__section">
      <div class="c-task-list-tool-use-details__section-header">
        <TextAugment size={1} weight="medium" color="primary">Task List</TextAugment>
      </div>
      <div class="c-task-list-tool-use-details__task-list">
        <Markdown markdown={taskListContent} />
      </div>
    </div>
  {:else if completed}
    <div class="c-task-list-tool-use-details__loading">
      <TextAugment size={1} color="neutral">Loading...</TextAugment>
    </div>
  {:else}
    <div class="c-task-list-tool-use-details__loading">
      <TextAugment size={1} color="neutral">Waiting for task list...</TextAugment>
    </div>
  {/if}
</div>

<style>
  .c-task-list-tool-use-details {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-task-list-tool-use-details__section {
    border: 1px solid var(--ds-color-neutral-a4);
    border-radius: var(--ds-radius-2);
    background-color: var(--ds-color-neutral-a1);
    overflow: hidden;
  }

  .c-task-list-tool-use-details__section-header {
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    background-color: var(--ds-color-neutral-a2);
    border-bottom: 1px solid var(--ds-color-neutral-a3);
  }

  .c-task-list-tool-use-details__loading {
    padding: var(--ds-spacing-1);
    text-align: center;
    background-color: var(--ds-color-neutral-a1);
    border-radius: var(--ds-radius-2);
    border: 1px solid var(--ds-color-neutral-a4);
  }

  .c-task-list-tool-use-details__empty {
    padding: var(--ds-spacing-1);
    text-align: center;
    background-color: var(--ds-color-neutral-a1);
    border-radius: var(--ds-radius-2);
    border: 1px solid var(--ds-color-neutral-a4);
  }
</style>
