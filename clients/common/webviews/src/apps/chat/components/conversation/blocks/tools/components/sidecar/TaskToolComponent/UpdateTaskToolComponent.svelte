<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import DiagramCellsIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/diagram-cells.svg?component";
  import { type ToolUseState, ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import ShellError from "../ShellError.svelte";
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";
  import TaskDiffRenderer from "./TaskDiffRenderer.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { getTaskDiffCounts } from "@augment-internal/sidecar-libs/src/agent/task/task-utils";

  // Required by the component API but not used in this component
  export const toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;

  // Check if we have task result text
  $: hasTaskResult = toolUseState.result?.text && !toolUseState.result.isError;
  $: resultText = toolUseState.result?.text || "";

  // Extract individual counts for display using utils
  $: taskCounts = hasTaskResult
    ? getTaskDiffCounts(resultText)
    : { created: 0, updated: 0, deleted: 0 };

  // Check if we have any changes to show
  $: hasChanges = taskCounts.created > 0 || taskCounts.updated > 0 || taskCounts.deleted > 0;
</script>

<BaseToolComponent bind:collapsed>
  <ToolUseHeader slot="header" toolName="Update Task List">
    <DiagramCellsIcon slot="icon" />
    <svelte:fragment slot="secondary">
      {#if hasTaskResult}
        <div class="c-task-list-tool__counts">
          {#if taskCounts.created > 0}
            <StatusBadgeAugment color="success" size={1}>
              {taskCounts.created} created
            </StatusBadgeAugment>
          {/if}
          {#if taskCounts.updated > 0}
            <StatusBadgeAugment color="info" size={1}>
              {taskCounts.updated} updated
            </StatusBadgeAugment>
          {/if}
          {#if taskCounts.deleted > 0}
            <StatusBadgeAugment color="warning" size={1}>
              {taskCounts.deleted} deleted
            </StatusBadgeAugment>
          {/if}
          {#if taskCounts.created === 0 && taskCounts.updated === 0 && taskCounts.deleted === 0}
            <StatusBadgeAugment color="info" size={1}>No changes</StatusBadgeAugment>
          {/if}
        </div>
      {/if}
    </svelte:fragment>
  </ToolUseHeader>

  <!-- Show task diff when available -->
  <div slot="details" class="c-task-list-tool__details">
    {#if hasTaskResult && hasChanges}
      <TaskDiffRenderer text={resultText} />
    {:else if hasTaskResult && !hasChanges}
      <div class="c-task-list-tool__no-diff">
        <TextAugment size={1} color="neutral">No task changes to display</TextAugment>
      </div>
    {/if}
  </div>
  {#if toolUseState.phase === ToolUsePhase.error}
    <ShellError slot="error" {...toolUseState.result ?? {}} />
  {/if}
</BaseToolComponent>

<style>
  .c-task-list-tool__details {
    padding: var(--ds-spacing-2);
  }

  .c-task-list-tool__counts {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
    flex-wrap: wrap;
  }

  .c-task-list-tool__no-diff {
    padding: var(--ds-spacing-2);
    text-align: center;
    background-color: var(--ds-color-neutral-a1);
    border-radius: var(--ds-radius-2);
    border: 1px solid var(--ds-color-neutral-a4);
  }
</style>
