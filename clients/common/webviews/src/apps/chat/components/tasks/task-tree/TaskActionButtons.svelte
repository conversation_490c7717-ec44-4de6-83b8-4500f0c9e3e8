<script lang="ts">
  import type { ICurrentConversationTaskStore } from "../../../models/task-store";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import Trash from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash.svg?component";
  import Play from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/play.svg?component";
  import {
    TaskState,
    TaskUpdatedBy,
  } from "@augment-internal/sidecar-libs/src/agent/task/task-types";

  // Props
  export let taskUuid: string;
  export let taskStore: ICurrentConversationTaskStore;
  export let editable: boolean = true;

  $: uuidToTask = taskStore.uuidToTask;
  $: task = $uuidToTask.get(taskUuid);

  async function handleDeleteTask() {
    if (!editable) return;
    await taskStore.deleteTask(taskUuid);
  }

  // Handle play action for not started tasks
  async function handlePlayTask() {
    if (!task || !editable || task.state !== TaskState.NOT_STARTED) return;
    await taskStore.updateTask(taskUuid, { state: TaskState.IN_PROGRESS }, TaskUpdatedBy.USER);
    await taskStore.runHydratedTask(task);
  }

  // Show play button only for not started tasks
  $: showPlayButton = task?.state === TaskState.NOT_STARTED;
</script>

<div class="c-task-action-buttons">
  <!-- Play Button - only shown for not started tasks -->
  {#if showPlayButton}
    <TextTooltipAugment content="Run task" triggerOn={[TooltipTriggerOn.Hover]}>
      <IconButtonAugment
        size={1}
        variant="ghost"
        color="neutral"
        disabled={!editable}
        on:click={handlePlayTask}
        class="c-task-action-button c-task-action-button--play"
      >
        <Play />
      </IconButtonAugment>
    </TextTooltipAugment>
  {/if}

  <!-- Delete Task Button, always shown -->
  <TextTooltipAugment content="Delete task" triggerOn={[TooltipTriggerOn.Hover]}>
    <IconButtonAugment
      size={1}
      variant="ghost"
      color="error"
      disabled={!editable}
      on:click={handleDeleteTask}
      class="c-task-action-button c-task-action-button--delete"
    >
      <Trash />
    </IconButtonAugment>
  </TextTooltipAugment>
</div>

<style>
  .c-task-action-buttons {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    margin-right: var(--ds-spacing-1);
  }
</style>
