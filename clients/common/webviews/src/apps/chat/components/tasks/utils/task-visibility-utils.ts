import {
  type HydratedTask,
  TaskState,
} from "@augment-internal/sidecar-libs/src/agent/task/task-types";

/**
 * Extended task type that includes visibility information
 */
export type VisibleHydratedTask = Omit<HydratedTask, "subTasksData"> & {
  isVisible: boolean;
  subTasksData?: VisibleHydratedTask[];
};

/**
 * Function to check if a task should be visible based on the filter set
 */
function shouldShowTask(task: HydratedTask, filterSet: Set<TaskState>): boolean {
  // If no filters are selected (empty set), exclude everything
  if (filterSet.size === 0) return false;
  // Show task if its state is in the active filter set
  return filterSet.has(task.state);
}

/**
 * Preprocesses the task tree to mark visibility flags using depth-first search.
 * This ensures that parent tasks are visible when any descendant matches the filter.
 *
 * @param task - The root task to preprocess
 * @param filterSet - The current filter set to apply
 * @returns The preprocessed task tree with isVisible flags
 */
export function preprocessTaskTreeVisibility(
  task: HydratedTask,
  filterSet: Set<TaskState> = new Set(Object.values(TaskState)),
): VisibleHydratedTask {
  // Create a copy of the task with the isVisible property
  const visibleTask: VisibleHydratedTask = {
    ...task,
    isVisible: false,
    subTasksData: [],
  };

  // Process subtasks recursively
  let hasVisibleDescendant = false;
  if (task.subTasksData && task.subTasksData.length > 0) {
    const processedSubTasks: VisibleHydratedTask[] = [];

    for (const subtask of task.subTasksData) {
      const processedSubtask = preprocessTaskTreeVisibility(subtask, filterSet);
      processedSubTasks.push(processedSubtask);

      // If any subtask or its descendants are visible, mark this as having visible descendants
      if (processedSubtask.isVisible) {
        hasVisibleDescendant = true;
      }
    }

    visibleTask.subTasksData = processedSubTasks;
  }

  // Determine if this task should be visible:
  // 1. If it matches the filter directly, OR
  // 2. If any of its descendants are visible
  const matchesFilter = shouldShowTask(task, filterSet);
  visibleTask.isVisible = matchesFilter || hasVisibleDescendant;

  return visibleTask;
}

/**
 * Filters the visible subtasks from a preprocessed task tree.
 * This is used to get only the visible children for rendering.
 *
 * @param task - The preprocessed task with visibility flags
 * @returns Array of visible subtasks
 */
export function getVisibleSubTasks(task: VisibleHydratedTask): VisibleHydratedTask[] {
  return task.subTasksData?.filter((subtask) => subtask.isVisible) || [];
}
