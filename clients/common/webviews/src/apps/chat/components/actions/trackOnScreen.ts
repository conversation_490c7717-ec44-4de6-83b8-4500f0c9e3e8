import throttle from "lodash.throttle";

export interface VisibilityObserverOptions {
  scrollTarget: HTMLElement;
  onVisible?: () => void;
  onHidden?: () => void;
  threshold?: number;
  visibilityDuration?: number;
  throttleDuration?: number;
  ignoreScroll?: boolean;
}

/**
 * A Svelte action that observes an element's visibility on the screen and triggers a callback
 * when the element has been visible for a specified duration and the user has stopped scrolling.
 *
 * This action uses the Intersection Observer API to efficiently detect when an element enters
 * or leaves the viewport. It also handles scroll events to ensure the callback is only triggered
 * when the user has stopped scrolling.
 *
 * @param node - The HTML element to observe
 * @param options - Configuration options for the observer
 * @param options.onVisible - Callback function to execute when the element is visible
 * @param options.onHidden - Callback function to execute when the element is not visible
 * @param options.threshold - Percentage of the element that needs to be visible to trigger the observer (default: 0.25)
 * @param options.visibilityDuration - Duration in milliseconds the element needs to be visible before triggering the callback (default: 250ms)
 * @returns An object with a destroy method to clean up event listeners and timers
 */
export function visibilityObserver(node: HTMLElement, options: VisibilityObserverOptions) {
  const {
    onVisible,
    onHidden,
    scrollTarget,
    threshold = 0.25,
    visibilityDuration = 250,
    throttleDuration = 100,
    ignoreScroll = false,
  } = options;

  // State variables to track visibility and scrolling
  let isIntersecting = false; // Tracks whether the element is currently intersecting the viewport
  let visibilityTimer: ReturnType<typeof setTimeout> | null = null; // Timer for tracking visibility duration
  let isScrolling = false; // Tracks whether the user is currently scrolling
  let scrollTimer: ReturnType<typeof setTimeout> | null = null; // Timer for detecting when scrolling stops

  // Create an Intersection Observer to detect when the element is in view
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        isIntersecting = entry.isIntersecting;
        checkVisibility(); // Check visibility state whenever intersection state changes
      });
    },
    { threshold }, // Set the threshold for when the observer should trigger
  );

  const resetTimers = () => {
    if (visibilityTimer !== null) {
      clearTimeout(visibilityTimer);
      visibilityTimer = null;
    }
    if (scrollTimer !== null) {
      clearTimeout(scrollTimer);
      scrollTimer = null;
    }
  };

  // Throttled function to check visibility and trigger the callback
  // This function is throttled to prevent excessive calls during rapid changes
  const checkVisibility = throttle(() => {
    if (isIntersecting && !isScrolling) {
      // Element is visible and user is not scrolling
      if (visibilityTimer === null) {
        // Start the visibility timer if it's not already running. This queues up
        // the visibility to be updated -- if it is interrupted, the visibilityTimer
        // will be cleared and a new one will be set.
        visibilityTimer = setTimeout(() => {
          onVisible?.(); // Call the provided callback function
          visibilityTimer = null; // Reset the timer
        }, visibilityDuration);
      }
    } else {
      // Element is not visible or user is scrolling
      onHidden?.();
      resetTimers(); // Reset any pending timers
    }
  }, throttleDuration); // Throttle to run at most once every throttleDuration milliseconds

  // Handler for scroll events to detect when scrolling stops
  const handleScroll = throttle(() => {
    if (ignoreScroll) {
      return;
    }

    isScrolling = true; // Set scrolling flag
    resetTimers(); // Reset any pending timers

    // Set a new scroll timer to detect when scrolling stops
    scrollTimer = setTimeout(() => {
      isScrolling = false; // Reset scrolling flag
      checkVisibility(); // Check visibility after scrolling stops
    }, throttleDuration); // Wait throttleDuration ms after last scroll event to consider scrolling stopped
  }, throttleDuration);

  // Start observing the node
  observer.observe(node);

  // Add scroll event listener to the window
  scrollTarget.addEventListener("scroll", handleScroll, true);

  // Return a destroy function to clean up resources
  return {
    destroy() {
      observer.disconnect(); // Stop observing
      scrollTarget.removeEventListener("scroll", handleScroll); // Remove scroll listener
      // Clear any pending timers
      if (visibilityTimer !== null) {
        clearTimeout(visibilityTimer);
      }
      if (scrollTimer !== null) {
        clearTimeout(scrollTimer);
      }
    },
  };
}

/**
 * A Svelte action that observes an element's visibility on the screen and triggers a callback
 * when the element has been visible for a specified duration and the user has stopped scrolling.
 *
 * This action uses the Intersection Observer API to efficiently detect when an element enters
 * or leaves the viewport. It also handles scroll events to ensure the callback is only triggered
 * when the user has stopped scrolling.
 *
 * @param node - The HTML element to observe
 * @param options - Configuration options for the observer
 * @param options.onVisible - Callback function to execute when the element is visible
 * @param options.onHidden - Callback function to execute when the element is not visible
 * @param options.scrollTarget - The element to listen for scroll events on
 * @param options.threshold - Percentage of the element that needs to be visible to trigger the observer (default: 0.25)
 * @param options.visibilityDuration - Duration in milliseconds the element needs to be visible before triggering the callback (default: 250ms)
 * @returns An object with a destroy method to clean up event listeners and timers
 */
export function visibilityObserverOnce(node: HTMLElement, options: VisibilityObserverOptions) {
  const action = visibilityObserver(node, {
    ...options,
    onVisible: () => {
      if (options.onVisible) {
        options.onVisible();
        action.destroy();
      }
    },
    onHidden: () => {
      if (options.onHidden) {
        options.onHidden();
        action.destroy();
      }
    },
  });

  return {
    destroy: () => {
      action.destroy();
    },
  };
}
