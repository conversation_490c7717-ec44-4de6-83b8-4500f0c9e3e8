<script lang="ts">
  import RegularCloudIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/cloud-arrow-up.svg?component";

  export let hasUpdates: boolean = false;
</script>

<div class="remote-agent-icon">
  <RegularCloudIcon />
  {#if hasUpdates}
    <div class="update-indicator"></div>
  {/if}
</div>

<style>
  .remote-agent-icon {
    position: relative;
    flex: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 14px;
  }

  .update-indicator {
    position: absolute;
    top: -1px;
    right: -3px;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: currentColor;
  }
</style>
