<script lang="ts">
  import type {
    AutofixIteration,
    IConversationAutofixExtraData,
  } from "$vscode/src/autofix/autofix-state";

  export let iterationId: string;
  export let autofixData: IConversationAutofixExtraData;

  let iteration: AutofixIteration | undefined;

  $: iteration = autofixData.autofixIterations?.filter((it) => it.id === iterationId)[0];
</script>

<div class="c-autofix-details-log">
  {iteration?.commandOutput}
</div>

<style>
  .c-autofix-details-log {
    padding: var(--ds-spacing-3);
    white-space: pre-line;
  }
</style>
