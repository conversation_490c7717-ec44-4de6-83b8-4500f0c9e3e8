import { expect, describe, test, vi } from "vitest";
import { MentionStore } from "./store";
import type { IChatMentionable } from "$common-webviews/src/apps/chat/types/mention-option";
import { get } from "svelte/store";

class MentionStoreTestKit {
  constructor() {}

  public static createFiles(names: string[]): IChatMentionable[] {
    return names.map((name) => ({
      id: name,
      label: name,
      file: {
        repoRoot: "/home/<USER>/example-repo-root",
        pathName: `path/to/${name}`,
      },
    }));
  }
}

describe("MentionStore", () => {
  test("constructor", () => {
    const store = new MentionStore({
      onSelectItem: () => true,
      getSuggestions: () => Promise.resolve([]),
      render: {
        mentionHoverContents: vi.fn(),
        suggestionItem: vi.fn(),
      },
    });
    expect(store).toBeDefined();
  });

  describe("selection lifecycle", () => {
    test("trigger suggestions set correctly", async () => {
      const onSelectItem = vi.fn();
      const suggestions = MentionStoreTestKit.createFiles(["file1.txt", "file2.txt", "file3.txt"]);
      const store = new MentionStore({
        onSelectItem,
        getSuggestions: () => Promise.resolve(suggestions),
        render: {
          mentionHoverContents: vi.fn(),
          suggestionItem: vi.fn(),
        },
      });
      store.setActive(true);
      await store.tipTapExtension.options.suggestion.items?.({
        query: "file",
        editor: vi.fn(),
      } as any);
      const items = get(store.mentionSuggestionsData)?.items;
      expect(items).toEqual(suggestions);
    });

    test("onSelectItem called", async () => {
      const onSelectItem = vi.fn(() => true);
      const suggestions = MentionStoreTestKit.createFiles(["file1.txt", "file2.txt", "file3.txt"]);
      const store = new MentionStore({
        onSelectItem,
        getSuggestions: () => Promise.resolve(suggestions),
        render: {
          mentionHoverContents: vi.fn(),
          suggestionItem: vi.fn(),
        },
      });
      store.setActive(true);
      store.tipTapExtension.options.suggestion.render?.().onStart?.({
        editor: null,
        range: null,
        props: suggestions[0],
        command: (option: IChatMentionable) => {
          store.tipTapExtension.options.suggestion.command?.({
            editor: null,
            range: null,
            props: option,
          } as any);
        },
        clientRect: vi.fn(),
      } as any);
      await store.tipTapExtension.options.suggestion.items?.({
        query: "file",
        editor: vi.fn(),
      } as any);
      const items = get(store.mentionSuggestionsData)?.items;
      expect(items).toEqual(suggestions);

      expect(onSelectItem).not.toHaveBeenCalled();
      store.selectItem(suggestions[0]);
      expect(onSelectItem).toHaveBeenCalled();
    });

    test("onSelectItem not called", async () => {
      const onSelectItem = vi.fn(() => false);
      const suggestions = MentionStoreTestKit.createFiles(["file1.txt", "file2.txt", "file3.txt"]);
      const store = new MentionStore({
        onSelectItem,
        getSuggestions: () => Promise.resolve(suggestions),
        render: {
          mentionHoverContents: vi.fn(),
          suggestionItem: vi.fn(),
        },
      });
      store.setActive(true);
      store.tipTapExtension.options.suggestion.render?.().onStart?.({
        editor: null,
        range: null,
        props: suggestions[0],
        command: (option: IChatMentionable) => {
          store.tipTapExtension.options.suggestion.command?.({
            editor: null,
            range: null,
            props: option,
          } as any);
        },
        clientRect: vi.fn(),
      } as any);
      await store.tipTapExtension.options.suggestion.items?.({
        query: "file",
        editor: vi.fn(),
      } as any);
      const items = get(store.mentionSuggestionsData)?.items;
      expect(items).toEqual(suggestions);

      expect(onSelectItem).not.toHaveBeenCalled();
      store.selectItem(suggestions[0]);
      expect(onSelectItem).toHaveBeenCalled();
    });

    test("mentionSuggestionsData set correctly", async () => {
      const onSelectItem = vi.fn();
      const suggestions = MentionStoreTestKit.createFiles(["file1.txt", "file2.txt", "file3.txt"]);
      const store = new MentionStore({
        onSelectItem,
        getSuggestions: () => Promise.resolve(suggestions),
        render: {
          mentionHoverContents: vi.fn(),
          suggestionItem: vi.fn(),
        },
      });
      store.setActive(true);
      await store.tipTapExtension.options.suggestion.items?.({
        query: "file",
        editor: vi.fn(),
      } as any);
      const items = get(store.mentionSuggestionsData)?.items;
      expect(items).toEqual(suggestions);

      store.tipTapExtension.options.suggestion.render?.().onStart?.({
        editor: null,
        range: null,
        props: suggestions[0],
        command: (option: IChatMentionable) => {
          store.tipTapExtension.options.suggestion.command?.({
            editor: null,
            range: null,
            props: option,
          } as any);
        },
        clientRect: vi.fn(),
      } as any);

      store.selectItem(suggestions[0]);
      const suggestionData = get(store.mentionSuggestionsData);
      expect(suggestionData?.selectedIdx).toEqual(0);
    });

    test("inactive state does not update suggestions", async () => {
      const onSelectItem = vi.fn();
      const suggestions = MentionStoreTestKit.createFiles(["file1.txt", "file2.txt", "file3.txt"]);
      const store = new MentionStore({
        onSelectItem,
        getSuggestions: () => Promise.resolve(suggestions),
        render: {
          mentionHoverContents: vi.fn(),
          suggestionItem: vi.fn(),
        },
      });
      store.setActive(false);
      await store.tipTapExtension.options.suggestion.items?.({
        query: "file",
        editor: vi.fn(),
      } as any);
      const items = get(store.mentionSuggestionsData)?.items;
      expect(items).not.toBeDefined();
    });
  });
});
