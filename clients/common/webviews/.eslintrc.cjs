/* eslint-disable @typescript-eslint/naming-convention */

module.exports = {
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:svelte/recommended",
    "plugin:@vitest/legacy-recommended",
  ],
  env: {
    browser: true,
  },
  globals: {
    // These were added to make linting pass when adding eslint:recommended.
    // We may be able to remove them by improving our typing or updating
    // eslint version and/or config.
    NodeJS: true,
  },
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: 6,
    sourceType: "module",
    project: "./tsconfig.json",
    tsconfigRootDir: __dirname,
    extraFileExtensions: [".svelte"],
  },
  plugins: ["@typescript-eslint", "@vitest"],
  rules: {
    "@vitest/no-focused-tests": "error",
    "@typescript-eslint/naming-convention": "error",
    "@typescript-eslint/semi": "warn",
    curly: "warn",
    eqeqeq: ["error", "smart"],
    "no-throw-literal": "warn",
    semi: "off",
    "no-console": ["error", { allow: ["warn", "error"] }],
    "@typescript-eslint/no-unused-vars": ["error", { argsIgnorePattern: "^_" }],
    "@typescript-eslint/consistent-type-imports": [
      "error",
      { prefer: "type-imports", fixStyle: "inline-type-imports" },
    ],

    // The following rules were disabled due to errors when adding
    // eslint:recommended.
    // Please feel free to enable the rule if you want to.
    "no-var": "off",
    "no-constant-condition": "off",
    "no-async-promise-executor": "off",
    "prefer-const": "off",
    "@typescript-eslint/no-extra-semi": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/ban-types": "off",
    "@typescript-eslint/no-non-null-assertion": "off",
    "@typescript-eslint/no-var-requires": "off",
    "@typescript-eslint/no-empty-function": "off",
    "@typescript-eslint/no-inferrable-types": "off",
    "@typescript-eslint/no-unnecessary-type-constraint": "off",
  },
  ignorePatterns: [
    "out",
    "dist",
    "**/*.d.ts",
    ".eslintrc.cjs",
    "svelte.config.js",
    "vite.config.ts",
    "build-smoke-test.js",
    "scripts",
  ],
  overrides: [
    {
      files: ["*.svelte"],
      parser: "svelte-eslint-parser",
      parserOptions: {
        project: true,
        tsconfigRootDir: __dirname,
        extraFileExtensions: [".svelte"],
        parser: "@typescript-eslint/parser",
        // Adds support for svelte generics in typescript. For more details, see:
        // https://github.com/sveltejs/svelte-eslint-parser/pull/477
        svelteFeatures: {
          experimentalGenerics: true,
        },
      },
      rules: {
        // These errors seem to occur due to type misunderstandings between
        // svelte and typescript.
        "@typescript-eslint/no-unsafe-member-access": "off",
        "@typescript-eslint/no-unsafe-argument": "off",
        "@typescript-eslint/no-unsafe-assignment": "off",
        "@typescript-eslint/no-unsafe-call": "off",
        "@typescript-eslint/no-unsafe-return": "off",
        "@typescript-eslint/no-redundant-type-constituents": "off",
      },
    },
    {
      extends: ["plugin:@vitest/legacy-recommended"],
      files: [
        "**/__tests__/**/*",
        "**/__mocks__/**/*",
        "**/mocks/**/*",
        "**/*.test.*",
        "build-smoke-test.js",
      ],
      env: {
        jest: true,
      },
      rules: {
        "no-console": "off",

        // We use expect.any(<T>) in our tests which returns an `any` value.
        "@typescript-eslint/no-unsafe-assignment": "off",

        "@typescript-eslint/no-namespace": "off",
        "@typescript-eslint/no-empty-interface": "off",
        "@typescript-eslint/no-non-null-asserted-optional-chain": "off",
      },
    },
    {
      // TODO (mattgauntseo): Make this the default
      extends: ["plugin:@typescript-eslint/recommended-type-checked"],
      files: ["src/design-system/**/*.ts"],
      rules: {},
    },
    {
      extends: ["plugin:@vitest/legacy-recommended"],
      files: ["src/design-system/**/*.test.ts"],
      env: {
        jest: true,
      },
      rules: {
        "no-console": "off",

        // We use expect.any(<T>) in our tests which returns an `any` value.
        "@typescript-eslint/no-unsafe-assignment": "off",

        // When mocking props, we might have defined an option parameter and
        // rely on this in tests, but TS complains.
        "@typescript-eslint/no-unsafe-member-access": "off",
        "@typescript-eslint/no-unsafe-call": "off",

        // Allow tests to make async functions that don't await anything
        "@typescript-eslint/require-await": "off",

        "@typescript-eslint/no-namespace": "off",
        "@typescript-eslint/no-empty-interface": "off",
        "@typescript-eslint/no-non-null-asserted-optional-chain": "off",
      },
    },
  ],
};
