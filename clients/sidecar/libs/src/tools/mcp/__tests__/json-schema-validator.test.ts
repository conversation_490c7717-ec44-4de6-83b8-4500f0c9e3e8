import {
  validateJsonSchema,
  JsonSchemaValidationError,
} from "../json-schema-validator";
import { type AugmentLogger } from "../../../logging";

// Mock the logger
const mockLogger: AugmentLogger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  log: jest.fn(),
  verbose: jest.fn(),
};

jest.mock("../../../logging", () => ({
  getLogger: jest.fn().mockReturnValue(mockLogger),
}));

describe("JSON Schema Validator", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("validateJsonSchema", () => {
    it("should validate a simple valid schema", () => {
      const schema = {
        type: "object",
        properties: {
          name: { type: "string" },
          age: { type: "integer" },
        },
        required: ["name"],
      };

      expect(() => {
        validateJsonSchema(schema, "TestSchema", mockLogger);
      }).not.toThrow();

      expect(mockLogger.debug).toHaveBeenCalled();
      expect(mockLogger.error).not.toHaveBeenCalled();
    });

    it("should throw an error for non-object schema", () => {
      const schema = "not an object";

      expect(() => {
        validateJsonSchema(schema, "TestSchema", mockLogger);
      }).toThrow(JsonSchemaValidationError);

      expect(mockLogger.error).toHaveBeenCalled();
      expect(mockLogger.debug).not.toHaveBeenCalled();
    });

    it("should validate Spotify playlist schema", () => {
      const spotifyPlaylistSchema = {
        type: "object",
        properties: {
          action: {
            description:
              "Action to perform: 'get', 'get_tracks', 'add_tracks', 'remove_tracks', 'change_details'.",
            title: "Action",
            type: "string",
          },
          playlist_id: {
            anyOf: [{ type: "string" }, { type: "null" }],
            default: null,
            description: "ID of the playlist to manage.",
            title: "Playlist Id",
          },
          track_ids: {
            anyOf: [
              {
                items: { type: "string" },
                type: "array",
              },
              { type: "null" },
            ],
            default: null,
            description: "List of track IDs to add/remove.",
            title: "Track Ids",
          },
          name: {
            anyOf: [{ type: "string" }, { type: "null" }],
            default: null,
            description: "New name for the playlist.",
            title: "Name",
          },
          description: {
            anyOf: [{ type: "string" }, { type: "null" }],
            default: null,
            description: "New description for the playlist.",
            title: "Description",
          },
        },
        description:
          "Manage Spotify playlists.\n- get: Get a list of user's playlists.\n- get_tracks: Get tracks in a specific playlist.\n- add_tracks: Add tracks to a specific playlist.\n- remove_tracks: Remove tracks from a specific playlist.\n- change_details: Change details of a specific playlist.",
        required: ["action"],
        title: "Playlist",
      };

      expect(() => {
        validateJsonSchema(
          spotifyPlaylistSchema,
          "SpotifyPlaylist",
          mockLogger,
        );
      }).not.toThrow();

      expect(mockLogger.debug).toHaveBeenCalled();
      expect(mockLogger.error).not.toHaveBeenCalled();
    });
    it("should validate Schema with $schema passed in", () => {
      // This schema has an invalid type that will be caught during validation
      const schemaWithDraft07 = {
        type: "object",
        properties: {
          context7CompatibleLibraryID: {
            type: "string",
            description:
              "Exact Context7-compatible library ID (e.g., 'mongodb/docs', 'vercel/nextjs') retrieved from 'resolve-library-id'.",
          },
          topic: {
            type: "string",
            description:
              "Topic to focus documentation on (e.g., 'hooks', 'routing').",
          },
          tokens: {
            type: "number",
            description:
              "Maximum number of tokens of documentation to retrieve (default: 10000). Higher values provide more context but consume more tokens.",
          },
        },
        required: ["context7CompatibleLibraryID"],
        additionalProperties: false,
        $schema: "http://json-schema.org/draft-07/schema#",
      };

      expect(() => {
        validateJsonSchema(schemaWithDraft07, "TypeErrorSchema", mockLogger);
      }).not.toThrow();

      // Should log a warning about type error
      expect(mockLogger.debug).toHaveBeenCalled();
      expect(mockLogger.error).not.toHaveBeenCalled();
    });

    it("should throw error on schema with 'int' type", () => {
      const schemaWithIntType = {
        type: "object",
        properties: {
          source: {
            type: "string",
            description: "URL or local file path to the document",
          },
          no_of_qnas: {
            type: "int",
            description: "Number of Q&A to generate",
          },
        },
        required: ["source"],
      };

      expect(() => {
        validateJsonSchema(schemaWithIntType, "QnAGenerator", mockLogger);
      }).toThrow();

      // Should log a warning about converting 'int' to 'integer'
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          "Invalid schema for tool QnAGenerator: schema is invalid",
        ),
      );
    });

    it("should handle schema with meta/core references", () => {
      const schemaWithMetaCoreRef = {
        type: "object",
        properties: {
          name: {
            type: "string",
            $ref: "meta/core#/definitions/stringType",
          },
        },
      };

      expect(() => {
        validateJsonSchema(
          schemaWithMetaCoreRef,
          "MetaCoreRefSchema",
          mockLogger,
        );
      }).toThrow();

      // Should log a warning about meta/core reference
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          "Invalid schema for tool MetaCoreRefSchema: can't resolve reference",
        ),
      );
    });

    it("should handle schema with type errors", () => {
      // This schema has an invalid type that will be caught during validation
      const schemaWithTypeError = {
        type: "object",
        properties: {
          value: {
            type: 123, // Type should be a string or array of strings
            description: "A value with invalid type definition",
          },
        },
      };

      expect(() => {
        validateJsonSchema(schemaWithTypeError, "TypeErrorSchema", mockLogger);
      }).toThrow();

      // Should log a warning about type error
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          "Invalid schema for tool TypeErrorSchema: schema is invalid",
        ),
      );
    });

    it("should validate schema with date-time format", () => {
      const schemaWithDateTimeFormat = {
        type: "object",
        properties: {
          eventName: {
            type: "string",
            description: "Name of the event",
          },
          scheduledTime: {
            type: "string",
            format: "date-time",
            description: "When the event is scheduled in ISO 8601 format",
          },
          location: {
            type: "string",
            description: "Location of the event",
          },
        },
        required: ["eventName", "scheduledTime"],
      };

      expect(() => {
        validateJsonSchema(
          schemaWithDateTimeFormat,
          "EventSchema",
          mockLogger,
        );
      }).not.toThrow();

      expect(mockLogger.debug).toHaveBeenCalled();
      expect(mockLogger.error).not.toHaveBeenCalled();
    });
  });
});
