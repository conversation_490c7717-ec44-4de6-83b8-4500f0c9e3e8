import {
  RemoteToolId,
  ToolAvailabilityStatus,
  ToolSafety,
} from "../../tool-types";
import { RemoteToolHost } from "../remote-tool-host";
import * as toolUtils from "../../tool-utils";
import { MockClientWorkspaces } from "@augment-internal/sidecar-libs/src/__tests__/mocks/mock-client-workspaces";
import { RemoteToolDefinition } from "../remote-tool-host";
import { setLibraryClientWorkspaces } from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";

jest.mock("../../tool-utils", () => ({
  executeCommand: jest.fn(),
}));

describe("RemoteToolHost", () => {
  const mockRemoteSourceInfo = {
    retrieveRemoteTools: jest.fn().mockResolvedValue([]),
    filterToolsWithExtraInput: jest.fn().mockReturnValue(new Set()),
    runRemoteTool: jest.fn().mockResolvedValue({
      toolOutput: "Not implemented",
      toolResultMessage: "Not implemented",
      isError: true,
    }),
    checkToolSafety: jest.fn().mockResolvedValue(false),
  };

  const mockClientWorkspaces = new MockClientWorkspaces();
  mockClientWorkspaces.getCwd.mockReturnValue(Promise.resolve("/mock/cwd"));

  beforeEach(() => {
    jest.clearAllMocks();
    // Setup mock git command responses
    (toolUtils.executeCommand as jest.Mock).mockImplementation(
      (command: string) => {
        if (command === "git rev-parse --show-toplevel") {
          return Promise.resolve("/mock/git/root");
        }
        switch (command) {
          case "git config --get remote.origin.url":
            return Promise.resolve("https://github.com/org/repo.git");
          case "git rev-parse --abbrev-ref HEAD":
            return Promise.resolve("main");
          case "git rev-parse --abbrev-ref origin/HEAD":
            return Promise.resolve("origin/main");
          case "git config --get user.email":
            return Promise.resolve("<EMAIL>");
          default:
            return Promise.resolve(undefined);
        }
      },
    );

    setLibraryClientWorkspaces(mockClientWorkspaces);
  });

  it("return tools that have extra input", async () => {
    mockRemoteSourceInfo.retrieveRemoteTools.mockResolvedValue([
      {
        toolDefinition: {
          name: "tool1",
          description: "Tool 1",
          input_schema_json: "{}",
          tool_safety: ToolSafety.Safe,
        },
        remoteToolId: RemoteToolId.WebSearch,
        availabilityStatus: ToolAvailabilityStatus.Available,
        toolSafety: ToolSafety.Safe,
        oauthUrl: "https://tool1.com/oauth",
      },
      {
        toolDefinition: {
          name: "tool2",
          description: "Tool 2",
          input_schema_json: "{}",
          tool_safety: ToolSafety.Safe,
        },
        remoteToolId: RemoteToolId.Jira,
        availabilityStatus: ToolAvailabilityStatus.UserConfigRequired,
        toolSafety: ToolSafety.Safe,
        oauthUrl: "https://tool2.com/oauth",
      },
      {
        toolDefinition: {
          name: "tool3",
          description: "Tool 3",
          input_schema_json: "{}",
          tool_safety: ToolSafety.Safe,
        },
        remoteToolId: RemoteToolId.Confluence,
        availabilityStatus: ToolAvailabilityStatus.UserConfigRequired,
        toolSafety: ToolSafety.Safe,
        oauthUrl: "https://tool3.com/oauth",
      },
    ] satisfies RemoteToolDefinition[]);
    mockRemoteSourceInfo.filterToolsWithExtraInput.mockReturnValue(
      new Set([RemoteToolId.Jira]),
    );

    const host = new RemoteToolHost(mockRemoteSourceInfo);
    const tools = await host.getToolDefinitions();

    expect(mockRemoteSourceInfo.filterToolsWithExtraInput).toHaveBeenCalledWith(
      [RemoteToolId.Jira, RemoteToolId.Confluence],
    );

    expect(tools.length).toBe(2);
    expect(tools.some((tool) => tool.definition.name === "tool1")).toBe(true);
    expect(tools.some((tool) => tool.definition.name === "tool2")).toBe(true);
  });

  it("applies transformations to matching tools", async () => {
    const mockTools = [
      {
        toolDefinition: {
          name: "github",
          description: "GitHub API",
          input_schema_json: "{}",
          tool_safety: ToolSafety.Safe,
        },
        remoteToolId: RemoteToolId.GitHubApi,
        availabilityStatus: ToolAvailabilityStatus.Available,
        toolSafety: ToolSafety.Safe,
      },
      {
        toolDefinition: {
          name: "jira",
          description: "Jira API",
          input_schema_json: "{}",
          tool_safety: ToolSafety.Safe,
        },
        remoteToolId: RemoteToolId.Jira,
        availabilityStatus: ToolAvailabilityStatus.Available,
        toolSafety: ToolSafety.Safe,
      },
    ];

    mockRemoteSourceInfo.retrieveRemoteTools.mockResolvedValue(mockTools);

    const host = new RemoteToolHost(mockRemoteSourceInfo);
    const tools = await host.getToolDefinitions();

    expect(tools.length).toBe(2);
    const githubTool = tools.find((t) => t.definition.name === "github");
    const jiraTool = tools.find((t) => t.definition.name === "jira");

    // Verify git commands execution order and options
    // First call should use initial CWD
    expect(toolUtils.executeCommand).toHaveBeenNthCalledWith(
      1,
      "git rev-parse --show-toplevel",
      expect.objectContaining({ cwd: "/mock/cwd" }),
    );

    // Verify that subsequent git commands use the git root
    const gitRootOptions = { cwd: "/mock/git/root", timeout: 1000 };
    expect(toolUtils.executeCommand).toHaveBeenCalledWith(
      "git config --get remote.origin.url",
      gitRootOptions,
    );
    expect(toolUtils.executeCommand).toHaveBeenCalledWith(
      "git rev-parse --abbrev-ref HEAD",
      gitRootOptions,
    );
    expect(toolUtils.executeCommand).toHaveBeenCalledWith(
      "git rev-parse --abbrev-ref origin/HEAD",
      gitRootOptions,
    );
    expect(toolUtils.executeCommand).toHaveBeenCalledWith(
      "git config --get user.email",
      gitRootOptions,
    );

    // Verify presence of git information values
    const description = githubTool?.definition.description || "";
    expect(description).toContain("/mock/git/root");
    expect(description).toContain("https://github.com/org/repo.git");
    expect(description).toContain("<EMAIL>");
    expect(description).toContain("main");

    expect(jiraTool?.definition.description).toBe("Jira API");
  });

  it("uses provided cwd function", async () => {
    const mockTools = [
      {
        toolDefinition: {
          name: "github",
          description: "GitHub API",
          input_schema_json: "{}",
          tool_safety: ToolSafety.Safe,
        },
        remoteToolId: RemoteToolId.GitHubApi,
        availabilityStatus: ToolAvailabilityStatus.Available,
        toolSafety: ToolSafety.Safe,
      },
    ];

    mockRemoteSourceInfo.retrieveRemoteTools.mockResolvedValue(mockTools);
    const host = new RemoteToolHost(mockRemoteSourceInfo);
    await host.getToolDefinitions();

    expect(mockClientWorkspaces.getCwd).toHaveBeenCalled();
  });

  it("handles missing git email configuration", async () => {
    // Mock git commands but return undefined for email
    (toolUtils.executeCommand as jest.Mock).mockImplementation(
      (command: string) => {
        if (command === "git rev-parse --show-toplevel") {
          return Promise.resolve("/mock/git/root");
        }
        switch (command) {
          case "git config --get remote.origin.url":
            return Promise.resolve("https://github.com/org/repo.git");
          case "git rev-parse --abbrev-ref HEAD":
            return Promise.resolve("main");
          case "git rev-parse --abbrev-ref origin/HEAD":
            return Promise.resolve("origin/main");
          case "git config --get user.email":
            return Promise.resolve(undefined); // Email not configured
          default:
            return Promise.resolve(undefined);
        }
      },
    );

    const mockTools = [
      {
        toolDefinition: {
          name: "github",
          description: "GitHub API",
          input_schema_json: "{}",
          tool_safety: ToolSafety.Safe,
        },
        remoteToolId: RemoteToolId.GitHubApi,
        availabilityStatus: ToolAvailabilityStatus.Available,
        toolSafety: ToolSafety.Safe,
        oauthUrl: "https://github.com/oauth",
      },
    ] satisfies RemoteToolDefinition[];

    mockRemoteSourceInfo.retrieveRemoteTools.mockResolvedValue(mockTools);

    const host = new RemoteToolHost(mockRemoteSourceInfo);
    const tools = await host.getToolDefinitions();

    expect(tools.length).toBe(1);
    const githubTool = tools[0];

    const description = githubTool.definition.description;
    expect(description).toContain("/mock/git/root");
    expect(description).toContain("https://github.com/org/repo.git");
    expect(description).toContain("main");

    // Verify email is not included
    expect(description).not.toContain("<EMAIL>");
  });

  it("includes request_id in tool response", async () => {
    // Setup mock responses
    const mockTools = [
      {
        toolDefinition: {
          name: "github",
          description: "GitHub API",
          input_schema_json: "{}",
          tool_safety: ToolSafety.Safe,
        },
        remoteToolId: RemoteToolId.GitHubApi,
        availabilityStatus: ToolAvailabilityStatus.Available,
        toolSafety: ToolSafety.Safe,
        oauthUrl: "https://github.com/oauth",
      },
    ] satisfies RemoteToolDefinition[];

    mockRemoteSourceInfo.retrieveRemoteTools.mockResolvedValue(mockTools);
    mockRemoteSourceInfo.runRemoteTool.mockResolvedValue({
      toolOutput: "Tool output",
      toolResultMessage: "Success",
      status: 1, // ExecutionSuccess
    });

    const host = new RemoteToolHost(mockRemoteSourceInfo);
    await host.getToolDefinitions();
    const result = await host.callTool(
      "chat-request-id",
      "tool-use-id",
      "github",
      { param: "value" },
      [],
    );

    // Verify the result contains the requestId
    expect(result.text).toBe("Tool output");
    expect(result.isError).toBe(false);
    expect(result.requestId).toBeDefined();

    // Verify the runRemoteTool was called with a request ID
    expect(mockRemoteSourceInfo.runRemoteTool).toHaveBeenCalledWith(
      result.requestId, // Should be the same as the one in the result
      "github",
      JSON.stringify({ param: "value" }),
      RemoteToolId.GitHubApi,
      expect.any(AbortSignal),
    );
  });
});
