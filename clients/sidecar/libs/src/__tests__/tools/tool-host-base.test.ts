import { ToolHostBase, ITool } from "../../tools/tool-host-base";
import {
  ToolHostName,
  ToolSafety,
  ToolUseResponse,
  LocalToolType,
} from "../../tools/tool-types";
import { Exchange } from "../../chat/chat-types";
import { IToolHost } from "../../tools/tool-host";

describe("ToolHostBase", () => {
  const MOCK_TOOL_DELAY = 10;

  // Use LocalToolType for testing instead of a custom enum
  // This ensures we're using the same types as production code

  // Mock tool implementation
  class MockToolImpl implements ITool<LocalToolType> {
    constructor(
      public readonly name: LocalToolType,
      public readonly toolSafety: ToolSafety = ToolSafety.Safe,
      public readonly description: string = "Mock tool description",
      public readonly inputSchemaJson: string = '{"type":"object","properties":{}}',
    ) {}

    checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
      return this.toolSafety === ToolSafety.Safe;
    }

    async call(
      _toolInput: Record<string, unknown>,
      _chatHistory: Exchange[],
      abortSignal: AbortSignal,
    ): Promise<ToolUseResponse> {
      // Use a promise that can be aborted
      try {
        await new Promise((resolve, reject) => {
          const abort = () => reject(new Error("Aborted"));
          abortSignal.addEventListener("abort", abort);
          jest.advanceTimersByTime(MOCK_TOOL_DELAY);
          resolve(undefined);
        });
      } catch (e) {
        return { isError: true, text: "Cancelled" };
      }

      if (abortSignal.aborted) {
        return { isError: true, text: "Cancelled" };
      }
      return { isError: false, text: "Success" };
    }
  }

  class MockToolHost extends ToolHostBase<LocalToolType> {
    factory(_preconditionWait: Promise<void>): IToolHost {
      return new MockToolHost(this._tools, ToolHostName.localToolHost);
    }
  }

  let mockTool1: ITool<LocalToolType>;
  let mockTool2: ITool<LocalToolType>;
  let toolHost: MockToolHost;

  beforeEach(() => {
    jest.useFakeTimers();
    mockTool1 = new MockToolImpl(LocalToolType.readFile);
    mockTool2 = new MockToolImpl(LocalToolType.saveFile, ToolSafety.Unsafe);
    toolHost = new MockToolHost(
      [mockTool1, mockTool2],
      ToolHostName.localToolHost,
    );
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe("getToolDefinitions", () => {
    it("should return tool definitions for all tools", async () => {
      const definitions = await toolHost.getToolDefinitions();

      expect(definitions).toHaveLength(2);
      expect(definitions[0]).toEqual({
        definition: {
          name: LocalToolType.readFile,
          description: "Mock tool description",
          input_schema_json: '{"type":"object","properties":{}}',
          tool_safety: ToolSafety.Safe,
        },
        identifier: {
          toolId: LocalToolType.readFile,
          hostName: ToolHostName.localToolHost,
        },
        isConfigured: true,
        enabled: true,
        toolSafety: ToolSafety.Safe,
      });
      expect(definitions[1]).toEqual({
        definition: {
          name: LocalToolType.saveFile,
          description: "Mock tool description",
          input_schema_json: '{"type":"object","properties":{}}',
          tool_safety: ToolSafety.Unsafe,
        },
        identifier: {
          toolId: LocalToolType.saveFile,
          hostName: ToolHostName.localToolHost,
        },
        isConfigured: true,
        enabled: true,
        toolSafety: ToolSafety.Unsafe,
      });
    });
  });

  describe("callTool", () => {
    it("should successfully call an existing tool", async () => {
      const resultPromise = toolHost.callTool(
        "request1",
        "toolUse1",
        LocalToolType.readFile.toString(),
        {},
        [],
      );

      jest.advanceTimersByTime(MOCK_TOOL_DELAY);
      const result = await resultPromise;

      expect(result).toEqual({ isError: false, text: "Success" });
    });

    it("should return error for non-existent tool", async () => {
      const result = await toolHost.callTool(
        "request1",
        "toolUse1",
        "nonexistent",
        {},
        [],
      );

      expect(result).toEqual({
        isError: true,
        text: "Tool nonexistent not found.",
      });
    });

    it("should handle tool cancellation", async () => {
      // Start a tool call
      const toolPromise = toolHost.callTool(
        "request1",
        "toolUse1",
        LocalToolType.readFile.toString(),
        {},
        [],
      );

      // Close the tool host, which should cancel the running tool
      await toolHost.close(true);

      jest.advanceTimersByTime(MOCK_TOOL_DELAY);
      const result = await toolPromise;

      expect(result).toEqual({ isError: true, text: "Cancelled" });
    });

    it("should clear running tool after completion", async () => {
      const toolPromise = toolHost.callTool(
        "request1",
        "toolUse1",
        LocalToolType.readFile.toString(),
        {},
        [],
      );

      jest.advanceTimersByTime(MOCK_TOOL_DELAY);
      await toolPromise;

      expect(toolHost.isRequestActive("request1", "toolUse1")).toBe(false);
    });

    it("should clear running tool after error", async () => {
      await toolHost.callTool("request1", "toolUse1", "nonexistent", {}, []);

      expect(toolHost.isRequestActive("request1", "toolUse1")).toBe(false);
    });
  });

  describe("checkToolCallSafe", () => {
    it("should return true for safe tool", async () => {
      const isSafe = await toolHost.checkToolCallSafe(
        LocalToolType.readFile.toString(),
        {},
      );
      expect(isSafe).toBe(true);
    });

    it("should return false for unsafe tool", async () => {
      const isSafe = await toolHost.checkToolCallSafe(
        LocalToolType.saveFile.toString(),
        {},
      );
      expect(isSafe).toBe(false);
    });

    it("should return false for non-existent tool", async () => {
      const isSafe = await toolHost.checkToolCallSafe("nonexistent", {});
      expect(isSafe).toBe(false);
    });
  });

  describe("isRequestActive", () => {
    it("should track active request during tool execution", async () => {
      // Start tool execution without awaiting
      const toolPromise = toolHost.callTool(
        "request1",
        "toolUse1",
        LocalToolType.readFile.toString(),
        {},
        [],
      );

      // Check if request is active before advancing time
      expect(toolHost.isRequestActive("request1", "toolUse1")).toBe(true);
      expect(toolHost.isRequestActive("request2", "toolUse1")).toBe(false);
      expect(toolHost.isRequestActive("request1", "toolUse2")).toBe(false);

      // Complete the tool execution
      jest.advanceTimersByTime(MOCK_TOOL_DELAY);
      await toolPromise;
    });
  });

  describe("close", () => {
    it("should cancel running tool", async () => {
      // Start a tool execution
      const toolPromise = toolHost.callTool(
        "request1",
        "toolUse1",
        LocalToolType.readFile.toString(),
        {},
        [],
      );

      // Close the tool host
      await toolHost.close(true);

      // Advance time and verify tool was cancelled
      jest.advanceTimersByTime(MOCK_TOOL_DELAY);
      const result = await toolPromise;
      expect(result).toEqual({ isError: true, text: "Cancelled" });
    });

    it("should work when no tool is running", async () => {
      await expect(toolHost.close(true)).resolves.toBeUndefined();
    });
  });
});
