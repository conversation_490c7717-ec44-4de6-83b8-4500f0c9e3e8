import { MetricsReporter } from "@augment-internal/sidecar-libs/src/metrics/metrics-reporter";
import {
  AgentSessionEventData,
  AgentTracingData,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import { msecToTimestamp } from "@augment-internal/sidecar-libs/src/utils/time";

import { getAPIClient } from "../client-interfaces/api-client";
import { AgentSessionEvent } from "../client-interfaces/api-client-types";
import { getClientFeatureFlags } from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";

export class AgentSessionEventReporter extends MetricsReporter<AgentSessionEvent> {
  private static maxRecords = 10000;
  private static batchSize = 1000;
  private static uploadMsec = 10000;

  private static instance: AgentSessionEventReporter | null = null;

  /**
   * Gets the singleton instance of AgentSessionEventReporter
   * @param featureFlagManager The feature flag manager
   * @param maxRecords Optional maximum number of records
   * @param uploadMs Optional upload interval in milliseconds
   * @param batchSize Optional batch size
   * @returns The singleton instance of AgentSessionEventReporter
   */
  public static getInstance(): AgentSessionEventReporter {
    if (!AgentSessionEventReporter.instance) {
      AgentSessionEventReporter.instance = new AgentSessionEventReporter();
      AgentSessionEventReporter.instance.enableUpload();
    }
    return AgentSessionEventReporter.instance;
  }

  public static reset(): void {
    if (AgentSessionEventReporter.instance) {
      AgentSessionEventReporter.instance.dispose();
      AgentSessionEventReporter.instance = null;
    }
  }

  private constructor() {
    super(
      "AgentSessionEventReporter",
      AgentSessionEventReporter.maxRecords,
      AgentSessionEventReporter.uploadMsec,
      AgentSessionEventReporter.batchSize,
    );
  }

  private formatTracingData<DebugFlag extends string>(
    tracingData: AgentTracingData<DebugFlag>,
  ) {
    return {
      flags: tracingData.flags,
      nums: tracingData.nums,
      /* eslint-disable @typescript-eslint/naming-convention */
      string_stats: tracingData.string_stats,
      request_ids: tracingData.request_ids,
      /* eslint-enable @typescript-eslint/naming-convention */
    };
  }

  // reportEvent reports a user agent event.
  public reportEvent(eventData: AgentSessionEventData): void {
    const [eventTimeSec, eventTimeNsec] = msecToTimestamp(Date.now());

    let eventDataPayload = undefined;
    /* eslint-disable @typescript-eslint/naming-convention */
    const enable_memories_tracing =
      getClientFeatureFlags().flags.memoriesParams.enable_memories_tracing;

    if (eventData.eventData?.agentReversionData) {
      eventDataPayload = {
        agent_reversion_data: {
          // Leaving this object empty for now as the fields are still in flux.
          // See comment in `clients/vscode/src/metrics/types.ts` for context.
        },
      };
    }
    if (eventData.eventData?.agentInterruptionData) {
      eventDataPayload = {
        agent_interruption_data: {
          request_id: eventData.eventData.agentInterruptionData.requestId,
          curr_conversation_length:
            eventData.eventData.agentInterruptionData.currConversationLength,
        },
      };
    }
    if (eventData.eventData?.rememberToolCallData) {
      if (!enable_memories_tracing) return;
      eventDataPayload = {
        remember_tool_call_data: {
          caller: eventData.eventData.rememberToolCallData.caller,
          is_complex_new_memory:
            eventData.eventData.rememberToolCallData.is_complex_new_memory,
          tracing_data: this.formatTracingData(
            eventData.eventData.rememberToolCallData.tracingData,
          ),
        },
      };
    }
    if (eventData.eventData?.initialOrientationData) {
      if (!enable_memories_tracing) return;
      eventDataPayload = {
        initial_orientation_data: {
          caller: eventData.eventData.initialOrientationData.caller,
          tracing_data: this.formatTracingData(
            eventData.eventData.initialOrientationData.tracingData,
          ),
        },
      };
    }
    if (eventData.eventData?.classifyAndDistillData) {
      if (!enable_memories_tracing) return;
      eventDataPayload = {
        classify_and_distill_data: {
          tracing_data: this.formatTracingData(
            eventData.eventData.classifyAndDistillData.tracingData,
          ),
        },
      };
    }
    if (eventData.eventData?.flushMemoriesData) {
      if (!enable_memories_tracing) return;
      eventDataPayload = {
        flush_memories_data: {
          tracing_data: this.formatTracingData(
            eventData.eventData.flushMemoriesData.tracingData,
          ),
        },
      };
    }
    if (eventData.eventData?.memoriesFileOpenData) {
      eventDataPayload = {
        memories_file_open_data: {
          memories_path_undefined:
            eventData.eventData.memoriesFileOpenData.memoriesPathUndefined,
        },
      };
    }
    if (eventData.eventData?.memoriesMoveData) {
      eventDataPayload = {
        memories_move_data: {
          target: eventData.eventData.memoriesMoveData.target,
        },
      };
    }
    if (eventData.eventData?.rulesImportedData) {
      eventDataPayload = {
        rules_imported_data: {
          type: eventData.eventData.rulesImportedData.type,
          num_files: eventData.eventData.rulesImportedData.numFiles,
          source: eventData.eventData.rulesImportedData.source,
        },
      };
    }

    this.report({
      event_time_sec: eventTimeSec,
      event_time_nsec: eventTimeNsec,
      event_name: eventData.eventName,
      conversation_id: eventData.conversationId || "unknown",
      event_data: eventDataPayload,
    });
    /* eslint-enable @typescript-eslint/naming-convention */
  }

  protected performUpload(batch: AgentSessionEvent[]): Promise<void> {
    return getAPIClient().logAgentSessionEvent(batch);
  }
}

/**
 * Gets the singleton instance of AgentSessionEventReporter
 * @param featureFlagManager The feature flag manager
 * @returns The singleton instance of AgentSessionEventReporter
 */
export function getAgentSessionEventReporter(): AgentSessionEventReporter {
  return AgentSessionEventReporter.getInstance();
}

export function resetAgentSessionEventReporter() {
  AgentSessionEventReporter.reset();
}
