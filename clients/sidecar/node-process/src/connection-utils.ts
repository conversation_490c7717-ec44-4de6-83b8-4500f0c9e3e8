import { CancellationTokenSource, Connection } from "vscode-languageserver";
import { getLogger } from "./logging";
import { JsonValue } from "@bufbuild/protobuf";

const DEFAULT_TIMEOUT_MS = 10000;

const logger = getLogger("connection-utils");

export function sendRequestWithTimeout(
  connection: Connection,
  method: string,
  params?: JsonValue,
  options: { timeoutMs?: number } = {},
) {
  const tokenSource = new CancellationTokenSource();

  // Set up timeout
  const timeoutId = setTimeout(() => {
    logger.warn(`Cancelling request due to timeout: ${method}`);
    tokenSource.cancel();
  }, options.timeoutMs ?? DEFAULT_TIMEOUT_MS);

  return connection
    .sendRequest<JsonValue>(method, params, tokenSource.token)
    .finally(() => {
      clearTimeout(timeoutId);
    });
}
