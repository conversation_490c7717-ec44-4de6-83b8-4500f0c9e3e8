import { LaunchProcessTool } from "../launch-process-tool";

describe("LaunchProcessTool", () => {
  let tool: LaunchProcessTool;

  beforeEach(() => {
    tool = new LaunchProcessTool();
  });

  describe("checkToolCallSafe", () => {
    it("should return true for allowlisted basic commands", () => {
      expect(tool.checkToolCallSafe({ command: "ls" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "pwd" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "date" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "whoami" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "cat file.txt" })).toBe(true);
    });

    it("should return true for allowlisted git commands", () => {
      expect(tool.checkToolCallSafe({ command: "git status" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "git log" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "git diff" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "git show" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "git branch" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "git log --oneline" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "git diff HEAD~1" })).toBe(true);
    });

    it("should return false for dangerous git commands", () => {
      expect(tool.checkToolCallSafe({ command: "git push" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: "git commit" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: "git reset" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: "git branch -D" })).toBe(false);
    });

    it("should return false for commands with dangerous characters", () => {
      expect(tool.checkToolCallSafe({ command: "ls | grep test" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: "cat file.txt > output.txt" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: "ls && rm file" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: "echo $(whoami)" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: "ls; rm file" })).toBe(false);
    });

    it("should return false for unknown commands", () => {
      expect(tool.checkToolCallSafe({ command: "rm file.txt" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: "sudo ls" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: "unknown_command" })).toBe(false);
    });

    it("should return true for allowlisted bazel commands", () => {
      expect(tool.checkToolCallSafe({ command: "bazel query //..." })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "bazel info" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "bazel version" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "bazel build --nobuild //target" })).toBe(true);
    });

    it("should return false for dangerous bazel commands", () => {
      expect(tool.checkToolCallSafe({ command: "bazel build //target" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: "bazel run //target" })).toBe(false);
    });

    it("should return true for allowlisted kubectl commands", () => {
      expect(tool.checkToolCallSafe({ command: "kubectl get pods" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "kubectl describe pod" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "kubectl logs pod-name" })).toBe(true);
      expect(tool.checkToolCallSafe({ command: "kubectl version" })).toBe(true);
    });

    it("should return false for dangerous kubectl commands", () => {
      expect(tool.checkToolCallSafe({ command: "kubectl delete pod" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: "kubectl apply -f" })).toBe(false);
    });

    it("should handle prefix rules correctly", () => {
      // uname -a should be allowed (prefix rule)
      expect(tool.checkToolCallSafe({ command: "uname -a" })).toBe(true);
      // uname -b should be rejected (not matching prefix)
      expect(tool.checkToolCallSafe({ command: "uname -b" })).toBe(false);
    });

    it("should handle exact rules correctly", () => {
      // git branch should be allowed (exact rule)
      expect(tool.checkToolCallSafe({ command: "git branch" })).toBe(true);
      // git branch -D should be rejected (not exact match)
      expect(tool.checkToolCallSafe({ command: "git branch -D" })).toBe(false);
    });

    it("should handle not_contains rules correctly", () => {
      // ping without -f should be allowed
      expect(tool.checkToolCallSafe({ command: "ping google.com" })).toBe(true);
      // ping with -f should be rejected
      expect(tool.checkToolCallSafe({ command: "ping -f google.com" })).toBe(false);
      
      // tail without -f should be allowed
      expect(tool.checkToolCallSafe({ command: "tail file.txt" })).toBe(true);
      // tail with -f should be rejected
      expect(tool.checkToolCallSafe({ command: "tail -f file.txt" })).toBe(false);
    });

    it("should return false for invalid input", () => {
      expect(tool.checkToolCallSafe({})).toBe(false);
      expect(tool.checkToolCallSafe({ command: "" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: null })).toBe(false);
      expect(tool.checkToolCallSafe({ command: undefined })).toBe(false);
      expect(tool.checkToolCallSafe({ command: 123 })).toBe(false);
    });

    it("should return false for empty or blank commands", () => {
      expect(tool.checkToolCallSafe({ command: "" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: "   " })).toBe(false);
    });

    it("should handle errors gracefully", () => {
      // Test with malformed input that might cause errors
      expect(tool.checkToolCallSafe({ command: "git log $(echo 'rm -rf /')" })).toBe(false);
      expect(tool.checkToolCallSafe({ command: "ls | grep test" })).toBe(false);
    });
  });

  describe("basic properties", () => {
    it("should have correct tool properties", () => {
      expect(tool.name).toBe("launch-process");
      expect(tool.version).toBe(2);
      expect(tool.description).toContain("Launch a new process with a shell command");
      expect(tool.inputSchemaJson).toContain("command");
    });
  });
});
