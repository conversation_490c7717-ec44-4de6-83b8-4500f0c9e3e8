import { type AugmentLogger, getLogger } from "../logging";
import { <PERSON>Buffer } from "../ring-buffer";
import { retryWithBackoff } from "../utils/promise-utils";

/**
 * MetricsReporter is a class for batching metrics
 * for uploading at regular intervals.
 */
export abstract class MetricsReporter<T> {
    /**
     * MetricsReporter uploads metrics in the background. It stores metrics reported by
     * `report` in a buffer (`_store`) and uploads them every `_uploadMsec`
     * milliseconds. The store has a maximum size (`_maxRecords`). If the periodic uploads
     * fail, or are not able to keep up with the rate of newly reported metrics, the store
     * may eventually fill up. If this happens, old metrics will be dropped to make space for new metrics.
     *
     * Each upload operation attempts to upload the entire contents of the store, but it does
     * so in batches of `batchSize` records.
     */

    private _logger: AugmentLogger;
    private _store: RingBuffer<T>;
    private _uploadIntervalId: NodeJS.Timeout | undefined = undefined;
    private _currentUploadPromise: Promise<void> | undefined;
    constructor(
        loggerName: string,
        maxRecords: number,
        private _uploadMsec: number,
        private _uploadBatchSize: number
    ) {
        this._store = new RingBuffer<T>(maxRecords);
        this._logger = getLogger(loggerName);
    }

    public report(item: T): void {
        this._store.addItem(item);
    }

    public get uploadEnabled(): boolean {
        return this._uploadIntervalId !== undefined;
    }

    // enableReporting begins periodic uploading of metrics to the back end. If uploading is
    // already enabled, it does nothing.
    public enableUpload(): void {
        if (this.uploadEnabled) {
            return;
        }

        this._uploadIntervalId = setInterval(() => {
            if (this._currentUploadPromise !== undefined) {
                // Don't start a new upload if the previous one hasn't completed.
                return;
            }

            void (async () => {
                try {
                    this._currentUploadPromise = this._doUpload();
                    await this._currentUploadPromise;
                } finally {
                    this._currentUploadPromise = undefined;
                }
            })();
        }, this._uploadMsec);
    }

    // _doUpload batches up the current store values and calls
    // performUpload() to upload them.
    private async _doUpload(): Promise<void> {
        if (this._store.length === 0) {
            return;
        }

        let toUpload = this._store.slice();
        this._store.clear();

        for (let startIdx = 0; startIdx < toUpload.length; startIdx += this._uploadBatchSize) {
            const batch = toUpload.slice(startIdx, startIdx + this._uploadBatchSize);
            await retryWithBackoff<void>(async () => {
                if (!this.uploadEnabled) {
                    return;
                }
                try {
                    this._logger.debug(`Uploading ${batch.length} metric(s)`);
                    return await this.performUpload(batch);
                } catch (e: any) {
                    this._logger.error(
                        `Error uploading metrics: ${e} ${e instanceof Error ? e.stack : ""}`
                    );
                    throw e;
                }
            }, this._logger);
        }
    }

    // disableUpload stops uploading of metrics to the back end. If uploading is not enabled,
    // it does nothing.
    public disableUpload(): void {
        clearInterval(this._uploadIntervalId);
        this._uploadIntervalId = undefined;
    }

    public dispose() {
        this.disableUpload();
    }

    protected abstract performUpload(batch: T[]): Promise<void>;
}
