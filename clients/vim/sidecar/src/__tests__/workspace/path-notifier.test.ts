import { MockFSUtils, mockFSUtils } from "../../__mocks__/fs-utils";
import { MockAPIServer } from "../../__mocks__/mock-api-server";
import { MockFileSystem } from "../../__mocks__/mock-filesystem";
import { MockWorkspaceFolder } from "../../__mocks__/vscode-mocks";
import { defaultSupportedFileExtensions } from "../../languages";
import {
    IgnoreSource,
    IgnoreSourceBuiltin,
    IgnoreSourceFile,
    IgnoreStackBuilder,
} from "../../utils/ignore-file";
import { PathAcceptance } from "../../utils/path-acceptance";
import { makePathFilter, PathFilter } from "../../utils/path-iterator";
import { descendentPath, directoryContainsPath } from "../../utils/path-utils";
import { FileType } from "../../utils/types";
import { uriToAbsPath } from "../../utils/uri";
import * as vscode from "../../vscode";
import { BlobNameCalculator } from "../../workspace/blob-name-calculator";
import { PathNotifier, PathNotifyEvent } from "../../workspace/path-notifier";
import { verifyDefined } from "../__utils__/test-utils";
import { advanceTimeUntilTrue } from "../__utils__/time";

type PathInfo = {
    fileType: FileType;
    acceptance: PathAcceptance;
    content: string | undefined;
};

/**
 * PathNotifierTestKit provides functionality that is common to tests of the
 * PathNotifier class. It also implements PathNotifierTarget, which means it
 * can create a PathNotifier and use itself as the notifier's target.
 */
class PathNotifierTestKit {
    // Array of names of ignore files, in order of precedence. Files later in the array override
    // files earlier in the array.
    static readonly ignoreSources: Array<IgnoreSource> = [
        new IgnoreSourceFile(".gitignore"),
        new IgnoreSourceBuiltin("/"),
        new IgnoreSourceFile(".augmentignore"),
    ];
    public workspaceName: string;
    public apiServer = new MockAPIServer();
    public blobNameCalculator = new BlobNameCalculator(1000000);
    public counters = {
        fileFound: 0,
        fileChanged: 0,
        fileDeleted: 0,
    };

    // pathNameMap: Map<relPath, PathInfo>
    public readonly pathNameMap = new Map<string, PathInfo>();

    constructor(public readonly repoRootUri: vscode.Uri) {
        this.workspaceName = String(repoRootUri);
        this.fs.writeFileUtf8(this.toAbsPath(".augmentroot"), "", true);
    }

    public get fs(): MockFSUtils {
        return mockFSUtils;
    }

    public toAbsPath(pathName: string) {
        return uriToAbsPath(vscode.Utils.joinPath(this.repoRootUri, pathName));
    }

    public getBlobName(pathName: string): string {
        const content = this.fs.readFileRaw(this.toAbsPath(pathName));
        return this.blobNameCalculator.calculate(pathName, content)!;
    }

    // createFiles creates the given directories and files in the mock filesystem.
    public createFiles(files: Map<string, string>) {
        for (const [pathName, text] of files) {
            this.writeFile(pathName, text);
        }
    }

    // writeFile creates or overwrites the given file in the mock filesystem.
    public writeFile(pathName: string, text: string): string {
        return this.fs.writeFileUtf8(this.toAbsPath(pathName), text, true);
    }

    // makeDirs creates the given directory, and any missing parent directories, in the mock
    // filesystem.
    public makeDirs(pathName: string) {
        this.fs.makeDirs(this.toAbsPath(pathName));
    }

    // deleteFile deletes the given file in the mock filesystem.
    public deleteFile(pathName: string) {
        this.fs.deleteFile(this.toAbsPath(pathName));
    }

    // createPathNotifier creates a PathNotifier using this kit as its target.
    public async createPathNotifier(
        repoRootUri = this.repoRootUri
    ): Promise<[PathFilter, PathNotifier]> {
        const workspaceFolder = new MockWorkspaceFolder(uriToAbsPath(repoRootUri));
        const folderRoot = uriToAbsPath(repoRootUri);
        const pathFilter = await makePathFilter(
            vscode.Uri.file(folderRoot),
            repoRootUri,
            new IgnoreStackBuilder(PathNotifierTestKit.ignoreSources),
            defaultSupportedFileExtensions
        );
        const repoRoot = uriToAbsPath(repoRootUri);
        const pathNotifier = new PathNotifier(
            this.workspaceName,
            folderRoot,
            repoRoot,
            pathFilter,
            workspaceFolder
        );

        pathNotifier.onDidFindPath((event: PathNotifyEvent) =>
            this.notifyFileFound(event.relPath, event.fileType, event.acceptance)
        );

        pathNotifier.onDidChangePath((event: PathNotifyEvent) =>
            this.notifyFileChanged(event.relPath, event.fileType, event.acceptance)
        );

        pathNotifier.onDidDeletePath((relPath: string) => {
            this.notifyFileDeleted(relPath);
        });

        return [pathFilter, pathNotifier];
    }

    public async awaitContext(pathFilter: PathFilter): Promise<void> {
        await advanceTimeUntilTrue(() => this.verify(pathFilter));
    }

    public verify(pathFilter: PathFilter): boolean {
        for (const [absPath, _] of this.fs.createIterator(uriToAbsPath(this.repoRootUri)).next()) {
            const relPath = descendentPath(uriToAbsPath(this.repoRootUri), absPath);
            verifyDefined(relPath);
            const pathInfo = this.pathNameMap.get(relPath);
            if (pathInfo === undefined) {
                return false;
            }
            const acceptance = pathFilter.getPathInfo(relPath, pathInfo.fileType);
            if (acceptance.accepted !== pathInfo.acceptance.accepted) {
                return false;
            }
            const st = this.fs.statFile(absPath);
            if (st.type !== pathInfo.fileType) {
                return false;
            }
            // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
            if (st.type !== FileType.file) {
                continue;
            }
            const content = this.fs.readFileUtf8(absPath);
            if (content !== pathInfo.content) {
                return false;
            }
        }
        return true;
    }

    public notifyFileChanged(
        relPath: string,
        fileType: FileType,
        acceptance: PathAcceptance
    ): void {
        this._updateFileInfo(relPath, fileType, acceptance);
        if (fileType === FileType.file && acceptance.accepted) {
            this.counters.fileChanged++;
        }
    }

    public notifyFileFound(relPath: string, fileType: FileType, acceptance: PathAcceptance): void {
        this._updateFileInfo(relPath, fileType, acceptance);
        if (fileType === FileType.file && acceptance.accepted) {
            this.counters.fileFound++;
        }
    }

    private _updateFileInfo(relPath: string, fileType: FileType, acceptance: PathAcceptance) {
        let content: string | undefined = undefined;
        if (fileType === FileType.file) {
            try {
                content = this.fs.readFileUtf8(this.toAbsPath(relPath));
            } catch {}
        }
        this.pathNameMap.set(relPath, { fileType, acceptance, content });
    }

    public notifyFileDeleted(relPath: string): void {
        this.counters.fileDeleted++;
        this.pathNameMap.delete(relPath);
    }
}

describe("PathNotifier", () => {
    const sampleAugmentRoot = vscode.Uri.file("/src");

    const sampleFiles = new Map<string, string>([
        ["giraffe/dog/ostrich.py", "this is a source file"],
        ["giraffe/dog/partridge.py", "this is another source file"],
        ["whale/grizzly.cpp", "yet another"],
    ]);

    let kit: PathNotifierTestKit;

    beforeEach(() => {
        jest.useFakeTimers();
        mockFSUtils.reset();
        kit = new PathNotifierTestKit(sampleAugmentRoot);
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    // populate-files verifies that the PathNotifier's startup reports all files that
    // existed before it was created.
    test("populate-files", async () => {
        kit.createFiles(sampleFiles);
        const [pathFilter, pathNotifier] = await kit.createPathNotifier();
        await pathNotifier.enumeratePaths();
        await kit.awaitContext(pathFilter);
        expect(kit.counters).toEqual({
            fileFound: 3,
            fileChanged: 0,
            fileDeleted: 0,
        });
    });

    // add-file verifies that the PathNotifier's startup reports files that are created
    // after it starts up.
    test("add-file", async () => {
        const [pathFilter, pathNotifier] = await kit.createPathNotifier();
        kit.createFiles(sampleFiles);
        await pathNotifier.enumeratePaths();
        await kit.awaitContext(pathFilter);
        expect(kit.counters).toEqual({
            fileFound: 3,
            fileChanged: 0,
            fileDeleted: 0,
        });
    });

    // add-file verifies that the PathNotifier's startup reports files that are updated
    // after it starts up.
    test("update-file", async () => {
        kit.createFiles(sampleFiles);
        const [pathFilter, pathNotifier] = await kit.createPathNotifier();
        await pathNotifier.enumeratePaths();
        await kit.awaitContext(pathFilter);
        expect(kit.counters).toEqual({
            fileFound: 3,
            fileChanged: 0,
            fileDeleted: 0,
        });

        const updatePath = "giraffe/dog/partridge.py";
        expect(sampleFiles.keys()).toContain(updatePath);
        kit.writeFile(updatePath, "different contents");
        await kit.awaitContext(pathFilter);
        expect(kit.counters).toEqual({
            fileFound: 3,
            fileChanged: 1,
            fileDeleted: 0,
        });
    });

    // add-file verifies that the PathNotifier's startup reports files that are deleted
    // after it starts up.
    test("delete-file", async () => {
        const pathNameSet = new Set<string>(sampleFiles.keys());

        kit.createFiles(sampleFiles);
        const [pathFilter, pathNotifier] = await kit.createPathNotifier();
        await pathNotifier.enumeratePaths();
        await kit.awaitContext(pathFilter);
        expect(kit.counters).toEqual({
            fileFound: 3,
            fileChanged: 0,
            fileDeleted: 0,
        });

        const deletePath = "giraffe/dog/partridge.py";
        expect(sampleFiles.keys()).toContain(deletePath);
        kit.deleteFile(deletePath);
        pathNameSet.delete(deletePath);

        await kit.awaitContext(pathFilter);
        expect(kit.counters).toEqual({
            fileFound: 3,
            fileChanged: 0,
            fileDeleted: 1,
        });
    });

    // Verify that PathNotifier.enumeratePaths() can be called multiple times and returns
    // consistent results.
    test("multiple enumerations", async () => {
        kit.createFiles(sampleFiles);
        const [_pathFilter, pathNotifier] = await kit.createPathNotifier();

        let currStats: { files: number; dirs: number; other: number };

        let disp = pathNotifier.onDidFindPath((event: PathNotifyEvent) => {
            if (event.fileType === FileType.file) {
                currStats.files++;
            } else if (event.fileType === FileType.directory) {
                currStats.dirs++;
            } else {
                currStats.other++;
            }
        });

        const stats1 = { files: 0, dirs: 0, other: 0 };
        currStats = stats1;
        await pathNotifier.enumeratePaths();
        expect(stats1.files).toBeGreaterThan(0);
        expect(stats1.dirs).toBeGreaterThan(0);
        expect(stats1.other).toBe(0);

        const stats2 = { files: 0, dirs: 0, other: 0 };
        currStats = stats2;
        await pathNotifier.enumeratePaths();
        expect(stats2.files).toBe(stats1.files);
        expect(stats2.dirs).toBe(stats1.dirs);
        expect(stats2.other).toBe(stats1.other);

        disp.dispose();
    });

    test.each([
        ["ignored-dir", FileType.directory],
        ["ignored-dir/a.py", FileType.file],
        ["ignored-dir/xxx", FileType.directory],
        ["ignored-dir/xxx/b.py", FileType.file],
    ])(
        "ignore paths within ignored directories(%s)",
        async (pathName: string, fileType: FileType) => {
            const ignoreFiles = new Map<string, string>([[".gitignore", "ignored-dir/\n"]]);
            kit.createFiles(ignoreFiles);
            const [_pathFilter, pathNotifier] = await kit.createPathNotifier();
            await pathNotifier.enumeratePaths();

            let notified = false;
            pathNotifier.onDidCreatePath((event: PathNotifyEvent) => {
                if (event.relPath !== pathName) {
                    expect(event.fileType).toBe(FileType.directory);
                    expect(directoryContainsPath(event.relPath, pathName)).toBe(true);
                    return;
                }
                expect(event.fileType).toBe(fileType);
                expect(event.acceptance.accepted).toBe(false);
                notified = true;
            });

            if (fileType === FileType.file) {
                kit.writeFile(pathName, "hi there");
            } else {
                kit.makeDirs(pathName);
            }
            await advanceTimeUntilTrue(() => notified);
        }
    );

    test.each([
        ["file", true],
        ["untitled", true],
        // TODO(AU-6323): We should support notebooks
        ["vscode-notebook-cell", false],
        ["trash", false],
        ["git", false],
    ])(
        "supported uri schemes (%s)",
        async (scheme: string, expected: boolean) => {
            const [_pathFilter, pathNotifier] = await kit.createPathNotifier();

            const relPath = "giraffe/dog/partridge.py";
            const absPath = kit.toAbsPath(relPath);
            const uri = vscode.Uri.from({ scheme, path: absPath });

            let notifiedChange = false;
            let notifiedDelete = false;

            pathNotifier.onDidChangePath((event: PathNotifyEvent) => {
                if (event.relPath !== relPath) {
                    return;
                }
                notifiedChange = true;
            });
            pathNotifier.onDidDeletePath((eventRelPath: string) => {
                if (eventRelPath !== relPath) {
                    return;
                }
                notifiedDelete = true;
            });

            await pathNotifier.enumeratePaths();

            mockFSUtils.publishFSEvent(MockFileSystem.FSEvent.modify, uri);
            expect(notifiedChange).toBe(expected);

            mockFSUtils.publishFSEvent(MockFileSystem.FSEvent.delete, uri);
            expect(notifiedDelete).toBe(expected);
        },
        1000000
    );
});
