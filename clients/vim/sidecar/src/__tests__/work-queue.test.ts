import { assert } from "console";

import { APIError } from "../exceptions";
import { makePromise } from "../utils/promise-utils";
import { APIStatus } from "../utils/types";
import { KVWorkQueue, LIFOWorkQueue } from "../utils/work-queue";
import { Disposable } from "../vscode";
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON>, WorkQueue } from "../work-queue";

class MockShutdownError extends Error {
    constructor() {
        super("Mock shutdown error");
    }
}

function makeFailMap() {
    return new Map<number, { maxTries: number; eventuallySucceed: boolean }>();
}

function makeRetryMap() {
    return new Map<number, { maxTries: number; eventuallySucceed: boolean; tries: number }>();
}

/**
 * MockCountReporter is a CountReporter that validates calls to its `update` method.
 */
class MockCountReporter {
    public reportCount = 0;
    public cancelled = false;

    constructor(readonly inputs: Set<number>) {}

    update(currentCount: number) {
        expect(this.cancelled).toBeFalsy();
        expect(currentCount).toBe(this.inputs.size);
        this.reportCount++;
    }

    cancel() {
        this.cancelled = true;
    }
}

/**
 * MockProgressReporter is a ProgressReporter that validates calls to its `update`
 * method.
 */
class MockProgressReporter {
    private initialItemCount: number;
    public reportCount = 0;
    public cancelled = false;

    constructor(
        readonly inputs: Set<number>,
        private initialUpdateCount: number
    ) {
        this.initialItemCount = this.inputs.size;
    }

    update(completed: number, total: number) {
        expect(this.cancelled).toBe(false);
        expect(total).toBe(this.initialItemCount);
        expect(completed).toBe(this.initialItemCount - this.inputs.size);
        this.reportCount++;
    }

    cancel() {
        this.cancelled = true;
    }

    verify(updateCount: number, cancelled = false) {
        expect(this.cancelled).toBe(cancelled);
        if (!cancelled) {
            expect(this.reportCount).toBe(updateCount - this.initialUpdateCount);
        }
    }
}

class MockItemFailedPermanent extends APIError {
    constructor(public n: number) {
        super(APIStatus.unknown, `MockItemFailedPermanent: n = ${n}`);
    }
}

class MockItemFailedTransient extends APIError {
    constructor(public n: number) {
        super(APIStatus.unavailable, `MockItemFailedTransient: n = ${n}`);
    }
}

enum WorkQueueType {
    single,
    batch,
}

/**
 * WorkQueueTestKit is a class that provides common functionality for units tests
 * of the WorkQueue class.
 */
abstract class WorkQueueTestKit {
    inputs = new Set<number>();
    delayPromise = Promise.resolve();
    retryMap = makeRetryMap();
    countReporter: MockCountReporter;
    stopped = false;

    // These counters count events that should be reported to CountReporters
    // and ProgressReporters.
    addEvents = 0;
    deleteEvents = 0;
    processedEvents = 0;

    constructor(
        public workQueue: WorkQueue<number>,
        private failMap = makeFailMap()
    ) {
        // Construct retryMap from failMap. The only difference is that the former
        // keeps a count of the number of attempts made for the item so far.
        for (let [n, failInfo] of this.failMap) {
            this.retryMap.set(n, {
                maxTries: failInfo.maxTries,
                eventuallySucceed: failInfo.eventuallySucceed,
                tries: 0,
            });
        }

        // Install a CountReporter that will verify not only the correct number of
        // items in the queue but also the number of times the count was updated.
        // The latter should count every item twice, once when it was added to the
        // queue and once when it was either processed or deleted.
        this.countReporter = new MockCountReporter(this.inputs);
        this.workQueue.reportQueueSize(this.countReporter);
    }

    verifySize(expectedSize?: number) {
        expect(this.inputs.size).toBe(this.workQueue.size());
        if (expectedSize !== undefined) {
            expect(this.inputs.size).toBe(expectedSize);
        }
    }

    add(n: number): void {
        if (!this.stopped) {
            this.inputs.add(n);
            this.addEvents++;
        }
        this.workQueue.add(n);
    }

    delete(n: number): void {
        if (!this.stopped) {
            this.inputs.delete(n);
            this.deleteEvents++;
        }
        this.workQueue.delete(n);
    }

    // Delay event processing until the given Disposable is disposed.
    delay(): Disposable {
        const [promise, resolve] = makePromise();
        this.delayPromise = promise;
        return new Disposable(() => resolve());
    }

    /**
     * shouldFailItem consults the retryMap to choose the disposition of the given
     * item. There are four possible outcomes:
     * 1. The item is not mentioned in the failMap: return false (item should succeed)
     * 2. The item has not yet failed `maxTries` times: throw error with retry=true
     * 3. The item has failed `maxTries` times and has `eventuallySucceed` = false:
     *    return true (item should fail)
     * 4. The item has failed `maxTries` times and has `eventuallySucceed` = true:
     *    return false (item should succeed)
     */
    protected _shouldFailItem(n: number, errorHandler: ErrorHandler): boolean {
        const retries = this.retryMap.get(n);
        if (retries) {
            expect(retries.tries).toBeLessThanOrEqual(retries.maxTries);
            retries.tries++;
            if (retries.tries < retries.maxTries) {
                errorHandler.throwError(new MockItemFailedTransient(n), true);
            }
            return !retries.eventuallySucceed;
        }
        return false;
    }

    // stops the queue and verifies that "empty waiters" and ProgressReporters are
    // cancelled.
    stop() {
        const progressReporter = new MockProgressReporter(this.inputs, this._eventCount());
        const promise = this.workQueue.awaitEmpty(progressReporter);
        expect(this.workQueue.size()).toBeGreaterThan(0);

        this.stopped = true;
        this.workQueue.stop();

        promise.catch((e) => expect(e).toBeInstanceOf(MockShutdownError));
        expect(progressReporter.cancelled).toBe(true);
    }

    // returns the cumulative number of events that should have been reported
    // to CountReports and ProgressReporters
    private _eventCount(): number {
        return this.addEvents + this.deleteEvents + this.processedEvents;
    }

    // Waits for the queue to drain all items and verifies the behavior of
    // CountReporters and ProgressReporters.
    async finalize(awaitEmptyDispose?: Disposable): Promise<void> {
        // awaitEmpty returns before the queue is actually empty if items fail or
        // if the queue is stopped. Hence this retry loop and try-catch block, for
        // the benefit of the tests that involve errors or shutdown.
        let progressReporter: MockProgressReporter;
        while (true) {
            const count = this.workQueue.size();
            progressReporter = new MockProgressReporter(this.inputs, this._eventCount());
            const promise = this.workQueue.awaitEmpty(progressReporter);
            awaitEmptyDispose?.dispose();
            try {
                await promise;
            } catch (e) {
                if (e instanceof MockShutdownError) {
                    expect(this.stopped).toBe(true);
                    break;
                }
                if (e instanceof MockItemFailedPermanent || e instanceof MockItemFailedTransient) {
                    // Verify that the failed item is one that is supposed to have
                    // failed, then wait again.
                    expect(typeof e.n).toBe("number");
                    const n = e.n;
                    const failInfo = this.failMap.get(n);
                    expect(failInfo).toBeDefined();
                    continue;
                }
                assert(false);
            }
            if (count === 0 || this.stopped) {
                break;
            }
        }

        if (!this.stopped) {
            this.verifySize(0);
            progressReporter.verify(this._eventCount(), this.stopped);

            // The count reporter should have reported a count when it was first
            // created as well as each time an item was added or removed, for a
            // total of `itemsAdded * 2 + 1` reports.
            expect(this.countReporter.reportCount).toBe(1 + this._eventCount());

            // Verify that the items that were supposed to fail and retry did in fact
            // retry the correct number of times.
            for (let [_n, retries] of this.retryMap) {
                expect(retries.tries).toBe(retries.maxTries);
            }
        }
    }
}

class SingletonWorkQueueTestKit extends WorkQueueTestKit {
    constructor(failMap = makeFailMap()) {
        const backoffParams = {
            initialMS: 0,
            mult: 0,
            maxMS: 0,
        };
        const workQueue = new WorkQueue<number>(
            "test",
            {
                processOne: (n: number, e: ErrorHandler): Promise<void> => this.processOne(n, e),
            },
            new MockShutdownError(),
            backoffParams
        );
        super(workQueue, failMap);
    }

    // singleton queue item processor
    async processOne(n: number, errorHandler: ErrorHandler): Promise<void> {
        this.verifySize();
        expect(this.inputs).toContain(n);
        await this.delayPromise;

        const failItem = this._shouldFailItem(n, errorHandler);
        this.inputs.delete(n);
        this.processedEvents++;
        if (failItem) {
            errorHandler.throwError(new MockItemFailedPermanent(n), false);
        }
    }
}

class BatchWorkQueueTestKit extends WorkQueueTestKit {
    public readonly _batchSizes = new Array<number>();

    constructor(
        private _maxBatchSize?: number,
        failMap = makeFailMap()
    ) {
        const backoffParams = {
            initialMS: 0,
            mult: 0,
            maxMS: 0,
        };
        const workQueue = new WorkQueue<number>(
            "test",
            {
                processBatch: (nset: Set<number>, e: ErrorHandler): Promise<void> =>
                    this.processBatch(nset, e),
                maxBatchSize: _maxBatchSize,
            },
            new Error(),
            backoffParams
        );
        super(workQueue, failMap);
    }

    // batch queue item processor
    async processBatch(nset: Set<number>, _: ErrorHandler): Promise<void> {
        this._batchSizes.push(nset.size);
        if (this._maxBatchSize !== undefined) {
            expect(nset.size).toBeLessThanOrEqual(this._maxBatchSize);
        }
        this.verifySize();
        for (const n of nset) {
            expect(this.inputs).toContain(n);
        }
        await this.delayPromise;

        for (const n of nset) {
            this.inputs.delete(n);
        }
        this.processedEvents++;
    }
}

function makeWorkQueueTestKit(queueType: WorkQueueType, failMap = makeFailMap()): WorkQueueTestKit {
    if (queueType === WorkQueueType.single) {
        return new SingletonWorkQueueTestKit(failMap);
    } else {
        return new BatchWorkQueueTestKit(undefined, failMap);
    }
}

describe("WorkQueue", () => {
    /**
     * Test the ability to add items to the queue and verify that all items are processed.
     * Runs for both singleton and batch queues.
     */
    test.each([WorkQueueType.single, WorkQueueType.batch])(
        "add.%p",
        async (queueType: WorkQueueType) => {
            const count = 10;
            const kit = makeWorkQueueTestKit(queueType);
            for (let x = 0; x < count; x++) {
                kit.add(x);
            }
            await kit.finalize();
        }
    );

    /**
     * Tests the ability to add items to the queue and remove them before they are
     * processed. Runs for both singleton and batch queues.
     */
    test.each([WorkQueueType.single, WorkQueueType.batch])(
        "delete.%p",
        async (queueType: WorkQueueType) => {
            const count = 10;
            const kit = makeWorkQueueTestKit(queueType);
            const delayDisp = kit.delay();

            for (let x = 0; x < count; x++) {
                kit.add(x);
            }
            kit.verifySize(count);

            const toDelete = count / 2;
            kit.delete(toDelete);
            kit.verifySize(count - 1);

            await kit.finalize(delayDisp);
        }
    );

    /**
     * Tests the handling of failures encountered while processing queue items.
     */
    test("failure", async () => {
        const count = 10;
        const failMap = makeFailMap();

        failMap.set(3, { maxTries: 1, eventuallySucceed: false });
        failMap.set(6, { maxTries: 4, eventuallySucceed: true });
        failMap.set(8, { maxTries: 3, eventuallySucceed: false });

        const kit = makeWorkQueueTestKit(WorkQueueType.single, failMap);
        for (let x = 0; x < count; x++) {
            kit.add(x);
        }

        await kit.finalize();
    });

    /**
     * Tests the behavior of the queue when it is stopped.
     */
    test("shutdown", async () => {
        const count = 10;
        const kit = makeWorkQueueTestKit(WorkQueueType.single);
        const delayDisp = kit.delay();

        for (let x = 0; x < count; x++) {
            kit.add(x);
        }
        kit.verifySize(count);

        kit.stop();

        kit.add(100);
        kit.verifySize(count);
        kit.delete(count / 2);
        kit.verifySize(count);

        await kit.finalize(delayDisp);
    });

    /**
     * Verifies that a batch queue respects the maxBatchSize parameter.
     */
    test("batch-max", async () => {
        const batchSize = 4;
        const totalItems = 59;
        const kit = new BatchWorkQueueTestKit(batchSize);
        const delayDisp = kit.delay();

        for (let x = 0; x < totalItems; x++) {
            kit.add(x);
        }

        await kit.finalize(delayDisp);

        let itemCount = 0;
        for (const batchSize of kit._batchSizes) {
            itemCount += batchSize;
            expect(batchSize).toBeLessThanOrEqual(batchSize);
        }
        expect(itemCount).toBe(totalItems);
    });

    /**
     * Tests the cancel function of the work queue.
     */
    test("cancel", async () => {
        jest.useFakeTimers();
        const queue = new KVWorkQueue<number, string>(async (): Promise<void> => {
            await new Promise((resolve) => setTimeout(resolve, 50)); // Simulate some processing time
        });

        // Add items to the queue
        queue.insert(1, "one");
        queue.insert(2, "two");
        queue.insert(3, "three");

        expect(queue.size).toBe(3);

        // Start processing
        const kickPromise = queue.kick();

        // Wait a bit to allow some processing to occur
        await jest.runAllTimersAsync();

        // Cancel an item
        queue.cancel(2);

        // Wait for the kick promise to resolve
        await kickPromise;

        // Check that the cancelled item was removed and the queue is empty
        expect(queue.size).toBe(0);
        expect(queue.get(1)).toBeUndefined();
        expect(queue.get(2)).toBeUndefined();
        expect(queue.get(3)).toBeUndefined();

        // Try to cancel a non-existent item (should not throw an error)
        queue.cancel(4);
        jest.useRealTimers();
    });

    /**
     * Tests the LIFO (Last-In-First-Out) behavior of the LIFOWorkQueue.
     */
    test("lifo-order", async () => {
        const processedItems: number[] = [];
        const queue = new LIFOWorkQueue<number, void>(async (item) => {
            if (item !== undefined) {
                processedItems.push(item);
            }
        });

        // Add items in order: 1, 2, 3
        queue.insert(1);
        queue.insert(2);
        queue.insert(3);

        expect(queue.size).toBe(3);

        // Process all items
        await queue.kick();

        // Verify LIFO order: 3, 2, 1
        expect(processedItems).toEqual([3, 2, 1]);
        expect(queue.size).toBe(0);
    });

    /**
     * Tests that the LIFOWorkQueue properly handles items added while processing.
     */
    test("lifo-concurrent-add", async () => {
        const processedItems: number[] = [];
        const queue = new LIFOWorkQueue<number, void>(async (item) => {
            if (item !== undefined) {
                processedItems.push(item);
                if (item === 3) {
                    // Add more items while processing
                    queue.insert(4);
                    queue.insert(5);
                }
            }
        });

        // Add initial items
        queue.insert(1);
        queue.insert(2);
        queue.insert(3);

        // Process all items
        await queue.kick();

        // Verify LIFO order including dynamically added items
        expect(processedItems).toEqual([3, 5, 4, 2, 1]);
        expect(queue.size).toBe(0);
    });

    /**
     * Tests that the LIFOWorkQueue properly handles stopping.
     */
    test("lifo-stop", async () => {
        const processedItems: number[] = [];
        const queue = new LIFOWorkQueue<number, void>(async (item) => {
            if (item !== undefined) {
                processedItems.push(item);
                if (item === 3) {
                    queue.dispose();
                }
            }
        });

        // Add items
        queue.insert(1);
        queue.insert(2);
        queue.insert(3);
        queue.insert(4);
        queue.insert(5);

        // Process until stopped
        await queue.kick();

        // Verify only items processed before stop
        expect(processedItems).toEqual([5, 4, 3]);

        // Verify that new items are not processed after stop
        queue.insert(6);
        await queue.kick();
        expect(processedItems).toEqual([5, 4, 3]);
    });

    /**
     * Tests that the LIFOWorkQueue properly handles errors during processing.
     */
    test("lifo-error-handling", async () => {
        const processedItems: number[] = [];
        const queue = new LIFOWorkQueue<number, void>(async (item) => {
            if (item !== undefined) {
                if (item === 3) {
                    throw new Error("Test error");
                }
                processedItems.push(item);
            }
        });

        // Add items
        queue.insert(1);
        queue.insert(2);
        expect(queue.insert(3)).rejects.toThrow("Test error");
        queue.insert(4);
        queue.insert(5);

        // Process all items
        await queue.kick();

        // Verify that processing continues after error
        expect(processedItems).toEqual([5, 4, 2, 1]);
        expect(queue.size).toBe(0);
    });

    /**
     * Tests that the LIFOWorkQueue properly handles concurrent kicks.
     */
    test("lifo-concurrent-kicks", async () => {
        const processedItems: number[] = [];
        const queue = new LIFOWorkQueue<number, void>(async (item) => {
            if (item !== undefined) {
                await new Promise((resolve) => setTimeout(resolve, 10));
                processedItems.push(item);
            }
        });

        // Add initial items
        queue.insert(1);
        queue.insert(2);
        queue.insert(3);

        // Kick multiple times concurrently. This will immediately try to process
        // the most recent item before yielding control back to the current function.
        const kicks = Promise.all([queue.kick(), queue.kick(), queue.kick()]);

        // Add more items while kicking
        queue.insert(4);
        queue.insert(5);

        await kicks;

        // Verify LIFO order and that items were processed only once
        expect(processedItems).toEqual([3, 5, 4, 2, 1]);
        expect(queue.size).toBe(0);
    });
});
