" Implementation for the vim test runner
"
" Should be sourced from the test file e.g.
"
"   vim -Nu NONE -S run.vim test_example.vim
"
" This command opens the test file test_example.vim in a clean vim instance
" with no configuration or initialization scripts, then sources run.vim.

function! s:Log(message)
    if type(a:message) == type([])
        call writefile(a:message, s:test_log_file, 'as')
    else
        call writefile([a:message], s:test_log_file, 'as')
    endif
endfunction

function! s:OnTestTimeout(_)
    call s:Log(v:errors)
    call s:Log('Timed out')
    cquit!
endfunction

function! s:EarlyExit()
    call s:Log(v:errors)
    call s:Log('Exited early')
    cquit!
endfunction

" Create the log file
let s:test_name = expand('%:p:t')
let s:test_log_file = expand('%:p:h:h') . '/logs/' . s:test_name . '.log'
call writefile([], s:test_log_file, 's')

" Source the open test file and close it
source %
%bwipe!

" Extract the list of test functions
let s:tests = execute('function /^Test_')
    \ ->substitute('function \(\k*()\)', '\1', 'g')
    \ ->split()

call s:Log('Found ' . len(s:tests) . ' tests in ' . s:test_name)

" Set a timer for the test execution and fail if it takes too long
if exists('*TestTimeoutMs')
    call timer_start(TestTimeoutMs(), function('s:OnTestTimeout'))
else
    " Default to 10 seconds
    call timer_start(10000, function('s:OnTestTimeout'))
endif

" Run the setup function if it exists, exiting early if there is an error
if exists('*Setup')
    call s:Log('Calling Setup()')

    au VimLeavePre * call s:EarlyExit()

    try
        call Setup()
    catch
        let error = 'Uncaught exception ' . v:exception . ' at ' . v:throwpoint
        call add(v:errors, error)
    finally
        au! VimLeavePre
    endtry

    if len(v:errors) > 0
        call s:Log(v:errors)
        cquit!
    endif
endif

" Run each test
let s:errors = []
for test_function in s:tests
    call s:Log('Running test ' . test_function)

    au VimLeavePre * call s:EarlyExit()

    try
        " Call the before test function if it exists
        if exists('*BeforeTest')
            call BeforeTest()
        endif

        execute 'call ' . test_function
    catch
        let error = 'Uncaught exception ' . v:exception . ' at ' . v:throwpoint
        call add(v:errors, error)
    finally
        au! VimLeavePre
    endtry

    " Add errors to the log
    call s:Log(v:errors)
    call extend(s:errors, v:errors)
    let v:errors = []

    " Reset the buffer
    %bwipe!
endfor

" Exit vim, setting an error code if any tests failed
if len(s:errors) > 0
    cquit!
else
    quit!
endif
