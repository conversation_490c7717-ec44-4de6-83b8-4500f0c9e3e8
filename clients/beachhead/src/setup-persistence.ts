import * as fs from "fs";
import * as path from "path";

import { getLogger } from "./logging";

const SETUP_SCRIPT_COMPLETED_MARKER_FILE = ".setup_script_completed";

/**
 * SetupPersistence handles persisting setup completion state to marker files.
 * It provides methods to check and mark setup completion to avoid redundant operations
 * when the beachhead client restarts with a persistent root volume.
 */
export class SetupPersistence {
    private logger = getLogger("SetupPersistence");
    private directoryPath: string;

    /**
     * Creates a new SetupPersistence instance.
     *
     * @param workspacePath The workspace directory path where marker files will be stored
     */
    constructor(directoryPath: string) {
        this.directoryPath = directoryPath;
    }

    /**
     * Checks if the setup script has already been completed.
     * This indicates that all init commands (git clone, checkout, apply, setup script)
     * have been successfully executed.
     *
     * @returns true if setup script has been completed, false otherwise
     */
    isSetupScriptCompleted(): boolean {
        try {
            const markerPath = this.getSetupScriptMarkerPath();
            return fs.existsSync(markerPath);
        } catch (error) {
            this.logger.error(`Error checking setup script completion: ${String(error)}`);
            return false;
        }
    }

    /**
     * Marks the setup script as completed by creating a marker file.
     * This should be called after all init commands have been successfully executed.
     *
     * @returns A promise that resolves when the marker is created, or rejects if there's an error
     */
    async markSetupScriptCompleted(): Promise<void> {
        try {
            const markerPath = this.getSetupScriptMarkerPath();
            // Ensure the directory exists before writing the marker file
            await fs.promises.mkdir(this.directoryPath, { recursive: true });
            await fs.promises.writeFile(markerPath, "");
            this.logger.info(`Setup script completion marked: ${markerPath}`);
        } catch (error) {
            this.logger.error(`Failed to mark setup script as completed: ${String(error)}`);
            throw error;
        }
    }

    /**
     * Gets the path to the setup script completion marker file.
     *
     * @returns The path to the marker file
     */
    private getSetupScriptMarkerPath(): string {
        return path.join(this.directoryPath, SETUP_SCRIPT_COMPLETED_MARKER_FILE);
    }
}
