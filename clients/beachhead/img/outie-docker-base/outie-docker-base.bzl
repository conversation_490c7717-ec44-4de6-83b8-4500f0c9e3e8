#
# This file is auto-generated by rebuild.sh. DO NOT EDIT BY HAND.
#

load("@rules_oci//oci:pull.bzl", "oci_pull")

def docker_outie_base_setup():
    oci_pull(
        name = "remote-agents-outie-base",
        registry = "us-central1-docker.pkg.dev",
        repository = "system-services-dev/base-images/remote-agents-outie-base",
        digest = "sha256:9e16a63f6c72c983b049b954166bf406aacc3fba75a2a05a40b3d819d4471b26",
    )
