#
# This file is auto-generated by rebuild.sh. DO NOT EDIT BY HAND.
#

load("@rules_oci//oci:pull.bzl", "oci_pull")

def docker_innie_base_setup():
    oci_pull(
        name = "remote-agents-innie-base",
        registry = "us-central1-docker.pkg.dev",
        repository = "system-services-dev/base-images/remote-agents-innie-base",
        digest = "sha256:6b1d2c21b80d81d29ca03c48257c406b913d46e627064f158f01397ec9bd528f",
    )
