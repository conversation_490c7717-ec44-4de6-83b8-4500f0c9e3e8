import { AggregateCheckpointManager } from "@augment-internal/sidecar-libs/src/agent/checkpoint/aggregate-checkpoint-manager";
import { ChatMode, Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ChatRequestNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { IPluginFileStore } from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import { DiffViewDocument } from "@augment-internal/sidecar-libs/src/diff-view/document";
import { AgentEditTools } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import {
    errorToolResponse,
    successToolResponse,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import { IToolHost } from "@augment-internal/sidecar-libs/src/tools/tool-host";
import { ITool, ToolHostBase } from "@augment-internal/sidecar-libs/src/tools/tool-host-base";
import {
    ToolBase,
    ToolHostName,
    ToolSafety,
    ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { delayMs } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import * as vscode from "vscode";

import { APIServer } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { FeatureFlagManager } from "../feature-flags";
import { getLogger } from "../logging";
import { RemoteAgentChatRequestDetails, RemoteAgentStatus } from "../remote-agent-manager/types";
import { AugmentGlobalState, GlobalContextKey } from "../utils/context";
import { isExtensionVersionGte } from "../utils/environment";
import { SetupScriptsManager } from "../utils/remote-agent-setup/setup-scripts-manager";
import { GitCommitIndexer } from "../vcs/git-commit-indexer";
import { LocalToolType } from "../webview-providers/tool-types";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { formatDiagnostics } from "./diagnostics-utils";
import { StrReplaceEditorTool } from "./str-replace-editor-tool";
import {
    TerminalKillProcessTool,
    TerminalLaunchProcessTool,
    TerminalListProcessesTool,
    TerminalProcessTools,
    TerminalReadProcessTool,
    TerminalWriteProcessTool,
} from "./terminal-tools";
import { _getQualifiedPath, _readFile } from "./tools-utils";

export const localToolHostFactory = (
    apiServer: APIServer,
    workspaceManager: WorkspaceManager,
    checkpointManager: AggregateCheckpointManager,
    featureFlagManager: FeatureFlagManager,
    extensionRoot: vscode.Uri,
    globalState: AugmentGlobalState,
    enableStart: number,
    gitCommitIndexer: GitCommitIndexer | undefined,
    configListener: AugmentConfigListener,
    assetManager?: IPluginFileStore
) => {
    return (chatMode: ChatMode) => {
        return new LocalToolHost(
            chatMode,
            workspaceManager,
            apiServer,
            checkpointManager,
            featureFlagManager,
            extensionRoot,
            globalState,
            enableStart,
            gitCommitIndexer,
            configListener,
            assetManager
        );
    };
};

/**
 * A tool host for tools that run locally in the IDE.
 */
export class LocalToolHost extends ToolHostBase<LocalToolType> {
    private readonly _terminalProcessTools: TerminalProcessTools | undefined;

    constructor(
        private readonly _chatMode: ChatMode,
        private readonly _workspaceManager: WorkspaceManager,
        private readonly _apiServer: APIServer,
        private readonly _checkpointManager: AggregateCheckpointManager,
        private readonly _featureFlagManager: FeatureFlagManager,
        private readonly _extensionRoot: vscode.Uri,
        private readonly _globalState: AugmentGlobalState,
        private readonly _enableStart: number,
        private readonly _gitCommitIndexer: GitCommitIndexer | undefined,
        private readonly _configListener: AugmentConfigListener,
        private readonly _assetManager?: IPluginFileStore
    ) {
        const tools: ITool<LocalToolType>[] = [];
        let terminalProcessTools: TerminalProcessTools | undefined = undefined;
        if (_chatMode === ChatMode.agent) {
            if (
                _featureFlagManager.currentFlags?.vscodeAgentEditTool !==
                AgentEditTools.strReplaceEditor
            ) {
                tools.push(new ReadFileTool(_workspaceManager));
                tools.push(new EditFileTool(_workspaceManager, _apiServer, _checkpointManager));
            } else {
                // Use the VSCode-specific implementation of the string replace tool with diagnostics support
                tools.push(
                    new StrReplaceEditorTool(
                        _checkpointManager,
                        _workspaceManager
                    ) as unknown as ITool<LocalToolType>
                );
            }

            tools.push(new OpenBrowserTool());
            tools.push(new DiagnosticsTool(_workspaceManager));
            tools.push(new TerminalReadOutputTool());

            if (_gitCommitIndexer !== undefined) {
                tools.push(new GitCommitRetrievalTool(_apiServer, _gitCommitIndexer));
            }

            if (
                _featureFlagManager.currentFlags?.enableSpawnSubAgentTool &&
                isExtensionVersionGte(
                    _featureFlagManager.currentFlags.vscodeBackgroundAgentsMinVersion ?? ""
                )
            ) {
                tools.push(new SpawnSubAgentTool(_apiServer, _globalState, _configListener));
            }

            terminalProcessTools = new TerminalProcessTools(
                _extensionRoot,
                _globalState,
                _enableStart,
                _featureFlagManager.currentFlags?.enableUntruncatedContentStorage,
                _featureFlagManager.currentFlags?.maxLinesTerminalProcessOutput,
                _assetManager
            );
            tools.push(new TerminalLaunchProcessTool(_workspaceManager, terminalProcessTools));
            tools.push(new TerminalKillProcessTool(terminalProcessTools));
            tools.push(new TerminalReadProcessTool(terminalProcessTools, _workspaceManager));
            tools.push(new TerminalWriteProcessTool(terminalProcessTools));
            tools.push(new TerminalListProcessesTool(terminalProcessTools));
        } else {
            tools.push(new ReadFileTool(_workspaceManager));
        }

        super(tools, ToolHostName.localToolHost);
        this._terminalProcessTools = terminalProcessTools;
    }

    /** Stops all running processes and disposes of the terminal listener. */
    public async close(_cancelledByUser: boolean = false): Promise<void> {
        await super.close();
        if (this._terminalProcessTools !== undefined) {
            this._terminalProcessTools.cleanup();
        }
    }

    /** Stops all running processes but keeps the terminal listener. */
    public closeAllToolProcesses(): Promise<void> {
        if (this._terminalProcessTools !== undefined) {
            this._terminalProcessTools.closeAllProcesses();
        }
        return Promise.resolve();
    }

    factory(_preconditionWait: Promise<void>): IToolHost {
        return new LocalToolHost(
            this._chatMode,
            this._workspaceManager,
            this._apiServer,
            this._checkpointManager,
            this._featureFlagManager,
            this._extensionRoot,
            this._globalState,
            this._enableStart,
            this._gitCommitIndexer,
            this._configListener,
            this._assetManager
        );
    }
}

/**
 * A tool that reads a file.
 */
class ReadFileTool extends ToolBase<LocalToolType> {
    constructor(private readonly _workspaceManager: WorkspaceManager) {
        super(LocalToolType.readFile, ToolSafety.Safe);
    }

    public readonly description: string = "Read a file.";

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            file_path: {
                type: "string",
                description: "The path of the file to read.",
            },
        },
        required: ["file_path"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal
    ): Promise<ToolUseResponse> {
        const relPath = toolInput.file_path as string;
        try {
            const contents = await _readFile(relPath, this._workspaceManager);
            if (contents === undefined) {
                return errorToolResponse(`Cannot read file: ${relPath}`);
            }
            return successToolResponse(contents);
        } catch (e: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            return errorToolResponse(`Failed to read file: ${relPath}: ${e.message ?? ""}`);
        }
    }
}

/**
 * A tool that edits a file.
 */
class EditFileTool extends ToolBase<LocalToolType> {
    private readonly _maxDiagnosticDelayMs = 5000;

    constructor(
        private readonly _workspaceManager: WorkspaceManager,
        private readonly _apiServer: APIServer,
        private readonly _checkpointManager: AggregateCheckpointManager
    ) {
        super(LocalToolType.editFile, ToolSafety.Safe);
    }

    public readonly description: string = `
Edit a file. Accepts a file path and a description of the edit.
This tool can edit whole files.
The description should be detailed and precise, and include all required information to perform the edit.
It can include both natural language and code. It can include multiple code snippets to described different
edits in the file. It can include descriptions of how to perform these edits precisely.

All the contents that should go in a file should be placed in a markdown code block, like this:

<begin-example>
Add a function called foo.

\`\`\`
def foo():
    ...
\`\`\`
</end-example>

This includes all contents, even if it's not code.

Be precise or I will take away your toys.

Prefer to use this tool when editing parts of a file.
`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            file_path: {
                type: "string",
                description: "The path of the file to edit.",
            },
            edit_summary: {
                type: "string",
                description: "A brief description of the edit to be made. 1-2 sentences.",
            },
            detailed_edit_description: {
                type: "string",
                description:
                    "A detailed and precise description of the edit. Can include natural language and code snippets.",
            },
        },
        required: ["file_path", "edit_summary", "detailed_edit_description"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        chatHistory: Exchange[],
        abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const filePath = toolInput.file_path as string;
        let requestId = undefined;
        try {
            const editSummary = toolInput.edit_summary as string;
            const detailedEditDescription = toolInput.detailed_edit_description as string;
            const fileContents = await _readFile(filePath, this._workspaceManager);
            if (fileContents === undefined) {
                return errorToolResponse(`Cannot read file: ${filePath}`);
            }
            const pathName = _getQualifiedPath(filePath, this._workspaceManager);
            if (pathName === undefined) {
                return errorToolResponse(`Cannot resolve path: ${filePath}`);
            }

            const startingDiagnostics = this._getDiagnostics();

            requestId = this._apiServer.createRequestId();
            const editResult = await this._apiServer.agentEditFile(
                requestId,
                filePath,
                editSummary,
                detailedEditDescription,
                fileContents,
                abortSignal
            );
            if (editResult.isError) {
                return errorToolResponse(`Failed to edit file: ${filePath}`, requestId);
            }

            const document = new DiffViewDocument(
                pathName,
                fileContents,
                editResult.modifiedFileContents,
                {}
            );

            // Do not create a checkpoint if the file contents are the same
            if (fileContents === editResult.modifiedFileContents) {
                document.dispose();
                return successToolResponse(`No changes made to file {${filePath}}`, requestId);
            }

            // Get the exchange ID that resulted in this tool call.
            const toolUseExchangeRequestId = chatHistory.at(-1)?.request_id ?? requestId;
            // Create a new checkpoint and update it to persist to disk.
            const conversationId = this._checkpointManager.currentConversationId ?? "";
            await this._checkpointManager.addCheckpoint(
                {
                    conversationId,
                    path: pathName,
                },
                {
                    sourceToolCallRequestId: toolUseExchangeRequestId,
                    timestamp: Date.now(),
                    document,
                    conversationId,
                }
            );

            // Show the model diagnostics since they are often errors.
            // First, let's wait for them to be computed, polling every second.
            const endingDiagnostics = await this._waitForNewDiagnostics(startingDiagnostics);
            const newDiagnostics = this._filterDiagnosticsMap(
                endingDiagnostics,
                startingDiagnostics
            );
            const newDiagnosticsString = Array.from(newDiagnostics.entries())
                .map(([path, diagnostics]) => {
                    return `${path}\n${diagnostics.map((d) => `${d.range.start.line}-${d.range.end.line}: ${d.message}`).join("\n")}`;
                })
                .join("\n\n");
            // Compute additional diagnostics for the current file, which may have been caused by a previous edit.
            const curFileDiagnostics = endingDiagnostics.get(pathName.absPath) ?? [];
            const additionalDiagnosticsForCurrentFile = this._filterDiagnostics(
                curFileDiagnostics,
                newDiagnostics.get(pathName.absPath) ?? []
            );
            const curFileDiagnosticsString = additionalDiagnosticsForCurrentFile
                .map((d) => `${d.range.start.line}-${d.range.end.line}: ${d.message}`)
                .join("\n");

            return successToolResponse(
                `File edited successfully.  Saved file {${filePath}}.  New diagnostics:\n${newDiagnosticsString}\n\nAdditional ${filePath} diagnostics:\n${curFileDiagnosticsString}`,
                requestId
            );
        } catch (e: any) {
            return errorToolResponse(
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                `Failed to edit file: ${filePath}: ${e.message ?? ""}`,
                requestId
            );
        }
    }

    private _getDiagnostics(): Map<string, vscode.Diagnostic[]> {
        const result = new Map<string, vscode.Diagnostic[]>();
        vscode.languages.getDiagnostics().forEach(([uri, diagnostics]) => {
            if (diagnostics.length > 0) {
                result.set(uri.fsPath, diagnostics);
            }
        });
        return result;
    }

    private _filterDiagnosticsMap(
        diagnostics: Map<string, vscode.Diagnostic[]>,
        toRemove: Map<string, vscode.Diagnostic[]>
    ) {
        const result = new Map<string, vscode.Diagnostic[]>();
        diagnostics.forEach((diagnostics, path) => {
            const toRemoveForPath = toRemove.get(path) ?? [];
            const filteredDiagnostics = this._filterDiagnostics(diagnostics, toRemoveForPath);
            if (filteredDiagnostics.length > 0) {
                result.set(path, filteredDiagnostics);
            }
        });
        return result;
    }

    private _filterDiagnostics(diagnostics: vscode.Diagnostic[], toRemove: vscode.Diagnostic[]) {
        return diagnostics.filter((diagnostic) => {
            return !toRemove.some((d) => {
                return (
                    diagnostic.range.start.line === d.range.start.line &&
                    diagnostic.range.end.line === d.range.end.line &&
                    diagnostic.message === d.message &&
                    diagnostic.severity === d.severity
                );
            });
        });
    }

    private async _waitForNewDiagnostics(
        startingDiagnostics: Map<string, vscode.Diagnostic[]>
    ): Promise<Map<string, vscode.Diagnostic[]>> {
        const startTime = Date.now();
        let lastDiagnostics = this._getDiagnostics();

        while (Date.now() - startTime < this._maxDiagnosticDelayMs) {
            const currentDiagnostics = this._getDiagnostics();
            const newDiagnostics = this._filterDiagnosticsMap(
                currentDiagnostics,
                startingDiagnostics
            );

            // If we found new diagnostics that weren't in the last check, return them
            if (
                this._hasDifferentDiagnostics(
                    newDiagnostics,
                    this._filterDiagnosticsMap(lastDiagnostics, startingDiagnostics)
                )
            ) {
                return currentDiagnostics;
            }

            lastDiagnostics = currentDiagnostics;
            await delayMs(1000); // Poll every second
        }

        // Return the last known diagnostics if we timeout
        return lastDiagnostics;
    }

    private _hasDifferentDiagnostics(
        diag1: Map<string, vscode.Diagnostic[]>,
        diag2: Map<string, vscode.Diagnostic[]>
    ): boolean {
        if (diag1.size !== diag2.size) {
            return true;
        }

        for (const [path, diagnostics] of diag1) {
            const otherDiagnostics = diag2.get(path);
            if (!otherDiagnostics || diagnostics.length !== otherDiagnostics.length) {
                return true;
            }

            for (let i = 0; i < diagnostics.length; i++) {
                const d1 = diagnostics[i];
                const d2 = otherDiagnostics[i];
                if (
                    d1.range.start.line !== d2.range.start.line ||
                    d1.range.end.line !== d2.range.end.line ||
                    d1.message !== d2.message ||
                    d1.severity !== d2.severity
                ) {
                    return true;
                }
            }
        }
        return false;
    }
}

/**
 * Tool to open URLs in the default browser.
 */
class OpenBrowserTool extends ToolBase<LocalToolType> {
    constructor() {
        super(LocalToolType.openBrowser, ToolSafety.Safe);
    }

    public readonly description: string = `\
Open a URL in the default browser.

1. The tool takes in a URL and opens it in the default browser.
2. The tool does not return any content. It is intended for the user to visually inspect and interact with the page. You will not have access to it.
3. You should not use \`open-browser\` on a URL that you have called the tool on before in the conversation history, because the page is already open in the user's browser and the user can see it and refresh it themselves. Each time you call \`open-browser\`, it will jump the user to the browser window, which is highly annoying to the user.`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            url: {
                type: "string",
                description: "The URL to open in the browser.",
            },
        },
        required: ["url"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        try {
            const url = toolInput.url as string;
            const uri = vscode.Uri.parse(url);
            const success = await vscode.env.openExternal(uri);
            if (!success) {
                return errorToolResponse(
                    `Failed to open ${url} in browser: system denied the request`
                );
            }
            return successToolResponse(`Opened ${url} in browser`);
        } catch (e: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            return errorToolResponse(`Failed to open URL in browser: ${e.message ?? ""}`);
        }
    }
}

/**
 * A tool that returns diagnostics from the IDE.
 */
class DiagnosticsTool extends ToolBase<LocalToolType> {
    constructor(private readonly _workspaceManager: WorkspaceManager) {
        super(LocalToolType.diagnostics, ToolSafety.Safe);
    }

    public readonly description: string =
        "Get issues (errors, warnings, etc.) from the IDE. You must provide the paths of the files for which you want to get issues.";

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            paths: {
                type: "array",
                items: {
                    type: "string",
                },
                description: "Required list of file paths to get issues for from the IDE.",
            },
        },
        required: ["paths"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal
    ): Promise<ToolUseResponse> {
        try {
            const paths = toolInput.paths as string[];
            const allDiagnostics = this._getDiagnostics();

            if (!paths || paths.length === 0) {
                return errorToolResponse("No paths provided.");
            }

            // If paths are provided, filter diagnostics to only those paths
            const filteredDiagnostics = new Map<string, vscode.Diagnostic[]>();
            for (const path of paths) {
                // Try to find diagnostics for this path
                for (const [diagPath, diagnostics] of allDiagnostics.entries()) {
                    if (diagPath.includes(path)) {
                        filteredDiagnostics.set(diagPath, diagnostics);
                    }
                }
            }

            // Format the diagnostics
            const diagnosticsString = await formatDiagnostics(
                filteredDiagnostics,
                this._workspaceManager
            );

            if (diagnosticsString.trim() === "") {
                return successToolResponse("No diagnostics found.");
            }

            return successToolResponse(
                `The IDE reports the following issues:\n${diagnosticsString}`
            );
        } catch (e: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            return errorToolResponse(`Failed to get diagnostics: ${e.message ?? ""}`);
        }
    }

    /**
     * Get all current diagnostics from VSCode
     */
    private _getDiagnostics(): Map<string, vscode.Diagnostic[]> {
        const result = new Map<string, vscode.Diagnostic[]>();
        vscode.languages.getDiagnostics().forEach(([uri, diagnostics]) => {
            if (diagnostics.length > 0) {
                result.set(uri.fsPath, diagnostics);
            }
        });
        return result;
    }
}

/**
 * A tool that reads output from the most recently used VSCode terminal.
 */
/**
 * A tool that retrieves information from the codebase using git commit history.
 * This tool uses the GitCommitIndexer to get the checkpoint ID for git commits
 * and uses that for codebase retrieval.
 */
export class GitCommitRetrievalTool extends ToolBase<LocalToolType> {
    private readonly _logger = getLogger("GitCommitRetrievalTool");
    // Limiting to 5k characters.
    private readonly _maxRetrievalSize: number = 5_000;

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _gitCommitIndexer: GitCommitIndexer
    ) {
        super(LocalToolType.gitCommitRetrieval, ToolSafety.Safe);
    }

    public readonly description: string = `\
This tool is Augment's context engine with git commit history awareness. It:
1. Takes in a natural language description of the code you are looking for;
2. Uses the git commit history as the only context for retrieval;
3. Otherwise functions like the standard codebase-retrieval tool.`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            information_request: {
                type: "string",
                description: "A description of the information you need.",
            },
        },
        required: ["information_request"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        chatHistory: Exchange[],
        abortSignal: AbortSignal
    ): Promise<ToolUseResponse> {
        const requestId = this._apiServer.createRequestId();
        try {
            const informationRequest = toolInput.information_request as string;

            // Get the checkpoint ID from the GitCommitIndexer
            const checkpointId = this._gitCommitIndexer.getCheckpointId();

            if (!checkpointId) {
                this._logger.debug(
                    "No git commit checkpoint ID available, using standard retrieval"
                );
            } else {
                this._logger.debug(`Using git commit checkpoint ID: ${checkpointId}`);
            }

            // Create a blobs object with the checkpoint ID
            const blobs = {
                checkpointId: checkpointId,
                addedBlobs: [],
                deletedBlobs: [],
            };

            // Call the API server with the blobs
            const result = await this._apiServer.agentCodebaseRetrieval(
                requestId,
                informationRequest,
                blobs,
                chatHistory,
                this._maxRetrievalSize,
                {
                    disableCodebaseRetrieval: true,
                    enableCommitRetrieval: true,
                },
                abortSignal
            );

            return successToolResponse(result.formattedRetrieval, requestId);
        } catch (e: any) {
            return errorToolResponse(
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                `Failed to retrieve codebase information: ${e.message ?? ""}`,
                requestId
            );
        }
    }
}

export class TerminalReadOutputTool extends ToolBase<LocalToolType> {
    constructor() {
        super(LocalToolType.readTerminal, ToolSafety.Safe);
    }

    public readonly description: string = `\
Read output from the active or most-recently used VSCode terminal.

By default, it reads all of the text visible in the terminal, not just the output of the most recent command.

If you want to read only the selected text in the terminal, set \`only_selected=true\` in the tool input.
Only do this if you know the user has selected text that you want to read.

Note that this is unrelated to the ${LocalToolType.listProcesses} and ${LocalToolType.readProcess} tools, which interact with processes that were launched with the "launch-process" tool.`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            only_selected: {
                type: "boolean",
                description: "Whether to read only the selected text in the terminal.",
            },
        },
        required: [],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal
    ): Promise<ToolUseResponse> {
        try {
            const terminal = vscode.window.activeTerminal;
            if (!terminal) {
                return errorToolResponse("There are no open VSCode terminals.");
            }
            const onlySelected = toolInput.only_selected as boolean;
            const output = await this._getTerminalOutput(terminal, onlySelected);
            return successToolResponse(`\
Here is the output${onlySelected ? " (selected text only)" : ""} from the active or most-recently used terminal:

${output}`);
        } catch (e: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            return errorToolResponse(`Failed to read terminal output: ${e.message ?? ""}`);
        }
    }

    /**
     * Gets the output from a terminal using clipboard commands
     */
    private async _getTerminalOutput(
        terminal: vscode.Terminal,
        onlySelected: boolean
    ): Promise<string> {
        const oldClipboard = await vscode.env.clipboard.readText();
        try {
            terminal.show(true);
            await vscode.env.clipboard.writeText("");
            if (!onlySelected) {
                await vscode.commands.executeCommand("workbench.action.terminal.selectAll");
            }
            await vscode.commands.executeCommand("workbench.action.terminal.copySelection");
            const output = await vscode.env.clipboard.readText();
            await vscode.commands.executeCommand("workbench.action.terminal.clearSelection");
            return output;
        } finally {
            await vscode.env.clipboard.writeText(oldClipboard);
        }
    }
}

/**
 * A tool that spawns a new remote sub-agent.
 * This tool is only available to local agents running in VSCode, not to remote agents.
 */
class SpawnSubAgentTool extends ToolBase<LocalToolType> {
    private readonly _logger = getLogger("SpawnSubAgentTool");

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _globalState: AugmentGlobalState,
        private readonly _configListener: AugmentConfigListener
    ) {
        super(LocalToolType.spawnSubAgent, ToolSafety.Unsafe);
    }

    public readonly description: string = `\
Remote Agent

Create a new remote sub-agent that runs in an isolated environment. This tool allows you to spawn a sub-agent to work on tasks in parallel.

The sub-agent will:
1. Run in a completely isolated remote environment
2. Have access to the specified repository and branch
3. Continue running even when your local environment is shut down
4. Work independently on the given prompt

Use this when you want to delegate work to a sub-agent that can work in parallel on a specific task.`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            summary: {
                type: "string",
                description: "A short one-sentence summary of the task (for display purposes).",
            },
            prompt: {
                type: "string",
                description: "The detailed initial prompt/task for the sub-agent to work on.",
            },
            repo_url: {
                type: "string",
                description:
                    "The repository URL for the sub-agent to work with (e.g., 'https://github.com/user/repo').",
            },
            branch: {
                type: "string",
                description:
                    "The git branch for the sub-agent to start from (e.g., 'main', 'develop').",
            },
        },
        required: ["summary", "prompt", "repo_url", "branch"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, unknown>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const requestId = this._apiServer.createRequestId();

        try {
            const summary = toolInput.summary as string;
            const prompt = toolInput.prompt as string;
            const repoUrl = toolInput.repo_url as string;
            const branch = toolInput.branch as string;

            // Validate inputs
            if (!summary || !summary.trim()) {
                return errorToolResponse("Summary cannot be empty.", requestId);
            }

            if (!prompt || !prompt.trim()) {
                return errorToolResponse("Prompt cannot be empty.", requestId);
            }

            if (!repoUrl || !repoUrl.trim()) {
                return errorToolResponse("Repository URL cannot be empty.", requestId);
            }

            if (!branch || !branch.trim()) {
                return errorToolResponse("Branch cannot be empty.", requestId);
            }

            // Check if user is authenticated with GitHub
            try {
                const githubAuthStatus = await this._apiServer.isUserGithubConfigured();
                if (!githubAuthStatus.is_configured) {
                    return errorToolResponse(
                        "GitHub authentication is required to spawn remote agents. " +
                            "Please authenticate with GitHub through the Augment settings panel.",
                        requestId
                    );
                }
            } catch (error) {
                this._logger.error("Failed to check GitHub authentication status", error);
                return errorToolResponse(
                    "Failed to verify GitHub authentication. Please try again or check your connection.",
                    requestId
                );
            }

            // Validate repository URL format
            try {
                new URL(repoUrl);
            } catch {
                return errorToolResponse("Invalid repository URL format.", requestId);
            }

            this._logger.debug(
                `Creating remote sub-agent with prompt: "${prompt}", repo: ${repoUrl}, branch: ${branch}`
            );

            // Create the workspace setup for the remote agent
            const workspaceSetup = {
                /* eslint-disable @typescript-eslint/naming-convention */
                starting_files: {
                    github_commit_ref: {
                        repository_url: repoUrl,
                        git_ref: branch,
                    },
                },
                /* eslint-enable @typescript-eslint/naming-convention */
            };

            const mcpServerConfigs = this._configListener.config.mcpServers.map((server) => ({
                command: server.command,
                args: server.args ?? [],
                env: server.env ?? {},
                /* eslint-disable @typescript-eslint/naming-convention */
                use_shell_interpolation: server.useShellInterpolation ?? false,
                /* eslint-enable @typescript-eslint/naming-convention */
                name: server.name,
                disabled: server.disabled ?? false,
            }));

            const requestDetails: RemoteAgentChatRequestDetails = {
                /* eslint-disable @typescript-eslint/naming-convention */
                request_nodes: [
                    {
                        id: 1,
                        type: ChatRequestNodeType.TEXT,
                        text_node: {
                            content: prompt,
                        },
                    },
                ],
                user_guidelines: this._configListener.config.chat.userGuidelines || "",
                workspace_guidelines: "", // TODO: Add workspace guidelines support
                agent_memories: "", // TODO: Add agent memories support
                mcp_servers: mcpServerConfigs,
                /* eslint-enable @typescript-eslint/naming-convention */
            };

            // Get the default setup script (same logic as remote agent webview)
            const setupScript = await this._getDefaultSetupScript();

            // Call the create remote agent API (similar to RemoteAgentsMessenger)
            const response = await this._apiServer.createRemoteAgent(
                workspaceSetup,
                requestDetails,
                undefined, // Default model
                setupScript, // Use the default setup script
                false // isSetupScriptAgent - not a setup script agent
            );

            // Check if the response indicates success
            if (!response.remote_agent_id || response.status === RemoteAgentStatus.agentFailed) {
                this._logger.error(`Failed to create remote sub-agent: status ${response.status}`);
                return errorToolResponse(
                    `Failed to create remote sub-agent: status ${response.status}`,
                    requestId
                );
            }

            this._logger.debug(
                `Successfully created remote sub-agent with ID: ${response.remote_agent_id}`
            );

            // Create a structured response that includes both human-readable text and machine-readable data
            const structuredResponse = {
                success: true,
                remoteAgentId: response.remote_agent_id,
                status: response.status,
                repository: repoUrl,
                branch: branch,
                summary: summary,
                prompt: prompt,
                message:
                    `Successfully created remote sub-agent with ID: ${response.remote_agent_id}\n` +
                    `Status: ${response.status}\n` +
                    `Repository: ${repoUrl}\n` +
                    `Branch: ${branch}\n` +
                    `Summary: ${summary}\n` +
                    `Initial prompt: ${prompt}\n\n` +
                    `The sub-agent is now starting up and will begin working on the task. ` +
                    `You can monitor its progress through the Remote Agents panel in VSCode.`,
            };

            return successToolResponse(JSON.stringify(structuredResponse, null, 2), requestId);
        } catch (e: unknown) {
            this._logger.error("Failed to create remote sub-agent", e);
            return errorToolResponse(
                `Failed to create remote sub-agent: ${e instanceof Error ? e.message : String(e)}`,
                requestId
            );
        }
    }

    /**
     * Gets the default setup script using the same logic as the remote agent webview.
     * Returns the last used setup script from global state, or undefined if none was saved.
     */
    private async _getDefaultSetupScript(): Promise<string | undefined> {
        try {
            // Get the last used setup script path from global state
            const lastSetupScriptPath = this._globalState.get<string>(
                GlobalContextKey.lastRemoteAgentSetupScript
            );

            // If no script was previously saved, return undefined (no setup script)
            if (!lastSetupScriptPath) {
                this._logger.debug("No previous setup script found, using no setup script");
                return undefined;
            }

            // Try to find and read the setup script content
            const setupScriptsManager = new SetupScriptsManager(this._globalState);
            const availableScripts = await setupScriptsManager.listSetupScripts();

            const selectedScript = availableScripts.find(
                (script) => script.path === lastSetupScriptPath
            );

            if (selectedScript) {
                this._logger.debug(
                    `Using setup script: ${selectedScript.name} from ${selectedScript.path}`
                );
                return selectedScript.content;
            } else {
                this._logger.warn(`Previously used setup script not found: ${lastSetupScriptPath}`);
                return undefined;
            }
        } catch (error) {
            this._logger.error("Failed to get default setup script", error);
            return undefined;
        }
    }
}
