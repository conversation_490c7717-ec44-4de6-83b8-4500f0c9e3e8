import { Exchange, TerminalInfo } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { IPluginFileStore } from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import { getAgentSessionEventReporter } from "@augment-internal/sidecar-libs/src/metrics/agent-session-event-reporter";
import { AgentSessionEventName } from "@augment-internal/sidecar-libs/src/metrics/types";
import {
    checkShellAllowlist,
    getShellAllowlist,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-allowlist";
import { ShellProcessTools } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-process-tools";
import {
    getDefaultShell,
    isSupportedShell,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-utils";
import {
    errorToolResponse,
    successToolResponse,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import {
    ToolBase,
    ToolSafety,
    ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { ShellConfig, TerminalSettings } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { executeCommand } from "@augment-internal/sidecar-libs/src/tools/tool-utils";
import { delayMs } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { withTimeout } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { truncateMiddle } from "@augment-internal/sidecar-libs/src/utils/strings";
import {
    TruncateOptions,
    truncateWithMetadata,
} from "@augment-internal/sidecar-libs/src/utils/truncation-utils";
import {
    TruncatedContentType,
    UntruncatedContentManager,
} from "@augment-internal/sidecar-libs/src/utils/untruncated-content-manager";
import { spawn } from "child_process";
import path from "path";
import { split } from "shlex";
import * as vscode from "vscode";

import { getLogger } from "../logging";
import { AugmentGlobalState, FileStorageKey } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { isVsCodeVersionGte } from "../utils/environment";
import {
    createTempDirectory,
    deleteDirectory,
    directoryExists,
    fileExists,
    readDirectorySync,
    writeFileUtf8,
} from "../utils/fs-utils";
import { isAbsolutePathName } from "../utils/path-utils";
import { FileType } from "../utils/types";
import { LocalToolType } from "../webview-providers/tool-types";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { getCwdForTool } from "./tools-utils";

/**
 * A helper class to manage processes and their output, which is used to implement
 * various process tools.
 */
interface TerminalProcess {
    terminal: vscode.Terminal;
    command: string;
    lastCommand: string;
    output: string;
    killed: boolean;
    execution?: any;
    readStream?: AsyncIterable<string>;
    exitCode: number | null;
    seenStartEvent: boolean;
}

/**
 * Extended shell information with additional properties needed by terminal tools
 */
type ShellInfo = ShellConfig & {
    /** Path to a temporary directory that should be cleaned up when done */
    tempDir?: string;
};

function areSameShellInfo(a: ShellInfo, b: ShellInfo): boolean {
    return (
        a.name === b.name &&
        a.path === b.path &&
        a.args?.join(" ") === b.args?.join(" ") &&
        Object.keys(a.env ?? {}).join(" ") === Object.keys(b.env ?? {}).join(" ") &&
        Object.values(a.env ?? {}).join(" ") === Object.values(b.env ?? {}).join(" ")
    );
}

export class TerminalProcessTools extends DisposableService {
    private static _shellInfo: ShellInfo = TerminalProcessTools._getDefaultShell(); // Initialize with default, will be updated in _initializeShells
    private readonly _processes = new Map<number, TerminalProcess>();
    private readonly _waitResolvers = new Map<
        number,
        (result: { output: string; returnCode: number | null }) => void
    >();
    private _nextId = 1;
    private _shellExecutionListener?: vscode.Disposable;
    private _activePoller?: NodeJS.Timeout;
    private static _longRunningTerminal?: vscode.Terminal;
    private _terminalNeedsUpdate: boolean = false;
    private _logger = getLogger("TerminalProcessTools");
    private readonly _maxOutputLength = 63 * 1024; // 64KiB - 1KiB buffer for additional text from the tools themselves
    // Store all shells that pass capability checks
    private _supportedShells: Array<{
        shellInfo: ShellInfo;
        capability: TerminalCapabilityResult;
    }> = [];
    private _terminalSettings: TerminalSettings = { supportedShells: [] };
    private readonly _untruncatedContentManager?: UntruncatedContentManager;
    // Shell process tools for handling "exec" type shells
    private _shellProcessTools?: ShellProcessTools;

    // Ensure we only show a warning once (per VSCode session) if shell integration is not enabled.
    // Otherwise we might show it on every mode switch, which could be annoying.
    private static _showedShellIntegrationMessage: boolean = false;

    constructor(
        private readonly _extensionRoot: vscode.Uri,
        private readonly _globalState: AugmentGlobalState,
        private readonly _enableStart: number,
        private readonly _assetManager?: IPluginFileStore,
        private readonly _enableUntruncatedContentStorage: boolean = false
    ) {
        super();

        const shellIntegrationEnabled = vscode.workspace
            .getConfiguration("terminal.integrated.shellIntegration")
            .get("enabled");
        if (!shellIntegrationEnabled && !TerminalProcessTools._showedShellIntegrationMessage) {
            void vscode.window
                .showWarningMessage(
                    "Please enable 'Terminal > Integrated > Shell Integration: Enabled' in your VSCode settings for the Augment agent to be able to use terminals.",
                    "Open Settings"
                )
                .then((selection) => {
                    if (selection === "Open Settings") {
                        void vscode.commands.executeCommand(
                            "workbench.action.openSettings",
                            "@id:terminal.integrated.shellIntegration.enabled"
                        );
                    }
                });
            TerminalProcessTools._showedShellIntegrationMessage = true;
        }

        this._untruncatedContentManager = this._assetManager
            ? new UntruncatedContentManager(this._assetManager)
            : undefined;

        // Register for notifications when terminal settings change
        this.addDisposable(
            this._globalState.onDidChangeFileStorage<TerminalSettings>(
                FileStorageKey.terminalSettings,
                (event) => {
                    this._terminalSettings = event.value;
                    this._logger.verbose("Terminal settings changed");
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalSettingsChanged,
                        conversationId: "",
                    });
                    this._checkAndUpdateShell();
                }
            )
        );

        // Load initial settings
        void this._globalState
            .load<TerminalSettings>(FileStorageKey.terminalSettings)
            .then((settings) => {
                this._terminalSettings = settings ?? { supportedShells: [] };
                this._logger.verbose("Initial terminal settings loaded");
                this._checkAndUpdateShell();
            })
            .catch((error) => {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                this._logger.debug(`Error loading initial terminal settings: ${error.message}`);
                getAgentSessionEventReporter().reportEvent({
                    eventName: AgentSessionEventName.vsCodeTerminalErrorLoadingSettings,
                    conversationId: "",
                });
                this._terminalSettings = { supportedShells: [] };
            });

        // Find supported shells and set the default shell
        void this._initializeShells();

        // Try to use shell integration if available.
        // These APIs are only available in VSCode 1.93.0 and above.
        // TODO: Call them directly once we build against that.
        /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment */
        const window = vscode.window as any;
        if (window.onDidEndTerminalShellExecution && isVsCodeVersionGte("1.93.0")) {
            this._logger.verbose(`Registering for onDidEndTerminalShellExecution.`);
            // Store the disposable so we can clean it up later
            this._shellExecutionListener = window.onDidEndTerminalShellExecution(async (e: any) => {
                this._logger.verbose(
                    `Got onDidEndTerminalShellExecution event: ${e.execution?.commandLine?.value}, ${e.exitCode}, ${e.execution?.commandLine?.confidence}, ${e.terminal?.name}`
                );
                // Find the terminal process.
                // We want the last process with this terminal since many things can use the
                // long-running terminal.
                for (const [id, process] of Array.from(this._processes).reverse()) {
                    this._logger.verbose(
                        `Checking process ${id} for completion with ${e.execution === process.execution}.`
                    );
                    // Note that we do not check if the command that just ended is the exact same
                    // as the one we're expecting.  This is because VSCode sometimes corrupts that
                    // string.  While this might over-trigger, it hopefully won't, and we really
                    // don't want to miss this event and keep polling until the timeout.
                    if (
                        process.terminal === e.terminal &&
                        !process.killed &&
                        process.exitCode === null &&
                        (!process.execution || e.execution === process.execution)
                    ) {
                        if (!process.seenStartEvent) {
                            this._logger.verbose(
                                `Process ${id} completed before we saw start event.`
                            );
                            getAgentSessionEventReporter().reportEvent({
                                eventName: AgentSessionEventName.vsCodeTerminalMissedStartEvent,
                                conversationId: "",
                            });
                            break;
                        }
                        // Update exitCode and get output
                        this._logger.verbose(`Process ${id} is complete.`);
                        process.exitCode = e.exitCode ?? null;
                        if (e.execution.commandLine.value !== process.command) {
                            this._logger.debug(
                                `Command line for process ${id} does not match. Expected ${process.command} but got ${e.execution.commandLine.value}`
                            );
                            getAgentSessionEventReporter().reportEvent({
                                eventName: AgentSessionEventName.vsCodeTerminalBuggyExecutionEvents,
                                conversationId: "",
                            });
                        }
                        // In some cases where the string doesn't match exactly but is slightly
                        // wrong (e.g., due to physical wrapping), readStream seems to be correct.
                        // In other cases (e.g., zsh with p10k), it's empty and readStream is
                        // worse than using the clipboard.  We thus hope this generalizes.
                        // I love VSCode.
                        if (process.readStream && e.execution.commandLine.value.length > 0) {
                            // Wait a little bit to make sure the output is fully written.
                            // This also appears to fix an issue where `cwd` isn't
                            // updated on local machines right after a command is run
                            // in the terminal.
                            // NOTE(arun): This delay is a magic number: 1ms didn't work
                            // and a binary search indicated that 2ms seemed to work?
                            // Waiting for 10ms out of an abundance of caution.
                            await delayMs(10);
                            this._logger.debug(`Reading exact output for process ${id}`);
                            // Even if the process is complete, readStream calls might still hang,
                            // so we use a timeout here.
                            const rawOutput = await this._readProcessStreamWithTimeout(
                                process.readStream,
                                id
                            );
                            if (rawOutput === undefined) {
                                // Timed out without reading any data, fall back to clipboard
                                this._logger.debug(
                                    `Process ${id} timed out without reading data, falling back to clipboard`
                                );
                                process.output = await this._getOutputFromClipboard(id);
                            } else if (this._isBuggyOutput(rawOutput)) {
                                this._logger.debug(
                                    `Buggy output detected for process ${id}. Please upgrade VSCode.`
                                );
                                getAgentSessionEventReporter().reportEvent({
                                    eventName: AgentSessionEventName.vsCodeTerminalBuggyOutput,
                                    conversationId: "",
                                });
                                process.output = await this._getOutputFromClipboard(id);
                            } else {
                                // Strip control codes from the output
                                const cleanOutput = _stripControlCodes(rawOutput);

                                // Use our helper method to truncate the output
                                process.output = await this._truncateOutput(
                                    cleanOutput,
                                    id.toString()
                                );
                            }
                        } else {
                            process.output = await this._getOutputFromClipboard(id);
                        }

                        // For non-long-running terminals, dispose them.
                        // For long-running terminals, we just mark the process as complete.
                        if (process.terminal !== TerminalProcessTools._longRunningTerminal) {
                            process.terminal.dispose();
                        }
                        process.killed = true;

                        // Resolve any waiting promises
                        const resolver = this._waitResolvers.get(id);
                        if (resolver) {
                            resolver({
                                output: process.output,
                                returnCode: process.exitCode,
                            });
                        }
                        break;
                    }
                }
            });
        } else {
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalShellIntegrationNotAvailable,
                conversationId: "",
            });
        }

        if (window.onDidStartTerminalShellExecution) {
            this.addDisposable(
                window.onDidStartTerminalShellExecution((e: any) => {
                    this._logger.verbose(
                        `Got onDidStartTerminalShellExecution event: ${e.execution?.commandLine?.value}, ${e.execution?.commandLine?.confidence}, ${e.terminal?.name}`
                    );
                    for (const [id, process] of Array.from(this._processes).reverse()) {
                        this._logger.verbose(`Checking process ${id} for starting.`);
                        if (
                            process.terminal === e.terminal &&
                            !process.killed &&
                            process.exitCode === null
                        ) {
                            this._logger.verbose(`Process ${id} is starting.`);
                            process.seenStartEvent = true;
                            break;
                        }
                    }
                })
            );
        }
        /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment */

        // Listen for the terminal getting closed.
        this.addDisposable(
            vscode.window.onDidCloseTerminal(async (terminal) => {
                this._logger.verbose(`Got onDidCloseTerminal event: ${terminal.name}`);
                for (const [id, process] of this._processes) {
                    if (process.terminal === terminal && !process.killed) {
                        if (process.readStream) {
                            // If we do a for await we will hang forever.
                            process.output = await this._getOutputFromPossiblyIncompleteProcess(
                                process,
                                id
                            );
                        }
                        process.killed = true;
                        // Get the actual error code of the process.
                        process.exitCode = terminal.exitStatus?.code ?? -1;

                        const resolver = this._waitResolvers.get(id);
                        if (resolver) {
                            resolver({
                                output: process.output,
                                returnCode: process.exitCode,
                            });
                        }
                        break;
                    }
                }
            })
        );
    }

    /**
     * Checks if the current shell is an "exec" type shell
     */
    private _isExecShell(): boolean {
        return TerminalProcessTools._shellInfo.friendlyName === "exec";
    }

    /**
     * Checks if there's a saved shell preference and updates the current shell if needed
     * @returns True if the shell was updated, false otherwise
     */
    private _checkAndUpdateShell(): boolean {
        try {
            const savedShellName = this._terminalSettings.selectedShell;

            // If we have a saved shell preference and it's different from the current one
            if (savedShellName && savedShellName !== TerminalProcessTools._shellInfo.friendlyName) {
                // Find the shell in our supported shells
                const savedShell = this._supportedShells.find(
                    (s) => s.shellInfo.friendlyName === savedShellName
                );
                if (savedShell) {
                    // Update the current shell if it's running.
                    TerminalProcessTools._shellInfo = savedShell.shellInfo;
                    if (TerminalProcessTools._longRunningTerminal) {
                        this._terminalNeedsUpdate = true;
                    }
                    if (this._isExecShell()) {
                        this._shellProcessTools?.cleanup();
                        this._shellProcessTools = new ShellProcessTools(
                            TerminalProcessTools._shellInfo
                        );
                    }
                    this._logger.debug(
                        `Updated shell to saved preference: ${TerminalProcessTools._shellInfo.friendlyName}`
                    );
                    return true;
                }
            }
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error checking for shell updates: ${error.message}`);
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalErrorCheckingForShellUpdates,
                conversationId: "",
            });
        }

        return false;
    }

    public async launch(
        command: string,
        cwd: string | undefined,
        abortSignal: AbortSignal,
        useLongRunningTerminal: boolean
    ): Promise<number | string> {
        // Check if we need to update the shell based on saved preferences
        this._checkAndUpdateShell();

        // If using "exec" shell, delegate to ShellProcessTools
        if (this._isExecShell() && this._shellProcessTools) {
            return this._shellProcessTools.launch(command, cwd, abortSignal);
        }

        const id = this._nextId++;
        let isNewTerminal = false;
        let terminal: vscode.Terminal;
        if (useLongRunningTerminal) {
            // If the long-running terminal is gone, create a new one.
            if (
                !TerminalProcessTools._longRunningTerminal ||
                TerminalProcessTools._longRunningTerminal.exitStatus
            ) {
                TerminalProcessTools._longRunningTerminal = await this._createTerminal(
                    "Augment",
                    cwd
                );
                isNewTerminal = true;
            } else if (TerminalProcessTools._longRunningTerminal && this._terminalNeedsUpdate) {
                // If the terminal needs an update, dispose it and create a new one.
                TerminalProcessTools._longRunningTerminal.dispose();
                TerminalProcessTools._longRunningTerminal = await this._createTerminal(
                    "Augment",
                    cwd
                );
                this._terminalNeedsUpdate = false;
                isNewTerminal = true;
            } else if (
                // If there is an existing long-running terminal that doesn't have shell
                // integration, dispose it and create a new one.  We can't reuse the same terminal
                // because without shell integration we wouldn't know its ending cwd, which would
                // confuse the model.  But we want to leave the terminal open after the process
                // completes and until the next one starts so the user can see the output.
                TerminalProcessTools._longRunningTerminal &&
                !this._terminalHasShellIntegration(TerminalProcessTools._longRunningTerminal)
            ) {
                TerminalProcessTools._longRunningTerminal.dispose();
                TerminalProcessTools._longRunningTerminal = await this._createTerminal(
                    `Augment - ${command}`,
                    cwd
                );
                isNewTerminal = true;
            }
            terminal = TerminalProcessTools._longRunningTerminal;
        } else {
            terminal = await this._createTerminal(`Augment - ${command}`, cwd);
        }

        // If we're trying to use the long-running terminal but it's in use, fail.
        if (
            useLongRunningTerminal &&
            Array.from(this._processes.values()).some(
                (p) => p.terminal === terminal && p.exitCode === null && !p.killed
            )
        ) {
            return `\
Cannot launch another waiting process while another waiting process is running. The id of the existing waiting process is ${id}.
Please wait until this process is complete (you can use a tool for this purpose), or launch the new process as a background process with \`wait=false\`.`;
        }

        terminal.show(true);

        let lastCommand = "";
        if (terminal === TerminalProcessTools._longRunningTerminal && !isNewTerminal) {
            let noop = "";
            if (!isVsCodeVersionGte("1.98.0") || !this._isTerminalBasicallySupported()) {
                noop = this.shellName === "powershell" ? "#" : ":";
            }
            // Send a noop command to make sure we're at the prompt
            /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access */
            if (this._terminalHasShellIntegration(terminal)) {
                (terminal as any).shellIntegration.executeCommand(noop);
            } else {
                terminal.sendText(noop);
            }
            /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access */
            // Block execution until the above noop command completes.
            // Without this we sometimes fail to read the output of the command....
            let pollerInterval: NodeJS.Timeout | null = null;
            // Flag to ensure only one instance of the async function runs at a time.
            let isExecuting = false;
            await withTimeout(
                new Promise((resolve) => {
                    pollerInterval = setInterval(() => {
                        // Only start a new execution if not already executing
                        if (!isExecuting) {
                            isExecuting = true;
                            void (async () => {
                                try {
                                    this._logger.verbose(`Polling for noop command result`);
                                    lastCommand = (await this._getLastCommand(terminal))
                                        .lastCommand;
                                    if (lastCommand === noop) {
                                        if (pollerInterval) {
                                            clearInterval(pollerInterval);
                                            pollerInterval = null;
                                        }
                                        this._logger.debug(`Successfully got noop command result.`);
                                        resolve(void 0);
                                    }
                                } finally {
                                    // Always reset the flag when done
                                    isExecuting = false;
                                }
                            })();
                        }
                    }, 100);
                }),
                1000
            )
                .catch(() => {
                    this._logger.debug(`Timed out waiting for noop command to complete`);
                    getAgentSessionEventReporter().reportEvent({
                        eventName:
                            AgentSessionEventName.vsCodeTerminalTimedOutWaitingForNoopCommand,
                        conversationId: "",
                    });
                })
                .finally(() => {
                    if (pollerInterval) {
                        clearInterval(pollerInterval);
                        pollerInterval = null;
                    }
                });
        }

        const processInfo: TerminalProcess = {
            terminal,
            command,
            lastCommand,
            output: "",
            killed: false,
            readStream: undefined,
            execution: undefined,
            exitCode: null,
            seenStartEvent: false,
        };
        this._processes.set(id, processInfo);

        /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment */
        // If we're using the long-running terminal, shell integration may already exist.
        if (this._terminalHasShellIntegration(terminal)) {
            const execution = (terminal as any).shellIntegration.executeCommand(command);
            processInfo.execution = execution;
            processInfo.readStream = execution.read();
            this._logger.debug(`Using existing shell integration for command: ${command}`);
        } else if (
            this._isTerminalBasicallySupported() &&
            (vscode.window as any).onDidChangeTerminalShellIntegration
        ) {
            // We have to wait for shell integration to be enabled.
            let shellIntegrationListener: vscode.Disposable | undefined;
            shellIntegrationListener = (vscode.window as any).onDidChangeTerminalShellIntegration(
                (e: any) => {
                    if (e.terminal === terminal && e.shellIntegration && !processInfo.readStream) {
                        const execution = e.shellIntegration.executeCommand(command);
                        processInfo.execution = execution;
                        processInfo.readStream = execution.read();
                        this._logger.debug(`Using shell integration for command: ${command}`);
                        if (shellIntegrationListener) {
                            shellIntegrationListener.dispose();
                            shellIntegrationListener = undefined;
                        }
                    }
                }
            );
            // Fallback to sendText if there is no shell integration within 2 seconds of launching.
            // Yes, this is the official way to to use the API.
            setTimeout(() => {
                if (!processInfo.readStream) {
                    terminal.sendText(command);
                    this._logger.debug(`Failed to use shell integration for command: ${command}`);
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalFailedToUseShellIntegration,
                        conversationId: "",
                    });
                }
                if (shellIntegrationListener) {
                    shellIntegrationListener.dispose();
                    shellIntegrationListener = undefined;
                }
            }, 2000);
        } else {
            this._logger.debug(`Not using shell integration for command: ${command}`);
            terminal.sendText(command);
        }
        /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment */

        // Handle abort signal
        abortSignal.addEventListener("abort", () => {
            void this.kill(id);
        });

        return id;
    }

    /**
     * Creates a terminal with consistent configuration
     */
    private async _createTerminal(name: string, cwd: string | undefined): Promise<vscode.Terminal> {
        /* eslint-disable @typescript-eslint/naming-convention */
        let env: Record<string, string> | undefined =
            this.shellName === "bash" || this.shellName === "zsh" || this.shellName === "fish"
                ? {
                      PAGER: "cat",
                      LESS: "-FX",
                      GIT_PAGER: "cat",
                  }
                : this.shellName === "powershell"
                  ? {
                        GIT_PAGER: "",
                    }
                  : {};
        /* eslint-enable @typescript-eslint/naming-convention */
        // Add any shell-specific environment variables from shellInfo
        if (TerminalProcessTools._shellInfo.env) {
            env = { ...env, ...TerminalProcessTools._shellInfo.env };
        }
        // If no environment variables are set, use undefined instead of an empty object
        if (env && Object.keys(env).length === 0) {
            env = undefined;
        }

        const terminal = vscode.window.createTerminal({
            name,
            shellPath: TerminalProcessTools._shellInfo.path ?? this.shellName,
            shellArgs: TerminalProcessTools._shellInfo.args,
            cwd,
            env,
            iconPath: {
                light: vscode.Uri.joinPath(this._extensionRoot, "media", "panel-icon-light.svg"),
                dark: vscode.Uri.joinPath(this._extensionRoot, "media", "panel-icon-dark.svg"),
            },
            isTransient: true,
        });

        // Run startup script if available
        if (this._terminalSettings.startupScript && this._terminalSettings.startupScript.trim()) {
            /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access */
            if (this._terminalHasShellIntegration(terminal)) {
                (terminal as any).shellIntegration.executeCommand(
                    this._terminalSettings.startupScript
                );
            } else {
                terminal.sendText(this._terminalSettings.startupScript);
            }
            /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access */

            // Wait a bit to let the script execute before continuing.
            // Unfortunately, it's difficult to know if the startup script has completed.
            // Executing it with executeCommand/sendText means we can get an arbitrary number of
            // execution complete messages, each of which can be one or more lines.
            // We could instead write it to a temp file and execute that or add a special marker,
            // but we'd then expose that to the user and we'd have to figure out how to handle
            // timeouts.  So we just wait a bit and hope it's done.
            await delayMs(100);
        }

        return terminal;
    }

    public async kill(
        id: number
    ): Promise<{ output: string; killed: boolean; returnCode: number | null } | undefined> {
        // If using "exec" shell, delegate to ShellProcessTools
        if (this._isExecShell() && this._shellProcessTools) {
            return this._shellProcessTools.kill(id);
        }

        const process = this._processes.get(id);
        if (!process) {
            return undefined;
        }
        if (process.killed) {
            return { output: process.output, killed: false, returnCode: process.exitCode };
        }

        this._logger.verbose(`Killing process ${id}`);
        if (process.terminal === TerminalProcessTools._longRunningTerminal) {
            // For long-running terminal, send SIGINT (Ctrl+C) to terminate the current process.
            process.terminal.sendText("\x03", false);
        } else {
            process.terminal.dispose();
        }
        process.killed = true;
        process.exitCode = -1;
        process.output = await this._getOutputFromPossiblyIncompleteProcess(process, id);
        return { output: process.output, killed: true, returnCode: process.exitCode };
    }

    public isInLongRunningTerminal(id: number): boolean {
        // "exec" shells don't use long-running terminals
        if (this._isExecShell()) {
            return false;
        }

        const process = this._processes.get(id);
        return !!process && process.terminal === TerminalProcessTools._longRunningTerminal;
    }

    public async readOutput(
        id: number
    ): Promise<{ output: string; returnCode: number | null } | undefined> {
        // If using "exec" shell, delegate to ShellProcessTools
        if (this._isExecShell() && this._shellProcessTools) {
            return this._shellProcessTools.readOutput(id);
        }

        const process = this._processes.get(id);
        if (!process) {
            return undefined;
        }

        // For completed processes, return stored output
        if (process.exitCode !== null || process.killed) {
            return {
                output: process.output,
                returnCode: process.exitCode,
            };
        }

        this._logger.verbose(`Reading output for process ${id}`);
        const output = await this._getOutputFromPossiblyIncompleteProcess(process, id);

        return {
            output,
            returnCode: process.exitCode,
        };
    }

    public writeInput(id: number, input: string): boolean {
        // If using "exec" shell, delegate to ShellProcessTools
        if (this._isExecShell() && this._shellProcessTools) {
            return this._shellProcessTools.writeInput(id, input);
        }

        const process = this._processes.get(id);
        if (!process || process.killed) {
            return false;
        }
        process.terminal.sendText(input);
        return true;
    }

    public listProcesses(): {
        id: number;
        command: string;
        state: "running" | "completed" | "killed";
        returnCode: number | null;
    }[] {
        // If using "exec" shell, delegate to ShellProcessTools
        if (this._isExecShell() && this._shellProcessTools) {
            return this._shellProcessTools.listProcesses();
        }

        const result = [];
        for (const [id, process] of this._processes.entries()) {
            const state: "running" | "completed" | "killed" = process.killed
                ? process.exitCode === -1
                    ? "killed"
                    : "completed"
                : "running";
            result.push({
                id,
                command: process.command,
                state,
                returnCode: process.exitCode,
            });
        }
        return result;
    }

    public waitForProcess(
        id: number,
        timeoutSeconds: number,
        abortSignal: AbortSignal
    ): Promise<{ output: string; returnCode: number | null }> {
        // If using "exec" shell, delegate to ShellProcessTools
        if (this._isExecShell() && this._shellProcessTools) {
            return this._shellProcessTools.waitForProcess(id, timeoutSeconds, abortSignal);
        }

        return new Promise((resolve) => {
            void (async () => {
                const process = this._processes.get(id);
                if (!process) {
                    resolve({ output: "", returnCode: null });
                    return;
                }

                // If process already completed, return immediately
                if (process.exitCode !== null) {
                    resolve({
                        output: process.output,
                        returnCode: process.exitCode,
                    });
                    return;
                }

                const timer = setTimeout(() => {
                    // Process still running after timeout
                    this._logger.verbose(
                        `Process ${id} still running after ${timeoutSeconds} seconds.  Timing out.`
                    );
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalWaitTimeout,
                        conversationId: "",
                    });
                    this._waitResolvers.delete(id);
                    if (this._activePoller) {
                        clearInterval(this._activePoller);
                        this._activePoller = undefined;
                    }
                    void this.readOutput(id).then((output) => {
                        resolve({
                            output: output?.output ?? "",
                            returnCode: null,
                        });
                    });
                }, timeoutSeconds * 1000);

                this._logger.verbose(
                    `Waiting for process ${id} to complete.  Shell listener is ${this._shellExecutionListener ? "registered" : "not registered"}`
                );

                // As a backup, we poll to see if the process has completed.
                // This may be needed if we cannot listen for onDidEndTerminalShellExecution or if it
                // somehow missed the event.
                if (process.lastCommand === process.command) {
                    // Unfortunately, if the last command is the same as the current one and we don't
                    // know if we'll get the completion events, we don't have a way to know when the
                    // process has completed.  So we just show the model the current output and let
                    // it figure out what to do.  This is a heuristic that prioritizes the common
                    // case. It isn't perfect but we don't live in a perfect world.
                    this._logger.debug(`Last command is the same as the current one.`);
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalLastCommandIsSameAsCurrent,
                        conversationId: "",
                    });
                    const result = await this.readOutput(id);
                    if (!result) {
                        this._logger.debug(`Failed to read output for process ${id}`);
                        getAgentSessionEventReporter().reportEvent({
                            eventName: AgentSessionEventName.vsCodeTerminalFailedToReadOutput,
                            conversationId: "",
                        });
                        resolve({ output: "", returnCode: null });
                    } else {
                        resolve(result);
                    }
                } else {
                    if (this._activePoller) {
                        clearInterval(this._activePoller);
                        this._activePoller = undefined;
                    }
                    this._activePoller = setInterval(() => {
                        void (async () => {
                            this._logger.verbose(`Polling to see if process ${id} is done.`);
                            const { lastCommand, isComplete } = await this._getLastCommand(
                                process.terminal
                            );
                            // For multi-line commands, we can't directly check if the commands
                            // match, as the version in the terminal may have extra characters
                            // (e.g., extra lines can start with >).  As a heuristic, we thus check
                            // if the first lines match.
                            const firstLinesMatch =
                                lastCommand.split("\n")[0] === process.command.split("\n")[0];
                            if (isComplete || (isComplete === undefined && firstLinesMatch)) {
                                this._logger.debug(
                                    `Polling determined process ${id} is done with ${isComplete} and ${firstLinesMatch}.`
                                );
                                getAgentSessionEventReporter().reportEvent({
                                    eventName:
                                        AgentSessionEventName.vsCodeTerminalPollingDeterminedProcessIsDone,
                                    conversationId: "",
                                });
                                clearTimeout(timer);
                                clearInterval(this._activePoller);
                                this._activePoller = undefined;
                                const result = await this.readOutput(id);
                                process.output = result?.output ?? "";
                                process.exitCode = result?.returnCode ?? null;
                                process.killed = true;
                                if (!result) {
                                    this._logger.debug(`Failed to read output for process ${id}`);
                                    getAgentSessionEventReporter().reportEvent({
                                        eventName:
                                            AgentSessionEventName.vsCodeTerminalFailedToReadOutput,
                                        conversationId: "",
                                    });
                                    resolve({ output: "", returnCode: null });
                                } else {
                                    resolve(result);
                                }
                            }
                        })();
                    }, 1000);
                    // Store resolver to be called when process completes
                    this._waitResolvers.set(id, (result) => {
                        clearTimeout(timer);
                        clearInterval(this._activePoller);
                        this._activePoller = undefined;
                        this._waitResolvers.delete(id);
                        resolve(result);
                    });
                }

                const abortHandler = () => {
                    clearTimeout(timer);
                    clearInterval(this._activePoller);
                    this._activePoller = undefined;
                    this._waitResolvers.delete(id);
                    resolve({ output: "", returnCode: null });
                };
                void abortSignal.addEventListener("abort", abortHandler);
            })();
        });
    }

    private async _getOutputFromPossiblyIncompleteProcess(process: TerminalProcess, id: number) {
        if (process.readStream) {
            const rawOutput = await this._readProcessStreamWithTimeout(process.readStream, id);
            if (rawOutput === undefined) {
                // Timed out without reading any data, fall back to clipboard
                this._logger.debug(
                    `Process ${id} timed out without reading data in incomplete process, falling back to clipboard`
                );
                return await this._getOutputFromClipboard(id);
            }
            const cleanOutput = _stripControlCodes(rawOutput);

            // Use our helper method to truncate the output
            const result = await this._truncateOutput(cleanOutput, id.toString());
            this._logger.debug(
                `Reading exact intermediate output for process ${id} got ${result.length} character(s).`
            );
            return result;
        } else {
            if (!process.terminal.exitStatus) {
                process.terminal.show(true);
            }
            return await this._getOutputFromClipboard(id);
        }
    }

    private async _readProcessStreamWithTimeout(
        readStream: AsyncIterable<string>,
        id: number
    ): Promise<string | undefined> {
        let rawOutput = "";
        let hasReadData = false;
        try {
            // Use a non-blocking approach to read only currently available output
            // We'll create a function to read a single chunk with a timeout
            const readChunkWithTimeout = async (
                iterator: AsyncIterator<string>
            ): Promise<IteratorResult<string>> => {
                let timeoutId: NodeJS.Timeout | undefined;
                let isResolved = false;

                // Create a promise that resolves after a short timeout
                const timeoutPromise = new Promise<IteratorResult<string>>((resolve) => {
                    timeoutId = setTimeout(() => {
                        if (!isResolved) {
                            isResolved = true;
                            this._logger.debug(`Read timeout occurred for process ${id}`);
                            resolve({ done: true, value: undefined });
                        }
                    }, 100);
                });

                // Race between getting the next chunk and timing out
                const result = await Promise.race([
                    iterator.next().then((result) => {
                        if (!isResolved) {
                            isResolved = true;
                            // Clear the timeout to prevent it from executing later
                            if (timeoutId) {
                                clearTimeout(timeoutId);
                            }
                        }
                        return result;
                    }),
                    timeoutPromise,
                ]);

                return result;
            };

            // Get the iterator from the AsyncIterable
            const iterator = readStream[Symbol.asyncIterator]();

            // Read chunks until we timeout (no more immediate data)
            let result = await readChunkWithTimeout(iterator);
            while (!result.done) {
                if (result.value !== undefined) {
                    rawOutput += result.value;
                    hasReadData = true;
                }
                result = await readChunkWithTimeout(iterator);
            }
        } catch (e: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error reading stream for process ${id}: ${e.message ?? ""}`);
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalFailedToReadOutput,
                conversationId: "",
            });
        }

        // Return undefined if we timed out without reading any data
        if (!hasReadData) {
            this._logger.debug(
                `Process ${id} timed out without reading any data, returning undefined`
            );
            return undefined;
        }

        return rawOutput;
    }

    private async _getOutputFromClipboard(id: number) {
        const terminal = this._processes.get(id)?.terminal;
        if (
            terminal &&
            terminal === TerminalProcessTools._longRunningTerminal &&
            !this._terminalHasShellIntegration(terminal)
        ) {
            return (await this._getLastCommandAndOutputFallback()).output;
        }
        getAgentSessionEventReporter().reportEvent({
            eventName: AgentSessionEventName.vsCodeTerminalReadingApproximateOutput,
            conversationId: "",
        });
        const oldClipboard = await vscode.env.clipboard.readText();
        await vscode.env.clipboard.writeText("");
        await vscode.commands.executeCommand("workbench.action.terminal.copyLastCommandOutput");
        const output = await vscode.env.clipboard.readText();
        await vscode.env.clipboard.writeText(oldClipboard);
        this._logger.verbose(`Read approximate output from clipboard got ${output.trim()}`);

        // Use our helper method to truncate the output
        return await this._truncateOutput(output, id.toString());
    }

    private async _getLastCommand(terminal?: vscode.Terminal) {
        if (
            terminal &&
            terminal === TerminalProcessTools._longRunningTerminal &&
            !this._terminalHasShellIntegration(terminal)
        ) {
            const {
                command,
                output: _output,
                isComplete,
            } = await this._getLastCommandAndOutputFallback();
            return { lastCommand: command, isComplete };
        }
        const oldClipboard = await vscode.env.clipboard.readText();
        await vscode.env.clipboard.writeText("");
        await vscode.commands.executeCommand("workbench.action.terminal.copyLastCommand");
        const lastCommand = await vscode.env.clipboard.readText();
        await vscode.env.clipboard.writeText(oldClipboard);
        this._logger.verbose(`Read last command from clipboard got ${lastCommand.trim()}`);
        return { lastCommand };
    }

    /**
     * VSCode's terminal APIs for copying the last command and output
     * (workbench.action.terminal.copy*) don't seem to work when shell integrations are not
     * enabled.  So we use a fallback that seems to work more reliably.
     */
    private async _getLastCommandAndOutputFallback() {
        const oldClipboard = await vscode.env.clipboard.readText();
        await vscode.env.clipboard.writeText("");
        await vscode.commands.executeCommand("workbench.action.terminal.selectToPreviousCommand");
        await vscode.commands.executeCommand("workbench.action.terminal.copySelection");
        const lastCommandAndOutput = await vscode.env.clipboard.readText();
        await vscode.commands.executeCommand("workbench.action.terminal.selectAll");
        await vscode.commands.executeCommand("workbench.action.terminal.copyAndClearSelection");
        const allOutput = await vscode.env.clipboard.readText();
        await vscode.env.clipboard.writeText(oldClipboard);

        // Extract the command and the output.
        // Match common shell prompts more accurately
        // PowerShell: typically "PS path>" or just "PS>"
        // Bash: typically includes username@hostname:path$ or just "$"
        // Zsh: typically includes username@hostname:path% or just "%"
        // Default: catch other prompts with a generic pattern
        const prompt = _shellPrompts[this.shellName] ?? _shellPrompts["bash"];

        const firstLine = lastCommandAndOutput.split("\n").shift() ?? "";
        const command = firstLine.replace(prompt, "").trim();

        // Get the raw output by removing the first line (command)
        const rawOutput = lastCommandAndOutput.slice(firstLine.length + 1);
        // Remove prompt lines that might appear at the end of the output
        // Split by newlines, filter out lines that match the prompt pattern, and join back
        const outputLines = rawOutput.split("\n");
        const filteredLines = outputLines.filter((line) => !prompt.test(line));
        const output = filteredLines.join("\n");

        // Check whether the last non-empty line is a full prompt (with nothing after it).
        // Note that sometimes the result of `selectToPreviousCommand` will not contain the prompt
        // at the end (if the terminal is small enough or scrolled up and it's not onscreen?), so
        // we instead check the last non-empty line in the entire terminal output.
        const lastNonEmptyLine = allOutput
            .split("\n")
            .reverse()
            .find((line) => line.trim().length > 0);
        const isComplete =
            lastNonEmptyLine != null && lastNonEmptyLine.replace(prompt, "").trim().length === 0;

        this._logger.verbose(
            `Reading last command and output from clipboard got ${lastCommandAndOutput.trim()}, command: ${command}, output: ${output.trim()}, isComplete: ${isComplete}`
        );
        return { command, output, isComplete };
    }

    public closeAllProcesses() {
        // Clean up shell processes if using "exec" shell
        if (this._shellProcessTools) {
            this._shellProcessTools.closeAllProcesses();
        }

        // Clean up all terminal processes
        for (const process of this._processes.values()) {
            if (!process.killed) {
                if (process.terminal === TerminalProcessTools._longRunningTerminal) {
                    // Send SIGINT (Ctrl+C) to terminate the current process.
                    // We keep the terminal open so users can see the output.
                    TerminalProcessTools._longRunningTerminal.sendText("\x03", false);
                } else {
                    process.terminal.dispose();
                }
            }
        }
        this._processes.clear();

        if (this._activePoller) {
            clearInterval(this._activePoller);
            this._activePoller = undefined;
        }
    }

    public cleanup() {
        this.closeAllProcesses();
        // Clean up shell execution listener
        if (this._shellExecutionListener) {
            this._shellExecutionListener.dispose();
            this._shellExecutionListener = undefined;
        }
        // Clean up any temporary directories
        void this._cleanupTempDirectories();
        this.dispose();
    }

    /**
     * Cleans up any temporary directories created for shells
     */
    private async _cleanupTempDirectories(): Promise<void> {
        // Clean up temporary directories for all shells
        for (const shell of this._supportedShells) {
            if (shell.shellInfo.tempDir) {
                try {
                    await deleteDirectory(shell.shellInfo.tempDir);
                } catch (error: any) {
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                    this._logger.debug(`Error cleaning up temporary directory: ${error.message}`);
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalErrorCleaningUpTempDir,
                        conversationId: "",
                    });
                }
            }
        }

        // Also clean up the current shell's temp directory if it exists
        if (TerminalProcessTools._shellInfo.tempDir) {
            try {
                await deleteDirectory(TerminalProcessTools._shellInfo.tempDir);
            } catch (error: any) {
                this._logger.debug(
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                    `Error cleaning up current shell temporary directory: ${error.message}`
                );
                getAgentSessionEventReporter().reportEvent({
                    eventName: AgentSessionEventName.vsCodeTerminalErrorCleaningUpTempDir,
                    conversationId: "",
                });
            }
        }
    }

    /**
     * Gets the current capability result for the active shell
     */
    private get _currentCapability(): TerminalCapabilityResult {
        if (this._supportedShells.length > 0) {
            // Find the capability for the current shell
            const current = this._supportedShells.find(
                (s) =>
                    s.shellInfo.name === TerminalProcessTools._shellInfo.name &&
                    s.shellInfo.path === TerminalProcessTools._shellInfo.path
            );

            if (current) {
                return current.capability;
            }
        }

        // Default to unknown if we don't have capability information
        return {
            capability: TerminalCapability.unknown,
            details: "Capability information not available",
        };
    }

    /**
     * Checks if the terminal has basic support (may have issues but is usable)
     */
    private _isTerminalBasicallySupported(): boolean {
        const capability = this._currentCapability.capability;
        return this._isBasicallySupported(capability);
    }

    // We consider a terminal to be basically supported if it has shell integration and execution events,
    // even if it has output issues or buggy execution events.
    private _isBasicallySupported(capability: TerminalCapability): boolean {
        return (
            capability === TerminalCapability.supported ||
            capability === TerminalCapability.buggyOutput ||
            capability === TerminalCapability.noisyOutput ||
            capability === TerminalCapability.noOutput ||
            capability === TerminalCapability.buggyExecutionEvents
        );
    }

    /**
     * Gets the terminal info for the long running terminal.
     */
    public getLongRunningTerminalInfo(): TerminalInfo | undefined {
        // "exec" shells don't use long-running terminals
        if (this._isExecShell()) {
            return undefined;
        }

        // `shellIntegration` is required for this feature, and in the API since 1.93.
        if (!this._isTerminalBasicallySupported() || !TerminalProcessTools._longRunningTerminal) {
            return undefined;
        }
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
        const cwd = (TerminalProcessTools._longRunningTerminal as any).shellIntegration?.cwd;
        if (!(cwd instanceof vscode.Uri)) {
            return undefined;
        }
        return {
            // NOTE(arun): For now, we only support a single long running terminal so
            // we can hardcode the ID to 0. In the future, we may want to support
            // multiple long running terminals, in which case we should use a different
            // ID for each one.
            // eslint-disable-next-line @typescript-eslint/naming-convention
            terminal_id: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            current_working_directory: cwd.fsPath,
        };
    }

    /**
     * Initializes the shells by finding all supported shells and setting the default shell
     */
    private async _initializeShells(): Promise<void> {
        const originalShellInfo = TerminalProcessTools._shellInfo;
        try {
            // Find all supported shells
            this._supportedShells = await this._findSupportedShells();

            // Set the default shell to the best one available
            if (this._supportedShells.length > 0) {
                const defaultProfile = this._getVSCodeDefaultProfile();
                // Sort by capability (best first), then by default profile (if any), but always put "exec" last
                this._supportedShells.sort((a, b) => {
                    // Always put "exec" shells last
                    if (
                        a.shellInfo.friendlyName === "exec" &&
                        b.shellInfo.friendlyName !== "exec"
                    ) {
                        return 1;
                    }
                    if (
                        b.shellInfo.friendlyName === "exec" &&
                        a.shellInfo.friendlyName !== "exec"
                    ) {
                        return -1;
                    }
                    // If both are "exec" or neither is "exec", sort normally

                    // First sort by capability
                    const capabilityDiff = b.capability.capability - a.capability.capability;
                    if (capabilityDiff !== 0) {
                        return capabilityDiff;
                    }
                    // If capabilities are equal, prefer the default profile
                    if (defaultProfile) {
                        if (
                            a.shellInfo.name === defaultProfile.name &&
                            b.shellInfo.name !== defaultProfile.name
                        ) {
                            return -1;
                        }
                        if (
                            b.shellInfo.name === defaultProfile.name &&
                            a.shellInfo.name !== defaultProfile.name
                        ) {
                            return 1;
                        }
                    }
                    // If neither is default or both have same relation to default, maintain original order
                    return 0;
                });

                // Save the supported shells to global state
                // Convert shells to the format expected by the UI
                // Filter out shells with undefined names and remove duplicates
                const uiShells = this._supportedShells
                    .map((s) => ({
                        name: s.shellInfo.name,
                        path: s.shellInfo.path,
                        friendlyName: s.shellInfo.friendlyName,
                        supportString:
                            s.capability.capability === TerminalCapability.supported
                                ? "Fully supported"
                                : this._isBasicallySupported(s.capability.capability)
                                  ? "Partially supported"
                                  : "Not supported",
                    }))
                    .filter(
                        (shell, index, self) =>
                            index === self.findIndex((s) => s.friendlyName === shell.friendlyName)
                    );

                // Save the supported shells to global state
                this._terminalSettings.supportedShells = uiShells;
                await this._globalState.save(
                    FileStorageKey.terminalSettings,
                    this._terminalSettings
                );

                // Try to load saved shell preference
                const savedShellName = this._terminalSettings.selectedShell;

                // If we have a saved shell preference and it's in the supported shells, use it
                if (savedShellName) {
                    const savedShell = this._supportedShells.find(
                        (s) => s.shellInfo.friendlyName === savedShellName
                    );
                    if (savedShell) {
                        TerminalProcessTools._shellInfo = savedShell.shellInfo;
                        this._logger.debug(
                            `Shell initialization complete. Using saved shell preference: ${TerminalProcessTools._shellInfo.friendlyName} (${TerminalCapability[savedShell.capability.capability]})`
                        );
                    } else {
                        // Saved shell not found, use the best available
                        TerminalProcessTools._shellInfo = this._supportedShells[0].shellInfo;
                        this._logger.debug(
                            `Shell initialization complete. Saved shell '${savedShellName}' not found, using best available: ${TerminalProcessTools._shellInfo.friendlyName} (${TerminalCapability[this._supportedShells[0].capability.capability]})`
                        );
                    }
                } else {
                    // No saved preference, use the best available
                    TerminalProcessTools._shellInfo = this._supportedShells[0].shellInfo;
                    this._logger.debug(
                        `Shell initialization complete. Using shell: ${TerminalProcessTools._shellInfo.friendlyName} (${TerminalCapability[this._supportedShells[0].capability.capability]})`
                    );
                }
            } else {
                // Fallback to default shell if no supported shells found
                TerminalProcessTools._shellInfo = TerminalProcessTools._getDefaultShell();
                this._logger.debug(
                    `Shell initialization complete. No supported shells found, using default: ${TerminalProcessTools._shellInfo.friendlyName}`
                );
                getAgentSessionEventReporter().reportEvent({
                    eventName: AgentSessionEventName.vsCodeTerminalNoSupportedShellsFound,
                    conversationId: "",
                });
            }
        } catch (error: any) {
            // If anything goes wrong, use the default shell
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error initializing shells: ${error.message}`);
            TerminalProcessTools._shellInfo = TerminalProcessTools._getDefaultShell();
            this._logger.debug(
                `Shell initialization complete with errors. Using default shell: ${TerminalProcessTools._shellInfo.friendlyName}`
            );
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalErrorInitializingShells,
                conversationId: "",
            });
        }
        if (
            TerminalProcessTools._longRunningTerminal &&
            !areSameShellInfo(originalShellInfo, TerminalProcessTools._shellInfo)
        ) {
            this._terminalNeedsUpdate = true;
        }
    }

    /**
     * Gets all shells that should be checked for capabilities
     */
    private async _getAllShellsToCheck(): Promise<ShellInfo[]> {
        const shellsToCheck: ShellInfo[] = [];

        // Get the VSCode default profile
        const defaultShellInfo = this._getVSCodeDefaultProfile();
        if (defaultShellInfo) {
            shellsToCheck.push(defaultShellInfo);
        }

        // Get all VSCode profiles
        const profiles = this._getVSCodeProfiles();
        for (const profile of profiles) {
            // Skip if we already have this shell in the list
            if (shellsToCheck.some((s) => s.name === profile.name && s.path === profile.path)) {
                continue;
            }
            shellsToCheck.push(profile);
        }

        // Add special case for zsh with clean environment
        const zshShellInfo = await this._createZshShellInfo(profiles);
        if (zshShellInfo) {
            shellsToCheck.push(zshShellInfo);
        }

        // Add special case for bash with clean environment
        const bashShellInfo = await this._createBashShellInfo(profiles);
        if (bashShellInfo) {
            shellsToCheck.push(bashShellInfo);
        }

        // Add the "exec" shell type (always supported)
        shellsToCheck.push({
            name: getDefaultShell(process.platform),
            path: undefined,
            args: undefined,
            env: undefined,
            friendlyName: "exec",
            tempDir: undefined,
        });

        return shellsToCheck;
    }

    /**
     * Finds all supported shells and checks their capabilities
     */
    private async _findSupportedShells(): Promise<
        Array<{ shellInfo: ShellInfo; capability: TerminalCapabilityResult }>
    > {
        const supportedShells: Array<{
            shellInfo: ShellInfo;
            capability: TerminalCapabilityResult;
        }> = [];

        // Get all shells to check
        const shellsToCheck = await this._getAllShellsToCheck();

        // If VSCode just started, our capability checks can be flaky because VSCode is still
        // starting up, which can cause timeouts. We thus wait briefly before running the checks.
        const timeSinceEnable = Date.now() - this._enableStart;
        if (timeSinceEnable < 5000) {
            this._logger.debug(
                `First terminal initialization since VSCode startup, delaying shell capability checks`
            );
            await delayMs(5000 - timeSinceEnable);
        }

        // Check each shell's capabilities
        for (const shellInfo of shellsToCheck) {
            // Special handling for "exec" shell - always mark as supported
            if (shellInfo.friendlyName === "exec") {
                this._logger.debug(`Adding exec shell as always supported`);
                supportedShells.push({
                    shellInfo,
                    capability: {
                        capability: TerminalCapability.supported,
                        details: "exec shell uses child_process and is always supported",
                    },
                });
                continue;
            }

            if (!isSupportedShell(shellInfo.name) && !shellInfo.name.includes("powershell")) {
                this._logger.debug(`Skipping unsupported shell: ${shellInfo.name}`);
                continue;
            }

            try {
                this._logger.debug(
                    `Checking capabilities for shell: ${shellInfo.name} ${shellInfo.path || ""} ${shellInfo.args?.join(" ") || ""}`
                );

                // Create a temporary capability checker for this shell
                const checker = new TerminalCapabilityChecker(shellInfo);
                const capability = await checker.checkCapabilities();
                checker.dispose();

                this._logger.debug(
                    `Shell ${shellInfo.name} capability: ${TerminalCapability[capability.capability]} - ${capability.details}`
                );

                supportedShells.push({ shellInfo, capability });
            } catch (error: any) {
                this._logger.debug(
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                    `Error checking capabilities for shell ${shellInfo.name}: ${error.message}`
                );
                getAgentSessionEventReporter().reportEvent({
                    eventName: AgentSessionEventName.vsCodeTerminalErrorCheckingShellCapability,
                    conversationId: "",
                });
            }
        }

        return supportedShells;
    }

    /**
     * Gets the default shell for the current platform
     */
    private static _getDefaultShell(): ShellInfo {
        const defaultShell = getDefaultShell(process.platform) as string;
        return {
            name: defaultShell,
            path: undefined,
            args: undefined,
            env: undefined,
            friendlyName: defaultShell + " (default)",
            tempDir: undefined,
        };
    }

    /**
     * Creates a ShellInfo for zsh with a clean environment
     * @param profiles The list of available shell profiles
     * @returns A ShellInfo for zsh with a clean environment, or undefined if zsh is not available
     */
    private async _createZshShellInfo(profiles: ShellInfo[]): Promise<ShellInfo | undefined> {
        if (process.platform !== "darwin" && process.platform !== "linux") {
            return undefined;
        }

        const zshPath = profiles.find((p) => p.name === "zsh")?.path;
        if (!zshPath) {
            return undefined;
        }

        try {
            // Create a temporary directory for zsh configuration
            const tempDir = await this._createZshTempEnvironment();
            return {
                name: "zsh",
                path: zshPath,
                args: undefined,
                env: {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    ZDOTDIR: tempDir, // Set ZDOTDIR to our temp directory with empty .zshrc
                },
                friendlyName: "zsh (vanilla)",
                tempDir: tempDir, // Store the temp dir path for cleanup later
            };
        } catch (error: any) {
            this._logger.debug(
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                `Failed to create temporary zsh environment: ${error.message}`
            );
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalErrorCreatingZshEnvironment,
                conversationId: "",
            });
            return undefined;
        }
    }

    /**
     * Creates a temporary directory with an empty .zshrc file for zsh
     * @returns Path to the temporary directory
     */
    private async _createZshTempEnvironment(): Promise<string> {
        const tempDir = await createTempDirectory("augment-zsh-");
        await writeFileUtf8(
            path.join(tempDir, ".zshrc"),
            "# Empty .zshrc file created by Augment\n"
        );
        return tempDir;
    }

    /**
     * Creates a ShellInfo for bash with a clean environment
     * @param profiles The list of available shell profiles
     * @returns A ShellInfo for bash with a clean environment, or undefined if bash is not available
     */
    private async _createBashShellInfo(profiles: ShellInfo[]): Promise<ShellInfo | undefined> {
        if (process.platform !== "darwin" && process.platform !== "linux") {
            return undefined;
        }

        const bashPath = profiles.find((p) => p.name === "bash")?.path;
        if (!bashPath) {
            return undefined;
        }

        // We need to invoke bash with --init-file <path to VSCode's shellIntegration-bash.sh>.
        // We find that by starting a hidden bash terminal and using ps to see how it was invoked.
        const tempTerminal = vscode.window.createTerminal({
            name: "augment-bash-test",
            shellPath: bashPath,
            hideFromUser: true,
            isTransient: true,
        });
        const pid = await tempTerminal.processId;
        const bashArgs = await executeCommand(`ps -p ${pid} -o args=`, {});
        tempTerminal.dispose();
        if (!bashArgs) {
            return undefined;
        }
        const argsParts = split(bashArgs);
        const initFileIndex = argsParts.findIndex((arg) => arg === "--init-file");
        if (initFileIndex === -1 || initFileIndex >= argsParts.length - 1) {
            return undefined;
        }
        const initFilePath = argsParts[initFileIndex + 1];

        return {
            name: "bash",
            path: bashPath,
            args: ["--init-file", initFilePath],
            friendlyName: "bash (vanilla)",
        };
    }

    /**
     * Gets the OS-specific section name for terminal configuration
     */
    private _getOSSection(): string {
        if (process.platform === "win32") {
            return "windows";
        } else if (process.platform === "darwin") {
            return "osx";
        } else {
            return "linux";
        }
    }

    /**
     * Processes shell info to handle special cases like Git Bash and PowerShell on Windows
     * @param shellInfo The shell info to process
     * @param logErrors Whether to log errors
     * @returns Processed shell info or undefined if processing failed
     */
    private _processShellInfo(
        shellInfo: { name: string; path: string | undefined; args: string[] | undefined },
        logErrors: boolean
    ): ShellInfo | undefined {
        // Windows uses "Git Bash" instead of "bash".
        if (shellInfo.name === "git bash" && process.platform === "win32") {
            const gitBashInfo = this._getGitBashInfo(shellInfo.path, shellInfo.args);
            if (!gitBashInfo) {
                if (logErrors) {
                    this._logger.debug(`Failed to find/use Git Bash path`);
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalFailedToFindGitBash,
                        conversationId: "",
                    });
                }
                return undefined;
            }
            shellInfo.name = gitBashInfo.name;
            shellInfo.path = gitBashInfo.path;
            shellInfo.args = gitBashInfo.args;
        }

        // Handle PowerShell on Windows
        // The name could be something like "PowerShell" or "PowerShell 7" or "Windows PowerShell"
        if (shellInfo.name.includes("powershell") && process.platform === "win32") {
            const powershellPath = this._findPowerShellPath();
            if (!powershellPath) {
                if (logErrors) {
                    this._logger.debug(`Failed to find PowerShell path`);
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalFailedToFindPowerShell,
                        conversationId: "",
                    });
                }
                return undefined;
            }
            shellInfo.name = "powershell";
            shellInfo.path = powershellPath;
        }

        if (shellInfo.name && isSupportedShell(shellInfo.name)) {
            return {
                ...shellInfo,
                friendlyName: shellInfo.name,
            };
        } else if (shellInfo.name) {
            if (logErrors) {
                this._logger.debug(`Unsupported shell: ${shellInfo.name}`);
                getAgentSessionEventReporter().reportEvent({
                    eventName: AgentSessionEventName.vsCodeTerminalUnsupportedVSCodeShell,
                    conversationId: "",
                });
            }
            return undefined;
        }
    }

    /**
     * Gets the VSCode default profile
     */
    private _getVSCodeDefaultProfile(): ShellInfo | undefined {
        const section = this._getOSSection();
        const config = vscode.workspace.getConfiguration("terminal.integrated.defaultProfile");
        const configValue = config.get(section);
        if (typeof configValue === "string") {
            const innerConfig = vscode.workspace.getConfiguration(
                `terminal.integrated.profiles.${section}.${configValue}`
            );
            // Windows uses "PowerShell", so we need to convert to lowercase.
            const name = configValue.trim().toLowerCase();
            const path = innerConfig.get<string>("path");
            const args = innerConfig.get<string[]>("args");
            return this._processShellInfo({ name, path, args }, true);
        }
        return undefined;
    }

    /**
     * Gets all VSCode terminal profiles
     */
    private _getVSCodeProfiles(): ShellInfo[] {
        const profiles: ShellInfo[] = [];
        const section = this._getOSSection();
        const config = vscode.workspace.getConfiguration(`terminal.integrated.profiles.${section}`);
        const profileNames = Object.keys(config);
        for (const profileName of profileNames) {
            const profile = config.get<{ path?: string; args?: string[] }>(profileName);
            if (!profile) {
                continue;
            }
            const name = profileName.toLowerCase();
            const path = profile.path;
            const args = profile.args;
            const processedShell = this._processShellInfo({ name, path, args }, false);
            if (processedShell) {
                profiles.push(processedShell);
            }
        }
        return profiles;
    }

    /**
     * Helper method to get Git Bash information (name, path, args)
     * @param existingPath Optional existing path to use
     * @param existingArgs Optional existing args to use
     * @returns Git Bash shell info or undefined if not found
     */
    private _getGitBashInfo(existingPath?: string, existingArgs?: string[]): ShellInfo | undefined {
        let path = existingPath;
        if (!path) {
            path = this._findGitBashPath();
        }
        if (!path || !fileExists(path)) {
            return undefined;
        }
        const args = existingArgs ?? ["--login", "-i"];
        return {
            name: "bash",
            path,
            args,
            friendlyName: "Git Bash",
        };
    }

    /**
     * Finds the Git Bash executable path on Windows
     * @returns Path to Git Bash executable or undefined if not found
     */
    private _findGitBashPath() {
        const gitParentPaths = [
            `${process.env["ProgramFiles"]}`,
            `${process.env["ProgramW6432"]}`,
            `${process.env["ProgramFiles(x86)"]}`,
        ];
        for (const parentPath of gitParentPaths) {
            if (!directoryExists(parentPath)) {
                continue;
            }
            const gitPaths = [
                path.join(parentPath, "Git", "bin", "bash.exe"),
                path.join(parentPath, "Git", "usr", "bin", "bash.exe"),
            ];
            for (const gitPath of gitPaths) {
                if (fileExists(gitPath)) {
                    return gitPath;
                }
            }
        }
        return undefined;
    }

    /**
     * Finds the PowerShell executable path on Windows
     * There are two types of PowerShell on Windows:
     * 1. The legacy Windows PowerShell, which is usually located at
     *    C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe
     * 2. The newer PowerShell, which is usually located at
     *    C:\Program Files\PowerShell\<version>\pwsh.exe
     * We prefer the newer PowerShell with the highest version number,
     * but if it's not found, we fall back to the legacy Windows PowerShell.
     * @returns Path to PowerShell executable or undefined if not found
     */
    private _findPowerShellPath() {
        // Look for paths like C:\Program Files\PowerShell\7\pwsh.exe.
        const programFilePaths = [
            `${process.env["ProgramFiles"]}`,
            `${process.env["ProgramW6432"]}`,
            `${process.env["ProgramFiles(x86)"]}`,
        ];
        let highestVersionNumber: string | undefined = undefined;
        let bestPath: string | undefined = undefined;
        for (const programFilePath of programFilePaths) {
            const powershellPath = path.join(programFilePath, "PowerShell");
            if (!directoryExists(programFilePath) || !directoryExists(powershellPath)) {
                continue;
            }
            for (const [childDir, fileType] of readDirectorySync(powershellPath)) {
                if (fileType === FileType.directory && childDir.match(/^\d+$/)) {
                    const pwshPath = path.join(powershellPath, childDir, "pwsh.exe");
                    if (fileExists(pwshPath)) {
                        if (!highestVersionNumber || childDir > highestVersionNumber) {
                            highestVersionNumber = childDir;
                            bestPath = pwshPath;
                        }
                    }
                }
            }
        }
        if (bestPath) {
            return bestPath;
        }

        // Look for paths like C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe.
        const possibleArches = ["System32", "SysNative"];
        for (const arch of possibleArches) {
            const windowsPowerShellPath = path.join(
                process.env["windir"] ?? "C:\\Windows",
                arch,
                "WindowsPowerShell",
                "v1.0",
                "powershell.exe"
            );
            if (fileExists(windowsPowerShellPath)) {
                return windowsPowerShellPath;
            }
        }
        return undefined;
    }

    public get shellName() {
        return TerminalProcessTools._shellInfo.name;
    }

    /**
     * Helper method to truncate output using the AssetManager if available, or fallback to truncateMiddle
     * @param content The content to truncate
     * @param toolUseId The ID to use for storing the untruncated content
     * @returns The truncated content
     */
    private async _truncateOutput(content: string, toolUseId: string): Promise<string> {
        if (this._untruncatedContentManager) {
            try {
                const options: TruncateOptions = {
                    maxBytes: this._maxOutputLength,
                    contentType: TruncatedContentType.ToolOutput,
                    toolUseId,
                };

                const result = await truncateWithMetadata(
                    content,
                    options,
                    this._untruncatedContentManager,
                    this._enableUntruncatedContentStorage
                );
                return result.truncatedContent;
            } catch (error: any) {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                this._logger.debug(`Error using truncateWithMetadata: ${error.message}`);
            }
        }
        // Fall back to truncateMiddle if AssetManager is not available
        return truncateMiddle(content, this._maxOutputLength);
    }

    /**
     * The terminal API in VSCode versions before 1.98.0 is pretty broken: the execution.read
     * method often does not return the actual output.  For details, see the upstream bug
     * https://github.com/microsoft/vscode/issues/237208.
     * We detect this case by checking if we're on a buggy VSCode version and if the output
     * does not contain the escape sequences representing execution completion.  See
     * https://code.visualstudio.com/docs/terminal/shell-integration#_supported-escape-sequences
     * for details on the escape sequences.
     */
    private _isBuggyOutput(text: string) {
        // Check if the current shell has buggy output
        if (this._currentCapability.capability === TerminalCapability.buggyOutput) {
            return true;
        }

        // Otherwise, use the traditional detection method
        return (
            !isVsCodeVersionGte("1.98.0") &&
            !text.includes("\x1b]633;D") &&
            !text.includes("\x1b]133;D")
        );
    }

    private _terminalHasShellIntegration(terminal: vscode.Terminal) {
        // First check if the terminal has shell integration based on capability
        if (!this._isTerminalBasicallySupported()) {
            return false;
        }

        // Then check if this specific terminal has shell integration
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-return
        return (terminal as any).shellIntegration !== undefined;
    }
}

/**
 * A tool that launches a new process.
 */
export class TerminalLaunchProcessTool extends ToolBase<LocalToolType> {
    constructor(
        private readonly _workspaceManager: WorkspaceManager,
        public readonly processTools: TerminalProcessTools
    ) {
        super(LocalToolType.launchProcess, ToolSafety.Check);
    }

    public readonly version = 2;

    public get description(): string {
        return `\
Launch a new process with a shell command. A process can be waiting (\`wait=true\`) or non-waiting (\`wait=false\`).

If \`wait=true\`, launches the process in an interactive terminal, and waits for the process to complete up to
\`max_wait_seconds\` seconds. If the process ends during this period, the tool call returns. If the timeout
expires, the process will continue running in the background but the tool call will return. You can then
interact with the process using the other process tools.

Note: Only one waiting process can be running at a time. If you try to launch a process with \`wait=true\`
while another is running, the tool will return an error.

If \`wait=false\`, launches a background process in a separate terminal. This returns immediately, while the
process keeps running in the background.

Notes:
- Use \`wait=true\` processes when the command is expected to be short, or when you can't
proceed with your task until the process is complete. Use \`wait=false\` for processes that are
expected to run in the background, such as starting a server you'll need to interact with, or a
long-running process that does not need to complete before proceeding with the task.
- If this tool returns while the process is still running, you can continue to interact with the process
using the other available tools. You can wait for the process, read from it, write to it, kill it, etc.
- You can use this tool to interact with the user's local version control system. Do not use the
retrieval tool for that purpose.
- If there is a more specific tool available that can perform the function, use that tool instead of
this one.

The OS is ${process.platform}. The shell is '${this.processTools.shellName}'.`;
    }

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            command: {
                type: "string",
                description: "The shell command to execute.",
            },
            wait: {
                type: "boolean",
                description: "Whether to wait for the command to complete.",
            },
            max_wait_seconds: {
                type: "number",
                description: `Number of seconds to wait for the command to complete. Only relevant when wait=true. 10 minutes may be a good default: increase from there if needed.`,
            },
            cwd: {
                type: "string",
                description:
                    "Required parameter. Absolute path to the working directory for the command.",
            },
        },
        required: ["command", "wait", "max_wait_seconds", "cwd"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(toolInput: Record<string, any>): boolean {
        const allowlist = getShellAllowlist(process.platform, this.processTools.shellName);
        const command = toolInput.command as string;
        return checkShellAllowlist(allowlist, command, this.processTools.shellName);
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        try {
            const wait = toolInput.wait as boolean;
            const maxWaitSeconds = toolInput.max_wait_seconds as number;
            const cwd =
                (toolInput.cwd as string | undefined) ?? getCwdForTool(this._workspaceManager);
            const useLongRunningTerminal = !!wait;

            const pid = await this.processTools.launch(
                toolInput.command as string,
                cwd,
                abortSignal,
                useLongRunningTerminal
            );
            if (typeof pid === "string") {
                return errorToolResponse(pid);
            }

            if (!wait) {
                return successToolResponse(`Process launched with terminal ID ${pid}`);
            }

            // Wait for process to complete or timeout
            const result = await this.processTools.waitForProcess(pid, maxWaitSeconds, abortSignal);
            const afterCwdMessage = getAfterCwdMessage(
                pid,
                this.processTools,
                this._workspaceManager
            );

            if (result.returnCode === null) {
                return successToolResponse(`\
Command may still be running. You can use read-process to get more output
and kill-process to terminate it if needed.
Terminal ID ${pid}
Output so far:
<output>
${result.output}
</output>
${afterCwdMessage}`);
            }

            return {
                text: `\
Here are the results from executing the command.
<return-code>
${result.returnCode}
</return-code>
<output>
${result.output}
</output>
${afterCwdMessage}`,
                isError: result.returnCode !== 0,
            };
        } catch (e: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            return errorToolResponse(`Failed to launch process: ${e.message ?? ""}`);
        }
    }
}

/**
 * A tool that kills a terminal.
 * Note that this only kills terminals launched with the launch-process tool.
 */
export class TerminalKillProcessTool extends ToolBase<LocalToolType> {
    constructor(private readonly _processTools: TerminalProcessTools) {
        super(LocalToolType.killProcess, ToolSafety.Safe);
    }

    public readonly description: string = "Kill a process by its terminal ID.";

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            terminal_id: {
                type: "integer",
                description: "Terminal ID to kill.",
            },
        },
        required: ["terminal_id"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const id = toolInput.terminal_id as number;
        const output = await this._processTools.kill(id);
        if (output) {
            if (output.killed) {
                return successToolResponse(
                    `Terminal ${id} killed\n<output>${output.output}</output>`
                );
            } else {
                return successToolResponse(
                    `Terminal ${id} already exited\n<output>${output.output}</output>\n<return-code>${output.returnCode}</return-code>`
                );
            }
        } else {
            return errorToolResponse(`Terminal ${id} not found`);
        }
    }
}

/**
 * A tool that reads output from a terminal.
 * Note that this only reads from terminals launched with the launch-process tool.
 */
export class TerminalReadProcessTool extends ToolBase<LocalToolType> {
    constructor(
        private readonly _processTools: TerminalProcessTools,
        private readonly _workspaceManager: WorkspaceManager
    ) {
        super(LocalToolType.readProcess, ToolSafety.Safe);
    }

    public readonly description: string = `\
Read output from a terminal.

If \`wait=true\` and the process has not yet completed, waits for the terminal to complete up to \`max_wait_seconds\` seconds before returning its output.

If \`wait=false\` or the process has already completed, returns immediately with the current output.`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            terminal_id: {
                type: "integer",
                description: "Terminal ID to read from.",
            },
            wait: {
                type: "boolean",
                description: "Whether to wait for the command to complete.",
            },
            max_wait_seconds: {
                type: "number",
                description: `Number of seconds to wait for the command to complete. Only relevant when wait=true. 1 minute may be a good default: increase from there if needed.`,
            },
        },
        required: ["terminal_id", "wait", "max_wait_seconds"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const id = toolInput.terminal_id as number;
        const wait = toolInput.wait as boolean;
        const maxWaitSeconds = toolInput.max_wait_seconds as number;
        if (wait) {
            await this._processTools.waitForProcess(id, maxWaitSeconds, _abortSignal);
        }
        const output = await this._processTools.readOutput(id);
        if (!output) {
            return errorToolResponse(`Terminal ${id} not found`);
        }

        const afterCwdMessage = getAfterCwdMessage(id, this._processTools, this._workspaceManager);

        const status = output.returnCode !== null ? "completed" : "still running";
        let response = `\
Here is the output from terminal ${id} (status: ${status}):
<output>${output.output}</output>\n`;

        if (output.returnCode !== null) {
            response += `<return-code>\n${output.returnCode}\n</return-code>\n`;
        }
        if (afterCwdMessage) {
            response += afterCwdMessage;
        }

        return successToolResponse(response);
    }
}

/**
 * A tool that writes input to a terminal.
 * Note that this only writes to terminals launched with the launch-process tool.
 */
export class TerminalWriteProcessTool extends ToolBase<LocalToolType> {
    constructor(private readonly _processTools: TerminalProcessTools) {
        super(LocalToolType.writeProcess, ToolSafety.Safe);
    }

    public readonly description: string = "Write input to a terminal.";

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            terminal_id: {
                type: "integer",
                description: "Terminal ID to write to.",
            },
            input_text: {
                type: "string",
                description: "Text to write to the process's stdin.",
            },
        },
        required: ["terminal_id", "input_text"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const id = toolInput.terminal_id as number;
        const inputText = toolInput.input_text as string;
        if (this._processTools.writeInput(id, inputText)) {
            return Promise.resolve(successToolResponse(`Input written to terminal ${id}`));
        } else {
            return Promise.resolve(errorToolResponse(`Terminal ${id} not found or write failed`));
        }
    }
}

/**
 * A tool that lists all known terminals and their states.
 * Note that this only lists terminals launched with the launch-process tool.
 */
export class TerminalListProcessesTool extends ToolBase<LocalToolType> {
    constructor(private readonly _processTools: TerminalProcessTools) {
        super(LocalToolType.listProcesses, ToolSafety.Safe);
    }

    public readonly description: string = `List all known terminals created with the ${LocalToolType.launchProcess} tool and their states.`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {},
        required: [],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public call(
        _toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const processes = this._processTools.listProcesses();
        if (processes.length === 0) {
            return Promise.resolve(successToolResponse("No processes found"));
        }

        const lines = processes.map((proc) => {
            let status = proc.state;
            if (proc.returnCode !== null) {
                status += ` (return code: ${proc.returnCode})`;
            }
            return `Terminal ${proc.id} [${status}]: ${proc.command}`;
        });
        // List the number of running processes to make it clear to the model.
        const runningCount = processes.filter((proc) => proc.state === "running").length;
        if (runningCount === 1) {
            lines.push("\nThere is 1 process still running.");
        } else if (runningCount > 1) {
            lines.push(`\nThere are ${runningCount} processes still running.`);
        }

        return Promise.resolve(
            successToolResponse("Here are all known processes:\n\n" + lines.join("\n"))
        );
    }
}

function getAfterCwdMessage(
    processId: number,
    processTools: TerminalProcessTools,
    workspaceManager: WorkspaceManager
): string | undefined {
    const afterCwd = processTools.getLongRunningTerminalInfo()?.current_working_directory;
    if (!processTools.isInLongRunningTerminal(processId) || !afterCwd) {
        return;
    }
    // Try to use a relative path if possible, otherwise fallback on the absolute path.
    // NOTE(arun): we're using relative paths for now because that's the most
    // consistent choice with the rest of ours sytem.
    let afterRelativeCwd = workspaceManager.safeResolvePathName(afterCwd)?.relPath;
    if (afterRelativeCwd != null && !isAbsolutePathName(afterRelativeCwd)) {
        afterRelativeCwd = `//${afterRelativeCwd}`;
    }
    return `The terminal's current working directory is now \`${afterRelativeCwd ?? afterCwd}\`.\n`;
}

function _stripControlCodes(text: string): string {
    return (
        text
            // Remove ANSI escape sequences
            // eslint-disable-next-line no-control-regex
            .replace(/\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])/g, "")
            // Handle OSC sequences that might not start with escape character
            // These can appear in terminal output with shell integration
            // eslint-disable-next-line no-control-regex
            .replace(/(?:\x1b\])?(\d+;[^\x07\x1b]*(?:\x07|\x1b\\))/g, "")
    );
}

/**
 * Regular expressions for matching common shell prompts
 */
const _shellPrompts: { [key: string]: RegExp } = {
    powershell: /^\s*(?:PS\s+(?:[^>]*))?\s*>\s*/,
    bash: /^\s*(?:[^@]*@[^:]*:)?(?:[^$]*)?\$\s*/,
    zsh: /^\s*(?:[^@]*@[^:]*:)?(?:[^%]*)?%\s*/,
    fish: /^\s*(?:[^@]*@[^:]*\s)?(?:[^>]*)?>\s*/,
};

enum TerminalCapability {
    unknown = 1,
    unsupportedAPI = 2,
    shellNotFound = 3,
    noShellIntegration = 4,
    noExecutionEvents = 5,
    buggyExecutionEvents = 6,
    noOutput = 7,
    noisyOutput = 8,
    buggyOutput = 9,
    supported = 10,
}

interface TerminalCapabilityResult {
    capability: TerminalCapability;
    details: string;
}

/**
 * A class that checks if the VSCode terminal has the capabilities we need.
 * It tests for:
 * 1. VSCode version and API support
 * 2. Shell integration availability
 * 3. Terminal execution events
 * 4. Output reliability
 */
class TerminalCapabilityChecker extends DisposableService {
    private _result: TerminalCapabilityResult = {
        capability: TerminalCapability.unknown,
        details: "Check not started",
    };
    private _checkPromise: Promise<TerminalCapabilityResult> | undefined;
    private _logger = getLogger("TerminalCapabilityChecker");
    private _testCommand = "echo 'Terminal capability test'";

    constructor(private readonly _shellInfo: ShellInfo) {
        super();
        void this._startBackgroundCheck();
    }

    /**
     * Checks if the terminal has the capabilities we need.
     * This method can be called multiple times, but will only run the check once
     * unless forceRecheck is true.
     */
    public async checkCapabilities(forceRecheck = false): Promise<TerminalCapabilityResult> {
        // Return cached result if we have one and not forcing a recheck
        if (this._result.capability !== TerminalCapability.unknown && !forceRecheck) {
            return this._result;
        }

        // If a check is already in progress, return that promise
        if (this._checkPromise) {
            return this._checkPromise;
        }

        // Start a new check
        this._checkPromise = this._performCheck();
        try {
            this._result = await this._checkPromise;
            return this._result;
        } finally {
            this._checkPromise = undefined;
        }
    }

    /**
     * Starts the background check for terminal capabilities
     */
    private async _startBackgroundCheck(): Promise<void> {
        try {
            const result = await this.checkCapabilities();
            this._logger.debug(
                `Terminal capability check result: ${this._getCapabilityDescription(result)}`
            );
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error during initial terminal capability check: ${error.message}`);
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalErrorCheckingShellCapability,
                conversationId: "",
            });
        }
    }

    /**
     * Gets a human-readable description of the terminal capability
     */
    private _getCapabilityDescription(result: TerminalCapabilityResult = this._result): string {
        return `${TerminalCapability[result.capability]}: ${result.details}`;
    }

    /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment */
    private async _performCheck(): Promise<TerminalCapabilityResult> {
        const window = vscode.window as any;
        if (!window.onDidEndTerminalShellExecution || !isVsCodeVersionGte("1.93.0")) {
            return {
                capability: TerminalCapability.unsupportedAPI,
                details: `VSCode version ${vscode.version} does not support required terminal APIs`,
            };
        }

        // Check if the shell exists before creating a terminal
        // This prevents VSCode from showing a warning if the shell doesn't exist
        const shellExists = await this._checkShellExists();
        if (!shellExists) {
            return {
                capability: TerminalCapability.shellNotFound,
                details: `Shell ${this._shellInfo.path ?? this._shellInfo.name} does not exist`,
            };
        }

        // Create a hidden terminal for testing
        const terminal = vscode.window.createTerminal({
            name: "Augment Terminal Capability Check",
            hideFromUser: true,
            shellPath: this._shellInfo?.path ?? this._shellInfo.name,
            shellArgs: this._shellInfo?.args,
            env: this._shellInfo?.env,
            isTransient: true,
        });

        try {
            // Wait for shell integration (which will either resolve when the event is received or when it times out)
            const shellIntegrationResult = await this._listenForShellIntegration(terminal);

            // If we got a listener, dispose it
            if (shellIntegrationResult.listener) {
                shellIntegrationResult.listener.dispose();
            }

            if (!shellIntegrationResult.shellIntegrationDetected) {
                return {
                    capability: TerminalCapability.noShellIntegration,
                    details: shellIntegrationResult.message,
                };
            }

            // Run the test command and get the execution and readStream
            const { execution, readStream } = this._executeTestCommand(
                terminal,
                shellIntegrationResult.shellIntegrationDetected
            );

            // Set up the execution event listener
            const executionEventPromise = this._listenForExecutionEvent(
                terminal,
                execution,
                readStream
            );

            // Wait for execution event (which will either resolve when the event is received or when it times out)
            const executionEventResult = await executionEventPromise;

            // If we got a listener, dispose it
            if (executionEventResult.listener) {
                executionEventResult.listener.dispose();
            }

            // Return the capability determined by the execution event listener with appropriate details
            let details: string;
            switch (executionEventResult.capability) {
                case TerminalCapability.buggyExecutionEvents:
                    details = "Terminal receives execution events but for incorrect commands";
                    break;
                case TerminalCapability.noExecutionEvents:
                    details = "No execution events received";
                    break;
                case TerminalCapability.noOutput:
                    details = "Execution event received but no output was captured";
                    break;
                case TerminalCapability.supported:
                    details = "Terminal has all required capabilities with exact output";
                    break;
                case TerminalCapability.noisyOutput:
                    details =
                        "Terminal has all required capabilities but output contains extra content";
                    break;
                case TerminalCapability.buggyOutput:
                    details = "Terminal has all required capabilities but output is incorrect";
                    break;
                default:
                    details = "Unknown terminal capability";
                    break;
            }
            return {
                capability: executionEventResult.capability,
                details,
            };
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Terminal capability check failed: ${error.message}`);
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalErrorCheckingShellCapability,
                conversationId: "",
            });
            return {
                capability: TerminalCapability.unknown,
                details: `Check failed with error: ${error.message}`,
            };
        } finally {
            // Clean up
            if (terminal) {
                terminal.dispose();
            }
        }
    }

    private _listenForShellIntegration(terminal: vscode.Terminal): Promise<{
        message: string;
        listener?: vscode.Disposable;
        shellIntegrationDetected: boolean;
    }> {
        return new Promise<{
            message: string;
            listener?: vscode.Disposable;
            shellIntegrationDetected: boolean;
        }>((resolve) => {
            if (!terminal) {
                resolve({ message: "Terminal not created", shellIntegrationDetected: false });
                return;
            }

            if (this._terminalHasShellIntegration(terminal)) {
                resolve({
                    message: "Shell integration already available",
                    shellIntegrationDetected: true,
                });
                return;
            }

            // Listen for shell integration to be enabled
            const window = vscode.window as any;
            if (window.onDidChangeTerminalShellIntegration) {
                const listener = window.onDidChangeTerminalShellIntegration((e: any) => {
                    if (e.terminal === terminal && e.shellIntegration) {
                        resolve({
                            message: "Shell integration detected",
                            shellIntegrationDetected: true,
                        });
                    }
                });

                void this._createTimeout("Shell integration", 3000).then((timeoutResult) => {
                    resolve({
                        message: timeoutResult.message,
                        listener,
                        shellIntegrationDetected: false,
                    });
                });
            } else {
                resolve({
                    message: "onDidChangeTerminalShellIntegration API not available",
                    shellIntegrationDetected: false,
                });
            }
        });
    }

    private _listenForExecutionEvent(
        terminal: vscode.Terminal,
        _execution?: any,
        readStream?: AsyncIterable<string>
    ): Promise<{
        listener?: vscode.Disposable;
        capability: TerminalCapability;
    }> {
        return new Promise<{
            listener?: vscode.Disposable;
            capability: TerminalCapability;
        }>((resolve) => {
            const window = vscode.window as any;
            if (!window.onDidEndTerminalShellExecution) {
                resolve({
                    capability: TerminalCapability.noExecutionEvents,
                });
                return;
            }

            const listener = window.onDidEndTerminalShellExecution(async (e: any) => {
                // Check if we received an execution event for our test command
                const isForTestCommand =
                    e.terminal === terminal && e.execution.commandLine.value === this._testCommand;

                // Check if we received an execution event but for a different command
                // This indicates buggy execution events where events are received for other commands
                const isBuggyExecutionEvent =
                    e.terminal === terminal && e.execution.commandLine.value !== this._testCommand;

                if (isForTestCommand) {
                    let capability = TerminalCapability.noOutput; // Default if no output

                    if (readStream) {
                        try {
                            let output = "";
                            for await (const chunk of readStream) {
                                output += chunk;
                            }
                            const cleaned = _stripControlCodes(output.trim());

                            // Determine capability based on output quality
                            if (cleaned && cleaned === "Terminal capability test") {
                                capability = TerminalCapability.supported;
                            } else if (cleaned && cleaned.includes("Terminal capability test")) {
                                capability = TerminalCapability.noisyOutput;
                            } else if (output && output.length > 0) {
                                capability = TerminalCapability.buggyOutput;
                            }
                        } catch (error: any) {
                            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                            this._logger.debug(`Error reading execution output: ${error.message}`);
                        }
                    }

                    resolve({
                        capability,
                    });
                } else if (isBuggyExecutionEvent) {
                    // We received an execution event but for a different command
                    // This indicates buggy execution events
                    this._logger.debug(
                        `Buggy execution event received for command: ${e.execution.commandLine.value}`
                    );
                    resolve({
                        capability: TerminalCapability.buggyExecutionEvents,
                    });
                }
            });

            void this._createTimeout("Execution event", 2000).then(() => {
                resolve({
                    listener,
                    capability: TerminalCapability.noExecutionEvents,
                });
            });
        });
    }

    private _executeTestCommand(
        terminal: vscode.Terminal,
        shellIntegrationDetected: boolean
    ): { execution?: any; readStream?: AsyncIterable<string> } {
        if (!terminal || !shellIntegrationDetected) {
            return {};
        }

        try {
            // Use shell integration to execute the command if available
            const terminalAny = terminal as any;
            if (terminalAny.shellIntegration) {
                const execution = terminalAny.shellIntegration.executeCommand(this._testCommand);
                const readStream = execution.read();
                return { execution, readStream };
            } else {
                // Fallback to regular sendText
                terminal.sendText(this._testCommand);
                return {};
            }
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error executing test command: ${error.message}`);
            return {};
        }
    }

    private _createTimeout(stage: string, timeoutMs: number): Promise<{ message: string }> {
        return new Promise<{ message: string }>((resolve) => {
            const timeout = setTimeout(() => {
                resolve({ message: `Timeout waiting for ${stage}` });
            }, timeoutMs);
            this.addDisposable(new vscode.Disposable(() => clearTimeout(timeout)));
        });
    }

    private _terminalHasShellIntegration(terminal: vscode.Terminal): boolean {
        return !!(terminal as any).shellIntegration;
    }

    /**
     * Checks if the shell exists by trying to run it.
     * This prevents VSCode from showing a warning if the shell doesn't exist.
     * We're only checking if the shell executable exists and can be started,
     * not if it's fully functional or supports all features we need.
     * @returns true if the shell exists, false otherwise
     */
    private async _checkShellExists(): Promise<boolean> {
        const shellPath = this._shellInfo.path ?? this._shellInfo.name;
        try {
            // For absolute paths, check if the file exists
            if (this._shellInfo.path && isAbsolutePathName(this._shellInfo.path)) {
                return fileExists(this._shellInfo.path);
            }
            // For shell names without paths, try to run a simple command to check if it exists
            // Use child_process.spawn with shell: false to avoid using the system shell
            // This will throw an error if the command doesn't exist
            return new Promise<boolean>((resolve) => {
                try {
                    const process = spawn(shellPath, [], {
                        shell: false,
                        stdio: "ignore",
                    });
                    process.on("error", () => {
                        // Error event means the shell couldn't be spawned
                        resolve(false);
                    });
                    process.on("exit", () => {
                        // Even if the command returns non-zero, the shell exists
                        resolve(true);
                    });
                    // Set a timeout in case the process hangs.
                    // Some shells might not exit immediately, especially if they're loading config files.
                    // But if we can start the process at all, the shell exists.
                    setTimeout(() => {
                        try {
                            process.kill();
                        } catch (e) {
                            // Ignore errors when killing the process.
                        }
                        resolve(true); // Assume it exists if we could start it but it's hanging.
                    }, 2000); // Increased timeout to give shells more time to start.
                } catch (e) {
                    // Any exception means the shell couldn't be spawned.
                    resolve(false);
                }
            });
        } catch (e) {
            // Any exception means the shell couldn't be checked.
            return false;
        }
    }
    /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment */

    /**
     * Cleans up resources
     */
    public override dispose(): void {
        super.dispose();
    }
}

// Export for testing
// eslint-disable-next-line @typescript-eslint/naming-convention
export const __test__ = {
    stripControlCodes: _stripControlCodes,
    shellPrompts: _shellPrompts,
};
