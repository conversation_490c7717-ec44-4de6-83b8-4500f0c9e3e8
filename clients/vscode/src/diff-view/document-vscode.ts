import {
    DiffViewDocument,
    type DiffViewDocumentOptions,
} from "@augment-internal/sidecar-libs/src/diff-view/document";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import os from "os";
import * as vscode from "vscode";

import type { AugmentLogger } from "../logging";
import { getUntitledDoc } from "../workspace/untitled";

/**
 * VSCodeDiffViewDocument extends DiffViewDocument to provide VSCode-specific functionality.
 */
export class VSCodeDiffViewDocument extends DiffViewDocument {
    private _instance = crypto.randomUUID();

    /**
     * Creates a new VSCodeDiffViewDocument instance.
     * @param filePath The qualified path name of the document.
     * @param originalCode The original code content.
     * @param modifiedCode The modified code content.
     * @param opts Optional settings for the document.
     */
    constructor(
        filePath: QualifiedPathName,
        originalCode: string | undefined,
        modifiedCode: string | undefined,
        opts?: DiffViewDocumentOptions
    ) {
        // Make sure we always use trailing newlines if content exists
        const eol = os.EOL;
        if (modifiedCode !== undefined) {
            modifiedCode = modifiedCode.endsWith(eol) ? modifiedCode : modifiedCode + eol;
        }
        if (originalCode !== undefined) {
            originalCode = originalCode.endsWith(eol) ? originalCode : originalCode + eol;
        }

        super(filePath, originalCode, modifiedCode, { ...opts });
        this.addDisposable(vscode.workspace.onDidChangeTextDocument(this._onBaseDocUpdated));
        this.addDisposable(this.onOriginalUpdated(() => void this._write()));
    }

    /**
     * Creates a VSCodeDiffViewDocument from a path name.
     * @param filePath The qualified path name of the document.
     * @param newCode Optional new code content.
     * @param logger Optional logger for debugging.
     * @returns A Promise resolving to a new VSCodeDiffViewDocument instance.
     */
    public static fromPathName = async (
        filePath: QualifiedPathName,
        newCode?: string | undefined,
        logger?: AugmentLogger
    ): Promise<VSCodeDiffViewDocument> => {
        const doc = new VSCodeDiffViewDocument(filePath, "", "", { logger });
        const originalCode = await doc._read();
        // If originalCode is empty string and file doesn't exist, use undefined
        const effectiveOriginalCode =
            originalCode === "" && !(await doc._fileExists()) ? undefined : originalCode;
        doc.updateCodeVersions(effectiveOriginalCode, newCode ?? effectiveOriginalCode);
        return doc;
    };

    /**
     * Checks if the file exists on disk.
     * @returns A Promise resolving to true if the file exists, false otherwise.
     */
    private _fileExists = async (): Promise<boolean> => {
        try {
            await vscode.workspace.fs.stat(vscode.Uri.file(this.filePath.absPath));
            return true;
        } catch {
            return false;
        }
    };

    /**
     * Reads the content of the VSCode text document.
     * @returns A Promise resolving to the document's text content.
     */
    private _read = async (): Promise<string> => {
        const doc = await this._getVsCodeTextDocument();
        return doc ? doc.getText() : "";
    };

    /**
     * Writes the original code to the VSCode text document.
     */
    private _write = async (): Promise<void> => {
        // If originalCode is undefined, it means the file should be deleted
        if (this.originalCode === undefined) {
            try {
                const uri = vscode.Uri.file(this.filePath.absPath);
                // Check if file exists before trying to delete it
                if (await this._fileExists()) {
                    this._opts.logger?.debug(`Deleting file ${this.filePath.absPath}`);
                    await vscode.workspace.fs.delete(uri);
                }
                return;
            } catch (e) {
                this._opts.logger?.error(
                    `Failed to delete file ${this.filePath.absPath}: ${String(e)}`
                );
                return;
            }
        }

        const doc = await this._getVsCodeTextDocument();
        if (doc && doc.getText() !== this.originalCode) {
            this._opts.logger?.debug(`Writing to doc ${this._instance}`);

            const edit = new vscode.WorkspaceEdit();
            edit.replace(
                doc.uri,
                new vscode.Range(0, 0, Number.MAX_SAFE_INTEGER, 0),
                this.originalCode
            );
            await vscode.workspace.applyEdit(edit);
        }
    };

    /**
     * Retrieves the VSCode TextDocument object for this document.
     * @returns A Promise resolving to the VSCode TextDocument or undefined if not found.
     */
    private _getVsCodeTextDocument = async (): Promise<vscode.TextDocument | undefined> => {
        try {
            let doc: vscode.TextDocument | undefined;
            if (this.isUntitled) {
                doc = getUntitledDoc(this.filePath);
            } else {
                doc = await vscode.workspace.openTextDocument(this.filePath.absPath);
            }

            if (doc === undefined) {
                throw new Error(`Failed to open document ${this.filePath.absPath}`);
            }
            return doc;
        } catch (e) {
            this._opts.logger?.error(`Failed to read file ${this.filePath.absPath}: ${String(e)}`);
            return undefined;
        }
    };

    /**
     * Listens for changes to the base document and updates the DiffViewDocument accordingly.
     * @param e The TextDocumentChangeEvent.
     */
    private _onBaseDocUpdated = (e: vscode.TextDocumentChangeEvent) => {
        if (e.document.uri.fsPath === this.absPath) {
            const currCode = e.document.getText();
            // Do not notify about changes we have already tracked
            if (currCode === this.originalCode) {
                return;
            }
            this.updateOriginal(currCode);
        }
    };
}
