import { VCSChange } from "@augment-internal/sidecar-libs/src/vcs/watcher/types";
import * as vscode from "vscode";

import { AugmentConfigListener } from "../../augment-config-listener";
import { getLogger } from "../../logging";
import * as enums from "../enums";
import { GitAdapterImpl } from "../git-adapter";
import { VCSDetails } from "../vcs-finder";
import { GitFacade } from "./git-facade";
import * as headChangeWatcher from "./head-change-watcher";
import * as interfaces from "./placeholders/interfaces";
import * as types from "./types";
import { VCSRepoWatcherImpl } from "./vcs-repo-watcher";

class VCSWatcherImpl implements types.VCSWatcher {
    private _repoWatchers: Map<number, types.VCSRepoWatcher> = new Map();
    private _disposables: Map<number, vscode.Disposable[]> = new Map();
    private _logger = getLogger("VCSWatcher");
    private _headChangeListener: vscode.Disposable | undefined;

    constructor(
        private _fileUploader: interfaces.FileUploader,
        private configListener: AugmentConfigListener
    ) {}

    private _createFacade(vcsDetails: VCSDetails): types.VCSFacade {
        if (vcsDetails.toolName === enums.VCSToolName.git) {
            return new GitFacade(new GitAdapterImpl(vcsDetails.root));
        }
        throw new Error("only git is supported for now");
    }

    startTracking(
        workspaceName: string,
        folderId: number,
        vcsDetails: VCSDetails,
        fileChangeWatcher: interfaces.FileChangeWatcher,
        blobNameRetriever: interfaces.BlobNameRetriever,
        fileUtils: interfaces.FileUtils
    ): vscode.Disposable {
        this._logger.debug(`startTracking folderId ${folderId}`);
        if (this._repoWatchers.has(folderId)) {
            this._logger.debug(`folderId ${folderId} is already being tracked`);
            return new vscode.Disposable(() => {});
        }
        const watcher = new VCSRepoWatcherImpl(
            workspaceName,
            this._createFacade(vcsDetails),
            fileUtils,
            this._fileUploader,
            fileChangeWatcher,
            blobNameRetriever,
            this.configListener
        );
        this._repoWatchers.set(folderId, watcher);
        this._disposables.set(folderId, [watcher]);
        void watcher.handleHeadChange(); // handle dirty workspace when start tracking.

        return new vscode.Disposable(() => this._stopTracking(folderId));
    }

    private _stopTracking(folderId: number) {
        this._logger.debug(`stopTracking folderId ${folderId}`);
        const disposables = this._disposables.get(folderId);
        if (disposables) {
            for (const disposable of disposables) {
                disposable.dispose();
            }
            this._disposables.delete(folderId);
        }
        this._repoWatchers.delete(folderId);
    }

    dispose() {
        this._logger.debug("Disposing VCSWatcher");
        for (const folderId of this._repoWatchers.keys()) {
            this._stopTracking(folderId);
        }
        this._repoWatchers.clear();
        this._disposables.clear();
    }

    getWatchedFolderIds(): number[] {
        return Array.from(this._repoWatchers.keys());
    }

    async getChanges(): Promise<VCSChange> {
        const result: VCSChange = {
            commits: [],
            workingDirectory: [],
        };
        for (const watcher of this._repoWatchers.values()) {
            const changes = await watcher.getChanges();
            result.commits = result.commits.concat(changes.commits);
            result.workingDirectory = result.workingDirectory.concat(changes.workingDirectory);
        }
        return result;
    }

    handleUnknownBlobs(unknownBlobNames: string[]) {
        for (const watcher of this._repoWatchers.values()) {
            watcher.handleUnknownBlobs(unknownBlobNames);
        }
    }

    listenForEvents() {
        this._logger.debug("Registering for events");
        if (this._headChangeListener !== undefined) {
            this._headChangeListener.dispose();
        }

        this._headChangeListener = headChangeWatcher.onDidChange(
            (event: headChangeWatcher.HeadChangeEvent) => {
                this._logger.debug(`Head changed for repo ${event.repoId}`);
                this._repoWatchers
                    .get(event.repoId)
                    ?.handleHeadChange()
                    .catch((e) => {
                        if (e instanceof Error) {
                            this._logger.error(
                                `Error handling head change: ${e.message ?? ""} ${e.stack}`
                            );
                        } else {
                            this._logger.error(`Error handling head change: ${e}`);
                        }
                    });
            }
        );
    }
}

export { VCSWatcherImpl };
