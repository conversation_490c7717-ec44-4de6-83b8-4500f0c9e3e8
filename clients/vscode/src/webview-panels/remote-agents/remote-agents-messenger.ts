/* eslint-disable @typescript-eslint/naming-convention */
import { APIServer } from "$vscode/src/augment-api";
import { AugmentConfigListener } from "$vscode/src/augment-config-listener";
import { getAgentMemories } from "$vscode/src/chat/agent-onboarding-orientation";
import { GuidelinesWatcher } from "$vscode/src/chat/guidelines-watcher";
import { FeatureFlagManager } from "$vscode/src/feature-flags";
import { logger } from "$vscode/src/init/logging";
import { getLogger } from "$vscode/src/logging";
import { type WorkTimer } from "$vscode/src/metrics/work-timer";
import {
    IRemoteAgentDiffPanelOptions,
    RemoteAgentChatRequestDetails,
    RemoteAgentStatus,
} from "$vscode/src/remote-agent-manager/types";
import { RemoteWorkspaceResolver } from "$vscode/src/remote-workspace-resolver";
import { AugmentGlobalState, FileStorageKey, GlobalContextKey } from "$vscode/src/utils/context";
import { SetupScriptsManager } from "$vscode/src/utils/remote-agent-setup/setup-scripts-manager";
import { RemoteAgentSshManager } from "$vscode/src/utils/ssh/remote-agent-ssh-manager";
import { removeRemotePlatform } from "$vscode/src/utils/ssh/ssh-utils";
import { StreamManager } from "$vscode/src/utils/stream-manager";
import { AsyncMsgHandler } from "$vscode/src/utils/webviews/messaging";
import { openDiffInBuffer } from "$vscode/src/utils/webviews/open-diff-in-buffer";
import { ToolConfigStore } from "$vscode/src/webview-panels/stores/tool-config-store";
import { WebviewManager } from "$vscode/src/webview-providers/webview-manager";
import {
    CancelRemoteAgentsStreamRequest,
    CloseRemoteAgentHomePanelMessage,
    CreateRemoteAgentRequestMessage,
    CreateRemoteAgentResponseMessage,
    DeleteRemoteAgentNotificationEnabledMessage,
    DeleteRemoteAgentPinnedStatusMessage,
    DeleteRemoteAgentRequestMessage,
    DeleteRemoteAgentResponseMessage,
    DeleteSetupScriptRequest,
    DeleteSetupScriptResponse,
    EmptyMessage,
    GetLastRemoteAgentSetupResponse,
    GetRemoteAgentChatHistoryRequest,
    GetRemoteAgentChatHistoryResponse,
    GetRemoteAgentNotificationEnabledMessage,
    GetRemoteAgentNotificationEnabledResponseMessage,
    GetRemoteAgentOverviewsRequest,
    GetRemoteAgentOverviewsResponse,
    GetRemoteAgentPinnedStatusRequestMessage,
    GetRemoteAgentPinnedStatusResponseMessage,
    GetRemoteAgentStatusMessage,
    ListSetupScriptsRequest,
    ListSetupScriptsResponse,
    MCPServer,
    OpenDiffInBufferMessage,
    OpenScratchFileRequestMessage,
    RemoteAgentChatRequestMessage,
    RemoteAgentChatResponseMessage,
    RemoteAgentHistoryStreamRequest,
    RemoteAgentHistoryStreamResponse,
    RemoteAgentInterruptRequestMessage,
    RemoteAgentInterruptResponseMessage,
    RemoteAgentNotifyReadyMessage,
    RemoteAgentOverviewsStreamRequest,
    RemoteAgentOverviewsStreamResponse,
    RemoteAgentPauseRequestMessage,
    RemoteAgentResumeRequestMessage,
    RemoteAgentSshRequestMessage,
    RemoteAgentSshResponseMessage,
    RemoteAgentStatusChangedMessage,
    RemoteAgentStatusResponseMessage,
    RemoteAgentWorkspaceLogsRequestMessage,
    RemoteAgentWorkspaceLogsResponseMessage,
    RenameSetupScriptRequest,
    RenameSetupScriptResponse,
    ReportRemoteAgentEventMessage,
    SaveLastRemoteAgentSetupRequest,
    SaveSetupScriptRequest,
    SaveSetupScriptResponse,
    SetRemoteAgentNotificationEnabledMessage,
    SetRemoteAgentPinnedStatusMessage,
    ShowRemoteAgentDiffPanelMessage,
    ShowRemoteAgentHomePanelMessage,
    WebViewMessage,
    WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import { WorkspaceManager } from "$vscode/src/workspace/workspace-manager";

import { ChatRequestNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { getRemoteAgentSessionEventReporter } from "@augment-internal/sidecar-libs/src/metrics/remote-agent-session-event-reporter";
import {
    RemoteAgentSessionEventName,
    RemoteAgentSetupState,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import * as vscode from "vscode";

import { RemoteAgentDiffPanel } from "./remote-agent-diff-panel";
import { RemoteAgentHomePanel } from "./remote-agent-home-panel";
import { RemoteAgentNotificationSettingsContext, RemoteAgentPinnedStatusContext } from "./types";

// ESM import all

/**
 * Handler for remote agent webview messages. It's just a
 * bunch of message handlers you can register with an AsyncMsgHandler to hook up
 * listeners to a given webview for remote agent messages.
 */
export class RemoteAgentsMessenger {
    private _remoteAgentSshManager: RemoteAgentSshManager;
    private _setupScriptsManager: SetupScriptsManager;
    private _webview: vscode.Webview | undefined;
    private _logger = getLogger("RemoteAgentsMessenger");
    private _streamManager = new StreamManager();

    private _disposables: vscode.Disposable[] = [];
    public static readonly messengerId = "remote-agents-messenger";
    private _getAgentMemoriesAbsPath: () => string | undefined;

    constructor(
        private _api: APIServer,
        private readonly _extensionUri: vscode.Uri,
        private readonly _workTimer: WorkTimer,
        private readonly _globalState: AugmentGlobalState,
        private readonly _toolConfigStore: ToolConfigStore,
        private readonly _configListener: AugmentConfigListener,
        private readonly _guidelinesWatcher: GuidelinesWatcher,
        private readonly _workspaceManager: WorkspaceManager,
        private readonly _extensionContext: vscode.ExtensionContext,
        private readonly _featureFlagManager: FeatureFlagManager
    ) {
        this._remoteAgentSshManager = new RemoteAgentSshManager(this._api);
        this._setupScriptsManager = new SetupScriptsManager(this._globalState);

        // Get the WebviewManager instance
        const webviewManager = WebviewManager.getInstance();
        this._logger.info(
            "RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener"
        );

        // listen for file changes, and trigger the file focus change event
        // Store the disposable so it can be properly cleaned up if needed
        this._disposables.push(
            vscode.window.onDidChangeActiveTextEditor((e) => {
                if (!e) {
                    return;
                }

                webviewManager.broadcastMessage({
                    type: WebViewMessageType.diffViewFileFocus,
                    data: {
                        filePath: e.document.uri.fsPath,
                    },
                });
            })
        );

        this._disposables.push(this._streamManager);

        this._getAgentMemoriesAbsPath = this.getAgentMemoriesAbsPath.bind(this);
    }

    /**
     * Disposes of all resources held by this messenger
     */
    dispose(): void {
        this._logger.debug("Disposing RemoteAgentsMessenger");
        // Dispose all registered disposables
        for (const disposable of this._disposables) {
            disposable.dispose();
        }
        this._disposables = [];
    }

    register(_asyncMsgHandler: AsyncMsgHandler, webview: vscode.Webview) {
        this._logger.info("Registering RemoteAgentsMessenger with AsyncMsgHandler");
        this._webview = webview;

        _asyncMsgHandler.registerHandler(
            WebViewMessageType.createRemoteAgentRequest,
            this.handleCreateRemoteAgentMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.deleteRemoteAgentRequest,
            this.handleDeleteRemoteAgentMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.getRemoteAgentChatHistoryRequest,
            this.handleGetRemoteAgentChatHistoryMessage.bind(this)
        );
        _asyncMsgHandler.registerStreamHandler(
            WebViewMessageType.remoteAgentOverviewsStreamRequest,
            this.handleRemoteAgentOverviewsStreamRequest.bind(this)
        );
        _asyncMsgHandler.registerStreamHandler(
            WebViewMessageType.remoteAgentHistoryStreamRequest,
            this.handleRemoteAgentHistoryStreamRequest.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.cancelRemoteAgentsStreamRequest,
            this.handleCancelRemoteAgentsStreamRequest.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.remoteAgentChatRequest,
            this.handleRemoteAgentChatRequestMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.remoteAgentInterruptRequest,
            this.handleRemoteAgentInterruptRequestMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.getRemoteAgentOverviewsRequest,
            this.handleGetRemoteAgentOverviewsMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.listSetupScriptsRequest,
            this.handleListSetupScriptsMessage.bind(this)
        );

        _asyncMsgHandler.registerHandler(
            WebViewMessageType.saveSetupScriptRequest,
            this.handleSaveSetupScriptMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.deleteSetupScriptRequest,
            this.handleDeleteSetupScriptMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.renameSetupScriptRequest,
            this.handleRenameSetupScriptMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.remoteAgentSshRequest,
            this.handleRemoteAgentSshRequestMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.setRemoteAgentNotificationEnabled,
            this.handleSetRemoteAgentNotificationEnabledMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.getRemoteAgentNotificationEnabledRequest,
            this.handleGetRemoteAgentNotificationEnabledMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.deleteRemoteAgentNotificationEnabled,
            this.handleDeleteRemoteAgentNotificationEnabledMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.remoteAgentNotifyReady,
            this.handleRemoteAgentNotifyReadyMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.remoteAgentWorkspaceLogsRequest,
            this.handleRemoteAgentWorkspaceLogsRequestMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.openScratchFileRequest,
            this.handleOpenScratchFileRequestMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.openDiffInBuffer,
            this.handleOpenDiffInBufferMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.getRemoteAgentStatus,
            this.handleGetRemoteAgentStatusMessage.bind(this)
        );

        _asyncMsgHandler.registerHandler(
            WebViewMessageType.saveLastRemoteAgentSetupRequest,
            this.handleSaveLastRemoteAgentSetupRequest.bind(this)
        );

        _asyncMsgHandler.registerHandler(
            WebViewMessageType.getLastRemoteAgentSetupRequest,
            this.handleGetLastRemoteAgentSetupRequest.bind(this)
        );

        // Register handlers for pinned remote agents
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.setRemoteAgentPinnedStatus,
            this.handleSetRemoteAgentPinnedStatusMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.getRemoteAgentPinnedStatusRequest,
            this.handleGetRemoteAgentPinnedStatusMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.deleteRemoteAgentPinnedStatus,
            this.handleDeleteRemoteAgentPinnedStatusMessage.bind(this)
        );

        _asyncMsgHandler.registerHandler(
            WebViewMessageType.remoteAgentPauseRequest,
            this.handleRemoteAgentPauseRequestMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.remoteAgentResumeRequest,
            this.handleRemoteAgentResumeRequestMessage.bind(this)
        );

        // Register handler for remote agent event metric
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.reportRemoteAgentEvent,
            this.handleReportRemoteAgentEventMessage.bind(this)
        );

        webview.onDidReceiveMessage((msg: WebViewMessage) => {
            switch (msg.type) {
                case WebViewMessageType.showRemoteAgentDiffPanel: {
                    this.handleShowRemoteAgentDiffPanelMessage(msg);
                    break;
                }
                case WebViewMessageType.closeRemoteAgentDiffPanel: {
                    RemoteAgentDiffPanel.close();
                    break;
                }
                case WebViewMessageType.showRemoteAgentHomePanel: {
                    this.handleShowRemoteAgentHomePanelMessage(msg);
                    break;
                }
                case WebViewMessageType.closeRemoteAgentHomePanel: {
                    this.handleCloseRemoteAgentHomePanelMessage(msg);
                    break;
                }
                default: {
                    // Ignore
                    break;
                }
            }
        });

        let remoteWorkspaceResolver = RemoteWorkspaceResolver.getInstance();
        if (!remoteWorkspaceResolver) {
            RemoteWorkspaceResolver.initialize(this._api, this._featureFlagManager);
            remoteWorkspaceResolver = RemoteWorkspaceResolver.getInstance();
        }
        if (remoteWorkspaceResolver) {
            this._disposables.push(
                remoteWorkspaceResolver.subscribeToRemoteAgentStatus((isRemoteAgent) => {
                    void webview.postMessage({
                        type: WebViewMessageType.remoteAgentStatusChanged,
                        data: {
                            isRemoteAgentSshWindow: isRemoteAgent,
                            remoteAgentId: RemoteWorkspaceResolver.getRemoteAgentId(),
                        },
                    } as RemoteAgentStatusChangedMessage);
                })
            );
            void webview.postMessage({
                type: WebViewMessageType.remoteAgentStatusChanged,
                data: {
                    isRemoteAgentSshWindow: RemoteWorkspaceResolver.isRemoteAgent(),
                    remoteAgentId: RemoteWorkspaceResolver.getRemoteAgentId(),
                },
            } as RemoteAgentStatusChangedMessage);
        }

        const webviewManager = WebviewManager.getInstance();
        if (!webviewManager.hasHandler(RemoteAgentsMessenger.messengerId)) {
            webviewManager.registerMessageHandler(
                RemoteAgentsMessenger.messengerId,
                async (msg: WebViewMessage) => {
                    await webview.postMessage(msg);
                }
            );
        }
    }

    private getAgentMemoriesAbsPath(): string | undefined {
        // Get the workspace-specific memories file path
        const baseUri = this._extensionContext.storageUri;
        if (baseUri) {
            return vscode.Uri.joinPath(baseUri, "Augment-Memories").fsPath;
        }
        return undefined;
    }

    private async handleCreateRemoteAgentMessage(msg: CreateRemoteAgentRequestMessage) {
        try {
            const {
                prompt,
                workspaceSetup,
                setupScript,
                isSetupScriptAgent,
                modelId,
                remoteAgentCreationMetrics,
            } = msg.data;

            // Get MCP servers from ToolConfigStore
            const mcpServers = await this._toolConfigStore.getMCPServers();
            const mcpServerConfigs = mcpServers
                .filter((server: MCPServer) => !server.disabled)
                .map((server: MCPServer) => ({
                    command: server.command,
                    args: server.arguments
                        ? server.arguments.split(" ").filter((arg: string) => arg.trim() !== "")
                        : [],
                    env: server.env ?? {},
                    use_shell_interpolation: server.useShellInterpolation,
                    name: server.name,
                    disabled: server.disabled,
                }));

            const requestDetails: RemoteAgentChatRequestDetails = {
                request_nodes: [
                    {
                        id: 1,
                        type: ChatRequestNodeType.TEXT,
                        text_node: {
                            content: prompt,
                        },
                    },
                ],
                user_guidelines: this._configListener.config?.chat.userGuidelines || "",
                workspace_guidelines: this._guidelinesWatcher.getCurrentWorkspaceGuidelinesContent(
                    this._workspaceManager
                ),
                agent_memories: await getAgentMemories(this._getAgentMemoriesAbsPath),
                mcp_servers: mcpServerConfigs,
            };

            const response = await this._api.createRemoteAgent(
                workspaceSetup,
                requestDetails,
                modelId,
                setupScript,
                isSetupScriptAgent
            );

            // Report the remote agent setup event
            getRemoteAgentSessionEventReporter().reportEvent({
                eventName: RemoteAgentSessionEventName.remoteAgentSetup,
                remoteAgentId: response.remote_agent_id,
                eventData: {
                    remoteAgentSetupData: {
                        usedGeneratedSetupScript: !!setupScript,
                        setupState: RemoteAgentSetupState.unknown,
                    },
                },
            });

            const changedRepo = remoteAgentCreationMetrics?.changedRepo ?? false;
            const changedBranch = remoteAgentCreationMetrics?.changedBranch ?? false;

            // Report the remote agent created event
            getRemoteAgentSessionEventReporter().reportEvent({
                eventName: RemoteAgentSessionEventName.remoteAgentCreated,
                remoteAgentId: response.remote_agent_id,
                eventData: {
                    remoteAgentCreated: {
                        changedRepo: changedRepo,
                        changedBranch: changedBranch,
                    },
                },
            });

            // eslint-disable-next-line no-console
            console.log("Created remote agent:", response);
            return {
                type: WebViewMessageType.createRemoteAgentResponse,
                data: {
                    success: true,
                    agentId: response.remote_agent_id,
                },
            } as CreateRemoteAgentResponseMessage;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to create remote agent:", error);
            return {
                type: WebViewMessageType.createRemoteAgentResponse,
                data: {
                    success: false,
                    error: error instanceof Error ? error.message : "Unknown error",
                },
            } as CreateRemoteAgentResponseMessage;
        }
    }

    private async handleDeleteRemoteAgentMessage(msg: DeleteRemoteAgentRequestMessage) {
        const { agentId, doSkipConfirmation } = msg.data;

        let hasPressedCancel = false;

        // Skip confirmation dialog if doSkipConfirmation is true
        if (!doSkipConfirmation) {
            const result = await vscode.window.showInformationMessage(
                "Are you sure you want to delete this remote agent?",
                { modal: true }, // Options
                { title: "Cancel", isCloseAffordance: true },
                { title: "Delete" }
            );
            hasPressedCancel = result?.title === "Cancel";
        }

        if (hasPressedCancel) {
            return {
                type: WebViewMessageType.deleteRemoteAgentResponse,
                data: {
                    success: false,
                    error: "User cancelled deletion",
                },
            } as DeleteRemoteAgentResponseMessage;
        }
        try {
            await this._api.deleteRemoteAgent(agentId);

            // Remove the remote platform for the agent so we don't clutter the user's Remote SSH settings
            await removeRemotePlatform(agentId);

            return {
                type: WebViewMessageType.deleteRemoteAgentResponse,
                data: {
                    success: true,
                },
            } as DeleteRemoteAgentResponseMessage;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to delete remote agent:", error);
            return {
                type: WebViewMessageType.deleteRemoteAgentResponse,
                data: {
                    success: false,
                    error: error instanceof Error ? error.message : "Unknown error",
                },
            } as DeleteRemoteAgentResponseMessage;
        }
    }

    private async handleGetRemoteAgentOverviewsMessage(_: GetRemoteAgentOverviewsRequest) {
        try {
            const response = await this._api.listRemoteAgents();
            return {
                type: WebViewMessageType.getRemoteAgentOverviewsResponse,
                data: {
                    overviews: response.remote_agents,
                    maxRemoteAgents: response.max_remote_agents,
                    maxActiveRemoteAgents: response.max_active_remote_agents,
                },
            } as GetRemoteAgentOverviewsResponse;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to get remote agent overviews:", error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
                type: WebViewMessageType.getRemoteAgentOverviewsResponse,
                data: {
                    overviews: [],
                    maxRemoteAgents: 0,
                    maxActiveRemoteAgents: 0,
                    error: errorMessage,
                },
            } as GetRemoteAgentOverviewsResponse;
        }
    }

    private async handleGetRemoteAgentChatHistoryMessage(msg: GetRemoteAgentChatHistoryRequest) {
        try {
            const { agentId, lastProcessedSequenceId } = msg.data;
            const response = await this._api.getRemoteAgentChatHistory(
                agentId,
                lastProcessedSequenceId
            );
            return {
                type: WebViewMessageType.getRemoteAgentChatHistoryResponse,
                data: {
                    chatHistory: response.chat_history,
                },
            } as GetRemoteAgentChatHistoryResponse;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to get remote agent chat history:", error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
                type: WebViewMessageType.getRemoteAgentChatHistoryResponse,
                data: {
                    chatHistory: [],
                    error: errorMessage,
                },
            } as GetRemoteAgentChatHistoryResponse;
        }
    }

    private async *handleRemoteAgentOverviewsStreamRequest(
        msg: RemoteAgentOverviewsStreamRequest
    ): AsyncGenerator<RemoteAgentOverviewsStreamResponse> {
        const { lastUpdateTimestamp, streamId } = msg.data;
        const controller = this._streamManager.startStream(streamId);

        const streamGenerator = this._api.getRemoteAgentOverviewsStream(
            lastUpdateTimestamp,
            controller.signal
        );

        // Process the stream and yield each update
        for await (const update of streamGenerator) {
            // Yield the update as a response message
            yield {
                type: WebViewMessageType.remoteAgentOverviewsStreamResponse,
                data: update,
            } as RemoteAgentOverviewsStreamResponse;
        }
    }

    private async *handleRemoteAgentHistoryStreamRequest(
        msg: RemoteAgentHistoryStreamRequest
    ): AsyncGenerator<RemoteAgentHistoryStreamResponse> {
        const { agentId, lastProcessedSequenceId, streamId } = msg.data;
        const controller = this._streamManager.startStream(streamId);

        const streamGenerator = this._api.getRemoteAgentChatHistoryStream(
            agentId,
            lastProcessedSequenceId,
            controller.signal
        );

        // Process the stream and yield each update
        for await (const update of streamGenerator) {
            // Yield the update as a response message
            yield {
                type: WebViewMessageType.remoteAgentHistoryStreamResponse,
                data: update,
            } as RemoteAgentHistoryStreamResponse;
        }
    }

    private handleCancelRemoteAgentsStreamRequest(
        msg: CancelRemoteAgentsStreamRequest
    ): EmptyMessage {
        const { streamId } = msg.data;
        this._streamManager.cancelStream(streamId);

        return {
            type: WebViewMessageType.empty,
        };
    }

    private async handleRemoteAgentChatRequestMessage(msg: RemoteAgentChatRequestMessage) {
        try {
            const { agentId, requestDetails, timeoutMs } = msg.data;
            const response = await this._api.remoteAgentChat(agentId, requestDetails, timeoutMs);
            return {
                type: WebViewMessageType.remoteAgentChatResponse,
                data: {
                    nodes: response.nodes,
                },
            } as RemoteAgentChatResponseMessage;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to get remote agent chat response:", error);
            return {
                type: WebViewMessageType.remoteAgentChatResponse,
                data: {
                    nodes: [],
                    error: error instanceof Error ? error.message : "Unknown error",
                },
            } as RemoteAgentChatResponseMessage;
        }
    }

    private async handleRemoteAgentInterruptRequestMessage(
        msg: RemoteAgentInterruptRequestMessage
    ) {
        // eslint-disable-next-line no-console
        console.log("Interrupting remote agent:", msg.data.agentId);
        const { agentId } = msg.data;
        try {
            const response = await this._api.interruptRemoteAgent(agentId);
            return {
                type: WebViewMessageType.remoteAgentInterruptResponse,
                data: response,
            } as RemoteAgentInterruptResponseMessage;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to interrupt remote agent:", error);
            return {
                type: WebViewMessageType.remoteAgentInterruptResponse,
                data: RemoteAgentStatus.agentFailed,
            } as RemoteAgentInterruptResponseMessage;
        }
    }

    private async handleRemoteAgentSshRequestMessage(
        msg: RemoteAgentSshRequestMessage
    ): Promise<RemoteAgentSshResponseMessage> {
        try {
            const { agentId } = msg.data;
            await this._remoteAgentSshManager.connectToRemoteAgent(agentId);
            return {
                type: WebViewMessageType.remoteAgentSshResponse,
                data: {
                    success: true,
                },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to get remote agent ssh config:", error);
            return {
                type: WebViewMessageType.remoteAgentSshResponse,
                data: {
                    success: false,
                    error: error instanceof Error ? error.message : "Unknown error",
                },
            };
        }
    }

    private async handleSetRemoteAgentNotificationEnabledMessage(
        msg: SetRemoteAgentNotificationEnabledMessage
    ): Promise<EmptyMessage> {
        try {
            const { agentId, enabled } = msg.data;
            const notificationSettings =
                (await this._globalState.load<RemoteAgentNotificationSettingsContext>(
                    FileStorageKey.remoteAgentNotificationEnabled,
                    { uniquePerWorkspace: true }
                )) ?? {};
            await this._globalState.save(
                FileStorageKey.remoteAgentNotificationEnabled,
                {
                    ...notificationSettings,
                    [agentId]: enabled,
                },
                {
                    uniquePerWorkspace: true,
                }
            );
            return {
                type: WebViewMessageType.empty,
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to set remote agent notification enabled:", error);
            return {
                type: WebViewMessageType.empty,
            };
        }
    }

    private async handleGetRemoteAgentNotificationEnabledMessage(
        msg: GetRemoteAgentNotificationEnabledMessage
    ): Promise<GetRemoteAgentNotificationEnabledResponseMessage> {
        try {
            const notificationSettings =
                await this._globalState.load<RemoteAgentNotificationSettingsContext>(
                    FileStorageKey.remoteAgentNotificationEnabled,
                    { uniquePerWorkspace: true }
                );
            const { agentIds } = msg.data;
            if (!agentIds) {
                return {
                    type: WebViewMessageType.getRemoteAgentNotificationEnabledResponse,
                    data: notificationSettings ?? {},
                };
            }
            return {
                type: WebViewMessageType.getRemoteAgentNotificationEnabledResponse,
                data: agentIds.reduce((acc, id) => {
                    acc[id] = notificationSettings?.[id] ?? false;
                    return acc;
                }, {} as RemoteAgentNotificationSettingsContext),
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to get remote agent notification enabled:", error);
            return {
                type: WebViewMessageType.getRemoteAgentNotificationEnabledResponse,
                data: {},
            };
        }
    }

    private async handleDeleteRemoteAgentNotificationEnabledMessage(
        msg: DeleteRemoteAgentNotificationEnabledMessage
    ): Promise<EmptyMessage> {
        try {
            const { agentId } = msg.data;
            const notificationSettings =
                await this._globalState.load<RemoteAgentNotificationSettingsContext>(
                    FileStorageKey.remoteAgentNotificationEnabled,
                    { uniquePerWorkspace: true }
                );
            if (!notificationSettings) {
                return {
                    type: WebViewMessageType.empty,
                };
            }
            delete notificationSettings[agentId];
            await this._globalState.save(
                FileStorageKey.remoteAgentNotificationEnabled,
                notificationSettings,
                {
                    uniquePerWorkspace: true,
                }
            );
            return {
                type: WebViewMessageType.empty,
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to delete remote agent notification enabled:", error);
            return {
                type: WebViewMessageType.empty,
            };
        }
    }

    private async handleRemoteAgentNotifyReadyMessage(
        msg: RemoteAgentNotifyReadyMessage
    ): Promise<EmptyMessage> {
        const agentId = msg.data.remote_agent_id;
        const agentName = msg.data.session_summary;

        const notificationSettings =
            await this._globalState.load<RemoteAgentNotificationSettingsContext>(
                FileStorageKey.remoteAgentNotificationEnabled,
                { uniquePerWorkspace: true }
            );
        if (!notificationSettings?.[agentId]) {
            return {
                type: WebViewMessageType.empty,
            };
        }

        const maxNameLength = 50; // Maximum length for agent name in notification
        const truncatedName =
            agentName && agentName.length > maxNameLength
                ? `${agentName.substring(0, maxNameLength)}...`
                : agentName;
        const displayName = truncatedName || "Remote Agent";
        const notificationMessage = `Remote agent "${displayName}" is waiting for your input!`;

        void vscode.window
            .showInformationMessage(notificationMessage, {
                title: "Go to Remote Agent",
                isCloseAffordance: false,
                onClick: async () => {
                    // Focus the chat window
                    return vscode.commands
                        .executeCommand("vscode-augment.focusAugmentPanel")
                        .then(() => {
                            void this._webview?.postMessage({
                                type: WebViewMessageType.remoteAgentSelectAgentId,
                                data: { agentId },
                            });
                        });
                },
            })
            .then((selection) => {
                if (selection?.onClick) {
                    void selection.onClick();
                }
            });

        return {
            type: WebViewMessageType.empty,
        };
    }

    private handleShowRemoteAgentDiffPanelMessage(msg: ShowRemoteAgentDiffPanelMessage) {
        const opts: IRemoteAgentDiffPanelOptions = msg.data;
        RemoteAgentDiffPanel.createOrShow(
            this._extensionUri,
            this._api,
            this._workTimer,
            this._globalState,
            opts
        );
    }

    /**
     * Handle request to list all available setup scripts
     * Searches in multiple locations:
     * - ~/.augment/env/
     * - <git_root>/.augment/env/
     * - <workspace_root>/.augment/env/
     */
    private async handleListSetupScriptsMessage(
        _: ListSetupScriptsRequest
    ): Promise<ListSetupScriptsResponse> {
        try {
            const scripts = await this._setupScriptsManager.listSetupScripts();
            return {
                type: WebViewMessageType.listSetupScriptsResponse,
                data: { scripts },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to list setup scripts:", error);
            return {
                type: WebViewMessageType.listSetupScriptsResponse,
                data: {
                    scripts: [],
                    error: error instanceof Error ? error.message : String(error),
                },
            };
        }
    }

    /**
     * Handle request to save a setup script to a specific location
     */
    private async handleSaveSetupScriptMessage(
        msg: SaveSetupScriptRequest
    ): Promise<SaveSetupScriptResponse> {
        try {
            const { name, content, location } = msg.data;
            const path = await this._setupScriptsManager.createSetupScriptFile(
                location,
                name,
                content
            );
            return {
                type: WebViewMessageType.saveSetupScriptResponse,
                data: { success: true, path },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to save setup script:", error);
            return {
                type: WebViewMessageType.saveSetupScriptResponse,
                data: {
                    success: false,
                    error: error instanceof Error ? error.message : String(error),
                },
            };
        }
    }

    private async handleRemoteAgentWorkspaceLogsRequestMessage(
        msg: RemoteAgentWorkspaceLogsRequestMessage
    ): Promise<RemoteAgentWorkspaceLogsResponseMessage> {
        try {
            const { agentId, lastProcessedStep, lastProcessedSequenceId } = msg.data;
            const response = await this._api.getRemoteAgentWorkspaceLogs(
                agentId,
                lastProcessedStep,
                lastProcessedSequenceId
            );
            return {
                type: WebViewMessageType.remoteAgentWorkspaceLogsResponse,
                data: {
                    workspaceSetupStatus: response.workspace_setup_status,
                },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to get remote agent workspace logs:", error);
            return {
                type: WebViewMessageType.remoteAgentWorkspaceLogsResponse,
                data: {
                    workspaceSetupStatus: { steps: [] },
                },
            };
        }
    }

    private handleShowRemoteAgentHomePanelMessage(_: ShowRemoteAgentHomePanelMessage) {
        const deps = {
            extensionUri: this._extensionUri,
            apiServer: this._api,
            workTimer: this._workTimer,
            globalState: this._globalState,
            toolConfigStore: this._toolConfigStore,
            configListener: this._configListener,
            guidelinesWatcher: this._guidelinesWatcher,
            workspaceManager: this._workspaceManager,
            extensionContext: this._extensionContext,
            featureFlagManager: this._featureFlagManager,
        };
        RemoteAgentHomePanel.createOrShow(deps);
    }

    private handleCloseRemoteAgentHomePanelMessage(_: CloseRemoteAgentHomePanelMessage) {
        RemoteAgentHomePanel.close();
    }

    /**
     * Handle request to delete a setup script
     */
    private async handleDeleteSetupScriptMessage(
        msg: DeleteSetupScriptRequest
    ): Promise<DeleteSetupScriptResponse> {
        try {
            const { name, location } = msg.data;
            const success = await this._setupScriptsManager.deleteSetupScript(name, location);
            return {
                type: WebViewMessageType.deleteSetupScriptResponse,
                data: { success },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to delete setup script:", error);
            return {
                type: WebViewMessageType.deleteSetupScriptResponse,
                data: {
                    success: false,
                    error: error instanceof Error ? error.message : String(error),
                },
            };
        }
    }

    /**
     * Handle request to rename a setup script
     */
    private async handleRenameSetupScriptMessage(
        msg: RenameSetupScriptRequest
    ): Promise<RenameSetupScriptResponse> {
        try {
            const { oldName, newName, location } = msg.data;
            const path = await this._setupScriptsManager.renameSetupScript(
                oldName,
                newName,
                location
            );
            return {
                type: WebViewMessageType.renameSetupScriptResponse,
                data: { success: true, path },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to rename setup script:", error);
            return {
                type: WebViewMessageType.renameSetupScriptResponse,
                data: {
                    success: false,
                    error: error instanceof Error ? error.message : String(error),
                },
            };
        }
    }

    /**
     * Handle request to open a scratch file with content
     * Creates a new untitled file with the provided content
     */
    private async handleOpenScratchFileRequestMessage(
        msg: OpenScratchFileRequestMessage
    ): Promise<EmptyMessage> {
        try {
            const { content, language } = msg.data;

            const document = await vscode.workspace.openTextDocument({
                content,
                language: language || "shellscript",
            });
            await vscode.window.showTextDocument(document);

            return {
                type: WebViewMessageType.empty,
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to open scratch file:", error);
            return {
                type: WebViewMessageType.empty,
            };
        }
    }

    /**
     * Handle request to open a diff in a buffer
     */
    private async handleOpenDiffInBufferMessage(
        msg: OpenDiffInBufferMessage
    ): Promise<EmptyMessage> {
        try {
            const { oldContents, newContents, filePath } = msg.data;
            await openDiffInBuffer(oldContents, newContents, filePath);
            return {
                type: WebViewMessageType.empty,
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to open diff in buffer:", error);
            return {
                type: WebViewMessageType.empty,
            };
        }
    }

    /**
     * Handle request to get remote agent status
     * Returns whether the current window is a remote agent window and the agent ID if applicable
     */
    private handleGetRemoteAgentStatusMessage(
        _: GetRemoteAgentStatusMessage
    ): RemoteAgentStatusResponseMessage {
        this._logger.info("Remote agent status handler called");
        try {
            const isRemoteAgentSshWindow = RemoteWorkspaceResolver.isRemoteAgent();
            const remoteAgentId = RemoteWorkspaceResolver.getRemoteAgentId();

            this._logger.info(
                `Remote agent status: isRemoteAgentSshWindow=${isRemoteAgentSshWindow}, remoteAgentId=${remoteAgentId}`
            );

            return {
                type: WebViewMessageType.remoteAgentStatusResponse,
                data: {
                    isRemoteAgentSshWindow,
                    remoteAgentId,
                },
            } as RemoteAgentStatusResponseMessage;
        } catch (error) {
            this._logger.error("Error getting remote agent status", error);
            return {
                type: WebViewMessageType.remoteAgentStatusResponse,
                data: {
                    isRemoteAgentSshWindow: false,
                    remoteAgentId: undefined,
                },
            };
        }
    }

    /*
     * Handle request to save last remote agent setup preferences
     */
    private async handleSaveLastRemoteAgentSetupRequest(
        msg: SaveLastRemoteAgentSetupRequest
    ): Promise<EmptyMessage> {
        try {
            const {
                lastRemoteAgentGitRepoUrl,
                lastRemoteAgentGitBranch,
                lastRemoteAgentSetupScript,
            } = msg.data;

            if (lastRemoteAgentGitRepoUrl) {
                await this._globalState.update(
                    GlobalContextKey.lastRemoteAgentGitRepoUrl,
                    lastRemoteAgentGitRepoUrl
                );
            }

            if (lastRemoteAgentGitBranch) {
                await this._globalState.update(
                    GlobalContextKey.lastRemoteAgentGitBranch,
                    lastRemoteAgentGitBranch
                );
            }

            await this._globalState.update(
                GlobalContextKey.lastRemoteAgentSetupScript,
                lastRemoteAgentSetupScript
            );

            return {
                type: WebViewMessageType.empty,
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to save last remote agent setup:", error);
            return {
                type: WebViewMessageType.empty,
            };
        }
    }

    /**
     * Handle request to get last remote agent setup preferences
     */
    private handleGetLastRemoteAgentSetupRequest(): Promise<GetLastRemoteAgentSetupResponse> {
        try {
            const lastRemoteAgentGitRepoUrl =
                this._globalState.get<string>(GlobalContextKey.lastRemoteAgentGitRepoUrl) || null;

            const lastRemoteAgentGitBranch =
                this._globalState.get<string>(GlobalContextKey.lastRemoteAgentGitBranch) || null;

            const lastRemoteAgentSetupScript =
                this._globalState.get<string>(GlobalContextKey.lastRemoteAgentSetupScript) || null;

            return Promise.resolve({
                type: WebViewMessageType.getLastRemoteAgentSetupResponse,
                data: {
                    lastRemoteAgentGitRepoUrl,
                    lastRemoteAgentGitBranch,
                    lastRemoteAgentSetupScript,
                },
            });
        } catch (error) {
            // eslint-disable-next-line no-console
            logger.error("Failed to get last remote agent setup:", error);
            return Promise.resolve({
                type: WebViewMessageType.getLastRemoteAgentSetupResponse,
                data: {
                    lastRemoteAgentGitRepoUrl: null,
                    lastRemoteAgentGitBranch: null,
                    lastRemoteAgentSetupScript: null,
                },
            });
        }
    }

    /**
     * Handle setting the pinned status for a remote agent
     */
    private async handleSetRemoteAgentPinnedStatusMessage(
        msg: SetRemoteAgentPinnedStatusMessage
    ): Promise<EmptyMessage> {
        try {
            const { agentId, isPinned } = msg.data;
            const pinnedStatus =
                (await this._globalState.load<RemoteAgentPinnedStatusContext>(
                    FileStorageKey.remoteAgentPinnedStatus,
                    { uniquePerWorkspace: true }
                )) ?? {};
            await this._globalState.save(
                FileStorageKey.remoteAgentPinnedStatus,
                {
                    ...pinnedStatus,
                    [agentId]: isPinned,
                },
                {
                    uniquePerWorkspace: true,
                }
            );
            return {
                type: WebViewMessageType.empty,
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            logger.error("Failed to set remote agent pinned status:", error);
            return {
                type: WebViewMessageType.empty,
            };
        }
    }

    /**
     * Handle getting the pinned status for remote agents
     */
    private async handleGetRemoteAgentPinnedStatusMessage(
        msg: GetRemoteAgentPinnedStatusRequestMessage
    ): Promise<GetRemoteAgentPinnedStatusResponseMessage> {
        try {
            // Initialize with empty object if not found
            const pinnedStatus =
                (await this._globalState.load<RemoteAgentPinnedStatusContext>(
                    FileStorageKey.remoteAgentPinnedStatus,
                    { uniquePerWorkspace: true }
                )) || {};

            const agentIds = msg.data?.agentIds;

            if (!agentIds) {
                return {
                    type: WebViewMessageType.getRemoteAgentPinnedStatusResponse,
                    data: pinnedStatus,
                };
            }

            const result = agentIds.reduce((acc, id) => {
                acc[id] = pinnedStatus[id] || false;
                return acc;
            }, {} as RemoteAgentPinnedStatusContext);

            return {
                type: WebViewMessageType.getRemoteAgentPinnedStatusResponse,
                data: result,
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            logger.error("Failed to get remote agent pinned status:", error);
            return {
                type: WebViewMessageType.getRemoteAgentPinnedStatusResponse,
                data: {},
            };
        }
    }

    /**
     * Handle deleting the pinned status for a remote agent
     */
    private async handleDeleteRemoteAgentPinnedStatusMessage(
        msg: DeleteRemoteAgentPinnedStatusMessage
    ): Promise<EmptyMessage> {
        try {
            const { agentId } = msg.data;
            const pinnedStatus = await this._globalState.load<RemoteAgentPinnedStatusContext>(
                FileStorageKey.remoteAgentPinnedStatus,
                { uniquePerWorkspace: true }
            );
            if (!pinnedStatus) {
                return {
                    type: WebViewMessageType.empty,
                };
            }
            delete pinnedStatus[agentId];
            await this._globalState.save(FileStorageKey.remoteAgentPinnedStatus, pinnedStatus, {
                uniquePerWorkspace: true,
            });
            return {
                type: WebViewMessageType.empty,
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            logger.error("Failed to delete remote agent pinned status:", error);
            return {
                type: WebViewMessageType.empty,
            };
        }
    }

    private async handleRemoteAgentPauseRequestMessage(
        msg: RemoteAgentPauseRequestMessage
    ): Promise<EmptyMessage> {
        try {
            const { agentId } = msg.data;
            await this._api.pauseRemoteAgentWorkspace(agentId);
            return {
                type: WebViewMessageType.empty,
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            logger.error("Failed to pause remote agent:", error);
            return {
                type: WebViewMessageType.empty,
            };
        }
    }

    private async handleRemoteAgentResumeRequestMessage(
        msg: RemoteAgentResumeRequestMessage
    ): Promise<EmptyMessage> {
        try {
            const { agentId } = msg.data;
            await this._api.resumeRemoteAgentWorkspace(agentId);
            return {
                type: WebViewMessageType.empty,
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            logger.error("Failed to resume remote agent:", error);
            return {
                type: WebViewMessageType.empty,
            };
        }
    }

    /**
     * Handle remote agent event
     * Reports a metric when the setup page is opened
     */
    private handleReportRemoteAgentEventMessage(msg: ReportRemoteAgentEventMessage): EmptyMessage {
        try {
            // Report the setup page opened event
            // Note: We don't have a specific remote agent ID at this point since this is
            // triggered when the setup page is mounted, before an agent is created
            getRemoteAgentSessionEventReporter().reportEvent(msg.data);
        } catch (error) {
            // Don't let error reporting itself cause issues
            this._logger.error("Failed to report setup page opened event:", error);
        }

        return {
            type: WebViewMessageType.empty,
        };
    }
}
