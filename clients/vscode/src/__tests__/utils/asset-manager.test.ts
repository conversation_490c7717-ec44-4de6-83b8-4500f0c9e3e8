import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import * as vscode from "vscode";

import { mockFSUtils } from "../../__mocks__/fs-utils";
import { ExtensionContext } from "../../__mocks__/vscode-mocks";
import { AssetManager } from "../../client-interfaces/asset-manager";

describe("AssetManager", () => {
    let extensionContext: ExtensionContext;
    let assetManager: AssetManager;
    let storageUri: vscode.Uri;
    let assetBasePath: string;

    beforeEach(() => {
        // Reset the mock filesystem before each test
        mockFSUtils.reset();

        // Create a mock extension context
        storageUri = vscode.Uri.file("/storage");
        // Create a mock extension context with a spy to override the storageUri property
        extensionContext = new ExtensionContext();
        // Use Object.defineProperty to override the read-only property
        Object.defineProperty(extensionContext, "storageUri", {
            get: () => storageUri,
        });

        // Create the asset manager
        assetManager = new AssetManager(extensionContext);

        // Base path for assets
        assetBasePath = joinPath(storageUri.fsPath, "augment-user-assets");
    });

    test("saveAsset should save an asset in the root directory", async () => {
        const assetName = "test-asset.txt";
        const assetContent = new Uint8Array([1, 2, 3, 4, 5]);

        // Save the asset
        await assetManager.saveAsset(assetName, assetContent);

        // Verify the content
        const expectedPath = joinPath(assetBasePath, assetName);
        mockFSUtils.verifyFileRaw(expectedPath, assetContent);
    });

    test("saveAsset should save an asset in a nested directory", async () => {
        const assetName = "nested/directory/test-asset.txt";
        const assetContent = new Uint8Array([1, 2, 3, 4, 5]);

        // Save the asset
        await assetManager.saveAsset(assetName, assetContent);

        // Verify the content
        const expectedPath = joinPath(assetBasePath, assetName);
        mockFSUtils.verifyFileRaw(expectedPath, assetContent);

        // Verify the directory was created
        const dirPath = expectedPath.substring(0, expectedPath.lastIndexOf("/"));
        expect(mockFSUtils.statFile(dirPath).type).toBe("Directory"); // FileType.directory
    });

    test("loadAsset should load an asset from the root directory", async () => {
        const assetName = "test-asset.txt";
        const assetContent = new Uint8Array([1, 2, 3, 4, 5]);

        // Save the asset first
        await assetManager.saveAsset(assetName, assetContent);

        // Load the asset
        const loadedContent = await assetManager.loadAsset(assetName);

        // Verify the content
        expect(loadedContent).toEqual(assetContent);
    });

    test("loadAsset should load an asset from a nested directory", async () => {
        const assetName = "nested/directory/test-asset.txt";
        const assetContent = new Uint8Array([1, 2, 3, 4, 5]);

        // Save the asset first
        await assetManager.saveAsset(assetName, assetContent);

        // Load the asset
        const loadedContent = await assetManager.loadAsset(assetName);

        // Verify the content
        expect(loadedContent).toEqual(assetContent);
    });

    test("loadAsset should return undefined for non-existent assets", async () => {
        const assetName = "non-existent-asset.txt";

        // Load the asset
        const loadedContent = await assetManager.loadAsset(assetName);

        // Verify the content is undefined
        expect(loadedContent).toBeUndefined();
    });

    test("deleteAsset should delete an asset from the root directory", async () => {
        const assetName = "test-asset.txt";
        const assetContent = new Uint8Array([1, 2, 3, 4, 5]);

        // Save the asset first
        await assetManager.saveAsset(assetName, assetContent);

        // Delete the asset
        await assetManager.deleteAsset(assetName);

        // Verify the asset was deleted
        const expectedPath = joinPath(assetBasePath, assetName);
        expect(() => mockFSUtils.statFile(expectedPath)).toThrow();

        // Verify loadAsset returns undefined
        const loadedContent = await assetManager.loadAsset(assetName);
        expect(loadedContent).toBeUndefined();
    });

    test("deleteAsset should delete an asset from a nested directory", async () => {
        const assetName = "nested/directory/test-asset.txt";
        const assetContent = new Uint8Array([1, 2, 3, 4, 5]);

        // Save the asset first
        await assetManager.saveAsset(assetName, assetContent);

        // Delete the asset
        await assetManager.deleteAsset(assetName);

        // Verify the asset was deleted
        const expectedPath = joinPath(assetBasePath, assetName);
        expect(() => mockFSUtils.statFile(expectedPath)).toThrow();

        // Verify loadAsset returns undefined
        const loadedContent = await assetManager.loadAsset(assetName);
        expect(loadedContent).toBeUndefined();
    });

    test("should handle multiple assets in the same nested directory", async () => {
        const assetName1 = "nested/directory/test-asset1.txt";
        const assetName2 = "nested/directory/test-asset2.txt";
        const assetContent1 = new Uint8Array([1, 2, 3]);
        const assetContent2 = new Uint8Array([4, 5, 6]);

        // Save the assets
        await assetManager.saveAsset(assetName1, assetContent1);
        await assetManager.saveAsset(assetName2, assetContent2);

        // Verify both assets were saved correctly
        const expectedPath1 = joinPath(assetBasePath, assetName1);
        const expectedPath2 = joinPath(assetBasePath, assetName2);

        mockFSUtils.verifyFileRaw(expectedPath1, assetContent1);
        mockFSUtils.verifyFileRaw(expectedPath2, assetContent2);

        // Load the assets
        const loadedContent1 = await assetManager.loadAsset(assetName1);
        const loadedContent2 = await assetManager.loadAsset(assetName2);

        // Verify the content
        expect(loadedContent1).toEqual(assetContent1);
        expect(loadedContent2).toEqual(assetContent2);
    });

    test("should handle assets with deeply nested paths", async () => {
        const assetName = "very/deeply/nested/path/structure/test-asset.txt";
        const assetContent = new Uint8Array([1, 2, 3, 4, 5]);

        // Save the asset
        await assetManager.saveAsset(assetName, assetContent);

        // Verify the content
        const expectedPath = joinPath(assetBasePath, assetName);
        mockFSUtils.verifyFileRaw(expectedPath, assetContent);

        // Verify all directories were created
        let currentPath = assetBasePath;
        const parts = assetName.split("/");
        // Check all directories except the last part (which is the file)
        for (let i = 0; i < parts.length - 1; i++) {
            currentPath = joinPath(currentPath, parts[i]);
            expect(mockFSUtils.statFile(currentPath).type).toBe("Directory"); // FileType.directory
        }
    });
});
