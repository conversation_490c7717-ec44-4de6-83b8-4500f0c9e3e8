import { RecentItems } from "../../utils/recent-items";

describe("recent-items", () => {
    test("addItem", () => {
        const recentEvens = new RecentItems<number>(2, (n) => n % 2 === 0);
        recentEvens.addItem(1);
        expect(recentEvens.items.length).toBe(0);

        recentEvens.addItem(2);
        expect(recentEvens.items.length).toBe(1);

        recentEvens.addItem(3);
        expect(recentEvens.items.length).toBe(1);

        recentEvens.addItem(4);
        expect(recentEvens.items.length).toBe(2);

        expect(recentEvens.items).toEqual([2, 4]);

        recentEvens.addItem(6);
        expect(recentEvens.items.length).toBe(2);

        expect(recentEvens.items).toEqual([4, 6]);
    });
});
