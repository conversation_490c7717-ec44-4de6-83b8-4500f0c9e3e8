/**
 * Unit tests for AugmentExtension class.
 */
// These mocks must come first.
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import type { WorkspaceConfiguration } from "vscode";
import * as vscode from "vscode";

import { MockFSUtils, mockFSUtils } from "../__mocks__/fs-utils";
import { MockAPIServer } from "../__mocks__/mock-api-server";
import {
    generateMockWorkspaceConfig,
    getExampleUserConfig,
} from "../__mocks__/mock-augment-config";
import {
    mockDefaultModelPrefixLength,
    mockDefaultModelSuffixLength,
    mockGetModelsResult,
} from "../__mocks__/mock-modelinfo";
import {
    CancellationToken,
    completionProviderRegistry,
    ExtensionContext,
    inlineCompletionProviderRegistry,
    languages,
    Memento,
    mockWorkspaceConfigChange,
    MutableTextDocument,
    newMockStatusBar,
    openTextDocument,
    resetMockWorkspace,
    setWorkspaceFolders,
    TextEditor,
    Webview,
    WebviewView,
    window,
} from "../__mocks__/vscode-mocks";
import { ModelConfig } from "../augment-api";
import { AugmentConfig, AugmentConfigListener } from "../augment-config-listener";
import { AuthSessionStore } from "../auth/auth-session-store";
import { OAuthFlow } from "../auth/oauth-flow";
import { ChatRequest } from "../chat/chat-model";
import { AssetManager } from "../client-interfaces/asset-manager";
import { AugmentInstruction } from "../code-edit-types";
import { CompletionItemsProvider } from "../completions/completion-items-provider";
import { RecentCompletions } from "../completions/recent-completions";
import { _exportedForTesting, AugmentExtension, getSessionId } from "../extension";
import { defaultFeatureFlags } from "../feature-flags";
import { ActionsModel } from "../main-panel/action-cards/actions-model";
import { ChatExtensionMessage } from "../main-panel/apps/chat-webview-app";
import { SignInApp } from "../main-panel/apps/sign-in-webview-app";
import { OnboardingSessionEventReporter } from "../metrics/onboarding-session-event-reporter";
import { NextEditResultInfo } from "../next-edit/next-edit-types";
import { initializing, initialState, noAutoCompletions } from "../statusbar/status-bar-states";
import { AugmentGlobalState } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { RecentItems } from "../utils/recent-items";
import { MainPanelWebviewProvider } from "../webview-providers/main-panel-webview-provider";
import { MainPanelApp, NextEditWebViewMessage } from "../webview-providers/webview-messages";
import { SyncingEnabledTracker } from "../workspace/syncing-enabled-tracker";
import { verifyDefined } from "./__utils__/test-utils";
import { advanceTimeUntilTrue } from "./__utils__/time";

class ExtensionTestKit extends DisposableService {
    static readonly workspaceFolders: ReadonlyArray<string> = ["/test/folder"];
    static readonly workspaceFiles = new Map<string, string>([
        ["/test/folder/first_file.py", "def first(x):\n    return x + 1\n\n"],
        ["/test/folder/second_file.cpp", "main(){ return 0; }"],
    ]);

    public readonly fileUris: vscode.Uri[];
    public configListener: AugmentConfigListener;
    public readonly blobNameCalculator = new BlobNameCalculator(10000);
    public readonly dummyDocument: MutableTextDocument;

    constructor() {
        super();
        setWorkspaceFolders(ExtensionTestKit.workspaceFolders);
        this.configListener = new AugmentConfigListener();
        this.addDisposable(this.configListener);

        this.fileUris = [];
        for (const [pathName, contents] of ExtensionTestKit.workspaceFiles) {
            this.fs.writeFileUtf8(pathName, contents, true);
            this.fileUris.push(vscode.Uri.file(pathName));
        }

        this.dummyDocument = this.createAndOpenFile("dummy.py", "this is dummy.py");
    }

    public createAndOpenFile(fileName: string, contents: string): MutableTextDocument {
        const uri = vscode.Uri.file(fileName);
        this.fs.writeFileUtf8(fileName, contents, true);
        return openTextDocument(uri);
    }

    // createExtension creates an extension object. This object must be disposed before the end
    // of the test, as it starts background work that must be shut down (via dispose) or the test
    // will hang.
    // The apiServer used by the extension can be optionally overriden with a provided instance.
    public async createExtension(
        completionResponseParams: {
            suggestedPrefixCharCount: number | undefined;
            suggestedSuffixCharCount: number | undefined;
        } = MockAPIServer.defaultCompletionResponseParams,
        overrideApiServer?: MockAPIServer,
        options: {
            mainPanelProvider?: MainPanelWebviewProvider;
            changeMainPanelApp?: vscode.EventEmitter<MainPanelApp>;
            context?: ExtensionContext;
        } = {}
    ): Promise<AugmentExtension> {
        const context = options.context ?? new ExtensionContext();
        const globalState = new AugmentGlobalState(context);
        const sessionId = getSessionId(globalState);
        const apiServer =
            overrideApiServer ??
            new MockAPIServer(undefined, undefined, sessionId, completionResponseParams);
        const auth = new AuthSessionStore(context, this.configListener);
        const recentCompletions = new RecentCompletions();
        const recentInstructions = new RecentItems<AugmentInstruction>(10);
        const recentNextEditResults = new RecentItems<NextEditResultInfo>(10);
        const recentChats = new RecentItems<ChatRequest>(10);
        const chatExtensionEvent = new vscode.EventEmitter<ChatExtensionMessage>();
        const nextEditWebViewEvent = new vscode.EventEmitter<NextEditWebViewMessage>();
        const mainPanelProvider =
            options.mainPanelProvider ?? new MainPanelWebviewProvider(context.extensionUri);
        const changeApp = options.changeMainPanelApp ?? new vscode.EventEmitter<MainPanelApp>();
        const syncingStateTracker = new SyncingEnabledTracker();
        const mockAssetManager = new AssetManager(context);
        const extension = new AugmentExtension(
            context,
            globalState,
            this.configListener,
            apiServer,
            auth,
            recentCompletions,
            recentInstructions,
            recentNextEditResults,
            recentChats,
            nextEditWebViewEvent,
            new vscode.EventEmitter<void>(),
            mainPanelProvider,
            changeApp,
            new ActionsModel(globalState),
            syncingStateTracker,
            chatExtensionEvent,
            new OnboardingSessionEventReporter(apiServer),
            mockAssetManager
        );

        this.addDisposable(extension);

        await extension.enable();
        await this.awaitStarted(extension);
        return extension;
    }

    public async runTimers() {
        await jest.advanceTimersByTimeAsync(100);
    }

    public async awaitStarted(extension: AugmentExtension) {
        const workspaceManager = extension.workspaceManager;
        verifyDefined(workspaceManager);
        await advanceTimeUntilTrue(() => workspaceManager.initialFoldersSynced);
    }

    public get config(): AugmentConfig {
        return this.configListener.config;
    }

    public get fs(): MockFSUtils {
        return mockFSUtils;
    }

    public copyConfig(config: AugmentConfig): WorkspaceConfiguration {
        return generateMockWorkspaceConfig({
            shortcutsDisplayDelayMS: config.shortcutsDisplayDelayMS,
            completions: {
                enableAutomaticCompletions: config.completions.enableAutomaticCompletions,
                disableCompletionsByLanguage: Array.from(
                    config.completions.disableCompletionsByLanguage
                ),
            },
            advanced: {
                apiToken: config.apiToken,
                completionURL: config.completionURL,
                model: config.modelName,
                codeInstruction: {
                    model: `${config.codeInstruction.model}`,
                },
                chat: {
                    url: `${config.chat.url}`,
                    model: `${config.chat.model}`,
                    stream: config.chat.stream,
                },
                oauth: {
                    clientID: config.oauth.clientID,
                    url: config.oauth.url,
                },
                enableWorkspaceUpload: config.enableUpload,
                enableDebugFeatures: config.enableDebugFeatures,
                enableReviewerWorkflows: config.enableReviewerWorkflows,
                completions: config.completions,
                enableDataCollection: config.enableDataCollection,
                recencySignalManager: config.recencySignalManager,
                preferenceCollection: {
                    enable: config.preferenceCollection.enable,
                    enableRetrievalDataCollection:
                        config.preferenceCollection.enableRetrievalDataCollection,
                    enableRandomizedMode: config.preferenceCollection.enableRandomizedMode,
                },
                vcs: {
                    watcherEnabled: config.vcs.watcherEnabled,
                },
                smartPaste: config.smartPaste,
                instructions: config.instructions,
                integrations: config.integrations,
            },
        });
    }
}

describe("extension", () => {
    let kit: ExtensionTestKit;

    beforeEach(() => {
        jest.useFakeTimers();
        resetMockWorkspace();
        languages.reset();
        kit = new ExtensionTestKit();
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.useRealTimers();
        languages.reset();
        resetMockWorkspace();
        kit.dispose();
    });

    test("ensure inline completion providers are registered", async () => {
        await kit.createExtension();

        const completionRegistry = inlineCompletionProviderRegistry;
        expect(completionRegistry.registry.size).toBeGreaterThan(0);
    });

    test("enable/disable intellisense completions", async () => {
        mockWorkspaceConfigChange(generateMockWorkspaceConfig(getExampleUserConfig()));
        const registerSpy = jest.spyOn(languages, "registerCompletionItemProvider");

        await kit.createExtension();

        // Make enableAutomaticCompletions true and check a completion item
        // provider was registered.
        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig(
                getExampleUserConfig({
                    completions: {
                        enableAutomaticCompletions: true,
                    },
                    advanced: {
                        completions: {
                            addIntelliSenseSuggestions: true,
                        },
                    },
                })
            )
        );
        expect(completionProviderRegistry.registry.size).toBeGreaterThan(0);
        expect(registerSpy).toHaveBeenCalledTimes(1);
        expect(registerSpy.mock.calls[0]).toEqual([
            CompletionItemsProvider.languageSelector,
            expect.any(CompletionItemsProvider),
            ...CompletionItemsProvider.triggerCharacters,
        ]);
        registerSpy.mockClear();

        // Disable intellisense
        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig(
                getExampleUserConfig({
                    completions: {
                        enableAutomaticCompletions: true,
                    },
                    advanced: {
                        completions: {
                            addIntelliSenseSuggestions: false,
                        },
                    },
                })
            )
        );
        expect(completionProviderRegistry.registry.size).toBe(0);

        // Re-enable intellisense
        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig(
                getExampleUserConfig({
                    completions: {
                        enableAutomaticCompletions: true,
                    },
                    advanced: {
                        completions: {
                            addIntelliSenseSuggestions: true,
                        },
                    },
                })
            )
        );
        expect(completionProviderRegistry.registry.size).toBeGreaterThan(0);
        expect(registerSpy).toHaveBeenCalledTimes(1);
        expect(registerSpy.mock.calls[0]).toEqual([
            CompletionItemsProvider.languageSelector,
            expect.any(CompletionItemsProvider),
            ...CompletionItemsProvider.triggerCharacters,
        ]);
    });

    test("session-id-persistent", async () => {
        const extension1 = await kit.createExtension();
        const sessionId1 = extension1.sessionId;

        // Key entry already there.  id should be the same
        const extension2 = await kit.createExtension();
        expect(extension2.sessionId).toBe(sessionId1);
    });

    test("session-id-change-with-fs-reset", async () => {
        const extension1 = await kit.createExtension();
        const sessionId1 = extension1.sessionId;

        // if we reset the storage id should reset
        Memento.resetValues();
        const extension2 = await kit.createExtension();
        expect(extension2.sessionId).not.toBe(sessionId1);
    });

    test("update-status-trace", async () => {
        const extension1 = await kit.createExtension();
        extension1.updateStatusTrace();
        const content = await extension1.provideTextDocumentContent(
            AugmentExtension.displayStatusUri
        );
        expect(content.length).toBeGreaterThan(1000);
    });

    test("test-model-info", async () => {
        // model, language and feature flag info should match what we send in the initial
        // get model call.
        const extension = await kit.createExtension();
        expect(extension.modelInfo!.suggestedPrefixCharCount).toBe(mockDefaultModelPrefixLength);
        expect(extension.modelInfo!.suggestedSuffixCharCount).toBe(mockDefaultModelSuffixLength);
        // Check if the language list contains the right languages.
        expect(extension.languages.map((x) => x.name).sort()).toStrictEqual(
            mockGetModelsResult.languages.map((x) => x.name).sort()
        );
        expect(extension.featureFlagManager.currentFlags).toStrictEqual(defaultFeatureFlags);
    });

    test("config-sync-on-flag-change", async () => {
        const mockSetContext = jest.fn();
        vscode.commands.registerCommand("setContext", mockSetContext);
        const context = new ExtensionContext();
        const extension = await kit.createExtension(undefined, undefined, { context });
        expect(extension.featureFlagManager.currentFlags.vscodeNextEditMinVersion).toBe("");
        _exportedForTesting.setupContextKeySync(extension, kit.configListener, context);
        expect(
            mockSetContext.mock.calls.find((x) => x[0] === "vscode-augment.enableNextEdit")
        ).toBeDefined();
        mockSetContext.mockReset();
        extension.featureFlagManager.update({
            ...extension.featureFlagManager.currentFlags,
            vscodeNextEditMinVersion: "0.0.0",
        });
        expect(extension.featureFlagManager.currentFlags.vscodeNextEditMinVersion).toBe("0.0.0");
        expect(
            mockSetContext.mock.calls.find((x) => x[0] === "vscode-augment.enableNextEdit")
        ).toBeDefined();
    });
});

describe("extension.getRecencyInfo", () => {
    let kit: ExtensionTestKit;
    let apiServer: MockAPIServer;
    let extension: AugmentExtension;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        kit = new ExtensionTestKit();
        apiServer = new MockAPIServer();
        extension = await kit.createExtension(undefined, apiServer);
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    function enableTabSwitch() {
        const enabledTabSwitch = kit.copyConfig(kit.config);
        enabledTabSwitch.advanced.recencySignalManager.collectTabSwitchEvents = true;
        mockWorkspaceConfigChange(enabledTabSwitch);
    }

    test("empty recency info state", async () => {
        const recencyInfo = extension.getRecencyInfo();
        expect(recencyInfo.tab_switch_events).toBeUndefined();
    });

    test("recency info with tab switch events", async () => {
        enableTabSwitch();
        await kit.awaitStarted(extension);
        const uri = kit.fileUris[0];
        const document = openTextDocument(uri);

        window.activeTextEditorChanged.fire(new TextEditor(document));

        const recencyInfo = extension.getRecencyInfo();
        expect(recencyInfo.tab_switch_events).toEqual([
            {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                file_blob_name: "0db2c452a2f60745211dcca26aa4f5fba6daf8bd172be61e33b17ba834141d9f",
                path: "first_file.py",
            },
        ]);
    });
});

describe("extension lifecycle", () => {
    let kit: ExtensionTestKit;

    beforeEach(async () => {
        jest.useFakeTimers();
        kit = new ExtensionTestKit();
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    test("enable in progress", async () => {
        const apiServer = new MockAPIServer();

        let spy = jest.spyOn(apiServer, "getModelConfig");
        const extension = await kit.createExtension(undefined, apiServer);

        expect(extension.enableInProgress).toBe(false);

        const origFunc = apiServer.getModelConfig;
        spy.mockImplementation(async (): Promise<ModelConfig> => {
            expect(extension.enableInProgress).toBe(true);
            return origFunc();
        });

        await extension.enable();
        expect(extension.enableInProgress).toBe(false);
    });

    test("reload extension", async () => {
        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig(
                getExampleUserConfig({
                    completions: {
                        enableAutomaticCompletions: false,
                    },
                })
            )
        );

        const mockStatusBar: vscode.StatusBarItem = newMockStatusBar();
        window.createStatusBarItem = jest.fn().mockReturnValue(mockStatusBar);

        const apiServer = new MockAPIServer();

        let spy = jest.spyOn(apiServer, "getModelConfig");

        const extension = await kit.createExtension(undefined, apiServer);

        expect(kit.config.completions.enableAutomaticCompletions).toBe(false);
        expect(extension.ready).toBe(true);
        expect(mockStatusBar.tooltip).toBe(noAutoCompletions.tooltip);

        const spyCallCount = spy.mock.calls.length;
        expect(spyCallCount).toBe(1);

        extension.disable();

        // Make the next call to getModelConfig throw an error
        spy = spy.mockImplementation(async (): Promise<ModelConfig> => {
            throw new Error("injected error");
        });
        const unusccessfulEnable = extension.enable();

        // Wait until the extension has requested the model config at least once.
        await advanceTimeUntilTrue(() => spy.mock.calls.length - spyCallCount >= 1);

        // The get model config will loop retrying so the status bar should be initializing.
        expect(mockStatusBar.tooltip).toBe(initializing.tooltip);

        // Disable and the extension and ensure the previous enable was cancelled.
        extension.disable();
        // getModelConfig is run in a loop with a backoff delay, so trigger the delay timeout
        jest.runOnlyPendingTimers();
        await unusccessfulEnable;

        // Check for the status bar tooltip
        expect(mockStatusBar.tooltip).toBe(initialState.tooltip);

        // Make the getModelConfig call succeed
        spy.mockRestore();
        await extension.enable();
        await kit.awaitStarted(extension);
        expect(mockStatusBar.tooltip).toBe(noAutoCompletions.tooltip);
    });
});

describe("extension main panel", () => {
    let kit: ExtensionTestKit;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();

        mockFSUtils.writeFileUtf8(
            "/path/common-webviews/main-panel.html",
            "<html><head></head>example of webviews/dist/main-panel.html</html>",
            true
        );

        kit = new ExtensionTestKit();
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    test("should change app after enable", async () => {
        const changeApp = new vscode.EventEmitter<MainPanelApp>();
        const panelProvider = new MainPanelWebviewProvider(
            vscode.Uri.parse("vscode://example/path")
        );
        const extension = await kit.createExtension(undefined, undefined, {
            mainPanelProvider: panelProvider,
            changeMainPanelApp: changeApp,
        });
        await extension.enable();

        const webviewView = new WebviewView("main-panel", new Webview({}, "", ""), true);
        await panelProvider.resolveWebviewView(
            webviewView,
            {
                state: undefined,
            },
            new CancellationToken()
        );
        expect(webviewView.title).toBe("");

        changeApp.fire(MainPanelApp.workspaceContext);
        expect(webviewView.title).toBe("Workspace Context");

        // We change sign in app via auth session events, so this should be a
        // noop
        changeApp.fire(MainPanelApp.signIn);
        expect(webviewView.title).toBe("Workspace Context");

        // Other places should be able to change the app
        const context = new ExtensionContext();
        const config = new AugmentConfigListener();
        const apiServer = new MockAPIServer();
        const auth = new AuthSessionStore(context, config);
        const globalState = new AugmentGlobalState(context);
        const actionsModel = new ActionsModel(globalState);
        panelProvider.changeApp(
            new SignInApp(
                apiServer,
                config,
                new OAuthFlow(
                    context,
                    config,
                    apiServer,
                    auth,
                    new OnboardingSessionEventReporter(apiServer)
                ),
                actionsModel
            )
        );
        expect(webviewView.title).toBe("");

        changeApp.fire(MainPanelApp.chat);
        expect(webviewView.title).toBe("");
    });
});
