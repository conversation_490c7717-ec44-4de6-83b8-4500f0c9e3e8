import * as lodash from "lodash";
import * as vscode from "vscode";

import { AugmentConfigListener } from "./augment-config-listener";
import { FocusAugmentPanel } from "./commands/focus-augment-panel";
import {
    SlashDocumentCommand,
    SlashExplainCommand,
    SlashFixCommand,
    SlashTestCommand,
} from "./commands/slash-command";
import { FeatureFlagManager } from "./feature-flags";
import {
    buildGhostTextDecoration,
    buildKeybindingDecoration,
    buildRightSpacerDecoration,
} from "./utils/decoration-types";
import { DisposableService } from "./utils/disposable-service";
import { isExtensionVersionGte } from "./utils/environment";
import { KeybindingWatcher } from "./utils/keybindings";
import { getKeybindingDecor } from "./utils/keyboard/keyboard-decorations";
import { endOfLineRange } from "./utils/line-utils";

/** The number of lines within the cursor to show the decoration */
const DECORATION_CURSOR_RANGE = 2;
/** How long in ms to debounce the decoration rendering */
const DECORATION_DEBOUNCE_MS = 200;
/** The maximum number of characters in a line */
const DECORATION_MAX_CHAR = 80;
/** The smallest margin to use for the decoration */
const DECORATION_MIN_MARGIN = 3;
/** The length of a keybind icon and its margin */
const DECORATION_KEY_BIND_LENGTH = 3;
/** The length of the gap between hints */
const DECORATION_GAP_LENGTH = 4;

interface DecorationHint {
    /** The text to be shown in the decoration. */
    text: string;
    /** The ID for the keybinding to show. */
    keyBindingId: string;
}

/**
 * This class is responsible for showing hints to the user to open the current
 * selection in the chat panel by displaying decorations.
 */
export class OpenChatHintManager extends DisposableService {
    private _decorations: Decorations;
    private _previousEditor: vscode.TextEditor | undefined;
    private _hoverPanel: HoverPanel;

    /** Displays the open chat hint decoration (debounced 200 ms) */
    private showHintDecoration: lodash.DebouncedFunc<(line: number) => void>;

    constructor(
        private readonly _configListener: AugmentConfigListener,
        private readonly _context: vscode.ExtensionContext,
        private readonly _keybindingWatcher: KeybindingWatcher,
        private readonly _featureFlagManager: FeatureFlagManager,
        private readonly _hints: DecorationHint[]
    ) {
        super();

        this._decorations = new Decorations(this._context, this._keybindingWatcher, this._hints);
        this._hoverPanel = new HoverPanel();

        this.showHintDecoration = lodash.debounce((line: number) => {
            this._decorations.decorate(line);
        }, DECORATION_DEBOUNCE_MS);
    }

    public enable() {
        this.dispose();

        if (
            this._configListener.config.enableDebugFeatures ||
            isExtensionVersionGte(
                this._featureFlagManager.currentFlags.vscodeChatHintDecorationMinVersion
            )
        ) {
            this._hoverPanel.enable();

            this.addDisposable(
                // when the active editor changes, move the decoration to the
                // new active editor
                vscode.window.onDidChangeActiveTextEditor((editor) => {
                    this._decorations.clearDecorations(this._previousEditor);
                    this._previousEditor = editor;
                    if (editor && OpenChatHintManager.canDecorateEditor(editor)) {
                        this.enableMultilineSelectionHints(editor.selection);
                    }
                })
            );
            this.addDisposable(
                vscode.window.onDidChangeTextEditorSelection((event) => {
                    if (!OpenChatHintManager.canDecorateEditor(event.textEditor)) {
                        return;
                    }
                    const selection = event.selections[0]; // primary selection
                    this.enableMultilineSelectionHints(selection);
                })
            );
        }
    }

    public dispose() {
        super.dispose();
        this._decorations.clearDecorations();
        this._hoverPanel.dispose();
    }

    /**
     * Decorate and enable the hover panel for the selection if it is not a
     * single line selection. Otherwise, clear all existing decorations and
     * panels.
     *
     * @param selection the selection to decorate
     */
    private enableMultilineSelectionHints(selection: vscode.Selection) {
        // clear the decoration every time the selection changes
        // and let the debounced function handle when the decoration finally renders
        this._decorations.clearDecorations();
        if (!selection.isSingleLine) {
            this.showHintDecoration(this.getOffsetLine(selection)); // debounced
            this._hoverPanel.setRange(selection.start, selection.end);
        } else {
            this._hoverPanel.setRange();
        }
    }

    /**
     * @returns true if the scheme of the document in the editor is 'file' or 'untitled'
     */
    static canDecorateEditor(editor: vscode.TextEditor) {
        return editor.document.uri.scheme === "file" || editor.document.uri.scheme === "untitled";
    }

    /**
     * Instead of showing the decoration on the active selection, show the
     * decoration on the line that has the shortest length in order to avoid
     * conflicts with other decorations.
     *
     * If the shortest line is the active line, then the second shortest line is
     * used in order to avoid conflicting with other existing decorations.
     **/
    private getOffsetLine(selection: vscode.Selection) {
        const editor = vscode.window.activeTextEditor;
        const selectionUp = selection.active.line < selection.anchor.line;
        if (!editor) {
            if (selectionUp) {
                return selection.active.line - 1;
            } else {
                return selection.active.line + 1;
            }
        }
        const direction = selectionUp ? 1 : -1;
        // the bounds of the selection
        const bounds = [
            Math.min(selection.anchor.line, selection.active.line),
            Math.max(selection.anchor.line, selection.active.line),
        ];
        // the range of lines to consider:
        // - within 10 lines of the cursor line in the direction of the anchor
        // - up to the cursor line
        const start = selection.active.line + direction * DECORATION_CURSOR_RANGE;
        const end = selection.active.line;
        const range = [Math.min(start, end), Math.max(start, end)];
        // restrict the range to the bounds of the selection
        const lines = lodash.range(
            Math.max(range[0], bounds[0]),
            Math.min(range[1], bounds[1]) + 1
        );
        lines.sort((a, b) => {
            const result =
                editor.document.lineAt(a).text.length - editor.document.lineAt(b).text.length;
            if (result === 0) {
                // if the length of the lines are equal, sort by how close it is
                // to the cursor
                return selectionUp ? a - b : b - a;
            }
            return result;
        });
        return lines[0] === selection.active.line ? lines[1] : lines[0];
    }
}

interface KeyHintDecorationType {
    /** Array of decoration types for the keybinding images. */
    keyBindingDecorationTypes: vscode.TextEditorDecorationType[];
    /** Decoration type for the text. */
    textDecorationType: vscode.TextEditorDecorationType;
    /**
     * Decoration type for the gap between multiple KeyHintDecorationTypes. If
     * there are no more key hints after this one, this should be undefined.
     */
    gapDecorationType?: vscode.TextEditorDecorationType;
}

class Decorations {
    private _rightSpacerDecorationType: vscode.TextEditorDecorationType;
    private _keyHintDecorationTypes: KeyHintDecorationType[];
    private _decorationLength: number = 0;

    constructor(
        private readonly _context: vscode.ExtensionContext,
        private readonly _keybindingWatcher: KeybindingWatcher,
        private readonly _hints: DecorationHint[]
    ) {
        this._rightSpacerDecorationType = buildRightSpacerDecoration();
        this._keyHintDecorationTypes = [];
        this._hints.forEach((hint, index) => {
            const keyBindings = getKeybindingDecor(
                hint.keyBindingId,
                this._keybindingWatcher,
                this._context
            );
            this._decorationLength += keyBindings.length * DECORATION_KEY_BIND_LENGTH;
            const decoration: KeyHintDecorationType = {
                keyBindingDecorationTypes: buildKeybindingDecoration(),
                textDecorationType: buildGhostTextDecoration(hint.text),
            };
            this._decorationLength += hint.text.length;
            if (index < this._hints.length - 1) {
                decoration.gapDecorationType = buildGhostTextDecoration(" | ");
                this._decorationLength += DECORATION_GAP_LENGTH;
            }
            this._keyHintDecorationTypes.push(decoration);
        });
    }

    public decorate(line: number) {
        const editor = vscode.window.activeTextEditor;
        if (!editor || !OpenChatHintManager.canDecorateEditor(editor)) {
            return;
        }
        if (this._keyHintDecorationTypes.length === 0) {
            return;
        }

        const endOfLine = endOfLineRange(editor, line);
        const lineLength = editor.document.lineAt(line).text.length;
        const margin = Math.max(
            DECORATION_MAX_CHAR - lineLength - this._decorationLength,
            DECORATION_MIN_MARGIN
        );
        editor.setDecorations(this._rightSpacerDecorationType, [
            {
                range: endOfLine,
                renderOptions: {
                    after: {
                        margin: `0 0 0 ${margin}ch`,
                    },
                },
            },
        ]);
        this._keyHintDecorationTypes.forEach((keyHint, hintIndex) => {
            const keyBindings = getKeybindingDecor(
                this._hints[hintIndex].keyBindingId,
                this._keybindingWatcher,
                this._context
            );
            keyHint.keyBindingDecorationTypes.forEach((decorationType, keyIndex) => {
                if (keyBindings[keyIndex]) {
                    editor.setDecorations(decorationType, [
                        {
                            range: endOfLine,
                            renderOptions: keyBindings[keyIndex],
                        },
                    ]);
                }
            });
            editor.setDecorations(keyHint.textDecorationType, [
                {
                    range: endOfLine,
                },
            ]);
            if (keyHint.gapDecorationType) {
                editor.setDecorations(keyHint.gapDecorationType, [
                    {
                        range: endOfLine,
                        renderOptions: {
                            after: {
                                margin: "0 0.65em 0 0.5em",
                            },
                        },
                    },
                ]);
            }
        });
    }

    public clearDecorations(editor?: vscode.TextEditor) {
        const activeEditor = editor ?? vscode.window.activeTextEditor;
        if (!activeEditor) {
            return;
        }
        activeEditor.setDecorations(this._rightSpacerDecorationType, []);
        this._keyHintDecorationTypes.forEach((type) => {
            type.keyBindingDecorationTypes.forEach((decorationType) => {
                activeEditor.setDecorations(decorationType, []);
            });
            activeEditor.setDecorations(type.textDecorationType, []);
            if (type.gapDecorationType) {
                activeEditor.setDecorations(type.gapDecorationType, []);
            }
        });
    }
}

class HoverPanel extends DisposableService implements vscode.HoverProvider {
    private _provider: vscode.Disposable | undefined;

    private _range: vscode.Range | undefined;

    public enable() {
        // register the currently active editor
        this.registerHoverProvider(vscode.window.activeTextEditor);
        // when the editor changes, register the new editor
        this.addDisposable(
            vscode.window.onDidChangeActiveTextEditor((editor) => {
                this.registerHoverProvider(editor);
            })
        );
    }

    /**
     * Set the range where the hover panel should be shown. If args are
     * undefined, the hover panel will be disabled.
     */
    public setRange(start?: vscode.Position, end?: vscode.Position) {
        if (start && end) {
            this._range = new vscode.Range(start, end);
            return;
        }
        this._range = undefined;
    }

    public provideHover(
        _document: vscode.TextDocument,
        position: vscode.Position,
        _token: vscode.CancellationToken
    ) {
        if (this._range && this._range.contains(position)) {
            const rawStr = `
$(augment-icon-simple)
&nbsp;
<a href="command:${FocusAugmentPanel.commandID}" title="Open Augment Chat">
    Open in Chat
</a> |
<a href="command:${SlashFixCommand.commandID}" title="Fix with Augment Chat">
    Fix
</a> |
<a href="command:${SlashExplainCommand.commandID}" title="Explain with Augment Chat">
    Explain
</a> | <a href="command:${SlashTestCommand.commandID}" title="Write a test with Augment Chat">
    Write a Test
</a> | <a href="command:${SlashDocumentCommand.commandID}" title="Document with Augment Chat">
    Document
</a>
            `;
            const str = new vscode.MarkdownString(rawStr);
            str.isTrusted = true;
            str.supportHtml = true;
            str.supportThemeIcons = true;
            return new vscode.Hover(str, this._range);
        }
    }

    /**
     * Add a hover provider for the given editor.
     *
     * @param editor the editor to register the hover provider for
     */
    private registerHoverProvider(editor: vscode.TextEditor | undefined) {
        this._provider?.dispose();
        this._provider = undefined;
        if (!editor || !OpenChatHintManager.canDecorateEditor(editor)) {
            return;
        }
        this._provider = vscode.languages.registerHoverProvider(
            { pattern: editor.document.uri.fsPath },
            this
        );
    }
}
