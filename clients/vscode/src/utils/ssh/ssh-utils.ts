import * as vscode from "vscode";

/**
 * Sets the remote platform for a specific SSH host to avoid OS detection prompts
 *
 * @param remoteAgentId The ID of the remote agent to set platform for
 * @param platform The platform to set (linux, macOS, or windows)
 */
export async function setRemotePlatform(
    remoteAgentId: string,
    platform: string = "linux"
): Promise<void> {
    const sshConfig = vscode.workspace.getConfiguration("remote.SSH");
    const remotePlatform = sshConfig.get<Record<string, string>>("remotePlatform") || {};

    // Add or update the platform for this host
    remotePlatform[remoteAgentId] = platform;

    // Update the configuration
    await sshConfig.update("remotePlatform", remotePlatform, vscode.ConfigurationTarget.Global);
}

/**
 * Removes the remote platform for a specific SSH host
 *
 * @param remoteAgentId The ID of the remote agent to remove platform for
 */
export async function removeRemotePlatform(remoteAgentId: string): Promise<void> {
    const sshConfig = vscode.workspace.getConfiguration("remote.SSH");
    const remotePlatform = sshConfig.get<Record<string, string>>("remotePlatform") || {};

    // Remove the platform for this host
    delete remotePlatform[remoteAgentId];

    // Update the configuration
    await sshConfig.update("remotePlatform", remotePlatform, vscode.ConfigurationTarget.Global);
}

/**
 * The default connect timeout in seconds for SSH to remote agents
 */
const CONNECT_TIMEOUT_MS = 60_000;

/**
 * Creates a regex pattern to match a specific SSH host entry in a config file
 *
 * @param remoteAgentId The ID of the remote agent to match
 * @param exactMatch If true, matches only the host line; if false, matches the entire host block
 * @returns A RegExp object that matches the specified host
 */
export function createHostRegex(remoteAgentId: string, exactMatch: boolean = false): RegExp {
    if (exactMatch) {
        // Match only the Host line
        return new RegExp(`\\bHost\\s+${remoteAgentId}\\b`, "i");
    } else {
        // Match the entire host block (from Host line to the next Host line or end of file)
        return new RegExp(`\\bHost\\s+${remoteAgentId}\\b[\\s\\S]*?(?=\\bHost\\b|$)`, "i");
    }
}

/**
 * Gets the list of installed extensions
 *
 * @returns An array of extension IDs
 */
export function getInstalledExtensions(): string[] {
    const installedExtensions = vscode.extensions.all
        .filter(
            (extension) =>
                !extension.id.startsWith("ms-vscode") && !extension.id.startsWith("vscode")
        )
        .map((extension) => extension.id);
    return installedExtensions;
}

/**
 * Temporarily adds Augment to defaultExtensions, executes a callback, then restores the original settings
 *
 * @param callback Function to execute while the temporary settings are active
 * @returns The result of the callback function
 */
export async function withTemporarySSHSettings<T>(callback: () => Promise<T>): Promise<T> {
    const sshConfig = vscode.workspace.getConfiguration("remote.SSH");

    const originalDefaultExtensions = sshConfig.get<string[]>("defaultExtensions") || [];
    const allExtensions = getInstalledExtensions();

    // Increase the connect timeout to 120 seconds so we have time to install extensions
    const originalConnectTimeout = sshConfig.get<number>("connectTimeout") || 15;

    const newDefaultExtensions = allExtensions;

    try {
        await sshConfig.update(
            "defaultExtensions",
            newDefaultExtensions,
            vscode.ConfigurationTarget.Global
        );
        await sshConfig.update(
            "connectTimeout",
            CONNECT_TIMEOUT_MS,
            vscode.ConfigurationTarget.Global
        );

        return await callback();
    } finally {
        // Restore original settings
        await sshConfig.update(
            "defaultExtensions",
            originalDefaultExtensions,
            vscode.ConfigurationTarget.Global
        );
        await sshConfig.update(
            "connectTimeout",
            originalConnectTimeout,
            vscode.ConfigurationTarget.Global
        );
    }
}

/**
 * Creates a regex pattern to match an Include statement for a specific config file
 *
 * @param configPath The path to the config file to match
 * @returns A RegExp object that matches the Include statement
 */
export function createIncludeRegex(configPath: string): RegExp {
    // Escape any special regex characters in the path
    const escapedPath = configPath.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    // Match the Include line with the specific path
    return new RegExp(`^\\s*Include\\s+${escapedPath}\\s*$`, "im");
}

/**
 * The comment to add above the Include statement
 */
export const AUGMENT_SSH_CONFIG_COMMENT = "# Added by Augment for remote agent SSH connections";

/**
 * Opens a new VS Code window connected to the specified SSH host
 *
 * @param remoteAgentId The ID of the remote agent to connect to
 * @returns A promise that resolves when the command is executed
 */
export async function openSSHWindow(remoteAgentId: string): Promise<void> {
    // TODO (jw): make this configurable before we launch
    await vscode.commands.executeCommand(
        "vscode.openFolder",
        vscode.Uri.parse(`vscode-remote://ssh-remote+${remoteAgentId}/mnt/persist/workspace`),
        { forceNewWindow: true }
    );

    // Then remove it from the recently opened list
    // to prevent clutter in the user's list of recently opened folders
    await vscode.commands.executeCommand(
        "vscode.removeFromRecentlyOpened",
        vscode.Uri.parse(`vscode-remote://ssh-remote+${remoteAgentId}/mnt/persist/workspace`)
    );
}
