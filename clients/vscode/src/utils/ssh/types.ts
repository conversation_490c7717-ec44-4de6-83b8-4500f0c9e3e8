/**
 * Options for SSH key generation
 */
export interface SshKeygenOptions {
    /** Key type (e.g., 'rsa', 'ed25519') */
    type: string;

    /** Key size in bits (for RSA keys) */
    bits?: number;

    /** Comment to add to the key */
    comment?: string;

    /** Passphrase for the key (empty for no passphrase) */
    passphrase?: string;

    /** Filename for the key (without path) */
    filename?: string;
}

/**
 * Result of SSH key generation
 */
export interface SshKeygenResult {
    /** Path to the private key file */
    privateKeyPath: string;

    /** Path to the public key file */
    publicKeyPath: string;

    /** Content of the public key */
    publicKey: string;
}
