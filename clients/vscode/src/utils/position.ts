// Utilities to manipulate ranges and positions.
import * as vscode from "vscode";

/**
 * Expands a given range such that its start and end positions are on line boundaries.
 * @param range The range to be expanded.
 * @param document The document the range is part of.
 * @param includeEndLine If true, will include the newline character at the end
 * @param dropLastLineIfEmpty If range is multiline and last line is empty, we drop it
 * of the ending line (if there is one).
 */
export function expandToLineBoundaries(
    range: vscode.Range,
    document: vscode.TextDocument,
    includeEndLine = false,
    dropLastLineIfEmpty = true
): vscode.Range {
    const startPosition = new vscode.Position(range.start.line, 0);

    let endLine = range.end.line;
    // This effectively means that line is not selected,
    // but cursor is placed at the beginning of this line
    // We should not include this line into selection, then.
    if (dropLastLineIfEmpty && range.end.character === 0 && endLine > range.start.line) {
        endLine -= 1;
    }

    let endPosition = document.lineAt(endLine).range.end;
    if (includeEndLine && document.lineCount > range.end.line + 1) {
        endPosition = new vscode.Position(range.end.line + 1, 0);
    }
    return new vscode.Range(startPosition, endPosition);
}
