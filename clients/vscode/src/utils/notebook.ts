import * as vscode from "vscode";

// The separator we insert between cells in a notebook.
export const CELL_SEPARATOR = "\n\n";

// isNotebookUri returns true if the given uri is for a notebook.
export function isNotebookUri(uri: vscode.Uri): boolean {
    return uri.scheme === "vscode-notebook-cell";
}

// getActiveNotebookCodeTextAndPrefixLength returns the text of the active
// notebook, or undefined if there is no active notebook, and the length of its
// prefix (which is everything before the current cell), both ignoring text
// cells.
export function getActiveNotebookCodeTextAndPrefixLength(
    document: vscode.TextDocument
): [string | undefined, number] {
    const notebookDocument = vscode.window.activeNotebookEditor?.notebook;
    if (!notebookDocument) {
        return [undefined, 0];
    }
    if (!isCursorInCodeCell(document, notebookDocument)) {
        return [undefined, 0];
    }
    return [
        getNotebookCodeText(notebookDocument),
        getNotebookPrefixLengthForDocument(notebookDocument, document),
    ];
}

// getNotebookDocument returns the notebook document that contains the given
// document, or undefined if the document is not in a notebook.
export function getNotebookDocument(
    document: vscode.TextDocument
): vscode.NotebookDocument | undefined {
    // As an optimization, avoid iterating through the workspace's notebook
    // documents if we are not in a notebook.
    if (!isNotebookUri(document.uri)) {
        return undefined;
    }
    for (const notebookDocument of vscode.workspace.notebookDocuments) {
        for (const cell of notebookDocument.getCells()) {
            if (cell.document === document) {
                return notebookDocument;
            }
        }
    }
    return undefined;
}

// getNotebookCodeText returns the text of the given notebook, ignoring markdown
// cells.
export function getNotebookCodeText(document: vscode.NotebookDocument): string {
    return getConcatenatedCodeCellText(document.getCells());
}

// isCursorInCodeCell returns true if the cursor is in a code cell.
function isCursorInCodeCell(
    document: vscode.TextDocument,
    notebookDocument: vscode.NotebookDocument
): boolean {
    const curCell = notebookDocument.getCells().find((cell) => cell.document === document);
    if (!curCell) {
        return false;
    }
    return curCell.kind === vscode.NotebookCellKind.Code;
}

// getNotebookPrefixLength returns the length of the prefix (everything before
// the current cell) of the given notebook.
export function getNotebookPrefixLengthForDocument(
    notebookDocument: vscode.NotebookDocument,
    document: vscode.TextDocument
): number {
    const cells = notebookDocument.getCells();
    const curCellIndex = cells.findIndex((cell) => cell.document === document);
    if (curCellIndex === -1) {
        // This can happen if this document is not in this notebook.
        return 0;
    }
    return getNotebookPrefixLengthForCellIndex(notebookDocument, curCellIndex, true);
}

// getNotebookPrefixLength returns the length of the prefix (all code cells
// before the current cell) of the given notebook.  If countSeparator is true,
// the length includes the separator between cells if the prefix is non-empty.
export function getNotebookPrefixLengthForCellIndex(
    notebookDocument: vscode.NotebookDocument,
    cellIndex: number,
    countSeparator: boolean
): number {
    const prefix = getConcatenatedCodeCellText(notebookDocument.getCells().slice(0, cellIndex));
    if (countSeparator && prefix) {
        return prefix.length + CELL_SEPARATOR.length;
    }
    return prefix.length;
}

// getConcatenatedCodeCellText returns the text of the given cells (ignoring any
// text cells given).
export function getConcatenatedCodeCellText(cells: readonly vscode.NotebookCell[]): string {
    const slicedCells = cells.filter((cell) => cell.kind === vscode.NotebookCellKind.Code);
    let result = slicedCells.map((cell) => cell.document.getText()).join(CELL_SEPARATOR);
    return result;
}

export function isNotebookDocument(
    document: vscode.TextDocument | vscode.NotebookDocument
): document is vscode.NotebookDocument {
    return (document as vscode.NotebookDocument).getCells !== undefined;
}
