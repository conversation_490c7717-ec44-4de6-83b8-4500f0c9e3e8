import { type AugmentLogger, getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { ClientMetricsReporter } from "./client-metrics-reporter";
import { WorkTimer } from "./work-timer";

type Options = {
    periodMs: number;
    debugThresholdMs: number;
    infoThresholdMs: number;
};
export class StallDetector extends DisposableService {
    private readonly _logger: AugmentLogger = getLogger("StallDetector");
    private _targetWakeup: number = 0;
    private _nextCheck: NodeJS.Timeout | undefined;

    constructor(
        private _reporter: ClientMetricsReporter,
        private _workTimer: WorkTimer,
        private _options: Options
    ) {
        super();
        // Enforce a maximum rate
        this._options.periodMs = Math.max(this._options.periodMs, 10);
        // Enforce minimum threshold, and sane ordering of debug/info levels
        // Neither threshold can be below 10ms, and debug threshold must not exceed info threshold
        this._options.infoThresholdMs = Math.max(this._options.infoThresholdMs, 10);
        this._options.debugThresholdMs = Math.min(
            Math.max(this._options.debugThresholdMs, 10),
            this._options.infoThresholdMs
        );

        this._schedule();
        this.addDisposable({ dispose: () => clearTimeout(this._nextCheck) });
    }

    private _schedule(): void {
        this._targetWakeup = Date.now() + this._options.periodMs;
        this._nextCheck = setTimeout(() => this._checkStall(), this._options.periodMs);
    }

    private _checkStall(): void {
        let now = Date.now();
        let delay = now - this._targetWakeup;
        if (delay > this._options.debugThresholdMs) {
            let msg = `Event loop delay: Timer(${this._options.periodMs} msec) ran ${delay} msec late.`;
            let level = delay > this._options.infoThresholdMs ? "info" : "debug";
            this._logger.log(level, msg);
            this._reporter.report({
                // eslint-disable-next-line @typescript-eslint/naming-convention
                client_metric: "event_loop_delay",
                value: delay,
            });
        }
        const recentWork = this._workTimer.popSlowEvents();
        if (recentWork.length > 0) {
            this._logger.info(`Recent work: ${JSON.stringify(recentWork)}`);
        }
        this._schedule();
    }
}
