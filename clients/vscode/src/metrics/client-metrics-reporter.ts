import { MetricsReporter } from "@augment-internal/sidecar-libs/src/metrics/metrics-reporter";

import { APIServer, ClientMetric } from "../augment-api";
import { WebviewClientMetricData } from "../webview-providers/webview-messages";

export class ClientMetricsReporter extends MetricsReporter<ClientMetric> {
    public static defaultMaxRecords = 10000;
    public static defaultBatchSize = 500;
    public static defaultUploadMsec = 10000;

    constructor(private _apiServer: APIServer) {
        super(
            "ClientMetricsReporter",
            ClientMetricsReporter.defaultMaxRecords,
            ClientMetricsReporter.defaultUploadMsec,
            ClientMetricsReporter.defaultBatchSize
        );
    }

    public reportWebviewClientMetric = (metric: WebviewClientMetricData): void => {
        this.report({
            // eslint-disable-next-line @typescript-eslint/naming-convention
            client_metric: `webview__${metric.webviewName}__${metric.client_metric}`,
            value: metric.value,
        });
    };

    protected performUpload(batch: ClientMetric[]): Promise<void> {
        return this._apiServer.clientMetrics(batch);
    }
}
