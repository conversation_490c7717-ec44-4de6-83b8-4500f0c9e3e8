// TODO: replace with a library
// eslint-disable-next-line
import _ from "lodash";

interface Record {
    [key: string]: any;
}

type SearchOptions = {
    limit?: number;
};

/**
 * A basic in-memory data store for managing records.
 *
 * 1. Assumes the presence of a unique primary key for each record.
 * 2. Performance is not a primary focus at this stage; `Object.values` may become a bottleneck and should be revisited.
 * 3. Results are returned directly without cloning. Consumers are expected not to mutate the objects.
 * 4. It is recommended to use flat objects instead of nested ones for easier management.
 *
 * This is expected to change. Please do not expend effort on this and adhere to the above guidelines.
 */
export class DataStore<T extends Record> {
    private store: { [key: string]: T };
    private primaryKey: keyof T; // primaryKey must be a key of T

    constructor(primaryKey: keyof T) {
        this.store = {};
        this.primaryKey = primaryKey;
    }

    // Add a new record
    add(record: T): void {
        const key = record[this.primaryKey];
        if (this.get(key)) {
            throw new Error(`Record with primary key ${key} already exists.`);
        }
        this.store[key] = Object.assign({}, record); // Create a copy to avoid mutation
    }

    // Update an existing record by primary key
    update(id: T[keyof T], updatedRecord: Partial<T>): void {
        this.store[id] = { ...this.store[id], ...updatedRecord };
    }

    // Delete a record by primary key
    delete(id: T[keyof T]): T | undefined {
        const record = this.store[id];
        delete this.store[id];
        return record;
    }

    deleteBy(fields: Partial<T>): void {
        const records = this.search(fields);
        for (const record of records) {
            this.delete(record[this.primaryKey]);
        }
    }

    // Get a record by primary key
    get(id: T[keyof T]): T | undefined {
        return this.store[id];
    }

    // Search records by fields (supports partial matching)
    search(fields: Partial<T>, { limit }: SearchOptions = {}): T[] {
        let count = 0;
        const result: T[] = [];

        for (const record of Object.values(this.store)) {
            if (_.every(fields, (value, key) => _.isEqual(record[key], value))) {
                result.push(record);
                count++;
                if (limit && count >= limit) {
                    break;
                }
            }
        }
        return result;
    }

    // Get all records (return a shallow copy to avoid mutation)
    getAll(): T[] {
        return Object.values(this.store);
    }

    // Clear all records
    clear(): void {
        this.store = {};
    }
}
