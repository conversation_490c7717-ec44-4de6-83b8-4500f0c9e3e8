import { DisposableInterval, DisposableService } from "$vscode/src/utils/disposable-service";

import { IQueue, IQueueProcessor } from "./interfaces";

type RetryConfig = {
    retryMs: number;
};

class RetryScheduler<T> extends DisposableService {
    private _retryQueue: T[] = [];
    private _retryInterval: DisposableInterval | undefined;

    constructor(
        private readonly _queue: IQueueProcessor & IQueue<T>,
        private readonly _config: RetryConfig
    ) {
        super();
        this._retryInterval = new DisposableInterval(() => this._retry(), this._config.retryMs);
        this.addDisposable(this._retryInterval);
    }

    retry(item: T) {
        this._retryQueue.push(item);
    }

    retryAll(items: T[]) {
        this._retryQueue.push(...items);
    }

    private _retry() {
        for (const item of this._retryQueue) {
            this._queue.enqueue(item);
        }
        this._retryQueue = [];
        this._queue.startProcess();
    }
}

export { RetryScheduler };
