import { getLogger } from "../../logging";
import { DataStore } from "./in-memory-store";

export enum BlobStatusType {
    indexed = "indexed",
    uploaded = "uploaded",
    embargoed = "embargoed",
}

export type BlobStatus = {
    pathName: string;
    blobName: string;
    status: BlobStatusType;

    // timestamps are in epoch millis
    uploadRequestedAt?: number; // . populated when we request an upload
    indexedAt?: number; // populated when we get an indexed event
};

/**
 * This implementation is optimized for correctness over performance.
 * We can optimize performance later if needed.
 */
export class BlobStatusStore {
    private _logger = getLogger("BlobStatusStore");
    private _store = new DataStore<BlobStatus>("blobName");

    // for backward compatibility we keep track of embargoed paths
    // however, we might want to revisit this in the future.
    // Augment only embargoes. We never unembargo.
    private _embargoedPaths = new Set<string>();

    getIndexedBlobName(pathName: string): string | undefined {
        if (this._embargoedPaths.has(pathName)) {
            this._logger.debug(`Path [${pathName}] is embargoed`);
            return undefined;
        }
        const records = this._store.search({ pathName, status: BlobStatusType.indexed });
        if (records.length === 0) {
            this._logger.verbose(`No indexed blob found for ${pathName}`);
            return undefined;
        }
        if (records.length > 1) {
            this._logger.warn(`Multiple indexed blobs found for ${pathName}`);

            // return the most recent one
            records.sort((a, b) => (a.indexedAt ?? 0) - (b.indexedAt ?? 0));
            return records[records.length - 1].blobName;
        }
        return records[0].blobName;
    }

    isTrackingBlob(blobName: string): boolean {
        return this._store.get(blobName) !== undefined;
    }

    removePath(pathName: string): void {
        this._store.deleteBy({ pathName });
        this._embargoedPaths.delete(pathName);
    }

    isTrackingPath(pathName: string): boolean {
        return (
            this._embargoedPaths.has(pathName) ||
            this._store.search({ pathName }, { limit: 1 }).length > 0
        );
    }

    clear(): void {
        this._store.clear();
        this._embargoedPaths.clear();
    }

    getTrackedPaths(): Array<string> {
        return [
            ...new Set(
                ...this._store.getAll().map((record) => record.pathName),
                ...this._embargoedPaths
            ),
        ];
    }

    addIndexedBlob(blobName: string, pathName: string): void {
        this.addUploadedBlob(blobName, pathName);
        this.updateBlobIndexed(blobName);
    }

    addUploadedBlob(blobName: string, pathName: string): void {
        this._store.add({
            blobName,
            pathName,
            status: BlobStatusType.uploaded,
            uploadRequestedAt: Date.now(),
        });
    }

    getLastBlobNameForPath(pathName: string): string | undefined {
        const latest = this._store.search({ pathName }).reduce((latest, item) => {
            if (latest === undefined) {
                return item;
            }
            return (latest.uploadRequestedAt ?? 0) > (item.uploadRequestedAt ?? 0) ? latest : item;
        });
        return latest?.blobName;
    }

    /**
     * @description Updates the indexed blob name for the path.
     * Removes all previously indexed blobs for the path.
     * Guarantees that indexed blob is the latest to be uploaded.
     *
     * @param blobName - blob name that was indexed.
     * @returns void
     */
    updateBlobIndexed(blobName: string) {
        const now = Date.now();
        const record = this._store.get(blobName);

        if (!record) {
            // this should not happen
            this._logger.debug(`[ERROR] Failed to find record for ${blobName}`);
            return;
        }

        if (record.status === BlobStatusType.indexed) {
            this._logger.debug(`[WARN] Blob ${blobName} is already indexed`);
            return;
        }

        const indexedRecords = this._store.search({
            pathName: record.pathName,
            status: BlobStatusType.indexed,
        });

        if (indexedRecords.length === 0) {
            record.status = BlobStatusType.indexed;
            record.indexedAt = now;
            return;
        }

        const lastToUpload = indexedRecords.reduce((a, b) =>
            (a.uploadRequestedAt ?? 0) > (b.uploadRequestedAt ?? 0) ? a : b
        );

        if ((lastToUpload.uploadRequestedAt ?? 0) > (record.uploadRequestedAt ?? 0)) {
            this._logger.info(
                `Blob ${blobName} is indexed but there is a newer blob to upload for ${record.pathName}`
            );
            this._store.delete(blobName);
            return;
        }

        // remove all other indexed records
        for (const indexedRecord of indexedRecords) {
            this._store.delete(indexedRecord.blobName);
        }

        record.status = BlobStatusType.indexed;
        record.indexedAt = now;
    }

    embargoPath(pathName: string): void {
        if (this._embargoedPaths.has(pathName)) {
            this._logger.debug(`Path [${pathName}] is already embargoed`); // this should not happen.
            return;
        }
        this._logger.debug(`Embargoing path [${pathName}]`);
        this._store.deleteBy({ pathName });
        this._embargoedPaths.add(pathName);
    }

    isEmbargoed(pathName: string): boolean {
        return this._embargoedPaths.has(pathName);
    }

    // A blob name might need update if client and server calculate blobName differently.
    // This should never happen but it is possible.
    updateBlobName(expectedBlobName: string, actualBlobName: string): void {
        const record = this._store.get(expectedBlobName);
        if (!record) {
            this._logger.debug(`[ERROR] Failed to find record for ${expectedBlobName}`);
            return;
        }
        this._store.delete(expectedBlobName);
        record.blobName = actualBlobName;
        this._store.add(record);
    }

    getAllPathToIndexedBlob(): Map<string, string> {
        return this._store.search({ status: BlobStatusType.indexed }).reduce((map, record) => {
            map.set(record.pathName, record.blobName);
            return map;
        }, new Map<string, string>());
    }
}
