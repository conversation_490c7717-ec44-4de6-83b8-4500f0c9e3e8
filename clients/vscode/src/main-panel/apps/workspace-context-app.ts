import { WorkTimer } from "$vscode/src/metrics/work-timer";

import { Webview } from "vscode";

import { FeatureFlagManager } from "../../feature-flags";
import { DisposableService } from "../../utils/disposable-service";
import { MainPanelApp } from "../../webview-providers/webview-messages";
import { WorkspaceManager } from "../../workspace/workspace-manager";
import { WorkspaceUIModel } from "../../workspace/workspace-ui-model";
import { MainPanelAppController } from "../main-panel-app-controller";

export class WorkspaceContextApp extends DisposableService implements MainPanelAppController {
    private _workspaceUiModel: WorkspaceUIModel | null = null;

    constructor(
        private readonly _workspaceManager: WorkspaceManager,
        private readonly _featureFlagManager: FeatureFlagManager,
        private readonly _workTimer: WorkTimer
    ) {
        super();
    }

    appType(): MainPanelApp {
        return MainPanelApp.workspaceContext;
    }

    title(): string {
        return "Workspace Context";
    }

    register(webview: Webview): void {
        this._workspaceUiModel?.dispose();

        if (this._featureFlagManager.currentFlags.enableWorkspaceManagerUi) {
            this._workspaceUiModel = new WorkspaceUIModel(
                this._workspaceManager,
                webview,
                this._featureFlagManager,
                this._workTimer
            );
            this.addDisposable(this._workspaceUiModel);
        }
    }
}
