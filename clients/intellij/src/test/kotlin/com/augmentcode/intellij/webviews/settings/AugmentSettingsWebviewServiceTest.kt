package com.augmentcode.intellij.webviews.settings

import com.augmentcode.intellij.settings.AugmentSettingsWebviewEditorVirtualFile.Companion.SETTINGS_VIRTUAL_FILE_NAME
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.webviews.RPCAdapter
import com.augmentcode.intellij.webviews.parseJsonToProto
import com.augmentcode.rpc.NavigateToSettingsSectionRequest
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.util.Disposer
import com.intellij.testFramework.PlatformTestUtil
import io.mockk.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentSettingsWebviewServiceTest : AugmentBasePlatformTestCase() {
  /**
   * Test that navigateToSection calls webview.postMessage with the section name in the payload
   */

  @Test
  fun testNavigateToSectionCallsPostMessage() {
    // Get the settings service
    val settingsService = AugmentSettingsWebviewService.getInstance(project)
    settingsService.openSettingsWebview()
    // Wait for all UI events to complete
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Verify that the settings file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Settings virtual file should be opened in editor",
      openFiles.any { it.name == SETTINGS_VIRTUAL_FILE_NAME },
    )

    val rpcMock = mockk<RPCAdapter>(relaxed = true)

    val messagingService = SettingsMessagingService.getInstance(project)
    messagingService.registerRPCAdapter(rpcMock)
    Disposer.register(testRootDisposable) {
      messagingService.unregisterRPCAdapter(rpcMock)
    }

    // Now navigate to a specific section by calling the method directly
    val sectionToNavigate = "tools"
    messagingService.navigateToSection(sectionToNavigate)

    val messageSlot = slot<String>()
    waitForAssertion({
      coVerify(exactly = 1) {
        rpcMock.postMessage(capture(messageSlot))
      }
    })

    // Verify the message contains the section name
    val capturedMessage = messageSlot.captured
    val parsedMessage = parseJsonToProto(capturedMessage)
    val navigationRequest = parsedMessage.unpack(NavigateToSettingsSectionRequest::class.java)
    assertEquals(
      "Message should contain the section name",
      sectionToNavigate,
      navigationRequest.data,
    )
  }

  /**
   * Test that the settings can be opened with a specific section
   */
  @Test
  fun testOpenSettingsCreatesFileWithSection() {
    // Get the settings service
    val settingsService = AugmentSettingsWebviewService.getInstance(project)
    val sectionToOpen = "user-guidelines"

    // Open settings with a specific section
    settingsService.openSettingsWebview(sectionToOpen)
    // Wait for all UI events to complete
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Verify that the settings file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Settings virtual file should be opened in editor",
      openFiles.any { it.name == SETTINGS_VIRTUAL_FILE_NAME },
    )

    // Verify that the settings file is created with the correct section
    val section = SettingsMessagingService.getInstance(project).getNavigateToSectionOnLoad()

    // Verify the section is set correctly
    assertEquals("Settings file should have the correct section", sectionToOpen, section)
  }
}
