package com.augmentcode.intellij.completion

import com.augmentcode.api.CompletionItem
import com.augmentcode.api.CompletionRequest
import com.augmentcode.api.CompletionResult
import com.augmentcode.intellij.api.AugmentHttpClient.Companion.createGson
import com.augmentcode.intellij.auth.AugmentCredentials
import com.augmentcode.intellij.index.AugmentBlobState
import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.status.StateChangeListener
import com.augmentcode.intellij.status.StateDefinition
import com.augmentcode.intellij.status.StateDefinitions
import com.augmentcode.intellij.status.StateManager
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.HttpUtil.PREFIX_CHAR_COUNT
import com.augmentcode.intellij.testutils.HttpUtil.SUFFIX_CHAR_COUNT
import com.augmentcode.intellij.testutils.mockOAuthState
import com.augmentcode.intellij.testutils.waitForAssertion
import com.google.gson.GsonBuilder
import com.intellij.codeInsight.inline.completion.InlineCompletionEvent
import com.intellij.codeInsight.inline.completion.InlineCompletionHandler
import com.intellij.codeInsight.inline.completion.testInlineCompletion
import com.intellij.openapi.vfs.readText
import com.intellij.testFramework.TestDataPath
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import kotlinx.coroutines.*
import kotlinx.coroutines.runBlocking
import org.junit.Rule
import org.junit.Test
import org.junit.rules.Timeout
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class AugmentCompletionSuggestionTest : AugmentBasePlatformTestCase() {
  @get:Rule
  val globalTimeout: Timeout = Timeout.seconds(10)

  override fun getTestDataPath() = "src/test/testData/suggestions"

  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  var mockEngine: MockEngine =
    MockEngine { _ ->
      respond(
        content = "Unexpected request",
        status = HttpStatusCode.InternalServerError,
      )
    }

  override fun setUp() {
    super.setUp()
    mockOAuthState(
      AugmentCredentials("access-token", "http://test-server"),
      testRootDisposable,
    )
  }

  private fun doTest(testFile: String) {
    myFixture.configureByFile(testFile)
    val currentCaret = myFixture.editor.caretModel.currentCaret

    var blobs: Collection<AugmentBlobState>? = null
    waitForAssertion({
      blobs = AugmentBlobStateReader.allUnfilteredIndexes(project)
      assertNotNull(blobs)
    })

    @Suppress("DEPRECATION")
    val completionEvent = InlineCompletionEvent.DirectCall(myFixture.editor, currentCaret)
    val completionRequest = completionEvent.toRequest()
    assertNotNull(completionRequest)
    val actualRequest =
      runBlocking {
        AugmentRemoteSyncingManager.getInstance(project).findUnsynced(blobs!!)
        AugmentCompletionSuggestion(completionRequest!!).buildAugmentCompletionRequest()
      }
    assertNotNull(actualRequest)

    val expectedRequestFileName = "${testFile.substringBeforeLast('.')}.json"
    val expectedRequestFile = myFixture.copyFileToProject(expectedRequestFileName)
    assertNotNull(expectedRequestFile)
    val gson = GsonBuilder()
    gson.setPrettyPrinting()
    assertEquals(
      expectedRequestFile.readText().trim(),
      gson.create().toJson(actualRequest),
    )
  }

  @Test
  fun testEmptyFile() {
    doTest("emptyFile.txt")
  }

  @Test
  fun testEofEmpty() {
    doTest("eofEmpty.txt")
  }

  @Test
  fun testEofEnd() {
    doTest("eofEnd.txt")
  }

  @Test
  fun testEofMiddle() {
    doTest("eofMiddle.txt")
  }

  @Test
  fun testMarkdown() {
    doTest("readme.md")
  }

  @Test
  fun testSequenceIdIncrementsAcrossRequests() {
    myFixture.configureByText("test.txt", "hello <caret>")

    var blobs: Collection<AugmentBlobState>? = null
    waitForAssertion({
      blobs = AugmentBlobStateReader.allUnfilteredIndexes(project)
      assertNotNull(blobs)
    })

    @Suppress("DEPRECATION")
    val completionEvent = InlineCompletionEvent.DirectCall(myFixture.editor, myFixture.editor.caretModel.currentCaret)
    val completionRequest = completionEvent.toRequest()
    assertNotNull(completionRequest)

    val firstRequest =
      runBlocking {
        AugmentRemoteSyncingManager.getInstance(project).findUnsynced(blobs!!)
        AugmentCompletionSuggestion(completionRequest!!).buildAugmentCompletionRequest()
      }

    val secondRequest =
      runBlocking {
        AugmentCompletionSuggestion(completionRequest!!).buildAugmentCompletionRequest()
      }

    assertNotNull(firstRequest)
    assertNotNull(secondRequest)
    assertNotNull(firstRequest!!.sequenceId)
    assertNotNull(secondRequest!!.sequenceId)
    assertTrue(
      "Second sequence ID should be greater than first",
      secondRequest.sequenceId > firstRequest.sequenceId,
    )
  }
}

@Suppress("UnstableApiUsage")
@RunWith(JUnit4::class)
class AugmentCompletionSuggestionExceptions : AugmentBasePlatformTestCase() {
  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  var mockEngine: MockEngine =
    MockEngine { _ ->
      respond(
        content = "Unexpected request",
        status = HttpStatusCode.InternalServerError,
      )
    }

  override fun setUp() {
    super.setUp()
    mockOAuthState(
      AugmentCredentials("access-token", "http://test-server"),
      testRootDisposable,
    )
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testCancellationExceptionShouldBeCaught() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test.ts", "console.log('Hello <caret>'")

      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> throw CancellationException("Injected exception")
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("console.log('Hello '")
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testWrappedCancellationExceptionShouldBeCaught() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test.ts", "console.log('Hello <caret>'")

      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> throw IllegalStateException("Wrapped", CancellationException("Injected exception"))
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("console.log('Hello '")
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testIllegalStateExceptionShouldBeCaught() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test.ts", "console.log('Hello <caret>'")

      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> throw IllegalStateException("Injected exception")
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("console.log('Hello '")
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testExceptionShouldBeCaught() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test.ts", "console.log('Hello <caret>'")

      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> throw Exception("Injected exception")
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("console.log('Hello '")
    }
  }
}

@Suppress("UnstableApiUsage")
@RunWith(JUnit4::class)
class AugmentCompletionSuggestionWithStatusTest : AugmentBasePlatformTestCase() {
  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  private val gson = createGson()
  var mockEngine: MockEngine =
    MockEngine { _ ->
      respond(
        content = "Unexpected request",
        status = HttpStatusCode.InternalServerError,
      )
    }

  override fun setUp() {
    super.setUp()

    mockOAuthState(
      AugmentCredentials("access-token", "http://test-server"),
      testRootDisposable,
    )
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testSkipTokenCompletion() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test.ts", "console.log('Hello <caret>'")

      // Now allow the secondary inline completion call to proceed and accept it
      val completionResult =
        CompletionResult().apply {
          requestId = "test"
          completionItems =
            listOf(
              CompletionItem().apply {
                text = "Augment"
                skippedSuffix = "'"
                suffixReplacementText = "');"
              },
            )
        }
      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = gson.toJson(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("console.log('Hello Augment');")
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testStateManagerCompletionChanges() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test.ts", "console.log('Hello <caret>'")

      // Now allow the secondary inline completion call to proceed and accept it
      val completionResult =
        CompletionResult().apply {
          requestId = "test"
          completionItems =
            listOf(
              CompletionItem().apply {
                text = "Augment"
                skippedSuffix = "'"
                suffixReplacementText = "');"
              },
            )
        }
      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = gson.toJson(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      assertEquals(StateDefinitions.Base, StateManager.getInstance(project).getPriorityState())

      val connection = myFixture.project.messageBus.connect(testRootDisposable)
      val states = mutableListOf<StateDefinition>()
      connection.subscribe(
        StateChangeListener.TOPIC,
        object : StateChangeListener {
          override fun onChange(state: StateDefinition) {
            states += state
          }
        },
      )

      // Call completion resulting in generating completion -> base state changes
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("console.log('Hello Augment');")

      // Because we trigger a follow-on completion, we may get addition generation state changes
      // so just ensure we get a generating completion followed by a base state change
      assertTrue(states.size >= 2)
      assertEquals(StateDefinitions.GeneratingCompletion, states[0])
      assertEquals(StateDefinitions.Base, states[1])
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testStateManagerZeroCompletionChanges() {
    myFixture.testInlineCompletion {
      myFixture.configureByText("test-no-completions.ts", "console.log('Hello <caret>'")

      val completionResult =
        CompletionResult().apply {
          requestId = "test"
          completionItems = emptyList()
        }
      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = gson.toJson(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      assertEquals(StateDefinitions.Base, StateManager.getInstance(project).getPriorityState())

      val connection = myFixture.project.messageBus.connect(testRootDisposable)
      val states = mutableListOf<StateDefinition>()
      connection.subscribe(
        StateChangeListener.TOPIC,
        object : StateChangeListener {
          override fun onChange(state: StateDefinition) {
            states += state
          }
        },
      )
      // Trigger completion causing generating completion -> no completions -> base state changes
      callInlineCompletion()

      delay() // so everything is processed

      assertTrue(states.size >= 2)
      assertEquals(StateDefinitions.GeneratingCompletion, states[0])
      assertEquals(StateDefinitions.NoCompletions, states[1])
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testPrefixAndSuffixInRequestForCompletion() {
    myFixture.testInlineCompletion {
      // The prefix should be all "b" and the suffix should be all "c"
      myFixture.configureByText("test.ts", "a${"b".repeat(PREFIX_CHAR_COUNT)}<caret>${"c".repeat(SUFFIX_CHAR_COUNT)}d")

      // Now allow the secondary inline completion call to proceed and accept it
      val completionResult =
        CompletionResult().apply {
          requestId = "test"
          completionItems =
            listOf(
              CompletionItem().apply {
                text = "Augment"
                skippedSuffix = ""
                suffixReplacementText = ""
              },
            )
        }
      var completionRequest: CompletionRequest? = null
      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              println("Completion request: ${request.body.toByteArray().decodeToString()}")
              completionRequest = gson.fromJson(request.body.toByteArray().decodeToString(), CompletionRequest::class.java)
              respond(
                content = gson.toJson(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/record-onboarding-session-event" -> HttpUtil.respondOK(this)
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult("a${"b".repeat(PREFIX_CHAR_COUNT)}Augment${"c".repeat(SUFFIX_CHAR_COUNT)}d")

      assertNotNull(completionRequest)
      assertEquals("b".repeat(PREFIX_CHAR_COUNT), completionRequest?.prompt)
      assertEquals("c".repeat(SUFFIX_CHAR_COUNT), completionRequest?.suffix)

      // Check the offsets in the completion request are correct
      // Prefix begin is after "a" - so offset 1
      assertEquals(1, completionRequest?.prefixBegin)
      // Cursor position is after "a" and prefix of "b".repeat(PREFIX_CHAR_COUNT)
      assertEquals(1 + PREFIX_CHAR_COUNT, completionRequest?.cursorPosition)
      // Suffix end is after "a", prefix of "b".repeat(PREFIX_CHAR_COUNT), and suffix of "c".repeat(SUFFIX_CHAR_COUNT)
      assertEquals(1 + PREFIX_CHAR_COUNT + SUFFIX_CHAR_COUNT, completionRequest?.suffixEnd)
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testUnicodePrefixAndSuffixInRequestForCompletion() {
    myFixture.testInlineCompletion {
      val multiByteUnicodeChar = "\uD83D\uDE00" // Grinning Face Emoji
      // The prefix should be "😀bbbb..." and the suffix should be "cccc...😀" where b and c satisfy the char count
      myFixture.configureByText(
        "test.ts",
        "a${multiByteUnicodeChar}${"b".repeat(PREFIX_CHAR_COUNT - 1)}<caret>${"c".repeat(SUFFIX_CHAR_COUNT - 1)}${multiByteUnicodeChar}d",
      )

      // Now allow the secondary inline completion call to proceed and accept it
      val completionResult =
        CompletionResult().apply {
          requestId = "test"
          completionItems =
            listOf(
              CompletionItem().apply {
                text = "Augment"
                skippedSuffix = ""
                suffixReplacementText = ""
              },
            )
        }
      var completionRequest: CompletionRequest? = null
      mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              completionRequest = gson.fromJson(request.body.toByteArray().decodeToString(), CompletionRequest::class.java)
              respond(
                content = gson.toJson(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()

      delay() // so everything is processed
      insert()
      myFixture.checkResult(
        "a${multiByteUnicodeChar}${"b".repeat(PREFIX_CHAR_COUNT - 1)}Augment${"c".repeat(SUFFIX_CHAR_COUNT - 1)}${multiByteUnicodeChar}d",
      )

      assertNotNull(completionRequest)
      assertEquals("b".repeat(PREFIX_CHAR_COUNT - 1), completionRequest?.prompt)
      assertEquals("c".repeat(SUFFIX_CHAR_COUNT - 1), completionRequest?.suffix)

      // Check the offsets in the completion request are correct
      // Prefix begin is after "a" and the multibyte Unicode char - so offset 3
      assertEquals(3, completionRequest?.prefixBegin)
      // Cursor position is after "a" + multibyte Unicode char and prefix of "b".repeat(PREFIX_CHAR_COUNT - 1)
      assertEquals(3 + PREFIX_CHAR_COUNT - 1, completionRequest?.cursorPosition)
      // Suffix end is after "a" + multibyte Unicode char and prefix of "b".repeat(PREFIX_CHAR_COUNT - 1), and suffix of "c".repeat(SUFFIX_CHAR_COUNT - 1)
      assertEquals(3 + PREFIX_CHAR_COUNT - 1 + SUFFIX_CHAR_COUNT - 1, completionRequest?.suffixEnd)
    }
  }
}
