package com.augmentcode.intellij.utils

import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class SemVerTest {
  @Test
  fun testParseSimple() {
    val version = SemVer.parse("1.2.3")
    assert(version.major == 1L)
    assert(version.minor == 2L)
    assert(version.patch == 3L)
  }

  @Test
  fun testParseWithBeta() {
    val version = SemVer.parse("1.2.3-beta")
    assert(version.major == 1L)
    assert(version.minor == 2L)
    assert(version.patch == 3L)
  }

  @Test
  fun testParseWithLongVersion() {
    val version = SemVer.parse("99999999.888888888.7777777777")
    assert(version.major == 99999999L)
    assert(version.minor == 888888888L)
    assert(version.patch == 7777777777L)
  }

  @Test(expected = IllegalArgumentException::class)
  fun testInvalidVersion() {
    SemVer.parse("asdf")
  }

  @Test(expected = IllegalArgumentException::class)
  fun testEmptyString() {
    SemVer.parse("")
  }

  @Test
  fun testComparePatch() {
    val v1 = SemVer.parse("1.2.3")
    val v2 = SemVer.parse("1.2.4")
    assert(v1 < v2)
  }

  @Test
  fun testCompareMinor() {
    val v1 = SemVer.parse("1.2.3")
    val v2 = SemVer.parse("1.3.0")
    assert(v1 < v2)
  }

  @Test
  fun testCompareMajor() {
    val v1 = SemVer.parse("1.2.3")
    val v2 = SemVer.parse("2.0.0")
    assert(v1 < v2)
  }

  @Test
  fun testEquals() {
    val v1 = SemVer.parse("1.2.3")
    val v2 = SemVer.parse("1.2.3")
    assert(v1 == v2)
  }
}
