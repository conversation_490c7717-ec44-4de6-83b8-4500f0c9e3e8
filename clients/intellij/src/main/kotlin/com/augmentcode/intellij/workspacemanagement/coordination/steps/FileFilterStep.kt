package com.augmentcode.intellij.workspacemanagement.coordination.steps

import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.index.ignore.PathFilterService
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.utils.SymlinkUtil
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.VirtualFile
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import org.assertj.core.util.VisibleForTesting
import java.nio.file.Path
import kotlin.coroutines.cancellation.CancellationException

class FileFilterStep(
  private val project: Project,
  private val scope: CoroutineScope,
  private val inputChannel: RoughlySizedChannel<Path>,
  private val outputChannel: RoughlySizedChannel<VirtualFile>,
) : Disposable {
  private val logger = thisLogger()

  @VisibleForTesting
  internal var processingJob: Job? = null

  override fun dispose() {
    stopProcessing()
  }

  fun startProcessing() {
    if (processingJob != null) {
      logger.info("Upload job already running")
      return
    }

    processingJob =
      scope.launch {
        logger.info("Starting upload job")
        while (isActive) {
          try {
            processFilesFromChannel()
          } catch (e: CancellationException) {
            throw e
          } catch (e: Exception) {
            logger.warn("Failed to process upload batch", e)
          }
        }
      }
  }

  fun stopProcessing() {
    thisLogger().info("Stopping file filtering job")
    processingJob?.cancel()
    processingJob = null
  }

  private suspend fun processFilesFromChannel() {
    val path = inputChannel.tryReceive().getOrNull() ?: return
    logger.trace("Received file for processing: $path")

    // If we are not signed in, reject the files
    val context = PluginStateService.instance.context
    if (!context.isSignedIn || context.model == null) {
      logger.warn("Not signed in, ignoring file: $path")
      return
    }

    // TODO: Handle getting document

    // Need to handle null virtualFile because it is null during project startup.
    // (It's unclear whether this is the right thing.)
    val virtualFile = VfsUtil.findFile(path, true)
    if (virtualFile == null) {
      logger.info("Failed to find virtual file for path: $path")
      return
    }

    val accepted = isAccepted(context.flags, context.model, virtualFile)
    if (!accepted) {
      return
    }
    outputChannel.send(virtualFile)
  }

  @VisibleForTesting
  internal suspend fun isAccepted(
    flags: FeatureFlags,
    model: AugmentModel,
    virtualFile: VirtualFile,
  ): Boolean {
    // If the file is a symlink, do not accept the file
    // The `canonicalPath` is the actual file path of the file and `path` is the path of the symlink.
    if (SymlinkUtil.isSymlink(virtualFile)) {
      logger.trace("File rejected by filter because it's a symlink: ${virtualFile.path}")
      return false
    }

    if (virtualFile.fileType.isBinary) {
      logger.trace("File rejected due to binary file type: ${virtualFile.path}")
      return false
    }

    // Check file against model info checks.
    if (!isSupportedFileSize(flags, virtualFile)) {
      logger.trace("File rejected due to size exceeding limit: ${virtualFile.path}")
      return false
    }

    if (!isSupportedFileExtension(flags, model, virtualFile.extension)) {
      logger.trace("File rejected due to unsupported file extension: ${virtualFile.path}")
      return false
    }

    if (!PathFilterService.getInstance(project).isAccepted(virtualFile)) {
      logger.trace("File rejected due to gitignore rules: ${virtualFile.path}")
      return false
    }

    logger.trace("Accepted file: ${virtualFile.path}")
    return true
  }

  // filtration based on information from the API
  private fun isSupportedFileSize(
    flags: FeatureFlags,
    file: VirtualFile,
  ): Boolean {
    return file.length <= flags.maxUploadSizeBytes
  }

  @VisibleForTesting
  internal fun isSupportedFileExtension(
    flags: FeatureFlags,
    model: AugmentModel,
    extension: String?,
  ): Boolean {
    if (flags.bypassLanguageFilter) {
      return true
    }

    if (extension == null) {
      return false
    }

    return model.supportedFileExtensions.contains(extension)
  }
}
