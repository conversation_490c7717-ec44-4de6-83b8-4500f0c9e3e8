package com.augmentcode.intellij.utils

import com.augmentcode.rpc.OpenFileRequest
import com.intellij.find.FindManager
import com.intellij.openapi.editor.Document
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.util.concurrency.annotations.RequiresReadLock
import kotlin.math.max

object TextRangeCalculator {
  /**
   * Calculates the range to select in the editor based on the range in the request.
   * If the request has a snippet and no ranges, we search for the snippet in the document
   * and return its range.
   */
  @RequiresReadLock
  fun calculateRange(
    document: Document,
    request: OpenFileRequest,
    project: Project,
  ): TextRange? =
    when {
      request.data.hasFullRange() -> {
        val fullRange = request.data.fullRange
        TextRange(
          document.getLineStartOffset(fullRange.startLineNumber) + fullRange.startColumn,
          document.getLineStartOffset(fullRange.endLineNumber) + fullRange.endColumn,
        )
      }

      request.data.hasRange() -> {
        val range = request.data.range
        // Subtract 1 because range is 1-based, but editor is 0-based
        // (The webview can return 0 for start and end, so we need to handle that)
        TextRange(
          document.getLineStartOffset(max(range.start - 1, 0)),
          document.getLineEndOffset(max(range.stop - 1, 0)),
        )
      }

      request.data.hasSnippet() -> {
        calculateSnippetRange(document, request, project)
      }

      else -> null
    }

  private fun calculateSnippetRange(
    document: Document,
    request: OpenFileRequest,
    project: Project,
  ): TextRange? {
    val findManager = FindManager.getInstance(project)
    val model = findManager.findInFileModel.clone()
    model.apply {
      stringToFind = stripText(request.data.snippet)
      isGlobal = true
      isMultiline = true
      isCaseSensitive = true
      isRegularExpressions = false
      isWholeWordsOnly = false
    }

    return findManager
      .findString(
        stripText(document.text),
        0,
        model,
      ).let { result ->
        if (result.isStringFound) {
          transformRange(TextRange(result.startOffset, result.endOffset), document)
        } else {
          null
        }
      }
  }

  /**
   * Transforms the text to search it. We remove leading and trailing whitespace.
   */
  private fun stripText(text: String): String = text.lines().joinToString("\n") { it.trim() }

  private fun transformRange(
    transformedRange: TextRange,
    document: Document,
  ): TextRange {
    val originalText = document.text
    return TextRange(
      transformIndex(transformedRange.startOffset, originalText),
      transformIndex(transformedRange.endOffset, originalText),
    )
  }

  /**
   * When we transform the text to search it, we remove leading and trailing whitespace.
   * Then intellij gives us the index of the match in the transformed text. So we need to
   * transform the index back to the original text.
   *
   * @param index The index of the match in the transformed text.
   * @param text The original text.
   * @return The index of the match in the original text.
   */
  private fun transformIndex(
    index: Int,
    text: String,
  ): Int {
    val lines = text.lines()
    // Find which line we're on in the stripped text by counting newlines.
    // This will also be the line index in the original text.
    val lineIndex = stripText(text).substring(0, index).count { it == '\n' }
    var newIndex = index

    // For each previous line, add back the whitespace that was trimmed
    for (i in 0 until lineIndex) {
      newIndex += lines[i].length - lines[i].trim().length // Add the number of characters removed from the line
    }

    // Add back the leading whitespace for the current line
    newIndex += lines[lineIndex].length - lines[lineIndex].trimStart().length
    return newIndex
  }
}
