package com.augmentcode.intellij.ignore

import com.intellij.openapi.diagnostic.thisLogger
import java.io.IOException

class InvalidGitIgnorePatternException(message: String) : Exception(message)

class GitIgnore(gitIgnoreContent: String) {
  companion object {
    // This is the separator dictated in the gitignore documentation (same as Linux/Unix)
    const val GITIGNORE_PATH_SEPARATOR: String = "/"
    private val WINDOWS_ABSOLUTE_PATH_REGEX = Regex("^[a-zA-Z]:/.*")

    // Regex to match one or more consecutive forward slashes
    private val CONSECUTIVE_SLASHES_REGEX = Regex("/+")
  }

  private val logger = thisLogger()
  internal val ignoreRules = mutableListOf<IgnoreRule>()

  init {
    val reader = gitIgnoreContent.lineSequence()
    try {
      for (readerLine in reader) {
        val line = readerLine.trim()
        if (line.isEmpty() || line.startsWith("#")) {
          continue
        }
        val ignoreRule =
          if (line.startsWith("!")) {
            IgnoreRule(true, line.substring(1))
          } else {
            IgnoreRule(false, line)
          }
        ignoreRules.add(ignoreRule)
      }
    } catch (e: IOException) {
      logger.error("Failed to read gitignore file: ${e.message}", e)
    }
  }

  suspend fun isIgnoredFile(filename: String): Boolean? {
    val matchFileName = standardizeFilename(filename)
    var mustBeIgnored: Boolean? = null

    for (ignoreRule in ignoreRules) {
      val ruleVerdict = ignoreRule.isIgnoredFile(matchFileName)
      if (ruleVerdict == null) {
        continue
      }
      mustBeIgnored = ruleVerdict

      // Handling the "bug" in git that results in unexpected ignores
      if (ruleVerdict && ignoreRule.directoryMatch) {
        // From: https://www.atlassian.com/git/tutorials/saving-changes/gitignore
        // These rules
        //    logs/
        //    !logs/important.log
        // ignore these files
        //    logs/debug.log
        //    logs/important.log
        // Documentation:
        //    Wait a minute! Shouldn't logs/important.log be negated in the example on the left
        //    Nope! Due to a performance-related quirk in Git, you can not negate a file that
        //    is ignored due to a pattern matching a directory

        // So if there was a directory match rule that ignores this file then that one always wins.

        break
      }
    }

    return mustBeIgnored
  }

  fun standardizeFilename(filename: String): String {
    // Important that WINDOWS_ABSOLUTE_PATH_REGEX and CONSECUTIVE_SLASHES_REGEX are defined as constants
    // because creating a new Regex object for each call to standardizeFilename is  expensive.
    var unixifiedName = separatorsToUnix(filename)
    if (!unixifiedName.matches(WINDOWS_ABSOLUTE_PATH_REGEX)) {
      unixifiedName = GITIGNORE_PATH_SEPARATOR + unixifiedName
    }
    return unixifiedName.replace(CONSECUTIVE_SLASHES_REGEX, "/")
  }

  fun separatorsToUnix(path: String): String {
    return path.replace("\\", GITIGNORE_PATH_SEPARATOR)
  }
}

internal class IgnoreRule(
  private val negate: Boolean,
  private val fileExpression: String,
) {
  companion object {
    const val LITERAL_STAR_MARKER = "||>s<||"
    const val LITERAL_QUESTION_MARK = "||>q<||"
    const val BASE_DIR_REGEX = "^/?"
  }

  val filePattern: Regex
  val directoryMatch: Boolean

  init {
    directoryMatch = !negate && fileExpression.endsWith("/")

    var fileRegex =
      fileExpression
        .trim { it <= ' ' } // Clear leading and trailing spaces
        .replace("^!".toRegex(), "") // Strip the negation at the start of the line (if any)
        .replace("[!", "[^") // Fix the 'not' range or 'not' set
        .replace("\\*", LITERAL_STAR_MARKER) // Move the escaped * to something special that does not have a * in it
        .replace("\\?", LITERAL_QUESTION_MARK) // Move the escaped ? to something special that does not have a ? in it
        .replace("?", "[^/]")
    ; // The character "?" matches any one character except "/".

    if (fileExpression.contains("/") && !fileExpression.endsWith("/")) {
      // Patterns specifying a file in a particular directory are relative to the repository root.
      if (fileRegex.startsWith("/")) {
        fileRegex.substring(1)
      }
    } else {
      // If there is a separator at the beginning or middle (or both) of the pattern,
      // then the pattern is relative to the directory level of the particular .gitignore file itself.
      // Otherwise, the pattern may also match at any level below the .gitignore level.
      if (Regex("./.").find(fileRegex) == null) {
        // If a path does not start with a /, the path is treated as if it starts with a globstar. README.md is treated the same way as /**/README.md
        fileRegex = fileRegex.replace("^([^/*])".toRegex(), "**/$1")
      }
    }

    fileRegex =
      fileRegex
        .replace("\\ ", " ") // The escaped spaces must become spaces again.
        // Some characters do NOT have a special meaning
        .replace("$", "\\$")
        .replace("(", "\\(")
        .replace(")", "\\)")

    // Convert cases like
    //     coverage*[.json, .xml, .info]
    // into
    //     coverage*(.json|.xml|.info)
    if (fileRegex.contains("[") && fileRegex.contains(",")) {
      var changed = false
      while (true) {
        val newRegex = fileRegex.replace("\\[([^]]+) *, *([^]]+)]".toRegex(), "[$1|$2]")
        if (newRegex == fileRegex) {
          break
        }
        fileRegex = newRegex
        changed = true
      }
      if (changed) {
        fileRegex = fileRegex.replace("\\[([^]]+\\|[^]]+)]".toRegex(), "($1)")
      }
    }

    fileRegex =
      fileRegex // "/foo" --> End can be a filename (so we pin to the end) or a directory name (so we expect another / )
        .replace("([^/*])$".toRegex(), "$1(/|\\$)")
        .replace(".", "\\.") // Avoid bad wildcards
        .replace("\\.*", "\\.[^/]*") //  matching  /.* onto /.foo/bar.xml
        .replace("?", "[^/]") // Single character match
        // The Globstar "/**/bar" must also match "bar"
        .replace("^\\*\\*/".toRegex(), "(.*/)?") // The Globstar "foo/**/bar" must also match "foo/bar"
        .replace("/**", "(/.*)?") // The wildcard "foo/*/bar" must match exactly 1 subdir "foo/something/bar"
        // and not "foo/bar", "foo//bar" or "foo/something/something/bar"
        .replace("/*/", "/[^/]+/")
        .replace("/*/", "/[^/]+/")
        .replace("**", ".*") // Convert to the Regex wildcards
        .replace("^\\*".toRegex(), ".*") // Match anything at the start
        .replace("^/".toRegex(), "^/") // If starts with / then pin to the start.
        .replace(
          "/\\*([^/]*)$".toRegex(),
          "/[^/]*$1\\$",
        ) // A trailing '/*something' means NO further subdirs should be matched
        .replace("/*", "/[^/]*") // "/foo/*\.js"  --> "/foo/.*\.js"
        .replace("([^.\\]])\\*".toRegex(), "$1[^/]*") // Match anything at the start
        .replace(LITERAL_STAR_MARKER, "\\Q*\\E") // Move the 'something special' to the literal *
        .replace(LITERAL_QUESTION_MARK, "\\Q?\\E") // Move the 'something special' to the literal ?
        .replace("/+".toRegex(), "/") // Remove duplication
        .replace("/\\E/", "/\\E") // Remove '/' duplication around a literal marker

    val finalRegex = "$BASE_DIR_REGEX$fileRegex"
    try {
      filePattern = Regex(finalRegex)
    } catch (e: Exception) {
      val bang = if (negate) "!" else ""
      throw InvalidGitIgnorePatternException(
        "Invalid gitignore rule: Expression >>>$bang$fileExpression<<< was converted to regex >>>$finalRegex<<<",
      )
    }
  }

  suspend fun isIgnoredFile(filename: String): Boolean? {
    val matcherService = MatcherFactoryService.getInstance()

    if (matcherService.match(filePattern.pattern, filename)) {
      return !negate
    }
    return null
  }

  val ignoreExpression: String
    get() = (if (negate) "!" else "") + fileExpression
}
