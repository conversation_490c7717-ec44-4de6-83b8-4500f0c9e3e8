package logger

import (
	"io"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// Logger is a wrapper around `zerolog.Logger`. It provides a convenient API for embedding in other
// structs. It also includes backwards-compatible methods for easing the conversion to zerolog.
//
// See `GlobalLoggerFromFlags()` for configuring the logger.
type Logger struct {
	logger *zerolog.Logger
}

// New returns a `Logger` wrapper around the global `zerolog/log.Logger`. If `w` is not nil,
// it will be used as the output writer; otherwise the global logger's existing output writer is used.
func New(w io.Writer) *Logger {
	logger := log.Logger
	if w != nil {
		logger = logger.Output(w)
	}
	return &Logger{
		logger: &logger,
	}
}

// Logger is a nil-safe getter for the zerolog.Logger, it falls back to the global logger.
func (l *Logger) Logger() *zerolog.Logger {
	if l == nil || l.logger == nil {
		return &log.Logger
	}
	return l.logger
}

// WithString returns a new `Logger` with the given key-value pair added to the context.
func (l *Logger) WithString(key, val string) *Logger {
	logger := l.Logger().With().Str(key, val).Logger()
	return &Logger{
		logger: &logger,
	}
}

// WithDict returns a new `Logger` with the given key-value pairs added to the context.
func (l *Logger) WithDict(key string, vals map[string]string) *Logger {
	d := zerolog.Dict()
	for k, v := range vals {
		d = d.Str(k, v)
	}
	logger := l.Logger().With().Dict(key, d).Logger()
	return &Logger{
		logger: &logger,
	}
}

////////////////////////////////////////////////////////////////////////////////
//
// Simplified Logger API for embedding in other structs (e.g. clients).
//

func (l *Logger) Trace() *zerolog.Event {
	return l.Logger().Trace()
}

func (l *Logger) Debug() *zerolog.Event {
	return l.Logger().Debug()
}

func (l *Logger) Info() *zerolog.Event {
	return l.Logger().Info()
}

// Notice is fake. zerolog doesn't support notice, instead we map this to warning.
func (l *Logger) Notice() *zerolog.Event {
	return l.Logger().Warn()
}

func (l *Logger) Warn() *zerolog.Event {
	return l.Logger().Warn()
}

func (l *Logger) Error() *zerolog.Event {
	return l.Logger().Error()
}

func (l *Logger) Err(err error) *zerolog.Event {
	return l.Logger().Err(err)
}

func (l *Logger) Fatal() *zerolog.Event {
	return l.Logger().Fatal()
}

////////////////////////////////////////////////////////////////////////////////
//
// Backwards-compatible logging methods, pre-zerolog.
//

func (l *Logger) LogDebug(fmt string, a ...any) {
	l.Debug().CallerSkipFrame(1).Msgf(fmt, a...)
}

func (l *Logger) LogInfo(fmt string, a ...any) {
	l.Info().CallerSkipFrame(1).Msgf(fmt, a...)
}

// LogNotice is fake. zerolog doesn't support notice, instead we map this to warning.
func (l *Logger) LogNotice(fmt string, a ...any) {
	l.Notice().CallerSkipFrame(1).Msgf(fmt, a...)
}

func (l *Logger) LogWarn(fmt string, a ...any) {
	l.Warn().CallerSkipFrame(1).Msgf(fmt, a...)
}

func (l *Logger) LogErr(fmt string, a ...any) {
	l.Error().CallerSkipFrame(1).Msgf(fmt, a...)
}
