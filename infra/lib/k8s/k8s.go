package k8s

import (
	"bufio"
	"bytes"
	"context"
	"crypto/rsa"
	"encoding/json"
	"iter"
	"net/http"
	"os"
	"os/exec"
	"sync"

	"golang.org/x/sync/errgroup"

	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/watch"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/remotecommand"

	snapshotv1 "github.com/kubernetes-csi/external-snapshotter/client/v8/clientset/versioned/typed/volumesnapshot/v1"
	apiauthv1 "k8s.io/api/authentication/v1"
	apicorev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	metav1a "k8s.io/client-go/applyconfigurations/meta/v1"
	appsv1 "k8s.io/client-go/kubernetes/typed/apps/v1"
	authv1 "k8s.io/client-go/kubernetes/typed/authentication/v1"
	corev1 "k8s.io/client-go/kubernetes/typed/core/v1"
	netv1 "k8s.io/client-go/kubernetes/typed/networking/v1"
	rbacv1 "k8s.io/client-go/kubernetes/typed/rbac/v1"

	"github.com/augmentcode/augment/infra/lib/logger"
)

// IsNotFound checks if `err` is considered a K8s API NotFound error.
func IsNotFound(err error) bool {
	return apierrors.IsNotFound(err)
}

// NotFoundOK masks `IsNotFound()` errors as nil, otherwise it returns `err` unmodified.
func NotFoundOK(err error) error {
	if IsNotFound(err) {
		return nil
	}
	return err
}

// Client is a K8s client.
type Client struct {
	*logger.Logger

	cfg     *Config
	clients *kubernetes.Clientset
	dyn     *dynamic.DynamicClient
	volsnap *snapshotv1.SnapshotV1Client
	ns      *string
	uext    metav1a.UnstructuredExtractor

	sealKey *rsa.PublicKey
	sealLk  *sync.Mutex

	defaultFieldManager string
}

// New builds a new Client. If context is empty, default to kubeconfig's
// `current-context` value. If no KUBECONFIG is found, attempt to fall back
// to the in-cluster config.
func New(context string) (*Client, error) {
	if cfg, err := LoadConfig(context); err != nil {
		return nil, err
	} else {
		return NewFromConfig(cfg)
	}
}

// NewFromKubeconfig builds a new Client from KUBECONFIG or explicit paths.
// It does not attempt to fall back to in-cluster.
func NewFromKubeconfig(context string, paths ...string) (*Client, error) {
	if cfg, err := LoadKubeConfig(context, paths...); err != nil {
		return nil, err
	} else {
		return NewFromConfig(cfg)
	}
}

// NewFromInCluster builds a new Client strictly from in-cluster config.
func NewFromInCluster() (*Client, error) {
	if cfg, err := LoadInClusterConfig(); err != nil {
		return nil, err
	} else {
		return NewFromConfig(cfg)
	}
}

// NewFromConfig builds a new Client from a given Config.
func NewFromConfig(cfg *Config) (*Client, error) {
	if clients, err := kubernetes.NewForConfig(cfg.REST()); err != nil {
		return nil, err
	} else if dyn, err := dynamic.NewForConfig(cfg.REST()); err != nil {
		return nil, err
	} else if volsnap, err := snapshotv1.NewForConfig(cfg.REST()); err != nil {
		return nil, err
	} else {
		return &Client{
			Logger:  logger.New(nil),
			cfg:     cfg,
			clients: clients,
			dyn:     dyn,
			volsnap: volsnap,
			sealLk:  &sync.Mutex{},
		}, nil
	}
}

func (c Client) Config() *Config {
	return c.cfg
}

func (c Client) Namespace() string {
	if c.ns != nil {
		return *c.ns
	}
	return c.Config().Namespace()
}

func (c Client) InNamespace(ns string) Client {
	c.ns = &ns
	return c
}

func (c *Client) SetDefaultFieldManager(fm string) {
	c.defaultFieldManager = fm
}

func (c *Client) DefaultFieldManager() string {
	return c.defaultFieldManager
}

func (c *Client) SetNewUnstructuredExtractor(ctx context.Context) error {
	_ = ctx // context isn't used, but it should be. We take it to signal that this will send an RPC.
	if uext, err := metav1a.NewUnstructuredExtractor(c.clients); err != nil {
		return err
	} else {
		c.uext = uext
		return nil
	}
}

func (c *Client) UnstructuredExtractor() metav1a.UnstructuredExtractor {
	return c.uext
}

func (c *Client) ExtractUnstructuredApply(u *unstructured.Unstructured, fieldManager string) (*unstructured.Unstructured, error) {
	if fieldManager == "" {
		fieldManager = c.defaultFieldManager
	}
	return c.UnstructuredExtractor().Extract(u, fieldManager)
}

func (c *Client) ExtractUnstructuredObjectApply(o *Object, fieldManager string) (*unstructured.Unstructured, error) {
	if u, err := o.Unstructured(); err != nil {
		return nil, err
	} else {
		return c.ExtractUnstructuredApply(u, fieldManager)
	}
}

////////////////////////////////////////////////////////////////////////////////
// APIs

func (c Client) corev1() corev1.CoreV1Interface {
	return c.clients.CoreV1()
}

func (c Client) appsv1() appsv1.AppsV1Interface {
	return c.clients.AppsV1()
}

func (c Client) netv1() netv1.NetworkingV1Interface {
	return c.clients.NetworkingV1()
}

func (c Client) rbacv1() rbacv1.RbacV1Interface {
	return c.clients.RbacV1()
}

func (c Client) snapshotv1() snapshotv1.SnapshotV1Interface {
	return c.volsnap
}

func (c Client) authv1() authv1.AuthenticationV1Interface {
	return c.clients.AuthenticationV1()
}

////////////////////////////////////////////////////////////////////////////////

func (c Client) Dynamic(group, version, resource string) dynamic.ResourceInterface {
	gvr := schema.GroupVersionResource{
		Group:    group,
		Version:  version,
		Resource: resource,
	}
	return c.dyn.Resource(gvr).Namespace(c.Namespace())
}

func (c Client) DynamicForObject(o *Object) dynamic.ResourceInterface {
	return c.dyn.Resource(o.GVR()).Namespace(c.Namespace())
}

////////////////////////////////////////////////////////////////////////////////
// Object Typed Interfaces

func (c Client) namespaces() corev1.NamespaceInterface {
	return c.corev1().Namespaces()
}

func (c Client) configmaps() corev1.ConfigMapInterface {
	return c.corev1().ConfigMaps(c.Namespace())
}

func (c Client) events() corev1.EventInterface {
	return c.corev1().Events(c.Namespace())
}

func (c Client) pods() corev1.PodInterface {
	return c.corev1().Pods(c.Namespace())
}

func (c Client) pvcs() corev1.PersistentVolumeClaimInterface {
	return c.corev1().PersistentVolumeClaims(c.Namespace())
}

func (c Client) secrets() corev1.SecretInterface {
	return c.corev1().Secrets(c.Namespace())
}

func (c Client) services() corev1.ServiceInterface {
	return c.corev1().Services(c.Namespace())
}

func (c Client) serviceaccounts() corev1.ServiceAccountInterface {
	return c.corev1().ServiceAccounts(c.Namespace())
}

func (c Client) deployments() appsv1.DeploymentInterface {
	return c.appsv1().Deployments(c.Namespace())
}

func (c Client) statefulsets() appsv1.StatefulSetInterface {
	return c.appsv1().StatefulSets(c.Namespace())
}

func (c Client) replicasets() appsv1.ReplicaSetInterface {
	return c.appsv1().ReplicaSets(c.Namespace())
}

func (c Client) ingresses() netv1.IngressInterface {
	return c.netv1().Ingresses(c.Namespace())
}

func (c Client) rolebindings() rbacv1.RoleBindingInterface {
	return c.rbacv1().RoleBindings(c.Namespace())
}

func (c Client) volumesnapshots() snapshotv1.VolumeSnapshotInterface {
	return c.snapshotv1().VolumeSnapshots(c.Namespace())
}

func (c Client) selfsubjectreviews() authv1.SelfSubjectReviewInterface {
	return c.authv1().SelfSubjectReviews()
}

func (c Client) volumesnapshots_dynamic() dynamic.ResourceInterface {
	return c.Dynamic("snapshot.storage.k8s.io", "v1", "volumesnapshots")
}

////////////////////////////////////////////////////////////////////////////////
// List*()

func (c Client) ListNamespaces(ctx context.Context, opts ...ListOpt) ([]*Namespace, error) {
	nss, err := c.namespaces().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*Namespace{}
	for _, item := range nss.Items {
		ret = append(ret, NewNamespace(&item))
	}
	return ret, nil
}

func (c Client) ListConfigMaps(ctx context.Context, opts ...ListOpt) ([]*ConfigMap, error) {
	configmaps, err := c.configmaps().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*ConfigMap{}
	for _, item := range configmaps.Items {
		ret = append(ret, NewConfigMap(&item))
	}
	return ret, nil
}

func (c Client) ListEvents(ctx context.Context, opts ...ListOpt) (EventList, error) {
	objs, err := c.events().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := EventList{}
	for _, item := range objs.Items {
		ret = append(ret, NewEvent(&item))
	}
	return ret, nil
}

func (c Client) ListPods(ctx context.Context, opts ...ListOpt) ([]*Pod, error) {
	pods, err := c.pods().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*Pod{}
	for _, p := range pods.Items {
		ret = append(ret, NewPod(&p))
	}
	return ret, nil
}

func (c Client) ListPVCs(ctx context.Context, opts ...ListOpt) ([]*PVC, error) {
	pvcs, err := c.pvcs().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*PVC{}
	for _, item := range pvcs.Items {
		ret = append(ret, NewPVC(&item))
	}
	return ret, nil
}

func (c Client) ListNodes(ctx context.Context, opts ...ListOpt) ([]*Node, error) {
	nodes, err := c.corev1().Nodes().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*Node{}
	for _, item := range nodes.Items {
		ret = append(ret, NewNode(&item))
	}
	return ret, nil
}

func (c Client) ListSecrets(ctx context.Context, opts ...ListOpt) ([]*Secret, error) {
	secrets, err := c.secrets().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*Secret{}
	for _, item := range secrets.Items {
		ret = append(ret, NewSecret(&item))
	}
	return ret, nil
}

func (c Client) ListServices(ctx context.Context, opts ...ListOpt) ([]*Service, error) {
	ss, err := c.services().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*Service{}
	for _, item := range ss.Items {
		ret = append(ret, NewService(&item))
	}
	return ret, nil
}

func (c Client) ListServiceAccounts(ctx context.Context, opts ...ListOpt) ([]*ServiceAccount, error) {
	sas, err := c.serviceaccounts().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*ServiceAccount{}
	for _, item := range sas.Items {
		ret = append(ret, NewServiceAccount(&item))
	}
	return ret, nil
}

func (c Client) ListDeployments(ctx context.Context, opts ...ListOpt) ([]*Deployment, error) {
	deployments, err := c.deployments().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*Deployment{}
	for _, p := range deployments.Items {
		ret = append(ret, NewDeployment(&p))
	}
	return ret, nil
}

func (c Client) ListStatefulSets(ctx context.Context, opts ...ListOpt) ([]*StatefulSet, error) {
	objs, err := c.statefulsets().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*StatefulSet{}
	for _, obj := range objs.Items {
		ret = append(ret, NewStatefulSet(&obj))
	}
	return ret, nil
}

func (c Client) ListReplicaSets(ctx context.Context, opts ...ListOpt) ([]*ReplicaSet, error) {
	replicasets, err := c.replicasets().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*ReplicaSet{}
	for _, p := range replicasets.Items {
		ret = append(ret, NewReplicaSet(&p))
	}
	return ret, nil
}

func (c Client) ListIngresses(ctx context.Context, opts ...ListOpt) ([]*Ingress, error) {
	igs, err := c.ingresses().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*Ingress{}
	for _, item := range igs.Items {
		ret = append(ret, NewIngress(&item))
	}
	return ret, nil
}

func (c Client) ListRoleBindings(ctx context.Context, opts ...ListOpt) ([]*RoleBinding, error) {
	rolebindings, err := c.rolebindings().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*RoleBinding{}
	for _, p := range rolebindings.Items {
		ret = append(ret, NewRoleBinding(&p))
	}
	return ret, nil
}

func (c Client) ListVolumeSnapshots(ctx context.Context, opts ...ListOpt) ([]*VolumeSnapshot, error) {
	lst, err := c.volumesnapshots().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*VolumeSnapshot{}
	for _, p := range lst.Items {
		ret = append(ret, NewVolumeSnapshot(&p))
	}
	return ret, nil
}

////////////////////////////////////////////////////////////////////////////////
// Get*()

func (c Client) GetNamespace(ctx context.Context, name string, opts ...GetOpt) (*Namespace, error) {
	ns, err := c.namespaces().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewNamespace(ns), nil
}

func (c Client) GetConfigMap(ctx context.Context, name string, opts ...GetOpt) (*ConfigMap, error) {
	configmap, err := c.configmaps().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewConfigMap(configmap), nil
}

func (c Client) GetPod(ctx context.Context, name string, opts ...GetOpt) (*Pod, error) {
	pod, err := c.pods().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewPod(pod), nil
}

func (c Client) GetPVC(ctx context.Context, name string, opts ...GetOpt) (*PVC, error) {
	pvc, err := c.pvcs().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewPVC(pvc), nil
}

func (c Client) GetSecret(ctx context.Context, name string, opts ...GetOpt) (*Secret, error) {
	secret, err := c.secrets().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewSecret(secret), nil
}

func (c Client) GetService(ctx context.Context, name string, opts ...GetOpt) (*Service, error) {
	s, err := c.services().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewService(s), nil
}

func (c Client) GetServiceAccount(ctx context.Context, name string, opts ...GetOpt) (*ServiceAccount, error) {
	sa, err := c.serviceaccounts().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewServiceAccount(sa), nil
}

func (c Client) GetDeployment(ctx context.Context, name string, opts ...GetOpt) (*Deployment, error) {
	deployment, err := c.deployments().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewDeployment(deployment), nil
}

func (c Client) GetStatefulSet(ctx context.Context, name string, opts ...GetOpt) (*StatefulSet, error) {
	obj, err := c.statefulsets().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewStatefulSet(obj), nil
}

func (c Client) GetReplicaSet(ctx context.Context, name string, opts ...GetOpt) (*ReplicaSet, error) {
	replicaset, err := c.replicasets().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewReplicaSet(replicaset), nil
}

func (c Client) GetIngress(ctx context.Context, name string, opts ...GetOpt) (*Ingress, error) {
	i, err := c.ingresses().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewIngress(i), nil
}

func (c Client) GetRoleBinding(ctx context.Context, name string, opts ...GetOpt) (*RoleBinding, error) {
	rolebinding, err := c.rolebindings().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewRoleBinding(rolebinding), nil
}

func (c Client) GetVolumeSnapshot(ctx context.Context, name string, opts ...GetOpt) (*VolumeSnapshot, error) {
	raw, err := c.volumesnapshots().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewVolumeSnapshot(raw), nil
}

////////////////////////////////////////////////////////////////////////////////
// Watch*()
//
// NOTE(mattm): Informers are a more robust layer than raw watchers. The watch
// stream can close; informers resume and pick up where they left off. However,
// I had issues with informers and fine-grained RBAC.

func (c Client) Watch(
	ctx context.Context,
	typ string,
	w func(context.Context, metav1.ListOptions) (watch.Interface, error),
	opts []ListOpt,
	h func(watch.Event) (Object, bool),
) error {
	resourceVersion := ""
	opts = append(opts, ListAllowWatchBookmarks(true))
	for {
		lopts := ListOptions(append(opts, ListResourceVersion(resourceVersion, ""))...)
		watcher, err := w(ctx, lopts)
		if err != nil {
			return err
		}
		for keepwatch := true; keepwatch; {
			select {
			case <-ctx.Done():
				c.LogWarn("Watch%s(): Context cancelled, stopping watcher.", typ)
				watcher.Stop()
				return nil
			case ev, ok := <-watcher.ResultChan():
				if !ok {
					c.LogWarn("Watch%s(): ResultChan closed.", typ)
					watcher.Stop()
					keepwatch = false
				} else if ev.Type == watch.Error {
					if status, ok := ev.Object.(*metav1.Status); !ok {
						c.LogErr("Watch%s(): Received [%v] without a status.", typ, ev.Type)
					} else if status.Code != http.StatusGone {
						c.LogErr("Watch%s(): Received [%v] with reason:%s code:%d message:%s: <%v>.", typ, ev.Type, status.Reason, status.Code, status.Message, status.Details)
					} else {
						// API docs say to restart the watch with a fresh resourceVersion on 410.
						watcher.Stop()
						keepwatch = false
						resourceVersion = ""
					}
				} else if ev.Type == watch.Bookmark {
					if meta, err := meta.Accessor(ev.Object); err != nil {
						c.LogWarn("Watch%s(): Failed to unmarshal [%v] meta: %v", typ, ev.Type, err)
					} else {
						resourceVersion = meta.GetResourceVersion()
					}
				} else {
					if _, ok := h(ev); !ok {
						c.LogWarn("Watch%s(): Failed to unmarshal [%v] ev: %+v.", typ, ev.Type, ev.Object)
					}
				}
			}
		}
	}
}

func (c Client) WatchNamespaces(ctx context.Context, f func(string, *Namespace), opts ...ListOpt) error {
	return c.Watch(ctx, "Namespaces", c.namespaces().Watch, opts, func(ev watch.Event) (Object, bool) {
		if raw, ok := ev.Object.(*apicorev1.Namespace); !ok {
			return Object{}, false
		} else {
			obj := NewNamespace(raw)
			f(string(ev.Type), obj)
			return obj.Object, true
		}
	})
}

func (c Client) WatchConfigMaps(ctx context.Context, f func(string, *ConfigMap), opts ...ListOpt) error {
	return c.Watch(ctx, "ConfigMaps", c.configmaps().Watch, opts, func(ev watch.Event) (Object, bool) {
		if raw, ok := ev.Object.(*apicorev1.ConfigMap); !ok {
			return Object{}, false
		} else {
			obj := NewConfigMap(raw)
			f(string(ev.Type), obj)
			return obj.Object, true
		}
	})
}

func (c Client) WatchPods(ctx context.Context, f func(string, *Pod), opts ...ListOpt) error {
	return c.Watch(ctx, "Pods", c.pods().Watch, opts, func(ev watch.Event) (Object, bool) {
		if raw, ok := ev.Object.(*apicorev1.Pod); !ok {
			return Object{}, false
		} else {
			obj := NewPod(raw)
			f(string(ev.Type), obj)
			return obj.Object, true
		}
	})
}

func (c Client) WatchSecrets(ctx context.Context, f func(string, *Secret), opts ...ListOpt) error {
	return c.Watch(ctx, "Secrets", c.secrets().Watch, opts, func(ev watch.Event) (Object, bool) {
		if raw, ok := ev.Object.(*apicorev1.Secret); !ok {
			return Object{}, false
		} else {
			obj := NewSecret(raw)
			f(string(ev.Type), obj)
			return obj.Object, true
		}
	})
}

////////////////////////////////////////////////////////////////////////////////
// Create*()

func (c Client) CreateNamespace(ctx context.Context, cm *Namespace) (*Namespace, error) {
	ns2, err := c.namespaces().Create(ctx, cm.Raw(), CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewNamespace(ns2), nil
}

func (c Client) CreateConfigMap(ctx context.Context, cm *ConfigMap) (*ConfigMap, error) {
	cm2, err := c.configmaps().Create(ctx, cm.Raw(), CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewConfigMap(cm2), nil
}

func (c Client) CreatePVC(ctx context.Context, pvc *PVC) (*PVC, error) {
	pvc2, err := c.pvcs().Create(ctx, pvc.Raw(), CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewPVC(pvc2), nil
}

func (c Client) CreateSecret(ctx context.Context, s *Secret) (*Secret, error) {
	s2, err := c.secrets().Create(ctx, s.Raw(), CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewSecret(s2), nil
}

func (c Client) CreateService(ctx context.Context, s *Service) (*Service, error) {
	s2, err := c.services().Create(ctx, s.Raw(), CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewService(s2), nil
}

func (c Client) CreateServiceAccount(ctx context.Context, sa *ServiceAccount) (*ServiceAccount, error) {
	sa2, err := c.serviceaccounts().Create(ctx, sa.Raw(), CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewServiceAccount(sa2), nil
}

func (c Client) CreateDeployment(ctx context.Context, d *Deployment) (*Deployment, error) {
	d2, err := c.deployments().Create(ctx, d.Raw(), CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewDeployment(d2), nil
}

func (c Client) CreateIngress(ctx context.Context, i *Ingress) (*Ingress, error) {
	i2, err := c.ingresses().Create(ctx, i.Raw(), CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewIngress(i2), nil
}

func (c Client) CreateRoleBinding(ctx context.Context, rb *RoleBinding) (*RoleBinding, error) {
	rb2, err := c.rolebindings().Create(ctx, rb.Raw(), CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewRoleBinding(rb2), nil
}

////////////////////////////////////////////////////////////////////////////////
// Update*()

func (c Client) UpdateNamespace(ctx context.Context, cm *Namespace) (*Namespace, error) {
	ns2, err := c.namespaces().Update(ctx, cm.Raw(), UpdateOptions())
	if err != nil {
		return nil, err
	}
	return NewNamespace(ns2), nil
}

func (c Client) UpdateConfigMap(ctx context.Context, cm *ConfigMap) (*ConfigMap, error) {
	cm2, err := c.configmaps().Update(ctx, cm.Raw(), UpdateOptions())
	if err != nil {
		return nil, err
	}
	return NewConfigMap(cm2), nil
}

func (c Client) UpdatePVC(ctx context.Context, pvc *PVC) (*PVC, error) {
	pvc2, err := c.pvcs().Update(ctx, pvc.Raw(), UpdateOptions())
	if err != nil {
		return nil, err
	}
	return NewPVC(pvc2), nil
}

func (c Client) UpdateSecret(ctx context.Context, sec *Secret) (*Secret, error) {
	sec2, err := c.secrets().Update(ctx, sec.Raw(), UpdateOptions())
	if err != nil {
		return nil, err
	}
	return NewSecret(sec2), nil
}

func (c Client) UpdateService(ctx context.Context, s *Service) (*Service, error) {
	s2, err := c.services().Update(ctx, s.Raw(), UpdateOptions())
	if err != nil {
		return nil, err
	}
	return NewService(s2), nil
}

func (c Client) UpdateServiceAccount(ctx context.Context, sa *ServiceAccount) (*ServiceAccount, error) {
	sa2, err := c.serviceaccounts().Update(ctx, sa.Raw(), UpdateOptions())
	if err != nil {
		return nil, err
	}
	return NewServiceAccount(sa2), nil
}

func (c Client) UpdateOrCreateServiceAccount(ctx context.Context, sa *ServiceAccount) (*ServiceAccount, error) {
	if cur, err := c.GetServiceAccount(ctx, sa.Name()); err == nil {
		sa.Raw().Secrets = append(cur.Raw().Secrets, sa.Raw().Secrets...)
		return c.UpdateServiceAccount(ctx, sa)
	} else if IsNotFound(err) {
		return c.CreateServiceAccount(ctx, sa)
	} else {
		return nil, err
	}
}

func (c Client) UpdateDeployment(ctx context.Context, d *Deployment) (*Deployment, error) {
	d2, err := c.deployments().Update(ctx, d.Raw(), UpdateOptions())
	if err != nil {
		return nil, err
	}
	return NewDeployment(d2), nil
}

func (c Client) UpdateRoleBinding(ctx context.Context, rb *RoleBinding) (*RoleBinding, error) {
	rb2, err := c.rolebindings().Update(ctx, rb.Raw(), UpdateOptions())
	if err != nil {
		return nil, err
	}
	return NewRoleBinding(rb2), nil
}

func (c Client) UpdateOrCreateRoleBinding(ctx context.Context, rb *RoleBinding) (*RoleBinding, error) {
	rb2, err := c.UpdateRoleBinding(ctx, rb)
	if IsNotFound(err) {
		return c.CreateRoleBinding(ctx, rb)
	}
	return rb2, err
}

func (c Client) UpdateIngress(ctx context.Context, i *Ingress) (*Ingress, error) {
	i2, err := c.ingresses().Update(ctx, i.Raw(), UpdateOptions())
	if err != nil {
		return nil, err
	}
	return NewIngress(i2), nil
}

////////////////////////////////////////////////////////////////////////////////
// Replace*()
//
// Replace*() does a Create*() or Update*() depending on whether the object has
// a metadata.uid.

func (c Client) ReplaceNamespace(ctx context.Context, obj *Namespace) (*Namespace, error) {
	if obj.HasUID() {
		return c.UpdateNamespace(ctx, obj)
	} else {
		return c.CreateNamespace(ctx, obj)
	}
}

func (c Client) ReplaceConfigMap(ctx context.Context, obj *ConfigMap) (*ConfigMap, error) {
	if obj.HasUID() {
		return c.UpdateConfigMap(ctx, obj)
	} else {
		return c.CreateConfigMap(ctx, obj)
	}
}

func (c Client) ReplacePVC(ctx context.Context, obj *PVC) (*PVC, error) {
	if obj.HasUID() {
		return c.UpdatePVC(ctx, obj)
	} else {
		return c.CreatePVC(ctx, obj)
	}
}

func (c Client) ReplaceSecret(ctx context.Context, obj *Secret) (*Secret, error) {
	if obj.HasUID() {
		return c.UpdateSecret(ctx, obj)
	} else {
		return c.CreateSecret(ctx, obj)
	}
}

func (c Client) ReplaceService(ctx context.Context, obj *Service) (*Service, error) {
	if obj.HasUID() {
		return c.UpdateService(ctx, obj)
	} else {
		return c.CreateService(ctx, obj)
	}
}

func (c Client) ReplaceServiceAccount(ctx context.Context, obj *ServiceAccount) (*ServiceAccount, error) {
	if obj.HasUID() {
		return c.UpdateServiceAccount(ctx, obj)
	} else {
		return c.CreateServiceAccount(ctx, obj)
	}
}

func (c Client) ReplaceDeployment(ctx context.Context, obj *Deployment) (*Deployment, error) {
	if obj.HasUID() {
		return c.UpdateDeployment(ctx, obj)
	} else {
		return c.CreateDeployment(ctx, obj)
	}
}

func (c Client) ReplaceIngress(ctx context.Context, obj *Ingress) (*Ingress, error) {
	if obj.HasUID() {
		return c.UpdateIngress(ctx, obj)
	} else {
		return c.CreateIngress(ctx, obj)
	}
}

func (c Client) ReplaceRoleBinding(ctx context.Context, obj *RoleBinding) (*RoleBinding, error) {
	if obj.HasUID() {
		return c.UpdateRoleBinding(ctx, obj)
	} else {
		return c.CreateRoleBinding(ctx, obj)
	}
}

////////////////////////////////////////////////////////////////////////////////
// Patch*()

func (c Client) PatchNamespace(ctx context.Context, name string, pt types.PatchType, data any) (*Namespace, error) {
	json, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	res, err := c.namespaces().Patch(ctx, name, pt, json, PatchOptions())
	if err != nil {
		return nil, err
	}
	return NewNamespace(res), nil
}

func (c Client) PatchConfigMap(ctx context.Context, name string, pt types.PatchType, data any) (*ConfigMap, error) {
	json, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	res, err := c.configmaps().Patch(ctx, name, pt, json, PatchOptions())
	if err != nil {
		return nil, err
	}
	return NewConfigMap(res), nil
}

func (c Client) PatchPVC(ctx context.Context, name string, pt types.PatchType, data any) (*PVC, error) {
	json, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	res, err := c.pvcs().Patch(ctx, name, pt, json, PatchOptions())
	if err != nil {
		return nil, err
	}
	return NewPVC(res), nil
}

func (c Client) PatchSecret(ctx context.Context, name string, pt types.PatchType, data any) (*Secret, error) {
	json, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	res, err := c.secrets().Patch(ctx, name, pt, json, PatchOptions())
	if err != nil {
		return nil, err
	}
	return NewSecret(res), nil
}

func (c Client) PatchService(ctx context.Context, name string, pt types.PatchType, data any) (*Service, error) {
	json, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	res, err := c.services().Patch(ctx, name, pt, json, PatchOptions())
	if err != nil {
		return nil, err
	}
	return NewService(res), nil
}

func (c Client) PatchDeployment(ctx context.Context, name string, data any) (*Deployment, error) {
	json, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	res, err := c.deployments().Patch(ctx, name, types.StrategicMergePatchType, json, PatchOptions())
	if err != nil {
		return nil, err
	}
	return NewDeployment(res), nil
}

func (c Client) PatchIngress(ctx context.Context, name string, pt types.PatchType, data any) (*Ingress, error) {
	json, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	res, err := c.ingresses().Patch(ctx, name, pt, json, PatchOptions())
	if err != nil {
		return nil, err
	}
	return NewIngress(res), nil
}

func (c Client) PatchStatefulSet(ctx context.Context, name string, data any) (*StatefulSet, error) {
	json, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	res, err := c.statefulsets().Patch(ctx, name, types.StrategicMergePatchType, json, PatchOptions())
	if err != nil {
		return nil, err
	}
	return NewStatefulSet(res), nil
}

////////////////////////////////////////////////////////////////////////////////
// Delete*()

func (c Client) DeleteNamespace(ctx context.Context, name string) error {
	c.LogInfo("Deleting Namespace %s...", name)
	return c.namespaces().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeleteConfigMap(ctx context.Context, name string) error {
	c.LogInfo("Deleting ConfigMap %s...", name)
	return c.configmaps().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeletePod(ctx context.Context, name string) error {
	c.LogInfo("Deleting Pod %s...", name)
	return c.pods().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeletePVC(ctx context.Context, name string) error {
	c.LogInfo("Deleting PVC %s...", name)
	return c.pvcs().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeleteSecret(ctx context.Context, name string) error {
	c.LogInfo("Deleting Secret %s...", name)
	return c.secrets().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeleteService(ctx context.Context, name string) error {
	c.LogInfo("Deleting Service %s...", name)
	return c.services().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeleteServiceAccount(ctx context.Context, name string) error {
	c.LogInfo("Deleting ServiceAccount %s...", name)
	return c.serviceaccounts().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeleteDeployment(ctx context.Context, name string) error {
	c.LogInfo("Deleting Deployment %s...", name)
	return c.deployments().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeleteStatefulSet(ctx context.Context, name string) error {
	c.LogInfo("Deleting StatefulSet %s...", name)
	return c.statefulsets().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeleteIngress(ctx context.Context, name string) error {
	c.LogInfo("Deleting Ingress %s...", name)
	return c.ingresses().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeleteRoleBinding(ctx context.Context, name string) error {
	c.LogInfo("Deleting RoleBinding %s...", name)
	return c.rolebindings().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeleteVolumeSnapshot(ctx context.Context, name string) error {
	c.LogInfo("Deleting VolumeSnapshot %s...", name)
	return c.volumesnapshots().Delete(ctx, name, DeleteOptions())
}

////////////////////////////////////////////////////////////////////////////////
// DeleteIf*()

func (c Client) DeleteIfNamespace(ctx context.Context, name string) error {
	if _, err := c.GetNamespace(ctx, name); err == nil {
		return NotFoundOK(c.DeleteNamespace(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting Namespace %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) DeleteIfConfigMap(ctx context.Context, name string) error {
	if _, err := c.GetConfigMap(ctx, name); err == nil {
		return NotFoundOK(c.DeleteConfigMap(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting ConfigMap %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) DeleteIfPod(ctx context.Context, name string) error {
	if _, err := c.GetPod(ctx, name); err == nil {
		return NotFoundOK(c.DeletePod(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting Pod %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) DeleteIfPVC(ctx context.Context, name string) error {
	if _, err := c.GetPVC(ctx, name); err == nil {
		return NotFoundOK(c.DeletePVC(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting PVC %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) DeleteIfSecret(ctx context.Context, name string) error {
	if _, err := c.GetSecret(ctx, name); err == nil {
		return NotFoundOK(c.DeleteSecret(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting Secret %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) DeleteIfService(ctx context.Context, name string) error {
	if _, err := c.GetService(ctx, name); err == nil {
		return NotFoundOK(c.DeleteService(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting Service %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) DeleteIfServiceAccount(ctx context.Context, name string) error {
	if _, err := c.GetServiceAccount(ctx, name); err == nil {
		return NotFoundOK(c.DeleteServiceAccount(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting ServiceAccount %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) DeleteIfDeployment(ctx context.Context, name string) error {
	if _, err := c.GetDeployment(ctx, name); err == nil {
		return NotFoundOK(c.DeleteDeployment(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting Deployment %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) DeleteIfStatefulSet(ctx context.Context, name string) error {
	if _, err := c.GetStatefulSet(ctx, name); err == nil {
		return NotFoundOK(c.DeleteStatefulSet(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting StatefulSet %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) DeleteIfIngress(ctx context.Context, name string) error {
	if _, err := c.GetIngress(ctx, name); err == nil {
		return NotFoundOK(c.DeleteIngress(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting Ingress %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) DeleteIfRoleBinding(ctx context.Context, name string) error {
	if _, err := c.GetRoleBinding(ctx, name); err == nil {
		return NotFoundOK(c.DeleteRoleBinding(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting RoleBinding %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) DeleteIfVolumeSnapshot(ctx context.Context, name string) error {
	if _, err := c.GetVolumeSnapshot(ctx, name); err == nil {
		return NotFoundOK(c.DeleteVolumeSnapshot(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting VolumeSnapshot %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

////////////////////////////////////////////////////////////////////////////////
// Apply*()

func (c Client) ApplyNamespace(ctx context.Context, cfg *NamespaceApplyConfig, opts ...ApplyOpt) (*Namespace, error) {
	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	ns2, err := c.namespaces().Apply(ctx, cfg.NamespaceApplyConfiguration, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewNamespace(ns2), nil
}

func (c Client) ApplyConfigMap(ctx context.Context, cfg *ConfigMapApplyConfig, opts ...ApplyOpt) (*ConfigMap, error) {
	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	resp, err := c.configmaps().Apply(ctx, cfg.ConfigMapApplyConfiguration, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewConfigMap(resp), nil
}

func (c Client) ApplySecret(ctx context.Context, cfg *SecretApplyConfig, opts ...ApplyOpt) (*Secret, error) {
	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	resp, err := c.secrets().Apply(ctx, cfg.SecretApplyConfiguration, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewSecret(resp), nil
}

func (c Client) ApplyService(ctx context.Context, cfg *ServiceApplyConfig, opts ...ApplyOpt) (*Service, error) {
	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	resp, err := c.services().Apply(ctx, cfg.ServiceApplyConfiguration, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewService(resp), nil
}

func (c Client) ApplyServiceAccount(ctx context.Context, cfg *ServiceAccountApplyConfig, opts ...ApplyOpt) (*ServiceAccount, error) {
	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	resp, err := c.serviceaccounts().Apply(ctx, cfg.ServiceAccountApplyConfiguration, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewServiceAccount(resp), nil
}

func (c Client) ApplyPVC(ctx context.Context, cfg *PVCApplyConfig, opts ...ApplyOpt) (*PVC, error) {
	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	resp, err := c.pvcs().Apply(ctx, cfg.PersistentVolumeClaimApplyConfiguration, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewPVC(resp), nil
}

func (c Client) ApplyPod(ctx context.Context, cfg *PodApplyConfig, opts ...ApplyOpt) (*Pod, error) {
	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	resp, err := c.pods().Apply(ctx, cfg.PodApplyConfiguration, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewPod(resp), nil
}

func (c Client) ApplyDeployment(ctx context.Context, cfg *DeploymentApplyConfig, opts ...ApplyOpt) (*Deployment, error) {
	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	resp, err := c.deployments().Apply(ctx, cfg.DeploymentApplyConfiguration, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewDeployment(resp), nil
}

func (c Client) ApplyStatefulSet(ctx context.Context, cfg *StatefulSetApplyConfig, opts ...ApplyOpt) (*StatefulSet, error) {
	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	resp, err := c.statefulsets().Apply(ctx, cfg.StatefulSetApplyConfiguration, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewStatefulSet(resp), nil
}

func (c Client) ApplyRoleBinding(ctx context.Context, cfg *RoleBindingApplyConfig, opts ...ApplyOpt) (*RoleBinding, error) {
	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	resp, err := c.rolebindings().Apply(ctx, cfg.RoleBindingApplyConfiguration, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewRoleBinding(resp), nil
}

func (c Client) ApplyVolumeSnapshot(ctx context.Context, o *VolumeSnapshot, opts ...ApplyOpt) (*VolumeSnapshot, error) {
	u, err := o.Unstructured()
	if err != nil {
		return nil, err
	}

	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	u2, err := c.volumesnapshots_dynamic().Apply(ctx, u.GetName(), u, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	o2 := NewVolumeSnapshot(nil)
	if err := o2.FromUnstructured(u2); err != nil {
		return nil, err
	}
	return o2, nil
}

////////////////////////////////////////////////////////////////////////////////
//
// Additional Object-specific methods
//

// Events //////////////////////////////////////////////////////////////////////

func (c Client) ListEventsFor(ctx context.Context, kind, name string) (EventList, error) {
	return c.ListEvents(ctx, ListFieldSelector(
		fields.AndSelectors(
			fields.OneTermEqualSelector("involvedObject.kind", kind),
			fields.OneTermEqualSelector("involvedObject.name", name),
		).String(),
	))
}

// Pod /////////////////////////////////////////////////////////////////////////

func (c Client) ExecPodNative(ctx context.Context, name string, stdin, tty bool, cmd ...string) error {
	req := c.corev1().RESTClient().
		Post().
		Namespace(c.Namespace()).
		Resource("pods").
		Name(name).
		SubResource("exec").
		VersionedParams(&apicorev1.PodExecOptions{
			Command: cmd,
			Stdin:   stdin,
			Stdout:  true,
			Stderr:  true,
			TTY:     tty,
		}, scheme.ParameterCodec)
	exec, err := remotecommand.NewSPDYExecutor(c.Config().REST(), "POST", req.URL())
	if err != nil {
		return err
	}
	in := os.Stdin
	if !tty {
		in = nil
	}
	if err := exec.StreamWithContext(ctx, remotecommand.StreamOptions{
		Stdin:  in,
		Stdout: os.Stdout,
		Stderr: os.Stderr,
		Tty:    tty,
	}); err != nil {
		return err
	}
	return nil
}

// ExecPod calls `kubectl exec` with stdio connected to the system.
func (c Client) ExecPod(ctx context.Context, name, cname string, stdin, tty bool, cmd ...string) error {
	args := []string{}
	args = append(args, "--context", c.Config().Context())
	args = append(args, "exec", name)
	args = append(args, "--quiet")
	if cname != "" {
		args = append(args, "-c", cname)
	}
	if stdin {
		args = append(args, "-i")
	}
	if tty {
		args = append(args, "-t")
	}
	args = append(args, "--")
	args = append(args, cmd...)
	proc := exec.CommandContext(ctx, "kubectl", args...)
	if stdin {
		proc.Stdin = os.Stdin
	}
	proc.Stdout = os.Stdout
	proc.Stderr = os.Stderr
	return proc.Run()
}

// ExecPodIO calls `kubectl exec` with stdio connected to byte buffers so they can be managed pragmatically.
func (c Client) ExecPodIO(ctx context.Context, name, cname string, in []byte, cmd ...string) ([]byte, []byte, error) {
	args := []string{}
	args = append(args, "--context", c.Config().Context())
	args = append(args, "exec", name)
	args = append(args, "--quiet")
	if cname != "" {
		args = append(args, "-c", cname)
	}
	if len(in) > 0 {
		args = append(args, "-i")
	}
	args = append(args, "--")
	args = append(args, cmd...)
	proc := exec.CommandContext(ctx, "kubectl", args...)
	if len(in) > 0 {
		proc.Stdin = bytes.NewBuffer(in)
	}
	stdout, stderr := bytes.Buffer{}, bytes.Buffer{}
	proc.Stdout, proc.Stderr = &stdout, &stderr
	err := proc.Run()
	return stdout.Bytes(), stderr.Bytes(), err
}

// PodLogs returns an iterator which streams log lines or an error. The lines are not newline-terminated. If an error is returned,
// it will be the last item yielded by the iterator.
func (c Client) PodLogs(ctx context.Context, name string, opts ...PodLogOpt) iter.Seq2[string, error] {
	return func(yield func(string, error) bool) {
		req := c.pods().GetLogs(name, PodLogOptions(opts...))
		r, err := req.Stream(ctx)
		if err != nil {
			yield("", err)
			return
		}
		defer func() {
			if err := r.Close(); err != nil {
				c.LogWarn("pod/%s: Error closing log stream: %v.", name, err)
			}
		}()

		// The K8s Logs API streams continuously. We wrap the stream in a buffered reader,
		// blocking on one line at a time.
		scanner := bufio.NewScanner(r)
		for scanner.Scan() {
			line := scanner.Text()
			if cont := yield(line, nil); !cont {
				return
			}
		}
		if err := scanner.Err(); err != nil {
			yield("", err)
		}
	}
}

// Deployment //////////////////////////////////////////////////////////////////

func (c Client) GetDeploymentPod(ctx context.Context, name string) (*Pod, error) {
	d, err := c.GetDeployment(ctx, name)
	if err != nil {
		return nil, err
	}
	return d.GetPod(ctx, &c)
}

func (c Client) GetStatefulSetPod(ctx context.Context, name string) (*Pod, error) {
	o, err := c.GetStatefulSet(ctx, name)
	if err != nil {
		return nil, err
	}
	return o.GetPod(ctx, &c)
}

func (c Client) PatchDeploymentReplicas(ctx context.Context, name string, r int32) (*Deployment, error) {
	d := struct {
		Spec struct {
			Replicas int32 `json:"replicas"`
		} `json:"spec"`
	}{}
	d.Spec.Replicas = r
	return c.PatchDeployment(ctx, name, d)
}

func (c Client) PatchStatefulSetReplicas(ctx context.Context, name string, r int32) (*StatefulSet, error) {
	s := struct {
		Spec struct {
			Replicas int32 `json:"replicas"`
		} `json:"spec"`
	}{}
	s.Spec.Replicas = r
	return c.PatchStatefulSet(ctx, name, s)
}

// PatchDeploymentImage updates the image of the specified container (`cname`) in a Deployment.
func (c Client) PatchDeploymentImage(ctx context.Context, name, cname string, image string) (*Deployment, error) {
	type container struct {
		Name  string `json:"name"`
		Image string `json:"image"`
	}
	d := struct {
		Spec struct {
			Template struct {
				Spec struct {
					Containers []container `json:"containers"`
				} `json:"spec"`
			} `json:"template"`
		} `json:"spec"`
	}{}
	d.Spec.Template.Spec.Containers = []container{{
		Name:  cname,
		Image: image,
	}}
	return c.PatchDeployment(ctx, name, d)
}

// Service /////////////////////////////////////////////////////////////////////

func (c Client) ServiceGET(name string) *rest.Request {
	return c.corev1().RESTClient().Get().
		Namespace(c.Namespace()).
		Resource("services").
		Name(name).
		SubResource("proxy")
}

func (c Client) ServicePOST(name string) *rest.Request {
	return c.corev1().RESTClient().Post().
		Namespace(c.Namespace()).
		Resource("services").
		Name(name).
		SubResource("proxy")
}

// ServiceAccount //////////////////////////////////////////////////////////////

func (c Client) CreateToken(ctx context.Context, name string, exp int64) (*TokenRequest, error) {
	req := NewTokenRequestExp(exp)
	resp, err := c.serviceaccounts().CreateToken(ctx, name, req.Raw(), CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewTokenRequest(resp), nil
}

// AsServiceAccount returns a new Client authenticated as the given service account from the given namespace. The
// new client's default namespace will be the same as the original (which may be different than the namespace that
// the service account is in).
func (c Client) AsServiceAccount(ctx context.Context, namespace, name string, exp int64) (*Client, error) {
	if tr, err := c.InNamespace(namespace).CreateToken(ctx, name, exp); err != nil {
		return nil, err
	} else if cfg, err := c.Config().WithToken(tr.Token()); err != nil {
		return nil, err
	} else if cli, err := NewFromConfig(cfg); err != nil {
		return nil, err
	} else {
		cli.ns = c.ns
		cli.defaultFieldManager = c.defaultFieldManager
		return cli, nil
	}
}

// AsServiceAccountStaticToken is like `AsServiceAccount()` but uses statically created token `Secrets` instead
// of dynamic `TokenRequest` tokens.
func (c Client) AsServiceAccountStaticToken(ctx context.Context, namespace, name string) (*Client, error) {
	k := c.InNamespace(namespace)
	if sa, err := k.GetServiceAccount(ctx, name); err != nil {
		return nil, err
	} else if sec, err := sa.TokenSecret(ctx, &k); err != nil {
		return nil, err
	} else if token, err := sec.ServiceAccountToken(); err != nil {
		return nil, err
	} else if cfg, err := c.Config().WithToken(token); err != nil {
		return nil, err
	} else if cli, err := NewFromConfig(cfg); err != nil {
		return nil, err
	} else {
		cli.ns = c.ns
		cli.defaultFieldManager = c.defaultFieldManager
		return cli, nil
	}
}

// SelfSubjectReview ///////////////////////////////////////////////////////////

func (c Client) WhoAmI(ctx context.Context) (*SelfSubjectReview, error) {
	req, err := c.selfsubjectreviews().Create(ctx, &apiauthv1.SelfSubjectReview{}, CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewSelfSubjectReview(req), nil
}

// Node ////////////////////////////////////////////////////////////////////////
func (c Client) ListGPUNodes(ctx context.Context, opts ...ListOpt) ([]*Node, error) {
	nodes, err := c.ListNodes(ctx, opts...)
	if err != nil {
		return nil, err
	}
	ret := []*Node{}
	for _, n := range nodes {
		if n.GPUCapacity() > 0 {
			ret = append(ret, n)
		}
	}
	return ret, nil
}

// ListNodesWithUsage extends ListNodes() to also tally current resource usage. To tally resource usage
// we need to list all pods in the cluster so those are returned too in case they're needed.
func (c Client) ListNodesWithUsage(ctx context.Context, opts ...ListOpt) ([]*Node, []*Pod, error) {
	var nodes []*Node
	var allpods []*Pod

	grp, gctx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		var err error
		nodes, err = c.ListNodes(gctx)
		return err
	})
	grp.Go(func() error {
		var err error
		allpods, err = c.InNamespace("").ListPods(gctx)
		return err
	})
	if err := grp.Wait(); err != nil {
		return nil, nil, err
	}
	CountResourceUsage(nodes, allpods)
	return nodes, allpods, nil
}
