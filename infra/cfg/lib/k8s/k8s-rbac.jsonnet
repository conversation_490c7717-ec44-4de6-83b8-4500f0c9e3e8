local k8s_base = import 'k8s-base.jsonnet';

k8s_base + {
  local g = self,

  LIST_VERBS:: g.BaseRole.Rule.LIST_VERBSE,
  READ_VERBS:: g.BaseRole.Rule.READ_VERBS,
  WRITE_VERBS_NO_DELETE:: g.BaseRole.Rule.WRITE_VERBS_NO_DELETE,
  WRITE_VERBS_NO_COLLECTION:: g.BaseRole.Rule.WRITE_VERBS_NO_COLLECTION,
  WRITE_VERBS:: g.BaseRole.Rule.WRITE_VERBS,

  BaseRole:: {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    rules+: [],
    Rule:: {
      LIST:: 'list',
      WATCH:: 'watch',
      GET:: 'get',
      CREATE:: 'create',
      UPDATE:: 'update',
      PATCH:: 'patch',
      DELETE:: 'delete',
      DELETECOLLECTION:: 'deletecollection',

      LIST_VERBS:: [self.LIST, self.WATCH],
      READ_VERBS:: [self.GET] + self.LIST_VERBS,
      WRITE_VERBS_NO_DELETE:: [self.CREATE, self.UPDATE, self.PATCH],
      WRITE_VERBS_NO_COLLECTION:: self.WRITE_VERBS_NO_DELETE + [self.DELETE],
      WRITE_VERBS:: self.WRITE_VERBS_NO_COLLECTION + [self.DELETECOLLECTION],
    },
  },

  Role:: $.Object + $.BaseRole + {
    kind: 'Role',
  },

  ClusterRole:: $.ClusterObject + $.BaseRole + {
    kind: 'ClusterRole',
  },

  BaseRoleBinding:: {
    role_name:: error 'The "role_name" field must be set.',
    role_ref_kind:: error 'The "role_ref_kind" must be set.',
    users+:: [],  // A convenience for setting a list of User subjects.
    groups+:: [],  // A convenience for setting a list of Group subjects.
    sas+:: [],  // A convenience for setting a list of ServiceAccount subjects.

    local s = self,
    apiVersion: 'rbac.authorization.k8s.io/v1',

    roleRef+: {
      apiGroup: 'rbac.authorization.k8s.io',
      kind: s.role_ref_kind,
      name: s.role_name,
    },

    subjects+: s.user_subjects + s.group_subjects + s.sa_subjects,
    user_subjects+:: [
      {
        kind: 'User',
        name: u,
      }
      for u in s.users
    ],
    group_subjects+:: [
      {
        kind: 'Group',
        name: g,
      }
      for g in s.groups
    ],
    sa_subjects+:: [
      {
        kind: 'ServiceAccount',
        name: sa.metadata.name,
        namespace: sa.metadata.namespace,
      }
      for sa in s.sas
    ],
  },

  RoleBinding:: $.Object + $.BaseRoleBinding + {
    kind: 'RoleBinding',
    role_ref_kind:: 'Role',
  },

  ClusterRoleBinding:: $.ClusterObject + $.BaseRoleBinding + {
    kind: 'ClusterRoleBinding',
    role_ref_kind:: 'ClusterRole',
  },
}
