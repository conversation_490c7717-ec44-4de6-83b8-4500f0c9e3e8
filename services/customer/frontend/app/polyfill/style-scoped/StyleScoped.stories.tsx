import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

type ScopedStoryArgs = {
  color: string;
  showStyle: boolean;
};

function ScopedStory({ color, showStyle }: ScopedStoryArgs) {
  return (
    <div style={{ margin: 20 }}>
      <h1 style={{ fontSize: "1.5em", marginBottom: 20 }}>Scoped‐CSS demo</h1>
      <p>
        Use the Storybook controls to change the <code>color</code> or toggle
        the scoped style tag on/off.
      </p>

      <div
        className="wrapper"
        style={{ padding: 10, border: "1px solid #ddd", marginBottom: 20 }}
      >
        {showStyle && (
          <style scoped>
            {`
              :scope .styled-class {
                color: ${color};
                padding: 0.5em;
                border: 2px dashed ${color};
              }
            `}
          </style>
        )}

        <div className="styled-class">
          {showStyle
            ? "This text is styled by a <style scoped>"
            : "This text is not styled because there is no style tag"}
        </div>
        <div className="unstyled">This text is not styled.</div>
      </div>
      <div className="styled-class">
        This text is not styled because it&apos;s outside the{" "}
        <code>.wrapper</code> element.
      </div>
    </div>
  );
}

type Story = StoryObj<typeof ScopedStory>;

const meta = {
  title: "Polyfill/ScopedStyles",
  component: (args) => <ScopedStory key={JSON.stringify(args)} {...args} />,
  argTypes: {
    color: { control: "color" },
    showStyle: { control: "boolean" },
  },
  args: {
    color: "#0070f3",
    showStyle: true,
  },
} satisfies Meta<typeof ScopedStory>;

export default meta;

export const Default: Story = {
  args: {
    color: "#0070f3",
    showStyle: true,
  },
  render: (args) => <ScopedStory key={JSON.stringify(args)} {...args} />,
};
