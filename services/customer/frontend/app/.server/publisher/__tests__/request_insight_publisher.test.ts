import { beforeEach, describe, expect, it, vi } from "vitest";
import { Timestamp } from "@bufbuild/protobuf";

// Test constants
const TEST_CONFIG = {
  PROJECT_ID: "test-project",
  TOPIC_NAME: "test-topic",
  REQUEST_ID: "test-request-id",
  TENANT_ID: "test-tenant-id",
  TENANT_NAME: "test-tenant-name",
  MESSAGE_ID: "mock-msg-id",
} as const;

// Mock fetch for API calls
const mockFetch = vi.fn().mockImplementation(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({ messageIds: [TEST_CONFIG.MESSAGE_ID] }),
  }),
);
global.fetch = mockFetch;

import { RequestInsightPublisher } from "../request-insight-publisher";
import {
  RequestInsightMessage,
  RequestEvent,
  TenantInfo,
} from "~services/request_insight/request_insight_pb";

describe("RequestInsightPublisher", () => {
  let publisher: RequestInsightPublisher;

  beforeEach(() => {
    vi.clearAllMocks();
    publisher = new RequestInsightPublisher(
      TEST_CONFIG.PROJECT_ID,
      TEST_CONFIG.TOPIC_NAME,
    );
  });

  it("should initialize with correct project and topic", () => {
    expect(publisher).toBeDefined();
  });

  it("should successfully publish a request event using API", async () => {
    // Arrange
    const tenantInfo = new TenantInfo({
      tenantId: TEST_CONFIG.TENANT_ID,
      tenantName: TEST_CONFIG.TENANT_NAME,
    });
    const event = new RequestEvent({
      time: Timestamp.fromDate(new Date()),
    });

    // Act
    await publisher.publishRequestEvent(
      TEST_CONFIG.REQUEST_ID,
      tenantInfo,
      event,
    );

    // Assert
    expect(mockFetch).toHaveBeenCalledTimes(1);
    expect(mockFetch).toHaveBeenCalledWith(
      `https://pubsub.googleapis.com/v1/projects/${TEST_CONFIG.PROJECT_ID}/topics/${TEST_CONFIG.TOPIC_NAME}:publish`,
      expect.objectContaining({
        method: "POST",
        headers: expect.objectContaining({
          "Content-Type": "application/json",
        }),
      }),
    );

    // Verify the message structure
    const callArgs = mockFetch.mock.calls[0][1];
    const body = JSON.parse(callArgs.body);
    expect(body).toHaveProperty("messages");
    expect(body.messages).toHaveLength(1);
    expect(body.messages[0]).toHaveProperty("data");

    // Decode and verify the message content
    const decodedData = Buffer.from(body.messages[0].data, "base64");
    const decodedMessage = RequestInsightMessage.fromBinary(decodedData);
    expect(decodedMessage.message.case).toBe("updateRequestInfoRequest");
    if (decodedMessage.message.case === "updateRequestInfoRequest") {
      const request = decodedMessage.message.value;
      expect(request.requestId).toBe(TEST_CONFIG.REQUEST_ID);
      expect(request.tenantInfo?.tenantId).toBe(tenantInfo.tenantId);
      expect(request.tenantInfo?.tenantName).toBe(tenantInfo.tenantName);
      expect(request.events).toHaveLength(1);
      expect(request.events[0]).toEqual(event);
    }
  });

  it("should retry on API failure", async () => {
    // Arrange
    const tenantInfo = new TenantInfo({
      tenantId: TEST_CONFIG.TENANT_ID,
      tenantName: TEST_CONFIG.TENANT_NAME,
    });
    const event = new RequestEvent({
      time: Timestamp.fromDate(new Date()),
    });

    // Mock fetch to fail once then succeed
    const mockFetch = vi
      .fn()
      .mockRejectedValueOnce(new Error("API failure"))
      .mockImplementationOnce(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve({ messageIds: [TEST_CONFIG.MESSAGE_ID] }),
        }),
      );
    global.fetch = mockFetch;

    // Act
    await publisher.publishRequestEvent(
      TEST_CONFIG.REQUEST_ID,
      tenantInfo,
      event,
    );

    // Assert
    expect(mockFetch).toHaveBeenCalledTimes(2);
  });

  it("should throw error after max retries", async () => {
    // Arrange
    const tenantInfo = new TenantInfo({
      tenantId: TEST_CONFIG.TENANT_ID,
      tenantName: TEST_CONFIG.TENANT_NAME,
    });
    const event = new RequestEvent({
      time: Timestamp.fromDate(new Date()),
    });

    // Mock fetch to always fail
    const mockFetch = vi.fn().mockRejectedValue(new Error("API failure"));
    global.fetch = mockFetch;

    // Mock setTimeout to make it run immediately
    const originalSetTimeout = global.setTimeout;
    global.setTimeout = vi.fn((callback) => {
      return originalSetTimeout(callback, 0);
    }) as unknown as typeof setTimeout;

    try {
      // Act & Assert
      await expect(
        publisher.publishRequestEvent(
          TEST_CONFIG.REQUEST_ID,
          tenantInfo,
          event,
        ),
      ).rejects.toThrow("API failure");

      expect(mockFetch).toHaveBeenCalledTimes(11);
    } finally {
      // Restore original setTimeout
      global.setTimeout = originalSetTimeout;
    }
  }, 10000);
});
