import fs from "fs";
import { logger } from "@augment-internal/logging";
import type { GrpcTransportOptions } from "@connectrpc/connect-node";
import type { AugmentPlanType } from "app/schemas/augment-plan";
import { PlansConfig, type PlanConfig } from "app/schemas/plan";
import { assertUnreachable } from "@augment-internal/ts-utils/type";
import { ENDPOINTS, CONFIG_FILES, SECRETS, ENV_VARS } from "./config-env";
import { requestIdInterceptor } from "./grpc/request-id-interceptor";
/**
 * Thes are special the have other processing to do than copy from ENV.
 */
type SpecialConfig = {
  CLOUD_DOMAIN_SUFFIXES: Record<string, string>;
  CENTRAL_CLIENT_CERT: Buffer | undefined;
  CENTRAL_CLIENT_KEY: Buffer | undefined;
  CENTRAL_CA_CERT: Buffer | undefined;
  orbPlansConfig: PlansConfig;
  IS_MTLS_ENABLED: boolean;
  rejectUnauthorized: boolean;
};
export const REQUIRED_CONFIG = [
  "TENANT_WATCHER_ENDPOINT",
  "AUTH_CENTRAL_ENDPOINT",
  "AUTH_ENDPOINT",
  "TOKEN_EXCHANGE_ENDPOINT",
  "RI_ANALYTICS_ENDPOINT_TEMPLATE",
  "SLACK_BOT_PROCESSOR_ENDPOINT_TEMPLATE",
  "GLEAN_ENDPOINT_TEMPLATE",
  "GITHUB_PROCESSOR_ENDPOINT_TEMPLATE",
  "SHARE_ENDPOINT_TEMPLATE",
  "NOTION_ENDPOINT_TEMPLATE",
  "ATLASSIAN_ENDPOINT_TEMPLATE",
  "LINEAR_ENDPOINT_TEMPLATE",
  "SUPABASE_ENDPOINT_TEMPLATE",
  "CLOUD_DOMAIN_SUFFIXES",
  "REQUEST_INSIGHT_PUBLISHER_CONFIG_FILE",
  "ORB_PLANS_CONFIG",
] as const;
/**
 * These are defaulted values.
 */
export const DEFAULT_CONFIG = {
  AUTH_CALLBACK_URL: "http://localhost:5173/auth/callback",
  rejectUnauthorized: true,
} as const;

const keys = <T extends object>(o: T) => Object.keys(o) as (keyof T)[];
const DEFAULT_KEYS = keys(DEFAULT_CONFIG);

const ALL_KEYS = [
  ...ENDPOINTS,
  ...CONFIG_FILES,
  ...SECRETS,
  ...ENV_VARS,
  ...DEFAULT_KEYS,
] as const;

type VARS = (typeof ALL_KEYS)[number];

type RequiredKeys = (typeof REQUIRED_CONFIG)[number];
type AllConfig = Omit<{ [K in VARS]: string }, keyof SpecialConfig>;
type OptionalKeys = Exclude<VARS, RequiredKeys>;

type EndpointConfig = Required<
  Pick<AllConfig, RequiredKeys & keyof AllConfig>
> &
  Pick<AllConfig, OptionalKeys & keyof AllConfig>;

/**
 * These are not set via env variables directly,
 * but are derived from settings.   So they are available,
 * but will not be read directlry from the ENV.
 */
export const DERIVED_CONFIG = [
  "REQUEST_INSIGHT_PROJECT_ID",
  "REQUEST_INSIGHT_TOPIC_NAME",
] as const;

type DerivedConfig = Record<(typeof DERIVED_CONFIG)[number], string>;

type ConfigFnI = {
  format: typeof format;
  createGrpcTransportOptions(endpoint: string): GrpcTransportOptions;
  orbPlanIdToAugmentPlanType(orbPlanId: string): AugmentPlanType;
};

type ConfigI = EndpointConfig & SpecialConfig & DerivedConfig & ConfigFnI;

/**
 * This prevents initialization, during testing.
 *
 * @param env
 * @returns
 */
function newConfigPreInit(
  env: Record<string, string | undefined> = process.env,
): ConfigI {
  const ret: Partial<EndpointConfig> = {};

  for (const key of ALL_KEYS) {
    (ret as any)[key] = env[key] ?? (DEFAULT_CONFIG as any)[key] ?? "";
  }
  const config = ret as ConfigI;
  config.IS_MTLS_ENABLED = env.IS_MTLS_ENABLED === "true";
  //REJECT_UNAUTHORIZED is likely undefined, in which case we want it enabled. Only
  // when it is explicitly set to false do we want to disable it.
  config.rejectUnauthorized = env.REJECT_UNAUTHORIZED !== "false";

  config.orbPlanIdToAugmentPlanType = (orbPlanId: string): AugmentPlanType => {
    const plan = findPlanById(config.orbPlansConfig, orbPlanId);
    if (!plan) {
      return "unknown";
    }
    const planType = plan.features.plan_type;
    switch (planType) {
      case "community":
        return "community";
      case "trial":
        return "trial";
      case "paid":
        return "paid";
      default:
        return assertUnreachable(
          planType,
          `Plan type not supported: ${planType}`,
        );
    }
  };

  config.format = format;

  config.createGrpcTransportOptions = (
    endpoint: string,
  ): GrpcTransportOptions => {
    logger.info(`Creating grpc transport: ${endpoint}`);
    return {
      baseUrl: `${config.IS_MTLS_ENABLED ? "https" : "http"}://${endpoint}`,
      httpVersion: "2",
      nodeOptions: {
        rejectUnauthorized: config.rejectUnauthorized,
        ca: config.CENTRAL_CA_CERT,
        key: config.CENTRAL_CLIENT_KEY,
        cert: config.CENTRAL_CLIENT_CERT,
      },
      interceptors: [requestIdInterceptor],
    };
  };
  return config;
}

function newConfig(
  env: Record<string, string | undefined> = process.env,
): ConfigI {
  const config = newConfigPreInit(env);
  const missingFiles = CONFIG_FILES.filter((k) =>
    !config[k]
      ? `missing env path "${k}"`
      : fs.existsSync(config[k])
        ? false
        : `missing file ${config[k]}`,
  );
  if (missingFiles.length > 0) {
    throw new Error(`Missing config files: ${missingFiles.join(", ")}`);
  }
  const authSecretsContent = fs.readFileSync(config.AUTH_SECRETS_PATH!, "utf8");
  const authSecrets = JSON.parse(authSecretsContent);
  config.AUTH_CLIENT_ID = authSecrets.client_id;
  config.AUTH_CLIENT_SECRET = authSecrets.client_secret;
  config.SESSION_SECRET = authSecrets.session_secret;

  config.STRIPE_SECRET = fs
    .readFileSync(config.STRIPE_SECRET_PATH!, "utf8")
    .trim();

  // Read LaunchDarkly SDK key from file if path is provided
  config.FEATURE_FLAGS_SDK_KEY = fs
    .readFileSync(config.FEATURE_FLAGS_SDK_KEY_PATH!, "utf8")
    .trim();

  if (config.IS_MTLS_ENABLED) {
    config.CENTRAL_CLIENT_CERT = fs.readFileSync(
      env.CENTRAL_CLIENT_CERT_PATH || "/central-client-certs/tls.crt",
    );
    config.CENTRAL_CLIENT_KEY = fs.readFileSync(
      env.CENTRAL_CLIENT_KEY_PATH || "/central-client-certs/tls.key",
    );
    config.CENTRAL_CA_CERT = fs.readFileSync(
      env.CENTRAL_CA_CERT_PATH || "/central-client-certs/ca.crt",
    );
    logger.info("MTLS is enabled", env);
  } else {
    logger.info("MTLS is disabled");
  }
  const missing = REQUIRED_CONFIG.filter((k) => !config[k]);
  if (missing.length > 0)
    throw new Error(`Missing required config: ${missing.join(", ")}`);

  config.orbPlansConfig = PlansConfig.parse(
    parseJsonProperty(env, "ORB_PLANS_CONFIG"),
  );

  config.CLOUD_DOMAIN_SUFFIXES = parseJsonProperty(
    env,
    "CLOUD_DOMAIN_SUFFIXES",
  );

  try {
    const requestInsightPublisherConfig = JSON.parse(
      fs.readFileSync(config.REQUEST_INSIGHT_PUBLISHER_CONFIG_FILE!, "utf8"),
    );
    config.REQUEST_INSIGHT_PROJECT_ID =
      requestInsightPublisherConfig.project_id;
    config.REQUEST_INSIGHT_TOPIC_NAME =
      requestInsightPublisherConfig.topic_name;
  } catch (e) {
    throw new Error(`failed at parsing REQUEST_INSIGHT_PUBLISHER_CONFIG_FILE`);
  }

  return config;
}

/**
 * Returns the community plan
 * Always exists - throws if not found
 */
export function getCommunityPlan(plans: PlansConfig): PlanConfig {
  const plan = plans.find(
    (p: PlanConfig) => p.features.plan_type === "community",
  );
  if (!plan) {
    throw new Error("Failed to find community plan");
  }
  return plan;
}

/**
 * Returns the trial plan
 * Always exists - throws if not found
 */
export function getTrialPlan(plans: PlansConfig): PlanConfig {
  const plan = plans.find((p: PlanConfig) => p.features.plan_type === "trial");
  if (!plan) {
    throw new Error("Failed to find trial plan");
  }
  return plan;
}

/**
 * Find a plan by ID
 * Returns null if not found
 */
export function findPlanById(
  plans: PlansConfig,
  planId: string,
): PlanConfig | null {
  return plans.find((p: PlanConfig) => p.id === planId) || null;
}

/**
 * Simple string formatter that takes a string and replaces all '%s' instances with
 * the corresponding index of the args. do not use `util.format` it does not work the way
 * you think or at least the way I think.
 *
 * @param fmt
 * @param args
 * @returns
 */
function format(fmt: string, ...args: unknown[]) {
  let idx = 0;
  return fmt.replace(/%s/g, (match) => {
    return idx < args.length ? String(args[idx++]) : match;
  });
}

function parseJsonProperty<
  T,
  K extends keyof T & string,
  D extends T[K] | undefined | null,
>(v: T, key: K, def?: D): D extends undefined ? any : D {
  const value = v[key];
  if (value == null) {
    if (def === undefined) {
      throw new Error(`Missing required config: ${key}`);
    }
    return def as any;
  }
  if (typeof value !== "string") {
    return value as any;
  }
  try {
    return JSON.parse(value);
  } catch (e) {
    logger.warn(`Failed to parse JSON for key: ${key}`, e);
    if (def === undefined) {
      throw new Error(`Missing required config: ${key}`);
    }
    return def as any;
  }
}

export const Config =
  process.env.NODE_ENV === "test" ? newConfigPreInit() : newConfig();
