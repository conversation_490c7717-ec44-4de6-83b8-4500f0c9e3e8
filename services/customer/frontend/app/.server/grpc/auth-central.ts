import { createGrpcTransport } from "@connectrpc/connect-node";
import { createPromiseClient } from "@connectrpc/connect";
import { logger } from "@augment-internal/logging";
import { Config } from "../config";
import type { SessionUser } from "../auth";
import {
  UserId,
  UserId_UserIdType,
  type CustomerUiRole,
} from "~services/auth/central/server/auth_entities_pb";
import {
  AuthService,
  TeamManagementService,
} from "~services/auth/central/server/auth_connect";
import type {
  GetUserResponse,
  GetUserOnTenantResponse,
  ListTenantUsersResponse,
  RemoveUserFromTenantResponse,
  AddUserToTenantResponse,
  UpdateUserOnTenantResponse,
  InviteUsersToTenantResponse,
  GetTenantInvitationsResponse,
  ResolveInvitationsResponse,
  GetSubscriptionResponse,
  UpdateSubscriptionResponse,
  CreateTenantForTeamResponse,
  GetCreateTenantForTeamStatusResponse,
  DeleteInvitationResponse,
  CancelSubscriptionResponse,
  PurchaseCreditsResponse,
  GetAllOrbPlansResponse,
  GetUserOrbPlanInfoResponse,
  PutUserOnPlanResponse,
  UnschedulePendingSubscriptionCancellationResponse,
  UnschedulePlanChangesResponse,
  GetSubscriptionRequest,
  GetUserOrbCreditsInfoResponse,
  GetUserOrbPaymentInfoResponse,
  GetUserOrbSubscriptionInfoResponse,
  GetTenantPlanStatusResponse,
} from "~services/auth/central/server/auth_pb";
import { PutUserOnPlanRequest_Plan } from "~services/auth/central/server/auth_pb";
import { getTokenExchangeClient } from "./token-exchange";
import { Scope } from "~services/token_exchange/token_exchange_pb";

/**
 * Gets a signed token for the specified user with the requested scopes
 * @param user Current user context
 * @param scopes Array of scopes to request for the token
 * @returns The signed token string
 * @throws Error if token exchange fails
 */
async function getSignedToken(
  user: SessionUser,
  scopes: Scope[],
): Promise<string> {
  const { signedToken } = await getTokenExchangeClient().getSignedTokenForUser({
    userId: user.userId,
    opaqueUserId: new UserId({
      userId: user.userId,
      userIdType: UserId_UserIdType.AUGMENT,
    }),
    userEmail: user.email,
    tenantId: user.tenantId,
    shardNamespace: user.shardNamespace,
    scopes,
  });

  if (!signedToken) {
    throw new Error("No token returned from token exchange service");
  }

  return signedToken;
}

/**
 * Gets authentication headers with a signed token for the specified user
 * @param user Current user context
 * @param scopes Array of scopes to request for the token
 * @param additionalHeaders Optional additional headers to include
 * @returns Headers object with authorization and any additional headers
 */
async function getHeaders(
  user: SessionUser,
  scopes: Scope[],
  additionalHeaders: Record<string, string> = {},
): Promise<Record<string, string>> {
  const signedToken = await getSignedToken(user, scopes);

  return {
    authorization: `Bearer ${signedToken}`,
    ...additionalHeaders,
  };
}

/**
 * AuthCentralClient is a singleton class that provides methods for interacting with the Auth Central service.
 *
 * Scope: Indicates the level of permissions granted to a token.
 * Refer to token_exchange.proto for more details.
 *
 * Opaque user ID: A unique identifier for a user that doesn't reveal any personal information.
 * It includes a user type id so that users can be identified across different authentication providers.
 */
export class AuthCentralClient {
  private static instance?: AuthCentralClient;

  static getInstance(): AuthCentralClient {
    if (!AuthCentralClient.instance) {
      AuthCentralClient.instance = new AuthCentralClient();
    }
    return AuthCentralClient.instance;
  }

  // Used for testing
  static resetInstance(): void {
    AuthCentralClient.instance = undefined;
  }

  constructor(
    private readonly authServiceClient = createPromiseClient(
      AuthService,
      createGrpcTransport(
        Config.createGrpcTransportOptions(Config.AUTH_CENTRAL_ENDPOINT),
      ),
    ),
    private readonly teamManagementClient = createPromiseClient(
      TeamManagementService,
      createGrpcTransport(
        Config.createGrpcTransportOptions(Config.AUTH_CENTRAL_ENDPOINT),
      ),
    ),
  ) {}

  /**
   * Get user information from Auth Central
   * @param user Current user context
   * @returns User information from Auth Central
   */
  async getUser(user: SessionUser): Promise<GetUserResponse> {
    try {
      logger.info(`Calling getUser: userId=${user.userId}`);

      // Include the token in the request metadata
      return this.authServiceClient.getUser(
        {
          userId: user.userId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_R]) },
      );
    } catch (error) {
      logger.error(
        `Failed to get user information: userId=${user.userId}, error=${error}`,
      );
      throw error;
    }
  }

  /**
   * List all users in a tenant
   * @param user Current user context
   * @param tenantId ID of the tenant to list users for (defaults to user's tenant)
   * @returns List of users in the tenant
   */
  async listTenantUsers(
    user: SessionUser,
    tenantId?: string,
  ): Promise<ListTenantUsersResponse> {
    try {
      const targetTenantId = tenantId ?? user.tenantId;
      if (!targetTenantId) {
        throw new Error("No tenant ID provided and user has no tenant ID");
      }

      logger.info(
        `Calling listTenantUsers: tenantId=${targetTenantId}, userId=${user.userId}`,
      );

      const response = await this.authServiceClient.listTenantUsers(
        {
          tenantId: targetTenantId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_R]) },
      );

      logger.info(
        `listTenantUsers response received: userCount=${response.users.length}`,
      );

      return response;
    } catch (error) {
      logger.error(
        `Failed to list tenant users: userId=${user.userId}, tenantId=${tenantId ?? user.tenantId}, error=${error}`,
      );
      throw error;
    }
  }

  /**
   * Get information on user/tenant association
   * @param user Current user context
   * @param targetUserId ID of the user to get information for
   * @param targetTenantId ID of the tenant (defaults to user's tenant)
   * @returns User-tenant association information including customer UI roles
   */
  async getUserOnTenant(
    user: SessionUser,
    targetUserId: string,
    targetTenantId?: string,
  ): Promise<GetUserOnTenantResponse> {
    try {
      const tenantId = targetTenantId ?? user.tenantId;
      if (!tenantId) {
        throw new Error("No tenant ID provided and user has no tenant ID");
      }

      logger.info(
        `Calling getUserOnTenant: tenantId=${tenantId}, targetUserId=${targetUserId}, requestingUserId=${user.userId}`,
      );

      const response = await this.authServiceClient.getUserOnTenant(
        {
          tenantId,
          userId: targetUserId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_R]) },
      );

      logger.info(
        `getUserOnTenant response received: roles=${response.customerUiRoles.join(", ")}`,
      );

      return response;
    } catch (error) {
      logger.error(
        `Failed to get user on tenant: targetUserId=${targetUserId}, tenantId=${targetTenantId ?? user.tenantId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Remove a user from a tenant
   * @param user Current user context
   * @param targetUserId ID of the user to remove
   * @param targetTenantId ID of the tenant (defaults to user's tenant)
   * @returns Empty response on success
   */
  async removeUserFromTenant(
    user: SessionUser,
    targetUserId: string,
    targetTenantId?: string,
  ): Promise<RemoveUserFromTenantResponse> {
    try {
      const tenantId = targetTenantId ?? user.tenantId;
      if (!tenantId) {
        throw new Error("No tenant ID provided and user has no tenant ID");
      }

      logger.info(
        `Calling removeUserFromTenant: tenantId=${tenantId}, userId=${targetUserId}, requestingUserId=${user.userId}`,
      );

      return await this.authServiceClient.removeUserFromTenant(
        {
          userId: targetUserId,
          tenantId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_RW]) },
      );
    } catch (error) {
      logger.error(
        `Failed to remove user from tenant: targetUserId=${targetUserId}, tenantId=${targetTenantId ?? user.tenantId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Add a user to a tenant
   * @param user Current user context
   * @param email Email of the user to add
   * @param targetTenantId ID of the tenant (defaults to user's tenant)
   * @returns Response containing the added user information
   */
  async addUserToTenant(
    user: SessionUser,
    email: string,
    targetTenantId?: string,
  ): Promise<AddUserToTenantResponse> {
    try {
      const tenantId = targetTenantId ?? user.tenantId;
      if (!tenantId) {
        throw new Error("No tenant ID provided and user has no tenant ID");
      }

      logger.info(
        `Calling addUserToTenant: email=${email}, tenantId=${tenantId}, requestingUserId=${user.userId}`,
      );

      return await this.authServiceClient.addUserToTenant(
        {
          email,
          tenantId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_RW]) },
      );
    } catch (error) {
      logger.error(
        `Failed to add user to tenant: email=${email}, tenantId=${targetTenantId ?? user.tenantId}, error=${error}`,
      );
      throw error;
    }
  }

  /**
   * Update a user's roles on a tenant
   * @param user Current user context
   * @param targetUserId ID of the user to update
   * @param customerUiRoles Array of roles to assign to the user
   * @param targetTenantId ID of the tenant (defaults to user's tenant)
   * @returns Response containing the updated roles
   */
  async updateUserOnTenant(
    user: SessionUser,
    targetUserId: string,
    customerUiRoles: CustomerUiRole[],
    targetTenantId?: string,
  ): Promise<UpdateUserOnTenantResponse> {
    try {
      const tenantId = targetTenantId ?? user.tenantId;
      if (!tenantId) {
        throw new Error("No tenant ID provided and user has no tenant ID");
      }

      logger.info(
        `Calling updateUserOnTenant: tenantId=${tenantId}, targetUserId=${targetUserId}, roles=${customerUiRoles.join(", ")}, requestingUserId=${user.userId}`,
      );

      return await this.authServiceClient.updateUserOnTenant(
        {
          userId: targetUserId,
          tenantId,
          customerUiRoles,
        },
        { headers: await getHeaders(user, [Scope.AUTH_RW]) },
      );
    } catch (error) {
      logger.error(
        `Failed to update user on tenant: targetUserId=${targetUserId}, tenantId=${targetTenantId ?? user.tenantId}, roles=${customerUiRoles.join(", ")}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Invite users to a tenant
   * @param user Current user context
   * @param emails Array of email addresses to invite
   * @param targetTenantId ID of the tenant (defaults to user's tenant)
   * @returns Response containing the invitation information
   */
  async inviteUsersToTenant(
    user: SessionUser,
    emails: string[],
    targetTenantId?: string,
  ): Promise<InviteUsersToTenantResponse> {
    try {
      const tenantId = targetTenantId ?? user.tenantId;
      if (!tenantId) {
        throw new Error("No tenant ID provided and user has no tenant ID");
      }

      logger.info(
        `Calling inviteUsersToTenant: emails=${emails.join(", ")}, tenantId=${tenantId}, requestingUserId=${user.userId}`,
      );

      return await this.teamManagementClient.inviteUsersToTenant(
        {
          tenantId,
          inviteeEmails: emails,
        },
        { headers: await getHeaders(user, [Scope.AUTH_RW]) },
      );
    } catch (error) {
      logger.error(
        `Failed to invite users to tenant: emails=${emails.join(", ")}, tenantId=${targetTenantId ?? user.tenantId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get all invitations for a tenant
   * @param user Current user context
   * @param targetTenantId ID of the tenant (defaults to user's tenant)
   * @returns Response containing all invitations for the tenant
   */
  async getTenantInvitations(
    user: SessionUser,
    targetTenantId?: string,
  ): Promise<GetTenantInvitationsResponse> {
    try {
      const tenantId = targetTenantId ?? user.tenantId;
      if (!tenantId) {
        throw new Error("No tenant ID provided and user has no tenant ID");
      }

      logger.info(
        `Calling getTenantInvitations: tenantId=${tenantId}, requestingUserId=${user.userId}`,
      );

      return await this.teamManagementClient.getTenantInvitations(
        {
          tenantId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_R]) },
      );
    } catch (error) {
      logger.error(
        `Failed to get tenant invitations: tenantId=${targetTenantId ?? user.tenantId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Resolve (accept or decline) invitations
   * @param user Current user context
   * @param acceptInvitationId ID of the invitation to accept (optional)
   * @param declineInvitationIds IDs of the invitations to decline (optional)
   * @returns Empty response on success
   */
  async resolveInvitations(
    user: SessionUser,
    acceptInvitationId?: string,
    declineInvitationIds?: string[],
  ): Promise<ResolveInvitationsResponse> {
    try {
      logger.info(
        `Calling resolveInvitations: acceptInvitationId=${acceptInvitationId || "none"}, declineInvitationIds=${declineInvitationIds?.join(", ") || "none"}, requestingUserId=${user.userId}`,
      );

      return await this.teamManagementClient.resolveInvitations(
        {
          acceptInvitationId,
          declineInvitationIds: declineInvitationIds ?? [],
        },
        { headers: await getHeaders(user, [Scope.AUTH_RW]) },
      );
    } catch (error) {
      logger.error(
        `Failed to resolve invitations: acceptInvitationId=${acceptInvitationId ?? "none"}, declineInvitationIds=${declineInvitationIds?.join(", ") ?? "none"}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get subscription information for a tenant
   * @param sessionUser Current user context
   * @returns Response containing the subscription information
   */
  async getSubscriptionFromDatabase(
    sessionUser: SessionUser,
  ): Promise<GetSubscriptionResponse> {
    try {
      logger.info(`Calling getSubscription: userId=${sessionUser.userId}`);

      // Get user information from Auth Central to retrieve the subscription ID
      const userResponse = await this.getUser(sessionUser);

      if (!userResponse.user || !userResponse.user.tenants[0]) {
        throw new Error(
          "Failed to get user/tenant information from Auth Central",
        );
      }

      // always use the tenant to get the subscription because of teams
      const subscriptionId = userResponse.user.orbSubscriptionId;
      const lookupId: GetSubscriptionRequest["lookupId"] = {
        case: "tenantId",
        value: userResponse.user.tenants[0],
      };

      logger.info(
        `Retrieved subscription ID from Auth Central: subscriptionId=${subscriptionId}, userId=${sessionUser.userId}`,
      );

      return await this.teamManagementClient.getSubscription(
        { lookupId },
        { headers: await getHeaders(sessionUser, [Scope.AUTH_R]) },
      );
    } catch (error) {
      logger.error(
        `Failed to get subscription for user: userId=${sessionUser.userId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update the number of seats in a subscription
   * @param user Current user context
   * @param seats New number of seats
   * @param subscriptionId ID of the subscription to update
   * @returns Empty response on success
   */
  async updateSubscription(
    user: SessionUser,
    seats: number,
    subscriptionId: string,
  ): Promise<UpdateSubscriptionResponse> {
    try {
      logger.info(
        `Calling updateSubscription: subscriptionId=${subscriptionId}, seats=${seats}, userId=${user.userId}`,
      );

      return await this.teamManagementClient.updateSubscription(
        {
          subscriptionId,
          seats,
        },
        { headers: await getHeaders(user, [Scope.AUTH_RW]) },
      );
    } catch (error) {
      logger.error(
        `Failed to update subscription: subscriptionId=${subscriptionId}, seats=${seats}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Create a new tenant for a team
   * @param user Current user context
   * @returns Response containing the tenant creation ID
   */
  async createTenantForTeam(
    user: SessionUser,
  ): Promise<CreateTenantForTeamResponse> {
    try {
      logger.info(`Calling createTenantForTeam: userId=${user.userId}`);

      return await this.teamManagementClient.createTenantForTeam(
        {
          adminUserId: user.userId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_RW]) },
      );
    } catch (error) {
      logger.error(
        `Failed to create tenant for team: userId=${user.userId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get the status of a tenant creation
   * @param user Current user context
   * @param tenantCreationId ID of the tenant creation to check
   * @returns Response containing the tenant creation status
   */
  async getCreateTenantForTeamStatus(
    user: SessionUser,
    tenantCreationId: string,
  ): Promise<GetCreateTenantForTeamStatusResponse> {
    try {
      logger.info(
        `Calling getCreateTenantForTeamStatus: tenantCreationId=${tenantCreationId}, userId=${user.userId}`,
      );

      return await this.teamManagementClient.getCreateTenantForTeamStatus(
        {
          tenantCreationId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_R]) },
      );
    } catch (error) {
      logger.error(
        `Failed to get tenant creation status: tenantCreationId=${tenantCreationId}, userId=${user.userId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Delete a pending invitation
   * @param user Current user context
   * @param invitationId ID of the invitation to delete
   * @param targetTenantId ID of the tenant (defaults to user's tenant)
   * @returns Empty response on success
   */
  async deleteInvitation(
    user: SessionUser,
    invitationId: string,
    targetTenantId?: string,
  ): Promise<DeleteInvitationResponse> {
    try {
      const tenantId = targetTenantId ?? user.tenantId;
      if (!tenantId) {
        throw new Error("No tenant ID provided and user has no tenant ID");
      }

      logger.info(
        `Calling deleteInvitation: invitationId=${invitationId}, tenantId=${tenantId}, requestingUserId=${user.userId}`,
      );

      return await this.teamManagementClient.deleteInvitation(
        {
          invitationId,
          tenantId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_RW]) },
      );
    } catch (error) {
      logger.error(
        `Failed to delete invitation: invitationId=${invitationId}, tenantId=${targetTenantId ?? user.tenantId}`,
        error,
      );
      throw error;
    }
  }

  async cancelSubscription(
    user: SessionUser,
  ): Promise<CancelSubscriptionResponse> {
    try {
      logger.info(
        `Calling cancelSubscription: userId=${user.userId}, tenantId=${user.tenantId}`,
      );

      return await this.teamManagementClient.cancelSubscription(
        {
          userId: user.userId,
          tenantId: user.tenantId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_RW]) },
      );
    } catch (error) {
      logger.error(
        `Failed to cancel subscription: userId=${user.userId}, tenantId=${user.tenantId}`,
        error,
      );
      throw error;
    }
  }

  async purchaseCredits(
    user: SessionUser,
    credits: number,
  ): Promise<PurchaseCreditsResponse> {
    try {
      logger.info(
        `Calling purchaseCredits: userId=${user.userId}, tenantId=${user.tenantId}, credits=${credits}`,
      );

      return await this.teamManagementClient.purchaseCredits(
        {
          userId: user.userId,
          tenantId: user.tenantId,
          credits,
        },
        { headers: await getHeaders(user, [Scope.AUTH_RW]) },
      );
    } catch (error) {
      logger.error(
        `Failed to purchase credits: userId=${user.userId}, tenantId=${user.tenantId}, credits=${credits}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get all available Orb plans
   * @param user Current user context
   * @returns Response containing all available Orb plans
   */
  async getAllOrbPlans(user: SessionUser): Promise<GetAllOrbPlansResponse> {
    try {
      logger.info(`Calling getAllOrbPlans`);

      return await this.teamManagementClient.getAllOrbPlans(
        {},
        { headers: await getHeaders(user, [Scope.AUTH_R]) },
      );
    } catch (error) {
      logger.error(`Failed to get all Orb plans`, error);
      throw error;
    }
  }

  /**
   * Get Orb plan information for a user
   * @param user Current user context
   * @returns Response containing the user's Orb plan information
   */
  async getUserOrbPlanInfo(
    user: SessionUser,
  ): Promise<GetUserOrbPlanInfoResponse> {
    try {
      logger.info(`Calling getUserOrbPlanInfo: userId=${user.userId}`);

      return await this.teamManagementClient.getUserOrbPlanInfo(
        {
          userId: user.userId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_R]) },
      );
    } catch (error) {
      logger.error(
        `Failed to get user Orb plan info: userId=${user.userId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Put a user on a specific plan
   * @param user Current user context
   * @param planId The plan ID to put the user on (e.g., "orb_community_plan")
   * @returns Empty response on success as this is an async operation
   */
  async putUserOnPlan(
    user: SessionUser,
    planId: string,
  ): Promise<PutUserOnPlanResponse> {
    try {
      logger.info(
        `Calling putUserOnPlan: userId=${user.userId}, planId=${planId}`,
      );

      // TODO: Deprecate the plan enum when plan_id is used
      let plan = PutUserOnPlanRequest_Plan.UNKNOWN_PLAN;
      if (planId === "orb_community_plan") {
        plan = PutUserOnPlanRequest_Plan.COMMUNITY;
      } else if (planId === "orb_developer_plan") {
        plan = PutUserOnPlanRequest_Plan.DEVELOPER;
      }

      // Include the token in the request metadata and pass parameters directly
      return await this.teamManagementClient.putUserOnPlan(
        {
          userId: user.userId,
          plan,
          planId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_RW]) },
      );
    } catch (error) {
      logger.error(
        `Failed to put user on plan: userId=${user.userId}, email=${user.email}, tenantId=${user.tenantId}, planId=${planId}, error=${error}`,
      );
      throw error;
    }
  }

  /**
   * Unschedule a pending subscription cancellation
   * @param user Current user context
   * @returns Empty response on success
   */
  async unschedulePendingSubscriptionCancellation(
    user: SessionUser,
  ): Promise<UnschedulePendingSubscriptionCancellationResponse> {
    try {
      logger.info(
        `Calling unschedulePendingSubscriptionCancellation: userId=${user.userId}`,
      );

      return await this.teamManagementClient.unschedulePendingSubscriptionCancellation(
        {
          userId: user.userId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_RW]) },
      );
    } catch (error) {
      logger.error(
        `Failed to unschedule pending subscription cancellation: userId=${user.userId}, error=${error}`,
      );
      throw error;
    }
  }

  /**
   * Unschedule pending plan changes
   * @param user Current user context
   * @returns Empty response on success
   */
  async unschedulePlanChanges(
    user: SessionUser,
  ): Promise<UnschedulePlanChangesResponse> {
    try {
      logger.info(`Calling unschedulePlanChanges: userId=${user.userId}`);

      return await this.teamManagementClient.unschedulePlanChanges(
        {
          userId: user.userId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_RW]) },
      );
    } catch (error) {
      logger.error(
        `Failed to unschedule pending plan changes: userId=${user.userId}, error=${error}`,
      );
      throw error;
    }
  }

  /**
   * Get Orb credits information for a user
   * @param user Current user context
   * @returns Response containing the user\'s Orb credits information
   */
  async getUserOrbCreditsInfo(
    user: SessionUser,
  ): Promise<GetUserOrbCreditsInfoResponse> {
    try {
      logger.info(`Calling getUserOrbCreditsInfo: userId=${user.userId}`);

      return await this.teamManagementClient.getUserOrbCreditsInfo(
        {
          userId: user.userId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_R]) },
      );
    } catch (error) {
      logger.error(
        `Failed to get user Orb credits info: userId=${user.userId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get Orb payment information for a user
   * @param user Current user context
   * @returns Response containing the user\'s Orb payment information
   */
  async getUserOrbPaymentInfo(
    user: SessionUser,
  ): Promise<GetUserOrbPaymentInfoResponse> {
    try {
      logger.info(`Calling getUserOrbPaymentInfo: userId=${user.userId}`);

      return await this.teamManagementClient.getUserOrbPaymentInfo(
        {
          userId: user.userId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_R]) },
      );
    } catch (error) {
      logger.error(
        `Failed to get user Orb payment info: userId=${user.userId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get Orb subscription information for a user
   * @param user Current user context
   * @returns Response containing the user\'s Orb subscription information
   */
  async getUserOrbSubscriptionInfo(
    user: SessionUser,
  ): Promise<GetUserOrbSubscriptionInfoResponse> {
    try {
      logger.info(`Calling getUserOrbSubscriptionInfo: userId=${user.userId}`);

      return await this.teamManagementClient.getUserOrbSubscriptionInfo(
        {
          userId: user.userId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_R]) },
      );
    } catch (error) {
      logger.error(
        `Failed to get user Orb subscription info: userId=${user.userId}`,
        error,
      );
      throw error;
    }
  }

  async getTenantPlanStatus(
    user: SessionUser,
  ): Promise<GetTenantPlanStatusResponse> {
    try {
      logger.info(`Calling getTenantPlanStatus: tenantId=${user.tenantId}`);

      return await this.teamManagementClient.getTenantPlanStatus(
        {
          tenantId: user.tenantId,
        },
        { headers: await getHeaders(user, [Scope.AUTH_R]) },
      );
    } catch (error) {
      logger.error(
        `Failed to get tenant plan status info: userId=${user.userId}`,
        error,
      );
      throw error;
    }
  }
}
