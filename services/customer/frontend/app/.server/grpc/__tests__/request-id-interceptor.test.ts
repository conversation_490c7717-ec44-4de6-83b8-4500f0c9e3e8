import { describe, it, expect, vi } from "vitest";
import { requestIdInterceptor } from "../request-id-interceptor";

// Mock crypto.randomUUID
vi.mock("crypto", () => ({
  randomUUID: vi.fn(() => "test-request-id-12345"),
}));

// Mock logger
vi.mock("@augment-internal/logging", () => ({
  logger: {
    debug: vi.fn(),
    error: vi.fn(),
  },
}));

describe("requestIdInterceptor", () => {
  it("should add x-request-id header to requests", async () => {
    const mockNext = vi.fn().mockResolvedValue({ data: "response" });
    const mockReq = {
      header: new Map(),
      service: { typeName: "TestService" },
      method: { name: "testMethod" },
    };

    const interceptor = requestIdInterceptor(mockNext);
    await interceptor(mockReq as any);

    expect(mockReq.header.get("x-request-id")).toBe("test-request-id-12345");
    expect(mockNext).toHaveBeenCalledWith(mockReq);
  });

  it("should log request initiation and completion", async () => {
    const { logger } = await import("@augment-internal/logging");
    const mockNext = vi.fn().mockResolvedValue({ data: "response" });
    const mockReq = {
      header: new Map(),
      service: { typeName: "TestService" },
      method: { name: "testMethod" },
    };

    const interceptor = requestIdInterceptor(mockNext);
    await interceptor(mockReq as any);

    expect(logger.debug).toHaveBeenCalledWith("gRPC request initiated", {
      requestId: "test-request-id-12345",
      service: "TestService",
      method: "testMethod",
      route_id: "grpc_TestService_testMethod",
    });

    expect(logger.debug).toHaveBeenCalledWith("gRPC request completed", {
      requestId: "test-request-id-12345",
      service: "TestService",
      method: "testMethod",
      route_id: "grpc_TestService_testMethod",
    });
  });

  it("should log errors and attach requestId to error object", async () => {
    const { logger } = await import("@augment-internal/logging");
    const testError = new Error("Test error");
    const mockNext = vi.fn().mockRejectedValue(testError);
    const mockReq = {
      header: new Map(),
      service: { typeName: "TestService" },
      method: { name: "testMethod" },
    };

    const interceptor = requestIdInterceptor(mockNext);

    await expect(interceptor(mockReq as any)).rejects.toThrow("Test error");

    expect(logger.error).toHaveBeenCalledWith("gRPC request failed", {
      requestId: "test-request-id-12345",
      service: "TestService",
      method: "testMethod",
      route_id: "grpc_TestService_testMethod",
      error: "Test error",
    });

    expect((testError as any).requestId).toBe("test-request-id-12345");
  });

  it("should generate unique request IDs for each request", async () => {
    const { randomUUID } = await import("crypto");
    (randomUUID as any).mockReturnValueOnce("id-1").mockReturnValueOnce("id-2");

    const mockNext = vi.fn().mockResolvedValue({ data: "response" });
    const mockReq1 = {
      header: new Map(),
      service: { typeName: "TestService" },
      method: { name: "testMethod" },
    };
    const mockReq2 = {
      header: new Map(),
      service: { typeName: "TestService" },
      method: { name: "testMethod" },
    };

    const interceptor = requestIdInterceptor(mockNext);
    await interceptor(mockReq1 as any);
    await interceptor(mockReq2 as any);

    expect(mockReq1.header.get("x-request-id")).toBe("id-1");
    expect(mockReq2.header.get("x-request-id")).toBe("id-2");
  });
});
