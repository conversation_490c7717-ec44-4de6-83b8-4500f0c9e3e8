import { type CallOptions, createPromiseClient } from "@connectrpc/connect";
import type { PartialMessage } from "@bufbuild/protobuf";

import { Supabase } from "~services/integrations/supabase/supabase_connect";
import type {
  HydrateSupabaseSettingsRequest,
  HydrateSupabaseSettingsResponse,
} from "~services/integrations/supabase/supabase_pb";

import { Config } from "../config";
import { TenantShardAwareTransport } from "./tenant-shard-aware-transport";

export class SupabaseProcessorClient {
  constructor(
    private client = createPromiseClient(
      Supabase,
      new TenantShardAwareTransport(
        (_tenantId: string, shardNamespace: string, cloud: string) => {
          // Assume that we are adding this suffix to the app name
          const suffix =
            cloud === Config.CURRENT_CLOUD
              ? // The local service - we use these in the same cloud so that we can
                // create fewer global services, which can run into GCP limits
                // rather easily.
                `-svc.${shardNamespace}`
              : // Otherwise, use the global service suffix
                `.${shardNamespace}.t.${Config.CLOUD_DOMAIN_SUFFIXES[cloud]}`;
          return Config.format(Config.SUPABASE_ENDPOINT_TEMPLATE, suffix);
        },
      ),
    ),
  ) {}

  hydrateSupabaseSettings = (
    request: PartialMessage<HydrateSupabaseSettingsRequest>,
    options?: CallOptions,
  ): Promise<HydrateSupabaseSettingsResponse> => {
    return this.client.hydrateSupabaseSettings(
      request as HydrateSupabaseSettingsRequest,
      options,
    );
  };
}

export const supabaseProcessorClient = new SupabaseProcessorClient();
