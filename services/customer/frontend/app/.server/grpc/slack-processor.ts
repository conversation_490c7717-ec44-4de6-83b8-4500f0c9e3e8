import { type CallOptions, createPromiseClient } from "@connectrpc/connect";
import type { PartialMessage } from "@bufbuild/protobuf";

import { SlackBotProcessor } from "~services/integrations/slack_bot/processor/processor_connect";
import type {
  LinkSlackUserToAugmentUserRequest,
  LinkSlackUserToAugmentUserResponse,
  HydrateSlackSettingsRequest,
  HydrateSlackSettingsResponse,
} from "~services/integrations/slack_bot/processor/processor_pb";

import { Config } from "../config";
import { TenantShardAwareTransport } from "./tenant-shard-aware-transport";

export class SlackProcessorClient {
  constructor(
    private client = createPromiseClient(
      SlackBotProcessor,
      new TenantShardAwareTransport(
        (_tenantId: string, shardNamespace: string, cloud: string) => {
          // Assume that we are adding this suffix to the app name
          const suffix =
            cloud === Config.CURRENT_CLOUD
              ? // The local service - we use these in the same cloud so that we can
                // create fewer global services, which can run into GCP limits
                // rather easily.
                `-svc.${shardNamespace}`
              : // Otherwise, use the global service suffix
                `.${shardNamespace}.t.${Config.CLOUD_DOMAIN_SUFFIXES[cloud]}`;
          return Config.format(
            Config.SLACK_BOT_PROCESSOR_ENDPOINT_TEMPLATE,
            suffix,
          );
        },
      ),
    ),
  ) {}

  hydrateSlackSettings = (
    request: PartialMessage<HydrateSlackSettingsRequest>,
    options?: CallOptions,
  ): Promise<HydrateSlackSettingsResponse> =>
    this.client.hydrateSlackSettings(
      request as HydrateSlackSettingsRequest,
      options,
    );

  linkSlackUserToAugmentUser = (
    request: PartialMessage<LinkSlackUserToAugmentUserRequest>,
    options?: CallOptions,
  ): Promise<LinkSlackUserToAugmentUserResponse> =>
    this.client.linkSlackUserToAugmentUser(
      request as LinkSlackUserToAugmentUserRequest,
      options,
    );
}

export const slackProcessorClient = new SlackProcessorClient();
