import type { Interceptor } from "@connectrpc/connect";
import { randomUUID } from "crypto";
import { logger } from "@augment-internal/logging";

/**
 * Interceptor that adds a unique request ID to each gRPC request.
 * The request ID is added as the x-request-id header and is used
 * for debugging, tracing, and support purposes.
 */
export const requestIdInterceptor: Interceptor = (next) => async (req) => {
  // Generate a new request ID for this request
  const requestId = randomUUID();

  // Add the request ID to the headers
  req.header.set("x-request-id", requestId);

  // Log the request with its ID for debugging
  logger.debug("gRPC request initiated", {
    requestId,
    service: req.service.typeName,
    method: req.method.name,
    route_id: `grpc_${req.service.typeName}_${req.method.name}`,
  });

  try {
    // Continue with the request
    const response = await next(req);

    // Log successful completion
    logger.debug("gRPC request completed", {
      requestId,
      service: req.service.typeName,
      method: req.method.name,
      route_id: `grpc_${req.service.typeName}_${req.method.name}`,
    });

    return response;
  } catch (error) {
    // Log errors with the request ID
    logger.error("gRPC request failed", {
      requestId,
      service: req.service.typeName,
      method: req.method.name,
      route_id: `grpc_${req.service.typeName}_${req.method.name}`,
      error: error instanceof Error ? error.message : String(error),
    });

    // Re-throw the error with request ID attached if possible
    if (error instanceof Error && typeof error === "object") {
      (error as any).requestId = requestId;
    }
    throw error;
  }
};
