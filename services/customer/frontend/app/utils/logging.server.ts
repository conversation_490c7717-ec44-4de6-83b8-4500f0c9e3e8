import { logger } from "@augment-internal/logging";
import type { SessionUser } from "../.server/auth";

/**
 * Creates a structured logging context for API routes.
 *
 * @param user - The authenticated user from the session
 * @param request - The incoming HTTP request
 * @param requestId - Unique request ID for distributed tracing
 * @returns Logging helper with info, warn, and error methods that include consistent metadata
 */
export function createRouteLogContext(
  user: SessionUser,
  request: Request,
  requestId: string,
) {
  const url = new URL(request.url);

  // Extract route_id from URL path
  // Remove query params and convert path to route format
  // e.g., /api/team -> routes/api.team
  const pathSegments = url.pathname.split("/").filter(Boolean);
  const routeId =
    pathSegments.length > 0
      ? `routes/${pathSegments.join(".")}`
      : "routes/_index";

  const baseContext = {
    request_id: requestId,
    user_id: user.userId,
    tenant_id: user.tenantId,
    route_id: routeId,
    method: request.method,
    path: url.pathname,
  };

  return {
    info: (message: string, extra?: Record<string, any>) =>
      logger.info({ message, ...baseContext, ...extra }),

    warn: (message: string, error?: unknown, extra?: Record<string, any>) => {
      const errorInfo = error
        ? {
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
          }
        : {};
      logger.warn({ message, ...baseContext, ...errorInfo, ...extra });
    },

    error: (message: string, error?: unknown, extra?: Record<string, any>) => {
      const errorInfo = error
        ? {
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
          }
        : {};
      logger.error({ message, ...baseContext, ...errorInfo, ...extra });
    },
  };
}
