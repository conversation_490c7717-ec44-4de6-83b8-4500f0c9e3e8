//This file contains type guards for objects and values.
import { not } from "./boolean";
import type { Nullish } from "@augment-internal/ts-utils/type";

/**
 * A little helper to ensure the returning
 * object has a key.
 */
export function has<T extends PropertyKey>(
  v: unknown,
  key: T,
  ...keys: T[]
): v is {
  [k in T]: unknown;
} {
  if (!(isObjectish(v) && key in v)) {
    return false;
  }
  //[].every is true.
  return keys.every((k) => k in v);
}

export function isObjectish(v: unknown): v is Exclude<object, Nullish> {
  if (v == null) {
    return false;
  }
  switch (typeof v) {
    case "object": //fallthrough
    case "function":
      return true;
    default:
      return false;
  }
}

export function hasValueOfType<K extends PropertyKey, T>(
  v: unknown,
  key: K,
  guard: GuardFn<T>,
): v is { [k in K]: T } {
  return has(v, key) && guard(v[key]);
}
type GuardShape<T> = {
  [k in keyof T]: T[k] extends GuardFn<infer V> ? V : never;
};
/**
 * Returns a function that can be used to see if a function has a shape.
 * It takes an object which keys are property keys and values are type guards.
 * and if all the type guards succeed, then the object is of the shape.
 *
 * Example:
 * ```
 * const isError = hasShape({
 *  message:isString,
 *  status:isNumber,
 *  code:optional(isNumber)
 * })
 *
 * const v:unknown = {message:"hello",status:200}
 * if(isError(v)){
 *  //v is of the shape
 * }
 * const v2:unknown = {message:"hello"}
 * if(isError(v2)){
 *  //v2 is not of the shape
 * }
 * const v3:unknown = {message:"hello", status:300, code:'I AM NOT A NUMBER'}
 * if(isError(v3)){
 *  //v3 is not of the shape
 * }
 *
 * ```
 * @param v
 * @returns
 */
export const hasShape = <T extends { [k in PropertyKey]: GuardFn<unknown> }>(
  v: T,
) => {
  const keys = Object.keys(v);
  return function guard(o: unknown): o is GuardShape<T> {
    if (!isObjectish(o)) {
      return false;
    }
    return keys.every((k) => v[k](has(o, k) ? o[k] : undefined));
  };
};

export function isString(v: unknown): v is string {
  return typeof v === "string";
}

export function isNumber(v: unknown): v is number {
  return typeof v === "number" || (isString(v) && !isNaN(Number(v)));
}

type GuardFn<T extends V, V = any> = (v: V) => v is T;

/**
 * Returns true if the value is not null or undefined.
 * @example
 * isNonNullable(null); // false
 * isNonNullable(''); // true
 */
export function isNonNullable<T>(v: T | Nullish): v is T {
  return v != null && v !== undefined;
}

/**
 * Returns true if the value is null or undefined.
 * @example
 * isNullable(null); // true
 * isNullable(''); // false
 */
export function isNullable<T>(v: T | Nullish): v is Nullish {
  return not(isNonNullable)(v);
}

export function optional<T>(guard: GuardFn<T>) {
  return function guarded(v: unknown): v is T | Nullish {
    return v == null || guard(v);
  };
}

export function assertGuard<T>(guard: GuardFn<T>) {
  return function assert(v: unknown): asserts v is T {
    if (!guard(v)) {
      throw new GuardError(`Value ${v} does not match guard ${guard.name}`);
    }
  };
}

export class GuardError extends Error {
  constructor(public message: string) {
    super(message);
    Object.setPrototypeOf(this, GuardError.prototype);
  }
}

export function maybeType<V, T>(
  v: V,
  guard: GuardFn<T>,
): V extends T ? T : T extends V ? T | undefined : undefined {
  if (guard(v)) return v as any;
  return undefined as any;
}

export function isAsyncFunction(
  fn: any,
): fn is (...args: any[]) => Promise<any> {
  return fn.constructor.name === "AsyncFunction";
}
