/* eslint-disable @typescript-eslint/ban-types */
import type { UnionToIntersection } from "@augment-internal/ts-utils/type";

export function isPlainObject(v: unknown): v is Record<string, unknown> {
  return (
    v != null &&
    typeof v === "object" &&
    (v.constructor === Object || v.constructor == null)
  );
}

export function isObject(v: unknown): v is object {
  return v != null && typeof v === "object";
}

/**
 * Assigns the sources with the target in a new object.
 * @params {...sources} sources to assign
 * @example
 * const source1 = { a: 1 }
 * const source2 = { b: 2 }
 * const target = { c: 3 }
 * const d = assign(a,b)(target)
 */
export function assign<const T extends object, const O1 extends object>(
  source1: O1,
): (target: T) => T & O1;
export function assign<
  const T extends object,
  const O1 extends object,
  const O2 extends object,
>(source1: O1, source2: O2): (target: T) => T & O1 & O2;
export function assign<
  const T extends object,
  const O1 extends object,
  const O2 extends object,
  const O3 extends object,
>(sources: O1, source2: O2, source3: O3): (target: T) => T & O1 & O2 & O3;
export function assign<const T extends object, const Os extends object[]>(
  ...sources: Os
): (target: T) => T & UnionToIntersection<Os>;
export function assign<const T extends object>(...sources: object[]) {
  return (target: T) => Object.assign({}, target, ...sources);
}

/**
 * Deeply assigns the sources with the target in a new object.
 * @params {...sources} sources to assign
 * @example
 * const source1 = { a: 1, b: { c: 2 } }
 * const source2 = { b: { d: 3 } }
 * const target = { e: 4 }
 * const d = deepAssign(a,b)(target)
 * // { a: 1, b: { c: 2, d: 3 }, e: 4 }
 */
export function deepAssign<const T extends object, const S1 extends object>(
  source1: S1,
): (target: T) => DeepMerge<T, S1>;

export function deepAssign<
  const T extends object,
  const S1 extends object,
  const S2 extends object,
>(source1: S1, source2: S2): (target: T) => DeepMerge<T, S1, S2>;

export function deepAssign<
  const T extends object,
  const S1 extends object,
  const S2 extends object,
  const S3 extends object,
>(
  source1: S1,
  source2: S2,
  source3: S3,
): (target: T) => DeepMerge<T, S1, S2, S3>;

/** Generic implementation – supports any number of sources internally */
export function deepAssign(...sources: object[]) {
  return function assign<T extends object>(target: T) {
    for (const src of sources) {
      deepMergeInto(target, src);
    }
    return target as any; // cast is safe thanks to type-level DeepMerge
  };
}

function deepMergeInto(target: any, source: any): void {
  if (!isObject(source)) return;

  for (const key of Reflect.ownKeys(source)) {
    const sVal = (source as any)[key];
    const tVal = (target as any)[key];

    if (isObject(sVal) && isObject(tVal)) {
      // Both sides are objects → recurse and mutate target in place
      deepMergeInto(tVal, sVal);
    } else {
      // Otherwise behave exactly like Object.assign
      (target as any)[key] = sVal;
    }
  }
}

export type DeepMerge<T, S1 = {}, S2 = {}, S3 = {}> = MergeTwo<
  MergeTwo<MergeTwo<T, S1>, S2>,
  S3
>;

/** Recursively merge two object types */
type MergeTwo<A, B> = {
  [K in keyof A | keyof B]: K extends keyof B
    ? K extends keyof A
      ? A[K] extends object
        ? B[K] extends object
          ? MergeTwo<A[K], B[K]>
          : B[K]
        : B[K]
      : B[K]
    : K extends keyof A
      ? A[K]
      : never;
};

type MemberNames<T extends Record<string, string | number>> = {
  [K in keyof T]: T[K] extends number ? K : never;
}[keyof T];

type MemberValues<T extends Record<string, string | number>> =
  T[MemberNames<T>];

// 2) The two type-level mappings you need:
type StringValue<T extends Record<string, string | number>, V> = {
  [K in MemberNames<T>]: T[K] extends V ? K : never;
}[MemberNames<T>];

type NumericValue<
  T extends Record<string, string | number>,
  K extends MemberNames<T>,
> = T[K];

/**
 * Builds a `const` object from a numeric enum.
 * Convert between numeric and string values with `of`
 * @example
 * enum SubscriptionEnum {
 *   UNKNOWN = 0,
 *   UPCOMING = 1,
 *   ACTIVE = 2,
 *   ENDED = 3,
 * }
 * const Subscription = toConstObject(SubscriptionEnum);
 * type Subscription = toConstObject.infer<typeof Subscription>;
 * Subscription.ACTIVE // "ACTIVE"
 * Subscription.toEnumValue(SubscriptionEnum.ACTIVE) // "ACTIVE"
 * Subscription.fromEnumValue(Subscription.ACTIVE) // 2
 */
export function toConstObject<T extends Record<string, string | number>>(
  enumObj: T,
): {
  [K in MemberNames<T>]: K;
} & {
  fromEnumValue<V extends MemberValues<T> | undefined>(
    value: V,
  ): V extends undefined ? undefined : StringValue<T, V>;
  toEnumValue<V extends MemberNames<T> | undefined>(
    value: V,
  ): V extends undefined ? undefined : NumericValue<T, NonNullable<V>>;
} {
  const constObj = Object.fromEntries(
    Object.keys(enumObj)
      .filter((key) => typeof enumObj[key] === "number")
      .map((key) => [key, key]),
  ) as any;
  function toEnumValue(value: string) {
    if (value == null) return undefined;
    if (typeof value !== "string" || !Object.hasOwn(constObj, value)) {
      throw new Error(`Invalid enum value: ${value}`);
    }
    return enumObj[value];
  }
  function fromEnumValue(value: number) {
    if (value == null) return undefined;
    if (typeof value !== "number" || !Object.hasOwn(enumObj, value)) {
      throw new Error(`Invalid enum value: ${value}`);
    }
    return enumObj[value];
  }
  return Object.assign(constObj, { fromEnumValue, toEnumValue });
}

// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace toConstObject {
  export type infer<T extends Record<string, any>> = Exclude<
    keyof T,
    "toEnumValue" | "fromEnumValue"
  >;
}
