import type { AnyFunction } from "@augment-internal/ts-utils/type";

/**
 * Combines multiple functions into a single function that calls each of them in order.
 *
 * @param {...((...args: any[]) => void)} fns - The functions to be combined.
 * @returns {(...args: any[]) => void} A function that calls each of the input functions with the given arguments.
 */
export function composeFns<T extends (...args: any[]) => void>(
  ...fns: (T | undefined)[]
) {
  return (...args: Parameters<T>) => fns.map(call.withArgs(...args));
}

/**
 * Reduces multiple functions into a single function that calls each of them in order.
 *
 * @param {...((arg: T) => T)} fns - The functions to be reduced.
 * @returns {(args: T) => T} A function that calls each of the input functions with the given arguments.
 * @example
 * const fn = reduceFns(
 *   (x) => x + 1,
 *   (x) => x * 2,
 * );
 * fn(1); // 4
 */
// A helper type alias for any function.
type Func = (arg: any) => any;

/**
 * ValidChain<T>
 *
 * This type recursively enforces that for any tuple of functions T,
 * every adjacent pair is compatible — that is, the return type
 * of the first function must be assignable to the parameter type of the second.
 *
 * We constrain the "rest" of the tuple to be an array of functions to avoid the
 * error encountered with union types.
 */
type ValidChain<T extends Func[]> = T extends [infer F, infer G, ...infer Rest]
  ? F extends Func
    ? G extends Func
      ? Rest extends Func[]
        ? ReturnType<F> extends Parameters<G>[0]
          ? ValidChain<[G, ...Rest]>
          : never
        : never
      : never
    : never
  : T;

/**
 * Last<T>
 *
 * Given a tuple of functions T, this type extracts the last function
 * and ensures that it is indeed a function.
 */
type Last<T extends Func[]> = T extends [...any, infer L]
  ? L extends Func
    ? L
    : never
  : never;

/**
 * ReducedFunction<T>
 *
 * Given a tuple T of functions (where T has at least one function),
 * the reduced (composed) function will:
 *  - Accept the parameter type of the first function.
 *  - Return the return type of the last function.
 */
type ReducedFunction<T extends [Func, ...Func[]]> = (
  arg: Parameters<T[0]>[0],
) => ReturnType<Last<T>>;

/**
 * reduceFns
 *
 * Composes an arbitrary number of functions passed as a variadic tuple.
 * The type parameter T is constrained so that:
 *  - T is a nonempty array of functions.
 *  - ValidChain<T> ensures that each consecutive pair is compatible.
 *
 * The returned function accepts the parameter type of the first function in T
 * and returns the result of the last function.
 */
export function reduceFns<T extends [Func, ...Func[]]>(
  ...fns: ValidChain<T> extends never ? never : T
): ReducedFunction<T> {
  return ((input: Parameters<T[0]>[0]) =>
    fns.reduce((acc, fn) => fn(acc), input)) as ReducedFunction<T>;
}

/**
 * Calls a function with the given arguments.
 *
 * @param {...any[]} args - The arguments to pass to the function.
 * @property {(...args: any[]) => void} withArgs - A function to apply arguments to functions
 * @returns {void}
 */
export function call(fn: () => void) {
  return fn();
}
call.withArgs = <Args extends any[]>(...args: Args) => {
  return (fn: ((...args: Args) => void) | undefined) => fn?.(...args);
};

/**
 * Resolves a value by invoking it if it's a function, or returning it directly otherwise.
 *
 * @template T - The type of the function to be resolved.
 * @param { T | ReturnType<T>} fn - A function to be invoked with the given parameters or a direct value.
 * @param {Parameters<T>} params - The parameters to pass to the function if `fn` is callable.
 * @returns {ReturnType<T>} The result of invoking `fn` with `params` if it is a function, or `fn` itself.
 */
export function resolve<T extends AnyFunction>(
  fn: T | ReturnType<T>,
  ...params: Parameters<T>
): ReturnType<T> {
  return typeof fn === "function" ? (fn as T)(...params) : fn;
}

export function isFunction<T extends AnyFunction>(v: any): v is T {
  return typeof v === "function";
}

export function setStateAction<T, SF extends (state: T) => T>(
  stateOrFn: T | SF,
  currentState: T,
): T {
  if (isFunction<SF>(stateOrFn)) return stateOrFn(currentState);
  return stateOrFn;
}
