import { z } from "zod";

/** schema for /api/put-user-on-plan */
export const PutUserOnPlanRequestSchema = z.object({
  planId: z.string(),
});

export type PutUserOnPlanRequestSchema = z.infer<
  typeof PutUserOnPlanRequestSchema
>;

export const PutUserOnPlanResponseSchema = z.object({
  type: z.enum(["team", "individual"]),
  success: z.boolean(),
  message: z.string().optional(),
});

export type PutUserOnPlanResponseSchema = z.infer<
  typeof PutUserOnPlanResponseSchema
>;
