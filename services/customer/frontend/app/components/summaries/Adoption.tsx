import { useMemo } from "react";
import { Grid } from "@radix-ui/themes";
import { t } from "../../utils/helpers";
import { useAppStore } from "../../utils/app-store";
import { between } from "../../utils/data";
import { useDateFilter } from "../../utils/date";
import { avgPropertyByDay } from "../../utils/value-calculations";
import SectionLayout from "../global/SectionLayout";
import MetricCard from "../cards/MetricCard";
import type {
  DateAndCount,
  GetChatStatsResponse_ChatStats,
} from "../../../../../request_insight/analytics/request_insight_analytics_pb";
import { useQuery } from "@tanstack/react-query";
import { dateRangeQuery } from "app/client-cache";

export default function Adoption({ title }: { title: string }) {
  const earliestData = useAppStore((state) => state.earliestData);

  const [glanceFilter, setGlanceFilter, startDate, endDate] = useDateFilter(
    earliestData,
    "Adoption",
    "glanceFilter",
  );

  const filter = useMemo(() => {
    return between(startDate, endDate);
  }, [startDate, endDate]);

  const { data: activeUsersData } = useQuery(
    dateRangeQuery<DateAndCount[]>("active-users", startDate, endDate),
  );

  const { data: chatStatsData } = useQuery(
    dateRangeQuery<GetChatStatsResponse_ChatStats[]>(
      "chat-stats",
      startDate,
      endDate,
    ),
  );

  const averageDailyActiveUsersValue = useMemo(() => {
    if (!activeUsersData) {
      return null;
    }

    const avgDailyActiveUsers = avgPropertyByDay(
      activeUsersData,
      "count",
      filter,
    );
    return Math.round(avgDailyActiveUsers).toLocaleString();
  }, [activeUsersData, filter]);

  const averageDailyChatMessagesValue = useMemo(() => {
    if (!chatStatsData) {
      return null;
    }

    const avgChatsPerDay = avgPropertyByDay(
      chatStatsData,
      "requestCount",
      filter,
    );
    return Math.round(avgChatsPerDay).toLocaleString();
  }, [chatStatsData, filter]);

  return (
    <SectionLayout
      title={title}
      activeFilter={glanceFilter}
      setActiveFilter={setGlanceFilter}
    >
      <Grid columns={{ initial: "1", sm: "2" }} gap="4">
        <MetricCard
          title={t("averageDailyActiveUsers").title}
          value={averageDailyActiveUsersValue}
        />

        <MetricCard
          title={t("dailyChatMessages").title}
          definition={t("dailyChatMessages").definition}
          value={averageDailyChatMessagesValue}
        />
      </Grid>
    </SectionLayout>
  );
}
