import { useMemo } from "react";
import { Grid } from "@radix-ui/themes";
import { t } from "../../utils/helpers";
import { useAppStore } from "../../utils/app-store";
import { between } from "../../utils/data";
import { useDateFilter } from "../../utils/date";
import {
  avgPropertyByDay,
  sumByProperty,
} from "../../utils/value-calculations";
import SectionLayout from "../global/SectionLayout";
import MetricCard from "../cards/MetricCard";

import type {
  DateAndCount,
  GetCompletionStatsResponse_CompletionStats,
  GetEditStatsResponse_EditStats,
} from "../../../../../request_insight/analytics/request_insight_analytics_pb";
import { useQuery } from "@tanstack/react-query";
import { dateRangeQuery } from "app/client-cache";

export default function Overview({
  title,
  description,
}: {
  title: string;
  description: string;
}) {
  const earliestData = useAppStore((state) => state.earliestData);

  const [glanceFilter, setGlanceFilter, startDate, endDate] = useDateFilter(
    earliestData,
    "Overview",
    "glanceFilter",
  );

  const filter = useMemo(() => {
    return between(startDate, endDate);
  }, [startDate, endDate]);

  const { data: activeUsersData } = useQuery(
    dateRangeQuery<DateAndCount[]>("active-users", startDate, endDate),
  );

  const { data: completionStatsData } = useQuery(
    dateRangeQuery<GetCompletionStatsResponse_CompletionStats[]>(
      "completion-stats",
      startDate,
      endDate,
    ),
  );

  const { data: chatStatsData } = useQuery(
    dateRangeQuery<GetCompletionStatsResponse_CompletionStats[]>(
      "chat-stats",
      startDate,
      endDate,
    ),
  );

  const { data: editStatsData } = useQuery(
    dateRangeQuery<GetEditStatsResponse_EditStats[]>(
      "edit-stats",
      startDate,
      endDate,
    ),
  );

  const avgDailyActiveUsersValue = useMemo(() => {
    if (!activeUsersData) {
      return null;
    }

    const averageDailyActiveUsers = avgPropertyByDay(
      activeUsersData,
      "count",
      filter,
    );
    return Math.round(averageDailyActiveUsers).toLocaleString();
  }, [activeUsersData, filter]);

  const completionStatsValue = useMemo(() => {
    if (!completionStatsData) {
      return null;
    }

    return sumByProperty(
      completionStatsData,
      "acceptedCount",
      filter,
    ).toLocaleString();
  }, [completionStatsData, filter]);

  const editStatsValue = useMemo(() => {
    if (!editStatsData) {
      return null;
    }

    return sumByProperty(
      editStatsData,
      "acceptedCount",
      filter,
    ).toLocaleString();
  }, [editStatsData, filter]);

  const chatStatsValue = useMemo(() => {
    if (!chatStatsData) {
      return null;
    }

    return sumByProperty(
      chatStatsData,
      "requestCount",
      filter,
    ).toLocaleString();
  }, [chatStatsData, filter]);

  return (
    <SectionLayout
      title={title}
      description={description}
      activeFilter={glanceFilter}
      setActiveFilter={setGlanceFilter}
    >
      <Grid columns={{ initial: "1", sm: "2", md: "4" }} gap="4">
        <MetricCard
          title={t("averageDailyActiveUsers").title}
          value={avgDailyActiveUsersValue}
        />

        <MetricCard
          title={t("completionsAccepted").title}
          value={completionStatsValue}
        />

        <MetricCard title={t("chatMessages").title} value={chatStatsValue} />

        <MetricCard
          title={t("instructionsAccepted").title}
          value={editStatsValue}
        />
      </Grid>
    </SectionLayout>
  );
}
