import { useMemo } from "react";
import { Grid } from "@radix-ui/themes";
import { t } from "../../utils/helpers";
import { useAppStore } from "../../utils/app-store";
import { between } from "../../utils/data";
import { useDateFilter } from "../../utils/date";
import { combineSums, sumByProperty } from "../../utils/value-calculations";
import SectionLayout from "../global/SectionLayout";
import MetricCard from "../cards/MetricCard";
import type {
  GetCompletionStatsResponse_CompletionStats,
  GetEditStatsResponse_EditStats,
} from "../../../../../request_insight/analytics/request_insight_analytics_pb";
import { useQuery } from "@tanstack/react-query";
import { dateRangeQuery } from "app/client-cache";

export default function CodeChanges({ title }: { title: string }) {
  const earliestData = useAppStore((state) => state.earliestData);

  const [glanceFilter, setGlanceFilter, startDate, endDate] = useDateFilter(
    earliestData,
    "CodeChanges",
    "glanceFilter",
  );

  const filter = useMemo(() => {
    return between(startDate, endDate);
  }, [startDate, endDate]);

  const { data: completionStatsData } = useQuery(
    dateRangeQuery<GetCompletionStatsResponse_CompletionStats[]>(
      "completion-stats",
      startDate,
      endDate,
    ),
  );

  const { data: editStatsData } = useQuery(
    dateRangeQuery<GetEditStatsResponse_EditStats[]>(
      "edit-stats",
      startDate,
      endDate,
    ),
  );

  const completionStatsValue = useMemo(() => {
    if (!completionStatsData) {
      return null;
    }

    return sumByProperty(
      completionStatsData,
      "acceptedCount",
      filter,
    ).toLocaleString();
  }, [completionStatsData, filter]);

  const editStatsValue = useMemo(() => {
    if (!editStatsData) {
      return null;
    }

    return sumByProperty(
      editStatsData,
      "acceptedCount",
      filter,
    ).toLocaleString();
  }, [editStatsData, filter]);

  const codeLinesValue = useMemo(() => {
    if (!editStatsData || !completionStatsData) {
      return null;
    }

    return combineSums(
      editStatsData,
      "acceptedLineCount",
      completionStatsData,
      "acceptedLineCount",
      filter,
    ).toLocaleString();
  }, [editStatsData, completionStatsData, filter]);

  return (
    <SectionLayout
      title={title}
      activeFilter={glanceFilter}
      setActiveFilter={setGlanceFilter}
    >
      <Grid columns={{ initial: "1", sm: "3" }} gap="4">
        <MetricCard
          title={t("completionsAccepted").title}
          definition={t("completionsAccepted").definition}
          value={completionStatsValue}
        />

        <MetricCard
          title={t("instructionsAccepted").title}
          definition={t("instructionsAccepted").definition}
          value={editStatsValue}
        />

        <MetricCard
          title={t("codeLines").title}
          definition={t("codeLines").definition}
          value={codeLinesValue}
        />
      </Grid>
    </SectionLayout>
  );
}
