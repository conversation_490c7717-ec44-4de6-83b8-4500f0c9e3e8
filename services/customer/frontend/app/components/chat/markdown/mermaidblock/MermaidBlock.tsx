import type React from "react";
import { useEffect, useRef, useState } from "react";
import mermaid from "mermaid";
import "./mermaid-block.css";
import Codeblock, { type CodeblockButton } from "../codeblock/Codeblock";
import { useTheme } from "next-themes";

export interface MermaidBlockProps {
  code: string;
  meta: {
    [k: string]: string;
  };
}

const MermaidBlock: React.FC<MermaidBlockProps> = ({ code, meta }) => {
  const [diagramId] = useState(`mermaid-${crypto.randomUUID()}`);
  const [showDiagram, setShowDiagram] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const diagramElement = useRef<HTMLDivElement>(null);
  const { resolvedTheme } = useTheme();

  // Initialize mermaid
  useEffect(() => {
    try {
      mermaid.initialize({
        startOnLoad: false,
        theme: resolvedTheme === "dark" ? "dark" : "default",
      });
      setIsInitialized(true);
    } catch (err) {
      console.error("Mermaid initialization failed:", err);
      setError(true);
    }
  }, [resolvedTheme]);
  useEffect(() => {
    const render = async () => {
      try {
        if (!diagramElement.current || !isInitialized) {
          return;
        }
        const { svg } = await mermaid.render(
          diagramId,
          code,
          diagramElement.current,
        );
        diagramElement.current.innerHTML = svg;
        const diagramSvg = diagramElement.current.querySelector<SVGSVGElement>(
          `#${diagramId}`,
        );
        if (!diagramSvg) {
          throw new Error("Diagram SVG not found");
        }
        diagramSvg.style.maxWidth = "100%";
        setIsLoading(false);
        setError(false);
      } catch (error) {
        console.error("Mermaid diagram failed to render: ", error);
        setIsLoading(false);
        setError(true);
      }
    };
    if (showDiagram) {
      render();
    }
  }, [code, diagramId, showDiagram, resolvedTheme, isInitialized]);

  const toggleDiagram: CodeblockButton = {
    title: showDiagram ? "Show Code" : "Show Diagram",
    text: showDiagram ? "Code" : "Diagram",
    onClick: () => setShowDiagram((show) => !show),
    icon: showDiagram ? "data_object" : "account_tree",
  };

  return (
    <Codeblock
      code={code}
      language="mermaid"
      meta={meta}
      buttons={error ? [] : [toggleDiagram]}
      height={error ? "auto" : 500}
    >
      {showDiagram && !error ? (
        <div className={`mermaid-content ${isLoading ? "mermaid-hide" : ""}`}>
          <div className="mermaid-diagram" ref={diagramElement} />
        </div>
      ) : null}
    </Codeblock>
  );
};

export default MermaidBlock;
