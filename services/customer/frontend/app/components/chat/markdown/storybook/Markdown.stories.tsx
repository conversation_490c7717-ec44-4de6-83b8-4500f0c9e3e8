import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Flex } from "@radix-ui/themes";
import Markdown from "../Markdown";

const meta = {
  title: "chat/Markdown",
  component: Markdown,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    content: {
      description: "The input markdown text to be rendered",
      control: { type: "text" },
    },
  },
  // Default Values
  args: {
    content: `
# Title

This is some paragraph text.
    `,
  },
} satisfies Meta<typeof Markdown>;

export default meta;

type Story = StoryObj<typeof meta>;

// Stories
export const AugmentChatMessage: Story = {
  args: {
    content: `
Certainly! I'll create a simple Mermaid diagram for you. Here's an example of a basic flowchart:

\`\`\`mermaid path=clients/common/webviews/src/apps/chat/components/markdown-ext/MermaidBlock.svelte mode=EDIT
graph TD
    A[Start] --> B{Is it raining?}
    B -- Yes --> C[Take an umbrella]
    B -- No --> D[Enjoy the weather]
    C --> E[Go outside]
    D --> E
    E --> F[End]
\`\`\`
This Mermaid diagram represents a simple decision flowchart about whether to take an umbrella based on the weather. Here's a breakdown of the diagram:

1. It starts with a "Start" node (A).
2. It then goes to a decision node (B) asking "Is it raining?".
3. If yes, it leads to "Take an umbrella" (C).
4. If no, it leads to "Enjoy the weather" (D).
5. Both C and D lead to "Go outside" (E).
6. Finally, it ends with an "End" node (F).

This diagram should render correctly in the MermaidBlock component you're working with, as it uses the Mermaid syntax to generate the flowchart.
    `,
  },
  render: (args: any) => (
    <Flex justify="end" direction="column" align="end" gap="1">
      <div className="c-chat-message__content">
        <Markdown {...args} />
      </div>
    </Flex>
  ),
};
