import { useEffect, useId, type ReactNode } from "react";
import { useAtom, atom } from "jotai";
import { Dialog, Flex } from "@radix-ui/themes";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import type { AnyFunction } from "@augment-internal/ts-utils/type";
import { cn, typography } from "app/utils/style";
import { Enabled } from "../Enabled";
import { isNonNullable } from "app/utils/guards";
import { useWizardContext } from "../Wizard/Wizard";

// Global atom to track which dialog is currently open
export const dialogOpenAtom = atom<string | null>(null);

type ShouldChange = boolean;
type ShouldClose = boolean;

type WithClose = (
  fn: AnyFunction,
) => (...args: unknown[]) => void | ShouldClose;
type PlainClose = (...args: Exclude<unknown, AnyFunction>[]) => void;

export type CloseFnProp = WithClose & PlainClose;

export type ModalProps = {
  /** The title of the modal - can be a string or React node */
  title?: ReactNode;

  /** The description of the modal (optional) - can be a string or React node */
  description?: ReactNode;

  /** The content of the modal */
  children: ReactNode | (<T extends CloseFnProp>(close: T) => ReactNode);

  /** The trigger that opens the modal */
  trigger?: (open: () => void) => ReactNode;

  /** Optional footer content with close function */
  footer?: ReactNode | (<T extends CloseFnProp>(close: T) => ReactNode);

  /**
   * Optional data-testid attribute for testing
   * If not provided, an auto-generated ID will be used
   */
  "data-testid"?: string;

  /** Optional className for the modal content */
  className?: string;

  /** Optional flag to indicate if the modal has unsaved changes */
  isDirty?: boolean;

  /** Optional flag to indicate if the modal is open */
  isOpen?: boolean;

  /**
   * Callback when the open state changes
   * @returns `false` to prevent the open state from changing
   */
  onOpenChange?: (isOpen: boolean) => void | ShouldChange;
} & Omit<Dialog.ContentProps, "onOpenChange" | "children">;

/**
 * A generic modal component built with Radix Themes Dialog
 *
 * Behavior:
 * - on Click (Overlay), KeyPress (Esc), Click (CloseButton): close modal
 * - on mount: focus trap
 * - Rendered with Portal
 */
export function Modal(props: ModalProps) {
  const {
    title,
    description,
    children,
    footer,
    trigger,
    className,
    isOpen: externalIsOpen,
    onOpenChange,
    ...rest
  } = props;

  // Determine if this modal is controlled externally
  const isControlled = isNonNullable(externalIsOpen);
  // Get the global dialog state
  const [dialogOpen, setDialogOpen] = useAtom(dialogOpenAtom);
  const id = useId();

  const { isInsideWizard } = useWizardContext();

  const isOpen = isControlled ? externalIsOpen : dialogOpen === id;

  useEffect(() => {
    return () => {
      if (!isInsideWizard && isOpen && dialogOpen === id) {
        setDialogOpen(null);
      }
    };
  }, [setDialogOpen, isOpen, dialogOpen, id, isInsideWizard]);

  function handleOpenChange(isOpen: boolean) {
    if (isInsideWizard) {
      console.warn(
        "Modal's open state should be controlled by Wizard when inside a Wizard",
      );
      return;
    }

    if (isOpen && dialogOpen !== null && dialogOpen !== id) {
      console.warn("Another modal is already open");
      return;
    }

    const shouldChange = onOpenChange?.(isOpen);
    if (shouldChange === false) return;
    setDialogOpen(isOpen ? id : null);
  }

  const withClose = ((fn) =>
    typeof fn === "function"
      ? async () => {
          const shouldClose = await fn();
          if (shouldClose === false) return;
          handleOpenChange(false);
        }
      : handleOpenChange(false)) as CloseFnProp;

  if (isInsideWizard) {
    return (
      <ModalContent
        title={title}
        description={description}
        footer={footer}
        className={className}
        isOpen={true}
        withClose={withClose}
      >
        {children}
      </ModalContent>
    );
  }

  // Render the trigger and dialog
  return (
    <>
      {trigger?.(() => handleOpenChange(true))}

      <Dialog.Root open={isOpen} onOpenChange={handleOpenChange}>
        <Dialog.Content
          maxWidth="600px"
          aria-modal="true"
          role="dialog"
          aria-label={title ? String(title) : "Dialog"}
          aria-describedby={description ? String(description) : undefined}
          {...rest}
          asChild
        >
          <div className={cn(className, "modal-content")}>
            <ModalContent
              title={title}
              description={description}
              footer={footer}
              className={className}
              isOpen={isOpen}
              withClose={withClose}
            >
              {children}
            </ModalContent>
          </div>
        </Dialog.Content>
      </Dialog.Root>
    </>
  );
}

type ModalContentProps = Pick<
  ModalProps,
  "title" | "description" | "footer" | "className" | "children"
> & {
  isOpen: boolean;
  withClose: CloseFnProp;
};

const ModalContent = function ModalContent({
  title,
  description,
  children,
  footer,
  isOpen,
  withClose,
}: ModalContentProps) {
  return (
    <>
      <Enabled enabled={Boolean(title || description)} asChild>
        <div className="modal-header">
          <Enabled enabled={Boolean(title)}>
            <Dialog.Title className="modal-title">{title}</Dialog.Title>
          </Enabled>
          <Enabled enabled={Boolean(description)}>
            <Dialog.Description className="modal-description">
              {description}
            </Dialog.Description>
          </Enabled>
        </div>
      </Enabled>
      <VisuallyHidden>
        <Dialog.Title>{title ?? description ?? "Dialog"}</Dialog.Title>
      </VisuallyHidden>

      <div className="modal-body" key={isOpen ? "modal-open" : "modal-closed"}>
        {typeof children === "function" ? children(withClose) : children}
      </div>

      {footer && (
        <Flex gap="3" mt="4" justify="end" className="modal-footer">
          {typeof footer === "function" ? footer(withClose) : footer}
        </Flex>
      )}

      <style scoped>
        {`
      @keyframes overlayShow {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }

      @keyframes contentShow {
        from {
          opacity: 0;
          transform: translate(-50%, -48%) scale(0.96);
        }
        to {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
      }

      .animate-overlayShow {
        animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
      }

      .animate-contentShow {
        animation: contentShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
      }

      :scope {
        display: flex;
        flex-direction: column;
        gap: var(--ds-spacing-2);
        padding: var(--ds-spacing-4);
      }

      .modal-header {
        margin-bottom: var(--ds-spacing-2);
      }

      .modal-title {
        font-size: var(--ds-font-size-5);
        font-weight: var(--ds-font-weight-medium);
        line-height: var(--ds-line-height-5);
        color: var(--ds-text-default);
        margin-bottom: 0;
      }

      .modal-description {
        ${typography.text2.regular}
        line-height: 1.5;
        color: var(--ds-color-text-subtle);
      }

      .modal-body {
        ${typography.text2.regular}
        margin-top: 0;
        margin-bottom: var(--ds-spacing-2);
      }

      .modal-footer {
        font-size: var(--ds-font-size-3);
        margin-top: 0;
      }
    `}
      </style>
    </>
  );
};

export default Modal;
