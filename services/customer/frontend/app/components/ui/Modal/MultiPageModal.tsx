import { useState, type ReactNode } from "react";
import { Modal, type CloseFnProp, type ModalProps } from "./Modal";
import { clamp } from "app/utils/number";
import { isNonNullable } from "app/utils/guards";

export type PageProps = Pick<ModalProps, "title" | "description"> & {
  content:
    | ReactNode
    | (<T extends CloseFnProp>(
        close: T,
        setPage: (page: number) => void,
      ) => ReactNode);
  footer?:
    | ReactNode
    | (<T extends CloseFnProp>(
        close: T,
        setPage: (page: number) => void,
      ) => ReactNode);
};

// In the future we could allow consumers to specify title, description, and footer which would be fallbacks
// should the children not specify them. That would increase the complexity and testing requirements of this component
// so we will not do that at this time.
type MultiPageModalProps = Omit<
  ModalProps,
  "title" | "description" | "footer" | "children"
> & {
  pages: PageProps[];
  pageNumber?: number;
  setPageNumber?: (pageNumber: number) => void;
};

export function MultiPageModal(props: MultiPageModalProps) {
  const {
    pageNumber: externalPageNumber,
    setPageNumber: externalSetPageNumber,
    pages: propsPages,
    ...restProps
  } = props;
  const isControlled =
    isNonNullable(externalPageNumber) && isNonNullable(externalSetPageNumber);
  const [internalPageNumber, internalSetPageNumber] = useState(0);
  const pageNumber = isControlled ? externalPageNumber : internalPageNumber;
  const setPageNumber = isControlled
    ? externalSetPageNumber
    : internalSetPageNumber;

  const pages = Array.isArray(propsPages) ? propsPages : [propsPages];

  const safePage = clamp(pageNumber, 0, pages.length - 1);
  const pageProps = pages[safePage];

  const footerFn = (withClose: CloseFnProp) => {
    if (typeof pageProps.footer === "function") {
      return pageProps.footer(withClose, setPageNumber);
    }
    return pageProps.footer;
  };

  const contentFn = (withClose: CloseFnProp) => {
    if (typeof pageProps.content === "function") {
      return pageProps.content(withClose, setPageNumber);
    }
    return pageProps.content;
  };

  return (
    <Modal
      title={pageProps.title}
      description={pageProps.description}
      footer={footerFn}
      {...restProps}
    >
      {contentFn}
    </Modal>
  );
}
