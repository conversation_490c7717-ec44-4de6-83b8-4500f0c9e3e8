import {
  Link as RemixLink,
  useNavigate,
  useRouteError,
} from "@remix-run/react";
import {
  Theme,
  Container,
  Flex,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Link as RadixLink,
} from "@radix-ui/themes";
import { UnauthorizedError } from "app/client-cache/error";

const errorMessages: { [key: string]: string } = {
  "Not Found": "The page you are looking for does not exist.",
};

type RouteError =
  | {
      status: number;
      statusText: string;
      internal: boolean;
      data: { message?: string; requestId?: string };
    }
  | Error;

function processError(error: RouteError) {
  if (error instanceof UnauthorizedError) {
    return error;
  }
  if (error instanceof Error) {
    return {
      status: 500,
      statusText: "Internal Server Error",
      internal: true,
      data: error,
    };
  }
  return error;
}

export function GeneralErrorBoundary() {
  const error = processError(useRouteError() as RouteError);
  const { status = 500, statusText = "Internal Server Error", data } = error;
  const navigate = useNavigate();

  // Extract requestId safely
  const requestId =
    data && typeof data === "object" && "requestId" in data
      ? data.requestId
      : undefined;

  let content = null;
  if (status === 401) {
    navigate("/login");
  } else if (status === 403) {
    content = (
      <>
        <img
          src="/augie-inspector.svg"
          alt="Augment Error"
          width="64"
          height="64"
        />
        <Text mt="2" mb="4" align="center">
          Sorry, but you don&apos;t have permission to access this page. Admin
          privileges are required. Refer to the{" "}
          <RadixLink href="https://docs.augmentcode.com/">
            Augment docs site
          </RadixLink>{" "}
          for more details.
        </Text>
      </>
    );
  } else {
    let message = errorMessages[statusText] ?? data?.message;
    if (!message) {
      message = "An unexpected error occurred. Please try again later.";
    }

    content = (
      <>
        <img
          src="/augie-error.svg"
          alt="Augment Error"
          width="64"
          height="64"
        />
        <Text size="6" weight="bold" mt="2">
          Error {status}
        </Text>
        <Text size="5" mt="2" mb="2">
          {message}
        </Text>
        <Text size="2" mb="4" color="gray" align="center">
          Time: {new Date().toISOString()}
          {requestId && (
            <>
              <br />
              Request ID: {requestId}
            </>
          )}
        </Text>
      </>
    );
  }

  return (
    <Theme
      accentColor="crimson"
      grayColor="sand"
      panelBackground="solid"
      scaling="95%"
    >
      <Container size="2">
        <Card m="4" variant="surface">
          <Flex direction="column" align="center" justify="center">
            {content}
            <RemixLink to="/logout" reloadDocument>
              <Button size="2" variant="surface">
                Go Back
              </Button>
            </RemixLink>
          </Flex>
        </Card>
      </Container>
    </Theme>
  );
}
