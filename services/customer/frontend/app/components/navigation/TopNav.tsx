import { Box, Flex, TabNav } from "@radix-ui/themes";
import { NavLink, useLocation } from "@remix-run/react";
import { useQuery } from "@tanstack/react-query";
import {
  userQueryOptions,
  planChangeInProgressAtom,
  subscriptionCreationPendingAtom,
} from "app/client-cache";
import { CSSTransition } from "app/components/ui/CSSTransitions/CSSTransition";
import {
  fadeAnimation,
  translateYAnimation,
} from "app/components/ui/CSSTransitions/animations";
import { cn } from "app/utils/style";
import { useAtomValue } from "jotai";
import { useRef } from "react";
import { BaseHeader } from "./BaseHeader";
import { PlanDisplay } from "./PlanDisplay";

export default function TopNav() {
  const containerWidth = "1200px";
  const { data: userData } = useQuery(userQueryOptions);

  // Extract data from query with defaults for loading states
  const tenantTier = userData?.tenantTier;
  const isSubscriptionCreationInProgress = useAtomValue(
    subscriptionCreationPendingAtom,
  );
  const isPlanChangeInProgress = useAtomValue(planChangeInProgressAtom);
  const isPending = isSubscriptionCreationInProgress || isPlanChangeInProgress;
  const isAdmin = userData?.isAdmin ?? false;
  const showTeamManagementLink = userData?.showTeamManagementLink ?? false;

  const location = useLocation();

  const tabNavRef = useRef(null);

  let tabLinks: Array<{
    label: string;
    link: string;
  }> = [];
  // can't do anything else on the select plan page or when plan change is pending.
  if (
    location.pathname !== "/account/select-plan" &&
    tenantTier &&
    !isPending
  ) {
    if (tenantTier == "enterprise" && isAdmin) {
      tabLinks.push(
        { label: "Overview", link: "/dashboard/overview" },
        { label: "Code Changes", link: "/dashboard/code-changes" },
        { label: "Adoption", link: "/dashboard/adoption" },
      );
    }
    if (tenantTier !== "enterprise") {
      tabLinks.push({ label: "Subscription", link: "/account/subscription" });
    }
    if (showTeamManagementLink) {
      tabLinks.push({ label: "Team", link: "/account/team" });
    }
  }

  if (tabLinks.length <= 1) {
    tabLinks = [];
  }

  return (
    <Box className="topnav-container">
      <style scoped>
        {`
          /* Scoped styles for TopNav component */
          .tab-link {
            font-weight: 600 !important;
            font-size: 15px !important;
            padding: 8px 12px !important;
            letter-spacing: 0 !important;
            text-rendering: optimizeLegibility !important;
            -webkit-font-smoothing: antialiased !important;
            font-feature-settings: "kern" !important;
          }

          .tab-link-active {
            color: var(--slate-11) !important;
            opacity: 1 !important;
          }

          .tab-link-inactive {
            opacity: 0.7 !important;
          }

          /* Target inner elements as well */
          .tab-link a,
          .tab-link span,
          .tab-link div {
            font-weight: 600 !important;
            font-size: 15px !important;
            letter-spacing: 0 !important;
          }

          /* Tab navigation animation with CSSTransition */
          :scope .tab-nav-container {
            overflow: hidden;
            margin-top: 12px;
          }

          /* Use CSSTransition animation functions */
          ${fadeAnimation("tab-nav", 0, 1)}
          ${translateYAnimation("-10px", "0px")}

          /* Custom height animation for tab navigation */
          :scope .tab-nav-enter {
            max-height: 0;
          }

          :scope .tab-nav-enter-active {
            max-height: 60px;
          }

          :scope .tab-nav-exit {
            max-height: 60px;
          }

          :scope .tab-nav-exit-active {
            max-height: 0;
          }

          /* Plan section styles */
          :scope .topnav-plan-section {
            position: relative;
            z-index: 1;
          }

          :scope .topnav-plan-section.hidden {
            opacity: 0;
          }

          :scope .topnav-plan-text-real {
            font-size: 15px;
            letter-spacing: -0.02em;
          }

          /* Plan text gradient styles */
          :scope .topnav-plan-text-gradient {
            background: linear-gradient(90deg, var(--plan-color), color-mix(in srgb, var(--plan-color) 70%, #000));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            color: inherit;
          }

          :scope .topnav-plan-text-pending {
            background: none;
            -webkit-background-clip: unset;
            -webkit-text-fill-color: var(--plan-color);
            color: var(--plan-color);
          }

          /* TopNav container - sticky positioning for entire header + nav */
          :scope.topnav-container {
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            padding-bottom: 18px;
            margin-bottom: 24px;
          }

          :scope .topnav-content {
            margin: 0 auto;
            width: 100%;
            padding: 0 32px;
            /* Ensure consistent layout during loading */
            min-height: 38px;
          }

          /* Tab navigation styles */
          :scope .topnav-tabs {
            border-bottom: none;
            position: relative;
            z-index: 2;
            margin-top: 6px;
          }
        `}
      </style>
      <BaseHeader containerWidth={containerWidth} sticky={false}>
        <PlanDisplay />
      </BaseHeader>
      <Box className="topnav-content" style={{ maxWidth: containerWidth }}>
        <Flex direction="column" gap="0">
          <CSSTransition
            in={tabLinks.length > 0}
            timeout={300}
            baseClassName="tab-nav"
            unmountOnExit
            nodeRef={tabNavRef}
          >
            <Box className="tab-nav-container" ref={tabNavRef}>
              <TabNav.Root size="2" className="topnav-tabs">
                {tabLinks.map((tabLink) => (
                  <TabNav.Link
                    key={tabLink.link}
                    asChild
                    active={location.pathname === tabLink.link}
                    className={cn("tab-link", {
                      "tab-link-active": location.pathname === tabLink.link,
                      "tab-link-inactive": location.pathname !== tabLink.link,
                    })}
                  >
                    <NavLink to={tabLink.link} prefetch="intent">
                      {tabLink.label}
                    </NavLink>
                  </TabNav.Link>
                ))}
              </TabNav.Root>
            </Box>
          </CSSTransition>
        </Flex>
      </Box>
    </Box>
  );
}
