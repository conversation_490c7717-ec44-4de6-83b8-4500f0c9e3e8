import { Card, Grid, Box } from "@radix-ui/themes";
import Metric from "./Metric";
import AreaChart from "../charts/AreaChart";
import type { ChartDataPointSchema } from "app/schemas/chart";

interface ComboCardProps {
  title: string;
  definition?: string;
  metricpy?: string;
  metricValue: string | null;
  areaChartData: ChartDataPointSchema[] | null;
  color: string;
}

export default function ComboCard({
  title,
  definition,
  metricpy = "4",
  metricValue,
  areaChartData,
  color,
}: ComboCardProps) {
  return (
    <Card variant="classic">
      <Grid
        height="100%"
        gap="4"
        columns="2"
        style={{ gridTemplateColumns: "40% 60%" }}
      >
        <Metric
          title={title}
          definition={definition}
          py={metricpy}
          value={metricValue}
        />
        <Box
          flexGrow="1"
          flexShrink="1"
          flexBasis="0%"
          width="92%"
          height="100%"
        >
          <AreaChart
            data={areaChartData}
            showTrendLine={false}
            showAxis={false}
            color={color}
          />
        </Box>
      </Grid>
    </Card>
  );
}
