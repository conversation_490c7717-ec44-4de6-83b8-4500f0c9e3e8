import type { Meta, StoryObj } from "@storybook/react";
import { PurchaseCreditsModal } from "./PurchaseCreditsModal";
import { generateMock } from "@anatine/zod-mock";
import { OrbCustomerInfoSchema } from "app/schemas/orb";
import { delay } from "@augment-internal/ts-utils/timer";
import { HttpResponse } from "msw";

const mockOrbCustomerInfo = {
  ...generateMock(OrbCustomerInfoSchema),
  additionalUsageUnitCost: "0.********",
};

const meta = {
  title: "Components/Account/Billing/PurchaseCreditsModal",
  component: PurchaseCreditsModal,
  parameters: {
    layout: "fullscreen",
    msw: {
      overrides: (http) => [
        http.get("/api/subscription", async () => {
          await delay(1000);
          return HttpResponse.json(mockOrbCustomerInfo);
        }),
      ],
    },
  },
} satisfies Meta<typeof PurchaseCreditsModal>;

export default meta;
type Story = StoryObj<typeof PurchaseCreditsModal>;

export const Default: Story = {
  args: {},
};
