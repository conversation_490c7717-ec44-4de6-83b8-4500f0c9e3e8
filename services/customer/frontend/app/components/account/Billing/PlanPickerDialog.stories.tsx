import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Button, Text } from "@radix-ui/themes";
import { useState } from "react";
import { fn } from "@storybook/test";
import { PlanPickerDialog } from "./PlanPickerDialog";
import { getPlanDescriptionWithPricingLink } from "./utils";
import { samplePlans, customPlans } from "./storyData";

// Wrapper component to control dialog state
function PlanPickerDialogWithControls(props: any) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string | undefined>(
    undefined,
  );

  return (
    <div className="container">
      <Button
        size="3"
        variant="solid"
        color="blue"
        onClick={() => setIsOpen(true)}
        className="open-button"
      >
        Open Plan Picker
      </Button>
      <div className="selection-status">
        {selectedPlan ? (
          <Text size="3" weight="bold" className="selected-text">
            Selected Plan: <span className="plan-name">{selectedPlan}</span>
          </Text>
        ) : (
          <Text size="3" color="gray">
            No plan selected
          </Text>
        )}
      </div>
      <PlanPickerDialog
        {...props}
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        onSelectPlan={(planId) => {
          setSelectedPlan(planId);
        }}
      />
    </div>
  );
}

// Always open version for Storybook
function AlwaysOpenPlanPickerDialog(props: any) {
  const [selectedPlan, setSelectedPlan] = useState<string | undefined>(
    undefined,
  );

  return (
    <div className="container">
      <div
        className={`status-box ${selectedPlan ? "selected" : "not-selected"}`}
      >
        {selectedPlan ? (
          <Text size="3" weight="bold" className="selected-text">
            Selected Plan: <span className="plan-name">{selectedPlan}</span>
          </Text>
        ) : (
          <Text size="3" weight="bold" className="not-selected-text">
            No plan selected
          </Text>
        )}
      </div>
      <PlanPickerDialog
        {...props}
        isOpen={true}
        onSelectPlan={(planId) => {
          setSelectedPlan(planId);
        }}
      />
    </div>
  );
}

const meta = {
  title: "Components/Account/PlanPickerDialog",
  component: PlanPickerDialog,
  parameters: {
    layout: "centered",
  },
  args: {
    plans: samplePlans,
    title: "Choose Your Plan",
    description: getPlanDescriptionWithPricingLink(),
    isOpen: true,
    onOpenChange: fn(),
    onSelectPlan: fn(),
  },
} satisfies Meta<typeof PlanPickerDialog>;

export default meta;
type Story = StoryObj<typeof PlanPickerDialog>;

// Interactive story with controls
export const Interactive: Story = {
  render: (args) => <PlanPickerDialogWithControls {...args} />,
};

// Main variants
export const Default: Story = {
  render: (args) => <AlwaysOpenPlanPickerDialog {...args} />,
};

export const WithCurrentPlan: Story = {
  args: {
    currentPlanId: "orb_developer_plan",
  },
  render: (args) => <AlwaysOpenPlanPickerDialog {...args} />,
};

export const WithCurrentMaxPlan: Story = {
  args: {
    currentPlanId: "orb_max_plan",
  },
  render: (args) => <AlwaysOpenPlanPickerDialog {...args} />,
};

export const WithCommunityPlan: Story = {
  args: {
    currentPlanId: "orb_community_plan",
  },
  render: (args) => <AlwaysOpenPlanPickerDialog {...args} />,
};

export const CustomPlans: Story = {
  args: {
    plans: customPlans,
    currentPlanId: "basic",
  },
  render: (args) => <AlwaysOpenPlanPickerDialog {...args} />,
};
