import type { Meta } from "@storybook/react";
import { CancelSubscriptionModal } from "./CancelSubscriptionModal";
import { useState } from "react";
import { samplePlans } from "./storyData";

const meta = {
  title: "Components/Account/Billing/CancelSubscriptionModal",
  component: CancelSubscriptionModal,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof CancelSubscriptionModal>;

export default meta;

export const Default = () => {
  const [isOpen, setIsOpen] = useState(true);
  return (
    <CancelSubscriptionModal
      plans={samplePlans}
      currentPlanId={"orb_developer_plan"}
      onSelectPlan={() => {}}
      isOpen={isOpen}
      onOpenChange={setIsOpen}
    />
  );
};
