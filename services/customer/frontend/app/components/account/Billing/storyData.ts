import type { PlanOptionSchema } from "app/schemas/plan";

/**
 * Sample plan data for stories
 * This is shared between PlanPickerDialog.stories.tsx and SubscriptionPage.stories.tsx
 */
export const samplePlans: PlanOptionSchema[] = [
  {
    id: "orb_community_plan",
    augmentPlanType: "community",
    name: "Community Plan",
    description: "Community Plan plan with 50 user messages per month.",
    agentRequests: 50,
    hasTraining: true,
    hasTeams: false,
    price: "0.00",
    priceLabel: "Free",
    color: "var(--sky-8)",
    colorScheme: {
      radixColor: "sky",
      gradientStart: "#0ea5e9",
      gradientEnd: "#0284c7",
    },
  },
  {
    id: "orb_developer_plan",
    augmentPlanType: "paid",
    name: "Developer Plan",
    description: "Developer Plan plan with 600 user messages per month.",
    agentRequests: 600,
    hasTraining: false,
    hasTeams: true,
    price: "50.00",
    priceLabel: "$50.00/mo",
    color: "var(--indigo-9)",
    colorScheme: {
      radixColor: "indigo",
      gradientStart: "#4f46e5",
      gradientEnd: "#3730a3",
    },
  },
  {
    id: "orb_pro_plan",
    augmentPlanType: "paid",
    name: "Pro Plan",
    description: "Pro Plan plan with 1500 user messages per month.",
    agentRequests: 1500,
    hasTraining: false,
    hasTeams: true,
    price: "100.00",
    priceLabel: "$100.00/mo",
    color: "var(--purple-9)",
    colorScheme: {
      radixColor: "purple",
      gradientStart: "#8b5cf6",
      gradientEnd: "#6d28d9",
    },
  },
  {
    id: "orb_max_plan",
    augmentPlanType: "paid",
    name: "Max Plan",
    description: "Max Plan plan with 4500 user messages per month.",
    agentRequests: 4500,
    hasTraining: false,
    hasTeams: true,
    price: "250.00",
    priceLabel: "$250.00/mo",
    color: "var(--amber-10)",
    colorScheme: {
      radixColor: "amber",
      gradientStart: "#f59e0b",
      gradientEnd: "#d97706",
    },
  },
  {
    id: "mock_enterprise_plan",
    augmentPlanType: "enterprise",
    name: "Enterprise Plan",
    description:
      "Enterprise plans including SSO, OIDC, SCIM, Slack integration, dedicated support, and volume discounts",
    agentRequests: 0, // This will be overridden by display logic
    hasTraining: false,
    hasTeams: true,
    price: "0",
    priceLabel: "Custom",
    color: "var(--gray-12)",
    colorScheme: {
      radixColor: "gray",
      gradientStart: "#18181b",
      gradientEnd: "#09090b",
    },
  },
];

/**
 * Alternative custom plans for stories
 */
export const customPlans: PlanOptionSchema[] = [
  {
    id: "basic",
    augmentPlanType: "unknown",
    name: "Basic",
    description: "Essential features for small teams",
    agentRequests: 100,
    hasTraining: false,
    hasTeams: true,
    price: "9",
    priceLabel: "$9/mo",
    color: "var(--green-9)",
    colorScheme: {
      radixColor: "green",
      gradientStart: "#22c55e",
      gradientEnd: "#16a34a",
    },
  },
  {
    id: "pro",
    augmentPlanType: "unknown",
    name: "Pro",
    description: "Advanced features for growing teams",
    agentRequests: 1000,
    hasTraining: false,
    hasTeams: true,
    price: "29",
    priceLabel: "$29/mo",
    color: "var(--orange-9)",
    colorScheme: {
      radixColor: "orange",
      gradientStart: "#f97316",
      gradientEnd: "#ea580c",
    },
  },
];
