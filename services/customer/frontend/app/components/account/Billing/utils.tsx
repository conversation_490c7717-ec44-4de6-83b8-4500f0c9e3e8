/**
 * Returns a JSX element with the plan description and a link to the pricing page
 * @param customText Optional custom text to use instead of the default
 * @returns JSX element with description and pricing link
 */
export const getPlanDescriptionWithPricingLink = (
  customText: string = "Select the plan that best fits your needs.",
) => (
  <>
    {customText}{" "}
    <a
      href="https://www.augmentcode.com/pricing"
      target="_blank"
      rel="noopener noreferrer"
      style={{
        color: "var(--blue-9)",
        textDecoration: "underline",
      }}
    >
      View detailed plan information
    </a>
  </>
);
