import { But<PERSON>, Checkbox, Flex, Text } from "@radix-ui/themes";
import { PlanOptionCard } from "./PlanOptionCard";
import {
  isCommunityPlanOption,
  isEnterprisePlanOption,
  type PlanOptionSchema,
} from "app/schemas/plan";
import type { PageProps } from "app/components/ui/Modal/MultiPageModal";
import { CSSTransition, fadeAnimation } from "app/components/ui/CSSTransitions";
import { type ComponentProps, useEffect, useRef, useState } from "react";
import { Callout } from "app/components/ui/Callout";
import { USAGE_UNITS } from "app/data/constants";
import { useMutation } from "@tanstack/react-query";
import { createCheckoutSession, putUserOnPlan } from "app/client-cache";
import { toast } from "app/components/ui/Toast";
import { isChangeImmediate } from "app/business-logic/plan-changes";

export type UseChangePlanConfirmationPageDialogPartsProps = {
  selectedPlanId: string | null;
  currentPlanId?: string;
  scheduledPlanId?: string;
  plans: PlanOptionSchema[];
  hasPaymentMethod?: boolean;
};

export function useChangePlanConfirmationPageDialogParts({
  selectedPlanId,
  currentPlanId,
  scheduledPlanId,
  plans,
  hasPaymentMethod,
}: UseChangePlanConfirmationPageDialogPartsProps): PageProps {
  const [isProcessing, setIsProcessing] = useState(false);
  const [confirmationChecked, setConfirmationChecked] =
    useState<ComponentProps<typeof Checkbox>["checked"]>(false);
  const createCheckoutSessionMutation = useMutation(createCheckoutSession);

  const selectedPlan = plans.find((plan) => plan.id === selectedPlanId);
  const planMutation = useMutation(putUserOnPlan);

  if (!selectedPlan)
    return {
      title: "Error",
      description: "",
      content: () => <Callout type="error">Selected plan not found</Callout>,
      footer: () => <></>,
    };

  const handleSelectPlan = async () => {
    // Don't allow selecting the current plan
    if (selectedPlanId && !isEnterprise() && selectedPlanId !== currentPlanId) {
      setIsProcessing(true);

      try {
        // Check if the user has a payment method or if it's a Community plan
        const isCommunityPlan = isCommunityPlanOption(selectedPlan);

        if (!selectedPlan) {
          throw new Error("Selected plan not found");
        }

        // For Community plan or if user has payment method, proceed directly
        if (hasPaymentMethod || isCommunityPlan) {
          await planMutation.mutateAsync({ planId: selectedPlanId });
          setIsProcessing(false);
          return true; // shoud close
        }

        // For non-Community plans without payment method, create checkout session
        createCheckoutSessionMutation.mutate(
          { planId: selectedPlanId },
          {
            onSuccess: () => {
              // Keep isProcessing true during redirection
              // The redirect is handled in the mutation
            },
            onError: () => {
              // Only reset processing state on error
              setIsProcessing(false);
            },
          },
        );
        return false; // don't close
      } catch (error) {
        console.error("Error handling plan selection:", error);
        toast.error({
          title: "Error",
          description: "Failed to process your request. Please try again.",
        });
        setIsProcessing(false);
      }
    }
  };

  function isEnterprise() {
    const selectedPlan = plans.find((plan) => plan.id === selectedPlanId);
    return !!selectedPlan && isEnterprisePlanOption(selectedPlan);
  }

  function getButtonText() {
    if (isProcessing) {
      return "Processing...";
    } else if (selectedPlanId) {
      // Check if the selected plan is a Community plan
      return hasPaymentMethod ||
        (selectedPlan && isCommunityPlanOption(selectedPlan))
        ? "Select Plan"
        : "Proceed to Payment";
    } else {
      return "Select a Plan";
    }
  }

  function hasValidPlanPricing(
    currentPlanId: string | undefined,
    targetPlanId: string,
  ): boolean {
    try {
      isChangeImmediate(currentPlanId, targetPlanId, plans);
      return true;
    } catch (error) {
      return false;
    }
  }

  function getCheckboxText() {
    if (!selectedPlanId) return "";
    if (!selectedPlan)
      throw new Error("Error with plan selection, please contact support");

    let aiTrainingText = "";
    if (selectedPlan.hasTraining) {
      aiTrainingText = `By switching to the ${selectedPlan.name}, I agree to permit Augment to train AI models on my code and usage data.`;
    }

    // Determine if this is an upgrade (immediate) or downgrade (end of billing period)
    let isImmediate: boolean;
    let hasValidPricing = true;
    try {
      isImmediate = isChangeImmediate(currentPlanId, selectedPlanId, plans);
    } catch (error) {
      // If we can't determine upgrade status due to invalid pricing data,
      // mark pricing as invalid and disable the plan change
      hasValidPricing = false;
      isImmediate = false; // This value won't be used since hasValidPricing is false
    }

    return (
      <>
        <p>{aiTrainingText}</p>
        <p>
          {!hasValidPricing ? (
            <>
              Unable to determine plan change timing due to invalid pricing
              data. Please contact support for assistance.
            </>
          ) : isImmediate ? (
            <>
              I understand this change will take effect{" "}
              <strong>immediately</strong>, and I will lose any unused{" "}
              {USAGE_UNITS} from my monthly subscription, but any additional
              purchased {USAGE_UNITS} will remain.
            </>
          ) : (
            <>
              I understand this downgrade will take effect at the end of my
              billing period. My existing {USAGE_UNITS} and invoice amount will
              remain unchanged for the current billing period.
            </>
          )}
        </p>
      </>
    );
  }

  return {
    title: "Confirm Plan Change",
    description: "Review your plan change.",
    content: (
      <ChangePlanConfirmationPage
        selectedPlan={selectedPlan}
        confirmationChecked={confirmationChecked}
        setConfirmationChecked={setConfirmationChecked}
        checkboxText={getCheckboxText()}
      />
    ),
    footer: (close, setPage) => (
      <>
        <Button
          variant="soft"
          onClick={() => {
            setPage(0);
            setConfirmationChecked(false);
          }}
        >
          Back
        </Button>
        <Button
          size="2"
          variant="solid"
          onClick={close(handleSelectPlan)}
          disabled={
            !confirmationChecked ||
            !selectedPlanId ||
            isEnterprise() ||
            selectedPlanId === currentPlanId ||
            selectedPlanId === scheduledPlanId ||
            isProcessing ||
            (selectedPlanId
              ? !hasValidPlanPricing(currentPlanId, selectedPlanId)
              : false)
          }
          className="plan-select-button"
        >
          {getButtonText()}
        </Button>
      </>
    ),
  };
}

type ChangePlanConfirmationPageProps = {
  selectedPlan: PlanOptionSchema;
  confirmationChecked: ComponentProps<typeof Checkbox>["checked"];
  setConfirmationChecked: (checked: boolean) => void;
  checkboxText: React.ReactNode;
};

function ChangePlanConfirmationPage({
  selectedPlan,
  confirmationChecked,
  setConfirmationChecked,
  checkboxText,
}: ChangePlanConfirmationPageProps) {
  const [isVisible, setIsVisible] = useState(false);
  useEffect(() => {
    setIsVisible(true);
  }, []);
  const selectedPlanRef = useRef(null);
  const checkboxRef = useRef(null);

  if (!selectedPlan) return null;
  return (
    <div className="plan-confirmation">
      <style scoped>{`
          ${fadeAnimation("selected-plan-container")}
          ${fadeAnimation("confirmation-checkbox-container")}

          :scope.plan-confirmation {
            display: flex;
            flex-direction: column;
            gap: var(--ds-spacing-4);
            min-height: 200px;
          }

          .confirmation-card {
            background: var(--ds-color-warning-4);
            border: 1px solid var(--ds-color-warning-9);
            padding: var(--ds-spacing-3);
            border-radius: var(--ds-radius-3);
          }

          .confirmation-card p {
            margin-bottom: var(--ds-spacing-1);
            line-height: 1.5;
          }

          .confirmation-card p:last-child {
            margin-bottom: 0;
          }

        `}</style>
      <CSSTransition
        in={isVisible}
        timeout={400}
        baseClassName="selected-plan-container"
        nodeRef={selectedPlanRef}
      >
        <div className="selected-plan-container" ref={selectedPlanRef}>
          <PlanOptionCard
            plan={selectedPlan}
            isCurrent={false}
            isSelected={true}
            allowCardClick={false}
            showButton={false}
            onClick={() => {}} // No-op since card is not clickable
          />
        </div>
      </CSSTransition>
      <CSSTransition
        in={isVisible}
        timeout={400}
        baseClassName="confirmation-checkbox-container"
        nodeRef={checkboxRef}
      >
        <div className="confirmation-checkbox-container" ref={checkboxRef}>
          <Callout type="warning" size="small" icon={null}>
            <Text as="label">
              <Flex gap="2">
                <Checkbox
                  checked={confirmationChecked}
                  onCheckedChange={setConfirmationChecked}
                  className="confirmation-checkbox"
                  aria-labelledby="confirmation-text"
                />
                <Text size="2" color="gray" id="confirmation-text">
                  {checkboxText}
                </Text>
              </Flex>
            </Text>
          </Callout>
        </div>
      </CSSTransition>
    </div>
  );
}
