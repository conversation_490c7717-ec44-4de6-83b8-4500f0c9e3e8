import { MultiPageModal } from "app/components/ui/Modal";
import { PlanOptionCard } from "./PlanOptionCard";
import { useMutation } from "@tanstack/react-query";
import { cancelSubscription } from "app/client-cache";
import { Button } from "@radix-ui/themes";
import { isCommunityPlanOption, type PlanOptionSchema } from "app/schemas/plan";
import { toast } from "app/components/ui/Toast";
import { typography } from "app/utils/style";
import { Enabled } from "app/components/ui/Enabled";
import { useChangePlanConfirmationPageDialogParts } from "./ChangePlanConfirmationPage";

export type CancelSubscriptionModalProps = {
  plans: PlanOptionSchema[];
  currentPlanId?: string;
  scheduledPlanId?: string;
};

export function CancelSubscriptionModal({
  plans,
  currentPlanId,
  scheduledPlanId,
}: CancelSubscriptionModalProps) {
  const cancelSubscriptionMutation = useMutation(cancelSubscription);

  const communityPlan = plans.find((plan) => isCommunityPlanOption(plan));
  const isCommunityCurrentPlan = currentPlanId
    ? plans.find((plan) => plan.id === currentPlanId)?.augmentPlanType ===
      "community"
    : false;

  const shouldOfferCommunityPlan = !!(!isCommunityCurrentPlan && communityPlan);

  const changePlanConfirmationDialogParts =
    useChangePlanConfirmationPageDialogParts({
      selectedPlanId: communityPlan?.id ?? null,
      currentPlanId,
      scheduledPlanId,
      plans,
      hasPaymentMethod: false, // note we don't care because we are just changing to community.
    });

  function handleCancelSubscription() {
    cancelSubscriptionMutation.mutate(undefined, {
      onSuccess: () => {
        toast.success({
          title: "Subscription canceled",
          description: "Your subscription has been successfully canceled.",
        });
      },
      onError: () => {
        toast.error({
          title: "Failed to cancel subscription",
          description: "Please try again later.",
        });
      },
    });
  }

  return (
    <MultiPageModal
      maxWidth="550px"
      trigger={(open) => (
        <Button
          onClick={open}
          color="red"
          variant="ghost"
          disabled={cancelSubscriptionMutation.isPending}
        >
          Cancel subscription
        </Button>
      )}
      pages={[
        {
          title: "Cancel your subscription?",
          description: shouldOfferCommunityPlan
            ? "Before you cancel, consider switching to our free Community plan to keep access to basic features."
            : "",
          footer: (close) => (
            <>
              <Button variant="soft" onClick={close}>
                Close
              </Button>
              <Button
                onClick={close(handleCancelSubscription)}
                disabled={cancelSubscriptionMutation.isPending}
                variant="solid"
                color="red"
              >
                Cancel subscription
              </Button>
            </>
          ),
          content: (_close, setPage) => (
            <>
              <Enabled enabled={shouldOfferCommunityPlan}>
                {communityPlan && (
                  <PlanOptionCard
                    plan={communityPlan}
                    isCurrent={false}
                    allowCardClick={false}
                    onClick={() => setPage(1)}
                    className="community-plan-card"
                  />
                )}
              </Enabled>
              <div className="soft-notice">
                If you cancel your subscription:
                <ul>
                  <li>You will lose access to premium features</li>
                  <li>
                    Your subscription will remain active until the end of your
                    billing period
                  </li>
                  <li>
                    You won&apos;t be charged again after your current billing
                    period
                  </li>
                </ul>
              </div>
              <style scoped>{`
                :scope.modal-body {
                  display: flex;
                  flex-direction: column;
                  margin-top: var(--ds-spacing-3);
                  gap: var(--ds-spacing-3);
                  ${typography.text2.regular}
                  line-height: 1.75;
                }

                .soft-notice {
                  margin-top: var(--ds-spacing-4);
                  color: var(--ds-color-text-subtle);
                  ul {
                    list-style: disc;
                    padding-left: var(--ds-spacing-5);
                  }
                }
              `}</style>
            </>
          ),
        },
        ...(shouldOfferCommunityPlan
          ? [changePlanConfirmationDialogParts]
          : []),
      ]}
    />
  );
}
