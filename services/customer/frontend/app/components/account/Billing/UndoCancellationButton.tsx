import { Button } from "@radix-ui/themes";
import { useMutation } from "@tanstack/react-query";
import { queryClient } from "app/client-cache/queryClient.client";
import {
  subscriptionQueryOptions,
  unscheduleCancellation,
} from "app/client-cache";
import { toast } from "app/components/ui/Toast";

export function UndoCancellationButton() {
  const unscheduleCancellationMutation = useMutation(unscheduleCancellation);

  function handleUndoCancellation() {
    unscheduleCancellationMutation.mutate(undefined, {
      onSuccess: () => {
        toast.success({
          title: "Cancellation undone",
          description: "Your subscription will continue as normal.",
        });
        queryClient.invalidateQueries(subscriptionQueryOptions);
      },
      onError: () => {
        toast.error({
          title: "Failed to undo cancellation",
          description: "Please try again later.",
        });
      },
    });
  }

  return (
    <Button
      onClick={handleUndoCancellation}
      disabled={unscheduleCancellationMutation.isPending}
      variant="solid"
      color="green"
    >
      {unscheduleCancellationMutation.isPending
        ? "Processing..."
        : "Undo Cancellation"}
    </Button>
  );
}
