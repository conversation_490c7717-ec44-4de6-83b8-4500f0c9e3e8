import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>lex, <PERSON>, Heading, Box } from "@radix-ui/themes";
import { LightningBoltIcon } from "@radix-ui/react-icons";
import { getPlanDescriptionWithPricingLink } from "./utils";
import {
  isEnterprisePlanOption,
  isTrialPlanOption,
  type PlanOptionSchema,
} from "app/schemas/plan";
import { PlanOptionCard } from "./PlanOptionCard";

export type PlanPickerProps = {
  plans: PlanOptionSchema[];
  currentPlanId?: string;
  scheduledPlanId?: string;
  onSelectPlan: (planId: string) => void;
  onProceedToCheckout?: () => void; // New prop for handling checkout
  selectedPlanId?: string | null; // New prop to control the selected plan from parent
  title?: React.ReactNode;
  description?: React.ReactNode;
  showMessage?: boolean;
  message?: React.ReactNode;
  showCardButtons?: boolean; // Whether to show the individual select buttons on each card
  showMainButton?: boolean; // Whether to show the main "Select Plan" button at the bottom
  isLoading?: boolean; // Whether the plan change is in progress
};

export function PlanPicker(props: PlanPickerProps) {
  const {
    plans,
    currentPlanId,
    scheduledPlanId,
    onSelectPlan,
    onProceedToCheckout,
    selectedPlanId: externalSelectedPlanId,
    title = "Choose a Plan",
    description = getPlanDescriptionWithPricingLink(),
    showMessage = false,
    message = "You need to select a plan to continue using Augment",
    showCardButtons = false,
    showMainButton = true,
    isLoading = false,
  } = props;

  // Use internal state if no external state is provided
  const [internalSelectedPlanId, setInternalSelectedPlanId] = useState<
    string | null
  >(null);

  function getSelectedPlanType(planId: string) {
    const plan = plans.find((plan) => plan.id === planId);
    return plan ? plan.augmentPlanType : "unknown";
  }

  // Use external state if provided, otherwise use internal state
  const selectedPlanId =
    externalSelectedPlanId !== undefined
      ? externalSelectedPlanId
      : internalSelectedPlanId;

  const handleProceedToCheckout = () => {
    if (
      selectedPlanId &&
      getSelectedPlanType(selectedPlanId) !== "enterprise"
    ) {
      // If onProceedToCheckout is provided, use it, otherwise fall back to onSelectPlan
      if (onProceedToCheckout) {
        onProceedToCheckout();
      } else {
        onSelectPlan(selectedPlanId);
      }
    }
  };

  // filter out trial plans for display
  const filteredPlans = plans.filter((plan) => !isTrialPlanOption(plan));

  return (
    <Flex direction="column" gap="2">
      <style scoped>{`
        :scope .enterprise-separator {
          margin-bottom: var(--ds-spacing-3);
          position: relative;
          display: flex;
          align-items: center;
          text-align: center;
        }

        :scope .enterprise-separator::before,
        :scope .enterprise-separator::after {
          content: '';
          flex: 1;
          height: 1px;
          background: var(--ds-color-neutral-6);
        }

        :scope .enterprise-separator-label {
          padding: 0 var(--ds-spacing-4);
          color: var(--ds-color-neutral-11);
          font-size: var(--ds-font-size-1);
          font-weight: var(--ds-font-weight-medium);
          text-transform: uppercase;
          letter-spacing: 0.5px;
          flex-shrink: 0;
        }
      `}</style>
      {title && (
        <Heading size="6" style={{ marginBottom: "0" }}>
          {title}
        </Heading>
      )}
      {description && (
        <div
          style={{
            color: "var(--gray-11)",
            fontSize: "15px",
            marginBottom: "4px",
          }}
        >
          {description}
        </div>
      )}

      {showMessage && (
        <Card
          style={{
            marginBottom: "8px",
            background: "var(--blue-3)",
            border: "1px solid var(--blue-6)",
            padding: "8px 12px",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.08)",
          }}
        >
          <div
            style={{
              fontSize: "15px",
              fontWeight: "bold",
              lineHeight: "1.4",
              color: "var(--blue-11)",
            }}
          >
            {message}
          </div>
        </Card>
      )}

      <Flex direction="column" gap="3">
        {filteredPlans.map((plan, index) => {
          // note enterprise plan is assumed to be the final plan
          const shouldShowSeparator =
            isEnterprisePlanOption(plan) &&
            index > 0 &&
            !isEnterprisePlanOption(plans[index - 1]);

          return (
            <div key={plan.id}>
              {shouldShowSeparator && (
                <div className="enterprise-separator">
                  <div className="enterprise-separator-label">
                    Enterprise Solutions
                  </div>
                </div>
              )}
              <PlanOptionCard
                plan={plan}
                isSelected={selectedPlanId === plan.id}
                isCurrent={currentPlanId === plan.id}
                isScheduled={scheduledPlanId === plan.id}
                showButton={
                  showCardButtons || plan.augmentPlanType === "enterprise"
                }
                onClick={() => {
                  // Prevent selecting the enterprise plan, current plan, or scheduled plan
                  if (
                    plan.augmentPlanType !== "enterprise" &&
                    plan.id !== currentPlanId &&
                    plan.id !== scheduledPlanId
                  ) {
                    // Update the internal state if we're using it
                    if (externalSelectedPlanId === undefined) {
                      setInternalSelectedPlanId(plan.id);
                    }
                    // Always notify parent component about the selection
                    onSelectPlan(plan.id);
                  }
                }}
              />
            </div>
          );
        })}
      </Flex>

      {/* Only show the main button if showMainButton is true */}
      {showMainButton && (
        <Box
          style={{
            marginTop: "24px",
            display: "flex",
            justifyContent: "flex-end",
          }}
        >
          <Button
            size="3"
            variant="solid"
            color="blue"
            onClick={() => handleProceedToCheckout()}
            disabled={
              !selectedPlanId ||
              getSelectedPlanType(selectedPlanId) === "enterprise" ||
              isLoading
            }
            style={{
              fontWeight: "bold",
              boxShadow: "0 3px 8px rgba(0, 0, 0, 0.25)",
              width: "300px",
              height: "44px",
              fontSize: "15px",
              letterSpacing: "0.01em",
            }}
          >
            <LightningBoltIcon
              width="18"
              height="18"
              style={{ marginRight: "8px" }}
            />
            {isLoading
              ? "Processing..."
              : selectedPlanId &&
                  getSelectedPlanType(selectedPlanId) === "community"
                ? "Change Plan"
                : "Proceed to Checkout"}
          </Button>
        </Box>
      )}
    </Flex>
  );
}
