import { useEffect, useState } from "react";
import { But<PERSON> } from "@radix-ui/themes";
import { MultiPageModal } from "../../ui/Modal";
import { PlanPicker } from "./PlanPicker";
import type { PlanOptionSchema } from "app/schemas/plan";
import { getPlanDescriptionWithPricingLink } from "./utils";
import { useChangePlanConfirmationPageDialogParts } from "./ChangePlanConfirmationPage";

export type PlanPickerDialogProps = {
  plans: PlanOptionSchema[];
  currentPlanId?: string;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  title?: string;
  description?: React.ReactNode;
  hasPaymentMethod?: boolean;
  scheduledPlanId?: string;
};

export function PlanPickerDialog({
  plans,
  currentPlanId,
  isOpen,
  onOpenChange,
  title = "Choose a Plan",
  description = getPlanDescriptionWithPricingLink(),
  hasPaymentMethod = false,
  scheduledPlanId,
}: PlanPickerDialogProps) {
  // Track the selected plan separately from the current plan
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  const [pageNumber, setPageNumber] = useState(0);

  const changePlanConfirmationDialogParts =
    useChangePlanConfirmationPageDialogParts({
      selectedPlanId,
      currentPlanId,
      scheduledPlanId,
      plans,
      hasPaymentMethod,
    });

  const clearState = () => {
    setSelectedPlanId(null);
    setPageNumber(0);
  };

  useEffect(() => {
    if (!isOpen) {
      clearState();
    }
  }, [isOpen]);

  return (
    <MultiPageModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      pageNumber={pageNumber}
      setPageNumber={setPageNumber}
      pages={[
        {
          title,
          description,
          footer: (close) => (
            <>
              <Button
                size="2"
                variant="soft"
                color="gray"
                onClick={close(clearState)}
                className="cancel-button"
              >
                Cancel
              </Button>
            </>
          ),
          content: (_close, setPage) => (
            <div>
              <PlanPicker
                plans={plans}
                currentPlanId={currentPlanId}
                scheduledPlanId={scheduledPlanId}
                onSelectPlan={(planId) => {
                  setSelectedPlanId(planId);
                  setPage(1);
                }}
                title=""
                description=""
                showMainButton={false} // Hide the main button since we have one in the dialog footer
              />
              <style scoped>{`
              :scope {
                /* Dialog footer button styles */
                .cancel-button {
                  height: 36px;
                  padding: 0 16px;
                }

                .plan-select-button {
                  font-weight: bold;
                  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
                  padding: 0 16px;
                  height: 36px;
                  opacity: 1;
                }

                .plan-select-button[disabled] {
                  opacity: 0.6;
                }

                .plan-picker-credits-warning {
                  margin-top: var(--ds-spacing-4);
                }

                .plan-picker-confirmation-card {
                  margin-top: var(--ds-spacing-4);
                  background: var(--ds-color-warning-4);
                  border: 1px solid var(--ds-color-warning-9);

                  p {
                    margin-bottom: var(--ds-spacing-1);
                  }

                  p:last-child {
                    margin-bottom: 0;
                  }
                }
              }
            `}</style>
            </div>
          ),
        },
        changePlanConfirmationDialogParts,
      ]}
    />
  );
}

export default PlanPickerDialog;
