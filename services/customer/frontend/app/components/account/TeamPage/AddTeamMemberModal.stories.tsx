import type { <PERSON>a, StoryObj } from "@storybook/react";
import { AddTeamMemberModal } from "./AddTeamMemberModal";
import { delay } from "@augment-internal/ts-utils/timer";
import mocks from "app/mocks";
import { HttpResponse } from "msw";

const meta = {
  title: "Components/Account/TeamPage/AddTeamMemberModal",
  component: AddTeamMemberModal,
  parameters: {
    layout: "centered",
    msw: {
      delay: {
        team: 1000,
        user: 1000,
      },
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof AddTeamMemberModal>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};

// Create a story with a mock team (isMock=true) to show the community plan warning
export const WithCommunityPlanWarning: Story = {
  parameters: {
    msw: {
      overrides: (http) => [
        http.get("/api/team", async () => {
          await delay(1000);
          return HttpResponse.json(mocks.team.GET.Response[200].none);
        }),
        http.get("/api/user", async () => {
          await delay(1000);
          return HttpResponse.json(mocks.user.GET.Response[200]);
        }),
      ],
    },
  },
};
