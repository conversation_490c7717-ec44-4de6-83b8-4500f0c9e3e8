import { PersonIcon } from "@radix-ui/react-icons";
import { Button } from "@radix-ui/themes";
import { useMutation } from "@tanstack/react-query";
import { removeTeamMember } from "app/client-cache";
import Modal from "app/components/ui/Modal";
import { toast } from "app/components/ui/Toast";
import type { TeamMemberSchema } from "app/schemas/team";
import { typography } from "app/utils/style";
import { atom, type PrimitiveAtom, useAtom, useSetAtom } from "jotai";

export function createRemoveMemberModal() {
  const removeMemberModalAtom = atom(false);
  return {
    RemoveMemberTrigger: ({
      ...props
    }: Omit<RemoveMemberTriggerProps, "enabledAtom">) => (
      <RemoveMemberTrigger {...props} enabledAtom={removeMemberModalAtom} />
    ),
    RemoveMemberModal: (props: Omit<RemoveMemberModalProps, "enabledAtom">) => (
      <RemoveMemberModal {...props} enabledAtom={removeMemberModalAtom} />
    ),
  };
}

type RemoveMemberTriggerProps = {
  enabledAtom: PrimitiveAtom<boolean>;
};

/**
 * This is the Remote trigger for the modal.
 * RemoveMemberTrigger is in the ActionsMenu dropdown.
 * Since the dropdown closes when RemoveMemberTrigger is clicked,
 * we cannot have the modal and trigger in the same component.
 * To fix this, we have the trigger as a separate component from the modal.
 */
function RemoveMemberTrigger(props: RemoveMemberTriggerProps) {
  const { enabledAtom } = props;
  const setIsOpen = useSetAtom(enabledAtom);
  return (
    <Button onClick={() => setIsOpen(true)} variant="ghost" color="red">
      Remove Member
    </Button>
  );
}

export type RemoveMemberModalProps = {
  member: Pick<TeamMemberSchema, "email" | "id">;
  enabledAtom: PrimitiveAtom<boolean>;
};

function RemoveMemberModal(props: RemoveMemberModalProps) {
  const { member, enabledAtom } = props;
  const [isOpen, setIsOpen] = useAtom(enabledAtom);
  const removeMemberMutation = useMutation(removeTeamMember);
  function handleRemoveMember() {
    removeMemberMutation.mutate(member.id, {
      onSuccess: () => {
        toast.success({
          title: "Team member removed",
          description: `Successfully removed ${member.email} from the team.`,
        });
      },
      onError: (error) => {
        toast.error({
          title: "Failed to remove team member",
          description: error.message || "Please try again.",
        });
      },
    });
  }

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      className="remove-member-modal"
      maxWidth={"375px"}
      footer={(close) => (
        <>
          <Button variant="soft" onClick={close}>
            Cancel
          </Button>
          <Button
            onClick={close(handleRemoveMember)}
            disabled={removeMemberMutation.isPending}
            variant="solid"
            color="red"
          >
            Remove Member
          </Button>
        </>
      )}
    >
      <div className="remove-member-modal-body">
        <PersonIcon className="person-icon" />
        <div className="title">Remove Team Member</div>
        <div className="message-are-you-sure">
          Are you sure you want to remove{" "}
          <span className="email" title={member.email}>
            {member.email}
          </span>{" "}
          from the team?
        </div>
      </div>
      <style scoped>{`
          :scope .remove-member-modal-body {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--ds-spacing-2);
            ${typography.text3.regular}
            color: var(--ds-color-text-subtle);

            .person-icon {
              width: var(--ds-spacing-8);
              height: var(--ds-spacing-8);
              padding: var(--ds-spacing-3);
              background-color: var(--ds-color-error-3);
              border-radius: 50%;
              ${typography.text5.bold}
              color: var(--ds-color-error-9);
              margin-bottom: var(--ds-spacing-2);
            }

            .title {
              ${typography.text4.bold}
            }

            .message-are-you-sure {
              display: block;
            }

            .email {
              ${typography.text2.bold}
              color: var(--ds-color-text-subtle);
              max-width: 200px;
              display: block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              display: inline-block;
              line-height: 16px;
              vertical-align: middle;
            }
          }
        `}</style>
    </Modal>
  );
}
