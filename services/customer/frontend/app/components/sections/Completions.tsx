import { useMemo } from "react";
import { Box, Flex, Grid } from "@radix-ui/themes";
import { t } from "../../utils/helpers";
import { useAppStore } from "../../utils/app-store";
import { between } from "../../utils/data";
import { useDateFilter } from "../../utils/date";
import {
  calculateRate,
  getDateValueList,
  sumByProperty,
} from "../../utils/value-calculations";
import SectionLayout from "../global/SectionLayout";
import ComboCard from "../cards/ComboCard";
import MetricCard from "../cards/MetricCard";
import ChartCard from "../cards/ChartCard";
import AreaChart from "../charts/AreaChart";
import type { GetCompletionStatsResponse_CompletionStats } from "../../../../../request_insight/analytics/request_insight_analytics_pb";
import { useQuery } from "@tanstack/react-query";
import { dateRangeQuery } from "app/client-cache";

export default function Completions({
  title,
  color,
}: {
  title: string;
  color: string;
}) {
  const earliestData = useAppStore((state) => state.earliestData);

  const [
    completionsFilterValue,
    setCompletionsFilterValue,
    startDate,
    endDate,
  ] = useDateFilter(earliestData, "CodeChanges", "completionsFilter");

  const filter = useMemo(() => {
    return between(startDate, endDate);
  }, [startDate, endDate]);

  const { data: completionStatsData } = useQuery(
    dateRangeQuery<GetCompletionStatsResponse_CompletionStats[]>(
      "completion-stats",
      startDate,
      endDate,
    ),
  );

  const dailyCompletionsValues = useMemo(() => {
    if (!completionStatsData) {
      return null;
    }

    return getDateValueList(completionStatsData, "requestCount", filter);
  }, [completionStatsData, filter]);

  const completionsLinesAcceptedValue = useMemo(() => {
    if (!completionStatsData) {
      return null;
    }

    return sumByProperty(
      completionStatsData,
      "acceptedLineCount",
      filter,
    ).toLocaleString();
  }, [completionStatsData, filter]);

  const completionsCharactersAcceptedValue = useMemo(() => {
    if (!completionStatsData) {
      return null;
    }

    return sumByProperty(
      completionStatsData,
      "acceptedCharacterCount",
      filter,
    ).toLocaleString();
  }, [completionStatsData, filter]);

  const completionsAcceptanceRate = useMemo(() => {
    if (!completionStatsData) {
      return null;
    }

    return (
      calculateRate(
        completionStatsData,
        "acceptedCount",
        "requestCount",
        filter,
      )
        .toFixed(1)
        .toLocaleString() + "%"
    );
  }, [completionStatsData, filter]);

  return (
    <SectionLayout
      title={title}
      activeFilter={completionsFilterValue}
      setActiveFilter={(value) => setCompletionsFilterValue(value)}
    >
      <Grid
        columns={{ initial: "1", md: "2" }}
        gap="4"
        width="auto"
        height="auto"
      >
        <Box gridRow="span 2" height="400px">
          <ChartCard
            title={t("dailyCodeCompletions").title}
            granularity={t("daily").title}
            rollingAvg={t("sevenDayAverage").title}
          >
            <AreaChart
              color={color}
              showTrendLine={true}
              showAxis={true}
              data={dailyCompletionsValues}
            />
          </ChartCard>
        </Box>

        <Grid columns="1" gap="4">
          <Box flexGrow="1" flexShrink="1" flexBasis="0%">
            <ComboCard
              title={t("completionAcceptanceRate").title}
              metricpy="8"
              metricValue={completionsAcceptanceRate}
              areaChartData={dailyCompletionsValues}
              color={color}
            />
          </Box>

          <Flex direction="row" gap="4">
            <Box flexGrow="1" flexShrink="1" flexBasis="0%">
              <MetricCard
                py="8"
                title={t("completionsLinesAccepted").title}
                value={completionsLinesAcceptedValue}
              />
            </Box>
            <Box flexGrow="1" flexShrink="1" flexBasis="0%">
              <MetricCard
                py="8"
                title={t("completionsCharactersAccepted").title}
                value={completionsCharactersAcceptedValue}
              />
            </Box>
          </Flex>
        </Grid>
      </Grid>
    </SectionLayout>
  );
}
