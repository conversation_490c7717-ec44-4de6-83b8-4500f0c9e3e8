import { withApiRoute } from "../.server/auth";
import { getAuthenticatedApiData, getDateFilters } from "../utils/api";

import { cachingClient } from "../.server/grpc/request-insights-analytics";

export const loader = withApiRoute(
  async ({ request, user }) => {
    const { tenantId } = user;
    const dateFilters = getDateFilters(request);

    const { activeUsers } = await getAuthenticatedApiData(
      user,
      {
        tenantId,
        dateFilters,
      },
      cachingClient.getActiveUsers.bind(cachingClient),
    );

    return Response.json(activeUsers);
  },
  {
    adminOnly: true,
    enterpriseOnly: true,
  },
);
