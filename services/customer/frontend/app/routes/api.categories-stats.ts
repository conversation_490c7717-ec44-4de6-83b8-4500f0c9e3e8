import { withApiRoute } from "../.server/auth";
import {
  cacheableJson,
  getAuthenticatedApiData,
  getDateFilters,
} from "../utils/api";
import { cachingClient } from "../.server/grpc/request-insights-analytics";
import { GetCategoriesStatsRequest_RequestType } from "~services/request_insight/analytics/request_insight_analytics_pb";

export const loader = withApiRoute(
  async ({ request, user }) => {
    const { tenantId } = user;
    const dateFilters = getDateFilters(request);

    const url = new URL(request.url);
    const requestType = url.searchParams.get("requestType");
    if (
      !requestType ||
      !Object.values(GetCategoriesStatsRequest_RequestType).includes(
        requestType,
      )
    ) {
      throw Response.json(
        { message: "requestType is required" },
        { status: 400, statusText: "Bad Request" },
      );
    }

    const { categoriesStats } = await getAuthenticatedApiData(
      user,
      {
        tenantId,
        dateFilters,
        requestType:
          GetCategoriesStatsRequest_RequestType[
            requestType as keyof typeof GetCategoriesStatsRequest_RequestType
          ],
      },
      cachingClient.getCategoriesStats.bind(cachingClient),
    );

    return cacheableJson(categoriesStats);
  },
  {
    adminOnly: true,
    enterpriseOnly: true,
  },
);
