import { withApiRoute } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import type { TeamPlanChangePendingSchema } from "app/schemas/team";

export const loader = withApiRoute(
  async ({ user }) => {
    const authCentralClient = AuthCentralClient.getInstance();
    const response = await authCentralClient.getTenantPlanStatus(user);
    return Response.json(response satisfies TeamPlanChangePendingSchema);
  },
  {
    adminOnly: false,
  },
);
