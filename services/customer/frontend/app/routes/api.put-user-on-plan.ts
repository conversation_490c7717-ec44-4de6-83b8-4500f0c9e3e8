import { withApiRoute } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { PutUserOnPlanRequestSchema } from "../schemas/put-user-on-plan";
import type { PutUserOnPlanResponseSchema } from "../schemas/put-user-on-plan";
import { isUserInSelfServeTeam } from "app/utils/team.server";

// We don't validate the user's current plan in this endpoint because the validation
// requires ensuring the plan is "active" which I see as bug prone in the BFF &
// should be handled by the backend
export const action = withApiRoute(
  async ({ user, request, log }) => {
    const start = Date.now();

    log.info("Processing plan change request");

    if (request.method !== "POST") {
      const status = 405;
      log.info("Method not allowed", { status_code: status });
      return Response.json({ error: "Method not allowed" }, { status });
    }

    const userId = user.userId;

    try {
      const body = await request.json();
      const validatedData = PutUserOnPlanRequestSchema.parse(body);
      const { planId } = validatedData;

      log.info("Initiating plan change", {
        plan_id: planId,
      });

      // Call the putUserOnPlan method to trigger the plan change
      await AuthCentralClient.getInstance().putUserOnPlan(user, planId);

      const isTeam = await isUserInSelfServeTeam(user);
      const status = 200;

      log.info("Plan change initiated successfully", {
        plan_id: planId,
        account_type: isTeam ? "team" : "individual",
        duration_ms: Date.now() - start,
        status_code: status,
      });

      return Response.json({
        type: isTeam ? "team" : "individual",
        success: true,
        message: `User ${userId} plan change has been initiated to ${planId}.`,
      } satisfies PutUserOnPlanResponseSchema);
    } catch (error) {
      const status = 500;
      log.error("Failed to process plan change request", error, {
        duration_ms: Date.now() - start,
        status_code: status,
      });
      return Response.json({ error: "Failed to process request" }, { status });
    }
  },
  {
    adminOnly: false,
  },
);
