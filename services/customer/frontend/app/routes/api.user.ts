import { json } from "@remix-run/node";
import { type SessionUser, isAdmin, withApiRoute } from "../.server/auth";
import { getUserPlan } from "../utils/subscription.server";
import { getSession, commitSession } from "../.server/session";
import type { UserApiGETResponseSchema } from "../schemas/user";
import { isUserInSelfServeTeam, teamsAllowed } from "../utils/team.server";

export const loader = withApiRoute(
  async ({ user, request, log }) => {
    const start = Date.now();
    const { email } = user;

    log.info("Loading user data");

    const planWithTenant = await getUserPlan(user);
    const {
      tenantId,
      tenantName,
      shardNamespace,
      isSubscriptionPending,
      suspensions,
      ...plan
    } = planWithTenant;

    const session = await getSession(request.headers.get("cookie"));
    const sessionUser = session.get("user") as SessionUser;

    session.set("user", {
      ...sessionUser,
      tenantId,
      tenantName,
      shardNamespace,
    });

    try {
      // Check if the user is in a self-serve team
      const isSelfServeTeamMember = await isUserInSelfServeTeam(user);

      const showTeamManagementLink = await teamsAllowed(user);

      if (plan.pending && isSubscriptionPending) {
        log.error(
          "Plan change and Orb setup are pending simultaneously. These states should be mutually exclusive.",
          undefined,
          {
            plan_pending: plan.pending,
            subscription_pending: isSubscriptionPending,
          },
        );
        throw new Error("Invalid account state");
      }

      const userIsAdmin = await isAdmin(user);
      const responseData: UserApiGETResponseSchema = {
        email,
        isAdmin: userIsAdmin,
        isSelfServeTeamMember,
        plan,
        tenantTier: planWithTenant.tenantTier,
        isSubscriptionPending,
        suspensions,
        showTeamManagementLink,
      };

      const status = 200;
      log.info("Successfully loaded user data", {
        is_admin: userIsAdmin,
        is_self_serve_team_member: isSelfServeTeamMember,
        tenant_tier: planWithTenant.tenantTier,
        plan_name: plan.name,
        subscription_pending: isSubscriptionPending,
        suspensions_count: suspensions?.length || 0,
        show_team_management: showTeamManagementLink,
        duration_ms: Date.now() - start,
        status_code: status,
      });

      return json(responseData, {
        headers: {
          "Set-Cookie": await commitSession(session),
        },
      });
    } catch (error) {
      const status = 500;
      log.error("Failed to load user data", error, {
        duration_ms: Date.now() - start,
        status_code: status,
      });
      return json({ message: "Internal Server Error" }, { status });
    }
  },
  {
    adminOnly: false,
  },
);
