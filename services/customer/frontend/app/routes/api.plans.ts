import { withApiRoute } from "../.server/auth";
import { getAllPlans } from "../utils/plans.server";

export const loader = withApiRoute(
  async ({ user, log }) => {
    const start = Date.now();

    log.info("Retrieving available plans");

    try {
      const plans = await getAllPlans(user);

      const status = 200;
      log.info("Successfully retrieved plans", {
        plan_count: plans.length,
        duration_ms: Date.now() - start,
        status_code: status,
      });

      return Response.json(plans);
    } catch (error) {
      const status = 500;
      log.error("Failed to retrieve plans", error, {
        duration_ms: Date.now() - start,
        status_code: status,
      });

      return Response.json(
        {
          error:
            error instanceof Error ? error.message : "Failed to retrieve plans",
        },
        { status },
      );
    }
  },
  {
    adminOnly: false,
  },
);
