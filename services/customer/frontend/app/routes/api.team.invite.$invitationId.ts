import { json } from "@remix-run/node";
import { withApiRoute } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import {
  TeamDeleteInvitationRequestSchema,
  type TeamDeleteInvitationSuccessSchema,
  type TeamDeleteInvitationErrorSchema,
} from "../schemas/team-invite";
import { ensureTeamForUser, teamsAllowed } from "../utils/team.server";

export const action = withApiRoute(async ({ request, params, user, log }) => {
  if (!(await teamsAllowed(user))) {
    return json<TeamDeleteInvitationErrorSchema>(
      { message: "Team management is not enabled" },
      { status: 403 },
    );
  }

  if (request.method !== "DELETE") {
    return json<TeamDeleteInvitationErrorSchema>(
      { message: "Method not allowed" },
      { status: 405 },
    );
  }

  const { invitationId } = params;
  if (!invitationId) {
    return json<TeamDeleteInvitationErrorSchema>(
      { message: "Invitation ID is required" },
      { status: 400 },
    );
  }

  // Validate the invitation ID format
  try {
    TeamDeleteInvitationRequestSchema.parse({ invitationId });
  } catch (error) {
    const status = 400;
    log.error("Invalid invitation ID format", error, {
      invitation_id: invitationId,
      status_code: status,
    });
    return json<TeamDeleteInvitationErrorSchema>(
      { message: "Invalid invitation ID format" },
      { status },
    );
  }

  try {
    const tenantId = await ensureTeamForUser(user);
    const authCentralClient = AuthCentralClient.getInstance();

    // Delete the invitation
    try {
      await authCentralClient.deleteInvitation(user, invitationId, tenantId);

      const status = 200;
      log.info("Invitation deleted successfully", {
        invitation_id: invitationId,
        status_code: status,
      });
      return json<TeamDeleteInvitationSuccessSchema>({}, { status });
    } catch (error) {
      const status = 500;
      log.error("Failed to delete invitation", error, {
        invitation_id: invitationId,
        status_code: status,
      });
      return json<TeamDeleteInvitationErrorSchema>(
        { message: "Internal Server Error" },
        { status },
      );
    }
  } catch (error) {
    const status = 500;
    log.error("Error deleting invitation", error, {
      invitation_id: invitationId,
      status_code: status,
    });
    return json<TeamDeleteInvitationErrorSchema>(
      { message: "Internal Server Error" },
      { status },
    );
  }
});
