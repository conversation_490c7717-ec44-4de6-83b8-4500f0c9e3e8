import { z } from "zod";
import { withApiRoute } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import type { GetUserOrbCreditsInfoResponse } from "~services/auth/central/server/auth_pb";

export const loader = withApiRoute(
  async ({ user, log }) => {
    const start = Date.now();
    const authCentralClient = AuthCentralClient.getInstance();

    log.info("Getting user Orb credits info");

    try {
      const response: GetUserOrbCreditsInfoResponse =
        await authCentralClient.getUserOrbCreditsInfo(user);

      const status = 200;
      log.info("Successfully retrieved user Orb credits info", {
        duration_ms: Date.now() - start,
        status_code: status,
      });

      // Returning the whole response object.
      // The client can then select the specific fields it needs.
      return Response.json(response);
    } catch (error) {
      const status = 500;
      log.error("Failed to get user Orb credits info", error, {
        duration_ms: Date.now() - start,
        status_code: status,
      });
      return Response.json({ message: "Internal Server Error" }, { status });
    }
  },
  {
    adminOnly: false,
    enterpriseOnly: false,
  },
);

export const action = withApiRoute(
  async ({ user, request, log }) => {
    const start = Date.now();

    log.info("Processing credits purchase request");

    if (request.method !== "POST") {
      const status = 405;
      log.info("Method not allowed", { status_code: status });
      return Response.json({ error: "Method not allowed" }, { status });
    }

    const authCentralClient = AuthCentralClient.getInstance();

    try {
      const formData = await request.formData();
      const credits = formData.get("credits");

      // Validate credits parameter
      if (!credits) {
        const status = 400;
        log.error("Missing credits parameter", undefined, {
          duration_ms: Date.now() - start,
          status_code: status,
        });
        return Response.json(
          { error: "Missing credits parameter" },
          { status },
        );
      }

      const creditsNumber = Number(credits);

      try {
        z.number().positive().gte(100).lte(1000).parse(creditsNumber);
      } catch (validationError) {
        const status = 400;
        log.error("Invalid credits value", validationError, {
          credits_value: credits,
          duration_ms: Date.now() - start,
          status_code: status,
        });
        return Response.json(
          { error: "Credits must be between 100 and 1000" },
          { status },
        );
      }

      log.info("Purchasing credits", {
        credits_amount: creditsNumber,
      });

      await authCentralClient.purchaseCredits(user, creditsNumber);

      const status = 200;
      log.info("Credits purchased successfully", {
        credits_amount: creditsNumber,
        duration_ms: Date.now() - start,
        status_code: status,
      });

      return Response.json({});
    } catch (error) {
      const status = 500;
      log.error("Failed to purchase credits", error, {
        duration_ms: Date.now() - start,
        status_code: status,
      });
      return Response.json({ error: "Failed to purchase credits" }, { status });
    }
  },
  {
    adminOnly: true,
  },
);
