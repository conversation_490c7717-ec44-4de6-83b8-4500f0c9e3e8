import { withApiRoute } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import type { GetUserOrbPaymentInfoResponse } from "~services/auth/central/server/auth_pb";

export const loader = withApiRoute(
  async ({ user, log }) => {
    const start = Date.now();
    const authCentralClient = AuthCentralClient.getInstance();

    log.info("Getting user Orb payment info");

    try {
      const response: GetUserOrbPaymentInfoResponse =
        await authCentralClient.getUserOrbPaymentInfo(user);

      const status = 200;
      log.info("Successfully retrieved user Orb payment info", {
        duration_ms: Date.now() - start,
        status_code: status,
      });

      return Response.json(response);
    } catch (error) {
      const status = 500;
      log.error("Failed to get user Orb payment info", error, {
        duration_ms: Date.now() - start,
        status_code: status,
      });

      return Response.json({ message: "Internal Server Error" }, { status });
    }
  },
  {
    adminOnly: false,
    enterpriseOnly: false,
  },
);
