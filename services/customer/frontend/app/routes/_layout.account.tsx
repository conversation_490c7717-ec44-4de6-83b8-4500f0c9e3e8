import { redirect, json } from "@remix-run/node";
import { Outlet, useLocation } from "@remix-run/react";
import { Spinner } from "@radix-ui/themes";
import { withAuth, isEnterpriseTier } from "../.server/auth";
import { ProgressPage } from "app/components/ui/ProgressPage";
import { isUserInLegacySelfServeTeam } from "app/utils/team.server";
import { logger } from "@augment-internal/logging";
import { AuthCentralClient } from "app/.server/grpc/auth-central";
import { OrbSubscriptionInfo_SubscriptionStatus } from "~services/auth/central/server/auth_pb";
import { useAtomValue } from "jotai";
import {
  planChangeInProgressAtom,
  subscriptionCreationPendingAtom,
} from "app/client-cache";
import { cn } from "app/utils/style";

export const loader = withAuth(
  async ({ request, user }) => {
    try {
      const url = new URL(request.url);
      const pathname = url.pathname;

      if (
        (await isEnterpriseTier(user)) ||
        (await isUserInLegacySelfServeTeam(user))
      ) {
        return redirect("/");
      }

      const orbSubscriptionInfo = (
        await AuthCentralClient.getInstance().getUserOrbSubscriptionInfo(user)
      ).orbSubscriptionInfo;

      if (
        pathname !== "/account/select-plan" &&
        pathname !== "/account/select-plan/" &&
        orbSubscriptionInfo.case === "subscription" &&
        orbSubscriptionInfo.value.subscriptionStatus ===
          OrbSubscriptionInfo_SubscriptionStatus.ENDED
      ) {
        return redirect("/account/select-plan");
      }

      if (pathname === "/account" || pathname === "/account/") {
        return redirect("/account/subscription");
      }

      return json({});
    } catch (error) {
      logger.error("Error in account loader:", { error });
      throw error;
    }
  },
  {
    adminOnly: false,
    entry: "/account", // we need to come back to /account so we can decide where to redirect based on billing method
  },
);

export default function Account() {
  const location = useLocation();
  const isPlanChangeInProgress = useAtomValue(planChangeInProgressAtom);
  const isSubscriptionCreationInProgress = useAtomValue(
    subscriptionCreationPendingAtom,
  );

  const isSelectPlanPage =
    location.pathname === "/account/select-plan" ||
    location.pathname === "/account/select-plan/";

  if (isPlanChangeInProgress) {
    return (
      <ProgressPage
        title="Processing Your Plan Change"
        description="This may take a few moments."
        message="You will be redirected automatically."
        progressColor="ds-color-accent"
        icon={<Spinner />}
      />
    );
  }

  if (isSubscriptionCreationInProgress) {
    return (
      <ProgressPage
        title="Setting Up Your Account"
        description="This may take a few moments."
        message="You will be redirected automatically."
        progressColor="ds-color-accent"
        icon={<Spinner />}
      />
    );
  }

  return (
    <div
      className={cn("account-layout", {
        "select-plan-page": isSelectPlanPage,
      })}
    >
      <Outlet />
      <style scoped>{`
        :scope.account-layout {
          display: flex;
          flex-direction: row;
          gap: 24px;
        }
        :scope.select-plan-page {
          display: block;
        }
      `}</style>
    </div>
  );
}
