import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  type MockedFunction,
} from "vitest";

import { action } from "../api.credits";
import { AuthCentralClient } from "app/.server/grpc/auth-central";

vi.mock("app/.server/auth", () => ({
  withApiRoute: (handler: any) => handler,
}));

vi.mock("app/.server/grpc/auth-central", () => {
  const mockInstance = {
    purchaseCredits: vi.fn(),
  };

  return {
    AuthCentralClient: {
      getInstance: vi.fn(() => mockInstance),
    },
  };
});

describe("api.credits.action", () => {
  const mockUser = {
    userId: "test-user-id",
    email: "<EMAIL>",
    tenantId: "test-tenant-id",
  };

  const mockLog = {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };

  let mockAuthCentralInstance: {
    purchaseCredits: MockedFunction<any>;
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockAuthCentralInstance = AuthCentralClient.getInstance() as any;
  });

  it("should handle POST request to purchase credits", async () => {
    const mockRequest = new Request("http://localhost/api/credits", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: "credits=500",
    });

    mockAuthCentralInstance.purchaseCredits.mockResolvedValueOnce({});

    const response = await action({
      request: mockRequest,
      user: mockUser,
      log: mockLog,
    } as any);

    expect(mockAuthCentralInstance.purchaseCredits).toHaveBeenCalledWith(
      mockUser,
      500,
    );
    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
    }
  });
});
