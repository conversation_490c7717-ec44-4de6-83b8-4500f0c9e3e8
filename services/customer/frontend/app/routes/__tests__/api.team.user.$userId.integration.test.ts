import { describe, it, expect, vi, beforeAll, afterAll, inject } from "vitest";

import { action } from "../api.team.user.$userId";
import { Config } from "../../.server/config";
import { AuthCentralClient } from "../../.server/grpc/auth-central";
import { resetTokenExchangeClient } from "../../.server/grpc/token-exchange";
import { CustomerUiRole } from "~services/auth/central/server/auth_entities_pb";

vi.mock("../../.server/auth", async () => {
  const originalModule = vi.importActual("../../.server/auth");
  const TenantTier = (
    await import("~services/tenant_watcher/tenant_watcher_pb")
  ).TenantTier;
  return {
    ...(await originalModule),
    authenticator: {
      isAuthenticated: vi.fn().mockResolvedValue(true),
    },
    getSession: vi.fn().mockResolvedValue({
      get: vi.fn().mockReturnValue(null),
    }),
    withApiRoute: (handler: any) => {
      return (args: any) => handler({ ...args, user: args.user });
    },
    getTenantTier: vi.fn().mockResolvedValue(TenantTier.PROFESSIONAL),
  };
});

vi.mock("../../utils/team.server", () => {
  return {
    teamsAllowed: vi.fn().mockResolvedValue(true),
  };
});

const mockUser = {
  userId: "test-user-id",
  sessionId: "test-session-id",
  tenantId: "test456",
  tenantName: "self-serve-team",
  shardNamespace: "d0",
  email: "<EMAIL>",
  roles: ["ADMIN"],
  createdAt: Date.now(),
};

const nonAdminEmail = "<EMAIL>";
let nonAdminUserId: string | null = null;

describe("api.team.user.$userId DELETE action", () => {
  beforeAll(async () => {
    const testServerConfig = inject("testServerConfig");

    Config.AUTH_CENTRAL_ENDPOINT = testServerConfig.auth_central_grpc_server;
    AuthCentralClient.resetInstance();

    Config.TOKEN_EXCHANGE_ENDPOINT = testServerConfig.token_exchange_server;
    resetTokenExchangeClient();

    const authCentralClient = AuthCentralClient.getInstance();

    try {
      const response = await authCentralClient.addUserToTenant(
        mockUser,
        mockUser.email,
      );
      if (!response.user) {
        throw new Error("No user returned from addUserToTenant");
      }
      mockUser.userId = response.user.id;

      await authCentralClient.updateUserOnTenant(mockUser, mockUser.userId, [
        CustomerUiRole.ADMIN,
      ]);

      const nonAdminResponse = await authCentralClient.addUserToTenant(
        mockUser,
        nonAdminEmail,
      );
      if (nonAdminResponse.user) {
        nonAdminUserId = nonAdminResponse.user.id;
      }
    } catch (err) {
      console.warn(`Setup failed: ${err}`);
    }
  });

  afterAll(async () => {
    try {
      const authCentralClient = AuthCentralClient.getInstance();

      // Clean up the admin user
      if (mockUser.userId) {
        await authCentralClient.removeUserFromTenant(
          mockUser,
          mockUser.userId,
          mockUser.tenantId,
        );
        console.info(
          `Cleanup successful: Removed admin user ${mockUser.email}`,
        );
      }

      // Clean up non-admin user if it wasn't deleted by the test
      if (nonAdminUserId) {
        await authCentralClient.removeUserFromTenant(
          mockUser,
          nonAdminUserId,
          mockUser.tenantId,
        );
        console.info(
          `Cleanup successful: Removed non-admin user ${nonAdminEmail}`,
        );
      }
    } catch (err) {
      console.warn(`Cleanup failed: ${err}`);
    }
  });

  it("should handle user removal with real Auth Central client", async () => {
    try {
      const authCentralClient = AuthCentralClient.getInstance();

      const initialUsersList =
        await authCentralClient.listTenantUsers(mockUser);

      const nonAdminUser = initialUsersList.users.find(
        (user) => user.email === nonAdminEmail,
      );

      expect(nonAdminUser).not.toBeUndefined();
      if (!nonAdminUser) {
        return;
      }

      nonAdminUserId = nonAdminUser.id;

      const mockRequest = new Request("http://localhost/api/team/user/123", {
        method: "DELETE",
      });

      const mockLog = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
      };

      const response = await action({
        request: mockRequest,
        params: { userId: nonAdminUserId },
        user: mockUser,
        log: mockLog,
      } as any);

      expect(response).not.toBeNull();
      expect(response instanceof Response).toBe(true);

      if (response instanceof Response) {
        expect(response.status).toBe(200);

        const finalUsersList =
          await authCentralClient.listTenantUsers(mockUser);

        const userStillExists = finalUsersList.users.some(
          (u) => u.id === nonAdminUserId,
        );
        expect(userStillExists).toBe(false);

        // Since the test deleted the non-admin user, set to null so afterAll doesn't try to delete again
        nonAdminUserId = null;

        // Verify we still have at least one admin
        let adminCount = 0;
        for (const user of finalUsersList.users) {
          try {
            const userOnTenant = await authCentralClient.getUserOnTenant(
              mockUser,
              user.id,
            );
            if (userOnTenant.customerUiRoles.includes(CustomerUiRole.ADMIN)) {
              adminCount++;
            }
          } catch (err) {
            console.warn(`Failed to check if user ${user.id} is admin: ${err}`);
          }
        }

        expect(adminCount).toBeGreaterThan(0);
      }
    } catch (error) {
      console.error(`Test failed with error: ${error}`);
      throw error; // Let the test fail if there's an error
    }
  });
});
