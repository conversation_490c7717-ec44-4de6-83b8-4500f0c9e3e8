import { describe, it, expect, vi, beforeAll, afterAll, inject } from "vitest";

import { action } from "../api.team.invite";
import { Config } from "../../.server/config";
import { AuthCentralClient } from "../../.server/grpc/auth-central";
import { resetTokenExchangeClient } from "../../.server/grpc/token-exchange";
import { CustomerUiRole } from "~services/auth/central/server/auth_entities_pb";

vi.mock("../../.server/auth", async () => {
  const originalModule = vi.importActual("../../.server/auth");
  const TenantTier = (
    await import("~services/tenant_watcher/tenant_watcher_pb")
  ).TenantTier;
  return {
    ...(await originalModule),
    authenticator: {
      isAuthenticated: vi.fn().mockResolvedValue(true),
    },
    withApiRoute: (handler: any) => {
      return (args: any) => handler({ ...args, user: args.user });
    },
    getTenantTier: vi.fn().mockResolvedValue(TenantTier.PROFESSIONAL),
  };
});

// Mock the team.server.ts module
vi.mock("../../utils/team.server", () => {
  return {
    ensureTeamForUser: vi.fn().mockImplementation((user) => {
      return Promise.resolve(user.tenantId);
    }),
    isUserInSelfServeTeam: vi.fn().mockResolvedValue(true),
    teamsAllowed: vi.fn().mockResolvedValue(true),
  };
});

// Add this mock for the session module
vi.mock("../../.server/session", async (importOriginal) => {
  const actual = await importOriginal();

  return {
    ...(actual as object),
    getSession: vi.fn().mockResolvedValue({
      get: vi.fn().mockImplementation((key) => {
        if (key === "user") {
          return mockUser;
        }
        return null;
      }),
      set: vi.fn(),
      flash: vi.fn(),
      unset: vi.fn(),
    }),
    commitSession: vi.fn().mockResolvedValue("mock-session-cookie"),
    destroySession: vi.fn().mockResolvedValue(""),
  };
});

const mockUser = {
  userId: "test-user-456",
  sessionId: "test-session-id",
  tenantId: "test456",
  tenantName: "self-serve-team",
  shardNamespace: "d0",
  email: "<EMAIL>",
  roles: ["ADMIN"],
  createdAt: Date.now(),
};

const testInviteEmail = "<EMAIL>";

describe("api.team.invite action", () => {
  beforeAll(async () => {
    const testServerConfig = inject("testServerConfig");

    Config.AUTH_CENTRAL_ENDPOINT = testServerConfig.auth_central_grpc_server;
    AuthCentralClient.resetInstance();

    Config.TOKEN_EXCHANGE_ENDPOINT = testServerConfig.token_exchange_server;
    resetTokenExchangeClient();

    const authCentralClient = AuthCentralClient.getInstance();

    try {
      // Add the test admin user to the tenant
      const response = await authCentralClient.addUserToTenant(
        mockUser,
        mockUser.email,
      );
      if (!response.user) {
        throw new Error("Unable to add test user to tenant");
      }
      mockUser.userId = response.user.id;

      await authCentralClient.updateUserOnTenant(mockUser, mockUser.userId, [
        CustomerUiRole.ADMIN,
      ]);
    } catch (err) {
      console.warn(`Setup failed: ${err}`);
    }
  });

  afterAll(async () => {
    try {
      const authCentralClient = AuthCentralClient.getInstance();

      // Clean up the admin user
      if (mockUser.userId) {
        await authCentralClient.removeUserFromTenant(
          mockUser,
          mockUser.userId,
          mockUser.tenantId,
        );
        console.info(
          `Cleanup successful: Removed admin user ${mockUser.email}`,
        );
      }

      // Clean up any invitations created during the test
      try {
        const invitations =
          await authCentralClient.getTenantInvitations(mockUser);
        const testInvitation = invitations.invitations.find(
          (inv) => inv.inviteeEmail === testInviteEmail,
        );

        if (testInvitation) {
          await authCentralClient.resolveInvitations(mockUser, undefined, [
            testInvitation.id,
          ]);
          console.info(
            `Cleanup successful: Removed invitation for ${testInviteEmail}`,
          );
        }
      } catch (err) {
        console.warn(`Failed to clean up invitations: ${err}`);
      }
    } catch (err) {
      console.warn(`Cleanup failed: ${err}`);
    }
  });

  it("should successfully invite a user", async () => {
    try {
      const authCentralClient = AuthCentralClient.getInstance();

      const mockRequest = new Request("http://localhost/api/team/invite", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ emails: [testInviteEmail] }),
      });

      const mockLog = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
      };

      const response = await action({
        request: mockRequest,
        user: mockUser,
        log: mockLog,
      } as any);

      expect(response).not.toBeNull();
      expect(response instanceof Response).toBe(true);

      if (response instanceof Response) {
        expect(response.status).toBe(200);

        const { invitations } =
          await authCentralClient.getTenantInvitations(mockUser);

        const invitationExists = invitations.some(
          (inv) => inv.inviteeEmail === testInviteEmail,
        );
        expect(invitationExists).toBe(true);
      }
    } catch (error) {
      console.error(`Test failed with error: ${error}`);
      throw error;
    }
  });
});
