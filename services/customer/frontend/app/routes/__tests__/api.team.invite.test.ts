import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  type MockedFunction,
} from "vitest";
import { faker } from "@faker-js/faker";
import { action } from "../api.team.invite";
import { CustomerUiRole } from "~services/auth/central/server/auth_entities_pb";
import { ensureTeamForUser } from "../../utils/team.server";

// Mock the ensureTeamForUser function from api.team
const ensureTeamForUserMock = ensureTeamForUser as MockedFunction<
  typeof ensureTeamForUser
>;

vi.mock("../../utils/team.server", () => {
  return {
    ensureTeamForUser: vi.fn(),
    teamsAllowed: vi.fn().mockResolvedValue(true),
  };
});

vi.mock("../../.server/auth", async () => {
  const TenantTier = (
    await import("~services/tenant_watcher/tenant_watcher_pb")
  ).TenantTier;
  return {
    withApiRoute: vi.fn((fn) => {
      return (args: unknown) => fn(args);
    }),
    getTenantTier: vi.fn().mockResolvedValue(TenantTier.PROFESSIONAL),
  };
});

// Create a mock for TenantWatcher
const mockTenantWatcherInstance = {
  tenantFor: vi.fn(),
  tenantsFor: vi.fn(),
};

vi.mock("../../.server/grpc/tenant-watcher", () => {
  return {
    TenantWatcherCachingClient: {
      getInstance: vi.fn(() => mockTenantWatcherInstance),
    },
  };
});

// Create a shared mock instance that will be returned by getInstance
const mockAuthCentralInstance = {
  getUserOnTenant: vi.fn(),
  inviteUsersToTenant: vi.fn(),
  createTenantForTeam: vi.fn(),
};

// Mock the Auth Central client
vi.mock("../../.server/grpc/auth-central", () => {
  return {
    AuthCentralClient: {
      getInstance: vi.fn(() => mockAuthCentralInstance),
    },
  };
});

// Add this mock for the session utility
vi.mock("../../utils/session.server", () => {
  return {
    updateSessionWithTenantAndRoles: vi.fn().mockResolvedValue({
      headers: {},
    }),
  };
});

describe("api.team.invite action", () => {
  const tenantId = faker.string.uuid();
  const adminUserId = faker.string.uuid();
  const nonAdminUserId = faker.string.uuid();

  const mockAdminUser = {
    userId: adminUserId,
    sessionId: faker.string.uuid(),
    tenantId: tenantId,
    tenantName: faker.company.name(),
    shardNamespace: faker.system.directoryPath(),
    email: faker.internet.email(),
    roles: ["ADMIN"],
    createdAt: faker.date.recent().getTime(),
  };

  const mockNonAdminUser = {
    ...mockAdminUser,
    userId: nonAdminUserId,
    roles: [],
  };

  const mockUserWithoutTenant = {
    ...mockAdminUser,
    tenantId: undefined,
  };

  const validEmails = [
    faker.internet.email(),
    faker.internet.email(),
    faker.internet.email(),
  ];

  const mockLog = {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock ensureTeamForUser to return the tenant ID by default
    vi.mocked(ensureTeamForUserMock).mockResolvedValue(tenantId);

    // Mock tenantWatcher to return a valid tenant
    mockTenantWatcherInstance.tenantFor.mockResolvedValue({
      id: tenantId,
      name: faker.company.name(),
      shardNamespace: faker.system.directoryPath(),
      config: {
        configs: {
          is_self_serve_team: "true",
        },
      },
    });

    // Mock getUserOnTenant to return admin role for admin user and non-admin role for others
    mockAuthCentralInstance.getUserOnTenant.mockImplementation(
      async (_, userId) => {
        if (userId === adminUserId) {
          return {
            customerUiRoles: [CustomerUiRole.ADMIN],
          };
        }
        return {
          customerUiRoles: [CustomerUiRole.UNKNOWN_CUSTOMER_UI_ROLE],
        };
      },
    );

    // Mock successful invitation by default
    mockAuthCentralInstance.inviteUsersToTenant.mockResolvedValue({
      invitationStatuses: validEmails.map((email) => ({
        email,
        status: 1, // SUCCESS
      })),
    });
  });

  it("should return 405 for non-POST requests", async () => {
    const mockRequest = new Request("http://localhost/api/team/invite", {
      method: "GET",
    });

    const response = await action({
      request: mockRequest,
      user: mockAdminUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(405);
      const data = await response.json();
      expect(data).toHaveProperty("message", "Method not allowed");
    }
  });

  it("should create a team if user doesn't have one", async () => {
    const newTenantId = faker.string.uuid();
    vi.mocked(ensureTeamForUserMock).mockResolvedValueOnce(newTenantId);

    const mockRequest = new Request("http://localhost/api/team/invite", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ emails: validEmails }),
    });

    const response = await action({
      request: mockRequest,
      user: mockUserWithoutTenant,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
      expect(ensureTeamForUserMock).toHaveBeenCalledWith(mockUserWithoutTenant);
      expect(mockAuthCentralInstance.inviteUsersToTenant).toHaveBeenCalledWith(
        mockUserWithoutTenant,
        validEmails,
        newTenantId,
      );
    }
  });

  it("should allow non-admin to invite", async () => {
    const mockRequest = new Request("http://localhost/api/team/invite", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ emails: validEmails }),
    });

    const response = await action({
      request: mockRequest,
      user: mockNonAdminUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
    }
  });

  it("should return 400 for invalid request format", async () => {
    const mockRequest = new Request("http://localhost/api/team/invite", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ invalidField: "value" }), // Missing emails field
    });

    const response = await action({
      request: mockRequest,
      user: mockAdminUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty("message", "Invalid request format");
    }
  });

  it("should return 400 if no emails provided", async () => {
    const mockRequest = new Request("http://localhost/api/team/invite", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ emails: [] }), // Empty emails array
    });

    const response = await action({
      request: mockRequest,
      user: mockAdminUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty("message", "No emails provided");
    }
  });

  it("should return 200 if all invitations are successful", async () => {
    const mockRequest = new Request("http://localhost/api/team/invite", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ emails: validEmails }),
    });

    const response = await action({
      request: mockRequest,
      user: mockAdminUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
      expect(mockAuthCentralInstance.inviteUsersToTenant).toHaveBeenCalledWith(
        mockAdminUser,
        validEmails,
        tenantId,
      );
    }
  });

  it("should return 207 if some invitations failed", async () => {
    const failedEmail = validEmails[0];

    mockAuthCentralInstance.inviteUsersToTenant.mockResolvedValueOnce({
      invitationStatuses: [
        { email: failedEmail, status: 2 }, // FAILED
        ...validEmails.slice(1).map((email) => ({
          email,
          status: 1, // SUCCESS
        })),
      ],
    });

    const mockRequest = new Request("http://localhost/api/team/invite", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ emails: validEmails }),
    });

    const response = await action({
      request: mockRequest,
      user: mockAdminUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(207);
      const data = await response.json();
      expect(data).toHaveProperty("failed");
      expect(data.failed).toContain(failedEmail);
    }
  });

  it("should return 400 if all invitations failed", async () => {
    mockAuthCentralInstance.inviteUsersToTenant.mockResolvedValueOnce({
      invitationStatuses: validEmails.map((email) => ({
        email,
        status: 2, // FAILED
      })),
    });

    const mockRequest = new Request("http://localhost/api/team/invite", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ emails: validEmails }),
    });

    const response = await action({
      request: mockRequest,
      user: mockAdminUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty("failed");
      expect(data.failed).toHaveLength(validEmails.length);
    }
  });

  it("should handle errors when inviting users", async () => {
    mockAuthCentralInstance.inviteUsersToTenant.mockRejectedValueOnce(
      new Error("Failed to invite users"),
    );

    const mockRequest = new Request("http://localhost/api/team/invite", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ emails: validEmails }),
    });

    const response = await action({
      request: mockRequest,
      user: mockAdminUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data).toHaveProperty("message", "Internal Server Error");
    }
  });

  it("should handle JSON parsing errors", async () => {
    const mockRequest = new Request("http://localhost/api/team/invite", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: "invalid json", // Invalid JSON
    });

    const response = await action({
      request: mockRequest,
      user: mockAdminUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty("message", "Invalid request format");
    }
  });
});
