import { json } from "@remix-run/node";
import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  type MockedFunction,
} from "vitest";
import { faker } from "@faker-js/faker";
import {
  CustomerUiRole,
  User,
  Subscription,
} from "~services/auth/central/server/auth_entities_pb";
import {
  ListTenantUsersResponse,
  GetSubscriptionResponse,
  type CreateTenantForTeamResponse,
} from "~services/auth/central/server/auth_pb";
import type { LoaderFunction } from "@remix-run/router";
import { loader, action } from "../api.team";
import {
  ensureTeamForUser,
  isUserInSelfServeTeam,
} from "app/utils/team.server";

const ensureTeamForUserMock = ensureTeamForUser as MockedFunction<
  typeof ensureTeamForUser
>;

const isUserInSelfServeTeamMock = isUserInSelfServeTeam as MockedFunction<
  typeof isUserInSelfServeTeam
>;

// Mock the api.team module
vi.mock("../../utils/team.server", () => {
  return {
    ensureTeamForUser: vi.fn(),
    isUserInSelfServeTeam: vi.fn(),
    teamsAllowed: vi.fn().mockResolvedValue(true),
  };
});

vi.mock("../../utils/api", () => {
  return {
    apiJson: vi.fn((data, init) => json(data, init)),
  };
});

vi.mock("../../.server/auth", async () => {
  const TenantTier = (
    await import("~services/tenant_watcher/tenant_watcher_pb")
  ).TenantTier;
  return {
    withApiRoute: vi.fn((fn) => {
      return (args: unknown) => fn(args);
    }),
    getTenantTier: vi.fn().mockResolvedValue(TenantTier.PROFESSIONAL),
  };
});

const mockTenantWatcherInstance = {
  tenantFor: vi.fn(),
  tenantsFor: vi.fn(),
};

vi.mock("../../.server/grpc/tenant-watcher", () => {
  return {
    TenantWatcherCachingClient: {
      getInstance: vi.fn(() => mockTenantWatcherInstance),
    },
  };
});

// Create a shared mock instance that will be returned by getInstance
const mockAuthCentralInstance = {
  listTenantUsers: vi.fn(),
  getUserOnTenant: vi.fn(),
  getTenantInvitations: vi.fn(),
  getSubscriptionFromDatabase: vi.fn(),
  updateSubscription: vi.fn(),
  getCreateTenantForTeamStatus: vi.fn(),
  createTenantForTeam: vi.fn(),
};

// Mock the Auth Central client
vi.mock("../../.server/grpc/auth-central", () => {
  return {
    AuthCentralClient: {
      getInstance: vi.fn(() => mockAuthCentralInstance),
    },
  };
});

// Add this mock for the session utility
vi.mock("../../utils/session.server", () => {
  return {
    updateSessionWithTenantAndRoles: vi.fn().mockResolvedValue({
      headers: {},
    }),
  };
});

function createFakeUser(overrides: Partial<User> = {}) {
  return new User({
    id: faker.string.uuid(),
    email: faker.internet.email(),
    createdAt: {
      seconds: BigInt(Math.floor(Date.now() / 1000)),
      nanos: 0,
    },
    nonce: BigInt(0),
    tenants: [],
    stripeCustomerId: faker.string.alphanumeric(16),
    ...overrides,
  });
}

function createFakeListTenantUsersResponse(
  userOverrides: Partial<User>[] = [],
) {
  const users = userOverrides.map((overrides) => createFakeUser(overrides));
  return new ListTenantUsersResponse({
    users: users,
  });
}

function createFakeSubscription(overrides: Partial<Subscription> = {}) {
  return new Subscription({
    subscriptionId: faker.string.uuid(),
    stripeCustomerId: faker.string.alphanumeric(16),
    priceId: "price_1QuILlAcLbOdpxPg6ehebUNN",
    seats: 10,
    ...overrides,
  });
}

function createFakeGetSubscriptionResponse(
  overrides: Partial<Subscription> = {},
) {
  return new GetSubscriptionResponse({
    subscription: createFakeSubscription(overrides),
  });
}

describe("api.team loader", () => {
  const tenantId = faker.string.uuid();
  const adminUserId = faker.string.uuid();
  const nonAdminUserId = faker.string.uuid();
  const subscriptionId = faker.string.uuid();

  const mockUser = {
    userId: faker.string.uuid(),
    sessionId: faker.string.uuid(),
    tenantId: tenantId,
    tenantName: faker.company.name(),
    shardNamespace: faker.system.directoryPath(),
    email: faker.internet.email(),
    roles: ["ADMIN"],
    createdAt: faker.date.recent().getTime(),
  };

  const mockUserWithoutTenant = {
    ...mockUser,
    tenantId: undefined,
  };

  const mockLog = {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementations
    mockAuthCentralInstance.listTenantUsers.mockResolvedValue(
      createFakeListTenantUsersResponse([
        { id: adminUserId },
        { id: nonAdminUserId },
      ]),
    );

    mockAuthCentralInstance.getUserOnTenant.mockImplementation(
      async (_, userId) => {
        return {
          customerUiRoles:
            userId === adminUserId
              ? [CustomerUiRole.ADMIN]
              : [CustomerUiRole.UNKNOWN_CUSTOMER_UI_ROLE],
        };
      },
    );

    mockAuthCentralInstance.getTenantInvitations.mockResolvedValue({
      invitations: Array(2).fill({
        id: faker.string.uuid(),
        inviteeEmail: faker.internet.email(),
        createdAt: {
          seconds: BigInt(Math.floor(Date.now() / 1000)),
          nanos: 0,
        },
      }),
    });

    mockAuthCentralInstance.getSubscriptionFromDatabase.mockResolvedValue(
      createFakeGetSubscriptionResponse({
        subscriptionId: subscriptionId,
        seats: 10,
      }),
    );

    // Default tenant watcher mock
    vi.mocked(mockTenantWatcherInstance.tenantFor).mockResolvedValue({
      id: tenantId,
      name: "test-tenant",
      shardNamespace: "test-shard",
      cloud: "test-cloud",
      config: {
        configs: {
          is_self_serve_team: "true",
        },
      },
    } as any);
  });

  it("should return status 'none' when user has no tenant", async () => {
    const request = new Request("http://localhost/api/team");
    const response = await loader({
      request,
      user: mockUserWithoutTenant,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data).toHaveProperty("status", "none");
    }
  });

  it("should return status 'none' for non-self-serve team", async () => {
    // Mock tenant with is_self_serve_team=false
    vi.mocked(mockTenantWatcherInstance.tenantFor).mockResolvedValueOnce({
      id: tenantId,
      name: "test-tenant",
      shardNamespace: "test-shard",
      cloud: "test-cloud",
      config: {
        configs: {
          is_self_serve_team: "false",
        },
      },
    } as any);

    const request = new Request("http://localhost/api/team");
    const response = await loader({
      request,
      user: mockUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data).toHaveProperty("status", "none");
    }
  });

  it("should return team data for self-serve team", async () => {
    const request = new Request("http://localhost/api/team");
    const response = await loader({
      request,
      user: mockUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
      const data = await response.json();

      expect(data).toHaveProperty("status", "active");
      expect(data).toHaveProperty("team");
      expect(data.team).toHaveProperty("id", tenantId);
      expect(data.team).toHaveProperty("seats", 10);
      expect(data.team).toHaveProperty("users");
      expect(data.team).toHaveProperty("invitations");

      expect(data.team.users).toHaveLength(2);
      const adminUser = data.team.users.find((u: any) => u.id === adminUserId);
      const nonAdminUser = data.team.users.find(
        (u: any) => u.id === nonAdminUserId,
      );
      expect(adminUser).toHaveProperty("role", "ADMIN");
      expect(nonAdminUser).toHaveProperty("role", "MEMBER");
    }
  });

  it("should handle tenant watcher errors gracefully", async () => {
    // Mock tenant watcher to throw an error
    vi.mocked(mockTenantWatcherInstance.tenantFor).mockRejectedValueOnce(
      new Error("Failed to get tenant"),
    );

    const request = new Request("http://localhost/api/team");
    const response = await loader({
      request,
      user: mockUser,
      log: mockLog,
    } as any);

    // Should still return team data even if tenant watcher fails
    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data).toHaveProperty("status", "active");
      expect(data).toHaveProperty("team");
    }
  });

  it("should handle subscription service errors gracefully", async () => {
    // Mock subscription service to throw an error
    mockAuthCentralInstance.getSubscriptionFromDatabase.mockRejectedValueOnce(
      new Error("Failed to get subscription"),
    );

    const request = new Request("http://localhost/api/team");
    const response = await loader({
      request,
      user: mockUser,
      log: mockLog,
    } as any);

    // Should use number of users as seats if subscription service fails
    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data).toHaveProperty("status", "active");
      expect(data).toHaveProperty("team");
      expect(data.team).toHaveProperty("seats", 2); // Number of users in the mock response
    }
  });

  it("should handle validation errors", async () => {
    // Mock listTenantUsers to return invalid data that will fail validation
    mockAuthCentralInstance.listTenantUsers.mockResolvedValueOnce({
      users: [{ id: "not-a-uuid" }], // Invalid UUID will fail validation
    });

    const request = new Request("http://localhost/api/team");
    const response = await loader({
      request,
      user: mockUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data).toHaveProperty("message", "Internal Server Error");
    }
  });
});

describe("api.team action - POST", () => {
  const tenantId = faker.string.uuid();
  const newTenantCreationId = faker.string.uuid();

  const mockUser = {
    userId: faker.string.uuid(),
    sessionId: faker.string.uuid(),
    tenantId: tenantId,
    tenantName: faker.company.name(),
    shardNamespace: faker.system.directoryPath(),
    email: faker.internet.email(),
    roles: ["ADMIN"],
    createdAt: faker.date.recent().getTime(),
  };

  const mockLog = {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Default tenant watcher mock for non-self-serve team
    vi.mocked(mockTenantWatcherInstance.tenantFor).mockResolvedValue({
      id: tenantId,
      name: "test-tenant",
      shardNamespace: "test-shard",
      cloud: "test-cloud",
      config: {
        configs: {
          is_self_serve_team: "false",
        },
      },
    } as any);

    // Default createTenantForTeam mock
    mockAuthCentralInstance.createTenantForTeam.mockResolvedValue({
      tenantCreationId: newTenantCreationId,
    } as CreateTenantForTeamResponse);

    // Default isUserInSelfServeTeam mock - returns false by default
    isUserInSelfServeTeamMock.mockResolvedValue(false);
  });

  it("should create a new team successfully", async () => {
    const request = new Request("http://localhost/api/team", {
      method: "POST",
    });

    const response = await action({
      request,
      user: mockUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data).toHaveProperty("tenant_creation_id", newTenantCreationId);
    }

    expect(mockAuthCentralInstance.createTenantForTeam).toHaveBeenCalledWith(
      mockUser,
    );
  });

  it("should reject team creation if user already has a self-serve team", async () => {
    // Mock isUserInSelfServeTeam to return true
    isUserInSelfServeTeamMock.mockResolvedValueOnce(true);

    const request = new Request("http://localhost/api/team", {
      method: "POST",
    });

    const response = await action({
      request,
      user: mockUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty(
        "message",
        "User already associated with a self-serve team",
      );
    }

    expect(mockAuthCentralInstance.createTenantForTeam).not.toHaveBeenCalled();
  });

  it("should handle tenant watcher errors during team creation", async () => {
    // Mock isUserInSelfServeTeam to throw an error
    isUserInSelfServeTeamMock.mockRejectedValueOnce(
      new Error("Failed to get tenant"),
    );

    const request = new Request("http://localhost/api/team", {
      method: "POST",
    });

    const response = await action({
      request,
      user: mockUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data).toHaveProperty("message", "Failed to check tenant status");
    }

    expect(mockAuthCentralInstance.createTenantForTeam).not.toHaveBeenCalled();
  });

  it("should handle createTenantForTeam errors", async () => {
    // Mock isUserInSelfServeTeam to return false
    isUserInSelfServeTeamMock.mockResolvedValueOnce(false);

    // Mock createTenantForTeam to throw an error
    mockAuthCentralInstance.createTenantForTeam.mockRejectedValueOnce(
      new Error("Failed to create tenant"),
    );

    const request = new Request("http://localhost/api/team", {
      method: "POST",
    });

    const response = await action({
      request,
      user: mockUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data).toHaveProperty("message", "Failed to start creating team");
    }
  });
});

describe("api.team action - PATCH", () => {
  const tenantId = faker.string.uuid();
  const subscriptionId = faker.string.uuid();

  const mockAdminUser = {
    userId: faker.string.uuid(),
    sessionId: faker.string.uuid(),
    tenantId: tenantId,
    tenantName: faker.company.name(),
    shardNamespace: faker.system.directoryPath(),
    email: faker.internet.email(),
    roles: ["ADMIN"],
    createdAt: faker.date.recent().getTime(),
  };

  const mockUserWithoutTenant = {
    ...mockAdminUser,
    tenantId: undefined,
  };

  const mockLog = {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock ensureTeamForUser to return the tenant ID by default
    ensureTeamForUserMock.mockResolvedValue(tenantId);

    // Mock tenantWatcher to return a valid tenant
    mockTenantWatcherInstance.tenantFor.mockResolvedValue({
      id: tenantId,
      name: faker.company.name(),
      shardNamespace: faker.system.directoryPath(),
      config: {
        configs: {
          is_self_serve_team: "true",
        },
      },
    });

    mockAuthCentralInstance.getSubscriptionFromDatabase.mockResolvedValue(
      createFakeGetSubscriptionResponse({
        subscriptionId: subscriptionId,
        seats: 10,
      }),
    );

    mockAuthCentralInstance.updateSubscription.mockResolvedValue({});
  });

  it("should create a team if user doesn't have one", async () => {
    const newTenantId = faker.string.uuid();
    ensureTeamForUserMock.mockResolvedValueOnce(newTenantId);

    const mockRequest = new Request("http://localhost/api/team", {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ seats: 15 }),
    });

    const response = await action({
      request: mockRequest,
      user: mockUserWithoutTenant,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
    }
  });

  it("should return 400 for invalid request body", async () => {
    const mockRequest = new Request("http://localhost/api/team", {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ invalid: "data" }),
    });

    let response: ReturnType<LoaderFunction> | null = null;
    try {
      response = await action({
        request: mockRequest,
        user: mockAdminUser,
        log: mockLog,
      } as any);
    } catch (error) {
      // Ignore the error, we're just testing the response
    }

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty("message", "Invalid request format");
    }
  });

  it("should successfully update seats when valid", async () => {
    const newSeats = 15;
    const mockRequest = new Request("http://localhost/api/team", {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ seats: newSeats }),
    });

    const response = await action({
      request: mockRequest,
      user: mockAdminUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data).toHaveProperty("seats", newSeats);
    }
  });

  it("should handle getSubscription errors", async () => {
    mockAuthCentralInstance.getSubscriptionFromDatabase.mockRejectedValueOnce(
      new Error("Failed to get subscription"),
    );

    const mockRequest = new Request("http://localhost/api/team", {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ seats: 15 }),
    });

    const response = await action({
      request: mockRequest,
      user: mockAdminUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data).toHaveProperty(
        "message",
        "Failed to retrieve current subscription",
      );
    }
  });

  it("should handle updateSubscription errors", async () => {
    mockAuthCentralInstance.updateSubscription.mockRejectedValueOnce(
      new Error("Failed to update subscription"),
    );

    const mockRequest = new Request("http://localhost/api/team", {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ seats: 15 }),
    });

    const response = await action({
      request: mockRequest,
      user: mockAdminUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data).toHaveProperty(
        "message",
        "Failed to update subscription seats",
      );
    }
  });

  it("should handle non-PATCH/POST methods", async () => {
    const mockRequest = new Request("http://localhost/api/team", {
      method: "DELETE",
    });

    const response = await action({
      request: mockRequest,
      user: mockAdminUser,
      log: mockLog,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(405);
      const data = await response.json();
      expect(data).toHaveProperty("message", "Method not allowed");
    }
  });
});
