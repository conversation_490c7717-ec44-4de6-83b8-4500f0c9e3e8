import { json } from "@remix-run/node";
import { isAdmin, withApiRoute } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import type { OrbCustomerInfoSchema } from "app/schemas/orb";
import { Config } from "app/.server/config";
import { OrbSubscriptionInfo_SubscriptionStatus } from "~services/auth/central/server/auth_pb";

export const loader = withApiRoute(async ({ user, log }) => {
  const start = Date.now();

  log.info("Fetching subscription information");

  // Get user details from Auth Central
  const authCentralClient = AuthCentralClient.getInstance();

  try {
    // TODO split into separate apis
    const [userOrbPlanInfo, userOrbInfo] = await Promise.all([
      await authCentralClient
        .getUserOrbPlanInfo(user)
        .then((response) => response.orbPlanInfo),
      await authCentralClient
        .getUserOrbSubscriptionInfo(user)
        .then((response) => response.orbSubscriptionInfo),
    ]);

    if (!userOrbPlanInfo || !userOrbInfo) {
      throw new Error("Failed to get user Orb info");
    }
    if (userOrbInfo.case !== "subscription") {
      throw new Error("Failed to get user Orb info");
    }

    const subscriptionInfo = userOrbInfo.value;
    const userIsAdmin = await isAdmin(user);

    const result: OrbCustomerInfoSchema = {
      portalUrl: userIsAdmin ? subscriptionInfo.portalUrl : null,
      planId: userOrbPlanInfo.externalPlanId,
      augmentPlanType: Config.orbPlanIdToAugmentPlanType(
        userOrbPlanInfo.externalPlanId,
      ),
      planName: userOrbPlanInfo?.formattedPlanName ?? null,
      billingPeriodEnd: subscriptionInfo.billingPeriodEndDateIso ?? null,
      trialPeriodEnd: subscriptionInfo.trialPeriodEndDateIso ?? null,
      creditsRenewingEachBillingCycle:
        subscriptionInfo.usageUnitsRenewingEachBillingCycle,
      creditsIncludedThisBillingCycle:
        subscriptionInfo.usageUnitsIncludedThisBillingCycle,
      billingCycleBillingAmount:
        subscriptionInfo.nextBillingCycleAmount ?? "0.00",
      monthlyTotalCost: subscriptionInfo.monthlyTotalCost ?? "0.00",
      pricePerSeat: userOrbPlanInfo?.pricePerSeat ?? "0.00",
      maxNumSeats: userOrbPlanInfo?.maxNumSeats ?? 100,
      numberOfSeatsThisBillingCycle: subscriptionInfo.seats,
      numberOfSeatsNextBillingCycle: subscriptionInfo.seats,
      subscriptionEndDate: subscriptionInfo.subscriptionEndDateIso ?? null,
      planIsExpired:
        subscriptionInfo.subscriptionStatus ===
        OrbSubscriptionInfo_SubscriptionStatus.ENDED,
      addUsageAvailable: !!userOrbPlanInfo?.addUsageAvailable,
      teamsAllowed: !!userOrbPlanInfo?.teamsAllowed,
      additionalUsageUnitCost:
        userOrbPlanInfo?.additionalUsageUnitCost ?? "0.00",
      scheduledTargetPlanId: subscriptionInfo.scheduledTargetPlanId ?? null,
    };

    const status = 200;
    log.info("Successfully fetched subscription information", {
      plan_id: userOrbPlanInfo.externalPlanId,
      plan_name: userOrbPlanInfo?.formattedPlanName,
      subscription_status: subscriptionInfo.subscriptionStatus,
      is_admin: userIsAdmin,
      duration_ms: Date.now() - start,
      status_code: status,
    });

    return json(result);
  } catch (e) {
    const status = 500;
    log.error("Failed to fetch Orb customer info", e, {
      duration_ms: Date.now() - start,
      status_code: status,
    });
    return json({ error: "Failed to fetch customer info" }, { status });
  }
});

export const action = withApiRoute(
  async ({ user, request, log }) => {
    const start = Date.now();

    log.info("Processing subscription action", {
      method: request.method,
    });

    if (
      request.method !== "DELETE" &&
      request.method !== "PATCH" &&
      request.method !== "PUT"
    ) {
      const status = 405;
      log.info("Method not allowed", {
        method: request.method,
        status_code: status,
      });
      return json({ error: "Method not allowed" }, { status });
    }

    const authCentralClient = AuthCentralClient.getInstance();

    if (request.method === "DELETE") {
      try {
        log.info("Cancelling subscription");
        await authCentralClient.cancelSubscription(user);

        const status = 200;
        log.info("Subscription cancelled successfully", {
          duration_ms: Date.now() - start,
          status_code: status,
        });
        return json({});
      } catch (error) {
        const status = 500;
        log.error("Failed to cancel subscription", error, {
          duration_ms: Date.now() - start,
          status_code: status,
        });
        return json({ error: "Failed to cancel subscription" }, { status });
      }
    } else if (request.method === "PATCH") {
      try {
        log.info("Unscheduling pending subscription cancellation");
        await authCentralClient.unschedulePendingSubscriptionCancellation(user);

        const status = 200;
        log.info("Successfully unscheduled subscription cancellation", {
          duration_ms: Date.now() - start,
          status_code: status,
        });
        return json({});
      } catch (error) {
        const status = 500;
        log.error("Failed to unschedule cancellation", error, {
          duration_ms: Date.now() - start,
          status_code: status,
        });
        return json({ error: "Failed to unschedule cancellation" }, { status });
      }
    } else if (request.method === "PUT") {
      try {
        log.info("Unscheduling plan changes");
        await authCentralClient.unschedulePlanChanges(user);

        const status = 200;
        log.info("Successfully unscheduled plan changes", {
          duration_ms: Date.now() - start,
          status_code: status,
        });
        return json({});
      } catch (error) {
        const status = 500;
        log.error("Failed to unschedule plan changes", error, {
          duration_ms: Date.now() - start,
          status_code: status,
        });
        return json({ error: "Failed to unschedule plan changes" }, { status });
      }
    }

    return json({});
  },
  {
    adminOnly: true,
  },
);
