import { json } from "@remix-run/node";
import { withApiRoute } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { CustomerUiRole } from "~services/auth/central/server/auth_entities_pb";
import type {
  TeamUserDeleteSuccessSchema,
  TeamUserDeleteErrorSchema,
} from "../schemas/team-user-delete";
import { teamsAllowed } from "app/utils/team.server";

export const action = withApiRoute(async ({ request, params, user, log }) => {
  if (request.method !== "DELETE") {
    return json<TeamUserDeleteErrorSchema>(
      { message: "Method not allowed" },
      { status: 405 },
    );
  }

  if (!(await teamsAllowed(user))) {
    return json<TeamUserDeleteErrorSchema>(
      { message: "Team management is not enabled" },
      { status: 403 },
    );
  }

  try {
    const { userId } = params;
    const { tenantId } = user;

    if (!userId) {
      return json<TeamUserDeleteErrorSchema>(
        { message: "User ID is required" },
        { status: 400 },
      );
    }

    if (!tenantId) {
      return json<TeamUserDeleteErrorSchema>(
        { message: "User not associated with a tenant" },
        { status: 400 },
      );
    }

    const authCentralClient = AuthCentralClient.getInstance();

    // Check if the current user is an admin OR the target is the current user
    try {
      const currentUserOnTenant = await authCentralClient.getUserOnTenant(
        user,
        user.userId,
        tenantId,
      );

      if (
        !currentUserOnTenant.customerUiRoles.includes(CustomerUiRole.ADMIN) &&
        user.userId !== userId
      ) {
        const status = 403;
        log.warn("Non-admin user attempted to remove another user", undefined, {
          target_user_id: userId,
          status_code: status,
        });
        return json<TeamUserDeleteErrorSchema>({}, { status });
      }
    } catch (error) {
      const status = 500;
      log.error("Failed to check admin status for user", error, {
        target_user_id: user.userId,
        status_code: status,
      });
      return json<TeamUserDeleteErrorSchema>(
        { message: "Internal Server Error" },
        { status },
      );
    }

    // Get all users in the tenant to check if target user exists and count admins
    let tenantUsers;
    try {
      tenantUsers = await authCentralClient.listTenantUsers(user, tenantId);
    } catch (error) {
      const status = 500;
      log.error("Failed to list users for tenant", error, {
        status_code: status,
      });
      return json<TeamUserDeleteErrorSchema>(
        { message: "Internal Server Error" },
        { status },
      );
    }

    const targetUserExists = tenantUsers.users.some(
      (tenantUser) => tenantUser.id === userId,
    );

    if (!targetUserExists) {
      const status = 404;
      log.warn("User not found in tenant", undefined, {
        target_user_id: userId,
        status_code: status,
      });
      return json<TeamUserDeleteErrorSchema>({}, { status });
    }

    // Count admins and check if target user is an admin
    let adminCount = 0;
    let isTargetUserAdmin = false;

    for (const tenantUser of tenantUsers.users) {
      try {
        const userOnTenant = await authCentralClient.getUserOnTenant(
          user,
          tenantUser.id,
          tenantId,
        );

        const isAdmin = userOnTenant.customerUiRoles.includes(
          CustomerUiRole.ADMIN,
        );

        if (isAdmin) {
          adminCount++;
        }

        if (tenantUser.id === userId && isAdmin) {
          isTargetUserAdmin = true;
        }
      } catch (error) {
        log.warn("Failed to get roles for user on tenant", error, {
          target_user_id: tenantUser.id,
        });
        // Continue checking other users
      }
    }

    // If the target user is the last admin, prevent removal
    if (isTargetUserAdmin && adminCount === 1) {
      const status = 400;
      log.warn("Attempted to remove the last admin", undefined, {
        target_user_id: userId,
        status_code: status,
      });
      return json<TeamUserDeleteErrorSchema>({}, { status });
    }

    // Remove the user from the tenant
    try {
      await authCentralClient.removeUserFromTenant(user, userId, tenantId);
      const status = 200;
      log.info("User removed from tenant", {
        target_user_id: userId,
        status_code: status,
      });
      return json<TeamUserDeleteSuccessSchema>({}, { status });
    } catch (error) {
      const status = 500;
      log.error("Failed to remove user from tenant", error, {
        target_user_id: userId,
        status_code: status,
      });
      return json<TeamUserDeleteErrorSchema>(
        { message: "Internal Server Error" },
        { status },
      );
    }
  } catch (error) {
    const status = 500;
    log.error("Error removing user from tenant", error, {
      status_code: status,
    });
    return json<TeamUserDeleteErrorSchema>(
      { message: "Internal Server Error" },
      { status },
    );
  }
});
