import { json, redirect } from "@remix-run/node";
import { useLoaderD<PERSON>, useNavigate } from "@remix-run/react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Box, Container, Spinner } from "@radix-ui/themes";
import { useState } from "react";
import {
  userQueryOptions,
  putUserOnPlan,
  createCheckoutSession,
} from "../client-cache";
import { PlanPicker } from "../components/account/Billing/PlanPicker";
import type { PlanOptionSchema } from "app/schemas/plan";
import { getPlanDescriptionWithPricingLink } from "../components/account/Billing/utils";
import { withAuth } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { getAllPlans } from "../utils/plans.server";
import { OrbSubscriptionInfo_SubscriptionStatus } from "~services/auth/central/server/auth_pb";
import TeamPlanExpired from "app/components/account/Billing/TeamPlanExpired";
import { ProgressPage } from "app/components/ui/ProgressPage";
import { Callout } from "app/components/ui/Callout";
import { logger } from "@augment-internal/logging";

function getMessage(
  hasExpiredPlan: boolean,
  hasPaymentFailureCancellation: boolean,
) {
  if (hasPaymentFailureCancellation) {
    return "Your subscription has been cancelled due to a payment failure. Please select a plan below to continue using Augment.";
  } else if (hasExpiredPlan) {
    return "Your subscription has expired. Please select a plan below to continue using Augment.";
  } else {
    return "Please select a plan below to continue using Augment.";
  }
}

export const loader = withAuth(
  async ({ user }) => {
    try {
      const [plans, userOrbSubscriptionInfo, paymentInfo] = await Promise.all([
        getAllPlans(user),
        AuthCentralClient.getInstance().getUserOrbSubscriptionInfo(user),
        AuthCentralClient.getInstance().getUserOrbPaymentInfo(user),
      ]);

      const subscription = userOrbSubscriptionInfo.orbSubscriptionInfo;
      const hasExpiredPlan =
        subscription.case === "subscription" &&
        subscription.value.subscriptionStatus ===
          OrbSubscriptionInfo_SubscriptionStatus.ENDED;

      if (!hasExpiredPlan) {
        return redirect("/account");
      }

      const hasPaymentFailureCancellation =
        subscription?.case === "subscription" &&
        subscription.value.subscriptionStatus ===
          OrbSubscriptionInfo_SubscriptionStatus.ENDED &&
        subscription.value.cancelledDueToPaymentFailure;

      const message = getMessage(hasExpiredPlan, hasPaymentFailureCancellation);

      return json({
        message,
        hasExpiredPlan,
        plans,
        hasPaymentMethod:
          paymentInfo.hasPaymentMethod && !hasPaymentFailureCancellation,
      });
    } catch (error) {
      logger.error("Error in select-plan loader:", { error });
      throw error;
    }
  },
  {
    adminOnly: false,
  },
);

export default function SelectPlanPage() {
  const { message, plans, hasPaymentMethod } = useLoaderData<typeof loader>();
  const navigate = useNavigate();

  // We still need the user data for the mutation, but we don't use it for displaying the current plan
  const {
    data: userData,
    isLoading,
    isError,
    error,
  } = useQuery(userQueryOptions);

  // State to track the selected plan
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);

  // Use the shared mutation from client-cache
  const putUserOnPlanMutation = useMutation(putUserOnPlan);

  // This function only updates the local state when a plan is selected
  const handleSelectPlan = (planId: string) => {
    const selectedPlan = plans.find(
      (plan: PlanOptionSchema) => plan.id === planId,
    );
    if (!selectedPlan) return;

    // Only allow community or paid plans
    if (
      selectedPlan.augmentPlanType === "community" ||
      selectedPlan.augmentPlanType === "paid"
    ) {
      setSelectedPlanId(planId);
    }
  };

  // Use the createCheckoutSession mutation
  const createCheckoutSessionMutation = useMutation(createCheckoutSession);

  // This function triggers the mutation when the user clicks "Proceed to Checkout"
  const handleProceedToCheckout = async () => {
    if (selectedPlanId) {
      // Check if it's a Community plan
      const selectedPlan = plans.find(
        (plan: PlanOptionSchema) => plan.id === selectedPlanId,
      );
      const isCommunityPlan = selectedPlan?.augmentPlanType === "community";

      // For Community plan or if user has payment method, proceed directly
      if (hasPaymentMethod || isCommunityPlan) {
        handlePlanChange();
        return;
      }

      // For non-Community plans without payment method, create checkout session
      createCheckoutSessionMutation.mutate({ planId: selectedPlanId });
    }
  };

  // Helper function to handle the plan change
  const handlePlanChange = () => {
    putUserOnPlanMutation.mutate(
      { planId: selectedPlanId! },
      {
        onSuccess: () => {
          navigate("/account/subscription");
        },
      },
    );
  };

  if (isLoading) {
    return (
      <ProgressPage
        title="Loading Your Account"
        description=""
        message=""
        progressColor="ds-color-accent"
        icon={<Spinner />}
      />
    );
  }
  if (isError) {
    return (
      <Callout type="error">
        Error Loading Your Account
        <p>{error.message}</p>
      </Callout>
    );
  }

  if (
    !isLoading &&
    userData &&
    !userData.isAdmin &&
    userData.isSelfServeTeamMember
  ) {
    // non admin users on self-serve teams with expired plans should be given the option
    // to exit the team or contact their admin.
    // Note we are doing this on the select-plan page to avoid dealing with routing
    // as a temporary stopgap while we sort out loading and routing issues.
    return <TeamPlanExpired />;
  }

  return (
    <Container
      size="3"
      style={{ maxWidth: "800px", margin: "0 auto", padding: "40px 0" }}
    >
      <Box style={{ padding: "24px" }}>
        <PlanPicker
          plans={plans || []}
          currentPlanId="" // Don't show any plan as current on this page
          onSelectPlan={handleSelectPlan}
          onProceedToCheckout={handleProceedToCheckout}
          selectedPlanId={selectedPlanId}
          title="Select a Plan"
          description={getPlanDescriptionWithPricingLink()}
          showMessage={true}
          message={message}
          isLoading={
            putUserOnPlanMutation.isPending ||
            createCheckoutSessionMutation.isPending
          }
        />
      </Box>
    </Container>
  );
}
