import { withApiRoute } from "../.server/auth";
import {
  cacheableJson,
  getAuthenticatedApiData,
  getDateFilters,
} from "../utils/api";
import { cachingClient } from "../.server/grpc/request-insights-analytics";

export const loader = withApiRoute(
  async ({ request, user }) => {
    const { tenantId } = user;
    const dateFilters = getDateFilters(request);

    const { completionStats } = await getAuthenticatedApiData(
      user,
      {
        tenantId,
        dateFilters,
      },
      cachingClient.getCompletionStats.bind(cachingClient),
    );

    return cacheableJson(completionStats);
  },
  {
    adminOnly: true,
    enterpriseOnly: true,
  },
);
