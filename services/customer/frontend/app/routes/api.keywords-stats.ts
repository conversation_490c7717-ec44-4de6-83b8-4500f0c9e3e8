import { withApiRoute } from "../.server/auth";
import {
  cacheableJson,
  getAuthenticatedApiData,
  getDateFilters,
} from "../utils/api";
import { cachingClient } from "../.server/grpc/request-insights-analytics";
import {
  GetKeywordsStatsRequest,
  GetKeywordsStatsRequest_RequestType,
  type GetKeywordsStatsResponse,
} from "~services/request_insight/analytics/request_insight_analytics_pb";

export const loader = withApiRoute(
  async ({ request, user }) => {
    const { tenantId } = user;
    const dateFilters = getDateFilters(request);

    const url = new URL(request.url);
    const requestType = url.searchParams.get("requestType");
    if (
      !requestType ||
      !Object.values(GetKeywordsStatsRequest_RequestType).includes(requestType)
    ) {
      throw Response.json(
        { message: "requestType is required" },
        { status: 400, statusText: "Bad Request" },
      );
    }

    const { keywordsStats } = await getAuthenticatedApiData<
      GetKeywordsStatsResponse,
      GetKeywordsStatsRequest
    >(
      user,
      new GetKeywordsStatsRequest({
        tenantId,
        dateFilters,
        requestType:
          GetKeywordsStatsRequest_RequestType[
            requestType as keyof typeof GetKeywordsStatsRequest_RequestType
          ],
      }),
      cachingClient.getKeywordsStats.bind(cachingClient),
    );

    return cacheableJson(keywordsStats);
  },
  {
    adminOnly: true,
    enterpriseOnly: true,
  },
);
