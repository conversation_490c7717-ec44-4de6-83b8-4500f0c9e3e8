import { queryClient } from "../queryClient.client";
import { teamQueryOptions } from "../queries";
import { mutationOptions } from "../queryOptions";
import {
  TeamInviteResponseSchema,
  type TeamInviteRequestSchema,
} from "../../schemas/team-invite";

export const addTeamMembers = mutationOptions({
  mutationFn: async (emails: string[]) => {
    const requestData: TeamInviteRequestSchema = { emails };

    const response = await fetch("/api/team/invite", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || "Failed to add team members");
    }

    return TeamInviteResponseSchema.parse(data);
  },
  onSuccess: () => {
    queryClient.invalidateQueries(teamQueryOptions);
  },
});
