import { queryClient } from "../queryClient.client";
import { teamQueryOptions } from "../queries";
import { mutationOptions } from "../queryOptions";
import { TeamDeleteInvitationResponseSchema } from "../../schemas/team-invite";

export const deleteTeamInvitation = mutationOptions({
  mutationFn: async (invitationId: string) => {
    const response = await fetch(`/api/team/invite/${invitationId}`, {
      method: "DELETE",
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || "Failed to delete team invitation");
    }

    return TeamDeleteInvitationResponseSchema.parse(data);
  },
  onSuccess: () => {
    queryClient.invalidateQueries(teamQueryOptions);
  },
});
