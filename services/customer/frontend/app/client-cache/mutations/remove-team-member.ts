import { queryClient } from "../queryClient.client";
import { teamQueryOptions } from "../queries";
import { mutationOptions } from "../queryOptions";
import { TeamUserDeleteResponseSchema } from "../../schemas/team-user-delete";

export const removeTeamMember = mutationOptions({
  mutationFn: async (userId: string) => {
    const response = await fetch(`/api/team/user/${userId}`, {
      method: "DELETE",
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || "Failed to remove team member");
    }

    return TeamUserDeleteResponseSchema.parse(data);
  },
  onSuccess: () => {
    queryClient.invalidateQueries(teamQueryOptions);
  },
});
