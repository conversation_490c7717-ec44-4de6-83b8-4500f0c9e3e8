import { mutationOptions } from "../queryOptions";
import { toast } from "app/components/ui/Toast";
import type { EmptyObject } from "@augment-internal/ts-utils/type";
import type { PutUserOnPlanRequestSchema } from "app/schemas/put-user-on-plan";
import { jotaiStore } from "../jotaiStore.client";
import {
  userPlanChangePendingBaseAtom,
  teamPlanChangePendingBaseAtom,
} from "../atoms/pending";
import { queryClient } from "../queryClient.client";
import {
  teamPlanChangePendingQueryOptions,
  userQueryOptions,
} from "../queries";

export const putUserOnPlan = mutationOptions({
  mutationKey: ["putUserOnPlan"],
  mutationFn: async ({ planId }: PutUserOnPlanRequestSchema) => {
    const response = await fetch(`/api/put-user-on-plan`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ planId }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || "Failed to update plan");
    }

    return data;
  },
  onSuccess: (data) => {
    // Check if the user is already on this plan
    if (data.alreadyOnPlan) {
      toast({
        title: "Already on this plan",
        description: data.message || "You are already on this plan.",
        status: "default",
      });
      return {} as EmptyObject;
    }

    // Reset invalid queries
    queryClient.resetQueries(userQueryOptions);
    queryClient.resetQueries(teamPlanChangePendingQueryOptions);

    // Set the plan change in progress atom to true
    if (data.type === "team") {
      jotaiStore.set(teamPlanChangePendingBaseAtom, true);
    } else {
      jotaiStore.set(userPlanChangePendingBaseAtom, true);
    }

    return {} as EmptyObject;
  },
  onError: () => {
    // Show error toast
    toast.error({
      title: "Plan Update Failed",
      description: "Failed to update plan. Please try again.",
    });
  },
});
