import { queryClient } from "../queryClient.client";
import { mutationOptions } from "../queryOptions";
import { subscriptionQueryOptions } from "../queries";
import type { EmptyObject } from "@augment-internal/ts-utils/type";

export const unscheduleCancellation = mutationOptions({
  mutationFn: async () => {
    const response = await fetch(`/api/subscription`, {
      method: "PATCH",
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || "Failed to unschedule cancellation");
    }

    return {} as EmptyObject;
  },
  onSuccess: () => {
    queryClient.invalidateQueries(subscriptionQueryOptions);
  },
});
