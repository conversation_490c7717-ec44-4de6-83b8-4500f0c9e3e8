import { queryOptions } from "../queryOptions";
import { OrbCustomerInfoSchema } from "../../schemas/orb";
import { queryFetch } from "app/utils/query-fetch";
import { jotaiStore } from "../jotaiStore.client";
import { planChangeInProgressAtom } from "../atoms";

export const subscriptionQueryOptions = queryOptions({
  queryKey: ["subscription"],
  queryFn: queryFetch("/api/subscription", OrbCustomerInfoSchema),
  enabled: () => {
    const anyPlanChangeInProgress = jotaiStore.get(planChangeInProgressAtom);
    return !anyPlanChangeInProgress;
  },
});
