import { queryOptions } from "../queryOptions";
import {
  type TeamStatusMockSchema,
  TeamStatusSchema,
} from "../../schemas/team";
import { userQueryOptions } from "./user";
import { queryClient } from "../queryClient.client";
import { queryFetch } from "app/utils/query-fetch";
import type { UserApiGETResponseSchema } from "app/schemas/user";
import { TeamPlanChangePendingSchema } from "app/schemas/team";

function makeStubTeamActive(
  userData: UserApiGETResponseSchema,
): TeamStatusMockSchema {
  return {
    status: "active",
    isMock: true,
    team: {
      id: `mock-team-${userData.email}`,
      users: [
        {
          id: `mock-user-${userData.email}`,
          email: userData.email,
          role: "ADMIN",
          joinedAt: new Date().toISOString(),
        },
      ],
      seats: 1,
      invitations: [],
    },
  };
}

export const teamQueryOptions = queryOptions({
  queryKey: ["team"],
  queryFn: async () => {
    const teamStatus = await queryFetch("/api/team", TeamStatusSchema)();

    // If status is 'none', return a mocked team instead of showing create button
    if (teamStatus.status === "none") {
      // Ensure user data is available using ensureQueryData
      const userData = await queryClient.ensureQueryData(userQueryOptions);

      return makeStubTeamActive(userData);
    }

    return teamStatus;
  },
});

export const teamPlanChangePendingQueryOptions = queryOptions({
  queryKey: ["team-plan-change-pending"],
  queryFn: queryFetch(
    "/api/team/plan-change-pending",
    TeamPlanChangePendingSchema,
  ),
});
