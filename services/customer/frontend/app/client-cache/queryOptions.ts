import {
  // eslint-disable-next-line no-restricted-imports
  queryOptions as queryOptionsWithDataTag,
  type DefinedInitialDataOptions,
  type UndefinedInitialDataOptions,
  type DefaultError,
  type QueryKey,
  type UseMutationOptions,
} from "@tanstack/react-query";
import { atomWithQuery } from "jotai-tanstack-query";
import type { AnyFunction, DeepMutable } from "@augment-internal/ts-utils/type";

export function wrapWithOnSuccess(options: any) {
  const { onSuccess, queryFn, ...queryOpts } = options;

  const wrappedQueryFn =
    onSuccess && queryFn
      ? async (...args: any[]) => {
          const result = await queryFn(...args);
          onSuccess(result);
          return result;
        }
      : queryFn;

  return withAtomWithQuery(
    queryOptionsWithDataTag({
      ...queryOpts,
      queryFn: wrappedQueryFn,
    }),
  );
}

type AtomWithQuery<
  TQueryFnData = unknown,
  TError = Error,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
> = ReturnType<typeof atomWithQuery<TQueryFnData, TError, TData, TQueryKey>>;

const withAtomCreator = <T extends AnyFunction>(atomCreator: T) => {
  return <U extends object>(queryOptions: U): U & T =>
    Object.assign(
      queryOptions,
      atomCreator(() => queryOptions),
    );
};

export const withAtomWithQuery = withAtomCreator(atomWithQuery);

type QueryOptionsWithDataTag<
  TQueryKey extends DeepMutable<QueryKey>,
  TQueryFnData,
  TError,
  TData,
> = ReturnType<
  typeof queryOptionsWithDataTag<TQueryFnData, TError, TData, TQueryKey>
>;

/**
 * Query Options Creator
 * @param options - The query options or a function that accepts props and returns the query options
 * @example
 * // Static query options
 * const userOptions = queryOptions(() => ({
 *   queryKey: ["user"],
 *   queryFn: async function fetchUser() {
 *     ...
 *   },
 * }));
 * useQuery(userOptions); // pass the options directly
 * @example
 * // Dynamic query options
 * const planOptions = queryOptions((planId: number) => ({
 *   queryKey: ["plan", planId],
 *   queryFn: async function fetchPlan() {
 *     ...
 *   },
 * }));
 * useQuery(planOptions(planId)); // pass the props to the factory function
 */
export function queryOptions<
  const TQueryKey extends DeepMutable<QueryKey>,
  TQueryFnData = unknown,
  TError = DefaultError,
  TData = TQueryFnData,
>(
  options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey> & {
    onSuccess?: (data: TData) => void;
  },
): QueryOptionsWithDataTag<TQueryKey, TQueryFnData, TError, TData> &
  AtomWithQuery<TQueryFnData, TError, TData, TQueryKey>;

export function queryOptions<
  const TQueryKey extends DeepMutable<QueryKey>,
  TQueryFnData = unknown,
  TError = DefaultError,
  TData = TQueryFnData,
>(
  options: UndefinedInitialDataOptions<
    TQueryFnData,
    TError,
    TData,
    TQueryKey
  > & {
    onSuccess?: (data: TData) => void;
  },
): QueryOptionsWithDataTag<TQueryKey, TQueryFnData, TError, TData> &
  AtomWithQuery<TQueryFnData, TError, TData, TQueryKey>;

export function queryOptions<
  TProps extends unknown[],
  const TQueryKey extends DeepMutable<QueryKey>,
  TQueryFnData = unknown,
  TError = DefaultError,
  TData = TQueryFnData,
>(
  optionsCreator: (...props: TProps) => DefinedInitialDataOptions<
    TQueryFnData,
    TError,
    TData,
    TQueryKey
  > & {
    onSuccess?: (data: TData) => void;
  },
): (
  ...props: TProps
) => QueryOptionsWithDataTag<TQueryKey, TQueryFnData, TError, TData> &
  AtomWithQuery<TQueryFnData, TError, TData, TQueryKey>;

export function queryOptions<
  TProps extends unknown[],
  const TQueryKey extends DeepMutable<QueryKey>,
  TQueryFnData = unknown,
  TError = DefaultError,
  TData = TQueryFnData,
>(
  optionsCreator: (...props: TProps) => UndefinedInitialDataOptions<
    TQueryFnData,
    TError,
    TData,
    TQueryKey
  > & {
    onSuccess?: (data: TData) => void;
  },
): (
  ...props: TProps
) => QueryOptionsWithDataTag<TQueryKey, TQueryFnData, TError, TData> &
  AtomWithQuery<TQueryFnData, TError, TData, TQueryKey>;

export function queryOptions(optionsOrCreator: any): any {
  if (typeof optionsOrCreator === "function") {
    return (...props: any[]) => wrapWithOnSuccess(optionsOrCreator(...props));
  }
  return wrapWithOnSuccess(optionsOrCreator);
}

/**
 * Mutation Options Creator
 * @param options - The mutation options or a function that accepts props and returns the mutation options
 * @example
 * // Static mutation options
 * const mutationOptions = mutationOptions({
 *   mutationFn: async (variables) => {
 *     ...
 *   },
 * });
 * useMutation(mutationOptions); // pass the options directly
 *
 * @example
 * // Dynamic mutation options
 * const mutationOptions = mutationOptions((props) => ({
 *   mutationFn: async (variables) => {
 *     ...
 *   },
 * }));
 * useMutation(mutationOptions(props)); // pass the props to the factory function
 */
export function mutationOptions<
  TData = unknown,
  TError = DefaultError,
  TVariables = void,
  TContext = unknown,
>(
  options: UseMutationOptions<TData, TError, TVariables, TContext>,
): UseMutationOptions<TData, TError, TVariables, TContext>;

export function mutationOptions<
  TData = unknown,
  TError = DefaultError,
  TVariables = void,
  TContext = void,
  TProps extends unknown[] = unknown[],
>(
  optionsCreator: (
    ...props: TProps
  ) => UseMutationOptions<TData, TError, TVariables, TContext>,
): (
  ...props: TProps
) => UseMutationOptions<TData, TError, TVariables, TContext>;

export function mutationOptions(options: any): any {
  if (typeof options === "function") {
    return (...props: any[]) => options(...props);
  }
  return options;
}
