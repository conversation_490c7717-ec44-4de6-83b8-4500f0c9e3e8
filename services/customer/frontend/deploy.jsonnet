// K8S deployment file for the customer web UI
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local orbConfigFunc = import 'services/deploy/configs/orb.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';
local requestInsightPublisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  local appName = 'customer-ui';
  local orbConfig = orbConfigFunc(env);
  local isMtlsEnabled = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local centralClientCert = certLib.createCentralClientCert(
    name='customer-ui-central-client-certificate',
    appName=appName,
    namespace=namespace,
    env=env,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );
  local requestInsightPublisher = requestInsightPublisherLib(cloud, env, namespace, appName=appName);
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix='cui', dropClusterName=false,
  );

  local stripeSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='stripe',
    version={
      PROD: '1',
      STAGING: '3',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount
  );

  // Create LaunchDarkly secret
  local launchDarklySecret = dynamicFeatureFlagsLib.createLaunchDarklySecret(
    cloud=cloud,
    env=env,
    namespace=namespace,
    appName=appName
  );

  local ingressHostname = endpoints.getCustomerUiHostname(env=env, cloud=cloud, namespace=namespace);
  local authHostname = endpoints.get_auth_hostname(env, namespace, cloud);
  local backendConfig = gcpLib.createBackendConfig(app=appName,
                                                   cloud=cloud,
                                                   namespace=namespace,
                                                   sessionAffinity=true,
                                                   healthCheck={
                                                     checkIntervalSec: 15,
                                                     port: 5000,
                                                     type: 'HTTP',
                                                     requestPath: '/health',
                                                   },
                                                   cdn=if env == 'DEV' then {
                                                     enabled: false,
                                                   } else {
                                                     enabled: true,
                                                     cachePolicy: {
                                                       includeHost: true,
                                                       includeProtocol: true,
                                                       includeQueryString: true,
                                                     },
                                                     serveWhileStale: 0,
                                                     cacheMode: 'USE_ORIGIN_HEADERS',
                                                   },
                                                   securityPolicy=if env == 'DEV' then 'ingress-web' else 'ingress-ip-throttle',
                                                   iap=false);


  local serviceAccountObjects = serviceAccount.objects + [
    requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
  ];

  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'customer-ui-svc',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        'cloud.google.com/app-protocols': std.manifestJson({ 'public-http': 'HTTP' }),
      },
    },
    spec: {
      type: 'NodePort',
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 80,
          name: 'public-http',
          targetPort: 'public-http',
        },
      ],
    },
  };
  local tokenExchangeEndpoint = endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace);
  local config = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: 'customer-ui-config',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
    },
    data: {
      PORT: '5000',
      NODE_ENV: 'production',
      IS_MTLS_ENABLED: '%s' % isMtlsEnabled,
      AUTH_SECRETS_PATH: '/secrets/auth.json',
      AUTH_LOGOUT_ENDPOINT: 'https://%s/logout' % authHostname,
      AUTH_ENDPOINT: 'https://%s/authorize' % authHostname,
      AUTH_CALLBACK_URL: 'https://%s/auth/callback' % ingressHostname,
      NAMESPACE: namespace,
      CURRENT_CLOUD: '%s' % cloud,
      POD_ENV: env,
      POD_NAME: '$(POD_NAME)',
      CENTRAL_CA_CERT: '/central-client-certs/ca.crt',
      CENTRAL_CLIENT_KEY: '/central-client-certs/tls.key',
      CENTRAL_CLIENT_CERT: '/central-client-certs/tls.crt',
      TOKEN_EXCHANGE_ENDPOINT: '%s' % tokenExchangeEndpoint,
      // Note that this may be formatted to end up with a global or local
      // address, depending on which cloud the namespace is in.
      SLACK_BOT_PROCESSOR_ENDPOINT_TEMPLATE: 'slack-bot-processor%s:50051',
      SHARE_ENDPOINT_TEMPLATE: 'share-svc.%s:50051',
      // Note that this may be formatted to end up with a global or local
      // address, depending on which cloud the namespace is in.
      GITHUB_PROCESSOR_ENDPOINT_TEMPLATE: 'github-processor%s:50051',
      // Note that this may be formatted to end up with a global or local
      // address, depending on which cloud the namespace is in.
      GLEAN_ENDPOINT_TEMPLATE: 'glean%s:50051',
      NOTION_ENDPOINT_TEMPLATE: 'notion%s:50051',
      ATLASSIAN_ENDPOINT_TEMPLATE: 'atlassian%s:50051',
      LINEAR_ENDPOINT_TEMPLATE: 'linear%s:50051',
      SUPABASE_ENDPOINT_TEMPLATE: 'supabase%s:50051',
      RI_ANALYTICS_ENDPOINT_TEMPLATE: if env == 'DEV' then 'request-insight-analytics-svc.%s:50051' else 'request-insight-analytics.%s.t.%s:50051',
      TENANT_WATCHER_ENDPOINT: '%s' % endpoints.getTenantWatcherGrpcUrl(env=env, cloud=cloud, namespace=namespace),
      AUTH_CENTRAL_ENDPOINT: '%s:50051' % endpoints.getAuthCentralGrpcUrl(env=env, cloud=cloud, namespace=namespace),
      CLOUD_DOMAIN_SUFFIXES: std.manifestJson({
        [c]: cloudInfo[c].internalDomainSuffix
        for c in std.objectFields(cloudInfo)
        if cloudInfo.getLeadClusterForCluster(c) == cloud
      }),
      REQUEST_INSIGHT_PUBLISHER_CONFIG_FILE: requestInsightPublisher.configFilePath,
      STRIPE_SECRET_PATH: stripeSecret.filePath,
      FEATURE_FLAGS_SDK_KEY: '',  // Empty string as default, will be overridden by the secret
      FEATURE_FLAGS_SDK_KEY_PATH: launchDarklySecret.secretsFilePath,
      DYNAMIC_FEATURE_FLAGS_ENDPOINT: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else '',
      ORB_PLANS_CONFIG: std.manifestJson(orbConfig.plans),
      ALLOW_CLIENT_SOURCEMAPS: if env == 'DEV' then 'true' else '',
    },
  };
  local container =
    {
      name: 'customer-ui',
      target: {
        name: '//services/customer/frontend:image',
        dst: 'customer-ui',
      },
      ports: [
        {
          containerPort: 5000,
          name: 'public-http',
        },
      ],
      env: launchDarklySecret.env + [
        {
          name: 'POD_NAME',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.name',
            },
          },
        },
      ],
      volumeMounts: [
        {
          name: 'secrets',
          mountPath: '/secrets',
          readOnly: true,
        },
        centralClientCert.volumeMountDef,
        requestInsightPublisher.volumeMountDef,
        stripeSecret.volumeMountDef,
        launchDarklySecret.volumeMountDef,
      ],
      envFrom: [
        { configMapRef: { name: 'customer-ui-config' } },
      ],
      resources: {
        limits: {
          cpu: 1,
          memory: '1Gi',
        },
      },
      readinessProbe: {
        httpGet: {
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 5,
        periodSeconds: 10,
      },
      livenessProbe: {
        httpGet: {
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 15,
        periodSeconds: 20,
      },
    };
  local pod =
    {
      serviceAccountName: serviceAccount.name,
      tolerations: tolerations,
      affinity: affinity,
      priorityClassName: cloudInfo.envToPriorityClass(env),
      containers: [
        container,
      ],
      volumes: [
        centralClientCert.podVolumeDef,
        requestInsightPublisher.podVolumeDef,
        stripeSecret.podVolumeDef,
        launchDarklySecret.podVolumeDef,
        {
          name: 'secrets',
          secret: {
            secretName: 'customer-ui-secrets',  // pragma: allowlist secret
            optional: false,
          },
        },
      ],
    };
  local frontendConfig = gcpLib.createFrontendConfig(app=appName, cloud=cloud, namespace=namespace);
  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: appName,
        },
        name: 'customer-ui-ingress',
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: 'customer-ssl-cert',  // pragma: allowlist secret
            hosts: [ingressHostname],
          },
        ],
        rules: [
          {
            host: ingressHostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: 'customer-ui-svc',
                      port: {
                        number: 80,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'customer-ui',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };

  local authJsonSecret = {
    GCP_US_CENTRAL1_DEV: {
      DEV: 'AgBivssjlV1nK6PkyVuQSPCTVpHLgrVBzAB9CJOieucFCnhpk8YMZKEFww5RtKGm1gq1ZQkwsWS9i1FsDzjeSsp3H/Y2HqitSPZpjB2vPKg2UPz2prQ7q3nFfhM+gGmEdHFfeeqpOu5CXK5eNaQ1CmARyxJ5a5nfL5mN7j10zZ31q6seZtBonuB8M9cGCgSMZSyUSXjKHmmuzGGxzILjo3GiBIaZk9iNKfDfE7eOL7ky6GEsxB1cbnM8sPrgGzWppg/2MtYJSnwrJBYLlycPAnjexBHwqEZp6XzGjz9Jocj7GuEPkvRFNNFHv3PnpEVEl+lXaXTBf/ExdcbnMO8FdMphoiQd1tJTugYu2Cc+AOR0rPfXpVK/+jdwaRk1b8Frz5cnmzo7/I0gxBvLZLN4zNQKDZsZgcvEPhQz8VHBKBVNtXXZzFVjzEmw+kX7JLhm2Xp8bIiMawHe2Yb2/8WcMvYwAiAxxkGp6zGzB22xJ3nvWZ0LVEFJR5oWsRqCPOI27chCIwQEVU7uLBZMltTrQ0Zis2xv9E1jdbByIzw9hZwqqhbWX7jH1pimqLxU2X8GRoCtg7KzY1MlvRb5UoTb42pZA16Ub+TW70iFHMjZucBPxmWR1r6iJFp6BfYYRv+U7RqwXt5vpvhFxUP6TZ4tTTMMTIa9f32dA8CaqkW9t2bP2SB4S1QPBBBdfeihw5HN2Yclu4ShoVJNxlFK98p42wWhUWBPS53cKcs/M4u3oR7IFUYEi0dBMZRaE8DwIvIhGcPtKFn6RVZbdjiIN7QuTV+tVccMLtTwe9krV0HcVLZW+EQ/tdUk5q/SEMog52Q=',
    },
    GCP_US_CENTRAL1_PROD: {
      PROD: 'AgCNrSftsfp/rQYvgyJs0/nnCkxKmJrVXLJ8lpCfgMKGODmHCu0eMD7/CKAsee+JRpyaCAklQRGr/6vbTwrY8nBKWFh5ejTElYO4Xls4vvvwJa6oTYfWaIgDn3syuvminsrs67cdHVKxbqKwUScs4MESg6KsVX7+czHPhqtvU5dfejalLz1UnRZgs7AMxZJA2ayTruELWGdtI76snrG0eoFcD9eo3/6mUWkL1gskg50Kw3FGHl3MebQU95OI10pRJM62trZEABa/7d4Yp6bIqfmn0pqtWVIVopjE6SXz24zOcT8zHyW8mTfXamUk1q/HQ4VSMmGQQc8h3Bu/vwr8yH65PP9Kcnu6a88rvzpeDqDN9QeoQ4ZNBvNktTUKbXfvjLK23gHJfd4JXPgWKeX9yGCjMfZK4fuRGlgyHY8Ztz8O9bKTXVV1u/yr+ni0Bj8HoT4Tr9xRpC8bQyZrya0JgpxNFQAiMt+Ri9/quVSbovNSf07MeCFDrucXaG+rs+lkQjnf12j2/nIdujIbFO3bo4n5ChtH87tPlIQswBJ1lWSXbHcFOigILsl3HTEcU3RaUL1Mt/iwy0o7l+Coah+gkooIQqyUjDZgxoi+3zb6A3HTnDYOK41/Fm7Dw9/RqnwcwADyBT5AzO0ETWjwmDi8Ef4Y0FDr03mcNx5Ra58OHCI4BzBW/HNOpYlQvvt5SfDl4cXXbiNYyYs7NrvwqiIQ2o/S+oH5eWRr3jwYeKfmNqPaVluNRoS6K0Bd8e+4BGzaRcBK1KaCe/FXCjjqydxFl+JtNGDMNVDfoOEjh+J8crHxFGsSGAXq8UtemF3Bi6XLdTx8UU4C92YP6PGnd/g42f8VOeM=',
      STAGING: 'AgDQehgNy8lN345mc+i0iSDqDBbXRA1u+6FU9YRSPqUd5VmdJerOhzO+CYla/3Le18xS2PqrN5CzqK9UwtWUTniWkWiDeDDLT22vqAS2zyXXUIVhs7IDuX22UBnF6o4rXpzDvfQHQ8XUBimUCZGZDc/xiQtV6jWPblZJo3XqKxiUqtkO7Xr9aAH1kj4bxkwE1MfISgZhyI9vEs5bF1cjnBKQT9PyejySLURhRXevzuJADPYBn68RJy8iRtkHqVTn4oRtcZrmUcQLIS9toGsfUVRCktKNMerIPMqvydRh2yKblPx0fjDVYUsUVzJOUOra9axRdQ5XDI70ADSSAJ7D+Twdki6tsnzva+u3zaY61Ag7inUtLGGMR7hRT+e1Hx+wwDoW8XOv7pFZ8/7afxpsGI3zhesxDN+R1Lx+p7SgnE2ZplnorfVB74BP74gDKE/euX+IOsxnK2wyTXshMTcyGbGtQhwU0FieHroK63N1BoSIdpPkP1HZ40xtT/72c1WMyeAws/Msw9D2+Yl7H4Gq0bNejX3SyhJe2M+KPs8Kj385HheLe7XXbLnm6v6/dHW17Iii3E1DQPp9EasLEaeNFZP8o6RSR6OL52F6ysIqyOzdSEu515vUs/M9V9w9qfgBcWrSb/cIsJbyH2QkXWFNsl354NbVkr1W4mo+jjO1h+LpmSAQGzcDSNPO7CiCnWwO7olb5cRMuP9jq863+Vy/imJ8h054+JsLjoxTOVoMQCkk+LxowH51WYunm2XxUFgnkrdShdlvKi0jcgnVLfJYU48mxY/796BnguDYdkwNVJrUjyVlGFYr6rSs5WkerMFIt0Ab/PJs4RMDdS/1vPvc56SGPuM=',
    },
  }[cloud][env];
  local secrets = {
    kind: 'SealedSecret',
    apiVersion: 'bitnami.com/v1alpha1',
    metadata: {
      name: 'customer-ui-secrets',
      namespace: namespace,
      labels: {
        app: appName,
      },
      creationTimestamp: null,
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
    },
    spec: {
      template: {
        metadata: {
          name: 'customer-ui-secrets',
          namespace: namespace,
          labels: {
            app: appName,
          },
          creationTimestamp: null,
          annotations: {
            'sealedsecrets.bitnami.com/cluster-wide': 'true',
          },
        },
      },
      encryptedData: {
        'auth.json': authJsonSecret,
      },
    },
  };
  lib.flatten([
    config,
    secrets,
    service,
    deployment,
    ingressObjects,
    centralClientCert.objects,
    serviceAccountObjects,
    stripeSecret.objects,
    launchDarklySecret.k8s_objects,
  ])
