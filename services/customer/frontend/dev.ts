#!/usr/bin/env tsx
/**
 * This script sets up the env for running the customer app in dev mode.
 * It reads from the deployed customer_ui pod to determine what ports need to be forwarded.
 * It also reads from the forwards file to determine any additional ports that need to be forwarded.
 * You can forward additional ports by adding them to "forwards" in the current directory.
 * This format is the environmental variable=service:port. If the port is not specified, it will use the port from the service.
 * If the service does not exist in the namespace, it will warn you and skip it.
 *
 * Generally you will want to execute via `pnpm dev` or via bazel `npx ibazel run //services/customer/frontend:dev`.
 *
 *
 */
import * as k8s from "@kubernetes/client-node";
import * as fs from "fs/promises";
import * as os from "os";
import * as stream from "node:stream";
import * as path from "path";
import { logger as _logger } from "@augment-internal/logging";
import { spawn } from "node:child_process";
import { ENV_VARS, ENDPOINTS, CONFIG_FILES } from "app/.server/config-env";
import * as net from "net";

/**
 * if you need to fixup vars, and don't have any better ideas, put them here.
 * So at least your shame can be easily found.
 */
const FIXUP = {
  DYNAMIC_FEATURE_FLAGS_ENDPOINT(val: string): string {
    if (!val.startsWith("http")) {
      return `http://${val}`;
    }
    return val;
  },
} as const;

const levels = ["warn", "info", "debug", "error"] as const;

const format = (message: string, obj?: any) =>
  obj == null
    ? message
    : message.replace(/{([^}]+)}/g, (match, key) => obj[key] ?? match);

const logger = levels.reduce(
  (acc, level) => {
    acc[level] = (message: string, obj?: unknown) =>
      _logger[level](format(message, obj));
    return acc;
  },
  {} as {
    [k in (typeof levels)[number]]: (
      ...args: Parameters<typeof format>
    ) => void;
  },
);
/**
 * Find an open port and return it... It is possible
 * it is no longer availble by the time this function returns,
 * but unlikely.
 */
const portCache = new Set<number>();
async function availablePort(start = 50000, retry = 1000) {
  function check(port: number) {
    return new Promise<boolean>((resolve, reject) => {
      const server = net.createServer();
      server.listen(port, "127.0.0.1", () => {
        server.close((err) => {
          if (err) {
            logger.warn(`error closing server ${err}`);
            return reject(err);
          }
          resolve(true);
        });
      });
      server.on("error", () => {
        resolve(false);
      });
    });
  }
  for (let i = 0; i < retry; i++) {
    if (portCache.size !== portCache.add(start + i).size) {
      if (await check(start + i)) {
        return start + i;
      }
    }
  }
  throw new Error(
    `Failed to find available port, checking ${start}-${start + retry}`,
  );
}

async function importUserJson(): Promise<string> {
  try {
    return (await import(`${os.homedir()}/.augment/user.json`)).name;
  } catch (err) {
    logger.error("Error reading user.json:", err);
    throw err;
  }
}

// Configuration
const KUBE_CONTEXT =
  process.env.KUBE_CONTEXT ||
  "gke_system-services-dev_us-central1_us-central1-dev";
const KUBE_NAMESPACE =
  process.env.KUBE_NAMESPACE || "dev-" + (await importUserJson());

const PORT = 5200;
const HMR_PORT = process.env.HMR_PORT || 5201;

// Kubernetes client setup
const kc = new k8s.KubeConfig();
kc.loadFromDefault();

// Set the current context if specified
if (KUBE_CONTEXT) {
  const contexts = kc.getContexts();
  const contextExists = contexts.some((ctx) => ctx.name === KUBE_CONTEXT);
  if (contextExists) {
    kc.setCurrentContext(KUBE_CONTEXT);
  } else {
    logger.warn(`Context {ctx} not found in kubeconfig, using default`, {
      ctx: KUBE_CONTEXT,
    });
    throw new Error(`Context ${KUBE_CONTEXT} not found in kubeconfig`);
  }
}

/**
 * A little performance helper, cause calling into K8's can
 * be slow.
 * @param fn
 * @returns
 */

function memo<T extends (...args: any[]) => any>(fn: T): T {
  const envCache = new Map<string, ReturnType<T>>();
  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = JSON.stringify(args);
    if (envCache.has(key)) {
      return envCache.get(key)!;
    }
    const ret = fn(...args);
    envCache.set(key, ret);
    return ret;
  }) as any;
}

const k8sApi = kc.makeApiClient(k8s.CoreV1Api);

const ENV_VAR_MAP = new Map<string, string>();

// Helper functions
const getPod = memo(async (serviceName: string): Promise<string> => {
  const response = await k8sApi.listNamespacedPod({
    namespace: KUBE_NAMESPACE,
    labelSelector: `app=${serviceName}`,
  });
  const ret = response.items[0]?.metadata?.name;
  if (!ret) {
    throw new Error(`No pod found with label app=${serviceName}`);
  }
  return ret;
});

class Writable extends stream.Writable {
  public output = "";
  _write(chunk: any, encoding: string, callback: () => void) {
    this.output += chunk.toString();
    callback();
  }

  toString() {
    return this.output;
  }
}

async function podExec(
  container: string,
  ...command: string[]
): Promise<string> {
  const pod = await getPod(container);

  const exec = new k8s.Exec(kc);
  const stdout = new Writable();
  const stderr = new Writable();

  try {
    return await new Promise<string>((resolve, reject) => {
      exec.exec(
        KUBE_NAMESPACE,
        pod,
        container,
        command,
        stdout,
        stderr,
        process.stdin,
        false,
        (status) => {
          if (status.status === "Success") {
            resolve(stdout.toString());
          } else {
            logger.warn("Error executing command '{command}': {message}", {
              message: status.message ?? stderr.toString(),
              command: command.join(" "),
            });
            reject(status.message ?? stderr.toString());
          }
          return true;
        },
      );
    });
  } catch (err) {
    console.trace(err);
    logger.error("Error executing command:", err);
    throw err;
  }
}

async function cpFromPod(
  appLabel: string,
  filePath: string,
  hint: string,
): Promise<string> {
  const tempFileDir = await fs.mkdtemp(
    path.join(os.tmpdir(), toLocalPath(hint)),
  );

  const tempFile = path.join(tempFileDir, path.basename(filePath));
  const pod = await getPod(appLabel);
  try {
    const resp = await podExec(appLabel, "cat", filePath);
    await fs.writeFile(tempFile, resp);
  } catch (err) {
    logger.error(
      "Error copying file from pod {pod}:${filePath} to ${tempFile}: {message}",
      {
        pod,
        filePath,
        tempFile,
        message: err,
      },
    );
    //    throw err;
  }
  closeOnExit.add({
    close() {
      fs.rm(tempFileDir, { recursive: true, force: true });
    },
  });
  //  return await podExec(appLabel, "cat", filePath);
  return tempFile;
}

const podEnvVal = memo(
  async (appLabel: string, envVar: string): Promise<string> => {
    try {
      return (await podExec(appLabel, "printenv", envVar)).trim();
    } catch (e) {
      logger.warn(
        "reading env var {envVar} failed from {appLabel} likely the variable does not exist on the pod.",
        {
          envVar,
          appLabel,
        },
      );
      return "";
    }
  },
);

async function printPodVar(appLabel: string, envVar: string): Promise<void> {
  const value = await podEnvVal(appLabel, envVar);
  decl(envVar, value);
}

function toLocalPath(varName: string): string {
  return varName
    .replace(/\r/g, "")
    .replace(/\n/g, "")
    .replace(/^[/]?/, "ignore_")
    .replace(/[!@#$%^&*(){:}]/g, "_")
    .replace(/_config/g, "")
    .replace(/[.]json/g, "")
    .replace(/--/g, "_")
    .replace(/__/g, "_")
    .toLowerCase();
}

const closeOnExit = new Set<{ close(): unknown }>();

process.on("exit", () => {
  logger.info("closing services {size}", closeOnExit);
  for (const server of closeOnExit) {
    try {
      server.close();
    } catch (e) {
      logger.error("Error closing server", e);
    }
  }
});

/**
 * Ideally this would use the k8s api to port forward but it's not working.
 * So we use kubectl instead.
 *
 * Please look at using:
 *    https://github.com/kubernetes-client/javascript/blob/master/examples/typescript/port-forward/port-forward.ts
 *
 * I tried and failed.
 *
 * @param service
 * @returns
 */
async function portFwd(service: string) {
  const name = service.split(":")[0];
  const res = await k8sApi.readNamespacedService({
    name,
    namespace: KUBE_NAMESPACE,
  });
  const port = res.spec?.ports?.[0]?.port;
  if (!port) {
    throw new Error(`No port found for service ${service}`);
  }
  //  return `${res.spec?.clusterIP}:${port}`;
  //  todo: check if port is available
  const localPort = await availablePort();
  const resp = spawn("kubectl", [
    "port-forward",
    "--context",
    KUBE_CONTEXT,
    "--namespace",
    KUBE_NAMESPACE,
    `service/${name}`,
    `${localPort}:${port}`,
  ]);
  logger.info("port forwarding {service} to localhost:{localPort}", {
    service,
    localPort,
  });
  closeOnExit.add({
    close() {
      resp.kill();
    },
  });
  return `localhost:${localPort}`;
}

/**
 * Declares an environment variable. Also removes new lines,
 * may be unnessary now that we don't write to disk.
 *
 * To override a value, set the AU_ version of the env var.
 *
 * @param key {string} environmental key.
 * @param value {string} value to set.
 */
function decl(key: string, value: string) {
  const val = value.replace(/\r/g, "").replace(/\n/g, "");
  ENV_VAR_MAP.set(
    key,
    process.env[`AU_${key}`] ??
      (key in FIXUP ? FIXUP[key as keyof typeof FIXUP](val) : val),
  );
}

async function serviceFromTemplate(template: string): Promise<string> {
  const service = await podEnvVal("customer-ui", template);
  return service.replace(/^https?:\/\//, "").split(/[:.%]/)[0];
}

// Main functions
async function setupEnvFiles(): Promise<void> {
  for (const fvar of new Set(CONFIG_FILES)) {
    const podPath = await podEnvVal("customer-ui", fvar);
    const tempFile = await cpFromPod("customer-ui", podPath, fvar);
    decl(fvar, tempFile);
  }
}

async function setupEnvVars(): Promise<void> {
  for (const evar of new Set(ENV_VARS)) {
    await printPodVar("customer-ui", evar);
  }

  decl("PORT", PORT.toString());
  decl("NODE_ENV", "development");
  decl("HMR_PORT", HMR_PORT.toString());
  decl("AUTH_CALLBACK_URL", `http://localhost:${PORT}/auth/callback`);
}
async function readIfExists(file: string) {
  try {
    return fs.readFile(file, "utf8");
  } catch {
    return;
  }
}
async function forwardMap(file: string) {
  const forwards =
    (await readIfExists(file))
      ?.split("\n")
      .map((line) => line.split("#")[0].trim())
      .filter(Boolean) ?? [];
  return Object.fromEntries(
    forwards.map(
      (line) => /^([^=]*)=?(.*)$/.exec(line)!.slice(1, 3) as [string, string],
    ),
  );
}
/**
 * Try to setup forwards, if the the forwards are in our namespace,
 * otherwise warn and skip them.
 */

async function setupForwards(): Promise<void> {
  const forwards = await forwardMap("./forwards");
  for (const template of new Set([...Object.keys(forwards), ...ENDPOINTS])) {
    const service = forwards[template] || (await serviceFromTemplate(template));

    try {
      await k8sApi.readNamespacedService({
        name: service,
        namespace: KUBE_NAMESPACE,
      });
    } catch (e) {
      logger.debug(
        `Service '${service}' does not exist in namespace ${template} '${KUBE_NAMESPACE}'`,
      );
      await printPodVar("customer-ui", template);
      continue;
    }
    try {
      const localPort = await portFwd(service);
      decl(template, localPort);
    } catch (e) {
      throw new Error(`Error port forwarding ${service} for ${template}`, {
        cause: e,
      });
    }
  }
}

function sshInfo(port: number) {
  logger.info("VSCode may automatically setup this up for you");
  logger.info("$ ssh -L {port}:localhost:{port} {host}", {
    port,
    host: `${KUBE_NAMESPACE}-1`,
  });
}

async function setupMTLS() {
  decl("IS_MTLS_ENABLED", "true");
  decl("REJECT_UNAUTHORIZED", "false");
  const VAR_FILES = {
    CENTRAL_CLIENT_CERT_PATH: "/central-client-certs/tls.crt",
    CENTRAL_CLIENT_KEY_PATH: "/central-client-certs/tls.key",
    CENTRAL_CA_CERT_PATH: "/central-client-certs/ca.crt",
  };
  for (const [tvar, file] of Object.entries(VAR_FILES)) {
    const tempFile = await cpFromPod(
      "customer-ui",
      file,
      tvar.toLocaleLowerCase(),
    );
    logger.debug("wrote value to temp file {tvar}={tempFile} from {file}", {
      tvar,
      file,
      tempFile,
    });
    decl(tvar, tempFile);
  }
}
const isMtlsEnabled =
  (await podEnvVal("customer-ui", "IS_MTLS_ENABLED")) === "true";
async function setupEnv(): Promise<void> {
  logger.info("Setting up the environment, might take a minute...");
  logger.info("MTLS is {message}", {
    message: isMtlsEnabled ? "enabled" : "disabled",
  });

  const customerUi = await getPod("customer-ui");

  // Just some helpful debug info
  logger.info(`# generated: ${new Date().toISOString()}`);
  logger.info(`# namespace: ${KUBE_NAMESPACE}`);
  logger.info(`# context: ${KUBE_CONTEXT}`);
  logger.info(`# customer-ui: ${customerUi}`);

  logger.info("setting up environmental variables");
  await setupEnvVars();

  logger.info("setting up files");
  await setupEnvFiles();

  logger.info("setting up port forwards");
  await setupForwards();

  if (isMtlsEnabled) {
    logger.info("setting up MTLS");
    //The port forwarding makes this necessary, otherwise the cert validation fails.
    await setupMTLS();
  }

  if (os.hostname().startsWith(KUBE_NAMESPACE)) {
    logger.info(
      "to enable HMR create a tunnel from your dev instance to your local machine for port {port}",
      { port: HMR_PORT },
    );
    sshInfo(Number(HMR_PORT));
    sshInfo(PORT);
  }
}

// // Main execution
async function main() {
  await setupEnv();

  Object.assign(process.env, Object.fromEntries(ENV_VAR_MAP));
  await import("./server");
}

main().catch((e) => {
  console.trace(e);
  process.exit(1);
});
