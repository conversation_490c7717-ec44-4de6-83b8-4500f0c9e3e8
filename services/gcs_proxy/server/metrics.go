package server

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	// MockKMSUsage is incremented when the gcs_proxy starts with mock KMS.
	// This counter exists to trigger alerts if this happens in production.
	MockKMSUsage = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "au_gcs_proxy_mock_kms_usage",
			Help: "Incremented when gcs_proxy starts with mock KMS",
		},
	)

	// Track chunk sizes for read and write operations
	// Also provides visibility into how much data is encrypted
	// operation = "read" or "write"
	ChunkSizeBytes = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "au_gcs_proxy_chunk_size_bytes",
			Help:    "Histogram of chunk sizes in bytes",
			Buckets: append([]float64{0}, prometheus.ExponentialBuckets(256, 4, 11)...), // 0, 256B to 256MB
		},
		[]string{"tenant_name", "operation", "encrypted"},
	)

	// Track latency-add for cryptographic operations
	// operation = "open" or "seal"
	CryptoLatency = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "au_gcs_proxy_crypto_latency",
			Help: "Histogram of the latency of cryptographic operations",
			Buckets: []float64{
				0.0001, 0.0002, 0.0005, // 0.1ms, 0.2ms, 0.5ms
				0.001, 0.002, 0.005, // 1ms, 2ms, 5ms
				0.01, 0.02, 0.05, // 10ms, 20ms, 50ms
				0.1, 0.2, 0.5, // 100ms, 200ms, 500ms
				1.0, 2.0, 5.0, // 1s, 2s, 5s
			},
		},
		[]string{"tenant_name", "operation", "status_code"},
	)
)
