local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  // Alert when the GCS Proxy is using mock KMS. This would be a security incident in production.
  local mockKMSSpec = {
    displayName: 'GCS Proxy using Mock <PERSON>',
    conditionPrometheusQueryLanguage: {
      duration: '60s',
      evaluationInterval: '60s',
      labels: { severity: 'error' },
      query: |||
        au_gcs_proxy_mock_kms_usage > 0
      |||,
    },
  };

  // Alert if there are key access errors
  // Warning severity only since customer may have intentionally revoked key.
  local keyAccessErrorsSpec = {
    displayName: 'GCS Proxy Key Access Errors',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        max by (namespace)(
          increase(au_gcs_proxy_crypto_latency_count{status_code="key_access_denied"}[15m])
        ) > 0
      |||,
    },
  };

  // Alert if there are encryption or decryption errors
  // Error severity since they indicate a system issue.
  local encryptionErrorsSpec = {
    displayName: 'GCS Proxy Encryption Errors',
    conditionPrometheusQueryLanguage: {
      duration: '300s',  // Increased from 60s to 300s for consistency with key access errors
      evaluationInterval: '60s',
      labels: { severity: 'error' },
      query: |||
        max by (namespace, table_name, status_code)(
          increase(au_gcs_proxy_crypto_latency_count{status_code=~"encryption_failed|decryption_failed|invalid_format|cipher_fail|unknown_error"}[15m])
        ) > 0
      |||,
    },
  };

  // Alert if there are Google KMS access errors
  // Error severity since they indicate infrastructure or permission issues
  local googleKMSErrorsSpec = {
    displayName: 'GCS Proxy Google KMS Access Errors',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'error' },
      query: |||
        max by (status_code)(
          increase(au_gcs_proxy_crypto_latency_count{status_code=~"google_kms_error|permission_denied"}[15m])
        ) > 0
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      mockKMSSpec,
      'gcs-proxy-mock-kms',
      'GCS Proxy is using mock KMS which is insecure for production use',
    ),
    monitoringLib.alertPolicy(
      cloud,
      keyAccessErrorsSpec,
      'gcs-proxy-key-access-errors',
      'GCS Proxy is experiencing key access errors, which may indicate intentional key revocation',
    ),
    monitoringLib.alertPolicy(
      cloud,
      encryptionErrorsSpec,
      'gcs-proxy-encryption-errors',
      'GCS Proxy is experiencing encryption or decryption errors, which indicates a system issue',
    ),
    monitoringLib.alertPolicy(
      cloud,
      googleKMSErrorsSpec,
      'gcs-proxy-google-kms-errors',
      'GCS Proxy is experiencing Google KMS access errors, which indicates infrastructure or permission issues',
    ),
  ]
