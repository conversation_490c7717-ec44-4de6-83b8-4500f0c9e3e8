package server

import (
	"context"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	tenantcrypto "github.com/augmentcode/augment/services/lib/encryption"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"google.golang.org/api/iterator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"cloud.google.com/go/storage"
	"github.com/googleapis/gax-go/v2"

	"github.com/augmentcode/augment/services/lib/grpc/auth"

	proto "github.com/augmentcode/augment/services/gcs_proxy/proto"
	token_exchange "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/rs/zerolog/log"
)

type GcsProxyConfig struct {
	// idea right now is that the service will have access to both buckets, even in non central
	// namespaces. keep the service stateless and generic.
	// we can revisit this as the service evolves
	RIEnterpriseBucket    string
	RINonEnterpriseBucket string
	ProjectId             string

	// Retry configurations, per bucket operation.
	MaxAttempts       int
	InitialBackoffMs  int
	MaxBackoffMs      int
	BackoffMultiplier float64

	// CMK support configuration
	KMSServiceAccountEmail string
	UseMockKMS             bool
}

type bucketHandle interface {
	BucketName() string
	Object(name string) *storage.ObjectHandle
	Objects(ctx context.Context, q *storage.Query) *storage.ObjectIterator
}

type gcsObjects struct {
	// the docs make it very clear clients should be cached and reused. Bucket handles are probably
	// not necessary, and creating them doesn't actually do network ops, but no harm in caching them
	gcsClient             *storage.Client
	riEnterpriseBucket    bucketHandle
	riNonEnterpriseBucket bucketHandle
}

type GcsProxyService struct {
	// proto.GcsProxyServer
	// todo: feature flags, etc
	ctx          context.Context
	config       GcsProxyConfig
	tenantCache  tenantwatcherclient.TenantCache
	tenantCrypto tenantcrypto.TenantCrypto
	gcsObjects   gcsObjects
}

func New(ctx context.Context, config GcsProxyConfig, tenantWatcherClient tenantwatcherclient.TenantWatcherClient) (*GcsProxyService, error) {
	// don't want to start the service if we don't have the appropriate GCS access
	// todo: unclear if there are reasons we'd want to, say, retry this
	log.Info().Msg("Creating GCS client")
	gcsClient, err := storage.NewClient(ctx)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating GCS client")
	}

	// Set retry behavior for all bucket operations.
	log.Info().Msgf("Setting GCS client retry behavior: maxAttempts=%d, initialBackoffMs=%d, maxBackoffMs=%d, backoffMultiplier=%f",
		config.MaxAttempts, config.InitialBackoffMs, config.MaxBackoffMs, config.BackoffMultiplier)
	if config.MaxAttempts > 1 {
		gcsClient.SetRetry(
			storage.WithMaxAttempts(config.MaxAttempts),
			storage.WithBackoff(gax.Backoff{
				Initial:    time.Millisecond * time.Duration(config.InitialBackoffMs),
				Max:        time.Millisecond * time.Duration(config.MaxBackoffMs),
				Multiplier: config.BackoffMultiplier,
			}),
		)
	}
	log.Info().Msg("Created GCS client")

	log.Info().Msgf("Creating RI enterprise bucket handle: %s", config.RIEnterpriseBucket)
	riEnterpriseBucketHandle := gcsClient.Bucket(config.RIEnterpriseBucket)
	log.Info().Msgf("Creating RI non-enterprise bucket handle: %s", config.RINonEnterpriseBucket)
	riNonEnterpriseBucketHandle := gcsClient.Bucket(config.RINonEnterpriseBucket)

	// TODO: creating the bucket handles doesn't perform network operations, so we should
	// maybe add some access validation here

	tenantCache := tenantwatcherclient.NewTenantCache(tenantWatcherClient, "")

	var kmsClient tenantcrypto.KMSClient
	if config.UseMockKMS {
		log.Info().Msg("Using mock KMS client")
		MockKMSUsage.Inc()
		kmsClient = tenantcrypto.NewMockKMSClient()
	} else {
		log.Info().Msg("Using Google KMS client")
		kmsServiceAccountEmail := config.KMSServiceAccountEmail
		if kmsServiceAccountEmail == "" {
			return nil, fmt.Errorf("no KMS service account specified for KMS operations")
		}
		log.Info().Msgf("Using KMS service account %s", kmsServiceAccountEmail)
		kmsClient, err = tenantcrypto.NewGoogleKMSClientWithServiceAccount(ctx, kmsServiceAccountEmail)
		if err != nil {
			return nil, fmt.Errorf("failed to initialize Google KMS client: %w", err)
		}
	}

	tenantCrypto, err := tenantcrypto.New(ctx, kmsClient)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize tenant data encryption: %w", err)
	}
	// Preload tenant keys for encrypted tenants
	tenants, err := tenantCache.GetAllTenants()
	if err != nil {
		return nil, fmt.Errorf("failed get tenants for key preload: %w", err)
	}
	err = tenantCrypto.LoadTenantKeys(ctx, tenants)
	if err != nil {
		log.Warn().Err(err).Msg("Failure preloading tenant keys")
	}

	return &GcsProxyService{
		ctx:    ctx,
		config: config,
		// This should just auto fill in the shard namespace (or central)
		// There does not seem to be a way to watch all namespaces outside of the central tenant, which is probably
		// fine
		tenantCache:  tenantCache,
		tenantCrypto: tenantCrypto,
		gcsObjects: gcsObjects{
			gcsClient:             gcsClient,
			riEnterpriseBucket:    riEnterpriseBucketHandle,
			riNonEnterpriseBucket: riNonEnterpriseBucketHandle,
		},
	}, nil
}

// Closes any appropriate clients
func (p *GcsProxyService) Close() {
	if p.tenantCrypto != nil {
		p.tenantCrypto.Close()
	}
	if p.tenantCache != nil {
		p.tenantCache.Close()
	}

	err := p.gcsObjects.gcsClient.Close()
	if err != nil {
		log.Error().Err(err).Msg("Error closing GCS client")
	} else {
		log.Info().Msg("Closed GCS client")
	}
}

func (p *GcsProxyService) getRIBucketHandle(tenant *tenantproto.Tenant) (bucketHandle, error) {
	tier := tenant.Tier
	if tenant.Config != nil && tenant.Config.Configs != nil {
		tierOverride, ok := tenant.Config.Configs["override_data_export_tier"]
		if ok {
			switch tierOverride {
			case "\"COMMUNITY\"":
				tier = tenantproto.TenantTier_COMMUNITY
			case "\"PROFESSIONAL\"":
				tier = tenantproto.TenantTier_PROFESSIONAL
			case "\"ENTERPRISE\"":
				tier = tenantproto.TenantTier_ENTERPRISE
			}
		}
	}

	switch tier {
	case tenantproto.TenantTier_ENTERPRISE, tenantproto.TenantTier_PROFESSIONAL:
		return p.gcsObjects.riEnterpriseBucket, nil
	case tenantproto.TenantTier_COMMUNITY:
		return p.gcsObjects.riNonEnterpriseBucket, nil
	default:
		return nil, fmt.Errorf("unknown tenant tier: %s", tier.String())
	}
}

// validate access given the context and tenantID; return the appropriate bucket handle or error
func (p *GcsProxyService) validateTenantGetBucketHandle(ctx context.Context, tenantID string, scope token_exchange.Scope) (bucketHandle, *tenantproto.Tenant) {
	ctxClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Error().Msg("Failed to get auth claims from context")
		return nil, nil
	}

	if !ctxClaims.IsTenantAllowed(tenantID) {
		log.Error().Msgf("Auth claims give permission for tenant %s, but request has tenant %s", ctxClaims.TenantID, tenantID)
		return nil, nil
	}

	// Either tenant id claim matches the request tenant id (in which case we are good permissions wise, but
	// we could still be in the wrong namespace -- so still need to call GetTenant)
	// or there is no tenant id claim (in which case we need to check the namespace via GetTenant)
	tenant, err := p.tenantCache.GetTenant(tenantID)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to get tenant id %s info from cache", tenantID)
		return nil, nil
	}

	namespace := tenant.ShardNamespace
	if ctxClaims.ShardNamespace != "" && ctxClaims.ShardNamespace != namespace &&
		ctxClaims.ShardNamespace != tenant.OtherNamespace {
		log.Error().Msgf("Auth claims give permission for namespace %s, but tenant %s is in namespace %s (other_namespace: %s)",
			ctxClaims.ShardNamespace, tenantID, namespace, tenant.OtherNamespace)
		return nil, nil
	}

	if !ctxClaims.HasScope(scope) {
		log.Error().Msgf("Tenant %s does not have scope %s", tenantID, scope)
		return nil, nil
	}

	riBucket, err := p.getRIBucketHandle(tenant)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to get bucket handle for tenant %s", tenantID)
		return nil, nil
	}

	return riBucket, tenant
}

func (p *GcsProxyService) makeKey(tenantID string, objectPath string) string {
	return tenantID + "/" + objectPath
}

func (p *GcsProxyService) Write(ctx context.Context, req *proto.WriteRequest) (*proto.WriteResponse, error) {
	bucket, tenant := p.validateTenantGetBucketHandle(ctx, req.TenantId, token_exchange.Scope_REQUEST_RESTRICTED_RW)
	if bucket == nil {
		// validate function logs so no reason to spam
		return nil, status.Errorf(codes.PermissionDenied, "invalid tenant or namespace")
	}

	key := p.makeKey(req.TenantId, req.ObjectPath)
	log.Info().Msgf("Writing to GCS bucket %s, key %s", bucket.BucketName(), key)
	obj := bucket.Object(key)
	writer := obj.NewWriter(ctx)

	// Encrypt data if needed. Skip for empty values.
	data := req.Data
	isEncrypted := tenantcrypto.HasSealKey(tenant) && len(data) > 0
	var encryptionStatus string
	if len(data) == 0 {
		encryptionStatus = "empty"
	} else {
		encryptionStatus = strconv.FormatBool(isEncrypted)
	}
	ChunkSizeBytes.WithLabelValues(tenantwatcherclient.MetricsTenantName(tenant), "write", encryptionStatus).Observe(float64(len(data)))
	if isEncrypted {
		startTime := time.Now()
		sealed, err := p.tenantCrypto.Seal(ctx, tenant, data)
		CryptoLatency.WithLabelValues(
			tenantwatcherclient.MetricsTenantName(tenant),
			"seal",
			tenantcrypto.GetStatusString(err),
		).Observe(time.Since(startTime).Seconds())
		if err != nil {
			return nil, status.Errorf(codes.PermissionDenied, "failed to encrypt data: %v", err)
		}
		data = sealed
		writer.Metadata = map[string]string{"isEncrypted": "true"}
	}

	// Guidance from GCS --
	// If you upload small objects (< 16MiB), you should set ChunkSize
	// to a value slightly larger than the objects' sizes to avoid memory bloat.
	// This is especially important if you are uploading many small objects
	// concurrently. See
	// https://cloud.google.com/storage/docs/json_api/v1/how-tos/upload#size
	// for more information about performance trade-offs related to ChunkSize.
	// Most request insights are likely < 128KB, so this is a good balance
	// (though some will be much smaller -- we can keep monitoring)
	writer.ChunkSize = 128 * 1024 // 128KB chunks
	_, err := writer.Write(data)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to write to GCS")
		return nil, status.Errorf(codes.Internal, "failed to write to GCS: %v", err)
	}
	err = writer.Close()
	if err != nil {
		// note: these can be write errors, not just close errors
		log.Error().Err(err).Msgf("Failed to close GCS writer")
		return nil, status.Errorf(codes.Internal, "failed to close GCS writer: %v", err)
	}

	return &proto.WriteResponse{}, nil
}

func (p *GcsProxyService) Read(ctx context.Context, req *proto.ReadRequest) (*proto.ReadResponse, error) {
	bucket, tenant := p.validateTenantGetBucketHandle(ctx, req.TenantId, token_exchange.Scope_REQUEST_RESTRICTED_R)
	if bucket == nil {
		// validate function logs so no reason to spam
		return nil, status.Errorf(codes.PermissionDenied, "invalid tenant or namespace")
	}

	key := p.makeKey(req.TenantId, req.ObjectPath)
	log.Info().Msgf("Reading from GCS bucket %s, key %s", bucket.BucketName(), key)
	obj := bucket.Object(key)
	reader, err := obj.NewReader(ctx)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to read from GCS")
		return nil, status.Errorf(codes.NotFound, "failed to read from GCS: %v", err)
	}
	defer reader.Close()

	data, err := io.ReadAll(reader)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to read from GCS")
		return nil, status.Errorf(codes.Internal, "failed to read from GCS: %v", err)
	}

	// Determine if object is encrypted
	isEncrypted := false
	attrs, err := obj.Attrs(ctx)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to get object attributes from GCS")
		return nil, status.Errorf(codes.NotFound, "failed to get object attributes from GCS: %v", err)
	}
	if attrs.Metadata != nil {
		if val, exists := attrs.Metadata["isEncrypted"]; exists && val == "true" {
			isEncrypted = true
		}
	}
	var encryptionStatus string
	if len(data) == 0 {
		encryptionStatus = "empty"
	} else {
		encryptionStatus = strconv.FormatBool(isEncrypted)
	}
	ChunkSizeBytes.WithLabelValues(tenantwatcherclient.MetricsTenantName(tenant), "read", encryptionStatus).Observe(float64(len(data)))
	if isEncrypted {
		startTime := time.Now()
		decrypted, err := p.tenantCrypto.Open(ctx, tenant, data)
		CryptoLatency.WithLabelValues(
			tenantwatcherclient.MetricsTenantName(tenant),
			"open",
			tenantcrypto.GetStatusString(err),
		).Observe(time.Since(startTime).Seconds())
		if err != nil {
			log.Error().Err(err).Msgf("Failed to decrypt data")
			return nil, status.Errorf(codes.PermissionDenied, "failed to decrypt data: %v", err)
		}
		data = decrypted
	}

	return &proto.ReadResponse{Data: data}, nil
}

func (p *GcsProxyService) List(ctx context.Context, req *proto.ListRequest) (*proto.ListResponse, error) {
	bucket, _ := p.validateTenantGetBucketHandle(ctx, req.TenantId, token_exchange.Scope_REQUEST_RESTRICTED_R)
	if bucket == nil {
		// validate function logs so no reason to spam
		return nil, status.Errorf(codes.PermissionDenied, "invalid tenant or namespace")
	}

	prefix := p.makeKey(req.TenantId, req.Prefix)
	log.Info().Msgf("Listing from GCS bucket %s, prefix %s", bucket.BucketName(), prefix)
	var result []string
	it := bucket.Objects(ctx, &storage.Query{Prefix: prefix})
	for {
		obj, err := it.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			log.Error().Err(err).Msgf("Failed to list from GCS")
			return nil, status.Errorf(codes.Internal, "failed to list from GCS: %v", err)
		}

		relativePath, err := p.validateAndExtractObjectPath(obj.Name, req.TenantId)
		if err != nil {
			return nil, err
		}

		result = append(result, relativePath)
	}

	return &proto.ListResponse{ObjectPaths: result}, nil
}

// Validates the object path and extracts the relative path without the tenant ID prefix
func (p *GcsProxyService) validateAndExtractObjectPath(objectName string, expectedTenantID string) (string, error) {
	split := strings.Split(objectName, "/")
	if err := p.validatePathFormat(split, objectName); err != nil {
		return "", err
	}

	if err := p.validateTenantID(split[0], expectedTenantID, objectName); err != nil {
		return "", err
	}

	return p.extractRelativePath(split), nil
}

// Ensures the path has at least two components (tenant ID and object name)
func (p *GcsProxyService) validatePathFormat(pathComponents []string, objectName string) error {
	if len(pathComponents) < 2 {
		errMsg := fmt.Sprintf("unexpected object name format: %s, expected at least one '/'", objectName)
		log.Error().Msg(errMsg)
		return status.Error(codes.Internal, errMsg)
	}
	return nil
}

// Verifies that the tenant ID in the path matches the expected tenant ID
func (p *GcsProxyService) validateTenantID(actualTenantID, expectedTenantID, objectName string) error {
	if actualTenantID != expectedTenantID {
		errMsg := fmt.Sprintf("unexpected tenantID in object name: %s, expected %s", objectName, expectedTenantID)
		log.Error().Msg(errMsg)
		return status.Error(codes.Internal, errMsg)
	}
	return nil
}

// Returns the path without the tenant ID prefix
func (p *GcsProxyService) extractRelativePath(pathComponents []string) string {
	return strings.Join(pathComponents[1:], "/")
}
