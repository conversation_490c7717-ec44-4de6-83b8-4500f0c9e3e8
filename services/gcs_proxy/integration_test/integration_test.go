package integration_test

import (
	"context"
	"io"
	"net"
	"testing"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/test/bufconn"

	"github.com/augmentcode/augment/base/go/secretstring"
	gcsproxyclient "github.com/augmentcode/augment/services/gcs_proxy/client"
	proto "github.com/augmentcode/augment/services/gcs_proxy/proto"
	"github.com/augmentcode/augment/services/gcs_proxy/server"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/fsouza/fake-gcs-server/fakestorage"
	"github.com/rs/zerolog/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const bufSize = 1024 * 1024

var claimsChannel chan *auth.AugmentClaims

func setupFakeServer(t *testing.T) (*grpc.Server, *bufconn.Listener, *fakestorage.Server) {
	lis := bufconn.Listen(bufSize)

	// Create a mock auth interceptor -- this is "token exchange" -- easier than embedding claims in the token
	// and mocking token exchange, and gives us the same type of testing coverage
	mockAuthInterceptor := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		claims := <-claimsChannel
		log.Info().Msgf("Mock auth interceptor called; adding claims to context: %v ", claims)
		ctx = auth.WithAugmentClaims(ctx, claims)
		return handler(ctx, req)
	}

	// Create the server with the mock auth interceptor
	s := grpc.NewServer(
		grpc.UnaryInterceptor(mockAuthInterceptor),
	)

	ctx := context.Background()
	gcsEmulator, gcsObjects := server.NewGcsEmulatorWithDefaultObjectsForTesting(t) // this function takes care of cleaning up
	tenantClient := server.NewDefaultMockTenantWatcherClientForTesting()
	service := server.NewTestService(ctx, gcsObjects, tenantClient)
	proto.RegisterGcsProxyServer(s, service)
	go func() {
		if err := s.Serve(lis); err != nil {
			t.Fatalf("Server exited with error: %v", err)
		}
	}()
	t.Cleanup(func() {
		service.Close()
		s.Stop()
		lis.Close()
	})
	return s, lis, gcsEmulator
}

func TestWriteReadList(t *testing.T) {
	_, lis, gcsEmulator := setupFakeServer(t)

	// Create a client connection using the in-memory listener
	conn, err := grpc.Dial("bufnet",
		grpc.WithContextDialer(func(context.Context, string) (net.Conn, error) {
			return lis.Dial()
		}),
		grpc.WithTransportCredentials(insecure.NewCredentials()))
	assert.NoError(t, err)
	defer conn.Close()

	client := gcsproxyclient.NewGcsProxyClientFromConn(conn)

	// Create a channel to pass the desired auth claims for the test
	claimsChannel = make(chan *auth.AugmentClaims, 1)
	defer close(claimsChannel) // not necessary, just habit

	// Add request context
	rc := requestcontext.New(
		requestcontext.NewRandomRequestId(),
		requestcontext.NewRandomRequestSessionId(),
		"integration-test",
		secretstring.SecretString{},
	)

	// Test cases for different tenant types
	testCases := []struct {
		name        string
		tenantID    string
		isEncrypted bool
		bucketName  string
	}{
		{
			name:        "Enterprise Non-Encrypted Tenant",
			tenantID:    "enterprise-tenant",
			isEncrypted: false,
			bucketName:  "enterprise-bucket",
		},
		{
			name:        "Enterprise Encrypted Tenant",
			tenantID:    "enterprise-encrypted-tenant",
			isEncrypted: true,
			bucketName:  "enterprise-bucket",
		},
		{
			name:        "Community Non-Encrypted Tenant",
			tenantID:    "community-tenant",
			isEncrypted: false,
			bucketName:  "non-enterprise-bucket",
		},
		{
			name:        "Community Encrypted Tenant",
			tenantID:    "community-encrypted-tenant",
			isEncrypted: true,
			bucketName:  "non-enterprise-bucket",
		},
	}

	// Get direct GCS client to verify raw data
	gcsClient := gcsEmulator.Client()

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			testData := []byte("test-data-for-" + tc.tenantID)
			objectPath := "test-object-" + tc.tenantID
			gcsObjectPath := tc.tenantID + "/" + objectPath

			// Test write
			claimsChannel <- &auth.AugmentClaims{
				TenantID: tc.tenantID,
				Scope:    []string{"REQUEST_RESTRICTED_R", "REQUEST_RESTRICTED_RW"},
			}
			_, err = client.Write(context.Background(), rc, &proto.WriteRequest{
				TenantId:   tc.tenantID,
				ObjectPath: objectPath,
				Data:       testData,
			})
			assert.NoError(t, err)

			// Test read
			claimsChannel <- &auth.AugmentClaims{
				TenantID: tc.tenantID,
				Scope:    []string{"REQUEST_RESTRICTED_R"},
			}
			readResp, err := client.Read(context.Background(), rc, &proto.ReadRequest{
				TenantId:   tc.tenantID,
				ObjectPath: objectPath,
			})
			require.NoError(t, err)
			require.NotNil(t, readResp)
			assert.Equal(t, testData, readResp.Data)

			// Test list
			claimsChannel <- &auth.AugmentClaims{
				TenantID: tc.tenantID,
				Scope:    []string{"REQUEST_RESTRICTED_R"},
			}
			listResp, err := client.List(context.Background(), rc, &proto.ListRequest{
				TenantId: tc.tenantID,
				Prefix:   "",
			})
			require.NoError(t, err)
			require.NotNil(t, listResp)
			assert.Contains(t, listResp.ObjectPaths, objectPath)

			// Verify raw data in GCS
			rawReader, err := gcsClient.Bucket(tc.bucketName).Object(gcsObjectPath).NewReader(context.Background())
			assert.NoError(t, err)
			defer rawReader.Close()

			rawData, err := io.ReadAll(rawReader)
			assert.NoError(t, err)

			// Check if data is encrypted or not
			if tc.isEncrypted {
				// For encrypted tenants, raw data should be different from test data
				assert.NotEqual(t, testData, rawData, "Data should be encrypted in GCS for encrypted tenant")

				// Verify metadata indicates encryption
				attrs, err := gcsClient.Bucket(tc.bucketName).Object(gcsObjectPath).Attrs(context.Background())
				assert.NoError(t, err)
				assert.Equal(t, "true", attrs.Metadata["isEncrypted"], "Metadata should indicate encryption")
			} else {
				// For non-encrypted tenants, raw data should match test data
				assert.Equal(t, testData, rawData, "Data should not be encrypted in GCS for non-encrypted tenant")

				// Verify metadata does not indicate encryption
				attrs, err := gcsClient.Bucket(tc.bucketName).Object(gcsObjectPath).Attrs(context.Background())
				assert.NoError(t, err)
				assert.NotEqual(t, "true", attrs.Metadata["isEncrypted"], "Metadata should not indicate encryption")
			}

			// enough error path unit testing that we don't need to repeat it here
		})
	}
}
