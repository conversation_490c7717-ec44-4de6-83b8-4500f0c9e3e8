// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
{
  deployment: [
    {
      name: 'auth-central',
      kubecfg: {
        target: '//services/auth/central/server:kubecfg',
        task: [
          {
            cloud: std.asciiUpper(centralNamespace.cloud),
            env: centralNamespace.env,
            namespace: centralNamespace.namespace,
          }
          for centralNamespace in cloudInfo.centralNamespaces
          if centralNamespace.cloud != 'GCP_EU_WEST4_PROD' && centralNamespace.cloud != 'GCP_US_CENTRAL1_GSC_PROD'
        ],
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['des', 'costa', 'aswin'],
          slack_channel: '#system-services',
        },
      },
      priority: 1,  // Try to run this early since it's a central dependency of other deploys
    },
    {
      name: 'staging-auth-central-spanner-database-tombstone',
      kubecfg_tombstone: {
        object: [{
          apiVersion: 'spanner.cnrm.cloud.google.com/v1beta1',
          kind: 'SpannerDatabase',
          name: 'central-staging-auth',
        }],
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },
        ],
      },
    },
    {
      name: 'prod-auth-central-spanner-database-tombstone',
      kubecfg_tombstone: {
        object: [{
          apiVersion: 'spanner.cnrm.cloud.google.com/v1beta1',
          kind: 'SpannerDatabase',
          name: 'central-auth',
        }],
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
    },
    {
      name: 'auth-central-monitoring',
      kubecfg: {
        target: '//services/auth/central/server:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['des', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}
