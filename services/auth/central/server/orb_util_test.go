package main

import (
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestCheckUserTierChangeInProgress(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	userDAO := daoFactory.GetUserDAO() // We'll use this userDAO instance

	ctx := bigtableFixture.Ctx // Use context from fixture
	testUserID := "test-user-123"

	tests := []struct {
		name            string
		userIDInput     string
		setupDAOState   func(t *testing.T, dao *UserDAO, userID string) error
		expectedErrCode codes.Code
		expectNilErr    bool
	}{
		{
			name:        "No tier change in progress",
			userIDInput: testUserID,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      fmt.Sprintf("%<EMAIL>", userID),
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			expectNilErr: true,
		},
		{
			name:        "Tier change in progress",
			userIDInput: testUserID,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:    userID,
					Email: fmt.Sprintf("%<EMAIL>", userID),
					TierChange: &auth_entities.User_TierChangeInfo{
						Id: "change-abc",
					},
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			expectedErrCode: codes.FailedPrecondition,
		},
		{
			name:        "User not found",
			userIDInput: testUserID, // This user ID will not be created
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// Do nothing, ensuring the user does not exist (rely on ClearTable)
				return nil
			},
			expectedErrCode: codes.NotFound,
		},
		{
			name:        "Empty userID",
			userIDInput: "",
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// Do nothing
				return nil
			},
			expectedErrCode: codes.InvalidArgument,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bigtableFixture.ClearTable(t) // Clear table for each test case

			err := tt.setupDAOState(t, userDAO, tt.userIDInput)
			require.NoError(t, err, "setupDAOState failed") // Fail fast if setup fails

			actualErr := checkUserTierChangeInProgress(ctx, tt.userIDInput, userDAO)

			if tt.expectNilErr {
				assert.NoError(t, actualErr)
			} else {
				assert.Error(t, actualErr)
				if tt.expectedErrCode != codes.OK {
					s, ok := status.FromError(actualErr)
					if assert.True(t, ok, "Error should be a gRPC status error") {
						assert.Equal(t, tt.expectedErrCode, s.Code(), fmt.Sprintf("Expected error code %s, got %s (err: %v)", tt.expectedErrCode, s.Code(), actualErr))
					}
				}
			}
		})
	}
}

func TestSafeCreateCustomer(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	userDAO := daoFactory.GetUserDAO() // Real UserDAO

	ctx := bigtableFixture.Ctx // Use context from fixture
	testUserID := "user-for-scc"
	mockCustomer := orb.OrbCustomer{Email: "<EMAIL>", Name: "Test User SCC"}
	idempotencyKey := "idem-key-scc"
	expectedCustID := "new-cust-id-scc"

	tests := []struct {
		name                string
		userIDInput         string
		customerInput       orb.OrbCustomer
		usingStripeInput    bool
		idempotencyKeyInput *string
		setupDAOState       func(t *testing.T, dao *UserDAO, userID string, customerEmail string) error // Changed from setupTierChecker
		setupOrbClient      func(client *orb.MockOrbClient)
		expectedID          string
		expectedErrCode     codes.Code
		expectSpecificErr   error
		expectNilErr        bool
	}{
		{
			name:                "Tier change in progress",
			userIDInput:         testUserID,
			customerInput:       mockCustomer,
			usingStripeInput:    true,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string, customerEmail string) error {
				user := &auth_entities.User{
					Id:    userID,
					Email: customerEmail,
					TierChange: &auth_entities.User_TierChangeInfo{
						Id: "change-scc",
					},
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.FailedPrecondition,
		},
		{
			name:                "No tier change - Orb call succeeds",
			userIDInput:         testUserID,
			customerInput:       mockCustomer,
			usingStripeInput:    true,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string, customerEmail string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      customerEmail,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("CreateCustomer", ctx, mockCustomer, true, &idempotencyKey).Return(expectedCustID, nil).Once()
			},
			expectedID:   expectedCustID,
			expectNilErr: true,
		},
		{
			name:                "No tier change - Orb call fails",
			userIDInput:         testUserID,
			customerInput:       mockCustomer,
			usingStripeInput:    false,
			idempotencyKeyInput: nil,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string, customerEmail string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      customerEmail,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("CreateCustomer", ctx, mockCustomer, false, (*string)(nil)).Return("", errors.New("orb create failed")).Once()
			},
			expectSpecificErr: errors.New("orb create failed"),
		},
		{
			name:                "User not found (was UserTierStatusChecker fails)",
			userIDInput:         testUserID,
			customerInput:       mockCustomer,
			usingStripeInput:    true,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string, customerEmail string) error {
				// Do not create the user, checkUserTierChangeInProgress will return NotFound
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.NotFound, // Changed from codes.Unavailable
		},
		{
			name:                "Invalid UserID (empty string)",
			userIDInput:         "",
			customerInput:       mockCustomer,
			usingStripeInput:    true,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string, customerEmail string) error {
				// Do nothing, error is checked before DAO interaction
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.InvalidArgument,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bigtableFixture.ClearTable(t) // Clear table for each test case
			mockOrbCl := orb.NewMockOrbClient()

			// Call setupDAOState
			err := tt.setupDAOState(t, userDAO, tt.userIDInput, tt.customerInput.Email)
			require.NoError(t, err, "setupDAOState failed")

			tt.setupOrbClient(mockOrbCl)

			actualID, err := SafeCreateCustomer(ctx, mockOrbCl, userDAO, tt.userIDInput, tt.customerInput, tt.usingStripeInput, tt.idempotencyKeyInput)

			if tt.expectNilErr {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedID, actualID)
			} else {
				assert.Error(t, err)
				if tt.expectedErrCode != codes.OK { // codes.OK means no specific code check
					s, ok := status.FromError(err)
					if assert.True(t, ok, "Error should be a gRPC status error") {
						assert.Equal(t, tt.expectedErrCode, s.Code(), fmt.Sprintf("Expected error code %s, got %s (err: %v)", tt.expectedErrCode, s.Code(), err))
					}
				}
				if tt.expectSpecificErr != nil {
					assert.ErrorContains(t, err, tt.expectSpecificErr.Error())
				}
			}
			mockOrbCl.AssertExpectations(t)
		})
	}
}

func TestSafeCreateSubscription(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	userDAO := daoFactory.GetUserDAO() // Real UserDAO

	ctx := bigtableFixture.Ctx // Use context from fixture
	testUserID := "user-for-scs"
	testUserEmailForDAO := "<EMAIL>"
	mockSubscription := orb.OrbSubscription{CustomerOrbID: "cust-orb-id-scs", ExternalPlanID: "pro-plan"}
	idempotencyKey := "idem-key-scs"
	expectedSubID := "new-sub-id-scs"

	tests := []struct {
		name                string
		userIDInput         string
		subscriptionInput   orb.OrbSubscription
		idempotencyKeyInput *string
		setupDAOState       func(t *testing.T, dao *UserDAO, userID string) error // Changed
		setupOrbClient      func(client *orb.MockOrbClient)
		expectedID          string
		expectedErrCode     codes.Code
		expectSpecificErr   error
		expectNilErr        bool
	}{
		{
			name:                "Tier change in progress",
			userIDInput:         testUserID,
			subscriptionInput:   mockSubscription,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:    userID,
					Email: testUserEmailForDAO, // Added email
					TierChange: &auth_entities.User_TierChangeInfo{
						Id: "change-scs",
					},
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.FailedPrecondition,
		},
		{
			name:                "No tier change - Orb call succeeds",
			userIDInput:         testUserID,
			subscriptionInput:   mockSubscription,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO, // Added email
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("CreateSubscription", ctx, mockSubscription, &idempotencyKey).Return(expectedSubID, nil).Once()
			},
			expectedID:   expectedSubID,
			expectNilErr: true,
		},
		{
			name:                "No tier change - Orb call fails",
			userIDInput:         testUserID,
			subscriptionInput:   mockSubscription,
			idempotencyKeyInput: nil,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO, // Added email
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("CreateSubscription", ctx, mockSubscription, (*string)(nil)).Return("", errors.New("orb create sub failed")).Once()
			},
			expectSpecificErr: errors.New("orb create sub failed"),
		},
		{
			name:                "User not found (was UserTierStatusChecker fails)",
			userIDInput:         testUserID,
			subscriptionInput:   mockSubscription,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// Do not create the user
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.NotFound, // Changed
		},
		{
			name:                "Invalid UserID (empty string)",
			userIDInput:         "",
			subscriptionInput:   mockSubscription,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// No setup needed, error is pre-DAO
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.InvalidArgument,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bigtableFixture.ClearTable(t)
			mockOrbCl := orb.NewMockOrbClient()

			err := tt.setupDAOState(t, userDAO, tt.userIDInput)
			require.NoError(t, err, "setupDAOState failed")

			tt.setupOrbClient(mockOrbCl)

			actualID, err := SafeCreateSubscription(ctx, mockOrbCl, userDAO, tt.userIDInput, tt.subscriptionInput, tt.idempotencyKeyInput)

			if tt.expectNilErr {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedID, actualID)
			} else {
				assert.Error(t, err)
				if tt.expectedErrCode != codes.OK {
					s, ok := status.FromError(err)
					if assert.True(t, ok, "Error should be a gRPC status error") {
						assert.Equal(t, tt.expectedErrCode, s.Code(), fmt.Sprintf("Expected error code %s, got %s (err: %v)", tt.expectedErrCode, s.Code(), err))
					}
				}
				if tt.expectSpecificErr != nil {
					assert.ErrorContains(t, err, tt.expectSpecificErr.Error())
				}
			}
			mockOrbCl.AssertExpectations(t)
		})
	}
}

func TestSafePurchaseCredits(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	userDAO := daoFactory.GetUserDAO() // Real UserDAO

	ctx := bigtableFixture.Ctx // Use context from fixture
	testUserID := "user-for-spc"
	testUserEmailForDAO := "<EMAIL>" // Consistent email for DAO setup
	mockCreditPurchase := orb.OrbCreditPurchase{CustomerOrbID: "cust-orb-id-spc", NumberCredits: 100, Currency: "USD", ExpiryDate: time.Now().AddDate(1, 0, 0)}
	idempotencyKey := "idem-key-spc"

	tests := []struct {
		name                string
		userIDInput         string
		creditPurchaseInput orb.OrbCreditPurchase
		idempotencyKeyInput *string
		setupDAOState       func(t *testing.T, dao *UserDAO, userID string) error // Changed
		setupOrbClient      func(client *orb.MockOrbClient)
		expectedErrCode     codes.Code
		expectSpecificErr   error
		expectNilErr        bool
	}{
		{
			name:                "Tier change in progress",
			userIDInput:         testUserID,
			creditPurchaseInput: mockCreditPurchase,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:    userID,
					Email: testUserEmailForDAO,
					TierChange: &auth_entities.User_TierChangeInfo{
						Id: "change-spc",
					},
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.FailedPrecondition,
		},
		{
			name:                "No tier change - Orb call succeeds",
			userIDInput:         testUserID,
			creditPurchaseInput: mockCreditPurchase,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("PurchaseCredits", ctx, mockCreditPurchase, &idempotencyKey).Return(nil).Once()
			},
			expectNilErr: true,
		},
		{
			name:                "No tier change - Orb call fails",
			userIDInput:         testUserID,
			creditPurchaseInput: mockCreditPurchase,
			idempotencyKeyInput: nil,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("PurchaseCredits", ctx, mockCreditPurchase, (*string)(nil)).Return(errors.New("orb purchase credits failed")).Once()
			},
			expectSpecificErr: errors.New("orb purchase credits failed"),
		},
		{
			name:                "User not found (was UserTierStatusChecker fails)",
			userIDInput:         testUserID,
			creditPurchaseInput: mockCreditPurchase,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// Do not create the user
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.NotFound, // Changed
		},
		{
			name:                "Invalid UserID (empty string)",
			userIDInput:         "",
			creditPurchaseInput: mockCreditPurchase,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// No setup needed
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.InvalidArgument,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bigtableFixture.ClearTable(t)
			mockOrbCl := orb.NewMockOrbClient()

			err := tt.setupDAOState(t, userDAO, tt.userIDInput)
			require.NoError(t, err, "setupDAOState failed")

			tt.setupOrbClient(mockOrbCl)

			err = SafePurchaseCredits(ctx, mockOrbCl, userDAO, tt.userIDInput, tt.creditPurchaseInput, tt.idempotencyKeyInput)

			if tt.expectNilErr {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				if tt.expectedErrCode != codes.OK {
					s, ok := status.FromError(err)
					if assert.True(t, ok, "Error should be a gRPC status error") {
						assert.Equal(t, tt.expectedErrCode, s.Code(), fmt.Sprintf("Expected error code %s, got %s (err: %v)", tt.expectedErrCode, s.Code(), err))
					}
				}
				if tt.expectSpecificErr != nil {
					assert.ErrorContains(t, err, tt.expectSpecificErr.Error())
				}
			}
			mockOrbCl.AssertExpectations(t)
		})
	}
}

func TestSafeCancelOrbSubscription(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	userDAO := daoFactory.GetUserDAO() // Real UserDAO

	ctx := bigtableFixture.Ctx // Use context from fixture
	testUserID := "user-for-scos"
	testUserEmailForDAO := "<EMAIL>"
	testOrbSubID := "orb-sub-to-cancel"
	idempotencyKey := "idem-key-scos"

	tests := []struct {
		name                           string
		userIDInput                    string
		orbSubIDInput                  string
		cancelTimeInput                orb.PlanChangeType
		cancelDateInput                *time.Time
		idempotencyKeyInput            *string
		unscheduleExistingCancellation bool
		setupDAOState                  func(t *testing.T, dao *UserDAO, userID string) error
		setupOrbClient                 func(client *orb.MockOrbClient)
		expectedErrCode                codes.Code
		expectSpecificErr              error
		expectNilErr                   bool
	}{
		{
			name:                           "Tier change in progress",
			userIDInput:                    testUserID,
			orbSubIDInput:                  testOrbSubID,
			cancelTimeInput:                orb.PlanChangeImmediate,
			idempotencyKeyInput:            &idempotencyKey,
			unscheduleExistingCancellation: false,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:    userID,
					Email: testUserEmailForDAO,
					TierChange: &auth_entities.User_TierChangeInfo{
						Id: "change-scos",
					},
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.FailedPrecondition,
		},
		{
			name:                           "No tier change - Active subscription - Immediate cancellation",
			userIDInput:                    testUserID,
			orbSubIDInput:                  testOrbSubID,
			cancelTimeInput:                orb.PlanChangeImmediate,
			idempotencyKeyInput:            &idempotencyKey,
			unscheduleExistingCancellation: true,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				// Mock GetUserSubscription to return active subscription
				client.On("GetUserSubscription", ctx, testOrbSubID, (*orb.ItemIds)(nil)).Return(&orb.OrbSubscriptionInfo{
					OrbSubscriptionID: testOrbSubID,
					OrbStatus:         "active",
					EndDate:           time.Time{}, // No end date = not scheduled for cancellation
				}, nil).Once()
				client.On("CancelOrbSubscription", ctx, testOrbSubID, orb.PlanChangeImmediate, (*time.Time)(nil), &idempotencyKey).Return(nil).Once()
			},
			expectNilErr: true,
		},
		{
			name:                           "Subscription already scheduled for cancellation - unschedule and reschedule",
			userIDInput:                    testUserID,
			orbSubIDInput:                  testOrbSubID,
			cancelTimeInput:                orb.PlanChangeEndOfTerm,
			cancelDateInput:                nil,
			idempotencyKeyInput:            nil,
			unscheduleExistingCancellation: true,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				// Mock GetUserSubscription to return subscription scheduled for cancellation
				futureDate := time.Now().Add(7 * 24 * time.Hour)
				client.On("GetUserSubscription", ctx, testOrbSubID, (*orb.ItemIds)(nil)).Return(&orb.OrbSubscriptionInfo{
					OrbSubscriptionID: testOrbSubID,
					OrbStatus:         "active",
					EndDate:           futureDate, // Has end date = scheduled for cancellation
				}, nil).Once()
				client.On("UnschedulePendingSubscriptionCancellation", ctx, testOrbSubID, (*string)(nil)).Return(nil).Once()
				client.On("CancelOrbSubscription", ctx, testOrbSubID, orb.PlanChangeEndOfTerm, (*time.Time)(nil), (*string)(nil)).Return(nil).Once()
			},
			expectNilErr: true,
		},
		{
			name:                           "Subscription already scheduled for cancellation - don't unschedule",
			userIDInput:                    testUserID,
			orbSubIDInput:                  testOrbSubID,
			cancelTimeInput:                orb.PlanChangeEndOfTerm,
			cancelDateInput:                nil,
			idempotencyKeyInput:            nil,
			unscheduleExistingCancellation: false,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				// Mock GetUserSubscription to return subscription scheduled for cancellation
				futureDate := time.Now().Add(7 * 24 * time.Hour)
				client.On("GetUserSubscription", ctx, testOrbSubID, (*orb.ItemIds)(nil)).Return(&orb.OrbSubscriptionInfo{
					OrbSubscriptionID: testOrbSubID,
					OrbStatus:         "active",
					EndDate:           futureDate, // Has end date = scheduled for cancellation
				}, nil).Once()
				// Should not call unschedule or cancel
			},
			expectNilErr: true,
		},
		{
			name:                "No tier change - Orb call fails",
			userIDInput:         testUserID,
			orbSubIDInput:       testOrbSubID,
			cancelTimeInput:     orb.PlanChangeEndOfTerm,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("GetUserSubscription", ctx, testOrbSubID, (*orb.ItemIds)(nil)).Return(&orb.OrbSubscriptionInfo{
					OrbSubscriptionID: testOrbSubID,
					OrbStatus:         "active",
					EndDate:           time.Time{}, // No end date = not scheduled for cancellation
				}, nil).Once()
				client.On("CancelOrbSubscription", ctx, testOrbSubID, orb.PlanChangeEndOfTerm, (*time.Time)(nil), &idempotencyKey).Return(errors.New("orb cancel sub failed")).Once()
			},
			expectSpecificErr: errors.New("orb cancel sub failed"),
		},
		{
			name:                "User not found (was UserTierStatusChecker fails)",
			userIDInput:         testUserID,
			orbSubIDInput:       testOrbSubID,
			cancelTimeInput:     orb.PlanChangeImmediate,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// Do not create user
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.NotFound, // Changed
		},
		{
			name:                "Invalid UserID (empty string)",
			userIDInput:         "",
			orbSubIDInput:       testOrbSubID,
			cancelTimeInput:     orb.PlanChangeImmediate,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// No setup needed
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.InvalidArgument,
		},
		{
			name:                "Orb Subscription is already ended",
			userIDInput:         testUserID,
			orbSubIDInput:       testOrbSubID,
			cancelTimeInput:     orb.PlanChangeImmediate,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				// Mock GetUserSubscription to return ended subscription
				client.On("GetUserSubscription", ctx, testOrbSubID, (*orb.ItemIds)(nil)).Return(&orb.OrbSubscriptionInfo{
					OrbSubscriptionID: testOrbSubID,
					OrbStatus:         "ended",
					EndDate:           time.Time{}, // No end date = not scheduled for cancellation
				}, nil).Once()
			},
			expectNilErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bigtableFixture.ClearTable(t)
			mockOrbCl := orb.NewMockOrbClient()

			err := tt.setupDAOState(t, userDAO, tt.userIDInput)
			require.NoError(t, err, "setupDAOState failed")

			tt.setupOrbClient(mockOrbCl)

			err = SafeCancelOrbSubscription(ctx, mockOrbCl, userDAO, tt.userIDInput, tt.orbSubIDInput, tt.cancelTimeInput, tt.cancelDateInput, tt.idempotencyKeyInput, tt.unscheduleExistingCancellation)

			if tt.expectNilErr {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				if tt.expectedErrCode != codes.OK {
					s, ok := status.FromError(err)
					if assert.True(t, ok, "Error should be a gRPC status error") {
						assert.Equal(t, tt.expectedErrCode, s.Code(), fmt.Sprintf("Expected error code %s, got %s (err: %v)", tt.expectedErrCode, s.Code(), err))
					}
				}
				if tt.expectSpecificErr != nil {
					assert.ErrorContains(t, err, tt.expectSpecificErr.Error())
				}
			}
			mockOrbCl.AssertExpectations(t)
		})
	}
}

func TestSafeUpdateFixedQuantity(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	userDAO := daoFactory.GetUserDAO() // Real UserDAO

	ctx := bigtableFixture.Ctx // Use context from fixture
	testUserID := "user-for-sufq"
	testUserEmailForDAO := "<EMAIL>"
	mockQuantityUpdate := orb.OrbQuantityUpdate{
		OrbSubscriptionID: "sub-id-sufq",
		PriceOverride:     orb.OrbPriceOverrides{PriceID: "price-id-sufq", Quantity: 5},
		UpdateTimeType:    orb.PlanChangeImmediate,
	}
	idempotencyKey := "idem-key-sufq"
	specificUpdateDate := time.Now().Add(12 * time.Hour)

	tests := []struct {
		name                string
		userIDInput         string
		quantityUpdateInput orb.OrbQuantityUpdate
		idempotencyKeyInput *string
		setupDAOState       func(t *testing.T, dao *UserDAO, userID string) error // Changed
		setupOrbClient      func(client *orb.MockOrbClient)
		expectedErrCode     codes.Code
		expectSpecificErr   error
		expectNilErr        bool
	}{
		{
			name:                "Tier change in progress",
			userIDInput:         testUserID,
			quantityUpdateInput: mockQuantityUpdate,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:    userID,
					Email: testUserEmailForDAO,
					TierChange: &auth_entities.User_TierChangeInfo{
						Id: "change-sufq",
					},
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.FailedPrecondition,
		},
		{
			name:                "No tier change - Orb call succeeds - Immediate",
			userIDInput:         testUserID,
			quantityUpdateInput: mockQuantityUpdate, // UpdateTimeType is PlanChangeImmediate
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("UpdateFixedQuantity", ctx, mockQuantityUpdate, &idempotencyKey).Return(nil).Once()
			},
			expectNilErr: true,
		},
		{
			name:        "No tier change - Orb call succeeds - Specific Date",
			userIDInput: testUserID,
			quantityUpdateInput: orb.OrbQuantityUpdate{
				OrbSubscriptionID: "sub-id-sufq-date",
				PriceOverride:     orb.OrbPriceOverrides{PriceID: "price-id-sufq-date", Quantity: 3},
				UpdateTimeType:    orb.PlanChangeSpecificDate,
				UpdateChangeDate:  &specificUpdateDate,
			},
			idempotencyKeyInput: nil,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				expectedUpdateArg := orb.OrbQuantityUpdate{
					OrbSubscriptionID: "sub-id-sufq-date",
					PriceOverride:     orb.OrbPriceOverrides{PriceID: "price-id-sufq-date", Quantity: 3},
					UpdateTimeType:    orb.PlanChangeSpecificDate,
					UpdateChangeDate:  &specificUpdateDate,
				}
				client.On("UpdateFixedQuantity", ctx, expectedUpdateArg, (*string)(nil)).Return(nil).Once()
			},
			expectNilErr: true,
		},
		{
			name:                "No tier change - Orb call fails",
			userIDInput:         testUserID,
			quantityUpdateInput: mockQuantityUpdate,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("UpdateFixedQuantity", ctx, mockQuantityUpdate, &idempotencyKey).Return(errors.New("orb update quantity failed")).Once()
			},
			expectSpecificErr: errors.New("orb update quantity failed"),
		},
		{
			name:                "User not found (was UserTierStatusChecker fails)",
			userIDInput:         testUserID,
			quantityUpdateInput: mockQuantityUpdate,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// Do not create user
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.NotFound, // Changed
		},
		{
			name:                "Invalid UserID (empty string)",
			userIDInput:         "",
			quantityUpdateInput: mockQuantityUpdate,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// No setup needed
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.InvalidArgument,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bigtableFixture.ClearTable(t)
			mockOrbCl := orb.NewMockOrbClient()

			err := tt.setupDAOState(t, userDAO, tt.userIDInput)
			require.NoError(t, err, "setupDAOState failed")

			tt.setupOrbClient(mockOrbCl)

			err = SafeUpdateFixedQuantity(ctx, mockOrbCl, userDAO, tt.userIDInput, tt.quantityUpdateInput, tt.idempotencyKeyInput)

			if tt.expectNilErr {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				if tt.expectedErrCode != codes.OK {
					s, ok := status.FromError(err)
					if assert.True(t, ok, "Error should be a gRPC status error") {
						assert.Equal(t, tt.expectedErrCode, s.Code(), fmt.Sprintf("Expected error code %s, got %s (err: %v)", tt.expectedErrCode, s.Code(), err))
					}
				}
				if tt.expectSpecificErr != nil {
					assert.ErrorContains(t, err, tt.expectSpecificErr.Error())
				}
			}
			mockOrbCl.AssertExpectations(t)
		})
	}
}

func TestSafeAddCreditBalanceDepletedAlert(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	userDAO := daoFactory.GetUserDAO() // Real UserDAO

	ctx := bigtableFixture.Ctx // Use context from fixture
	testUserID := "user-for-sacbda"
	testUserEmailForDAO := "<EMAIL>"
	testCustomerOrbID := "cust-orb-id-sacbda"
	testCurrency := "USD"
	idempotencyKey := "idem-key-sacbda"

	tests := []struct {
		name                string
		userIDInput         string
		customerOrbIDInput  string
		currencyInput       string
		idempotencyKeyInput *string
		setupDAOState       func(t *testing.T, dao *UserDAO, userID string) error // Changed
		setupOrbClient      func(client *orb.MockOrbClient)
		expectedErrCode     codes.Code
		expectSpecificErr   error
		expectNilErr        bool
	}{
		{
			name:                "Tier change in progress",
			userIDInput:         testUserID,
			customerOrbIDInput:  testCustomerOrbID,
			currencyInput:       testCurrency,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:    userID,
					Email: testUserEmailForDAO,
					TierChange: &auth_entities.User_TierChangeInfo{
						Id: "change-sacbda",
					},
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.FailedPrecondition,
		},
		{
			name:                "No tier change - Orb call succeeds",
			userIDInput:         testUserID,
			customerOrbIDInput:  testCustomerOrbID,
			currencyInput:       testCurrency,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("AddCreditBalanceDepletedAlert", ctx, testCustomerOrbID, testCurrency, &idempotencyKey).Return(nil).Once()
			},
			expectNilErr: true,
		},
		{
			name:                "No tier change - Orb call fails",
			userIDInput:         testUserID,
			customerOrbIDInput:  testCustomerOrbID,
			currencyInput:       testCurrency,
			idempotencyKeyInput: nil,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("AddCreditBalanceDepletedAlert", ctx, testCustomerOrbID, testCurrency, (*string)(nil)).Return(errors.New("orb add depleted alert failed")).Once()
			},
			expectSpecificErr: errors.New("orb add depleted alert failed"),
		},
		{
			name:                "User not found (was UserTierStatusChecker fails)",
			userIDInput:         testUserID,
			customerOrbIDInput:  testCustomerOrbID,
			currencyInput:       testCurrency,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// Do not create user
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.NotFound, // Changed
		},
		{
			name:                "Invalid UserID (empty string)",
			userIDInput:         "",
			customerOrbIDInput:  testCustomerOrbID,
			currencyInput:       testCurrency,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// No setup needed
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.InvalidArgument,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bigtableFixture.ClearTable(t)
			mockOrbCl := orb.NewMockOrbClient()

			err := tt.setupDAOState(t, userDAO, tt.userIDInput)
			require.NoError(t, err, "setupDAOState failed")

			tt.setupOrbClient(mockOrbCl)

			err = SafeAddCreditBalanceDepletedAlert(ctx, mockOrbCl, userDAO, tt.userIDInput, tt.customerOrbIDInput, tt.currencyInput, tt.idempotencyKeyInput)

			if tt.expectNilErr {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				if tt.expectedErrCode != codes.OK {
					s, ok := status.FromError(err)
					if assert.True(t, ok, "Error should be a gRPC status error") {
						assert.Equal(t, tt.expectedErrCode, s.Code(), fmt.Sprintf("Expected error code %s, got %s (err: %v)", tt.expectedErrCode, s.Code(), err))
					}
				}
				if tt.expectSpecificErr != nil {
					assert.ErrorContains(t, err, tt.expectSpecificErr.Error())
				}
			}
			mockOrbCl.AssertExpectations(t)
		})
	}
}

func TestSafeAddCreditBalanceRecoveredAlert(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	userDAO := daoFactory.GetUserDAO() // Real UserDAO

	ctx := bigtableFixture.Ctx // Use context from fixture
	testUserID := "user-for-sacbra"
	testUserEmailForDAO := "<EMAIL>"
	testCustomerOrbID := "cust-orb-id-sacbra"
	testCurrency := "EUR"
	idempotencyKey := "idem-key-sacbra"

	tests := []struct {
		name                string
		userIDInput         string
		customerOrbIDInput  string
		currencyInput       string
		idempotencyKeyInput *string
		setupDAOState       func(t *testing.T, dao *UserDAO, userID string) error // Changed
		setupOrbClient      func(client *orb.MockOrbClient)
		expectedErrCode     codes.Code
		expectSpecificErr   error
		expectNilErr        bool
	}{
		{
			name:                "Tier change in progress",
			userIDInput:         testUserID,
			customerOrbIDInput:  testCustomerOrbID,
			currencyInput:       testCurrency,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:    userID,
					Email: testUserEmailForDAO,
					TierChange: &auth_entities.User_TierChangeInfo{
						Id: "change-sacbra",
					},
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.FailedPrecondition,
		},
		{
			name:                "No tier change - Orb call succeeds",
			userIDInput:         testUserID,
			customerOrbIDInput:  testCustomerOrbID,
			currencyInput:       testCurrency,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("AddCreditBalanceRecoveredAlert", ctx, testCustomerOrbID, testCurrency, &idempotencyKey).Return(nil).Once()
			},
			expectNilErr: true,
		},
		{
			name:                "No tier change - Orb call fails",
			userIDInput:         testUserID,
			customerOrbIDInput:  testCustomerOrbID,
			currencyInput:       testCurrency,
			idempotencyKeyInput: nil,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("AddCreditBalanceRecoveredAlert", ctx, testCustomerOrbID, testCurrency, (*string)(nil)).Return(errors.New("orb add recovered alert failed")).Once()
			},
			expectSpecificErr: errors.New("orb add recovered alert failed"),
		},
		{
			name:                "User not found (was UserTierStatusChecker fails)",
			userIDInput:         testUserID,
			customerOrbIDInput:  testCustomerOrbID,
			currencyInput:       testCurrency,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// Do not create user
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.NotFound, // Changed
		},
		{
			name:                "Invalid UserID (empty string)",
			userIDInput:         "",
			customerOrbIDInput:  testCustomerOrbID,
			currencyInput:       testCurrency,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// No setup needed
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.InvalidArgument,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bigtableFixture.ClearTable(t)
			mockOrbCl := orb.NewMockOrbClient()

			err := tt.setupDAOState(t, userDAO, tt.userIDInput)
			require.NoError(t, err, "setupDAOState failed")

			tt.setupOrbClient(mockOrbCl)

			err = SafeAddCreditBalanceRecoveredAlert(ctx, mockOrbCl, userDAO, tt.userIDInput, tt.customerOrbIDInput, tt.currencyInput, tt.idempotencyKeyInput)

			if tt.expectNilErr {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				if tt.expectedErrCode != codes.OK {
					s, ok := status.FromError(err)
					if assert.True(t, ok, "Error should be a gRPC status error") {
						assert.Equal(t, tt.expectedErrCode, s.Code(), fmt.Sprintf("Expected error code %s, got %s (err: %v)", tt.expectedErrCode, s.Code(), err))
					}
				}
				if tt.expectSpecificErr != nil {
					assert.ErrorContains(t, err, tt.expectSpecificErr.Error())
				}
			}
			mockOrbCl.AssertExpectations(t)
		})
	}
}

func TestSafeSetCustomerPlanType(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	userDAO := daoFactory.GetUserDAO() // Real UserDAO

	ctx := bigtableFixture.Ctx // Use context from fixture
	testUserID := "user-for-susp"
	testUserEmailForDAO := "<EMAIL>"
	testOrbSubID := "orb-sub-to-update"
	testExternalPlanID := "new-plan-id"
	idempotencyKey := "idem-key-susp"
	specificChangeDate := time.Now().Add(24 * time.Hour)

	tests := []struct {
		name                string
		userIDInput         string
		orbSubIDInput       string
		externalPlanIDInput string
		changeTimeInput     orb.PlanChangeType
		changeDateInput     *time.Time
		idempotencyKeyInput *string
		setupDAOState       func(t *testing.T, dao *UserDAO, userID string) error // Changed
		setupOrbClient      func(client *orb.MockOrbClient)
		expectedErrCode     codes.Code
		expectSpecificErr   error
		expectNilErr        bool
	}{
		{
			name:                "Tier change in progress",
			userIDInput:         testUserID,
			orbSubIDInput:       testOrbSubID,
			externalPlanIDInput: testExternalPlanID,
			changeTimeInput:     orb.PlanChangeImmediate,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:    userID,
					Email: testUserEmailForDAO,
					TierChange: &auth_entities.User_TierChangeInfo{
						Id: "change-susp",
					},
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.FailedPrecondition,
		},
		{
			name:                "No tier change - Orb call succeeds - Immediate",
			userIDInput:         testUserID,
			orbSubIDInput:       testOrbSubID,
			externalPlanIDInput: testExternalPlanID,
			changeTimeInput:     orb.PlanChangeImmediate,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("SetCustomerPlanType", ctx, orb.OrbPlanChange{SubscriptionID: testOrbSubID, NewPlanID: testExternalPlanID, PlanChangeType: orb.PlanChangeImmediate, PlanChangeDate: nil}, &idempotencyKey).Return(nil).Once()
			},
			expectNilErr: true,
		},
		{
			name:                "No tier change - Orb call succeeds - Specific Date",
			userIDInput:         testUserID,
			orbSubIDInput:       testOrbSubID,
			externalPlanIDInput: testExternalPlanID,
			changeTimeInput:     orb.PlanChangeSpecificDate,
			changeDateInput:     &specificChangeDate,
			idempotencyKeyInput: nil,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("SetCustomerPlanType", ctx, orb.OrbPlanChange{SubscriptionID: testOrbSubID, NewPlanID: testExternalPlanID, PlanChangeType: orb.PlanChangeSpecificDate, PlanChangeDate: &specificChangeDate}, (*string)(nil)).Return(nil).Once()
			},
			expectNilErr: true,
		},
		{
			name:                "No tier change - Orb call fails",
			userIDInput:         testUserID,
			orbSubIDInput:       testOrbSubID,
			externalPlanIDInput: testExternalPlanID,
			changeTimeInput:     orb.PlanChangeEndOfTerm,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				user := &auth_entities.User{
					Id:         userID,
					Email:      testUserEmailForDAO,
					TierChange: nil,
				}
				_, err := dao.Create(ctx, user)
				return err
			},
			setupOrbClient: func(client *orb.MockOrbClient) {
				client.On("SetCustomerPlanType", ctx, orb.OrbPlanChange{SubscriptionID: testOrbSubID, NewPlanID: testExternalPlanID, PlanChangeType: orb.PlanChangeEndOfTerm, PlanChangeDate: nil}, &idempotencyKey).Return(errors.New("orb update plan failed")).Once()
			},
			expectSpecificErr: errors.New("orb update plan failed"),
		},
		{
			name:                "User not found (was UserTierStatusChecker fails)",
			userIDInput:         testUserID,
			orbSubIDInput:       testOrbSubID,
			externalPlanIDInput: testExternalPlanID,
			changeTimeInput:     orb.PlanChangeImmediate,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// Do not create user
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.NotFound, // Changed
		},
		{
			name:                "Invalid UserID (empty string)",
			userIDInput:         "",
			orbSubIDInput:       testOrbSubID,
			externalPlanIDInput: testExternalPlanID,
			changeTimeInput:     orb.PlanChangeImmediate,
			idempotencyKeyInput: &idempotencyKey,
			setupDAOState: func(t *testing.T, dao *UserDAO, userID string) error {
				// No setup needed
				return nil
			},
			setupOrbClient:  func(client *orb.MockOrbClient) {},
			expectedErrCode: codes.InvalidArgument,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bigtableFixture.ClearTable(t)
			mockOrbCl := orb.NewMockOrbClient()

			err := tt.setupDAOState(t, userDAO, tt.userIDInput)
			require.NoError(t, err, "setupDAOState failed")

			tt.setupOrbClient(mockOrbCl)

			planChange := orb.OrbPlanChange{
				SubscriptionID: tt.orbSubIDInput,
				NewPlanID:      tt.externalPlanIDInput,
				PlanChangeType: tt.changeTimeInput,
				PlanChangeDate: tt.changeDateInput,
			}
			err = SafeSetCustomerPlanType(ctx, mockOrbCl, userDAO, tt.userIDInput, planChange, tt.idempotencyKeyInput)

			if tt.expectNilErr {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				if tt.expectedErrCode != codes.OK {
					s, ok := status.FromError(err)
					if assert.True(t, ok, "Error should be a gRPC status error") {
						assert.Equal(t, tt.expectedErrCode, s.Code(), fmt.Sprintf("Expected error code %s, got %s (err: %v)", tt.expectedErrCode, s.Code(), err))
					}
				}
				if tt.expectSpecificErr != nil {
					assert.ErrorContains(t, err, tt.expectSpecificErr.Error())
				}
			}
			mockOrbCl.AssertExpectations(t)
		})
	}
}
