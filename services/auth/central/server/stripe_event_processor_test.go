package main

import (
	"context"
	"testing"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	authpb "github.com/augmentcode/augment/services/auth/central/server/auth"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	stripe_event "github.com/augmentcode/augment/services/auth/stripe_webhook/stripe_event"
	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func TestStripeEventProcessorIntegration(t *testing.T) {
	// Set up BigTable fixture
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	// Create a context
	ctx := context.Background()

	// Create a DAO factory with the BigTable fixture
	daoFactory := NewDAOFactory(bigtableFixture.Table)

	// Create a mock Stripe client
	mockStripeClient := NewMockStripeClient()

	// Create a mock Orb client
	mockOrbClient := orb.NewMockOrbClient()

	// Create a mock feature flag handler
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create auth servicer for API testing
	mockTenantWatcherClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			{
				Id:             "test-tenant-id",
				Name:           "test-tenant",
				ShardNamespace: "test.com",
				Cloud:          "test-cloud",
				Tier:           tw_pb.TenantTier_PROFESSIONAL,
			},
			{
				Id:             "test-community-tenant-id",
				Name:           "test-community-tenant",
				ShardNamespace: "test.com",
				Cloud:          "test-cloud",
				Tier:           tw_pb.TenantTier_COMMUNITY,
			},
		},
	}
	tenantMap := NewTenantMap(daoFactory, mockTenantWatcherClient, "test.com", featureFlagHandle, NewMockAsyncOpsPublisher(), audit.NewDefaultAuditLogger())
	authServicer := NewAuthGrpcServer(
		featureFlagHandle,
		daoFactory,
		tenantMap,
		audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		&StripeConfig{Enabled: false},
		&OrbConfig{Enabled: false},
		mockStripeClient,
	)
	authServicer.orbClient = mockOrbClient

	// Create the Stripe event processor
	processor := &StripeEventProcessor{
		config:            &Config{},
		daoFactory:        daoFactory,
		stripeClient:      mockStripeClient,
		featureFlagHandle: featureFlagHandle,
		orbClient:         mockOrbClient,
	}

	// Helper function to create authorized context
	createAuthContext := func(tenantID string) context.Context {
		return auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
			TenantID:   tenantID,
			TenantName: "test-tenant",
			Scope:      []string{tokenexchangeproto.Scope_AUTH_R.String()},
		})
	}

	t.Run("Setup Intent Succeeded Event with Orb", func(t *testing.T) {
		// Create a test user with Orb billing method
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		orbSubscriptionID := "sub_" + uuid.New().String()
		orbCustomerID := "cus_orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:                userID,
			Email:             "<EMAIL>",
			StripeCustomerId:  stripeCustomerID,
			OrbCustomerId:     orbCustomerID,
			OrbSubscriptionId: orbSubscriptionID,
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create a subscription for the user with HasPaymentMethod = false
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription := &auth_entities.Subscription{
			SubscriptionId:    orbSubscriptionID,
			StripeCustomerId:  stripeCustomerID,
			PriceId:           "price_test",
			Status:            auth_entities.Subscription_ACTIVE,
			Seats:             5,
			CancelAtPeriodEnd: false,
			HasPaymentMethod:  false, // Initially false
			Owner:             &auth_entities.Subscription_UserId{UserId: userID},
			CreatedAt:         timestamppb.Now(),
			UpdatedAt:         timestamppb.Now(),
		}
		_, err = subscriptionDAO.Create(ctx, subscription)
		require.NoError(t, err, "Failed to create subscription")

		// Create a setup intent succeeded event
		setupIntentID := "seti_" + uuid.New().String()
		paymentMethodID := "pm_" + uuid.New().String()
		event := &stripe_event.StripeEvent{
			EventType:        "setup_intent.succeeded",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_SetupIntentEvent{
				SetupIntentEvent: &stripe_event.SetupIntentEvent{
					SetupIntentId:   setupIntentID,
					PaymentMethodId: paymentMethodID,
					Status:          "succeeded",
					CustomerId:      stripeCustomerID,
					Livemode:        false,
					Usage:           "off_session",
				},
			},
		}
		processor.orbClient.(*orb.MockOrbClient).On("UpdateCustomerBillingAddress", mock.Anything, mock.Anything, mock.Anything).Return(nil)

		// Process the event directly with the Orb-enabled processor
		err = processor.processStripeEvent(ctx, event)
		require.NoError(t, err, "Failed to process setup intent succeeded event")

		// Verify the user still exists
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err, "Failed to get user")
		assert.Equal(t, stripeCustomerID, updatedUser.StripeCustomerId, "User should have the correct Stripe customer ID")

		// Verify the subscription was updated with HasPaymentMethod = true
		updatedSubscription, err := subscriptionDAO.Get(ctx, orbSubscriptionID)
		require.NoError(t, err, "Failed to get updated subscription")
		assert.True(t, updatedSubscription.HasPaymentMethod, "Subscription should have HasPaymentMethod set to true")

		// Verify that the Orb client was called to update the customer billing address
		processor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateCustomerBillingAddress", mock.Anything, orbCustomerID, &orb.Address{
			Line1:      "123 Test St",
			City:       "Test City",
			State:      "Test State",
			PostalCode: "12345",
			Country:    "US",
		})
		processor.orbClient.(*orb.MockOrbClient).AssertNumberOfCalls(t, "UpdateCustomerBillingAddress", 1)
	})

	t.Run("Setup Intent with Unknown Customer", func(t *testing.T) {
		// Create a setup intent event with a customer ID that doesn't exist
		stripeCustomerID := "cus_nonexistent_" + uuid.New().String()
		setupIntentID := "seti_" + uuid.New().String()
		paymentMethodID := "pm_" + uuid.New().String()
		event := &stripe_event.StripeEvent{
			EventType:        "setup_intent.succeeded",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_SetupIntentEvent{
				SetupIntentEvent: &stripe_event.SetupIntentEvent{
					SetupIntentId:   setupIntentID,
					PaymentMethodId: paymentMethodID,
					Status:          "succeeded",
					CustomerId:      stripeCustomerID,
					Livemode:        false,
					Usage:           "off_session",
				},
			},
		}

		// Process the event directly
		err := processor.processStripeEvent(ctx, event)
		require.Error(t, err, "Should error when processing setup intent event with unknown customer")
	})

	t.Run("Setup Intent with Missing Customer ID", func(t *testing.T) {
		// Create a setup intent event with no customer ID
		setupIntentID := "seti_" + uuid.New().String()
		paymentMethodID := "pm_" + uuid.New().String()
		event := &stripe_event.StripeEvent{
			EventType: "setup_intent.succeeded",
			// No StripeCustomerId field
			Event: &stripe_event.StripeEvent_SetupIntentEvent{
				SetupIntentEvent: &stripe_event.SetupIntentEvent{
					SetupIntentId:   setupIntentID,
					PaymentMethodId: paymentMethodID,
					Status:          "succeeded",
					// No CustomerId field
					Livemode: false,
					Usage:    "off_session",
				},
			},
		}

		// Process the event directly
		err := processor.processStripeEvent(ctx, event)
		require.Error(t, err, "Should error when processing setup intent event with missing customer ID")
	})

	t.Run("Setup Intent Succeeded Removes Free Trial Abuse Suspensions", func(t *testing.T) {
		// Create a test user with a free trial abuse suspension
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		setupIntentID := "seti_" + uuid.New().String()
		paymentMethodID := "pm_" + uuid.New().String()
		tenantID := "test-tenant-id"

		// Create a suspension for free trial abuse
		suspension := &auth_entities.UserSuspension{
			SuspensionId:   uuid.New().String(),
			CreatedTime:    timestamppb.Now(),
			SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
			Evidence:       "Test suspension for free trial abuse",
		}

		// Create a user with the suspension
		user := &auth_entities.User{
			Id:               userID,
			Email:            "<EMAIL>",
			StripeCustomerId: stripeCustomerID,
			BillingMethod:    auth_entities.BillingMethod_BILLING_METHOD_ORB,
			Tenants:          []string{tenantID},
			Suspensions:      []*auth_entities.UserSuspension{suspension},
		}

		// Configure mock to return no payment method initially
		mockStripeClient.SetCustomerHasPaymentMethod(stripeCustomerID, false)

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user with suspension")

		authCtx := createAuthContext(tenantID)

		// Verify user is suspended before payment method setup
		getUserResp, err := authServicer.GetUser(authCtx, &authpb.GetUserRequest{
			UserId:   userID,
			TenantId: &tenantID,
		})
		require.NoError(t, err, "Failed to get user before payment setup")
		assert.NotEmpty(t, getUserResp.User.Suspensions, "User should have suspensions before payment setup")

		// Create a setup intent succeeded event
		event := &stripe_event.StripeEvent{
			EventType:        "setup_intent.succeeded",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_SetupIntentEvent{
				SetupIntentEvent: &stripe_event.SetupIntentEvent{
					SetupIntentId:   setupIntentID,
					PaymentMethodId: paymentMethodID,
					Status:          "succeeded",
					CustomerId:      stripeCustomerID,
					Livemode:        false,
					Usage:           "off_session",
				},
			},
		}

		processor.orbClient.(*orb.MockOrbClient).On("UpdateCustomerBillingAddress", mock.Anything, mock.Anything, mock.Anything).Return(nil)

		// Configure mock to return payment method exists after setup
		mockStripeClient.SetCustomerHasPaymentMethod(stripeCustomerID, true)

		// Process the event
		err = processor.processStripeEvent(ctx, event)
		require.NoError(t, err, "Failed to process setup intent succeeded event")

		// Verify user behavior after payment method setup - suspensions should be filtered out
		getUserResp, err = authServicer.GetUser(authCtx, &authpb.GetUserRequest{
			UserId:   userID,
			TenantId: &tenantID,
		})
		require.NoError(t, err, "Failed to get user after payment setup")
		assert.Empty(t, getUserResp.User.Suspensions, "User should have no effective suspensions after payment setup")
	})

	t.Run("Setup Intent Succeeded Removes Community Abuse Suspensions", func(t *testing.T) {
		// Create a test user with a community abuse suspension
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		setupIntentID := "seti_" + uuid.New().String()
		paymentMethodID := "pm_" + uuid.New().String()
		tenantID := "test-community-tenant-id"

		// Create a suspension for community abuse
		suspension := &auth_entities.UserSuspension{
			SuspensionId:   uuid.New().String(),
			CreatedTime:    timestamppb.Now(),
			SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_COMMUNITY_ABUSE,
			Evidence:       "Test suspension for community abuse",
		}

		// Create a user with the suspension
		user := &auth_entities.User{
			Id:               userID,
			Email:            "<EMAIL>",
			StripeCustomerId: stripeCustomerID,
			BillingMethod:    auth_entities.BillingMethod_BILLING_METHOD_ORB,
			Tenants:          []string{tenantID},
			Suspensions:      []*auth_entities.UserSuspension{suspension},
		}

		// Configure mock to return no payment method initially
		mockStripeClient.SetCustomerHasPaymentMethod(stripeCustomerID, false)

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user with suspension")

		authCtx := createAuthContext(tenantID)

		// Verify user is suspended before payment method setup
		getUserResp, err := authServicer.GetUser(authCtx, &authpb.GetUserRequest{
			UserId:   userID,
			TenantId: &tenantID,
		})
		require.NoError(t, err, "Failed to get user before payment setup")
		assert.NotEmpty(t, getUserResp.User.Suspensions, "User should have suspensions before payment setup")

		// Create a setup intent succeeded event
		event := &stripe_event.StripeEvent{
			EventType:        "setup_intent.succeeded",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_SetupIntentEvent{
				SetupIntentEvent: &stripe_event.SetupIntentEvent{
					SetupIntentId:   setupIntentID,
					PaymentMethodId: paymentMethodID,
					Status:          "succeeded",
					CustomerId:      stripeCustomerID,
					Livemode:        false,
					Usage:           "off_session",
				},
			},
		}

		processor.orbClient.(*orb.MockOrbClient).On("UpdateCustomerBillingAddress", mock.Anything, mock.Anything, mock.Anything).Return(nil)

		// Configure mock to return payment method exists after setup
		mockStripeClient.SetCustomerHasPaymentMethod(stripeCustomerID, true)

		// Process the event directly
		err = processor.processStripeEvent(ctx, event)
		require.NoError(t, err, "Failed to process setup intent succeeded event")

		// Verify user behavior after payment method setup - suspensions should be filtered out
		getUserResp, err = authServicer.GetUser(authCtx, &authpb.GetUserRequest{
			UserId:   userID,
			TenantId: &tenantID,
		})
		require.NoError(t, err, "Failed to get user after payment setup")
		assert.Empty(t, getUserResp.User.Suspensions, "User should have no effective suspensions after payment setup")
	})

	t.Run("Payment Method Detached Event with Orb", func(t *testing.T) {
		// Create a mock Orb client
		mockOrbClient := orb.NewMockOrbClient()

		// Create a processor with the Orb client
		processorWithOrb := &StripeEventProcessor{
			config:            &Config{},
			daoFactory:        daoFactory,
			stripeClient:      mockStripeClient,
			featureFlagHandle: featureFlagHandle,
			orbClient:         mockOrbClient,
		}

		// Create a test user with Orb billing method
		userID := uuid.New().String()
		stripeCustomerID := "cus_" + uuid.New().String()
		subscriptionID := "sub_" + uuid.New().String()

		user := &auth_entities.User{
			Id:                userID,
			Email:             "<EMAIL>",
			StripeCustomerId:  stripeCustomerID,
			SubscriptionId:    proto.String(subscriptionID),
			OrbSubscriptionId: subscriptionID, // Add OrbSubscriptionId
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create a subscription for the user with HasPaymentMethod = true
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription := &auth_entities.Subscription{
			SubscriptionId:    subscriptionID,
			StripeCustomerId:  stripeCustomerID,
			PriceId:           "price_test",
			Status:            auth_entities.Subscription_ACTIVE,
			Seats:             5,
			CancelAtPeriodEnd: false,
			HasPaymentMethod:  true, // Initially true
			Owner:             &auth_entities.Subscription_UserId{UserId: userID},
			CreatedAt:         timestamppb.Now(),
			UpdatedAt:         timestamppb.Now(),
		}
		_, err = subscriptionDAO.Create(ctx, subscription)
		require.NoError(t, err, "Failed to create subscription")

		// Create a custom mock Stripe client that returns false for HasPaymentMethod
		customMockStripeClient := NewMockStripeClient()
		// Copy all the subscriptions from the original mock client
		for id, sub := range mockStripeClient.subscriptions {
			customMockStripeClient.subscriptions[id] = sub
		}
		// Override HasPaymentMethod to always return false
		customMockStripeClient.SetCustomerHasPaymentMethod(stripeCustomerID, false)

		// Create a processor with the custom mock client
		processorWithOrb.stripeClient = customMockStripeClient

		// Create a payment method detached event
		paymentMethodID := "pm_" + uuid.New().String()
		event := &stripe_event.StripeEvent{
			EventType:        "payment_method.detached",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_PaymentMethodEvent{
				PaymentMethodEvent: &stripe_event.PaymentMethodEvent{
					PaymentMethodId: paymentMethodID,
				},
			},
		}

		// Process the event directly with the Orb-enabled processor
		err = processorWithOrb.processStripeEvent(ctx, event)
		require.NoError(t, err, "Failed to process payment method detached event")

		// Verify the user still exists
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err, "Failed to get user")
		assert.Equal(t, stripeCustomerID, updatedUser.StripeCustomerId, "User should have the correct Stripe customer ID")

		// Verify the subscription was updated with HasPaymentMethod = false
		updatedSubscription, err := subscriptionDAO.Get(ctx, subscriptionID)
		require.NoError(t, err, "Failed to get updated subscription")
		assert.False(t, updatedSubscription.HasPaymentMethod, "Subscription should have HasPaymentMethod set to false")
	})

	t.Run("Payment Method Detached with Unknown Customer", func(t *testing.T) {
		// Create a payment method detached event with a customer ID that doesn't exist
		stripeCustomerID := "cus_nonexistent_" + uuid.New().String()
		paymentMethodID := "pm_" + uuid.New().String()
		event := &stripe_event.StripeEvent{
			EventType:        "payment_method.detached",
			StripeCustomerId: proto.String(stripeCustomerID),
			Event: &stripe_event.StripeEvent_PaymentMethodEvent{
				PaymentMethodEvent: &stripe_event.PaymentMethodEvent{
					PaymentMethodId: paymentMethodID,
				},
			},
		}

		// Process the event directly
		err := processor.processStripeEvent(ctx, event)
		require.Error(t, err, "Should error when processing payment method detached event with unknown customer")
	})
}
