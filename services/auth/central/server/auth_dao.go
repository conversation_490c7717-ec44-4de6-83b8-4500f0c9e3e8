package main

import (
	"context"
	"crypto/sha256"
	"fmt"
	"math"
	"math/rand"
	"regexp"
	"strings"
	"time"

	bigtable "cloud.google.com/go/bigtable"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/proto"
)

const (
	valueColumnName = "value"
)

// compareAndSet compares a value in a row and sets it to a new value if the old value matches
// Returns true if the value was set, false otherwise
// Can also raise an exception if there is a connectivity problem with Bigtable
func compareAndSet(
	table *bigtable.Table,
	rowKey string,
	columnFamily string,
	columnName string,
	oldValue []byte,
	newValue []byte,
) (bool, error) {
	mut := bigtable.NewMutation()
	if oldValue != nil {
		var valueFilter bigtable.Filter
		filterLenLimit := 10240
		if len(oldValue) > filterLenLimit {
			valueFilter = bigtable.ValueFilter(regexp.QuoteMeta(string(oldValue[:filterLenLimit])) + "\\C*")
		} else {
			valueFilter = bigtable.ValueFilter(regexp.QuoteMeta(string(oldValue)))
		}
		filter := bigtable.ChainFilters(
			bigtable.FamilyFilter(columnFamily),
			bigtable.ColumnFilter(columnName),
			valueFilter,
		)
		mut.DeleteCellsInColumn(columnFamily, columnName)
		mut.Set(columnFamily, columnName, bigtable.Now(), newValue)
		condMut := bigtable.NewCondMutation(filter, mut, nil)
		var was_filter_match bool
		err := table.Apply(context.Background(), rowKey, condMut, bigtable.GetCondMutationResult(&was_filter_match))
		if err != nil {
			return false, err
		}
		return was_filter_match, nil
	} else {
		filter := bigtable.ChainFilters(
			bigtable.FamilyFilter(columnFamily),
			bigtable.ColumnFilter(columnName),
		)
		mut.Set(columnFamily, columnName, bigtable.Now(), newValue)
		condMut := bigtable.NewCondMutation(filter, nil, mut)
		var was_filter_match bool
		err := table.Apply(context.Background(), rowKey, condMut, bigtable.GetCondMutationResult(&was_filter_match))
		if err != nil {
			return false, err
		}
		return !was_filter_match, nil
	}
}

func DefaultRetry(retry int) float64 {
	if retry > 0 {
		delay := 0.01 * (math.Pow(1.2, float64(retry)) + rand.Float64())
		log.Info().Msgf("retrying after %v seconds", delay)
		time.Sleep(time.Duration(delay * float64(time.Second)))
	}
	return float64(time.Now().Unix())
}

// ProtoBigtableDAO handles persistence for objects in Bigtable
type ProtoBigtableDAO[T proto.Message] struct {
	table        *bigtable.Table
	columnFamily string
	rowCategory  string
	instantiateT func() T
}

func NewProtoBigtableDAO[T proto.Message](
	table *bigtable.Table,
	columnFamily string,
	rowCategory string,
	instantiateT func() T,
) *ProtoBigtableDAO[T] {
	return &ProtoBigtableDAO[T]{
		table:        table,
		columnFamily: columnFamily,
		rowCategory:  rowCategory,
		instantiateT: instantiateT,
	}
}

func (d *ProtoBigtableDAO[T]) create(ctx context.Context, rowKey string, value T) (T, error) {
	mut := bigtable.NewMutation()
	data, err := proto.Marshal(value)
	if err != nil {
		var zero T
		return zero, fmt.Errorf("failed to marshal proto: %v", err)
	}

	mut.Set(d.columnFamily, valueColumnName, bigtable.Now(), data)
	if err := d.table.Apply(ctx, rowKey, mut); err != nil {
		var zero T
		return zero, fmt.Errorf("failed to apply mutation: %v", err)
	}

	return value, nil
}

func (d *ProtoBigtableDAO[T]) delete(ctx context.Context, rowKey string) error {
	mut := bigtable.NewMutation()
	mut.DeleteRow()
	return d.table.Apply(ctx, rowKey, mut)
}

func (d *ProtoBigtableDAO[T]) batchDelete(ctx context.Context, rowKeys []string) error {
	deleteRowMutation := bigtable.NewMutation()
	deleteRowMutation.DeleteRow()

	// Chunking batch delete to avoid bigtable limits
	const chunkSize = 1000
	for i := 0; i < len(rowKeys); i += chunkSize {
		end := i + chunkSize
		if end > len(rowKeys) {
			end = len(rowKeys)
		}
		chunk := rowKeys[i:end]

		mutations := make([]*bigtable.Mutation, len(chunk))
		for j := range chunk {
			mutations[j] = deleteRowMutation
		}

		errs, err := d.table.ApplyBulk(ctx, chunk, mutations)
		if errs != nil {
			log.Error().Err(errs[0]).Msgf("Failed to delete %d rows", len(errs))
			return fmt.Errorf("failed to delete %d rows: %v", len(errs), errs[0])
		}

		if err != nil {
			log.Error().Err(err).Msg("Failed to delete rows")
			return fmt.Errorf("failed to delete rows: %v", err)
		}
	}
	return nil
}

func (d *ProtoBigtableDAO[T]) batchGet(ctx context.Context, rowKeys []string, rowKeyFn func(string) string, batchSize int, callback func(T) bool) error {
	var unmarshalErr error

	handler := func(row bigtable.Row) bool {
		items := row[d.columnFamily]
		if len(items) != 0 {
			value := d.instantiateT()
			if err := proto.Unmarshal(items[0].Value, value); err != nil {
				unmarshalErr = fmt.Errorf("failed to unmarshal proto: %v", err)
				return false // Stop iteration on error
			}

			return callback(value)
		}
		return true
	}

	rowList := bigtable.RowList{}

	for _, key := range rowKeys {
		rowList = append(rowList, rowKeyFn(key))
	}

	start := 0
	size := len(rowList)

	for start < size {
		end := min(start+batchSize, size)
		err := d.table.ReadRows(
			ctx,
			rowList[start:end],
			handler,
		)
		if err != nil {
			return fmt.Errorf("failed to read rows: %v", err)
		}
		if unmarshalErr != nil {
			return unmarshalErr
		}
		start = end
	}

	return nil
}

func (d *ProtoBigtableDAO[T]) update(ctx context.Context, rowKey string, value T) (T, error) {
	return d.create(ctx, rowKey, value)
}

func (d *ProtoBigtableDAO[T]) compareAndSet(ctx context.Context, rowKey string, rawValue []byte, value T) (bool, error) {
	serializedNewValue, err := proto.Marshal(value)
	if err != nil {
		return false, fmt.Errorf("failed to marshal proto: %v", err)
	}

	return compareAndSet(
		d.table,
		rowKey,
		d.columnFamily,
		valueColumnName,
		rawValue,
		serializedNewValue,
	)
}

func (d *ProtoBigtableDAO[T]) get(ctx context.Context, rowKey string) (T, error) {
	row, err := d.table.ReadRow(ctx, rowKey)
	if err != nil {
		var zero T
		return zero, fmt.Errorf("failed to read row: %v", err)
	}
	if row == nil {
		var zero T
		return zero, nil
	}

	items := row[d.columnFamily]
	if len(items) == 0 {
		var zero T
		return zero, nil
	}

	value := d.instantiateT()
	if err := proto.Unmarshal(items[0].Value, value); err != nil {
		var zero T
		return zero, fmt.Errorf("failed to unmarshal proto: %v", err)
	}

	return value, nil
}

func (d *ProtoBigtableDAO[T]) getWithRaw(ctx context.Context, rowKey string) (T, []byte, error) {
	row, err := d.table.ReadRow(ctx, rowKey)
	if err != nil {
		var zero T
		return zero, nil, fmt.Errorf("failed to read row: %v", err)
	}
	if row == nil {
		var zero T
		return zero, nil, nil
	}

	items := row[d.columnFamily]
	if len(items) == 0 {
		var zero T
		return zero, nil, nil
	}

	value := d.instantiateT()
	if err := proto.Unmarshal(items[0].Value, value); err != nil {
		var zero T
		return zero, nil, fmt.Errorf("failed to unmarshal proto: %v", err)
	}

	return value, items[0].Value, nil
}

func (d *ProtoBigtableDAO[T]) findAllByRegex(ctx context.Context, regex string, callback func(T) bool) error {
	var unmarshalErr error

	handler := func(row bigtable.Row) bool {
		items := row[d.columnFamily]
		if len(items) != 0 {
			value := d.instantiateT()
			if err := proto.Unmarshal(items[0].Value, value); err != nil {
				unmarshalErr = fmt.Errorf("failed to unmarshal proto: %v", err)
				return false // Stop iteration on error
			}

			return callback(value)
		}
		return true
	}

	err := d.table.ReadRows(
		ctx,
		bigtable.PrefixRange(""),
		handler,
		bigtable.RowFilter(bigtable.RowKeyFilter(regex)),
	)
	if err != nil {
		return fmt.Errorf("failed to read rows: %v", err)
	}
	if unmarshalErr != nil {
		return unmarshalErr
	}

	return nil
}

func successor(key string) string {
	// Find the last byte that is not 0xFF and increment it.
	for key != "" && key[len(key)-1] == 0xFF {
		key = key[:len(key)-1]
	}
	if key == "" {
		return ""
	}
	return key[:len(key)-1] + string(key[len(key)-1]+1)
}

func (d *ProtoBigtableDAO[T]) findAllByPrefix(ctx context.Context, prefix string, startKey string, callback func(T) bool) error {
	var unmarshalErr error

	if !strings.HasPrefix(startKey, prefix) {
		panic(fmt.Sprintf("startKey %s must have prefix %s", startKey, prefix))
	}

	handler := func(row bigtable.Row) bool {
		items := row[d.columnFamily]
		if len(items) != 0 {
			value := d.instantiateT()
			if err := proto.Unmarshal(items[0].Value, value); err != nil {
				unmarshalErr = fmt.Errorf("failed to unmarshal proto: %v", err)
				return false // Stop iteration on error
			}

			return callback(value)
		}
		return true
	}

	err := d.table.ReadRows(
		ctx,
		bigtable.NewClosedOpenRange(startKey, successor(prefix)),
		handler,
	)
	if err != nil {
		return fmt.Errorf("failed to read rows: %v", err)
	}
	if unmarshalErr != nil {
		return unmarshalErr
	}

	return nil
}

// tryUpdate updates the value in rowKey if the updateFn returns true.
//
// Returns the latest value read. If rowKey or the value in rowKey doesn't exist
// then returns nil.
//
// This mechanism exists because the underlying Bigtable storage doesn't support
// partial updates. Without this retry mechanism, concurrent updates could result
// in lost writes. This is not primarily an optimistic locking pattern, but rather
// a way to ensure no updates are dropped during concurrent modifications.
//
// If there is a conflict (i.e. the value was updated by someone else in between
// the read and the write), then updateFn will be called again with the
// latest version of the value. This will happen up to ten times.
// If the updateFn returns false, then the value will not be updated.
func (d *ProtoBigtableDAO[T]) tryUpdate(ctx context.Context, rowKey string, updateFn func(value T) bool, onRetry func(retry int) float64) (T, error) {
	for retry := 0; retry < 10; retry++ {
		_ = onRetry(retry)

		value, valueRaw, err := d.getWithRaw(ctx, rowKey)
		if err != nil || valueRaw == nil {
			var zero T
			return zero, err
		}

		if !updateFn(value) {
			return value, nil
		}

		result, err := d.compareAndSet(ctx, rowKey, valueRaw, value)
		if err != nil {
			var zero T
			return zero, err
		}
		if result {
			return value, nil
		}
	}

	var zero T
	return zero, fmt.Errorf("Update failed on conflict")
}

// Create the value in rowKey if it doesn't exist yet. Returns true if value was written and false
// otherwise.
func (d *ProtoBigtableDAO[T]) tryCreate(ctx context.Context, rowKey string, value T) (bool, error) {
	result, err := d.compareAndSet(ctx, rowKey, nil, value)
	if err != nil {
		return false, err
	}
	return result, nil
}

func (d *ProtoBigtableDAO[T]) findAll(ctx context.Context, callback func(T) bool) error {
	prefix := fmt.Sprintf("%s#", d.rowCategory)
	return d.findAllByPrefix(ctx, prefix, prefix, callback)
}

func (d *ProtoBigtableDAO[T]) findAllWithStartKey(ctx context.Context, startKey string, callback func(T) bool) error {
	prefix := fmt.Sprintf("%s#", d.rowCategory)
	return d.findAllByPrefix(ctx, prefix, startKey, callback)
}

// Persistence for objects scoped to a tenant

type ProtoBigtableTenantDAO[T proto.Message] struct {
	*ProtoBigtableDAO[T]
	tenant string
}

func NewProtoBigtableTenantDAO[T proto.Message](
	table *bigtable.Table,
	columnFamily string,
	rowCategory string,
	instantiateT func() T,
	tenant string,
) *ProtoBigtableTenantDAO[T] {
	return &ProtoBigtableTenantDAO[T]{
		ProtoBigtableDAO: NewProtoBigtableDAO[T](
			table,
			columnFamily,
			rowCategory,
			instantiateT,
		),
		tenant: tenant,
	}
}

func (d *ProtoBigtableTenantDAO[T]) rowKeyPrefix() string {
	if d.tenant == "" {
		panic("tenant must be set")
	}
	return fmt.Sprintf("%s#%s", d.rowCategory, d.tenant)
}

// UserTenantMappingDAO handles UserTenantMapping operations
type UserTenantMappingDAO struct {
	*ProtoBigtableTenantDAO[*auth_entities.UserTenantMapping]
}

func NewUserTenantMappingDAO(table *bigtable.Table, tenant string) *UserTenantMappingDAO {
	return &UserTenantMappingDAO{
		ProtoBigtableTenantDAO: NewProtoBigtableTenantDAO[*auth_entities.UserTenantMapping](
			table,
			"UserTenantMapping",
			"UserTenantMapping",
			func() *auth_entities.UserTenantMapping { return &auth_entities.UserTenantMapping{} },
			tenant,
		),
	}
}

func (d *UserTenantMappingDAO) rowKey(userID string) string {
	return fmt.Sprintf("%s#%s", d.rowKeyPrefix(), userID)
}

func (d *UserTenantMappingDAO) Instantiate() *auth_entities.UserTenantMapping {
	return &auth_entities.UserTenantMapping{}
}

/*
 * callback returns true to continue iterating, false to stop iterating
 */
func (d *UserTenantMappingDAO) FindAll(ctx context.Context, callback func(*auth_entities.UserTenantMapping) bool) error {
	prefix := d.rowKeyPrefix()
	return d.findAllByPrefix(ctx, prefix, prefix, callback)
}

func (d *UserTenantMappingDAO) GetByUser(ctx context.Context, userID string) (*auth_entities.UserTenantMapping, error) {
	return d.get(ctx, d.rowKey(userID))
}

func (d *UserTenantMappingDAO) Create(ctx context.Context, mapping *auth_entities.UserTenantMapping) (*auth_entities.UserTenantMapping, error) {
	return d.create(ctx, d.rowKey(mapping.GetUserId()), mapping)
}

func (d *UserTenantMappingDAO) Update(ctx context.Context, mapping *auth_entities.UserTenantMapping) (*auth_entities.UserTenantMapping, error) {
	return d.update(ctx, d.rowKey(mapping.GetUserId()), mapping)
}

func (d *UserTenantMappingDAO) Delete(ctx context.Context, userID string) error {
	return d.delete(ctx, d.rowKey(userID))
}

/*
 * callback returns true to continue iterating, false to stop iterating
 */
func (d *UserTenantMappingDAO) ListMappingsForAllTenants(ctx context.Context, callback func(*auth_entities.UserTenantMapping) bool) error {
	prefix := fmt.Sprintf("%s#", d.rowCategory)
	return d.findAllByPrefix(ctx, prefix, prefix, callback)
}

// FindAllPaginated returns a paginated list of user tenant mappings across all tenants
// startKey is the exclusive start key for pagination (empty string for first page)
// pageSize is the maximum number of user tenant mappings to return
// Returns the user tenant mappings, the next start key for pagination, and an error
func (d *UserTenantMappingDAO) FindAllPaginated(ctx context.Context, startKey string, pageSize uint32) ([]*auth_entities.UserTenantMapping, string, error) {
	prefix := fmt.Sprintf("%s#", d.rowCategory)

	// Create the appropriate row range for pagination
	var rowRange bigtable.RowRange
	if startKey != "" {
		// If startKey is provided, start after that key but still within the prefix
		rowRange = bigtable.NewRange(startKey, prefix+"\xFF")
	} else {
		// For the first page, use the prefix range
		rowRange = bigtable.PrefixRange(prefix)
	}

	// We fetch one extra item to determine if there are more results
	limit := int64(pageSize + 1)

	var results []*auth_entities.UserTenantMapping
	var rowKeys []string
	var unmarshalErr error

	handler := func(row bigtable.Row) bool {
		items := row[d.columnFamily]
		if len(items) != 0 {
			value := d.instantiateT()
			if err := proto.Unmarshal(items[0].Value, value); err != nil {
				unmarshalErr = fmt.Errorf("failed to unmarshal proto: %v", err)
				return false // Stop iteration on error
			}

			results = append(results, value)
			rowKeys = append(rowKeys, row.Key())
		}
		return true
	}

	// Read rows with the appropriate range and limit
	err := d.table.ReadRows(
		ctx,
		rowRange,
		handler,
		bigtable.LimitRows(limit),
	)
	if err != nil {
		return nil, "", fmt.Errorf("failed to read rows: %v", err)
	}
	if unmarshalErr != nil {
		return nil, "", unmarshalErr
	}

	// Check if there are more results
	hasMore := len(results) > int(pageSize)

	// Determine the next start key
	nextStartKey := ""
	if hasMore {
		// Use the key of the next item (the extra item we fetched) as the next start key
		nextStartKey = rowKeys[pageSize]
		// Remove the extra item
		results = results[:pageSize]
	}

	return results, nextStartKey, nil
}

// UserDAO handles User operations
type UserDAO struct {
	*ProtoBigtableDAO[*auth_entities.User]
}

func NewUserDAO(table *bigtable.Table) *UserDAO {
	return &UserDAO{
		ProtoBigtableDAO: NewProtoBigtableDAO[*auth_entities.User](
			table,
			"User",
			"User",
			func() *auth_entities.User { return &auth_entities.User{} },
		),
	}
}

func (d *UserDAO) rowKey(userID string) string {
	return fmt.Sprintf("%s#%s", d.rowCategory, userID)
}

func (d *UserDAO) Instantiate() *auth_entities.User {
	return &auth_entities.User{}
}

func (d *UserDAO) Create(ctx context.Context, user *auth_entities.User) (*auth_entities.User, error) {
	_, err := d.tryCreate(ctx, d.rowKey(user.GetId()), user)
	if err != nil {
		return nil, err
	}
	return d.Get(ctx, user.GetId())
}

func (d *UserDAO) Get(ctx context.Context, userID string) (*auth_entities.User, error) {
	return d.get(ctx, d.rowKey(userID))
}

func (d *UserDAO) BatchGet(ctx context.Context, userIDs []string, batchSize int, callback func(*auth_entities.User) bool) error {
	return d.batchGet(ctx, userIDs, d.rowKey, batchSize, callback)
}

func (d *UserDAO) TryUpdate(ctx context.Context, userID string, updateFn func(user *auth_entities.User) bool, onRetry func(retry int) float64) (*auth_entities.User, error) {
	return d.tryUpdate(ctx, d.rowKey(userID), updateFn, onRetry)
}

/*
 * callback returns true to continue iterating, false to stop iterating
 */
func (d *UserDAO) FindAll(ctx context.Context, callback func(*auth_entities.User) bool) error {
	return d.findAll(ctx, callback)
}

func (d *UserDAO) FindAllWithStartKey(ctx context.Context, startUserID string, callback func(*auth_entities.User) bool) error {
	return d.findAllWithStartKey(ctx, d.rowKey(startUserID), callback)
}

func (d *UserDAO) UpdateNonce(ctx context.Context, userID string) error {
	user, err := d.TryUpdate(ctx, userID, func(user *auth_entities.User) bool {
		nonce := uint64(rand.Int63())
		user.Nonce = nonce
		return true
	}, DefaultRetry)
	if err != nil {
		return err
	}
	if user == nil {
		log.Warn().Str("user_id", userID).Msgf("User not found when updating nonce")
	}
	return nil
}

func (d *UserDAO) GetSubscriptionID(ctx context.Context, userID string) (string, error) {
	user, err := d.Get(ctx, userID)
	if err != nil {
		return "", err
	}
	return user.GetSubscriptionId(), nil
}

func (d *UserDAO) GetOrbSubscriptionID(ctx context.Context, userID string) (string, error) {
	user, err := d.Get(ctx, userID)
	if err != nil {
		return "", err
	}
	return user.GetOrbSubscriptionId(), nil
}

// TokenHashDAO handles TokenHash operations
type TokenHashDAO struct {
	*ProtoBigtableDAO[*auth_entities.TokenHash]
}

func NewTokenHashDAO(table *bigtable.Table) *TokenHashDAO {
	return &TokenHashDAO{
		ProtoBigtableDAO: NewProtoBigtableDAO[*auth_entities.TokenHash](
			table,
			"TokenHash",
			"TokenHash",
			func() *auth_entities.TokenHash { return &auth_entities.TokenHash{} },
		),
	}
}

func (d *TokenHashDAO) rowKey(tokenHash string) string {
	return fmt.Sprintf("%s#%s", d.rowCategory, tokenHash)
}

func (d *TokenHashDAO) Instantiate() *auth_entities.TokenHash {
	return &auth_entities.TokenHash{}
}

func (d *TokenHashDAO) Create(ctx context.Context, tokenHash *auth_entities.TokenHash) (*auth_entities.TokenHash, error) {
	return d.create(ctx, d.rowKey(tokenHash.GetHash()), tokenHash)
}

func (d *TokenHashDAO) Get(ctx context.Context, hash string) (*auth_entities.TokenHash, error) {
	return d.get(ctx, d.rowKey(hash))
}

func (d *TokenHashDAO) Delete(ctx context.Context, tokenHash string) error {
	return d.delete(ctx, d.rowKey(tokenHash))
}

func (d *TokenHashDAO) BatchDelete(ctx context.Context, tokenHash []string) error {
	rowKeys := make([]string, len(tokenHash))
	for i, hash := range tokenHash {
		rowKeys[i] = d.rowKey(hash)
	}
	return d.batchDelete(ctx, rowKeys)
}

/*
 * callback returns true to continue iterating, false to stop iterating
 */
func (d *TokenHashDAO) FindAll(ctx context.Context, callback func(*auth_entities.TokenHash) bool) error {
	return d.findAll(ctx, callback)
}

// CodeDAO handles Code operations
type CodeDAO struct {
	*ProtoBigtableDAO[*auth_entities.Code]
}

func NewCodeDAO(table *bigtable.Table) *CodeDAO {
	return &CodeDAO{
		ProtoBigtableDAO: NewProtoBigtableDAO[*auth_entities.Code](
			table,
			"Code",
			"Code",
			func() *auth_entities.Code { return &auth_entities.Code{} },
		),
	}
}

func (d *CodeDAO) rowKey(code string) string {
	return fmt.Sprintf("%s#%s", d.rowCategory, code)
}

func (d *CodeDAO) Instantiate() *auth_entities.Code {
	return &auth_entities.Code{}
}

func (d *CodeDAO) Create(ctx context.Context, code *auth_entities.Code) (*auth_entities.Code, error) {
	return d.create(ctx, d.rowKey(code.GetCode()), code)
}

func (d *CodeDAO) Get(ctx context.Context, code string) (*auth_entities.Code, error) {
	return d.get(ctx, d.rowKey(code))
}

func (d *CodeDAO) Update(ctx context.Context, code *auth_entities.Code) (*auth_entities.Code, error) {
	return d.update(ctx, d.rowKey(code.GetCode()), code)
}

// TermsApprovalDAO handles TermsApproval operations
type TermsApprovalDAO struct {
	*ProtoBigtableDAO[*auth_entities.TermsApproval]
}

func NewTermsApprovalDAO(table *bigtable.Table) *TermsApprovalDAO {
	return &TermsApprovalDAO{
		ProtoBigtableDAO: NewProtoBigtableDAO[*auth_entities.TermsApproval](
			table,
			"TermsApproval",
			"TermsApproval",
			func() *auth_entities.TermsApproval { return &auth_entities.TermsApproval{} },
		),
	}
}

func (d *TermsApprovalDAO) rowKey(email, revision string) string {
	emailHash := sha256.Sum256([]byte(email))
	return fmt.Sprintf("%s#%x#%s", d.rowCategory, emailHash, revision)
}

func (d *TermsApprovalDAO) Instantiate() *auth_entities.TermsApproval {
	return &auth_entities.TermsApproval{}
}

func (d *TermsApprovalDAO) Create(ctx context.Context, terms *auth_entities.TermsApproval) (*auth_entities.TermsApproval, error) {
	return d.create(ctx, d.rowKey(terms.GetEmail(), terms.GetRevision()), terms)
}

func (d *TermsApprovalDAO) Get(ctx context.Context, email, revision string) (*auth_entities.TermsApproval, error) {
	return d.get(ctx, d.rowKey(email, revision))
}

// TenantCreationDAO handles TenantCreation operations
type TenantCreationDAO struct {
	*ProtoBigtableDAO[*auth_entities.TenantCreation]
}

func NewTenantCreationDAO(table *bigtable.Table) *TenantCreationDAO {
	return &TenantCreationDAO{
		ProtoBigtableDAO: NewProtoBigtableDAO[*auth_entities.TenantCreation](
			table,
			"TenantCreation",
			"TenantCreation",
			func() *auth_entities.TenantCreation { return &auth_entities.TenantCreation{} },
		),
	}
}

func (d *TenantCreationDAO) rowKey(id string) string {
	return fmt.Sprintf("%s#%s", d.rowCategory, id)
}

func (d *TenantCreationDAO) Instantiate() *auth_entities.TenantCreation {
	return &auth_entities.TenantCreation{}
}

func (d *TenantCreationDAO) Create(ctx context.Context, tenantCreation *auth_entities.TenantCreation) (*auth_entities.TenantCreation, error) {
	return d.create(ctx, d.rowKey(tenantCreation.GetId()), tenantCreation)
}

func (d *TenantCreationDAO) Get(ctx context.Context, id string) (*auth_entities.TenantCreation, error) {
	return d.get(ctx, d.rowKey(id))
}

func (d *TenantCreationDAO) Update(ctx context.Context, tenantCreation *auth_entities.TenantCreation) (*auth_entities.TenantCreation, error) {
	return d.update(ctx, d.rowKey(tenantCreation.GetId()), tenantCreation)
}

// TenantSubscriptionMappingDAO handles TenantSubscriptionMapping operations
type TenantSubscriptionMappingDAO struct {
	*ProtoBigtableDAO[*auth_entities.TenantSubscriptionMapping]
}

func NewTenantSubscriptionMappingDAO(table *bigtable.Table) *TenantSubscriptionMappingDAO {
	return &TenantSubscriptionMappingDAO{
		ProtoBigtableDAO: NewProtoBigtableDAO[*auth_entities.TenantSubscriptionMapping](
			table,
			"TenantSubscriptionMapping",
			"TenantSubscriptionMapping",
			func() *auth_entities.TenantSubscriptionMapping { return &auth_entities.TenantSubscriptionMapping{} },
		),
	}
}

func (d *TenantSubscriptionMappingDAO) rowKey(tenantID string) string {
	return fmt.Sprintf("%s#%s", d.rowCategory, tenantID)
}

func (d *TenantSubscriptionMappingDAO) Instantiate() *auth_entities.TenantSubscriptionMapping {
	return &auth_entities.TenantSubscriptionMapping{}
}

func (d *TenantSubscriptionMappingDAO) Get(ctx context.Context, tenantID string) (*auth_entities.TenantSubscriptionMapping, error) {
	return d.get(ctx, d.rowKey(tenantID))
}

func (d *TenantSubscriptionMappingDAO) Create(ctx context.Context, mapping *auth_entities.TenantSubscriptionMapping) (*auth_entities.TenantSubscriptionMapping, error) {
	return d.create(ctx, d.rowKey(mapping.GetTenantId()), mapping)
}

func (d *TenantSubscriptionMappingDAO) Update(ctx context.Context, mapping *auth_entities.TenantSubscriptionMapping) (*auth_entities.TenantSubscriptionMapping, error) {
	return d.update(ctx, d.rowKey(mapping.GetTenantId()), mapping)
}

func (d *TenantSubscriptionMappingDAO) TryUpdate(ctx context.Context, tenantID string, updateFn func(mapping *auth_entities.TenantSubscriptionMapping) bool, onRetry func(retry int) float64) (*auth_entities.TenantSubscriptionMapping, error) {
	return d.tryUpdate(ctx, d.rowKey(tenantID), updateFn, onRetry)
}

func (d *TenantSubscriptionMappingDAO) Delete(ctx context.Context, tenantID string) error {
	return d.delete(ctx, d.rowKey(tenantID))
}

/*
 * callback returns true to continue iterating, false to stop iterating
 */
func (d *TenantSubscriptionMappingDAO) FindAll(ctx context.Context, callback func(*auth_entities.TenantSubscriptionMapping) bool) error {
	return d.findAll(ctx, callback)
}

// FindAllPaginated returns a paginated list of tenant subscription mappings
// startKey is the exclusive start key for pagination (empty string for first page)
// pageSize is the maximum number of tenant subscription mappings to return
// Returns the tenant subscription mappings, the next start key for pagination, and an error
func (d *TenantSubscriptionMappingDAO) FindAllPaginated(ctx context.Context, startKey string, pageSize uint32) ([]*auth_entities.TenantSubscriptionMapping, string, error) {
	prefix := fmt.Sprintf("%s#", d.rowCategory)

	// Create the appropriate row range for pagination
	var rowRange bigtable.RowRange
	if startKey != "" {
		// If startKey is provided, start after that key but still within the prefix
		rowRange = bigtable.NewRange(startKey, prefix+"\xFF")
	} else {
		// For the first page, use the prefix range
		rowRange = bigtable.PrefixRange(prefix)
	}

	// We fetch one extra item to determine if there are more results
	limit := int64(pageSize + 1)

	var results []*auth_entities.TenantSubscriptionMapping
	var rowKeys []string
	var unmarshalErr error

	handler := func(row bigtable.Row) bool {
		items := row[d.columnFamily]
		if len(items) != 0 {
			value := d.instantiateT()
			if err := proto.Unmarshal(items[0].Value, value); err != nil {
				unmarshalErr = fmt.Errorf("failed to unmarshal proto: %v", err)
				return false // Stop iteration on error
			}

			results = append(results, value)
			rowKeys = append(rowKeys, row.Key())
		}
		return true
	}

	// Read rows with the appropriate range and limit
	err := d.table.ReadRows(
		ctx,
		rowRange,
		handler,
		bigtable.LimitRows(limit),
	)
	if err != nil {
		return nil, "", fmt.Errorf("failed to read rows: %v", err)
	}
	if unmarshalErr != nil {
		return nil, "", unmarshalErr
	}

	// Check if there are more results
	hasMore := len(results) > int(pageSize)

	// Determine the next start key
	nextStartKey := ""
	if hasMore {
		// Use the key of the next item (the extra item we fetched) as the next start key
		nextStartKey = rowKeys[pageSize]
		// Remove the extra item
		results = results[:pageSize]
	}

	return results, nextStartKey, nil
}

// SubscriptionDAO handles Subscription operations
type SubscriptionDAO struct {
	*ProtoBigtableDAO[*auth_entities.Subscription]
}

func NewSubscriptionDAO(table *bigtable.Table) *SubscriptionDAO {
	return &SubscriptionDAO{
		ProtoBigtableDAO: NewProtoBigtableDAO[*auth_entities.Subscription](
			table,
			"Subscription",
			"Subscription",
			func() *auth_entities.Subscription { return &auth_entities.Subscription{} },
		),
	}
}

func (d *SubscriptionDAO) rowKey(subscriptionID string) string {
	return fmt.Sprintf("%s#%s", d.rowCategory, subscriptionID)
}

func (d *SubscriptionDAO) Instantiate() *auth_entities.Subscription {
	return &auth_entities.Subscription{}
}

func (d *SubscriptionDAO) Create(ctx context.Context, subscription *auth_entities.Subscription) (*auth_entities.Subscription, error) {
	return d.create(ctx, d.rowKey(subscription.GetSubscriptionId()), subscription)
}

func (d *SubscriptionDAO) Get(ctx context.Context, subscriptionID string) (*auth_entities.Subscription, error) {
	return d.get(ctx, d.rowKey(subscriptionID))
}

func (d *SubscriptionDAO) Update(ctx context.Context, subscription *auth_entities.Subscription) (*auth_entities.Subscription, error) {
	return d.update(ctx, d.rowKey(subscription.GetSubscriptionId()), subscription)
}

func (d *SubscriptionDAO) TryUpdate(ctx context.Context, subscriptionID string, updateFn func(subscription *auth_entities.Subscription) bool, onRetry func(retry int) float64) (*auth_entities.Subscription, error) {
	return d.tryUpdate(ctx, d.rowKey(subscriptionID), updateFn, onRetry)
}

func (d *SubscriptionDAO) Delete(ctx context.Context, subscriptionID string) error {
	return d.delete(ctx, d.rowKey(subscriptionID))
}

/*
 * callback returns true to continue iterating, false to stop iterating
 */
func (d *SubscriptionDAO) FindAll(ctx context.Context, callback func(*auth_entities.Subscription) bool) error {
	return d.findAll(ctx, callback)
}

// callback returns true to continue iterating, false to stop iterating
func (d *SubscriptionDAO) FindAllWithStartKey(ctx context.Context, startKey string, callback func(*auth_entities.Subscription) bool) error {
	return d.findAllWithStartKey(ctx, d.rowKey(startKey), callback)
}

// FindAllPaginated returns a paginated list of subscriptions
// startKey is the exclusive start key for pagination (empty string for first page)
// pageSize is the maximum number of subscriptions to return
// Returns the subscriptions, the next start key for pagination, and an error
func (d *SubscriptionDAO) FindAllPaginated(ctx context.Context, startKey string, pageSize uint32) ([]*auth_entities.Subscription, string, error) {
	prefix := fmt.Sprintf("%s#", d.rowCategory)

	// Create the appropriate row range for pagination
	var rowRange bigtable.RowRange
	if startKey != "" {
		// If startKey is provided, start after that key but still within the prefix
		rowRange = bigtable.NewRange(startKey, prefix+"\xFF")
	} else {
		// For the first page, use the prefix range
		rowRange = bigtable.PrefixRange(prefix)
	}

	// We'll fetch pageSize + 1 to determine if there are more results
	limit := int64(pageSize + 1)

	var results []*auth_entities.Subscription
	var rowKeys []string
	var unmarshalErr error

	handler := func(row bigtable.Row) bool {
		// Stop if we've reached our limit
		if len(results) >= int(pageSize+1) {
			return false
		}

		items := row[d.columnFamily]
		if len(items) != 0 {
			value := d.instantiateT()
			if err := proto.Unmarshal(items[0].Value, value); err != nil {
				unmarshalErr = fmt.Errorf("failed to unmarshal proto: %v", err)
				return false // Stop iteration on error
			}

			results = append(results, value)
			rowKeys = append(rowKeys, row.Key())
		}
		return true
	}

	// Read rows with the appropriate range and limit
	err := d.table.ReadRows(
		ctx,
		rowRange,
		handler,
		bigtable.LimitRows(limit),
	)
	if err != nil {
		return nil, "", fmt.Errorf("failed to read rows: %v", err)
	}
	if unmarshalErr != nil {
		return nil, "", unmarshalErr
	}

	// Check if there are more results
	hasMore := len(results) > int(pageSize)

	// Determine the next start key
	nextStartKey := ""
	if hasMore {
		// Use the key of the next item (the extra item we fetched) as the next start key
		nextStartKey = rowKeys[pageSize]
		// Remove the extra item
		results = results[:pageSize]
	}

	return results, nextStartKey, nil
}

// TenantInvitationDAO handles TenantInvitation operations

type TenantInvitationDAO struct {
	*ProtoBigtableTenantDAO[*auth_entities.TenantInvitation]
}

func NewTenantInvitationDAO(table *bigtable.Table, tenant string) *TenantInvitationDAO {
	return &TenantInvitationDAO{
		ProtoBigtableTenantDAO: NewProtoBigtableTenantDAO[*auth_entities.TenantInvitation](
			table,
			"TenantInvitation",
			"TenantInvitation",
			func() *auth_entities.TenantInvitation { return &auth_entities.TenantInvitation{} },
			tenant,
		),
	}
}

func (d *TenantInvitationDAO) rowKey(invitationID string) string {
	return fmt.Sprintf("%s#%s", d.rowKeyPrefix(), invitationID)
}

func (d *TenantInvitationDAO) Instantiate() *auth_entities.TenantInvitation {
	return &auth_entities.TenantInvitation{}
}

/*
 * callback returns true to continue iterating, false to stop iterating
 */
func (d *TenantInvitationDAO) FindAll(
	ctx context.Context, callback func(*auth_entities.TenantInvitation) bool,
) error {
	prefix := d.rowKeyPrefix()
	return d.findAllByPrefix(ctx, prefix, prefix, callback)
}

func (d *TenantInvitationDAO) Get(
	ctx context.Context, invitationID string,
) (*auth_entities.TenantInvitation, error) {
	return d.get(ctx, d.rowKey(invitationID))
}

func (d *TenantInvitationDAO) Create(
	ctx context.Context, invitation *auth_entities.TenantInvitation,
) (*auth_entities.TenantInvitation, error) {
	return d.create(ctx, d.rowKey(invitation.GetId()), invitation)
}

func (d *TenantInvitationDAO) Update(
	ctx context.Context, invitation *auth_entities.TenantInvitation,
) (*auth_entities.TenantInvitation, error) {
	return d.update(ctx, d.rowKey(invitation.GetId()), invitation)
}

func (d *TenantInvitationDAO) Delete(ctx context.Context, invitationID string) error {
	return d.delete(ctx, d.rowKey(invitationID))
}

/*
 * callback returns true to continue iterating, false to stop iterating
 */
func (d *TenantInvitationDAO) ListInvitationsForAllTenants(
	ctx context.Context, callback func(*auth_entities.TenantInvitation) bool,
) error {
	prefix := fmt.Sprintf("%s#", d.rowCategory)
	return d.findAllByPrefix(ctx, prefix, prefix, callback)
}

// This is expected to return 0 or 1 results. Technically more are possible since the key includes
// both tenant and invitation ID, but these are randomly generated UUIDs and shouldn't collide.
// Note(jacqueline): Having this function at all is a bit of a hack, but it will get cleaned up when
// we migrate to Spanner.
//
// callback returns true to continue iterating, false to stop iterating
func (d *TenantInvitationDAO) FindAllForInvitationID(
	ctx context.Context, invitationID string, callback func(*auth_entities.TenantInvitation) bool,
) error {
	regex := fmt.Sprintf("^%s#.*#%s$", d.rowCategory, invitationID)
	return d.findAllByRegex(ctx, regex, callback)
}

// InvitationResolutionDAO handles invitation resolution operations
type InvitationResolutionDAO struct {
	*ProtoBigtableDAO[*auth_entities.InvitationResolution]
}

func NewInvitationResolutionDAO(table *bigtable.Table) *InvitationResolutionDAO {
	return &InvitationResolutionDAO{
		ProtoBigtableDAO: NewProtoBigtableDAO[*auth_entities.InvitationResolution](
			table,
			"InvitationResolution",
			"InvitationResolution",
			func() *auth_entities.InvitationResolution { return &auth_entities.InvitationResolution{} },
		),
	}
}

func (d *InvitationResolutionDAO) rowKey(id string) string {
	return fmt.Sprintf("%s#%s", d.rowCategory, id)
}

func (d *InvitationResolutionDAO) Instantiate() *auth_entities.InvitationResolution {
	return &auth_entities.InvitationResolution{}
}

func (d *InvitationResolutionDAO) Create(
	ctx context.Context,
	tenantCreation *auth_entities.InvitationResolution,
) (*auth_entities.InvitationResolution, error) {
	return d.create(ctx, d.rowKey(tenantCreation.GetId()), tenantCreation)
}

func (d *InvitationResolutionDAO) Get(
	ctx context.Context, id string,
) (*auth_entities.InvitationResolution, error) {
	return d.get(ctx, d.rowKey(id))
}

func (d *InvitationResolutionDAO) Update(
	ctx context.Context,
	tenantCreation *auth_entities.InvitationResolution,
) (*auth_entities.InvitationResolution, error) {
	return d.update(ctx, d.rowKey(tenantCreation.GetId()), tenantCreation)
}

// TenantCreationDAO handles TenantCreation operations
type IDPUserMappingDAO struct {
	*ProtoBigtableDAO[*auth_entities.IdpUserMapping]
}

func NewIDPUserMappingDAO(table *bigtable.Table) *IDPUserMappingDAO {
	return &IDPUserMappingDAO{
		ProtoBigtableDAO: NewProtoBigtableDAO[*auth_entities.IdpUserMapping](
			table,
			"IDPUserMapping",
			"IDPUserMapping",
			func() *auth_entities.IdpUserMapping { return &auth_entities.IdpUserMapping{} },
		),
	}
}

func (d *IDPUserMappingDAO) rowKey(idpUserID string) string {
	return fmt.Sprintf("%s#%s", d.rowCategory, idpUserID)
}

func (d *IDPUserMappingDAO) Instantiate() *auth_entities.IdpUserMapping {
	return &auth_entities.IdpUserMapping{}
}

func (d *IDPUserMappingDAO) Create(ctx context.Context, mapping *auth_entities.IdpUserMapping) (*auth_entities.IdpUserMapping, error) {
	return d.create(ctx, d.rowKey(mapping.GetIdpUserId()), mapping)
}

func (d *IDPUserMappingDAO) TryCreate(ctx context.Context, mapping *auth_entities.IdpUserMapping) (bool, error) {
	return d.tryCreate(ctx, d.rowKey(mapping.GetIdpUserId()), mapping)
}

func (d *IDPUserMappingDAO) TryUpdate(ctx context.Context, idpUserID string, updateFn func(mapping *auth_entities.IdpUserMapping) bool, onRetry func(retry int) float64) (*auth_entities.IdpUserMapping, error) {
	return d.tryUpdate(ctx, d.rowKey(idpUserID), updateFn, onRetry)
}

func (d *IDPUserMappingDAO) Get(ctx context.Context, idpUserID string) (*auth_entities.IdpUserMapping, error) {
	return d.get(ctx, d.rowKey(idpUserID))
}

func (d *IDPUserMappingDAO) FindAll(
	ctx context.Context, callback func(*auth_entities.IdpUserMapping) bool,
) error {
	return d.findAll(ctx, callback)
}

// PendingChangeDAO handles storing all Pending Changes, including plan changes
type PendingChangeDAO struct {
	*ProtoBigtableDAO[*auth_entities.PendingChange]
}

func NewPendingChangeDAO(table *bigtable.Table) *PendingChangeDAO {
	return &PendingChangeDAO{
		ProtoBigtableDAO: NewProtoBigtableDAO[*auth_entities.PendingChange](
			table,
			"PendingChange",
			"PendingChange",
			func() *auth_entities.PendingChange { return &auth_entities.PendingChange{} },
		),
	}
}

func (d *PendingChangeDAO) rowKey(id string) string {
	return fmt.Sprintf("%s#%s", d.rowCategory, id)
}

func (d *PendingChangeDAO) Instantiate() *auth_entities.PendingChange {
	return &auth_entities.PendingChange{}
}

func (d *PendingChangeDAO) Create(ctx context.Context, pendingChange *auth_entities.PendingChange) (*auth_entities.PendingChange, error) {
	return d.create(ctx, d.rowKey(pendingChange.GetId()), pendingChange)
}

func (d *PendingChangeDAO) Get(ctx context.Context, id string) (*auth_entities.PendingChange, error) {
	return d.get(ctx, d.rowKey(id))
}

func (d *PendingChangeDAO) Update(ctx context.Context, pendingChange *auth_entities.PendingChange) (*auth_entities.PendingChange, error) {
	return d.update(ctx, d.rowKey(pendingChange.GetId()), pendingChange)
}

func (d *PendingChangeDAO) Delete(ctx context.Context, id string) error {
	return d.delete(ctx, d.rowKey(id))
}

func (d *PendingChangeDAO) TryUpdate(ctx context.Context, id string, updateFn func(pendingChange *auth_entities.PendingChange) bool, onRetry func(retry int) float64) (*auth_entities.PendingChange, error) {
	return d.tryUpdate(ctx, d.rowKey(id), updateFn, onRetry)
}

func (d *PendingChangeDAO) FindAll(ctx context.Context, callback func(*auth_entities.PendingChange) bool) error {
	return d.findAll(ctx, callback)
}

// DAOFactory creates DAOs
type DAOFactory struct {
	table *bigtable.Table
}

func NewDAOFactory(table *bigtable.Table) *DAOFactory {
	return &DAOFactory{table: table}
}

// IMPORTANT: UserTenantMapping is keyed by tenant name, not tenant ID!
func (f *DAOFactory) GetUserTenantMappingDAO(tenantName string) *UserTenantMappingDAO {
	return NewUserTenantMappingDAO(f.table, tenantName)
}

func (f *DAOFactory) GetUserDAO() *UserDAO {
	return NewUserDAO(f.table)
}

func (f *DAOFactory) GetTokenHashDAO() *TokenHashDAO {
	return NewTokenHashDAO(f.table)
}

func (f *DAOFactory) GetCodeDAO() *CodeDAO {
	return NewCodeDAO(f.table)
}

func (f *DAOFactory) GetTermsDAO() *TermsApprovalDAO {
	return NewTermsApprovalDAO(f.table)
}

func (f *DAOFactory) GetTenantCreationDAO() *TenantCreationDAO {
	return NewTenantCreationDAO(f.table)
}

func (f *DAOFactory) GetTenantSubscriptionMappingDAO() *TenantSubscriptionMappingDAO {
	return NewTenantSubscriptionMappingDAO(f.table)
}

func (f *DAOFactory) GetSubscriptionDAO() *SubscriptionDAO {
	return NewSubscriptionDAO(f.table)
}

func (f *DAOFactory) GetTenantInvitationDAO(tenantID string) *TenantInvitationDAO {
	return NewTenantInvitationDAO(f.table, tenantID)
}

func (f *DAOFactory) GetInvitationResolutionDAO() *InvitationResolutionDAO {
	return NewInvitationResolutionDAO(f.table)
}

func (f *DAOFactory) GetIDPUserMappingDAO() *IDPUserMappingDAO {
	return NewIDPUserMappingDAO(f.table)
}

func (f *DAOFactory) GetPendingChangeDAO() *PendingChangeDAO {
	return NewPendingChangeDAO(f.table)
}
