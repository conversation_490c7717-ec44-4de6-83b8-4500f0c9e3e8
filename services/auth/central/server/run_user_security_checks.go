package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	front_end_token_service "github.com/augmentcode/augment/services/auth/central/server/front_end_token_service"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// kind is verosint, verisoul, recaptcha, etc.
// result is success, error, etc.
var securityCheckResults = prometheus.NewCounterVec(prometheus.CounterOpts{
	Name: "au_auth_central_security_checks",
	Help: "Number of security checks",
}, []string{"kind", "result"})

// verosintReportFetcher encapsulates fetching the report from verosint
type verosintReportFetcherParameters struct {
	augmentUserId    string
	idpUserId        string
	emailAddress     string
	ipAddress        string
	userAgent        string
	verosintDeviceId string
}

type verosintReportFetcher interface {
	fetchReport(ctx context.Context, params verosintReportFetcherParameters) (string, error)
}

// runUserSecurityChecksHandler implements runUserSecurityChecks
type runUserSecurityChecksHandler struct {
	requestInsightPublisher ripublisher.RequestInsightPublisher
	verosintReportFetcher   verosintReportFetcher
	logger                  zerolog.Logger
	verosintEnabled         func() bool
}

// httpsPostFunc is a function that makes an HTTPS POST request with a JSON payload
type httpsPostFunc func(ctx context.Context, url string, bearerToken secretstring.SecretString, jsonData interface{}, timeout time.Duration) (*httpPostResponse, error)

// httpPostResponse represents the response from an HTTP POST request
type httpPostResponse struct {
	statusCode int
	body       []byte
	headers    map[string][]string
}

type verosintReportFetcherImpl struct {
	apiKey        secretstring.SecretString
	endpoint      string
	httpsPostFunc httpsPostFunc
	timeout       time.Duration
}

func (f *verosintReportFetcherImpl) fetchReport(ctx context.Context, params verosintReportFetcherParameters) (string, error) {
	// Make a request to verosint
	resp, err := f.httpsPostFunc(ctx, f.endpoint, f.apiKey, map[string]interface{}{
		// verbose means fetch the details - not just the outcome of rules
		"verbose": true,
		// The Verosint API requires at least one rule. This rule never matches.
		"rules": []interface{}{
			map[string]interface{}{
				"name":     "dummy",
				"query":    "false",
				"outcomes": []string{"ALLOW"},
			},
		},
		"identifiers": map[string]string{
			"email":     params.emailAddress,
			"ip":        params.ipAddress,
			"accountId": params.idpUserId,
			"deviceId":  params.verosintDeviceId,
			"targetApp": "Augment Auth Central",
			"userAgent": params.userAgent,
		},
	}, f.timeout)
	if err != nil {
		return "", fmt.Errorf("failed to make HTTP request: %w", err)
	}
	if resp.statusCode != http.StatusOK {
		return "", fmt.Errorf("verosint returned non-200 status code: %d details: %s", resp.statusCode, string(resp.body))
	}

	return string(resp.body), nil
}

type mockVerosintReportFetcherImpl struct {
	paramsSeen []verosintReportFetcherParameters

	report string
	err    error
}

func (f *mockVerosintReportFetcherImpl) fetchReport(ctx context.Context, params verosintReportFetcherParameters) (string, error) {
	f.paramsSeen = append(f.paramsSeen, params)

	return f.report, f.err
}

type runUserSecurityChecks interface {
	runUserSecurityChecks(ctx context.Context, req *front_end_token_service.RunUserSecurityChecksRequest) (*front_end_token_service.RunUserSecurityChecksResponse, error)
}

func (h *runUserSecurityChecksHandler) runUserSecurityChecks(
	ctx context.Context, req *front_end_token_service.RunUserSecurityChecksRequest,
) (*front_end_token_service.RunUserSecurityChecksResponse, error) {
	if !h.verosintEnabled() {
		return &front_end_token_service.RunUserSecurityChecksResponse{
			Passed: true,
		}, nil
	}

	var err error

	defer func() {
		if err != nil {
			securityCheckResults.WithLabelValues("verosint", "error").Inc()
		} else {
			securityCheckResults.WithLabelValues("verosint", "success").Inc()
		}
	}()

	report, err := h.verosintReportFetcher.fetchReport(ctx, verosintReportFetcherParameters{
		augmentUserId:    req.AugmentUserId,
		idpUserId:        req.IdpUserId,
		emailAddress:     req.EmailAddress,
		ipAddress:        req.IpAddress,
		userAgent:        req.UserAgent,
		verosintDeviceId: req.VerosintDeviceId,
	})
	if err != nil {
		h.logger.Err(err).Msg("Failed to fetch verosint report")
		return nil, err
	}

	h.logger.Info().Str("augment_user_id", req.AugmentUserId).Str("idp_user_id", req.IdpUserId).Str("email_address", req.EmailAddress).Msgf("Verosint report: %s", report)

	event := ripublisher.NewGenericEvent()
	var userId *auth_entities.UserId
	if req.AugmentUserId != "" {
		userId = &auth_entities.UserId{
			UserIdType: auth_entities.UserId_AUGMENT,
			UserId:     req.AugmentUserId,
		}
	}
	event.Event = &riproto.GenericEvent_Verosint{
		Verosint: &riproto.Verosint{
			OpaqueUserId: userId,
			Report:       report,
		},
	}

	err = h.requestInsightPublisher.PublishGenericEvent(ctx, "", event)
	if err != nil {
		h.logger.Err(err).Msg("Failed to publish verosint report to request insight")
		return nil, err
	}

	return &front_end_token_service.RunUserSecurityChecksResponse{
		Passed: true,
	}, nil
}

// doHTTPSPost makes an HTTPS POST request with JSON data and returns the response
func doHTTPSPost(ctx context.Context, url string, bearerToken secretstring.SecretString, jsonData interface{}, timeout time.Duration) (*httpPostResponse, error) {
	// Marshal the JSON data
	jsonBytes, err := json.Marshal(jsonData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JSON data: %w", err)
	}

	// Create the HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Set headers
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", bearerToken.Expose()))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "augment-auth-central-server")

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: timeout,
	}

	// Make the request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// Read the response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Return the response
	return &httpPostResponse{
		statusCode: resp.StatusCode,
		body:       body,
		headers:    resp.Header,
	}, nil
}

type runUserSecurityChecksDependencies struct {
	requestInsightPublisher ripublisher.RequestInsightPublisher
	verosintReportFetcher   verosintReportFetcher
	logger                  *zerolog.Logger
	verosintEnabled         func() bool
}

func newRunUserSecurityChecksHandler(
	deps runUserSecurityChecksDependencies,
) *runUserSecurityChecksHandler {
	h := &runUserSecurityChecksHandler{
		requestInsightPublisher: deps.requestInsightPublisher,
		verosintReportFetcher:   deps.verosintReportFetcher,
		verosintEnabled:         deps.verosintEnabled,
	}

	if deps.requestInsightPublisher == nil {
		h.requestInsightPublisher = ripublisher.NewRequestInsightPublisherMock()
	}

	if deps.verosintReportFetcher == nil {
		h.verosintReportFetcher = &mockVerosintReportFetcherImpl{}
	}

	if deps.logger == nil {
		h.logger = log.Logger
	} else {
		h.logger = *deps.logger
	}

	if h.verosintEnabled == nil {
		h.verosintEnabled = func() bool {
			return true
		}
	}

	return h
}
