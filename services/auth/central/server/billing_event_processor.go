package main

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	orb_event "github.com/augmentcode/augment/services/auth/billing_webhook/orb_event"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/integrations/orb"
	pubsub "github.com/augmentcode/augment/services/lib/pubsub"
	tenantutil "github.com/augmentcode/augment/services/tenant_watcher/util"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var billingEventMetrics = promauto.NewHistogramVec(
	prometheus.HistogramOpts{
		Name:    "au_auth_central_billing_processor_latency",
		Help:    "Orb events processed with duration in seconds",
		Buckets: prometheus.DefBuckets,
	},
	[]string{"event_type", "status"},
)

type BillingEventProcessor struct {
	Config            *Config
	DAOFactory        *DAOFactory
	subscribeClient   pubsub.SubscribeClient
	orbClient         orb.OrbClient
	stripeClient      StripeClient
	tenantMap         *TenantMap
	featureFlagHandle featureflags.FeatureFlagHandle
	auditLogger       *audit.AuditLogger
}

func NewBillingEventProcessor(
	ctx context.Context,
	config *Config,
	daoFactory *DAOFactory,
	orbClient orb.OrbClient,
	stripeClient StripeClient,
	tenantMap *TenantMap,
	featureFlagHandle featureflags.FeatureFlagHandle,
	auditLogger *audit.AuditLogger,
) (*BillingEventProcessor, error) {
	if config.BillingEventProcessorConfig.SubscriptionName == "" {
		return nil, fmt.Errorf("subscription name is required")
	}

	subscribeConfig := &pubsub.SubscribeClientConfig{
		ProjectId:                        config.GCP.ProjectID,
		SubscriptionId:                   config.BillingEventProcessorConfig.SubscriptionName,
		DeadLetterSubscriptionId:         config.BillingEventProcessorConfig.DeadLetterSubscriptionName,
		MaxConcurrentReceivers:           config.BillingEventProcessorConfig.MaxConcurrentReceivers,
		DeadLetterMaxConcurrentReceivers: config.BillingEventProcessorConfig.DeadLetterMaxConcurrentReceivers,
		ConfigureDeadLetterHandling: func() bool {
			return checkBillingEventProcessorDeadLetterFlag(featureFlagHandle)
		},
	}

	subscribeClient, err := pubsub.NewSubscribeClient(ctx, subscribeConfig)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create subscribe client")
		return nil, fmt.Errorf("failed to create subscribe client: %w", err)
	}

	return &BillingEventProcessor{
		Config:            config,
		DAOFactory:        daoFactory,
		subscribeClient:   subscribeClient,
		orbClient:         orbClient,
		stripeClient:      stripeClient,
		tenantMap:         tenantMap,
		featureFlagHandle: featureFlagHandle,
		auditLogger:       auditLogger,
	}, nil
}

// Run starts the processor and blocks until the context is canceled
func (p *BillingEventProcessor) Run(ctx context.Context) error {
	// Our pubsub library already handles:
	// 1. Panic recovery in message handlers
	// 2. Automatic retries for failed messages (via Nack)
	// 3. Reconnection for transient service issues
	log.Info().Msg("Starting Billing event processor")
	err := p.receiveAndProcess(ctx)

	// If we get here, either the context was canceled or there was a fatal error
	if ctx.Err() != nil {
		log.Info().Err(ctx.Err()).Msg("Context cancelled, stopping BillingEventProcessor")
		return ctx.Err()
	}

	// If we get here with a non-nil error, it's a fatal error from the PubSub client
	if err != nil {
		log.Error().Err(err).Msg("Fatal error in Billing event processor")
	}
	return err
}

// Stop stops the processor and cleans up resources
func (p *BillingEventProcessor) Stop() {
	if p.subscribeClient != nil {
		p.subscribeClient.Close()
	}
}

// receiveAndProcess receives and processes messages from the PubSub subscription
func (p *BillingEventProcessor) receiveAndProcess(ctx context.Context) error {
	return p.subscribeClient.Receive(ctx, func(ctx context.Context, msgData []byte) error {
		// Start timing the message processing
		start := time.Now()
		var eventType string = "unknown" // Default event type
		var status string = "success"    // Default status

		// Defer the metric recording to ensure it happens regardless of how we exit the function
		defer func() {
			// This is the only place we record metrics
			billingEventMetrics.WithLabelValues(eventType, status).Observe(time.Since(start).Seconds())
		}()

		var orbEvent orb_event.OrbEvent
		// Try to unmarshal as Protocol Buffers first
		err := proto.Unmarshal(msgData, &orbEvent)
		if err != nil {
			log.Info().Err(err).Str("raw_message", string(msgData)).Str("message_length", fmt.Sprintf("%d", len(msgData))).Msg("Received Orb event message with unmarshal error")
			status = "error"
			return err // Return error to trigger Nack
		}

		// Update the event type once we know it
		eventType = orbEvent.EventType

		if err := p.ProcessOrbEvent(ctx, &orbEvent); err != nil {
			log.Error().Err(err).Msg("Failed to process Orb event")
			status = "error"
			return err // Return error to trigger Nack
		}

		return nil // Return nil to trigger Ack
	})
}

func (p *BillingEventProcessor) ProcessOrbEvent(ctx context.Context, event *orb_event.OrbEvent) error {
	log.Info().
		Str("event_type", event.EventType).
		Str("request_id", event.Metadata.RequestId).
		Msg("Processing Orb event")

	// Handle different event types
	switch {
	case strings.HasPrefix(event.EventType, "customer.credit_balance_"):
		return p.handleCreditBalanceEvents(ctx, event)
	case strings.HasPrefix(event.EventType, "subscription."):
		return p.handleSubscriptionEvents(ctx, event)
	case strings.HasPrefix(event.EventType, "invoice."):
		return p.handleInvoiceEvents(ctx, event)
	default:
		log.Info().Str("event_type", event.EventType).Msg("Ignoring unhandled event type")
		return nil
	}
}

func (p *BillingEventProcessor) handleCreditBalanceEvents(ctx context.Context, event *orb_event.OrbEvent) error {
	orbCustomerID := event.OrbCustomerId
	if orbCustomerID == nil {
		return fmt.Errorf("customer ID is nil")
	}

	customerBalanceEvent := event.GetCustomerBalanceEvent()
	if customerBalanceEvent.PricingUnit != p.Config.Orb.PricingUnit {
		log.Info().Str("event_pricing_unit", customerBalanceEvent.PricingUnit).Str("config_pricing_unit", p.Config.Orb.PricingUnit).Msg("Ignoring credit balance event for non-usermessage pricing unit")
		return nil
	}

	// API Call to check customer balance
	// https://docs.withorb.com/self-serve/product-access#blocking-product-access-on-depletion
	// Simply listening to the webhook is insufficient, we need to query for certainty
	if p.orbClient == nil {
		return fmt.Errorf("orb client is nil, cannot get customer credit balance")
	}
	balance, err := p.orbClient.GetCustomerCreditBalance(ctx, *orbCustomerID, p.Config.Orb.PricingUnit)
	if err != nil {
		return fmt.Errorf("failed to get customer's balance: %w", err)
	}

	isBalanceDepleted := balance <= 0
	log.Info().Str("customer_id", *orbCustomerID).Float64("balance", balance).Msg("Customer's balance event received")

	// 1. Find user with augment user id if available, else scan to find user with orb customer id
	var user *auth_entities.User
	if event.AugmentUserId != "" {
		user, err = p.findUserByAugmentUserId(ctx, event.AugmentUserId)
		if err != nil {
			log.Warn().Err(err).Str("augment_user_id", event.AugmentUserId).Msg("Error finding user for Orb customer ID")
		}
	}
	if user == nil {
		user, err = p.findUserByOrbCustomerId(ctx, *orbCustomerID)
		if err != nil {
			log.Warn().Err(err).Str("orb_customer_id", *orbCustomerID).Msg("Error finding user for Orb customer ID")
			// For credit balance events, we don't want to return an error when no user is found
			// This is different from subscription events where we do want to return an error
			return nil
		}
	}

	// 2. Find orb subscription id field on user
	if user.OrbSubscriptionId == "" {
		log.Warn().Str("user_id", user.Id).Msg("User has no Orb subscription ID")
		return nil
	}

	// 3. Get the subscription object
	subscriptionDAO := p.DAOFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, user.OrbSubscriptionId)
	if err != nil {
		return fmt.Errorf("failed to get subscription: %w", err)
	}

	if subscription == nil {
		log.Warn().Str("subscription_id", user.OrbSubscriptionId).Msg("Subscription not found")
		return nil
	}

	// 4. Set the attribute based on the balance value
	_, err = subscriptionDAO.TryUpdate(ctx, user.OrbSubscriptionId, func(s *auth_entities.Subscription) bool {
		if s.UsageBalanceDepleted != isBalanceDepleted {
			s.UsageBalanceDepleted = isBalanceDepleted
			s.UpdatedAt = timestamppb.Now()
			log.Info().
				Str("subscription_id", user.OrbSubscriptionId).
				Bool("usage_balance_depleted", isBalanceDepleted).
				Float64("balance", balance).
				Str("user_id", user.Id).
				Msg("Updated subscription usage_balance_depleted attribute based on balance value")
			p.auditLogger.WriteAuditLog(
				"",
				"",
				"",
				fmt.Sprintf("Updated subscription with ID %s usage_balance_depleted attribute %v based on balance of %v", user.OrbSubscriptionId, isBalanceDepleted, balance),
			)
			return true
		}
		return false
	}, DefaultRetry)
	if err != nil {
		return fmt.Errorf("failed to update subscription: %w", err)
	}

	return nil
}

// handleSubscriptionEvents handles all subscription-related events from Orb
func (p *BillingEventProcessor) handleSubscriptionEvents(ctx context.Context, event *orb_event.OrbEvent) error {
	// In the current implementation, we assume that only an individual user can create a subscription.
	// The tenant creation process will then transfer the subscription to the team by setting a tenant as the subscription owner.
	// Switching tiers as a team does not create a new subscription.
	subEvent := event.GetSubscriptionEvent()
	if subEvent == nil {
		log.Error().
			Str("event_type", event.EventType).
			Str("event_id", event.Metadata.EventId).
			Msg("Subscription event is nil")
		return fmt.Errorf("subscription event is nil")
	}

	if event.OrbCustomerId == nil {
		log.Error().
			Str("event_type", event.EventType).
			Str("event_id", event.Metadata.EventId).
			Msg("Orb customer ID is nil")
		return fmt.Errorf("orb customer ID is nil")
	}

	log.Info().
		Str("event_type", event.EventType).
		Str("subscription_id", subEvent.SubscriptionId).
		Str("orb_customer_id", *event.OrbCustomerId).
		Msg("Processing subscription event")

	// Fetch the latest subscription data from Orb API using GetUserSubscription
	if p.orbClient == nil {
		log.Error().
			Str("subscription_id", subEvent.SubscriptionId).
			Msg("Orb client is nil, cannot get subscription data")
		return fmt.Errorf("orb client is nil, cannot get subscription data")
	}
	orbSubscriptionInfo, err := p.orbClient.GetUserSubscription(ctx, subEvent.SubscriptionId, &orb.ItemIds{
		SeatsID:            p.Config.Orb.SeatsItemID,
		IncludedMessagesID: p.Config.Orb.IncludedMessagesItemID,
	})
	if err != nil {
		log.Error().
			Err(err).
			Str("subscription_id", subEvent.SubscriptionId).
			Msg("Failed to get latest subscription data from Orb")
		return fmt.Errorf("failed to get latest subscription data from Orb: %v", err)
	}

	log.Info().
		Str("subscription_id", subEvent.SubscriptionId).
		Str("external_plan_id", orbSubscriptionInfo.ExternalPlanID).
		Str("status", orbSubscriptionInfo.OrbStatus).
		Int("seats", orbSubscriptionInfo.CurrentFixedQuantities.Seats).
		Int("included_messages_per_month", orbSubscriptionInfo.CurrentFixedQuantities.IncludedMessages).
		Msg("Retrieved latest subscription data from Orb")

	// Parse the Orb subscription info into our Subscription struct
	latestSubscription := parseOrbSubscriptionInfo(orbSubscriptionInfo, *event.OrbCustomerId)

	// Note: We only search for the user because the orbCustomerId isn't removed during tenant creation for team.
	// Instead, the user becomes an admin, and both the orbCustomerId and subscription are transferred to the team.
	// Try to find a user for this subscription
	var user *auth_entities.User
	if event.AugmentUserId != "" {
		user, err = p.findUserByAugmentUserId(ctx, event.AugmentUserId)
		if err != nil {
			log.Warn().Err(err).Str("augment_user_id", event.AugmentUserId).Msg("Error finding user for Orb customer ID")
			// Don't return an error here, just continue to find user by orb customer id
		}
	}
	if user == nil {
		user, err = p.findUserByOrbCustomerId(ctx, *event.OrbCustomerId)
		if err != nil {
			log.Warn().Err(err).Str("orb_customer_id", *event.OrbCustomerId).Msg("Error finding user for Orb customer ID")
			// For subscription events, we DO want to return an error when no user is found
			// This is different from credit balance events where we just log and continue
			return err
		}
	}

	hasPaymentMethod := false
	if user.StripeCustomerId != "" {
		hasPaymentMethod, err = p.stripeClient.HasPaymentMethod(user.StripeCustomerId)
		if err != nil {
			log.Error().Err(err).
				Str("user_id", user.Id).
				Str("stripe_customer_id", user.StripeCustomerId).
				Msg("Failed to check payment method status")
			return fmt.Errorf("failed to check payment method status: %v", err)
		}
	} else {
		log.Error().Str("user_id", user.Id).Msg("User does not have a Stripe customer ID")
	}
	latestSubscription.HasPaymentMethod = hasPaymentMethod

	// Find the TenantSubscriptionMapping of the user's self-serve team tenant (if any). Currently the admin user and the self-serve team
	// tenant shares the same Orb customer ID and Orb subscription ID.
	tenantSubscriptionMapping, err := p.findTenantSubscriptionMappingByAdminUser(ctx, user)
	if err != nil {
		log.Error().Err(err).Str("user_id", user.Id).Msg("Failed to find TenantSubscriptionMapping by admin user")
		return err
	}

	// Process the subscription with the user and the TenantSubscriptionMapping (if found)
	return p.processSubscription(ctx, latestSubscription, user, tenantSubscriptionMapping)
}

// findUserByUserId tries to find a user by their augment user ID
func (p *BillingEventProcessor) findUserByAugmentUserId(ctx context.Context, augmentUserId string) (*auth_entities.User, error) {
	userDAO := p.DAOFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, augmentUserId)
	if err != nil {
		log.Error().Err(err).Msg("Invalid augment_user_ID in Orb customer metadata")
		return nil, err
	}
	if user == nil {
		log.Error().Str("augment_user_id", augmentUserId).Msg("no user found with augment_user_Id")
		return nil, fmt.Errorf("no user found with augment_user_Id %s", augmentUserId)
	}
	log.Info().Str("augment_user_id", augmentUserId).Msg("Found user by augment_user_id")
	return user, nil
}

// findUserByCustomerId tries to find a individual user or admin user within a team associated with an Orb customer ID
func (p *BillingEventProcessor) findUserByOrbCustomerId(ctx context.Context, orbCustomerID string) (*auth_entities.User, error) {
	var user *auth_entities.User
	var err error
	userDAO := p.DAOFactory.GetUserDAO()

	err = userDAO.FindAll(ctx, func(u *auth_entities.User) bool {
		if u.OrbCustomerId == orbCustomerID {
			user = u
			return false
		}
		return true
	})
	if err != nil {
		log.Error().Err(err).Msg("Failed to find users in subscription event handler")
		return nil, err
	}
	if user == nil {
		log.Error().Str("orb_customer_id", orbCustomerID).Msg("no user or admin user in tenant found for Orb customer ID")
		return nil, fmt.Errorf("no user or admin user in tenant found for Orb customer ID %s", orbCustomerID)
	}

	return user, nil
}

// Find Stripe Customer ID for Orb Customer ID
func findStripeCustomerIDByOrbCustomerID(ctx context.Context, orbCustomerID string, orbClient orb.OrbClient) (string, error) {
	orbCustomerInfo, err := orbClient.GetCustomerInfo(ctx, orbCustomerID)
	if err != nil {
		log.Error().Err(err).Str("orb_customer_id", orbCustomerID).Msg("Failed to get customer info from Orb")
		return "", err
	}
	if orbCustomerInfo.StripeCustomerID == "" {
		log.Error().Str("orb_customer_id", orbCustomerID).Msg("No Stripe customer ID found for Orb customer ID")
		return "", fmt.Errorf("no Stripe customer ID found for Orb customer ID %s", orbCustomerID)
	}
	return orbCustomerInfo.StripeCustomerID, nil
}

// Find TenantSubscriptionMapping from the admin user. Currently the admin user and the self-serve team tenant has a common Orb customer ID.
func (p *BillingEventProcessor) findTenantSubscriptionMappingByAdminUser(ctx context.Context, user *auth_entities.User) (*auth_entities.TenantSubscriptionMapping, error) {
	tenantSubscriptionMappingDAO := p.DAOFactory.GetTenantSubscriptionMappingDAO()
	tenantIDs := user.Tenants
	for _, tenantID := range tenantIDs {
		tenant, err := p.tenantMap.GetTenantByID(tenantID)
		if err != nil || tenant == nil {
			log.Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to get tenant")
			continue
		}

		if tenantutil.IsSelfServeTeamTenant(tenant) {
			tenantSubscriptionMapping, err := tenantSubscriptionMappingDAO.Get(ctx, tenantID)
			if err != nil || tenantSubscriptionMapping == nil {
				log.Error().Err(err).
					Str("tenant_id", tenantID).
					Msg("Failed to get TenantSubscriptionMapping for self-serve team")
				continue
			}
			if tenantSubscriptionMapping.OrbCustomerId != user.OrbCustomerId {
				// If the self-serve team tenant and the user have different Orb customer IDs, some is wrong
				log.Error().
					Str("tenant_id", tenantID).
					Str("user_id", user.Id).
					Str("user_orb_customer_id", user.OrbCustomerId).
					Str("tenant_orb_customer_id", tenantSubscriptionMapping.OrbCustomerId).
					Msg("Orb customer ID in tenant subscription mapping does not match user's orb customer ID")
				continue
			}

			// Found the TenantSubscriptionMapping for the self-serve team tenant of the user
			return tenantSubscriptionMapping, nil
		}
	}

	return nil, nil
}

// processSubscription updates or creates a subscription based on Orb data
func (p *BillingEventProcessor) processSubscription(
	ctx context.Context,
	latestSubscription *auth_entities.Subscription,
	user *auth_entities.User,
	tenantSubscriptionMapping *auth_entities.TenantSubscriptionMapping,
) error {
	subscriptionDAO := p.DAOFactory.GetSubscriptionDAO()

	// Check the customer's balance from Orb to determine if it's depleted
	balance, err := p.orbClient.GetCustomerCreditBalance(ctx, latestSubscription.OrbCustomerId, p.Config.Orb.PricingUnit)
	if err != nil {
		log.Error().Err(err).Str("orb_customer_id", latestSubscription.OrbCustomerId).Msg("Failed to get customer's balance from Orb")
		// Continue with the default value (false) for UsageBalanceDepleted
	} else {
		// Update the UsageBalanceDepleted field based on the actual balance
		isBalanceDepleted := balance <= 0
		latestSubscription.UsageBalanceDepleted = isBalanceDepleted
		log.Info().
			Str("orb_customer_id", latestSubscription.OrbCustomerId).
			Float64("balance", balance).
			Bool("usage_balance_depleted", isBalanceDepleted).
			Msg("Set subscription usage_balance_depleted based on current balance")
	}

	// Check if we already have this subscription in our database
	existingSubscription, err := subscriptionDAO.Get(ctx, latestSubscription.SubscriptionId)
	if err != nil {
		log.Error().Err(err).Str("subscription_id", latestSubscription.SubscriptionId).Msg("Failed to check for existing subscription")
		return fmt.Errorf("failed to check for existing subscription: %v", err)
	}

	if existingSubscription == nil {
		// Create a new subscription for individual user or team
		// Set the owner and system fields
		if tenantSubscriptionMapping == nil {
			log.Info().Str("user_id", user.Id).Str("subscription_id", latestSubscription.SubscriptionId).Msg("Creating new subscription for user")
			latestSubscription.Owner = &auth_entities.Subscription_UserId{UserId: user.Id}
		} else {
			log.Info().Str("tenant_id", tenantSubscriptionMapping.TenantId).Str("subscription_id", latestSubscription.SubscriptionId).Msg("Creating new subscription for team")
			latestSubscription.Owner = &auth_entities.Subscription_TenantId{TenantId: tenantSubscriptionMapping.TenantId}
		}
		latestSubscription.CreatedAt = timestamppb.Now()
		latestSubscription.UpdatedAt = timestamppb.Now()

		// Create the subscription
		_, err := subscriptionDAO.Create(ctx, latestSubscription)
		if err != nil {
			log.Error().Err(err).Str("subscription_id", latestSubscription.SubscriptionId).Msg("Failed to create subscription")
			return fmt.Errorf("failed to create subscription: %v", err)
		}
	} else {
		// Use TryUpdate to update the subscription if needed
		_, err = subscriptionDAO.TryUpdate(ctx, existingSubscription.SubscriptionId, func(s *auth_entities.Subscription) bool {
			// Check if any fields need to be updated
			if !compareOrbSubscriptionFields(s, latestSubscription) {
				log.Info().Str("subscription_id", latestSubscription.SubscriptionId).Msg("Subscription data is already up to date, no changes needed")
				return false // No changes needed
			}

			log.Info().Str("subscription_id", latestSubscription.SubscriptionId).Msg("Updating existing subscription for user")

			// Update all common fields
			// Note we don't update owner here since it's updated in the tenant creation process
			updateSubscriptionFields(s, latestSubscription)

			return true
		}, DefaultRetry)
		if err != nil {
			log.Error().Err(err).Str("subscription_id", latestSubscription.SubscriptionId).Msg("Failed to update subscription")
			return fmt.Errorf("failed to update subscription: %v", err)
		}
	}

	// Update the user's orb subscription ID if needed.  Only do it for active subscriptions
	if user.OrbSubscriptionId != latestSubscription.SubscriptionId && latestSubscription.OrbStatus == auth_entities.Subscription_ORB_STATUS_ACTIVE {
		userDAO := p.DAOFactory.GetUserDAO()
		log.Info().Str("user_id", user.Id).Str("old_subscription_id", user.OrbSubscriptionId).Str("new_subscription_id", latestSubscription.SubscriptionId).Msg("Updating user's orb subscription ID")
		_, err = userDAO.TryUpdate(ctx, user.Id, func(u *auth_entities.User) bool {
			u.OrbSubscriptionId = latestSubscription.SubscriptionId
			return true
		}, DefaultRetry)
		if err != nil {
			log.Error().Err(err).Str("user_id", user.Id).Str("subscription_id", latestSubscription.SubscriptionId).Msg("Failed to update user's orb subscription ID")
			return fmt.Errorf("failed to update user's orb subscription ID: %v", err)
		}
		log.Info().Str("user_id", user.Id).Str("subscription_id", latestSubscription.SubscriptionId).Msg("Successfully updated user's orb subscription ID")
	}

	// Update the orb subscription ID of the TenantSubscriptionMapping of a team if needed. Only do it for active subscriptions
	if tenantSubscriptionMapping != nil &&
		tenantSubscriptionMapping.OrbSubscriptionId != latestSubscription.SubscriptionId &&
		latestSubscription.OrbStatus == auth_entities.Subscription_ORB_STATUS_ACTIVE {
		tenantSubscriptionMappingDAO := p.DAOFactory.GetTenantSubscriptionMappingDAO()
		log.Info().
			Str("tenant_id", tenantSubscriptionMapping.TenantId).
			Str("old_subscription_id", tenantSubscriptionMapping.OrbSubscriptionId).
			Str("new_subscription_id", latestSubscription.SubscriptionId).
			Msg("Updating TenantSubscriptionMapping's orb subscription ID")
		_, err = tenantSubscriptionMappingDAO.TryUpdate(ctx, tenantSubscriptionMapping.TenantId, func(tsm *auth_entities.TenantSubscriptionMapping) bool {
			tsm.OrbSubscriptionId = latestSubscription.SubscriptionId
			return true
		}, DefaultRetry)
		if err != nil {
			log.Error().Err(err).Str("tenant_id", tenantSubscriptionMapping.TenantId).Str("subscription_id", latestSubscription.SubscriptionId).Msg("Failed to update tenant's orb subscription ID")
			return fmt.Errorf("failed to update tenant's orb subscription ID: %v", err)
		}
		log.Info().Str("tenant_id", tenantSubscriptionMapping.TenantId).Str("subscription_id", latestSubscription.SubscriptionId).Msg("Successfully updated tenant's orb subscription ID")
	}

	return nil
}

// parseOrbSubscriptionInfo converts an OrbSubscriptionInfo to our internal Subscription entity
func parseOrbSubscriptionInfo(orbSubscriptionInfo *orb.OrbSubscriptionInfo, orbCustomerID string) *auth_entities.Subscription {
	// Create a subscription with values from OrbSubscriptionInfo
	subscription := &auth_entities.Subscription{
		SubscriptionId: orbSubscriptionInfo.OrbSubscriptionID,
		PriceId:        orbSubscriptionInfo.ExternalPlanID, // Using external plan ID as price ID
		StartDate:      timestamppb.New(orbSubscriptionInfo.StartDate),
		BillingMethod:  auth_entities.BillingMethod_BILLING_METHOD_ORB,

		// Add the new Orb-specific fields
		OrbCustomerId:        orbCustomerID,
		OrbStatus:            parseOrbStatus(orbSubscriptionInfo.OrbStatus),
		ExternalPlanId:       orbSubscriptionInfo.ExternalPlanID,
		UsageBalanceDepleted: false, // Will be updated when processing the subscription
	}

	// Only set EndDate if it's not a zero value
	if !orbSubscriptionInfo.EndDate.IsZero() {
		subscription.EndDate = timestamppb.New(orbSubscriptionInfo.EndDate)
	}

	// Set the current seats and credits, so long as the current fixed quantities is not nil
	if orbSubscriptionInfo.CurrentFixedQuantities != nil {
		subscription.Seats = int32(orbSubscriptionInfo.CurrentFixedQuantities.Seats)
		subscription.IncludedCreditsId = orbSubscriptionInfo.CurrentFixedQuantities.IncludedMessagesID
		subscription.CreditsPerMonth = float64(orbSubscriptionInfo.CurrentFixedQuantities.IncludedMessages)
		subscription.SeatsId = orbSubscriptionInfo.CurrentFixedQuantities.SeatsID
	}

	// If we have future scheduled, set current seats to the future, as this is what the F/E should show
	if orbSubscriptionInfo.FutureFixedQuantities != nil && orbSubscriptionInfo.FutureFixedQuantities.Seats > 0 {
		log.Info().Str("subscription_id", orbSubscriptionInfo.OrbSubscriptionID).Int("current_seats", orbSubscriptionInfo.CurrentFixedQuantities.Seats).Int("future_seats", orbSubscriptionInfo.FutureFixedQuantities.Seats).Msg("Setting current seats to future seats")
		subscription.Seats = int32(orbSubscriptionInfo.FutureFixedQuantities.Seats)
	}

	return subscription
}

// parseOrbStatus maps a string status to our OrbStatus enum
func parseOrbStatus(status string) auth_entities.Subscription_OrbStatus {
	switch status {
	case "active":
		return auth_entities.Subscription_ORB_STATUS_ACTIVE
	case "ended":
		return auth_entities.Subscription_ORB_STATUS_ENDED
	case "upcoming":
		return auth_entities.Subscription_ORB_STATUS_UPCOMING
	default:
		return auth_entities.Subscription_ORB_STATUS_UNKNOWN
	}
}

// compareOrbSubscriptionFields compares the fields of two subscriptions that come from Orb
// Returns true if the fields are different and the subscription needs to be updated
func compareOrbSubscriptionFields(existing, updated *auth_entities.Subscription) bool {
	existingCopy := proto.Clone(existing).(*auth_entities.Subscription)
	updatedCopy := proto.Clone(updated).(*auth_entities.Subscription)

	// Zero out fields that shouldn't be compared
	existingCopy.CreatedAt = nil
	updatedCopy.CreatedAt = nil
	existingCopy.UpdatedAt = nil
	updatedCopy.UpdatedAt = nil

	return !proto.Equal(existingCopy, updatedCopy)
}

// updateSubscriptionFields updates the common fields of a subscription from the latest subscription data
func updateSubscriptionFields(s *auth_entities.Subscription, latest *auth_entities.Subscription) {
	// Update the basic subscription fields
	s.PriceId = latest.PriceId
	s.Status = latest.Status
	s.Seats = latest.Seats
	s.StartDate = latest.StartDate
	s.EndDate = latest.EndDate
	s.TrialEnd = latest.TrialEnd
	s.CancelAtPeriodEnd = latest.CancelAtPeriodEnd
	s.UpdatedAt = timestamppb.Now()
	s.BillingMethod = latest.BillingMethod
	s.HasPaymentMethod = latest.HasPaymentMethod

	// Update Orb-specific fields
	s.OrbCustomerId = latest.OrbCustomerId
	s.OrbStatus = latest.OrbStatus
	s.ExternalPlanId = latest.ExternalPlanId
	s.SeatsId = latest.SeatsId
	s.IncludedCreditsId = latest.IncludedCreditsId
	s.CreditsPerMonth = latest.CreditsPerMonth
	s.UsageBalanceDepleted = latest.UsageBalanceDepleted
}

// handleInvoiceEvents handles all invoice-related events from Orb
func (p *BillingEventProcessor) handleInvoiceEvents(ctx context.Context, event *orb_event.OrbEvent) error {
	// Handle different invoice event types
	switch event.EventType {
	case "invoice.payment_failed":
		invoiceEvent := event.GetInvoiceEvent()
		if invoiceEvent == nil {
			log.Error().
				Str("event_type", event.EventType).
				Str("event_id", event.Metadata.EventId).
				Msg("Invoice event is nil")
			return fmt.Errorf("invoice event is nil")
		}

		log.Info().
			Str("event_type", event.EventType).
			Str("invoice_id", invoiceEvent.InvoiceId).
			Msg("Processing invoice.payment_failed event")

		return p.handleInvoicePaymentFailed(ctx, invoiceEvent.InvoiceId)
	default:
		log.Info().
			Str("event_type", event.EventType).
			Msg("Ignoring unhandled invoice event type")
		return nil
	}
}

// handleInvoicePaymentFailed handles the specific logic for invoice.payment_failed events
func (p *BillingEventProcessor) handleInvoicePaymentFailed(ctx context.Context, invoiceId string) error {
	// Check if Orb client is available
	if p.orbClient == nil {
		log.Error().Msg("Orb client is nil, cannot process invoice event")
		return fmt.Errorf("orb client is nil, cannot process invoice event")
	}
	invoice, err := p.orbClient.GetInvoice(ctx, invoiceId)
	if err != nil {
		log.Error().
			Err(err).
			Str("invoice_id", invoiceId).
			Msg("Failed to fetch invoice details from Orb")
		return fmt.Errorf("failed to fetch invoice details from Orb: %w", err)
	}

	log.Info().
		Str("invoice_id", invoice.ID).
		Str("subscription_id", invoice.SubscriptionID).
		Int32("payment_attempts", invoice.PaymentAttempts).
		Str("source", invoice.Source).
		Str("status", invoice.Status).
		Msg("Retrieved invoice details from Orb")

	cancelResult := shouldCancelOrbSubscription(ctx, invoice, p.orbClient, p.stripeClient)
	if cancelResult.Err != nil {
		log.Error().Err(cancelResult.Err).Str("invoice_id", invoice.ID).Msg("Failed to check if we should cancel subscription")
		return cancelResult.Err
	}

	if cancelResult.ShouldCancel {
		err := cancelOrbSubscriptionForFailedPayment(ctx, invoice, cancelResult.Reason, p.orbClient, p.auditLogger, p.DAOFactory)
		if err != nil {
			return err
		}
		if cancelResult.ClearPaymentMethods {
			err := removePaymentMethodForCustomer(ctx, cancelResult.StripeCustomerID, p.stripeClient)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

type CancelResult struct {
	ShouldCancel        bool   // whether we should cancel the subscription
	Reason              string // reason for cancelling
	Err                 error  // error in determining cancellation
	ClearPaymentMethods bool   // whether we should clear payment methods
	StripeCustomerID    string // stripe customer ID
}

// Returns (whether we should cancel, cancellation reason, whether there was an error, and whether we should remove payment methods)
func shouldCancelOrbSubscription(ctx context.Context, invoice *orb.OrbInvoice, orbClient orb.OrbClient, stripeClient StripeClient) *CancelResult {
	// If the source is not a subscription or the status is not issued, we don't need to cancel
	if invoice.Source != "subscription" || invoice.Status != "issued" {
		log.Info().
			Str("invoice_id", invoice.ID).
			Str("subscription_id", invoice.SubscriptionID).
			Int32("payment_attempts", invoice.PaymentAttempts).
			Str("source", invoice.Source).
			Str("status", invoice.Status).
			Msg("Not canceling subscription yet, not a subscription event or not issued")
		return &CancelResult{
			ShouldCancel:        false,
			Reason:              "",
			Err:                 nil,
			ClearPaymentMethods: false,
		}
	}

	// Check if we need to cancel the subscription based on payment attempt count
	// Second payment attempt indicates one failed payment in the past
	if invoice.PaymentAttempts >= 2 {
		return &CancelResult{
			ShouldCancel:        true,
			Reason:              "2 or more failed payments",
			Err:                 nil,
			ClearPaymentMethods: false,
		}
	}

	// Get the Stripe Customer ID from the Orb customer ID, to look at payments and payment methods
	stripeCustomerID, err := findStripeCustomerIDByOrbCustomerID(ctx, invoice.CustomerID, orbClient)
	if err != nil {
		log.Error().
			Err(err).
			Str("customer_id", invoice.CustomerID).
			Msg("Failed to find Stripe Customer ID for Orb customer ID")
		return &CancelResult{
			ShouldCancel:        false,
			Reason:              "",
			Err:                 fmt.Errorf("failed to Stripe Customer ID for Orb customer ID: %w", err),
			ClearPaymentMethods: false,
		}
	}

	// Check if we need to cancel the subscription based on user's past failed payments
	// If the user has not had a successful payment in the past, cancel the subscription
	// This is to help prevent fraud
	hasSuccessfulPayment, hasSuccessfulPaymentErr := stripeClient.CustomerHasSuccessfulPayment(stripeCustomerID)
	if hasSuccessfulPaymentErr != nil {
		log.Error().
			Err(hasSuccessfulPaymentErr).
			Str("customer_id", invoice.CustomerID).
			Msg("Failed to check for successful payment")
	}

	if !hasSuccessfulPayment {
		return &CancelResult{
			ShouldCancel:        true,
			Reason:              "failed payment and no successful payment in the past",
			Err:                 nil,
			ClearPaymentMethods: true,
			StripeCustomerID:    stripeCustomerID,
		}
	}

	// Check if the user has no payment method attached. If the user has no payment method, cancel immediately.
	// This is to prevent users from removing their credit card to prevent future dunning attempts.
	hasPaymentMethod, hasPaymentMethodErr := stripeClient.HasPaymentMethod(stripeCustomerID)
	if hasPaymentMethodErr != nil {
		log.Error().Err(hasPaymentMethodErr).Str("customer_id", invoice.CustomerID).Msg("Failed to check for payment method")
	}

	if !hasPaymentMethod {
		return &CancelResult{
			ShouldCancel:        true,
			Reason:              "payemnt failed and no payment method attached",
			Err:                 nil,
			ClearPaymentMethods: false,
			StripeCustomerID:    stripeCustomerID,
		}
	}

	if hasPaymentMethodErr != nil || hasSuccessfulPaymentErr != nil {
		return &CancelResult{
			ShouldCancel:        false,
			Reason:              "",
			Err:                 fmt.Errorf("failed to check for payment method or successful payment"),
			ClearPaymentMethods: false,
			StripeCustomerID:    stripeCustomerID,
		}
	}

	return &CancelResult{
		ShouldCancel:        false,
		Reason:              "",
		Err:                 nil,
		ClearPaymentMethods: false,
		StripeCustomerID:    stripeCustomerID,
	}
}

func removePaymentMethodForCustomer(ctx context.Context, stripeCustomerID string, stripeClient StripeClient) error {
	// Remove the payment method from the Stripe customer
	err := stripeClient.RemovePaymentMethodsFromCustomer(stripeCustomerID)
	if err != nil {
		log.Error().
			Err(err).
			Str("customer_id", stripeCustomerID).
			Msg("Failed to remove payment methods from customer")
		return fmt.Errorf("failed to remove payment methods from customer: %w", err)
	}
	return nil
}

func cancelOrbSubscriptionForFailedPayment(ctx context.Context, invoice *orb.OrbInvoice, reason string, orbClient orb.OrbClient, auditLogger *audit.AuditLogger, daoFactory *DAOFactory) error {
	log.Info().
		Str("subscription_id", invoice.SubscriptionID).
		Int32("payment_attempts", invoice.PaymentAttempts).
		Str("reason", reason).
		Msg("Canceling subscription")

	// Check to see if the subscription is already cancelled or scheduled to be cancelled
	sub, err := orbClient.GetUserSubscription(ctx, invoice.SubscriptionID, nil)
	if err != nil {
		log.Error().
			Err(err).
			Str("subscription_id", invoice.SubscriptionID).
			Msg("Failed to get subscription")
		return fmt.Errorf("failed to get subscription: %w", err)
	}
	if !sub.EndDate.IsZero() && sub.OrbStatus == "ended" {
		log.Info().
			Str("subscription_id", invoice.SubscriptionID).
			Msg("Subscription already canceled")
		return nil
	} else if !sub.EndDate.IsZero() && sub.OrbStatus == "active" {
		log.Info().
			Str("subscription_id", invoice.SubscriptionID).
			Msg("Subscription already scheduled to be canceled")
		// Unschedule the pending cancellation before we perform the current cancellation
		err = orbClient.UnschedulePendingSubscriptionCancellation(ctx, invoice.SubscriptionID, nil)
		if err != nil {
			log.Error().
				Err(err).
				Str("subscription_id", invoice.SubscriptionID).
				Msg("Failed to unschedule subscription cancellation")
			return fmt.Errorf("failed to unschedule subscription cancellation: %w", err)
		}
		log.Info().
			Str("subscription_id", invoice.SubscriptionID).
			Msg("Successfully unscheduled subscription cancellation")
	}

	// Cancel the subscription in Orb
	idempotencyKey := fmt.Sprintf("%s-%s-cancel", invoice.SubscriptionID, invoice.ID)
	err = orbClient.CancelOrbSubscription(ctx, invoice.SubscriptionID, orb.PlanChangeImmediate, nil, &idempotencyKey)
	if err != nil {
		log.Error().
			Err(err).
			Str("subscription_id", invoice.SubscriptionID).
			Msg("Failed to cancel subscription in Orb")
		return fmt.Errorf("failed to cancel subscription in Orb: %w", err)
	}

	log.Info().
		Str("subscription_id", invoice.SubscriptionID).
		Msg("Successfully canceled subscription in Orb")

	// Update the subscription record to mark it as cancelled due to payment failure
	err = updateSubscriptionPaymentFailureFlag(ctx, invoice.SubscriptionID, daoFactory)
	if err != nil {
		log.Error().
			Err(err).
			Str("subscription_id", invoice.SubscriptionID).
			Msg("Failed to update subscription payment failure flag")
		return fmt.Errorf("failed to update subscription payment failure flag: %w", err)
	}

	auditLogger.WriteAuditLog(
		"",
		"",
		"",
		fmt.Sprintf("Successfully canceled subscription %s in Orb due to %s", invoice.SubscriptionID, reason),
	)
	return nil
}

// updateSubscriptionPaymentFailureFlag updates the subscription to mark it as cancelled due to payment failure
func updateSubscriptionPaymentFailureFlag(ctx context.Context, subscriptionID string, DAOFactory *DAOFactory) error {
	subscriptionDAO := DAOFactory.GetSubscriptionDAO()

	_, err := subscriptionDAO.TryUpdate(ctx, subscriptionID, func(s *auth_entities.Subscription) bool {
		if !s.CancelledDueToPaymentFailure {
			s.CancelledDueToPaymentFailure = true
			log.Info().
				Str("subscription_id", subscriptionID).
				Msg("Updated subscription to mark as cancelled due to payment failure")
			return true
		}
		return false // No changes needed
	}, DefaultRetry)
	if err != nil {
		return fmt.Errorf("failed to update subscription payment failure flag: %w", err)
	}

	return nil
}

// checkBillingEventProcessorDeadLetterFlag checks if dead letter queue processing is enabled via feature flag
func checkBillingEventProcessorDeadLetterFlag(featureFlagHandle featureflags.FeatureFlagHandle) bool {
	flagVal, err := featureFlagHandle.GetBool("auth_central_billing_event_processor_process_dead_letter_queue", false)
	if err != nil {
		log.Error().Err(err).Msg("Error reading dead letter queue feature flag")
		return false
	}
	return flagVal
}
