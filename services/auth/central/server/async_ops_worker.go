package main

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/integrations/customerio"
	"github.com/augmentcode/augment/services/integrations/orb"
	pubsub "github.com/augmentcode/augment/services/lib/pubsub"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/proto"

	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
)

// Record the latency (in seconds) and status of all async operations. Operation is the name of the
// proto message type (e.g., "CreateTenantForTeam") and status is either "OK" or "ERROR".
var operationLatency = prometheus.NewHistogramVec(
	prometheus.HistogramOpts{
		Name: "au_auth_central_async_ops_latency",
		Help: "Async operations processed with duration in seconds",
		// Default buckets are exponential values that make sense for recording latency.
		Buckets: prometheus.DefBuckets,
	},
	[]string{"operation", "status"},
)

// AsyncOpsWorker is responsible for processing messages from the async-ops pubsub topic
// A generic pubsub topic is used for internal async operations that need to be performed
// by auth central services. The AsyncOpsWorker will listen for messages on the topic and
// dispatch them to the appropriate processor.
type AsyncOpsWorker struct {
	config            *Config
	daoFactory        *DAOFactory
	tenantMap         *TenantMap
	subscribeClient   pubsub.SubscribeClient
	stripeClient      StripeClient
	orbClient         orb.OrbClient
	featureFlagHandle featureflags.FeatureFlagHandle
	auditLogger       *audit.AuditLogger

	// Processors
	userTierChangeProcessor       *UserTierChangeProcessor
	teamPlanChangeProcessor       *TeamPlanChangeProcessor
	tenantCreationProcessor       *TenantCreationProcessor
	invitationResolutionProcessor *InvitationResolutionProcessor
	subscriptionCreationProcessor *SubscriptionCreationProcessor
	invitationEmailProcessor      *InvitationEmailProcessor
	updateSubscriptionProcessor   *UpdateSubscriptionProcessor
}

func NewAsyncOpsWorker(
	ctx context.Context,
	config *Config,
	daoFactory *DAOFactory,
	tenantMap *TenantMap,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	stripeClient StripeClient,
	orbClient orb.OrbClient,
	featureFlagHandle featureflags.FeatureFlagHandle,
	requestInsightPublisher ripublisher.RequestInsightPublisher,
	auditLogger *audit.AuditLogger,
) (*AsyncOpsWorker, error) {
	prometheus.MustRegister(operationLatency)

	subscribeConfig := &pubsub.SubscribeClientConfig{
		ProjectId:                        config.GCP.ProjectID,
		SubscriptionId:                   config.AsyncOpsConfig.SubscriptionName,
		DeadLetterSubscriptionId:         config.AsyncOpsConfig.DeadLetterSubscriptionName,
		MaxConcurrentReceivers:           config.AsyncOpsConfig.MaxConcurrentReceivers,
		DeadLetterMaxConcurrentReceivers: config.AsyncOpsConfig.DeadLetterMaxConcurrentReceivers,
		ConfigureDeadLetterHandling: func() bool {
			return checkDeadLetterFlag(featureFlagHandle)
		},
	}

	subscribeClient, err := pubsub.NewSubscribeClient(ctx, subscribeConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create subscribe client: %w", err)
	}

	log.Info().Msg("Creating user tier change processor")
	userTierChangeProcessor, err := NewUserTierChangeProcessor(config, daoFactory, featureFlagHandle, auditLogger, tenantMap)
	if err != nil {
		return nil, fmt.Errorf("failed to create user tier change processor: %w", err)
	}

	var teamPlanChangeProcessor *TeamPlanChangeProcessor
	if orbClient != nil {
		log.Info().Msg("Creating team plan change processor")
		var err error
		teamPlanChangeProcessor, err = NewTeamPlanChangeProcessor(daoFactory, orbClient, &config.Orb, featureFlagHandle, auditLogger, tenantMap)
		if err != nil {
			return nil, fmt.Errorf("failed to create team plan change processor: %w", err)
		}
	} else {
		log.Info().Msg("Orb client is nil, not creating team plan change processor")
	}

	log.Info().Msg("Creating tenant creation processor")
	tenantCreationProcessor, err := NewTenantCreationProcessor(
		daoFactory, tenantMap, tokenExchangeClient, orbClient, requestInsightPublisher, auditLogger)
	if err != nil {
		return nil, fmt.Errorf("failed to create tenant creation processor: %w", err)
	}

	log.Info().Msg("Creating invitation resolution processor")
	invitationResolutionProcessor, err := NewInvitationResolutionProcessor(
		daoFactory, tenantMap, stripeClient, requestInsightPublisher, &config.Orb, orbClient, featureFlagHandle, auditLogger)
	if err != nil {
		return nil, fmt.Errorf("failed to create invitation resolution processor: %w", err)
	}

	log.Info().Msg("Creating subscription creation processor")
	subscriptionCreationProcessor, err := NewSubscriptionCreationProcessor(
		daoFactory, tenantMap, stripeClient, &config.Orb, orbClient,
		requestInsightPublisher, auditLogger)
	if err != nil {
		return nil, fmt.Errorf("failed to create subscription creation processor: %w", err)
	}

	// Initialize CustomerioClient for the invitation email processor
	log.Info().Msg("Initializing CustomerioClient")
	var customerioClient customerio.CustomerioClient
	key, err := os.ReadFile(config.CustomerIOAPIKeyPath)
	if err != nil {
		log.Error().Err(err).Msg("Failed to read customer.io API key")
		customerioClient = customerio.NewCustomerioClient("")
	} else {
		cleanKey := strings.TrimSpace(string(key))
		customerioClient = customerio.NewCustomerioClient(cleanKey)
		log.Info().Msg("CustomerioClient initialized")
	}

	log.Info().Msg("Creating invitation email processor")
	invitationEmailProcessor, err := NewInvitationEmailProcessor(
		daoFactory, customerioClient, auditLogger, config.AuthCentralHostname, featureFlagHandle)
	if err != nil {
		return nil, fmt.Errorf("failed to create invitation email processor: %w", err)
	}

	log.Info().Msg("Creating Subscription Update Processor")
	updateSubscriptionProcessor, err := NewUpdateSubscriptionProcessor(
		daoFactory, orbClient, &config.Orb, requestInsightPublisher, auditLogger)
	if err != nil {
		return nil, fmt.Errorf("failed to create subscription update processor: %w", err)
	}

	return &AsyncOpsWorker{
		config:                        config,
		daoFactory:                    daoFactory,
		tenantMap:                     tenantMap,
		subscribeClient:               subscribeClient,
		stripeClient:                  stripeClient,
		orbClient:                     orbClient,
		featureFlagHandle:             featureFlagHandle,
		auditLogger:                   auditLogger,
		userTierChangeProcessor:       userTierChangeProcessor,
		teamPlanChangeProcessor:       teamPlanChangeProcessor,
		tenantCreationProcessor:       tenantCreationProcessor,
		invitationResolutionProcessor: invitationResolutionProcessor,
		subscriptionCreationProcessor: subscriptionCreationProcessor,
		invitationEmailProcessor:      invitationEmailProcessor,
		updateSubscriptionProcessor:   updateSubscriptionProcessor,
	}, nil
}

func (w *AsyncOpsWorker) Run(ctx context.Context) error {
	log.Info().Msg("Starting AsyncOpsWorker")

	err := w.subscribeClient.Receive(ctx, func(ctx context.Context, msgData []byte) (returnErr error) {
		startTime := time.Now()
		operation := "unknown"
		defer func() {
			status := "OK"
			if returnErr != nil {
				status = "ERROR"
			}
			operationLatency.WithLabelValues(operation, status).Observe(time.Since(startTime).Seconds())
		}()

		// Parse the message as proto
		wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{}
		err := proto.Unmarshal(msgData, wrapperMsg)
		if err != nil {
			log.Error().Err(err).Msg("Failed to unmarshal message as protobuf")
			return nil // Acknowledge the message even if we can't parse it
		}

		switch msg := wrapperMsg.Message.(type) {
		case *auth_internal.AuthCentralAsyncOpsMessage_UserTierChangeMessage:
			operation = "UserTierChange"
			log.Info().Msg("Received UserTierChangeMessage")
			return w.userTierChangeProcessor.Process(ctx, msg.UserTierChangeMessage)
		case *auth_internal.AuthCentralAsyncOpsMessage_TeamPlanChangeMessage:
			operation = "TeamPlanChange"
			log.Info().Msg("Received TeamPlanChangeMessage")
			if w.teamPlanChangeProcessor == nil {
				log.Error().Msg("TeamPlanChangeProcessor is not initialized because Orb client is not available. Cannot process TeamPlanChangeMessage.")
				return fmt.Errorf("team plan change processor not initialized as Orb client is not available")
			}
			return w.teamPlanChangeProcessor.Process(ctx, msg.TeamPlanChangeMessage)
		case *auth_internal.AuthCentralAsyncOpsMessage_CreateTenantForTeamMessage:
			operation = "CreateTenantForTeam"
			log.Info().Msg("Received CreateTenantForTeamMessage")
			return w.tenantCreationProcessor.Process(ctx, msg.CreateTenantForTeamMessage)
		case *auth_internal.AuthCentralAsyncOpsMessage_ResolveInvitationsMessage:
			operation = "ResolveInvitations"
			log.Info().Msg("Received ResolveInvitationsMessage")
			return w.invitationResolutionProcessor.Process(ctx, msg.ResolveInvitationsMessage)
		case *auth_internal.AuthCentralAsyncOpsMessage_CreateSubscriptionMessage:
			operation = "CreateSubscription"
			log.Info().Msg("Received CreateSubscriptionMessage")
			return w.subscriptionCreationProcessor.Process(ctx, msg.CreateSubscriptionMessage)
		case *auth_internal.AuthCentralAsyncOpsMessage_SendInvitationEmailMessage:
			operation = "SendInvitationEmail"
			log.Info().Msg("Received SendInvitationEmailMessage")
			return w.invitationEmailProcessor.Process(ctx, msg.SendInvitationEmailMessage)
		case *auth_internal.AuthCentralAsyncOpsMessage_UpdateSubscriptionMessage:
			operation = "UpdateSubscription"
			log.Info().Msg("Received UpdateSubscriptionMessage")
			return w.updateSubscriptionProcessor.Process(ctx, msg.UpdateSubscriptionMessage)
		default:
			log.Info().Msg("Received unrecognized message, ignoring...")
			return nil
		}
	})

	if err != nil && err != context.Canceled {
		log.Error().Err(err).Msg("AsyncOpsWorker stopped with error")
	} else {
		log.Info().Msg("AsyncOpsWorker stopped")
	}

	return err
}

func (w *AsyncOpsWorker) Stop() {
	if w.subscribeClient != nil {
		w.subscribeClient.Close()
	}
}
