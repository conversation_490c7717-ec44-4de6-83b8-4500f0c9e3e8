syntax = "proto3";
package front_end_token_service;

import "services/auth/central/server/auth.proto";
import "services/auth/central/server/auth_entities.proto";

service FrontEndTokenService {
  // Create a one-time code that can be redeemed for a token
  rpc CreateCode(CreateCodeRequest) returns (CreateCodeResponse);

  // Front end equivalent of AddUserToTenant
  rpc EnsureUserInTenant(EnsureUserInTenantRequest) returns (EnsureUserInTenantResponse);

  // Get the nonce used to check whether a user's permanent session cookie with auth-central is still valid
  rpc GetNonceForUser(GetNonceForUserRequest) returns (GetNonceForUserResponse);

  // Fetch whether user has approved a certain version of the terms of servcie
  rpc GetTermsApproval(GetTermsApprovalRequest) returns (GetTermsApprovalResponse);

  // Get the info associated with a front-end token
  rpc GetTokenInfo(GetTokenInfoRequest) returns (GetTokenInfoResponse);

  // Returns the existing augment user, given an identity provider user ID (a.k.a. sub in OIDC) and an
  // e-mail address
  rpc GetUser(GetUserRequest) returns (GetUserResponse);

  // Returns the existing augment user, given an identity provider user ID (a.k.a. sub in OIDC) and an
  // e-mail address. Also returns any similar email addresses that were found.
  rpc GetUserAndSimilarEmails(GetUserAndSimilarEmailsRequest) returns (GetUserAndSimilarEmailsResponse);

  // Check if a user is an admin of any of their tenants
  rpc IsUserAdmin(IsUserAdminRequest) returns (IsUserAdminResponse);

  // Write whether a user has approved a certain version of the terms of service
  rpc SetTermsApproval(SetTermsApprovalRequest) returns (SetTermsApprovalResponse);

  // Check whether a signup is allowed at this time
  rpc SignupAllowed(SignupAllowedRequest) returns (SignupAllowedResponse);

  // Check how many signups are available at this time
  rpc SignupCreditsAvailable(SignupCreditsAvailableRequest) returns (SignupCreditsAvailableResponse);

  // Test only - set the signup parameters
  rpc TestOnlySetSignupParameters(TestOnlySetSignupParametersRequest) returns (TestOnlySetSignupParametersResponse);

  // Redeem a one-time code for a token
  rpc TokenFromCode(TokenFromCodeRequest) returns (TokenFromCodeResponse);

  // Shutdown the server - used for graceful shutdown
  rpc Shutdown(ShutdownRequest) returns (ShutdownResponse);

  // See documentation in auth.proto's TeamManagementService. This endpoint exists here in addition
  // to TeamManagementService because it's called during the signup/login flow.
  rpc GetUserInvitations(auth.GetUserInvitationsRequest) returns (auth.GetUserInvitationsResponse);

  // See documentation in auth.proto's TeamManagementService. This endpoint exists here in addition
  // to TeamManagementService because it's called during the signup/login flow.
  rpc ResolveInvitations(auth.ResolveInvitationsRequest) returns (auth.ResolveInvitationsResponse);
  rpc GetResolveInvitationsStatus(auth.GetResolveInvitationsStatusRequest) returns (auth.GetResolveInvitationsStatusResponse);

  // Run user checks with external services like Verosint
  rpc RunUserSecurityChecks(RunUserSecurityChecksRequest) returns (RunUserSecurityChecksResponse);
}

message GetNonceForUserRequest {
  string user_id = 1;
}

message GetNonceForUserResponse {
  uint64 nonce = 1;
}

message TokenFromCodeRequest {
  string code = 1;
  string code_verifier = 2;
  string client_id = 3;
  string redirect_uri = 4;
  string grant_type = 5;
  int64 expiration_time_seconds = 6;
}

message TokenFromCodeResponse {
  // If error is set, none of the other should be
  string error = 1;
  string access_token = 2;
  int64 expires_in = 3;
}

message ShutdownRequest {}
message ShutdownResponse {}

message CreateCodeRequest {
  string code = 1;
  string email = 2;
  string idp_user_id = 3;
  string augment_user_id = 4;
  string client_id = 5;
  string tenant_id = 6;
  string redirect_uri = 7;
  string code_challenge = 8;
}

message CreateCodeResponse {}

message GetTermsApprovalRequest {
  string email = 1;
  string revision = 2;
}

message GetTermsApprovalResponse {
  bool approved = 1;
}

message SetTermsApprovalRequest {
  string email = 1;
  string revision = 2;
  bool approved = 3;
}

message SetTermsApprovalResponse {}

message GetUserRequest {
  string idp_user_id = 1;
  string email_address = 2;
}

message GetUserResponse {
  auth_entities.User user = 1;
}

enum TenantEnsureMode {
  // Return error if user is not in the tenant (default)
  TENANT_ENSURE_MODE_REQUIRE_EXISTING = 0;
  // Add user to tenant only if they have no existing tenants
  TENANT_ENSURE_MODE_ADD_IF_EMPTY = 1;
  // Move user to tenant, removing them from all other tenants if there are in any
  TENANT_ENSURE_MODE_OVERWRITE = 2;
}

message EnsureUserInTenantRequest {
  // If an empty string, a new user will be created.
  // Otherwise the existing user will be added to the tenant.
  string augment_user_id = 1;
  string email_address = 2;
  string tenant_id = 3;
  string idp_user_id = 4;
  // How to handle ensuring the user is in the tenant
  TenantEnsureMode mode = 5;
  // User's names returned from identity providers
  string given_name = 6;
  string family_name = 7;
}

message EnsureUserInTenantResponse {
  auth_entities.User user = 1;
}

message SignupAllowedRequest {}

message SignupAllowedResponse {
  bool allowed = 1;
}

message SignupCreditsAvailableRequest {}

message SignupCreditsAvailableResponse {
  float credits_available = 1;
}

message TestOnlySetSignupParametersRequest {
  int32 max_burst = 1;
  int32 signup_per_day = 2;
}

message TestOnlySetSignupParametersResponse {}

message GetTokenInfoRequest {
  string token = 1;
}

message GetTokenInfoResponse {
  string augment_user_id = 1;
  string tenant_id = 2;
}

message GetUserAndSimilarEmailsRequest {
  string idp_user_id = 1;
  string email_address = 2;
}

message GetUserAndSimilarEmailsResponse {
  auth_entities.User user = 1;
  repeated string similar_emails = 2;
}

// Request to check if a user is an admin of any of their tenants
message IsUserAdminRequest {
  string user_id = 1;
}

// Response indicating whether the user is an admin
message IsUserAdminResponse {
  // True if the user is an admin of any of their tenants, false otherwise
  bool is_admin = 1;
}

message RunUserSecurityChecksRequest {
  string augment_user_id = 1;
  string idp_user_id = 2 [debug_redact = true];
  string email_address = 3 [debug_redact = true];
  string user_agent = 4;
  string ip_address = 5 [debug_redact = true];
  string verosint_device_id = 6;
  string verisoul_session_id = 7;
  string recaptcha_token = 8;
}

message RunUserSecurityChecksResponse {
  // True if the user passed all security checks, false otherwise
  bool passed = 1;
}
