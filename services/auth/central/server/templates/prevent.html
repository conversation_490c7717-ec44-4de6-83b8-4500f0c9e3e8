{% if verisoul %}
<script async src="https://js.verisoul.ai/{{ verisoul["env"] }}/bundle.js" verisoul-project-id="{{ verisoul["project_id"] }}"></script>
<script>!function(e){if(e.Verisoul)return;const r=[],t={},o=new Proxy(t,{get:(e,o)=>o in t?t[o]:(...e)=>new Promise(((t,n)=>r.push([o,e,t,n]))),set:(e,r,o)=>(t[r]=o,!0)});e.Verisoul=o;const n=()=>{Object.keys(t).length&&r.splice(0).forEach((([e,r,o,n])=>{try{Promise.resolve(t[e](...r)).then(o,n)}catch(e){n(e)}}))},c=document.querySelector("script[verisoul-project-id]"),s=()=>r.splice(0).forEach((([,,,e])=>e(new Error("Failed to load Verisoul SDK"))));if(!c)return void s();c.addEventListener("load",n,{once:!0}),c.addEventListener("error",(()=>{clearInterval(i),s()}),{once:!0});const i=setInterval((()=>{Object.keys(t).length&&(clearInterval(i),n())}),40)}(window);</script>
{% endif %}
{% if recaptcha_site_key %}
<script async src="https://www.google.com/recaptcha/enterprise.js?render={{ recaptcha_site_key }}"></script>
{% endif %}
{% if verosint %}
<script async src="https://cdn.verosint.com/verosintjs/0.2.11/verosintjs.umd.js" verosint-tag></script>
{% endif %}
<script>
  function withTimeout(promise, timeoutMs) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => reject(new Error("Execution aborted due to timeout.")), timeoutMs);
      promise.finally(() => clearTimeout(timer)).then(resolve, reject);
    });
  }

  async function onClick(e) {
    e.preventDefault();

    {% if verisoul %}
    const verisoulPromise = withTimeout(window.Verisoul.session(), 5000);
    {% else %}
    const verisoulPromise = Promise.resolve({ session_id: '' });
    {% endif %}
    {% if recaptcha_site_key %}
    const grecaptchaPromise = withTimeout((async () => {
      await new Promise((resolve) => {
        grecaptcha.enterprise.ready(resolve);
      });
      return await grecaptcha.enterprise.execute('{{ recaptcha_site_key }}', { action: getAction() });
    })(), 5000);
    {% else %}
    const grecaptchaPromise = Promise.resolve('');
    {% endif %}
    {% if verosint %}
    const verosintPromise = withTimeout((async () => {
      await verosintLoadPromise;
      return await VerosintJS.getDeviceId();
    })(), 5000);
    {% else %}
    const verosintPromise = Promise.resolve('');
    {% endif %}

    const [verisoulResponse, grecaptchaResponse, verosintResponse] = await Promise.allSettled([verisoulPromise, grecaptchaPromise, verosintPromise]);
    if (verisoulResponse.status === 'fulfilled') {
      document.getElementById('verisoul-session-id').value = verisoulResponse.value.session_id;
    } else {
      console.log('Failed to get verisoul session', verisoulResponse.reason);
    }
    if (grecaptchaResponse.status === 'fulfilled') {
      document.getElementById('g-recaptcha-response').value = grecaptchaResponse.value;
    } else {
      console.log('Failed to get grecaptcha response', grecaptchaResponse.reason);
    }
    if (verosintResponse.status === 'fulfilled') {
      document.getElementById('verosint-deviceid').value = verosintResponse.value;
    } else {
      console.log('Failed to get verosint device id', verosintResponse.reason);
    }
    document.getElementById('action-form').submit();
  }

  {% if recaptcha_site_key %}
  if (typeof window['grecaptcha'] === 'undefined') {
      grecaptcha = {
        ready: function(cb) {
          const c = '___grecaptcha_cfg';
          window[c] = window[c] || {};
          (window[c]['fns'] = window[c]['fns'] || []).push(cb);
        }
      };
  }
  {% endif %}

  {% if verosint %}
  v = document.querySelector("script[verosint-tag]");
  verosintLoadPromise = new Promise((resolve, reject) => {
    v.addEventListener("load", resolve);
    v.addEventListener("error", reject);
  });
  {% endif %}
</script>
