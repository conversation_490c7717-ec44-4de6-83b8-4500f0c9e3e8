package main

import (
	"context"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"cloud.google.com/go/bigtable"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/base/logging"
	"github.com/augmentcode/augment/base/logging/audit"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	authpb "github.com/augmentcode/augment/services/auth/central/server/auth"
	authdevpb "github.com/augmentcode/augment/services/auth/central/server/auth_dev"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	front_end_token_service "github.com/augmentcode/augment/services/auth/central/server/front_end_token_service"
	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/reflection"
)

var (
	configFile        = flag.String("config", "", "Path to config file")
	disableGetFeature = featureflags.NewBoolFlag("example_disable_get_feature", false)
)

var creditsGauge = prometheus.NewGauge(
	prometheus.GaugeOpts{
		Name: "au_auth_central_signup_credits",
		Help: "Number of signup credits available",
	},
)

var usersByTenantGauge = prometheus.NewGaugeVec(
	prometheus.GaugeOpts{
		Name: "au_auth_central_users_by_tenant",
		Help: "Number of users by tenant",
	},
	[]string{"tenant"},
)

var usersWithMultipleTenantsGauge = prometheus.NewGauge(
	prometheus.GaugeOpts{
		Name: "au_auth_central_users_with_multiple_tenants",
		Help: "Number of users with multiple tenants",
	},
)

func main() {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	logging.SetupServerLogging()

	flag.Parse()

	config, err := LoadConfig(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to load configuration")
	}
	log.Info().Msgf("Config: %v", config)

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	// Setup metrics
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(
			grpcprom.WithHistogramOpts(&prometheus.HistogramOpts{
				Subsystem: "auth_central_grpc",
			}),
		),
	)
	prometheus.MustRegister(srvMetrics)
	prometheus.MustRegister(creditsGauge)
	prometheus.MustRegister(usersByTenantGauge)
	prometheus.MustRegister(usersWithMultipleTenantsGauge)

	// Start Prometheus metrics HTTP server
	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(config.PrometheusBindAddress, nil)
		if err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	var featureFlagHandle featureflags.FeatureFlagHandle
	if config.FeatureFlagsSdkKeyPath != "" {
		var err error
		featureFlagHandle, err = featureflags.NewFeatureFlagHandleFromFile(
			config.FeatureFlagsSdkKeyPath,
			config.DynamicFeatureFlagsEndpoint,
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating feature flag handle")
		}
	} else {
		log.Info().Msg("Feature flags disabled: using local implementation")
		featureFlagHandle = featureflags.NewLocalFeatureFlagHandler()
	}

	var requestInsightPublisher ripublisher.RequestInsightPublisher
	if config.RequestInsightPublisherConfigPath == "" {
		log.Info().Msg("Request insight publisher disabled: using mock implementation")
		requestInsightPublisher = ripublisher.NewRequestInsightPublisherMock()
	} else {
		requestInsightPublisher, err = ripublisher.NewRequestInsightPublisherFromFile(
			ctx, config.RequestInsightPublisherConfigPath)
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating request insight publisher")
		}
	}
	defer requestInsightPublisher.Close()

	// Initialize async operations publisher
	asyncOpsPublisher, err := NewAsyncOpsPublisher(ctx, config.GCP.ProjectID, config.AsyncOpsConfig.TopicName)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating async operations publisher")
	}
	defer asyncOpsPublisher.Close()

	// Create client credentials for the central client
	centralClientCreds, err := tlsconfig.GetClientTls(config.GRPC.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating central client credentials")
	}

	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.GRPC.ServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}

	// Set up service token auth
	tokenExchangeClient, err := tokenexchange.New(
		config.AuthConfig.TokenExchangeEndpoint,
		config.Namespace,
		grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating token exchange client")
	}
	defer tokenExchangeClient.Close()

	// Setup gRPC server options
	var opts []grpc.ServerOption

	// TLS config
	opts = append(opts, grpc.Creds(serverTls))

	// OpenTelemetry
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))

	// Metrics interceptors
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
	))

	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	opts = append(opts,
		grpc.ChainUnaryInterceptor(authInterceptor.Intercept),
		grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept),
		// Motivation: periodically disconnecting clients causes the clients to
		// discover new replicas.
		//
		// This means any RPC over 30 seconds is at higher risk of being
		// interrupted. Long-lived RPCs for migration should be spawned
		// to the background or implemented in a separate service/job.
		grpc.KeepaliveParams(keepalive.ServerParameters{
			MaxConnectionAge:      300 * time.Second,
			MaxConnectionAgeGrace: 30 * time.Second,
		}),
	)

	grpcServer := grpc.NewServer(opts...)
	srvMetrics.InitializeMetrics(grpcServer)
	reflection.Register(grpcServer)
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	// Setup private gRPC server
	opts = nil
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		srvMetrics.StreamServerInterceptor(),
	))
	privateGrpcServer := grpc.NewServer(opts...)
	srvMetrics.InitializeMetrics(privateGrpcServer)
	reflection.Register(privateGrpcServer)
	healthgrpc.RegisterHealthServer(privateGrpcServer, health.NewServer())

	// Setup BigTable
	client, err := bigtable.NewClient(ctx, config.GCP.ProjectID, config.GCP.InstanceID)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create BigTable client")
	}
	defer client.Close()

	bigtableTable := client.Open(config.GCP.TableName)

	daoFactory := NewDAOFactory(bigtableTable)
	tenantWatcherClient := tenantwatcherclient.New(
		config.TenantWatcher.TenantWatcherEndpoint,
		grpc.WithTransportCredentials(centralClientCreds),
	)
	stripeClient := &DefaultStripeClient{}
	var orbClient orb.OrbClient
	if config.Orb.Enabled {
		// Read the Orb API key from the specified file path
		key, err := os.ReadFile(config.Orb.ApiKeyPath)
		if err != nil {
			log.Error().Err(err).Msg("Failed to read Orb secret key")
		} else {
			cleanKey := strings.TrimSpace(string(key))
			orbClient = orb.NewOrbClient(cleanKey, featureFlagHandle)
			log.Info().Msg("Orb client initialized successfully")
		}
	} else {
		log.Info().Msg("Orb client disabled")
	}
	auditLogger := audit.NewDefaultAuditLogger()
	tenantMap := NewTenantMap(
		daoFactory,
		tenantWatcherClient,
		config.TenantWatcher.APIProxyHostnameDomain,
		featureFlagHandle,
		asyncOpsPublisher,
		auditLogger,
	)
	server := NewAuthGrpcServer(
		featureFlagHandle,
		daoFactory,
		tenantMap,
		auditLogger,
		requestInsightPublisher,
		asyncOpsPublisher,
		&config.Stripe,
		&config.Orb,
		stripeClient,
	)
	authpb.RegisterAuthServiceServer(grpcServer, server)

	teamManagementServer := NewTeamManagementServer(
		featureFlagHandle,
		daoFactory,
		tenantMap,
		auditLogger,
		requestInsightPublisher,
		asyncOpsPublisher,
		stripeClient,
		&config.Orb,
		orbClient,
		nil, // Use default random selector
	)
	authpb.RegisterTeamManagementServiceServer(grpcServer, teamManagementServer)

	if config.DevServicerEnabled {
		authDevServicer := NewAuthDevServicer(
			&SubscriptionHandler{
				daoFactory: daoFactory,
			},
			daoFactory,
		)
		authdevpb.RegisterAuthDevServiceServer(grpcServer, authDevServicer)
	}

	shutdownChannel := make(chan struct{})

	signupLimiter := NewSignupLimiter(
		NewSignupLimiterPersister(bigtableTable),
		featureFlagHandle,
	)

	var runUserSecurityChecks runUserSecurityChecks

	if config.Verosint != nil {
		rawVerosintApiKey, err := os.ReadFile(config.Verosint.ApiKeyPath)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to read Verosint API key")
		}
		verosintApiKey := strings.TrimSpace(string(rawVerosintApiKey))

		verosintTimeout, err := time.ParseDuration(config.Verosint.Timeout)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to parse Verosint timeout")
		}

		runUserSecurityChecks = &runUserSecurityChecksHandler{
			verosintReportFetcher: &verosintReportFetcherImpl{
				apiKey:        secretstring.New(verosintApiKey),
				endpoint:      config.Verosint.Endpoint,
				httpsPostFunc: doHTTPSPost,
				timeout:       verosintTimeout,
			},
			requestInsightPublisher: requestInsightPublisher,
			logger:                  log.Logger,
			verosintEnabled: func() bool {
				ret, err := featureFlagHandle.GetBool("auth_central_verosint_reporting", true)
				if err != nil {
					return true
				}
				return ret
			},
		}
	}

	frontEndTokenServiceServer := NewFrontEndTokenServiceGrpcServer(
		featureFlagHandle,
		daoFactory,
		tenantMap,
		tokenExchangeClient,
		auditLogger,
		requestInsightPublisher,
		config,
		shutdownChannel,
		signupLimiter,
		teamManagementServer,
		stripeClient,
		runUserSecurityChecks,
	)

	front_end_token_service.RegisterFrontEndTokenServiceServer(privateGrpcServer, frontEndTokenServiceServer)

	// Periodically refresh gauges from DB
	go func() {
		for {
			credits, err := signupLimiter.CreditsAvailable(ctx)
			if err != nil {
				log.Error().Err(err).Msg("Failed to get signup credits available")
			} else {
				creditsGauge.Set(credits)
			}

			counts := make(map[string]int)

			err = daoFactory.GetUserTenantMappingDAO("").ListMappingsForAllTenants(ctx, func(mapping *auth_entities.UserTenantMapping) bool {
				counts[mapping.Tenant] = counts[mapping.Tenant] + 1
				return true
			})
			if err != nil {
				log.Error().Err(err).Msg("Failed to get user counts by tenant")
			} else {
				for tenant, count := range counts {
					usersByTenantGauge.WithLabelValues(tenant).Set(float64(count))
				}
			}

			select {
			case <-ctx.Done():
				return
			case <-time.After(30 * time.Second):
			}
		}
	}()

	// Periodically scan for users with multiple tenants
	go func() {
		for {
			userCount := 0
			userDao := daoFactory.GetUserDAO()

			err := userDao.FindAll(ctx, func(user *auth_entities.User) bool {
				if len(user.Tenants) > 1 {
					userCount++
					log.Info().Msgf("User %s has multiple tenants: %v", user.Id, user.Tenants)
				}
				return true
			})

			if err != nil {
				log.Error().Err(err).Msg("Failed to scan users for multiple tenants")
			} else {
				log.Info().Msgf("Found %d users with multiple tenants", userCount)
				usersWithMultipleTenantsGauge.Set(float64(userCount))
			}

			select {
			case <-ctx.Done():
				return
			case <-time.After(10 * time.Minute): // Check every 10 minutes
			}
		}
	}()

	// Setup wait group
	var wg sync.WaitGroup

	// Setup listeners
	var listeners []net.Listener
	for _, port := range config.GRPC.Ports {
		lis, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
		if err != nil {
			log.Fatal().Err(err).Msgf("Failed to listen on port %d", port)
		}
		listeners = append(listeners, lis)
		log.Info().Msgf("Listening on %v", lis.Addr())
		wg.Add(1)
	}

	if len(listeners) == 0 {
		log.Fatal().Msg("No ports configured to listen on")
	}

	privateListener, err := net.Listen("tcp", fmt.Sprintf(":%d", config.GRPC.PrivatePort))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen on port %d", config.GRPC.PrivatePort)
	}
	wg.Add(1)
	go func() {
		defer wg.Done()
		log.Info().Msgf("Private service listening on %v", privateListener.Addr())
		if err := privateGrpcServer.Serve(privateListener); err != nil && err != grpc.ErrServerStopped {
			log.Error().Err(err).Msg("Failed to serve private gRPC server")
			cancel()
		}
	}()

	// Setup revoker
	var revoker *Revoker = nil
	if config.RevokerConfig.SubscriptionName != "" {
		revoker, err = NewRevoker(ctx, config, daoFactory)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to create revoker")
		}
		wg.Add(1)
	} else {
		log.Info().Msg("Revoker disabled")
	}

	// Setup async ops worker
	var asyncOpsWorker *AsyncOpsWorker
	if config.AsyncOpsConfig.SubscriptionName != "" {
		asyncOpsWorker, err = NewAsyncOpsWorker(
			ctx, config, daoFactory, tenantMap, tokenExchangeClient, stripeClient, orbClient,
			featureFlagHandle, requestInsightPublisher, auditLogger,
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to create async ops worker")
		}
		wg.Add(1)
	} else {
		log.Info().Msg("Async ops worker disabled")
	}

	// We're going to swallow SIGTERM - the front end python will terminate us
	// Otherwise, we may terminate first when python needs our services.
	signal.Ignore(syscall.SIGTERM)
	// Start servers
	for i, lis := range listeners {
		go func(l net.Listener, index int) {
			defer wg.Done()
			log.Info().Msgf("Starting server on listener %d", index)
			if err := grpcServer.Serve(l); err != nil && err != grpc.ErrServerStopped {
				log.Error().Err(err).Msgf("Failed to serve on listener %d", index)
				cancel()
			}
		}(lis, i)
	}

	// Setup stripe event processor
	var stripeEventProcessor *StripeEventProcessor

	// Check if the Stripe event processor is enabled via feature flag
	stripeProcessorEnabledFlag, err := featureFlagHandle.GetBool("auth_central_enable_stripe_event_processor", false)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to get auth_central_enable_stripe_event_processor feature flag, defaulting to false")
		stripeProcessorEnabledFlag = false
	}
	// Only enable the Stripe event processor if both the subscription is configured and the feature flag is enabled
	if config.StripeEventProcessorConfig.SubscriptionName != "" && stripeProcessorEnabledFlag {
		stripeEventProcessor, err = NewStripeEventProcessor(ctx, config, daoFactory, featureFlagHandle)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to create stripe event processor")
		}
		wg.Add(1)
	} else {
		if !stripeProcessorEnabledFlag {
			log.Info().Msg("Stripe event processor disabled by feature flag")
		} else {
			log.Info().Msg("Stripe event processor disabled due to missing subscription")
		}
	}

	// Setup billing event processor
	var billingEventProcessor *BillingEventProcessor
	// Check if the Billing event processor is enabled via feature flag
	billingProcessorEnabledFlag, err := featureFlagHandle.GetBool("auth_central_enable_billing_event_processor", false)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to get auth_central_enable_billing_event_processor feature flag, defaulting to false")
		billingProcessorEnabledFlag = false
	}
	// Only enable the Billing event processor if both the subscription is configured and the feature flag is enabled
	if config.BillingEventProcessorConfig.SubscriptionName != "" && billingProcessorEnabledFlag {
		billingEventProcessor, err = NewBillingEventProcessor(ctx, config, daoFactory, orbClient, stripeClient, tenantMap, featureFlagHandle, auditLogger)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to create billing event processor")
		}
		log.Info().Msg("Billing event processor enabled")
		wg.Add(1)
	} else {
		if !billingProcessorEnabledFlag {
			log.Info().Msg("Billing event processor disabled by feature flag")
		} else {
			log.Info().Msg("Billing event processor disabled due to missing subscription")
		}
	}

	privatePort := privateListener.Addr().(*net.TCPAddr).Port
	publicPort := listeners[0].Addr().(*net.TCPAddr).Port
	log.Info().Int("public_port", publicPort).Int("private_port", privatePort).Msg("Server up")

	// Start revoker
	if revoker != nil {
		go func() {
			defer wg.Done()
			if err := revoker.Run(ctx); err != nil && err != context.Canceled {
				log.Error().Err(err).Msg("Error running revoker")
				cancel()
			}
		}()
	}

	// Start async ops worker
	if asyncOpsWorker != nil {
		go func() {
			defer wg.Done()
			if err := asyncOpsWorker.Run(ctx); err != nil && err != context.Canceled {
				log.Error().Err(err).Msg("Error running async ops worker")
				cancel()
			}
		}()
	}

	// Start stripe event processor
	if stripeEventProcessor != nil {
		go func() {
			defer wg.Done()
			if err := stripeEventProcessor.Run(ctx); err != nil && err != context.Canceled {
				log.Error().Err(err).Msg("Error running stripe event processor")
				cancel()
			}
		}()
	}

	// Start billing event processor
	if billingEventProcessor != nil {
		go func() {
			defer wg.Done()
			if err := billingEventProcessor.Run(ctx); err != nil && err != context.Canceled {
				log.Error().Err(err).Msg("Error running billing event processor")
				cancel()
			}
		}()
	}

	// Wait for shutdown signal
	select {
	case <-shutdownChannel:
		log.Info().Msgf("Received shutdown")
		cancel()
	case <-ctx.Done():
		log.Info().Msg("Context cancelled")
	}

	// Graceful shutdown
	if revoker != nil {
		revoker.Stop()
	}
	if asyncOpsWorker != nil {
		asyncOpsWorker.Stop()
	}
	if stripeEventProcessor != nil {
		stripeEventProcessor.Stop()
	}
	if billingEventProcessor != nil {
		billingEventProcessor.Stop()
	}
	tenantMap.Close()
	grpcServer.GracefulStop()
	privateGrpcServer.GracefulStop()
	wg.Wait()
	log.Info().Msg("Server stopped")
}
