"""Service module for handling user invitations."""

import logging
from typing import Any, List, Dict

import grpc
from google.protobuf.timestamp_pb2 import Timestamp

from services.auth.central.server import auth_pb2
from services.auth.central.server import front_end_token_service_pb2

# This will be set by the app.py module when initializing
front_end_token_service = None


def initialize(token_service: Any):
    """Initialize the invitation service with dependencies.

    Args:
        token_service: The front_end_token_service gRPC stub
    """
    global front_end_token_service
    front_end_token_service = token_service


def get_user_invitations(user_email: str) -> List[Dict[str, Any]]:
    """Get invitations for a user by email address.

    This calls the front_end_token_service.go GetUserInvitations endpoint to retrieve
    actual invitations for the user.

    Args:
        user_email: The email address of the user to get invitations for

    Returns:
        A list of invitation dictionaries with id, tenant_id, inviter_email, and created_at
    """
    if not user_email:
        raise ValueError("User email is required to fetch invitations.")

    if front_end_token_service is None:
        raise RuntimeError("Invitation service not initialized")

    logging.info(f"Getting invitations for email: {user_email}")

    request = auth_pb2.GetUserInvitationsRequest(email=user_email)
    try:
        response = front_end_token_service.GetUserInvitations(request)
    except grpc.RpcError as e:
        logging.error(f"gRPC error while fetching invitations for {user_email}: {e}")
        raise
    except Exception as e:
        logging.error(
            f"Unexpected error while fetching invitations for {user_email}: {e}"
        )
        raise

    invitations = [
        {
            "id": invitation.id,
            "tenant_id": invitation.tenant_id,
            "inviter_email": invitation.inviter_email,
            "created_at": invitation.created_at.ToDatetime().strftime("%Y-%m-%d")
            if invitation.created_at
            else "",
        }
        for invitation in response.invitations
    ]

    logging.info(f"Found {len(invitations)} invitations for {user_email}")
    return invitations


def has_invitations(user_email: str) -> bool:
    """Check if a user has any pending invitations.

    Args:
        user_email: The email address of the user to check for invitations

    Returns:
        True if the user has any pending invitations, False otherwise
    """
    invitations = get_user_invitations(user_email)
    has_invites = len(invitations) > 0
    logging.info(
        f"Checking invitations for {user_email}: found {len(invitations)} invitations"
    )
    return has_invites


def is_team_admin(idp_user_id: str, user_email: str) -> bool:
    """Check if a user is an admin of their own team.

    Args:
        idp_user_id: The IDP user ID of the user to check
        user_email: The email address of the user to check

    Returns:
        True if the user is an admin of their own team, False otherwise
    """
    if not idp_user_id:
        return False

    if front_end_token_service is None:
        logging.error("Invitation service not initialized")
        return False

    try:
        # Get the user by email
        user_request = front_end_token_service_pb2.GetUserRequest(
            idp_user_id=idp_user_id,
            email_address=user_email,
        )
        user_response = front_end_token_service.GetUser(user_request)
        user = user_response.user

        if not user:
            logging.info(f"User not found for email: {user_email}")
            return False

        is_admin_request = front_end_token_service_pb2.IsUserAdminRequest(
            user_id=user.id
        )
        is_admin_response = front_end_token_service.IsUserAdmin(is_admin_request)
        return is_admin_response.is_admin
    except Exception as e:
        logging.error(f"Error checking if user is team admin: {e}")
        return False


def get_invitation_resolution_status(resolution_id: str) -> Dict[str, Any]:
    """Get the status of an invitation resolution.

    Args:
        resolution_id: The ID of the invitation resolution to check

    Returns:
        A dictionary with status information including:
        - status: The status of the resolution (PENDING, SUCCESS, ERROR)
        - created_at: When the resolution was requested
    """
    if not resolution_id:
        raise ValueError("Resolution ID is required to check status.")

    if front_end_token_service is None:
        raise RuntimeError("Invitation service not initialized")

    logging.info(f"Checking invitation resolution status for ID: {resolution_id}")

    request = auth_pb2.GetResolveInvitationsStatusRequest(
        invitation_resolution_id=resolution_id
    )
    try:
        response = front_end_token_service.GetResolveInvitationsStatus(request)
    except grpc.RpcError as e:
        logging.error(
            f"gRPC error while checking resolution status for {resolution_id}: {e}"
        )
        raise
    except Exception as e:
        logging.error(
            f"Unexpected error while checking resolution status for {resolution_id}: {e}"
        )
        raise

    resolution = response.invitation_resolution
    status_map = {0: "UNKNOWN", 1: "PENDING", 2: "SUCCESS", 3: "ERROR"}

    created_at = ""
    if resolution.created_at:
        created_at = resolution.created_at.ToDatetime().strftime("%Y-%m-%d %H:%M:%S")

    result = {
        "id": resolution.id,
        "status": status_map.get(resolution.status, "UNKNOWN"),
        "created_at": created_at,
    }

    logging.info(f"Resolution status for {resolution_id}: {result['status']}")
    return result
