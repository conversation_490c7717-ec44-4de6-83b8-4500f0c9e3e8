package main

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"io"
	"iter"
	"net"
	"net/http"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/augmentcode/augment/base/go/secretstring"
	front_end_token_service "github.com/augmentcode/augment/services/auth/central/server/front_end_token_service"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	"github.com/prometheus/client_golang/prometheus/testutil"
	"github.com/rs/zerolog"

	"github.com/stretchr/testify/assert"
)

// This file is about testing runUserSecurityChecks in isolation.
//
// There are separate tests for the indiivdual pieces that make up runUserSecurityChecks, including:
// - runUserSecurityChecksHandler
// - verosintReportFetcher
// - doHTTPSPost
//
// The tests do not cover the code that parses configuration, instantitates the runUserSecurityChecksHandler,
// and wires it into front-end token service server.
//
// The advantages of this testing style are tests with less setup and easier error injection. The
// disadvantage of this testing style is that the tests are coupled to internal interfaces like
// FetchReport.

// Sample request object to make tests shorter
func getRunUserSecurityChecksRequest() *front_end_token_service.RunUserSecurityChecksRequest {
	return &front_end_token_service.RunUserSecurityChecksRequest{
		AugmentUserId:     "test-augment-user-id",
		IdpUserId:         "test-idp-user-id",
		EmailAddress:      "test-email-address",
		UserAgent:         "test-user-agent",
		IpAddress:         "test-ip-address",
		VerosintDeviceId:  "test-verosint-device-id",
		VerisoulSessionId: "test-verisoul-session-id",
		RecaptchaToken:    "test-recaptcha-token",
	}
}

// Sample report parameters objet to make tests shorter
func getVerosintFetchReportParameters() verosintReportFetcherParameters {
	return verosintReportFetcherParameters{
		augmentUserId:    "test-augment-user-id",
		idpUserId:        "test-idp-user-id",
		emailAddress:     "test-email-address",
		ipAddress:        "test-ip-address",
		userAgent:        "test-user-agent",
		verosintDeviceId: "test-verosint-device-id",
	}
}

// Run a local web server, invoking the handler passed in for the "/" path
//
// Returns the URL of the server and a function to clean up the server.
//
// Example:
//
//	url, closer := runWebServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
//		w.WriteHeader(http.StatusOK)
//	}))
//	defer closer()
func runWebServer(t *testing.T, handler http.Handler) (string, func()) {
	server := &http.Server{
		Addr: "127.0.0.1:0",
	}
	socket, err := net.Listen("tcp", server.Addr)
	assert.NoError(t, err)

	socketAddress := socket.Addr().String()

	// Create a Mux
	mux := http.NewServeMux()
	mux.Handle("/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		handler.ServeHTTP(w, r)
	}))
	server.Handler = mux

	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		server.Serve(socket)
	}()

	return "http://" + socketAddress + "/", func() {
		server.Close()
		wg.Wait()
	}
}

// Parse an input in JSON lines format and return an iterator over the parsed lines.
//
// Sample usage:
//
//	for line, err := range parseJSONLines(&logBuffer) {
//	    assert.NoError(t, err, "Failed to parse log output as JSON")
//	    // Process logEntry
//	}
func parseJSONLines(buffer io.Reader) iter.Seq2[map[string]interface{}, error] {
	return func(yield func(map[string]interface{}, error) bool) {
		scanner := bufio.NewScanner(buffer)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if line == "" {
				continue // Skip empty lines
			}

			var logEntry map[string]interface{}
			err := json.Unmarshal([]byte(line), &logEntry)
			if !yield(logEntry, err) {
				return
			}
		}

		// Check for scanner errors
		if err := scanner.Err(); err != nil {
			yield(nil, err)
		}
	}
}

func TestNewRunUserSecurityChecksHandler(t *testing.T) {
	t.Run("Default path with fakes passes", func(t *testing.T) {
		handler := newRunUserSecurityChecksHandler(runUserSecurityChecksDependencies{})
		response, err := handler.runUserSecurityChecks(context.Background(), getRunUserSecurityChecksRequest())
		assert.NoError(t, err)
		assert.NotNil(t, response)
		assert.True(t, response.Passed)
	})

	t.Run("Fetches report from verosint and sends it to request insight", func(t *testing.T) {
		mockPublisher := ripublisher.NewRequestInsightPublisherMock()
		verosintReport := "{ \"no_soup\": \"for_you\" }"
		mockVerosintReportFetcher := &mockVerosintReportFetcherImpl{
			report: verosintReport,
		}

		var logBuffer bytes.Buffer
		logger := zerolog.New(&logBuffer)

		h := newRunUserSecurityChecksHandler(runUserSecurityChecksDependencies{
			requestInsightPublisher: mockPublisher,
			verosintReportFetcher:   mockVerosintReportFetcher,
			logger:                  &logger,
		})

		initialSuccessCount := testutil.ToFloat64(securityCheckResults.WithLabelValues("verosint", "success"))

		h.runUserSecurityChecks(context.Background(), getRunUserSecurityChecksRequest())

		found := false
		for _, call := range mockPublisher.GenericEventCalls {
			verosint := call.Event.GetVerosint()
			if verosint != nil {
				assert.Equal(t, "test-augment-user-id", verosint.OpaqueUserId.UserId)
				if verosint.Report != verosintReport {
					t.Errorf("Verosint report doesn't match")
				}
				found = true
				break
			}
		}
		assert.True(t, found, "Verosint event not found")

		// Check that the log line with the report is printed using the iterator
		foundLogEntry := false
	logParsingLoop:
		for logEntry, err := range parseJSONLines(&logBuffer) {
			assert.NoError(t, err, "Failed to parse log output as JSON")

			// Check if this log entry contains the expected message
			if message, ok := logEntry["message"].(string); ok {
				expectedLogMessage := "Verosint report: " + verosintReport
				if strings.Contains(message, expectedLogMessage) {
					foundLogEntry = true

					// Also check that the log contains the expected user information
					assert.Equal(t, "test-augment-user-id", logEntry["augment_user_id"], "Log should contain augment_user_id")
					assert.Equal(t, "test-idp-user-id", logEntry["idp_user_id"], "Log should contain idp_user_id")
					assert.Equal(t, "test-email-address", logEntry["email_address"], "Log should contain email_address")
					assert.Equal(t, "info", logEntry["level"], "Log level should be info")
					break logParsingLoop
				}
			}
		}
		assert.True(t, foundLogEntry, "Expected log entry with Verosint report not found")

		// Assert that the success counter was incremented by 1
		finalSuccessCount := testutil.ToFloat64(securityCheckResults.WithLabelValues("verosint", "success"))
		assert.Equal(t, initialSuccessCount+1.0, finalSuccessCount, "Success counter should be incremented by 1")
	})

	t.Run("Empty user_id doesn't set opaque_user_id", func(t *testing.T) {
		mockPublisher := ripublisher.NewRequestInsightPublisherMock()
		verosintReport := "{ \"no_soup\": \"for_you\" }"
		mockVerosintReportFetcher := &mockVerosintReportFetcherImpl{
			report: verosintReport,
		}

		h := newRunUserSecurityChecksHandler(runUserSecurityChecksDependencies{
			requestInsightPublisher: mockPublisher,
			verosintReportFetcher:   mockVerosintReportFetcher,
		})

		req := getRunUserSecurityChecksRequest()
		req.AugmentUserId = ""

		h.runUserSecurityChecks(context.Background(), req)

		found := false
		for _, call := range mockPublisher.GenericEventCalls {
			verosint := call.Event.GetVerosint()
			if verosint != nil {
				assert.Nil(t, verosint.OpaqueUserId, "Opaque user ID should not be set")
				found = true
				break
			}
		}
		assert.True(t, found, "Verosint event not found")
	})

	t.Run("doHTTPSPost works on happy path", func(t *testing.T) {
		requestsSeen := make([]*http.Request, 0)
		bodySeen := make([]string, 0)

		url, closer := runWebServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			requestsSeen = append(requestsSeen, r)
			body, err := io.ReadAll(r.Body)
			assert.NoError(t, err)
			bodySeen = append(bodySeen, string(body))
			w.WriteHeader(http.StatusOK)
		}))
		defer closer()

		ctx := context.Background()
		bearerToken := secretstring.New("test-token")

		resp, err := doHTTPSPost(ctx, url, bearerToken, map[string]interface{}{
			"test": "data",
		}, 2*time.Second)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, 200, resp.statusCode)
		assert.Equal(t, 1, len(requestsSeen))
		assert.Equal(t, "Bearer test-token", requestsSeen[0].Header.Get("Authorization"))
		assert.Equal(t, "application/json", requestsSeen[0].Header.Get("Content-Type"))
		assert.Equal(t, 1, len(bodySeen))

		var jsonData map[string]interface{}
		err = json.Unmarshal([]byte(bodySeen[0]), &jsonData)
		assert.NoError(t, err)
		assert.Equal(t, "data", jsonData["test"])
	})

	t.Run("doHTTPSPost function doesn't error on non-200", func(t *testing.T) {
		url, closer := runWebServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusInternalServerError)
		}))
		defer closer()

		ctx := context.Background()
		bearerToken := secretstring.New("test-token")

		resp, err := doHTTPSPost(ctx, url, bearerToken, map[string]interface{}{
			"test": "data",
		}, 2*time.Second)
		assert.Nil(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, 500, resp.statusCode)
	})

	t.Run("doHTTPSPost function errors on time out", func(t *testing.T) {
		barrier := make(chan struct{}, 1)

		url, closer := runWebServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			<-barrier
			w.WriteHeader(http.StatusOK)
		}))
		defer closer()

		ctx := context.Background()
		bearerToken := secretstring.New("test-token")
		resp, err := doHTTPSPost(ctx, url, bearerToken, map[string]interface{}{
			"test": "data",
		}, 10*time.Millisecond)
		assert.Nil(t, resp)
		assert.Error(t, err)
		barrier <- struct{}{}
	})

	t.Run("Verosint fetcher posts user information and retrieves report", func(t *testing.T) {
		requests := make([]*http.Request, 0)
		bodies := make([]string, 0)

		// Instead of faking doHTTPSPost, we spin up a web server for this happy path test.

		url, closer := runWebServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			bodyBytes, err := io.ReadAll(r.Body)
			assert.NoError(t, err)
			bodies = append(bodies, string(bodyBytes))

			requests = append(requests, r)

			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)

			response := make(map[string]interface{})
			response["report"] = "test-report"
			err = json.NewEncoder(w).Encode(response)
			assert.NoError(t, err)
		}))
		defer closer()

		fetcher := &verosintReportFetcherImpl{
			apiKey:        secretstring.New("test-api-key"),
			endpoint:      url,
			httpsPostFunc: doHTTPSPost,
			timeout:       2 * time.Second,
		}
		report, err := fetcher.fetchReport(context.Background(), getVerosintFetchReportParameters())

		assert.NoError(t, err)
		assert.Equal(t, 1, len(requests))
		assert.Equal(t, 1, len(bodies))
		assert.Equal(t, "Bearer test-api-key", requests[0].Header.Get("Authorization"))
		assert.Equal(t, "application/json", requests[0].Header.Get("Content-Type"))
		var data map[string]interface{}
		err = json.Unmarshal([]byte(bodies[0]), &data)
		assert.NoError(t, err)

		assert.Equal(t, true, data["verbose"])
		assert.Equal(t, "test-email-address", data["identifiers"].(map[string]interface{})["email"])
		assert.Equal(t, "test-ip-address", data["identifiers"].(map[string]interface{})["ip"])
		assert.Equal(t, "test-idp-user-id", data["identifiers"].(map[string]interface{})["accountId"])
		assert.Equal(t, "test-verosint-device-id", data["identifiers"].(map[string]interface{})["deviceId"])
		assert.Equal(t, "test-user-agent", data["identifiers"].(map[string]interface{})["userAgent"])

		var reportData map[string]interface{}
		err = json.Unmarshal([]byte(report), &reportData)
		assert.NoError(t, err)
		assert.Equal(t, "test-report", reportData["report"])
	})

	t.Run("Verosint fetcher errors on non-200", func(t *testing.T) {
		fetcher := &verosintReportFetcherImpl{
			apiKey:   secretstring.New("test-api-key"),
			endpoint: "http://test-endpoint",
			httpsPostFunc: func(ctx context.Context, url string, bearerToken secretstring.SecretString, jsonData interface{}, timeout time.Duration) (*httpPostResponse, error) {
				return &httpPostResponse{
					statusCode: http.StatusInternalServerError,
				}, nil
			},
			timeout: 2 * time.Second,
		}
		_, err := fetcher.fetchReport(context.Background(), getVerosintFetchReportParameters())
		assert.Error(t, err)
	})

	t.Run("Verosint fetcher errors on HTTP fetch error", func(t *testing.T) {
		fetcher := &verosintReportFetcherImpl{
			apiKey:   secretstring.New("test-api-key"),
			endpoint: "http://test-endpoint",
			httpsPostFunc: func(ctx context.Context, url string, bearerToken secretstring.SecretString, jsonData interface{}, timeout time.Duration) (*httpPostResponse, error) {
				return nil, context.DeadlineExceeded
			},
			timeout: 2 * time.Second,
		}
		_, err := fetcher.fetchReport(context.Background(), getVerosintFetchReportParameters())
		assert.Error(t, err)
	})
}
