package main

import (
	"context"
	"fmt"
	"testing"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/augmentcode/augment/services/auth/central/server/test_utils"
	orbclient "github.com/augmentcode/augment/services/integrations/orb"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_proto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/orbcorp/orb-go"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	selfServeTeamTenantID = "self-serve"
	vanguardTenantID      = "vanguard0"
	enterpriseTenantID    = "enterprise-tenant"
	discoveryTenantID     = "discovery0"
)

// Optionally override orbClient if you want to do something outside the default behavior.
func newTestSubscriptionCreationProcessor(t *testing.T, orbClient *orbclient.MockOrbClient) (
	processor *SubscriptionCreationProcessor, cleanup func(),
) {
	// Create a mock Stripe client
	stripeClient := NewMockStripeClient()

	// Create a feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	bigtableFixture := NewBigtableFixture(t)
	daoFactoryFixture := NewDAOFactoryFixture(bigtableFixture)
	selfServeTeamTenant := &tw_proto.Tenant{
		Id:             selfServeTeamTenantID,
		Name:           selfServeTeamTenantID,
		Tier:           tw_proto.TenantTier_PROFESSIONAL,
		ShardNamespace: "d0",
		Cloud:          "CLOUD_PROD",
		Config: &tw_proto.Config{
			Configs: map[string]string{
				"is_self_serve_team": "true",
			},
		},
	}
	vanguardTenant := &tw_proto.Tenant{
		Id:             vanguardTenantID,
		Name:           vanguardTenantID,
		Tier:           tw_proto.TenantTier_COMMUNITY,
		ShardNamespace: "i0",
		Cloud:          "CLOUD_PROD",
	}
	discoveryTenant := &tw_proto.Tenant{
		Id:             discoveryTenantID,
		Name:           discoveryTenantID,
		Tier:           tw_proto.TenantTier_PROFESSIONAL,
		ShardNamespace: "d0",
		Cloud:          "CLOUD_PROD",
	}
	enterpriseTenant := &tw_proto.Tenant{
		Id:             enterpriseTenantID,
		Name:           enterpriseTenantID,
		Tier:           tw_proto.TenantTier_ENTERPRISE,
		ShardNamespace: "e0",
		Cloud:          "CLOUD_PROD",
		AuthConfiguration: &tw_proto.AuthConfiguration{
			Domain: "enterprise.com",
		},
	}
	tenantChangeChannel := make(chan tw_client.TenantChange, 1)
	tenantChangeChannel <- tw_client.TenantChange{
		Response: &tw_proto.WatchTenantsResponse{
			Tenants: []*tw_proto.TenantChange{
				{
					Type: &tw_proto.TenantChange_Updated{
						Updated: &tw_proto.TenantUpdate{
							Tenant: selfServeTeamTenant,
						},
					},
				},
				{
					Type: &tw_proto.TenantChange_Updated{
						Updated: &tw_proto.TenantUpdate{
							Tenant: vanguardTenant,
						},
					},
				},
				{
					Type: &tw_proto.TenantChange_Updated{
						Updated: &tw_proto.TenantUpdate{
							Tenant: enterpriseTenant,
						},
					},
				},
				{
					Type: &tw_proto.TenantChange_Updated{
						Updated: &tw_proto.TenantUpdate{
							Tenant: discoveryTenant,
						},
					},
				},
			},
			IsInitial: true,
		},
	}
	mockTenantWatcherClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_proto.Tenant{
			selfServeTeamTenant,
			vanguardTenant,
			enterpriseTenant,
			discoveryTenant,
		},
		Changes: []chan tw_client.TenantChange{
			tenantChangeChannel,
		},
	}
	tenantMap := NewTenantMap(
		daoFactoryFixture.DAOFactory,
		mockTenantWatcherClient,
		"us-central.api.augmentcode.com",
		featureFlagHandle,
		NewMockAsyncOpsPublisher(),
		audit.NewDefaultAuditLogger(),
	)

	orbConfig := &OrbConfig{
		Enabled:     true,
		PricingUnit: "usermessages",
		Plans: []PlanConfig{
			{
				ID: "orb-trial-plan",
				Features: PlanFeatures{
					PlanType: PlanTypePaidTrial,
				},
			},
			{
				ID: "orb-community-plan",
				Features: PlanFeatures{
					PlanType: PlanTypeCommunity,
				},
			},
			{
				ID: "orb-developer-plan",
				Features: PlanFeatures{
					PlanType: PlanTypePaid,
				},
			},
		},
	}

	if orbClient == nil {
		orbClient = orbclient.NewMockOrbClient()
		orbClient.
			On("CreateCustomer", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return("orb-customer-id", nil)
		orbClient.
			On("CreateSubscription", mock.Anything, mock.Anything, mock.Anything).
			Return("orb-subscription-id", nil)
		orbClient.
			On("AddCreditBalanceDepletedAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return(nil)
		orbClient.
			On("AddCreditBalanceRecoveredAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return(nil)
	}

	processor, err := NewSubscriptionCreationProcessor(
		daoFactoryFixture.DAOFactory,
		tenantMap,
		stripeClient,
		orbConfig,
		orbClient,
		ripublisher.NewRequestInsightPublisherMock(),
		audit.NewDefaultAuditLogger(),
	)
	require.NoError(t, err)
	cleanup = func() {
		bigtableFixture.Cleanup()
		daoFactoryFixture.Cleanup()
		tenantMap.Close()
	}
	return processor, cleanup
}

// Test handling of cases that shouldn't be processed.
func TestProcessorValidation(t *testing.T) {
	logging.SetupServerLogging()

	t.Run("no subscriptionCreationId", func(t *testing.T) {
		processor, cleanup := newTestSubscriptionCreationProcessor(t, nil)
		defer cleanup()

		userDAO := processor.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:                     "test-user",
			Email:                  "<EMAIL>",
			Tenants:                []string{vanguardTenantID},
			SubscriptionCreationId: "",
			SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
				Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
				Id:        "",
				CreatedAt: timestamppb.Now(),
				UpdatedAt: timestamppb.Now(),
			},
		}
		_, err := userDAO.Create(context.Background(), user)
		require.NoError(t, err)

		msg := &auth_internal.CreateSubscriptionMessage{
			Id:          "test-subscription-creation",
			UserId:      "test-user",
			TenantId:    vanguardTenantID,
			PublishTime: timestamppb.Now(),
		}
		err = processor.Process(context.Background(), msg)
		require.Error(t, err)
	})

	t.Run("status is success", func(t *testing.T) {
		processor, cleanup := newTestSubscriptionCreationProcessor(t, nil)
		defer cleanup()

		userDAO := processor.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:                     "test-user",
			Email:                  "<EMAIL>",
			Tenants:                []string{vanguardTenantID},
			SubscriptionCreationId: "test-subscription-creation",
			SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
				Status:    auth_entities.User_SubscriptionCreationInfo_SUCCESS,
				Id:        "test-subscription-creation",
				CreatedAt: timestamppb.Now(),
				UpdatedAt: timestamppb.Now(),
			},
		}
		_, err := userDAO.Create(context.Background(), user)
		require.NoError(t, err)

		msg := &auth_internal.CreateSubscriptionMessage{
			Id:          "test-subscription-creation",
			UserId:      "test-user",
			TenantId:    vanguardTenantID,
			PublishTime: timestamppb.Now(),
		}
		err = processor.Process(context.Background(), msg)
		require.NoError(t, err)
	})

	t.Run("another pending subscription creation", func(t *testing.T) {
		processor, cleanup := newTestSubscriptionCreationProcessor(t, nil)
		defer cleanup()

		userDAO := processor.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:                     "test-user",
			Email:                  "<EMAIL>",
			Tenants:                []string{vanguardTenantID},
			SubscriptionCreationId: "another-subscription-creation",
			SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
				Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
				Id:        "another-subscription-creation",
				CreatedAt: timestamppb.Now(),
				UpdatedAt: timestamppb.Now(),
			},
		}
		_, err := userDAO.Create(context.Background(), user)
		require.NoError(t, err)

		msg := &auth_internal.CreateSubscriptionMessage{
			Id:          "test-subscription-creation",
			UserId:      "test-user",
			TenantId:    vanguardTenantID,
			PublishTime: timestamppb.Now(),
		}
		err = processor.Process(context.Background(), msg)
		require.NoError(t, err)
	})

	t.Run("self serve tenant", func(t *testing.T) {
		processor, cleanup := newTestSubscriptionCreationProcessor(t, nil)
		defer cleanup()

		userDAO := processor.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:                     "test-user",
			Email:                  "<EMAIL>",
			Tenants:                []string{selfServeTeamTenantID},
			SubscriptionCreationId: "test-subscription-creation",
			SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
				Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
				Id:        "test-subscription-creation",
				CreatedAt: timestamppb.Now(),
				UpdatedAt: timestamppb.Now(),
			},
		}
		_, err := userDAO.Create(context.Background(), user)
		require.NoError(t, err)

		msg := &auth_internal.CreateSubscriptionMessage{
			Id:          "test-subscription-creation",
			UserId:      "test-user",
			TenantId:    selfServeTeamTenantID,
			PublishTime: timestamppb.Now(),
		}
		err = processor.Process(context.Background(), msg)
		require.NoError(t, err)
	})

	t.Run("orb billing method", func(t *testing.T) {
		processor, cleanup := newTestSubscriptionCreationProcessor(t, nil)
		defer cleanup()

		userDAO := processor.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:                     "test-user",
			Email:                  "<EMAIL>",
			Tenants:                []string{vanguardTenantID},
			SubscriptionCreationId: "test-subscription-creation",
			SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
				Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
				Id:        "test-subscription-creation",
				CreatedAt: timestamppb.Now(),
				UpdatedAt: timestamppb.Now(),
			},
			BillingMethod: auth_entities.BillingMethod_BILLING_METHOD_ORB,
		}
		_, err := userDAO.Create(context.Background(), user)
		require.NoError(t, err)

		msg := &auth_internal.CreateSubscriptionMessage{
			Id:          "test-subscription-creation",
			UserId:      "test-user",
			TenantId:    vanguardTenantID,
			PublishTime: timestamppb.Now(),
		}
		err = processor.Process(context.Background(), msg)
		require.NoError(t, err)
	})

	t.Run("invalid user email", func(t *testing.T) {
		processor, cleanup := newTestSubscriptionCreationProcessor(t, nil)
		defer cleanup()

		userDAO := processor.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:                     "test-user",
			Email:                  "1234567890",
			Tenants:                []string{vanguardTenantID},
			SubscriptionCreationId: "test-subscription-creation",
		}
		_, err := userDAO.Create(context.Background(), user)
		require.NoError(t, err)

		msg := &auth_internal.CreateSubscriptionMessage{
			Id:          "test-subscription-creation",
			UserId:      "test-user",
			TenantId:    vanguardTenantID,
			PublishTime: timestamppb.Now(),
		}
		err = processor.Process(context.Background(), msg)
		require.NoError(t, err)
	})
}

// Test the happy path for orb subscription creation.
func TestOrbSubscriptionCreation(t *testing.T) {
	t.Run("community", func(t *testing.T) {
		orbClient := orbclient.NewMockOrbClient()
		orbClient.
			On("CreateCustomer", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return("orb-customer-id", nil)
		orbClient.
			On("CreateSubscription", mock.Anything, mock.Anything, mock.Anything).
			Return("orb-subscription-id", nil)
		orbClient.
			On("AddCreditBalanceDepletedAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return(nil)
		orbClient.
			On("AddCreditBalanceRecoveredAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return(nil)

		processor, cleanup := newTestSubscriptionCreationProcessor(t, orbClient)
		defer cleanup()

		// Create a user
		userDAO := processor.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:                     "test-user",
			Email:                  "<EMAIL>",
			Tenants:                []string{vanguardTenantID},
			SubscriptionCreationId: "test-subscription-creation",
			SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
				Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
				Id:        "test-subscription-creation",
				CreatedAt: timestamppb.Now(),
				UpdatedAt: timestamppb.Now(),
			},
		}
		_, err := userDAO.Create(context.Background(), user)
		require.NoError(t, err)

		// Publish subscription creation message.
		msg := &auth_internal.CreateSubscriptionMessage{
			Id:          "test-subscription-creation",
			UserId:      user.Id,
			TenantId:    vanguardTenantID,
			PublishTime: timestamppb.Now(),
		}
		err = processor.Process(context.Background(), msg)
		require.NoError(t, err)

		// Fetch the updated user from the database to check the status
		updatedUser, err := userDAO.Get(context.Background(), user.Id)
		require.NoError(t, err)
		require.Equal(t, auth_entities.User_SubscriptionCreationInfo_SUCCESS, updatedUser.SubscriptionCreationInfo.Status)

		// Make sure we have the right billing method and Orb customer ID
		user, err = userDAO.Get(context.Background(), user.Id)
		require.NoError(t, err)
		require.NotEmpty(t, user.StripeCustomerId)

		// Make sure we have an orb customer with the right plan information.
		require.NotEmpty(t, user.OrbCustomerId)
		mockOrbClient := processor.orbClient.(*orbclient.MockOrbClient)
		mockOrbClient.AssertNumberOfCalls(t, "CreateSubscription", 1)
		mockOrbClient.AssertNumberOfCalls(t, "AddCreditBalanceDepletedAlert", 1)
		mockOrbClient.AssertNumberOfCalls(t, "AddCreditBalanceRecoveredAlert", 1)
		mockOrbClient.AssertCalled(
			t, "CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orbclient.OrbSubscription) bool {
				return subscription.ExternalPlanID == "orb-community-plan"
			}), mock.Anything,
		)
	})

	t.Run("developer", func(t *testing.T) {
		orbClient := orbclient.NewMockOrbClient()
		orbClient.
			On("CreateCustomer", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return("orb-customer-id", nil)
		orbClient.
			On("CreateSubscription", mock.Anything, mock.Anything, mock.Anything).
			Return("orb-subscription-id", nil)
		orbClient.
			On("AddCreditBalanceDepletedAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return(nil)
		orbClient.
			On("AddCreditBalanceRecoveredAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return(nil)

		processor, cleanup := newTestSubscriptionCreationProcessor(t, orbClient)
		defer cleanup()

		// Create a user
		userDAO := processor.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:                     "test-user",
			Email:                  "<EMAIL>",
			Tenants:                []string{discoveryTenantID},
			SubscriptionCreationId: "test-subscription-creation",
			SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
				Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
				Id:        "test-subscription-creation",
				CreatedAt: timestamppb.Now(),
				UpdatedAt: timestamppb.Now(),
			},
		}
		_, err := userDAO.Create(context.Background(), user)
		require.NoError(t, err)

		// Publish subscription creation message.
		msg := &auth_internal.CreateSubscriptionMessage{
			Id:          "test-subscription-creation",
			UserId:      user.Id,
			TenantId:    discoveryTenantID,
			PublishTime: timestamppb.Now(),
		}
		err = processor.Process(context.Background(), msg)
		require.NoError(t, err)

		// Fetch the updated user from the database to check the status
		updatedUser, err := userDAO.Get(context.Background(), user.Id)
		require.NoError(t, err)
		require.Equal(t, auth_entities.User_SubscriptionCreationInfo_SUCCESS, updatedUser.SubscriptionCreationInfo.Status)

		// Make sure we have the right billing method and Orb customer ID
		user, err = userDAO.Get(context.Background(), user.Id)
		require.NoError(t, err)
		require.NotEmpty(t, user.StripeCustomerId)

		// Make sure we have an orb customer with the right plan information.
		require.NotEmpty(t, user.OrbCustomerId)
		mockOrbClient := processor.orbClient.(*orbclient.MockOrbClient)
		mockOrbClient.AssertNumberOfCalls(t, "CreateSubscription", 1)
		mockOrbClient.AssertNumberOfCalls(t, "AddCreditBalanceDepletedAlert", 1)
		mockOrbClient.AssertNumberOfCalls(t, "AddCreditBalanceRecoveredAlert", 1)
		mockOrbClient.AssertCalled(
			t, "CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orbclient.OrbSubscription) bool {
				return subscription.ExternalPlanID == "orb-trial-plan" && subscription.EndDate != nil
			}), mock.Anything,
		)
	})
}

// Test that a billing method in a user's DAO is respected.
func TestBillingMethod(t *testing.T) {
	orbClient := orbclient.NewMockOrbClient()
	orbClient.
		On("CreateCustomer", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return("orb-customer-id", nil)
	orbClient.
		On("CreateSubscription", mock.Anything, mock.Anything, mock.Anything).
		Return("orb-subscription-id", nil)
	orbClient.
		On("AddCreditBalanceDepletedAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(nil)
	orbClient.
		On("AddCreditBalanceRecoveredAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(nil)

	processor, cleanup := newTestSubscriptionCreationProcessor(t, orbClient)
	defer cleanup()

	// Create a user with ORB billing method
	userDao := processor.daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:                     "test-user",
		Email:                  "<EMAIL>",
		Tenants:                []string{vanguardTenantID},
		SubscriptionCreationId: "test-subscription-creation",
		SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
			Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
			Id:        "test-subscription-creation",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		},
		BillingMethod: auth_entities.BillingMethod_BILLING_METHOD_ORB,
	}
	_, err := userDao.Create(context.Background(), user)
	require.NoError(t, err)

	msg := &auth_internal.CreateSubscriptionMessage{
		Id:          "test-subscription-creation",
		UserId:      "test-user",
		TenantId:    vanguardTenantID,
		PublishTime: timestamppb.Now(),
	}
	err = processor.Process(context.Background(), msg)
	require.NoError(t, err)

	// Fetch the updated user from the database to check the status
	updatedUser, err := userDao.Get(context.Background(), user.Id)
	require.NoError(t, err)
	require.Equal(t, auth_entities.User_SubscriptionCreationInfo_SUCCESS, updatedUser.SubscriptionCreationInfo.Status)

	// Make sure we set up Orb correctly
	user, err = userDao.Get(context.Background(), user.Id)
	require.NoError(t, err)
	require.NotEmpty(t, user.StripeCustomerId)
	require.NotEmpty(t, user.OrbCustomerId)
	require.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, user.BillingMethod)

	// Verify Orb calls
	mockOrbClient := processor.orbClient.(*orbclient.MockOrbClient)
	mockOrbClient.AssertNumberOfCalls(t, "CreateCustomer", 1)
	mockOrbClient.AssertNumberOfCalls(t, "CreateSubscription", 1)
}

// Test the situation where we've already created Orb alerts for the customer, in which case Orb
// returns a 400.
func TestExistingOrbAlerts(t *testing.T) {
	orbClient := orbclient.NewMockOrbClient()
	orbClient.
		On("CreateCustomer", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return("orb-customer-id", nil)
	orbClient.
		On("CreateSubscription", mock.Anything, mock.Anything, mock.Anything).
		Return("orb-subscription-id", nil)
	orbClient.
		On("AddCreditBalanceDepletedAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(&orb.Error{Type: orb.ErrorTypeRequestValidationError})
	orbClient.
		On("AddCreditBalanceRecoveredAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(&orb.Error{Type: orb.ErrorTypeRequestValidationError})
	processor, cleanup := newTestSubscriptionCreationProcessor(t, orbClient)
	defer cleanup()

	// Create a user
	userDAO := processor.daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:                     "test-user",
		Email:                  "<EMAIL>",
		Tenants:                []string{vanguardTenantID},
		SubscriptionCreationId: "test-subscription-creation",
		SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
			Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
			Id:        "test-subscription-creation",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		},
	}
	_, err := userDAO.Create(context.Background(), user)
	require.NoError(t, err)

	// Publish subscription creation message.
	msg := &auth_internal.CreateSubscriptionMessage{
		Id:          "test-subscription-creation",
		UserId:      user.Id,
		TenantId:    vanguardTenantID,
		PublishTime: timestamppb.Now(),
	}
	err = processor.Process(context.Background(), msg)
	require.NoError(t, err)

	// Validate that we got through subscription creation.
	user, err = userDAO.Get(context.Background(), user.Id)
	require.NoError(t, err)
	require.NotEmpty(t, user.OrbSubscriptionId)
	require.Equal(t, auth_entities.User_SubscriptionCreationInfo_SUCCESS, user.SubscriptionCreationInfo.Status)
}

func TestProcessSubscriptionCreationFailpointsAndRetry_Orb(t *testing.T) {
	panicPoints := []string{
		SubscriptionCreationPanicPoints.SetupStripeCustomerError,
		SubscriptionCreationPanicPoints.SetupOrbCustomerError,
		SubscriptionCreationPanicPoints.SetupOrbSubscriptionError,
	}

	for _, panicPoint := range panicPoints {
		t.Run(fmt.Sprintf("PanicPoint_%s", panicPoint), func(t *testing.T) {
			orbClient := orbclient.NewMockOrbClient()
			orbClient.
				On("CreateCustomer", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
				Return("orb-customer-id", nil)
			orbClient.
				On("CreateSubscription", mock.Anything, mock.Anything, mock.Anything).
				Return("orb-subscription-id", nil)
			orbClient.
				On("AddCreditBalanceDepletedAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
				Return(nil)
			orbClient.
				On("AddCreditBalanceRecoveredAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
				Return(nil)

			processor, cleanup := newTestSubscriptionCreationProcessor(t, orbClient)
			defer cleanup()

			// Create a user
			userDAO := processor.daoFactory.GetUserDAO()
			user := &auth_entities.User{
				Id:                     "test-user",
				Email:                  "<EMAIL>",
				Tenants:                []string{discoveryTenantID},
				SubscriptionCreationId: "test-subscription-creation",
				SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
					Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
					Id:        "test-subscription-creation",
					CreatedAt: timestamppb.Now(),
					UpdatedAt: timestamppb.Now(),
				},
			}
			_, err := userDAO.Create(context.Background(), user)
			require.NoError(t, err)

			// Publish subscription creation message.
			msg := &auth_internal.CreateSubscriptionMessage{
				Id:          "test-subscription-creation",
				UserId:      user.Id,
				TenantId:    discoveryTenantID,
				PublishTime: timestamppb.Now(),
			}
			test_utils.EnablePanicPoint(panicPoint)
			defer test_utils.DisableAllPanicPoints()

			// Process should panic due to the enabled panic point
			require.Panics(t, func() {
				_ = processor.Process(context.Background(), msg)
			})

			// Disable the panic point and retry
			test_utils.DisablePanicPoint(panicPoint)
			err = processor.Process(context.Background(), msg)
			require.NoError(t, err)

			// Check our db state
			updatedUser, err := userDAO.Get(context.Background(), user.Id)
			require.NoError(t, err)
			require.NotEmpty(t, updatedUser.StripeCustomerId)
			require.NotEmpty(t, updatedUser.OrbCustomerId)
			require.Equal(t, auth_entities.User_SubscriptionCreationInfo_SUCCESS, updatedUser.SubscriptionCreationInfo.Status)

			// Verify Orb calls
			mockOrbClient := processor.orbClient.(*orbclient.MockOrbClient)
			mockOrbClient.AssertNumberOfCalls(t, "CreateCustomer", 1)
			mockOrbClient.AssertNumberOfCalls(t, "CreateSubscription", 1)
		})
	}
}

// Make sure we're robust to old users without SubscriptionCreationInfo set.
func TestNilSubscriptionCreationInfo(t *testing.T) {
	orbClient := orbclient.NewMockOrbClient()
	orbClient.
		On("CreateCustomer", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return("orb-customer-id", nil)
	orbClient.
		On("CreateSubscription", mock.Anything, mock.Anything, mock.Anything).
		Return("orb-subscription-id", nil)
	orbClient.
		On("AddCreditBalanceDepletedAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(nil)
	orbClient.
		On("AddCreditBalanceRecoveredAlert", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(nil)

	processor, cleanup := newTestSubscriptionCreationProcessor(t, orbClient)
	defer cleanup()

	// Create a user
	userDAO := processor.daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:                     "test-user",
		Email:                  "<EMAIL>",
		Tenants:                []string{discoveryTenantID},
		SubscriptionCreationId: "test-subscription-creation",
	}
	_, err := userDAO.Create(context.Background(), user)
	require.NoError(t, err)

	// Publish subscription creation message.
	msg := &auth_internal.CreateSubscriptionMessage{
		Id:          "test-subscription-creation",
		UserId:      user.Id,
		TenantId:    discoveryTenantID,
		PublishTime: timestamppb.Now(),
	}
	err = processor.Process(context.Background(), msg)
	require.NoError(t, err)

	// Check our db state
	updatedUser, err := userDAO.Get(context.Background(), user.Id)
	require.NoError(t, err)
	require.NotEmpty(t, updatedUser.StripeCustomerId)
	require.NotEmpty(t, updatedUser.OrbCustomerId)
	require.Equal(t, auth_entities.User_SubscriptionCreationInfo_SUCCESS, updatedUser.SubscriptionCreationInfo.Status)
}
