package main

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/types/known/timestamppb"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	stripe_event "github.com/augmentcode/augment/services/auth/stripe_webhook/stripe_event"
	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/augmentcode/augment/services/lib/pubsub"
	"google.golang.org/protobuf/proto"
)

var processEventMetrics = promauto.NewHistogramVec(
	prometheus.HistogramOpts{
		Name:    "au_auth_central_stripe_processor_latency",
		Help:    "Stripe events processed with duration in seconds",
		Buckets: prometheus.DefBuckets,
	},
	[]string{"event_type", "status"},
)

// StripeEventProcessor processes Stripe events from PubSub
type StripeEventProcessor struct {
	config            *Config
	daoFactory        *DAOFactory
	subscribeClient   pubsub.SubscribeClient
	stripeClient      StripeClient
	featureFlagHandle featureflags.FeatureFlagHandle
	orbClient         orb.OrbClient
}

// NewStripeEventProcessor creates a new StripeEventProcessor
func NewStripeEventProcessor(ctx context.Context, config *Config, daoFactory *DAOFactory, featureFlagHandle featureflags.FeatureFlagHandle) (*StripeEventProcessor, error) {
	if config.StripeEventProcessorConfig.SubscriptionName == "" {
		return nil, fmt.Errorf("subscription name is required")
	}

	// Create a subscribe client using the library
	subscribeConfig := &pubsub.SubscribeClientConfig{
		ProjectId:                        config.GCP.ProjectID,
		SubscriptionId:                   config.StripeEventProcessorConfig.SubscriptionName,
		DeadLetterSubscriptionId:         config.StripeEventProcessorConfig.DeadLetterSubscriptionName,
		MaxConcurrentReceivers:           config.StripeEventProcessorConfig.MaxConcurrentReceivers,
		DeadLetterMaxConcurrentReceivers: config.StripeEventProcessorConfig.DeadLetterMaxConcurrentReceivers,
		ConfigureDeadLetterHandling: func() bool {
			return checkStripeEventProcessorDeadLetterFlag(featureFlagHandle)
		},
	}

	subscribeClient, err := pubsub.NewSubscribeClient(ctx, subscribeConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create subscribe client: %v", err)
	}

	// Create a Stripe client
	stripeClient := &DefaultStripeClient{}

	// Initialize Orb client if enabled
	var orbClient orb.OrbClient
	if config.Orb.Enabled {
		// Read the Orb API key from the specified file path
		key, err := os.ReadFile(config.Orb.ApiKeyPath)
		if err != nil {
			log.Error().Err(err).Msg("Failed to read Orb secret key")
		} else {
			cleanKey := strings.TrimSpace(string(key))
			orbClient = orb.NewOrbClient(cleanKey, featureFlagHandle)
			log.Info().Msg("Orb client initialized successfully")
		}
	}

	return &StripeEventProcessor{
		config:            config,
		daoFactory:        daoFactory,
		subscribeClient:   subscribeClient,
		stripeClient:      stripeClient,
		featureFlagHandle: featureFlagHandle,
		orbClient:         orbClient,
	}, nil
}

// Run starts the processor and blocks until the context is canceled
func (p *StripeEventProcessor) Run(ctx context.Context) error {
	// Our pubsub library already handles:
	// 1. Panic recovery in message handlers
	// 2. Automatic retries for failed messages (via Nack)
	// 3. Reconnection for transient service issues
	log.Info().Msg("Starting Stripe event processor")
	err := p.receiveAndProcess(ctx)

	// If we get here, either the context was canceled or there was a fatal error
	if ctx.Err() != nil {
		log.Info().Err(ctx.Err()).Msg("Context cancelled, stopping StripeEventProcessor")
		return ctx.Err()
	}

	// If we get here with a non-nil error, it's a fatal error from the PubSub client
	if err != nil {
		log.Error().Err(err).Msg("Fatal error in Stripe event processor")
	}
	return err
}

// Stop stops the processor and cleans up resources
func (p *StripeEventProcessor) Stop() {
	if p.subscribeClient != nil {
		p.subscribeClient.Close()
	}
}

// receiveAndProcess receives and processes messages from the PubSub subscription
func (p *StripeEventProcessor) receiveAndProcess(ctx context.Context) error {
	return p.subscribeClient.Receive(ctx, func(ctx context.Context, msgData []byte) error {
		// Start timing the message processing
		start := time.Now()
		var eventType string = "unknown" // Default event type
		var status string = "success"    // Default status

		// Defer the metric recording to ensure it happens regardless of how we exit the function
		defer func() {
			// This is the only place we record metrics
			processEventMetrics.WithLabelValues(eventType, status).Observe(time.Since(start).Seconds())
		}()

		var event stripe_event.StripeEvent
		// Try to unmarshal as Protocol Buffers first
		err := proto.Unmarshal(msgData, &event)
		if err != nil {
			log.Info().Err(err).Str("raw_message", string(msgData)).Str("message_length", fmt.Sprintf("%d", len(msgData))).Msg("Received Stripe event message with unmarshal error")
			status = "error"
			return err // Return error to trigger Nack
		}

		// Update the event type once we know it
		eventType = event.EventType

		if err := p.processStripeEvent(ctx, &event); err != nil {
			log.Error().Err(err).Msg("Failed to process Stripe event")
			status = "error"
			return err // Return error to trigger Nack
		}

		return nil // Return nil to trigger Ack
	})
}

// processStripeEvent processes a Stripe event
func (p *StripeEventProcessor) processStripeEvent(ctx context.Context, event *stripe_event.StripeEvent) error {
	log.Info().
		Str("event_type", event.EventType).
		Str("event_id", event.EventId).
		Msg("Processing Stripe event")

	// Handle different event types
	switch {
	case strings.HasPrefix(event.EventType, "setup_intent."):
		return p.handleSetupIntentEvents(ctx, event)
	case strings.HasPrefix(event.EventType, "customer."):
		return p.handleCustomerEvents(ctx, event)
	case strings.HasPrefix(event.EventType, "payment_method."):
		return p.handlePaymentMethodEvents(ctx, event)
	default:
		log.Info().Str("event_type", event.EventType).Msg("Ignoring unhandled event type")
		return nil
	}
}

// handleSetupIntentEvents processes payment method setup events from the Orb integration.
// Unlike subscriptions, SetupIntents establish payment methods without creating a subscription in our system.
func (p *StripeEventProcessor) handleSetupIntentEvents(ctx context.Context, event *stripe_event.StripeEvent) error {
	if p.orbClient == nil {
		// Bypass SetupIntent events if orb is not enabled.
		log.Info().Msg("Skipping setup intent event as orb is not enabled")
		return nil
	}

	setupIntentEvent := event.GetSetupIntentEvent()
	if setupIntentEvent == nil {
		return fmt.Errorf("setup intent event is nil")
	}

	if event.StripeCustomerId == nil {
		return fmt.Errorf("customer ID is nil")
	}

	log.Info().
		Str("event_type", event.EventType).
		Str("setup_intent_id", setupIntentEvent.SetupIntentId).
		Str("customer_id", *event.StripeCustomerId).
		Str("status", setupIntentEvent.Status).
		Msg("Processing setup intent event")

	switch event.EventType {
	case "setup_intent.succeeded":
		return p.handleSetupIntentSucceeded(ctx, setupIntentEvent, *event.StripeCustomerId)
	default:
		log.Info().Str("event_type", event.EventType).Msg("Ignoring unhandled setup intent event type")
		return nil
	}
}

// handleSetupIntentSucceeded marks a user as having a valid payment method for Orb billing.
// This enables the user to be billed through Orb's system rather than our direct Stripe integration.
func (p *StripeEventProcessor) handleSetupIntentSucceeded(ctx context.Context, setupIntentEvent *stripe_event.SetupIntentEvent, stripeCustomerID string) error {
	log.Info().
		Str("setup_intent_id", setupIntentEvent.SetupIntentId).
		Str("payment_method_id", setupIntentEvent.PaymentMethodId).
		Str("customer_id", stripeCustomerID).
		Msg("Processing setup intent succeeded event")

	// Find the user with the matching Stripe customer ID
	userDAO := p.daoFactory.GetUserDAO()
	var user *auth_entities.User
	err := userDAO.FindAll(ctx, func(u *auth_entities.User) bool {
		if u.StripeCustomerId == stripeCustomerID {
			user = u
			return false
		}
		return true
	})
	if err != nil {
		log.Error().Err(err).Str("stripe_customer_id", stripeCustomerID).Msg("Failed to find users")
		return fmt.Errorf("failed to find users: %v", err)
	}

	if user == nil {
		log.Error().Str("stripe_customer_id", stripeCustomerID).Msg("No user found with matching Stripe customer ID")
		return fmt.Errorf("no user found with stripe customer ID %s", stripeCustomerID)
	}

	// Sync the user's address from Stripe to Orb for tax collection
	err = p.updateUserAddress(ctx, setupIntentEvent.PaymentMethodId, user.OrbCustomerId)
	if err != nil {
		return fmt.Errorf("failed to update user address: %v", err)
	}

	// Update the user's payment method status if needed
	return p.updateUserPaymentMethodStatus(ctx, stripeCustomerID, user)
}

// After a user successfully sets up a payment method, we need to update their billing address in Orb for tax collection.
// Get their address from the Stripe payment method and save it to the user in Orb
func (p *StripeEventProcessor) updateUserAddress(ctx context.Context, paymentMethodID string, orbCustomerID string) error {
	if paymentMethodID != "" {
		// Get billing address from Stripe
		paymentMethod, err := p.stripeClient.GetPaymentMethod(paymentMethodID)
		if err != nil {
			return fmt.Errorf("failed to get payment method: %v", err)
		}

		// Send the billing address to Orb
		address := paymentMethod.BillingDetails.Address
		err = p.orbClient.UpdateCustomerBillingAddress(ctx, orbCustomerID, &orb.Address{
			Line1:      address.Line1,
			Line2:      address.Line2,
			City:       address.City,
			State:      address.State,
			PostalCode: address.PostalCode,
			Country:    address.Country,
		})
		if err != nil {
			return fmt.Errorf("failed to update customer billing address")
		}
	} else {
		log.Warn().
			Str("customer_id", orbCustomerID).
			Str("payment_method_id", paymentMethodID).
			Msg("Setup intent succeeded but no payment method ID was provided")
	}
	return nil
}

// handlePaymentMethodEvents processes Stripe payment method events.
func (p *StripeEventProcessor) handlePaymentMethodEvents(ctx context.Context, event *stripe_event.StripeEvent) error {
	if p.orbClient == nil {
		// Bypass SetupIntent events if orb is not enabled.
		log.Info().Msg("Skipping setup intent event as orb is not enabled")
		return nil
	}

	paymentMethodEvent := event.GetPaymentMethodEvent()
	if paymentMethodEvent == nil {
		return fmt.Errorf("payment method event is nil")
	}

	if event.StripeCustomerId == nil {
		return fmt.Errorf("customer ID is nil")
	}

	log.Info().
		Str("event_type", event.EventType).
		Str("payment_method_id", paymentMethodEvent.PaymentMethodId).
		Msg("Processing payment method event")

	switch event.EventType {
	case "payment_method.detached":
		return p.handlePaymentMethodDetached(ctx, paymentMethodEvent, *event.StripeCustomerId)
	default:
		log.Info().Str("event_type", event.EventType).Msg("Ignoring unhandled setup intent event type")
		return nil
	}
}

// handlePaymentMethodDetached handles the payment_method.detached event
// payment_method.detached is sent when a payment method is removed from a customer
func (p *StripeEventProcessor) handlePaymentMethodDetached(ctx context.Context, paymentMethodEvent *stripe_event.PaymentMethodEvent, stripeCustomerID string) error {
	log.Info().
		Str("payment_method_id", paymentMethodEvent.PaymentMethodId).
		Msg("Processing payment method detached event")

	// Find the user with the matching Stripe customer ID
	userDAO := p.daoFactory.GetUserDAO()

	// Find the user with matching Stripe customer ID
	var user *auth_entities.User
	err := userDAO.FindAll(ctx, func(u *auth_entities.User) bool {
		if u.StripeCustomerId == stripeCustomerID {
			user = u
			return false
		}
		return true
	})
	if err != nil {
		log.Error().Err(err).Str("stripe_customer_id", stripeCustomerID).Msg("Failed to find users")
		return fmt.Errorf("failed to find users: %v", err)
	}

	if user == nil {
		log.Error().Str("stripe_customer_id", stripeCustomerID).Msg("No user found with matching Stripe customer ID")
		return fmt.Errorf("no user found with stripe customer ID %s", stripeCustomerID)
	}

	// Update the user's payment method status if needed
	return p.updateUserPaymentMethodStatus(ctx, stripeCustomerID, user)
}

// updateUserPaymentMethodStatus is a helper function that updates a user's payment method status.
// It finds a user by Stripe customer ID, checks if they have a subscription with Orb billing,
// and updates the subscription's HasPaymentMethod field if needed.
func (p *StripeEventProcessor) updateUserPaymentMethodStatus(ctx context.Context, stripeCustomerID string, user *auth_entities.User) error {
	// Check if the user has a payment method
	hasPaymentMethod, err := p.stripeClient.HasPaymentMethod(stripeCustomerID)
	if err != nil {
		log.Error().Err(err).Str("stripe_customer_id", stripeCustomerID).Msg("Failed to check payment method status")
		return fmt.Errorf("failed to check payment method status: %v", err)
	}

	// Update the Subscription to indicate the payment method status
	subscriptionDAO := p.daoFactory.GetSubscriptionDAO()
	// Make sure the user has a subscription and is using Orb for billing
	if user.OrbSubscriptionId != "" && user.BillingMethod == auth_entities.BillingMethod_BILLING_METHOD_ORB {
		// Check if the payment method status has changed
		existingSubscription, err := subscriptionDAO.Get(ctx, user.OrbSubscriptionId)
		if err != nil {
			return fmt.Errorf("failed to get existing subscription: %v", err)
		}

		if existingSubscription.HasPaymentMethod == hasPaymentMethod {
			log.Info().Str("subscription_id", existingSubscription.SubscriptionId).Msg("Payment method status is already up to date, no changes needed")
			return nil
		}

		// Update the subscription
		_, err = subscriptionDAO.TryUpdate(ctx, user.OrbSubscriptionId, func(s *auth_entities.Subscription) bool {
			s.HasPaymentMethod = hasPaymentMethod
			s.UpdatedAt = timestamppb.Now()
			return true
		}, DefaultRetry)

		log.Info().Str("subscription_id", existingSubscription.SubscriptionId).Msg("Updated subscription payment method status")
		if err != nil {
			return fmt.Errorf("failed to update subscription: %v", err)
		}
	}

	return nil
}

// handleCustomerEvents processes Stripe customer lifecycle events.
func (p *StripeEventProcessor) handleCustomerEvents(ctx context.Context, event *stripe_event.StripeEvent) error {
	customerEvent := event.GetCustomerEvent()
	if customerEvent == nil {
		return fmt.Errorf("customer event is nil")
	}

	if event.StripeCustomerId == nil {
		return fmt.Errorf("customer ID is nil")
	}

	log.Info().
		Str("event_type", event.EventType).
		Str("customer_id", *event.StripeCustomerId).
		Msg("Processing customer event")

	// For now, we don't need to do anything with customer events
	// We may need to add logic here in the future
	return nil
}

// findUserForSubscription tries to find a user associated with a subscription
func (p *StripeEventProcessor) findUserForSubscription(ctx context.Context, metadata map[string]string, stripeCustomerID string) (*auth_entities.User, error) {
	var user *auth_entities.User
	var err error
	userDAO := p.daoFactory.GetUserDAO()

	// First try to find the user from metadata if available
	if metadata != nil {
		if userID, ok := metadata["augment_user_id"]; ok && userID != "" {
			log.Info().Str("augment_user_id", userID).Msg("Found augment_user_id in subscription metadata")
			// If we have a user ID, fetch the user
			user, err = userDAO.Get(ctx, userID)
			if err != nil {
				log.Warn().Err(err).Str("augment_user_id", userID).Msg("Failed to fetch user from metadata ID")
				// Don't return error here, we'll try the fallback method
			} else if user != nil {
				log.Info().Str("user_id", user.Id).Msg("Successfully found user from metadata ID")
				return user, nil
			}
		}
	}

	// If we didn't find a user from metadata, fall back to searching by customer ID
	if user == nil {
		log.Info().Str("stripe_customer_id", stripeCustomerID).Msg("Falling back to finding user by customer ID")

		err = userDAO.FindAll(ctx, func(u *auth_entities.User) bool {
			if u.StripeCustomerId == stripeCustomerID {
				user = u
				return false
			}
			return true
		})
		if err != nil {
			log.Warn().Err(err).Msg("Failed to find users in subscription event handler")
			return nil, err
		}
	}

	if user == nil {
		log.Warn().Str("stripe_customer_id", stripeCustomerID).Msg("No user found for customer ID in subscription event")
	}

	return user, nil
}

// checkStripeEventProcessorDeadLetterFlag checks if dead letter queue processing is enabled via feature flag
func checkStripeEventProcessorDeadLetterFlag(featureFlagHandle featureflags.FeatureFlagHandle) bool {
	flagVal, err := featureFlagHandle.GetBool("auth_central_stripe_event_processor_process_dead_letter_queue", false)
	if err != nil {
		log.Error().Err(err).Msg("Error reading dead letter queue feature flag")
		return false
	}
	return flagVal
}
