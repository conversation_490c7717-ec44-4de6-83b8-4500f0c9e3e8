package main

import (
	"context"
	"fmt"
	"testing"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/augmentcode/augment/services/auth/central/server/test_utils"
	"github.com/augmentcode/augment/services/integrations/orb"
	tw_client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var (
	oldTenant *tw_pb.Tenant = &tw_pb.Tenant{
		Id:             "old-tenant-id",
		Name:           "old-tenant",
		ShardNamespace: "old-namespace",
		Cloud:          "test-cloud",
	}
	newTenant *tw_pb.Tenant = &tw_pb.Tenant{
		Id:             "new-tenant-id",
		Name:           "new-tenant",
		ShardNamespace: "new-namespace",
		Cloud:          "test-cloud",
	}
)

var orbPlanConfig = []PlanConfig{
	{
		ID:        "orb_trial_plan",
		SortOrder: 2,
		Features: PlanFeatures{
			PlanType: PlanTypePaidTrial,
		},
	},
	{
		ID:        "orb_community_plan",
		SortOrder: 1,
		Features: PlanFeatures{
			PlanType: PlanTypeCommunity,
		},
	},
	{
		ID:        "orb_developer_plan",
		SortOrder: 3,
		Features: PlanFeatures{
			PlanType: PlanTypePaid,
		},
	},
	{
		ID:        "orb_professional_plan",
		SortOrder: 4,
		Features: PlanFeatures{
			PlanType: PlanTypePaid,
		},
	},
	{
		ID:        "orb_max_plan",
		SortOrder: 5,
		Features: PlanFeatures{
			PlanType: PlanTypePaid,
		},
	},
}

// setupTierChangeTest creates the common test setup for tier change tests
func setupTierChangeTest(t *testing.T) (context.Context, *DAOFactory, *TenantMap, *orb.MockOrbClient, func()) {
	// Create context
	ctx, cancel := context.WithCancel(context.Background())

	// Set up bigtable
	bigtableFixture := NewBigtableFixture(t)
	cleanup := func() {
		cancel()
		bigtableFixture.Cleanup()
	}

	// Create DAO factory
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	daoFactoryFixture := NewDAOFactoryFixture(bigtableFixture)

	// Create a mock tenant watcher client
	mockTenantWatcherClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			oldTenant,
			newTenant,
		},
	}

	// Create a mock orb client
	mockOrbClient := orb.NewMockOrbClient()

	// Create a mock tenant map
	tenantMap := NewTenantMap(
		daoFactoryFixture.DAOFactory,
		mockTenantWatcherClient,
		"us-central.api.augmentcode.com",
		featureflags.NewLocalFeatureFlagHandler(),
		NewMockAsyncOpsPublisher(),
		audit.NewDefaultAuditLogger(),
	)

	return ctx, daoFactory, tenantMap, mockOrbClient, cleanup
}

// TestHandleOrbSubscription tests the handleSubscriptionChange function with Orb integration
func TestHandleOrbSubscription(t *testing.T) {
	// Set up common test fixtures
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	userDAO := daoFactory.GetUserDAO()

	// Define test case structure
	type testCase struct {
		name                  string
		user                  *auth_entities.User
		tierTo                auth_entities.UserTier
		planTo                string
		orbFeatFlagEnabled    bool // This should be ignored in the updated implementation
		currentPlanID         string
		orbStatus             string
		expectPlanChange      bool
		expectNewSubscription bool
		expectError           bool
		useNilOrbClient       bool // Flag to indicate if this test should use a nil orbClient
	}

	// Create test cases with more focused scenarios
	testCases := []testCase{
		{
			name: "Orb billing method - feature flag disabled - should still use Orb",
			user: &auth_entities.User{
				Id:                "user1",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_1",
				OrbSubscriptionId: "orb_sub_1",
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_PROFESSIONAL,
			planTo:                "orb_developer_plan",
			orbFeatFlagEnabled:    false, // Feature flag disabled but should be ignored
			currentPlanID:         "orb_community_plan",
			orbStatus:             "active",
			expectPlanChange:      true, // Should still change plan because BillingMethod is ORB
			expectNewSubscription: false,
		},
		{
			name: "Orb billing method - nil orb client - should return error",
			user: &auth_entities.User{
				Id:                "user_nil_client",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_nil",
				OrbSubscriptionId: "orb_sub_nil",
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_PROFESSIONAL,
			planTo:                "orb_developer_plan",
			orbFeatFlagEnabled:    true,
			currentPlanID:         "orb_community_plan",
			orbStatus:             "active",
			expectPlanChange:      false,
			expectNewSubscription: false,
			expectError:           true,
			// Add a flag to indicate this test should use a nil orbClient
			useNilOrbClient: true,
		},
		{
			name: "Orb billing method - invalid tier - error",
			user: &auth_entities.User{
				Id:                "user6",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_6",
				OrbSubscriptionId: "orb_sub_6",
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_UNKNOWN_TIER,
			planTo:                "orb_unknown_plan",
			orbFeatFlagEnabled:    false, // Feature flag disabled but should be ignored
			currentPlanID:         "orb_trial_plan",
			orbStatus:             "active",
			expectPlanChange:      false,
			expectNewSubscription: false,
			expectError:           true,
		},
		{
			name: "Orb billing method - inactive subscription - create new subscription",
			user: &auth_entities.User{
				Id:                "user7",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_7",
				OrbSubscriptionId: "orb_sub_7",
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_PROFESSIONAL,
			planTo:                "orb_developer_plan",
			orbFeatFlagEnabled:    false, // Feature flag disabled but should be ignored
			orbStatus:             "canceled",
			expectPlanChange:      false,
			expectNewSubscription: true,
		},
		{
			name: "Orb billing method - no subscription ID - create new subscription",
			user: &auth_entities.User{
				Id:                "user8",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_8",
				OrbSubscriptionId: "", // Empty subscription ID
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_COMMUNITY,
			planTo:                "orb_community_plan",
			orbFeatFlagEnabled:    false, // Feature flag disabled but should be ignored
			expectPlanChange:      false,
			expectNewSubscription: true,
		},
		{
			name: "Orb billing method - error retrieving subscription - create new subscription",
			user: &auth_entities.User{
				Id:                "user9",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_9",
				OrbSubscriptionId: "error-subscription-id", // Special ID that triggers error
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_PROFESSIONAL,
			planTo:                "orb_developer_plan",
			orbFeatFlagEnabled:    false, // Feature flag disabled but should be ignored
			expectPlanChange:      false,
			expectNewSubscription: true,
		},
		{
			name: "Orb - Upgrade PROFESSIONAL to MAX",
			user: &auth_entities.User{
				Id:                "user10",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_10",
				OrbSubscriptionId: "orb_sub_10",
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_PROFESSIONAL,
			planTo:                "orb_max_plan",
			currentPlanID:         "orb_developer_plan", // Current PROFESSIONAL plan
			orbStatus:             "active",
			expectPlanChange:      true,
			expectNewSubscription: false,
			expectError:           false,
		},
		{
			name: "Orb - Downgrade MAX to PROFESSIONAL",
			user: &auth_entities.User{
				Id:                "user11",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_11",
				OrbSubscriptionId: "orb_sub_11",
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_PROFESSIONAL,
			planTo:                "orb_professional_plan",
			currentPlanID:         "orb_max_plan", // Current MAX plan
			orbStatus:             "active",
			expectPlanChange:      true,
			expectNewSubscription: false,
			expectError:           false,
		},
		{
			name: "Orb - Upgrade COMMUNITY to MAX",
			user: &auth_entities.User{
				Id:                "user12",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_12",
				OrbSubscriptionId: "orb_sub_12",
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_PROFESSIONAL,
			planTo:                "orb_max_plan",
			currentPlanID:         "orb_community_plan", // Current COMMUNITY plan
			orbStatus:             "active",
			expectPlanChange:      true,
			expectNewSubscription: false,
			expectError:           false,
		},
		{
			name: "Orb - New subscription to MAX (from inactive)",
			user: &auth_entities.User{
				Id:                "user13",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_13",
				OrbSubscriptionId: "orb_sub_13_inactive",
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_PROFESSIONAL,
			planTo:                "orb_max_plan",
			currentPlanID:         "orb_community_plan", // Does not matter much
			orbStatus:             "canceled",
			expectPlanChange:      false,
			expectNewSubscription: true,
			expectError:           false,
		},
		{
			name: "Orb - New subscription to MAX (no prior sub ID)",
			user: &auth_entities.User{
				Id:                "user14",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_14",
				OrbSubscriptionId: "", // No prior subscription ID
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_PROFESSIONAL,
			planTo:                "orb_max_plan",
			currentPlanID:         "",
			orbStatus:             "", // Does not matter
			expectPlanChange:      false,
			expectNewSubscription: true,
			expectError:           false,
		},
		{
			name: "Orb - No change (already MAX)",
			user: &auth_entities.User{
				Id:                "user15",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_15",
				OrbSubscriptionId: "orb_sub_15",
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_PROFESSIONAL,
			planTo:                "orb_max_plan",
			currentPlanID:         "orb_max_plan", // Already on MAX plan
			orbStatus:             "active",
			expectPlanChange:      false,
			expectNewSubscription: false,
			expectError:           false,
		},
		{
			name: "Orb - Unschedule pending cancellation when changing plan",
			user: &auth_entities.User{
				Id:                "user-with-canceled-subscription",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_16",
				OrbSubscriptionId: "orb_sub_16",
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_PROFESSIONAL,
			planTo:                "orb_max_plan",
			currentPlanID:         "orb_developer_plan", // Current plan
			orbStatus:             "active",
			expectPlanChange:      true,
			expectNewSubscription: false,
			expectError:           false,
		},
		{
			name: "Orb - Paid trial with EndDate should NOT unschedule cancellation",
			user: &auth_entities.User{
				Id:                "user-paid-trial-with-end-date",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_17",
				OrbSubscriptionId: "orb_sub_17",
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			},
			tierTo:                auth_entities.UserTier_PROFESSIONAL,
			planTo:                "orb_developer_plan",
			currentPlanID:         "orb_trial_plan", // Current paid trial plan
			orbStatus:             "active",
			expectPlanChange:      true,
			expectNewSubscription: false,
			expectError:           false,
		},
	}

	// Create all users in bigtable
	for _, tc := range testCases {
		_, err := userDAO.Create(ctx, tc.user)
		require.NoError(t, err, "Failed to create test user %s", tc.user.Id)
	}

	// Run each test case
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup feature flag and mock client
			featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
			mockOrbClient := orb.NewMockOrbClient()

			// Create UserTierManager
			manager := &UserTierManager{
				daoFactory:        daoFactory,
				featureFlagHandle: featureFlagHandle,
				orbConfig: &OrbConfig{
					Enabled:                true,
					Plans:                  orbPlanConfig,
					IncludedMessagesItemID: "included-messages-item-id",
				},
				orbClient:   mockOrbClient,
				auditLogger: audit.NewDefaultAuditLogger(),
			}

			// For the nil client test case, set orbClient to nil
			if tc.useNilOrbClient {
				manager.orbClient = nil
			}

			// Setup mock responses only if we're not testing nil client
			if !tc.useNilOrbClient {
				if tc.user.OrbSubscriptionId == "error-subscription-id" {
					mockOrbClient.On("GetUserSubscription", mock.Anything, "error-subscription-id", mock.Anything).
						Return(&orb.OrbSubscriptionInfo{}, fmt.Errorf("subscription not found"))
				} else if tc.user.OrbSubscriptionId != "" {
					subscriptionInfo := &orb.OrbSubscriptionInfo{
						OrbSubscriptionID:             tc.user.OrbSubscriptionId,
						ExternalPlanID:                tc.currentPlanID,
						OrbStatus:                     tc.orbStatus,
						CurrentBillingPeriodStartDate: time.Now().Add(-30 * 24 * time.Hour),
						CurrentBillingPeriodEndDate:   time.Now().Add(30 * 24 * time.Hour),
					}

					// For the cancellation unscheduling test case, set EndDate to indicate scheduled cancellation
					if tc.user.Id == "user-with-canceled-subscription" {
						subscriptionInfo.EndDate = time.Now().Add(30 * 24 * time.Hour) // Scheduled to end in 30 days
					}

					// For the paid trial test case, set EndDate to indicate scheduled cancellation
					if tc.user.Id == "user-paid-trial-with-end-date" {
						subscriptionInfo.EndDate = time.Now().Add(30 * 24 * time.Hour) // Scheduled to end in 30 days
					}

					mockOrbClient.On("GetUserSubscription", mock.Anything, tc.user.OrbSubscriptionId, mock.Anything).
						Return(subscriptionInfo, nil)
				}

				// Setup other mock responses
				mockOrbClient.On("SetCustomerPlanType", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockOrbClient.On("CreateSubscription", mock.Anything, mock.Anything, mock.Anything).Return("new-subscription-id", nil)

				// For the cancellation unscheduling test case, expect UnschedulePendingSubscriptionCancellation to be called
				if tc.user.Id == "user-with-canceled-subscription" {
					mockOrbClient.On("UnschedulePendingSubscriptionCancellation", mock.Anything, tc.user.OrbSubscriptionId, mock.Anything).Return(nil)
				}

				// Mock GetPlanInformation for current plan (using subscription ID to get grandfathered pricing)
				if tc.user.OrbSubscriptionId != "" && tc.user.OrbSubscriptionId != "error-subscription-id" {
					mockOrbClient.On("GetPlanInformation", mock.Anything, mock.Anything,
						mock.MatchedBy(func(subscriptionID *string) bool {
							return subscriptionID != nil && *subscriptionID == tc.user.OrbSubscriptionId
						}), (*string)(nil)).Return(&orb.OrbPlanInfo{
						ExternalPlanID:          tc.currentPlanID,
						MessagesPerSeat:         1000,
						IncludedMessagesPriceID: "included-messages-price-id",
					}, nil)
				}

				// Mock GetPlanInformation for target plan (using plan ID for standard configuration)
				mockOrbClient.On("GetPlanInformation", mock.Anything, mock.Anything, (*string)(nil),
					mock.MatchedBy(func(planID *string) bool {
						return planID != nil && *planID == tc.planTo
					})).Return(&orb.OrbPlanInfo{
					ExternalPlanID:          tc.planTo,
					MessagesPerSeat:         2000,
					IncludedMessagesPriceID: "included-messages-price-id",
				}, nil)

				mockOrbClient.On("PurchaseCredits", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockOrbClient.On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			}

			// Execute test
			msg := &auth_internal.UserTierChangeMessage{
				User:         tc.user,
				NewTier:      tc.tierTo,
				NewPlanId:    tc.planTo,
				TierChangeId: fmt.Sprintf("tier-change-%s", tc.user.Id),
				CurrentTenant: &tw_pb.Tenant{
					Name: "current_tenant",
				},
			}

			err := manager.processOrbSubscription(ctx, msg)

			// Verify results
			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Skip mock assertions for nil client test
			if !tc.useNilOrbClient {
				// Verify plan change calls
				if tc.expectPlanChange {
					mockOrbClient.AssertNumberOfCalls(t, "SetCustomerPlanType", 1)

					var expectedPlanID string
					if tc.tierTo == auth_entities.UserTier_PROFESSIONAL {
						expectedPlanID = tc.planTo
					} else {
						expectedPlanID = "orb_community_plan"
					}

					mockOrbClient.AssertCalled(t, "SetCustomerPlanType", mock.Anything,
						mock.MatchedBy(func(planChange orb.OrbPlanChange) bool {
							return planChange.CustomerOrbID == tc.user.OrbCustomerId &&
								planChange.SubscriptionID == tc.user.OrbSubscriptionId &&
								planChange.NewPlanID == expectedPlanID
						}), mock.Anything)
				} else {
					mockOrbClient.AssertNumberOfCalls(t, "SetCustomerPlanType", 0)
				}

				// Verify subscription creation
				if tc.expectNewSubscription {
					mockOrbClient.AssertNumberOfCalls(t, "CreateSubscription", 1)

					// Verify user was updated with new subscription ID
					if tc.user.OrbSubscriptionId == "" || tc.user.OrbSubscriptionId == "error-subscription-id" {
						updatedUser, err := userDAO.Get(ctx, tc.user.Id)
						require.NoError(t, err)
						assert.Equal(t, "new-subscription-id", updatedUser.OrbSubscriptionId)
					}
				} else {
					mockOrbClient.AssertNumberOfCalls(t, "CreateSubscription", 0)
				}

				// Verify UnschedulePendingSubscriptionCancellation was called for the cancellation unscheduling test
				if tc.user.Id == "user-with-canceled-subscription" {
					mockOrbClient.AssertNumberOfCalls(t, "UnschedulePendingSubscriptionCancellation", 1)
					mockOrbClient.AssertCalled(t, "UnschedulePendingSubscriptionCancellation", mock.Anything, tc.user.OrbSubscriptionId, mock.Anything)
				} else {
					mockOrbClient.AssertNumberOfCalls(t, "UnschedulePendingSubscriptionCancellation", 0)
				}

				// Specifically verify that paid trial subscriptions with EndDate do NOT unschedule cancellation
				if tc.user.Id == "user-paid-trial-with-end-date" {
					mockOrbClient.AssertNumberOfCalls(t, "UnschedulePendingSubscriptionCancellation", 0)
				}
			}
		})
	}
}

func setupOrbPlanChange(t *testing.T, tierTo auth_entities.UserTier, currentPlan string, planTo string) (context.Context, *UserTierManager, *auth_internal.UserTierChangeMessage, func()) {
	ctx, cancel := context.WithCancel(context.Background())

	bigtableFixture := NewBigtableFixture(t)

	daoFactory := NewDAOFactory(bigtableFixture.Table)

	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
	mockOrbClient := orb.NewMockOrbClient()

	mockOrbClient.On("SetCustomerPlanType", mock.Anything, mock.Anything, mock.Anything).Return(nil)
	mockOrbClient.On("GetUserSubscription", mock.Anything, "orb_sub_1", mock.Anything).
		Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID:             "orb_sub_1",
			ExternalPlanID:                currentPlan,
			OrbStatus:                     "active",
			CurrentBillingPeriodStartDate: time.Now().Add(-15 * 24 * time.Hour),
			CurrentBillingPeriodEndDate:   time.Now().Add(15 * 24 * time.Hour),
		}, nil)
	// Helper function to get price per seat based on plan ID
	getPricePerSeat := func(planID string) string {
		switch planID {
		case "orb_trial_plan":
			return "75.00"
		case "orb_community_plan":
			return "0.00"
		case "orb_developer_plan":
			return "100.00"
		case "orb_professional_plan":
			return "200.00"
		case "orb_max_plan":
			return "300.00"
		default:
			return "0.00"
		}
	}

	// Mock GetPlanInformation for current plan (using subscription ID to get grandfathered pricing)
	mockOrbClient.On("GetPlanInformation",
		mock.Anything,
		mock.Anything,
		mock.MatchedBy(func(subscriptionID *string) bool {
			return subscriptionID != nil && *subscriptionID == "orb_sub_1"
		}),
		(*string)(nil)).
		Return(&orb.OrbPlanInfo{
			ExternalPlanID:          currentPlan,
			MessagesPerSeat:         100,
			PricePerSeat:            getPricePerSeat(currentPlan),
			SeatsPriceID:            "seats-price-id",
			IncludedMessagesPriceID: "included-messages-price-id",
		}, nil)
	// Mock GetPlanInformation for target plan (using plan ID for standard configuration)
	mockOrbClient.On("GetPlanInformation",
		mock.Anything,
		mock.Anything,
		(*string)(nil),
		mock.MatchedBy(func(planID *string) bool {
			return planID != nil && (*planID == "orb_community_plan" || *planID == "orb_developer_plan")
		})).
		Return(&orb.OrbPlanInfo{
			ExternalPlanID:          planTo,
			MessagesPerSeat:         200,
			PricePerSeat:            getPricePerSeat(planTo),
			SeatsPriceID:            "seats-price-id",
			IncludedMessagesPriceID: "included-messages-price-id",
		}, nil)
	mockOrbClient.On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil)

	// Create UserTierManager
	manager := &UserTierManager{
		daoFactory:        daoFactory,
		featureFlagHandle: featureFlagHandle,
		orbConfig: &OrbConfig{
			Enabled:                true,
			Plans:                  orbPlanConfig,
			IncludedMessagesItemID: "included-messages-item-id",
		},
		orbClient:   mockOrbClient,
		auditLogger: audit.NewDefaultAuditLogger(),
	}

	user := &auth_entities.User{
		Id:                "user1",
		Email:             "<EMAIL>",
		OrbCustomerId:     "orb_cust_1",
		OrbSubscriptionId: "orb_sub_1",
		BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
	}

	// Create a user on the trial plan
	userDAO := manager.daoFactory.GetUserDAO()
	_, err := userDAO.Create(ctx, user)
	require.NoError(t, err)

	// Return tier change message
	msg := &auth_internal.UserTierChangeMessage{
		User:         user,
		NewTier:      tierTo,
		TierChangeId: fmt.Sprintf("tier-change-%s", user.Id),
		CurrentTenant: &tw_pb.Tenant{
			Name: "current_tenant",
		},
		PublishTime: timestamppb.Now(),
		NewPlanId:   planTo,
	}

	cleanup := func() {
		cancel()
		bigtableFixture.Cleanup()
	}

	return ctx, manager, msg, cleanup
}

// Test of the business logic around selecting plan changes, whether they hapen immediately or end of term, and whether we add pro-ration
func TestOrbPlanChangesWithProration(t *testing.T) {
	t.Run("Trial to Professional", func(t *testing.T) {
		ctx, manager, msg, cleanup := setupOrbPlanChange(t, auth_entities.UserTier_PROFESSIONAL, "orb_trial_plan", "orb_developer_plan")
		defer cleanup()

		mockOrbClient := manager.orbClient.(*orb.MockOrbClient)

		err := manager.processOrbSubscription(ctx, msg)
		require.NoError(t, err)

		// Verify plan change call
		mockOrbClient.AssertNumberOfCalls(t, "SetCustomerPlanType", 1)
		mockOrbClient.AssertCalled(t, "SetCustomerPlanType", mock.Anything,
			mock.MatchedBy(func(planChange orb.OrbPlanChange) bool {
				return planChange.NewPlanID == "orb_developer_plan" &&
					planChange.PlanChangeType == orb.PlanChangeImmediate &&
					planChange.BillingCycleAlignment == orb.BillingCycleAlignmentPlanChangeDate
			}), mock.Anything)
	})

	t.Run("Professional to Community", func(t *testing.T) {
		ctx, manager, msg, cleanup := setupOrbPlanChange(t, auth_entities.UserTier_COMMUNITY, "orb_developer_plan", "orb_community_plan")
		defer cleanup()

		mockOrbClient := manager.orbClient.(*orb.MockOrbClient)

		err := manager.processOrbSubscription(ctx, msg)
		require.NoError(t, err)

		// Verify plan change call
		mockOrbClient.AssertNumberOfCalls(t, "SetCustomerPlanType", 1)
		mockOrbClient.AssertCalled(t, "SetCustomerPlanType", mock.Anything,
			mock.MatchedBy(func(planChange orb.OrbPlanChange) bool {
				return planChange.NewPlanID == "orb_community_plan" &&
					planChange.PlanChangeType == orb.PlanChangeImmediate &&
					planChange.BillingCycleAlignment == orb.BillingCycleAlignmentPlanChangeDate
			}), mock.Anything)
	})

	t.Run("Community to Professional", func(t *testing.T) {
		ctx, manager, msg, cleanup := setupOrbPlanChange(t, auth_entities.UserTier_PROFESSIONAL, "orb_community_plan", "orb_developer_plan")
		defer cleanup()

		mockOrbClient := manager.orbClient.(*orb.MockOrbClient)

		err := manager.processOrbSubscription(ctx, msg)
		require.NoError(t, err)

		// Verify plan change call - Community to Paid plans should NOT have proration
		mockOrbClient.AssertNumberOfCalls(t, "SetCustomerPlanType", 1)
		mockOrbClient.AssertCalled(t, "SetCustomerPlanType", mock.Anything,
			mock.MatchedBy(func(planChange orb.OrbPlanChange) bool {
				return planChange.NewPlanID == "orb_developer_plan" &&
					planChange.PlanChangeType == orb.PlanChangeImmediate &&
					planChange.BillingCycleAlignment == orb.BillingCycleAlignmentPlanChangeDate && // billing dates change for community transitions
					len(planChange.PriceOverrides) == 0 // No proration for community → paid transitions
			}), mock.Anything)
		// No UpdateFixedQuantity call should be made for community → paid transitions
		mockOrbClient.AssertNumberOfCalls(t, "UpdateFixedQuantity", 0)
	})
}

// Test comprehensive plan transitions between all plan types
func TestPlanTransitions(t *testing.T) {
	testCases := []struct {
		name                     string
		currentPlanID            string
		targetPlanID             string
		isUpgrade                bool
		needsProrate             bool
		expectedPlanChangeType   orb.PlanChangeType
		expectedBillingAlignment orb.BillingCycleAlignment
		currentMessagesPerSeat   float64
		targetMessagesPerSeat    float64
		expectedProRatedCredits  float64 // Expected pro-rated credits for upgrades
	}{
		// Community/Trial to Paid plan transitions (no proration, billing cycle changes)
		{
			name:                     "Trial to Developer",
			currentPlanID:            "orb_trial_plan",
			targetPlanID:             "orb_developer_plan",
			isUpgrade:                true,
			needsProrate:             false, // No proration for community/trial → paid
			expectedPlanChangeType:   orb.PlanChangeImmediate,
			expectedBillingAlignment: orb.BillingCycleAlignmentPlanChangeDate,
			currentMessagesPerSeat:   75.0,
			targetMessagesPerSeat:    100.0,
			expectedProRatedCredits:  0, // No proration
		},
		{
			name:                     "Trial to Pro",
			currentPlanID:            "orb_trial_plan",
			targetPlanID:             "orb_professional_plan",
			isUpgrade:                true,
			needsProrate:             false, // No proration for community/trial → paid
			expectedPlanChangeType:   orb.PlanChangeImmediate,
			expectedBillingAlignment: orb.BillingCycleAlignmentPlanChangeDate,
			currentMessagesPerSeat:   75.0,
			targetMessagesPerSeat:    200.0,
			expectedProRatedCredits:  0, // No proration
		},
		{
			name:                     "Trial to Max",
			currentPlanID:            "orb_trial_plan",
			targetPlanID:             "orb_max_plan",
			isUpgrade:                true,
			needsProrate:             false, // No proration for community/trial → paid
			expectedPlanChangeType:   orb.PlanChangeImmediate,
			expectedBillingAlignment: orb.BillingCycleAlignmentPlanChangeDate,
			currentMessagesPerSeat:   75.0,
			targetMessagesPerSeat:    300.0,
			expectedProRatedCredits:  0, // No proration
		},
		{
			name:                     "Community to Developer",
			currentPlanID:            "orb_community_plan",
			targetPlanID:             "orb_developer_plan",
			isUpgrade:                true,
			needsProrate:             false, // No proration for community/trial → paid
			expectedPlanChangeType:   orb.PlanChangeImmediate,
			expectedBillingAlignment: orb.BillingCycleAlignmentPlanChangeDate,
			currentMessagesPerSeat:   50.0,
			targetMessagesPerSeat:    100.0,
			expectedProRatedCredits:  0, // No proration
		},
		{
			name:                     "Community to Pro",
			currentPlanID:            "orb_community_plan",
			targetPlanID:             "orb_professional_plan",
			isUpgrade:                true,
			needsProrate:             false, // No proration for community/trial → paid
			expectedPlanChangeType:   orb.PlanChangeImmediate,
			expectedBillingAlignment: orb.BillingCycleAlignmentPlanChangeDate,
			currentMessagesPerSeat:   50.0,
			targetMessagesPerSeat:    200.0,
			expectedProRatedCredits:  0, // No proration
		},
		{
			name:                     "Community to Max",
			currentPlanID:            "orb_community_plan",
			targetPlanID:             "orb_max_plan",
			isUpgrade:                true,
			needsProrate:             false, // No proration for community/trial → paid
			expectedPlanChangeType:   orb.PlanChangeImmediate,
			expectedBillingAlignment: orb.BillingCycleAlignmentPlanChangeDate,
			currentMessagesPerSeat:   50.0,
			targetMessagesPerSeat:    300.0,
			expectedProRatedCredits:  0, // No proration
		},
		// Paid to Community transitions (immediate, billing cycle changes)
		{
			name:                     "Developer to Community",
			currentPlanID:            "orb_developer_plan",
			targetPlanID:             "orb_community_plan",
			isUpgrade:                false,
			needsProrate:             false, // No proration for paid → community
			expectedPlanChangeType:   orb.PlanChangeImmediate,
			expectedBillingAlignment: orb.BillingCycleAlignmentPlanChangeDate,
			currentMessagesPerSeat:   100.0,
			targetMessagesPerSeat:    50.0,
			expectedProRatedCredits:  0, // No proration
		},
		// Paid-to-Paid plan transitions (proration for upgrades, billing cycle unchanged)
		{
			name:                     "Developer to Pro - Upgrade",
			currentPlanID:            "orb_developer_plan",
			targetPlanID:             "orb_professional_plan",
			isUpgrade:                true,
			needsProrate:             true,
			expectedPlanChangeType:   orb.PlanChangeImmediate,
			expectedBillingAlignment: orb.BillingCycleAlignmentUnchanged,
			currentMessagesPerSeat:   100.0,
			targetMessagesPerSeat:    200.0,
			expectedProRatedCredits:  150.0, // Pro-rated halfway through month
		},
		{
			name:                     "Developer to Max - Upgrade",
			currentPlanID:            "orb_developer_plan",
			targetPlanID:             "orb_max_plan",
			isUpgrade:                true,
			needsProrate:             true,
			expectedPlanChangeType:   orb.PlanChangeImmediate,
			expectedBillingAlignment: orb.BillingCycleAlignmentUnchanged,
			currentMessagesPerSeat:   100.0,
			targetMessagesPerSeat:    300.0,
			expectedProRatedCredits:  200.0, // Pro-rated halfway through month
		},
		{
			name:                     "Pro to Developer - Downgrade",
			currentPlanID:            "orb_professional_plan",
			targetPlanID:             "orb_developer_plan",
			isUpgrade:                false,
			needsProrate:             false,
			expectedPlanChangeType:   orb.PlanChangeEndOfTerm,
			expectedBillingAlignment: orb.BillingCycleAlignmentUnchanged,
			currentMessagesPerSeat:   200.0,
			targetMessagesPerSeat:    100.0,
			expectedProRatedCredits:  0, // No proration for downgrades
		},
		{
			name:                     "Pro to Max - Upgrade",
			currentPlanID:            "orb_professional_plan",
			targetPlanID:             "orb_max_plan",
			isUpgrade:                true,
			needsProrate:             true,
			expectedPlanChangeType:   orb.PlanChangeImmediate,
			expectedBillingAlignment: orb.BillingCycleAlignmentUnchanged,
			currentMessagesPerSeat:   200.0,
			targetMessagesPerSeat:    300.0,
			expectedProRatedCredits:  250.0, // Pro-rated halfway through month
		},
		{
			name:                     "Max to Developer - Downgrade",
			currentPlanID:            "orb_max_plan",
			targetPlanID:             "orb_developer_plan",
			isUpgrade:                false,
			needsProrate:             false,
			expectedPlanChangeType:   orb.PlanChangeEndOfTerm,
			expectedBillingAlignment: orb.BillingCycleAlignmentUnchanged,
			currentMessagesPerSeat:   300.0,
			targetMessagesPerSeat:    100.0,
			expectedProRatedCredits:  0, // No proration for downgrades
		},
		{
			name:                     "Max to Pro - Downgrade",
			currentPlanID:            "orb_max_plan",
			targetPlanID:             "orb_professional_plan",
			isUpgrade:                false,
			needsProrate:             false,
			expectedPlanChangeType:   orb.PlanChangeEndOfTerm,
			expectedBillingAlignment: orb.BillingCycleAlignmentUnchanged,
			currentMessagesPerSeat:   300.0,
			targetMessagesPerSeat:    200.0,
			expectedProRatedCredits:  0, // No proration for downgrades
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()

			bigtableFixture := NewBigtableFixture(t)
			defer bigtableFixture.Cleanup()

			daoFactory := NewDAOFactory(bigtableFixture.Table)
			featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
			mockOrbClient := orb.NewMockOrbClient()

			// Setup mock responses
			mockOrbClient.On("GetUserSubscription", mock.Anything, "orb_sub_1", mock.Anything).
				Return(&orb.OrbSubscriptionInfo{
					OrbSubscriptionID:             "orb_sub_1",
					ExternalPlanID:                tc.currentPlanID,
					OrbStatus:                     "active",
					CurrentBillingPeriodStartDate: time.Now().Add(-15 * 24 * time.Hour), // 15 days ago
					CurrentBillingPeriodEndDate:   time.Now().Add(15 * 24 * time.Hour),  // 15 days from now (halfway through)
				}, nil)

			// Helper function to get price per seat based on plan ID
			getPricePerSeat := func(planID string) string {
				switch planID {
				case "orb_trial_plan":
					return "75.00"
				case "orb_community_plan":
					return "0.00"
				case "orb_developer_plan":
					return "100.00"
				case "orb_professional_plan":
					return "200.00"
				case "orb_max_plan":
					return "300.00"
				default:
					return "0.00"
				}
			}

			// Mock GetPlanInformation for current plan (using subscription ID)
			mockOrbClient.On("GetPlanInformation",
				mock.Anything,
				mock.Anything,
				mock.MatchedBy(func(subscriptionID *string) bool {
					return subscriptionID != nil && *subscriptionID == "orb_sub_1"
				}),
				(*string)(nil)).
				Return(&orb.OrbPlanInfo{
					ExternalPlanID:          tc.currentPlanID,
					MessagesPerSeat:         tc.currentMessagesPerSeat,
					PricePerSeat:            getPricePerSeat(tc.currentPlanID),
					SeatsPriceID:            "seats-price-id",
					IncludedMessagesPriceID: "included-messages-price-id",
				}, nil)

			// Mock GetPlanInformation for target plan (using plan ID)
			mockOrbClient.On("GetPlanInformation",
				mock.Anything,
				mock.Anything,
				(*string)(nil),
				mock.MatchedBy(func(planID *string) bool {
					return planID != nil && *planID == tc.targetPlanID
				})).
				Return(&orb.OrbPlanInfo{
					ExternalPlanID:          tc.targetPlanID,
					MessagesPerSeat:         tc.targetMessagesPerSeat,
					PricePerSeat:            getPricePerSeat(tc.targetPlanID),
					SeatsPriceID:            "seats-price-id",
					IncludedMessagesPriceID: "included-messages-price-id",
				}, nil)

			// Mock SetCustomerPlanType
			mockOrbClient.On("SetCustomerPlanType", mock.Anything, mock.Anything, mock.Anything).Return(nil)

			// Mock UpdateFixedQuantity for upgrades
			if tc.needsProrate {
				mockOrbClient.On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			}

			// Create UserTierManager
			manager := &UserTierManager{
				daoFactory:        daoFactory,
				featureFlagHandle: featureFlagHandle,
				orbConfig: &OrbConfig{
					Enabled:                true,
					Plans:                  orbPlanConfig,
					IncludedMessagesItemID: "included-messages-item-id",
				},
				orbClient:   mockOrbClient,
				auditLogger: audit.NewDefaultAuditLogger(),
			}

			user := &auth_entities.User{
				Id:                "user1",
				Email:             "<EMAIL>",
				OrbCustomerId:     "orb_cust_1",
				OrbSubscriptionId: "orb_sub_1",
				BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			}

			// Create user
			userDAO := manager.daoFactory.GetUserDAO()
			_, err := userDAO.Create(ctx, user)
			require.NoError(t, err)

			// Create test message
			msg := &auth_internal.UserTierChangeMessage{
				User:         user,
				NewTier:      auth_entities.UserTier_PROFESSIONAL,
				TierChangeId: fmt.Sprintf("tier-change-%s", user.Id),
				CurrentTenant: &tw_pb.Tenant{
					Name: "current_tenant",
				},
				PublishTime: timestamppb.Now(),
				NewPlanId:   tc.targetPlanID,
			}

			// Execute test
			err = manager.processOrbSubscription(ctx, msg)
			require.NoError(t, err)

			// Verify plan change call
			mockOrbClient.AssertNumberOfCalls(t, "SetCustomerPlanType", 1)
			mockOrbClient.AssertCalled(t, "SetCustomerPlanType", mock.Anything,
				mock.MatchedBy(func(planChange orb.OrbPlanChange) bool {
					// Verify basic plan change properties
					if planChange.NewPlanID != tc.targetPlanID ||
						planChange.PlanChangeType != tc.expectedPlanChangeType ||
						planChange.BillingCycleAlignment != tc.expectedBillingAlignment {
						return false
					}

					// Verify proration logic
					if tc.needsProrate {
						// Should have price overrides for upgrades
						if len(planChange.PriceOverrides) != 1 {
							return false
						}
						if planChange.PriceOverrides[0].PriceID != "included-messages-price-id" {
							return false
						}
						if planChange.PriceOverrides[0].Quantity != tc.expectedProRatedCredits {
							return false
						}
					} else {
						// Should not have price overrides for downgrades
						if len(planChange.PriceOverrides) != 0 {
							return false
						}
					}

					return true
				}), mock.Anything)

			// Verify UpdateFixedQuantity call for upgrades
			if tc.needsProrate {
				mockOrbClient.AssertNumberOfCalls(t, "UpdateFixedQuantity", 1)
				mockOrbClient.AssertCalled(t, "UpdateFixedQuantity", mock.Anything,
					mock.MatchedBy(func(update orb.OrbQuantityUpdate) bool {
						return update.OrbSubscriptionID == "orb_sub_1" &&
							update.PriceOverride.PriceID == "included-messages-price-id" &&
							update.PriceOverride.Quantity == tc.targetMessagesPerSeat &&
							update.UpdateTimeType == orb.PlanChangeEndOfTerm
					}), mock.Anything)
			} else {
				mockOrbClient.AssertNumberOfCalls(t, "UpdateFixedQuantity", 0)
			}
		})
	}
}

// setupTierChangeTestWithUser extends setupTierChangeTest by creating a user and tier manager
func setupTierChangeTestWithUser(t *testing.T, withTierChange bool) (
	context.Context,
	*DAOFactory,
	*UserTierManager,
	*auth_entities.User,
	func(),
) {
	ctx, daoFactory, tenantMap, mockOrbClient, cleanup := setupTierChangeTest(t)

	// Create feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test user
	userDAO := daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:               "test-user-id",
		Email:            "<EMAIL>",
		Tenants:          []string{oldTenant.Id},
		StripeCustomerId: "cus_test123",
	}

	// Add tier change info if requested
	if withTierChange {
		user.TierChange = &auth_entities.User_TierChangeInfo{
			Id:         "test-tier-change-id",
			TargetTier: auth_entities.UserTier_PROFESSIONAL,
			CreatedAt:  timestamppb.Now(),
			UpdatedAt:  timestamppb.Now(),
		}
	}

	createdUser, err := userDAO.Create(ctx, user)
	require.NoError(t, err)

	// Create user tenant mapping for the current tenant
	currentTenantMappingDAO := daoFactory.GetUserTenantMappingDAO(oldTenant.Name)
	currentMapping := currentTenantMappingDAO.Instantiate()
	currentMapping.Tenant = oldTenant.Name
	currentMapping.UserId = createdUser.Id
	_, err = currentTenantMappingDAO.Create(ctx, currentMapping)
	require.NoError(t, err, "Failed to create user tenant mapping")

	// Create tier manager
	tierManager := &UserTierManager{
		daoFactory:        daoFactory,
		featureFlagHandle: featureFlagHandle,
		tenantMap:         tenantMap,
		auditLogger:       audit.NewDefaultAuditLogger(),
		orbClient:         mockOrbClient,
		orbConfig: &OrbConfig{
			Enabled: true,
			Plans:   orbPlanConfig,
		},
	}

	return ctx, daoFactory, tierManager, createdUser, cleanup
}

// createTestMessage creates a standard test message for tier change tests
func createTestMessage(user *auth_entities.User, publishTime time.Time, tierChangeId string, newPlanID string) *auth_internal.UserTierChangeMessage {
	return &auth_internal.UserTierChangeMessage{
		User:          user,
		CurrentTenant: oldTenant,
		NewTenant:     newTenant,
		NewTier:       auth_entities.UserTier_PROFESSIONAL,
		NewPlanId:     newPlanID,
		TierChangeId:  tierChangeId,
		PublishTime:   timestamppb.New(publishTime),
	}
}

// validateTierChangeResult validates the expected state after a tier change
func validateTierChangeResult(t *testing.T, ctx context.Context, daoFactory *DAOFactory,
	tierManager *UserTierManager, createdUser *auth_entities.User, updatedUser *auth_entities.User,
) {
	// Verify the user's tenants list was updated to contain ONLY the new tenant
	assert.Equal(t, 1, len(updatedUser.Tenants), "User should be in exactly one tenant")
	assert.Equal(t, "new-tenant-id", updatedUser.Tenants[0], "User's tenants list should contain only the new tenant")

	// Verify tenant mapping exists only for the new tenant
	newTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("new-tenant")
	newTenantMapping, err := newTenantMappingDAO.GetByUser(ctx, createdUser.Id)
	require.NoError(t, err)
	assert.NotNil(t, newTenantMapping, "User should have mapping to new tenant")

	// Verify the user has been removed from the current tenant
	currentTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("current-tenant")
	currentTenantMapping, err := currentTenantMappingDAO.GetByUser(ctx, createdUser.Id)
	require.NoError(t, err)
	assert.Nil(t, currentTenantMapping, "User should not have mapping to current tenant")

	// Verify tokens have been deleted for the current tenant
	tokenHashDAO := daoFactory.GetTokenHashDAO()
	err = tokenHashDAO.FindAll(ctx,
		func(token *auth_entities.TokenHash) bool {
			assert.False(t,
				token.AugmentUserId == createdUser.Id && token.TenantId == "current-tenant-id",
				"User should not have any tokens for the old tenant")
			return true
		},
	)
	require.NoError(t, err)

	// Verify user nonce was updated (this is how we invalidate cookies)
	assert.True(t, updatedUser.Nonce > createdUser.Nonce,
		"User nonce should be incremented to invalidate cookies")

	// Verify tier change info is still present
	assert.NotNil(t, updatedUser.TierChange, "Tier change info should still be present")
	assert.Equal(t, "test-tier-change-id", updatedUser.TierChange.Id, "Tier change ID should match")
	assert.Equal(t, auth_entities.UserTier_PROFESSIONAL, updatedUser.TierChange.TargetTier, "Target tier should match")
}

// TestExecuteTierChange tests the happy path
func TestExecuteTierChange(t *testing.T) {
	t.Run(fmt.Sprintf("HappyPath"), func(t *testing.T) {
		ctx, daoFactory, tierManager, createdUser, cleanup := setupTierChangeTestWithUser(t, true)
		defer cleanup()

		// Set up mock orb client expectations
		// We need to mock the CreateSubscription call since the user doesn't have an OrbSubscriptionId
		tierManager.orbClient.(*orb.MockOrbClient).On("CreateSubscription", mock.Anything, mock.Anything, mock.Anything).
			Return("new-subscription-id", nil)

		// Create test message
		msg := createTestMessage(createdUser, time.Now(), "test-tier-change-id", "orb_developer_plan")

		err := tierManager.executeTierChange(ctx, msg)
		require.NoError(t, err, "Message processing should succeed")

		// Verify the user was updated correctly
		userDAO := daoFactory.GetUserDAO()
		updatedUser, err := userDAO.Get(ctx, createdUser.Id)
		require.NoError(t, err)

		validateTierChangeResult(t, ctx, daoFactory, tierManager, createdUser, updatedUser)

		// Verify the orb client was called
		tierManager.orbClient.(*orb.MockOrbClient).AssertCalled(t, "CreateSubscription", mock.Anything, mock.Anything, mock.Anything)
	})
}

// TestExecuteTierChangeFailpoints tests all panic points in the tier change process
func TestExecuteTierChangeFailpoints(t *testing.T) {
	// Define all panic points to test
	panicPoints := []string{
		UserTierChangePanicPoints.MoveUserToTenantError,
	}

	for _, panicPoint := range panicPoints {
		t.Run(fmt.Sprintf("PanicPoint_%s", panicPoint), func(t *testing.T) {
			ctx, daoFactory, tierManager, createdUser, cleanup := setupTierChangeTestWithUser(t, true)
			defer cleanup()

			// Set up mock orb client expectations
			// We need to mock the CreateSubscription call since the user doesn't have an OrbSubscriptionId
			tierManager.orbClient.(*orb.MockOrbClient).On("CreateSubscription", mock.Anything, mock.Anything, mock.Anything).
				Return("new-subscription-id", nil)

			// Create test message
			msg := createTestMessage(createdUser, time.Now(), "test-tier-change-id", "orb_developer_plan")

			// Enable the current panic point
			test_utils.EnablePanicPoint(panicPoint)

			require.Panics(t, func() {
				_ = tierManager.executeTierChange(ctx, msg)
			})

			// Now disable the panic point
			test_utils.DisablePanicPoint(panicPoint)

			err := tierManager.executeTierChange(ctx, msg)
			require.NoError(t, err, "Message processing should succeed after disabling panic point")

			// Verify the user was updated correctly
			userDAO := daoFactory.GetUserDAO()
			updatedUser, err := userDAO.Get(ctx, createdUser.Id)
			require.NoError(t, err)

			validateTierChangeResult(t, ctx, daoFactory, tierManager, createdUser, updatedUser)

			// Verify the orb client was called
			tierManager.orbClient.(*orb.MockOrbClient).AssertCalled(t, "CreateSubscription", mock.Anything, mock.Anything, mock.Anything)
		})
	}
}

// TestProcessTierChangeNilTierChange tests that processTierChange doesn't process messages
// when the user has no tier change info and respects the 5-minute timeout
func TestProcessTierChangeNilTierChange(t *testing.T) {
	ctx, daoFactory, tierManager, createdUser, cleanup := setupTierChangeTestWithUser(t, false)
	defer cleanup()

	// Set up mock orb client expectations
	// We need to mock the CreateSubscription call since the user doesn't have an OrbSubscriptionId
	tierManager.orbClient.(*orb.MockOrbClient).On("CreateSubscription", mock.Anything, mock.Anything, mock.Anything).
		Return("new-subscription-id", nil)

	// Create processor with the tier manager
	processor := &UserTierChangeProcessor{
		daoFactory:  daoFactory,
		tierManager: tierManager,
	}

	// Test 1: Recent message (less than 5 minutes old) with nil tier change should return error
	msg := createTestMessage(createdUser, time.Now(), "test-tier-change-id", "orb_developer_plan")
	err := processor.Process(ctx, msg)
	assert.Error(t, err, "Should return error for recent message with nil tier change")
	assert.Contains(t, err.Error(), "Nil tier change on user, retrying",
		"Error should indicate nil tier change")

	// Test 2: Old message (more than 5 minutes old) with nil tier change should be acknowledged
	oldMsg := createTestMessage(createdUser, time.Now().Add(-6*time.Minute), "test-tier-change-id", "orb_developer_plan")
	err = processor.Process(ctx, oldMsg)
	assert.NoError(t, err, "Should not return error for old message with nil tier change")

	// Test 3: Message with mismatched tier change ID should be acknowledged
	// First add a tier change to the user with a different ID
	userDAO := daoFactory.GetUserDAO()
	updateUser := func(u *auth_entities.User) bool {
		u.TierChange = &auth_entities.User_TierChangeInfo{
			Id:         "different-tier-change-id",
			TargetTier: auth_entities.UserTier_PROFESSIONAL,
			CreatedAt:  timestamppb.Now(),
			UpdatedAt:  timestamppb.Now(),
		}
		return true
	}

	_, err = userDAO.TryUpdate(ctx, createdUser.Id, updateUser, DefaultRetry)
	require.NoError(t, err)

	// Message with different tier change ID
	mismatchMsg := createTestMessage(createdUser, time.Now().Add(-6*time.Minute), "test-tier-change-id", "orb_developer_plan")
	err = processor.Process(ctx, mismatchMsg)
	assert.NoError(t, err, "Should not return error for message with mismatched tier change ID")
}
