package main

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"time"

	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/integrations/orb"
	tenantutil "github.com/augmentcode/augment/services/tenant_watcher/util"
)

// UserBillingInfo represents billing information for a user independent of any protocol
type UserBillingInfo struct {
	UserID                   string
	Email                    string
	IsAdmin                  bool
	IsSelfServeTeam          bool
	OrbCustomerID            string
	OrbSubscriptionID        string
	StripeCustomerID         string
	StripeSubscriptionID     string
	BillingMethod            auth_entities.BillingMethod
	SubscriptionCreationInfo *auth_entities.User_SubscriptionCreationInfo
}

// GetUserBillingInfo retrieves user billing information based on the user ID and tenant ID.
// This function does not perform auth checks, so callers must ensure proper authorization.
// Returns an error that should be returned directly to the caller
func GetUserBillingInfo(
	ctx context.Context,
	userID string,
	tenantID string,
	daoFactory *DAOFactory,
	tenantMap *TenantMap,
) (*UserBillingInfo, error) {
	tenant, err := tenantMap.GetTenantByID(tenantID)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to fetch tenant: %s", tenantID)
		return nil, status.Error(codes.Internal, "Failed to fetch resource")
	}
	if tenant == nil {
		log.Error().Msgf("Tenant not found: %s", tenantID)
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Get the specific DAOs we need
	userDAO := daoFactory.GetUserDAO()
	tenantSubscriptionMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
	userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO(tenant.Name)

	// Get user information
	user, err := userDAO.Get(ctx, userID)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to fetch user: %s", userID)
		GetUserBillingInfoCounter.WithLabelValues("user_fetch_error").Inc()
		return nil, status.Error(codes.Internal, "Failed to fetch resource")
	}
	if user == nil {
		log.Error().Msgf("User not found: %s", userID)
		GetUserBillingInfoCounter.WithLabelValues("user_not_found").Inc()
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	billingInfo := &UserBillingInfo{
		UserID:  user.Id,
		Email:   user.Email,
		IsAdmin: false,
	}

	mapping, err := userTenantMappingDAO.GetByUser(ctx, userID)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to fetch user tenant mapping: %s", userID)
		GetUserBillingInfoCounter.WithLabelValues("user_tenant_mapping_fetch_error").Inc()
		return nil, status.Error(codes.Internal, "Failed to fetch resource")
	}
	if mapping == nil {
		log.Error().Msgf("User tenant mapping not found: %s", userID)
	} else {
		for _, role := range mapping.CustomerUiRoles {
			if role == auth_entities.CustomerUiRole_ADMIN {
				billingInfo.IsAdmin = true
				break
			}
		}
	}

	// TODO (bin): remove this code once legacy self-serve teams are migrated
	// Currently only the event ingestion calls GetUserBillingInfo for legacy teams, causing an
	// error since legacy self-serve teams do not have TenantSubscriptionMapping.
	if tenantutil.IsLegacySelfServeTeamTenant(tenant) {
		return billingInfo, nil
	}

	// If the tenant is a self serve team, return the Orb customer id of the team
	// Otherwise, return the Orb customer id of the user
	if tenantutil.IsSelfServeTeamTenant(tenant) {
		tenantSubscriptionMapping, err := tenantSubscriptionMappingDAO.Get(ctx, tenantID)
		if err != nil {
			GetUserBillingInfoCounter.WithLabelValues("tenant_subscription_mapping_fetch_error").Inc()
			log.Error().Err(err).Msgf("Failed to fetch tenantSubscriptionMapping: %s", tenantID)
			return nil, status.Error(codes.Internal, "Failed to fetch resource")
		}
		if tenantSubscriptionMapping == nil {
			log.Error().Msgf("tenantSubscriptionMapping not found: %s", tenantID)
			GetUserBillingInfoCounter.WithLabelValues("tenant_subscription_mapping_not_found").Inc()
			return nil, status.Error(codes.NotFound, "Resource not found")
		}
		billingInfo.IsSelfServeTeam = true
		billingInfo.OrbCustomerID = tenantSubscriptionMapping.OrbCustomerId
		billingInfo.OrbSubscriptionID = tenantSubscriptionMapping.OrbSubscriptionId
		billingInfo.StripeCustomerID = tenantSubscriptionMapping.StripeCustomerId
		billingInfo.StripeSubscriptionID = tenantSubscriptionMapping.StripeSubscriptionId
		billingInfo.BillingMethod = tenantSubscriptionMapping.BillingMethod
		billingInfo.SubscriptionCreationInfo = nil // team tenants do not have subscription creation info
	} else {
		billingInfo.IsSelfServeTeam = false
		billingInfo.OrbCustomerID = user.OrbCustomerId
		billingInfo.OrbSubscriptionID = user.OrbSubscriptionId
		billingInfo.StripeCustomerID = user.StripeCustomerId
		billingInfo.BillingMethod = user.BillingMethod
		billingInfo.SubscriptionCreationInfo = user.GetSubscriptionCreationInfo()
		if user.SubscriptionId != nil {
			billingInfo.StripeSubscriptionID = *user.SubscriptionId
		}
	}

	GetUserBillingInfoCounter.WithLabelValues("OK").Inc()
	return billingInfo, nil
}

// CalculateProRatedCredits calculates the pro-rated credits you should get for the current month
// based on the time left in the billing period, when changing from one set amount of credits per month to another set amount
// This is the TOTAL number of credits you should expect for the current billing period.
//
// Parameters:
// - initialCreditsPerMonth: number of credits per month where you are coming from
// - targetCreditsPerMonth: number of credits per month where you are going
// - billingStart: start date of the current billing period
// - billingEnd: end date of the current billing period
// - upgradeTime: time when the upgrade is happening
//
// Returns:
// - proRatedCredits: the calculated pro-rated credits
// - error: if the number of credits where you are going is fewer than the number of credits where you are coming from
func CalculateProRatedCredits(
	initialCreditsPerMonth float64,
	targetCreditsPerMonth float64,
	billingStart time.Time,
	billingEnd time.Time,
	upgradeTime time.Time,
) (float64, error) {
	// Convert all times to UTC
	upgradeTime = upgradeTime.UTC()
	billingStart = billingStart.UTC()
	billingEnd = billingEnd.UTC()

	// Calculate percentage of billing period remaining rounding to days
	upgradeTimeDay := time.Date(upgradeTime.Year(), upgradeTime.Month(), upgradeTime.Day(), 0, 0, 0, 0, time.UTC)
	billingStartDay := time.Date(billingStart.Year(), billingStart.Month(), billingStart.Day(), 0, 0, 0, 0, time.UTC)
	billingEndDay := time.Date(billingEnd.Year(), billingEnd.Month(), billingEnd.Day(), 0, 0, 0, 0, time.UTC)
	hoursInBillingPeriod := billingEndDay.Sub(billingStartDay).Hours()
	hoursRemaining := billingEndDay.Sub(upgradeTimeDay).Hours()
	percentLeftInBillingPeriod := hoursRemaining / hoursInBillingPeriod

	// Clamp to 0-100%
	percentLeftInBillingPeriod = math.Max(0, math.Min(1, percentLeftInBillingPeriod)) * 100

	// Calculate the difference in monthly credits between target and initial plans
	// Assert target is less than initial
	if targetCreditsPerMonth < initialCreditsPerMonth {
		return 0, fmt.Errorf("target plan credits (%f) must be greater than or equal to initial plan credits (%f)",
			targetCreditsPerMonth, initialCreditsPerMonth)
	}
	additionalCreditsPerMonth := targetCreditsPerMonth - initialCreditsPerMonth

	// Calculate pro-rated credits
	proRatedCredits := (additionalCreditsPerMonth * percentLeftInBillingPeriod / 100) + initialCreditsPerMonth

	// Take the ceiling to ensure we always round up
	proRatedCredits = math.Ceil(proRatedCredits)

	return proRatedCredits, nil
}

// IsPlanUpgrade determines if changing from currentPlanInfo to targetPlanInfo is an upgrade
// Returns true for upgrade (immediate change), false for downgrade (end-of-billing-period change)
// We're not checking messages per seat, since negative credits could arise when price increases but message count decreases.
// For current plans, we only check the price to avoid potential issues.
func IsPlanUpgrade(ctx context.Context, currentPlanInfo *orb.OrbPlanInfo, targetPlanInfo *orb.OrbPlanInfo) (bool, error) {
	if currentPlanInfo == nil || targetPlanInfo == nil {
		return false, fmt.Errorf("current plan info or target plan info is nil")
	}

	// Compare seats price
	if currentPlanInfo.SeatsPriceID != "" && targetPlanInfo.SeatsPriceID != "" {
		currentSeatsPrice, err := strconv.ParseFloat(currentPlanInfo.PricePerSeat, 64)
		if err != nil {
			return false, fmt.Errorf("failed to parse current plan seats price %s: %w", currentPlanInfo.PricePerSeat, err)
		}

		targetSeatsPrice, err := strconv.ParseFloat(targetPlanInfo.PricePerSeat, 64)
		if err != nil {
			return false, fmt.Errorf("failed to parse target plan seats price %s: %w", targetPlanInfo.PricePerSeat, err)
		}

		if targetSeatsPrice > currentSeatsPrice {
			return true, nil
		}
	}

	return false, nil
}

// Get active team members for a team
func GetActiveTeamMemberCount(ctx context.Context, daoFactory *DAOFactory, tenantMap *TenantMap, tenantID string) (int, error) {
	tenant, err := tenantMap.GetTenantByID(tenantID)
	if err != nil {
		return 0, fmt.Errorf("failed to get tenant: %w", err)
	} else if tenant == nil {
		return 0, fmt.Errorf("tenant not found")
	}

	userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO(tenant.Name)
	count := 0
	err = userTenantMappingDAO.FindAll(ctx, func(mapping *auth_entities.UserTenantMapping) bool {
		count++
		return true
	})
	if err != nil {
		return 0, fmt.Errorf("failed to fetch team members: %w", err)
	}

	return count, nil
}

// Get all the invitations for a tenant with the given status.
func GetInvitationsForTenant(
	ctx context.Context, daoFactory *DAOFactory, tenantID string, status auth_entities.TenantInvitation_Status,
) ([]*auth_entities.TenantInvitation, error) {
	var invitations []*auth_entities.TenantInvitation
	invitationDAO := daoFactory.GetTenantInvitationDAO(tenantID)

	err := invitationDAO.FindAll(ctx, func(invitation *auth_entities.TenantInvitation) bool {
		if invitation.Status == status {
			invitations = append(invitations, invitation)
		}
		return true
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get invitations: %w", err)
	}

	return invitations, nil
}
