load("//tools/bzl:go.bzl", "go_binary", "go_oci_image")
load("//tools/bzl:kubecfg.bzl", "kubecfg")

go_binary(
    name = "fix_stripe_subs_job",
    srcs = [
        "config.go",
        "job.go",
        "main.go",
    ],
    visibility = ["//services/auth/central:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_dao",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/tenant_watcher/util:go_lib",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_github_stripe_stripe_go_v80//:stripe-go",
        "@com_github_stripe_stripe_go_v80//refund",
        "@com_github_stripe_stripe_go_v80//subscription",
        "@com_google_cloud_go_bigtable//:bigtable",
        "@com_google_cloud_go_storage//:storage",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//credentials:go_default_library",
        "@org_golang_google_grpc//credentials/insecure:go_default_library",
        "@org_golang_google_protobuf//proto",
    ],
)

go_oci_image(
    name = "fix_stripe_subs_job_image",
    package_name = package_name(),
    binary = ":fix_stripe_subs_job",
    visibility = ["//services/auth/central:__subpackages__"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":fix_stripe_subs_job_image",
        "//services/auth/central/server:column_family.json",
    ],
    visibility = [
        "//services/auth/central:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
    ],
)
