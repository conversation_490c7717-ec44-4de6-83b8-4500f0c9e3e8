load("//tools/bzl:go.bzl", "go_binary", "go_oci_image")
load("//tools/bzl:kubecfg.bzl", "kubecfg")

go_binary(
    name = "stripe_id_cleanup_job",
    srcs = [
        "config.go",
        "job.go",
        "main.go",
    ],
    visibility = ["//services/auth/central:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_dao",
        "//services/auth/central/server:auth_entities_go_proto",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigtable//:bigtable",
        "@org_golang_google_protobuf//proto",
    ],
)

go_oci_image(
    name = "stripe_id_cleanup_job_image",
    package_name = package_name(),
    binary = ":stripe_id_cleanup_job",
    visibility = ["//services/auth/central:__subpackages__"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":stripe_id_cleanup_job_image",
        "//services/auth/central/server:column_family.json",
    ],
    visibility = [
        "//services/auth/central:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/lib/pubsub:pubsub-lib",
    ],
)
