package main

import (
	"context"
	"flag"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	bigtable "cloud.google.com/go/bigtable"
	auth_dao "github.com/augmentcode/augment/services/auth/central/server/auth_dao"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// UserDAO is an alias for the auth_dao.UserDAO type
type UserDAO = auth_dao.UserDAO

// NewUserDAO creates a new UserDAO
func NewUserDAO(table *bigtable.Table) *UserDAO {
	return auth_dao.NewUserDAO(table)
}

func main() {
	// Configure logging
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stdout, TimeFormat: time.RFC3339})

	// Parse command line flags
	dryRunFlag := flag.Bool("dry-run", false, "When true, run in dry-run mode (no changes); overrides config file")
	dryRunFlagSet := false
	flag.Visit(func(f *flag.Flag) {
		if f.Name == "dry-run" {
			dryRunFlagSet = true
		}
	})
	tenantsStr := flag.String("tenants", "", "Comma-separated list of tenant IDs to process (overrides config file)")
	configFile := flag.String("config-file", "", "Path to the configuration file")
	flag.Parse()

	// Set up context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle signals for graceful shutdown
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		sig := <-signalChan
		log.Info().Str("signal", sig.String()).Msg("Received signal, shutting down")
		cancel()
	}()

	// Load configuration from file
	if *configFile == "" {
		log.Error().Msg("Config file path is required. Use --config-file flag")
		os.Exit(1)
	}

	// Read the configuration
	config, err := ReadConfig(*configFile)
	if err != nil {
		log.Error().Err(err).Msg("Failed to read configuration")
		os.Exit(1)
	}

	// Override tenants from command line if provided
	var tenants []string
	if *tenantsStr != "" {
		// Split tenants string into slice
		tenants = strings.Split(*tenantsStr, ",")
		for i, tenant := range tenants {
			tenants[i] = strings.TrimSpace(tenant)
		}
		log.Info().Strs("tenants", tenants).Msg("Using tenant IDs from command line (overriding config)")
	} else {
		// Use tenants from config
		tenants = config.Tenants
		log.Info().Strs("tenants", tenants).Msg("Using tenant IDs from config file")
	}

	// Check if tenant IDs were provided
	if len(tenants) == 0 {
		log.Warn().Msg("No tenant IDs specified, job will not process any users")
	}

	// Determine dry run mode - command line flag overrides config file
	dryRun := config.DryRun
	if dryRunFlagSet {
		dryRun = *dryRunFlag
		log.Info().Bool("dry_run", dryRun).Msg("Using dry run setting from command line (overriding config)")
	} else {
		log.Info().Bool("dry_run", dryRun).Msg("Using dry run setting from config file")
	}

	log.Info().
		Bool("dry_run", dryRun).
		Strs("tenants", tenants).
		Msg("Stripe ID cleanup job configuration")

	// Setup BigTable connection
	log.Info().Msg("Setting up BigTable connection")

	// Create BigTable client
	client, err := bigtable.NewClient(ctx, config.BigTable.ProjectID, config.BigTable.InstanceID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create BigTable client")
		os.Exit(1)
	}
	defer client.Close()

	// Open the table
	bigtableTable := client.Open(config.BigTable.TableName)

	// Create UserDAO with the BigTable table
	log.Info().Msg("Creating UserDAO")
	userDAO := NewUserDAO(bigtableTable)

	// Run the job
	err = stripeIDCleanup(ctx, userDAO, tenants, dryRun)
	if err != nil {
		os.Exit(1)
	}

	// Close the BigTable client before exiting
	client.Close()
}
