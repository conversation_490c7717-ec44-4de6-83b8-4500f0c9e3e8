// K8S deployment file for the auth query service
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local tenants = import 'deploy/tenants/tenants_lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';

function(env, namespace, cloud, namespace_config)
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName='auth-query');
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(cloud=cloud, env=env, namespace=namespace, appName='auth-query');

  local serviceAccount = gcpLib.createServiceAccount(app='auth-query', cloud=cloud, env=env, namespace=namespace, iam=true);

  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local services = grpcLib.grpcService(appName='auth-query', namespace=namespace);
  local clientCentralCert = certLib.createCentralClientCert(
    name='auth-query-central-client-certificate',
    env=env,
    namespace=namespace,
    appName='auth-query',
    dnsNames=grpcLib.grpcServiceNames('auth-query', namespace=namespace),
  );
  local serverCert = certLib.createServerCert(name='auth-query-server-certificate',
                                              namespace=namespace,
                                              appName='auth-query',
                                              dnsNames=grpcLib.grpcServiceNames('auth-query'),
                                              volumeName='certs');
  local clientCert = certLib.createClientCert(name='auth-query-client-certificate',
                                              namespace=namespace,
                                              appName='auth-query');

  // Create secret for hardcoded tokens.
  local validateToken(tok) =
    assert tok.tenant_name != null : 'Empty tenant name for token in namespace %s' % namespace;
    local relevantTenants = tenants.relevantTenants(env, namespace, cloud, namespace_config);
    local found = [ten for ten in relevantTenants if ten.name == tok.tenant_name];
    assert std.length(found) == 1 : 'Did not find exactly one tenant with name %s' % tok.tenant_name;
    assert found[0].namespace == namespace : 'Token for tenant %s does not match namespace %s' % [tok.tenant_name, namespace];
    tok;
  local tokens = if cloudInfo.isKubecfgTestNamespace(namespace) then [] else std.map(validateToken, namespace_config.api_tokens);
  local health_check_tokens = (import 'deploy/tenants/tokens/health-check-allow.jsonnet')(namespace);

  local apiTokenSecret =
    {
      apiVersion: 'v1',
      kind: 'Secret',
      metadata: {
        name: 'auth-query-secret',
        namespace: namespace,
        labels: {
          app: 'auth-query',
        },
      },
      type: 'Opaque',
      data: {
        'secrets.json': std.base64(std.manifestJson({
          api_tokens: health_check_tokens + tokens,
        })),
      },
    };
  local tokenExchangeEndpoint = endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud);
  local tenantWatcherEndpoint = endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud);
  local headless = env != 'PROD';
  local authCentralEndpoint = endpointsLib.getAuthCentralGrpcUrl(env=env, namespace=namespace, cloud=cloud, headless=headless);
  local authCentralPort = 50051;
  local authCentralUrl = '%s:%s' % [authCentralEndpoint, authCentralPort];
  local config =
    {
      bind_address: '0.0.0.0:50051',
      feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
      dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
      namespace: namespace,
      metrics_server_bind_address: '0.0.0.0',
      metrics_server_port: 9090,
      client_mtls: mtls,
      client_ca_path: '/central-client-certs/ca.crt',
      client_key_path: '/central-client-certs/tls.key',
      client_cert_path: '/central-client-certs/tls.crt',
      server_mtls: mtls,
      server_ca_path: '/certs/ca.crt',
      server_key_path: '/certs/tls.key',
      server_cert_path: '/certs/tls.crt',
      token: {
        endpoint: tokenExchangeEndpoint,
        request_timeout_secs: 10,
      },
      tenant_watcher_endpoint: tenantWatcherEndpoint,
      auth_central_endpoint: authCentralUrl,
      token_info_cache_ttl_secs: namespace_config.flags.authQueryTokenCacheTtlSecs,
      migrated_tenant_ids: namespace_config.flags.migratedTenantIds,
      // This helps us bound the number of requests that will queue in auth query, which helps
      // us avoid OOM.
      //
      // The bound is the number of incoming connections to auth query * max_concurrent_streams.
      max_concurrent_streams: 100,
      // Disconnect clients after 10 minutes so they discover other auth query replicas
      max_connection_age_secs: 600,
    };
  local configMap = configMapLib.createConfigMap(appName='auth-query', namespace=namespace, config=config);
  local roles = [];
  local container =
    {
      name: 'auth-query',
      target: {
        name: '//services/auth/query/server:image',
        dst: 'auth_query',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      args: [
        '--config-file',
        configMap.filename,
        '--secrets-file',
        '/secrets/secrets.json',
      ],
      env: telemetryLib.telemetryEnv('auth-query', telemetryLib.collectorUri(env, namespace, cloud)) + [
        {
          name: 'RUST_BACKTRACE',
          value: '1',
        },
      ] + dynamicFeatureFlags.env,
      volumeMounts: [
        configMap.volumeMountDef,
        {
          name: 'secrets',
          mountPath: '/secrets/',
          readOnly: true,
        },
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
        clientCentralCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
      ],
      readinessProbe: grpcLib.grpcHealthCheck('auth-query-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('auth-query-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 1,
          memory: '512Mi',
        },
      },
    };
  local pod =
    {
      priorityClassName: cloudInfo.envToPriorityClass(env),
      serviceAccountName: serviceAccount.name,
      tolerations: tolerations,
      affinity: affinity,
      containers: [
        container,
      ],
      volumes: [
        {
          name: 'secrets',
          secret: {
            secretName: apiTokenSecret.metadata.name,  // pragma: allowlist secret
            optional: false,
          },
        },
        configMap.podVolumeDef,
        serverCert.podVolumeDef,
        clientCert.podVolumeDef,
        clientCentralCert.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
      ],
    };
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'auth-query',
      namespace: namespace,
      labels: {
        app: 'auth-query',
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: if env == 'DEV' then 1 else 2,
      minReadySeconds: if env == 'DEV' then 0 else 60,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: 'auth-query',
        },
      },
      template: {
        metadata: {
          labels: {
            app: 'auth-query',
          },
        },
        spec: pod,
      },
    },
  };
  local pbd = nodeLib.podDisruption(appName='auth-query', namespace=namespace, env=env);
  lib.flatten([
    configMap.objects,
    dynamicFeatureFlags.k8s_objects,
    clientCentralCert.objects,
    serverCert.objects,
    clientCert.objects,
    deployment,
    roles,
    services,
    pbd,
    apiTokenSecret,
    serviceAccount.objects,
  ])
