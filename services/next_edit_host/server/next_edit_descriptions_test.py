from unittest.mock import MagicMock
import uuid

import pytest

from base.diff_utils.diff_utils import File
from base.prompt_format_next_edit.description_prompt_formatter import (
    RavenDescribePromptFormatter,
)
from base.python.opentelemetry_utils.traced_threadpool import TracedThread<PERSON>oolExecutor
from services.inference_host import infer_pb2_grpc
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.next_edit_host import next_edit_pb2
from services.next_edit_host.server.handler import ScoredFileHunk
from services.next_edit_host.server.next_edit_descriptions import (
    DescriptionGenerationConfig,
    DescriptionHeuristicConfig,
    DescriptionStyle,
    create_next_edit_description_service,
)
from services.next_edit_host.server.next_edit_handler import (
    <PERSON><PERSON><PERSON>ara<PERSON>,
    _split_changes_into_hunks,
)
from services.next_edit_host.server.next_edit_request_insight_builder import (
    SuggestionEventBuilder,
)
from services.next_edit_host.server.next_edit_types import NextEditRequest


# I need a description service mock here
# We should make a class called a description test kit
# We can call the service to generate descriptions with a mock model
class DescriptionTestKit:
    """A test kit for the next edit descriptions."""

    def __init__(
        self,
        generation_config: DescriptionGenerationConfig,
        heuristic_config: DescriptionHeuristicConfig,
    ):
        self.generation_config = generation_config
        self.heuristic_config = heuristic_config
        self.description_gen_stub_factory = MagicMock(infer_pb2_grpc.InfererStub)
        self.description_gen_client = MagicMock(InfererClient)
        self.description_service = create_next_edit_description_service(
            generation_config=generation_config,
            heuristic_config=heuristic_config,
            description_gen_client=self.description_gen_client,
        )
        # point the service inference client to the one we control
        self.description_service.description_gen_client = self.description_gen_client

    def add_mock_description(
        self,
        description: str,
    ):
        """Provide mock inference results for description generation.
        Args:
            output: the description to tokenize.
        """
        self.description_gen_client.infer.return_value = (
            self._create_edit_description_reply(description)
        )

    def _create_edit_description_reply(
        self,
        description: str,
    ):
        tokenizer = self.description_service.chat_prompt_formatter.tokenizer

        tokens = tokenizer.tokenize_safe(description)
        tokens.append(tokenizer.special_tokens.eos)

        return InferenceClientProtocol.Reply(tokens, [0.0] * len(tokens))

    def get_description(
        self, file: File, edit_suggestion: ScoredFileHunk
    ) -> tuple[str, str]:
        auth_info = AuthInfo(
            tenant_id="1234567890",
            tenant_name="test-tenant",
            shard_namespace="test-shard",
            cloud="test-cloud",
        )
        request_id = uuid.uuid4()
        session_id = uuid.uuid4()
        request_context = RequestContext(
            request_id=str(request_id),
            request_session_id=str(session_id),
            request_source="unknown",
        )
        request = next_edit_pb2.NextEditRequest(model_name="test_model")
        return self.description_service.get_description_for_edit(
            edit_file=file,
            edit_suggestion=edit_suggestion,
            edit_suggestion_id="id",
            request=NextEditRequest(
                proto=request,
                context=request_context,
                auth_info=auth_info,
            ),
            ri_builder=MagicMock(SuggestionEventBuilder),
        )


test_config = DescriptionHeuristicConfig(
    delete_start="Delete: ",
    add_start="Add: ",
    mod_start="Change: ",
    whitespace_change_description="Fix whitespace",
    mod_middle=" \u279c ",
)


@pytest.fixture
def description_test_kit() -> DescriptionTestKit:
    return DescriptionTestKit(
        generation_config=DescriptionGenerationConfig(
            tokenizer_name="llama3_instruct",
            chat_prompt_formatter_name="llama3",
            prompt_formatter_name="raven_describe",
            prompt_formatter_config=RavenDescribePromptFormatter.Config(
                max_prompt_tokens=2048,
                diff_context_lines=5,
                use_descriptions_v2_model=False,
            ),
            sampling_params=SamplingParams(
                top_k=0,
                top_p=0.0,
                temperature=0.0,
                seed=0,
                inference_timeout_s=30.0,
            ),
            max_output_length=128,
        ),
        heuristic_config=test_config,
    )


def test_add_descriptions(description_test_kit: DescriptionTestKit):
    add_examples = [
        # Test adding a line
        {
            "name": "test adding a line",
            "before": "just a single line",
            "after": "just a single line\na much better line",
            "expected_description": ["Add: a much better line"],
            "expected_markdown_description": ["Add: `a much better line`"],
        },
        {
            "name": "test adding a long line",
            "before": "just a single line",
            "after": "just a single line\nan even better line because it is way longer than the previous tiny little line",
            "expected_description": [
                "Add: an even better line because it is way longer than the previous tiny little line",
                DescriptionStyle.NO_STYLE,
            ],
            "expected_markdown_description": [
                "Add: `an even better line because it is way longer than the previous tiny little line`",
                DescriptionStyle.MARKDOWN,
            ],
        },
        # Test adding two new lines (longer than char_limit)
        {
            "name": "test adding two new lines (longer than char_limit)",
            "before": "just a single line",
            "after": """just a single line
a much better line
an even better line
""",
            "expected_description": [None],
            "expected_markdown_description": [None],
        },
        {
            "name": "test adding two new lines (shorter than char_limit)",
            "before": "just a single line",
            "after": """just a single line
l1
l2
""",
            "expected_description": [None],
            "expected_markdown_description": [None],
        },
        {
            "name": "test adding single character",
            "before": "just a single line",
            "after": """just a single line1""",
            "expected_description": ["Change: line \u279c line1"],
            "expected_markdown_description": ["Change: `line` \u279c `line1`"],
        },
        {
            "name": "test adding word at beginning and end",
            "before": "line",
            "after": """beg line end""",
            "expected_description": ["Change: line \u279c beg line end"],
            "expected_markdown_description": ["Change: `line` \u279c `beg line end`"],
        },
        {
            "name": "test adding changing to list",
            "before": "item",
            "after": "[items]",
            "expected_description": ["Change: item \u279c [items]"],
            "expected_markdown_description": ["Change: `item` \u279c `[items]`"],
        },
        {
            "name": "test adding some words (space separated)",
            "before": "just a single line",
            "after": """just a single line with a word""",
            "expected_description": ["Add: with a word"],
            "expected_markdown_description": ["Add: `with a word`"],
        },
        {
            "name": "test adding inside underscore",
            "before": "long_variable_name",
            "after": "long_variableton_name",
            "expected_description": ["Change: variable \u279c variableton"],
            "expected_markdown_description": [
                "Change: `variable` \u279c `variableton`"
            ],
        },
        {
            "name": "test adding between underscores",
            "before": "long_variable_name",
            "after": "long_variable_with_name",
            "expected_description": ["Add: with_"],
            "expected_markdown_description": ["Add: `with_`"],
        },
        # test adding a empty line
        {
            "name": "test adding a empty line",
            "before": """\
""",
            "after": """\

""",
            "expected_description": ["Fix whitespace"],
            "expected_markdown_description": ["Fix whitespace"],
        },
        # test adding bunch of whitespace
        {
            "name": "test adding bunch of whitespace",
            "before": """\
we start here we end here
""",
            "after": """\
                we start here               we end here

""",
            "expected_description": ["Fix whitespace"],
            "expected_markdown_description": ["Fix whitespace"],
        },
    ]

    for example in add_examples:
        result = ScoredFileHunk(
            path="",
            blob_name="",
            char_start=0,
            char_end=len(example["before"]),
            localization_score=0.0,
            editing_score=0.0,
            truncation_char=None,
            existing_code=example["before"],
            suggested_code=example["after"],
            diff_spans=tuple(),
        )

        for j, split_result in enumerate(
            _split_changes_into_hunks(result, n_context_lines=3)
        ):
            no_style_description, markdown_description = (
                description_test_kit.description_service._get_heuristic_descriptions(
                    split_result
                )
            )
            assert no_style_description == example["expected_description"][j]
            assert markdown_description == example["expected_markdown_description"][j]


def test_delete_descriptions(description_test_kit: DescriptionTestKit):
    delete_examples = [
        # test deleting new line
        {
            "name": "test deleting new line",
            "before": """\

""",
            "after": """\
""",
            "expected_description": ["Fix whitespace"],
            "expected_markdown_description": ["Fix whitespace"],
        },
        # test random whitespace deletions
        {
            "name": "test random whitespace deletions",
            "before": """\

                once we               had whitespace          everywhere


""",
            "after": """\
once we had whitespace everywhere
""",
            "expected_description": ["Fix whitespace"],
            "expected_markdown_description": ["Fix whitespace"],
        },
        # Test two deletions on same line
        {
            "name": "Test two deletions on same line",
            "before": """\
    11 line 11
""",
            "after": """\
    1 line 1
""",
            "expected_description": ["Change: 11 line 11 \u279c 1 line 1"],
            "expected_markdown_description": ["Change: `11 line 11` \u279c `1 line 1`"],
        },
        # Test deletion on two adjacent lines
        {
            "name": "Test deletion on two adjacent lines",
            "before": """\
    line 11
    line 22
""",
            "after": """\
    line 1
    line 2
""",
            "expected_description": [None],
            "expected_markdown_description": [None],
        },
        # Test deletion of single character
        {
            "name": "Test deletion of single character",
            "before": "Trying to delete a single character: G",
            "after": "Trying to delete a single character: ",
            "expected_description": ["Delete: G"],
            "expected_markdown_description": ["Delete: `G`"],
        },
        # Test deletion of single long line
        {
            "name": "Test deletion of single long line",
            "before": "super long line that is definitley more than whatever we want the limit to be when we set the limit to be something\n",
            "after": "",
            "expected_description": [
                "Delete: super long line that is definitley more than whatever we want the limit to be when we set the limit to be something",
                DescriptionStyle.NO_STYLE,
            ],
            "expected_markdown_description": [
                "Delete: `super long line that is definitley more than whatever we want the limit to be when we set the limit to be something`",
                DescriptionStyle.MARKDOWN,
            ],
        },
        # Test multiple lines to delete
        {
            "name": "Test multiple lines to delete",
            "before": """\
    line 1
    line 2
    line 3
    line 4
    line 5
    line 6
""",
            "after": "",
            "expected_description": ["Delete 6 lines"],
            "expected_markdown_description": ["Delete 6 lines"],
        },
        # Test deleting spaces around equal sign
        {
            "name": "test deleting spaces around equal sign",
            "before": "request, auth_info, i, has_change = change_suggested",
            "after": "request, auth_info, i, has_change=change_suggested",
            "expected_description": ["Fix whitespace"],
            "expected_markdown_description": ["Fix whitespace"],
        },
    ]

    for example in delete_examples:
        result = ScoredFileHunk(
            path="",
            blob_name="",
            char_start=0,
            char_end=len(example["before"]),
            localization_score=0.0,
            editing_score=0.0,
            truncation_char=None,
            existing_code=example["before"],
            suggested_code=example["after"],
            diff_spans=tuple(),
        )

        for j, split_result in enumerate(
            _split_changes_into_hunks(result, n_context_lines=3)
        ):
            no_style_description, markdown_description = (
                description_test_kit.description_service._get_heuristic_descriptions(
                    split_result
                )
            )
            assert no_style_description == example["expected_description"][j]
            assert markdown_description == example["expected_markdown_description"][j]


def test_mod_descriptions(description_test_kit: DescriptionTestKit):
    mod_examples = [
        # Test modifying character
        {
            "name": "test modifying character",
            "before": "]",
            "after": "[",
            "expected_description": ["Change: ] \u279c ["],
            "expected_markdown_description": ["Change: `]` \u279c `[`"],
        },
        {
            "name": "test modifying from spaces to no spaces",
            "before": "normal s with s",
            "after": "normal swiths",
            "expected_description": ["Fix whitespace"],
            "expected_markdown_description": ["Fix whitespace"],
        },
        {
            "name": "test modifying away underscores",
            "before": "n_snake_case",
            "after": "nsnakecase",
            "expected_description": ["Change: _snake_ \u279c snake"],
            "expected_markdown_description": ["Change: `_snake_` \u279c `snake`"],
        },
        {
            "name": "test snake to camel case",
            "before": "n_snake_case",
            "after": "NSnakeCaseS",
            "expected_description": ["Change: n_snake_case \u279c NSnakeCaseS"],
            "expected_markdown_description": [
                "Change: `n_snake_case` \u279c `NSnakeCaseS`"
            ],
        },
    ]

    for example in mod_examples:
        result = ScoredFileHunk(
            path="",
            blob_name="",
            char_start=0,
            char_end=len(example["before"]),
            localization_score=0.0,
            editing_score=0.0,
            truncation_char=None,
            existing_code=example["before"],
            suggested_code=example["after"],
            diff_spans=tuple(),
        )

        for j, split_result in enumerate(
            _split_changes_into_hunks(result, n_context_lines=3)
        ):
            no_style_description, markdown_description = (
                description_test_kit.description_service._get_heuristic_descriptions(
                    split_result
                )
            )
            assert no_style_description == example["expected_description"][j]
            assert markdown_description == example["expected_markdown_description"][j]


def test_combined_description(description_test_kit: DescriptionTestKit):
    combined_examples = [
        # Test deleting line and then adding a line less than n_context_lines away
        {
            "name": "Test deleting line and then adding a line less than n_context_lines away",
            "before": """\
    line 1
    line 2
    line 3
""",
            "after": """\
    line 2
    line 3
    line 4
""",
            "expected_description": [None],
            "expected_markdown_description": [None],
        },
        # Test deleting line and then adding a line more than n_context_lines away
        {
            "name": "Test deleting line and then adding a line more than n_context_lines away",
            "before": """\
    line 1
    line 2
    line 3
    line 4
    line 5
    line 6
""",
            "after": """\
    line 2
    line 3
    line 4
    line 5
    line 6
    line 7
""",
            "expected_description": [
                "Delete: line 1",
                "",
                "Add: line 7",
            ],
            "expected_markdown_description": [
                "Delete: `line 1`",
                "",
                "Add: `line 7`",
            ],
        },
        # Test deleting first line, then modifying the second and third, and then adding line 7
        {
            "name": "Test deleting first line, then modifying the second and third, and then adding line 7",
            "before": """\
    line 1
    line 2
    line 3
    line 4
    line 5
    line 6
""",
            "after": """\
    pine 2
    pine 3
    line 4
    line 5
    line 6
    line 7
""",
            "expected_description": [
                None,
                "",
                "Add: line 7",
            ],
            "expected_markdown_description": [
                None,
                "",
                "Add: `line 7`",
            ],
        },
        # Test change within a word on a single line
        {
            "name": "Test change within a word on a single line",
            "before": "___________________",
            "after": "_+__+__+___________",
            "expected_description": [None],
            "expected_markdown_description": [None],
        },
        # test changing a line and then adding another line
        {
            "name": "test changing a line and then adding another line",
            "before": """\
    line 1
    line 2
    line 3
""",
            "after": """\
    line 1
    pine 2
    line 3
    line 4
""",
            "expected_description": [
                "",
                None,
                # "Change: l \u279c p, Add: line 4"
                # if len("Change: l \u279c p, Add: line 4") < test_config.char_limit
                # else None,
            ],
            "expected_markdown_description": [
                "",
                None,
                # "Change: l \u279c p, Add: line 4"
                # if len("Change: l \u279c p, Add: line 4") < test_config.char_limit
                # else None,
            ],
        },
    ]

    for example in combined_examples:
        result = ScoredFileHunk(
            path="src/example.py",
            blob_name="",
            char_start=0,
            char_end=len(example["before"]),
            localization_score=0.0,
            editing_score=0.0,
            truncation_char=None,
            existing_code=example["before"],
            suggested_code=example["after"],
            diff_spans=tuple(),
        )

        for j, split_result in enumerate(
            _split_changes_into_hunks(result, n_context_lines=3)
        ):
            no_style_description, markdown_description = (
                description_test_kit.description_service._get_heuristic_descriptions(
                    split_result
                )
            )
            assert no_style_description == example["expected_description"][j]
            assert markdown_description == example["expected_markdown_description"][j]


def test_no_change_has_empty_string_description(
    description_test_kit: DescriptionTestKit,
):
    """Test that there is no change when there is no description"""
    mock_file = File(path="path/to/file.py", contents="unchanged contents")

    edit_suggestion = ScoredFileHunk(
        path=mock_file.path,
        blob_name=mock_file.blob_name,
        char_start=0,
        char_end=len(mock_file.contents),
        localization_score=0.0,
        editing_score=0.0,
        truncation_char=None,
        existing_code=mock_file.contents,
        suggested_code=mock_file.contents,
        diff_spans=tuple(),
    )

    inference_responses = ["I'm the model, I should not describe non-changes!"]

    # we expect the description for a non-change to be the empty string
    expected_descriptions = [
        ("", ""),
    ]

    for i, mock_description in enumerate(inference_responses):
        description_test_kit.add_mock_description(mock_description)

        generated_description = description_test_kit.get_description(
            file=mock_file, edit_suggestion=edit_suggestion
        )

        assert generated_description == expected_descriptions[i]
