"""Tests for NextEditHandler."""

import dataclasses
import logging
import math
import uuid
from dataclasses import dataclass, replace
from pathlib import Path
from textwrap import dedent
from typing import Iterable
from unittest.mock import MagicMock

import pytest

from base import feature_flags
from base.blob_names.python.blob_names import get_blob_name
from base.diff_utils import edit_events_pb2
from base.diff_utils.changes import Modified, Unchanged
from base.diff_utils.diff_utils import File
from base.diff_utils.edit_events import GranularEditEvent, SquashableEdits
from base.logging.secret_logging import IgnoreSecretLogger, UnsafeLogger
from base.prompt_format_next_edit.gen_prompt_formatter import (
    encode_model_diff_output,
    equal_modulo_spaces,
)
from base.python.opentelemetry_utils.traced_threadpool import TracedThreadPoolExecutor
from base.ranges.line_map import LineMap
from base.ranges.range_types import <PERSON><PERSON><PERSON>ang<PERSON>, LineRange
from base.retrieval.chunking.smart_chunking import (
    SmartChunker,
    expand_point_by_smart_chunks,
)
from base.test_utils.synchronous_executor import SynchronousExecutor
from services.completion_host import completion_pb2
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.inference_host import infer_pb2_grpc
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.retriever import (
    FindMissingResult as RetrieverFindMissingResult,
)
from services.lib.retrieval.retriever import RetrievalChunk, RetrievalResult, Retriever
from services.next_edit_host import next_edit_pb2
from services.next_edit_host.server.handler import NextEditResult, ScoredFileHunk
from services.next_edit_host.server.next_edit_blob_state_validator import (
    NextEditBlobStateValidator,
)
from services.next_edit_host.server.next_edit_descriptions import (
    DescriptionHeuristicConfig,
)
from services.next_edit_host.server.next_edit_handler import (
    EditGenerationConfig,
    NextEditHandlerConfig,
    _post_process_hunk,
    _split_changes_into_hunks,
    create_next_edit_handler,
    from_proto,
)
from services.next_edit_host.server.next_edit_types import NextEditRequest
from services.next_edit_host.server.next_edit_utils import (
    NextEditCancelledException,
    RecentChangeTracker,
    SamplingParams,
)
from services.request_insight import request_insight_pb2
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from google.protobuf.timestamp_pb2 import Timestamp

local_feature_flags: feature_flags.LocalFeatureFlagSetter = (
    feature_flags.unit_test_setup()
)

test_logger = UnsafeLogger(logging.getLogger(__name__))


@dataclass
class EditGenExample:
    location: RetrievalChunk
    before: str
    after: str
    change_description: str = ""
    markdown_description: str = ""
    change_probability: float = 1.0


class HandlerTestKit:
    """A test kit for the next edit handler."""

    def __init__(
        self,
        config: NextEditHandlerConfig,
        api_version: int,
        model_name: str = "test-model",
    ):
        self.model_name = model_name
        self.api_version = api_version
        self.reset(config)

    def reset(self, config: NextEditHandlerConfig):
        # reset the mocks in the "reset" functions
        self.edit_gen_stub_factory = MagicMock(infer_pb2_grpc.InfererStub)
        self.description_gen_stub_factory = MagicMock(infer_pb2_grpc.InfererStub)
        self.edit_gen_client = MagicMock(InfererClient)
        self.location_edit_gen_client = MagicMock(InfererClient)
        self.description_gen_client = MagicMock(InfererClient)
        self.generation_retriever = MagicMock(Retriever)
        self.location_retriever = MagicMock(Retriever)
        self.ri_publisher = MagicMock(RequestInsightPublisher)
        self.content_manager_client = MagicMock(ContentManagerClient)
        self.handler = create_next_edit_handler(
            config=config,
            edit_gen_client=self.edit_gen_client,
            edit_gen_ranker_client=None,
            description_gen_client=self.description_gen_client,
            namespace="test_namespace",
            generation_retriever=self.generation_retriever,
            content_manager_client=self.content_manager_client,
            ri_publisher=self.ri_publisher,
            location_retriever=self.location_retriever,
            secret_logs_enabled=True,
        )
        self.handler.edit_gen_client = self.edit_gen_client
        self.handler.next_edit_locations_service.edit_gen_ranker_client = (
            self.location_edit_gen_client
        )  # we should probably split it for clarity.
        if self.handler.next_edit_description_service:
            self.handler.next_edit_description_service.description_gen_client = (
                self.description_gen_client
            )
        self.set_smart_chunk_size(config.max_smart_chunk_size)

    @property
    def config(self) -> NextEditHandlerConfig:
        return self.handler.config

    @property
    def prompt_formatter(self):
        return self.handler.edit_prompt_formatter

    def set_smart_chunk_size(self, size: int) -> None:
        self.handler.next_edit_locations_service.chunker.max_chunk_chars = size

    def add_mock_edit_gen(
        self,
        output: list[int] | tuple[str, str],
        probs: list[float] | None = None,
        force_change: bool = False,
        has_eos: bool = True,
    ):
        """Provide mock inference results for edit generation.
        Args:
            output: either the literal tokens to return as the output,
              or the before and after strings to form into a diff then tokenize.
            probs: Prefix of probabilities for output tokens. Will extend with 100% to
              match tokens length.
            force_change: If True, the output tokens will be forced to have a change token.
            has_eos: If True, the output tokens will have an EOS token appended.
        """
        if isinstance(output, tuple):
            before, after = output
            diff = "".join(encode_model_diff_output(before, after))
            tokens = self.prompt_formatter.tokenizer.tokenize_unsafe(diff)
            # add has changed token if not forced or before != after
            # add nothing if forced change (already added in handler)
            if force_change:
                pass
            elif before == after:
                tokens = [self.prompt_formatter.special_tokens.no_change] + tokens
            else:
                tokens = [self.prompt_formatter.special_tokens.has_change] + tokens
            if has_eos:
                tokens = tokens + [self.prompt_formatter.special_tokens.eos]
        else:
            tokens = output

        if probs is None:
            probs = [1 for _ in tokens]
        else:
            probs = probs + [1 for _ in range(len(tokens) - len(probs))]
        assert len(tokens) == len(probs)
        self.edit_gen_client.infer.return_value = InferenceClientProtocol.Reply(
            tokens, [math.log(prob) for prob in probs]
        )

        self.location_retriever.retrieve.return_value = RetrievalResult(
            retrieved_chunks=[],
            missing_blob_names=[],
            checkpoint_not_found=False,
        )

    def add_mock_edit_gen_stream(
        self,
        locations: list[RetrievalChunk],
        generated_examples: list[EditGenExample],
        location_edit_generated_examples: list[EditGenExample] = [],
        force_change: bool = False,
    ):
        """Provide mock inference results for edit generation.
        Args:
            locations: the locations to return.
            generated_examples: the before and after strings to form into a diff then tokenize.
            location_edit_generated_examples: the before and after strings to form into a diff then tokenize.
            force_change: If True, the output tokens will be forced to have a change token.
        """
        # The first returned location is the one at the cursor.
        self.location_retriever.retrieve.return_value = RetrievalResult(
            retrieved_chunks=locations,
            missing_blob_names=[],
            checkpoint_not_found=False,
        )

        def format_generated_example(example: EditGenExample):
            lines = example.before.splitlines()
            pad = len(str(len(lines)))
            return "\n".join(f"{i+1:>{pad}}|{line}" for i, line in enumerate(lines))

        def edit_gen_side_effect(input_tokens, **kwargs):
            input_string = self.handler.edit_prompt_formatter.tokenizer.detokenize(
                input_tokens
            )
            candidate_examples = list(
                filter(
                    lambda e: format_generated_example(e) in input_string,
                    generated_examples,
                )
            )
            if candidate_examples:
                example = candidate_examples[0]
                return self._create_edit_gen_reply(
                    example.before,
                    example.after,
                    example.change_probability,
                    force_change,
                )
            else:
                # Didn't prepare something, so generate no change.
                return self._create_edit_gen_reply("", "")

        def location_edit_gen_side_effect(input_tokens, **kwargs):
            input_string = self.handler.edit_prompt_formatter.tokenizer.detokenize(
                input_tokens
            )
            candidate_examples = list(
                filter(
                    lambda e: format_generated_example(e) in input_string,
                    generated_examples,
                )
            )
            if candidate_examples:
                example = candidate_examples[0]
                return self._create_edit_gen_reply(
                    example.before,
                    example.after,
                    example.change_probability,
                    force_change,
                )
            else:
                # Didn't prepare something, so generate no change.
                return self._create_edit_gen_reply("", "")

        def description_gen_side_effect(input_tokens, **kwargs):
            assert self.handler.next_edit_description_service is not None
            input_string = self.handler.next_edit_description_service.description_prompt_formatter.tokenizer.detokenize(
                input_tokens
            )
            candidate_examples = list(
                filter(
                    lambda e: "\n".join([f"-{s}" for s in e.before.splitlines()])
                    in input_string,
                    generated_examples,
                )
            )
            if candidate_examples:
                description = candidate_examples[0].change_description
            else:
                # Didn't prepare something, so use default.
                description = "default description"
            return self._create_edit_description_reply(description)

        self.edit_gen_client.infer.side_effect = edit_gen_side_effect
        self.location_edit_gen_client.infer.side_effect = location_edit_gen_side_effect
        self.description_gen_client.infer.side_effect = description_gen_side_effect

    def add_mock_content(self, present_blobs: dict[str, str] = {}):
        """Provide mock content for the content manager to find.
        Args:
            present_blobs: A dict of blob names to the contents.
        """

        def batch_download_all_mock(
            *args, **kwargs
        ) -> Iterable[tuple[bytes, dict[str, str]] | None]:
            requested_keys = args[0]
            for key in requested_keys:
                if key.blob_name in present_blobs:
                    yield (
                        present_blobs[key.blob_name].encode(),
                        {
                            "blob_name": key.blob_name,
                            "transformation_key": key.transformation_key,
                            "sub_key": key.sub_key,
                        },
                    )
                else:
                    yield None

        self.content_manager_client.batch_download_all.side_effect = (
            batch_download_all_mock
        )

    def add_mock_description(
        self,
        description: str,
    ):
        """Provide mock inference results for description generation.
        Args:
            output: the description to tokenize.
        """
        self.description_gen_client.infer.return_value = (
            self._create_edit_description_reply(description)
        )

    def _create_edit_gen_reply(
        self,
        before: str,
        after: str,
        change_probability: float = 1.0,
        force_change: bool = False,
    ):
        diff = "".join(encode_model_diff_output(before, after))

        if force_change:
            tokens = []
        elif equal_modulo_spaces(before, after):
            tokens = [self.prompt_formatter.special_tokens.no_change]
        else:
            tokens = [self.prompt_formatter.special_tokens.has_change]
        tokens.extend(self.prompt_formatter.tokenizer.tokenize_unsafe(diff))
        tokens.append(self.prompt_formatter.special_tokens.eos)

        probs = [math.log(change_probability)] + [0.0] * (len(tokens) - 1)
        assert len(tokens) == len(probs)
        return InferenceClientProtocol.Reply(tokens, probs)

    def _create_edit_description_reply(
        self,
        description: str,
    ):
        assert self.handler.next_edit_description_service is not None
        tokenizer = (
            self.handler.next_edit_description_service.chat_prompt_formatter.tokenizer
        )

        tokens = tokenizer.tokenize_safe(description)
        tokens.append(tokenizer.special_tokens.eos)

        return InferenceClientProtocol.Reply(tokens, [0.0] * len(tokens))

    def next_edit_stream(
        self,
        request: next_edit_pb2.NextEditRequest,
        request_id: uuid.UUID | None = None,
        session_id: uuid.UUID | None = None,
        max_workers: int = 8,
    ) -> Iterable[NextEditResult]:
        auth_info = AuthInfo(
            tenant_id="1234567890",
            tenant_name="test-tenant",
            shard_namespace="test-shard",
            cloud="test-cloud",
        )
        request.api_version = self.api_version

        if request_id is None:
            request_id = uuid.uuid4()
        if session_id is None:
            session_id = request_id
        request_context = RequestContext(
            request_id=str(request_id),
            request_session_id=str(session_id),
            request_source="unknown",
        )

        with TracedThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="test-threadpool-",
        ) as executor:
            result = list(
                self.handler.next_edit_stream(
                    request=NextEditRequest(
                        proto=request,
                        context=request_context,
                        auth_info=auth_info,
                    ),
                    executor=executor,
                )
            )
        return result


def create_handler_config() -> NextEditHandlerConfig:
    config_path = Path(__file__).parent / "handler_config_default.json"
    with open(config_path, "r") as f:
        default_config_json = f.read()
    default_config = NextEditHandlerConfig.from_json(default_config_json)
    return replace(
        default_config,
        # don't waste a bunch of memory in unit tests
        content_cache_size_mb=1,
        # set this to False to not break tests that don't have an edit event
        description_heuristic_config=replace(
            default_config.description_heuristic_config,
            char_limit=20,
        ),
        change_probability_min={"FOREGROUND": 0.5, "BACKGROUND": 0.6, "FORCED": 0.0},
        ignore_requests_with_no_file_changes=False,
        reranker_filter_threshold=0.3,
        validate_blob_state=False,
    )


def get_next_edit_gen_kit(api_version: int) -> HandlerTestKit:
    return HandlerTestKit(
        model_name="test_model",  # not a real model
        config=create_handler_config(),
        api_version=api_version,
    )


def with_suggestion_id(result: NextEditResult, suggestion_id: str) -> NextEditResult:
    return dataclasses.replace(
        result,
        suggested_edit=dataclasses.replace(
            result.suggested_edit, suggestion_id=suggestion_id
        ),
    )


# -----------------------------------------------------------------------------
# Tests that don't rely on a HandlerTestKit
PostProcessResult = request_insight_pb2.RINextEditSuggestion.PostProcessResult


@pytest.mark.parametrize(
    "path_to_blocked_ranges, expected",
    [
        ({}, PostProcessResult.NOOP),
        (
            {"src/example.py": {CharRange(0, 1)}},
            PostProcessResult.BLOCKED_LOCATION,
        ),
        (
            {"src/wrong_path.py": {CharRange(0, 1)}},
            PostProcessResult.NOOP,
        ),
        (
            {"src/example.py": {CharRange(0, 1000)}},
            PostProcessResult.BLOCKED_LOCATION,
        ),
        (
            {"src/example.py": {CharRange(0, 1), CharRange(3, 6)}},
            PostProcessResult.BLOCKED_LOCATION,
        ),
        (
            {"src/example.py": {CharRange(100, 1000)}},
            PostProcessResult.NOOP,
        ),
    ],
)
def test_should_filter_hunk(
    path_to_blocked_ranges: dict[str, set[CharRange]],
    expected: PostProcessResult.ValueType,
):
    """Test that we correctly filter hunks."""

    existing_code = "existing_code"
    hunk = ScoredFileHunk(
        path="src/example.py",
        blob_name="",
        char_start=0,
        char_end=len("existing"),
        existing_code=existing_code,
        suggested_code="suggested_code",
        localization_score=0.0,
        editing_score=0.0,
        diff_spans=[],
    )

    post_process_result = _post_process_hunk(
        hunk=hunk,
        request=MagicMock(),
        path_to_blocked_ranges=path_to_blocked_ranges,
        safe_logger=IgnoreSecretLogger(logging.getLogger(__name__)),
    )
    assert post_process_result == expected


def test_update_indexed_chunk_to_current():
    """Unit tests of _update_indexed_chunk_to_current."""

    indexed_file_text = """\
line 0
line 1
line 2
line 3
line 4
line 5
line 6
"""

    current_file_text = """\
line 0
line 1
line 2
modified line 3
inserted new line
modified line 4
line 5
line 6
"""

    # Create an indexed file
    file_path = "path/to/file.py"
    indexed_file = File(path=file_path, contents=indexed_file_text)

    # Choose 'line 3' and 'line 4' as the original retrieved chunk
    indexed_file_lmap = LineMap(indexed_file_text)
    retrieved_chunk_start = indexed_file_lmap.get_char_index(line=3, column=0)
    retrieved_chunk_end = indexed_file_lmap.get_char_index(line=5, column=0)

    retrieved_chunk = RetrievalChunk(
        text=indexed_file_text[retrieved_chunk_start:retrieved_chunk_end],
        path=file_path,
        char_start=retrieved_chunk_start,
        char_end=retrieved_chunk_end,
        blob_name=indexed_file.blob_name,
        chunk_index=None,
    )

    lmap = LineMap(current_file_text)
    current_file = File(path=file_path, contents=current_file_text)

    change_tracker = RecentChangeTracker()
    change_tracker.add_indexed_files([indexed_file])
    change_tracker.add_changed_files([current_file])

    updated_chunk = change_tracker.update_indexed_chunk_to_current(
        retrieved_chunk, test_logger
    )

    # the updated chunk should now include 3 lines because of the inserted line
    updated_chunk_expected_start = lmap.get_char_index(line=3, column=0)
    updated_chunk_expected_end = lmap.get_char_index(line=6, column=0)

    # Expanding the range won't do anything here as we are already at the line boundaries
    assert updated_chunk.char_start == updated_chunk_expected_start
    assert updated_chunk.char_end == updated_chunk_expected_end
    assert (
        updated_chunk.text
        == current_file.contents[
            updated_chunk_expected_start:updated_chunk_expected_end
        ]
    )
    assert updated_chunk.path == current_file.path
    assert updated_chunk.blob_name == current_file.blob_name


def test_convert_edit_events_to_recently_changed_files():
    before_file = File(
        path="path1",
        contents="",
    )
    after_file = File(
        path="path1",
        contents="print('Greetings World!')",
    )

    request = next_edit_pb2.NextEditRequest(
        # `selected_text` are not used.
        selected_text="",
        selection_begin_char=0,
        selection_end_char=0,
        mode=next_edit_pb2.NextEditMode.BACKGROUND,
        scope=next_edit_pb2.NextEditScope.CURSOR,
        recent_changes=[],
        edit_events=[
            edit_events_pb2.GranularEditEvent(
                path="path1",
                before_blob_name=before_file.blob_name,
                after_blob_name=after_file.blob_name,
                edits=[
                    edit_events_pb2.SingleEdit(
                        before_start=0,
                        after_start=0,
                        before_text="",
                        after_text="print('Greetings World!')",
                    )
                ],
            )
        ],
    )

    changed_file_list = SquashableEdits(
        edit_events=from_proto(request.edit_events, list[GranularEditEvent]),
        path_to_current_content={"path1": "print('Greetings World!')"},
    ).convert_edit_events_to_modified_files(
        safe_logger=IgnoreSecretLogger(logging.getLogger(__name__)),
        max_total_changed_chars=5000,
        is_source_file=lambda _: True,
        big_event_lines=8,
    )

    assert len(changed_file_list) == 1
    assert changed_file_list[0] == Modified(
        before=before_file,
        after=File(
            path="path1",
            contents="print('Greetings World!')",
        ),
    )


def test_split_changes_into_hunks():
    """Test that we correctly split changes into hunks."""
    change = Modified(
        """\
line 1
line 2
line 3
line 4
line 5
line 6
line 7
""",
        """\
modified line 1
line 2
line 3
modified line 4
added line 4.5
line 5
line 6
""",
    )
    lmaps = change.map(LineMap)

    change_hunk = ScoredFileHunk(
        path="src/example.py",
        blob_name="",
        char_start=0,
        char_end=len(change.before),
        existing_code=change.before,
        suggested_code=change.after,
        localization_score=0.0,
        editing_score=0.0,
        diff_spans=[],
    )

    hunks = list(_split_changes_into_hunks(change_hunk, n_context_lines=2))
    assert len(hunks) == 5
    expected_cranges = [
        lmaps.before.lrange_to_crange(LineRange(0, 1)),
        lmaps.before.lrange_to_crange(LineRange(1, 3)),
        lmaps.before.lrange_to_crange(LineRange(3, 4)),
        lmaps.before.lrange_to_crange(LineRange(4, 6)),
        lmaps.before.lrange_to_crange(LineRange(6, 7)),
    ]
    actual_cranges = [hunk.crange for hunk in hunks]
    assert actual_cranges == expected_cranges

    change = Modified(
        """\
line 1
line 2
line 3
line 4
line 5
line 6
line 7
""",
        """\
line 1
modified line 2
line 3
modified line 4
added line 4.5
line 5
line 6
""",
    )
    lmaps = change.map(LineMap)

    change_hunk = ScoredFileHunk(
        path="src/example.py",
        blob_name="",
        char_start=0,
        char_end=len(change.before),
        existing_code=change.before,
        suggested_code=change.after,
        localization_score=0.0,
        editing_score=0.0,
        diff_spans=[],
        truncation_char=len(change.after) - 2,  # 2 chars before end of line 6
    )

    hunks = list(_split_changes_into_hunks(change_hunk, n_context_lines=2))
    expected_cranges = [
        lmaps.before.lrange_to_crange(LineRange(0, 1)),  # line 1, unchanged
        lmaps.before.lrange_to_crange(LineRange(1, 4)),  # line 2-4, modified
        lmaps.before.lrange_to_crange(LineRange(4, 6)),  # line 5-6, unchanged
        lmaps.before.lrange_to_crange(LineRange(6, 7)),  # line 7, deleted
    ]
    actual_cranges = [hunk.crange for hunk in hunks]
    assert actual_cranges == expected_cranges

    truncation_char_in_3rd_hunk = len("line 5\nline 6\n") - 2
    expected_truncation_char = [None, None, truncation_char_in_3rd_hunk, None]
    actual_truncation_char = [hunk.truncation_char for hunk in hunks]
    assert actual_truncation_char == expected_truncation_char


# -----------------------------------------------------------------------------
# Tests that rely on a HandlerTestKit should be put under the class below


@pytest.mark.parametrize("api_version", [0, 1])
class TestMockedHandler:
    """Tests that rely on a HandlerTestKit."""

    def test_recent_changes_missing_blob_reporting(self, api_version: int):
        """Test reporting of missing blobs in recent changes"""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)

        next_edit_gen_kit.add_mock_content(
            {
                "blob_1": "def hello_world():\n    print('Hello World!')\n",
                "blob_2": "def hello_world_formal():\n    print('Greetings World!')\n",
                "blob_3": "def hi_world():\n    print('Hi World!')\n",
            }
        )
        before = "need a clue"
        after = "assert a['clue'] ==\n"
        next_edit_gen_kit.add_mock_edit_gen((before, after))

        request = next_edit_pb2.NextEditRequest(
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=len(before),
            mode=next_edit_pb2.NextEditMode.FORCED,
            scope=next_edit_pb2.NextEditScope.CURSOR,
            recent_changes=[
                completion_pb2.ReplacementText(
                    blob_name="blob_1",
                    path="path1",
                    char_start=0,
                    char_end=0,
                    replacement_text="print('Hello World!')",
                    present_in_blob=False,
                ),
                completion_pb2.ReplacementText(
                    blob_name="blob_missing",
                    path="path2",
                    char_start=0,
                    char_end=0,
                    replacement_text="print('Greetings World!')",
                    present_in_blob=False,
                ),
            ],
        )

        outputs = list(next_edit_gen_kit.next_edit_stream(request))

        expected_missing_blobs = ["blob_missing"]
        assert len(outputs) == 1
        assert set(outputs[0].unknown_blob_names) == set(
            expected_missing_blobs
        ), "unknown_blob_names incorrect"

        assert (
            next_edit_gen_kit.content_manager_client.batch_download_all.call_count == 1
        )

    def test_forced_mode(self, api_version: int):
        """Test that forced mode always replies, even if the change is low probability"""
        before = "need a clue"
        after = "assert a['clue'] ==\n"
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
            probs=[
                0.01
            ],  # should always return a change no matter how low the probability
            force_change=True,
        )
        request = next_edit_pb2.NextEditRequest(
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=len(before),
            mode=next_edit_pb2.NextEditMode.FORCED,
            scope=next_edit_pb2.NextEditScope.CURSOR,
        )

        outputs = list(next_edit_gen_kit.next_edit_stream(request))

        assert outputs[0].suggested_edit is not None
        assert outputs[0].suggested_edit.existing_code == before
        assert outputs[0].suggested_edit.suggested_code == after

    def test_output_truncation_char(self, api_version: int):
        """Test that forced mode always replies, even if the change is low probability"""
        before = "before text\n"
        after = "some truncated new text\n"
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
            probs=[
                0.99
            ],  # should always return a change no matter how low the probability
            force_change=False,
            has_eos=False,
        )
        request = next_edit_pb2.NextEditRequest(
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=0,
            mode=next_edit_pb2.NextEditMode.BACKGROUND,
            scope=next_edit_pb2.NextEditScope.CURSOR,
        )

        outputs = list(next_edit_gen_kit.next_edit_stream(request))

        assert outputs[0].suggested_edit is not None
        assert outputs[0].suggested_edit.existing_code == before
        assert outputs[0].suggested_edit.suggested_code == after
        assert outputs[0].suggested_edit.truncation_char == len(after)

    def test_request_with_no_file_changes(self, api_version: int):
        """Test that requests with no file changes are ignored (if ignore_requests_with_no_file_changes=True)."""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        new_config = replace(
            next_edit_gen_kit.config, ignore_requests_with_no_file_changes=True
        )
        next_edit_gen_kit.handler.config = new_config
        before = "need a clue"
        after = "assert a['clue'] ==\n"
        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
            probs=[
                0.9
            ],  # should always return a change no matter how low the probability
        )
        request = next_edit_pb2.NextEditRequest(
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=len(before),
            mode=next_edit_pb2.NextEditMode.BACKGROUND,
            scope=next_edit_pb2.NextEditScope.CURSOR,
        )
        # the result should be empty
        assert list(next_edit_gen_kit.next_edit_stream(request)) == []

        # but they should not be ignored if ignore_requests_with_no_file_changes is False
        new_config = replace(
            next_edit_gen_kit.config, ignore_requests_with_no_file_changes=False
        )
        next_edit_gen_kit.handler.config = new_config
        assert len(list(next_edit_gen_kit.next_edit_stream(request))) == 1

    def test_foreground_mode_drop_low_probability(self, api_version: int):
        """Test that foreground mode only replies if the change is sufficient probability"""
        """Test for the standard case of the next edit handler."""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        before = "need a clue"
        after = "assert a['clue'] ==\n"
        probability = next_edit_gen_kit.config.change_probability_min.get(
            next_edit_pb2.NextEditMode.Name(next_edit_pb2.NextEditMode.FOREGROUND)
        )
        assert (
            probability is not None
        ), "Missing config value for change_probability_min for mode FOREGROUND"
        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
            probs=[probability - 0.01],
        )
        request = next_edit_pb2.NextEditRequest(
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=len(before),
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.CURSOR,
        )

        outputs = list(next_edit_gen_kit.next_edit_stream(request))

        assert outputs[0].suggested_edit.existing_code == before
        assert outputs[0].suggested_edit.suggested_code == before

    def test_foreground_mode(self, api_version: int):
        """Test that foreground mode only replies if the change is sufficient probability"""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        before = "need a clue"
        after = "assert a['clue'] ==\n"
        probability = next_edit_gen_kit.config.change_probability_min.get(
            next_edit_pb2.NextEditMode.Name(next_edit_pb2.NextEditMode.FOREGROUND)
        )
        assert (
            probability is not None
        ), "Missing config value for change_probability_min for mode FOREGROUND"
        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
            probs=[probability + 0.01],
        )
        request = next_edit_pb2.NextEditRequest(
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=len(before),
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.CURSOR,
        )

        outputs = list(next_edit_gen_kit.next_edit_stream(request))

        assert outputs[0].suggested_edit.existing_code == before
        assert outputs[0].suggested_edit.suggested_code == after

    def test_background_mode(self, api_version: int):
        """Test that background mode only replies if the change is sufficient probability"""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        before = "need a clue"
        after = "assert a['clue'] ==\n"
        probability = next_edit_gen_kit.config.change_probability_min.get(
            next_edit_pb2.NextEditMode.Name(next_edit_pb2.NextEditMode.BACKGROUND)
        )
        assert (
            probability is not None
        ), "Missing config value for change_probability_min for mode BACKGROUND"
        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
            probs=[probability + 0.01],
        )
        outputs = next_edit_gen_kit.next_edit_stream(
            next_edit_pb2.NextEditRequest(
                selected_text=before,
                selection_begin_char=0,
                selection_end_char=len(before),
                mode=next_edit_pb2.NextEditMode.BACKGROUND,
                scope=next_edit_pb2.NextEditScope.CURSOR,
            )
        )
        item = next(iter(outputs))
        assert item.suggested_edit.existing_code == before
        assert item.suggested_edit.suggested_code == after

    def test_background_mode_drop_low_probability(self, api_version: int):
        """Test that background mode only replies if the change is sufficient probability"""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        before = "need a clue"
        after = "assert a['clue'] ==\n"
        probability = next_edit_gen_kit.config.change_probability_min.get(
            next_edit_pb2.NextEditMode.Name(next_edit_pb2.NextEditMode.BACKGROUND)
        )
        assert (
            probability is not None
        ), "Missing config value for change_probability_min for mode BACKGROUND"
        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
            probs=[probability - 0.01],
        )
        request = next_edit_pb2.NextEditRequest(
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=len(before),
            mode=next_edit_pb2.NextEditMode.BACKGROUND,
            scope=next_edit_pb2.NextEditScope.CURSOR,
        )
        output = next(iter(next_edit_gen_kit.next_edit_stream(request)))
        assert output.suggested_edit.existing_code == before
        assert output.suggested_edit.suggested_code == before

    @pytest.mark.parametrize(
        "file_before,file_after,edit_gen,single_edit",
        [
            (
                "",
                "a\nb\nc",
                # Model deletes the whole file.
                "",
                # Recent edit inserts the whole file.
                edit_events_pb2.SingleEdit(
                    before_start=0,
                    after_start=0,
                    before_text="",
                    after_text="a\nb\nc",
                ),
            ),
            (
                "a\nb\nc",
                "",
                # Model inserts the whole file.
                "a\nb\nc",
                # Recent edit deletes the whole file.
                edit_events_pb2.SingleEdit(
                    before_start=0,
                    after_start=0,
                    before_text="a\nb\nc",
                    after_text="",
                ),
            ),
            (
                "a\n2\nc",
                "a\nb\nc",
                # Model undos line 2.
                "a\n2\nc",
                # Recent edit modifies line 2.
                edit_events_pb2.SingleEdit(
                    before_start=2,
                    after_start=2,
                    before_text="2",
                    after_text="b",
                ),
            ),
        ],
    )
    def test_background_mode_drop_undo(
        self,
        file_before: str,
        file_after: str,
        edit_gen: str,
        single_edit: edit_events_pb2.SingleEdit,
        api_version: int,
    ):
        """Tests that undos are correctly filtered.

        Args:
            file_before: The file before applying `single_edit`.
            file_after: The file after applying `single_edit`.
            edit_gen: The file content after applying model generation to `file_after`. Note
            that the whole `file_after` are selected in `NextEditRequest`.
            single_edit: The single edit to apply to `file_before` to get `file_after`.
            next_edit_gen_kit: The `HandlerTestKit` to use.
        """
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        before_blob_name = get_blob_name("path1", file_before.encode("utf-8"))
        after_blob_name = get_blob_name("path1", file_after.encode("utf-8"))

        next_edit_gen_kit.add_mock_content(
            {
                before_blob_name: file_before,
                after_blob_name: file_after,
            }
        )
        next_edit_gen_kit.add_mock_edit_gen(
            (file_after, edit_gen),
        )
        request = next_edit_pb2.NextEditRequest(
            path="path1",
            blob_name=after_blob_name,
            # Selects the whole file.
            selected_text=file_after,
            selection_begin_char=0,
            selection_end_char=len(file_after),
            mode=next_edit_pb2.NextEditMode.BACKGROUND,
            scope=next_edit_pb2.NextEditScope.CURSOR,
            recent_changes=[],
            edit_events=[
                edit_events_pb2.GranularEditEvent(
                    path="path1",
                    before_blob_name=before_blob_name,
                    after_blob_name=after_blob_name,
                    edits=[single_edit],
                )
            ],
        )
        outputs = list(next_edit_gen_kit.next_edit_stream(request))
        # NOTE: some API versions will not return outputs.
        for output in outputs:
            assert output.suggested_edit.suggested_code != file_before
            # the client needs noop spans to check range or whatever. Lets verify we return them.
            assert (
                output.suggested_edit.existing_code
                == output.suggested_edit.suggested_code
            )

    @pytest.mark.parametrize(
        "case_number,file_before,file_after,edit_gen,suggested_edit,single_edit",
        [
            # Inserts additional line.
            (
                1,
                "",
                "a\nb\n",
                # Model inserts a line.
                "a\nb\nc\n",
                ("", "c\n"),
                # Recent edit inserts the whole file.
                edit_events_pb2.SingleEdit(
                    before_start=0,
                    after_start=0,
                    before_text="",
                    after_text="a\nb",
                ),
            ),
            # Partially deletion.
            (
                2,
                "",
                "a\nb\nc\n",
                # Model deletes line "a".
                "b\nc\n",
                ("a\n", ""),
                # Recent edit inserts the whole file.
                edit_events_pb2.SingleEdit(
                    before_start=0,
                    after_start=0,
                    before_text="",
                    after_text="a\nb\nc",
                ),
            ),
            # Partial insertion.
            (
                3,
                "a\nb\nc\nd\ne\n",
                "a\ne\n",
                # Model inserts line "b".
                "a\nb\ne\n",
                ("", "b\n"),
                # Recent edit deletes line "b", "c", "d".
                edit_events_pb2.SingleEdit(
                    before_start=2,
                    after_start=2,
                    before_text="b\nc\nd",
                    after_text="",
                ),
            ),
            # Partial modification.
            (
                4,
                "a\n2\n3\n4\ne\n",
                "a\nb\nc\nd\ne\n",
                # Model undos line 3.
                "a\nb\n3\nd\ne\n",
                ("c\n", "3\n"),
                # Recent edit modifies line 2, 3, 4.
                edit_events_pb2.SingleEdit(
                    before_start=2,
                    after_start=2,
                    before_text="2\n3\n4",
                    after_text="b\nc\nd",
                ),
            ),
            # Partial modification.
            (
                5,
                "a\n2\n3\n4\ne",
                "a\nb\nc\nd\ne",
                # Model undos line 2, 4.
                "a\n2\nc\n4\ne",
                ("b\nc\nd\n", "2\nc\n4\n"),
                # Recent edit modifies line 2, 3, 4.
                edit_events_pb2.SingleEdit(
                    before_start=2,
                    after_start=2,
                    before_text="2\n3\n4",
                    after_text="b\nc\nd",
                ),
            ),
        ],
    )
    def test_background_mode_not_drop_undo(
        self,
        case_number: int,
        file_before: str,
        file_after: str,
        edit_gen: str,
        suggested_edit: tuple[str, str],
        single_edit: edit_events_pb2.SingleEdit,
        api_version: int,
    ):
        """Tests that undos are correctly filtered.

        Args:
            file_before: The file before applying `single_edit`.
            file_after: The file after applying `single_edit`.
            edit_gen: The file content after applying model generation to `file_after`. Note
            that the whole `file_after` are selected in `NextEditRequest`.
            suggested_edit: The expected `existing_code` and `suggested_code` in the output.
            single_edit: The single edit to apply to `file_before` to get `file_after`.
            next_edit_gen_kit: The `HandlerTestKit` to use.
        """
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        before_blob_name = get_blob_name("path1", file_before.encode("utf-8"))
        after_blob_name = get_blob_name("path1", file_after.encode("utf-8"))

        next_edit_gen_kit.add_mock_content(
            {
                before_blob_name: file_before,
                after_blob_name: file_after,
            }
        )
        next_edit_gen_kit.add_mock_edit_gen(
            (file_after, edit_gen),
        )
        request = next_edit_pb2.NextEditRequest(
            path="path1",
            blob_name=after_blob_name,
            # Selects the whole file.
            selected_text=file_after,
            selection_begin_char=0,
            selection_end_char=len(file_after),
            mode=next_edit_pb2.NextEditMode.BACKGROUND,
            scope=next_edit_pb2.NextEditScope.CURSOR,
            recent_changes=[],
            edit_events=[
                edit_events_pb2.GranularEditEvent(
                    path="path1",
                    before_blob_name=before_blob_name,
                    after_blob_name=after_blob_name,
                    edits=[single_edit],
                )
            ],
        )
        outputs = next_edit_gen_kit.next_edit_stream(request)

        assert any(
            output.suggested_edit.existing_code == suggested_edit[0]
            and output.suggested_edit.suggested_code == suggested_edit[1]
            for output in outputs
        )

    def test_diff_spans_stream(self, api_version: int):
        """Test that diff spans are correctly computed."""
        before = dedent(
            """\
            line 1
            line 2
            line 3
            line 4
            line 5
            """
        )
        after = dedent(
            """\
            line 1
            line number 2
            line 3
            line changed
            line 4
            line 5
            """
        )

        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
        )
        request = next_edit_pb2.NextEditRequest(
            path="example.txt",
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=len(before),
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.CURSOR,
        )

        # NOTE(arun): without streaming, we only return the first changed hunk.
        outputs = list(next_edit_gen_kit.next_edit_stream(request))
        # Output order not guaranteed.
        outputs.sort(key=lambda x: x.suggested_edit.crange.start)
        assert (
            "".join(output.suggested_edit.existing_code for output in outputs) == before
        )
        assert (
            "".join(output.suggested_edit.suggested_code for output in outputs) == after
        )

        # expect diff spans to cover entire before and after text of each suggestion.
        for output in outputs:
            diff_spans = output.suggested_edit.diff_spans
            before_parts = [span.before for span in diff_spans]
            after_parts = [span.after for span in diff_spans]
            assert "".join(before_parts) == output.suggested_edit.existing_code
            assert "".join(after_parts) == output.suggested_edit.suggested_code

    def test_point_range_expansion(self, api_version: int):
        """Test that point ranges are expanded to a smart chunk"""

        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        # use a smart chunk size of 30 for this test
        max_chunk_chars = 30
        next_edit_gen_kit.set_smart_chunk_size(max_chunk_chars)

        full_text = dedent(
            """\
            prefix 1
            prefix 2
            prefix 3
            line 1
            line 2
            line 3
            line 4
            line 5
            suffix 1
            suffix 2
            suffix 3
            """
        )
        after = "replace 1\nreplace 2\nreplace 3\n"
        selection_range = CharRange(25, 25)  # a point inside "prefix 3"
        prefix = full_text[0 : selection_range.start]
        selected_text = full_text[selection_range.to_slice()]
        suffix = full_text[selection_range.stop :]
        expanded_range = expand_point_by_smart_chunks(
            full_text, len(prefix), max_chunk_chars
        )
        expanded_text = full_text[expanded_range.to_slice()]

        next_edit_gen_kit.add_mock_edit_gen(
            (expanded_text, after),
        )
        request = next_edit_pb2.NextEditRequest(
            prefix=prefix,
            selected_text=selected_text,
            suffix=suffix,
            selection_begin_char=len(prefix),
            selection_end_char=len(prefix),
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.CURSOR,
        )

        output = next(iter(next_edit_gen_kit.next_edit_stream(request)))
        assert output.suggested_edit.existing_code == expanded_text
        assert output.suggested_edit.suggested_code == after

    def test_line_boundary_expansion(self, api_version: int):
        """Test that partial line ranges are expanded to whole line boundaries"""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        full_text = dedent(
            """\
            prefix 1
            prefix 2
            prefix 3
            line 1
            line 2
            line 3
            line 4
            line 5
            suffix 1
            suffix 2
            suffix 3
            """
        )
        expanded_range = CharRange(27, 55)  # line 1 to line 4
        selection_range = CharRange(expanded_range.start + 2, expanded_range.stop - 2)
        prefix = full_text[0 : selection_range.start]
        selected_text = full_text[selection_range.to_slice()]
        suffix = full_text[selection_range.stop :]
        expanded_text = full_text[expanded_range.to_slice()]
        after = "replace 1\nreplace 2\nreplace 3\n"
        next_edit_gen_kit.add_mock_edit_gen(
            (expanded_text, after),
        )
        request = next_edit_pb2.NextEditRequest(
            prefix=prefix,
            selected_text=selected_text,
            suffix=suffix,
            selection_begin_char=selection_range.start,
            selection_end_char=selection_range.stop,
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.CURSOR,
        )

        outputs = list(next_edit_gen_kit.next_edit_stream(request))

        expanded_text = full_text[expanded_range.to_slice()]
        assert outputs[0].suggested_edit.existing_code == expanded_text
        assert outputs[0].suggested_edit.suggested_code == after

    def test_description_generation(self, api_version: int):
        """Test that background mode replies with a description."""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        before = (
            "a" * 29
        )  # make change larger to avoid heuristic description, but less than the chunk size limit
        after = (
            "assert a['clue'] ==\n" * 10
        )  # make change larger to avoid heuristic description
        description = ("Add assert", "")  # no markdown
        probability = next_edit_gen_kit.config.change_probability_min.get(
            next_edit_pb2.NextEditMode.Name(next_edit_pb2.NextEditMode.BACKGROUND)
        )
        assert (
            probability is not None
        ), "Missing config value for change_probability_min for mode BACKGROUND"
        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
            probs=[probability + 0.01],
        )
        next_edit_gen_kit.add_mock_description(description[0])
        output = next(
            iter(
                next_edit_gen_kit.next_edit_stream(
                    next_edit_pb2.NextEditRequest(
                        selected_text=before,
                        selection_begin_char=0,
                        selection_end_char=len(before),
                        mode=next_edit_pb2.NextEditMode.BACKGROUND,
                        scope=next_edit_pb2.NextEditScope.CURSOR,
                    )
                )
            )
        )
        assert (
            output.suggested_edit.existing_code == before
        ), f"{output.suggested_edit.existing_code} == {before}"
        assert (
            output.suggested_edit.suggested_code == after
        ), f"{output.suggested_edit.suggested_code} == {after}"
        assert (
            output.suggested_edit.change_description == description[0]
        ), f"{output.suggested_edit.change_description} == {description[0]}"
        assert (
            output.suggested_edit.markdown_change_description == description[1]
        ), f"{output.suggested_edit.markdown_change_description} == {description[1]}"

    def test_stream_request_handling_scope_cursor(self, api_version: int):
        """Test a basic stream request"""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        before = "need a clue"
        after = "assert a['clue'] ==\n"
        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
            probs=[
                0.01
            ],  # should always return a change no matter how low the probability
            force_change=True,
        )

        request = next_edit_pb2.NextEditRequest(
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=len(before),
            mode=next_edit_pb2.NextEditMode.FORCED,
            scope=next_edit_pb2.NextEditScope.CURSOR,
        )

        stream_output = next_edit_gen_kit.next_edit_stream(request)

        count = 0
        # TODO: Update this test when there's an actual stream response, not just an echo.
        for output in stream_output:
            assert output.suggested_edit.existing_code == before
            assert output.suggested_edit.suggested_code == after
            count += 1
        assert count == 1, f"Observed {count} responses, exactly 1."

    def test_stream_request_handling_scope_file(self, api_version: int):
        """Test a basic stream request"""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        # We need a max chunk size of 30 to break up the file below into 3 chunks.
        next_edit_gen_kit.set_smart_chunk_size(30)

        file = File(
            path="path/to/file.py",
            contents=dedent(
                """\
                def fn1():
                    print("fn1")

                def fn2():
                    print("fn2")

                def fn3():
                    print("fn3")
                """
            ),
        )
        next_edit_gen_kit.add_mock_content({file.blob_name: file.contents})

        generated_changes = [
            Modified(
                """\ndef fn3():\n    print("fn3")\n""",
                """\ndef fn3():\n    return "fn3"\n""",
            ),
            Modified(
                """\ndef fn2():\n    print("fn2")\n""",
                """\ndef fn2():\n    return "fn2"\n""",
            ),
            Modified(
                """def fn1():\n    print("fn1")\n""",
                """def fn1():\n    return "fn1"\n""",
            ),
        ]

        examples = [
            EditGenExample(
                location=RetrievalChunk(
                    text=change.before,
                    path=file.path,
                    char_start=file.contents.index(change.before),
                    char_end=file.contents.index(change.before) + len(change.before),
                    blob_name=file.blob_name,
                    chunk_index=0,
                    origin="retrieved",
                ),
                before=change.before,
                after=change.after,
            )
            for change in generated_changes
        ]

        # Cursor selection is just inside the last chunk.
        selection_begin_char = examples[0].location.char_start + 1
        selection_end_char = selection_begin_char
        prefix = file.contents[:selection_begin_char]
        suffix = file.contents[selection_end_char:]
        selected_text = file.contents[selection_begin_char:selection_end_char]

        request = next_edit_pb2.NextEditRequest(
            prefix=prefix,
            selected_text=selected_text,
            suffix=suffix,
            selection_begin_char=selection_begin_char,
            selection_end_char=selection_end_char,
            blob_name=file.blob_name,
            path=file.path,
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.FILE,
        )

        next_edit_gen_kit.add_mock_edit_gen_stream(
            locations=[example.location for example in examples],
            location_edit_generated_examples=examples,
            generated_examples=examples,
        )

        # Order-agnostic since stream output order is now timing dependent.
        stream_output = list(next_edit_gen_kit.next_edit_stream(request))
        stream_output.sort(key=lambda x: x.suggested_edit.crange.start)
        expected_changes = [
            Unchanged("def fn1():\n"),
            Modified("""    print("fn1")\n""", """    return "fn1"\n"""),
            Unchanged("\ndef fn2():\n"),
            Modified("""    print("fn2")\n""", """    return "fn2"\n"""),
            Unchanged("\ndef fn3():\n"),
            Modified("""    print("fn3")\n""", """    return "fn3"\n"""),
        ]

        assert len(stream_output) == len(
            expected_changes
        ), f"Observed {len(stream_output)} responses, expected {len(expected_changes)}"
        for change, output in zip(expected_changes, stream_output, strict=True):
            crange = CharRange(
                file.contents.index(change.before),
                file.contents.index(change.before) + len(change.before),
            )
            assert output.suggested_edit.crange == crange
            assert output.suggested_edit.existing_code == change.before
            assert output.suggested_edit.suggested_code == change.after

    def test_stream_request_handling_scope_workspace(self, api_version: int):
        """Test a basic stream request"""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        # We need a max chunk size of 30 for this example.
        next_edit_gen_kit.set_smart_chunk_size(30)

        files = [
            File(path="path/to/file.py", contents="needs a clue\n" * 5),
            File(path="path/to/a.py", contents="uses a clue\n" * 5),
            File(path="path/to/b.py", contents="generates a clue\n" * 5),
        ]

        next_edit_gen_kit.add_mock_content(
            {file.blob_name: file.contents for file in files}
        )

        # Two arbitrary lines will be suggested as edits
        examples = [
            EditGenExample(
                location=RetrievalChunk(
                    text="uses a clue\n",
                    path=files[1].path,
                    char_start=len("uses a clue\n" * 4),
                    char_end=len("uses a clue\n" * 5),
                    blob_name=files[1].blob_name,
                    chunk_index=0,
                    origin="retrieved",
                ),
                before="uses a clue\n",
                after="foo(a['clue']) #uses a clue\n",
                change_description="Add: foo(a['clue']) #",
                markdown_description="Add: `foo(a['clue']) #`",
            ),
            EditGenExample(
                location=RetrievalChunk(
                    text="generates a clue\n",
                    path=files[2].path,
                    char_start=0,
                    char_end=len("generates a clue\n"),
                    blob_name=files[2].blob_name,
                    chunk_index=0,
                    origin="retrieved",
                ),
                before="generates a clue\n",
                after="lambda: a['clue']\n",
                change_description="Return a clue in a lambda",
                markdown_description="",
            ),
        ]

        next_edit_gen_kit.add_mock_edit_gen_stream(
            locations=[example.location for example in examples],
            location_edit_generated_examples=examples,
            generated_examples=examples,
        )

        request = next_edit_pb2.NextEditRequest(
            selected_text=files[0].contents,
            selection_begin_char=0,
            selection_end_char=len(files[0].contents),
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.WORKSPACE,
        )

        stream_output = list(next_edit_gen_kit.next_edit_stream(request))
        stream_output.sort(key=lambda x: x.suggested_edit.path)
        stream_output_with_changes = [
            output
            for output in stream_output
            if output.suggested_edit.existing_code
            != output.suggested_edit.suggested_code
        ]

        for example, output in zip(examples, stream_output_with_changes, strict=True):
            assert output.suggested_edit.existing_code == example.before
            assert output.suggested_edit.suggested_code == example.after
            assert (
                output.suggested_edit.change_description == example.change_description
            )
            assert (
                output.suggested_edit.markdown_change_description
                == example.markdown_description
            )

    def test_gen_prompt_formatter_receives_retrieved_chunks(self, api_version: int):
        """Test that the gen prompt formatter receives the retrieved chunks."""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        before = "need a clue"
        after = "assert a['clue'] ==\n"

        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
        )

        mock_file = File(path="path/to/retrieved.py", contents="# Retrieved content.")

        # Create mock retrieved chunks
        retrieved_chunks = [
            RetrievalChunk(
                text=mock_file.contents,
                path=mock_file.path,
                char_start=0,
                char_end=len(mock_file.contents),
                blob_name=mock_file.blob_name,
                chunk_index=0,
                origin="retrieved",
            )
        ]

        # Setup mock content retriever to return the retrieved chunks
        next_edit_gen_kit.generation_retriever.retrieve.return_value = RetrievalResult(
            retrieved_chunks=retrieved_chunks,
            missing_blob_names=["missing_blob_1.py"],
            checkpoint_not_found=False,
        )

        next_edit_gen_kit.next_edit_stream(
            next_edit_pb2.NextEditRequest(
                selected_text=before,
                selection_begin_char=0,
                selection_end_char=len(before),
                mode=next_edit_pb2.NextEditMode.BACKGROUND,
                scope=next_edit_pb2.NextEditScope.CURSOR,
            )
        )

        input_tokens = next_edit_gen_kit.edit_gen_client.infer.call_args.kwargs[
            "input_tokens"
        ]
        input_string = (
            next_edit_gen_kit.handler.edit_prompt_formatter.tokenizer.detokenize(
                input_tokens
            )
        )
        assert "# Retrieved content" in input_string

    def test_gen_prompt_formatter_receives_updated_chunks(self, api_version: int):
        """Test that the gen prompt formatter receives the retrieved chunks."""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        before = "need a clue"
        after = "assert a['clue'] ==\n"

        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
        )

        path = "path/to/retrieved.py"
        indexed_file = File(path=path, contents="# Indexed content.")
        current_file = File(path=path, contents="# Current content.")

        # we only add the indexed file to simulate the current one not being indexed yet
        next_edit_gen_kit.add_mock_content(
            {indexed_file.blob_name: indexed_file.contents}
        )

        recent_changes = [
            # this will create the path_to_current_file map
            completion_pb2.ReplacementText(
                blob_name=indexed_file.blob_name,  # this must be the indexed blob name
                path=path,
                char_start=0,
                char_end=len(indexed_file.contents),
                replacement_text=current_file.contents,
                present_in_blob=False,
                expected_blob_name=current_file.blob_name,
            ),
        ]

        # Create mock retrieved chunks
        retrieved_chunks = [
            RetrievalChunk(
                text=indexed_file.contents,
                path=indexed_file.path,
                char_start=0,
                char_end=len(indexed_file.contents),
                blob_name=indexed_file.blob_name,
                chunk_index=0,
                origin="retrieved",
            )
        ]

        # Setup mock content retriever to return the retrieved chunks
        next_edit_gen_kit.generation_retriever.retrieve.return_value = RetrievalResult(
            retrieved_chunks=retrieved_chunks,
            missing_blob_names=["missing_blob_1.py"],
            checkpoint_not_found=False,
        )

        next_edit_gen_kit.next_edit_stream(
            next_edit_pb2.NextEditRequest(
                selected_text=before,
                selection_begin_char=0,
                selection_end_char=len(before),
                mode=next_edit_pb2.NextEditMode.BACKGROUND,
                scope=next_edit_pb2.NextEditScope.CURSOR,
                recent_changes=recent_changes,
            )
        )

        input_tokens = next_edit_gen_kit.edit_gen_client.infer.call_args.kwargs[
            "input_tokens"
        ]
        input_string = (
            next_edit_gen_kit.handler.edit_prompt_formatter.tokenizer.detokenize(
                input_tokens
            )
        )
        assert "# Current content" in input_string

    def test_update_indexed_chunk_to_current_end_to_end(self, api_version: int):
        """Test applying recent changes to retrieved location chunk."""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)

        indexed_text = dedent(
            """\
            line 0
            line 1
            line 2
            line 3
            line 4
            """
        )

        current_text = dedent(
            """\
            current line 0
            current line 1
            current line 2
            current line 3
            current line 4
            """
        )

        edited_text = dedent(
            """\
            edited line 0
            edited line 1
            edited line 2
            edited line 3
            edited line 4
            """
        )

        # recent changes never record file renaming events
        path = "constant/path/to/our/lovely/file.py"

        indexed_file = File(path=path, contents=indexed_text)

        # only the indexed file gets to be part of the content manager
        next_edit_gen_kit.add_mock_content(
            {indexed_file.blob_name: indexed_file.contents}
        )

        current_file = File(path=path, contents=current_text)
        edited_file = File(path=path, contents=edited_text)

        expected_editgen_examples = [
            EditGenExample(
                # we expect the edit to come from the current file if the indexed -> current update is working
                location=RetrievalChunk(
                    text=current_file.contents,
                    path=path,
                    char_start=0,
                    char_end=10,  # Any portion of the first smart chunk will do
                    blob_name=current_file.blob_name,
                    chunk_index=None,
                    origin="retrieved",
                ),
                # First two lines of current file content form one smart chunk
                before="\n".join(current_file.contents.split("\n")[:2]) + "\n",
                after=edited_file.contents,
            ),
        ]

        next_edit_gen_kit.add_mock_edit_gen_stream(
            locations=[
                # we want the location retriever to return the indexed file so it can be updated
                RetrievalChunk(
                    text=indexed_file.contents,
                    path=path,
                    char_start=0,
                    char_end=len(indexed_file.contents),
                    blob_name=indexed_file.blob_name,
                    chunk_index=None,
                    origin="retrieved",
                )
            ],
            location_edit_generated_examples=expected_editgen_examples,
            generated_examples=expected_editgen_examples,
        )

        recent_changes = [
            completion_pb2.ReplacementText(
                blob_name=indexed_file.blob_name,  # The blob name this change applies to
                path=path,
                char_start=0,
                char_end=len(indexed_file.contents),
                replacement_text=current_file.contents,
                present_in_blob=False,
                expected_blob_name=current_file.blob_name,
            ),
        ]

        # This is what the handler will actually see
        request = next_edit_pb2.NextEditRequest(
            # do not include a path here -- will mess up the updating
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.WORKSPACE,
            recent_changes=recent_changes,
        )

        stream_output = list(next_edit_gen_kit.next_edit_stream(request))
        stream_output.sort(key=lambda x: x.suggested_edit.crange.start)

        count = 0
        for example, output in zip(expected_editgen_examples, stream_output):
            assert output.suggested_edit.existing_code == example.before
            assert output.suggested_edit.suggested_code == example.after
            assert (
                output.suggested_edit.change_description == example.change_description
            )
            count += 1
        assert count == len(
            expected_editgen_examples
        ), f"Observed {count} responses, expected {len(expected_editgen_examples)}"

    def test_filter_blocked_location(self, api_version: int):
        """Test filtering blocked locations."""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)

        chunker = SmartChunker(
            max_chunk_chars=next_edit_gen_kit.config.max_smart_chunk_size, max_headers=0
        )

        indexed_text = dedent(
            """\
            line 0
            line 1
            line 2
            line 3
            line 4
            """
        )
        indexed_chunks = chunker.split_chunks(indexed_text, None)
        edited_text = dedent(
            """\
            edited line 0
            edited line 1
            edited line 2
            edited line 3
            edited line 4
            """
        )
        edited_chunks = chunker.split_chunks(edited_text, None)

        indexed_file = File(path="path/to/indexed/file.py", contents=indexed_text)
        next_edit_gen_kit.add_mock_content(
            {indexed_file.blob_name: indexed_file.contents}
        )

        retrieved_locations = [
            RetrievalChunk(
                text=chunk.text,
                path=indexed_file.path,
                char_start=chunk.char_offset,
                char_end=chunk.char_offset + len(chunk.text),
                blob_name=indexed_file.blob_name,
                chunk_index=None,
                origin="retrieved",
            )
            for chunk in indexed_chunks
        ]

        expected_editgen_examples = [
            EditGenExample(
                location=location,
                before=location.text,
                after=edit.text,
            )
            for location, edit in zip(retrieved_locations, edited_chunks)
        ]

        next_edit_gen_kit.add_mock_edit_gen_stream(
            locations=retrieved_locations,
            generated_examples=expected_editgen_examples,
            location_edit_generated_examples=expected_editgen_examples,
        )

        # we first make a request without a blocked location to check that the location is returned as expected
        request_no_block = next_edit_pb2.NextEditRequest(
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.WORKSPACE,
        )

        stream_output = next_edit_gen_kit.next_edit_stream(request_no_block)

        count = 0
        for output in stream_output:
            # Order of responses not guaranteed. Find the expected respone with the same source content.
            examples = [
                ex
                for ex in expected_editgen_examples
                if ex.before == output.suggested_edit.existing_code
            ]
            assert (
                examples
            ), f"No example found for {output.suggested_edit.existing_code}"
            assert (
                len(examples) == 1
            ), f"Too many example found for {output.suggested_edit.existing_code}"
            example = examples[0]
            assert output.suggested_edit.suggested_code == example.after
            assert (
                output.suggested_edit.change_description == example.change_description
            )
            count += 1
        assert count == len(
            expected_editgen_examples
        ), f"Observed {count} responses, expected {len(expected_editgen_examples)}"

        # create a blocked location that contains the retrieved location
        blocked_location = next_edit_pb2.FileRegion(
            path=indexed_file.path,  # blocking the location that we just saw
            char_start=0,
            char_end=len(indexed_file.contents),
        )

        # the retrived location should be blocked with this request
        request_block = next_edit_pb2.NextEditRequest(
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.WORKSPACE,
            blocked_locations=[blocked_location],
        )

        stream_output = next_edit_gen_kit.next_edit_stream(request_block)

        for output in stream_output:
            # nothing should be returned since the location is blocked
            assert False

        # create a blocked location that only touches the retrieved location
        blocked_location = next_edit_pb2.FileRegion(
            path=indexed_file.path,
            char_start=0,
            char_end=0,  # make sure point range also touches
        )

        # send a request where the retrieved location touches the blocked location
        request_block = next_edit_pb2.NextEditRequest(
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.WORKSPACE,
            blocked_locations=[blocked_location],
        )

        stream_output = next_edit_gen_kit.next_edit_stream(request_block)

        for output in stream_output:
            # nothing should change since we touch a blocked location
            assert (
                output.suggested_edit.suggested_code
                == output.suggested_edit.existing_code
            )

    def test_location_ranking(self, api_version: int):
        """Test that locations are sorted by score."""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)

        blob_1_file = File(path="path/to/blob_1.py", contents="this is blob 1")
        blob_2_file = File(path="path/to/blob_2.py", contents="this is blob 2")
        blob_3_file = File(path="path/to/blob_3.py", contents="this is blob 3")

        LOW_PROBABILITY = 0.6
        MID_PROBABILITY = 0.7
        HIGH_PROBABILITY = 0.8

        # TODO(moogi): change function name to "set_mock_content"
        # or alternatively implement it as "add" - so multiple calls do not override.
        next_edit_gen_kit.add_mock_content(
            {
                file.blob_name: file.contents
                for file in [blob_1_file, blob_2_file, blob_3_file]
            }
        )

        next_edit_gen_kit.handler._generate_edits_for_location = MagicMock(
            wrap=next_edit_gen_kit.handler._generate_edits_for_location
        )

        # The order does not matter here.. we will review the prompt
        expected_editgen_examples = [
            EditGenExample(
                # we expect the edit to come from the current file if the indexed -> current update is working
                location=RetrievalChunk(
                    text=file.contents,
                    path=file.path,
                    char_start=0,
                    char_end=len(file.contents),
                    blob_name=file.blob_name,
                    chunk_index=None,
                    origin="retrieved",
                ),
                before=file.contents,
                after=file.contents + " with an edit",
                change_probability={
                    blob_1_file: LOW_PROBABILITY,
                    blob_2_file: HIGH_PROBABILITY,
                    blob_3_file: MID_PROBABILITY,
                }[file],
            )
            for file in [blob_1_file, blob_2_file, blob_3_file]
        ]

        next_edit_gen_kit.add_mock_edit_gen_stream(
            locations=[
                # we want the location retriever to return the indexed file so it can be updated
                RetrievalChunk(
                    text=indexed_file.contents,
                    path=indexed_file.path,
                    char_start=0,
                    char_end=len(indexed_file.contents),
                    blob_name=indexed_file.blob_name,
                    chunk_index=None,
                    origin="retrieved",
                )
                for indexed_file in [blob_1_file, blob_2_file, blob_3_file]
            ],
            location_edit_generated_examples=expected_editgen_examples,
            generated_examples=expected_editgen_examples,
        )

        # This is what the handler will actually see
        request = next_edit_pb2.NextEditRequest(
            # including path to the indexed file will stop retrieve_files_for_blobs from updating indexed->current
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.WORKSPACE,
            vcs_change=next_edit_pb2.VCSChange(working_directory_changes=[]),
            recent_changes=[],
        )

        next_edit_gen_kit.next_edit_stream(request)

        # assert equivalent results (NOT sorted order since we weaken this with pipelining)
        expected = {blob_1_file.path, blob_2_file.path, blob_3_file.path}
        KWARGS = 1
        gen_edits = next_edit_gen_kit.handler._generate_edits_for_location
        assert gen_edits.call_count == 3

        assert (
            set(
                gen_edits.call_args_list[i][KWARGS]["prompt_input"].current_file.path
                for i in range(3)
            )
            == expected
        )

    def test_find_missing(self, api_version: int):
        """Tests that the find-missing call is correctly forwarded to the retriever."""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)

        # blob1 is missing in content manager, we ignore this fact and don't ask content_manager
        assert isinstance(next_edit_gen_kit.handler.content_manager_client, MagicMock)
        next_edit_gen_kit.handler.content_manager_client.find_missing.return_value = [
            "blob1"
        ]

        # blob2 is missing in retriever only, so it should be marked as nonindexed
        assert isinstance(next_edit_gen_kit.handler.generation_retriever, MagicMock)
        next_edit_gen_kit.handler.generation_retriever.find_missing.return_value = (
            RetrieverFindMissingResult(missing_blob_names=["blob1", "blob2"])
        )
        # blob3 is available so it should not appear anywhere
        find_missing_request = next_edit_pb2.FindMissingRequest(
            blob_names=["blob1", "blob2", "blob3"]
        )
        with SynchronousExecutor() as executor:
            find_missing_result = next_edit_gen_kit.handler.find_missing(
                find_missing_request,
                request_context=RequestContext.create(),
                executor=executor,
            )

        assert set(find_missing_result.missing_blob_names) == {"blob1"}
        assert set(find_missing_result.nonindexed_blob_names) == {"blob2"}

    def test_edit_gen_error(self, api_version: int):
        """Test that an error in edit generation is properly propagated."""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)

        before = "need a clue"
        after = "assert a['clue'] ==\n"
        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
            probs=[1.00],  # just force it to return a change
            force_change=True,
        )
        # Set up the mock to raise an exception
        next_edit_gen_kit.edit_gen_client.infer.side_effect = Exception(
            "Simulated edit gen error"
        )
        request = next_edit_pb2.NextEditRequest(
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=len(before),
            mode=next_edit_pb2.NextEditMode.FORCED,
            scope=next_edit_pb2.NextEditScope.CURSOR,
        )

        # Use pytest.raises to check for the exception
        with pytest.raises(Exception) as exc_info:
            res = list(next_edit_gen_kit.next_edit_stream(request))
            assert len(res) == 0

        # Check that the error message matches
        assert str(exc_info.value) == "Simulated edit gen error"

        # Verify that the edit_gen_client.infer was called
        next_edit_gen_kit.edit_gen_client.infer.assert_called_once()

    def test_early_exit(self, api_version: int):
        """Test that a request with a smaller timestamp is cancelled."""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        before = "need a clue"
        after = "assert a['clue'] ==\n"
        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
            probs=[1.00],  # just force it to return a change
            force_change=True,
        )

        # Create first request with timestamp 100
        request1 = next_edit_pb2.NextEditRequest(
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=len(before),
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.CURSOR,
            client_created_at=Timestamp(seconds=100),
        )

        # Create second request with timestamp 50 (earlier)
        request2 = next_edit_pb2.NextEditRequest(
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=len(before),
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.CURSOR,
            client_created_at=Timestamp(seconds=50),
        )
        # Grab a session id for both
        session_id = uuid.uuid4()

        # Process first request
        results1 = list(
            next_edit_gen_kit.next_edit_stream(request1, session_id=session_id)
        )
        assert len(results1) > 0

        try:
            next_edit_gen_kit.next_edit_stream(request2, session_id=session_id)
            assert False  # Should not reach here
        except NextEditCancelledException as e:
            expected = "Cancelled before starting any work."
            assert expected in str(e)

    def test_early_exit_forced(self, api_version: int):
        """Test that a forced request is not cancelled."""
        next_edit_gen_kit = get_next_edit_gen_kit(api_version)
        before = "need a clue"
        after = "assert a['clue'] ==\n"
        next_edit_gen_kit.add_mock_edit_gen(
            (before, after),
            probs=[1.00],  # just force it to return a change
            force_change=True,
        )

        # Create first request with timestamp 100
        request1 = next_edit_pb2.NextEditRequest(
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=len(before),
            mode=next_edit_pb2.NextEditMode.FORCED,
            scope=next_edit_pb2.NextEditScope.CURSOR,
            client_created_at=Timestamp(seconds=100),
        )

        # Create second request with timestamp 50 (earlier)
        request2 = next_edit_pb2.NextEditRequest(
            selected_text=before,
            selection_begin_char=0,
            selection_end_char=len(before),
            mode=next_edit_pb2.NextEditMode.FORCED,
            scope=next_edit_pb2.NextEditScope.CURSOR,
            client_created_at=Timestamp(seconds=50),
        )
        # Grab a session id for both
        session_id = uuid.uuid4()

        # Process first request
        results1 = list(
            next_edit_gen_kit.next_edit_stream(request1, session_id=session_id)
        )
        assert len(results1) > 0

        # Process second request - should not be cancelled as it is forced
        results2 = list(
            next_edit_gen_kit.next_edit_stream(request2, session_id=session_id)
        )
        assert len(results2) > 0
