local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  local sustainedLatencySpec = {
    displayName: 'Next Edit sustained background latency warning',
    conditionPrometheusQueryLanguage: {
      duration: '3600s',
      evaluationInterval: '300s',
      labels: { severity: 'warning' },
      // Per-namespace alert on a P90 time-to-first-result exceeding 2 seconds.
      // Because per-namespace can be noisy, and due to deficiencies in the the alerting API,
      // we do three levels of aggregation:
      // 1. P90 over the last hour exceeds threshold, requires >180 requests.
      // 2. This violation must be sustained, at least 40% of the last hour.
      // 3. Because of one-off spikes + missing datapoints, we set the duration to 3600s.
      // Together, this effectively means that the effective window is 2 hours, firing when
      // ~40% of the 1h P90 is above the threshold.
      // Modeled on the persistent_latency_policy in api_proxy/server/monitoring.jsonnet
      query: |||
        avg_over_time(
            (
                (
                    sum by (cluster, namespace) (
                        increase(
                            au_next_edit_host_latency_seconds_bucket{
                            le="+Inf",
                            next_edit_mode="BACKGROUND",
                            request_source="client"
                            }[1h]
                        )
                    ) > bool 180
                )
                *
                (
                    histogram_quantile(
                        0.90,
                        sum by (le, cluster, namespace) (
                            rate(
                            au_next_edit_host_latency_seconds_bucket{
                                next_edit_mode="BACKGROUND",
                                request_source="client"
                            }[1h]
                            )
                        )
                    ) > bool 2.0
                )
            )[1h:5m]
        ) > 0.4
      |||,
    },
  };

  local nextEditSuggestionQueueTimeoutSpec = {
    displayName: 'Next Edit suggestion queue timeouts',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace, cluster) (rate(au_next_edit_host_suggestions_queue_timeout[5m])) > 0.1
      |||,
    },
  };

  local locationString = 'namespace %s cluster %s' % [monitoringLib.label('namespace'), monitoringLib.label('cluster')];
  [
    monitoringLib.alertPolicy(cloud, sustainedLatencySpec, 'next-edit-sustained-background-latency', 'Next Edit P90 background latency frequently above 2 seconds for more than 2 hours in %s' % locationString, team='next-edit'),
    monitoringLib.alertPolicy(cloud, nextEditSuggestionQueueTimeoutSpec, 'next-edit-suggestion-queue-timeout', 'Next Edit suggestion queue timeouts high frequency in %s' % locationString, team='next-edit'),
  ]
