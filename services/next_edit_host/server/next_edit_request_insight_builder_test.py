"""Tests for NextEditRequestInsightBuilder."""

from unittest.mock import MagicMock

from base.tokenizers.tiktoken_starcoder_tokenizer import StarCoderTokenizer
from services.lib.retrieval.retriever import Retrieval<PERSON>hunk
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.next_edit_host import next_edit_pb2
from services.next_edit_host.server.handler import (
    NextEditResult,
    ScoredFileHunk,
)
from services.next_edit_host.server.next_edit_request_insight_builder import (
    NextEditRequestInsightBuilder,
)
from services.request_insight import request_insight_pb2


def _test_update_request(
    request_id: str, events: list[request_insight_pb2.RequestEvent], auth_info: AuthInfo
):
    del auth_info
    return request_insight_pb2.UpdateRequestInfoRequest(
        request_id=request_id,
        events=events,
    )


def test_record_request():
    """Tests that record_request correctly submit events."""
    ri_publisher = MagicMock()
    ri_publisher.update_request_info_request = MagicMock(
        side_effect=_test_update_request
    )

    ri_builder = NextEditRequestInsightBuilder(ri_publisher)
    next_edit_request = next_edit_pb2.NextEditRequest(model_name="test_model")

    request_context = RequestContext.create()

    ri_builder.record_request(
        request=next_edit_request,
        request_context=request_context,
        auth_info=AuthInfo(
            tenant_id="test_tenant_id",
            tenant_name="test_tenant",
            shard_namespace="test_shard",
            cloud="test_cloud",
        ),
    )

    assert len(ri_publisher.publish_request_insight.call_args.args) == 1
    request = ri_publisher.publish_request_insight.call_args[0][0]
    assert request.request_id == request_context.request_id
    assert len(request.events) == 1
    assert request.events[
        0
    ].next_edit_host_request == request_insight_pb2.RINextEditRequest(
        request=next_edit_request,
    )


def test_next_edit_response_ri_builder():
    """Tests that NextEditResponseInsightBuilder correctly submit events."""
    ri_publisher = MagicMock()
    ri_publisher.update_request_info_request = MagicMock(
        side_effect=_test_update_request
    )
    ri_builder = NextEditRequestInsightBuilder(ri_publisher)

    file_path = "foo/bar.py"
    file_content = "test"
    generated_text = "passes"
    edit_suggestion = ScoredFileHunk(
        path=file_path,
        blob_name="blob",
        char_start=0,
        char_end=len(file_content),
        existing_code=file_content,
        suggested_code=generated_text,
        localization_score=0.0,
        editing_score=0.5,
        diff_spans=[],
        change_description="change description",
        markdown_change_description="markdown description",
    )
    next_edit_result = NextEditResult(
        suggested_edit=edit_suggestion,
        unknown_blob_names=[],
        checkpoint_not_found=False,
    )
    tokenizer = StarCoderTokenizer()

    text = "void quicksort"
    tokens = tokenizer.tokenize_safe(text)
    (_, offsets) = tokenizer.detokenize_with_offsets(tokens)
    log_probs = [0.7] * len(tokens)

    retrieval_chunk = RetrievalChunk(
        path="path",
        text="test",
        char_start=0,
        char_end=4,
        blob_name="blob",
        chunk_index=0,
        score=123.45,
        origin="origin",
    )

    request_context = RequestContext.create()
    auth_info = AuthInfo(
        tenant_id="test_tenant_id",
        tenant_name="test_tenant",
        shard_namespace="test_shard",
        cloud="test_cloud",
    )

    # Build the response piece by piece, as it should be built as the request is processed.
    response_ri = ri_builder.record_response(request_context, auth_info)
    location_ri = response_ri.record_location_retrieval()
    location_ri.add_returned_location(retrieval_chunk)
    location_ri.publish_locations()

    gen_ri = response_ri.record_generation()
    gen_ri.add_retrieved_chunks([retrieval_chunk])
    gen_ri.add_generation_prompt(tokenizer, tokens)
    gen_ri.add_generation_output(tokenizer, tokens, log_probs)
    gen_ri.publish()

    suggestion_ri = response_ri.record_suggestion(gen_ri.get_generation_id(), 5)
    suggestion_ri.add_description_prompt(tokenizer, tokens)
    suggestion_ri.add_description_output(tokenizer, tokens, log_probs)
    suggestion_ri.add_response(next_edit_result)
    suggestion_ri.publish()

    assert len(ri_publisher.publish_request_insight.call_args_list) == 3
    assert all(
        [
            len(call.args) == 1
            for call in ri_publisher.publish_request_insight.call_args_list
        ]
    )
    requests = [
        call.args[0] for call in ri_publisher.publish_request_insight.call_args_list
    ]
    assert all(
        [request.request_id == request_context.request_id for request in requests]
    )

    merged_request = request_insight_pb2.UpdateRequestInfoRequest()
    for request in requests:
        merged_request.MergeFrom(request)
    events = merged_request.events
    assert all([event.HasField("next_edit_host_response") for event in events])

    event_timestamp_millis = [event.time.ToMilliseconds() for event in events]
    unique_timestamp_millis = set(event_timestamp_millis)
    assert len(unique_timestamp_millis) == len(
        event_timestamp_millis
    ), "Event timestamps must be unique at ms resolution"

    ri_response = request_insight_pb2.RINextEditResponse()
    for event in events:
        ri_response.MergeFrom(event.next_edit_host_response)

    assert len(ri_response.retrieved_locations) == 1
    assert len(ri_response.generation) == 1
    assert len(ri_response.suggestions) == 1

    assert ri_response.retrieved_locations == [
        request_insight_pb2.RetrievalChunk(
            text="test",
            path="path",
            char_offset=0,
            char_end=4,
            blob_name="blob",
            chunk_index=0,
            score=123.45,
            origin="origin",
        )
    ]
    insight_generation = ri_response.generation[0]
    assert insight_generation.generation_id == gen_ri.get_generation_id()
    assert insight_generation.generation_prompt == request_insight_pb2.Tokenization(
        token_ids=tokens,
        text=text,
        offsets=offsets,
        log_probs=None,
    )
    assert insight_generation.generation_output == request_insight_pb2.Tokenization(
        token_ids=tokens,
        text=text,
        offsets=offsets,
        log_probs=log_probs,
    )
    assert insight_generation.retrieved_chunks == [
        request_insight_pb2.RetrievalChunk(
            text="test",
            path="path",
            char_offset=0,
            char_end=4,
            blob_name="blob",
            chunk_index=0,
            score=123.45,
            origin="origin",
        )
    ]

    insight_suggestion = ri_response.suggestions[0]
    assert insight_suggestion.generation_id == insight_generation.generation_id
    assert insight_suggestion.suggestion_order == 5
    assert insight_suggestion.description_prompt == request_insight_pb2.Tokenization(
        token_ids=tokens,
        text=text,
        offsets=offsets,
        log_probs=None,
    )
    assert insight_suggestion.description_output == request_insight_pb2.Tokenization(
        token_ids=tokens,
        text=text,
        offsets=offsets,
        log_probs=log_probs,
    )
    assert insight_suggestion.result == next_edit_result.to_next_edit_response_proto()


def test_next_edit_result_to_response_proto():
    """Tests next_edit_result_to_response_proto function."""
    file_path = "foo/bar.py"
    file_content = "test"
    generated_text = "passes"
    edit_suggestion = ScoredFileHunk(
        path=file_path,
        blob_name="blob",
        char_start=0,
        char_end=len(file_content),
        existing_code=file_content,
        suggested_code=generated_text,
        localization_score=0.0,
        editing_score=0.5,
        diff_spans=[],
        change_description="change description",
        markdown_change_description="markdown description",
    )
    next_edit_result = NextEditResult(
        suggested_edit=edit_suggestion,
        unknown_blob_names=[],
        checkpoint_not_found=False,
    )
    next_edit_response = next_edit_pb2.NextEditResponse(
        suggested_edit=edit_suggestion.to_file_hunk_proto(),
        unknown_blob_names=[],
        checkpoint_not_found=False,
    )

    assert next_edit_result.to_next_edit_response_proto() == next_edit_response
