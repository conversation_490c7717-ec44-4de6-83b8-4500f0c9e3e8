package service

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	kmspb "cloud.google.com/go/kms/apiv1/kmspb"
	bigtableproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	"github.com/augmentcode/augment/services/lib/encryption"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/googleapis/gax-go/v2"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestAddTenantIDToKey(t *testing.T) {
	tenantID := "my-tenant-id"
	key := []byte("events#1234567890")
	expectedKey := []byte("my-tenant-id#events#1234567890")

	result := addTenantIDToKey(key, tenantID, false)
	assert.Equal(t, expectedKey, result, "key with tenant ID prefix should match expected")
}

func TestRemoveTenantIDFromKey(t *testing.T) {
	tenantID := "my-tenant-id"
	key := []byte("my-tenant-id#events#1234567890")
	expectedKey := []byte("events#1234567890")

	result := removeTenantIDFromKey(key, tenantID)
	assert.Equal(t, expectedKey, result, "key with tenant ID removed should match expected")
}

func TestAddTenantIDToKeyWithEmptyKey(t *testing.T) {
	tenantID := "my-tenant-id"
	key := []byte("")

	result := addTenantIDToKey(key, tenantID, false)
	assert.Equal(t, []byte(""), result, "empty key should remain empty")
}

func TestRemoveTenantIDFromKeyWithEmptyKey(t *testing.T) {
	tenantID := "my-tenant-id"
	key := []byte("")

	result := removeTenantIDFromKey(key, tenantID)
	assert.Equal(t, []byte(""), result, "empty key should remain empty")
}

func TestRemoveWrongTenantIDFromKeyWithEmptyKey(t *testing.T) {
	tenantID := "wrong-tenant-id"
	key := []byte("my-tenant-id#events#1234567890")

	result := removeTenantIDFromKey(key, tenantID)
	assert.Equal(t, key, result, "key with different tenant ID removed should not change")
}

func TestCheckTenantDataAccess(t *testing.T) {
	ctx := context.Background()

	// Define tenant configurations
	tenantConfigs := map[string]struct {
		id            string
		name          string
		encryptionKey string
	}{
		"tenant-without-encryption": {
			id:            "tenant-without-encryption",
			name:          "tenant-without-encryption",
			encryptionKey: "",
		},
		"tenant-with-encryption": {
			id:            "tenant-with-encryption",
			name:          "tenant-with-encryption",
			encryptionKey: "projects/foo/locations/global/keyRings/bar/cryptoKeys/key_1",
		},
		"tenant-with-encryption-error": {
			id:            "tenant-with-encryption-error",
			name:          "tenant-with-encryption-error",
			encryptionKey: "projects/foo/locations/global/keyRings/bar/cryptoKeys/key_2",
		},
	}

	// Setup test cases
	tests := []struct {
		name          string
		tenantID      string
		kmsError      error
		expectedError error
	}{
		{
			name:          "tenant without encryption",
			tenantID:      "tenant-without-encryption",
			expectedError: nil,
		},
		{
			name:          "tenant with encryption and accessible key",
			tenantID:      "tenant-with-encryption",
			kmsError:      nil,
			expectedError: nil,
		},
		{
			name:          "tenant with encryption and inaccessible key",
			tenantID:      "tenant-with-encryption-error",
			kmsError:      status.Error(codes.PermissionDenied, "permission denied"),
			expectedError: encryption.ErrKeyAccessDenied,
		},
		{
			name:          "tenant not found",
			tenantID:      "nonexistent-tenant",
			expectedError: status.Error(codes.Internal, "failed to get tenant details"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create tenant map from configurations
			tenantMap := make(map[string]*tenantproto.Tenant)
			for id, config := range tenantConfigs {
				tenantMap[id] = &tenantproto.Tenant{
					Id:                config.id,
					Name:              config.name,
					EncryptionKeyName: config.encryptionKey,
				}
			}

			// Create mock tenant cache
			mockTenantCache := &mockTenantCache{
				tenants: tenantMap,
			}

			// Create mock KMS client with specific error for the problematic key
			mockKMS := &mockKMSWithErrors{
				keyErrors: map[string]error{
					"projects/foo/locations/global/keyRings/bar/cryptoKeys/key_2": tt.kmsError,
				},
			}

			// Create real tenant crypto with mock KMS
			tenantCrypto, err := encryption.New(ctx, mockKMS, encryption.WithDeniedKeyTTL(10*time.Millisecond))
			assert.NoError(t, err)
			defer tenantCrypto.Close()

			// Create server with mocks
			server := &BigtableProxyServer{
				tenantCache:  mockTenantCache,
				tenantCrypto: tenantCrypto,
			}

			// Call the function
			resp, err := server.CheckTenantDataAccess(
				context.Background(),
				&bigtableproto.CheckTenantDataAccessRequest{
					TenantId: tt.tenantID,
				},
			)

			// Print debug info
			t.Logf("Test case: %s", tt.name)
			t.Logf("Response: %+v", resp)
			t.Logf("Error: %v", err)

			// Check for expected error
			if tt.expectedError == nil {
				assert.NoError(t, err, "Expected no error for test case: %s", tt.name)
				assert.NotNil(t, resp, "Expected non-nil response for test case: %s", tt.name)
				assert.True(t, resp.HasAccess,
					"Expected HasAccess=true but got %v for test case: %s",
					resp.HasAccess, tt.name)
			} else {
				// For specific error types like ErrKeyAccessDenied
				if errors.Is(err, encryption.ErrKeyAccessDenied) {
					assert.ErrorIs(t, err, encryption.ErrKeyAccessDenied)
				} else if statusErr, ok := status.FromError(tt.expectedError); ok {
					// For gRPC status errors, just check the code
					gotStatusErr, ok := status.FromError(err)
					assert.True(t, ok, "Expected gRPC status error but got: %v", err)
					assert.Equal(t, statusErr.Code(), gotStatusErr.Code(),
						"Expected error code %v but got %v for test case: %s",
						statusErr.Code(), gotStatusErr.Code(), tt.name)
				}

				// When there's an error, the response should be nil or HasAccess should be false
				if resp != nil {
					assert.False(t, resp.HasAccess,
						"Expected HasAccess=false when there's an error for test case: %s", tt.name)
				}
			}
		})
	}
}

// Mock implementations
type mockTenantCache struct {
	tenants map[string]*tenantproto.Tenant
}

func (m *mockTenantCache) GetTenant(tenantID string) (*tenantproto.Tenant, error) {
	tenant, ok := m.tenants[tenantID]
	if !ok {
		return nil, fmt.Errorf("tenant not found: %s", tenantID)
	}
	return tenant, nil
}

func (m *mockTenantCache) GetTenantByName(tenantName string) (*tenantproto.Tenant, error) {
	return nil, nil
}

func (m *mockTenantCache) GetAllTenants() ([]*tenantproto.Tenant, error) {
	tenants := make([]*tenantproto.Tenant, 0, len(m.tenants))
	for _, tenant := range m.tenants {
		tenants = append(tenants, tenant)
	}
	return nil, nil
}

func (m *mockTenantCache) GetTenantsInNamespace(shardNamespace string) ([]*tenantproto.Tenant, error) {
	tenants := make([]*tenantproto.Tenant, 0, len(m.tenants))
	for _, tenant := range m.tenants {
		tenants = append(tenants, tenant)
	}
	return nil, nil
}

func (m *mockTenantCache) Close() {
	// No-op for mock
}

// Mock KMS client that returns specific errors for specific keys
type mockKMSWithErrors struct {
	encryption.KMSClient
	keyErrors map[string]error
	keyMap    map[string][]byte
}

func (m *mockKMSWithErrors) MacSign(ctx context.Context, req *kmspb.MacSignRequest, opts ...gax.CallOption) (*kmspb.MacSignResponse, error) {
	keyName := req.Name
	if err, ok := m.keyErrors[keyName]; ok && err != nil {
		return nil, err
	}

	// Default to using the standard mock implementation
	mockClient := encryption.NewMockKMSClient()
	return mockClient.MacSign(ctx, req, opts...)
}

func (m *mockKMSWithErrors) Close() error {
	return nil
}
