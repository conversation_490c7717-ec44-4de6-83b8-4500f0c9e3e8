local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  // Alert if we're using mock KMS in production.
  local mockKMSSpec = {
    displayName: 'Bigtable Proxy using Mock KMS',
    conditionPrometheusQueryLanguage: {
      duration: '60s',
      evaluationInterval: '60s',
      labels: { severity: 'error' },
      query: |||
        au_bigtable_proxy_mock_kms_usage > 0
      |||,
    },
  };

  // Alert if there are key access denied errors (from any operation)
  // Warning severity only since customer may have intentionally revoked key.
  local keyAccessErrorsSpec = {
    displayName: 'Bigtable Proxy Key Access Denied',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        max by (namespace)(
          increase(au_bigtable_proxy_crypto_latency_count{status_code="key_access_denied"}[15m])
          or
          increase(au_bigtable_proxy_key_access_check_count{status_code="key_access_denied"}[15m])
        ) > 0
      |||,
    },
  };

  // Alert if there are encryption or decryption errors
  // Error severity since they indicate a system issue.
  local encryptionErrorsSpec = {
    displayName: 'Bigtable Proxy Encryption Errors',
    conditionPrometheusQueryLanguage: {
      duration: '300s',  // Increased from 60s to 300s for consistency with key access errors
      evaluationInterval: '60s',
      labels: { severity: 'error' },
      query: |||
        max by (namespace, table_name, status_code)(
          increase(au_bigtable_proxy_crypto_latency_count{status_code=~"encryption_failed|decryption_failed|invalid_format|cipher_fail|unknown_error"}[15m])
        ) > 0
      |||,
    },
  };

  // Alert if there are Google KMS access errors
  // Error severity since they indicate infrastructure or permission issues
  local googleKMSErrorsSpec = {
    displayName: 'Bigtable Proxy Google KMS Access Errors',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'error' },
      query: |||
        max by (status_code)(
          increase(au_bigtable_proxy_crypto_latency_count{status_code=~"google_kms_error|permission_denied"}[15m])
        ) > 0
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      mockKMSSpec,
      'bigtable-proxy-mock-kms',
      'Bigtable Proxy is using mock KMS which is insecure for production use',
    ),
    monitoringLib.alertPolicy(
      cloud,
      keyAccessErrorsSpec,
      'bigtable-proxy-key-access-errors',
      'Bigtable Proxy is experiencing key access denied errors, which may indicate intentional key revocation',
    ),
    monitoringLib.alertPolicy(
      cloud,
      encryptionErrorsSpec,
      'bigtable-proxy-encryption-errors',
      'Bigtable Proxy is experiencing encryption or decryption errors, which indicates a system issue',
    ),
    monitoringLib.alertPolicy(
      cloud,
      googleKMSErrorsSpec,
      'bigtable-proxy-google-kms-errors',
      'Bigtable Proxy is experiencing Google KMS access errors, which indicates infrastructure or permission issues',
    ),
  ]
