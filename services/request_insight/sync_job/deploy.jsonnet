local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';
local bigqueryLib = import 'services/request_insight/lib/bigquery_lib.jsonnet';

function(cloud, env, namespace, namespace_config)
  local appName = 'request-insight-sync';
  local shortAppName = 'ri-sync';

  local clientCert = certLib.createCentralClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );
  local serviceAccount = gcpLib.createServiceAccount(
    app=appName, cloud=cloud, env=env, namespace=namespace, iam=true, overridePrefix=shortAppName
  );
  local datasetLib = (import 'services/request_insight/analytics_dataset/dataset_lib.jsonnet')(
    cloud, env, namespace, namespace_config.flags.useSharedDevRequestInsightBigquery
  );

  // The sync job needs somewhere to create temporary tables. Since BigQuery datasets don't support
  // IAM permissions, allowing this on the main analytics dataset would require this job to have
  // OWNER permissions on the dataset. Rather than giving those permissions and cluttering the main
  // dataset with temporary state, we create a separate dataset for this purpose.
  local tempDatasetName = '%s-request-insight-sync-temp' % datasetLib.namePrefix;
  local tempDatasetGcpName = std.strReplace(tempDatasetName, '-', '_');
  local tempDatasetAccess = lib.flatten([
    {
      role: 'OWNER',
      userByEmail: serviceAccount.serviceAccountGcpEmailAddress,
    },
    // Give full access in dev/staging and no access in prod. These tables can have PII, so for
    // simplicity it's easier to just disallow any direct access in prod.
    if env != 'PROD' then {
      role: 'OWNER',
      groupByEmail: '<EMAIL>',
    } else [],
  ]);
  local tempDataset = bigqueryLib.createDataset(
    tempDatasetName,
    tempDatasetGcpName,
    datasetLib.location,
    tempDatasetAccess,
    cloud,
    env,
    datasetLib.dataNamespace,
    appName
  );

  local config = {
    promPort: 9090,
    clientMtls: clientCert.config,
    namespace: namespace,
    projectId: cloudInfo[cloud].projectId,
    datasetName: datasetLib.datasetGcp,
    tempDatasetName: tempDatasetGcpName,
    authEndpoint: '%s:50051' % endpoints.getAuthCentralGrpcUrl(env=env, cloud=cloud, namespace=namespace),
    tenantWatcherEndpoint: endpoints.getTenantWatcherGrpcUrl(env=env, cloud=cloud, namespace=namespace),
    tokenExchangeEndpoint: endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace),
    pushgatewayURL: 'http://prometheus-pushgateway.monitoring.svc.cluster.local:9091',
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local bigqueryAccess = [
    // Access to the dataset.
    bigqueryLib.datasetAccess(
      namespace,
      appName,
      datasetLib.cloudIdentityGroup,
      datasetLib.dataNamespace,
      serviceAccount.serviceAccountGcpEmailAddress,
    ),
    // Permission to run jobs (i.e., queries).
    gcpLib.grantAccess(
      name='%s-bigquery-job-policy' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'Project',
        external: 'project/%s' % cloudInfo[cloud].projectId,
      },
      bindings=[
        {
          role: 'roles/bigquery.jobUser',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ]
    ),
    // Grant access to PII columns (like email) that are protected by policy tags
    gcpLib.grantAccess(
      name='%s-pii-finegrained-reader-access' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'DataCatalogPolicyTag',
        external: bigqueryLib.dataAccessPolicyTag(cloud, env, 'pii'),
      },
      bindings=[
        {
          role: 'roles/datacatalog.categoryFineGrainedReader',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ]
    ),
  ];

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local container =
    {
      name: appName,
      target: {
        name: '//services/request_insight/sync_job:image',
        dst: appName,
      },
      volumeMounts: [
        configMap.volumeMountDef,
        clientCert.volumeMountDef,
      ],
      args: [
        '--config',
        '/config/config.json',
      ],
      env: [
        {
          name: 'POD_NAME',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.name',
            },
          },
        },
        {
          name: 'POD_NAMESPACE',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.namespace',
            },
          },
        },
      ],
      resources: {
        limits: {
          cpu: 0.5,
          memory: '1Gi',
        },
      },
    };

  local pod = {
    serviceAccountName: serviceAccount.name,
    restartPolicy: 'Never',
    tolerations: tolerations,
    affinity: affinity,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
    volumes: [
      configMap.podVolumeDef,
      clientCert.podVolumeDef,
    ],
  };

  local cronjob = {
    apiVersion: 'batch/v1',
    kind: 'CronJob',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      schedule: '*/10 * * * *',
      // Suspend by default in dev.
      suspend: env == 'DEV',
      // Don't start jobs that were skipped when this job was suspended. Set to 2 minutes to give
      // the scheduler some slack.
      startingDeadlineSeconds: 120,
      successfulJobsHistoryLimit: 2,
      failedJobsHistoryLimit: 4,
      concurrencyPolicy: 'Forbid',
      timeZone: 'America/Los_Angeles',
      jobTemplate: {
        metadata: {
          labels: {
            app: 'deploy',
          },
        },
        spec: {
          // Disable backoff
          backoffLimit: 0,
          template: {
            metadata: {
              labels: {
                app: appName,
              },
            },
            spec: pod,
          },
        },
      },
    },
  };

  lib.flatten([
    tempDataset,
    configMap.objects,
    clientCert.objects,
    serviceAccount.objects,
    bigqueryAccess,
    cronjob,
  ])
