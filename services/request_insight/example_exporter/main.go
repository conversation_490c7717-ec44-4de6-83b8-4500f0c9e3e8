package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"

	"github.com/augmentcode/augment/base/logging"
	"github.com/augmentcode/augment/services/lib/pubsub"
	"github.com/augmentcode/augment/services/request_insight/lib/subscriber"
	pb "github.com/augmentcode/augment/services/request_insight/proto"
)

// Example of a minimal configuration. When writing your own exporter, add exporter-specific fields
// to the outer struct.
type Config struct {
	pubsub.SubscribeClientConfig
	HealthFile string
	PromPort   int
}

// Load configuration from the given file.
func loadConfig(configFile string) (*Config, error) {
	var config Config
	if configFile == "" {
		return nil, fmt.Errorf("missing config file")
	}

	f, err := os.Open(configFile)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		return nil, err
	}

	log.Info().Msgf("Config: %v", config)
	return &config, nil
}

func declareHealthy(healthFile string) {
	log.Info().Msgf("Declaring healthy in %s", healthFile)
	f, err := os.Create(healthFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create health file")
	}
	defer f.Close()
	f.WriteString("OK")
}

// "Process" a single message. This function is the main thing that needs to be implemented for a
// real exporter.
func processMessage(ctx context.Context, message *pb.RequestInsightMessage) error {
	switch message.Message.(type) {
	case *pb.RequestInsightMessage_UpdateRequestInfoRequest:
		log.Info().Msgf("Received UpdateRequestInfoRequest")
	case *pb.RequestInsightMessage_RecordFullExportUserEventsRequest:
		log.Info().Msgf("Received RecordFullExportUserEventsRequest")
	}

	return nil
}

func main() {
	logging.SetupServerLogging()

	// Parse flags.
	configFile := flag.String("config", "", "Path to config file")
	flag.Parse()

	// Load config.
	config, err := loadConfig(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error loading config")
	}

	// Start metrics server.
	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Create subscriber.
	ctx := context.Background()
	subscriber, err := subscriber.New(ctx, &config.SubscribeClientConfig, processMessage)
	if err != nil {
		log.Fatal().Err(err).Msg("Error initializing RequestInsightSubscriber")
	}
	defer subscriber.Close()

	// Wait 10 seconds before declaring healthy, to give the subscriber time to error out if there's
	// something wrong with the subscription. Ideally we would do this in the subscriber itself, but
	// Go's pubsub library only calls your provided callback when there are messages to process. For
	// low-traffic namespaces this can cause kubernetes crashloops.
	go func() {
		time.Sleep(10 * time.Second)
		declareHealthy(config.HealthFile)
	}()

	// Run the subscriber. This should never return in the happy case.
	subscriber.Run(ctx)
}
