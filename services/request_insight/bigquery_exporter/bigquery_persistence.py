"""Persistence for Request Insight in BigQuery."""

import json
import uuid
import re
from collections import defaultdict
from pathlib import Path
from time import sleep
from typing import Any, Callable, Iterable, Optional, Sequence
from urllib.parse import urlparse, urlunparse

import structlog
from google.api_core.exceptions import Forbidden, NotFound
from google.cloud.bigquery import Client
from google.cloud.bigquery.table import Table
from google.protobuf.json_format import MessageToDict
from google.rpc import status_pb2
from prometheus_client import Counter, Gauge, Histogram

import services.request_insight.request_insight_pb2 as request_insight_pb2
from base.blob_names import blob_names_pb2
from services.agents import agents_pb2
from services.auth.central.server import auth_entities_pb2
from services.chat_host import chat_pb2
from services.completion_host import completion_pb2
from services.edit_host import edit_pb2
from services.integrations.glean import glean_pb2
from services.integrations.slack_bot import slack_event_pb2
from services.next_edit_host import next_edit_pb2
from services.request_insight.bigquery_exporter.config import DataConfig
from services.share import share_pb2
from services.tenant_watcher import tenant_watcher_pb2

log = structlog.get_logger()

# Keep track of expected startup failures so that we can page if it lasts for longer than expected.
# See BigQueryPersistence docs. Status is "FORBIDDEN" or "NOT_FOUND".
_dataset_startup_error_gauge = Gauge(
    "au_request_insight_bigquery_startup_error_gauge",
    ">0 iff dataset connection is startup_error",
    ["status"],
)

# Latency for a single insert to a BigQuery table. Skip dataset_name because
# table_name includes it. Skip a status label here because failures are reported
# for each row, but a single insert can contain many rows.
_bigquery_insert_latency_seconds = Histogram(
    "au_bigquery_table_insert_latency_seconds",
    "Latency of inserts to BigQuery, by table, in seconds",
    ["table_name"],
)

# Number of rows inserted to each BigQuery table, along with status. Skip
# dataset_name because table_name includes it.
_bigquery_insert_row_count = Counter(
    "au_bigquery_table_insert_row_count",
    "Number of new rows inserted to BigQuery, by table",
    ["table_name", "error"],
)


def sanitize_external_source_ids(external_source_ids: Iterable[str]) -> list[str]:
    """Sanitize an external source ID to hide any sensitive information."""
    output = set()
    for external_source_id in external_source_ids:
        if external_source_id == "":
            output.add(external_source_id)
        elif external_source_id.startswith("docset://"):
            # Docsets are okay
            output.add(external_source_id)
        else:
            # We don't have any other external sources yet, but mask them in case we
            # add any with sensitive info.
            output.add("***")

    # Remove duplicates. Also sort the output so it is stable
    return sorted(list(output))


class BigQueryPersistence:
    """Writes Request Insight data to BigQuery.

    Every request insight event type has a separate table. See the definition of _tables in the
    constructor for the list of currently supported tables, and
    services/request_insight/analytics_dataset/schema.jsonnet for the formal schema of each table.

    Absolutely no sensitive customer information can be written to BigQuery! Our BigQuery datasets
    are cross-tenant and are intended for generic analytics only.
    """

    class RequestEventTable:
        """Wrapper around information about a BigQuery request event table."""

        def __init__(
            self,
            full_table_name: str,
            event_type: str,
            event_to_row_fn: Callable[
                [
                    str,
                    str,
                    request_insight_pb2.TenantInfo,
                    request_insight_pb2.RequestEvent,
                ],
                dict[str, object],
            ],
        ):
            """Constructor.

            Args:
                full_table_name: The BigQuery table name, including dataset (e.g.,
                    "request_insight_dataset.completion_resolution").
                event_type: The event type (e.g., "completion_resolution").
                event_to_row_fn: A function that turns a request_id and event into a dictionary that
                    can be written directly to the BigQuery table.
            """
            self.full_table_name: str = full_table_name
            self.event_type: str = event_type
            self.event_to_row_fn: Callable[
                [
                    str,
                    str,
                    request_insight_pb2.TenantInfo,
                    request_insight_pb2.RequestEvent,
                ],
                dict[str, object],
            ] = event_to_row_fn
            self.table: Optional[Table] = None

        def connect(self, bigquery_client):
            """Connect to the BigQuery table.

            This isn't done in the constructor because we expect connection errors at startup.
            See BigQueryPersistence.connect() docs.

            Args:
                bigquery_client: The BigQuery client to connect with.
            """
            self.table = bigquery_client.get_table(self.full_table_name)

    class SessionEventTable:
        """Wrapper around information about a BigQuery session event table."""

        def __init__(
            self,
            full_table_name: str,
            event_type: str,
            event_to_row_fn: Callable[
                [
                    str,
                    auth_entities_pb2.UserId,
                    request_insight_pb2.TenantInfo,
                    request_insight_pb2.SessionEvent,
                ],
                dict[str, object],
            ],
        ):
            """Constructor.

            Args:
                full_table_name: The BigQuery table name, including dataset (e.g.,
                    "request_insight_dataset.completion_resolution").
                event_type: The event type (e.g., "completion_resolution").
                event_to_row_fn: A function that turns a request_id and event into a dictionary that
                    can be written directly to the BigQuery table.
            """
            self.full_table_name: str = full_table_name
            self.event_type: str = event_type
            self.event_to_row_fn: Callable[
                [
                    str,
                    auth_entities_pb2.UserId,
                    request_insight_pb2.TenantInfo,
                    request_insight_pb2.SessionEvent,
                ],
                dict[str, object],
            ] = event_to_row_fn
            self.table: Optional[Table] = None

        def connect(self, bigquery_client):
            """Connect to the BigQuery table.

            This isn't done in the constructor because we expect connection errors at startup.
            See BigQueryPersistence.connect() docs.

            Args:
                bigquery_client: The BigQuery client to connect with.
            """
            self.table = bigquery_client.get_table(self.full_table_name)

    class TenantEventTable:
        """Wrapper around information about a BigQuery tenant event table."""

        def __init__(
            self,
            full_table_name: str,
            event_type: str,
            event_to_row_fn: Callable[
                [request_insight_pb2.TenantInfo, request_insight_pb2.TenantEvent],
                dict[str, object],
            ],
        ):
            """Constructor.

            Args:
                full_table_name: The BigQuery table name, including dataset (e.g.,
                    "request_insight_dataset.tenant_event").
                event_type: The event type (e.g., "add_user_to_tenant").
                event_to_row_fn: A function that turns a tenant_info and event into a dictionary that
                    can be written directly to the BigQuery table.
            """
            self.full_table_name: str = full_table_name
            self.event_type: str = event_type
            self.event_to_row_fn: Callable[
                [request_insight_pb2.TenantInfo, request_insight_pb2.TenantEvent],
                dict[str, object],
            ] = event_to_row_fn
            self.table: Optional[Table] = None

        def connect(self, bigquery_client):
            """Connect to the BigQuery table.

            This isn't done in the constructor because we expect connection errors at startup.
            See BigQueryPersistence.connect() docs.

            Args:
                bigquery_client: The BigQuery client to connect with.
            """
            self.table = bigquery_client.get_table(self.full_table_name)

    class GenericEventTable:
        """Wrapper around information about a BigQuery generic event table."""

        def __init__(
            self,
            full_table_name: str,
            event_type: str,
            event_to_row_fn: Callable[
                [str, request_insight_pb2.GenericEvent],
                dict[str, object],
            ],
        ):
            """Constructor.

            Args:
                full_table_name: The BigQuery table name, including dataset (e.g.,
                    "request_insight_dataset.recaptcha").
                event_type: The event type (e.g., "recaptcha").
                event_to_row_fn: A function that turns a tenant_info and event into a dictionary that
                    can be written directly to the BigQuery table.
            """
            self.full_table_name: str = full_table_name
            self.event_type: str = event_type
            self.event_to_row_fn: Callable[
                [str, request_insight_pb2.GenericEvent],
                dict[str, object],
            ] = event_to_row_fn
            self.table: Optional[Table] = None

        def connect(self, bigquery_client):
            """Connect to the BigQuery table.

            This isn't done in the constructor because we expect connection errors at startup.
            See BigQueryPersistence.connect() docs.

            Args:
                bigquery_client: The BigQuery client to connect with.
            """
            self.table = bigquery_client.get_table(self.full_table_name)

    def __init__(self, dataset_name: str, namespace: str, data_config: DataConfig):
        """Initialize BigQueryPersistence.

        Unless you're writing tests, you should also call connect().
        """
        self._namespace: str = namespace
        self._dataset_name: str = dataset_name
        self._bigquery_client: Optional[Client] = None
        self._path_keywords: list[str] = data_config.path_keywords
        self._instruction_keywords: list[str] = data_config.instruction_keywords
        self._chat_keywords: list[str] = data_config.chat_keywords
        self._keyword_categories: dict[str, list[str]] = data_config.keyword_categories
        self._file_extensions: set[str] = set(data_config.file_extensions)
        self._terminal_command_prefixes: set[str] = set(
            data_config.terminal_command_prefixes
        )

        # Add new request event tables here.
        self._request_event_tables: list[BigQueryPersistence.RequestEventTable] = [
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.request_metadata",
                "request_metadata",
                self._request_metadata_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.completion_resolution",
                "completion_resolution",
                self._completion_resolution_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.completion_request",
                "infer_request",
                self._completion_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.completion_emit",
                "completion_emit",
                self._completion_emit_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.completion_host_request",
                "completion_host_request",
                self._completion_host_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.completion_host_response",
                "completion_host_response",
                self._completion_host_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.retrieval_response",
                "retrieval_response",
                self._retrieval_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.completion_feedback",
                "completion_feedback",
                self._completion_feedback_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.chat_feedback",
                "chat_feedback",
                self._chat_feedback_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.agent_feedback",
                "agent_feedback",
                self._agent_feedback_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agent_feedback",
                "remote_agent_feedback",
                self._remote_agent_feedback_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.inference_host_response",
                "inference_host_response",
                self._inference_host_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.api_http_response",
                "api_http_response",
                self._api_http_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.embeddings_search_request",
                "embeddings_search_request",
                self._embeddings_search_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.embeddings_search_response",
                "embeddings_search_response",
                self._embeddings_search_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.edit_host_request",
                "edit_host_request",
                self._edit_host_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.edit_host_response",
                "edit_host_response",
                self._edit_host_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.edit_resolution",
                "edit_resolution",
                self._edit_resolution_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.edit_emit",
                "edit_emit",
                self._edit_emit_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.instruction_host_request",
                "instruction_host_request",
                self._instruction_host_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.instruction_host_response",
                "instruction_host_response",
                self._instruction_host_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.instruction_resolution",
                "instruction_resolution",
                self._instruction_resolution_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.instruction_emit",
                "instruction_emit",
                self._instruction_emit_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.smart_paste_resolution",
                "smart_paste_resolution",
                self._smart_paste_resolution_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.smart_paste_client_timeline",
                "smart_paste_client_timeline",
                self._smart_paste_client_timeline_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.extension_error",
                "extension_error",
                self._extension_error_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.chat_host_request",
                "chat_host_request",
                self._chat_host_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.chat_host_response",
                "chat_host_response",
                self._chat_host_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.next_edit_host_request",
                "next_edit_host_request",
                self._next_edit_host_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.next_edit_host_response",
                "next_edit_host_response",
                self._next_edit_host_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.next_edit_feedback",
                "next_edit_feedback",
                self._next_edit_feedback_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.slackbot_request",
                "slackbot_request",
                self._slackbot_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.slackbot_response",
                "slackbot_response",
                self._slackbot_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.slackbot_feedback",
                "slackbot_feedback",
                self._slackbot_feedback_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.completion_post_process",
                "completion_post_process",
                self._completion_post_process_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.parenthesis_truncation",
                "parenthesis_truncation",
                self._parenthesis_truncation_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.share_save_chat_request",
                "share_save_chat_request",
                self._share_save_chat_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.share_save_chat_response",
                "share_save_chat_response",
                self._share_save_chat_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.share_get_chat_request",
                "share_get_chat_request",
                self._share_get_chat_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.share_get_chat_response",
                "share_get_chat_response",
                self._share_get_chat_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.slackbot_installation_event",
                "slackbot_installation_event",
                self._slackbot_installation_event_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.github_app_installation_event",
                "github_app_installation_event",
                self._github_app_installation_event_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.glean_request",
                "glean_request",
                self._glean_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.glean_response",
                "glean_response",
                self._glean_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.glean_oauth_url_request",
                "glean_oauth_url_request",
                self._glean_oauth_url_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.glean_oauth_url_response",
                "glean_oauth_url_response",
                self._glean_oauth_url_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.tool_use_data",
                "tool_use_data",
                self._tool_use_data_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_tool_call_request",
                "remote_tool_call_request",
                self._remote_tool_call_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_tool_call_response",
                "remote_tool_call_response",
                self._remote_tool_call_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.request_blocked",
                "request_blocked",
                self._request_blocked_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.request_suspicious",
                "request_suspicious",
                self._request_suspicious_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.token_exchange_error",
                "token_exchange_error",
                self._token_exchange_error_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.daily_request_limit_exceeded",
                "daily_request_limit_exceeded",
                self._daily_request_limit_exceeded_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.chat_user_message",
                "chat_user_message",
                self._chat_user_message_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.find_missing",
                "find_missing",
                self._find_missing_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.batch_upload",
                "batch_upload",
                self._batch_upload_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_create_request",
                "remote_agents_create_request",
                self._remote_agents_create_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_create_response",
                "remote_agents_create_response",
                self._remote_agents_create_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_chat_request",
                "remote_agents_chat_request",
                self._remote_agents_chat_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_chat_response",
                "remote_agents_chat_response",
                self._remote_agents_chat_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_interrupt_request",
                "remote_agents_interrupt_request",
                self._remote_agents_interrupt_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_interrupt_response",
                "remote_agents_interrupt_response",
                self._remote_agents_interrupt_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_delete_request",
                "remote_agents_delete_request",
                self._remote_agents_delete_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_delete_response",
                "remote_agents_delete_response",
                self._remote_agents_delete_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_add_ssh_key_request",
                "remote_agents_add_ssh_key_request",
                self._remote_agents_add_ssh_key_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_add_ssh_key_response",
                "remote_agents_add_ssh_key_response",
                self._remote_agents_add_ssh_key_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_pause_request",
                "remote_agents_pause_request",
                self._remote_agents_pause_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_pause_response",
                "remote_agents_pause_response",
                self._remote_agents_pause_response_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_resume_request",
                "remote_agents_resume_request",
                self._remote_agents_resume_request_row,
            ),
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.remote_agents_resume_response",
                "remote_agents_resume_response",
                self._remote_agents_resume_response_row,
            ),
        ]

        # Add new session event tables here.
        self._session_event_tables: list[BigQueryPersistence.SessionEventTable] = [
            BigQueryPersistence.SessionEventTable(
                f"{self._dataset_name}.next_edit_session_event",
                "next_edit_session_event",
                self._next_edit_session_event_row,
            ),
            BigQueryPersistence.SessionEventTable(
                f"{self._dataset_name}.onboarding_session_event",
                "onboarding_session_event",
                self._onboarding_session_event_row,
            ),
            BigQueryPersistence.SessionEventTable(
                f"{self._dataset_name}.client_metric",
                "client_metric",
                self._client_metric_row,
            ),
            BigQueryPersistence.SessionEventTable(
                f"{self._dataset_name}.feature_vector_report",
                "feature_vector_report",
                self._feature_vector_report_row,
            ),
            BigQueryPersistence.SessionEventTable(
                f"{self._dataset_name}.extension_session_event",
                "extension_session_event",
                self._extension_session_event_row,
            ),
            BigQueryPersistence.SessionEventTable(
                f"{self._dataset_name}.customer_ui_session_event",
                "customer_ui_session_event",
                self._customer_ui_session_event_row,
            ),
            BigQueryPersistence.SessionEventTable(
                f"{self._dataset_name}.agent_session_event",
                "agent_session_event",
                self._agent_session_event_row,
            ),
            BigQueryPersistence.SessionEventTable(
                f"{self._dataset_name}.remote_agent_session_event",
                "remote_agent_session_event",
                self._remote_agent_session_event_row,
            ),
            BigQueryPersistence.SessionEventTable(
                f"{self._dataset_name}.purchase_credits",
                "purchase_credits",
                self._purchase_credits_row,
            ),
            BigQueryPersistence.SessionEventTable(
                f"{self._dataset_name}.cancel_subscription",
                "cancel_subscription",
                self._cancel_subscription_row,
            ),
            BigQueryPersistence.SessionEventTable(
                f"{self._dataset_name}.unschedule_pending_subscription_cancellation",
                "unschedule_pending_subscription_cancellation",
                self._unschedule_pending_subscription_cancellation_row,
            ),
            BigQueryPersistence.SessionEventTable(
                f"{self._dataset_name}.github_user_authorize",
                "github_user_authorize",
                self._github_user_authorize_row,
            ),
            BigQueryPersistence.SessionEventTable(
                f"{self._dataset_name}.remote_agent_workspace_uptime",
                "remote_agent_workspace_uptime",
                self._remote_agent_workspace_uptime_row,
            ),
        ]

        # Add new request event tables here for agent request events.
        self._request_event_tables.append(
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.agent_request_event",
                "agent_request_event",
                self._agent_request_event_row,
            ),
        )

        self._request_event_tables.append(
            BigQueryPersistence.RequestEventTable(
                f"{self._dataset_name}.prompt_cache_usage",
                "prompt_cache_usage",
                self._prompt_cache_usage_row,
            ),
        )

        # Add new tenant event tables here.
        self._tenant_event_tables: list[BigQueryPersistence.TenantEventTable] = [
            BigQueryPersistence.TenantEventTable(
                f"{self._dataset_name}.add_user_to_tenant",
                "add_user_to_tenant",
                self._add_user_to_tenant_row,
            ),
            BigQueryPersistence.TenantEventTable(
                f"{self._dataset_name}.remove_user_from_tenant",
                "remove_user_from_tenant",
                self._user_removed_from_tenant_row,
            ),
            BigQueryPersistence.TenantEventTable(
                f"{self._dataset_name}.invite_user_to_tenant",
                "invite_user_to_tenant",
                self._invite_user_to_tenant_row,
            ),
            BigQueryPersistence.TenantEventTable(
                f"{self._dataset_name}.delete_invitation",
                "delete_invitation",
                self._delete_invitation_row,
            ),
            BigQueryPersistence.TenantEventTable(
                f"{self._dataset_name}.update_subscription",
                "update_subscription",
                self._update_subscription_row,
            ),
            BigQueryPersistence.TenantEventTable(
                f"{self._dataset_name}.create_tenant_for_team",
                "create_tenant_for_team",
                self._create_tenant_for_team_row,
            ),
            BigQueryPersistence.TenantEventTable(
                f"{self._dataset_name}.accept_invitation",
                "accept_invitation",
                self._accept_invitation_row,
            ),
            BigQueryPersistence.TenantEventTable(
                f"{self._dataset_name}.decline_invitation",
                "decline_invitation",
                self._decline_invitation_row,
            ),
        ]

        # Add new generic event tables here.
        self._generic_event_tables: list[BigQueryPersistence.GenericEventTable] = [
            BigQueryPersistence.GenericEventTable(
                f"{self._dataset_name}.recaptcha",
                "recaptcha",
                self._recaptcha_row,
            ),
            BigQueryPersistence.GenericEventTable(
                f"{self._dataset_name}.verisoul",
                "verisoul",
                self._verisoul_row,
            ),
            BigQueryPersistence.GenericEventTable(
                f"{self._dataset_name}.verosint",
                "verosint",
                self._verosint_row,
            ),
        ]

        # Ensure we only have one table per event type.
        event_types = set()
        for table in self._request_event_tables + self._session_event_tables:
            if table.event_type in event_types:
                raise ValueError(
                    f"Duplicate BigQuery table for event type {table.event_type}"
                )
            event_types.add(table.event_type)

    def connect(self):
        """Connect to BigQuery.

        This method blocks until we are able to successfully connect to BigQuery. This is expected
        to take a while the first time request-insight is deployed in a tenant because
        CloudIdentityMemberships are slow to propagate through Google's system (anecdotally
        it can take up to 10 minutes). Arguably we should just crash and let kubernetes restarts
        deal with it, but we don't want to confuse people with a failing deployment for an expected
        issue. We keep track of a metric for containers stuck in this state so that we can page on
        it if it never resolves itself.
        """
        while True:
            try:
                self._bigquery_client = Client()
                for table in (
                    self._request_event_tables
                    + self._session_event_tables
                    + self._tenant_event_tables
                    + self._generic_event_tables
                ):
                    table.connect(self._bigquery_client)
                _dataset_startup_error_gauge.labels("FORBIDDEN").set(0)
                _dataset_startup_error_gauge.labels("NOT_FOUND").set(0)
                break
            except Forbidden as ex:
                _dataset_startup_error_gauge.labels("FORBIDDEN").set(1)
                log.warning(
                    "Forbidden to connect to BigQuery. This is expected the first time "
                    "request-insight is deployed in a namespace. Will retry in 30 seconds. "
                    "Error: %s",
                    ex,
                )
                sleep(30)
            except NotFound as ex:
                _dataset_startup_error_gauge.labels("NOT_FOUND").set(1)
                log.error(
                    "Not found error connecting to BigQuery. This can happen if request-insight is "
                    "deployed before a table creation is completed. This is unexpected, since the "
                    "BigQuery deployments runs with higher priority.  Will retry in 30 seconds. "
                    "Error: %s",
                    ex,
                )
                sleep(30)
            except Exception as ex:  # pylint: disable=broad-exception-caught
                log.error("Unexpected error connecting to BigQuery: %s", ex)
                log.exception(ex)
                raise

    def write(
        self,
        pubsub_messages: Iterable[request_insight_pb2.RequestInsightMessage],
    ):
        """Write the given events to BigQuery.

        Every event type has its own table and is batched together.

        Args:
            pubsub_messages: The list of RequestInsightMessages to write.
        """
        if self._bigquery_client is None:
            raise ValueError(
                "BigQuery client not connected. Did you forget to call connect()?"
            )

        # Gather all the event data.
        # TODO(jacqueline): This code could be simplified if we change request events to have
        #                   TenantInfo in the UpdateRequestInfoRequest proto, analogous to session
        #                   events.
        table_to_rows: dict[
            BigQueryPersistence.RequestEventTable
            | BigQueryPersistence.SessionEventTable
            | BigQueryPersistence.TenantEventTable
            | BigQueryPersistence.GenericEventTable,
            list[dict[str, object]],
        ] = defaultdict(list)
        for message in pubsub_messages:
            match message.WhichOneof("message"):
                case "update_request_info_request":
                    request = message.update_request_info_request
                    for event in request.events:
                        event_type = event.WhichOneof("event")
                        table = next(
                            (
                                t
                                for t in self._request_event_tables
                                if t.event_type == event_type
                            ),
                            None,
                        )
                        if table:
                            row = table.event_to_row_fn(
                                request.request_id,
                                request.session_id,
                                request.tenant_info,
                                event,
                            )
                            table_to_rows[table].append(row)
                case "record_session_events_request":
                    request = message.record_session_events_request
                    for event in request.events:
                        event_type = event.WhichOneof("event")
                        table = next(
                            (
                                t
                                for t in self._session_event_tables
                                if t.event_type == event_type
                            ),
                            None,
                        )
                        if table:
                            row = table.event_to_row_fn(
                                request.session_id,
                                request.opaque_user_id,
                                request.tenant_info,
                                event,
                            )
                            table_to_rows[table].append(row)
                case "record_tenant_events_request":
                    request = message.record_tenant_events_request
                    for event in request.events:
                        event_type = event.WhichOneof("event")
                        table = next(
                            (
                                t
                                for t in self._tenant_event_tables
                                if t.event_type == event_type
                            ),
                            None,
                        )
                        if table:
                            row = table.event_to_row_fn(request.tenant_info, event)
                            table_to_rows[table].append(row)
                case "record_generic_events_request":
                    request = message.record_generic_events_request
                    for event in request.events:
                        event_type = event.WhichOneof("event")
                        table = next(
                            (
                                t
                                for t in self._generic_event_tables
                                if t.event_type == event_type
                            ),
                            None,
                        )
                        if table:
                            row = table.event_to_row_fn(request.session_id, event)
                            table_to_rows[table].append(row)
                case _:
                    log.info("Skipping %s message", message.WhichOneof("message"))

        # Write the data to BigQuery.
        for table, rows in table_to_rows.items():
            num_rows = len(rows)
            log.info(
                "Writing %d %s events to bigquery",
                num_rows,
                table.event_type,
            )
            try:
                if table.table is None:
                    raise ValueError(
                        f"BigQuery table {table.full_table_name} not connected. "
                        "Did you forget to call connect()?"
                    )
                with _bigquery_insert_latency_seconds.labels(table.table).time():
                    insert_errors = self._bigquery_client.insert_rows(table.table, rows)
            except Exception as ex:  # pylint: disable=broad-exception-caught
                # Update metrics/logs if there is an exception
                log.warn(
                    "Exception inserting %d events to bigquery: %s",
                    num_rows,
                    ex,
                )
                log.exception(ex)
                _bigquery_insert_row_count.labels(table.table, "exception").inc(
                    num_rows
                )
            else:
                # Update metrics/logs for the normal case
                num_error_rows = len(insert_errors)
                _bigquery_insert_row_count.labels(table.table, "").inc(
                    num_rows - num_error_rows
                )

                if num_error_rows > 0:
                    log.warn(
                        "Error inserting %d events to bigquery: %s",
                        num_error_rows,
                        insert_errors,
                    )
                    # Group the errors by reason, as described by:
                    # https://cloud.google.com/python/docs/reference/bigquery/latest/google.cloud.bigquery.client.Client#google_cloud_bigquery_client_Client_insert_rows
                    # https://cloud.google.com/bigquery/docs/error-messages
                    for insert_error in insert_errors:
                        # There could be multiple errors for a single row, for
                        # now we just pick one and ignore the others so the
                        # count is correct
                        error_reason = insert_error.get("errors", [{}])[0].get(
                            "reason", "unknown_error"
                        )
                        _bigquery_insert_row_count.labels(
                            table.table, error_reason
                        ).inc()

    def _common_request_event_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
        sanitized_message,
    ) -> dict[str, object]:
        """Returns the common base of a request insight row.

        See services/request_insight/analytics_dataset/schema.jsonnet for the formal schema.
        """
        result = {
            "request_id": request_id,
            "shard_namespace": self._namespace,
            "tenant": tenant_info.tenant_name or self._namespace,
            "tenant_id": tenant_info.tenant_id,
            "time": event.time.ToDatetime(),
            # Convert this to an object rather than a string, to ensure this
            # gets stored as a JSON object and not a JSON string
            "sanitized_json": MessageToDict(
                sanitized_message,
                preserving_proto_field_name=True,
            ),
        }
        if session_id:
            result["session_id"] = session_id
        return result

    def _common_session_event_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
        sanitized_message,
    ) -> dict[str, object]:
        """Returns the common base of a session event row.

        See services/request_insight/analytics_dataset/schema.jsonnet for the formal schema.
        """
        result = {
            "session_id": session_id,
            "shard_namespace": self._namespace,
            "tenant": tenant_info.tenant_name or self._namespace,
            "tenant_id": tenant_info.tenant_id,
            "time": event.time.ToDatetime(),
            # Convert this to an object rather than a string, to ensure this
            # gets stored as a JSON object and not a JSON string
            "sanitized_json": MessageToDict(
                sanitized_message,
                preserving_proto_field_name=True,
            ),
        }
        if opaque_user_id and opaque_user_id.user_id:
            result["opaque_user_id"] = opaque_user_id.user_id
            result["user_id_type"] = auth_entities_pb2.UserId.UserIdType.Name(
                opaque_user_id.user_id_type
            )
        return result

    def _common_tenant_event_row(
        self,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.TenantEvent,
        sanitized_message,
    ) -> dict[str, object]:
        """Returns the common base of a tenant event row.

        See services/request_insight/analytics_dataset/schema.jsonnet for the formal schema.
        """
        return {
            "tenant": tenant_info.tenant_name or self._namespace,
            "tenant_id": tenant_info.tenant_id,
            "time": event.time.ToDatetime(),
            # Convert this to an object rather than a string, to ensure this
            # gets stored as a JSON object and not a JSON string
            "sanitized_json": MessageToDict(
                sanitized_message,
                preserving_proto_field_name=True,
            ),
        }

    def _common_generic_event_row(
        self,
        session_id: str,
        event: request_insight_pb2.GenericEvent,
        sanitized_message,
    ) -> dict[str, object]:
        """Returns the common base of a generic event row.

        See services/request_insight/analytics_dataset/schema.jsonnet for the formal schema.
        """
        return {
            "session_id": session_id,
            "event_id": event.event_id,
            "time": event.time.ToDatetime(),
            # Convert this to an object rather than a string, to ensure this
            # gets stored as a JSON object and not a JSON string
            "sanitized_json": MessageToDict(
                sanitized_message,
                preserving_proto_field_name=True,
            ),
        }

    def _request_metadata_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the request_metadata table."""
        # Request metadata doesn't use the common base schema because we don't want json for this
        # table.
        request_metadata = event.request_metadata
        result = {
            "request_id": request_id,
            "shard_namespace": self._namespace,
            "tenant": tenant_info.tenant_name or self._namespace,
            "tenant_id": tenant_info.tenant_id,
            "time": event.time.ToDatetime(),
            "request_type": request_insight_pb2.RequestType.Name(
                request_metadata.request_type
            ),
            "session_id": request_metadata.session_id,
            "user_id": request_metadata.user_id,
            "user_agent": request_metadata.user_agent,
            "source_ip": request_metadata.source_ip,
        }
        if request_metadata.user_email:
            result["user_email"] = request_metadata.user_email
        if request_metadata.opaque_user_id.user_id:
            result["opaque_user_id"] = request_metadata.opaque_user_id.user_id
            result["user_id_type"] = auth_entities_pb2.UserId.UserIdType.Name(
                request_metadata.opaque_user_id.user_id_type
            )

        return result

    def _completion_resolution_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the completion_resolution table."""
        completion_resolution = event.completion_resolution
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_completion_resolution(completion_resolution),
        )
        base_row["accepted"] = completion_resolution.accepted_idx >= 0
        return base_row

    def _edit_resolution_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the edit_resolution table."""
        edit_resolution = event.edit_resolution
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_edit_resolution(edit_resolution),
        )
        base_row["accepted"] = edit_resolution.is_accepted
        return base_row

    def _instruction_resolution_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the instruction_resolution table."""
        instruction_resolution = event.instruction_resolution
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_instruction_resolution(instruction_resolution),
        )
        return base_row

    def _smart_paste_resolution_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the smart_paste_resolution table."""
        smart_paste_resolution = event.smart_paste_resolution
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_smart_paste_resolution(smart_paste_resolution),
        )
        return base_row

    def _smart_paste_client_timeline_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the smart_paste_timeline table."""
        smart_paste_client_timeline = event.smart_paste_client_timeline
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_smart_paste_client_timeline(smart_paste_client_timeline),
        )
        return base_row

    def _edit_host_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the edit_host_request table."""
        edit_host_request = event.edit_host_request

        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_edit_host_request(edit_host_request),
        )
        base_row["keywords"] = self._get_instruction_keywords(
            edit_host_request.request.instruction
        )
        base_row["categories"] = self._get_instruction_categories(
            edit_host_request.request.instruction
        )
        base_row["path_extension"] = self._get_sanitized_path_extension(
            edit_host_request.request.path
        )
        return base_row

    def _instruction_host_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the instruction_host_request table."""
        instruction_host_request = event.instruction_host_request

        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_instruction_host_request(instruction_host_request),
        )
        # Instruction fields
        if instruction_host_request.request.instruction:
            base_row["categories"] = self._get_instruction_categories(
                instruction_host_request.request.instruction
            )
            base_row["path_extension"] = self._get_sanitized_path_extension(
                instruction_host_request.request.path
            )
        # Smart paste fields
        if instruction_host_request.request.code_block:
            base_row["code_block_character_count"] = len(
                instruction_host_request.request.code_block
            )
            base_row["code_block_line_count"] = len(
                instruction_host_request.request.code_block.splitlines()
            )
            base_row["target_file_path_extension"] = self._get_sanitized_path_extension(
                instruction_host_request.request.target_file_path
            )
            base_row["target_file_content_character_count"] = len(
                instruction_host_request.request.target_file_content
            )
            base_row["target_file_content_line_count"] = len(
                instruction_host_request.request.target_file_content.splitlines()
            )
        return base_row

    def _get_instruction_keywords(self, instruction: str) -> list[str]:
        """Returns a list of keywords in the instruction.

        We can't record the full contents of an instruction from the user because it could contain
        sensitive information, so instead we record whether it contains any interesting keywords.
        """
        result = []

        # Quick fix instructions are a special case that always start with "fix it:".
        if instruction.lower().startswith("fix it:"):
            result.append("quickfix")

        # We're erring on the stricter side with an exact word match for now. If we find there are
        # too many false negatives we can change to something more permissive.
        instruction_words = instruction.lower().split()
        result.extend(
            [word for word in instruction_words if word in self._instruction_keywords]
        )

        return result

    def _get_instruction_categories(self, instruction: str) -> list[str]:
        """Returns a list of categories of the keywords found in the instruction.

        We can't record the full contents of an instruction from the user because it could contain
        sensitive information, so instead we record whether it contains any interesting cateogries
        of keywords.
        """
        categories = []

        # Quick fix instructions are a special case that always start with "fix it:".
        if instruction.lower().startswith("fix it:"):
            categories.append("quickfix")

        message_words = set(instruction.lower().split())
        for category, keywords in self._keyword_categories.items():
            # include the category name in the search
            if set([category] + keywords).intersection(message_words):
                categories.append(category)

        return categories

    def _completion_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the completion_request table.

        Note that this event is referred to as "infer_request" in the protos but
        "completion_request" in BigQuery."""
        infer_request = event.infer_request
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_infer_request(infer_request),
        )
        base_row["session_id"] = infer_request.session_id
        base_row["user_id"] = infer_request.user_id
        base_row["path_keywords"] = self._get_path_keywords(infer_request.path)
        base_row["path_extension"] = self._get_sanitized_path_extension(
            infer_request.path
        )
        return base_row

    def _get_path_keywords(self, path: str) -> list[str]:
        """Returns a list of keywords in the path.

        We can't record full paths because they're considered sensitive customer data, so instead we
        record whether it contains any interesting keywords.
        """
        return [k for k in self._path_keywords if k in path.lower()]

    def _edit_emit_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the edit_emit table."""
        edit_emit = event.edit_emit
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_edit_emit(edit_emit),
        )
        return base_row

    def _instruction_emit_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the instruction_emit table."""
        instruction_emit = event.instruction_emit
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_instruction_emit(instruction_emit),
        )
        return base_row

    def _completion_emit_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the completion_emit table."""
        completion_emit = event.completion_emit
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_completion_emit(completion_emit),
        )
        return base_row

    def _completion_host_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the completion_host_request table."""
        completion_host_request = event.completion_host_request
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_completion_host_request(completion_host_request),
        )
        return base_row

    def _edit_host_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the edit_host_response table."""
        edit_host_response = event.edit_host_response

        # We can't export the text of the edit response itself,
        # but we're interested in the (non-empty) line count
        text = edit_host_response.response.text
        lines = [line for line in text.splitlines() if line.strip()]

        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_edit_host_response(edit_host_response),
        )
        base_row["character_count"] = len(text)
        base_row["line_count"] = len(lines)
        base_row["unknown_blobs_count"] = len(
            edit_host_response.response.unknown_blob_names
        )
        return base_row

    def _instruction_host_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the instruction_host_response table."""
        instruction_host_response = event.instruction_host_response
        # We can't export the text of the edit response itself,
        # but we're interested in the (non-empty) line count
        text = "\n".join(
            [
                replace_text_block.text
                for replace_text_block in instruction_host_response.response.replace_text
            ]
        )
        lines = [line for line in text.splitlines() if line.strip()]
        instruction_host_response = event.instruction_host_response
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_instruction_host_response(instruction_host_response),
        )
        base_row["replace_text_character_count"] = len(text)
        base_row["replace_text_line_count"] = len(lines)
        base_row["unknown_blobs_count"] = len(
            instruction_host_response.response.unknown_blob_names
        )
        return base_row

    def _completion_host_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the completion_host_response table."""
        completion_host_response = event.completion_host_response

        # We can't export the text of the completion itself, but we're interested in the line count.
        # Count non-empty lines.
        text = completion_host_response.text
        lines = [line for line in text.splitlines() if line.strip()]

        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_completion_host_response(completion_host_response),
        )
        base_row["character_count"] = len(completion_host_response.text)
        base_row["line_count"] = len(lines)
        base_row["unknown_blobs_count"] = len(
            completion_host_response.unknown_blob_names
        )
        return base_row

    def _retrieval_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the retrieval_response table."""
        retrieval_response = event.retrieval_response
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_retrieval_response(retrieval_response),
        )
        base_row["retrieval_type"] = request_insight_pb2.RetrievalType.Name(
            retrieval_response.retrieval_type
        )
        base_row["retrieved_chunk_count"] = len(retrieval_response.retrieved_chunks)
        return base_row

    def _completion_feedback_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the completion_feedback table."""
        completion_feedback = event.completion_feedback
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_completion_feedback(completion_feedback),
        )
        base_row["has_note"] = completion_feedback.note.strip() != ""
        base_row["note"] = completion_feedback.note.strip()
        return base_row

    def _chat_feedback_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the chat_feedback table."""
        chat_feedback = event.chat_feedback
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_chat_feedback(chat_feedback),
        )
        base_row["has_note"] = chat_feedback.note.strip() != ""
        base_row["note"] = chat_feedback.note.strip()
        return base_row

    def _agent_feedback_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the agent_feedback table."""
        agent_feedback = event.agent_feedback
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_agent_feedback(agent_feedback),
        )
        base_row["has_note"] = agent_feedback.note.strip() != ""
        base_row["note"] = agent_feedback.note.strip()
        return base_row

    def _remote_agent_feedback_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agent_feedback table."""
        remote_agent_feedback = event.remote_agent_feedback
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_remote_agent_feedback(remote_agent_feedback),
        )
        base_row["has_note"] = remote_agent_feedback.note.strip() != ""
        base_row["note"] = remote_agent_feedback.note.strip()
        return base_row

    def _inference_host_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the inference_host_response table."""
        inference_host_response = event.inference_host_response
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_inference_host_response(inference_host_response),
        )
        return base_row

    def _api_http_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the api_http_response table."""
        api_http_response = event.api_http_response
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_api_http_response(api_http_response),
        )
        return base_row

    def _embeddings_search_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the embeddings_search_request table."""
        embeddings_search_request = event.embeddings_search_request
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_embeddings_search_request(embeddings_search_request),
        )
        return base_row

    def _embeddings_search_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the embeddings_search_response table."""
        embeddings_search_response = event.embeddings_search_response
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_embeddings_search_response(embeddings_search_response),
        )
        base_row["missing_blobs_count"] = len(
            embeddings_search_response.missing_blob_names
        )
        return base_row

    def _extension_error_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the extension_error table."""
        extension_error = event.extension_error
        sanitized, additional_columns = _sanitize_extension_error(extension_error)
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            sanitized,
        )
        base_row.update(additional_columns)
        return base_row

    def _chat_host_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the chat_host_request table."""
        chat_host_request = event.chat_host_request
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_chat_host_request(chat_host_request),
        )
        selected_code = chat_host_request.request.selected_code
        selected_code_lines = [
            line for line in selected_code.splitlines() if line.strip()
        ]
        base_row["selected_code_character_count"] = len(selected_code)
        base_row["selected_code_line_count"] = len(selected_code_lines)
        base_row["message_keywords"] = self._get_chat_keywords(
            chat_host_request.request.message
        )
        base_row["message_categories"] = self._get_chat_categories(
            chat_host_request.request.message
        )
        base_row["path_extension"] = self._get_sanitized_path_extension(
            chat_host_request.request.path
        )
        base_row["masked_external_source_ids"] = sanitize_external_source_ids(
            chat_host_request.request.external_source_ids
        )

        sanitized_node_types = self._sanitize_chat_nodes(
            chat_host_request.request.nodes
        )

        base_row["text_node_count"] = sanitized_node_types.count("TEXT")
        base_row["tool_result_node_count"] = sanitized_node_types.count("TOOL_RESULT")
        base_row["image_node_count"] = sanitized_node_types.count("IMAGE")
        return base_row

    def _sanitize_chat_nodes(
        self, nodes: Sequence[chat_pb2.ChatRequestNode]
    ) -> list[str]:
        """Returns a list of sanitized chat nodes."""
        return [chat_pb2.ChatRequestNodeType.Name(node.type) for node in nodes]

    def _get_chat_keywords(self, message: str) -> list[str]:
        """Returns a list of keywords in the chat message.

        We can't record full messages because they're considered sensitive customer data, so instead
        we record whether they contain any interesting keywords.
        """
        message_words = message.lower().split()
        return [word for word in message_words if word in self._instruction_keywords]

    def _get_chat_categories(self, message: str) -> list[str]:
        """Returns a list of categories of the keywords found in the chat message.

        We can't record full messages because they're considered sensitive customer data, so instead
        we record whether they contain any interesting categories of keywords.
        """
        categories = []
        message_words = set(message.lower().split())
        for category, keywords in self._keyword_categories.items():
            # include the category name in the search
            if set([category] + keywords).intersection(message_words):
                categories.append(category)

        return categories

    def _chat_host_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the chat_host_response table."""
        chat_host_response = event.chat_host_response

        # We can't export the text of the chat response itself, but we're interested in the
        # character/line count. Count non-empty lines.
        text = chat_host_response.response.text
        lines = [line for line in text.splitlines() if line.strip()]

        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_chat_host_response(chat_host_response),
        )
        base_row["character_count"] = len(text)
        base_row["line_count"] = len(lines)
        base_row["unknown_blobs_count"] = len(
            chat_host_response.response.unknown_blob_names
        )
        base_row["masked_incorporated_external_source_ids"] = (
            sanitize_external_source_ids(
                [
                    source.source_id
                    for source in chat_host_response.response.incorporated_external_sources
                ]
            )
        )
        return base_row

    def _get_sanitized_path_extension(self, path: str) -> str | None:
        """Returns the file extension of the path, if it is in the allowlisted set of extensions.

        We only export allowlisted extensions to guard against weird extensions that could be
        sensitive information.
        """
        extension = Path(path.lower()).suffix
        return extension if extension in self._file_extensions else None

    def _next_edit_host_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the next_edit_host_request table."""
        next_edit_host_request = event.next_edit_host_request
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_next_edit_host_request(next_edit_host_request),
        )
        base_row["token_count"] = 0
        return base_row

    def _next_edit_host_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the next_edit_host_response table."""
        next_edit_host_response = event.next_edit_host_response
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_next_edit_host_response(next_edit_host_response),
        )
        base_row["character_counts"] = [
            len(suggestion.result.suggested_edit.suggested_code)
            for suggestion in next_edit_host_response.suggestions
        ]
        base_row["unknown_blobs_count"] = max(
            [
                len(suggestion.result.unknown_blob_names)
                for suggestion in next_edit_host_response.suggestions
            ]
            or [0]
        )
        return base_row

    def _next_edit_feedback_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the next_edit_feedback table."""
        next_edit_feedback = event.next_edit_feedback
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_next_edit_feedback(next_edit_feedback),
        )
        base_row["has_note"] = next_edit_feedback.note.strip() != ""
        base_row["note"] = next_edit_feedback.note.strip()
        return base_row

    def _slackbot_feedback_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the slackbot_feedback table."""
        slackbot_feedback = event.slackbot_feedback
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_slackbot_feedback(slackbot_feedback),
        )
        base_row["slack_response_timestamp"] = (
            slackbot_feedback.slack_response_timestamp
        )
        base_row["slack_channel_id"] = slackbot_feedback.slack_channel_id
        base_row["note"] = slackbot_feedback.note.strip()
        return base_row

    def _extension_session_event_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the extension_session_event table."""
        extension_session_event = event.extension_session_event
        base_row = self._common_session_event_row(
            session_id,
            opaque_user_id,
            tenant_info,
            event,
            _sanitize_extension_session_event(extension_session_event),
        )
        return base_row

    def _next_edit_session_event_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the next_edit_session_event table."""
        next_edit_session_event = event.next_edit_session_event
        base_row = self._common_session_event_row(
            session_id,
            opaque_user_id,
            tenant_info,
            event,
            _sanitize_next_edit_session_event(next_edit_session_event),
        )
        return base_row

    def _tool_use_data_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the tool_use_data table.

        This method processes tool usage events and ensures proper type handling for the BigQuery schema,
        particularly ensuring tool_run_duration_ms remains an integer.

        Args:
            request_id: The ID of the request associated with this tool use event.
            tenant_info: Information about the tenant making the request.
            event: The RequestEvent containing the tool use data.

        Returns:
            A dictionary containing the processed row data ready for BigQuery insertion, with the following
            structure:
            {
                "request_id": str,
                "tenant": str,
                "tenant_id": str,
                "shard_namespace": str,
                "time": datetime,
                "sanitized_json": {
                    "tool_name": str,
                    "tool_use_id": str,
                    "tool_output_is_error": bool,
                    "tool_run_duration_ms": int,
                }
            }
        """
        tool_use_data = event.tool_use_data
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_tool_use_data(tool_use_data),
        )

        def prefix_to_export(full_command):
            """Returns the prefix of the command that we should export, if any.

            Should return either None, or the longest match from the allowlist in self._terminal_command_prefixes.
            """
            if not full_command:
                return None
            longest_match = None
            for prefix in self._terminal_command_prefixes:
                if full_command.startswith(prefix):
                    if longest_match is None or len(prefix) > len(longest_match):
                        longest_match = prefix
            return longest_match

        terminal_tools = ["launch-process", "shell"]
        if tool_use_data.tool_name in terminal_tools:
            try:
                tool_input = json.loads(tool_use_data.tool_input)
            except json.JSONDecodeError:
                tool_input = {}
            full_command = tool_input.get("command", None)
            prefix = prefix_to_export(full_command)
            if prefix:
                base_row["terminal_command_prefix"] = prefix

        return base_row

    def _onboarding_session_event_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the onboarding_session_event table."""
        onboarding_session_event = event.onboarding_session_event
        base_row = self._common_session_event_row(
            session_id,
            opaque_user_id,
            tenant_info,
            event,
            _sanitize_onboarding_session_event(onboarding_session_event),
        )
        return base_row

    def _client_metric_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the client_metric table."""
        client_metric = event.client_metric
        base_row = self._common_session_event_row(
            session_id,
            opaque_user_id,
            tenant_info,
            event,
            _sanitize_client_metric(client_metric),
        )
        return base_row

    def _feature_vector_report_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the feature_vector_report table."""
        feature_vector_report = event.feature_vector_report
        base_row = self._common_session_event_row(
            session_id,
            opaque_user_id,
            tenant_info,
            event,
            _sanitize_feature_vector_report(feature_vector_report),
        )
        base_row["feature_vector"] = dict(feature_vector_report.feature_vector)
        base_row["source_ip"] = feature_vector_report.source_ip
        return base_row

    def _slackbot_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the slackbot_request table."""
        slackbot_request = event.slackbot_request
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_slackbot_request(slackbot_request),
        )
        return base_row

    def _slackbot_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the slackbot_response table."""
        slackbot_response = event.slackbot_response
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_slackbot_response(slackbot_response),
        )
        return base_row

    def _completion_post_process_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the completion_post_process table."""
        completion_post_process = event.completion_post_process
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_post_process_completion(completion_post_process),
        )
        return base_row

    def _parenthesis_truncation_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the parenthesis_truncation table."""
        parenthesis_truncation = event.parenthesis_truncation
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_parenthesis_truncation_data(parenthesis_truncation),
        )
        base_row["original_length"] = len(parenthesis_truncation.original_text)
        base_row["truncated_length"] = len(parenthesis_truncation.truncated_text)
        base_row["path_extension"] = self._get_sanitized_path_extension(
            parenthesis_truncation.path
        )
        return base_row

    def _share_save_chat_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the save_chat_request table."""
        request = event.share_save_chat_request
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_share_save_chat_request(request),
        )
        return base_row

    def _share_save_chat_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the completion_post_process table."""
        response = event.share_save_chat_response
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_share_save_chat_response(response),
        )
        return base_row

    def _share_get_chat_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the completion_post_process table."""
        request = event.share_get_chat_request
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_share_get_chat_request(request),
        )
        return base_row

    def _share_get_chat_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the get_chat_response table."""
        response = event.share_get_chat_response
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_share_get_chat_response(response),
        )
        return base_row

    def _github_app_installation_event_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the github_app_installation_event table."""
        github_app_installation_event = event.github_app_installation_event
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_github_app_installation_event(github_app_installation_event),
        )
        return base_row

    def _slackbot_installation_event_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the slackbot_installation_event table."""
        slackbot_installation_event = event.slackbot_installation_event
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_slackbot_installation_event(slackbot_installation_event),
        )
        return base_row

    def _add_user_to_tenant_row(
        self,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.TenantEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the add_user_to_tenant table."""
        add_user_to_tenant = event.add_user_to_tenant
        row = self._common_tenant_event_row(
            tenant_info,
            event,
            _sanitize_add_user_to_tenant(add_user_to_tenant),
        )
        row["augment_user_id"] = add_user_to_tenant.user.id
        row["user_email"] = add_user_to_tenant.user.email
        return row

    def _user_removed_from_tenant_row(
        self,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.TenantEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remove_user_from_tenant table."""
        remove_user_from_tenant = event.remove_user_from_tenant
        row = self._common_tenant_event_row(
            tenant_info,
            event,
            _sanitize_user_removed_from_tenant(remove_user_from_tenant),
        )
        row["augment_user_id"] = remove_user_from_tenant.user.id
        row["user_email"] = remove_user_from_tenant.user.email
        return row

    def _invite_user_to_tenant_row(
        self,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.TenantEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the invite_user_to_tenant table."""
        invite_user_to_tenant = event.invite_user_to_tenant
        row = self._common_tenant_event_row(
            tenant_info,
            event,
            _sanitize_invite_user_to_tenant(invite_user_to_tenant),
        )
        row["inviter_email"] = invite_user_to_tenant.invitation.inviter_email
        row["invitee_email"] = invite_user_to_tenant.invitation.invitee_email
        return row

    def _delete_invitation_row(
        self,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.TenantEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the delete_invitation table."""
        row = self._common_tenant_event_row(
            tenant_info,
            event,
            _sanitize_delete_invitation(event.delete_invitation),
        )
        return row

    def _update_subscription_row(
        self,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.TenantEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the update_subscription table."""
        row = self._common_tenant_event_row(
            tenant_info,
            event,
            _sanitize_update_subscription(event.update_subscription),
        )
        return row

    def _create_tenant_for_team_row(
        self,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.TenantEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the create_tenant_for_team table."""
        row = self._common_tenant_event_row(
            tenant_info,
            event,
            _sanitize_create_tenant_for_team(event.create_tenant_for_team),
        )
        return row

    def _accept_invitation_row(
        self,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.TenantEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the accept_invitation table."""
        row = self._common_tenant_event_row(
            tenant_info,
            event,
            _sanitize_accept_invitation(event.accept_invitation),
        )
        row["invitee_email"] = event.accept_invitation.user.email
        return row

    def _decline_invitation_row(
        self,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.TenantEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the decline_invitation table."""
        row = self._common_tenant_event_row(
            tenant_info,
            event,
            _sanitize_decline_invitation(event.decline_invitation),
        )
        row["invitee_email"] = event.decline_invitation.invitee_email
        return row

    def _glean_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the glean_request table."""
        glean_request = event.glean_request
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_glean_request(glean_request),
        )
        user_query = glean_request.request.query
        base_row["user_query_character_count"] = len(user_query)
        base_row["user_query_line_count"] = len(user_query.splitlines())
        base_row["user_query_word_count"] = len(user_query.split())
        return base_row

    def _glean_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the glean_response table."""
        glean_response = event.glean_response
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_glean_response(glean_response),
        )
        base_row["has_oauth_url"] = bool(glean_response.response.oauth_url)
        base_row["generated_search_query_count"] = len(
            glean_response.generate_search_queries_response
        )
        base_row["generated_search_query_words"] = [
            len(query.split())
            for query in glean_response.generate_search_queries_response
        ]
        base_row["result_statistics"] = []

        def get_doc_stats(doc: glean_pb2.Document) -> dict[str, Any]:
            """Return the statistics for a document.

            This includes nested document count, character count,
            line count and snippet count.
            """
            text = doc.content or ""
            stats = {
                "nested_document_count": 1,
                "character_count": len(text),
                "line_count": len(text.splitlines()),
                "snippet_count": len(doc.snippets),
            }
            for child in doc.children:
                counters = get_doc_stats(child)
                for key, value in counters.items():
                    stats[key] += value
            return stats

        for item in glean_response.response.documents:
            source = item.data_source_name
            stats = get_doc_stats(item)
            created_at = item.created_at.ToDatetime()
            if created_at.year < 1980:
                created_at = None
            updated_at = item.updated_at.ToDatetime()
            if updated_at.year < 1980:
                updated_at = None
            stats.update(
                {
                    "document_id": item.document_id,
                    "source": source,
                    "created_at": created_at,
                    "updated_at": updated_at,
                    "obfuscated_author_id": item.author.id,
                }
            )
            base_row["result_statistics"].append(stats)
        log.info(f"Sanitized glean response: {base_row}")
        return base_row

    def _glean_oauth_url_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the glean_oauth_url_request table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            request_insight_pb2.RIGleanOAuthURLRequest(),
        )
        return base_row

    def _glean_oauth_url_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the glean_oauth_url_response table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            request_insight_pb2.RIGleanOAuthURLResponse(),
        )
        return base_row

    def _customer_ui_session_event_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the customer_ui_session_event table."""
        base_row = self._common_session_event_row(
            session_id,
            opaque_user_id,
            tenant_info,
            event,
            _sanitize_customer_ui_session_event(event.customer_ui_session_event),
        )
        return base_row

    def _agent_session_event_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the agent_session_event table."""
        base_row = self._common_session_event_row(
            session_id,
            opaque_user_id,
            tenant_info,
            event,
            _sanitize_agent_session_event(event.agent_session_event),
        )
        return base_row

    def _agent_request_event_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the agent_request_event table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_agent_request_event(event.agent_request_event),
        )
        return base_row

    def _remote_agent_session_event_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agent_session_event table."""
        base_row = self._common_session_event_row(
            session_id,
            opaque_user_id,
            tenant_info,
            event,
            _sanitize_remote_agent_session_event(event.remote_agent_session_event),
        )
        return base_row

    def _prompt_cache_usage_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the prompt_cache_usage table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_prompt_cache_usage(event.prompt_cache_usage),
        )
        return base_row

    def _remote_tool_call_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_tool_call_request table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_remote_tool_call_request(event.remote_tool_call_request),
        )
        return base_row

    def _remote_tool_call_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_tool_call_response table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_remote_tool_call_response(event.remote_tool_call_response),
        )

        def _get_response_size_bytes(
            response: request_insight_pb2.RIRemoteToolCallResponse,
        ) -> int:
            """Returns the size of the tool response in bytes."""
            if response.HasField("edit_file_response"):
                return len(
                    response.edit_file_response.modified_file_contents.encode("utf-8")
                )
            if response.HasField("codebase_retrieval_response"):
                return len(
                    response.codebase_retrieval_response.formatted_retrieval.encode(
                        "utf-8"
                    )
                )
            if response.HasField("run_remote_tool_response"):
                return len(
                    response.run_remote_tool_response.tool_output.encode("utf-8")
                )

            # This shouldn't happen, but if the `response` field isn't we'll just say the
            # response was empty. We'll be able to detect that `response` was unset by
            # looking at the JSON field (should have no set keys).
            return 0

        base_row["tool_response_size_bytes"] = _get_response_size_bytes(
            event.remote_tool_call_response
        )
        return base_row

    def _request_blocked_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the request_blocked table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            # Don't bother with sanitized_json for this message since we pull everything out into
            # columns.
            request_insight_pb2.RequestBlocked(),
        )
        if event.request_blocked.opaque_user_id:
            base_row["opaque_user_id"] = event.request_blocked.opaque_user_id.user_id
            base_row["user_id_type"] = auth_entities_pb2.UserId.UserIdType.Name(
                event.request_blocked.opaque_user_id.user_id_type
            )
        if event.request_blocked.user_email:
            base_row["user_email"] = event.request_blocked.user_email
        if event.request_blocked.check_type:
            base_row["check_type"] = event.request_blocked.check_type
        return base_row

    def _request_suspicious_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the request_suspicious table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            # Don't bother with sanitized_json for this message since we pull everything out into
            # columns.
            request_insight_pb2.RequestSuspicious(),
        )
        if event.request_suspicious.opaque_user_id:
            base_row["opaque_user_id"] = event.request_suspicious.opaque_user_id.user_id
            base_row["user_id_type"] = auth_entities_pb2.UserId.UserIdType.Name(
                event.request_suspicious.opaque_user_id.user_id_type
            )
        if event.request_suspicious.user_email:
            base_row["user_email"] = event.request_suspicious.user_email
        if event.request_suspicious.check_type:
            base_row["check_type"] = event.request_suspicious.check_type
        return base_row

    def _token_exchange_error_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the token_exchange_error table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            # Don't bother with sanitized_json for this message since we pull everything out into
            # columns.
            request_insight_pb2.TokenExchangeError(),
        )
        if event.token_exchange_error.opaque_user_id:
            base_row["opaque_user_id"] = (
                event.token_exchange_error.opaque_user_id.user_id
            )
            base_row["user_id_type"] = auth_entities_pb2.UserId.UserIdType.Name(
                event.token_exchange_error.opaque_user_id.user_id_type
            )
        if event.token_exchange_error.user_email:
            base_row["user_email"] = event.token_exchange_error.user_email
        if event.token_exchange_error.reason:
            base_row["reason"] = request_insight_pb2.TokenExchangeError.Reason.Name(
                event.token_exchange_error.reason
            )
        return base_row

    def _daily_request_limit_exceeded_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the token_exchange_error table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            # Don't bother with sanitized_json for this message since we pull everything out into
            # columns.
            request_insight_pb2.DailyRequestLimitExceeded(),
        )
        if event.daily_request_limit_exceeded.opaque_user_id:
            base_row["opaque_user_id"] = (
                event.daily_request_limit_exceeded.opaque_user_id.user_id
            )
            base_row["user_id_type"] = auth_entities_pb2.UserId.UserIdType.Name(
                event.daily_request_limit_exceeded.opaque_user_id.user_id_type
            )
        if event.daily_request_limit_exceeded.limit:
            base_row["limit"] = event.daily_request_limit_exceeded.limit
        return base_row

    def _chat_user_message_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the chat_user_message table."""
        chat_user_message = event.chat_user_message
        sanitized_message = _sanitize_chat_user_message(chat_user_message)
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            sanitized_message,
        )
        base_row["chat_mode"] = request_insight_pb2.ChatUserMessage.ChatMode.Name(
            chat_user_message.chat_mode
        )
        base_row["chat_history_length"] = chat_user_message.chat_history_length
        base_row["image_node_count"] = chat_user_message.image_node_count
        base_row["character_count"] = chat_user_message.character_count
        base_row["line_count"] = chat_user_message.line_count

        try:
            if (
                chat_user_message.opaque_user_id
                and chat_user_message.opaque_user_id.user_id
            ):
                base_row["opaque_user_id"] = chat_user_message.opaque_user_id.user_id
                base_row["user_id_type"] = auth_entities_pb2.UserId.UserIdType.Name(
                    chat_user_message.opaque_user_id.user_id_type
                )
        except (AttributeError, ValueError):
            pass

        return base_row

    def _find_missing_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the find_missing table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            # Don't bother with sanitized_json for this message since we pull everything out into
            # columns.
            request_insight_pb2.RIFindMissing(),
        )
        if event.find_missing.model_name:
            base_row["model_name"] = event.find_missing.model_name
        base_row["blob_count"] = event.find_missing.blob_count
        base_row["missing_count"] = event.find_missing.missing_count
        base_row["nonindexed_count"] = event.find_missing.nonindexed_count
        return base_row

    def _batch_upload_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the batch_upload table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            # Don't bother with sanitized_json for this message since we pull everything out into
            # columns.
            request_insight_pb2.RIBatchUpload(),
        )
        base_row["blob_count"] = event.batch_upload.blob_count
        base_row["total_size"] = event.batch_upload.total_size
        return base_row

    def _remote_agents_create_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_create_request table."""
        return self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_remote_agents_create_request(event.remote_agents_create_request),
        )

    def _remote_agents_create_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_create_response table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_remote_agents_create_response(
                event.remote_agents_create_response
            ),
        )
        if event.remote_agents_create_response.HasField(
            "response"
        ) and event.remote_agents_create_response.response.HasField("agent"):
            base_row["agent_id"] = (
                event.remote_agents_create_response.response.agent.remote_agent_id
            )
        return base_row

    def _remote_agents_chat_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_chat_request table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_remote_agents_chat_request(event.remote_agents_chat_request),
        )
        if event.remote_agents_chat_request.HasField("request"):
            base_row["agent_id"] = (
                event.remote_agents_chat_request.request.remote_agent_id
            )
        return base_row

    def _remote_agents_chat_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_chat_response table."""
        return self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            request_insight_pb2.RemoteAgentsChatResponse(),
        )

    def _remote_agents_interrupt_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_interrupt_request table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_remote_agents_interrupt_request(
                event.remote_agents_interrupt_request
            ),
        )
        if event.remote_agents_interrupt_request.HasField("request"):
            base_row["agent_id"] = (
                event.remote_agents_interrupt_request.request.remote_agent_id
            )
        return base_row

    def _remote_agents_interrupt_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_interrupt_response table."""
        return self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            request_insight_pb2.RemoteAgentsInterruptResponse(),
        )

    def _remote_agents_delete_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_delete_request table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_remote_agents_delete_request(event.remote_agents_delete_request),
        )
        if event.remote_agents_delete_request.HasField("request"):
            base_row["agent_id"] = (
                event.remote_agents_delete_request.request.remote_agent_id
            )
        return base_row

    def _remote_agents_delete_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_delete_response table."""
        return self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            request_insight_pb2.RemoteAgentsDeleteResponse(),
        )

    def _remote_agents_add_ssh_key_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_add_ssh_key_request table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_remote_agents_add_ssh_key_request(
                event.remote_agents_add_ssh_key_request
            ),
        )
        if event.remote_agents_add_ssh_key_request.HasField("request"):
            base_row["agent_id"] = (
                event.remote_agents_add_ssh_key_request.request.remote_agent_id
            )
        return base_row

    def _remote_agents_add_ssh_key_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_add_ssh_key_response table."""
        return self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            request_insight_pb2.RemoteAgentsAddSSHKeyResponse(),
        )

    def _remote_agents_pause_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_pause_request table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_remote_agents_pause_request(event.remote_agents_pause_request),
        )
        if event.remote_agents_pause_request.HasField("request"):
            base_row["agent_id"] = (
                event.remote_agents_pause_request.request.remote_agent_id
            )
        return base_row

    def _remote_agents_pause_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_pause_response table."""
        return self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            request_insight_pb2.RemoteAgentsPauseResponse(),
        )

    def _remote_agents_resume_request_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_resume_request table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_remote_agents_resume_request(event.remote_agents_resume_request),
        )
        if event.remote_agents_resume_request.HasField("request"):
            base_row["agent_id"] = (
                event.remote_agents_resume_request.request.remote_agent_id
            )
        return base_row

    def _remote_agents_resume_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_resume_response table."""
        return self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            request_insight_pb2.RemoteAgentsResumeResponse(),
        )

    def _recaptcha_row(
        self,
        session_id: str,
        event: request_insight_pb2.GenericEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the recaptcha table."""
        row = self._common_generic_event_row(
            session_id,
            event,
            _sanitize_recaptcha_event(event.recaptcha),
        )
        row["email"] = event.recaptcha.email
        row["source_ip"] = event.recaptcha.source_ip
        return row

    def _verisoul_row(
        self,
        session_id: str,
        event: request_insight_pb2.GenericEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the verisoul table."""
        row = self._common_generic_event_row(
            session_id,
            event,
            _sanitize_verisoul(event.verisoul),
        )
        if event.verisoul.opaque_user_id:
            row["opaque_user_id"] = event.verisoul.opaque_user_id.user_id
            row["user_id_type"] = auth_entities_pb2.UserId.UserIdType.Name(
                event.verisoul.opaque_user_id.user_id_type
            )
        try:
            row["report"] = json.loads(event.verisoul.report)
        except json.JSONDecodeError:
            row["report"] = {"raw_report": event.verisoul.report}

        return row

    def _verosint_row(
        self,
        session_id: str,
        event: request_insight_pb2.GenericEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the verosint table."""
        row = self._common_generic_event_row(
            session_id,
            event,
            _sanitize_verosint(event.verosint),
        )
        if event.verosint.opaque_user_id:
            row["opaque_user_id"] = event.verosint.opaque_user_id.user_id
            row["user_id_type"] = auth_entities_pb2.UserId.UserIdType.Name(
                event.verosint.opaque_user_id.user_id_type
            )
        try:
            row["report"] = json.loads(event.verosint.report)
        except json.JSONDecodeError:
            row["report"] = {"raw_report": event.verosint.report}

        return row

    def _purchase_credits_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the purchase_credits table."""
        return self._common_session_event_row(
            session_id,
            opaque_user_id,
            tenant_info,
            event,
            _sanitize_purchase_credits(event.purchase_credits),
        )

    def _cancel_subscription_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the cancel_subscription table."""
        return self._common_session_event_row(
            session_id,
            opaque_user_id,
            tenant_info,
            event,
            _sanitize_cancel_subscription(event.cancel_subscription),
        )

    def _unschedule_pending_subscription_cancellation_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the unschedule_pending_subscription_cancellation table."""
        return self._common_session_event_row(
            session_id,
            opaque_user_id,
            tenant_info,
            event,
            _sanitize_unschedule_pending_subscription_cancellation(
                event.unschedule_pending_subscription_cancellation
            ),
        )

    def _github_user_authorize_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the github_user_authorize table."""
        github_user_authorize = event.github_user_authorize
        row = self._common_session_event_row(
            session_id,
            opaque_user_id,
            tenant_info,
            event,
            request_insight_pb2.GithubUserAuthorize(),
        )
        row["github_user_id"] = github_user_authorize.github_user_id
        return row

    def _remote_agent_workspace_uptime_row(
        self,
        session_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.SessionEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agent_workspace_uptime table."""
        remote_agent_workspace_uptime = event.remote_agent_workspace_uptime
        row = self._common_session_event_row(
            session_id,
            opaque_user_id,
            tenant_info,
            event,
            # Since we expand uptime_start and uptime_end into their own columns,
            # we don't need to include them in the sanitized_json.
            request_insight_pb2.RemoteAgentWorkspaceUptime(),
        )
        row["uptime_start"] = remote_agent_workspace_uptime.uptime_start.ToDatetime()
        row["uptime_end"] = remote_agent_workspace_uptime.uptime_end.ToDatetime()
        return row


def _sanitize_completion_resolution(
    completion_resolution: request_insight_pb2.CompletionResolution,
) -> request_insight_pb2.CompletionResolution:
    """Returns a CompletionResolution with sensitive fields removed."""
    sanitized = request_insight_pb2.CompletionResolution()
    sanitized.accepted_idx = completion_resolution.accepted_idx
    return sanitized


def _sanitize_edit_resolution(
    completion_resolution: request_insight_pb2.EditResolution,
) -> request_insight_pb2.EditResolution:
    """Returns a CompletionResolution with sensitive fields removed."""
    sanitized = request_insight_pb2.EditResolution()
    sanitized.is_accepted = completion_resolution.is_accepted
    return sanitized


def _sanitize_instruction_resolution(
    instruction_resolution: request_insight_pb2.InstructionResolution,
) -> request_insight_pb2.InstructionResolution:
    """Returns a InstructionResolution with sensitive fields removed."""
    sanitized = request_insight_pb2.InstructionResolution()
    sanitized.is_accepted_chunks.extend(instruction_resolution.is_accepted_chunks)
    sanitized.is_accept_all = instruction_resolution.is_accept_all
    sanitized.is_reject_all = instruction_resolution.is_reject_all
    return sanitized


def _sanitize_smart_paste_resolution(
    smart_paste_resolution: request_insight_pb2.SmartPasteResolution,
) -> request_insight_pb2.SmartPasteResolution:
    """Returns a SmartPasteResolution with sensitive fields removed."""
    sanitized = request_insight_pb2.SmartPasteResolution()
    sanitized.is_accepted_chunks.extend(smart_paste_resolution.is_accepted_chunks)
    sanitized.is_accept_all = smart_paste_resolution.is_accept_all
    sanitized.is_reject_all = smart_paste_resolution.is_reject_all
    return sanitized


def _sanitize_smart_paste_client_timeline(
    smart_paste_timeline: request_insight_pb2.SmartPasteClientTimeline,
) -> request_insight_pb2.SmartPasteClientTimeline:
    """Returns a SmartPasteClientTimeline with sensitive fields removed."""
    sanitized = request_insight_pb2.SmartPasteClientTimeline()
    sanitized.initial_request_time.CopyFrom(smart_paste_timeline.initial_request_time)
    sanitized.stream_finish_time.CopyFrom(smart_paste_timeline.stream_finish_time)
    sanitized.apply_time.CopyFrom(smart_paste_timeline.apply_time)
    return sanitized


def _sanitize_infer_request(
    infer_request: request_insight_pb2.InferRequest,
) -> request_insight_pb2.InferRequest:
    """Returns an InferRequest with sensitive fields removed."""
    sanitized = request_insight_pb2.InferRequest()
    sanitized.model = infer_request.model
    sanitized.top_k = infer_request.top_k
    sanitized.top_p = infer_request.top_p
    sanitized.temperature = infer_request.temperature
    sanitized.max_tokens = infer_request.max_tokens
    sanitized.session_id = infer_request.session_id
    sanitized.lang = infer_request.lang
    sanitized.user_agent = infer_request.user_agent
    sanitized.sequence_id = infer_request.sequence_id
    if infer_request.HasField("user_filter_threshold"):
        sanitized.user_filter_threshold = infer_request.user_filter_threshold

    # Excluded sensitive fields: prompt, path, suffix
    # Excluded PII: user_id
    # Excluded because it's a lot of data: memories
    return sanitized


def _sanitize_blobs(
    blobs: blob_names_pb2.Blobs,
) -> blob_names_pb2.Blobs:
    """Returns a Blobs with sensitive fields removed."""
    sanitized = blob_names_pb2.Blobs()
    sanitized.baseline_checkpoint_id = blobs.baseline_checkpoint_id
    sanitized.added.extend(blobs.added)
    sanitized.deleted.extend(blobs.deleted)
    return sanitized


def _sanitize_edit_host_request(
    edit_host_request: request_insight_pb2.RIEditRequest,
) -> request_insight_pb2.RIEditRequest:
    """Returns an EditRequest with sensitive fields removed."""
    sanitized_request = edit_pb2.EditRequest(
        model_name=edit_host_request.request.model_name,
        lang=edit_host_request.request.lang,
    )
    if edit_host_request.request.HasField("blobs"):
        sanitized_request.blobs.CopyFrom(
            _sanitize_blobs(edit_host_request.request.blobs)
        )
    sanitized_request.position.CopyFrom(
        edit_pb2.EditPosition(
            prefix_begin=edit_host_request.request.position.prefix_begin,
            suffix_end=edit_host_request.request.position.suffix_end,
            blob_name=edit_host_request.request.position.blob_name,
        )
    )
    # Excluded sensitive fields: prefix, path, suffix, selected_text, instruction, tokenization
    return request_insight_pb2.RIEditRequest(request=sanitized_request)


def _sanitize_instruction_host_request(
    instruction_host_request: request_insight_pb2.RIInstructionRequest,
) -> request_insight_pb2.RIInstructionRequest:
    """Returns an InstructionRequest with sensitive fields removed."""
    sanitized_request = edit_pb2.InstructionRequest(
        model_name=instruction_host_request.request.model_name,
        lang=instruction_host_request.request.lang,
    )
    if instruction_host_request.request.HasField("blobs"):
        sanitized_request.blobs.CopyFrom(
            _sanitize_blobs(instruction_host_request.request.blobs)
        )
    sanitized_request.position.CopyFrom(
        edit_pb2.EditPosition(
            prefix_begin=instruction_host_request.request.position.prefix_begin,
            suffix_end=instruction_host_request.request.position.suffix_end,
            blob_name=instruction_host_request.request.position.blob_name,
        )
    )
    # Excluded sensitive fields: prefix, path, suffix, target_file_path, target_file_content, code_block, tokenization
    return request_insight_pb2.RIInstructionRequest(request=sanitized_request)


def _sanitize_edit_emit(
    _edit_emit: request_insight_pb2.EditEmit,
) -> request_insight_pb2.EditEmit:
    """Returns an EditEmit with sensitive fields removed."""
    sanitized = request_insight_pb2.EditEmit()
    return sanitized


def _sanitize_instruction_emit(
    _instruction_emit: request_insight_pb2.InstructionEmit,
) -> request_insight_pb2.InstructionEmit:
    """Returns an InstructionEmit with sensitive fields removed."""
    sanitized = request_insight_pb2.InstructionEmit()
    return sanitized


def _sanitize_completion_emit(
    _completion_emit: request_insight_pb2.CompletionEmit,
) -> request_insight_pb2.CompletionEmit:
    """Returns a CompletionEmit with sensitive fields removed."""
    sanitized = request_insight_pb2.CompletionEmit()
    return sanitized


def _sanitize_completion_host_request(
    completion_host_request: request_insight_pb2.CompletionHostRequest,
) -> request_insight_pb2.CompletionHostRequest:
    """Returns a CompletionHostRequest with sensitive fields removed."""
    sanitized = request_insight_pb2.CompletionHostRequest()
    sanitized.seed = completion_host_request.seed
    sanitized.eos_token_id.extend(completion_host_request.eos_token_id)
    sanitized.top_k = completion_host_request.top_k
    sanitized.top_p = completion_host_request.top_p
    sanitized.temperature = completion_host_request.temperature
    sanitized.output_len = completion_host_request.output_len
    sanitized.enable_path_prefix = completion_host_request.enable_path_prefix
    sanitized.enable_fill_in_the_middle = (
        completion_host_request.enable_fill_in_the_middle
    )
    sanitized.enable_bm25 = completion_host_request.enable_bm25
    sanitized.enable_dense_retrieval = completion_host_request.enable_dense_retrieval
    sanitized.model = completion_host_request.model
    sanitized.blob_names.extend(completion_host_request.blob_names)
    if completion_host_request.HasField("blobs"):
        sanitized.blobs.CopyFrom(_sanitize_blobs(completion_host_request.blobs))
    sanitized.lang = completion_host_request.lang
    sanitized.position.CopyFrom(
        request_insight_pb2.CompletionHostRequestPosition(
            prefix_begin=completion_host_request.position.prefix_begin,
            cursor_position=completion_host_request.position.cursor_position,
            suffix_end=completion_host_request.position.suffix_end,
        )
    )
    sanitized.probe_only = completion_host_request.probe_only
    if completion_host_request.HasField("user_filter_threshold"):
        sanitized.user_filter_threshold = completion_host_request.user_filter_threshold

    # Excluded sensitive fields: prefix, path, suffix, tokenization
    return sanitized


def _sanitize_edit_host_response(
    edit_host_response: request_insight_pb2.RIEditResponse,
) -> request_insight_pb2.RIEditResponse:
    """Returns an EditResponse with sensitive fields removed."""
    # Excluded sensitive field: text
    sanitized = request_insight_pb2.RIEditResponse()
    sanitized.response.checkpoint_not_found = (
        edit_host_response.response.checkpoint_not_found
    )
    # Excluded due to size: unknown_blob_names
    return sanitized


def _sanitize_instruction_host_response(
    instruction_host_response: request_insight_pb2.RIInstructionResponse,
) -> request_insight_pb2.RIInstructionResponse:
    """Returns an InstructionResponse with sensitive fields removed."""
    sanitized = request_insight_pb2.RIInstructionResponse()
    sanitized.response.checkpoint_not_found = (
        instruction_host_response.response.checkpoint_not_found
    )

    # Excluded sensitive fields: replace_text
    # Excluded due to size: unknown_blob_names
    return sanitized


def _sanitize_completion_host_response(
    completion_host_response: request_insight_pb2.CompletionHostResponse,
) -> request_insight_pb2.CompletionHostResponse:
    """Returns a CompletionHostResponse with sensitive fields removed."""
    sanitized = request_insight_pb2.CompletionHostResponse()
    sanitized.checkpoint_not_found = completion_host_response.checkpoint_not_found

    # Excluded sensitive fields: text, skipped_suffix, suffix_replacement_text, tokenization
    # Excluded due to size: unknown_blob_names
    return sanitized


def _sanitize_retrieval_response(
    retrieval_response: request_insight_pb2.RetrievalResponse,
) -> request_insight_pb2.RetrievalResponse:
    """Returns a RetrievalResponse with sensitive fields removed."""
    sanitized = request_insight_pb2.RetrievalResponse()
    sanitized.retrieval_type = retrieval_response.retrieval_type

    # Excluded sensitive fields: query_prompt
    return sanitized


def _sanitize_inference_host_response(
    inference_host_response: request_insight_pb2.InferenceHostResponse,
) -> request_insight_pb2.InferenceHostResponse:
    """Returns a InferenceHostResponse with sensitive fields removed."""
    sanitized = request_insight_pb2.InferenceHostResponse()
    sanitized.cum_log_probs = inference_host_response.cum_log_probs

    # Excluded sensitive fields: tokenization
    return sanitized


def _sanitize_api_http_response(
    api_http_response: request_insight_pb2.ApiHttpResponse,
) -> request_insight_pb2.ApiHttpResponse:
    """Returns a ApiHttpResponse with sensitive fields removed."""
    sanitized = request_insight_pb2.ApiHttpResponse()
    sanitized.code = api_http_response.code
    sanitized.error_message = api_http_response.error_message

    return sanitized


def _sanitize_embeddings_search_request(
    embeddings_search_request: request_insight_pb2.EmbeddingsSearchRequest,
) -> request_insight_pb2.EmbeddingsSearchRequest:
    """Returns an EmbeddingsSearchRequest with sensitive fields removed."""
    sanitized = request_insight_pb2.EmbeddingsSearchRequest()
    sanitized.num_results = embeddings_search_request.num_results
    sanitized.transformation_key = embeddings_search_request.transformation_key
    sanitized.sub_key = embeddings_search_request.sub_key

    return sanitized


def _sanitize_embeddings_search_response(
    embeddings_search_response: request_insight_pb2.EmbeddingsSearchResponse,
) -> request_insight_pb2.EmbeddingsSearchResponse:
    """Returns a EmbeddingsSearchResponse with sensitive fields removed."""
    sanitized = request_insight_pb2.EmbeddingsSearchResponse()
    sanitized.results.extend(
        map(
            lambda chunk: request_insight_pb2.EmbeddingsSearchResult(
                blob_name=chunk.blob_name,
                chunk_index=chunk.chunk_index,
                value=chunk.value,
            ),
            embeddings_search_response.results,
        )
    )

    # Excluded due to size: missing_blob_names
    return sanitized


def _sanitize_extension_error(
    extension_error: request_insight_pb2.ExtensionError,
) -> tuple[request_insight_pb2.ExtensionError, dict[str, Any]]:
    """Returns a ExtensionError with sensitive fields removed."""
    sanitized = request_insight_pb2.ExtensionError()
    sanitized.original_request_id = extension_error.original_request_id

    # Excluded sensitive fields:
    #   - message: not supposed to be sensitive, but excluded just in case.
    #       We don't expect to use it for anything in bigtable anyway.
    #   - stack_trace: ditto
    #   - diagnostics: assumed to be sensitive, e.g. filesystem metadata

    additional_columns = {}
    if extension_error.message == "chat_stream_failed":
        sanitized.message = "chat_stream_failed"
        pattern = re.compile(
            r"""(?P<safe_substring>(Type)?Error:\s+ (
            Cancelled
            | fetch\ failed
            | terminated
            | HTTP\ error:\ [0-9]{3}
            | The\ operation\ was\ aborted\ due\ to\ timeout
        ))""",
            re.VERBOSE,
        )
        m = pattern.search(extension_error.stack_trace)
        if m:
            additional_columns["sanitized_error_type"] = m.group("safe_substring")
        for key in [
            "message_timestamp_ms",
            "extension_timestamp_ms",
            "last_chunk_timestamp_ms",
            "error_timestamp_ms",
            "chunks_received",
        ]:
            val = extension_error.diagnostics.get(key, "")
            if val.isdigit():
                sanitized.diagnostics[key] = val
    elif re.match(
        r"^Request timed out: [a-z\-]+, id: [0-9a-f\-]+$", extension_error.message
    ):
        sanitized.message = extension_error.message
    return sanitized, additional_columns


def _sanitize_glean_request(
    glean_request: request_insight_pb2.RIGleanRequest,
) -> request_insight_pb2.RIGleanRequest:
    """Returns a GleanRequest with sensitive fields removed."""
    sanitized = request_insight_pb2.RIGleanRequest()
    sanitized.request_source = glean_request.request_source
    sanitized.query_processor_config.gcp_project_id = (
        glean_request.query_processor_config.gcp_project_id
    )
    sanitized.query_processor_config.gcp_region = (
        glean_request.query_processor_config.gcp_region
    )
    sanitized.query_processor_config.model_name = (
        glean_request.query_processor_config.model_name
    )
    sanitized.query_processor_config.temperature = (
        glean_request.query_processor_config.temperature
    )
    sanitized.query_processor_config.max_output_tokens = (
        glean_request.query_processor_config.max_output_tokens
    )
    sanitized.query_processor_config.max_results = (
        glean_request.query_processor_config.max_results
    )
    sanitized.request.include_private_docs = glean_request.request.include_private_docs
    sanitized.request.user_id = glean_request.request.user_id
    return sanitized


def _sanitize_glean_document(
    glean_document: glean_pb2.Document,
) -> glean_pb2.Document:
    """Returns a Glean document with sensitive fields removed."""
    sanitized = glean_pb2.Document()
    sanitized.document_id = glean_document.document_id
    sanitized.document_type = glean_document.document_type
    sanitized.data_source = glean_document.data_source
    sanitized.data_source_name = glean_document.data_source_name
    sanitized.visibility = glean_document.visibility
    sanitized.created_at.FromDatetime(glean_document.created_at.ToDatetime())
    updated_at = glean_document.updated_at.ToDatetime()
    if updated_at.year < 1980:
        updated_at = glean_document.created_at.ToDatetime()
    sanitized.updated_at.FromDatetime(updated_at)

    sanitized.author.id = glean_document.author.id

    # Children are also sanitized
    for child in glean_document.children:
        sanitized.children.append(_sanitize_glean_document(child))

    # Snippets are sanitized too, keeping only ranges
    for snippet in glean_document.snippets:
        sanitized_snippet = glean_pb2.Document.Snippet()
        sanitized_snippet.document_id = snippet.document_id
        sanitized_snippet.start_range = snippet.start_range
        sanitized_snippet.end_range = snippet.end_range
        sanitized.snippets.append(sanitized_snippet)
    return sanitized


def _sanitize_glean_response(
    glean_response: request_insight_pb2.RIGleanResponse,
) -> request_insight_pb2.RIGleanResponse:
    """Returns a GleanResponse with sensitive fields removed."""
    sanitized = request_insight_pb2.RIGleanResponse()

    # Copy out the raw response and sanitize the content
    for doc in glean_response.response.documents:
        sanitized.response.documents.append(_sanitize_glean_document(doc))
    return sanitized


def _sanitize_completion_feedback(
    completion_feedback: request_insight_pb2.CompletionFeedback,
) -> request_insight_pb2.CompletionFeedback:
    """Returns a CompletionFeedback with sensitive fields removed."""
    sanitized = request_insight_pb2.CompletionFeedback()
    sanitized.rating = completion_feedback.rating
    # Excluded sensitive fields:
    #     - note: May contain PII and/or sensitive information like code.
    return sanitized


def _sanitize_chat_feedback(
    chat_feedback: request_insight_pb2.ChatFeedback,
) -> request_insight_pb2.ChatFeedback:
    """Returns a ChatFeedback with sensitive fields removed."""
    sanitized = request_insight_pb2.ChatFeedback()
    sanitized.rating = chat_feedback.rating
    # Excluded sensitive fields:
    #     - note: May contain PII and/or sensitive information like code.
    return sanitized


def _sanitize_chat_user_message(
    chat_user_message: request_insight_pb2.ChatUserMessage,
) -> request_insight_pb2.ChatUserMessage:
    """Returns a ChatUserMessage with sensitive fields removed.

    Preserves analytics-relevant fields while removing any potentially sensitive content.
    """
    sanitized = request_insight_pb2.ChatUserMessage()
    sanitized.chat_mode = chat_user_message.chat_mode
    sanitized.chat_history_length = chat_user_message.chat_history_length
    sanitized.image_node_count = chat_user_message.image_node_count
    sanitized.character_count = chat_user_message.character_count
    sanitized.line_count = chat_user_message.line_count

    # Copy opaque_user_id if present
    if chat_user_message.HasField("opaque_user_id"):
        sanitized.opaque_user_id.CopyFrom(chat_user_message.opaque_user_id)

    # Excluded sensitive fields: message, external_source_ids
    return sanitized


def _sanitize_agent_feedback(
    agent_feedback: request_insight_pb2.AgentFeedback,
) -> request_insight_pb2.AgentFeedback:
    """Returns an AgentFeedback with sensitive fields removed."""
    sanitized = request_insight_pb2.AgentFeedback()
    sanitized.rating = agent_feedback.rating
    # Excluded sensitive fields:
    #     - note: May contain PII and/or sensitive information like code.
    return sanitized


def _sanitize_remote_agent_feedback(
    remote_agent_feedback: request_insight_pb2.RemoteAgentFeedback,
) -> request_insight_pb2.RemoteAgentFeedback:
    """Returns a RemoteAgentFeedback with sensitive fields removed."""
    sanitized = request_insight_pb2.RemoteAgentFeedback()
    sanitized.rating = remote_agent_feedback.rating
    # Excluded sensitive fields:
    #     - note: May contain PII and/or sensitive information like code.
    return sanitized


def _sanitize_chat_host_request(
    chat_host_request: request_insight_pb2.RIChatRequest,
) -> request_insight_pb2.RIChatRequest:
    """Returns an RIChatRequest with sensitive fields removed."""
    sanitized = request_insight_pb2.RIChatRequest()
    sanitized.request_source = chat_host_request.request_source

    sanitized_request = chat_pb2.ChatRequest()
    sanitized_request.model_name = chat_host_request.request.model_name
    sanitized_request.chat_history.extend(
        map(
            # Excluded sensitive fields: request_message, response_text
            lambda exchange: chat_pb2.Exchange(request_id=exchange.request_id),
            chat_host_request.request.chat_history,
        )
    )
    sanitized_request.position.prefix_begin = (
        chat_host_request.request.position.prefix_begin
    )
    sanitized_request.position.suffix_end = (
        chat_host_request.request.position.suffix_end
    )
    sanitized_request.position.blob_name = chat_host_request.request.position.blob_name
    sanitized_request.lang = chat_host_request.request.lang
    sanitized_request.sequence_id = chat_host_request.request.sequence_id
    sanitized_request.user_guided_blobs.extend(
        chat_host_request.request.user_guided_blobs
    )
    for blob in chat_host_request.request.blobs:
        sanitized_request.blobs.add().CopyFrom(_sanitize_blobs(blob))
    context_rid = chat_host_request.request.context_code_exchange_request_id
    if context_rid == "new" or _is_valid_uuid(context_rid):
        sanitized_request.context_code_exchange_request_id = context_rid

    # Excluded sensitive fields: path, prefix, selected_code, suffix, message
    sanitized.request.CopyFrom(sanitized_request)

    # Copy over the silent field
    sanitized.request.silent = chat_host_request.request.silent

    # Excluded sensitive fields: tokenization
    return sanitized


def _sanitize_chat_host_response(
    chat_host_response: request_insight_pb2.RIChatResponse,
) -> request_insight_pb2.RIChatResponse:
    """Returns an RIChatResponse with sensitive fields removed."""
    sanitized = request_insight_pb2.RIChatResponse()
    sanitized.response.checkpoint_not_found = (
        chat_host_response.response.checkpoint_not_found
    )
    if chat_host_response.response.HasField("stop_reason"):
        sanitized.response.stop_reason = chat_host_response.response.stop_reason

    # Excluded sensitive fields: response.text, tokenization
    # Excluded due to size: unknown_blob_names
    return sanitized


def _sanitize_working_directory_change(
    working_directory_change: next_edit_pb2.WorkingDirectoryChange,
) -> next_edit_pb2.WorkingDirectoryChange:
    sanitized = next_edit_pb2.WorkingDirectoryChange()
    sanitized.change_type = working_directory_change.change_type
    sanitized.head_blob_name = working_directory_change.head_blob_name
    sanitized.indexed_blob_name = working_directory_change.indexed_blob_name
    sanitized.current_blob_name = working_directory_change.current_blob_name
    # Exclude sensitive fields: before_path, after_path
    return sanitized


def _sanitize_replacement_text(
    replacement_text: completion_pb2.ReplacementText,
) -> completion_pb2.ReplacementText:
    """Returns a ReplacementText with sensitive fields removed."""
    sanitized = completion_pb2.ReplacementText()
    sanitized.blob_name = replacement_text.blob_name
    sanitized.char_start = replacement_text.char_start
    sanitized.char_end = replacement_text.char_end
    sanitized.present_in_blob = replacement_text.present_in_blob
    sanitized.expected_blob_name = replacement_text.expected_blob_name
    # Exclude sensitive fields: path, replacement_text
    return sanitized


def _sanitize_next_edit_host_request(
    next_exit_host_request: request_insight_pb2.RINextEditRequest,
) -> request_insight_pb2.RINextEditRequest:
    """Returns an RINextEditRequest with sensitive fields removed."""
    sanitized = request_insight_pb2.RINextEditRequest()
    sanitized_request = next_edit_pb2.NextEditRequest()
    sanitized_request.model_name = next_exit_host_request.request.model_name
    sanitized_request.sequence_id = next_exit_host_request.request.sequence_id
    sanitized_request.lang = next_exit_host_request.request.lang
    sanitized_request.blobs.CopyFrom(
        _sanitize_blobs(next_exit_host_request.request.blobs)
    )
    sanitized_vcs_change = next_edit_pb2.VCSChange()
    sanitized_vcs_change.working_directory_changes.extend(
        [
            _sanitize_working_directory_change(wdc)
            for wdc in next_exit_host_request.request.vcs_change.working_directory_changes
        ]
    )
    sanitized_request.recent_changes.extend(
        [
            _sanitize_replacement_text(replacement_text)
            for replacement_text in next_exit_host_request.request.recent_changes
        ]
    )
    sanitized_request.vcs_change.CopyFrom(sanitized_vcs_change)
    sanitized_request.blob_name = next_exit_host_request.request.blob_name
    sanitized_request.selection_begin_char = (
        next_exit_host_request.request.selection_begin_char
    )
    sanitized_request.selection_end_char = (
        next_exit_host_request.request.selection_end_char
    )
    sanitized_request.mode = next_exit_host_request.request.mode
    sanitized_request.scope = next_exit_host_request.request.scope
    # Excluded sensitive fields: tokenization, instruction, path, prefix, selected_code, suffix, diagnostics
    sanitized.request.CopyFrom(sanitized_request)
    return sanitized


def _sanitize_diff_span(
    diff_spans: next_edit_pb2.DiffSpan,
) -> next_edit_pb2.DiffSpan:
    """Returns a DiffSpan with sensitive fields removed."""
    sanitized = next_edit_pb2.DiffSpan()
    sanitized.original.start = diff_spans.original.start
    sanitized.original.stop = diff_spans.original.stop
    sanitized.updated.start = diff_spans.updated.start
    sanitized.updated.stop = diff_spans.updated.stop
    return sanitized


def _sanitize_scored_file_hunk(
    scored_file_hunk: next_edit_pb2.ScoredFileHunk,
) -> next_edit_pb2.ScoredFileHunk:
    sanitized = next_edit_pb2.ScoredFileHunk()
    sanitized.blob_name = scored_file_hunk.blob_name
    sanitized.char_start = scored_file_hunk.char_start
    sanitized.char_end = scored_file_hunk.char_end
    sanitized.localization_score = scored_file_hunk.localization_score
    sanitized.editing_score = scored_file_hunk.editing_score
    sanitized.truncation_char = scored_file_hunk.truncation_char
    sanitized.diff_spans.extend(
        [_sanitize_diff_span(diff_span) for diff_span in scored_file_hunk.diff_spans]
    )
    # Exclude sensitive fields: path, existing_code, suggested_code, change_description
    return sanitized


def _sanitize_retrieval_chunk(
    retrieval_chunk: request_insight_pb2.RetrievalChunk,
) -> request_insight_pb2.RetrievalChunk:
    """Returns a RetrievalChunk with sensitive fields removed."""
    sanitized = request_insight_pb2.RetrievalChunk()
    sanitized.blob_name = retrieval_chunk.blob_name
    sanitized.chunk_index = retrieval_chunk.chunk_index
    sanitized.char_offset = retrieval_chunk.char_offset
    sanitized.char_end = retrieval_chunk.char_end
    sanitized.score = retrieval_chunk.score
    # exclude sensitive fields: text, path, origin
    return sanitized


def _sanitize_next_edit_host_response_generation(
    generation: request_insight_pb2.RINextEditGeneration,
) -> request_insight_pb2.RINextEditGeneration:
    """Returns an RINextEditGeneration with sensitive fields removed."""
    sanitized = request_insight_pb2.RINextEditGeneration()
    sanitized.generation_id = generation.generation_id
    sanitized.retrieved_chunks.extend(
        map(
            _sanitize_retrieval_chunk,
            generation.retrieved_chunks,
        )
    )
    # Exclude sensitive fields: generation_prompt, generation_output
    return sanitized


def _sanitize_next_edit_host_response_suggestion(
    suggestion: request_insight_pb2.RINextEditSuggestion,
) -> request_insight_pb2.RINextEditSuggestion:
    """Returns an RINextEditSuggestion with sensitive fields removed."""
    sanitized = request_insight_pb2.RINextEditSuggestion()
    sanitized.generation_id = suggestion.generation_id
    sanitized.result.suggested_edit.CopyFrom(
        _sanitize_scored_file_hunk(suggestion.result.suggested_edit)
    )
    sanitized.result.checkpoint_not_found = suggestion.result.checkpoint_not_found
    # Exclude sensitive fields: description_prompt, description_output
    # Excluded due to size: unknown_blob_names
    return sanitized


def _sanitize_next_edit_host_response(
    next_edit_host_response: request_insight_pb2.RINextEditResponse,
) -> request_insight_pb2.RINextEditResponse:
    """Returns an RINextEditResponse with sensitive fields removed."""
    sanitized = request_insight_pb2.RINextEditResponse()

    sanitized.retrieved_locations.extend(
        map(
            _sanitize_retrieval_chunk,
            next_edit_host_response.retrieved_locations,
        )
    )
    sanitized.generation.extend(
        map(
            _sanitize_next_edit_host_response_generation,
            next_edit_host_response.generation,
        )
    )
    sanitized.suggestions.extend(
        map(
            _sanitize_next_edit_host_response_suggestion,
            next_edit_host_response.suggestions,
        )
    )
    return sanitized


def _sanitize_next_edit_feedback(
    next_edit_feedback: request_insight_pb2.NextEditFeedback,
) -> request_insight_pb2.NextEditFeedback:
    """Returns a NextEditFeedback with sensitive fields removed."""
    sanitized = request_insight_pb2.NextEditFeedback()
    sanitized.rating = next_edit_feedback.rating
    # Excluded sensitive fields:
    #     - note: May contain PII and/or sensitive information like code.
    return sanitized


def _sanitize_extension_session_event(
    extension_session_event: request_insight_pb2.ExtensionSessionEvent,
) -> request_insight_pb2.ExtensionSessionEvent:
    """Returns a ExtensionSessionEvent with sensitive fields removed."""
    sanitized = request_insight_pb2.ExtensionSessionEvent()
    sanitized.event_name = extension_session_event.event_name
    sanitized.user_agent = extension_session_event.user_agent
    sanitized.additional_data.extend(extension_session_event.additional_data)
    return sanitized


def _sanitize_next_edit_session_event(
    next_edit_session_event: request_insight_pb2.NextEditSessionEvent,
) -> request_insight_pb2.NextEditSessionEvent:
    """Returns a NextEditSessionEvent with sensitive fields removed."""
    sanitized = request_insight_pb2.NextEditSessionEvent()
    sanitized.event_name = next_edit_session_event.event_name
    sanitized.user_agent = next_edit_session_event.user_agent

    # Check HasField for optional fields to avoid writing default values to the JSON.
    if next_edit_session_event.HasField("event_source"):
        sanitized.event_source = next_edit_session_event.event_source
    if next_edit_session_event.HasField("related_request_id"):
        sanitized.related_request_id = next_edit_session_event.related_request_id
    if next_edit_session_event.HasField("related_suggestion_id"):
        sanitized.related_suggestion_id = next_edit_session_event.related_suggestion_id
    return sanitized


def _sanitize_onboarding_session_event(
    onboarding_session_event: request_insight_pb2.OnboardingSessionEvent,
) -> request_insight_pb2.OnboardingSessionEvent:
    """Returns a OnboardingSessionEvent with sensitive fields removed."""
    sanitized = request_insight_pb2.OnboardingSessionEvent()
    sanitized.event_name = onboarding_session_event.event_name
    sanitized.user_agent = onboarding_session_event.user_agent
    return sanitized


def _sanitize_client_metric(
    client_metric: request_insight_pb2.ClientMetric,
) -> request_insight_pb2.ClientMetric:
    """Returns a ClientMetric with sensitive fields removed."""
    sanitized = request_insight_pb2.ClientMetric()
    sanitized.event_name = client_metric.event_name
    sanitized.user_agent = client_metric.user_agent
    sanitized.client_metric = client_metric.client_metric
    sanitized.value = client_metric.value
    return sanitized


def _sanitize_feature_vector_report(
    feature_vector_report: request_insight_pb2.FeatureVectorReport,
) -> request_insight_pb2.FeatureVectorReport:
    """Returns a FeatureVectorReport with sensitive fields removed."""
    sanitized = request_insight_pb2.FeatureVectorReport()
    sanitized.user_agent = feature_vector_report.user_agent
    for k, v in feature_vector_report.headers.items():
        if k.lower() in [
            "accept",
            "accept-language",
            "accept-encoding",
            "accept-charset",
            "content-type",
        ]:
            sanitized.headers[k] = v
    return sanitized


def _sanitize_slackbot_request(
    slackbot_request: request_insight_pb2.RISlackbotRequest,
) -> request_insight_pb2.RISlackbotRequest:
    """Returns a RISlackbotRequest with sensitive fields removed."""
    sanitized = request_insight_pb2.RISlackbotRequest()
    sanitized.channel_type = slackbot_request.channel_type
    sanitized.slack_event.metadata.CopyFrom(
        _sanitize_slackbot_event_metadata(slackbot_request.slack_event.metadata)
    )
    sanitized.slack_event.event_type = slackbot_request.slack_event.event_type
    if slackbot_request.slack_event.HasField("app_mention"):
        sanitized.slack_event.app_mention.CopyFrom(
            _sanitize_slackbot_app_mention_event(
                slackbot_request.slack_event.app_mention
            )
        )
    if slackbot_request.slack_event.HasField("message"):
        sanitized.slack_event.message.CopyFrom(
            _sanitize_slackbot_message_event(slackbot_request.slack_event.message)
        )
    if slackbot_request.slack_event.HasField("reaction_added"):
        sanitized.slack_event.reaction_added.CopyFrom(
            _sanitize_slackbot_reaction_added_event(
                slackbot_request.slack_event.reaction_added
            )
        )
    return sanitized


def _sanitize_slackbot_event_metadata(
    slackbot_event_metadata: slack_event_pb2.EventMetadata,
) -> slack_event_pb2.EventMetadata:
    """Returns a EventMetadata with sensitive fields removed."""
    sanitized = slack_event_pb2.EventMetadata()
    sanitized.team_id = slackbot_event_metadata.team_id
    sanitized.enterprise_id = slackbot_event_metadata.enterprise_id

    # Excluded tenant_id, request_id, and tenant_name because they're redundant.
    return sanitized


def _sanitize_slackbot_app_mention_event(
    app_mention_event: slack_event_pb2.AppMentionEvent,
) -> slack_event_pb2.AppMentionEvent:
    """Returns a AppMentionEvent with sensitive fields removed."""
    sanitized = slack_event_pb2.AppMentionEvent()
    sanitized.user = app_mention_event.user  # User ids from Slack aren't PII.
    sanitized.timestamp = app_mention_event.timestamp
    sanitized.thread_timestamp = app_mention_event.thread_timestamp
    sanitized.channel = app_mention_event.channel
    sanitized.event_timestamp = app_mention_event.event_timestamp
    sanitized.user_team = app_mention_event.user_team
    sanitized.source_team = app_mention_event.source_team

    # Excluded restricted fields: text
    return sanitized


def _sanitize_slackbot_message_event(
    message_event: slack_event_pb2.MessageEvent,
) -> slack_event_pb2.MessageEvent:
    """Returns a MessageEvent with sensitive fields removed."""
    sanitized = slack_event_pb2.MessageEvent()
    sanitized.client_msg_id = message_event.client_msg_id
    sanitized.user = message_event.user  # User ids from Slack aren't PII.
    sanitized.thread_timestamp = message_event.thread_timestamp
    sanitized.timestamp = message_event.timestamp
    sanitized.channel = message_event.channel
    sanitized.channel_type = message_event.channel_type
    sanitized.event_timestamp = message_event.event_timestamp
    sanitized.user_team = message_event.user_team
    sanitized.source_team = message_event.source_team
    sanitized.subtype = message_event.subtype

    # Excluded restricted fields: text
    return sanitized


def _sanitize_slackbot_reaction_added_event(
    reaction_added_event: slack_event_pb2.ReactionAddedEvent,
) -> slack_event_pb2.ReactionAddedEvent:
    """Returns a ReactionAddedEvent with sensitive fields removed."""
    sanitized = slack_event_pb2.ReactionAddedEvent()
    sanitized.user = reaction_added_event.user  # User ids from Slack aren't PII.
    sanitized.item_user = (
        reaction_added_event.item_user
    )  # User ids from Slack aren't PII.
    sanitized.event_timestamp = reaction_added_event.event_timestamp
    sanitized.item_type = reaction_added_event.item_type
    sanitized.item_channel = reaction_added_event.item_channel
    sanitized.reaction = reaction_added_event.reaction
    sanitized.item_timestamp = reaction_added_event.item_timestamp
    return sanitized


def _sanitize_slackbot_response(
    slackbot_response: request_insight_pb2.RISlackbotResponse,
) -> request_insight_pb2.RISlackbotResponse:
    """Returns a RISlackbotResponse with sensitive fields removed."""
    sanitized = request_insight_pb2.RISlackbotResponse()
    sanitized.channel = slackbot_response.channel
    sanitized.thread_timestamp = slackbot_response.thread_timestamp
    sanitized.request_message_timestamp = slackbot_response.request_message_timestamp
    for event in slackbot_response.response_events:
        sanitized_event = request_insight_pb2.RISlackbotResponse.Event()
        sanitized_event.time.CopyFrom(event.time)
        if event.HasField("post_message"):
            sanitized_event.post_message.CopyFrom(
                _sanitize_slackbot_response_post_message(event.post_message)
            )
        elif event.HasField("update_message"):
            sanitized_event.update_message.CopyFrom(
                _sanitize_slackbot_response_update_message(event.update_message)
            )
        sanitized.response_events.append(sanitized_event)
    log.info(f"Sanitized slackbot response: {MessageToDict(sanitized)}")
    return sanitized


def _sanitize_slackbot_response_post_message(
    post_message: request_insight_pb2.RISlackbotResponse.PostMessage,
) -> request_insight_pb2.RISlackbotResponse.PostMessage:
    """Returns a PostMessage with sensitive fields removed."""
    sanitized = request_insight_pb2.RISlackbotResponse.PostMessage()
    sanitized.response_message_timestamp = post_message.response_message_timestamp

    # Excluded sensitive fields: text
    return sanitized


def _sanitize_slackbot_response_update_message(
    update_message: request_insight_pb2.RISlackbotResponse.UpdateMessage,
) -> request_insight_pb2.RISlackbotResponse.UpdateMessage:
    """Returns a UpdateMessage with sensitive fields removed."""
    sanitized = request_insight_pb2.RISlackbotResponse.UpdateMessage()
    sanitized.response_message_timestamp = update_message.response_message_timestamp

    # Excluded sensitive fields: text
    return sanitized


def _sanitize_slackbot_feedback(
    slackbot_feedback: request_insight_pb2.SlackbotFeedback,
) -> request_insight_pb2.SlackbotFeedback:
    """Returns a SlackbotFeedback with sensitive fields removed."""
    sanitized = request_insight_pb2.SlackbotFeedback()
    sanitized.rating = slackbot_feedback.rating
    # Excluded sensitive fields:
    #     - note: May contain PII and/or sensitive information like code.
    return sanitized


def _sanitize_post_process_completion(
    post_process_completion: request_insight_pb2.CompletionPostProcess,
) -> request_insight_pb2.CompletionPostProcess:
    """Returns a CompletionPostProcess with sensitive fields removed."""
    sanitized = request_insight_pb2.CompletionPostProcess()
    sanitized.filter_score = post_process_completion.filter_score
    if post_process_completion.HasField("applied_filter_threshold"):
        sanitized.applied_filter_threshold = (
            post_process_completion.applied_filter_threshold
        )
    if post_process_completion.HasField("filter_reason"):
        sanitized.filter_reason = post_process_completion.filter_reason

    return sanitized


def _sanitize_parenthesis_truncation_data(
    truncation_data: request_insight_pb2.ParenthesisTruncation,
) -> request_insight_pb2.ParenthesisTruncation:
    """Returns a TruncationData with sensitive fields removed."""
    sanitized = request_insight_pb2.ParenthesisTruncation()
    sanitized.was_truncated = truncation_data.was_truncated
    sanitized.could_have_truncated = truncation_data.could_have_truncated

    # Excluded sensitive fields: original_text, truncated_text, path
    return sanitized


def _sanitize_share_save_chat_request(
    request: request_insight_pb2.RIShareSaveChatRequest,
) -> request_insight_pb2.RIShareSaveChatRequest:
    """Returns a RIShareSaveChatRequest event with sensitive fields removed."""
    save_request = request.request
    sanitized = request_insight_pb2.RIShareSaveChatRequest()
    sanitized.request.conversation_id = save_request.conversation_id
    sanitized.request.title = save_request.title
    # keep only the request_id from the chat exchange
    for exchange in save_request.chat:
        rec = share_pb2.ChatExchange()
        rec.request_id = exchange.request_id
        sanitized.request.chat.append(rec)
    return sanitized


def _sanitize_share_save_chat_response(
    response: request_insight_pb2.RIShareSaveChatResponse,
) -> request_insight_pb2.RIShareSaveChatResponse:
    """Returns a RIShareSaveChatResponse with sensitive fields removed."""
    save_response = response.response
    sanitized = request_insight_pb2.RIShareSaveChatResponse()
    # Keep the unique id of the saved chat conversation
    sanitized.response.uuid = save_response.uuid
    return sanitized


def _sanitize_share_get_chat_request(
    request: request_insight_pb2.RIShareGetChatRequest,
) -> request_insight_pb2.RIShareGetChatRequest:
    """Returns a RIShareGetChatRequest with sensitive fields removed."""
    get_request = request.request
    sanitized = request_insight_pb2.RIShareGetChatRequest()
    # Keep the unique id of the requested chat conversation
    sanitized.request.uuid = get_request.uuid
    return sanitized


def _sanitize_share_get_chat_response(
    response: request_insight_pb2.RIShareGetChatResponse,
) -> request_insight_pb2.RIShareGetChatResponse:
    """Returns a RIShareGetChatResponse event with sensitive fields removed."""
    get_response = response.response
    sanitized = request_insight_pb2.RIShareGetChatResponse()
    sanitized.response.uuid = get_response.uuid
    sanitized.response.date.CopyFrom(get_response.date)
    sanitized.response.conversation_id = get_response.conversation_id
    sanitized.response.title = get_response.title
    # Keep only the request id from each chat exchange
    for exchange in get_response.chat:
        rec = share_pb2.ChatExchange()
        rec.request_id = exchange.request_id
        sanitized.response.chat.append(rec)
    return sanitized


def _sanitize_github_app_installation_event(
    github_app_installation_event: request_insight_pb2.RIGithubAppInstallationEvent,
) -> request_insight_pb2.RIGithubAppInstallationEvent:
    """Returns a RIGithubAppInstallationEvent with sensitive fields removed."""
    sanitized = request_insight_pb2.RIGithubAppInstallationEvent(
        event_type=github_app_installation_event.event_type,
        # We don't copy the message field as it might contain sensitive information
        status=status_pb2.Status(  # pylint: disable=no-member # type: ignore
            code=github_app_installation_event.status.code,  # pylint: disable=no-member # type: ignore
        ),
    )
    return sanitized


def _sanitize_slackbot_installation_event(
    slackbot_installation_event: request_insight_pb2.RISlackbotInstallationEvent,
) -> request_insight_pb2.RISlackbotInstallationEvent:
    """Returns a RISlackbotInstallationEvent with sensitive fields removed."""
    sanitized = request_insight_pb2.RISlackbotInstallationEvent(
        event_type=slackbot_installation_event.event_type,
        # We don't copy the message field as it might contain sensitive information
        status=status_pb2.Status(  # pylint: disable=no-member # type: ignore
            code=slackbot_installation_event.status.code,  # pylint: disable=no-member # type: ignore
        ),
    )
    return sanitized


def _sanitize_add_user_to_tenant(
    add_user_to_tenant: request_insight_pb2.AddUserToTenant,
) -> request_insight_pb2.AddUserToTenant:
    """Returns a AddUserToTenant with sensitive fields removed."""
    sanitized = request_insight_pb2.AddUserToTenant(
        user=_sanitize_user(add_user_to_tenant.user)
    )
    return sanitized


def _sanitize_user_removed_from_tenant(
    remove_user_from_tenant: request_insight_pb2.RemoveUserFromTenant,
) -> request_insight_pb2.RemoveUserFromTenant:
    """Returns a RemoveUserFromTenant with sensitive fields removed."""
    sanitized = request_insight_pb2.RemoveUserFromTenant(
        user=_sanitize_user(remove_user_from_tenant.user)
    )
    return sanitized


def _sanitize_invite_user_to_tenant(
    invite_user_to_tenant: request_insight_pb2.InviteUserToTenant,
) -> request_insight_pb2.InviteUserToTenant:
    """Returns a InviteUserToTenant with sensitive fields removed."""
    sanitized = request_insight_pb2.InviteUserToTenant(
        invitation=auth_entities_pb2.TenantInvitation(
            id=invite_user_to_tenant.invitation.id,
            created_at=invite_user_to_tenant.invitation.created_at,
            inviter_user_id=invite_user_to_tenant.invitation.inviter_user_id,
        )
    )
    # Excluded PII: invitee_email, inviter_email
    # status is excluded because it would always be PENDING
    return sanitized


def _sanitize_delete_invitation(
    delete_invitation: request_insight_pb2.DeleteInvitation,
) -> request_insight_pb2.DeleteInvitation:
    """Returns a DeleteInvitation with sensitive fields removed."""
    sanitized = request_insight_pb2.DeleteInvitation(
        invitation_id=delete_invitation.invitation_id,
    )
    return sanitized


def _sanitize_update_subscription(
    update_subscription: request_insight_pb2.UpdateSubscription,
) -> request_insight_pb2.UpdateSubscription:
    """Returns a UpdateSubscription with sensitive fields removed."""
    sanitized = request_insight_pb2.UpdateSubscription(
        subscription_id=update_subscription.subscription_id,
        seats=update_subscription.seats,
    )
    return sanitized


def _sanitize_create_tenant_for_team(
    create_tenant_for_team: request_insight_pb2.CreateTenantForTeam,
) -> request_insight_pb2.CreateTenantForTeam:
    """Returns a CreateTenantForTeam with sensitive fields removed."""
    sanitized = request_insight_pb2.CreateTenantForTeam(
        tenant=_sanitize_tenant(create_tenant_for_team.tenant),
        admin_user_id=create_tenant_for_team.admin_user_id,
        subscription_id=create_tenant_for_team.subscription_id,
    )
    return sanitized


def _sanitize_accept_invitation(
    accept_invitation: request_insight_pb2.AcceptInvitation,
) -> request_insight_pb2.AcceptInvitation:
    """Returns a AcceptInvitation with sensitive fields removed."""
    sanitized = request_insight_pb2.AcceptInvitation(
        invitation_id=accept_invitation.invitation_id,
        user=_sanitize_user(accept_invitation.user),
    )
    return sanitized


def _sanitize_decline_invitation(
    decline_invitation: request_insight_pb2.DeclineInvitation,
) -> request_insight_pb2.DeclineInvitation:
    """Returns a DeclineInvitation with sensitive fields removed."""
    sanitized = request_insight_pb2.DeclineInvitation(
        invitation_id=decline_invitation.invitation_id,
    )
    return sanitized


def _sanitize_tenant(tenant: tenant_watcher_pb2.Tenant) -> tenant_watcher_pb2.Tenant:
    """Returns a Tenant with sensitive fields removed."""
    # Note(jacqueline): This is currently only used for recording self-serve team creation, so I'm
    # leaving out fields that aren't relevant to that.
    sanitized = tenant_watcher_pb2.Tenant(
        id=tenant.id,
        name=tenant.name,
        shard_namespace=tenant.shard_namespace,
        cloud=tenant.cloud,
        tier=tenant.tier,
        config=tenant_watcher_pb2.Config(
            configs=tenant.config.configs,
        ),
    )
    return sanitized


def _sanitize_user(user: auth_entities_pb2.User) -> auth_entities_pb2.User:
    """Returns a User with sensitive fields removed."""
    sanitized = auth_entities_pb2.User(
        id=user.id,
        tenants=user.tenants,
    )
    sanitized.created_at.CopyFrom(user.created_at)
    if user.HasField("in_usa"):
        sanitized.in_usa.CopyFrom(user.in_usa)
    # Excluded sensitive fields: email, nonce
    return sanitized


def _sanitize_customer_ui_session_event(
    customer_ui_event: request_insight_pb2.CustomerUISessionEvent,
) -> request_insight_pb2.CustomerUISessionEvent:
    """Returns a CustomerUISessionEvent with sensitive fields removed."""
    sanitized = request_insight_pb2.CustomerUISessionEvent()
    sanitized.user_id = customer_ui_event.user_id
    if customer_ui_event.HasField("session_end"):
        sanitized.session_end.CopyFrom(
            _sanitize_customer_ui_session_end(customer_ui_event.session_end)
        )
    if customer_ui_event.HasField("session_start"):
        sanitized.session_start.CopyFrom(
            _sanitize_customer_ui_session_start(customer_ui_event.session_start)
        )
    return sanitized


def _sanitize_customer_ui_session_start(
    session_start: request_insight_pb2.CustomerUISessionEvent.SessionStart,
) -> request_insight_pb2.CustomerUISessionEvent.SessionStart:
    """Returns a CustomerUISessionEvent.SessionStart with sensitive fields removed."""
    sanitized = request_insight_pb2.CustomerUISessionEvent.SessionStart()

    # Sanitize the redirect URL if present
    if session_start.redirect_url:
        sanitized.redirect_url = _sanitize_url(session_start.redirect_url)

    return sanitized


def _sanitize_customer_ui_session_end(
    session_end: request_insight_pb2.CustomerUISessionEvent.SessionEnd,
) -> request_insight_pb2.CustomerUISessionEvent.SessionEnd:
    sanitized = request_insight_pb2.CustomerUISessionEvent.SessionEnd()
    sanitized.reason = session_end.reason
    return sanitized


def _sanitize_tool_use_data(
    tool_use_data: request_insight_pb2.ToolUseData,
) -> request_insight_pb2.ToolUseData:
    sanitized = request_insight_pb2.ToolUseData()
    sanitized.is_mcp_tool = tool_use_data.is_mcp_tool
    sanitized.tool_name = (
        "mcp_tool" if tool_use_data.is_mcp_tool else tool_use_data.tool_name
    )
    sanitized.tool_use_id = tool_use_data.tool_use_id
    sanitized.tool_output_is_error = tool_use_data.tool_output_is_error
    sanitized.tool_run_duration_ms = tool_use_data.tool_run_duration_ms
    sanitized.conversation_id = tool_use_data.conversation_id
    sanitized.chat_history_length = tool_use_data.chat_history_length
    sanitized.tool_request_id = tool_use_data.tool_request_id
    sanitized.tool_output_len = tool_use_data.tool_output_len
    sanitized.tool_input_len = tool_use_data.tool_input_len
    return sanitized


def _sanitize_agent_reversion_data(
    agent_reversion_data: request_insight_pb2.AgentReversionData,
) -> request_insight_pb2.AgentReversionData:
    sanitized = request_insight_pb2.AgentReversionData()
    return sanitized


def _sanitize_agent_interruption(
    agent_interruption: request_insight_pb2.AgentInterruptionData,
) -> request_insight_pb2.AgentInterruptionData:
    sanitized = request_insight_pb2.AgentInterruptionData()
    sanitized.request_id = agent_interruption.request_id
    sanitized.curr_conversation_length = agent_interruption.curr_conversation_length
    return sanitized


def _sanitize_agent_tracing_data(
    sanitized: request_insight_pb2.AgentTracingData,
    original_data: request_insight_pb2.AgentTracingData,
    allowed_keys: list[str],
) -> request_insight_pb2.AgentTracingData:
    for k, v in original_data.flags.items():
        if k not in allowed_keys:
            continue
        sanitized.flags[k].timestamp.CopyFrom(v.timestamp)
        sanitized.flags[k].value = v.value
    for k, v in original_data.nums.items():
        if k not in allowed_keys:
            continue
        sanitized.nums[k].timestamp.CopyFrom(v.timestamp)
        sanitized.nums[k].value = v.value
    for k, v in original_data.string_stats.items():
        if k not in allowed_keys:
            continue
        sanitized.string_stats[k].timestamp.CopyFrom(v.timestamp)
        sanitized.string_stats[k].value.CopyFrom(v.value)
    for k, v in original_data.request_ids.items():
        # Just to be safe, check that the request ID is a valid UUID
        if k not in allowed_keys or not _is_valid_uuid(v.value):
            continue
        sanitized.request_ids[k].timestamp.CopyFrom(v.timestamp)
        sanitized.request_ids[k].value = v.value

    return sanitized


def _sanitize_remember_tool_call_data(
    remember_tool_call_data: request_insight_pb2.RememberToolCallData,
) -> request_insight_pb2.RememberToolCallData:
    allowed_keys = [
        "memoriesRequestId",
        "exceptionThrown",
        "toolOutputIsError",
        "injectionNoCodeWrapper",
        "rememberToolModelNameMissing",
        "compressionStarted",
        "compressionTargetMissing",
        "compressionPromptMissing",
        "compressionNumRecentMemoriesToKeepMissing",
        "compressionRecentMemoriesSubpromptMissing",
        "compressionMemoriesQueueSize",
        "compressionPromptStats",
        "compressionRequestId",
        "compressedMemoriesStats",
        "compressionFailed",
        "setMemoriesStart",
        "setMemoriesUpperBoundSizeMissing",
        "nonEmptyLines",
        "noMemoriesFile",
        "updateBufferFailed",
        "noChangesMade",
        "injectionStarted",
        "injectionCurrentMemoriesStats",
        "injectionPromptMissing",
        "injectionPromptStats",
        "injectionRequestId",
        "injectionUpdatedMemoriesStats",
        "injectionFailed",
    ]

    sanitized = request_insight_pb2.RememberToolCallData()
    sanitized.caller = remember_tool_call_data.caller
    sanitized.is_complex_new_memory = remember_tool_call_data.is_complex_new_memory
    sanitized.tracing_data.CopyFrom(
        _sanitize_agent_tracing_data(
            sanitized.tracing_data, remember_tool_call_data.tracing_data, allowed_keys
        )
    )
    return sanitized


def _sanitize_initial_orientation_data(
    initial_orientation_data: request_insight_pb2.InitialOrientationData,
) -> request_insight_pb2.InitialOrientationData:
    allowed_keys = [
        "exceptionThrown",
        "start",
        "end",
        "concurrencyLevelMissing",
        "initialOrientationDisabled",
        "noRootFolderFound",
        "retryWithLowerConcurrencyLevel",
        "localizationPromptMissing",
        "detectLanguagesPromptMissing",
        "orientationCompressionPromptMissing",
        "orientationMaxLanguagesMissing",
        "orientationBuildTestQueryMissing",
        "orientationModelNameMissing",
        "topLanguagesNumFiles",
        "topLanguagesNumCodeFiles",
        "topLanguagesRequestId",
        "topLanguagesModelResponseStats",
        "topLanguagesNumDetectedLanguages",
        "topLanguagesNumFinalLanguages",
        "localizationStarted",
        "localizationEnded",
        *[f"localizationPromptStats_{i}" for i in range(6)],
        *[f"localizationRequestId_{i}" for i in range(6)],
        *[f"localizationResponseStats_{i}" for i in range(6)],
        *[f"localizationParsingFailed_{i}" for i in range(6)],
        *[f"localizationNumLocations_{i}" for i in range(6)],
        "failedToListRootFolder",
        "agenticStarted",
        "agenticEnded",
        *[f"agenticNumTurns_{i}" for i in range(6)],
        *[f"agenticModelResponseStats_{i}" for i in range(6)],
        *[f"agenticFailedToComplete_{i}" for i in range(6)],
        "agenticModelResponseStats",
        "compressionRequestId",
        "compressionModelResponseStats",
        "compressionParsingFailed",
        "rememberStarted",
        "rememberEnded",
        "failedToReadGuidelines",
        "failedToWriteGuidelines",
    ]

    sanitized = request_insight_pb2.InitialOrientationData()
    sanitized.caller = initial_orientation_data.caller
    sanitized.tracing_data.CopyFrom(
        _sanitize_agent_tracing_data(
            sanitized.tracing_data, initial_orientation_data.tracing_data, allowed_keys
        )
    )
    return sanitized


def _sanitize_classify_and_distill_data(
    classify_and_distill_data: request_insight_pb2.ClassifyAndDistillData,
) -> request_insight_pb2.ClassifyAndDistillData:
    allowed_keys = [
        "memoriesRequestId",
        "exceptionThrown",
        "start",
        "end",
        "noPendingUserMessage",
        "startSendSilentExchange",
        "sendSilentExchangeRequestId",
        "sendSilentExchangeResponseStats",
        "noRequestId",
        "conversationChanged",
        "explanationStats",
        "contentStats",
        "invalidResponse",
        "worthRemembering",
        "lastUserExchangeRequestId",
        "noLastUserExchangeRequestId",
    ]

    sanitized = request_insight_pb2.ClassifyAndDistillData()
    sanitized.tracing_data.CopyFrom(
        _sanitize_agent_tracing_data(
            sanitized.tracing_data, classify_and_distill_data.tracing_data, allowed_keys
        )
    )
    return sanitized


def _sanitize_flush_memories_data(
    flush_memories_data: request_insight_pb2.FlushMemoriesData,
) -> request_insight_pb2.FlushMemoriesData:
    allowed_keys = [
        "start",
        "end",
        "memoriesRequestId",
        "exceptionThrown",
        "lastUserExchangeRequestId",
        "noMemoryData",
        "agenticTurnHasRememberToolCall",
        "emptyMemory",
        "removeUserExchangeMemoryFailed",
    ]

    sanitized = request_insight_pb2.FlushMemoriesData()
    sanitized.tracing_data.CopyFrom(
        _sanitize_agent_tracing_data(
            sanitized.tracing_data, flush_memories_data.tracing_data, allowed_keys
        )
    )
    return sanitized


def _sanitize_memories_file_open_data(
    memories_file_open_data: request_insight_pb2.MemoriesFileOpenData,
) -> request_insight_pb2.MemoriesFileOpenData:
    sanitized = request_insight_pb2.MemoriesFileOpenData()
    sanitized.memories_path_undefined = memories_file_open_data.memories_path_undefined
    return sanitized


def _sanitize_memories_move_data(
    memories_move_data: request_insight_pb2.MemoriesMoveData,
) -> request_insight_pb2.MemoriesMoveData:
    sanitized = request_insight_pb2.MemoriesMoveData()
    sanitized.target = memories_move_data.target
    return sanitized


def _sanitize_rules_imported_data(
    rules_imported_data: request_insight_pb2.RulesImportedData,
) -> request_insight_pb2.RulesImportedData:
    sanitized = request_insight_pb2.RulesImportedData()
    sanitized.type = rules_imported_data.type
    sanitized.num_files = rules_imported_data.num_files
    return sanitized


def _sanitize_agent_session_event(
    agent_session_event: request_insight_pb2.AgentSessionEvent,
) -> request_insight_pb2.AgentSessionEvent:
    sanitized = request_insight_pb2.AgentSessionEvent()
    sanitized.event_name = agent_session_event.event_name
    sanitized.user_agent = agent_session_event.user_agent
    sanitized.conversation_id = agent_session_event.conversation_id
    if agent_session_event.HasField("agent_reversion_data"):
        sanitized.agent_reversion_data.CopyFrom(
            _sanitize_agent_reversion_data(agent_session_event.agent_reversion_data)
        )
    if agent_session_event.HasField("agent_interruption_data"):
        sanitized.agent_interruption_data.CopyFrom(
            _sanitize_agent_interruption(agent_session_event.agent_interruption_data)
        )
    if agent_session_event.HasField("remember_tool_call_data"):
        sanitized.remember_tool_call_data.CopyFrom(
            _sanitize_remember_tool_call_data(
                agent_session_event.remember_tool_call_data
            )
        )
    if agent_session_event.HasField("memories_file_open_data"):
        sanitized.memories_file_open_data.CopyFrom(
            _sanitize_memories_file_open_data(
                agent_session_event.memories_file_open_data
            )
        )
    if agent_session_event.HasField("initial_orientation_data"):
        sanitized.initial_orientation_data.CopyFrom(
            _sanitize_initial_orientation_data(
                agent_session_event.initial_orientation_data
            )
        )
    if agent_session_event.HasField("classify_and_distill_data"):
        sanitized.classify_and_distill_data.CopyFrom(
            _sanitize_classify_and_distill_data(
                agent_session_event.classify_and_distill_data
            )
        )
    if agent_session_event.HasField("flush_memories_data"):
        sanitized.flush_memories_data.CopyFrom(
            _sanitize_flush_memories_data(agent_session_event.flush_memories_data)
        )

    if agent_session_event.HasField("memories_move_data"):
        sanitized.memories_move_data.CopyFrom(
            _sanitize_memories_move_data(agent_session_event.memories_move_data)
        )
    if agent_session_event.HasField("rules_imported_data"):
        sanitized.rules_imported_data.CopyFrom(
            _sanitize_rules_imported_data(agent_session_event.rules_imported_data)
        )

    return sanitized


def _sanitize_agent_request_event(
    agent_request_event: request_insight_pb2.AgentRequestEvent,
) -> request_insight_pb2.AgentRequestEvent:
    sanitized = request_insight_pb2.AgentRequestEvent()
    sanitized.event_name = agent_request_event.event_name
    sanitized.user_agent = agent_request_event.user_agent
    sanitized.conversation_id = agent_request_event.conversation_id
    sanitized.chat_history_length = agent_request_event.chat_history_length
    return sanitized


def _sanitize_remote_agent_session_event(
    remote_agent_session_event: request_insight_pb2.RemoteAgentSessionEvent,
) -> request_insight_pb2.RemoteAgentSessionEvent:
    sanitized = request_insight_pb2.RemoteAgentSessionEvent()
    sanitized.event_name = remote_agent_session_event.event_name
    sanitized.user_agent = remote_agent_session_event.user_agent
    sanitized.remote_agent_id = remote_agent_session_event.remote_agent_id
    if remote_agent_session_event.HasField("remote_agent_setup_data"):
        sanitized.remote_agent_setup_data.CopyFrom(
            _sanitize_remote_agent_setup_data(
                remote_agent_session_event.remote_agent_setup_data
            )
        )
    if remote_agent_session_event.HasField("setup_script_data"):
        sanitized.setup_script_data.CopyFrom(
            _sanitize_remote_agent_setup_script_data(
                remote_agent_session_event.setup_script_data
            )
        )
    if remote_agent_session_event.HasField("ssh_interaction_data"):
        sanitized.ssh_interaction_data.CopyFrom(
            _sanitize_ssh_interaction_data(
                remote_agent_session_event.ssh_interaction_data
            )
        )
    if remote_agent_session_event.HasField("notification_bell_data"):
        sanitized.notification_bell_data.CopyFrom(
            _sanitize_notification_bell_data(
                remote_agent_session_event.notification_bell_data
            )
        )
    if remote_agent_session_event.HasField("diff_panel_data"):
        sanitized.diff_panel_data.CopyFrom(
            _sanitize_diff_panel_data(remote_agent_session_event.diff_panel_data)
        )
    if remote_agent_session_event.HasField("setup_page_opened"):
        sanitized.setup_page_opened.CopyFrom(
            _sanitize_setup_page_opened(remote_agent_session_event.setup_page_opened)
        )
    if remote_agent_session_event.HasField("github_api_failure"):
        sanitized.github_api_failure.CopyFrom(
            _sanitize_github_api_failure(remote_agent_session_event.github_api_failure)
        )
    if remote_agent_session_event.HasField("remote_agent_created"):
        sanitized.remote_agent_created.CopyFrom(
            _sanitize_remote_agent_created(
                remote_agent_session_event.remote_agent_created
            )
        )
    if remote_agent_session_event.HasField("changes_applied_data"):
        sanitized.changes_applied_data.CopyFrom(
            _sanitize_changes_applied_data(
                remote_agent_session_event.changes_applied_data
            )
        )
    if remote_agent_session_event.HasField("created_pr_data"):
        sanitized.created_pr_data.CopyFrom(
            _sanitize_created_pr_data(remote_agent_session_event.created_pr_data)
        )

    return sanitized


def _sanitize_remote_agent_setup_data(
    remote_agent_setup_data: request_insight_pb2.RemoteAgentSetupData,
) -> request_insight_pb2.RemoteAgentSetupData:
    sanitized = request_insight_pb2.RemoteAgentSetupData()
    sanitized.used_generated_setup_script = (
        remote_agent_setup_data.used_generated_setup_script
    )
    sanitized.setup_state = remote_agent_setup_data.setup_state
    return sanitized


def _sanitize_remote_agent_setup_script_data(
    remote_agent_setup_script_data: request_insight_pb2.RemoteAgentSetupScriptData,
) -> request_insight_pb2.RemoteAgentSetupScriptData:
    sanitized = request_insight_pb2.RemoteAgentSetupScriptData()
    sanitized.num_tries = remote_agent_setup_script_data.num_tries
    sanitized.num_messages_sent = remote_agent_setup_script_data.num_messages_sent
    sanitized.generation_time_ms = remote_agent_setup_script_data.generation_time_ms
    sanitized.manual_modification = remote_agent_setup_script_data.manual_modification
    return sanitized


def _sanitize_ssh_interaction_data(
    ssh_interaction_data: request_insight_pb2.SSHInteractionData,
) -> request_insight_pb2.SSHInteractionData:
    sanitized = request_insight_pb2.SSHInteractionData()
    sanitized.interaction_type = ssh_interaction_data.interaction_type
    return sanitized


def _sanitize_notification_bell_data(
    notification_bell_data: request_insight_pb2.NotificationBellData,
) -> request_insight_pb2.NotificationBellData:
    sanitized = request_insight_pb2.NotificationBellData()
    sanitized.bell_state = notification_bell_data.bell_state
    return sanitized


def _sanitize_diff_panel_data(
    diff_panel_data: request_insight_pb2.DiffPanelData,
) -> request_insight_pb2.DiffPanelData:
    sanitized = request_insight_pb2.DiffPanelData()
    sanitized.loading_time_ms = diff_panel_data.loading_time_ms
    sanitized.applied = diff_panel_data.applied
    return sanitized


def _sanitize_setup_page_opened(
    _setup_page_opened: request_insight_pb2.SetupPageOpened,
) -> request_insight_pb2.SetupPageOpened:
    sanitized = request_insight_pb2.SetupPageOpened()
    return sanitized


def _sanitize_github_api_failure(
    github_api_failure: request_insight_pb2.GithubAPIFailure,
) -> request_insight_pb2.GithubAPIFailure:
    sanitized = request_insight_pb2.GithubAPIFailure()
    sanitized.error_code = github_api_failure.error_code
    return sanitized


def _sanitize_remote_agent_created(
    remote_agent_created: request_insight_pb2.RemoteAgentCreated,
) -> request_insight_pb2.RemoteAgentCreated:
    sanitized = request_insight_pb2.RemoteAgentCreated()
    sanitized.changed_repo = remote_agent_created.changed_repo
    sanitized.changed_branch = remote_agent_created.changed_branch
    return sanitized


def _sanitize_changes_applied_data(
    _changes_applied_data: request_insight_pb2.ChangesAppliedData,
) -> request_insight_pb2.ChangesAppliedData:
    sanitized = request_insight_pb2.ChangesAppliedData()
    return sanitized


def _sanitize_created_pr_data(
    _created_pr_data: request_insight_pb2.CreatedPRData,
) -> request_insight_pb2.CreatedPRData:
    sanitized = request_insight_pb2.CreatedPRData()
    return sanitized


def _sanitize_prompt_cache_usage(
    prompt_cache_usage: request_insight_pb2.PromptCacheUsage,
) -> request_insight_pb2.PromptCacheUsage:
    sanitized = request_insight_pb2.PromptCacheUsage()
    sanitized.input_tokens = prompt_cache_usage.input_tokens
    sanitized.cache_read_input_tokens = prompt_cache_usage.cache_read_input_tokens
    sanitized.cache_creation_input_tokens = (
        prompt_cache_usage.cache_creation_input_tokens
    )
    sanitized.text_input_tokens = prompt_cache_usage.text_input_tokens
    sanitized.tool_input_tokens = prompt_cache_usage.tool_input_tokens
    sanitized.text_output_tokens = prompt_cache_usage.text_output_tokens
    sanitized.tool_output_tokens = prompt_cache_usage.tool_output_tokens
    sanitized.model_caller = prompt_cache_usage.model_caller
    return sanitized


def _sanitize_remote_tool_call_request(
    remote_tool_call_request: request_insight_pb2.RIRemoteToolCallRequest,
) -> request_insight_pb2.RIRemoteToolCallRequest:
    sanitized = request_insight_pb2.RIRemoteToolCallRequest()

    if remote_tool_call_request.HasField("codebase_retrieval_request"):
        sanitized.codebase_retrieval_request.CopyFrom(
            agents_pb2.CodebaseRetrievalRequest(
                max_output_length=remote_tool_call_request.codebase_retrieval_request.max_output_length,
            )
        )
    if remote_tool_call_request.HasField("edit_file_request"):
        sanitized.edit_file_request.SetInParent()
    if remote_tool_call_request.HasField("ri_run_remote_tool_request"):
        sanitized.ri_run_remote_tool_request.CopyFrom(
            request_insight_pb2.RIRunRemoteToolRequest(
                tool_name=remote_tool_call_request.ri_run_remote_tool_request.tool_name,
                tool_id=remote_tool_call_request.ri_run_remote_tool_request.tool_id,
            )
        )

    return sanitized


def _sanitize_remote_tool_call_response(
    remote_tool_call_response: request_insight_pb2.RIRemoteToolCallResponse,
) -> request_insight_pb2.RIRemoteToolCallResponse:
    # Currently most fields aren't copied as most are PII.
    sanitized = request_insight_pb2.RIRemoteToolCallResponse()

    # We do however go through and mark which of the "oneof" fields were set.
    if remote_tool_call_response.HasField("codebase_retrieval_response"):
        sanitized.codebase_retrieval_response.CopyFrom(
            agents_pb2.CodebaseRetrievalResponse()
        )
    if remote_tool_call_response.HasField("edit_file_response"):
        sanitized.edit_file_response.CopyFrom(
            agents_pb2.EditFileResponse(
                is_error=remote_tool_call_response.edit_file_response.is_error,
            )
        )
    if remote_tool_call_response.HasField("run_remote_tool_response"):
        sanitized.run_remote_tool_response.CopyFrom(
            agents_pb2.RunRemoteToolResponse(
                is_error=remote_tool_call_response.run_remote_tool_response.is_error,
                status=remote_tool_call_response.run_remote_tool_response.status,
            )
        )

    return sanitized


def _sanitize_remote_agents_create_request(
    request: request_insight_pb2.RemoteAgentsCreateRequest,
) -> request_insight_pb2.RemoteAgentsCreateRequest:
    sanitized = request_insight_pb2.RemoteAgentsCreateRequest()
    if request.HasField("request") and request.request.HasField("config"):
        sanitized.request.config.model = request.request.config.model
        if request.request.config.HasField("is_setup_script_agent"):
            sanitized.request.config.is_setup_script_agent = (
                request.request.config.is_setup_script_agent
            )
        # Get the branch name if main/master
        if request.request.config.workspace_setup.github_ref.ref == "main":
            sanitized.request.config.workspace_setup.github_ref.ref = "main"
        elif request.request.config.workspace_setup.github_ref.ref == "master":
            sanitized.request.config.workspace_setup.github_ref.ref = "master"
    return sanitized


def _sanitize_remote_agents_create_response(
    response: request_insight_pb2.RemoteAgentsCreateResponse,
) -> request_insight_pb2.RemoteAgentsCreateResponse:
    sanitized = request_insight_pb2.RemoteAgentsCreateResponse()
    if response.HasField("response") and response.response.HasField("agent"):
        sanitized.response.agent.remote_agent_id = (
            response.response.agent.remote_agent_id
        )
        sanitized.response.agent.status = response.response.agent.status
        if response.response.agent.HasField("config"):
            sanitized.response.agent.config.model = response.response.agent.config.model
            if response.response.agent.config.HasField("is_setup_script_agent"):
                sanitized.response.agent.config.is_setup_script_agent = (
                    response.response.agent.config.is_setup_script_agent
                )
    return sanitized


def _sanitize_remote_agents_chat_request(
    request: request_insight_pb2.RemoteAgentsChatRequest,
) -> request_insight_pb2.RemoteAgentsChatRequest:
    sanitized = request_insight_pb2.RemoteAgentsChatRequest()
    if request.HasField("request"):
        sanitized.request.remote_agent_id = request.request.remote_agent_id
    return sanitized


def _sanitize_remote_agents_interrupt_request(
    request: request_insight_pb2.RemoteAgentsInterruptRequest,
) -> request_insight_pb2.RemoteAgentsInterruptRequest:
    sanitized = request_insight_pb2.RemoteAgentsInterruptRequest()
    if request.HasField("request"):
        sanitized.request.remote_agent_id = request.request.remote_agent_id
    return sanitized


def _sanitize_remote_agents_delete_request(
    request: request_insight_pb2.RemoteAgentsDeleteRequest,
) -> request_insight_pb2.RemoteAgentsDeleteRequest:
    sanitized = request_insight_pb2.RemoteAgentsDeleteRequest()
    if request.HasField("request"):
        sanitized.request.remote_agent_id = request.request.remote_agent_id
    return sanitized


def _sanitize_remote_agents_add_ssh_key_request(
    request: request_insight_pb2.RemoteAgentsAddSSHKeyRequest,
) -> request_insight_pb2.RemoteAgentsAddSSHKeyRequest:
    sanitized = request_insight_pb2.RemoteAgentsAddSSHKeyRequest()
    if request.HasField("request"):
        sanitized.request.remote_agent_id = request.request.remote_agent_id
    return sanitized


def _sanitize_remote_agents_pause_request(
    request: request_insight_pb2.RemoteAgentsPauseRequest,
) -> request_insight_pb2.RemoteAgentsPauseRequest:
    sanitized = request_insight_pb2.RemoteAgentsPauseRequest()
    if request.HasField("request"):
        sanitized.request.remote_agent_id = request.request.remote_agent_id
    return sanitized


def _sanitize_remote_agents_pause_response(
    response: request_insight_pb2.RemoteAgentsPauseResponse,
) -> request_insight_pb2.RemoteAgentsPauseResponse:
    sanitized = request_insight_pb2.RemoteAgentsPauseResponse()
    return sanitized


def _sanitize_remote_agents_resume_request(
    request: request_insight_pb2.RemoteAgentsResumeRequest,
) -> request_insight_pb2.RemoteAgentsResumeRequest:
    sanitized = request_insight_pb2.RemoteAgentsResumeRequest()
    if request.HasField("request"):
        sanitized.request.remote_agent_id = request.request.remote_agent_id
    return sanitized


def _sanitize_remote_agents_resume_response(
    response: request_insight_pb2.RemoteAgentsResumeResponse,
) -> request_insight_pb2.RemoteAgentsResumeResponse:
    sanitized = request_insight_pb2.RemoteAgentsResumeResponse()
    return sanitized


def _sanitize_url(url: str) -> str:
    """
    Sanitizes a URL by removing query parameters and fragments that might contain sensitive information.

    Args:
        url: The URL to sanitize.

    Returns:
        A sanitized version of the URL with query parameters and fragments removed,
        or a placeholder if an error occurs or the URL is considered invalid.
    """
    if not url:
        return ""

    try:
        parsed_url = urlparse(url)

        # If the URL has a scheme but no network location (netloc), consider it invalid.
        if parsed_url.scheme and not parsed_url.netloc:
            raise ValueError(f"Invalid URL format: {url}")

        # Rebuild the URL without query parameters and fragments.
        sanitized_url = urlunparse(
            (
                parsed_url.scheme,
                parsed_url.netloc,
                parsed_url.path,
                "",  # params
                "",  # query - removed for privacy
                "",  # fragment - removed for privacy
            )
        )
        return sanitized_url
    except Exception as ex:
        log.error("Unexpected error processing URL '%s': %s", url, ex)
        log.exception(ex)
        return "/[INVALID_URL]"


def _sanitize_recaptcha_event(
    recaptcha_event: request_insight_pb2.Recaptcha,
) -> request_insight_pb2.Recaptcha:
    sanitized = request_insight_pb2.Recaptcha()
    sanitized.assessment_name = recaptcha_event.assessment_name
    sanitized.assessment_reasons.extend(recaptcha_event.assessment_reasons)
    sanitized.score = recaptcha_event.score
    sanitized.action = recaptcha_event.action
    sanitized.user_agent = recaptcha_event.user_agent
    # omitted PII: ip address
    # omitted PII: email
    return sanitized


def _sanitize_verisoul(
    verisoul_event: request_insight_pb2.Verisoul,
) -> request_insight_pb2.Verisoul:
    sanitized = request_insight_pb2.Verisoul()
    sanitized.opaque_user_id.CopyFrom(verisoul_event.opaque_user_id)
    return sanitized


def _sanitize_verosint(
    verosint_event: request_insight_pb2.Verosint,
) -> request_insight_pb2.Verosint:
    sanitized = request_insight_pb2.Verosint()
    sanitized.opaque_user_id.CopyFrom(verosint_event.opaque_user_id)
    return sanitized


def _sanitize_purchase_credits(
    purchase_credits: request_insight_pb2.PurchaseCredits,
) -> request_insight_pb2.PurchaseCredits:
    sanitized = request_insight_pb2.PurchaseCredits()
    sanitized.orb_subscription_id = purchase_credits.orb_subscription_id
    sanitized.credits = purchase_credits.credits
    return sanitized


def _sanitize_cancel_subscription(
    cancel_subscription: request_insight_pb2.CancelSubscription,
) -> request_insight_pb2.CancelSubscription:
    sanitized = request_insight_pb2.CancelSubscription()
    sanitized.orb_subscription_id = cancel_subscription.orb_subscription_id
    return sanitized


def _sanitize_unschedule_pending_subscription_cancellation(
    unschedule_pending_subscription_cancellation: request_insight_pb2.UnschedulePendingSubscriptionCancellation,
) -> request_insight_pb2.UnschedulePendingSubscriptionCancellation:
    sanitized = request_insight_pb2.UnschedulePendingSubscriptionCancellation()
    sanitized.orb_subscription_id = (
        unschedule_pending_subscription_cancellation.orb_subscription_id
    )
    return sanitized


def _is_valid_uuid(val: str) -> bool:
    """Check if string is a valid UUID."""
    try:
        uuid.UUID(val)
        return True
    except ValueError:
        return False
