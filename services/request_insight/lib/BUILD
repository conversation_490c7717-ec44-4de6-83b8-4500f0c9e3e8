load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg_library")
load("//tools/bzl:python.bzl", "py_library")

kubecfg_library(
    name = "bigquery_lib",
    srcs = ["bigquery_lib.jsonnet"],
    visibility = [
        "//services/misuse_monitor:__subpackages__",
        "//services/request_insight:__subpackages__",
        "//tools/deletion_utils:__subpackages__",
    ],
    deps = [
        "//deploy/common:cloud_info",
    ],
)

kubecfg_library(
    name = "exporter_lib",
    srcs = ["exporter_lib.jsonnet"],
    visibility = [
        "//services/request_insight:__subpackages__",
        # temporarily add visibility for the billing service, will remove after switching to use a new billing event topic
        "//services/billing/server:__pkg__",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

py_library(
    name = "request_insight_subscriber",
    srcs = [
        "request_insight_subscriber.py",
    ],
    visibility = ["//services/request_insight:__subpackages__"],
    deps = [
        "//services/request_insight:request_insight_py_proto",
        requirement("prometheus-client"),
        requirement("google-cloud-pubsub"),
        requirement("structlog"),
        requirement("protobuf"),
    ],
)

go_library(
    name = "request_insight_subscriber_go",
    srcs = ["request_insight_subscriber.go"],
    importpath = "github.com/augmentcode/augment/services/request_insight/lib/subscriber",
    visibility = [
        "//services/request_insight:__subpackages__",
        # temporarily add visibility for the billing service, will remove after switching to use a new billing event topic
        "//services/billing/server:__pkg__",
    ],
    deps = [
        "//base/go/clock:clock_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/request_insight:request_insight_go_proto",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_rs_zerolog//log",
        "@org_golang_google_protobuf//proto",
    ],
)
