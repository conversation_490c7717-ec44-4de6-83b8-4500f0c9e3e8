package main

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"time"

	"cloud.google.com/go/bigquery"
	pb "github.com/augmentcode/augment/services/request_insight/proto"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// SearchExporter is the component of the RI support database exporter that writes a subset of event
// data to a search dataset in BigQuery.

var tableWrites = prometheus.NewCounterVec(
	prometheus.CounterOpts{
		Name: "au_ri_search_table_write_count",
		Help: "Number of rows written to each search table",
	},
	[]string{"table", "status"},
)

func init() {
	// Register metrics.
	prometheus.MustRegister(
		tableWrites,
	)
}

type SearchExportConfig struct {
	ProjectId   string
	DatasetName string
}

type SearchExporter interface {
	ProcessMessage(ctx context.Context, message *pb.RequestInsightMessage) error
	Close() error
}

type SearchExporterImpl struct {
	bqClient *bigquery.Client
	dataset  *bigquery.Dataset
}

func NewSearchExporter(ctx context.Context, config *SearchExportConfig) (*SearchExporterImpl, error) {
	bqClient, err := bigquery.NewClient(ctx, config.ProjectId)
	if err != nil {
		return nil, fmt.Errorf("Failed to create BigQuery client: %w", err)
	}

	dataset := bqClient.Dataset(config.DatasetName)
	return &SearchExporterImpl{
		bqClient: bqClient,
		dataset:  dataset,
	}, nil
}

func (e *SearchExporterImpl) Close() error {
	err := e.bqClient.Close()
	if err != nil {
		return fmt.Errorf("Failed to close BigQuery client: %w", err)
	}
	return nil
}

// Process a single message from the pub/sub queue. This is compatible with RequestInsightSubscriber
// but is intended to be used within SupportDatabaseExporter.
func (e *SearchExporterImpl) ProcessMessage(
	ctx context.Context, message *pb.RequestInsightMessage,
) error {
	// Collect the rows to insert in each table across all the events in this message.
	// Note that we fail fast on all operations. This means that if we're inserting into multiple
	// tables and one of them fails, we won't try to insert into the other tables. In most cases we're
	// dealing with a single message (or multiple messages of a single type), so we're probably not
	// losing many insertable events. This scheme simplifies the code and reduces duplicate rows.
	// (Note that failed messages will be retried later by the pub/sub queue.)
	tableToRows := map[string][]interface{}{}
	var requestOrSessionID string
	switch message.Message.(type) {
	case *pb.RequestInsightMessage_UpdateRequestInfoRequest:
		request := message.GetUpdateRequestInfoRequest()
		requestID := request.GetRequestId()
		tenantInfo := request.GetTenantInfo()
		requestOrSessionID = requestID
		if tenantInfo == nil || tenantInfo.GetTenantId() == "" || tenantInfo.GetTenantName() == "" {
			// Note(jacqueline): We're seeing some chat health check requsts in staging namespaces
			// without a tenant id, for reasons that we don't understand. Just drop them for now.
			log.Warn().Msgf(
				"Dropping a message for request %s because it's missing tenant info", requestID,
			)
			return nil
		}
		for _, event := range request.GetEvents() {
			if !isValidTimestamp(event.GetTime()) {
				log.Warn().Msgf(
					"Dropping %s event for request %s because its timestamp (%s) is invalid",
					getRequestEventType(event), requestID, event.GetTime(),
				)
				continue
			}
			eventRows, err := e.getRequestTableUpdates(ctx, requestID, tenantInfo, event)
			if err != nil {
				return err
			}
			for tableName, row := range eventRows {
				tableToRows[tableName] = append(tableToRows[tableName], row)
			}
		}
	case *pb.RequestInsightMessage_RecordSessionEventsRequest:
		request := message.GetRecordSessionEventsRequest()
		sessionID := request.GetSessionId()
		tenantInfo := request.GetTenantInfo()
		requestOrSessionID = sessionID
		if tenantInfo == nil || tenantInfo.GetTenantId() == "" || tenantInfo.GetTenantName() == "" {
			// Note(jacqueline): We're seeing some chat health check requsts in staging namespaces
			// without a tenant id, for reasons that we don't understand. Just drop them for now.
			log.Warn().Msgf(
				"Dropping a message for session %s because it's missing tenant info", sessionID,
			)
			return nil
		}
		for _, event := range request.GetEvents() {
			if !isValidTimestamp(event.GetTime()) {
				log.Warn().Msgf(
					"Dropping %s event for session %s because its timestamp (%s) is invalid",
					getSessionEventType(event), sessionID, event.GetTime(),
				)
				continue
			}
			eventRows, err := e.getSessionTableUpdates(ctx, sessionID, tenantInfo, event)
			if err != nil {
				return err
			}
			for tableName, row := range eventRows {
				tableToRows[tableName] = append(tableToRows[tableName], row)
			}
		}
	default:
		log.Debug().Msgf("Ignoring message of unsupported type")
	}

	for tableName, rows := range tableToRows {
		inserter := e.dataset.Table(tableName).Inserter()
		err := inserter.Put(ctx, rows)
		if err != nil {
			tableWrites.WithLabelValues(tableName, "ERROR").Add(float64(len(rows)))
			return fmt.Errorf("Failed to insert rows for table %s (request or session %s): %w", tableName, requestOrSessionID, err)
		} else {
			tableWrites.WithLabelValues(tableName, "OK").Add(float64(len(rows)))
			log.Debug().Msgf("Inserted %d rows into table %s ", len(rows), tableName)
		}
	}

	return nil
}

// Returns a map of table name to rows to insert into that table.
func (e *SearchExporterImpl) getRequestTableUpdates(
	ctx context.Context, requestID string, tenantInfo *pb.TenantInfo, event *pb.RequestEvent,
) (map[string]interface{}, error) {
	eventType := getRequestEventType(event)
	if eventType == "" {
		// This is expected during the rollout of a new event type.
		return nil, fmt.Errorf("Unknown event type. This is expected during rollout of a new event")
	}

	tableToRow := map[string]interface{}{}
	insertIgnoreNil(tableToRow, "request_event", getRequestEventRow(requestID, tenantInfo, event))
	insertIgnoreNil(tableToRow, "request_metadata", getRequestMetadataRow(requestID, tenantInfo, event))
	insertIgnoreNil(tableToRow, "model", getModelRow(requestID, tenantInfo, event))
	insertIgnoreNil(tableToRow, "resolution", getResolutionRow(requestID, tenantInfo, event))
	insertIgnoreNil(tableToRow, "http_status", getHttpStatusRow(requestID, tenantInfo, event))
	insertIgnoreNil(tableToRow, "completion_response", getCompletionResponseRow(requestID, tenantInfo, event))
	insertIgnoreNil(tableToRow, "completion_post_process", getCompletionPostProcessRow(requestID, tenantInfo, event))
	insertIgnoreNil(tableToRow, "parenthesis_truncation", getParenthesisTruncationRow(requestID, tenantInfo, event))
	return tableToRow, nil
}

func (e *SearchExporterImpl) getSessionTableUpdates(
	ctx context.Context, sessionID string, tenantInfo *pb.TenantInfo, event *pb.SessionEvent,
) (map[string]interface{}, error) {
	eventType := getSessionEventType(event)
	if eventType == "" {
		// This is expected during the rollout of a new event type.
		return nil, fmt.Errorf("Unknown event type. This is expected during rollout of a new event")
	}

	tableToRow := map[string]interface{}{}
	insertIgnoreNil(tableToRow, "session_event", getSessionEventRow(sessionID, tenantInfo, event))
	insertIgnoreNil(tableToRow, "remote_agent_log", getRemoteAgentLogRow(sessionID, tenantInfo, event))
	return tableToRow, nil
}

func insertIgnoreNil(m map[string]interface{}, key string, val interface{}) {
	// NB(jacqueline): For reasons I don't understand, just comparing to `nil` doesn't work here.
	// Augment claims it's because `bigquery.ValueSaver` is an interface and even though the value of
	// the interface is `nil` the interface as a whole is not.
	if !reflect.ValueOf(val).IsNil() {
		m[key] = val
	}
}

// Get the name of the event type for the given request event, or the empty string if it's unknown.
func getRequestEventType(event *pb.RequestEvent) string {
	ref := event.ProtoReflect()
	eventType := ref.WhichOneof(ref.Descriptor().Oneofs().ByName("event"))
	if eventType == nil {
		return ""
	}
	return string(eventType.Name())
}

// Get the name of the event type for the given session event, or the empty string if it's unknown.
func getSessionEventType(event *pb.SessionEvent) string {
	ref := event.ProtoReflect()
	eventType := ref.WhichOneof(ref.Descriptor().Oneofs().ByName("event"))
	if eventType == nil {
		return ""
	}
	return string(eventType.Name())
}

// Returns true if the given timestamp is valid for BigQuery and false otherwise.
func isValidTimestamp(t *timestamppb.Timestamp) bool {
	if t == nil || t.GetSeconds() == 0 {
		return false
	}

	// BigQuery allows timestamps up to 366 days in the future. We shouldn't have anything that far
	// in the future, but record up to 300 days in the future so we can track these cases down.
	maxFutureTime := time.Now().AddDate(0, 0, 300)
	timestampTime := time.Unix(t.GetSeconds(), int64(t.GetNanos()))
	return timestampTime.Before(maxFutureTime)
}

// Below this point are the structs that can be passed to the bigquery client API to insert rows.
// As long as the struct and all of its field are public, the client library can infer the schema
// from the struct. If you need more fine-grained control, you can also manually implement
// bigquery.ValueSaver.

type RequestEventBase struct {
	RequestId string    `bigquery:"request_id"`
	TenantID  string    `bigquery:"tenant_id"`
	Tenant    string    `bigquery:"tenant"`
	Time      time.Time `bigquery:"time"`
}

type SessionEventBase struct {
	SessionId string    `bigquery:"session_id"`
	TenantID  string    `bigquery:"tenant_id"`
	Tenant    string    `bigquery:"tenant"`
	Time      time.Time `bigquery:"time"`
}

type RequestEventRow struct {
	RequestEventBase
	EventType string `bigquery:"event_type"`
	EventId   string `bigquery:"event_id"`
}

func getRequestEventRow(
	requestID string, tenantInfo *pb.TenantInfo, event *pb.RequestEvent,
) *RequestEventRow {
	return &RequestEventRow{
		RequestEventBase: RequestEventBase{
			RequestId: requestID,
			TenantID:  tenantInfo.TenantId,
			Tenant:    tenantInfo.TenantName,
			Time:      event.Time.AsTime(),
		},
		EventType: getRequestEventType(event),
		EventId:   event.GetEventId(),
	}
}

type RequestMetadataRow struct {
	RequestEventBase
	RequestType  string              `bigquery:"request_type"`
	UserId       string              `bigquery:"user_id"`
	OpaqueUserId bigquery.NullString `bigquery:"opaque_user_id"`
	UserIdType   bigquery.NullString `bigquery:"user_id_type"`
	UserEmail    bigquery.NullString `bigquery:"user_email"`
	SessionId    string              `bigquery:"session_id"`
	UserAgent    string              `bigquery:"user_agent"`
}

func getRequestMetadataRow(
	requestID string, tenantInfo *pb.TenantInfo, event *pb.RequestEvent,
) *RequestMetadataRow {
	metadata := event.GetRequestMetadata()
	if metadata != nil {
		return &RequestMetadataRow{
			RequestEventBase: RequestEventBase{
				RequestId: requestID,
				TenantID:  tenantInfo.TenantId,
				Tenant:    tenantInfo.TenantName,
				Time:      event.Time.AsTime(),
			},
			RequestType:  metadata.GetRequestType().String(),
			SessionId:    metadata.GetSessionId(),
			UserId:       metadata.GetUserId(),
			OpaqueUserId: bigquery.NullString{Valid: metadata.GetOpaqueUserId() != nil, StringVal: metadata.GetOpaqueUserId().GetUserId()},
			UserIdType:   bigquery.NullString{Valid: metadata.GetOpaqueUserId() != nil, StringVal: metadata.GetOpaqueUserId().GetUserIdType().String()},
			UserEmail:    bigquery.NullString{Valid: metadata.GetUserEmail() != "", StringVal: metadata.GetUserEmail()},
			UserAgent:    metadata.GetUserAgent(),
		}
	}
	return nil
}

type modelRow struct {
	RequestEventBase
	ModelName string `bigquery:"model_name"`
}

func getModelRow(
	requestID string, tenantInfo *pb.TenantInfo, event *pb.RequestEvent,
) *modelRow {
	var model string
	switch event.Event.(type) {
	case *pb.RequestEvent_CompletionHostRequest:
		model = event.GetCompletionHostRequest().GetModel()
	case *pb.RequestEvent_InstructionHostRequest:
		model = event.GetInstructionHostRequest().GetRequest().GetModelName()
	case *pb.RequestEvent_ChatHostRequest:
		model = event.GetChatHostRequest().GetRequest().GetModelName()
	case *pb.RequestEvent_NextEditHostRequest:
		model = event.GetNextEditHostRequest().GetRequest().GetModelName()
	default:
		return nil
	}
	return &modelRow{
		RequestEventBase: RequestEventBase{
			RequestId: requestID,
			TenantID:  tenantInfo.TenantId,
			Tenant:    tenantInfo.TenantName,
			Time:      event.Time.AsTime(),
		},
		ModelName: model,
	}
}

type resolutionRow struct {
	RequestEventBase
	Accepted bool `bigquery:"accepted"`
}

func getResolutionRow(
	requestID string, tenantInfo *pb.TenantInfo, event *pb.RequestEvent,
) *resolutionRow {
	var accepted bool
	switch event.Event.(type) {
	case *pb.RequestEvent_CompletionResolution:
		accepted = event.GetCompletionResolution().GetAcceptedIdx() >= 0
	case *pb.RequestEvent_EditResolution:
		accepted = event.GetEditResolution().GetIsAccepted()
	case *pb.RequestEvent_NextEditResolution:
		accepted = event.GetNextEditResolution().GetIsAccepted()
	case *pb.RequestEvent_InstructionResolution:
		// For instructions, count any acceptance as an acceptance for the sake of search.
		resolutionEvent := event.GetInstructionResolution()
		if resolutionEvent.GetIsAcceptAll() {
			accepted = true
		} else if resolutionEvent.GetIsRejectAll() {
			accepted = false
		} else {
			for _, chunk := range resolutionEvent.GetIsAcceptedChunks() {
				if chunk {
					accepted = true
					break
				}
			}
		}
	default:
		return nil
	}

	return &resolutionRow{
		RequestEventBase: RequestEventBase{
			RequestId: requestID,
			TenantID:  tenantInfo.TenantId,
			Tenant:    tenantInfo.TenantName,
			Time:      event.Time.AsTime(),
		},
		Accepted: accepted,
	}
}

type httpStatusRow struct {
	RequestEventBase
	Code int `bigquery:"code"`
}

func getHttpStatusRow(
	requestID string, tenantInfo *pb.TenantInfo, event *pb.RequestEvent,
) *httpStatusRow {
	if event.GetApiHttpResponse() == nil {
		return nil
	}
	return &httpStatusRow{
		RequestEventBase: RequestEventBase{
			RequestId: requestID,
			TenantID:  tenantInfo.TenantId,
			Tenant:    tenantInfo.TenantName,
			Time:      event.Time.AsTime(),
		},
		Code: int(event.GetApiHttpResponse().GetCode()),
	}
}

type completionResponseRow struct {
	RequestEventBase
	CharacterCount    int `bigquery:"character_count"`
	NonemptyLineCount int `bigquery:"nonempty_line_count"`
}

func getCompletionResponseRow(
	requestID string, tenantInfo *pb.TenantInfo, event *pb.RequestEvent,
) *completionResponseRow {
	if event.GetCompletionHostResponse() == nil {
		return nil
	}
	text := event.GetCompletionHostResponse().GetText()
	lines := strings.Split(text, "\n")
	nonEmptyLines := 0
	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			nonEmptyLines++
		}
	}
	return &completionResponseRow{
		RequestEventBase: RequestEventBase{
			RequestId: requestID,
			TenantID:  tenantInfo.TenantId,
			Tenant:    tenantInfo.TenantName,
			Time:      event.Time.AsTime(),
		},
		CharacterCount:    len(text),
		NonemptyLineCount: nonEmptyLines,
	}
}

type completionPostProcessRow struct {
	RequestEventBase
	FilterScore            float32              `bigquery:"filter_score"`
	AppliedFilterThreshold bigquery.NullFloat64 `bigquery:"applied_filter_threshold"`
	FilterReason           bigquery.NullString  `bigquery:"filter_reason"`
}

func getCompletionPostProcessRow(
	requestID string, tenantInfo *pb.TenantInfo, event *pb.RequestEvent,
) *completionPostProcessRow {
	if event.GetCompletionPostProcess() == nil {
		return nil
	}
	postProcess := event.GetCompletionPostProcess()
	result := &completionPostProcessRow{
		RequestEventBase: RequestEventBase{
			RequestId: requestID,
			TenantID:  tenantInfo.TenantId,
			Tenant:    tenantInfo.TenantName,
			Time:      event.Time.AsTime(),
		},
		FilterScore: postProcess.GetFilterScore(),
	}
	if postProcess.AppliedFilterThreshold != nil {
		result.AppliedFilterThreshold = bigquery.NullFloat64{
			Valid:   true,
			Float64: float64(postProcess.GetAppliedFilterThreshold()),
		}
	}
	if postProcess.FilterReason != nil {
		result.FilterReason = bigquery.NullString{
			Valid:     true,
			StringVal: postProcess.GetFilterReason().String(),
		}
	}
	return result
}

type parenthesisTruncationRow struct {
	RequestEventBase
	OriginalLength     int  `bigquery:"original_length"`
	TruncatedLength    int  `bigquery:"truncated_length"`
	WasTruncated       bool `bigquery:"was_truncated"`
	CouldHaveTruncated bool `bigquery:"could_have_truncated"`
}

func getParenthesisTruncationRow(
	requestID string, tenantInfo *pb.TenantInfo, event *pb.RequestEvent,
) *parenthesisTruncationRow {
	if event.GetParenthesisTruncation() == nil {
		return nil
	}

	data := event.GetParenthesisTruncation()
	row := &parenthesisTruncationRow{
		RequestEventBase: RequestEventBase{
			RequestId: requestID,
			TenantID:  tenantInfo.TenantId,
			Tenant:    tenantInfo.TenantName,
			Time:      event.Time.AsTime(),
		},
		OriginalLength:     len(data.GetOriginalText()),
		TruncatedLength:    len(data.GetTruncatedText()),
		WasTruncated:       data.GetWasTruncated(),
		CouldHaveTruncated: data.GetCouldHaveTruncated(),
	}

	return row
}

type SessionEventRow struct {
	SessionEventBase
	EventType string `bigquery:"event_type"`
	EventId   string `bigquery:"event_id"`
	RequestId string `bigquery:"request_id"`
}

func getSessionEventRow(
	sessionID string, tenantInfo *pb.TenantInfo, event *pb.SessionEvent,
) *SessionEventRow {
	var requestID string
	switch event.Event.(type) {
	case *pb.SessionEvent_NextEditSessionEvent:
		requestID = event.GetNextEditSessionEvent().GetRelatedRequestId()
	case *pb.SessionEvent_CompletionRequestIdIssued:
		requestID = event.GetCompletionRequestIdIssued().GetRequestId()
	case *pb.SessionEvent_EditRequestIdIssued:
		requestID = event.GetEditRequestIdIssued().GetRequestId()
	case *pb.SessionEvent_NextEditRequestIdIssued:
		requestID = event.GetNextEditRequestIdIssued().GetRequestId()
	}
	return &SessionEventRow{
		SessionEventBase: SessionEventBase{
			SessionId: sessionID,
			TenantID:  tenantInfo.TenantId,
			Tenant:    tenantInfo.TenantName,
			Time:      event.Time.AsTime(),
		},
		EventType: getSessionEventType(event),
		EventId:   event.GetEventId(),
		RequestId: requestID,
	}
}

type remoteAgentLogRow struct {
	SessionEventBase
	EventId string `bigquery:"event_id"`
}

func getRemoteAgentLogRow(
	sessionID string, tenantInfo *pb.TenantInfo, event *pb.SessionEvent,
) *remoteAgentLogRow {
	if event.GetRemoteAgentLog() == nil {
		return nil
	}
	return &remoteAgentLogRow{
		SessionEventBase: SessionEventBase{
			SessionId: sessionID,
			TenantID:  tenantInfo.TenantId,
			Tenant:    tenantInfo.TenantName,
			Time:      event.Time.AsTime(),
		},
		EventId: event.GetEventId(),
	}
}
