package main

import (
	"bytes"
	"context"
	"fmt"
	"regexp"
	"text/template"
	"time"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/civil"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	pb "github.com/augmentcode/augment/services/request_insight/analytics/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/prometheus/client_golang/prometheus"

	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	defaultTimeZone = "America/Los_Angeles"

	// All queries should use this view instead of the raw request_metadata table, to filter out
	// health check and eval requests.
	metadataTableName = "human_request_metadata"

	// Agent Request Event table -- to get access to number of agent requests sent.
	agentRequestEventTableName = "agent_request_event"

	// Agent Tool Use table -- to get access to number of agent tool uses.
	toolUseTableName = "tool_use_data"
)

var daysRequested = prometheus.NewHistogramVec(
	prometheus.HistogramOpts{
		Name:    "au_request_insight_analytics_days_requested",
		Help:    "Number of days requested by a request with date filters",
		Buckets: []float64{1, 2, 4, 8, 16, 32, 64, 128, 256, 512},
	},
	[]string{"endpoint"},
)

func init() {
	// Register metrics.
	prometheus.MustRegister(
		daysRequested,
	)
}

type analyticsServer struct {
	pb.UnimplementedRequestInsightAnalyticsServer

	bqClient    *bigquery.Client
	datasetName string
	auditLogger *audit.AuditLogger
}

type AnalyticsServerConfig struct {
	ProjectId   string `json:"project_id"`
	DatasetName string `json:"dataset_name"`
}

// Used for parsing BigQuery results for queries that return a date and count.
type bqDateAndCount struct {
	Date  civil.Date
	Count int64
}

func newServer(
	ctx context.Context, serverConfig *AnalyticsServerConfig, auditLogger *audit.AuditLogger,
) (*analyticsServer, error) {
	log.Info().Msgf("Project id: %s", serverConfig.ProjectId)
	log.Info().Msgf("Dataset name: %s", serverConfig.DatasetName)

	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !checkDatasetName(serverConfig.DatasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", serverConfig.DatasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, serverConfig.ProjectId)
	if err != nil {
		return nil, err
	}

	s := analyticsServer{
		bqClient:    bqClient,
		datasetName: serverConfig.DatasetName,
		auditLogger: auditLogger,
	}
	return &s, nil
}

// Check that the provided dataset name contains only letters and underscores. Returns true iff the
// provided name is valid.
func checkDatasetName(name string) bool {
	return regexp.MustCompile(`^[a-zA-Z_]+$`).MatchString(name)
}

func (s analyticsServer) GetDevDays(
	ctx context.Context, req *pb.GetDevDaysRequest,
) (*pb.GetDevDaysResponse, error) {
	// Check for required arguments.
	if req.TenantId == "" || req.DateFilters == nil {
		return nil, status.Error(
			codes.InvalidArgument, "tenant_id and date_filters are required",
		)
	} else if err := checkDateFilters(req.DateFilters); err != nil {
		return nil, err
	}

	dateFilters := req.DateFilters
	numDays := daysBetween(dateFilters)
	daysRequested.WithLabelValues("GetDevDays").Observe(float64(numDays))
	log.Info().Msgf("GetDevDays: requested %d days for tenant %s", numDays, req.TenantId)

	// Check that the caller is authorized to access the requested tenant's data.
	err := s.checkAuthClaims(ctx, req.TenantId)
	if err != nil {
		return nil, err
	}

	// Make sure we have a timezone. For simplicity, we've decided to use the same timezone for all
	// customers for now, so it's expected that callers won't provide this.
	timezone := defaultTimeZone
	if dateFilters.Timezone != nil {
		timezone = dateFilters.GetTimezone()
	}

	// Construct the query.
	// BigQuery doesn't let you to parameterize table names (so we insert these with sprintf), and
	// Go doesn't let you escape backticks (so we include backticks here). Note that we check for
	// malicious dataset names in `newServer`.
	completionResolutionTable := fmt.Sprintf("`%s.completion_resolution`", s.datasetName)
	editResolutionTable := fmt.Sprintf("`%s.edit_resolution`", s.datasetName)
	chatRequestTable := fmt.Sprintf("`%s.chat_host_request`", s.datasetName)
	requestMetadataTable := fmt.Sprintf("`%s.%s`", s.datasetName, metadataTableName)
	query := s.bqClient.Query(fmt.Sprintf(`
		WITH unified_resolution AS (
			SELECT
				completion_resolution.time AS time,
				metadata.user_id AS user_id,
			FROM  %s AS completion_resolution
			JOIN %s AS metadata ON completion_resolution.request_id = metadata.request_id
			WHERE
				completion_resolution.accepted
				AND completion_resolution.tenant_id = @tenant_id
				AND DATE(completion_resolution.time, @timezone) >= DATE(@start_year, @start_month, @start_day)
				AND DATE(completion_resolution.time, @timezone) <= DATE(@end_year, @end_month, @end_day)

			UNION ALL

			SELECT
				edit_resolution.time AS time,
				metadata.user_id AS user_id,
			FROM  %s AS edit_resolution
			JOIN %s AS metadata ON edit_resolution.request_id = metadata.request_id
			WHERE
				edit_resolution.accepted
				AND edit_resolution.tenant_id = @tenant_id
				AND DATE(edit_resolution.time, @timezone) >= DATE(@start_year, @start_month, @start_day)
				AND DATE(edit_resolution.time, @timezone) <= DATE(@end_year, @end_month, @end_day)

			UNION ALL

			SELECT
				chat_request.time AS time,
				metadata.user_id AS user_id,
			FROM %s AS chat_request
			JOIN %s AS metadata ON chat_request.request_id = metadata.request_id
			WHERE
				chat_request.tenant_id = @tenant_id
				AND DATE(chat_request.time, @timezone) >= DATE(@start_year, @start_month, @start_day)
				AND DATE(chat_request.time, @timezone) <= DATE(@end_year, @end_month, @end_day)
		),
		dev_days AS (
			SELECT
				DATE(unified_resolution.time, @timezone) AS date,
				COUNT(DISTINCT unified_resolution.user_id) AS count
			FROM unified_resolution
			GROUP BY date
		)

		SELECT date, count,
		FROM dev_days
		ORDER BY date;
	`,
		completionResolutionTable, requestMetadataTable,
		editResolutionTable, requestMetadataTable,
		chatRequestTable, requestMetadataTable,
	))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "tenant_id", Value: req.TenantId},
		{Name: "timezone", Value: timezone},
		{Name: "start_year", Value: dateFilters.StartDate.Year},
		{Name: "start_month", Value: dateFilters.StartDate.Month},
		{Name: "start_day", Value: dateFilters.StartDate.Day},
		{Name: "end_year", Value: dateFilters.EndDate.Year},
		{Name: "end_month", Value: dateFilters.EndDate.Month},
		{Name: "end_day", Value: dateFilters.EndDate.Day},
	}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, status.Error(codes.InvalidArgument, "Query error")
	}

	// Parse the results.
	devDays, err := parseBqDateAndCounts(it)
	if err != nil {
		return nil, err
	}

	response := pb.GetDevDaysResponse{
		DevDays: addZeroDates(
			devDays, toCivilDate(dateFilters.StartDate), toCivilDate(dateFilters.EndDate),
		),
	}
	return &response, nil
}

func (s analyticsServer) GetActiveUsers(
	ctx context.Context, req *pb.GetActiveUsersRequest,
) (*pb.GetActiveUsersResponse, error) {
	// Check for required arguments.
	if req.TenantId == "" || req.DateFilters == nil {
		return nil, status.Error(
			codes.InvalidArgument, "tenant_id and date_filters are required",
		)
	} else if err := checkDateFilters(req.DateFilters); err != nil {
		return nil, err
	}
	for _, requestType := range req.RequestTypes {
		if requestType == pb.GetActiveUsersRequest_UNKNOWN {
			return nil, status.Error(codes.InvalidArgument, "invalid request type")
		}
	}

	dateFilters := req.DateFilters
	numDays := daysBetween(dateFilters)
	daysRequested.WithLabelValues("GetActiveUsers").Observe(float64(numDays))
	log.Info().Msgf("GetActiveUsers: requested %d days for tenant %s", numDays, req.TenantId)

	// Check that the caller is authorized to access the requested tenant's data.
	err := s.checkAuthClaims(ctx, req.TenantId)
	if err != nil {
		return nil, err
	}

	// Make sure we have a timezone. For simplicity, we've decided to use the same timezone for all
	// customers for now, so it's expected that callers won't provide this.
	timezone := defaultTimeZone
	if dateFilters.Timezone != nil {
		timezone = dateFilters.GetTimezone()
	}

	requestTypes := []string{}
	if len(req.RequestTypes) == 0 {
		requestTypes = []string{"COMPLETION", "EDIT", "CHAT"}
	} else {
		for _, requestType := range req.RequestTypes {
			requestTypes = append(requestTypes, requestType.String())
		}
	}

	// Construct the query.
	// BigQuery doesn't let you to parameterize table names (so we insert these with sprintf), and
	// Go doesn't let you escape backticks (so we include backticks here). Note that we check for
	// malicious dataset names in `newServer`.
	metadataTable := fmt.Sprintf("`%s.%s`", s.datasetName, metadataTableName)
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT
			DATE(metadata.time, @timezone) as date,
			COUNT(DISTINCT metadata.user_id) as count
		FROM %s AS metadata
		WHERE metadata.tenant_id = @tenant_id
			AND DATE(metadata.time, @timezone) >= DATE(@start_year, @start_month, @start_day)
			AND DATE(metadata.time, @timezone) <= DATE(@end_year, @end_month, @end_day)
			AND metadata.request_type IN UNNEST(@request_types)
		GROUP BY date
		ORDER BY date
	`, metadataTable))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "tenant_id", Value: req.TenantId},
		{Name: "timezone", Value: timezone},
		{Name: "request_types", Value: requestTypes},
		{Name: "start_year", Value: dateFilters.StartDate.Year},
		{Name: "start_month", Value: dateFilters.StartDate.Month},
		{Name: "start_day", Value: dateFilters.StartDate.Day},
		{Name: "end_year", Value: dateFilters.EndDate.Year},
		{Name: "end_month", Value: dateFilters.EndDate.Month},
		{Name: "end_day", Value: dateFilters.EndDate.Day},
	}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, status.Error(codes.Internal, "Query error")
	}

	// Parse the results.
	activeUsers, err := parseBqDateAndCounts(it)
	if err != nil {
		return nil, err
	}

	response := pb.GetActiveUsersResponse{
		ActiveUsers: addZeroDates(
			activeUsers, toCivilDate(dateFilters.StartDate), toCivilDate(dateFilters.EndDate),
		),
	}
	return &response, nil
}

func (s analyticsServer) GetCompletionStats(
	ctx context.Context, req *pb.GetCompletionStatsRequest,
) (*pb.GetCompletionStatsResponse, error) {
	// Check for required arguments.
	if req.TenantId == "" || req.DateFilters == nil {
		return nil, status.Error(
			codes.InvalidArgument, "tenant_id and date_filters are required",
		)
	} else if err := checkDateFilters(req.DateFilters); err != nil {
		return nil, err
	}

	dateFilters := req.DateFilters
	numDays := daysBetween(dateFilters)
	daysRequested.WithLabelValues("GetCompletionStats").Observe(float64(numDays))
	log.Info().Msgf("GetCompletionStats: requested %d days for tenant %s", numDays, req.TenantId)

	// Check that the caller is authorized to access the requested tenant's data.
	err := s.checkAuthClaims(ctx, req.TenantId)
	if err != nil {
		return nil, err
	}

	// Make sure we have a timezone. For simplicity, we've decided to use the same timezone for all
	// customers for now, so it's expected that callers won't provide this.
	timezone := defaultTimeZone
	if dateFilters.Timezone != nil {
		timezone = dateFilters.GetTimezone()
	}

	// Construct the query.
	// BigQuery doesn't let you to parameterize table names (so we insert these with sprintf), and
	// Go doesn't let you escape backticks (so we include backticks here). Note that we check for
	// malicious dataset names in `newServer`.
	responseTableName := fmt.Sprintf("`%s.completion_host_response`", s.datasetName)
	resolutionTableName := fmt.Sprintf("`%s.completion_resolution`", s.datasetName)
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT
			date,
			-- We use resolutions to count requests so that we only count completions that the user
			-- actually saw.
			COUNT(resolution.request_id) AS requestCount,
			COUNT(CASE WHEN resolution.accepted THEN 1 ELSE NULL END) AS acceptedCount,
			SUM(CASE WHEN resolution.accepted THEN IFNULL(response.line_count, 0) ELSE 0 END) AS acceptedLineCount,
			SUM(CASE WHEN resolution.accepted THEN IFNULL(response.character_count, 0) ELSE 0 END) AS acceptedCharacterCount,

		-- This returns a row per date in our date range.
		FROM UNNEST(GENERATE_DATE_ARRAY(
			DATE(@start_year, @start_month, @start_day),
			DATE(@end_year, @end_month, @end_day)
		)) AS date

		-- Join completion resolutions and responses. These are left joins so that days with no data
		-- aren't filtered out.
		LEFT JOIN %s AS resolution
			ON date = DATE(resolution.time, @timezone)
			AND resolution.tenant_id = @tenant_id
			-- The filters below aren't necessary for correctness, but they're needed for the query
			-- optimizer to filter out partitions correctly.
			AND DATE(resolution.time) >= DATE(@start_year, @start_month, @start_day)
			AND DATE(resolution.time) <= DATE(@end_year, @end_month, @end_day)
		LEFT JOIN %s AS response
			ON response.request_id = resolution.request_id
			-- The filters below aren't necessary for correctness, but they're needed for the query
			-- optimizer to filter out partitions correctly.
			AND DATE(response.time) >= DATE(@start_year, @start_month, @start_day)
			AND DATE(response.time) <= DATE(@end_year, @end_month, @end_day)

		GROUP BY date
		ORDER BY date
	`, resolutionTableName, responseTableName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "tenant_id", Value: req.TenantId},
		{Name: "timezone", Value: timezone},
		{Name: "start_year", Value: dateFilters.StartDate.Year},
		{Name: "start_month", Value: dateFilters.StartDate.Month},
		{Name: "start_day", Value: dateFilters.StartDate.Day},
		{Name: "end_year", Value: dateFilters.EndDate.Year},
		{Name: "end_month", Value: dateFilters.EndDate.Month},
		{Name: "end_day", Value: dateFilters.EndDate.Day},
	}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, status.Error(codes.Internal, "Query error")
	}

	// Parse the results.
	type bqCompletionStats struct {
		Date                   civil.Date
		RequestCount           int64
		AcceptedCount          int64
		AcceptedLineCount      int64
		AcceptedCharacterCount int64
	}
	var completionStats []*pb.GetCompletionStatsResponse_CompletionStats
	for {
		var bqRes bqCompletionStats
		err := it.Next(&bqRes)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, status.Error(codes.Internal, "Error parsing query results")
		} else {
			completionStats = append(completionStats, &pb.GetCompletionStatsResponse_CompletionStats{
				Aggregation: &pb.GetCompletionStatsResponse_CompletionStats_Date{
					Date: &pb.Date{
						Year:  int32(bqRes.Date.Year),
						Month: int32(bqRes.Date.Month),
						Day:   int32(bqRes.Date.Day),
					},
				},
				RequestCount:           uint32(bqRes.RequestCount),
				AcceptedCount:          uint32(bqRes.AcceptedCount),
				AcceptedLineCount:      uint32(bqRes.AcceptedLineCount),
				AcceptedCharacterCount: uint32(bqRes.AcceptedCharacterCount),
			})
		}
	}

	response := pb.GetCompletionStatsResponse{
		CompletionStats: completionStats,
	}
	return &response, nil
}

func (s analyticsServer) GetEditStats(
	ctx context.Context, req *pb.GetEditStatsRequest,
) (*pb.GetEditStatsResponse, error) {
	// Check for required arguments.
	if req.TenantId == "" || req.DateFilters == nil {
		return nil, status.Error(
			codes.InvalidArgument, "tenant_id and date_filters are required",
		)
	} else if err := checkDateFilters(req.DateFilters); err != nil {
		return nil, err
	}

	dateFilters := req.DateFilters
	numDays := daysBetween(dateFilters)
	daysRequested.WithLabelValues("GetEditStats").Observe(float64(numDays))
	log.Info().Msgf("GetEditStats: requested %d days for tenant %s", numDays, req.TenantId)

	// Check that the caller is authorized to access the requested tenant's data.
	err := s.checkAuthClaims(ctx, req.TenantId)
	if err != nil {
		return nil, err
	}

	// Make sure we have a timezone. For simplicity, we've decided to use the same timezone for all
	// customers for now, so it's expected that callers won't provide this.
	timezone := defaultTimeZone
	if dateFilters.Timezone != nil {
		timezone = dateFilters.GetTimezone()
	}

	// Construct the query.
	// BigQuery doesn't let you to parameterize table names (so we insert these with sprintf), and
	// Go doesn't let you escape backticks (so we include backticks here). Note that we check for
	// malicious dataset names in `newServer`.
	responseTableName := fmt.Sprintf("`%s.edit_host_response`", s.datasetName)
	resolutionTableName := fmt.Sprintf("`%s.edit_resolution`", s.datasetName)
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT
			date,
			-- Counting with the request table here should give a very similar result; we count
			-- resolutions to avoid the extra join.
			COUNT(resolution.request_id) AS requestCount,
			COUNT(CASE WHEN resolution.accepted THEN 1 ELSE NULL END) AS acceptedCount,
			SUM(CASE WHEN resolution.accepted THEN IFNULL(response.line_count, 0) ELSE 0 END) AS acceptedLineCount,
			SUM(CASE WHEN resolution.accepted THEN IFNULL(response.character_count, 0) ELSE 0 END) AS acceptedCharacterCount,

		-- This returns a row per date in our date range.
		FROM UNNEST(GENERATE_DATE_ARRAY(
			DATE(@start_year, @start_month, @start_day),
			DATE(@end_year, @end_month, @end_day)
		)) AS date

		-- Join edit resolutions and responses. These are left joins so that days with no data
		-- aren't filtered out.
		LEFT JOIN %s AS resolution
			ON date = DATE(resolution.time, @timezone)
			AND resolution.tenant_id = @tenant_id
			-- The filters below aren't necessary for correctness, but they're needed for the query
			-- optimizer to filter out partitions correctly.
			AND DATE(resolution.time) >= DATE(@start_year, @start_month, @start_day)
			AND DATE(resolution.time) <= DATE(@end_year, @end_month, @end_day)
		LEFT JOIN %s AS response
			ON response.request_id = resolution.request_id
			-- The filters below aren't necessary for correctness, but they're needed for the query
			-- optimizer to filter out partitions correctly.
			AND DATE(response.time) >= DATE(@start_year, @start_month, @start_day)
			AND DATE(response.time) <= DATE(@end_year, @end_month, @end_day)

		GROUP BY date
		ORDER BY date
	`, resolutionTableName, responseTableName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "tenant_id", Value: req.TenantId},
		{Name: "timezone", Value: timezone},
		{Name: "start_year", Value: dateFilters.StartDate.Year},
		{Name: "start_month", Value: dateFilters.StartDate.Month},
		{Name: "start_day", Value: dateFilters.StartDate.Day},
		{Name: "end_year", Value: dateFilters.EndDate.Year},
		{Name: "end_month", Value: dateFilters.EndDate.Month},
		{Name: "end_day", Value: dateFilters.EndDate.Day},
	}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, status.Error(codes.Internal, "Query error")
	}

	// Parse the results.
	type bqEditStats struct {
		Date                   civil.Date
		RequestCount           int64
		AcceptedCount          int64
		AcceptedLineCount      int64
		AcceptedCharacterCount int64
	}
	var editStats []*pb.GetEditStatsResponse_EditStats
	for {
		var bqRes bqEditStats
		err := it.Next(&bqRes)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, status.Error(codes.Internal, "Error parsing query results")
		} else {
			editStats = append(editStats, &pb.GetEditStatsResponse_EditStats{
				Aggregation: &pb.GetEditStatsResponse_EditStats_Date{
					Date: &pb.Date{
						Year:  int32(bqRes.Date.Year),
						Month: int32(bqRes.Date.Month),
						Day:   int32(bqRes.Date.Day),
					},
				},
				RequestCount:           uint32(bqRes.RequestCount),
				AcceptedCount:          uint32(bqRes.AcceptedCount),
				AcceptedLineCount:      uint32(bqRes.AcceptedLineCount),
				AcceptedCharacterCount: uint32(bqRes.AcceptedCharacterCount),
			})
		}
	}

	response := pb.GetEditStatsResponse{
		EditStats: editStats,
	}
	return &response, nil
}

func (s analyticsServer) GetChatStats(
	ctx context.Context, req *pb.GetChatStatsRequest,
) (*pb.GetChatStatsResponse, error) {
	// Check for required arguments.
	if req.TenantId == "" || req.DateFilters == nil {
		return nil, status.Error(
			codes.InvalidArgument, "tenant_id and date_filters are required",
		)
	} else if err := checkDateFilters(req.DateFilters); err != nil {
		return nil, err
	}

	dateFilters := req.DateFilters
	numDays := daysBetween(dateFilters)
	daysRequested.WithLabelValues("GetChatStats").Observe(float64(numDays))
	log.Info().Msgf("GetChatStats: requested %d days for tenant %s", numDays, req.TenantId)

	// Check that the caller is authorized to access the requested tenant's data.
	err := s.checkAuthClaims(ctx, req.TenantId)
	if err != nil {
		return nil, err
	}

	// Make sure we have a timezone. For simplicity, we've decided to use the same timezone for all
	// customers for now, so it's expected that callers won't provide this.
	timezone := defaultTimeZone
	if dateFilters.Timezone != nil {
		timezone = dateFilters.GetTimezone()
	}

	// Construct the query.
	// BigQuery doesn't let you to parameterize table names (so we insert these with sprintf), and
	// Go doesn't let you escape backticks (so we include backticks here). Note that we check for
	// malicious dataset names in `newServer`.
	metadataTableName := fmt.Sprintf("`%s.%s`", s.datasetName, metadataTableName)
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT
			date,
			COUNT(metadata.request_id) AS requestCount,

		-- This returns a row per date in our date range.
		FROM UNNEST(GENERATE_DATE_ARRAY(
			DATE(@start_year, @start_month, @start_day),
			DATE(@end_year, @end_month, @end_day)
		)) AS date

		-- Join chat requests. These are left joins so that days with no data aren't filtered out. We
		-- use metadata so that we can filter out non-human and Slackbot requests. (From our services'
		-- perspective Slackbot requests are chat requests, but from a customer's perspective they're
		-- different.)
		LEFT JOIN %s AS metadata
			ON date = DATE(metadata.time, @timezone)
			AND metadata.tenant_id = @tenant_id
			AND metadata.request_type = "CHAT"
			-- The filters below aren't necessary for correctness, but they're needed for the query
			-- optimizer to filter out partitions correctly.
			AND DATE(metadata.time) >= DATE(@start_year, @start_month, @start_day)
			AND DATE(metadata.time) <= DATE(@end_year, @end_month, @end_day)

		GROUP BY date
		ORDER BY date
	`, metadataTableName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "tenant_id", Value: req.TenantId},
		{Name: "timezone", Value: timezone},
		{Name: "start_year", Value: dateFilters.StartDate.Year},
		{Name: "start_month", Value: dateFilters.StartDate.Month},
		{Name: "start_day", Value: dateFilters.StartDate.Day},
		{Name: "end_year", Value: dateFilters.EndDate.Year},
		{Name: "end_month", Value: dateFilters.EndDate.Month},
		{Name: "end_day", Value: dateFilters.EndDate.Day},
	}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, status.Error(codes.Internal, "Query error")
	}

	// Parse the results.
	type bqChatStats struct {
		Date         civil.Date
		RequestCount int64
	}
	var chatStats []*pb.GetChatStatsResponse_ChatStats
	for {
		var bqRes bqChatStats
		err := it.Next(&bqRes)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, status.Error(codes.Internal, "Error parsing query results")
		} else {
			chatStats = append(chatStats, &pb.GetChatStatsResponse_ChatStats{
				Aggregation: &pb.GetChatStatsResponse_ChatStats_Date{
					Date: &pb.Date{
						Year:  int32(bqRes.Date.Year),
						Month: int32(bqRes.Date.Month),
						Day:   int32(bqRes.Date.Day),
					},
				},
				RequestCount: uint32(bqRes.RequestCount),
			})
		}
	}

	response := pb.GetChatStatsResponse{
		ChatStats: chatStats,
	}
	return &response, nil
}

func (s analyticsServer) GetKeywordsStats(
	ctx context.Context, req *pb.GetKeywordsStatsRequest,
) (*pb.GetKeywordsStatsResponse, error) {
	// Check for required arguments.
	if req.TenantId == "" || req.DateFilters == nil || req.RequestType == pb.GetKeywordsStatsRequest_UNKNOWN {
		return nil, status.Error(
			codes.InvalidArgument, "tenant_id, date_filters, and request_type are required",
		)
	} else if err := checkDateFilters(req.DateFilters); err != nil {
		return nil, err
	}

	dateFilters := req.DateFilters
	numDays := daysBetween(dateFilters)
	daysRequested.WithLabelValues("GetKeywordStats").Observe(float64(numDays))
	log.Info().Msgf("GetKeywordStats: requested %d days for tenant %s", numDays, req.TenantId)

	// Check that the caller is authorized to access the requested tenant's data.
	err := s.checkAuthClaims(ctx, req.TenantId)
	if err != nil {
		return nil, err
	}

	// Make sure we have a timezone. For simplicity, we've decided to use the same timezone for all
	// customers for now, so it's expected that callers won't provide this.
	timezone := defaultTimeZone
	if dateFilters.Timezone != nil {
		timezone = dateFilters.GetTimezone()
	}

	// Construct the query.
	// BigQuery doesn't let you to parameterize table names (so we insert these with sprintf), and
	// Go doesn't let you escape backticks (so we include backticks here). Note that we check for
	// malicious dataset names in `newServer`.
	var requestTableName string
	var keywordColumnName string
	switch req.RequestType {
	case pb.GetKeywordsStatsRequest_EDIT:
		requestTableName = fmt.Sprintf("`%s.edit_host_request`", s.datasetName)
		keywordColumnName = "keywords"
	case pb.GetKeywordsStatsRequest_CHAT:
		requestTableName = fmt.Sprintf("`%s.chat_host_request`", s.datasetName)
		keywordColumnName = "message_keywords"
	default:
		// This can happen if we add a new request type to the proto without updating the
		// implementation.
		return nil, status.Error(codes.InvalidArgument, "Unsupported request type")
	}
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT
			keyword,
			COUNT(request.time) AS count
		FROM %s AS request,
			UNNEST(request.%s) AS keyword
		WHERE request.tenant_id = @tenant_id
			AND DATE(request.time, @timezone) >= DATE(@start_year, @start_month, @start_day)
			AND DATE(request.time, @timezone) <= DATE(@end_year, @end_month, @end_day)
		GROUP BY keyword
	`, requestTableName, keywordColumnName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "tenant_id", Value: req.TenantId},
		{Name: "timezone", Value: timezone},
		{Name: "start_year", Value: dateFilters.StartDate.Year},
		{Name: "start_month", Value: dateFilters.StartDate.Month},
		{Name: "start_day", Value: dateFilters.StartDate.Day},
		{Name: "end_year", Value: dateFilters.EndDate.Year},
		{Name: "end_month", Value: dateFilters.EndDate.Month},
		{Name: "end_day", Value: dateFilters.EndDate.Day},
	}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, status.Error(codes.Internal, "Query error")
	}

	// Parse the results.
	type bqKeywords struct {
		Keyword string
		Count   int64
	}
	var keywordsStats []*pb.GetKeywordsStatsResponse_KeywordStats
	for {
		var bqRes bqKeywords
		err := it.Next(&bqRes)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, status.Error(codes.Internal, "Error parsing query results")
		} else {
			keywordsStats = append(keywordsStats, &pb.GetKeywordsStatsResponse_KeywordStats{
				Keyword: bqRes.Keyword,
				Count:   uint32(bqRes.Count),
			})
		}
	}

	response := pb.GetKeywordsStatsResponse{
		KeywordsStats: keywordsStats,
	}
	return &response, nil
}

func (s analyticsServer) GetCategoriesStats(
	ctx context.Context, req *pb.GetCategoriesStatsRequest,
) (*pb.GetCategoriesStatsResponse, error) {
	// Check for required arguments.
	if req.TenantId == "" || req.DateFilters == nil || req.RequestType == pb.GetCategoriesStatsRequest_UNKNOWN {
		return nil, status.Error(
			codes.InvalidArgument, "tenant_id, date_filters, and request_type are required",
		)
	} else if err := checkDateFilters(req.DateFilters); err != nil {
		return nil, err
	}

	dateFilters := req.DateFilters
	numDays := daysBetween(dateFilters)
	daysRequested.WithLabelValues("GetCategoriesStats").Observe(float64(numDays))
	log.Info().Msgf("GetCategoriesStats: requested %d days for tenant %s", numDays, req.TenantId)

	// Check that the caller is authorized to access the requested tenant's data.
	err := s.checkAuthClaims(ctx, req.TenantId)
	if err != nil {
		return nil, err
	}

	// Make sure we have a timezone. For simplicity, we've decided to use the same timezone for all
	// customers for now, so it's expected that callers won't provide this.
	timezone := defaultTimeZone
	if dateFilters.Timezone != nil {
		timezone = dateFilters.GetTimezone()
	}

	// Construct the query.
	// BigQuery doesn't let you to parameterize table names (so we insert these with sprintf), and
	// Go doesn't let you escape backticks (so we include backticks here). Note that we check for
	// malicious dataset names in `newServer`.
	var requestTableName string
	var categoriesColumnName string
	switch req.RequestType {
	case pb.GetCategoriesStatsRequest_EDIT:
		requestTableName = fmt.Sprintf("`%s.edit_host_request`", s.datasetName)
		categoriesColumnName = "categories"
	case pb.GetCategoriesStatsRequest_CHAT:
		requestTableName = fmt.Sprintf("`%s.chat_host_request`", s.datasetName)
		categoriesColumnName = "message_categories"
	default:
		// This can happen if we add a new request type to the proto without updating the
		// implementation.
		return nil, status.Error(codes.InvalidArgument, "Unsupported request type")
	}
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT
			category,
			COUNT(request.time) AS count
		FROM %s AS request,
			UNNEST(request.%s) AS category
		WHERE request.tenant_id = @tenant_id
			AND DATE(request.time, @timezone) >= DATE(@start_year, @start_month, @start_day)
			AND DATE(request.time, @timezone) <= DATE(@end_year, @end_month, @end_day)
		GROUP BY category
	`, requestTableName, categoriesColumnName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "tenant_id", Value: req.TenantId},
		{Name: "timezone", Value: timezone},
		{Name: "start_year", Value: dateFilters.StartDate.Year},
		{Name: "start_month", Value: dateFilters.StartDate.Month},
		{Name: "start_day", Value: dateFilters.StartDate.Day},
		{Name: "end_year", Value: dateFilters.EndDate.Year},
		{Name: "end_month", Value: dateFilters.EndDate.Month},
		{Name: "end_day", Value: dateFilters.EndDate.Day},
	}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, status.Error(codes.Internal, "Query error")
	}

	// Parse the results.
	type bqCategories struct {
		Category string
		Count    int64
	}
	var categoriesStats []*pb.GetCategoriesStatsResponse_CategoryStats
	for {
		var bqRes bqCategories
		err := it.Next(&bqRes)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, status.Error(codes.Internal, "Error parsing query results")
		} else {
			categoriesStats = append(categoriesStats, &pb.GetCategoriesStatsResponse_CategoryStats{
				Category: bqRes.Category,
				Count:    uint32(bqRes.Count),
			})
		}
	}

	response := pb.GetCategoriesStatsResponse{
		CategoriesStats: categoriesStats,
	}
	return &response, nil
}

func (s analyticsServer) GetUserLastRequestTimestamp(
	ctx context.Context, req *pb.GetUserLastRequestTimestampRequest,
) (*pb.GetUserLastRequestTimestampResponse, error) {
	// Check for required arguments.
	if req.TenantId == "" || req.UserId == "" || req.MinSearchTimestamp == nil {
		return nil, status.Error(
			codes.InvalidArgument, "tenant_id, user_id, and, min_search_timestamp are required",
		)
	}
	for _, requestType := range req.RequestTypes {
		if requestType == pb.GetUserLastRequestTimestampRequest_UNKNOWN {
			return nil, status.Error(codes.InvalidArgument, "invalid request type")
		}
	}

	// Check that the caller is authorized to access the requested tenant's data.
	err := s.checkUserAuthClaims(ctx, req.TenantId, req.UserId)
	if err != nil {
		return nil, err
	}

	requestTypes := []string{}
	if len(req.RequestTypes) == 0 {
		requestTypes = []string{"COMPLETION", "EDIT", "CHAT"}
	} else {
		for _, requestType := range req.RequestTypes {
			requestTypes = append(requestTypes, requestType.String())
		}
	}

	// Construct the query.
	// BigQuery doesn't let you to parameterize table names (so we insert these with sprintf), and
	// Go doesn't let you escape backticks (so we include backticks here). Note that we check for
	// malicious dataset names in `newServer`.
	metadataTable := fmt.Sprintf("`%s.%s`", s.datasetName, metadataTableName)
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT
			MAX(metadata.time) as last_request_timestamp
		FROM %s AS metadata
		WHERE metadata.tenant_id = @tenant_id
			AND metadata.user_id = @user_id
			AND metadata.request_type IN UNNEST(@request_types)
			AND metadata.time >= @min_search_timestamp
	`, metadataTable))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "tenant_id", Value: req.TenantId},
		{Name: "request_types", Value: requestTypes},
		{Name: "user_id", Value: req.UserId},
		{Name: "min_search_timestamp", Value: req.MinSearchTimestamp.AsTime()},
	}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, status.Error(codes.Internal, "Query error")
	}

	// Parse the results.
	var results []bigquery.Value
	err = it.Next(&results)
	if err != nil {
		log.Error().Msgf("Query results error: %v", err)
		return nil, status.Error(codes.Internal, "Error parsing query results")
	} else if len(results) != 1 {
		log.Error().Msgf(
			"Unexpected query results for GetEarliestRequestTimestamp (expected 1 result, got "+
				"%d). This indicates a bug in the query: %v",
			len(results), results,
		)
		return nil, status.Error(codes.Internal, "Error parsing query results")
	} else if results[0] == nil {
		// This is expected if the user hasn't made any requests yet.
		return &pb.GetUserLastRequestTimestampResponse{}, nil
	}

	lastRequestTime, ok := results[0].(time.Time)
	if !ok {
		log.Error().Msgf("Unexpected query results (expected time, got %T): %v", results[0], results[0])
		return nil, status.Error(codes.Internal, "Error parsing query results")
	}

	return &pb.GetUserLastRequestTimestampResponse{
		LastRequestTimestamp: timestamppb.New(lastRequestTime),
	}, nil
}

func (s analyticsServer) GetUserChatRequestStats(
	ctx context.Context, req *pb.GetUserChatRequestStatsRequest,
) (*pb.GetUserChatRequestStatsResponse, error) {
	// Function to return the number of chat requests made by a user in a given time period.
	// Check for required arguments.
	if req.TenantId == "" || req.UserId == "" || req.DateFilters == nil {
		return nil, status.Error(
			codes.InvalidArgument, "tenant_id, user_id, and date_filters are required",
		)
	} else if err := checkDateFilters(req.DateFilters); err != nil {
		return nil, err
	}

	// Check that the caller is authorized to access the requested tenant's data.
	err := s.checkUserAuthClaims(ctx, req.TenantId, req.UserId)
	if err != nil {
		return nil, err
	}

	dateFilters := req.DateFilters
	numDays := daysBetween(dateFilters)
	daysRequested.WithLabelValues("GetUserChatRequestStats").Observe(float64(numDays))
	log.Info().Msgf("GetUserChatRequestStats: requested %d days for tenant %s", numDays, req.TenantId)

	timezone := defaultTimeZone
	if req.DateFilters.Timezone != nil {
		timezone = req.DateFilters.GetTimezone()
	}

	// Construct the query.
	// BigQuery doesn't let you to parameterize table names (so we insert these with sprintf), and
	// Go doesn't let you escape backticks (so we include backticks here). Note that we check for
	// malicious dataset names in `newServer`.
	metadataTable := fmt.Sprintf("`%s.%s`", s.datasetName, metadataTableName)
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT
			COUNT(DISTINCT metadata.request_id) AS requestCount
		FROM %s AS metadata
		WHERE metadata.tenant_id = @tenant_id
			AND metadata.opaque_user_id = @user_id
			AND metadata.request_type = "CHAT"
			AND metadata.user_id_type = "AUGMENT"
			AND DATE(metadata.time, @timezone) >= DATE(@start_year, @start_month, @start_day)
			AND DATE(metadata.time, @timezone) <= DATE(@end_year, @end_month, @end_day)
	`, metadataTable))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "tenant_id", Value: req.TenantId},
		{Name: "user_id", Value: req.UserId},
		{Name: "timezone", Value: timezone},
		{Name: "start_year", Value: dateFilters.StartDate.Year},
		{Name: "start_month", Value: dateFilters.StartDate.Month},
		{Name: "start_day", Value: dateFilters.StartDate.Day},
		{Name: "end_year", Value: dateFilters.EndDate.Year},
		{Name: "end_month", Value: dateFilters.EndDate.Month},
		{Name: "end_day", Value: dateFilters.EndDate.Day},
	}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, status.Error(codes.Internal, "Query error")
	}

	// Parse the results.
	var results []bigquery.Value
	err = it.Next(&results)
	if err != nil {
		log.Error().Msgf("Query results error: %v", err)
		return nil, status.Error(codes.Internal, "Error parsing query results")
	} else if len(results) != 1 {
		log.Error().Msgf(
			"Unexpected query results for GetUserChatRequestStats (expected 1 result, got %d). "+
				"This indicates a bug in the query: %v",
			len(results), results,
		)
		return nil, status.Error(codes.Internal, "Error parsing query results")
	} else if results[0] == nil {
		// This is expected if the user hasn't made any requests yet.
		return &pb.GetUserChatRequestStatsResponse{}, nil
	}

	requestCount, ok := results[0].(int64)
	if !ok {
		log.Error().Msgf("Unexpected query results (expected int64, got %T): %v", results[0], results[0])
		return nil, status.Error(codes.Internal, "Error parsing query results")
	}

	return &pb.GetUserChatRequestStatsResponse{
		RequestCount: uint32(requestCount),
	}, nil
}

func (s analyticsServer) GetUserAgentRequestStats(
	ctx context.Context, req *pb.GetUserAgentRequestStatsRequest,
) (*pb.GetUserAgentRequestStatsResponse, error) {
	// Function to return the number of chat requests made by a user in a given time period.
	// Check for required arguments.
	if req.TenantId == "" || req.UserId == "" || req.DateFilters == nil {
		return nil, status.Error(
			codes.InvalidArgument, "tenant_id, user_id, and date_filters are required",
		)
	} else if err := checkDateFilters(req.DateFilters); err != nil {
		return nil, err
	}

	// Check that the caller is authorized to access the requested tenant's data.
	err := s.checkUserAuthClaims(ctx, req.TenantId, req.UserId)
	if err != nil {
		return nil, err
	}

	dateFilters := req.DateFilters
	numDays := daysBetween(dateFilters)
	daysRequested.WithLabelValues("GetUserAgentRequestStats").Observe(float64(numDays))
	log.Info().Msgf("GetUserAgentRequestStats: requested %d days for tenant %s", numDays, req.TenantId)

	timezone := defaultTimeZone
	if req.DateFilters.Timezone != nil {
		timezone = req.DateFilters.GetTimezone()
	}

	// Construct the query.
	// BigQuery doesn't let you to parameterize table names (so we insert these with sprintf), and
	// Go doesn't let you escape backticks (so we include backticks here). Note that we check for
	// malicious dataset names in `newServer`.
	metadataTable := fmt.Sprintf("`%s.%s`", s.datasetName, metadataTableName)
	agentRequestTable := fmt.Sprintf("`%s.%s`", s.datasetName, agentRequestEventTableName)
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT
			COUNT(DISTINCT agent_request.request_id) AS requestCount
		FROM %s AS metadata INNER JOIN %s AS agent_request ON metadata.request_id = agent_request.request_id
		WHERE metadata.tenant_id = @tenant_id
			AND metadata.opaque_user_id = @user_id
			AND metadata.request_type = "AGENT_CHAT"
			AND metadata.user_id_type = "AUGMENT"
			AND STRING(agent_request.sanitized_json.event_name) = "sent-user-message"
			AND DATE(metadata.time, @timezone) >= DATE(@start_year, @start_month, @start_day)
			AND DATE(metadata.time, @timezone) <= DATE(@end_year, @end_month, @end_day)
			AND DATE(agent_request.time, @timezone) >= DATE(@start_year, @start_month, @start_day)
			AND DATE(agent_request.time, @timezone) <= DATE(@end_year, @end_month, @end_day)
	`, metadataTable, agentRequestTable))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "tenant_id", Value: req.TenantId},
		{Name: "user_id", Value: req.UserId},
		{Name: "timezone", Value: timezone},
		{Name: "start_year", Value: dateFilters.StartDate.Year},
		{Name: "start_month", Value: dateFilters.StartDate.Month},
		{Name: "start_day", Value: dateFilters.StartDate.Day},
		{Name: "end_year", Value: dateFilters.EndDate.Year},
		{Name: "end_month", Value: dateFilters.EndDate.Month},
		{Name: "end_day", Value: dateFilters.EndDate.Day},
	}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, status.Error(codes.Internal, "Query error")
	}

	// Parse the results.
	var results []bigquery.Value
	err = it.Next(&results)
	if err != nil {
		log.Error().Msgf("Query results error: %v", err)
		return nil, status.Error(codes.Internal, "Error parsing query results")
	} else if len(results) != 1 {
		log.Error().Msgf(
			"Unexpected query results for GetUserAgentRequestStats (expected 1 result, got %d). "+
				"This indicates a bug in the query: %v",
			len(results), results,
		)
		return nil, status.Error(codes.Internal, "Error parsing query results")
	} else if results[0] == nil {
		// This is expected if the user hasn't made any requests yet.
		return &pb.GetUserAgentRequestStatsResponse{}, nil
	}

	requestCount, ok := results[0].(int64)
	if !ok {
		log.Error().Msgf("Unexpected query results (expected int64, got %T): %v", results[0], results[0])
		return nil, status.Error(codes.Internal, "Error parsing query results")
	}

	return &pb.GetUserAgentRequestStatsResponse{
		RequestCount: uint32(requestCount),
	}, nil
}

func (s analyticsServer) GetUserAgentToolUseStats(
	ctx context.Context, req *pb.GetUserAgentToolUseStatsRequest,
) (*pb.GetUserAgentToolUseStatsResponse, error) {
	// Function to return the number of agent tool uses made by a user in a given time period.
	// Check for required arguments.
	if req.TenantId == "" || req.UserId == "" || req.DateFilters == nil {
		return nil, status.Error(
			codes.InvalidArgument, "tenant_id, user_id, and date_filters are required",
		)
	} else if err := checkDateFilters(req.DateFilters); err != nil {
		return nil, err
	}

	// Check that the caller is authorized to access the requested tenant's data.
	err := s.checkUserAuthClaims(ctx, req.TenantId, req.UserId)
	if err != nil {
		return nil, err
	}

	dateFilters := req.DateFilters
	numDays := daysBetween(dateFilters)
	daysRequested.WithLabelValues("GetUserAgentToolUseStats").Observe(float64(numDays))
	log.Info().Msgf("GetUserAgentToolUseStats: requested %d days for tenant %s", numDays, req.TenantId)

	timezone := defaultTimeZone
	if req.DateFilters.Timezone != nil {
		timezone = req.DateFilters.GetTimezone()
	}

	// Construct the query.
	// BigQuery doesn't let you to parameterize table names (so we insert these with sprintf), and
	// Go doesn't let you escape backticks (so we include backticks here). Note that we check for
	// malicious dataset names in `newServer`.
	metadataTable := fmt.Sprintf("`%s.%s`", s.datasetName, metadataTableName)
	toolUseTable := fmt.Sprintf("`%s.%s`", s.datasetName, toolUseTableName)
	// This table involves a join because:
	// 1. The tool use data info is not sufficient, as tool uses may also be triggered by chat with tools
	// 2. The metadata table does not have tool use information, but it does have information about agent request IDs
	// Therefore, we join the metadata table with the tool use table to get agent tool calls.
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT
			COUNT(DISTINCT STRING(sanitized_json.tool_use_id)) AS requestCount
		FROM %s AS metadata INNER JOIN %s AS request ON metadata.request_id = request.request_id
		WHERE metadata.tenant_id = @tenant_id
			AND metadata.opaque_user_id = @user_id
			AND metadata.request_type = "AGENT_CHAT"
			AND metadata.user_id_type = "AUGMENT"
			AND DATE(metadata.time, @timezone) >= DATE(@start_year, @start_month, @start_day)
			AND DATE(metadata.time, @timezone) <= DATE(@end_year, @end_month, @end_day)
			AND DATE(request.time, @timezone) >= DATE(@start_year, @start_month, @start_day)
			AND DATE(request.time, @timezone) <= DATE(@end_year, @end_month, @end_day)
	`, metadataTable, toolUseTable))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "tenant_id", Value: req.TenantId},
		{Name: "user_id", Value: req.UserId},
		{Name: "timezone", Value: timezone},
		{Name: "start_year", Value: dateFilters.StartDate.Year},
		{Name: "start_month", Value: dateFilters.StartDate.Month},
		{Name: "start_day", Value: dateFilters.StartDate.Day},
		{Name: "end_year", Value: dateFilters.EndDate.Year},
		{Name: "end_month", Value: dateFilters.EndDate.Month},
		{Name: "end_day", Value: dateFilters.EndDate.Day},
	}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, status.Error(codes.Internal, "Query error")
	}

	// Parse the results.
	var results []bigquery.Value
	err = it.Next(&results)
	if err != nil {
		log.Error().Msgf("Query results error: %v", err)
		return nil, status.Error(codes.Internal, "Error parsing query results")
	} else if len(results) != 1 {
		log.Error().Msgf(
			"Unexpected query results for GetUserAgentToolUseStats (expected 1 result, got %d). "+
				"This indicates a bug in the query: %v",
			len(results), results,
		)
		return nil, status.Error(codes.Internal, "Error parsing query results")
	} else if results[0] == nil {
		// This is expected if the user hasn't made any tool uses yet.
		return &pb.GetUserAgentToolUseStatsResponse{}, nil
	}

	toolUseCount, ok := results[0].(int64)
	if !ok {
		log.Error().Msgf("Unexpected query results (expected int64, got %T): %v", results[0], results[0])
		return nil, status.Error(codes.Internal, "Error parsing query results")
	}

	return &pb.GetUserAgentToolUseStatsResponse{
		ToolUseCount: uint32(toolUseCount),
	}, nil
}

func (s analyticsServer) GetEarliestRequestTimestamp(
	ctx context.Context, req *pb.GetEarliestRequestTimestampRequest,
) (*pb.GetEarliestRequestTimestampResponse, error) {
	// Check for required arguments.
	if req.TenantId == "" {
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}

	log.Info().Msgf("GetEarliestRequestTimestamp: requested for tenant %s", req.TenantId)

	// Check that the caller is authorized to access the requested tenant's data.
	err := s.checkAuthClaims(ctx, req.TenantId)
	if err != nil {
		return nil, err
	}

	// Construct the query.
	// BigQuery doesn't let you parameterize table names (so we insert these with sprintf), and
	// Go doesn't let you escape backticks (so we include backticks here). Note that we check for
	// malicious dataset names in `newServer`.
	tableName := fmt.Sprintf("`%s.%s`", s.datasetName, metadataTableName)
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT MIN(metadata.time) AS time
		FROM %s AS metadata
		WHERE metadata.tenant_id = @tenant_id
	`, tableName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "tenant_id", Value: req.TenantId},
	}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, status.Error(codes.Internal, "Query error")
	}

	// Parse the results.
	var results []bigquery.Value
	err = it.Next(&results)
	if err != nil {
		log.Error().Msgf("Query results error: %v", err)
		return nil, status.Error(codes.Internal, "Error parsing query results")
	} else if len(results) != 1 {
		log.Error().Msgf(
			"Unexpected query results for GetEarliestRequestTimestamp (expected 1 result, got "+
				"%d). This indicates a bug in the query: %v",
			len(results), results,
		)
		return nil, status.Error(codes.Internal, "Error parsing query results")
	} else if results[0] == nil {
		// This is expected if the tenant hasn't made any requests yet.
		return &pb.GetEarliestRequestTimestampResponse{}, nil
	}

	earliestRequestTime, ok := results[0].(time.Time)
	if !ok {
		log.Error().Msgf("Unexpected query results (expected time, got %T): %v", results[0], results[0])
		return nil, status.Error(codes.Internal, "Error parsing query results")
	}

	return &pb.GetEarliestRequestTimestampResponse{
		EarliestRequestTimestamp: timestamppb.New(earliestRequestTime),
	}, nil
}

func (s *analyticsServer) ForgetUser(
	ctx context.Context, req *pb.ForgetUserRequest,
) (*pb.ForgetUserResponse, error) {
	// Check for PII_ADMIN scope.
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.PermissionDenied, "Invalid context")
	} else if !authClaims.HasScope(tokenexchangeproto.Scope_PII_ADMIN) {
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	// Check for required arguments.
	if req.UserId == "" || req.UserEmail == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id and user_email are required")
	}

	iapEmail, ok := authClaims.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			"",
			fmt.Sprintf("ForgetUser request received for user %s", req.UserId),
		)
	} else {
		s.auditLogger.WriteAuditLog(
			authClaims.UserID,
			authClaims.OpaqueUserIDType,
			"",
			fmt.Sprintf("ForgetUser request received for user %s", req.UserId),
		)
	}

	queryTemplate := `
		UPDATE {{.Table}}
		SET {{range $i, $col := .PIIColumns}}{{if $i}},{{end}}{{$col}} = "_DELETED_"{{end}}
		WHERE {{.IDColumn}} = @idValue
	`
	t := template.New("query")
	t, err := t.Parse(queryTemplate)
	if err != nil {
		log.Ctx(ctx).Error().Msgf("Failed to parse query template: %v", err)
		return nil, status.Error(codes.Internal, "Failed to parse query template")
	}

	// Note(jacqueline): For reasons that I don't understand, the bigquery emulator doesn't respect
	// the query's DefaultDatasetID setting here, so we need to fully qualify the table names.
	type queryData struct {
		Table      string
		PIIColumns []string
		IDColumn   string
		IDValue    string
	}
	piiInventory := []queryData{
		{
			Table:      fmt.Sprintf("`%s.request_metadata`", s.datasetName),
			PIIColumns: []string{"user_id", "user_email"},
			IDColumn:   "opaque_user_id",
			IDValue:    req.UserId,
		},
		{
			Table:      fmt.Sprintf("`%s.completion_request`", s.datasetName),
			PIIColumns: []string{"user_id"},
			IDColumn:   "request_id",
			IDValue:    req.UserId,
		},
		{
			Table:      fmt.Sprintf("`%s.add_user_to_tenant`", s.datasetName),
			PIIColumns: []string{"user_email"},
			IDColumn:   "augment_user_id",
			IDValue:    req.UserId,
		},
		{
			Table:      fmt.Sprintf("`%s.remove_user_from_tenant`", s.datasetName),
			PIIColumns: []string{"user_email"},
			IDColumn:   "augment_user_id",
			IDValue:    req.UserId,
		},
		{
			Table:      fmt.Sprintf("`%s.invite_user_to_tenant`", s.datasetName),
			PIIColumns: []string{"inviter_email"},
			IDColumn:   "inviter_email",
			IDValue:    req.UserEmail,
		},
		{
			Table:      fmt.Sprintf("`%s.invite_user_to_tenant`", s.datasetName),
			PIIColumns: []string{"invitee_email"},
			IDColumn:   "invitee_email",
			IDValue:    req.UserEmail,
		},
		{
			Table:      fmt.Sprintf("`%s.accept_invitation`", s.datasetName),
			PIIColumns: []string{"invitee_email"},
			IDColumn:   "invitee_email",
			IDValue:    req.UserEmail,
		},
		{
			Table:      fmt.Sprintf("`%s.decline_invitation`", s.datasetName),
			PIIColumns: []string{"invitee_email"},
			IDColumn:   "invitee_email",
			IDValue:    req.UserEmail,
		},
	}

	queries := []*bigquery.Query{}
	for _, piiTable := range piiInventory {
		var queryBuf bytes.Buffer
		err = t.Execute(
			&queryBuf,
			&piiTable,
		)
		if err != nil {
			log.Ctx(ctx).Error().Msgf("Failed to execute query template: %v", err)
			return nil, status.Error(codes.Internal, "Failed to execute query template")
		}

		query := s.bqClient.Query(queryBuf.String())
		query.DryRun = req.DryRun
		query.Parameters = []bigquery.QueryParameter{
			{Name: "idValue", Value: piiTable.IDValue},
		}
		queries = append(queries, query)
	}

	rowsUpdated := uint32(0)
	for _, query := range queries {
		job, err := query.Run(ctx)
		if err != nil {
			log.Ctx(ctx).Error().Msgf("Failed to run query: %v", err)
			return nil, status.Error(codes.Internal, "Failed to run query")
		}

		if !req.DryRun {
			jobStatus, err := job.Wait(ctx)
			if err != nil {
				log.Ctx(ctx).Error().Msgf("Failed to wait for query job: %v", err)
				return nil, status.Error(codes.Internal, "Failed to wait for query job")
			}

			if jobStatus.Err() != nil {
				log.Ctx(ctx).Error().Msgf("Query job failed: %v", jobStatus.Err())
				return nil, status.Error(codes.Internal, "Query job failed")
			}
		}

		jobStatus := job.LastStatus()
		if jobStatus == nil {
			log.Ctx(ctx).Error().Msgf("Failed to get job status")
			return nil, status.Error(codes.Internal, "Failed to get job status")
		}
		stats := jobStatus.Statistics.Details.(*bigquery.QueryStatistics)
		rowsUpdated += uint32(stats.NumDMLAffectedRows)
		log.Ctx(ctx).Info().Msgf("Query `%s` updated %d rows", query.Q, stats.NumDMLAffectedRows)
	}

	queryStrings := []string{}
	for _, query := range queries {
		queryStrings = append(queryStrings, query.Q)
	}
	return &pb.ForgetUserResponse{Queries: queryStrings, RowsUpdated: rowsUpdated}, nil
}

// Parse the results of a BigQuery query that returns a date and count into the proto DateAndCount
// representation. If something goes wrong, returns an error that can be returned directly to the
// caller.
func parseBqDateAndCounts(bqIt *bigquery.RowIterator) ([]*pb.DateAndCount, error) {
	var res []*pb.DateAndCount
	for {
		var bqRes bqDateAndCount
		err := bqIt.Next(&bqRes)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, status.Error(codes.Internal, "Error parsing query results")
		} else {
			res = append(res, &pb.DateAndCount{
				Date: &pb.Date{
					Year:  int32(bqRes.Date.Year),
					Month: int32(bqRes.Date.Month),
					Day:   int32(bqRes.Date.Day),
				},
				Count: int32(bqRes.Count),
			})
		}
	}
	return res, nil
}

// Add zero-count dates to the provided data for all dates between start and end, inclusive. Data
// should be sorted by date, ascending. This is useful for filling in data for BigQuery queries that
// don't return dates with no data.
func addZeroDates(data []*pb.DateAndCount, start, end civil.Date) []*pb.DateAndCount {
	dataIdx := 0
	var res []*pb.DateAndCount
	for date := start; date.Compare(end) <= 0; date = date.AddDays(1) {
		var count int32
		if dataIdx < len(data) && toCivilDate(data[dataIdx].Date).Compare(date) == 0 {
			count = data[dataIdx].Count
			dataIdx++
		} else {
			count = 0
		}

		res = append(res, &pb.DateAndCount{
			Date: &pb.Date{
				Year:  int32(date.Year),
				Month: int32(date.Month),
				Day:   int32(date.Day),
			},
			Count: count,
		})
	}
	return res
}

// Returns an error that can be returned directly to the caller if the provided date filters are
// invalid.
func checkDateFilters(filters *pb.DateFilters) error {
	if filters.StartDate == nil || filters.EndDate == nil {
		return status.Error(codes.InvalidArgument, "start_date and end_date are required")
	}

	startDate := toCivilDate(filters.StartDate)
	endDate := toCivilDate(filters.EndDate)
	if !startDate.IsValid() || !endDate.IsValid() {
		return status.Error(codes.InvalidArgument, "invalid date")
	} else if startDate.After(endDate) {
		return status.Error(codes.InvalidArgument, "start_date must be before end_date")
	}

	return nil
}

// Returns an error that can be returned directly to the caller if provided gRPC context does not
// contain auth claims that give access to the provided tenant id and user id.
func (s analyticsServer) checkUserAuthClaims(ctx context.Context, tenantID string, userId string) error {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Error().Msgf("Failed to get auth claims from context")
		return status.Error(codes.PermissionDenied, "Access denied")
	} else if authClaims.UserID != userId {
		log.Error().Msgf("Auth claims do not give permission for user")
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	return s.checkAuthClaims(ctx, tenantID)
}

// Returns an error that can be returned directly to the caller if provided gRPC context does not
// contain auth claims that give access to the provided tenant id.
func (s analyticsServer) checkAuthClaims(ctx context.Context, tenantID string) error {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Error().Msgf("Failed to get auth claims from context")
		return status.Error(codes.PermissionDenied, "Access denied")
	} else if authClaims.TenantID != tenantID {
		log.Error().Msgf(
			"Auth claims give permission for tenant %s, but request has tenant %s",
			authClaims.TenantID, tenantID,
		)
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	return nil
}

// Convert a proto date to a civil date.
func toCivilDate(pbDate *pb.Date) civil.Date {
	return civil.Date{
		Year:  int(pbDate.Year),
		Month: time.Month(pbDate.Month),
		Day:   int(pbDate.Day),
	}
}

// Returns the number of days between the start and end dates of the provided date filters. This is
// intended for use in metrics and logging, not queries.
func daysBetween(dateFilters *pb.DateFilters) int {
	start := toCivilDate(dateFilters.StartDate)
	end := toCivilDate(dateFilters.EndDate)
	return end.DaysSince(start) + 1
}
