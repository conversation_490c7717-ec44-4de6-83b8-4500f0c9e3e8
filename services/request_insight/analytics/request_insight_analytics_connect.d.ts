// @generated by protoc-gen-connect-es v1.4.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/request_insight/analytics/request_insight_analytics.proto (package request_insight.analytics, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { ForgetUserRequest, ForgetUserResponse, GetActiveUsersRequest, GetActiveUsersResponse, GetCategoriesStatsRequest, GetCategoriesStatsResponse, GetChatStatsRequest, GetChatStatsResponse, GetCompletionStatsRequest, GetCompletionStatsResponse, GetDevDaysRequest, GetDevDaysResponse, GetEarliestRequestTimestampRequest, GetEarliestRequestTimestampResponse, GetEditStatsRequest, GetEditStatsResponse, GetKeywordsStatsRequest, GetKeywordsStatsResponse, GetUserAgentRequestStatsRequest, GetUserAgentRequestStatsResponse, GetUserAgentToolUseStatsRequest, GetUserAgentToolUseStatsResponse, GetUserChatRequestStatsRequest, GetUserChatRequestStatsResponse, GetUserLastRequestTimestampRequest, GetUserLastRequestTimestampResponse } from "./request_insight_analytics_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service request_insight.analytics.RequestInsightAnalytics
 */
export declare const RequestInsightAnalytics: {
  readonly typeName: "request_insight.analytics.RequestInsightAnalytics",
  readonly methods: {
    /**
     * @generated from rpc request_insight.analytics.RequestInsightAnalytics.GetDevDays
     */
    readonly getDevDays: {
      readonly name: "GetDevDays",
      readonly I: typeof GetDevDaysRequest,
      readonly O: typeof GetDevDaysResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc request_insight.analytics.RequestInsightAnalytics.GetActiveUsers
     */
    readonly getActiveUsers: {
      readonly name: "GetActiveUsers",
      readonly I: typeof GetActiveUsersRequest,
      readonly O: typeof GetActiveUsersResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc request_insight.analytics.RequestInsightAnalytics.GetCompletionStats
     */
    readonly getCompletionStats: {
      readonly name: "GetCompletionStats",
      readonly I: typeof GetCompletionStatsRequest,
      readonly O: typeof GetCompletionStatsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc request_insight.analytics.RequestInsightAnalytics.GetEditStats
     */
    readonly getEditStats: {
      readonly name: "GetEditStats",
      readonly I: typeof GetEditStatsRequest,
      readonly O: typeof GetEditStatsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc request_insight.analytics.RequestInsightAnalytics.GetChatStats
     */
    readonly getChatStats: {
      readonly name: "GetChatStats",
      readonly I: typeof GetChatStatsRequest,
      readonly O: typeof GetChatStatsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc request_insight.analytics.RequestInsightAnalytics.GetKeywordsStats
     */
    readonly getKeywordsStats: {
      readonly name: "GetKeywordsStats",
      readonly I: typeof GetKeywordsStatsRequest,
      readonly O: typeof GetKeywordsStatsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc request_insight.analytics.RequestInsightAnalytics.GetCategoriesStats
     */
    readonly getCategoriesStats: {
      readonly name: "GetCategoriesStats",
      readonly I: typeof GetCategoriesStatsRequest,
      readonly O: typeof GetCategoriesStatsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc request_insight.analytics.RequestInsightAnalytics.GetEarliestRequestTimestamp
     */
    readonly getEarliestRequestTimestamp: {
      readonly name: "GetEarliestRequestTimestamp",
      readonly I: typeof GetEarliestRequestTimestampRequest,
      readonly O: typeof GetEarliestRequestTimestampResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc request_insight.analytics.RequestInsightAnalytics.GetUserLastRequestTimestamp
     */
    readonly getUserLastRequestTimestamp: {
      readonly name: "GetUserLastRequestTimestamp",
      readonly I: typeof GetUserLastRequestTimestampRequest,
      readonly O: typeof GetUserLastRequestTimestampResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc request_insight.analytics.RequestInsightAnalytics.GetUserChatRequestStats
     */
    readonly getUserChatRequestStats: {
      readonly name: "GetUserChatRequestStats",
      readonly I: typeof GetUserChatRequestStatsRequest,
      readonly O: typeof GetUserChatRequestStatsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc request_insight.analytics.RequestInsightAnalytics.GetUserAgentRequestStats
     */
    readonly getUserAgentRequestStats: {
      readonly name: "GetUserAgentRequestStats",
      readonly I: typeof GetUserAgentRequestStatsRequest,
      readonly O: typeof GetUserAgentRequestStatsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc request_insight.analytics.RequestInsightAnalytics.GetUserAgentToolUseStats
     */
    readonly getUserAgentToolUseStats: {
      readonly name: "GetUserAgentToolUseStats",
      readonly I: typeof GetUserAgentToolUseStatsRequest,
      readonly O: typeof GetUserAgentToolUseStatsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc request_insight.analytics.RequestInsightAnalytics.ForgetUser
     */
    readonly forgetUser: {
      readonly name: "ForgetUser",
      readonly I: typeof ForgetUserRequest,
      readonly O: typeof ForgetUserResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

