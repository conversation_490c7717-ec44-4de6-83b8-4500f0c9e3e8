local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';

// K8S deployment file for the request insight analytics service.
function(env, namespace, cloud, namespace_config)
  assert cloudInfo.isCentralNamespace(env, namespace, cloud);

  local datasetLib = (import 'services/request_insight/analytics_dataset/dataset_lib.jsonnet')(
    cloud, env, namespace, namespace_config.flags.useSharedDevRequestInsightBigquery
  );
  local bigqueryLib = import 'services/request_insight/lib/bigquery_lib.jsonnet';

  local appName = 'request-insight-analytics';
  local shortAppName = 'ri-analytics';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  local serviceAccount = gcpLib.createServiceAccount(
    app=appName, cloud=cloud, env=env, namespace=namespace, iam=true, overridePrefix=shortAppName
  );
  local grpcService = grpcLib.grpcService(appName=appName, namespace=namespace);

  // Analytics isn't a true global service (it's deployed to both us-central1 and eu-west4), but
  // we want to be able to access EU analytics from the US (since the customer-ui Remix backend is
  // only deployed in the US), so we need to deploy the global loadbalancer.
  local globalGrpcHostname = grpcLib.globalGrpcServiceHostname(cloud=cloud, serviceName=appName, namespace=namespace);
  local globalGrpcService = grpcLib.globalGrpcService(cloud=cloud, appName=appName, serviceBaseName=appName, namespace=namespace);

  local serverCert = certLib.createCentralServerCert(
    name='%s-server-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace) + [globalGrpcHostname],
    volumeName='certs'
  );
  local clientCert = certLib.createCentralClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    env=env,
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
    appName=appName,
    volumeName='client-certs',
  );
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  local bigqueryAccess = [
    // Read/write access is needed for running PII deletion queries. Any endpoints that write should
    // require PII_ADMIN scope.
    {
      apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
      kind: 'CloudIdentityMembership',
      metadata: {
        name: '%s-dataset-read-write-access-membership' % appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        groupRef: {
          name: datasetLib.cloudIdentityGroup,
          namespace: datasetLib.dataNamespace,
        },
        preferredMemberKey: {
          id: serviceAccount.serviceAccountGcpEmailAddress,
        },
        roles: [
          {
            name: 'MEMBER',
          },
        ],
      },
    },
    // Give permission to start BigQuery jobs (needed to run queries).
    gcpLib.grantAccess(
      name='%s-bigquery-job-policy' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'Project',
        external: 'project/%s' % cloudInfo[cloud].projectId,
      },
      bindings=[
        {
          role: 'roles/bigquery.jobUser',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ]
    ),
    // Grant access to PII. The service has access to everyone's PII, and we rely on queries to
    // filter to the authorized tenant.
    gcpLib.grantAccess(
      name='%s-pii-finegrained-reader-access' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'DataCatalogPolicyTag',
        external: bigqueryLib.dataAccessPolicyTag(cloud, env, 'pii'),
      },
      bindings=[
        {
          role: 'roles/datacatalog.categoryFineGrainedReader',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ],
    ),
  ];

  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config={
    port: 50051,
    server_mtls: if mtls then serverCert.config else null,
    client_mtls: if mtls then clientCert.config else null,
    prom_port: 9090,
    project_id: cloudInfo[cloud].projectId,
    dataset_name: datasetLib.datasetGcp,

    namespace: namespace,
    token_exchange_endpoint: endpoints.getTokenExchangeGrpcUrl(env, namespace, cloud),
  });

  local container =
    {
      name: appName,
      target: {
        name: '//services/request_insight/analytics:image',
        dst: appName,
      },
      args: [
        '--config',
        configMap.filename,
      ],
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % appName, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % appName, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      volumeMounts: [
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
      ],
      resources: {
        limits: {
          cpu: 0.2,
          memory: '512Mi',
        },
      },
    };

  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    tolerations: tolerations,
    affinity: affinity,
    volumes: [
      serverCert.podVolumeDef,
      clientCert.podVolumeDef,
      configMap.podVolumeDef,
    ],
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: 1,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
          annotations: {
            'reloader.stakater.com/auto': 'true',
          },
        },
        spec: pod,
      },
    },
  };
  local podDisruptionBudget = nodeLib.podDisruption(
    appName=appName,
    namespace=namespace,
    env=env,
    minAvailable=0
  );

  lib.flatten([
    configMap.objects,
    deployment,
    grpcService,
    if env != 'DEV' then globalGrpcService else null,
    serverCert.objects,
    clientCert.objects,
    serviceAccount.objects,
    podDisruptionBudget,
    bigqueryAccess,
  ])
