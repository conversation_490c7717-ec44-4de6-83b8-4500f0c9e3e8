// K8S deployment file for the request insight publisher configmap name, clients
// should import this if they want to use the configmap.
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

function(cloud, env, namespace, appName)
  local uniqueNamePrefix = if cloudInfo.isUniqueNamespace(cloud, env, namespace) then
    namespace
  else
    '%s-%s' % [cloudInfo[cloud].clusterName, namespace];
  local topicName = '%s-request-insight-topic' % uniqueNamePrefix;

  local commonName = 'request-insight-publisher-config';
  local configKeyName = 'request_insight_publisher_config.json';
  local iamPartialPolicy = function(namePrefix, iamServiceAccount)
    gcpLib.grantAccess(
      name='%s-ri-publish-sa-policy' % namePrefix,
      namespace=namespace,
      appName=appName,
      env=env,
      resourceRef={
        kind: 'PubSubTopic',
        name: topicName,
      },
      bindings=[
        {
          role: 'roles/pubsub.publisher',
          members: [{
            memberFrom: {
              serviceAccountRef: {
                name: iamServiceAccount,
              },
            },
          }],
        },
      ],
      abandon=true,
    );

  {
    // These are mainly used to create the pubsub topic and configmap
    topicName: topicName,
    configMapName: commonName,
    configMapKeyName: configKeyName,
    // An easy default for adding a volume to a pod
    podVolumeDef: {
      name: commonName,
      configMap: {
        name: commonName,
        items: [
          {
            key: configKeyName,
            path: 'config.json',
          },
        ],
      },
    },
    // An easy default for mounting the volume
    volumeMountDef: {
      name: commonName,
      mountPath: '/request_insight_publisher_config/',
      readOnly: true,
    },
    iamPartialPolicy: iamPartialPolicy,
    // The path to the local config file in the pod
    configFilePath: '/request_insight_publisher_config/config.json',
    // The RI pipeline isn't configured in GSC.
    requestInsightPublishingEnabled: cloud != 'GCP_US_CENTRAL1_GSC_PROD',
  }
