local eng = import 'deploy/common/eng.jsonnet';

// Generate IAM policies to allow our external service accounts to access the vendor buckets. See
// request_insight/external_service_accounts.jsonnet for details.
local externalAccess(env, cloud, namespace, bucketName) =
  if cloud != 'GCP_US_CENTRAL1_PROD' then [] else
    local saLib = import 'services/request_insight/external_service_accounts_lib.jsonnet';
    local externalServiceAccounts = saLib.getExternalServiceAccounts(env, cloud);
    local coreweaveVendorBucketAccessSA = externalServiceAccounts.coreweaveVendorBucketAccessSA;
    [
      // Give the CoreWeave vendor bucket access service account permission to write the bucket.
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: '%s-vendor-external-access-policy' % bucketName,
          namespace: namespace,
          labels: {
            app: 'request-insight-vendor-bucket-access',
          },
        },
        spec: {
          resourceRef: {
            kind: 'StorageBucket',
            external: bucketName,
          },
          bindings: [
            {
              role: 'roles/storage.objectUser',
              members: [
                {
                  member: 'serviceAccount:%s' % coreweaveVendorBucketAccessSA.email,
                },
              ],
            },
          ],
        },
      },
    ];

// Create GCS buckets to share data with vendors.
function(cloud, env, namespace, namespace_config)
  if !namespace_config.flags.sharedVendorBucket.enabled then [] else
    local bucketName = 'augment-vendor-%s' % [namespace];
    local vendorAdminEmails = namespace_config.flags.sharedVendorBucket.adminEmails;
    local vendorAccessEmails = namespace_config.flags.sharedVendorBucket.accessEmails;
    local engAccessEmails = std.map(function(u) '%<EMAIL>' % u.username, eng);

    [
      {
        apiVersion: 'storage.cnrm.cloud.google.com/v1beta1',
        kind: 'StorageBucket',
        metadata: {
          annotations: {
            // If set to true, the force-destroy directive cleans up the objects within
            // a storage bucket before issuing the delete command.
            // Setting it to false prevents the bucket from being deleted if it is not
            // empty.
            'cnrm.cloud.google.com/force-destroy': 'false',
          },
          labels: {
            app: 'request-insight-vendor-bucket-access',
          },
          name: bucketName,
          namespace: namespace,
        },
        spec: {
          // Enable IAM policies for this bucket
          uniformBucketLevelAccess: true,
        },
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: '%s-eng-access-policy' % bucketName,
          namespace: namespace,
          labels: {
            app: 'request-insight-vendor-bucket-access',
          },
        },
        spec: {
          resourceRef: {
            kind: 'StorageBucket',
            external: bucketName,
          },
          bindings: [
            {
              role: 'roles/storage.objectUser',
              members: std.map(function(email)
                {
                  member: 'user:%s' % email,
                }, engAccessEmails),
            },
          ],
        },
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: '%s-vendor-admin-access-policy' % bucketName,
          namespace: namespace,
          labels: {
            app: 'request-insight-vendor-bucket-access',
          },
        },
        spec: {
          resourceRef: {
            kind: 'StorageBucket',
            external: bucketName,
          },
          bindings: [
            {
              role: 'roles/storage.objectAdmin',
              members: std.map(function(email)
                {
                  member: 'user:%s' % email,
                }, vendorAdminEmails),
            },
          ],
        },
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: '%s-vendor-access-policy' % bucketName,
          namespace: namespace,
          labels: {
            app: 'request-insight-vendor-bucket-access',
          },
        },
        spec: {
          resourceRef: {
            kind: 'StorageBucket',
            external: bucketName,
          },
          bindings: [
            {
              role: 'roles/storage.objectUser',
              members: std.map(function(email)
                {
                  member: 'user:%s' % email,
                }, vendorAccessEmails),
            },
          ],
        },
      },
    ] + externalAccess(env, cloud, namespace, bucketName)
