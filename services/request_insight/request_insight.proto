syntax = "proto3";
package request_insight;

import "base/blob_names/blob_names.proto";
import "base/diff_utils/edit_events.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "services/agents/agents.proto";
import "services/auth/central/server/auth_entities.proto";
import "services/chat_host/chat.proto";
import "services/completion_host/completion.proto";
import "services/edit_host/edit.proto";
import "services/integrations/github/github_event.proto";
import "services/integrations/glean/glean.proto";
import "services/integrations/slack_bot/slack_event.proto";
import "services/next_edit_host/next_edit.proto";
import "services/remote_agents/remote_agents.proto";
import "services/share/share.proto";
import "services/tenant_watcher/tenant_watcher.proto";

message TenantInfo {
  // the tenant id
  string tenant_id = 1;

  // the tenant name
  string tenant_name = 2;
}

// request type to update the information about a request
message UpdateRequestInfoRequest {
  // the request id of the request
  string request_id = 1;

  reserved 2;

  // a list of events with information about the request
  repeated RequestEvent events = 3;

  // the tenant information of the request
  TenantInfo tenant_info = 4;

  // The session id of the request. This is recorded for every request event to make aggregate
  // queries over a session easier.
  // TODO(jacqueline): Currently most events aren't actually setting this. We should go through and
  //                   fix that.
  optional string session_id = 5;
}

// An event for a request. Put events that relate to the API calls/results for specific requests
// (e.g., the contents of a completion request or whether the response was accepted). Use
// SessionEvent instead for events that relate to user actions not easily associated with a request
// (e.g., clicking button A vs button B) that we want to record for all users.
// There's some gray area between RequestEvent and SessionEvent; if you need help choosing where to
// put your event you can ask in #team-insights.
message RequestEvent {
  reserved 3, 4, 5;
  reserved 55, 56, 57, 58;

  // the approximate time the event happened.
  //
  // Clocks between nodes are not syncronized so this should only
  // be taken as a rough estimation of the time.
  google.protobuf.Timestamp time = 1;

  oneof event {
    // information about the inference request as seen initially
    InferRequest infer_request = 2;
    CompletionHostRequest completion_host_request = 8;
    InferenceHostResponse inference_host_response = 10;
    CompletionHostResponse completion_host_response = 9;
    ApiHttpResponse api_http_response = 7;
    EmbeddingsSearchRequest embeddings_search_request = 11;
    EmbeddingsSearchResponse embeddings_search_response = 12;
    CompletionEmit completion_emit = 13;
    CompletionResolution completion_resolution = 14;
    RIEditRequest edit_host_request = 15;
    RIEditResponse edit_host_response = 16;
    CompletionFeedback completion_feedback = 17;
    EditEmit edit_emit = 18;
    EditResolution edit_resolution = 19;
    RequestMetadata request_metadata = 20;
    RetrievalResponse retrieval_response = 21;
    RIChatRequest chat_host_request = 22;
    RIChatResponse chat_host_response = 23;
    ExtensionError extension_error = 24;
    CompletionPostProcess completion_post_process = 25;
    PreferenceSample preference_sample = 27;
    ChatFeedback chat_feedback = 28;
    RerankerResponse reranker_response = 29;
    RINextEditRequest next_edit_host_request = 30;
    RINextEditResponse next_edit_host_response = 31;
    ClientCompletionTimeline client_completion_timeline = 32;
    NextEditFeedback next_edit_feedback = 33;
    NextEditResolution next_edit_resolution = 34;
    NextEditEmit next_edit_emit = 35;
    RIInstructionRequest instruction_host_request = 38;
    RIInstructionResponse instruction_host_response = 39;
    RISlackbotRequest slackbot_request = 40;
    RISlackbotResponse slackbot_response = 41;
    RIGithubEvent github_event = 42;
    RIGithubProcessingResult github_processing_result = 43;
    RIShareSaveChatRequest share_save_chat_request = 44;
    RIShareSaveChatResponse share_save_chat_response = 45;
    RIShareGetChatRequest share_get_chat_request = 46;
    RIShareGetChatResponse share_get_chat_response = 47;
    SmartPasteClientTimeline smart_paste_client_timeline = 48;
    SmartPasteResolution smart_paste_resolution = 49;
    RIGithubAppInstallationEvent github_app_installation_event = 50;
    RISlackbotInstallationEvent slackbot_installation_event = 51;
    InstructionResolution instruction_resolution = 52;
    InstructionEmit instruction_emit = 53;
    SlackbotFeedback slackbot_feedback = 54;
    RIGleanRequest glean_request = 59;
    RIGleanResponse glean_response = 60;
    RIGleanOAuthURLRequest glean_oauth_url_request = 61;
    RIGleanOAuthURLResponse glean_oauth_url_response = 62;
    RILLMGenerateRequest llm_generate_request = 63;
    RILLMGenerateResponse llm_generate_response = 64;
    RIRemoteToolCallRequest remote_tool_call_request = 65;
    RIRemoteToolCallResponse remote_tool_call_response = 66;
    SubAgentDialog sub_agent_dialog = 67;
    PromptCacheUsage prompt_cache_usage = 68;
    ToolUseData tool_use_data = 69;
    RouterResponse router_response = 70;
    PostprocessResponse postprocess_response = 71;
    AgentRequestEvent agent_request_event = 72;
    AgentFeedback agent_feedback = 73;
    RequestBlocked request_blocked = 74;
    RequestSuspicious request_suspicious = 75;
    TokenExchangeError token_exchange_error = 76;
    RIFindMissing find_missing = 77;
    RIBatchUpload batch_upload = 78;
    RemoteAgentsCreateRequest remote_agents_create_request = 79;
    RemoteAgentsCreateResponse remote_agents_create_response = 80;
    RemoteAgentsChatRequest remote_agents_chat_request = 81;
    RemoteAgentsChatResponse remote_agents_chat_response = 82;
    RemoteAgentsInterruptRequest remote_agents_interrupt_request = 83;
    RemoteAgentsInterruptResponse remote_agents_interrupt_response = 84;
    RemoteAgentsDeleteRequest remote_agents_delete_request = 85;
    RemoteAgentsDeleteResponse remote_agents_delete_response = 86;
    RemoteAgentsAddSSHKeyRequest remote_agents_add_ssh_key_request = 87;
    RemoteAgentsAddSSHKeyResponse remote_agents_add_ssh_key_response = 88;
    DailyRequestLimitExceeded daily_request_limit_exceeded = 89;
    ChatUserMessage chat_user_message = 90;
    ParenthesisTruncation parenthesis_truncation = 91;
    RemoteAgentFeedback remote_agent_feedback = 92;
    RemoteAgentsPauseRequest remote_agents_pause_request = 93;
    RemoteAgentsPauseResponse remote_agents_pause_response = 94;
    RemoteAgentsResumeRequest remote_agents_resume_request = 95;
    RemoteAgentsResumeResponse remote_agents_resume_response = 96;
  }

  reserved 26, 36;

  // A unique identifier for this event, set when the event is published. This is intended to help
  // exporters write events idempotently.
  optional string event_id = 37;
}

// completion request as received by api proxy. InferRequest is stale terminology.
message InferRequest {
  // name of the model as received by the API proxy
  string model = 1;

  // the prefix prompt at which the completion should be inserted
  string prompt = 2 [debug_redact = true];

  // relative file path to a given relevant root (e.g. the .augmentroot file).
  string path = 3 [debug_redact = true];

  // an optional suffix for fill-in-the-middle.
  // usually the text after the cursor location at which the completion should
  // be inserted
  string suffix = 4 [debug_redact = true];

  // name of memory objects that the client believes the server has for the given model
  // Each name is a sha256 in hex format of the content.
  //
  // the server should return any memory names the server doesn't have in the response
  // the server should perform the completion with the subset of memory objects it has
  repeated string memories = 5;

  // top-k value for sampling
  int32 top_k = 6;

  // top-p value for sampling
  float top_p = 7;

  // temperature for sampling
  float temperature = 8;

  // maximal number of tokens to generate
  // the server is free to generate less tokens than the value provided.
  int32 max_tokens = 9;

  // the user id of the authenticated user
  string user_id = 10;

  string session_id = 11;

  // programming language
  string lang = 12;

  // the user agent sent by the HTTP client
  string user_agent = 13;

  // sequence id sent by the client
  uint32 sequence_id = 14;

  // True when getting indexing status
  bool probe_only = 15;

  // Blob names included the completion request, specified in the
  // compact delta form. This field will replace the memories
  // field in the future.
  base.blob_names.Blobs blobs = 16;

  completion.RecencyInfo recency_info = 17;

  // the user filter threshold (undefined, 0.0 to 1.0) for the completion low quality filter post process.
  // This threshold is combined with the global filter threshold to determine the final applied filter threshold.
  optional float user_filter_threshold = 18;

  // More granular events representing the most recent edits.
  repeated base.diff_utils.GranularEditEvent edit_events = 19;
}

// Message to represent a sequence of tokens, optionally with associated log probabilities
message Tokenization {
  // invariant: token_ids, offsets, and log_probs should have the same length (or be empty).
  // IDs of the tokens used
  repeated int32 token_ids = 1 [debug_redact = true];
  // Per-token character offsets into the associated text
  repeated uint32 offsets = 2;
  // Per-token log probabilities, if applicable
  repeated float log_probs = 3;
  // Full text
  string text = 4 [debug_redact = true];
}

message EmbeddingsSearchRequest {
  repeated string blob_names = 1;

  // how many results to return, e.g. 32 to return the 32 most similar results
  int32 num_results = 2;

  // the transformation key to use for the search
  string transformation_key = 3;

  // the sub key to use for the given search
  string sub_key = 4;

  // Blob names included the embeddings search request, specified in the
  // compact delta form.
  repeated base.blob_names.Blobs blobs = 5;
}

message EmbeddingsSearchResult {
  string blob_name = 1;
  uint32 chunk_index = 2;
  float value = 3;
}

message EmbeddingsSearchResponse {
  repeated string missing_blob_names = 1;
  repeated EmbeddingsSearchResult results = 2;
}

message CompletionEmit {}

message CompletionResolution {
  // index of the accepted completion, or -1 if the completion was rejected
  int32 accepted_idx = 1;
}

message CompletionFeedback {
  // the overall rating for the completion ("positive", "negative", "neutral")
  FeedbackRating rating = 1;

  // a note on the completion from the user
  string note = 2;
}

message ChatFeedback {
  // the overall rating for the chat response ("positive", "negative", "neutral")
  FeedbackRating rating = 1;

  // a note on the chat response from the user
  string note = 2;
  string user_agent = 3;
}

message AgentFeedback {
  // the overall rating for the agent response ("positive", "negative", "neutral")
  FeedbackRating rating = 1;

  // a note on the agent response from the user
  string note = 2;
  string user_agent = 3;
}

message RemoteAgentFeedback {
  // the overall rating for the remote agent response ("positive", "negative", "neutral")
  FeedbackRating rating = 1;

  // a note on the remote agent response from the user
  string note = 2;
  string user_agent = 3;
}

message NextEditFeedback {
  // the overall rating for the next edit response ("positive", "negative", "neutral")
  FeedbackRating rating = 1;

  // a note on the next edit response from the user
  string note = 2;
}

message SlackbotFeedback {
  // the timestamp of the slackbot response the feedback is for
  // we use this in conjuction with the channel id to get the request id through the slackbot_response_lookup view
  string slack_response_timestamp = 1;

  // the channel id of the slackbot response the feedback is for
  // we use this in conjuction with the timestamp to get the request id through the slackbot_response_lookup view
  string slack_channel_id = 2;

  // the overall rating for the slackbot response ("positive", "negative", "neutral")
  FeedbackRating rating = 3;

  // a note on the slackbot response from the user
  string note = 4;
}

message EditEmit {}

message EditResolution {
  // True if the edit was accepted
  bool is_accepted = 1;

  // user-suggested correction of the reponse text
  optional string annotated_text = 2;

  // user-suggested correction of the instruction
  optional string annotated_instruction = 3;
}

message NextEditEmit {}

message InstructionResolution {
  // For each chunk, was it accepted
  repeated bool is_accepted_chunks = 1;
  // Did the user click accept all without interacting with any individual chunk
  bool is_accept_all = 2;
  // Did the user reject all (or close the diff viewer) without interacting with any individual chunk
  bool is_reject_all = 3;
}

message InstructionEmit {}

// Track client metrics - counters and latency tracking for actions in extensions, e.g. button clicks
message ClientMetric {
  // Name of the user event. Should never be empty.
  string event_name = 1;

  // The user agent of the client. Should never be empty.
  string user_agent = 2;

  // Name (key) of metric
  string client_metric = 3;

  // Value of metric (Usually a counter or latency)
  uint64 value = 4;
}

message SmartPasteClientTimeline {
  // Time of the initial request
  google.protobuf.Timestamp initial_request_time = 1;
  // Time of stream finishing successfully - can be before or after apply time
  google.protobuf.Timestamp stream_finish_time = 2;
  // Time of initial user interaction (clicking apply) - can be before or after emit time
  google.protobuf.Timestamp apply_time = 3;
}

message SmartPasteResolution {
  // For each chunk, was it accepted
  repeated bool is_accepted_chunks = 1;
  // Did the user click accept all without interacting with any individual chunk
  bool is_accept_all = 2;
  // Did the user reject all (or close the diff viewer) without interacting with any individual chunk
  bool is_reject_all = 3;
}

message NextEditResolution {
  // True if the edit was accepted
  bool is_accepted = 1;
}

// Represents a user preference between multiple answers
// More: https://www.notion.so/RFC-Preference-data-collection-01058dd73069477c8287652ef4d3c9bb
message PreferenceSample {
  // Requests participating in voting process
  repeated string request_ids = 1;

  // Quantitative comparisons between the requests.
  // For structure, see RFC mentioned above.
  // Keeping it as map to ease experimenting with different collection strategies
  map<string, int32> scores = 2;

  // Free-form feedback given by the user regarding this sample
  string feedback = 3;
}

enum FeedbackRating {
  UNSET = 0;
  POSITIVE = 1;
  NEGATIVE = 2;
}

// FIXME (arun, jiayi): Remove this message once CompletionHostRequest is refactored
message CompletionHostRequestPosition {
  // the offset in characters where the prefix begins from the beginning of the current
  // file.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  int32 prefix_begin = 1;

  // the offset in characters of the current cursor position from the beginning of the
  // current file.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  int32 cursor_position = 2;

  // the offset in characters where the suffix ends from the beginning of the current file.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  int32 suffix_end = 3;

  // the blob name of the current file.
  string blob_name = 4;
}

// FIXME (arun, jiayi): Refactor this to hold CompletionRequest from completion.proto
// information about the request submitted from a completion host
message CompletionHostRequest {
  reserved 1;

  // the random seed used
  uint64 seed = 2;

  // eos token id passed to inference host. This field used to be NOT repeated.
  // Name kept in singular form to maintain backward compatibility.
  repeated int32 eos_token_id = 4;

  int32 top_k = 5;

  float top_p = 6;

  float temperature = 7;

  int32 output_len = 8;

  bool enable_path_prefix = 9;
  bool enable_preference_tokens = 10;
  bool enable_fill_in_the_middle = 11;
  reserved 12;
  bool enable_bm25 = 13;
  bool enable_dense_retrieval = 14;

  // name of the model to use
  string model = 15;

  // the prefix prompt at which the completion should be inserted
  string prefix = 16 [debug_redact = true];

  // relative file path to a given relevant root (e.g. the .augmentroot file).
  string path = 17 [debug_redact = true];

  // an optional suffix for fill-in-the-middle.
  // usually the text after the cursor location at which the completion should
  // be inserted
  string suffix = 18 [debug_redact = true];

  repeated string blob_names = 19;

  // programming language (either based on the request or detected by other means)
  string lang = 20;

  CompletionHostRequestPosition position = 21;

  // True when getting indexing status
  bool probe_only = 22;

  // Blob names included the completion request, specified in the
  // compact checkpoint form. This field will replace the blob_names
  // field in the future.
  base.blob_names.Blobs blobs = 23;

  completion.RecencyInfo recency_info = 24;

  // the tokens used for inference after all prompt modifications
  Tokenization tokenization = 25;

  // the user filter threshold for the completion low quality filter post process
  optional float user_filter_threshold = 26;

  // More granular events representing the most recent edits.
  repeated base.diff_utils.GranularEditEvent edit_events = 27;
}

message InferenceHostResponse {
  reserved 1;

  // the cummulative log probability reported by FTM
  float cum_log_probs = 2;

  // the tokens generated by the FTM model (before any truncation or filtering)
  Tokenization tokenization = 3;
}

message RetrievalChunk {
  reserved 1;

  string text = 5 [debug_redact = true];

  string path = 6 [debug_redact = true];

  // this is char_start in the RetrievalChunk class
  int32 char_offset = 4;

  int32 char_end = 7;

  string blob_name = 2;

  int32 chunk_index = 3;

  string origin = 8;

  float score = 9;
}

message RerankerChunk {
  string text = 1 [debug_redact = true];

  string path = 2 [debug_redact = true];

  // this is char_start in the RetrievalChunk class
  int32 char_offset = 3;

  int32 char_end = 4;

  string blob_name = 5;

  int32 chunk_index = 6;

  string origin = 7;

  float score = 8;

  string short_description = 9 [debug_redact = true];
}

// information about the response submitted from the completion host
message CompletionHostResponse {
  reserved 1, 4, 5;

  string text = 2 [debug_redact = true];

  // all memory object names from the memories parameter the server doesn't know about
  repeated string unknown_blob_names = 3;

  // information about skipped suffix characters
  string skipped_suffix = 6 [debug_redact = true];
  string suffix_replacement_text = 7 [debug_redact = true];

  // Set to True if the checkpoint was not found.
  bool checkpoint_not_found = 8;

  // the tokens generated after truncation and filtering
  Tokenization tokenization = 9;
}

message CompletionPostProcess {
  enum FilterReason {
    UNKNOWN_FILTER_REASON = 0;
    NOT_FILTERED = 1;
    LOW_QUALITY = 2;
    DENY_LIST = 3;
  }

  // If True, the whole completion is filtered. Deprecated: Use filter_reason instead.
  optional bool is_low_quality = 1 [deprecated = true];

  // The filter score of the completion. The predicted score is between 0 and 1. If
  // the score is larger than a threshold, the completion is filtered.
  float filter_score = 12;

  // The final applied filter threshold (0.0 to 1.0) of the completion. If the filter score is larger than the threshold, the completion is filtered.
  // This is optional to work for json conversion but we expect it to be always set (otherwise 0.0 is not set).
  optional float applied_filter_threshold = 13;

  // The reason for filtering the completion. Can be NOT_FILTERED, LOW_QUALITY, or DENY_LIST. Should never be UNKNOWN_FILTER_REASON. LOW_QUALITY takes precedence over DENY_LIST.
  optional FilterReason filter_reason = 14;
}

message ParenthesisTruncation {
  string original_text = 1 [debug_redact = true];
  string truncated_text = 2 [debug_redact = true];
  bool was_truncated = 3;
  bool could_have_truncated = 4;
  string path = 5 [debug_redact = true];
}

enum RetrievalType {
  UNKNOWN_RETRIEVAL_TYPE = 0;
  DENSE = 1;
  SIGNATURE = 2;
  RECENCY = 3;
  USER_GUIDED = 4;
}

message RetrievalResponse {
  RetrievalType retrieval_type = 1;

  reserved 2;

  repeated RetrievalChunk retrieved_chunks = 3;

  Tokenization query_prompt = 4;
}

message RerankerResponse {
  repeated RerankerChunk reranked_chunks = 1;

  reserved 2;

  // Each entry is a different prompt.
  repeated Tokenization reranker_prompts = 3;
}

/// information about the response emitted by the public API
///
/// For detailed investigations why a request failed, lower level system like Loki
/// might be more suited.
message ApiHttpResponse {
  // HTTP status code
  uint32 code = 1;

  // error message if applicable
  string error_message = 2;
}

/// nothing is returned by a UpdateRequestInfo call.
message UpdateRequestInfoResponse {}

// Request to record information about a session.
message RecordSessionEventsRequest {
  string session_id = 1;
  TenantInfo tenant_info = 2;
  repeated SessionEvent events = 3;
  // TODO(jacqueline): Currently most events aren't actually setting this. We should go through and
  // fix that.
  auth_entities.UserId opaque_user_id = 4;
}

// An event for a session. Here, sessions are really stand-ins for users (note that we can always
// associate a session with a user after-the-fact). Put events here that relate to user actions not
// easily associated with a request (e.g., clicking button A vs button B) that we want to record for
// all users.
// Use RequestEvent instead for events that relate to the API calls/results for specific requests
// (e.g., the contents of a completion request or whether the response was accepted).
// Use UserEvent instead for invasive metrics that are only allowed to be recorded for vanguard
// users (e.g., keystrokes).
// There's some gray area between RequestEvent and SessionEvent; if you need help choosing where to
// put your event you can ask in #team-insights.
message SessionEvent {
  google.protobuf.Timestamp time = 1;

  // A unique identifier for this event, set when the event is published. This is intended to help
  // exporters write events idempotently.
  // This field should always be set in pub/sub messages. It's marked as optional for the sake of
  // JSON conversion in api-proxy, which has logic to fill in this value if it's empty.
  optional string event_id = 3;

  oneof event {
    NextEditSessionEvent next_edit_session_event = 2;
    OnboardingSessionEvent onboarding_session_event = 4;
    ClientMetric client_metric = 5;
    ExtensionSessionEvent extension_session_event = 10;
    CustomerUISessionEvent customer_ui_session_event = 11;
    AgentSessionEvent agent_session_event = 12;
    ContentManagerUploadBlobs content_manager_upload_blobs = 13;
    ContentManagerCheckpointBlobs content_manager_checkpoint_blobs = 14;
    RemoteAgentSessionEvent remote_agent_session_event = 15;
    PurchaseCredits purchase_credits = 16;
    CancelSubscription cancel_subscription = 17;
    UnschedulePendingSubscriptionCancellation unschedule_pending_subscription_cancellation = 18;
    RemoteAgentLog remote_agent_log = 19;
    FeatureVectorReport feature_vector_report = 20;
    UnschedulePlanChanges unschedule_plan_changes = 21;
    GithubUserAuthorize github_user_authorize = 22;
    RemoteAgentWorkspaceUptime remote_agent_workspace_uptime = 23;

    // These are only recorded for tenants with the full export enabled.
    TextEditEvent text_edit = 6;
    CompletionRequestIdIssuedEvent completion_request_id_issued = 7;
    EditRequestIdIssuedEvent edit_request_id_issued = 8;
    NextEditRequestIdIssuedEvent next_edit_request_id_issued = 9;
  }
}

message PurchaseCredits {
  string orb_subscription_id = 1;
  float credits = 2;
}

message CancelSubscription {
  string orb_subscription_id = 1;
}

message UnschedulePendingSubscriptionCancellation {
  string orb_subscription_id = 1;
}

message UnschedulePlanChanges {
  string orb_subscription_id = 1;
}

message CustomerUISessionEvent {
  string user_id = 1;
  message SessionStart {
    string redirect_url = 1;
  }

  message SessionEnd {
    enum Reason {
      UNKNOWN_SESSION_END_REASON = 0;
      LOGOUT = 1;
      TIMEOUT = 2;
    }

    Reason reason = 1;
  }

  oneof event {
    SessionStart session_start = 2;
    SessionEnd session_end = 3;
  }
}

message RemoteAgentWorkspaceUptime {
  // The start and end time of the workspace uptime. Note that since this is a
  // session event we implicitly get the session id (agent id) and user id.
  google.protobuf.Timestamp uptime_start = 1;
  google.protobuf.Timestamp uptime_end = 2;
}

message AgentInterruptionData {
  // The request id of the user-driven interrupt.
  string request_id = 1;
  // The number of turns in the conversation at the time of the interrupt.
  uint32 curr_conversation_length = 2;
}

message AgentReversionData {
  // Leaving this object empty for now as the fields are still in flux.
  // See comment in `clients/vscode/src/metrics/types.ts` for context.
}

/**
 * Tracing data, which should NOT contain PII or restricted data
 */
message AgentTracingData {
  // Using this for strings that might contain PII or restricted data
  message StringStats {
    int32 num_lines = 1;
    int32 num_chars = 2;
  }

  message TimedBoolean {
    bool value = 1;
    google.protobuf.Timestamp timestamp = 2;
  }

  message TimedNumber {
    double value = 1;
    google.protobuf.Timestamp timestamp = 2;
  }

  message TimedStringStats {
    StringStats value = 1;
    google.protobuf.Timestamp timestamp = 2;
  }

  message TimedString {
    string value = 1;
    google.protobuf.Timestamp timestamp = 2;
  }

  map<string, TimedBoolean> flags = 3;
  map<string, TimedNumber> nums = 4;
  map<string, TimedStringStats> string_stats = 5;
  map<string, TimedString> request_ids = 6;
}

/**
 * Data for the remember-tool-call event.
 */
message RememberToolCallData {
  // Specifies who runs the remember tool
  enum RememberToolCaller {
    // Default value
    UNSPECIFIED = 0;
    // Background process that runs on every user message
    CLASSIFY_AND_DISTILL = 1;
    // Initial orientation process
    ORIENTATION = 2;
  }

  RememberToolCaller caller = 1;
  bool is_complex_new_memory = 2;

  // Maps for storing the different types of data
  AgentTracingData tracing_data = 3;
}

message MemoriesFileOpenData {
  bool memories_path_undefined = 1;
}

message MemoriesMoveData {
  enum Target {
    UNSPECIFIED = 0;
    USER_GUIDELINES = 1;
    AUGMENT_GUIDELINES = 2;
    RULES = 3;
  }

  Target target = 1;
}

message RulesImportedData {
  enum Type {
    UNSPECIFIED = 0;
    MANUALLY_CREATED = 1;
    AUTO = 2;
    SELECTED_DIRECTORY = 3;
    SELECTED_FILE = 4;
  }

  Type type = 1;

  // Number of files that were imported
  uint32 num_files = 2;

  // Source of the rule import (e.g., "manual", "auto-import", "file-selection", "directory-selection")
  string source = 3;
}

/**
 * Data for the initial-orientation flow.
 */
message InitialOrientationData {
  // Specifies what triggered the initial orientation flow
  enum OrientationCaller {
    // Default value
    UNSPECIFIED = 0;
    // Onboarding flow
    ONBOARDING = 1;
    // Command was run
    COMMAND = 2;
  }

  OrientationCaller caller = 1;

  // Debug and tracing data
  AgentTracingData tracing_data = 2;
}

/**
 * Data for the "classify-and-distill" flow.
 * It's a background process that detects useful information from the conversation
 * to store it in the agent's memory.
 */
message ClassifyAndDistillData {
  // Debug and tracing data
  AgentTracingData tracing_data = 1;
}

/**
 * Data for "flush" operation of memories after classify-and-distill.
 * It's logically separate from classify-and-distill, so it's a separate event.
 */
message FlushMemoriesData {
  // Debug and tracing data
  AgentTracingData tracing_data = 1;
}

message AgentSessionEvent {
  // Name of the user event. Should never be empty.
  string event_name = 1;

  // The user agent of the client. Should never be empty.
  string user_agent = 2;

  // The ID of the conversation this event is associated with.
  // This field should be set for all agent-related events.
  string conversation_id = 3;

  // allow to send some additional data.
  oneof event_data {
    AgentReversionData agent_reversion_data = 4;
    AgentInterruptionData agent_interruption_data = 5;
    RememberToolCallData remember_tool_call_data = 6;
    MemoriesFileOpenData memories_file_open_data = 7;
    InitialOrientationData initial_orientation_data = 8;
    ClassifyAndDistillData classify_and_distill_data = 9;
    FlushMemoriesData flush_memories_data = 10;
    MemoriesMoveData memories_move_data = 11;
    RulesImportedData rules_imported_data = 12;
  }

  // Will add more fields once we get some clarity on the expected events.
}

/**
 * Are users completing the remote agent setup flow?
 */
message RemoteAgentSetupData {
  // user used a setup script
  bool used_generated_setup_script = 1;

  enum RemoteAgentSetupState {
    UNKNOWN = 0;
    COMPLETED = 1;
    FAILED = 2;
  }

  RemoteAgentSetupState setup_state = 2;
}

/**
 * This is for when the user uses generate setup script
 */
message RemoteAgentSetupScriptData {
  // how many times did the agent try to generate the script?
  int32 num_tries = 1;
  // how many messages sent to the agent before generating the script?
  int32 num_messages_sent = 3;
  // how long did it take to generate the script?
  int32 generation_time_ms = 4;
  // did the user make any manual modifications to the script
  bool manual_modification = 5; // not sure if this makes sense before accepting?
}

/**
 * How do users interact with the SSH option.
 */
message SSHInteractionData {
  enum InteractionType {
    UNKNOWN = 0;
    // The user clicked on the SSH button
    CLICKED = 1;
    // A remote session actually started for the user
    REMOTE_SESSION_STARTED = 2;
  }

  InteractionType interaction_type = 1;
}

/**
 * Helps us understand if and how the bell is used.
 */
message NotificationBellData {
  enum NotificationBellState {
    UNKNOWN = 0;
    // The user clicked on the notification bell
    ACTIVATED = 1;
    // The user dismissed the notification bell
    DEACTIVATED = 2;
    // The notification was shown
    NOTIFIED = 4;
    // The user accepted the notification
    ACCEPTED = 3;
  }

  NotificationBellState bell_state = 1;
}

/**
 * Data for the diff panel.
 */
message DiffPanelData {
  // How long did it take to load the diff panel?
  int32 loading_time_ms = 1;
  // Was the diff applied?
  bool applied = 2;
}

/**
 * Data for when the user opens the remote agent webview.
 */
message SetupPageOpened {}

/**
 * Data for when the user fails to connect to github.
 */
message GithubAPIFailure {
  // The error code from the API
  int32 error_code = 1;
}

/**
 * Data for when the user creates a remote agent.
 */
message RemoteAgentCreated {
  // Did the user change the repo from default?
  bool changed_repo = 1;
  // Did the user change the branch from default?
  bool changed_branch = 2;
}

/**
 * Data for when the user applies changes from the diff panel.
 */
message ChangesAppliedData {}

/**
 * Data for when the user creates a PR.
 */
message CreatedPRData {}

message RemoteAgentSessionEvent {
  // The name of the event. Should never be empty.
  string event_name = 1;

  // The user agent of the client. Should never be empty.
  string user_agent = 2;

  // The ID of the remote agent this event is associated with.
  // This field should be set for all agent-related events.
  string remote_agent_id = 3;

  // allow to send some additional data.
  oneof event_data {
    RemoteAgentSetupData remote_agent_setup_data = 4;
    RemoteAgentSetupScriptData setup_script_data = 5;
    SSHInteractionData ssh_interaction_data = 6;
    NotificationBellData notification_bell_data = 7;
    DiffPanelData diff_panel_data = 8;
    SetupPageOpened setup_page_opened = 9;
    GithubAPIFailure github_api_failure = 10;
    RemoteAgentCreated remote_agent_created = 11;
    ChangesAppliedData changes_applied_data = 12;
    CreatedPRData created_pr_data = 13;
  }

  // Will add more fields once we get some clarity on the expected events.
}

// AgentRequestEvent is an event tied to a specific agent request rather than a session.
// This is used for events that are tied to specific requests, such as user messages.
message AgentRequestEvent {
  // Name of the user event. Should never be empty.
  string event_name = 1;

  // The user agent of the client. Should never be empty.
  string user_agent = 2;

  // The ID of the conversation this event is associated with.
  // This field should be set for all agent-related events.
  string conversation_id = 3;

  // The length of the chat history at the time of the event.
  uint32 chat_history_length = 4;
}

message OnboardingSessionEvent {
  // The related session ID is in RecordSessionEventsRequest.
  // The time is in the SessionEvent wrapper.

  // Name of the user event. Should never be empty.
  // Some example events are:
  // - signed-in: user signed in
  // - first-started-syncing: user started syncing
  // - first-finished-syncing: user finished syncing
  // - saw-summary: user saw the autogenerated codebase summary
  // - first-used-chat: user used chat
  // - first-accepted-completion: user accepted a completion
  // - first-used-slash-action: user used a slash action
  string event_name = 1;

  // The user agent of the client. Should never be empty.
  string user_agent = 2;
}

message ExtensionSessionEvent {
  // Use this to record generic extension events that are not tied to specific features.
  // Examples: "configuration_changed", "command_executed"
  // For feature-specific events, use other event types like NextEditSessionEvent,
  // OnboardingSessionEvent, etc.

  string event_name = 1;

  string user_agent = 2;

  // Simple string key-value pairs for event data
  message KeyValue {
    string key = 1;
    string value = 2;
  }

  // allow to send some additional data.
  repeated KeyValue additional_data = 3;
}

message NextEditSessionEvent {
  // The related session ID is in RecordSessionEventsRequest.
  // The time is in the SessionEvent wrapper.

  // ID of the request the user event is related to.
  // emtpy iff the event is not related to a specific request.
  optional string related_request_id = 1;

  // ID of the suggestion the user event is related to.
  // empty iff the event is not related to a specific suggestion.
  optional string related_suggestion_id = 2;

  // Name of the user event. Should never be empty.
  string event_name = 3;

  // Source of the user event. Empty iff the event is not related to a specific source.
  optional string event_source = 4;

  // The user agent of the client. Should never be empty.
  string user_agent = 5;
}

// Content Manager upload blobs session event
message ContentManagerUploadBlobs {
  message UploadedBlobInfo {
    // The blob name
    string blob_name = 1;

    // The size of the blob in bytes
    uint64 size_bytes = 2;

    // Whether the blob already existed on the server
    bool existed = 3;
  }

  repeated UploadedBlobInfo uploaded_blobs = 1;
}

// Content Manager checkpoint blobs session event
message ContentManagerCheckpointBlobs {
  // The checkpoint ID
  string checkpoint_id = 1;

  // The baseline checkpoint ID this checkpoint was created from
  string baseline_checkpoint_id = 2;

  // The number of blobs that were added in this checkpoint compared to the baseline
  uint32 added_blobs_count = 3;

  // The number of blobs that were deleted in this checkpoint compared to the baseline
  uint32 deleted_blobs_count = 4;
}

// flattens the events out into a simpler structure for returning results
message RequestInfo {
  // information about the inference request as seen initially
  InferRequest infer_request = 1;
  google.protobuf.Timestamp infer_request_time = 2;

  reserved 3, 4;

  CompletionHostRequest completion_host_request = 11;
  google.protobuf.Timestamp completion_host_request_time = 12;

  reserved 5, 6;

  // the response from the model
  InferenceHostResponse inference_host_response = 15;
  google.protobuf.Timestamp inference_host_response_time = 16;

  reserved 7, 8;

  CompletionHostResponse completion_host_response = 13;
  google.protobuf.Timestamp completion_host_response_time = 14;

  ApiHttpResponse api_http_response = 9;
  google.protobuf.Timestamp api_http_response_time = 10;

  EmbeddingsSearchRequest embeddings_search_request = 17;
  google.protobuf.Timestamp embeddings_search_request_time = 18;

  EmbeddingsSearchResponse embeddings_search_response = 19;
  google.protobuf.Timestamp embeddings_search_response_time = 20;

  CompletionEmit completion_emit = 21;
  google.protobuf.Timestamp completion_emit_time = 22;

  CompletionResolution completion_resolution = 23;
  google.protobuf.Timestamp completion_resolution_time = 24;

  CompletionFeedback completion_feedback = 25;
  google.protobuf.Timestamp completion_feedback_time = 26;

  RIEditRequest edit_host_request = 27;
  google.protobuf.Timestamp edit_host_request_time = 28;

  RIEditResponse edit_host_response = 29;
  google.protobuf.Timestamp edit_host_response_time = 30;

  EditEmit edit_emit = 31;
  google.protobuf.Timestamp edit_emit_time = 32;

  EditResolution edit_resolution = 33;
  google.protobuf.Timestamp edit_resolution_time = 34;

  RequestMetadata request_metadata = 35;
  google.protobuf.Timestamp request_metadata_time = 36;

  // Note that this is a repeated field, so creators must combine any events
  // into a list, and also do the same for the times so that the two lists
  // should match 1:1
  repeated RetrievalResponse retrieval_response_list = 37;
  repeated google.protobuf.Timestamp retrieval_response_time_list = 38;

  RIChatRequest chat_host_request = 39;
  google.protobuf.Timestamp chat_host_request_time = 40;

  RIChatResponse chat_host_response = 41;
  google.protobuf.Timestamp chat_host_response_time = 42;

  // Note that this is a repeated field, so creators must combine any events
  // into a list, and also do the same for the times so that the two lists
  // should match 1:1
  repeated ExtensionError extension_error_list = 43;
  repeated google.protobuf.Timestamp extension_error_time_list = 44;

  CompletionPostProcess completion_post_process = 45;
  google.protobuf.Timestamp completion_post_process_time = 46;

  TenantInfo tenant_info = 47;

  ChatFeedback chat_feedback = 48;
  google.protobuf.Timestamp chat_feedback_time = 49;

  // Note that this is a repeated field, so creators must combine any events
  // into a list, and also do the same for the times so that the two lists
  // should match 1:1
  repeated RerankerResponse reranker_response_list = 50;
  repeated google.protobuf.Timestamp reranker_response_time_list = 51;

  RINextEditRequest next_edit_host_request = 52;
  google.protobuf.Timestamp next_edit_host_request_time = 53;

  RINextEditResponse next_edit_host_response = 54;
  google.protobuf.Timestamp next_edit_host_response_time = 55;

  ClientCompletionTimeline client_completion_timeline = 56;
  google.protobuf.Timestamp client_completion_timeline_time = 57;

  NextEditFeedback next_edit_feedback = 58;
  google.protobuf.Timestamp next_edit_feedback_time = 59;

  NextEditResolution next_edit_resolution = 60;
  google.protobuf.Timestamp next_edit_resolution_time = 61;

  NextEditEmit next_edit_emit = 62;
  google.protobuf.Timestamp next_edit_emit_time = 63;

  reserved 64, 65;

  RIInstructionRequest instruction_host_request = 66;
  google.protobuf.Timestamp instruction_host_request_time = 67;

  RIInstructionResponse instruction_host_response = 68;
  google.protobuf.Timestamp instruction_host_response_time = 69;

  repeated RISlackbotRequest slackbot_request_list = 70;
  repeated google.protobuf.Timestamp slackbot_request_time_list = 71;

  repeated RISlackbotResponse slackbot_response_list = 72;
  repeated google.protobuf.Timestamp slackbot_response_time_list = 73;

  RIGithubEvent github_event = 74;
  google.protobuf.Timestamp github_event_time = 75;

  repeated RIGithubProcessingResult github_processing_result_list = 76;
  repeated google.protobuf.Timestamp github_processing_result_time_list = 77;

  RIShareSaveChatRequest share_save_chat_request = 78;
  google.protobuf.Timestamp share_save_chat_request_time = 79;

  RIShareSaveChatResponse share_save_chat_response = 80;
  google.protobuf.Timestamp share_save_chat_response_time = 81;

  RIShareGetChatRequest share_get_chat_request = 82;
  google.protobuf.Timestamp share_get_chat_request_time = 83;

  RIShareGetChatResponse share_get_chat_response = 84;
  google.protobuf.Timestamp share_get_chat_response_time = 85;

  SmartPasteClientTimeline smart_paste_client_timeline = 86;
  google.protobuf.Timestamp smart_paste_client_timeline_time = 87;

  SmartPasteResolution smart_paste_resolution = 88;
  google.protobuf.Timestamp smart_paste_resolution_time = 89;

  RIGithubAppInstallationEvent github_app_installation_event = 90;
  google.protobuf.Timestamp github_app_installation_event_time = 91;

  RISlackbotInstallationEvent slackbot_installation_event = 92;
  google.protobuf.Timestamp slackbot_installation_event_time = 93;

  InstructionResolution instruction_resolution = 94;
  google.protobuf.Timestamp instruction_resolution_time = 95;

  InstructionEmit instruction_emit = 96;
  google.protobuf.Timestamp instruction_emit_time = 97;

  SlackbotFeedback slackbot_feedback = 98;
  google.protobuf.Timestamp slackbot_feedback_time = 99;

  reserved 100, 101, 102, 103, 104, 105, 106, 107;

  RIGleanRequest glean_request = 108;
  google.protobuf.Timestamp glean_request_time = 109;

  RIGleanResponse glean_response = 110;
  google.protobuf.Timestamp glean_response_time = 111;

  RILLMGenerateRequest llm_generate_request = 112;
  google.protobuf.Timestamp llm_generate_request_time = 113;

  RILLMGenerateResponse llm_generate_response = 114;
  google.protobuf.Timestamp llm_generate_response_time = 115;

  RIRemoteToolCallRequest remote_tool_call_request = 116;
  google.protobuf.Timestamp remote_tool_call_request_time = 117;

  RIRemoteToolCallResponse remote_tool_call_response = 118;
  google.protobuf.Timestamp remote_tool_call_response_time = 119;

  // Single timestamp for the entire dialog
  SubAgentDialog sub_agent_dialog = 120;
  google.protobuf.Timestamp sub_agent_dialog_time = 121;

  repeated PromptCacheUsage prompt_cache_usage_list = 122;
  repeated google.protobuf.Timestamp prompt_cache_usage_time_list = 123;

  repeated ToolUseData tool_use_data_list = 124;
  repeated google.protobuf.Timestamp tool_use_data_time_list = 125;

  RouterResponse router_response = 126;
  google.protobuf.Timestamp router_response_time = 127;

  PostprocessResponse postprocess_response = 128;
  google.protobuf.Timestamp postprocess_response_time = 129;

  AgentFeedback agent_feedback = 130;
  google.protobuf.Timestamp agent_feedback_time = 131;

  AgentRequestEvent agent_request_event = 132;
  google.protobuf.Timestamp agent_request_event_time = 133;

  RequestBlocked request_blocked = 134;
  google.protobuf.Timestamp request_blocked_time = 135;

  RequestSuspicious request_suspicious = 136;
  google.protobuf.Timestamp request_suspicious_time = 137;

  TokenExchangeError token_exchange_error = 138;
  google.protobuf.Timestamp token_exchange_error_time = 139;

  DailyRequestLimitExceeded daily_request_limit_exceeded = 140;
  google.protobuf.Timestamp daily_request_limit_exceeded_time = 141;

  RemoteAgentsCreateRequest remote_agents_create_request = 142;
  google.protobuf.Timestamp remote_agents_create_request_time = 143;

  RemoteAgentsCreateResponse remote_agents_create_response = 144;
  google.protobuf.Timestamp remote_agents_create_response_time = 145;

  RemoteAgentsChatRequest remote_agents_chat_request = 146;
  google.protobuf.Timestamp remote_agents_chat_request_time = 147;

  RemoteAgentsChatResponse remote_agents_chat_response = 148;
  google.protobuf.Timestamp remote_agents_chat_response_time = 149;

  RemoteAgentsInterruptRequest remote_agents_interrupt_request = 150;
  google.protobuf.Timestamp remote_agents_interrupt_request_time = 151;

  RemoteAgentsInterruptResponse remote_agents_interrupt_response = 152;
  google.protobuf.Timestamp remote_agents_interrupt_response_time = 153;

  RemoteAgentsDeleteRequest remote_agents_delete_request = 154;
  google.protobuf.Timestamp remote_agents_delete_request_time = 155;

  RemoteAgentsDeleteResponse remote_agents_delete_response = 156;
  google.protobuf.Timestamp remote_agents_delete_response_time = 157;

  RemoteAgentsAddSSHKeyRequest remote_agents_add_ssh_key_request = 158;
  google.protobuf.Timestamp remote_agents_add_ssh_key_request_time = 159;

  RemoteAgentsAddSSHKeyResponse remote_agents_add_ssh_key_response = 160;
  google.protobuf.Timestamp remote_agents_add_ssh_key_response_time = 161;

  ChatUserMessage chat_user_message = 162;
  google.protobuf.Timestamp chat_user_message_time = 163;

  ParenthesisTruncation parenthesis_truncation = 164;
  google.protobuf.Timestamp parenthesis_truncation_time = 165;

  RemoteAgentFeedback remote_agent_feedback = 166;
  google.protobuf.Timestamp remote_agent_feedback_time = 167;

  RemoteAgentsPauseRequest remote_agents_pause_request = 168;
  google.protobuf.Timestamp remote_agents_pause_request_time = 169;

  RemoteAgentsPauseResponse remote_agents_pause_response = 170;
  google.protobuf.Timestamp remote_agents_pause_response_time = 171;

  RemoteAgentsResumeRequest remote_agents_resume_request = 172;
  google.protobuf.Timestamp remote_agents_resume_request_time = 173;

  RemoteAgentsResumeResponse remote_agents_resume_response = 174;
  google.protobuf.Timestamp remote_agents_resume_response_time = 175;
}

message GetRequestInfoRequest {
  string user_id = 1;
  string request_id = 2;
}

message GetRequestInfoResponse {
  RequestInfo info = 1;
}

message FindRequestsRequest {
  option deprecated = true;

  google.protobuf.Timestamp start_time = 1;
  google.protobuf.Timestamp end_time = 2;
  // if set to non-zero value, the maximal amounts of entries to return
  uint32 limit = 3;
}

message FindRequestsResponse {
  option deprecated = true;

  string request_id = 1;
}

message FindSessionRequestsRequest {
  string session_id = 1;
  // If not set or set too high, the server may enforce a default limit.
  uint32 limit = 2;
}

message FindSessionRequestsResponse {
  string request_id = 1;
}

message FindUserRequestsRequest {
  string user_id = 1;
  // If not set or set too high, the server may enforce a default limit.
  uint32 limit = 2;
  // The type of request to filter by. If zero (UNKNOWN), returns all request types.
  RequestType request_type = 3;
}

message FindUserRequestsResponse {
  string request_id = 1;
}

// The type of the request. Technically this could be inferred from the events associated with a
// request, but this makes it more explicit and easier to query.
enum RequestType {
  UNKNOWN_REQUEST_TYPE = 0;
  COMPLETION = 1;
  EDIT = 2;
  CHAT = 3;
  EXTENSION_ERROR = 4;
  NEXT_EDIT = 5;
  SLACKBOT_CHAT = 6;
  SHARE_SAVE_CHAT = 7;
  SHARE_GET_CHAT = 8;
  AUTOFIX_CHECK = 9;
  AUTOFIX_PLAN = 10;
  LLM_GENERATE = 11;
  REMOTE_TOOL_CALL = 12;
  REMOTE_AGENT = 13;
  AGENT_CHAT = 14;
  LINEAR_OAUTH = 15;
  MEMORIES = 16;
  ORIENTATION = 17;
  MEMORIES_COMPRESSION = 18;
  REMOTE_AGENT_CHAT = 19;
  REMOTE_AGENT_LOG = 20;
}

// Wrapper around information we're interested in for all request types. The primary motivation for
// this event is to have a single table in BigQuery that users can join on for session and user ids
// across request types.
message RequestMetadata {
  // What type of request this is. Probably not very useful currently, but eventually we can use
  // this to cluster the BigQuery table if we need to.
  RequestType request_type = 1;

  // The session id of this request.
  string session_id = 2;

  // The user id of this request.
  // TODO(jacqueline): This is the deprecated notion of a user id, where it's either an email or an
  // API token id. Eventually I'd like to remove this field and rename opaque_user_id to user_id.
  string user_id = 3;

  // The user agent sent by the HTTP client.
  string user_agent = 4;

  // The opaque user id of this request. This ID can have different meanings, depending on the value
  // of user_id_type.
  auth_entities.UserId opaque_user_id = 5;

  // The user email of this request. Empty for requests using an API token.
  optional string user_email = 6;

  // ip address of the caller (based on x-forwarded-for header)
  string source_ip = 7;
}

// Extension point for what we log to RI regarding edit request.
message RIEditRequest {
  edit.EditRequest request = 1;

  reserved 2;

  // the retrieved chunks used in the prompt
  repeated RetrievalChunk retrieved_chunks = 3;

  // the tokens used for inference after all prompt modifications
  Tokenization tokenization = 4;
}

// Extension point for what we log to RI regarding edit response.
message RIEditResponse {
  edit.EditResponse response = 1;

  reserved 2;

  // the tokens generated after truncation and filtering
  Tokenization tokenization = 3;
}

// Extension point for what we log to RI regarding chat request.
message RIChatRequest {
  chat.ChatRequest request = 1;

  reserved 2;

  // the retrieved chunks used in the prompt
  repeated RetrievalChunk retrieved_chunks = 3;

  // the tokens used for inference after all prompt modifications
  Tokenization tokenization = 4;

  // external source ids used
  // this can be different from the external source ids sent in the request.
  repeated string external_source_ids = 5;

  // The source of the request, e.g. "slackbot". This should match the request source in the request
  // context of the request.
  string request_source = 6;
}

// Extension point for what we log to RI regarding chat response.
message RIChatResponse {
  chat.ChatResponse response = 1;

  reserved 2;

  // the tokens generated after truncation and filtering
  Tokenization tokenization = 3;

  // Error message received mid-stream, if any
  optional string error_message = 4;
}

// Extension point for what we log to RI regarding next edit request.
message RINextEditRequest {
  next_edit.NextEditRequest request = 1;

  // Inference prompt tokenization
  Tokenization tokenization = 2 [deprecated = true];

  // Selected chunk expanded range
  next_edit.CharRange expanded_range = 3 [deprecated = true];
}

message RINextEditSuggestion {
  enum PostProcessResult {
    UNKNOWN = 0;
    NOOP = 1;
    TOO_MANY_REMOVED_LINES = 2;
    INSERTED_TODO = 3;
    BLOCKED_LOCATION = 4;
    DELETED_IMPORTS = 5;
  }
  // The generation event that created this suggestion
  string generation_id = 1;

  // Tokenization of diff description prompt per suggestion
  Tokenization description_prompt = 2;

  // Tokenization of diff description inference per suggestion
  Tokenization description_output = 3;

  // Next edit suggestions provided to client, one per chunk in generation output
  next_edit.NextEditResponse result = 4;

  // Order in which this suggestion was returned to client
  uint32 suggestion_order = 5;

  // Post-processing result
  PostProcessResult post_process_result = 6;
}

// Per location activity for next edit
message RINextEditGeneration {
  enum PostProcessResult {
    NOOP = 0;
    LOW_PROB_CHANGED = 1;
    UNDO_RECENT_CHANGES = 2;
  }

  // ID for this generation event
  string generation_id = 1;

  // Chunks retrieved for this location for suggestion generation
  repeated RetrievalChunk retrieved_chunks = 2;

  // Tokenization of the generation prompt
  Tokenization generation_prompt = 3;

  // Tokenization of generation inference
  Tokenization generation_output = 4;

  // Location chunk for this generation
  RetrievalChunk location_chunk = 5;

  // Post-processing result
  PostProcessResult post_process_result = 6;

  // Editing score.
  float editing_score = 7;
}

// Extension point for what we log to RI regarding next edit response stream.
// Multiple messages will be written for a single request ID.
// Consumer must merge all messages to get the complete picture.
message RINextEditResponse {
  next_edit.NextEditResponse response = 1 [deprecated = true];
  Tokenization tokenization = 2 [deprecated = true];

  // Locations retrieved by the localization model
  repeated RetrievalChunk retrieved_locations = 3;

  // Change generation activity that leads to suggestions
  repeated RINextEditGeneration generation = 4;

  // The suggestions from the next edit stream
  repeated RINextEditSuggestion suggestions = 5;

  // Locations that were blocked
  repeated RetrievalChunk blocked_locations = 6;
}

message RIInstructionRequest {
  edit.InstructionRequest request = 1;

  // the retrieved chunks used in the prompt
  repeated RetrievalChunk retrieved_chunks = 2;

  // the tokens used for inference after all prompt modifications
  Tokenization tokenization = 3;
}

message RIInstructionResponse {
  // Aggregated instruction response can contain multiple tool calls
  edit.InstructionAggregateResponse response = 1;

  // the tokens generated after truncation and filtering
  Tokenization tokenization = 2;

  // Error message received mid-stream, if any
  optional string error_message = 3;
}

// An event representing an error reported from an extension.
message ExtensionError {
  // request ID for the original request (e.g. completion request), if any
  optional string original_request_id = 1;
  // error message from extension (this SHOULD NOT contain sensitive data)
  string message = 2;
  // stack trace from extension (this SHOULD NOT contain sensitive data)
  string stack_trace = 3;
  // arbitrary unstructured diagnostics (this MAY contain sensitive data; for support UI only)
  map<string, string> diagnostics = 4 [debug_redact = true];
}

// A wrapper around a slackbot.SlackEvent. Wrapping is useful in case we ever want to record
// additional metadata.
message RISlackbotRequest {
  enum ChannelType {
    UNKNOWN_CHANNEL_TYPE = 0;
    // Direct message between two individuals.
    IM = 1;
    // Multi-party instant message.
    MPIM = 2;
    SHARED_CHANNEL = 3;
    PRIVATE_CHANNEL = 4;
    GENERAL_CHANNEL = 5;
    // Catch-all for channels that don't fit into the other categories.
    CHANNEL = 6;
  }

  slackbot.SlackEvent slack_event = 1;

  // Add additional fields that are related to the request but aren't in the SlackEvent proto below.
  ChannelType channel_type = 2;
}

message RISlackbotResponse {
  message Event {
    // The time when this event occurred. This is not a Slack id.
    google.protobuf.Timestamp time = 1;

    oneof event {
      PostMessage post_message = 2;
      UpdateMessage update_message = 3;
    }
  }

  message PostMessage {
    // Slack id of the posted message.
    string response_message_timestamp = 1;
    string text = 2;
  }

  message UpdateMessage {
    // Slack id of the message being updated. This should match a previously posted message.
    string response_message_timestamp = 1;
    string text = 2;
  }

  // Slack id of the channel.
  string channel = 1;

  // Slack id of the thread being responded to.
  string thread_timestamp = 2;

  // Slack id of the message that prompted the response.
  string request_message_timestamp = 3;

  // All the events for this response. We stream responses so it's expected that this will have many
  // events.
  repeated Event response_events = 4;
}

// Share service related events.
message RIShareSaveChatRequest {
  share.SaveChatConversationRequest request = 1;
}

message RIShareSaveChatResponse {
  share.SaveChatConversationResponse response = 1;
}

message RIShareGetChatRequest {
  share.GetChatConversationRequest request = 1;
}

message RIShareGetChatResponse {
  share.GetChatConversationResponse response = 1;
}

// High level request containing user events collected by extension.
// Only collected for special tenants with explicit permission.
message RecordFullExportUserEventsRequest {
  // Wrapper around user events.
  ExtensionData extension_data = 1;

  // The user id of the user for which the event was collected. This is expected to be
  // empty in the json received from extension and filled in by api-proxy.
  optional string user_id = 2;

  // The session id of the user for which the event was collected. This is expected to be empty
  // in the json received from the extension and filled in by api-proxy.
  optional string session_id = 3;

  // the tenant information of the request
  optional TenantInfo tenant_info = 4;
}

/// nothing is returned by a RecordFullExportUserEventsRequest call.
message RecordFullExportUserEventsResponse {}

// This object is currently just a wrapper around user events. It exists to protect against a future
// where we want to separate the frontend and backend protos without implementing too much logic
// to copy between the two. In such a future, this object would be shared across both protos.
message ExtensionData {
  repeated FullExportUserEvent user_events = 1;
}

// Single user event collected by extension to be recorded.
message FullExportUserEvent {
  // The time when the event was collected.
  google.protobuf.Timestamp time = 1;

  // The file path of the file associated with the event.
  optional string file_path = 2 [debug_redact = true];

  oneof event {
    // A single VSCode text edit event, e.g. keystroke, copy paste
    TextEditEvent text_edit = 3;

    // Request id issued by the extension for a completion request.
    // This is used to link completion requests to the event stream we are collecting.
    // We collect this event and take the time stamp as early as possible after
    // triggering a completion, which is when the requestid is issued.
    CompletionRequestIdIssuedEvent completion_request_id_issued = 4;

    // Request id issued by the extension for an instruction request.
    EditRequestIdIssuedEvent edit_request_id_issued = 5;

    // Request id issued by the extension for a next edit request.
    NextEditRequestIdIssuedEvent next_edit_request_id_issued = 6;
  }
}

// A single VSCode text edit event, e.g. keystroke, copy paste.
message TextEditEvent {
  enum Reason {
    //  Default value for completeness. Not expected from real clients.
    UNKNOWN_REASON = 0;

    // The numbering is based on the enum in vscode edit reason.
    UNDO = 1;
    REDO = 2;
  }

  // The reason for the event (emitted by vscode specifically for undo or redo).
  optional Reason reason = 1;

  // Each event can consist of one or more content changes.
  repeated ContentChange content_changes = 2;

  // The file path of the file associated with the event.
  optional string file_path = 3;

  // Hash of the characters in and around the changes after applying them
  // Used to detect external file changes (e.g. branch switches) between text edit events
  // Calculated as the hash of a simple concat of the text defined by hash char ranges.
  optional string after_changes_hash = 4;

  // The char ranges used to compute the hashes
  // we avoid hashing entire files because it is expensive for large files
  // client is free to pick any set of char ranges
  repeated Range hash_char_ranges = 5;

  // The workspace uid of the workspace associated with the event.
  optional string source_folder_root = 6 [debug_redact = true];

  // The length (in characters) of the doc after the changes are applied. Catches hashing edge cases.
  optional uint32 after_doc_length = 7;
}

// Request id issued by the extension for a completion request.
message CompletionRequestIdIssuedEvent {
  // The request id issued by the extension.
  string request_id = 1;

  optional string file_path = 2;
}

// Request id issued by the extension for an instruction request.
message EditRequestIdIssuedEvent {
  // The request id issued by the extension.
  string request_id = 1;

  // The file path of the file associated with the event.
  optional string file_path = 2;
}

// Request id issued by the extension for a next edit request.
message NextEditRequestIdIssuedEvent {
  // The request id issued by the extension.
  string request_id = 1;

  // The file path of the file associated with the event.
  optional string file_path = 2;
}

// A single content change to a file.
message ContentChange {
  // The new text in the specified range.
  string text = 1 [debug_redact = true];

  // The range of the text that was replaced.
  Range range = 2;
}

// A range in a text document expressed as global start and end character positions.
message Range {
  // The range's start character position.
  int32 start = 1;

  // The range's end position (exclusive).
  int32 end = 2;
}

// Request to record information about a tenant. See `TenantEvent`.
message RecordTenantEventsRequest {
  TenantInfo tenant_info = 2;
  repeated TenantEvent events = 3;
}

// An event related to a tenant that isn't tied to any particular session or request.
message TenantEvent {
  google.protobuf.Timestamp time = 1;

  // A unique identifier for this event, set when the event is published. This is intended to help
  // exporters write events idempotently.
  string event_id = 2;

  oneof event {
    AddUserToTenant add_user_to_tenant = 3;
    RemoveUserFromTenant remove_user_from_tenant = 4;
    InviteUserToTenant invite_user_to_tenant = 5;
    DeleteInvitation delete_invitation = 6;
    UpdateSubscription update_subscription = 7;
    CreateTenantForTeam create_tenant_for_team = 8;
    AcceptInvitation accept_invitation = 9;
    DeclineInvitation decline_invitation = 10;
  }
}

message AddUserToTenant {
  // Full information about the user that was added.
  auth_entities.User user = 1;
}

message RemoveUserFromTenant {
  // Full information about the user that was removed.
  auth_entities.User user = 1;
}

message RecordGenericEventsRequest {
  // We always record a session id for generic events in an attempt to correlate different events
  // together. If you don't have something that makes sense as the session id, just assign a random
  // uuid.
  string session_id = 1;
  repeated GenericEvent events = 2;
}

// An event that isn't tied to any particular user or tenant. Putting events here should be a last
// resort. If you know the tenant you should almost certainly use SessionEvent instead.
message GenericEvent {
  google.protobuf.Timestamp time = 1;
  optional string event_id = 2;
  oneof event {
    Recaptcha recaptcha = 3;
    Verisoul verisoul = 4;
    Verosint verosint = 5;
  }
}

message Recaptcha {
  string email = 1;
  string assessment_name = 2;
  repeated string assessment_reasons = 3;
  float score = 4;
  string action = 5;
  string user_agent = 6;
  string source_ip = 7;
}

message Verisoul {
  // opaque_user_id may not be present in pre-sign up events
  auth_entities.UserId opaque_user_id = 1;
  // report - should be valid JSON
  string report = 2;
}

message Verosint {
  // opaque_user_id may not be present in pre-sign up events
  auth_entities.UserId opaque_user_id = 1;
  // report - should be valid JSON
  string report = 2;
}

// RemoteAgentLog is used to store log data from remote agents
message RemoteAgentLogEntry {
  // The log message
  string message = 1;
  // The transport type
  string transport = 2;
  // Raw timestamp from journalctl (microseconds since epoch as string)
  google.protobuf.Timestamp timestamp = 3;
  // Additional fields can be added as needed
}

// RemoteAgentLog is used to store log data from remote agents
message RemoteAgentLog {
  // The log entries
  repeated RemoteAgentLogEntry entries = 1;
  // The ID of the remote agent
  string remote_agent_id = 2;
  // The component that generated the log
  string component = 3;
  // Start timestamp of the log batch
  google.protobuf.Timestamp start_timestamp = 4;
  // End timestamp of the log batch
  google.protobuf.Timestamp end_timestamp = 5;
}

// A feature vector is a set of key-vaue pairs that describe a user's device.
message FeatureVectorReport {
  // The feature vector
  map<int32, string> feature_vector = 1;
  // The source IP
  string source_ip = 2;
  // User agent
  string user_agent = 3;
  // Subset of the request headers that are not PII (e.g. Accept but not Authorization)
  map<string, string> headers = 4;
}

// This is an internal proto, used for publishing to the pub/sub topic. We wrap around different
// types of messages so that we can add new message types in the future without creating a separate
// topic for each one.
message RequestInsightMessage {
  oneof message {
    UpdateRequestInfoRequest update_request_info_request = 1;
    RecordFullExportUserEventsRequest record_full_export_user_events_request = 2;
    RecordSessionEventsRequest record_session_events_request = 3;
    RecordTenantEventsRequest record_tenant_events_request = 4;
    RecordGenericEventsRequest record_generic_events_request = 5;
  }
}

// ClientCompletionTimeline tracks the times related to a completion request.
message ClientCompletionTimeline {
  google.protobuf.Timestamp initial_request_time = 2;
  google.protobuf.Timestamp api_start_time = 3;
  google.protobuf.Timestamp api_end_time = 4;
  google.protobuf.Timestamp emit_time = 5;
}

message RIGithubEvent {
  github_event.GithubEvent github_event = 1;
}

message RIGithubProcessingResult {
  message DiffInfo {
    string filename = 1;
    // in hex
    string old_blobname = 2;
    // in hex
    string new_blobname = 3;
    string old_filename = 4;
    bool removed = 5;
    bool renamed = 6;
  }
  // the final status of the diffing process
  google.rpc.Status status = 1;

  // the commit sha of the last updated commit
  // aka the commited that was "diffed" against
  string last_updated_commit = 2;

  // information about the diffing process
  repeated DiffInfo diff_infos = 3;
  repeated string filenames_to_download = 4;

  // information about ignorefiles
  repeated DiffInfo ignore_diff_infos = 7;
  repeated string ignore_filenames_to_download = 8;

  // the checkpoint blobs
  base.blob_names.Blobs checkpoint_blobs = 5;

  // any error returned by git apply
  string git_apply_error = 6;

  // any errors when downloading file contents
  string file_download_error = 9;

  // The blob name of the diff file
  string diff_blob_name = 10;

  // Files ignored when applying the diff
  repeated string ignored_files = 11;
}

enum InstallEventType {
  UNKNOWN_INSTALL_EVENT_TYPE = 0;
  UNINSTALL = 1;
  INSTALL = 2;
}

message RIGithubAppInstallationEvent {
  google.rpc.Status status = 1;
  InstallEventType event_type = 2;
}

// Recorded when a user authorizes our OAuth app with their user account.
message GithubUserAuthorize {
  int64 github_user_id = 1;
}

message RISlackbotInstallationEvent {
  google.rpc.Status status = 1;
  InstallEventType event_type = 2;
}

message RIGleanRequest {
  // Wraps the raw request
  glean.SearchRequest request = 1;
  string request_source = 2;

  // Nested query processor config
  message RIGleanQueryProcessorConfig {
    string gcp_project_id = 1;
    string gcp_region = 2;
    string model_name = 3;
    float temperature = 4;
    int32 max_output_tokens = 5;
    int32 max_results = 6;
  }
  RIGleanQueryProcessorConfig query_processor_config = 3;
}

message RILLMGenerateRequest {
  agents.LLMGenerateRequest request = 1;
}

message RILLMGenerateResponse {
  agents.LLMGenerateResponse response = 1;
}

message RIRunRemoteToolRequest {
  // Copied from agents.RunRemoteToolRequest, besides any API token-ish info
  string tool_name = 1 [deprecated = true];
  string tool_input_json = 2;
  agents.RemoteToolId tool_id = 3;
}

message RIRemoteToolCallRequest {
  // deprecated: agents.RunRemoteToolRequest, which may contain API tokens.
  // reserved cannot be used in a oneof, so it's out here
  reserved 3;

  oneof request {
    agents.CodebaseRetrievalRequest codebase_retrieval_request = 1;
    agents.EditFileRequest edit_file_request = 2;
    // This is an RI-specific type to help prevent accidentally including
    // sensitive info, like API tokens
    RIRunRemoteToolRequest ri_run_remote_tool_request = 4;
  }
}

message RIRemoteToolCallResponse {
  oneof response {
    agents.CodebaseRetrievalResponse codebase_retrieval_response = 1;
    agents.EditFileResponse edit_file_response = 2;
    // Expected: generic tool call response for server-defined tools
    agents.RunRemoteToolResponse run_remote_tool_response = 3;
  }
}

// For requests implemented as an agent in the backend, whose dialog
// may not be returned to the client as part of the response
message SubAgentDialog {
  repeated chat.Exchange dialog = 3;
}

message ToolUseData {
  string tool_name = 1;
  string tool_use_id = 2;
  optional bool tool_output_is_error = 3;
  uint32 tool_run_duration_ms = 4;
  string tool_input = 5;
  optional bool is_mcp_tool = 6;
  optional string conversation_id = 7;
  optional uint32 chat_history_length = 8;
  optional string tool_request_id = 9;
  optional uint32 tool_output_len = 10;
  optional uint32 tool_input_len = 11;
}

message ThirdPartyModelMessageHistory {
  string user_message = 1;
  string assistant_message = 2;
}

message ThirdPartyModelRequest {
  string prompt = 1;
  string system_prompt = 2;
  repeated ThirdPartyModelMessageHistory messages = 3;
}

message PromptCacheUsage {
  uint32 input_tokens = 1;
  uint32 cache_read_input_tokens = 2;
  uint32 cache_creation_input_tokens = 3;
  uint32 text_input_tokens = 4;
  uint32 tool_input_tokens = 5;
  uint32 text_output_tokens = 6;
  uint32 tool_output_tokens = 7;
  optional string model_caller = 8;
}

// The Glean response and associated metadata
message RIGleanResponse {
  // Wraps the raw response
  glean.SearchResponse response = 1;

  ThirdPartyModelRequest generate_search_queries_request = 2;
  repeated string generate_search_queries_response = 3;
}

message RIGleanOAuthURLRequest {
  // Empty message as GetOAuthURLRequest doesn't contain any fields
}

message RIGleanOAuthURLResponse {
  // The OAuth URL returned to the client
  string oauth_url = 1 [debug_redact = true];
}

message ParsedRouterResponse {
  string category = 1; // Name of the category enum
  repeated string filenames = 2;
  repeated string docsets = 3;
}

message RouterResponse {
  // Prompt tokenization
  Tokenization prompt_tokens = 1;

  // Response tokenization
  optional Tokenization response_tokens = 2;

  // Parsed response if successful
  optional ParsedRouterResponse parsed_response = 3;

  // Error if unsuccessful
  optional string error_message = 4;
}

message PostprocessResponse {
  // Tokenization if successful
  optional Tokenization tokenization = 1;

  // Error if unsuccessful
  optional string error_message = 2;
}

// Recorded when a request is blocked due to content filtering.
message RequestBlocked {
  auth_entities.UserId opaque_user_id = 1;
  string user_email = 2;
  string check_type = 3; // The type of check that caused the request to be blocked
}

// Recorded when a request is flagged as suspicious but not blocked.
message RequestSuspicious {
  auth_entities.UserId opaque_user_id = 1;
  string user_email = 2;
  string check_type = 3; // The type of check that flagged the request as suspicious
}

// Recorded when a FindMissing request is made to check for missing blobs.
message RIFindMissing {
  string model_name = 1; // The model name used in the FindMissing request
  int32 blob_count = 2; // The number of blobs in the request
  int32 missing_count = 3; // The number of missing blobs found
  int32 nonindexed_count = 4; // The number of nonindexed blobs found
}

// Recorded when a BatchUpload request is made to upload blobs.
message RIBatchUpload {
  int32 blob_count = 1; // The number of blobs in the upload request
  int64 total_size = 2; // The total size of all blobs in bytes
}

// Recorded when token-exchange refuses to authenticate a request.
message TokenExchangeError {
  enum Reason {
    UNKNOWN = 0;

    // Request was sent to the wrong shard. We see this happen when attackers probe to figure out
    // their tenant programatically.
    TENANT_NOT_IN_SHARD = 1;
  }

  Reason reason = 1;
  auth_entities.UserId opaque_user_id = 2;
  string user_email = 3;
}

// Recorded when a user exceeds their daily request limit for agent
message DailyRequestLimitExceeded {
  auth_entities.UserId opaque_user_id = 1;
  int32 limit = 2; // The daily limit
}

message InviteUserToTenant {
  auth_entities.TenantInvitation invitation = 1;
}

message DeleteInvitation {
  string invitation_id = 1;
}

// Note(jacqueline): This is copying fields directly from the TeamManagementService's
// UpdateSubscription endpoint. Normally I would want to import the proto rather than copying
// fields, but the intention is that this event is only triggered when the update succeeds, and I
// want to leave our usual full request wrapper for if/when we decide to record every
// request/response to this service.
message UpdateSubscription {
  string subscription_id = 1;
  int32 seats = 2;
}

message CreateTenantForTeam {
  services.tenant.Tenant tenant = 1;
  string admin_user_id = 2;
  string subscription_id = 3;
}

message AcceptInvitation {
  string invitation_id = 1;
  auth_entities.User user = 2;
}

message DeclineInvitation {
  string invitation_id = 1;

  // We can't record a full user proto here because one may not exist for this user.
  string invitee_email = 2;
}

message RemoteAgentsCreateRequest {
  remote_agents.CreateAgentRequest request = 1;
}

message RemoteAgentsCreateResponse {
  remote_agents.CreateAgentResponse response = 1;
}

message RemoteAgentsChatRequest {
  remote_agents.ChatRequest request = 1;
}

message RemoteAgentsChatResponse {
  remote_agents.ChatResponse response = 1;
}

message RemoteAgentsInterruptRequest {
  remote_agents.InterruptAgentRequest request = 1;
}

message RemoteAgentsInterruptResponse {
  remote_agents.InterruptAgentResponse response = 1;
}

message RemoteAgentsDeleteRequest {
  remote_agents.DeleteAgentRequest request = 1;
}

message RemoteAgentsDeleteResponse {
  remote_agents.DeleteAgentResponse response = 1;
}

message RemoteAgentsAddSSHKeyRequest {
  remote_agents.AddSSHKeyRequest request = 1;
}

message RemoteAgentsAddSSHKeyResponse {
  remote_agents.AddSSHKeyResponse response = 1;
}

message RemoteAgentsPauseRequest {
  remote_agents.PauseAgentRequest request = 1;
}

message RemoteAgentsPauseResponse {
  remote_agents.PauseAgentResponse response = 1;
}

message RemoteAgentsResumeRequest {
  remote_agents.ResumeAgentRequest request = 1;
}

message RemoteAgentsResumeResponse {
  remote_agents.ResumeAgentResponse response = 1;
}

// ChatUserMessage represents a message sent by a user in a chat conversation.
// This event captures important metadata about user messages to help analyze
// user behavior and improve the chat experience.
message ChatUserMessage {
  // Chat mode enum that mirrors the public_api_proto ChatMode enum
  enum ChatMode {
    CHAT_MODE_CHAT = 0;
    CHAT_MODE_AGENT = 1;
    CHAT_MODE_MEMORIES = 2;
    CHAT_MODE_ORIENTATION = 3;
    CHAT_MODE_MEMORIES_COMPRESSION = 4;
    CHAT_MODE_REMOTE_AGENT = 5;
  }

  // The chat mode used for this message
  ChatMode chat_mode = 1;

  // The length of the chat history at the time of the message
  uint32 chat_history_length = 2;

  // Number of image nodes in the message
  uint32 image_node_count = 3;

  // Character count of the text content
  uint32 character_count = 4;

  // Line count of the text content
  uint32 line_count = 5;

  // ID of the user who sent the message
  auth_entities.UserId opaque_user_id = 6;
  optional string model_name = 7;
}
