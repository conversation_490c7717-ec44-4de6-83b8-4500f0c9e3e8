package client

import (
	"context"
	"errors"
	"math/rand"
	"regexp"
	"sync"
	"testing"
	"time"

	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/hashicorp/go-retryablehttp"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var (
	ErrTenantNotFound    = errors.New("tenant not found")
	ErrNamespaceNotFound = errors.New("namespace not found")

	selfServeNamespaceRegex, _ = regexp.Compile(`^[di][0-9]+$`)
)

// We use these same conventions in some other python code as well, like in the
// embeddings indexer
// TODO: move this to services/lib?
func isRetryable(err error) bool {
	code := status.Code(err)
	switch code {
	case codes.ResourceExhausted, codes.Unavailable, codes.DeadlineExceeded:
		return true
	default:
		return false
	}
}

type TenantCache interface {
	// GetTenant returns a tenant by ID
	GetTenant(tenantID string) (*tenantproto.Tenant, error)

	// GetTenantByName returns a tenant by name
	GetTenantByName(tenantName string) (*tenantproto.Tenant, error)

	// GetAllTenants returns all tenants
	GetAllTenants() ([]*tenantproto.Tenant, error)

	// GetTenantsInNamespace returns all tenants in a namespace
	GetTenantsInNamespace(shardNamespace string) ([]*tenantproto.Tenant, error)

	// Close to cleanup resources for this client and cancels the context
	Close()
}

type tenantCache struct {
	cancel    context.CancelFunc
	namespace string

	client        TenantWatcherClient
	tenantChanges <-chan TenantChange

	tenantsLock sync.RWMutex
	tenants     map[string]*tenantproto.Tenant

	// This waits until the cache is initialized with at least one response.
	initialized sync.WaitGroup

	// This is called whenever we get an update
	onUpdate func()
}

func (tc *tenantCache) updateTenants(tenantChange TenantChange) {
	tc.tenantsLock.Lock()
	defer tc.tenantsLock.Unlock()
	tc.onUpdate()
	for _, rawChange := range tenantChange.Response.Tenants {
		switch change := rawChange.Type.(type) {
		case *tenantproto.TenantChange_Updated:
			log.Info().Msgf("Updated tenant: %v", change.Updated.Tenant)
			tc.tenants[change.Updated.Tenant.Id] = change.Updated.Tenant
		case *tenantproto.TenantChange_Removed:
			log.Info().Msgf("Removed tenant: %v", change.Removed.Tenant)
			delete(tc.tenants, change.Removed.Tenant.Id)
		}
	}
}

// Starts a new stream synchronously and listens for updates, stopping and
// returning when any error is encountered
func (tc *tenantCache) startStream(ctx context.Context) (anyUpdates bool, err error) {
	tc.tenantChanges, err = tc.client.WatchTenants(ctx, tc.namespace)
	if err != nil {
		return false, err
	}

	// Wait for the initial update before returning - the later updates can all
	// happen asynchronously
	tenantChange, ok := <-tc.tenantChanges

	if !ok {
		return false, errors.New("tenant watcher closed unexpectedly")
	} else if tenantChange.Err != nil {
		return false, tenantChange.Err
	}
	log.Info().Msg("Received initial update")
	if !tenantChange.Response.IsInitial {
		log.Warn().Msgf("Expected initial update to be first, found %v", tenantChange.Response)
	}
	tc.updateTenants(tenantChange)

	log.Info().Msg("Watching tenants")
	for tenantChange := range tc.tenantChanges {
		if tenantChange.Err != nil {
			return true, tenantChange.Err
		}
		tc.updateTenants(tenantChange)
	}

	return true, nil
}

// Starts a stream synchronously, watching for updates in the background. It
// does retries as appropriate, restarting the stream if necessary
func (tc *tenantCache) start(ctx context.Context) {
	// Setup for exponential backoff
	const minBackoff = time.Second
	const maxBackoff = time.Second * 32
	attemptNum := 0
	source := rand.New(rand.NewSource(int64(time.Now().Nanosecond())))

	for ; ; attemptNum++ {
		log.Info().Msg("Starting tenant watcher stream")
		anyUpdates, err := tc.startStream(ctx)
		if anyUpdates {
			// Reset the backoff counter
			attemptNum = 0
		}
		// Reuse the backoff from this library which we already have, and add
		// some extra jitter to help avoid thundering herd. The jitter is up to
		// 50% of the backoff value
		sleepDuration := retryablehttp.DefaultBackoff(minBackoff, maxBackoff, attemptNum, nil)
		jitter := (source.Float64() - 0.5) * float64(sleepDuration)
		sleepDuration += time.Duration(jitter)
		if err == nil {
			log.Info().Msgf("Tenant watcher closed connection, retrying in %s", sleepDuration)
			time.Sleep(sleepDuration)
		} else if isRetryable(err) {
			log.Error().Err(err).Msgf("Error listening to tenant watcher, retrying in %s", sleepDuration)
			time.Sleep(sleepDuration)
		} else if ctx.Err() != nil {
			log.Info().Msg("Tenant watcher cancelled")
			return
		} else {
			log.Panic().Err(err).Msg("Error listening to tenant watcher")
		}
	}
}

func (tc *tenantCache) GetTenant(tenantID string) (*tenantproto.Tenant, error) {
	tc.initialized.Wait()
	tc.tenantsLock.RLock()
	defer tc.tenantsLock.RUnlock()

	if tenant, ok := tc.tenants[tenantID]; ok {
		return tenant, nil
	}
	log.Warn().Msgf("tenant ID not found: %v", tenantID)
	return nil, ErrTenantNotFound
}

func (tc *tenantCache) GetTenantByName(tenantName string) (*tenantproto.Tenant, error) {
	tc.initialized.Wait()
	tc.tenantsLock.RLock()
	defer tc.tenantsLock.RUnlock()

	for _, tenant := range tc.tenants {
		if tenant.Name == tenantName {
			return tenant, nil
		}
	}
	log.Warn().Msgf("tenant name not found: %v", tenantName)
	return nil, ErrTenantNotFound
}

func (tc *tenantCache) GetAllTenants() ([]*tenantproto.Tenant, error) {
	tc.initialized.Wait()
	tc.tenantsLock.RLock()
	defer tc.tenantsLock.RUnlock()

	copy := make([]*tenantproto.Tenant, 0, len(tc.tenants))
	for _, tenant := range tc.tenants {
		copy = append(copy, tenant)
	}
	return copy, nil
}

func (tc *tenantCache) GetTenantsInNamespace(shardNamespace string) ([]*tenantproto.Tenant, error) {
	tc.initialized.Wait()
	tc.tenantsLock.RLock()
	defer tc.tenantsLock.RUnlock()

	var inNamespace []*tenantproto.Tenant
	for _, tenant := range tc.tenants {
		if tenant.ShardNamespace == shardNamespace || tenant.OtherNamespace == shardNamespace {
			inNamespace = append(inNamespace, tenant)
		}
	}
	if len(inNamespace) == 0 {
		// We should never have an empty namespace
		log.Warn().Msgf("could not find namespace %s", shardNamespace)
		return nil, ErrNamespaceNotFound
	}
	return inNamespace, nil
}

func (tc *tenantCache) Close() {
	tc.client.Close()
	tc.cancel()
}

func NewTenantCache(client TenantWatcherClient, namespace string) TenantCache {
	ctx, cancel := context.WithCancel(context.Background())
	tc := &tenantCache{
		cancel:    cancel,
		namespace: namespace,
		client:    client,
		tenants:   make(map[string]*tenantproto.Tenant),
	}
	// To mark initialized as done once the cache is populated with some initial
	// value, we add 1 here then use a OnceFunc to make sure Done() gets called
	// at most once.
	tc.initialized.Add(1)
	tc.onUpdate = sync.OnceFunc(func() {
		log.Info().Msg("Tenant cache initialized")
		tc.initialized.Done()
	})
	go tc.start(ctx)
	return tc
}

// For unit tests only, this returns a TenantCache along with a channel that
// unit tests can use to wait for notifications that updates have been processed
func NewTestTenantCache(t *testing.T, client TenantWatcherClient, namespace string) (cache TenantCache, updateCh <-chan struct{}) {
	ctx, cancel := context.WithCancel(context.Background())
	ch := make(chan struct{}, 10000)
	tc := &tenantCache{
		cancel:    cancel,
		namespace: namespace,
		client:    client,
		tenants:   make(map[string]*tenantproto.Tenant),
	}
	tc.initialized.Add(1)
	initializedOnce := sync.OnceFunc(func() {
		// We only want to call Done() once
		tc.initialized.Done()
	})
	tc.onUpdate = func() {
		initializedOnce()
		ch <- struct{}{}
	}
	go tc.start(ctx)
	return tc, ch
}

func MetricsTenantName(t *tenantproto.Tenant) string {
	if selfServeNamespaceRegex.MatchString(t.ShardNamespace) {
		return "[self-serve]"
	}
	return t.Name
}
