"""Tests for session_state module."""

import time

import grpc
import pydantic
import pytest

from services.completion_host.single_model_server.handler import CompletionException
from services.completion_host.single_model_server.session_state import (
    RequestPromptState,
    SessionState,
    SessionStateLRU,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext


@pytest.fixture
def auth_info():
    """Create a test AuthInfo object."""
    return AuthInfo(
        tenant_id="test_tenant_id",
        tenant_name="test_tenant",
        user_id=pydantic.SecretStr("test_user_id"),
        user_email="<EMAIL>",
        shard_namespace="test_shard",
        cloud="test_cloud",
    )


@pytest.fixture
def request_context():
    """Create a test RequestContext object."""
    return RequestContext(
        request_id="test_request_id",
        request_session_id="test_session_id",
        request_source="test",
    )


class TestSessionState:
    """Unit tests for SessionState class."""

    def test_session_state_initialization(self, auth_info):
        """Test SessionState initializes correctly."""
        session_state = SessionState(auth_info, prompt_caching_enabled=True)

        assert session_state.common_auth == auth_info
        assert session_state._prompt_caching_enabled is True
        assert session_state._pending_sequence_id == 0
        assert session_state._pending_session_state_uuid == ""
        assert session_state._prompt_state.sequence_id == 0

    def test_valid_for_request_same_auth(self, auth_info):
        """Test valid_for_request returns True for same auth."""
        session_state = SessionState(auth_info, prompt_caching_enabled=True)
        assert session_state.valid_for_request(auth_info) is True

    def test_update_sequence_id(self, auth_info):
        """Test update with higher sequence ID updates the state."""
        session_state = SessionState(auth_info, prompt_caching_enabled=True)

        # Update with higher sequence ID
        session_state.update(5, "session_state_uuid")
        assert session_state._pending_sequence_id == 5
        assert session_state._pending_session_state_uuid == "session_state_uuid"

        # Update with same sequence ID but different session state UUID
        session_state.update(5, "session_state_uuid2")
        assert session_state._pending_sequence_id == 5
        assert session_state._pending_session_state_uuid == "session_state_uuid2"

        # Update with higher sequence ID
        session_state.update(10, "session_state_uuid3")
        assert session_state._pending_sequence_id == 10
        assert session_state._pending_session_state_uuid == "session_state_uuid3"

    def test_update_cancels_within_rollback_time(self, auth_info):
        """Test update with lower sequence ID cancels if within rollback time."""
        session_state = SessionState(auth_info, prompt_caching_enabled=True)

        # Set initial sequence ID
        session_state.update(10, "session_state_uuid")

        # Attempting to update with lower sequence ID and different session state UUID should cancel (within rollback time)
        with pytest.raises(CompletionException) as exc_info:
            session_state.update(5, "session_state_uuid2")

        assert exc_info.value.code() == grpc.StatusCode.CANCELLED

    def test_update_lower_sequence_id_resets_after_rollback_time(self, auth_info):
        """Test update with lower sequence ID resets state after rollback time."""
        session_state = SessionState(auth_info, prompt_caching_enabled=True)

        # Set initial sequence ID and modify last update time to be old
        session_state.update(10, "session_state_uuid")
        session_state._last_update_time = (
            time.time() - SessionState.SEQUENCE_ROLLBACK_TIME - 1
        )

        # Store original prompt state to verify it gets reset
        original_prompt_state = session_state._prompt_state

        # Update with lower sequence ID should reset state
        session_state.update(5, "session_state_uuid2")
        assert session_state._pending_sequence_id == 5
        assert session_state._pending_session_state_uuid == "session_state_uuid2"
        assert session_state._prompt_state is not original_prompt_state
        assert session_state._prompt_state.sequence_id == 0

    def test_attempt_cancel_with_matching_sequence_id(self, auth_info):
        """Test attempt_cancel does not raise exception when session state UUIDs match."""
        session_state = SessionState(auth_info, prompt_caching_enabled=True)

        session_state.update(5, "session_state_uuid")

        # Should not raise exception when session state UUIDs match
        session_state.attempt_cancel("session_state_uuid", "test")

    def test_attempt_cancel_with_different_sequence_id(self, auth_info):
        """Test attempt_cancel raises exception when session state UUIDs don't match."""
        session_state = SessionState(auth_info, prompt_caching_enabled=True)

        session_state.update(5, "session_state_uuid2")

        # Should raise exception when session state UUIDs don't match
        with pytest.raises(CompletionException) as exc_info:
            session_state.attempt_cancel("session_state_uuid", "test")

        assert exc_info.value.code() == grpc.StatusCode.CANCELLED

    def test_get_prompt_state_with_caching_enabled(self, auth_info):
        """Test get_prompt_state returns state when caching is enabled."""
        session_state = SessionState(auth_info, prompt_caching_enabled=True)

        session_state.update(5, "session_state_uuid")
        prompt_state = session_state.get_prompt_state("session_state_uuid")

        assert prompt_state is not None
        assert prompt_state == session_state._prompt_state

    def test_get_prompt_state_with_caching_disabled(self, auth_info):
        """Test get_prompt_state returns None when caching is disabled."""
        session_state = SessionState(auth_info, prompt_caching_enabled=False)

        session_state.update(5, "session_state_uuid")
        prompt_state = session_state.get_prompt_state("session_state_uuid")

        assert prompt_state is None

    def test_get_prompt_state_cancels_on_wrong_session_state_uuid(self, auth_info):
        """Test get_prompt_state cancels when session state UUID doesn't match."""
        session_state = SessionState(auth_info, prompt_caching_enabled=True)

        session_state.update(5, "session_state_uuid")

        with pytest.raises(CompletionException) as exc_info:
            session_state.get_prompt_state("session_state_uuid2")

        assert exc_info.value.code() == grpc.StatusCode.CANCELLED

    def test_try_replace_prompt_state_success(self, auth_info):
        """Test try_replace_prompt_state succeeds with correct sequence ID and session state UUID."""
        session_state = SessionState(auth_info, prompt_caching_enabled=True)

        session_state.update(5, "session_state_uuid")

        new_prompt_state = RequestPromptState(sequence_id=5)
        session_state.try_replace_prompt_state(new_prompt_state, "session_state_uuid")

        assert session_state._prompt_state == new_prompt_state

    def test_try_replace_prompt_state_cancels_on_wrong_session_state_uuid(
        self, auth_info
    ):
        """Test try_replace_prompt_state cancels with wrong session state UUID."""
        session_state = SessionState(auth_info, prompt_caching_enabled=True)

        session_state.update(5, "session_state_uuid2")

        new_prompt_state = RequestPromptState(sequence_id=5)

        with pytest.raises(CompletionException) as exc_info:
            session_state.try_replace_prompt_state(
                new_prompt_state, "session_state_uuid"
            )

        assert exc_info.value.code() == grpc.StatusCode.CANCELLED

    def test_reset_clears_prompt_state(self, auth_info):
        """Test reset creates new prompt state."""
        session_state = SessionState(auth_info, prompt_caching_enabled=True)

        # Set some state
        session_state.update(5, "session_state_uuid")
        original_prompt_state = session_state._prompt_state

        session_state.reset()

        assert session_state._prompt_state is not original_prompt_state
        assert session_state._prompt_state.sequence_id == 0


class TestSessionStateManager:
    """Unit tests for SessionStateManager implementations."""

    def test_session_state_lru_creates_new_session(self, auth_info, request_context):
        """Test SessionStateLRU creates new session state."""
        manager = SessionStateLRU(size=10, prompt_caching_enabled=True)

        result = manager.get_session_state(request_context, auth_info)

        assert result is not None
        assert isinstance(result, SessionState)
        assert result.common_auth == auth_info
        assert result._prompt_caching_enabled is True

    def test_session_state_lru_reuses_existing_session(
        self, auth_info, request_context
    ):
        """Test SessionStateLRU reuses existing session state."""
        manager = SessionStateLRU(size=10, prompt_caching_enabled=True)

        # First call creates new session
        result1 = manager.get_session_state(request_context, auth_info)

        # Second call with same context should return same session
        result2 = manager.get_session_state(request_context, auth_info)

        assert result1 is result2

    def test_session_state_lru_different_sessions_for_different_users(
        self, request_context
    ):
        """Test SessionStateLRU creates different sessions for different users."""
        manager = SessionStateLRU(size=10, prompt_caching_enabled=True)

        auth_info1 = AuthInfo(
            tenant_id="test_tenant_id",
            tenant_name="test_tenant",
            user_id=pydantic.SecretStr("user1"),
            user_email="<EMAIL>",
            shard_namespace="test_shard",
            cloud="test_cloud",
        )

        auth_info2 = AuthInfo(
            tenant_id="test_tenant_id",
            tenant_name="test_tenant",
            user_id=pydantic.SecretStr("user2"),
            user_email="<EMAIL>",
            shard_namespace="test_shard",
            cloud="test_cloud",
        )

        result1 = manager.get_session_state(request_context, auth_info1)
        result2 = manager.get_session_state(request_context, auth_info2)

        assert result1 is not None
        assert result2 is not None
        assert result1 is not result2
        assert result1.common_auth == auth_info1
        assert result2.common_auth == auth_info2

    def test_session_state_lru_different_sessions_for_different_session_ids(
        self, auth_info
    ):
        """Test SessionStateLRU creates different sessions for different session IDs."""
        manager = SessionStateLRU(size=10, prompt_caching_enabled=True)

        request_context1 = RequestContext(
            request_id="test_request_id1",
            request_session_id="session1",
            request_source="test",
        )

        request_context2 = RequestContext(
            request_id="test_request_id2",
            request_session_id="session2",
            request_source="test",
        )

        result1 = manager.get_session_state(request_context1, auth_info)
        result2 = manager.get_session_state(request_context2, auth_info)

        assert result1 is not result2

    def test_session_state_lru_prompt_caching_disabled(
        self, auth_info, request_context
    ):
        """Test SessionStateLRU with prompt caching disabled."""
        manager = SessionStateLRU(size=10, prompt_caching_enabled=False)

        result = manager.get_session_state(request_context, auth_info)

        assert result is not None
        assert result._prompt_caching_enabled is False
