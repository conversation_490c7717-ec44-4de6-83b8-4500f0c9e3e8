// K8S deployment file for a completion server
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local requestInsightPublisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';

local AVG_CHAR_PER_TOKEN = 3;
local PROMPT_FRACTION = 0.5;
local PREFIX_FRACTION = 0.75;

// function to create a single model completion host
//    env: environemnt
//    namespace: namespace to use
//    model config: model config (from //services/deploy/configs) object
//    retrievalConfigs: configuration of the retrievals
//    overrideConfig: object that overrides a completion host configuration
function(
  env,
  namespace,
  namespace_config,
  cloud,
  name,
  modelConfig,
  inferenceServices,
  retrievalCollectorConfig,
  retrievalConfigs,
  inferenceMtls,
  mtls,
  overrideConfig=null,
  skip_token_str=null,
)
  local appName = name;
  // The completion model names are quite long and tend to share prefixes, but
  // we want unique short names because IAM service accounts have a 30 character
  // limit. To get around this, use a hash to generate a short name.
  local shortAppName = 'complet-%s' % std.substr(std.md5('%s-completion' % name), 0, 7);
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);
  local getValue(obj, key, default) =
    if obj != null && std.objectHas(obj, key) then obj[key] else default;
  local requestInsightPublisher = requestInsightPublisherLib(cloud, env, namespace, appName=appName);
  local services = grpcLib.grpcService(name, namespace=namespace);
  local post_processing_config = getValue(modelConfig, 'post_processing', null);
  local configMap =
    local config = {
      port: 50051,
      model_name: name,
      inference_host_endpoints: std.mapWithKey(function(k, v) '%s:50051' % v, inferenceServices),
      content_manager_endpoint: 'content-manager-svc:50051',
      working_set_endpoint: if namespace_config.flags.workingSetEndpoint != '' then namespace_config.flags.workingSetEndpoint else null,
      feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
      dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
      retrieval_collector_config: retrievalCollectorConfig,
      retrieval: { retrieval_configs: retrievalConfigs },
      max_prefix_char_count: modelConfig.inference.max_context_length * AVG_CHAR_PER_TOKEN,
      max_suffix_char_count: modelConfig.inference.max_context_length * AVG_CHAR_PER_TOKEN,
      handler_type: 'SingleRoundCompletionHandler',
      handler_config: {
        sampling_defaults: {
          default_top_k: 0,
          default_top_p: 0.0,
          default_temperature: 0.0,
          default_max_token_count: 96,
        },
        apportionment_config: if std.objectHas(modelConfig.inference, 'apportionment_config') then modelConfig.inference.apportionment_config else {
          max_content_len: modelConfig.inference.max_context_length,
          input_fraction: if std.length(retrievalConfigs) > 0 then PROMPT_FRACTION else 1.0,
          prefix_fraction: PREFIX_FRACTION,
          max_path_tokens: 20,
          // TODO(arun): Move this out of the completion host configuration.
          // Currently only the Ender prompt formatter uses this configuration, so it
          // will work as normal for all other prompt formatters.
          per_retriever_max_tokens: {
            signature_retriever: modelConfig.inference.max_context_length,
          },
        },
        tokenizer_name: modelConfig.inference.tokenizer_name,
        prompt_formatter_name: modelConfig.inference.prompt_formatter_name,
        prompt_formatter_config: getValue(modelConfig.inference, 'prompt_formatter_config', null),
        extra_stop_tokens: getValue(modelConfig.inference, 'extra_stop_tokens', []),
        post_processing: {
          anonymize_todos: true,
          suppress_verbatim_copy_from_context: true,
          low_quality_filter_config: getValue(post_processing_config, 'low_quality_filter_config', null),
          // Completions with these substrings will be surpressed UNLESS said substrings also exist in the prefix or suffix
          // NOTE: this won't scale forever; meant as a quick and dirty way to filter out bad completions for launch
          // Eventually we'll want to explore other solutions -- e.g. a filter model, fine-tuning our model, 3rd party libraries
          deny_list_filter: getValue(post_processing_config, 'deny_list_filter', true),
          deny_list: getValue(
            post_processing_config,
            'deny_list',
            ['fuck', 'shit', 'asshole', 'bitch', 'cunt', 'dumbass', 'idiot', 'fag', 'pussy', 'nigg', 'wetback', 'retarded', 'dyke', 'damn']
          ),
        },
        skip_token_str: skip_token_str,
      },
      auth_config: {
        token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
      },
    } + if !mtls then {} else {
      client_mtls: {
        ca_path: '/client-certs/ca.crt',
        key_path: '/client-certs/tls.key',
        cert_path: '/client-certs/tls.crt',
      },
      server_mtls: {
        ca_path: '/certs/ca.crt',
        key_path: '/certs/tls.key',
        cert_path: '/certs/tls.crt',
      },
    } + if !inferenceMtls then {} else {
      central_client_mtls: {
        ca_path: '/inference-client-certs/ca.crt',
        key_path: '/inference-client-certs/tls.key',
        cert_path: '/inference-client-certs/tls.crt',
      },
    };
    local overriddenConfig = if overrideConfig == null then config else std.mergePatch(config, overrideConfig);
    configMapLib.createConfigMap(appName=appName, namespace=namespace, config=overriddenConfig);
  // create a client TLS certificate to securely access the content manager
  local clientCert = certLib.createClientCert(
    name='%s-client-certificate' % name,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
  );
  local inferenceClientCert = certLib.createCentralClientCert(
    name='%s-infer-client-cert' % name,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='inference-client-certs',
    dnsNames=grpcLib.grpcServiceNames(name, namespace=namespace),
  );
  // create a server certificate for MTLS
  local serverCert = certLib.createServerCert(
    name='%s-server-certificate' % name,
    namespace=namespace,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNames(name),
    volumeName='certs',
  );
  local serviceAccount = gcpLib.createServiceAccount(
    app=appName, env=env, cloud=cloud, namespace=namespace, iam=true, overridePrefix=shortAppName,
  );
  local serviceAccountObjects = serviceAccount.objects + [
    requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
  ];
  local hasDenseRetriever = std.any(std.map(function(r) r.retriever_name == 'DenseRetriever', retrievalConfigs));
  local retrievalObjects = std.map(function(r) r.getRetrievalObjects(name, namespace), retrievalConfigs);
  local container =
    {
      name: 'completion',
      target: {
        name: '//services/completion_host/single_model_server:image',
        dst: 'completion',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      env: telemetryLib.telemetryEnv('completion-host', telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      volumeMounts: [
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
        inferenceClientCert.volumeMountDef,
        requestInsightPublisher.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
      ] + [r.volumeMountDef for r in retrievalObjects if r.volumeMountDef != null],
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 2,
          // Increase from 2Gi to 4Gi to prevent OOM issues
          memory: '4Gi',
        },
      },
    };
  local pod =
    {
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      volumes: [
        configMap.podVolumeDef,
        clientCert.podVolumeDef,
        inferenceClientCert.podVolumeDef,
        serverCert.podVolumeDef,
        requestInsightPublisher.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
      ] + [r.podVolumeDef for r in retrievalObjects if r.podVolumeDef != null],
    };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local pbd = nodeLib.podDisruption(appName=appName, namespace=namespace, env=env);
  // the percentage of the cpu quota that corresponds to one cpu
  local singleCpuPct = 100 / container.resources.limits.cpu;
  local scaledObject = {
    apiVersion: 'keda.sh/v1alpha1',
    kind: 'ScaledObject',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      scaleTargetRef: {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        name: appName,
      },
      minReplicaCount: if env == 'DEV' then 1 else 4,
      maxReplicaCount: if env == 'DEV' then 1 else std.round(8 * namespace_config.flags.completionHostReplicaScale),
      advanced: {
        horizontalPodAutoscalerConfig: {
          behavior: {
            scaleDown: {
              stabilizationWindowSeconds: 300,
              policies: [
                {
                  // allow 50% scale down per 5 minute rolling window
                  type: 'Percent',
                  value: 50,
                  periodSeconds: 300,
                },
              ],
            },
            scaleUp: {
              stabilizationWindowSeconds: 60,
              policies: [
                {
                  type: 'Percent',
                  value: 50,
                  periodSeconds: 30,
                },
              ],
            },
          },
        },
      },
      triggers: [
        {
          type: 'cpu',
          metricType: 'Utilization',
          metadata: {
            // scale when we hit 40% of a cpu core
            value: std.toString(std.ceil(singleCpuPct * 0.4)),
          },
        },
      ],
    },
  };
  lib.flatten([
    clientCert.objects,
    inferenceClientCert.objects,
    serverCert.objects,
    configMap.objects,
    dynamicFeatureFlags.k8s_objects,
    if env == 'STAGING' || env == 'DEV' then scaledObject else [],
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: name,
        namespace: namespace,
        labels: {
          app: name,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        replicas: if env == 'DEV' then 1 else std.round(6 * namespace_config.flags.completionHostReplicaScale),
        minReadySeconds: if env == 'DEV' then 0 else 60,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: name,
          },
        },
        template: {
          metadata: {
            labels: {
              app: name,
            },
          },
          spec: pod + {
            affinity: affinity,
            tolerations: tolerations,
            priorityClassName: cloudInfo.envToPriorityClass(env),
          },
        },
      },
    },
    services,
    [r.objects for r in retrievalObjects],
    serviceAccountObjects,
    pbd,
  ])
