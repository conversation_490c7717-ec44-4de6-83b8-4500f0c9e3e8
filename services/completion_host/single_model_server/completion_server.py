"""Server of a completion host."""

import argparse
import concurrent.futures
import logging
import os
import pathlib
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from typing import Optional, Iterable, Any

import grpc
import opentelemetry
import opentelemetry.instrumentation.grpc
from opentelemetry.instrumentation.grpc import filters
from services.inference_host.client.multiplex import MultiplexInferenceStubFactory
from services.lib.retrieval import retrieval_collector
import structlog
from dataclasses_json import dataclass_json
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection
from prometheus_client import Counter, Histogram, start_http_server

import base.feature_flags
import base.tracing
import services.lib.grpc.tls_config.tls_config as tls_config
import services.lib.grpc.stream_mux.server as stream_mux_server
from base.languages.languages import default_languages
from base.logging.struct_logging import setup_struct_logging
from base.python.grpc import client_options
from base.python.opentelemetry_utils.traced_threadpool import TracedThread<PERSON>ool<PERSON>xecutor
from base.python.signal_handler.signal_handler import <PERSON>fulSignalHand<PERSON>
from services.completion_host import completion_pb2, completion_pb2_grpc
from google.rpc import status_pb2
from services.lib.retrieval import (
    retriever_factory,
)
from services.completion_host.single_model_server.handler import (
    CompletionException,
    CompletionHandler,
)
from services.completion_host.single_model_server.completion_request_insight_builder import (
    CompletionRequestInsightBuilder,
)
from services.lib.retrieval.retriever_request_insight_builder import (
    RetrieverRequestInsightBuilder,
)
from services.lib.retrieval.retrieval_collector import (
    RetrievalCollector,
)
from services.lib.retrieval.retriever import Retriever
from services.completion_host.single_model_server.single_round_handler import (
    create_completion_handler,
)
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.inference_host import infer_pb2_grpc
from services.inference_host.client import inference_host_client
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.grpc.auth.service_auth_interceptor import (
    ServiceAuthInterceptor,
    get_auth_info_from_grpc_context,
)
from services.lib.grpc.auth.service_token_auth import (
    GrpcPublicKeySource,
    ServiceTokenAuth,
)
from services.lib.grpc.metrics.metrics import MetricsServerInterceptor
from services.lib.grpc.stream_mux import stream_mux_pb2
from services.lib.request_context.request_context import RequestContext, clamp_timeout
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.token_exchange.client import client as token_exchange_client
from services.working_set.client.client import WorkingSetClient, get_working_set_client

log = structlog.get_logger()

_completions_counter = Counter(
    "au_completion_host_counter",
    "Counts completion hosts requests",
    ["model", "lang", "status_code", "request_source", "tenant_name"],
)

INF = float("inf")
prefix_char_buckets = [
    0,
    32,
    64,
    128,
    256,
    512,
    1024,
    2048,
    4096,
    8192,
    16 * 1024,
    32 * 1024,
    INF,
]
_completion_prefix_size_summary = Histogram(
    "au_completion_host_prefix_size",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=prefix_char_buckets,
    documentation="Size of the completion prefix in chars",
)
_completion_suffix_size_summary = Histogram(
    "au_completion_host_suffix_size",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=prefix_char_buckets,
    documentation="Size of the completion suffix in chars",
)

blob_name_buckets = [
    0,
    1,
    2,
    4,
    8,
    16,
    32,
    64,
    128,
    256,
    512,
    1024,
    2048,
    4096,
    8192,
    16384,
    32768,
]
_completion_unknown_blob_name_count_summary = Histogram(
    "au_completion_host_unknown_blob_name_count",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=blob_name_buckets,
    documentation="Number of unknown blob names in a completion request",
)
_find_missing_blob_name_count_summary = Histogram(
    "au_completion_host_find_missing_blob_name_count",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=blob_name_buckets,
    documentation="Number of blob names in a find-missing request",
)
_find_missing_missing_blob_name_count_summary = Histogram(
    "au_completion_host_find_missing_missing_blob_name_count",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=blob_name_buckets,
    documentation="Number of missing raw blob names in a find-missing request",
)
_find_missing_nonindexed_blob_name_count_summary = Histogram(
    "au_completion_host_find_missing_nonindexed_blob_name_count",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=blob_name_buckets,
    documentation="Number of nonindexed blob names in a find-missing request",
)
# buckets for the latency histogram, from 1ms to 10s
latency_buckets = tuple([(1.1**i - 1) / 100.0 for i in range(75)] + [INF])
_completion_latency = Histogram(
    "au_completion_host_latency_seconds",
    labelnames=["model", "status_code", "request_source", "tenant_name"],
    documentation="Latency of a completion request (in the completion host)",
    buckets=latency_buckets,
)
_find_missing_latency = Histogram(
    "au_completion_host_find_missing_latency_seconds",
    labelnames=["model", "status_code", "request_source", "tenant_name"],
    documentation="Latency of a find-missing request (in the completion host)",
    buckets=latency_buckets,
)

_completion_requests_cancelled = Counter(
    "au_completion_requests_cancelled",
    "Completion requests cancelled",
    ["model", "request_source", "tenant_name"],
)

_completion_streams_exhausted = Counter(
    "au_completion_rpc_streams_exhausted",
    "Completion streams exhausted",
    ["model"],
)


# feature flag to enable multiplexing over multiple inference hosts
#
# Example
# {"default": 0.5, "gsc": 0.5}
#
# This assumes that the inference hosts are named "default" and "gsc"
_INFERER_MULTIPLEX = base.feature_flags.StringFlag("completion_inferer_multiplex", "")


tracer = base.tracing.setup_opentelemetry()


@dataclass_json
@dataclass
class AuthConfig:
    """Configuration for the token authentication."""

    # the endpoint for the token exchange service
    token_exchange_endpoint: str


@dataclass_json
@dataclass
class Config:
    """Configuration for a completion server."""

    port: int

    # name of the model.
    #
    # this has to match the name of the model instance config
    model_name: str

    # endpoints for the inference hosts
    # key is the name of the inference host, value is the endpoint
    #
    # , e.g.
    # {
    #   "default": "infer-wizard-svc.central:50051",
    #   "gsc": "infer-wizard.central.t.us-central1-gsc.prod.augmentcode.com:50051"
    # }
    inference_host_endpoints: dict[str, str]
    content_manager_endpoint: str
    working_set_endpoint: str | None

    retrieval: retriever_factory.RetrievalConfig
    retrieval_collector_config: retrieval_collector.RetrievalCollectorConfig
    handler_type: str
    handler_config: dict

    max_prefix_char_count: int
    """The maximal number of characters in the prefix."""

    max_suffix_char_count: int
    """The maximal number of characters in the suffix."""

    auth_config: AuthConfig

    max_handler_workers: int = 8
    """Maximum number of workers for the handlers."""
    max_server_workers: int = 32
    """Maximum number of workers for the server."""
    max_rpc_streams: int = 64
    """Maximum number of streams to allow for streaming completion. Increases
    the number of server worker threads.
    Once at limit, additional requests will abort immediately.
    """

    feature_flags_sdk_key_path: Optional[str] = None
    dynamic_feature_flags_endpoint: Optional[str] = None

    client_mtls: Optional[tls_config.ClientConfig] = None
    central_client_mtls: Optional[tls_config.ClientConfig] = None
    server_mtls: Optional[tls_config.ServerConfig] = None

    shutdown_grace_period_s: float = 20.0


def get_content_manager_client(config: Config) -> ContentManagerClient:
    """Returns a content manager client."""
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in config.content_manager_endpoint
        )
    )
    return ContentManagerClient.create_for_endpoint(
        config.content_manager_endpoint,
        credentials=tls_config.get_client_tls_creds(config.client_mtls),
        options=options,
    )


class CompletionServices(completion_pb2_grpc.CompletionServicer):
    """Services to implement the completion service API."""

    def __init__(
        self,
        config: Config,
        rpc_pool: ThreadPoolExecutor,
        handler: CompletionHandler,
        token_auth: ServiceTokenAuth,
        working_set_client: WorkingSetClient,
    ):
        """
        rpc_pool: The CompleteSession streaming RPC uses a single RPC thread to
        receive and respond to many requests over the same stream. In order for
        those requests to be processed in parallel by the handler, they are
        submitted to the thread pool provided in argument rpc_pool.

        This pool may be the same pool on which the grpc server runs these handlers. Don't use this
        pool unless you have thought through how to prevent deadlock. CompleteSession may
        submit requests to the pool because the number of streams is limited by
        max_rpc_streams < pool size.
        """
        self.config = config
        self.rpc_pool = rpc_pool
        self.working_set_pool = TracedThreadPoolExecutor(
            thread_name_prefix="working-set-"
        )
        self.stream_sema = threading.BoundedSemaphore(config.max_rpc_streams)
        self.handler = handler
        self.token_auth = token_auth
        self.working_set_client = working_set_client

        # If this metric has never been incremented, then alerting policies
        # which refer to it will fail to deploy/not be configurable.
        # Incrementing by 0 does not remedy this. Increment it by a small amount
        # to not trigger the alert...
        _completion_streams_exhausted.labels(self.config.model_name).inc(0.001)

    def Complete(
        self,
        request: completion_pb2.CompletionRequest,
        context: grpc.ServicerContext,
    ) -> completion_pb2.CompletionResponse:
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        try:
            return self._Complete(request, request_context, auth_info)
        except grpc.RpcError as ex:
            # Error logging and metrics are handled in _Complete;
            # just worry about propagating the error code (e.g. CANCELLED)
            context.abort(
                code=ex.code(),  # pylint: disable=no-member # type: ignore
                details=ex.details(),  # pylint: disable=no-member # type: ignore
            )
            assert False, "not reachable"

    def CompleteSession(
        self,
        request_iterable: Iterable[completion_pb2.SessionRequest],
        context: grpc.ServicerContext,
    ) -> Iterable[completion_pb2.SessionResponse]:
        if not self.stream_sema.acquire(blocking=False):
            logging.error(
                f"CompleteSession: Cannot create new stream. At limit of {self.config.max_rpc_streams}"
            )
            _completion_streams_exhausted.labels(self.config.model_name).inc()
            context.abort(
                grpc.StatusCode.RESOURCE_EXHAUSTED,
                "CompleteSession rpc streams at limit",
            )
            return
        session = None
        try:
            session = RequestContext.from_grpc_context(context).request_session_id
            logging.info(f"CompleteSession: stream opened session={session}")
            yield from self._CompleteSession(request_iterable, context)
        finally:
            logging.info(f"CompleteSession: stream closed session={session}")
            self.stream_sema.release()

    def _CompleteSession(
        self,
        request_iterable: Iterable[completion_pb2.SessionRequest],
        context: grpc.ServicerContext,
    ) -> Iterable[completion_pb2.SessionResponse]:
        stream_context = RequestContext.from_grpc_context(context)

        @stream_mux_server.open_telemetry_span(tracer, "CompleteSession/single_request")
        def run_single_request(request: completion_pb2.SessionRequest):
            resp = completion_pb2.SessionResponse(
                context=stream_mux_pb2.MuxedResponse(
                    request_id=request.context.request_id,
                    routing_id=request.context.routing_id,
                )
            )
            code: grpc.StatusCode = grpc.StatusCode.UNKNOWN
            message: str = ""

            try:
                req_context = stream_mux_server.extract_context(
                    stream_context, request.context
                )
                auth_info = self.token_auth.validate_access(
                    [], req_context.auth_token, "/completion.Completion/CompleteSession"
                )
                resp.response.CopyFrom(
                    self._Complete(
                        request.request, req_context, auth_info, streaming=True
                    )
                )
                code = grpc.StatusCode.OK
            except grpc.RpcError as ex:
                # Error logging and metrics are handled in _Complete;
                # just worry about propagating the error status
                code = ex.code()  # pylint: disable=no-member # type: ignore
                message = ex.details() or ""  # pylint: disable=no-member # type: ignore
            except Exception:  # pylint: disable=broad-exception-caught
                code = grpc.StatusCode.UNKNOWN
            resp.context.status.CopyFrom(  # pylint: disable=no-member # type: ignore
                status_pb2.Status(code=code.value[0], message=message)  # pylint: disable=no-member # type: ignore
            )
            stream_mux_server.ensure_backwards_compatibility(resp)
            return resp

        request_iterator = iter(request_iterable)

        def receive_request():
            try:
                return next(request_iterator)
            except StopIteration:
                return None

        # Inference requests run on the shared pool executor (self.rpc_pool).
        # We need one additional thread receiving requests from the client. We
        # run it in a local executor so that it creates a "future", which we can
        # wait on alongside the inference requests from the current thread.
        with ThreadPoolExecutor(max_workers=1) as local_executor:
            # Dealing with the 3 types of futures is too messy; use Any
            # The actual type is Future[Req | Resp | None]
            futures: list[Any] = [local_executor.submit(receive_request)]
            while futures:
                done, not_done = concurrent.futures.wait(
                    futures, return_when=concurrent.futures.FIRST_COMPLETED
                )
                futures = list(not_done)
                for fut in done:
                    res = fut.result()
                    if isinstance(res, completion_pb2.SessionRequest):
                        futures.append(self.rpc_pool.submit(run_single_request, res))
                        # This result came from our receiver; restart it
                        futures.append(local_executor.submit(receive_request))
                    elif res is None:
                        # Received the last request from the client, continue to
                        # loop until all outstanding requests are done.
                        continue
                    else:
                        assert isinstance(res, completion_pb2.SessionResponse)
                        # Q: Does stream-stream grpc quickly re-enter this
                        # generator so that we can start next request at low
                        # latency? If not, we can use one more thread to ensure
                        # requests are always being received and started ASAP.
                        yield res

    def _Complete(
        self,
        request: completion_pb2.CompletionRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        streaming: bool = False,
    ):
        request_start_time = time.time()
        status_code = grpc.StatusCode.UNKNOWN
        try:
            request_context.bind_context_logging()
            auth_info.bind_context_logging()

            _completion_prefix_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.prefix))
            _completion_suffix_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.suffix))
            log.info("completion content: model_name=%s", request.model_name)

            if len(request.prefix) > self.config.max_prefix_char_count:
                log.warning(
                    "Client sent prefix longer than max_prefix_char_count=%d",
                    self.config.max_prefix_char_count,
                )
                raise CompletionException(
                    grpc.StatusCode.INVALID_ARGUMENT,
                    "Client sent prefix longer than max_prefix_char_count",
                )
            if len(request.suffix) > self.config.max_suffix_char_count:
                log.warning(
                    "Client sent suffix longer than max_suffix_char_count=%d",
                    self.config.max_suffix_char_count,
                )
                raise CompletionException(
                    grpc.StatusCode.INVALID_ARGUMENT,
                    "Client sent suffix longer than max_suffix_char_count",
                )

            # We create an exclusive thread pool per request to avoid deadlocking.
            with TracedThreadPoolExecutor(
                max_workers=self.config.max_handler_workers,
                thread_name_prefix=f"complete-{request_context.request_id[-8:]}-",
            ) as executor:
                # Register the working set in the background
                self.working_set_pool.submit(
                    self.working_set_client.register_working_set,
                    request.blobs,
                    request_context,
                )

                output = self.handler.complete(
                    request=request,
                    request_context=request_context,
                    auth_info=auth_info,
                    executor=executor,
                    streaming=streaming,
                )

            response = completion_pb2.CompletionResponse()
            response.text = output.text
            if output.filter_score is not None:
                response.filter_score = output.filter_score
            _completion_unknown_blob_name_count_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(output.unknown_blob_names))
            response.unknown_blob_names.extend(output.unknown_blob_names)
            status_code = grpc.StatusCode.OK
            response.skipped_suffix = output.skipped_suffix
            response.suffix_replacement_text = output.suffix_replacement_text
            response.checkpoint_not_found = output.checkpoint_not_found
            return response
        except grpc.RpcError as ex:
            status_code = ex.code()  # pylint: disable=no-member # type: ignore
            if status_code == grpc.StatusCode.CANCELLED:
                log.info("Completion cancelled")
                _completion_requests_cancelled.labels(
                    request.model_name,
                    request_context.request_source,
                    auth_info.metrics_tenant_name,
                ).inc()
            elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
                log.warning("Completion failed: %s", ex)
            else:
                log.error("Completion failed: %s", ex)
                log.exception(ex)
            raise
        except Exception as ex:  # pylint: disable=broad-exception-caught
            log.error("Completion failed: %s", ex)
            log.exception(ex)
            raise
        finally:
            latency = time.time() - request_start_time
            _completions_counter.labels(
                request.model_name,
                request.lang,
                str(status_code),
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).inc()
            _completion_latency.labels(
                request.model_name,
                str(status_code),
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(latency)

    def FindMissing(
        self,
        request: completion_pb2.FindMissingRequest,
        context: grpc.ServicerContext,
    ) -> completion_pb2.FindMissingResponse:
        request_start_time = time.time()
        status_code = grpc.StatusCode.UNKNOWN
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        try:
            request_context.bind_context_logging()
            auth_info.bind_context_logging()

            _find_missing_blob_name_count_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.blob_names))

            log.info(
                "find_missing content: model_name=%s, blob_count=%d",
                request.model_name,
                len(request.blob_names),
            )

            # We create an exclusive thread pool per request to avoid deadlocking.
            with TracedThreadPoolExecutor(
                max_workers=self.config.max_handler_workers,
                thread_name_prefix=f"find-missing-{request_context.request_id[-8:]}-",
            ) as executor:
                output = self.handler.find_missing(
                    request,
                    request_context,
                    executor,
                )

            response = completion_pb2.FindMissingResponse()
            response.missing_blob_names.extend(output.missing_blob_names)
            response.nonindexed_blob_names.extend(output.nonindexed_blob_names)

            _find_missing_missing_blob_name_count_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(output.missing_blob_names))
            _find_missing_nonindexed_blob_name_count_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(output.nonindexed_blob_names))
            status_code = grpc.StatusCode.OK
            return response
        except grpc.RpcError as ex:
            status_code = ex.code()  # pylint: disable=no-member # type: ignore
            log.error("FindMissing failed: %s", ex)
            log.exception(ex)
            context.abort(
                code=ex.code(),  # pylint: disable=no-member # type: ignore
                details=ex.details(),  # pylint: disable=no-member # type: ignore
            )
            raise
        except Exception as ex:  # pylint: disable=broad-exception-caught
            log.error("FindMissing failed: %s", ex)
            log.exception(ex)
            raise
        finally:
            latency = time.time() - request_start_time
            _find_missing_latency.labels(
                request.model_name,
                str(status_code),
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(latency)


def _get_inference_stub_factory(
    inferer_endpoints: dict[str, str],
    credentials: Optional[grpc.ChannelCredentials],
    model_name: str,
) -> inference_host_client.InferenceStubFactoryProtocol:
    """Returns a client to the inference host or inference hosts."""
    rpc_clients: dict[str, infer_pb2_grpc.InfererStub] = {}
    for name, endpoint in inferer_endpoints.items():
        options = client_options.get_grpc_client_options(
            client_options.GrpcClientOptions(load_balancing="headless" in endpoint)
        )

        rpc_client = inference_host_client.create_inference_stub(
            endpoint, credentials=credentials, options=options
        )

        rpc_clients[name] = rpc_client
    if "default" not in rpc_clients:
        raise ValueError("No default client")

    if len(rpc_clients) > 1:
        return MultiplexInferenceStubFactory(
            rpc_clients=rpc_clients,
            model_name=model_name,
            feature_flag=_INFERER_MULTIPLEX,
        )
    else:
        return lambda: list(rpc_clients.values())[0]


def _get_handler(
    config: Config,
    inference_stub_factory: inference_host_client.InferenceStubFactoryProtocol,
    model_name: str,
    content_manager_client: ContentManagerClient,
    retriever: Retriever,
    ri_builder: CompletionRequestInsightBuilder,
) -> CompletionHandler:
    if config.handler_type == "SingleRoundCompletionHandler":
        if "POD_NAMESPACE" not in os.environ:
            raise ValueError("POD_NAMESPACE environment variable must be set.")
        namespace = os.environ["POD_NAMESPACE"]
        return create_completion_handler(
            config=config.handler_config,
            inference_stub_factory=inference_stub_factory,
            content_manager_client=content_manager_client,
            ri_builder=ri_builder,
            model_name=model_name,
            retriever=retriever,
            namespace=namespace,
        )
    else:
        raise ValueError(f"Unsupported handler type: {config.handler_type=}")


def run(
    config: Config,
    namespace: str,
    ri_publisher: RequestInsightPublisher,
    shutdown_event: threading.Event,
):
    content_manager_client = get_content_manager_client(config)
    working_set_client = get_working_set_client(config)
    inference_stub_factory = _get_inference_stub_factory(
        config.inference_host_endpoints,
        credentials=tls_config.get_client_tls_creds(config.central_client_mtls),
        model_name=config.model_name,
    )
    retriever_ri_builder = RetrieverRequestInsightBuilder(ri_publisher)
    retriever = retriever_factory.create_retriever(
        config.retrieval,
        content_manager_client,
        tls_config.get_client_tls_creds(config.client_mtls),
        retriever_ri_builder,
        search_timeout_ms=1000,
    )

    if config.retrieval_collector_config.enabled:
        retrieval_collector = RetrievalCollector(
            retriever=retriever,
            config=config.retrieval_collector_config,
        )
        retriever = retrieval_collector

    completion_ri_builder = CompletionRequestInsightBuilder(ri_publisher)
    handler = _get_handler(
        config=config,
        inference_stub_factory=inference_stub_factory,
        model_name=config.model_name,
        content_manager_client=content_manager_client,
        retriever=retriever,
        ri_builder=completion_ri_builder,
    )

    # Load the default languages to ensure they are cached before we receive the first request.
    _ = default_languages()

    token_client = token_exchange_client.GrpcTokenExchangeClient.create(
        config.auth_config.token_exchange_endpoint,
        namespace,
        tls_config.get_client_tls_creds(config.central_client_mtls),
    )
    service_auth = ServiceTokenAuth(
        GrpcPublicKeySource(token_client),
        required_scopes=["CONTENT_R"],
    )
    auth_interceptor = ServiceAuthInterceptor(service_auth)

    thread_count = config.max_server_workers + config.max_rpc_streams
    rpc_pool = ThreadPoolExecutor(
        max_workers=thread_count, thread_name_prefix="server-"
    )
    server = grpc.server(
        rpc_pool,
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(
                filter_=filters.negate(filters.method_name("CompleteSession"))
            ),
            MetricsServerInterceptor(),
            auth_interceptor,
        ],
    )
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    completion_pb2_grpc.add_CompletionServicer_to_server(
        CompletionServices(config, rpc_pool, handler, service_auth, working_set_client),
        server,
    )
    service_names = (
        completion_pb2.DESCRIPTOR.services_by_name["Completion"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)

    server_credentials = tls_config.get_server_tls_creds(config.server_mtls)

    if server_credentials is not None:
        server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        server.add_insecure_port(f"[::]:{config.port}")
    server.start()
    logging.info("Listening on %s", config.port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # type: ignore # pylint: disable=no-member
        config_file.read_text()
    )


def main():
    """Run the server."""
    handler = GracefulSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    RequestInsightPublisher.add_publisher_arguments(parser)
    args = parser.parse_args()
    logging.info("Args %s", args)

    if "POD_NAMESPACE" not in os.environ:
        raise RuntimeError("POD_NAMESPACE environment variable not set")
    namespace = os.environ["POD_NAMESPACE"]

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    # begin listening for Prometheus requests
    start_http_server(9090)

    config = _load_config(args.config_file)
    logging.info("Config %s", config)

    ri_publisher = RequestInsightPublisher.create_from_args(args)

    path = None
    custom_endpoint = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)
    if config.dynamic_feature_flags_endpoint is not None:
        custom_endpoint = config.dynamic_feature_flags_endpoint

    context = base.feature_flags.Context.setup(path, custom_endpoint)
    base.feature_flags.set_global_context(context)

    run(config, namespace, ri_publisher, handler.get_shutdown_event())


if __name__ == "__main__":
    main()
