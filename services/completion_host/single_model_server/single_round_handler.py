"""Contains the core logic of handling completion requests."""

import concurrent.futures
from dataclasses import dataclass, field
from typing import (
    Any,
    Optional,
    Sequence,
    Union,
    cast,
)

import dataclasses_json
import grpc
import opentelemetry.trace
import structlog
from typing_extensions import override

import base.feature_flags
import base.tokenizers
from base import prompt_format_completion
from base.blob_names.python import blob_names
from base.diff_utils.edit_events import GranularEditEvent
from base.diff_utils.proto_wrapper import from_proto

from base.prompt_format_retrieve.prompt_formatter import CompletionRetrieverPromptInput
from services.completion_host import completion_pb2
from services.completion_host.single_model_server.handler import (
    <PERSON><PERSON><PERSON><PERSON>x<PERSON>,
    CompletionHandler,
    CompletionResult,
    FindMissingResult,
)
from services.completion_host.single_model_server.handler_metrics import (
    CompletionHandlerMetrics,
)
from services.completion_host.single_model_server.post_processing import (
    <PERSON><PERSON>ro<PERSON><PERSON>,
    PostprocessingConfig,
)
from services.completion_host.single_model_server.truncate_at_parenthesis import (
    truncate_at_parenthesis,
)
from services.completion_host.single_model_server.completion_request_insight_builder import (
    CompletionRequestInsightBuilder,
)
from services.completion_host.single_model_server.session_state import (
    RequestPromptState,
    SessionStateManager,
    SessionStateLRU,
)
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.inference_host.client import inference_host_client
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext, clamp_timeout
from services.lib.retrieval.retriever import (
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
import uuid

Tokens = list[int]

tracer = opentelemetry.trace.get_tracer(__name__)

log = structlog.get_logger()

# Type-alias to keep things manageable.
_InferReply = inference_host_client.InferenceClientProtocol.Reply

_SEND_400_ON_BAD_CURSOR_POSITION = base.feature_flags.BoolFlag(
    "send_400_on_bad_cursor_position", False
)

_ENABLE_COMPLETION_EMBEDDINGS_SEARCH_CANCEL = base.feature_flags.BoolFlag(
    "enable_completion_embeddings_search_cancel", False
)

_ENABLE_COMPLETION_SESSION_STATE = base.feature_flags.BoolFlag(
    "enable_completion_session_state", False
)


@dataclass(frozen=True)
class SamplingDefaults(dataclasses_json.DataClassJsonMixin):
    """Class for sampling information if no sampling is configured by a request."""

    default_top_k: int = 0

    default_top_p: float = 0.0

    default_temperature: float = 0.0

    default_max_token_count: int = 280

    def __post_init__(self):
        assert self.default_top_k >= 0, "top_k must be non-negative."
        assert (
            0.0 <= self.default_top_p <= 1.0
        ), f"top_p must be between [0,1] is {self.default_top_p}."
        assert self.default_temperature >= 0.0, "temperature must be non-negative."


class SingleRoundCompletionHandler(CompletionHandler):
    """Handles requests with single round of retrieval + generation."""

    @dataclass(frozen=True)
    class Config(dataclasses_json.DataClassJsonMixin):
        """Configuration for a RAG completion handler."""

        tokenizer_name: str
        """Name of the tokenizer to use.

        Must match the tokenizer used to train the model."""

        prompt_formatter_name: str
        """Name of the prompt formatter to use.

        Must match the prompt formatter used to train the model."""

        apportionment_config: (
            prompt_format_completion.TokenApportionmentConfig | None
        ) = None
        """Configuration for token budgeting between prompt and retrieval."""

        # TODO(arun): Make required field and expand use to all formatters (AU-1052).
        prompt_formatter_config: dict[str, Any] | None = None
        """Configuration for the prompt formatter.

        Must match the prompt formatter used to train the model."""

        # extra tokens at which to stop generating
        # the tokens are expressed as text, but send to the inferer as tokens
        extra_stop_tokens: Optional[list[str]] = None

        # token that will trigger suffix to be skipped
        skip_token_str: Optional[str] = None

        # defaults when a request doesn't have sampling set
        sampling_defaults: SamplingDefaults = SamplingDefaults()

        post_processing: PostprocessingConfig = PostprocessingConfig()
        """Configuration for any postprocessing to be done on the output."""

    def __init__(
        self,
        stub_factory: inference_host_client.InferenceStubFactoryProtocol,
        model_name: str,
        tokenizer: base.tokenizers.Tokenizer,
        prompt_formatter: prompt_format_completion.CompletionPromptFormatter,
        retriever: Retriever[CompletionRetrieverPromptInput],
        content_manager_client: ContentManagerClient,
        ri_builder: CompletionRequestInsightBuilder,
        sampling_default: SamplingDefaults,
        extra_stop_tokens_str: list[str],
        post_processing_config: PostprocessingConfig,
        namespace: str,
        skip_token_str: Optional[str] = None,
        post_processor: Optional[PostProcesser] = None,
    ):
        """Initialize the handler."""
        # Inference client is selected based upon whether completion
        # request arrives via a streaming RPC.
        self.client = inference_host_client.InfererClient(stub_factory)
        self.stream_client = inference_host_client.InfererClient(
            stub_factory,
            sticky_session_seconds=15.0,
            sticky_session_count=64,
        )
        self.session_state_manager: SessionStateManager = SessionStateLRU(
            192, prompt_formatter.supports_caching()
        )

        self.model_name = model_name
        self.sampling_default = sampling_default
        self.post_processing_config = post_processing_config

        self.content_manager_client = content_manager_client
        self.retriever = retriever
        self.tokenizer = tokenizer
        self.prompt_formatter = prompt_formatter

        if skip_token_str:
            skip_token = skip_token_str.encode()
            self.skip_token = tokenizer.vocab.get(skip_token)
            if self.skip_token is None:
                raise KeyError(
                    f"Skip token {skip_token=} does not exist in vocab. {tokenizer=}"
                )
        else:
            self.skip_token = None

        self.namespace = namespace
        self.end_token_ids = {tokenizer.special_tokens.eos} | {
            token_id
            for token in extra_stop_tokens_str
            if (token_id := tokenizer.vocab.get(token.encode())) is not None
            and token != skip_token_str
        }
        self.metrics = CompletionHandlerMetrics()

        self.ri_builder = ri_builder
        self.post_processor = post_processor

    def _apply_request_defaults(self, request: completion_pb2.CompletionRequest):
        """Sets the default sampling information based on the completion protocol definition."""
        if request.max_tokens == 0:
            request.max_tokens = self.sampling_default.default_max_token_count

    @override
    def complete(
        self,
        request: completion_pb2.CompletionRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
        streaming: bool = False,
    ) -> CompletionResult:
        self._apply_request_defaults(request)

        # Validate the position field.
        if request.position.prefix_begin > request.position.suffix_end:
            raise CompletionException(
                grpc.StatusCode.INVALID_ARGUMENT,
                "Invalid position: prefix_begin must be <= suffix_end",
            )

        completion = Completion(
            self,
            request=request,
            request_context=request_context,
            auth_info=auth_info,
            executor=executor,
            streaming=streaming,
        )
        return completion.complete()

    @override
    def find_missing(
        self,
        request: completion_pb2.FindMissingRequest,
        request_context: RequestContext,
        executor: concurrent.futures.Executor,
    ) -> FindMissingResult:
        # Obtain a list of blobs that are not indexed for one or more retrievers.
        result = self.retriever.find_missing(
            request.blob_names, request_context, executor
        )
        all_missing_blobs = list(result.missing_blob_names)
        if not all_missing_blobs:
            return FindMissingResult(missing_blob_names=[], nonindexed_blob_names=[])

        # The indexed content might be missing because the raw content is missing. Consult the
        # content manager to get the list of blobs for which this is the case.
        missing_raw_blobs = self.content_manager_client.find_missing(
            blob_names=all_missing_blobs,
            request_context=request_context,
        )

        # Partition the contents of all_missing_blobs into unknown and nonindexed.
        nonindexed_blobs = list(set(all_missing_blobs) - set(missing_raw_blobs))
        return FindMissingResult(
            missing_blob_names=missing_raw_blobs, nonindexed_blob_names=nonindexed_blobs
        )


def create_completion_handler(
    config: Union[SingleRoundCompletionHandler.Config, dict],
    inference_stub_factory: inference_host_client.InferenceStubFactoryProtocol,
    content_manager_client: ContentManagerClient,
    ri_builder: CompletionRequestInsightBuilder,
    model_name: str,
    retriever: Retriever[CompletionRetrieverPromptInput],
    namespace: str,
) -> SingleRoundCompletionHandler:
    """Create a completion handler.

    Args:
        config: The configuration for the handler.
        inference_stub: The client to use for inference.
        ri_builder: The request insight builder to use.
        model_name: The name of the model. This has to match the name of the model instance config
        retriever: The retriever to use.

    Returns:
        A completion handler.
    """
    if isinstance(config, dict):
        config = cast(
            SingleRoundCompletionHandler.Config,
            # pylint:disable-next=no-member
            SingleRoundCompletionHandler.Config.schema().load(config),  # type: ignore
        )

    tokenizer = base.tokenizers.create_tokenizer_by_name(config.tokenizer_name)
    prompt_formatter = prompt_format_completion.get_completion_prompt_formatter_by_name(
        config.prompt_formatter_name,
        apportionment_config=config.apportionment_config,
        tokenizer=tokenizer,
        prompt_formatter_config=config.prompt_formatter_config,
    )

    post_processor = PostProcesser(config.post_processing, tokenizer, ri_builder)

    return SingleRoundCompletionHandler(
        stub_factory=inference_stub_factory,
        model_name=model_name,
        tokenizer=tokenizer,
        prompt_formatter=prompt_formatter,
        retriever=retriever,
        content_manager_client=content_manager_client,
        ri_builder=ri_builder,
        sampling_default=config.sampling_defaults,
        extra_stop_tokens_str=config.extra_stop_tokens or [],
        post_processing_config=config.post_processing,
        namespace=namespace,
        skip_token_str=config.skip_token_str,
        post_processor=post_processor,
    )


class Completion:
    """Class containing the state and methods w.r.t. to a single completion."""

    def __init__(
        self,
        parent: SingleRoundCompletionHandler,
        request: completion_pb2.CompletionRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
        streaming: bool,
    ):
        self.parent = parent
        self.request = request
        self.request_context = request_context
        self.auth_info = auth_info
        self.executor = executor
        self.context = base.feature_flags.get_global_context().bind_attribute(
            "tenant_name",
            self.auth_info.tenant_name,
        )

        if self.auth_info.opaque_user_id:
            self.context = self.context.bind_attribute(
                "user_uuid", self.auth_info.opaque_user_id
            )

        # UUID used to identify this specific request within completion_host for session state management.
        # This is necessary because some clients do not pass an increasing sequence_id,
        # and there can be retries with the same request_id, so we need a unique identifier
        # in completion_host to distinguish between different requests for cancellation purposes.
        self.session_state_uuid = str(uuid.uuid4())

        self.client = self.parent.client
        self.session_state = None
        if streaming:
            # If we expect future requests from the same session, use a streaming
            # client and session state for prompt caching.
            self.client = self.parent.stream_client
            if _ENABLE_COMPLETION_SESSION_STATE.get(self.context):
                self.session_state = (
                    self.parent.session_state_manager.get_session_state(
                        request_context, auth_info
                    )
                )

    def _retrieve(self) -> RetrievalResult:
        if not self.request.HasField("blobs"):
            log.error(
                "Received request %s without blobs", self.request_context.request_id
            )
            raise ValueError("Received request without blobs")
        blobs = blob_names.Blobs.from_proto(self.request.blobs)

        sequence_id_for_retrieval = (
            self.request.sequence_id
            if _ENABLE_COMPLETION_EMBEDDINGS_SEARCH_CANCEL.get(self.context)
            else None
        )

        retrieval_input = RetrievalInput(
            CompletionRetrieverPromptInput(
                prefix=self.request.prefix,
                suffix=self.request.suffix,
                path=self.request.path,
            ),
            blobs=[blobs],
            recency_info=self.request.recency_info,
            edit_events=from_proto(self.request.edit_events, list[GranularEditEvent]),
            namespace=self.parent.namespace,
            sequence_id=sequence_id_for_retrieval,
            model_name=self.parent.model_name,
        )

        if retrieval_input.workspace_empty():
            log.info(
                "Skipping retrieval as no blob names were provided.",
            )
            return RetrievalResult(
                retrieved_chunks=(), missing_blob_names=(), checkpoint_not_found=False
            )

        with (
            self.parent.metrics.retrieval_latency.labels(
                self.parent.model_name,
                self.request_context.request_source,
                self.auth_info.metrics_tenant_name,
            ).time(),
            tracer.start_as_current_span("retrieval"),
        ):
            retrieval_result = self.parent.retriever.retrieve(
                input_=retrieval_input,
                request_context=self.request_context,
                auth_info=self.auth_info,
                executor=self.executor,
            )
            return retrieval_result

    @staticmethod
    def _skip_and_detokenize(
        token_ids: Sequence[int],
        tokenizer: base.tokenizers.Tokenizer,
        skip_token: Optional[int],
        suffix: str,
        closing_strings: Sequence[str] = (
            "'''",
            '"""',
            "'",
            '"',
            "`",
            "]",
            "}",
            ")",
            ">",
        ),
    ) -> CompletionResult:
        """Replace skip token with suffix characters.

        Returns:
            text:  Inserted completion text before first skip token
            skipped_suffix:  The characters of the suffix that is replaced
            suffix_replacement_text:  The string to replace the above string with
        """
        if skip_token is None or not token_ids:
            return CompletionResult(
                text=tokenizer.detokenize(token_ids),
                skipped_suffix="",
                suffix_replacement_text="",
                unknown_blob_names=[],
            )
        # All skipped suffix characters
        skipped_text = []
        # all characters including detokenized sequence and inserted suffix
        text = []
        # Position to start searching for next skip token
        start = 0
        # Total number of skipped characters
        n_skipped = 0
        # Keep searching for skip token
        while start < len(token_ids):
            try:
                next_skip = token_ids.index(skip_token, start)
            # No more skip tokens
            except ValueError:
                text.append(tokenizer.detokenize(token_ids[start:]))
                break

            text.append(tokenizer.detokenize(token_ids[start:next_skip]))
            # Identify the portion of string skipped
            for i in range(n_skipped, len(suffix)):
                if not suffix[i].isspace():
                    # index of first char after whitespaces
                    ws_tail = i
                    break
            else:
                # there is only white space.  Cannot match skip to suffix.
                break
            for closing in closing_strings:
                if suffix[ws_tail : ws_tail + len(closing)] == closing:
                    skipped_chars = suffix[n_skipped : ws_tail + len(closing)]
                    text.append(skipped_chars)
                    skipped_text.append(skipped_chars)
                    n_skipped += len(skipped_chars)
                    break
            else:  # be conservative and stop the generation here
                log.warning(
                    "Cannot associate characters in the suffix to a skip token. "
                )
                break
            start = next_skip + 1
        replacement_text = "".join(text[1:])
        skipped_suffix = "".join(skipped_text)

        return CompletionResult(
            text=text[0],
            skipped_suffix=skipped_suffix,
            suffix_replacement_text=replacement_text,
            unknown_blob_names=[],
        )

    def _format_prompt(
        self,
        prompt_input: prompt_format_completion.PromptInput,
    ) -> Tokens:
        """Build tokenized prompt given request and retrieval. If configured, cached state from past
        prompts may be used and subsequently updated for future prompts.

        Returns:
            prompt as list of tokens
        Raises:
            CompletionException(CANCELLED), if while synchronizing with prompt state
            it is discovered that this request is superseded by another in the
            session with higher sequence id.
        """
        with (
            self.parent.metrics.tokenization_latency.labels(
                self.parent.model_name,
                self.request_context.request_source,
                self.auth_info.metrics_tenant_name,
            ).time(),
            tracer.start_as_current_span("format_prompt"),
        ):
            prompt_state = None
            if self.session_state is not None:
                prompt_state = self.session_state.get_prompt_state(
                    self.session_state_uuid
                )

            # If we do not have a prompt state (session state or prompt caching is disabled), we format the prompt from scratch
            if prompt_state is None:
                return self.parent.prompt_formatter.format_prompt(
                    prompt_input=prompt_input,
                    max_output_token_count=self.request.max_tokens,
                ).tokens()

            # Prompt caching is enabled, so we use the prompt state and update it
            request_blobs = blob_names.Blobs.from_proto(self.request.blobs)
            blob_changes = blob_names.compute_diff(prompt_state.blobs, request_blobs)
            blobs_no_longer_in_request: Sequence[bytes]
            if isinstance(blob_changes, blob_names.BlobsBaselineDiff):
                # Blob baseline changes are rare, and not worth computing
                # Discard prompt state
                prompt_state = RequestPromptState()
                blobs_no_longer_in_request = []
            else:
                blobs_no_longer_in_request = blob_changes.unique_to_lhs

            output, out_cache = self.parent.prompt_formatter.format_prompt_with_cache(
                prompt_input=prompt_input,
                max_output_token_count=self.request.max_tokens,
                prompt_cache=prompt_state.prompt_cache,
                invalid_blobs=blobs_no_longer_in_request,
            )

            new_prompt_state = RequestPromptState(
                sequence_id=self.request.sequence_id,
                blobs=request_blobs,
                prompt_cache=out_cache,
            )

            if self.session_state is not None:
                self.session_state.try_replace_prompt_state(
                    new_prompt_state, self.session_state_uuid
                )
            return output.tokens()

    def _infer(
        self, input_tokens: Tokens, max_tokens: int, seed: int
    ) -> inference_host_client.InferenceClientProtocol.Reply:
        timeout_s = clamp_timeout(self.request_context.time_remaining_or_raise(), 30)
        with self.parent.metrics.forward_latency.labels(
            self.parent.model_name,
            self.request_context.request_source,
            self.auth_info.metrics_tenant_name,
        ).time():
            return self.client.infer(
                input_tokens=input_tokens,
                max_output_length=max_tokens,
                end_token_ids=self.parent.end_token_ids,
                top_k=self.parent.sampling_default.default_top_k,
                top_p=self.parent.sampling_default.default_top_p,
                temperature=self.parent.sampling_default.default_temperature,
                random_seed=seed,
                request_context=self.request_context,
                timeout_s=timeout_s,
                sequence_id=self.request.sequence_id,
                probe_only=self.request.probe_only,
            )

    def complete(self) -> CompletionResult:
        """Performs a completion request based on the given configurations.

        Throws a ModelhostException with a status code in exceptional situations.
        """
        self.parent.metrics.completion_requests_received.labels(
            self.parent.model_name,
            self.request_context.request_source,
            self.auth_info.metrics_tenant_name,
        ).inc()

        log.info(
            "Generating",
            prefix_len=len(self.request.prefix),
            suffix_len=len(self.request.suffix),
        )

        if self.session_state is not None:
            self.session_state.update(self.request.sequence_id, self.session_state_uuid)

        # If the user doesn't provide a seed in the request, we use the proto default
        # value of 0.
        seed = cast(int, self.request.seed.value)

        # 1. Retrieve chunks.
        retrieval_result = self._retrieve()

        # 2. Generate the prompt input tokens.
        # Note that we may cancel the request during prompt formatting,
        # but we arrange to submit the request insight call in any case.
        input = prompt_format_completion.PromptInput(
            prefix=self.request.prefix,
            suffix=self.request.suffix,
            prefix_begin=self.request.position.prefix_begin,
            path=self.request.path or "",
            lang=self.request.lang,
            retrieved_chunks=map(
                RetrievalChunk.to_prompt_chunk,
                retrieval_result.get_retrieved_chunks(),
            ),
        )
        input_tokens: Tokens = []
        try:
            input_tokens = self._format_prompt(input)

            # 3. Generate the prompt output tokens.
            # We'll submit the inference call and the request insight call in parallel
            # to hide some latency.
            output_future = self.executor.submit(
                self._infer, input_tokens, self.request.max_tokens, seed
            )
        finally:
            # We rely on the thread pool to wait for this task to complete
            # before we return the result for this completion request.
            self.executor.submit(
                self.parent.ri_builder.record_request,
                input_tokens=input_tokens,
                seed=seed,
                eos_token_ids=self.parent.end_token_ids,
                tokenizer=self.parent.tokenizer,
                request=self.request,
                request_context=self.request_context,
                auth_info=self.auth_info,
                top_k=self.parent.sampling_default.default_top_k,
                top_p=self.parent.sampling_default.default_top_p,
                temperature=self.parent.sampling_default.default_temperature,
            )

        # In a typical infer call, there is no distinction between the "full" output
        output = full_output = output_future.result()

        # perform some basic post processing on the results.
        trim_result = _trim_output_tokens(
            eos_to_check=self.parent.end_token_ids,
            tokens=output.output_tokens,
            log_probs=output.log_probs,
        )
        truncated_output_tokens, truncated, truncated_log_probs = (
            trim_result.truncated_tokens,
            trim_result.is_truncated,
            trim_result.truncated_log_probs,
        )

        result: CompletionResult = self._skip_and_detokenize(
            truncated_output_tokens,
            self.parent.tokenizer,
            self.parent.skip_token,
            self.request.suffix,
        )

        if not truncated and "\n" in result.text and not result.skipped_suffix:
            result.text = result.text[: result.text.rindex("\n")]

        # Maybe applies post processing.
        user_filter_threshold = None
        if self.request.HasField("filter_threshold"):
            user_filter_threshold = self.request.filter_threshold
            if user_filter_threshold < 0.0 or user_filter_threshold > 1.0:
                log.error(
                    "Invalid filter threshold range: user_filter_threshold=%f. Must be in [0, 1].",
                    user_filter_threshold,
                )
                user_filter_threshold = None

        with tracer.start_as_current_span("post_process"):
            if self.parent.post_processor is not None:
                original_text = result.text
                post_processed_result = self.parent.post_processor.post_process(
                    request_context=self.request_context,
                    auth_info=self.auth_info,
                    completion=result.text,
                    prefix=self.request.prefix,
                    suffix=self.request.suffix,
                    path=self.request.path,
                    user_filter_threshold=user_filter_threshold,
                    executor=self.executor,
                    token_ids=truncated_output_tokens,
                    log_probs=truncated_log_probs,
                    model_name=self.parent.model_name,
                    extra_truncation_strings=self.request.truncation,
                    context=self.context,
                )
                result.text = post_processed_result.completion
                result.filter_score = post_processed_result.filter_score
                # If post processing cleared the completion, clear the skipped suffix.
                if original_text and not result.text:
                    result.skipped_suffix = ""
                    result.suffix_replacement_text = ""

        # Apply truncation at parenthesis if enabled (after post-processing)
        # This is separate from post-processing to keep the concerns separate
        truncation_result = None
        if result.text:  # Only apply if we have text after post-processing
            original_text = result.text
            truncation_result = truncate_at_parenthesis(
                result.text,
                self.request.path,
                self.request_context.request_session_id,
                self.context,
            )
            result.text = truncation_result.text

            self.executor.submit(
                self.parent.ri_builder.record_parenthesis_truncation,
                original_text=original_text,
                truncated_text=result.text,
                was_truncated=truncation_result.was_truncated,
                could_have_truncated=truncation_result.could_have_truncated,
                path=self.request.path,
                request_context=self.request_context,
                auth_info=self.auth_info,
            )

        # If retrieval could not find the blobs checkpoint, notify the caller.
        result.checkpoint_not_found = retrieval_result.get_checkpoint_not_found()

        # TODO(rich): must add the unknown_checkpoint_id to request insight
        # TODO(vzhao): add low quality filter result to request insight.
        self.executor.submit(
            self.parent.ri_builder.record_response,
            # Response shown to client.
            output_text=result.text,
            skipped_suffix=result.skipped_suffix,
            suffix_replacement_text=result.suffix_replacement_text,
            unknown_blob_names=retrieval_result.get_missing_blob_names(),
            # Full log probs before truncations.
            output_tokens=full_output.output_tokens,
            log_probs=full_output.log_probs,
            # output tokens after post processing and truncation
            truncated_output_tokens=truncated_output_tokens,
            truncated_log_probs=truncated_log_probs,
            # Request information.
            tokenizer=self.parent.tokenizer,
            request_context=self.request_context,
            auth_info=self.auth_info,
        )

        result.unknown_blob_names = list(retrieval_result.get_missing_blob_names())
        return result


@dataclass(frozen=True)
class TrimmedOutputTokens:
    """Result of _trim_output_tokens."""

    truncated_tokens: Sequence[int]
    is_truncated: bool
    truncated_log_probs: Optional[Sequence[float]]


# TODO(arun): We should probably move output post-processing like this to a common
# library -- probably base.prompt_format_completion.
def _trim_output_tokens(
    eos_to_check: set[int], tokens: Sequence[int], log_probs: Optional[Sequence[float]]
) -> TrimmedOutputTokens:
    """Trims tokens and log_probs."""

    is_truncated = False
    for i, token in enumerate(tokens):
        if token in eos_to_check:
            tokens = tokens[:i]
            is_truncated = True
            if log_probs is not None:
                log_probs = log_probs[:i]
            break

    return TrimmedOutputTokens(
        truncated_tokens=tokens,
        is_truncated=is_truncated,
        truncated_log_probs=log_probs,
    )
