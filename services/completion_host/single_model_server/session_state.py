"""Session state management for completion requests."""

import copy
import threading
import time
import structlog
from dataclasses import dataclass, field
from typing import MutableMapping, Protocol, Tuple

import grpc
import lru
import pydantic

from base.blob_names.python import blob_names
from base.prompt_format_completion.prompt_formatter import PromptCache
from services.completion_host.single_model_server.handler import CompletionException
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext

log = structlog.get_logger()


# We give out references to this from under a lock. If removing "frozen=True", consider
# copy.
@dataclass(frozen=True)
class RequestPromptState:
    """Capture of the prompt format state after a specific request"""

    sequence_id: int = 0
    """Sequence ID of the request in the session"""

    blobs: blob_names.Blobs = field(default_factory=blob_names.Blobs)
    """The blob names for the given request. The prompt formatter needs to be told
    which blobs from this set are invalid when the prompt cache is used for a later
    request."""

    prompt_cache: PromptCache = PromptCache()
    """Output of prompt formatter for the given request. A result of the request
    itself, retrievals for the request, and any prior prompt format cache which
    that request utilized."""


class SessionState:
    """Cache prompt state across multiple requests of a session. Shared by many
    requests so must be thread-safe."""

    SEQUENCE_ROLLBACK_TIME = 2
    """SequenceID of requests within a session can roll back on client restart.
       To avoid denial-of-service due to a high sequence ID in the state, we expect
       and allow such a rollback if the state has not been successfully used for
       any request within the rollback period.
       Effect: Upon a client restart, if user is routed to the same completion host
       and their SessionState is still cached, completion requests may all be cancelled
       until this amount of time in seconds has passed.
    """

    def __init__(self, auth_info: AuthInfo, prompt_caching_enabled: bool):
        # Requests sharing same session state should come from the same user
        # and have auth for the same tenant, as we're sharing completion request
        # details and retrieved chunks between them.
        self.common_auth = copy.deepcopy(auth_info)

        # TODO(brandon): test with/without _pending_sequence_id
        # It is a little complex, so if allowing the prompt to be changed by
        # any/all requests in any order doesn't negate the benefits, should
        # remove.  Intended benefits:
        # 1. Limit wasted effort formatting prompts / making requests to
        # inference for results which the client no longer cares about.
        # 2. Limit churn / branching state changes in the prompt cache which
        # could negate the benefits of caching by not providing inference with a
        # consistent prompt prefix over time.
        self._lock = threading.RLock()
        self._last_update_time = 0
        self._pending_sequence_id = 0
        # UUID used to identify requests within completion_host for session state management.
        # This is necessary because some clients do not pass an increasing sequence_id,
        # and there can be retries with the same request_id, so we need a unique identifier
        # in completion_host to distinguish between different requests for cancellation purposes.
        self._pending_session_state_uuid = ""
        self._prompt_state = RequestPromptState()
        self._prompt_caching_enabled = prompt_caching_enabled

    def valid_for_request(self, auth_info: AuthInfo) -> bool:
        """Returns true if the session state may be used for the given request."""
        return auth_info == self.common_auth

    def reset(self) -> None:
        """Resets the prompt state. Called when the sequence ID rolls back.

        Note that the sequence ID of the prompt state is the last request which set it,
        so upon a rollback we intend to reset it back to the default (0).
        """
        with self._lock:
            self._prompt_state = RequestPromptState()

    def attempt_cancel(
        self, calling_session_state_uuid: str, cancel_source: str
    ) -> None:
        """Attempts to cancel the request with the given session state UUID.
        Raises CompletionException(CANCELLED) if the request is not the pending request.

        Args:
            calling_session_state_uuid: UUID of the request attempting to proceed
            cancel_source: Source of the cancellation attempt (e.g., "update", "get_prompt_state")
        """
        with self._lock:
            if calling_session_state_uuid != self._pending_session_state_uuid:
                raise CompletionException(
                    grpc.StatusCode.CANCELLED,
                    f"Cancelled by completion host cancel source: {cancel_source}. Cancelled request with session_state_uuid={calling_session_state_uuid} in favor of new request with session_state_uuid={self._pending_session_state_uuid}.",
                )

    def update(self, calling_sequence_id: int, calling_session_state_uuid: str) -> None:
        """Updates the session state.
        - If the request is the highest sequence ID seen, the state is updated.
        - If the request has the same sequence ID, since it called update later,
          it should still be allowed to proceed and update the state.
        - If the request is not the next in the sequence, and the state has not been
          used for a while, the state is reset.
        - Otherwise, the request is cancelled.
        """
        now = time.time()
        with self._lock:
            if calling_sequence_id >= self._pending_sequence_id:
                log.info(
                    "Session state updated: higher sequence ID",
                    calling_sequence_id=calling_sequence_id,
                    pending_sequence_id=self._pending_sequence_id,
                    calling_session_state_uuid=calling_session_state_uuid,
                    pending_session_state_uuid=self._pending_session_state_uuid,
                )
                self._pending_sequence_id = calling_sequence_id
                self._pending_session_state_uuid = calling_session_state_uuid
                self._last_update_time = now
            elif now > self._last_update_time + SessionState.SEQUENCE_ROLLBACK_TIME:
                log.info(
                    "Session state updated: reset due to sequence rollback",
                    calling_sequence_id=calling_sequence_id,
                    pending_sequence_id=self._pending_sequence_id,
                    calling_session_state_uuid=calling_session_state_uuid,
                    pending_session_state_uuid=self._pending_session_state_uuid,
                )
                self._pending_sequence_id = calling_sequence_id
                self._pending_session_state_uuid = calling_session_state_uuid
                self._last_update_time = now
                self.reset()
            else:
                self.attempt_cancel(calling_session_state_uuid, "update")

    def get_prompt_state(
        self, calling_session_state_uuid: str
    ) -> RequestPromptState | None:
        with self._lock:
            self.attempt_cancel(calling_session_state_uuid, "get_prompt_state")
            if self._prompt_caching_enabled:
                return self._prompt_state
            return None

    def try_replace_prompt_state(
        self, prompt_state: RequestPromptState, calling_session_state_uuid: str
    ) -> None:
        with self._lock:
            self.attempt_cancel(calling_session_state_uuid, "try_replace_prompt_state")
            self._prompt_state = prompt_state


SessionID = str
UserID = pydantic.SecretStr
SessionStateKey = Tuple[UserID, SessionID]


class SessionStateManager(Protocol):
    def get_session_state(
        self, request_context: RequestContext, auth_info: AuthInfo
    ) -> SessionState | None:
        """Returns the session state for the given request, or None if the
        user/session should not participate in prompt caching, or if prompt
        caching is not supported for the model"""
        ...


class SessionStateLRU(SessionStateManager):
    def __init__(self, size: int, prompt_caching_enabled: bool):
        self._lock = threading.Lock()
        # LRU gives some protection against unbounded growth, in the dimension
        # of sessions, but:
        # a) Memory consumption of each entry changes over time, based upon prompt caching
        #    behavior and limits
        # b) Entries that are handed out and then evicted still consume memory in the process
        #    until the running requests complete (bounded by number of in-flight requests)
        # Could explore sizing based on TokenApportionmentConfig, if CompletionPromptFormatter
        # is bound to limit the size of PromptCache based upon that config.
        #
        self._sessions: MutableMapping[SessionStateKey, SessionState] = lru.LRU(
            size=size
        )
        self._prompt_caching_enabled = prompt_caching_enabled

    def get_session_state(
        self, request_context: RequestContext, auth_info: AuthInfo
    ) -> SessionState | None:
        if auth_info.user_id is None:
            # Prompt state caching not supported without a user ID
            return None

        key: SessionStateKey = (auth_info.user_id, request_context.request_session_id)
        with self._lock:
            state = self._sessions.get(key)
            if state is None or not state.valid_for_request(auth_info):
                state = SessionState(auth_info, self._prompt_caching_enabled)
                self._sessions[key] = state
            return state
