"""Tests for the handler module."""

import typing
import uuid
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import grpc
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
import pydantic
import pytest

import base.feature_flags
import base.tokenizers
from base import prompt_format_completion
from base.blob_names import blob_names_pb2
from base.prompt_format.common import PromptChunk, PromptFormatterOutput
from base.prompt_format_completion.prompt_formatter import (
    CompletionPromptFormatter,
    PromptCache,
    PromptInput,
)
from base.python.opentelemetry_utils.traced_threadpool import TracedThreadPoolExecutor
from services.completion_host import completion_pb2
from services.completion_host.single_model_server.handler import CompletionException
from services.lib.retrieval.null_retriever import NullRetriever
from services.completion_host.single_model_server.completion_request_insight_builder import (
    CompletionRequestInsightBuilder,
)
from services.lib.retrieval.retriever import (
    FindMissingResult as RetrieverFind<PERSON><PERSON>ing<PERSON><PERSON><PERSON>,
)
from services.lib.retrieval.retriever import (
    <PERSON>tri<PERSON><PERSON><PERSON><PERSON>,
    RetrievalR<PERSON>ult,
)
from services.completion_host.single_model_server.single_round_handler import (
    _SEND_400_ON_BAD_CURSOR_POSITION,
    _ENABLE_COMPLETION_SESSION_STATE,
    CompletionResult,
    FindMissingResult,
    PostprocessingConfig,
    SingleRoundCompletionHandler,
    create_completion_handler,
)
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.inference_host import infer_pb2_grpc
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)
from services.completion_host.single_model_server.post_processing import (
    LowQualityFilterResult,
    LowQualityFilterConfig,
)


@pytest.fixture
def feature_flags() -> (
    typing.Generator[base.feature_flags.LocalFeatureFlagSetter, None, None]
):
    yield from base.feature_flags.feature_flag_fixture()


class HandlerTestKit:
    """Kit to make testing the handler easier by mocking dependencies."""

    def __init__(
        self,
        config: SingleRoundCompletionHandler.Config,
        model_name: str = "test-model",
    ):
        self.model_name = model_name
        self.inference_stub = MagicMock(infer_pb2_grpc.InfererStub)
        self.inference_client = MagicMock(InfererClient)
        self.content_manager_client = MagicMock(ContentManagerClient)
        self.retriever = MagicMock(NullRetriever)
        self.ri_builder = CompletionRequestInsightBuilder(MagicMock())
        self.handler = create_completion_handler(
            config=config,
            retriever=self.retriever,
            content_manager_client=self.content_manager_client,
            ri_builder=self.ri_builder,
            inference_stub_factory=lambda: self.inference_stub,
            model_name=model_name,
            namespace="test_namespace",
        )
        self.handler.client = self.inference_client
        self.handler.stream_client = self.inference_client
        self.tokenizer = self.handler.tokenizer
        self.prompt_formatter = self.handler.prompt_formatter

    def create_inference_reply(
        self, output: list[int] | str, log_probs: list[float] | None = None
    ) -> InferenceClientProtocol.Reply:
        if isinstance(output, str):
            output = self.tokenizer.tokenize_unsafe(output)
        if log_probs:
            assert len(output) == len(log_probs)
        return InferenceClientProtocol.Reply(output, log_probs)

    def complete(
        self,
        request: completion_pb2.CompletionRequest,  # type: ignore
        request_id: uuid.UUID | None = None,
        session_id: uuid.UUID | None = None,
        auth_info: AuthInfo | None = None,
        max_workers: int = 4,
        streaming: bool = False,
    ) -> CompletionResult:
        """Test a completion.

        Returns the text of the last input.
        """
        if auth_info is None:
            auth_info = AuthInfo(
                tenant_id="1234567890",
                tenant_name="test-tenant",
                shard_namespace="test-shard-namespace",
                cloud="test-cloud",
            )
        request_with_defaults = completion_pb2.CompletionRequest(
            model_name=self.model_name,
            max_tokens=1,
            blobs=blob_names_pb2.Blobs(),
        )
        request_with_defaults.MergeFrom(request)

        if request_id is None:
            request_id = uuid.uuid4()
        if session_id is None:
            session_id = request_id
        request_context = RequestContext(
            request_id=str(request_id),
            request_session_id=str(session_id),
            request_source="unknown",
        )

        with TracedThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="test-threadpool-",
        ) as executor:
            result = self.handler.complete(
                request=request_with_defaults,
                request_context=request_context,
                auth_info=auth_info,
                executor=executor,
                streaming=streaming,
            )
        return result

    def find_missing(
        self,
        blob_names: typing.Sequence[str],
        request_id: uuid.UUID | None = None,
        session_id: uuid.UUID | None = None,
        max_workers: int = 4,
    ) -> FindMissingResult:
        request = completion_pb2.FindMissingRequest(
            model_name=self.model_name,
            blob_names=blob_names,
        )

        if request_id is None:
            request_id = uuid.uuid4()
        if session_id is None:
            session_id = request_id
        request_context = RequestContext(
            request_id=str(request_id),
            request_session_id=str(session_id),
            request_source="unknown",
        )

        with TracedThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="test-threadpool-",
        ) as executor:
            return self.handler.find_missing(
                request=request,
                request_context=request_context,
                executor=executor,
            )


@pytest.fixture
def codegen_kit() -> HandlerTestKit:
    return HandlerTestKit(
        model_name="codegen-6B",
        config=SingleRoundCompletionHandler.Config(
            tokenizer_name="fim",
            prompt_formatter_name="indiana",
            apportionment_config=prompt_format_completion.TokenApportionmentConfig(
                max_content_len=1000 * 1000,
                input_fraction=0.5,
                prefix_fraction=0.5,
                max_path_tokens=1000,
            ),
        ),
    )


def test_handler(codegen_kit: HandlerTestKit, feature_flags):
    """Test for the standard case of the completion handler."""
    codegen_kit.inference_client.infer.return_value = (
        codegen_kit.create_inference_reply("assert a['clue'] ==")
    )
    output = codegen_kit.complete(completion_pb2.CompletionRequest())
    assert output.text == "assert a['clue'] =="


def test_handler_eos(codegen_kit: HandlerTestKit, feature_flags):
    """Test with having multiple EOS tokens in the output."""
    codegen_kit.inference_client.infer.return_value = (
        codegen_kit.create_inference_reply(
            [
                *codegen_kit.tokenizer.tokenize_safe("assert a"),
                codegen_kit.tokenizer.special_tokens.eos,
                codegen_kit.tokenizer.special_tokens.eos,
                codegen_kit.tokenizer.special_tokens.eos,
                codegen_kit.tokenizer.special_tokens.eos,
            ]
        )
    )

    output = codegen_kit.complete(completion_pb2.CompletionRequest())
    assert output.text == "assert a"

    last_eos_token_ids = codegen_kit.inference_client.infer.call_args.kwargs[
        "end_token_ids"
    ]
    assert last_eos_token_ids == {codegen_kit.tokenizer.special_tokens.eos}


@pytest.mark.parametrize(
    ("position,send400"),
    [
        # Case: prefix > suffix
        (
            completion_pb2.CompletionPosition(
                prefix_begin=10, suffix_end=0, cursor_position=0, blob_name="foo-bar"
            ),
            False,
        ),
        (
            completion_pb2.CompletionPosition(
                prefix_begin=10, suffix_end=0, cursor_position=0, blob_name="foo-bar"
            ),
            True,
        ),
        (
            completion_pb2.CompletionPosition(
                prefix_begin=10, suffix_end=0, cursor_position=0, blob_name=""
            ),
            False,
        ),
        (
            completion_pb2.CompletionPosition(
                prefix_begin=10, suffix_end=0, cursor_position=0, blob_name=""
            ),
            True,
        ),
    ],
)
def test_handler_malformed_position(
    feature_flags,
    codegen_kit: HandlerTestKit,
    position: completion_pb2.CompletionPosition,
    send400: bool,
):
    """Test completion handler when there's a bad position."""
    feature_flags.set_flag(_SEND_400_ON_BAD_CURSOR_POSITION, send400)

    with pytest.raises(CompletionException) as excinfo:
        codegen_kit.complete(
            completion_pb2.CompletionRequest(
                path="foo.py",
                prefix="x" * 3,
                suffix="x" * 3,
                position=position,
            )
        )
    assert excinfo.value.code() == grpc.StatusCode.INVALID_ARGUMENT


@pytest.mark.parametrize(
    "position",
    [
        # Case: Empty positions in probe / status-check requests.
        completion_pb2.CompletionPosition(),
        # Case: cursor on the left boundary of the prefix-suffix range.
        completion_pb2.CompletionPosition(
            # We replace file[2:12] with the prefix/suffix below which is only 6 chars.
            prefix_begin=2,
            suffix_end=12,
            cursor_position=2,
            blob_name="foo-bar",
        ),
        # Case: cursor on the right boundary of the prefix-suffix range.
        completion_pb2.CompletionPosition(
            # We replace file[2:12] with the prefix/suffix below which is only 6 chars.
            prefix_begin=2,
            suffix_end=12,
            cursor_position=8,
            blob_name="foo-bar",
        ),
        # Case: position information without a blob name.
        completion_pb2.CompletionPosition(
            # We replace file[2:12] with the prefix/suffix below which is only 6 chars.
            prefix_begin=2,
            suffix_end=12,
            cursor_position=5,
            blob_name="",
        ),
    ],
)
@pytest.mark.parametrize("send400", [False, True])
def test_handler_wellformed_position(
    feature_flags,
    codegen_kit: HandlerTestKit,
    position: completion_pb2.CompletionPosition,
    send400: bool,
):
    """Test completion handler when there's a bad position."""
    feature_flags.set_flag(_SEND_400_ON_BAD_CURSOR_POSITION, send400)

    codegen_kit.inference_client.infer.return_value = (
        codegen_kit.create_inference_reply("assert a['clue'] ==")
    )

    response = codegen_kit.complete(
        completion_pb2.CompletionRequest(
            path="foo.py",
            prefix="x" * 3,
            suffix="x" * 3,
            position=position,
            probe_only=True,
        )
    )
    assert response.text == "assert a['clue'] =="


def test_correct_trimming(codegen_kit: HandlerTestKit):
    """Test output tokens trimming."""
    codegen_kit.inference_client.infer.return_value = (
        codegen_kit.create_inference_reply(
            [
                *codegen_kit.tokenizer.tokenize_safe("assert a"),
                codegen_kit.tokenizer.special_tokens.eos,
                *codegen_kit.tokenizer.tokenize_safe("assert b"),
                codegen_kit.tokenizer.special_tokens.eos,
            ]
        )
    )

    output = codegen_kit.complete(completion_pb2.CompletionRequest())
    assert output.text == "assert a"


def test_handler_eos_extra_stop_tokens():
    """Test handling of completion with extra EOS tokens in the output."""
    kit = HandlerTestKit(
        config=SingleRoundCompletionHandler.Config(
            tokenizer_name="rogue",
            prompt_formatter_name="rogue",
            apportionment_config=prompt_format_completion.TokenApportionmentConfig(
                max_content_len=1000 * 1000,
                input_fraction=0.5,
                prefix_fraction=0.5,
                max_path_tokens=1000,
            ),
            extra_stop_tokens=["<|pause|>"],
        ),
    )
    special_tokens = typing.cast(
        base.tokenizers.RogueSpecialTokens, kit.tokenizer.special_tokens
    )
    infer = kit.inference_client.infer
    infer.return_value = kit.create_inference_reply(
        [
            *kit.tokenizer.tokenize_safe("assert a"),
            special_tokens.pause,
            special_tokens.eos,
        ]
    )

    output = kit.complete(completion_pb2.CompletionRequest())
    assert output.text == "assert a"
    last_eos_token_ids = infer.call_args.kwargs["end_token_ids"]
    assert last_eos_token_ids == {special_tokens.eos, special_tokens.pause}


@pytest.mark.parametrize(
    "inference_reply, expected",
    [
        # If the model generation ends without an EOS, we should remove the partial line.
        ("hello\nworld", "hello"),
        # Logic also applies to new lines.
        ("hello\nworld\n", "hello\nworld"),
        # If the model generation ends with an EOS, don't remove partial lines.
        ("hello\nworld<|endoftext|>", "hello\nworld"),
        # If the model generation ends with an EOS, don't remove partial lines or new lines.
        ("hello\nworld\n<|endoftext|>", "hello\nworld\n"),
    ],
)
def test_handler_partial_line_behavior(
    codegen_kit: HandlerTestKit, inference_reply: str, expected: str
):
    """Test with results that contain partial lines that should be removed."""
    infer = codegen_kit.inference_client.infer
    infer.return_value = codegen_kit.create_inference_reply(inference_reply)
    output = codegen_kit.complete(completion_pb2.CompletionRequest())
    assert output.text == expected


def test_handler_truncation(codegen_kit: HandlerTestKit):
    """Test with with explicit single-line truncation."""
    infer = codegen_kit.inference_client.infer
    infer.return_value = codegen_kit.create_inference_reply("hello;;world")
    output = codegen_kit.complete(completion_pb2.CompletionRequest(truncation=[";;"]))
    assert output.text == "hello"


SKIP_TOKEN = "<|skip|>"


@pytest.mark.parametrize(
    argnames=["raw_text", "suffix", "completion", "skipped_suffix", "replacement_text"],
    argvalues=[
        (f"a: int, b: int{SKIP_TOKEN}:\n", ")\n", "a: int, b: int", ")", "):\n"),
        (
            f"\n    a: int,\n    b: int{SKIP_TOKEN}:\n",
            "\n)",
            "\n    a: int,\n    b: int",
            "\n)",
            "\n):\n",
        ),
        (
            "def func(a: int, b: int  ):",
            "  )",
            "def func(a: int, b: int  ):",
            "",
            "",
        ),
        (
            f"0{SKIP_TOKEN},\n    b: Num = Num(0),{SKIP_TOKEN}:\n",
            ")\n)some other text",
            "0",
            ")\n)",
            "),\n    b: Num = Num(0),\n):\n",
        ),
        (
            f"a + b{SKIP_TOKEN} ** 2{SKIP_TOKEN}  some dummy text",
            ")    #f is the squared sum of a and b.",
            "a + b",
            ")",
            ") ** 2",
        ),
    ],
    ids=[
        "test_plain_old_skip",
        "test_whitespace_before_skip",
        "test_no_skip",
        "test_two_skip",
        "test_unmatched_suffix",
    ],
)
def test_skip_token_handling(
    raw_text: str,
    suffix: str,
    completion: str,
    skipped_suffix: str,
    replacement_text: str,
):
    kit = HandlerTestKit(
        config=SingleRoundCompletionHandler.Config(
            prompt_formatter_name="codegen",
            skip_token_str=SKIP_TOKEN,
            tokenizer_name="rogue",
            apportionment_config=prompt_format_completion.TokenApportionmentConfig(
                max_content_len=1000 * 1000,
                input_fraction=0.5,
                prefix_fraction=0.5,
                max_path_tokens=1000,
            ),
        ),
    )
    infer = kit.inference_client.infer
    infer.return_value = kit.create_inference_reply(raw_text)
    result = kit.complete(completion_pb2.CompletionRequest(suffix=suffix))
    assert result.text == completion
    assert result.suffix_replacement_text == replacement_text
    assert result.skipped_suffix == skipped_suffix


def test_handler_without_fim_support_drops_fim_suffix():
    """Test with using a suffix (for fill-in-the-middle) on a model that doesn't support it."""
    kit = HandlerTestKit(
        config=SingleRoundCompletionHandler.Config(
            tokenizer_name="fim",
            # Codegen doesn't support suffixes.
            prompt_formatter_name="codegen",
            apportionment_config=prompt_format_completion.TokenApportionmentConfig(
                max_content_len=1000 * 1000,
                input_fraction=0.5,
                prefix_fraction=0.5,
                max_path_tokens=1000,
            ),
        ),
    )
    kit.complete(
        completion_pb2.CompletionRequest(
            prefix="def foo():",
            suffix="print('Hello World')",
        )
    )
    input_tokens = kit.inference_client.infer.call_args.kwargs["input_tokens"]
    assert input_tokens == kit.tokenizer.tokenize_safe("def foo():")


def test_handler_default_sampling(codegen_kit: HandlerTestKit):
    """Tests the default sampling configuration uses for the client calls."""
    codegen_kit.complete(
        completion_pb2.CompletionRequest(
            prefix="def foo():",
            suffix="print('Hello World')",
            max_tokens=280,
            blobs=blob_names_pb2.Blobs(),
        )
    )
    last_kwargs = codegen_kit.inference_client.infer.call_args.kwargs
    # We ignore the value coming in from the client for sampling
    assert last_kwargs["top_k"] == 0
    assert last_kwargs["top_p"] == 0.0
    assert last_kwargs["temperature"] == 0.0
    assert last_kwargs["max_output_length"] == 280


def test_probe_only(codegen_kit: HandlerTestKit):
    """Test running a completion without inference."""
    codegen_kit.retriever.retrieve.return_value = RetrievalResult(
        retrieved_chunks=[],
        missing_blob_names=["abc", "123"],
        checkpoint_not_found=False,
    )

    blob_names_hex = ["cafe"]
    blobs = blob_names_pb2.Blobs(added=[bytes.fromhex(x) for x in blob_names_hex])

    result = codegen_kit.complete(
        completion_pb2.CompletionRequest(blobs=blobs, probe_only=True)
    )

    assert codegen_kit.inference_client.infer.call_args_list[0].kwargs[
        "probe_only"
    ], "Inference was called with probe_only=True."
    assert set(result.unknown_blob_names) == set(["abc", "123"])


def test_find_missing(codegen_kit: HandlerTestKit):
    missing_blobs = ["missing-0", "missing-1"]
    nonindexed_blobs = ["nonindexed-0", "nonindexed-1"]
    codegen_kit.retriever.find_missing.return_value = RetrieverFindMissingResult(
        missing_blob_names=missing_blobs + nonindexed_blobs,
    )
    codegen_kit.content_manager_client.find_missing.return_value = missing_blobs

    to_check = ["valid-0", "valid-1"] + missing_blobs + nonindexed_blobs
    result = codegen_kit.find_missing(blob_names=to_check)

    assert set(result.missing_blob_names) == set(missing_blobs)
    assert set(result.nonindexed_blob_names) == set(nonindexed_blobs)


@pytest.mark.parametrize(
    argnames=["raw_text", "suffix", "completion", "skipped_suffix", "replacement_text"],
    argvalues=[
        ("a" * 20, "\n" + "a" * 20, "", "", ""),
        ("a" * 20, "\nb" + "a" * 20, "a" * 20, "", ""),
        (f'"{"a" * 20}{SKIP_TOKEN} + "b', f'"{"a" * 20}', "", "", ""),
        (f'"{"a"*20}{SKIP_TOKEN} + "b', f'"b{"a" * 20}', f'"{"a" * 20}', '"', '" + "b'),
        ("TODO(Abc):", "", "TODO:", "", ""),
        (f"TODO(Abc): return {SKIP_TOKEN}a", '""', "TODO: return ", '"', '"a'),
    ],
)
def test_handler_post_processing(
    raw_text: str,
    suffix: str,
    completion: str,
    skipped_suffix: str,
    replacement_text: str,
):
    kit = HandlerTestKit(
        config=SingleRoundCompletionHandler.Config(
            prompt_formatter_name="codegen",
            skip_token_str=SKIP_TOKEN,
            tokenizer_name="rogue",
            apportionment_config=prompt_format_completion.TokenApportionmentConfig(
                max_content_len=1000 * 1000,
                input_fraction=0.5,
                prefix_fraction=0.5,
                max_path_tokens=1000,
            ),
            post_processing=PostprocessingConfig(
                anonymize_todos=True,
                suppress_verbatim_copy_from_context=True,
            ),
        ),
    )

    infer = kit.inference_client.infer
    infer.return_value = kit.create_inference_reply(raw_text)
    result = kit.complete(completion_pb2.CompletionRequest(suffix=suffix))
    assert result.text == completion
    assert result.skipped_suffix == skipped_suffix
    assert result.suffix_replacement_text == replacement_text


@pytest.mark.parametrize(
    "use_filter, output, log_probs, filter_result, expected",
    [
        (
            False,
            [1, 2, 3],
            [0.1, 0.2, 0.3],
            "processed completion",
            ("<fim_prefix><fim_middle><fim_suffix>", "", "", None),
        ),
        (
            True,
            [1, 2, 3],
            [0.1, 0.2, 0.3],
            "processed completion",
            ("processed completion", "", "", 0.95),
        ),
        (
            False,
            f"hello{SKIP_TOKEN} + world",
            [0.1, 0.2, 0.3, 0.4],
            "",
            ("hello", '"', '" + world', None),
        ),
        (
            True,
            f"hello{SKIP_TOKEN} + world",
            [0.1, 0.2, 0.3, 0.4],
            "",
            ("", "", "", 0.95),
        ),
    ],
)
@patch("services.completion_host.single_model_server.post_processing.LowQualityFilter")
def test_handler_use_low_quality_filter(
    mock_filter: Mock,
    use_filter: bool,
    output: list[int],
    log_probs: list[float],
    filter_result: str,
    expected: tuple[str, str, str, typing.Optional[float]],
):
    mock_filter.return_value = mock_instance = MagicMock(
        return_value=LowQualityFilterResult(True, 0.95, 0.8)
    )
    mock_instance.process_completion.return_value = filter_result
    kit = HandlerTestKit(
        config=SingleRoundCompletionHandler.Config(
            prompt_formatter_name="codegen",
            skip_token_str=SKIP_TOKEN,
            tokenizer_name="rogue",
            apportionment_config=prompt_format_completion.TokenApportionmentConfig(
                max_content_len=1000 * 1000,
                input_fraction=0.5,
                prefix_fraction=0.5,
                max_path_tokens=1000,
            ),
            post_processing=PostprocessingConfig(
                #  `low_quality_filter_config` is not used because `LowQualityFilter` is
                # mocked.
                low_quality_filter_config=MagicMock() if use_filter else None
            ),
        ),
    )

    infer = kit.inference_client.infer
    infer.return_value = kit.create_inference_reply(
        # These tokens correspond to '<fim_prefix><fim_middle><fim_suffix>'.
        output=output,
        log_probs=log_probs,
    )
    result = kit.complete(
        completion_pb2.CompletionRequest(prefix='print("', suffix='")')
    )
    if use_filter:
        mock_instance.assert_called_once()
        mock_instance.process_completion.assert_called_once()
    else:
        mock_instance.assert_not_called()
    assert result.text == expected[0]
    assert result.skipped_suffix == expected[1]
    assert result.suffix_replacement_text == expected[2]
    assert result.filter_score == expected[3]


# The filter score is 0.7432 for the completion. We expect no filtering if the filter threshold is 1) None/out of bounds (the default is 1.0 in this case) or 2) above this value.
@pytest.mark.parametrize(
    "filter_threshold, expect_filtering",
    [
        (-0.2, False),
        (0, True),
        (0.4, True),
        (0.9, False),
        (1, False),
        (1.2, False),
        (None, False),
    ],
)
def test_completion_filter_threshold(
    filter_threshold: typing.Optional[float], expect_filtering: bool
):
    """Test running a completion with a filter threshold."""
    kit = HandlerTestKit(
        config=SingleRoundCompletionHandler.Config(
            prompt_formatter_name="codegen",
            skip_token_str=SKIP_TOKEN,
            tokenizer_name="rogue",
            apportionment_config=prompt_format_completion.TokenApportionmentConfig(
                max_content_len=1000 * 1000,
                input_fraction=0.5,
                prefix_fraction=0.5,
                max_path_tokens=1000,
            ),
            post_processing=PostprocessingConfig(
                low_quality_filter_config=LowQualityFilterConfig(
                    checkpoint_path="services/completion_host/single_model_server/prism_models/prism_roguesl_farpref_16B.json",
                    feature_extractor_version="feature_extractor_v1",
                    default_threshold=1.0,
                )
            ),
        ),
    )

    infer = kit.inference_client.infer
    infer.return_value = kit.create_inference_reply(
        # These tokens correspond to '<fim_prefix><fim_middle><fim_suffix>'.
        output=[1, 2, 3],
        log_probs=[0.1, 0.2, 0.3],
    )

    result = kit.complete(
        completion_pb2.CompletionRequest(
            prefix='print("',
            path="test.py",
            suffix='")',
            filter_threshold=filter_threshold,
        )
    )
    if expect_filtering:
        assert len(result.text) == 0
    else:
        assert len(result.text) > 0


def test_skip_overlap_filter(codegen_kit: HandlerTestKit):
    """Test running a completion does not perform overlap filtering."""
    codegen_kit.handler.prompt_formatter = MagicMock(CompletionPromptFormatter)
    codegen_kit.handler.prompt_formatter.format_prompt.return_value = (
        PromptFormatterOutput([])
    )

    codegen_kit.retriever.retrieve.return_value = RetrievalResult(
        retrieved_chunks=[
            RetrievalChunk(
                text="abc",
                path="xyz",
                char_start=0,
                char_end=3,
                blob_name=None,
                chunk_index=None,
            )
        ],
        missing_blob_names=[],
        checkpoint_not_found=False,
    )

    blob_names_hex = ["cafe"]
    blobs = blob_names_pb2.Blobs(added=[bytes.fromhex(x) for x in blob_names_hex])

    codegen_kit.complete(
        completion_pb2.CompletionRequest(
            blobs=blobs, prefix="abc", suffix="123", probe_only=True
        )
    )

    assert list(
        codegen_kit.handler.prompt_formatter.format_prompt.call_args_list[0][1][
            "prompt_input"
        ].retrieved_chunks
    ) == [
        PromptChunk(
            text="abc",
            path="xyz",
            unique_id=None,
            origin="",
            char_start=0,
            char_end=3,
            blob_name="",
        )
    ]


class SimpleAppendFormatter(CompletionPromptFormatter):
    def __init__(self, tokenizer: base.tokenizers.Tokenizer):
        self.tokenizer = tokenizer

    def format_prompt(
        self,
        prompt_input: PromptInput,
        max_output_token_count: int,
    ) -> PromptFormatterOutput:
        return self.format_prompt_with_cache(
            prompt_input, max_output_token_count, PromptCache(), []
        )[0]

    def supports_caching(self) -> bool:
        return True

    def format_prompt_with_cache(
        self,
        prompt_input: PromptInput,
        max_output_token_count: int,
        prompt_cache: PromptCache,
        invalid_blobs: typing.Sequence[bytes],
    ) -> typing.Tuple[PromptFormatterOutput, PromptCache]:
        # No invalidation of anything. Combine input and cache, then tokenize.
        chunk_set = set(chunk.unique_id for chunk in prompt_cache.prompt_ordered_chunks)
        chunks = list(prompt_cache.prompt_ordered_chunks)
        chunks.extend(
            c for c in prompt_input.retrieved_chunks if c.unique_id not in chunk_set
        )

        new_cache = PromptCache(prompt_ordered_chunks=chunks)
        tokens = self.tokenizer.tokenize_safe(" ".join(chunk.text for chunk in chunks))
        return PromptFormatterOutput([tokens]), new_cache


@pytest.fixture
def prompt_caching_kit(feature_flags):
    formatter = SimpleAppendFormatter(base.tokenizers.create_tokenizer_by_name("fim"))
    with patch(
        "single_round_handler.prompt_format_completion.get_completion_prompt_formatter_by_name",
        return_value=formatter,
    ):
        return HandlerTestKit(
            model_name="codegen-6B",
            config=SingleRoundCompletionHandler.Config(
                tokenizer_name="fim",
                prompt_formatter_name="unused",
                apportionment_config=prompt_format_completion.TokenApportionmentConfig(
                    max_content_len=1000 * 1000,
                    input_fraction=0.5,
                    prefix_fraction=0.5,
                    max_path_tokens=1000,
                ),
            ),
        )


def prompt_caching_retrieval_mock(*chunk_text):
    # Chunks must have blob_name and chunk_index to generate a unique_id
    chunks = [
        RetrievalChunk(
            text=txt,
            path="xyz",
            char_start=0,
            char_end=len(txt),
            blob_name=f"blob{txt}",
            chunk_index=0,
        )
        for txt in chunk_text
    ]

    return RetrievalResult(
        retrieved_chunks=chunks, missing_blob_names=[], checkpoint_not_found=False
    )


def test_prompt_caching(prompt_caching_kit, feature_flags):
    feature_flags.set_flag(_ENABLE_COMPLETION_SESSION_STATE, True)

    kit = prompt_caching_kit
    infer = kit.inference_client.infer
    infer.return_value = kit.create_inference_reply("unimportant")
    comp = completion_pb2.CompletionRequest(
        blobs=blob_names_pb2.Blobs(added=[bytes.fromhex("cafe")])
    )

    # User, session, and auth must remain stable in order to cache prompt state
    ses_id = uuid.uuid4()
    auth_info = AuthInfo(
        tenant_id="1234567890",
        tenant_name="test_tenant",
        user_id=pydantic.SecretStr("test_user"),
        shard_namespace="test-shard-namespace",
        cloud="test-cloud",
    )

    kit.retriever.retrieve.return_value = prompt_caching_retrieval_mock(
        "chunkA", "chunkB"
    )
    kit.complete(comp, session_id=ses_id, auth_info=auth_info, streaming=True)
    assert infer.call_args.kwargs["input_tokens"] == kit.tokenizer.tokenize_safe(
        "chunkA chunkB"
    )

    # Second retrieval retrieves only B and E
    # Because of cached state, prompt formatter can build a prompt including A, B, and E
    kit.retriever.retrieve.return_value = prompt_caching_retrieval_mock(
        "chunkB", "chunkE"
    )
    kit.complete(comp, session_id=ses_id, auth_info=auth_info, streaming=True)
    assert infer.call_args.kwargs["input_tokens"] == kit.tokenizer.tokenize_safe(
        "chunkA chunkB chunkE"
    )

    # Only new chunk from third retrieval is C
    kit.retriever.retrieve.return_value = prompt_caching_retrieval_mock(
        "chunkC", "chunkA", "chunkE"
    )
    kit.complete(comp, session_id=ses_id, auth_info=auth_info, streaming=True)
    assert infer.call_args.kwargs["input_tokens"] == kit.tokenizer.tokenize_safe(
        "chunkA chunkB chunkE chunkC"
    )


def test_prompt_caching_multi_session(prompt_caching_kit, feature_flags):
    """Test that each session maintains a separate prompt cache"""
    feature_flags.set_flag(_ENABLE_COMPLETION_SESSION_STATE, True)

    kit = prompt_caching_kit
    infer = kit.inference_client.infer
    infer.return_value = kit.create_inference_reply("unimportant")
    comp = completion_pb2.CompletionRequest(
        blobs=blob_names_pb2.Blobs(added=[bytes.fromhex("cafe")])
    )

    base_profile = {
        "session_id": uuid.uuid4(),
        "auth_info": AuthInfo(
            tenant_id="1234567890",
            tenant_name="test_tenant",
            user_id=pydantic.SecretStr("test_userA"),
            shard_namespace="test-shard-namespace",
            cloud="test-cloud",
        ),
    }
    profiles = [
        base_profile,
        base_profile | {"session_id": uuid.uuid4()},
        base_profile | {"session_id": uuid.uuid4()},
    ]

    # Interleave completions: profile 0, 1, 2, 0, 1, 2. Assert on the second completion for each profile
    kit.retriever.retrieve.return_value = prompt_caching_retrieval_mock("chunkA")
    kit.complete(comp, **profiles[0], streaming=True)
    kit.retriever.retrieve.return_value = prompt_caching_retrieval_mock("chunkE")
    kit.complete(comp, **profiles[1], streaming=True)
    kit.retriever.retrieve.return_value = prompt_caching_retrieval_mock(
        "chunkD", "chunkC"
    )
    kit.complete(comp, **profiles[2], streaming=True)

    # Second completion for each should use the cached state for that session/user
    kit.retriever.retrieve.return_value = prompt_caching_retrieval_mock("chunkB")
    kit.complete(comp, **profiles[0], streaming=True)
    assert infer.call_args.kwargs["input_tokens"] == kit.tokenizer.tokenize_safe(
        "chunkA chunkB"
    )

    kit.retriever.retrieve.return_value = prompt_caching_retrieval_mock(
        "chunkA", "chunkF"
    )
    kit.complete(comp, **profiles[1], streaming=True)
    assert infer.call_args.kwargs["input_tokens"] == kit.tokenizer.tokenize_safe(
        "chunkE chunkA chunkF"
    )

    kit.retriever.retrieve.return_value = prompt_caching_retrieval_mock("chunkD")
    kit.complete(comp, **profiles[2], streaming=True)
    assert infer.call_args.kwargs["input_tokens"] == kit.tokenizer.tokenize_safe(
        "chunkD chunkC"
    )


def test_prompt_caching_reset_on_auth_change(prompt_caching_kit, feature_flags):
    """Even for same user and session, a change to auth_info should reset the cache"""
    feature_flags.set_flag(_ENABLE_COMPLETION_SESSION_STATE, True)

    kit = prompt_caching_kit
    infer = kit.inference_client.infer
    infer.return_value = kit.create_inference_reply("unimportant")
    comp = completion_pb2.CompletionRequest(
        blobs=blob_names_pb2.Blobs(added=[bytes.fromhex("cafe")])
    )

    ses_id = uuid.uuid4()
    auth_info = AuthInfo(
        tenant_id="1234567890",
        tenant_name="test_tenant",
        user_id=pydantic.SecretStr("test_user"),
        shard_namespace="test-shard-namespace",
        cloud="test-cloud",
    )

    kit.retriever.retrieve.return_value = prompt_caching_retrieval_mock(
        "chunkA", "chunkB"
    )
    kit.complete(comp, session_id=ses_id, auth_info=auth_info, streaming=True)
    assert infer.call_args.kwargs["input_tokens"] == kit.tokenizer.tokenize_safe(
        "chunkA chunkB"
    )

    # Change tenant id
    auth_info.tenant_id = "0987654321"
    kit.retriever.retrieve.return_value = prompt_caching_retrieval_mock("chunkE")
    kit.complete(comp, session_id=ses_id, auth_info=auth_info, streaming=True)
    assert infer.call_args.kwargs["input_tokens"] == kit.tokenizer.tokenize_safe(
        "chunkE"
    )

    # One round with same auth
    kit.retriever.retrieve.return_value = prompt_caching_retrieval_mock(
        "chunkC", "chunkA", "chunkE"
    )
    kit.complete(comp, session_id=ses_id, auth_info=auth_info, streaming=True)
    assert infer.call_args.kwargs["input_tokens"] == kit.tokenizer.tokenize_safe(
        "chunkE chunkC chunkA"
    )

    # Change scopes
    auth_info.scopes.append("scope1")
    kit.retriever.retrieve.return_value = prompt_caching_retrieval_mock(
        "chunkD", "chunkC"
    )
    kit.complete(comp, session_id=ses_id, auth_info=auth_info, streaming=True)
    assert infer.call_args.kwargs["input_tokens"] == kit.tokenizer.tokenize_safe(
        "chunkD chunkC"
    )
