"""Tests for truncate_at_parenthesis module."""

import pytest

import base.feature_flags
from base.feature_flags import feature_flag_fixture
from services.completion_host.single_model_server.truncate_at_parenthesis import (
    truncate_at_parenthesis,
    _ENABLE_TRUNCATE_AT_PARENTHESIS,
    _TRUNCATE_AT_PARENTHESIS_RANDOMIZE,
)


@pytest.fixture
def feature_flags():
    yield from feature_flag_fixture()


def test_truncate_at_parenthesis_disabled(feature_flags):
    """Test that truncation doesn't happen when the feature flag is disabled."""
    feature_flags.set_flag(_ENABLE_TRUNCATE_AT_PARENTHESIS, False)
    context = base.feature_flags.get_global_context()

    completion = "def example(arg1, arg2):"
    result = truncate_at_parenthesis(completion, "test.py", "", context)

    assert result.text == completion
    assert not result.was_truncated
    assert not result.could_have_truncated


def test_truncate_at_parenthesis_enabled(feature_flags):
    """Test that truncation happens when the feature flag is enabled."""
    feature_flags.set_flag(_ENABLE_TRUNCATE_AT_PARENTHESIS, True)
    feature_flags.set_flag(_TRUNCATE_AT_PARENTHESIS_RANDOMIZE, False)
    context = base.feature_flags.get_global_context()

    completion = "def example(\narg1, arg2):"
    result = truncate_at_parenthesis(completion, "test.py", "", context)

    assert result.text == "def example("
    assert result.was_truncated
    assert result.could_have_truncated


def test_truncate_at_parenthesis_no_newline_after_paren(feature_flags):
    """Test that truncation does not happen if the char after ( is not a newline."""
    feature_flags.set_flag(_ENABLE_TRUNCATE_AT_PARENTHESIS, True)
    feature_flags.set_flag(_TRUNCATE_AT_PARENTHESIS_RANDOMIZE, False)
    context = base.feature_flags.get_global_context()

    completion = "def example(arg1, arg2):"
    result = truncate_at_parenthesis(completion, "test.py", "", context)

    assert result.text == completion
    assert not result.was_truncated
    assert not result.could_have_truncated


def test_truncate_at_parenthesis_alphanumeric_before(feature_flags):
    """Test that truncation finds first parenthesis with alphanumeric before."""
    feature_flags.set_flag(_ENABLE_TRUNCATE_AT_PARENTHESIS, True)
    feature_flags.set_flag(_TRUNCATE_AT_PARENTHESIS_RANDOMIZE, False)
    context = base.feature_flags.get_global_context()

    completion = "(if (\ncondition): print()(\n'Hello, (World!')"
    result = truncate_at_parenthesis(completion, "test.py", "", context)

    assert result.text == "(if (\ncondition): print()("
    assert result.was_truncated
    assert result.could_have_truncated


def test_truncate_at_parenthesis_excluded_language(feature_flags):
    """Test that truncation doesn't happen for excluded languages."""
    feature_flags.set_flag(_ENABLE_TRUNCATE_AT_PARENTHESIS, True)
    context = base.feature_flags.get_global_context()

    completion = '<div class="example(test)">'
    result = truncate_at_parenthesis(completion, "test.html", "", context)

    assert result.text == completion
    assert not result.was_truncated
    assert not result.could_have_truncated


def test_truncate_at_parenthesis_no_parenthesis(feature_flags):
    """Test that truncation doesn't happen when there's no parenthesis."""
    feature_flags.set_flag(_ENABLE_TRUNCATE_AT_PARENTHESIS, True)
    context = base.feature_flags.get_global_context()

    completion = "def example:"
    result = truncate_at_parenthesis(completion, "test.py", "", context)

    assert result.text == completion
    assert not result.was_truncated
    assert not result.could_have_truncated


def test_truncate_at_parenthesis_no_alphanumeric_before(feature_flags):
    """Test that truncation doesn't happen when there's no alphanumeric character before the parenthesis."""
    feature_flags.set_flag(_ENABLE_TRUNCATE_AT_PARENTHESIS, True)
    context = base.feature_flags.get_global_context()

    completion = "if (\ncondition):"
    result = truncate_at_parenthesis(completion, "test.py", "", context)

    assert result.text == completion
    assert not result.was_truncated
    assert not result.could_have_truncated


def test_truncate_small_completions(feature_flags):
    """Test that truncation happens when there are at least three characters before the parenthesis."""
    feature_flags.set_flag(_ENABLE_TRUNCATE_AT_PARENTHESIS, True)
    context = base.feature_flags.get_global_context()

    completion = "a"
    result = truncate_at_parenthesis(completion, "test.py", "", context)
    assert result.text == completion
    assert not result.was_truncated
    assert not result.could_have_truncated

    completion = "aa(bb"
    result = truncate_at_parenthesis(completion, "test.py", "", context)
    assert result.text == completion
    assert not result.was_truncated
    assert not result.could_have_truncated

    completion = "aaa(\nbb"
    result = truncate_at_parenthesis(completion, "test.py", "", context)
    assert result.text == "aaa("
    assert result.was_truncated
    assert result.could_have_truncated


def test_truncate_last_char_is_parenthesis(feature_flags):
    """Test that truncation doesn't happen when the last character is a parenthesis."""
    feature_flags.set_flag(_ENABLE_TRUNCATE_AT_PARENTHESIS, True)
    context = base.feature_flags.get_global_context()

    completion = "def example(\n"
    result = truncate_at_parenthesis(completion, "test.py", "", context)

    assert result.text == completion
    assert not result.was_truncated
    assert not result.could_have_truncated


def test_truncate_at_parenthesis_randomize(feature_flags, monkeypatch):
    """Test that truncation is randomized when the randomize flag is enabled."""
    feature_flags.set_flag(_ENABLE_TRUNCATE_AT_PARENTHESIS, True)
    feature_flags.set_flag(_TRUNCATE_AT_PARENTHESIS_RANDOMIZE, True)
    context = base.feature_flags.get_global_context()
    session_id = "test_session_id"

    # Mock should_apply_for_session_id to return True (should truncate)
    monkeypatch.setattr(
        "services.completion_host.single_model_server.truncate_at_parenthesis.should_apply_for_session_id",
        lambda _: True,
    )

    completion = "def example(\narg1, arg2):"
    result = truncate_at_parenthesis(completion, "test.py", session_id, context)

    assert result.text == "def example("
    assert result.was_truncated
    assert result.could_have_truncated

    # Mock should_apply_for_session_id to return False (should not truncate)
    monkeypatch.setattr(
        "services.completion_host.single_model_server.truncate_at_parenthesis.should_apply_for_session_id",
        lambda _: False,
    )

    result = truncate_at_parenthesis(completion, "test.py", session_id, context)

    assert result.text == completion
    assert not result.was_truncated
    assert result.could_have_truncated
