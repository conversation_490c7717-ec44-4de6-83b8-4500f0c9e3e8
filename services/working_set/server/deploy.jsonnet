// K8S deployment file for the route guide service
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local pubsubLib = import 'services/lib/pubsub/pubsub_lib.jsonnet';

// the function that creates the deployment
// env: the environment (DEV, PROD, ...)
// namespace: the namespace that the deployment is created in
// cloud: the cloud (GCP_US_CENTRAL1_DEV, GCP_US_CENTRAL1_PROD, ...)
// namespace_config: the namespace config from //deploy/tenants/namespace_configs
function(env, namespace, cloud, namespace_config)
  // the app name is used in the kubernetes object names. It is also added as label to each object
  // so that we know which app an object belongs to
  local appName = 'working-set';

  // mutual TLS is enabled if the namespace config has the forceMtls flag set
  // MTLS ensures that the client and server certificates are valid
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  // create dynamic feature flag config
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);


  // creates a client certificate so that the pod can authenticiate to grpc servers (incl. itself for health checks)
  // in the same namespace
  local clientCert = certLib.createClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // creates a client certificate so that the pod can authenticiate to grpc servers running in the central namespace
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // creates a server certificate for MTLS
  local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=grpcLib.grpcServiceNames(appName),
                                              volumeName='certs');

  // creates a service account for the pod
  // a service account is needed to access GCP resources or kubernetes resources
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true,
  );

  local checkpointIndexerTopic = pubsubLib.publisherTopic(
    cloud=cloud,
    env=env,
    namespace=namespace,
    appName=appName,
    serviceAccount=serviceAccount,
    topicSuffix='checkpoint-indexing',
  );

  local workingSetIndexCreationSubscriber = pubsubLib.namespaceSubscriber(
    env=env,
    namespace=namespace,
    appName=appName,
    cloud=cloud,
    publisherAppName='checkpoint-indexing',
    serviceAccount=serviceAccount,
    deadLetterMaxDeliveryAttempts=5,
    spec={
      ackDeadlineSeconds: 2 * 60,  // index resolution with retries can take up to 2 minutes
      retryPolicy: {
        minimumBackoff: '60s',
        maximumBackoff: '300s',
      },
    },
  );

  // configuration that will be passed to the server as a JSON file
  local config = {
    port: 50051,
    server_mtls: if mtls then serverCert.config else null,
    client_mtls: if mtls then clientCert.config else null,
    central_client_mtls: if mtls then centralClientCert.config else null,
    prom_port: 9090,
    namespace: namespace,
    project_id: cloudInfo[cloud].projectId,
    token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    content_manager_endpoint: 'content-manager-svc:50051',
    bigtable_proxy_endpoint: 'bigtable-proxy-svc:50051',
    tenant_watcher_endpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    publish_blob_index_metrics: namespace_config.flags.workingSetPublishIndexMetrics,
    session_timeout: '600s',
    stats_loop: '60s',
    cleanup_loop: '60s',
    checkpoint_blob_cache_ttl: '2h',
    checkpoint_blob_cache_size_mb: 3 * 1024,  // 3 GB
    checkpoint_expiration_time: '168h',  // 7 days (7 * 24 hours)
    ann_index_creation_topic_name: checkpointIndexerTopic.topicName,
    ann_index_creation_sub_name: workingSetIndexCreationSubscriber.subscriptionName,
    ann_index_blob_cache_ttl: '24h',
    ann_index_blob_cache_size_mb: 8 * 1024,  // 8 GB
    transformation_key_expiration_time: '672h',  // 4 weeks
    pending_ann_index_creation_timeout: '30m',  // index creation can take up to 30 minutes for large checkpoints
    ann_index_minimum_blobs: 20000,
    cpu_task_pool_max_size: 16,
    io_task_pool_size: 64,
  };
  // a config map is a Kubernetes object that contains configuration data it is "mounted" into a pod
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);
  // creates a service for the pod
  // a service is needed to expose the pod to the outside world
  local services = grpcLib.grpcService(appName=appName, namespace=namespace);
  local probeConfig = grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath);

  // creates a container that runs the server
  local container = {
    name: appName,
    // the target is the bazel target that builds the docker image
    target: {
      name: '//services/working_set/server:image',
      dst: 'working-set-manager',
    },
    // the arguments that are passed to the server
    args: [
      '--config',
      configMap.filename,
    ],
    // ports that the pod exposes
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    // the environment variables that are passed to the server
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
    // the volumes that are mounted into the pod
    volumeMounts: [
      configMap.volumeMountDef,
      centralClientCert.volumeMountDef,
      serverCert.volumeMountDef,
      clientCert.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
    ],
    // the health check is used to determine if the pod is ready to receive traffic
    readinessProbe: probeConfig + {
      periodSeconds: 30,
    },
    // the liveness check is used to determine if the pod is alive
    livenessProbe: probeConfig + {
      periodSeconds: 30,
    },
    startupProbe: probeConfig + {
      // Startup probe should give time to load mappings.
      failureThreshold: 20,
      periodSeconds: 30,
      // timeout for each probe
      timeoutSeconds: 10,
    },
    // the resource limits are used to determine how much CPU and memory the pod can use
    // we're asking for at least 4 vCPUs and can opportunistically use up to 16 vCPUs to speed
    // up the service initialization and help with spikes of index creation requests.
    resources: {
      requests: {
        cpu: 4,
      },
      limits: {
        cpu: 14,
        memory: if namespace_config.flags.usePremiumCpuHighmem then '32Gi' else '16Gi',
      },
    },
  };


  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  // the pod is the kubernetes object that runs the container
  local pod = {
    // the service account is used to access GCP resources or kubernetes resources
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    affinity: affinity,
    tolerations: tolerations,
    // the volumes are mounted into the pod
    volumes: [
      // the config map is mounted into the pod
      configMap.podVolumeDef,
      // the client certificate is mounted into the pod
      centralClientCert.podVolumeDef,
      // the server certificate is mounted into the pod
      serverCert.podVolumeDef,
      // the client certificate is mounted into the pod
      clientCert.podVolumeDef,
      // the dynamic feature flag config is mounted into the pod
      dynamicFeatureFlags.podVolumeDef,
    ],
  };

  // the tolerations and affinity are used to determine which nodes the pod can be scheduled on
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // the minimum amount of time that a pod needs to be ready before the deployment is considered successful
      minReadySeconds: 0,
      // the number of pods that are running at the same time
      replicas: 1,
      // the strategy is used to determine how the deployment is rolled out
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    serverCert.objects,
    centralClientCert.objects,
    clientCert.objects,
    dynamicFeatureFlags.k8s_objects,
    deployment,
    services,
    checkpointIndexerTopic.objects,
    workingSetIndexCreationSubscriber.objects,
  ])
