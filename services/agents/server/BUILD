load("@python_pip//:requirements.bzl", "requirement")
load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_to_json")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_binary(
    name = "agents_server",
    srcs = [
        "agents_server.py",
    ],
    deps = [
        ":agents_handler",
        "//base/logging:struct_logging",
        "//base/python/signal_handler",
        "//base/tracing:tracing_py",
        "//services/agents:agents_py_proto",
        "//services/chat_host/server:chat_third_party_handler",
        "//services/integrations/atlassian/client:client_py",
        "//services/integrations/github/processor/client:client_py",
        "//services/integrations/glean/client:client_py",
        "//services/integrations/linear/client:client_py",
        "//services/integrations/notion/client:client_py",
        "//services/integrations/supabase/client:client_py",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/grpc/auth:service_auth_interceptor",
        "//services/lib/grpc/auth:service_token_auth",
        "//services/lib/grpc/metrics",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight/publisher:publisher_py",
        requirement("grpcio-health-checking"),
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("opentelemetry-instrumentation-grpc"),
        requirement("prometheus-client"),
        requirement("protobuf"),
    ],
)

pytest_test(
    name = "codebase_retrieval_agent_test",
    srcs = ["codebase_retrieval_agent_test.py"],
    deps = [
        ":agents_handler",
        "//services/agents:tool",
    ],
)

pytest_test(
    name = "edit_file_agent_test",
    srcs = ["edit_file_agent_test.py"],
    deps = [
        ":agents_handler",
        "//services/agents:tool",
    ],
)

py_library(
    name = "agents_handler",
    srcs = [
        "agents_handler.py",
        "agents_tools.py",
        "codebase_retrieval_agent.py",
        "edit_file_agent.py",
    ],
    visibility = [
        # NOTE(arun): The agent integration test uses the tools defined in this package.
        "//services/test:__subpackages__",
    ],
    deps = [
        "//base/prompt_format_chat",
        "//base/prompt_format_retrieve:prompt_formatter",
        "//base/third_party_clients:clients",
        "//services/agents:agents_py_proto",
        "//services/agents:tool",
        "//services/api_proxy:model_finder_py_proto",
        "//services/api_proxy/client:grpc_client",
        "//services/chat_host:chat_proto_util",
        "//services/content_manager/client",
        "//services/edit_host:client",
        "//services/integrations/atlassian/agent_tools:atlassian_agent_tools",
        "//services/integrations/github/agent_tools:config",
        "//services/integrations/github/agent_tools:github_api_tool",
        "//services/integrations/github/processor/client:client_py",
        "//services/integrations/glean/agent_tools:glean_tools",
        "//services/integrations/google_search/agent_tools:google_search_agent_tools",
        "//services/integrations/linear/agent_tools:linear_agent_tools",
        "//services/integrations/notion/agent_tools:notion_agent_tools",
        "//services/integrations/notion/client:client_py",
        "//services/integrations/supabase/agent_tools:supabase_tool",
        "//services/integrations/supabase/client:client_py",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
        "//services/lib/retrieval:retriever",
        "//services/lib/retrieval:retriever_factory",
        "//services/request_insight/publisher:publisher_py",
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("jsonschema"),
        requirement("opentelemetry-api"),
        requirement("prometheus-client"),
    ],
)

pytest_test(
    name = "agents_handler_test",
    srcs = [
        "agents_handler_test.py",
    ],
    deps = [
        ":agents_handler",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":agents_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    cloud = ["GCP_US_CENTRAL1_DEV"],
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/chat_host/server:anthropic-lib",
        "//services/deploy:chatanol-qwen-v1-1_lib",
        "//services/deploy:commit-chatanol-v1_lib",
        "//services/deploy:endpoints",
        "//services/deploy:model_deployment_lib",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

# For spot-checking
jsonnet_to_json(
    name = "metadata_json",
    src = "METADATA.jsonnet",
    outs = ["METADATA.json"],
    # This shouldn't be directly consumed by any other targets.
    visibility = [],
    deps = [
        "//deploy/tenants:namespaces",
    ],
)
