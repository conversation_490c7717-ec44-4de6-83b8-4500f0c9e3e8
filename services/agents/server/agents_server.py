"""Server for the Agents service."""

import argparse
import logging
import os
import pathlib
import threading
import typing
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

import grpc
import opentelemetry
import opentelemetry.instrumentation.grpc
import prometheus_client
import structlog
from dataclasses_json import dataclass_json
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection

import base.feature_flags
import base.tracing
import services.lib.grpc.tls_config.tls_config as tls_config
from base.logging.struct_logging import setup_struct_logging
from base.python.grpc import client_options
from base.python.signal_handler.signal_handler import <PERSON>fulSignalHand<PERSON>
from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient
from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from base.third_party_clients.google_genai_client import GoogleGenaiClient
from base.third_party_clients.third_party_model_client import ThirdPartyModelClient
from services.agents import agents_pb2, agents_pb2_grpc
from services.agents.server.agents_handler import <PERSON><PERSON><PERSON><PERSON>
from services.agents.server.agents_tools import Agent<PERSON>oolsConfig, create_tools
from services.agents.server.edit_file_agent import EditClientProvider
from services.api_proxy.client import grpc_client as model_finder_client
from services.chat_host.server.chat_third_party_handler import AnthropicMultiClient
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.integrations.atlassian.client.client import AtlassianClient
from services.integrations.github.processor.client.client import GithubProcessorClient
from services.integrations.glean.client.client import GleanClient
from services.integrations.linear.client.client import LinearClient
from services.integrations.notion.client.client import NotionClient
from services.integrations.supabase.client.client import SupabaseClient
from services.lib.grpc.auth.service_auth_interceptor import (
    ServiceAuthInterceptor,
    get_auth_info_from_grpc_context,
)
from services.lib.grpc.auth.service_token_auth import (
    GrpcPublicKeySource,
    ServiceTokenAuth,
)
from services.lib.grpc.metrics.metrics import MetricsServerInterceptor
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval import retriever_factory
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.token_exchange.client import client as token_exchange_client

log = structlog.get_logger()
tracer = base.tracing.setup_opentelemetry()


@dataclass_json
@dataclass(frozen=True)
class ThirdPartyModelConfig:
    """Config for each chat model."""

    anthropic_api_key_path: str

    client_type: str
    """ Type of third party client, e.g. 'vertexai' """

    model_name: str

    temperature: float
    """ (default) temperature """

    max_output_tokens: int
    """ (default) max output tokens """

    gcp_project_id: str | None = None

    gcp_region: str | None = None


@dataclass_json
@dataclass
class AuthConfig:
    """Configuration for the token authentication."""

    # the endpoint for the token exchange service
    token_exchange_endpoint: str


@dataclass_json
@dataclass
class Config:
    """Configuration for the example server."""

    # the port the grpc server will listen on
    grpc_port: int

    # the path to the feature flag sdk key
    feature_flags_sdk_key_path: typing.Optional[str]

    # the endpoint for the dynamic feature flags service or None if not used
    dynamic_feature_flags_endpoint: typing.Optional[str]

    # the configuration for the token authentication
    auth_config: AuthConfig

    # Config for the chat model used for LLM generation requests
    # and agentic tool implementations
    # Must support tool use (i.e. be an Anthropic impl at time
    # of writing)
    chat_model_configs: dict[str, ThirdPartyModelConfig]
    default_chat_model_name: str

    # Config for retriever(s)
    codebase_retrieval: retriever_factory.RetrievalConfig
    commit_retrieval: retriever_factory.RetrievalConfig

    # Config for the atlassian client
    atlassian_endpoint: str

    # Config for the github processor client
    github_processor_endpoint: str

    # Config for the linear client
    linear_endpoint: str

    # Config for the content manager client
    content_manager_endpoint: str

    model_finder_endpoint: str

    # Config for the notion client
    notion_endpoint: str

    # Config for the supabase client
    supabase_endpoint: str

    # Config for the glean client
    glean_endpoint: str

    # Configuration for all of the server-side tools this agent has access to
    agent_tools_config: AgentToolsConfig

    # TLS configuration for the central clients
    central_client_mtls: typing.Optional[tls_config.ClientConfig] = None

    # TLS configuration for the client to talk to GRPC services in the same namespace
    client_mtls: typing.Optional[tls_config.ClientConfig] = None

    # TLS configuration for the server
    server_mtls: typing.Optional[tls_config.ServerConfig] = None

    # Grace period for the server to shutdown
    shutdown_grace_period_s: int = 25

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )


class AgentsServicer(agents_pb2_grpc.AgentsServicer):
    """AgentsServicer RPC server."""

    def __init__(
        self,
        config: Config,
        handler: AgentsHandler,
    ):
        self.config = config
        self.handler = handler

    def LLMGenerate(
        self,
        request: agents_pb2.LLMGenerateRequest,
        context: grpc.ServicerContext,
    ) -> agents_pb2.LLMGenerateResponse:
        auth_info = get_auth_info_from_grpc_context(context)
        request_context = RequestContext.from_grpc_context(context)
        with request_context.with_context_logging(), auth_info.with_context_logging():
            return self.handler.llm_generate(request, request_context, auth_info)

    def CodebaseRetrieval(
        self,
        request: agents_pb2.CodebaseRetrievalRequest,
        context: grpc.ServicerContext,
    ) -> agents_pb2.CodebaseRetrievalResponse:
        auth_info = get_auth_info_from_grpc_context(context)
        request_context = RequestContext.from_grpc_context(context)
        with request_context.with_context_logging(), auth_info.with_context_logging():
            return self.handler.codebase_retrieval(request, request_context, auth_info)

    def EditFile(
        self,
        request: agents_pb2.EditFileRequest,
        context: grpc.ServicerContext,
    ) -> agents_pb2.EditFileResponse:
        auth_info = get_auth_info_from_grpc_context(context)
        request_context = RequestContext.from_grpc_context(context)
        with request_context.with_context_logging(), auth_info.with_context_logging():
            return self.handler.edit_file(request, request_context, auth_info)

    def CheckToolSafety(
        self,
        request: agents_pb2.CheckToolSafetyRequest,
        context: grpc.ServicerContext,
    ) -> agents_pb2.CheckToolSafetyResponse:
        auth_info = get_auth_info_from_grpc_context(context)
        request_context = RequestContext.from_grpc_context(context)
        with request_context.with_context_logging(), auth_info.with_context_logging():
            return self.handler.check_tool_safety(request, request_context, auth_info)

    def ListRemoteTools(
        self,
        request: agents_pb2.ListRemoteToolsRequest,
        context: grpc.ServicerContext,
    ) -> agents_pb2.ListRemoteToolsResponse:
        auth_info = get_auth_info_from_grpc_context(context)
        request_context = RequestContext.from_grpc_context(context)
        with request_context.with_context_logging(), auth_info.with_context_logging():
            return self.handler.list_remote_tools(
                request,
                RequestContext.from_grpc_context(context),
                get_auth_info_from_grpc_context(context),
            )

    def RunRemoteTool(
        self,
        request: agents_pb2.RunRemoteToolRequest,
        context: grpc.ServicerContext,
    ) -> agents_pb2.RunRemoteToolResponse:
        auth_info = get_auth_info_from_grpc_context(context)
        request_context = RequestContext.from_grpc_context(context)
        with request_context.with_context_logging(), auth_info.with_context_logging():
            return self.handler.run_remote_tool(
                request,
                RequestContext.from_grpc_context(context),
                get_auth_info_from_grpc_context(context),
            )

    def RevokeToolAccess(
        self,
        request: agents_pb2.RevokeToolAccessRequest,
        context: grpc.ServicerContext,
    ) -> agents_pb2.RevokeToolAccessResponse:
        auth_info = get_auth_info_from_grpc_context(context)
        request_context = RequestContext.from_grpc_context(context)
        with request_context.with_context_logging(), auth_info.with_context_logging():
            status = self.handler.revoke_tool_access(
                request,
                request_context,
                auth_info,
            )
            return agents_pb2.RevokeToolAccessResponse(status=status)

    def TestToolConnection(
        self,
        request: agents_pb2.TestToolConnectionRequest,
        context: grpc.ServicerContext,
    ) -> agents_pb2.TestToolConnectionResponse:
        auth_info = get_auth_info_from_grpc_context(context)
        request_context = RequestContext.from_grpc_context(context)
        with request_context.with_context_logging(), auth_info.with_context_logging():
            status = self.handler.test_tool_connection(
                request,
                request_context,
                auth_info,
            )
            return agents_pb2.TestToolConnectionResponse(status=status)


# Code duplication of the same in chat host; not sure these ever need to differ,
# so move to base/third_party_clients
def _make_chat_client(config: ThirdPartyModelConfig) -> ThirdPartyModelClient:
    if config.client_type == "anthropic_vertexai":
        client = AnthropicVertexAiClient(
            project_id=config.gcp_project_id,
            region=config.gcp_region,
            model_name=config.model_name,
            temperature=config.temperature,
            max_output_tokens=config.max_output_tokens,
        )
    elif config.client_type == "anthropic_load_balanced":
        # Use AnthropicMultiClient to get region load-balancing and direct/vertex load-balancing
        anthropic_api_key = (
            pathlib.Path(config.anthropic_api_key_path)
            .read_text(encoding="utf-8")
            .strip()
        )
        client = AnthropicMultiClient(
            api_key=anthropic_api_key,
            project_id=config.gcp_project_id,
            region=config.gcp_region,
            model_name=config.model_name,
            temperature=config.temperature,
            max_output_tokens=config.max_output_tokens,
        )
    elif config.client_type == "anthropic_direct":
        anthropic_api_key = (
            pathlib.Path(config.anthropic_api_key_path)
            .read_text(encoding="utf-8")
            .strip()
        )
        client = AnthropicDirectClient(
            api_key=anthropic_api_key,
            model_name=config.model_name,
            temperature=config.temperature,
            max_output_tokens=config.max_output_tokens,
        )
    elif config.client_type == "google_genai":
        client = GoogleGenaiClient(
            project_id=config.gcp_project_id,
            region=config.gcp_region,
            model_name=config.model_name,
            temperature=config.temperature,
            max_output_tokens=config.max_output_tokens,
        )
    else:
        raise ValueError(f"Unknown model type: {config.client_type}")
    return client


def _make_content_manager_client(config: Config) -> ContentManagerClient:
    """Returns a content manager client."""
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in config.content_manager_endpoint
        )
    )
    return ContentManagerClient.create_for_endpoint(
        config.content_manager_endpoint,
        credentials=tls_config.get_client_tls_creds(config.client_mtls),
        options=options,
    )


def _make_model_finder_client(config: Config):
    """Returns a model finder client."""
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in config.model_finder_endpoint
        )
    )
    return model_finder_client.setup_stub(
        config.model_finder_endpoint,
        tls_config.get_client_tls_creds(config.client_mtls),
        options,
    )


def _make_handler(
    config: Config, ri_publisher: RequestInsightPublisher
) -> AgentsHandler:
    content_manager_client = _make_content_manager_client(config)
    codebase_retriever = retriever_factory.create_retriever(
        config.codebase_retrieval,
        content_manager_client,
        tls_config.get_client_tls_creds(config.client_mtls),
        # TODO: revisit when adding request insights
        ri_builder=None,
        search_timeout_ms=5000,
    )
    commit_retriever = retriever_factory.create_retriever(
        config.commit_retrieval,
        content_manager_client,
        tls_config.get_client_tls_creds(config.client_mtls),
        # TODO: revisit when adding request insights
        ri_builder=None,
        search_timeout_ms=5000,
    )
    model_finder = _make_model_finder_client(config)
    edit_client_provider = EditClientProvider(
        model_finder, tls_config.get_client_tls_creds(config.client_mtls)
    )
    chat_clients = {
        k: _make_chat_client(v) for k, v in config.chat_model_configs.items()
    }
    linear_client = LinearClient(
        config.linear_endpoint,
        tls_config.get_client_tls_creds(config.client_mtls),
    )
    notion_client = NotionClient(
        config.notion_endpoint,
        tls_config.get_client_tls_creds(config.client_mtls),
    )
    atlassian_client = AtlassianClient(
        config.atlassian_endpoint,
        tls_config.get_client_tls_creds(config.client_mtls),
    )
    github_processor_client = GithubProcessorClient(
        config.github_processor_endpoint,
        tls_config.get_client_tls_creds(config.client_mtls),
    )
    supabase_client = SupabaseClient(
        config.supabase_endpoint,
        tls_config.get_client_tls_creds(config.client_mtls),
    )
    glean_client = GleanClient(
        config.glean_endpoint,
        tls_config.get_client_tls_creds(config.client_mtls),
    )
    return AgentsHandler(
        ri_publisher,
        chat_clients,
        config.default_chat_model_name,
        codebase_retriever,
        commit_retriever,
        edit_client_provider,
        create_tools(
            config.agent_tools_config,
            llm_client=chat_clients[config.default_chat_model_name],
            linear_client=linear_client,
            notion_client=notion_client,
            atlassian_client=atlassian_client,
            github_processor_client=github_processor_client,
            supabase_client=supabase_client,
            glean_client=glean_client,
        ),
    )


def _serve(
    config: Config,
    ri_publisher: RequestInsightPublisher,
    shutdown_event: threading.Event,
):
    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)
    custom_endpoint = None
    if config.dynamic_feature_flags_endpoint is not None:
        custom_endpoint = config.dynamic_feature_flags_endpoint

    context = base.feature_flags.Context.setup(path, custom_endpoint)
    base.feature_flags.set_global_context(context)

    namespace = os.environ["POD_NAMESPACE"]
    token_client = token_exchange_client.GrpcTokenExchangeClient.create(
        config.auth_config.token_exchange_endpoint,
        namespace,
        tls_config.get_client_tls_creds(config.central_client_mtls),
    )
    service_auth = ServiceTokenAuth(
        GrpcPublicKeySource(token_client),
        required_scopes=["CONTENT_R"],
    )
    auth_interceptor = ServiceAuthInterceptor(service_auth)
    server = grpc.server(
        ThreadPoolExecutor(max_workers=20),
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(),
            MetricsServerInterceptor(),
            auth_interceptor,
        ],
    )

    # Reply to health check RPCs
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    agents_pb2_grpc.add_AgentsServicer_to_server(
        AgentsServicer(config, _make_handler(config, ri_publisher)),
        server,
    )
    service_names = (
        agents_pb2.DESCRIPTOR.services_by_name["Agents"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)

    server_credentials = tls_config.get_server_tls_creds(config.server_mtls)
    if server_credentials:
        actual_port = server.add_secure_port(
            f"[::]:{config.grpc_port}", server_credentials
        )
    else:
        actual_port = server.add_insecure_port(f"[::]:{config.grpc_port}")
    server.start()
    logging.info("Listening on %s", actual_port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def main():
    # Set up the signal handler
    # This will catch SIGTERM and SIGINT and exit gracefully
    standard_handler = GracefulSignalHandler()

    # Set up the logging
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    RequestInsightPublisher.add_publisher_arguments(parser)
    args = parser.parse_args()
    logging.info("Args %s", args)

    config = Config.load_config(args.config_file)
    logging.info("Config %s", config)

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()
    ri_publisher = RequestInsightPublisher.create_from_args(args)

    prometheus_client.start_http_server(9090)

    _serve(config, ri_publisher, standard_handler.get_shutdown_event())


if __name__ == "__main__":
    main()
