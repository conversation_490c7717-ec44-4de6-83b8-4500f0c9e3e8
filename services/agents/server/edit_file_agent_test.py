from copy import deepcopy
import pytest
from typing import Generator
from unittest.mock import Mock
from base.prompt_format.common import ChatRequestNodeType
from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
    ToolUseResponse,
)
from services.edit_host.edit_pb2 import (
    InstructionAggregateResponse,
    ReplaceText,
)
from services.agents.agents_pb2 import EditFileRequest
from services.agents.server.edit_file_agent import (
    EditFileAgent,
    FileEditClient,
    EditClientProvider,
)
import services.agents.server.edit_file_agent as edit_file_agent
from services.lib.request_context.request_context import RequestContext
from services.lib.grpc.auth.service_auth import AuthInfo
import base.feature_flags
from services.api_proxy import model_finder_pb2
from services.deploy.model_instance import model_instance_pb2
from services.deploy.model_instance.model_instance_pb2 import (
    ModelInstanceConfig,
    ModelType,
    EditModelConfig,
    ChatModelConfig,
)


@pytest.fixture()
def feature_flags_context() -> (
    Generator[base.feature_flags.LocalFeatureFlagSetter, None, None]
):
    yield from base.feature_flags.feature_flag_fixture()


def mock_model_finder():
    model_finder_resp = model_finder_pb2.GetModelsResponse(
        models=[
            ModelInstanceConfig(
                name="edit-A",
                model_type=ModelType.EDIT,
                edit=EditModelConfig(
                    edit_endpoint="hostA:1234",
                    model_priority=0,
                    suggested_prefix_char_count=0,
                    suggested_suffix_char_count=0,
                ),
            ),
            ModelInstanceConfig(
                name="edit-high-priority",
                model_type=ModelType.EDIT,
                edit=EditModelConfig(
                    edit_endpoint="hostC:1234",
                    model_priority=20,
                    suggested_prefix_char_count=0,
                    suggested_suffix_char_count=0,
                ),
            ),
            ModelInstanceConfig(
                name="edit-B",
                model_type=ModelType.EDIT,
                edit=EditModelConfig(
                    edit_endpoint="hostB:1234",
                    model_priority=1,
                    suggested_prefix_char_count=0,
                    suggested_suffix_char_count=0,
                ),
            ),
            ModelInstanceConfig(
                name="chat-highest-priority",
                model_type=ModelType.CHAT,
                chat=ChatModelConfig(
                    chat_endpoint="chat:1234",
                    model_priority=10000,
                    suggested_prefix_char_count=0,
                    suggested_suffix_char_count=0,
                ),
            ),
        ]
    )
    model_finder = Mock()
    model_finder.GetGenerationModels.return_value = model_finder_resp
    return model_finder


def test_default_model_selection():
    """Model selection based on priority when no feature flag is set."""
    model_finder = mock_model_finder()

    provider = EditClientProvider(model_finder, None)
    auth_info = AuthInfo(
        tenant_id="test_tenant_id",
        tenant_name="test_tenant",
        shard_namespace="test_shard_namespace",
        cloud="test_cloud",
    )
    request_context = RequestContext.create()

    client = provider.get_flag_based_client(auth_info, request_context)
    assert client.model_name == "edit-high-priority"
    model_finder.GetGenerationModels.assert_called_once()
    model_finder.reset_mock()

    # When not using feature flag, the model finder needs to be consulted
    # every call
    client = provider.get_flag_based_client(auth_info, request_context)
    assert client.model_name == "edit-high-priority"
    model_finder.GetGenerationModels.assert_called_once()

    # In case deployed models change
    model_finder.GetGenerationModels.return_value = model_finder_pb2.GetModelsResponse(
        models=[
            ModelInstanceConfig(
                name="edit-A",
                model_type=ModelType.EDIT,
                edit=EditModelConfig(
                    edit_endpoint="hostA:1234",
                    model_priority=0,
                    suggested_prefix_char_count=0,
                    suggested_suffix_char_count=0,
                ),
            ),
        ]
    )
    client = provider.get_flag_based_client(auth_info, request_context)
    assert client.model_name == "edit-A"


def test_feature_flag_model_selection(feature_flags_context):
    """Test that EditClientProvider selects models based on feature flags."""
    model_finder = mock_model_finder()

    provider = EditClientProvider(model_finder, None)
    auth_info = AuthInfo(
        tenant_id="test_tenant_id",
        tenant_name="test_tenant",
        shard_namespace="test_shard_namespace",
        cloud="test_cloud",
    )
    request_context = RequestContext.create()

    # We're not testing construction of edit client stub here, so mock it
    # to observe the arguments
    provider.construct_client = Mock()

    feature_flags_context.set_flag(edit_file_agent._SMART_PASTE_MODEL, "edit-A")
    client = provider.get_flag_based_client(auth_info, request_context)
    assert client.model_name == "edit-A"
    model_finder.GetGenerationModels.assert_called_once()
    model_finder.reset_mock()
    provider.construct_client.assert_called_once_with("edit-A", "hostA:1234")
    provider.construct_client.reset_mock()

    # With feature flag, a cached client matching the flag can be used
    client2 = provider.get_flag_based_client(auth_info, request_context)
    assert client2.model_name == "edit-A"
    assert client is client2
    model_finder.GetGenerationModels.assert_not_called()
    provider.construct_client.assert_not_called()

    # Switch model choice
    feature_flags_context.set_flag(edit_file_agent._SMART_PASTE_MODEL, "edit-B")
    client = provider.get_flag_based_client(auth_info, request_context)
    assert client.model_name == "edit-B"
    model_finder.GetGenerationModels.assert_called_once()
    provider.construct_client.assert_called_once_with("edit-B", "hostB:1234")

    # Invalid model
    feature_flags_context.set_flag(
        edit_file_agent._SMART_PASTE_MODEL, "non-existent-model"
    )
    with pytest.raises(ValueError, match="Model non-existent-model not found"):
        provider.get_flag_based_client(auth_info, request_context)


def test_no_edit_models_error():
    """Test error handling when no edit models are available."""
    model_finder = mock_model_finder()
    model_finder.GetGenerationModels.return_value = model_finder_pb2.GetModelsResponse(
        models=[]
    )
    provider = EditClientProvider(model_finder, None)

    auth_info = AuthInfo(
        tenant_id="test_tenant_id",
        tenant_name="test_tenant",
        shard_namespace="test_shard_namespace",
        cloud="test_cloud",
    )
    request_context = RequestContext.create()
    with pytest.raises(Exception, match="No edit models found"):
        provider.get_flag_based_client(auth_info, request_context)


def test_edit_file_agent_multiple_edits():
    """Test that EditFileAgent handles multiple edit cycles correctly based on review feedback."""
    # Mock dependencies
    edit_client = Mock()
    llm_client = Mock(spec=ThirdPartyModelClient)
    request_context = RequestContext.create()

    # Setup test data
    initial_content = "def add(a, b):\n    return a + b"
    first_edit = InstructionAggregateResponse(
        text="",
        replace_text=[
            ReplaceText(
                old_text="",
                text="def subtract(a, b):\n    return a - b",
                start_line=3,
                end_line=4,
                sequence_id=0,
            )
        ],
    )
    final_edit = InstructionAggregateResponse(
        text="",
        replace_text=[
            ReplaceText(
                old_text="",
                text="def multiply(a, b):\n    return a * b",
                start_line=5,
                end_line=6,
                sequence_id=0,
            )
        ],
    )

    # Mock edit_client responses
    edit_client.instruction.side_effect = [
        first_edit,  # First edit adds subtract
        final_edit,  # Second edit adds multiply
    ]
    edit_client_provider = Mock()
    edit_client_provider.get_flag_based_client.return_value = FileEditClient(
        edit_client, ""
    )

    # Setup LLM responses for the conversation flow
    llm_responses = [
        # First response - initial edit suggestion
        [
            ThirdPartyModelResponse(
                text="I'll add the subtract function first.",
                tool_use=ToolUseResponse(
                    tool_name="edit_file_v2",
                    tool_use_id="toolu_1",
                    input={
                        "edit_plan": "Add subtract function",
                        "suggested_edit": "def subtract(a, b):\n    return a - b",
                    },
                ),
            )
        ],
        # Second response - review and request for another edit
        [
            ThirdPartyModelResponse(
                text="The subtract function looks good, but we still need to add the multiply function.",
                tool_use=ToolUseResponse(
                    tool_name="edit_file_v2",
                    tool_use_id="toolu_2",
                    input={
                        "edit_plan": "Add multiply function",
                        "suggested_edit": "def multiply(a, b):\n    return a * b",
                    },
                ),
            )
        ],
        # Third response - final review and completion
        [
            ThirdPartyModelResponse(
                text="All the requested functions have been added correctly."
            ),
            ThirdPartyModelResponse(
                text="",
                tool_use=ToolUseResponse(
                    tool_name="complete",
                    tool_use_id="toolu_3",
                    input={"answer": "All functions added successfully"},
                ),
            ),
        ],
    ]

    # This function is called repeatedly with mutable arguments; to
    # know what their value was at the time of each call, deep copy
    class CopyingMock(Mock):
        def __call__(self, *args, **kwargs):
            args = deepcopy(args)
            kwargs = deepcopy(kwargs)
            return super().__call__(*args, **kwargs)

    llm_client.generate_response_stream = CopyingMock()
    llm_client.generate_response_stream.side_effect = llm_responses

    # Create the agent
    agent = EditFileAgent(
        client_provider=edit_client_provider,
        ri_publisher=Mock(),
    )

    # Create and run the request
    request = EditFileRequest(
        file_path="test.py",
        edit_summary="Add arithmetic functions",
        detailed_edit_description="Add functions for subtraction and multiplication",
        file_contents=initial_content,
    )

    auth_info = AuthInfo(
        tenant_id="test_tenant_id",
        tenant_name="test_tenant",
        shard_namespace="test_shard_namespace",
        cloud="test_cloud",
    )

    response = agent.run(request, llm_client, request_context, auth_info=auth_info)

    # Verify the response contains the final edit
    assert (
        response.modified_file_contents
        == initial_content
        + first_edit.replace_text[0].text
        + final_edit.replace_text[0].text
    )

    # Verify edit_client was called twice with correct parameters
    assert edit_client.instruction.call_count == 2

    first_call_args = edit_client.instruction.call_args_list[0]
    # Access the first positional argument (request)
    request_arg = first_call_args[0][0]
    context_arg = first_call_args[0][1]

    assert request_arg.chat_history[0].request_message == "Add subtract function"
    assert request_arg.target_file_path == "test.py"
    assert request_arg.target_file_content == initial_content
    assert request_arg.code_block == "def subtract(a, b):\n    return a - b"
    assert context_arg == request_context

    # Second call - adding multiply function
    second_call_args = edit_client.instruction.call_args_list[1]
    request_arg = second_call_args[0][0]
    context_arg = second_call_args[0][1]

    assert request_arg.chat_history[0].request_message == "Add multiply function"
    assert request_arg.target_file_path == "test.py"
    assert (
        request_arg.target_file_content
        == initial_content + first_edit.replace_text[0].text
    )
    assert request_arg.code_block == "def multiply(a, b):\n    return a * b"
    assert context_arg == request_context

    # Verify LLM client was called three times (initial edit, review+second edit, final review)
    assert llm_client.generate_response_stream.call_count == 3
    final_call = llm_client.generate_response_stream.call_args_list[-1]
    # Each call should be building up chat history to maintain context
    history = final_call.kwargs["chat_history"]
    assert len(history) == 2
    assert (
        f"Edit the file {request.file_path} according to the following instructions:"
        in history[0].request_message
    )
    assert any(
        node.type == ChatRequestNodeType.TOOL_RESULT
        and node.tool_result_node.tool_use_id == "toolu_1"
        for node in history[1].request_message
    )
    assert any(
        node.type == ChatRequestNodeType.TEXT
        and "Please review" in node.text_node.content
        for node in history[1].request_message
    )


def test_max_turns():
    """Test that EditFileAgent performs at most max_turns rounds of edit and review"""
    edit = Mock()
    edit_client_provider = Mock()
    edit_client_provider.get_flag_based_client.return_value = FileEditClient(edit, "")
    llm = Mock()
    agent = EditFileAgent(client_provider=edit_client_provider, ri_publisher=Mock())

    use_edit_tool = ThirdPartyModelResponse(
        text="",
        tool_use=ToolUseResponse(
            tool_name="edit_file_v2",
            tool_use_id="",
            input={
                "edit_plan": "Add multiply function",
                "suggested_edit": "def multiply(a, b):\n    return a * b",
            },
        ),
    )
    use_complete_tool = ThirdPartyModelResponse(
        text="",
        tool_use=ToolUseResponse(
            tool_name="complete", input={"answer": "done"}, tool_use_id=""
        ),
    )
    # Mock the LLM to always request an edit
    llm.generate_response_stream.return_value = [use_edit_tool]
    edit.instruction.return_value = InstructionAggregateResponse(text="")

    request = EditFileRequest(
        file_path="test.py",
        edit_summary="test",
        detailed_edit_description="test",
        file_contents="test",
    )
    request_context = RequestContext.create()
    auth_info = AuthInfo(
        tenant_id="test_tenant_id",
        tenant_name="test_tenant",
        shard_namespace="test_shard_namespace",
        cloud="test_cloud",
    )

    for max_turns in [1, 3, 8]:
        agent.max_turns = max_turns
        result = agent.run(request, llm, request_context, auth_info=auth_info)
        assert result.is_error
        # The first LLM call can only edit; each additional call serves as both
        # an opportunity to review the edit and to request another edit
        assert llm.generate_response_stream.call_count == max_turns + 1
        assert edit.instruction.call_count == max_turns
        llm.generate_response_stream.reset_mock()
        edit.instruction.reset_mock()

    llm.generate_response_stream.reset_mock(return_value=True)
    llm.generate_response_stream.side_effect = [[use_edit_tool]] * 3 + [
        [use_complete_tool]
    ]
    agent.max_turns = 3
    result = agent.run(request, llm, request_context, auth_info=auth_info)
    assert not result.is_error
