import json
from unittest.mock import Mock, patch

from pydantic import ValidationError

from services.agents.agents_pb2 import (
    CodebaseRetrievalRequest,
    CodebaseRetrievalResponse,
    RemoteToolId,
    RunRemoteToolRequest,
    RunRemoteToolResponse,
)
from services.agents.server.agents_handler import <PERSON>Hand<PERSON>
from services.agents.tool import Tool, ToolAuthenticationError, ToolNotAvailableError
from services.integrations.google_search.agent_tools.web_search_tool import (
    WebSearchConfig,
    WebSearchTool,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext


def test_run_remote_tool_success():
    # Mock dependencies
    ri_publisher = Mock()
    llm_client = Mock()
    retriever = Mock()

    # Create a mock tool that returns a simple response
    mock_tool = Mock(spec=Tool)
    mock_tool.id = RemoteToolId.WEB_SEARCH
    mock_tool.name = "web_search"
    mock_tool.run.return_value = "Search results for query"

    # Initialize handler with mock tool
    handler = Agents<PERSON>andler(
        ri_publisher=ri_publisher,
        llm_clients={"test_llm": llm_client},
        default_llm_client_name="test_llm",
        codebase_retriever=retriever,
        commit_retriever=Mock(),
        edit_client_provider=Mock(),
        tools=[mock_tool],
    )

    # Create test request
    request = RunRemoteToolRequest(
        tool_id=RemoteToolId.WEB_SEARCH,
        tool_input_json=json.dumps({"query": "test query"}),
    )

    # Create test context and auth info
    request_context = RequestContext.create()
    auth_info = AuthInfo(
        tenant_id="test-tenant",
        tenant_name="test",
        shard_namespace="test-namespace",
        cloud="test-cloud",
    )

    # Run the tool
    response = handler.run_remote_tool(request, request_context, auth_info)

    # Verify response
    assert isinstance(response, RunRemoteToolResponse)
    assert response.tool_output == "Search results for query"
    assert not response.is_error
    assert response.tool_result_message == ""

    # Verify tool was called with correct input
    mock_tool.run.assert_called_once()
    tool_input, _, _ = mock_tool.run.call_args[0]
    assert tool_input == {"query": "test query"}

    # Verify metrics and events were published
    assert (
        ri_publisher.publish_request_insight.call_count == 2
    )  # Request and response events


def test_run_remote_tool_not_found():
    # Initialize handler with no tools
    handler = AgentsHandler(
        ri_publisher=Mock(),
        llm_clients={"test_llm": Mock()},
        default_llm_client_name="test_llm",
        codebase_retriever=Mock(),
        commit_retriever=Mock(),
        edit_client_provider=Mock(),
        tools=[],
    )

    request = RunRemoteToolRequest(
        tool_id=RemoteToolId.WEB_SEARCH,
        tool_input_json="{}",
    )

    response = handler.run_remote_tool(
        request,
        RequestContext.create(),
        AuthInfo(
            tenant_id="test-tenant",
            tenant_name="test",
            shard_namespace="test-namespace",
            cloud="test-cloud",
        ),
    )

    assert response.is_error
    assert "Tool not found" in response.tool_result_message


def test_run_remote_tool_invalid_json():
    # Create mock tool
    mock_tool = Mock(spec=Tool)
    mock_tool.id = RemoteToolId.WEB_SEARCH
    mock_tool.name = "web_search"

    # Initialize handler
    handler = AgentsHandler(
        ri_publisher=Mock(),
        llm_clients={"test_llm": Mock()},
        default_llm_client_name="test_llm",
        codebase_retriever=Mock(),
        commit_retriever=Mock(),
        edit_client_provider=Mock(),
        tools=[mock_tool],
    )

    request = RunRemoteToolRequest(
        tool_id=RemoteToolId.WEB_SEARCH,
        tool_input_json="invalid json{",
    )

    response = handler.run_remote_tool(
        request,
        RequestContext.create(),
        AuthInfo(
            tenant_id="test-tenant",
            tenant_name="test",
            shard_namespace="test-namespace",
            cloud="test-cloud",
        ),
    )

    assert response.is_error
    assert "Invalid JSON input" in response.tool_result_message
    assert not mock_tool.run.called


def test_run_remote_tool_validation_error():
    # Create mock tool that raises ValidationError
    mock_tool = Mock(spec=Tool)
    mock_tool.id = RemoteToolId.WEB_SEARCH
    mock_tool.name = "web_search"

    def raise_validation_error(*args, **kwargs):
        # Let Pydantic naturally raise the ValidationError by validating invalid data
        WebSearchTool.input_model(invalid="input")  # type: ignore

    mock_tool.run.side_effect = raise_validation_error

    # Initialize handler
    handler = AgentsHandler(
        ri_publisher=Mock(),
        llm_clients={"test_llm": Mock()},
        default_llm_client_name="test_llm",
        codebase_retriever=Mock(),
        commit_retriever=Mock(),
        edit_client_provider=Mock(),
        tools=[mock_tool],
    )

    request = RunRemoteToolRequest(
        tool_id=RemoteToolId.WEB_SEARCH,
        tool_input_json='{"invalid": "input"}',
    )

    response = handler.run_remote_tool(
        request,
        RequestContext.create(),
        AuthInfo(
            tenant_id="test-tenant",
            tenant_name="test",
            shard_namespace="test-namespace",
            cloud="test-cloud",
        ),
    )

    assert response.is_error
    assert "Invalid tool input" in response.tool_result_message
    assert "field required" in response.tool_output.lower()
    assert "query" in response.tool_output


def test_run_remote_tool_auth_error():
    # Create mock tool that raises ToolAuthenticationError
    mock_tool = Mock(spec=Tool)
    mock_tool.id = RemoteToolId.WEB_SEARCH
    mock_tool.name = "web_search"
    mock_tool.run.side_effect = ToolAuthenticationError("Authentication failed")

    # Initialize handler
    handler = AgentsHandler(
        ri_publisher=Mock(),
        llm_clients={"test_llm": Mock()},
        default_llm_client_name="test_llm",
        codebase_retriever=Mock(),
        commit_retriever=Mock(),
        edit_client_provider=Mock(),
        tools=[mock_tool],
    )

    request = RunRemoteToolRequest(
        tool_id=RemoteToolId.WEB_SEARCH,
        tool_input_json='{"valid": "input"}',
    )

    response = handler.run_remote_tool(
        request,
        RequestContext.create(),
        AuthInfo(
            tenant_id="test-tenant",
            tenant_name="test",
            shard_namespace="test-namespace",
            cloud="test-cloud",
        ),
    )

    assert response.is_error
    assert "Tool authentication failed" in response.tool_result_message
    assert "Authentication failed" in response.tool_output


def test_run_remote_tool_not_available():
    # Create mock tool that raises ToolNotAvailableError
    mock_tool = Mock(spec=Tool)
    mock_tool.id = RemoteToolId.WEB_SEARCH
    mock_tool.name = "web_search"
    mock_tool.run.side_effect = ToolNotAvailableError("Tool is down")

    # Initialize handler
    handler = AgentsHandler(
        ri_publisher=Mock(),
        llm_clients={"test_llm": Mock()},
        default_llm_client_name="test_llm",
        codebase_retriever=Mock(),
        commit_retriever=Mock(),
        edit_client_provider=Mock(),
        tools=[mock_tool],
    )

    request = RunRemoteToolRequest(
        tool_id=RemoteToolId.WEB_SEARCH,
        tool_input_json='{"valid": "input"}',
    )

    response = handler.run_remote_tool(
        request,
        RequestContext.create(),
        AuthInfo(
            tenant_id="test-tenant",
            tenant_name="test",
            shard_namespace="test-namespace",
            cloud="test-cloud",
        ),
    )

    assert response.is_error
    assert "Tool not available" in response.tool_result_message
    assert "Tool is down" in response.tool_output


def test_run_remote_tool_unexpected_error():
    # Create mock tool that raises an unexpected error
    mock_tool = Mock(spec=Tool)
    mock_tool.id = RemoteToolId.WEB_SEARCH
    mock_tool.name = "web_search"
    mock_tool.run.side_effect = RuntimeError("Unexpected error")

    # Initialize handler
    handler = AgentsHandler(
        ri_publisher=Mock(),
        llm_clients={"test_llm": Mock()},
        default_llm_client_name="test_llm",
        codebase_retriever=Mock(),
        commit_retriever=Mock(),
        edit_client_provider=Mock(),
        tools=[mock_tool],
    )

    request = RunRemoteToolRequest(
        tool_id=RemoteToolId.WEB_SEARCH,
        tool_input_json='{"valid": "input"}',
    )

    response = handler.run_remote_tool(
        request,
        RequestContext.create(),
        AuthInfo(
            tenant_id="test-tenant",
            tenant_name="test",
            shard_namespace="test-namespace",
            cloud="test-cloud",
        ),
    )

    assert response.is_error
    assert "Tool execution error" in response.tool_result_message
    assert "Unexpected error" in response.tool_output


def test_codebase_retrieval_normal_operation():
    """Test normal codebase retrieval operation (both flags false)."""
    # Mock dependencies
    ri_publisher = Mock()
    llm_client = Mock()
    retriever = Mock()

    # Mock the retrieval agent
    mock_retrieval_agent = Mock()
    mock_retrieval_agent.run.return_value = CodebaseRetrievalResponse(
        formatted_retrieval="Codebase retrieval results"
    )

    # Initialize handler
    handler = AgentsHandler(
        ri_publisher=ri_publisher,
        llm_clients={"test_llm": llm_client},
        default_llm_client_name="test_llm",
        codebase_retriever=retriever,
        commit_retriever=Mock(),
        edit_client_provider=Mock(),
        tools=[],
    )

    # Create test request with both flags false (default behavior)
    request = CodebaseRetrievalRequest(
        information_request="Find test functions",
        dialog=[],
        blobs=[],
        max_output_length=1000,
        disable_codebase_retrieval=False,
        enable_commit_retrieval=False,
    )

    request_context = RequestContext.create()
    auth_info = AuthInfo(
        tenant_id="test-tenant",
        tenant_name="test",
        shard_namespace="test-namespace",
        cloud="test-cloud",
    )

    # Mock the retrieval agent creation
    with patch(
        "services.agents.server.agents_handler.CodebaseMultiRetrievalAgent"
    ) as mock_agent_class:
        mock_agent_class.return_value = mock_retrieval_agent

        # Run the method
        response = handler.codebase_retrieval(request, request_context, auth_info)

    # Verify response
    assert isinstance(response, CodebaseRetrievalResponse)
    assert response.formatted_retrieval == "Codebase retrieval results"

    # Verify the retrieval agent was created and called
    mock_agent_class.assert_called_once()
    mock_retrieval_agent.run.assert_called_once_with(
        request, llm_client, request_context, auth_info
    )


def test_codebase_retrieval_disabled():
    """Test codebase retrieval when disabled via flag."""
    # Mock dependencies
    ri_publisher = Mock()
    llm_client = Mock()
    retriever = Mock()

    # Initialize handler
    handler = AgentsHandler(
        ri_publisher=ri_publisher,
        llm_clients={"test_llm": llm_client},
        default_llm_client_name="test_llm",
        codebase_retriever=retriever,
        commit_retriever=Mock(),
        edit_client_provider=Mock(),
        tools=[],
    )

    # Create test request with codebase retrieval disabled
    request = CodebaseRetrievalRequest(
        information_request="Find test functions",
        dialog=[],
        blobs=[],
        max_output_length=1000,
        disable_codebase_retrieval=True,
        enable_commit_retrieval=False,
    )

    request_context = RequestContext.create()
    auth_info = AuthInfo(
        tenant_id="test-tenant",
        tenant_name="test",
        shard_namespace="test-namespace",
        cloud="test-cloud",
    )

    # Mock the retrieval agent creation to ensure it's not called
    with patch(
        "services.agents.server.agents_handler.CodebaseMultiRetrievalAgent"
    ) as mock_agent_class:
        # Run the method
        response = handler.codebase_retrieval(request, request_context, auth_info)

    # Verify response
    assert isinstance(response, CodebaseRetrievalResponse)
    assert (
        response.formatted_retrieval
        == "No retrieval was performed based on the provided flags."
    )

    # Verify the retrieval agent was NOT created
    mock_agent_class.assert_not_called()


def test_codebase_retrieval_commit_enabled():
    """Test commit retrieval when enabled via flag."""
    # Mock dependencies
    ri_publisher = Mock()
    llm_client = Mock()
    codebase_retriever = Mock()
    commit_retriever = Mock()

    # Mock the commit retrieval agent
    mock_commit_agent = Mock()
    mock_commit_agent.run.return_value = CodebaseRetrievalResponse(
        formatted_retrieval="Commit retrieval results"
    )

    # Initialize handler
    handler = AgentsHandler(
        ri_publisher=ri_publisher,
        llm_clients={"test_llm": llm_client},
        default_llm_client_name="test_llm",
        codebase_retriever=codebase_retriever,
        commit_retriever=commit_retriever,
        edit_client_provider=Mock(),
        tools=[],
    )

    # Create test request with commit retrieval enabled but codebase disabled
    request = CodebaseRetrievalRequest(
        information_request="Find test functions",
        dialog=[],
        blobs=[],
        max_output_length=1000,
        disable_codebase_retrieval=True,
        enable_commit_retrieval=True,
    )

    request_context = RequestContext.create()
    auth_info = AuthInfo(
        tenant_id="test-tenant",
        tenant_name="test",
        shard_namespace="test-namespace",
        cloud="test-cloud",
    )

    # Mock the SimpleRetrievalAgent for commit retrieval
    with patch(
        "services.agents.server.agents_handler.SimpleRetrievalAgent"
    ) as mock_agent_class:
        mock_agent_class.return_value = mock_commit_agent

        # Run the method
        response = handler.codebase_retrieval(request, request_context, auth_info)

    # Verify response contains commit retrieval results
    assert isinstance(response, CodebaseRetrievalResponse)
    assert response.formatted_retrieval == "Commit retrieval results"

    # Verify the commit retrieval agent was created and called
    mock_agent_class.assert_called_once_with(
        retriever=commit_retriever,
        ri_publisher=ri_publisher,
    )
    mock_commit_agent.run.assert_called_once_with(
        request, None, request_context, auth_info
    )


def test_codebase_retrieval_both_enabled():
    """Test when both codebase and commit retrieval are enabled."""
    # Mock dependencies
    ri_publisher = Mock()
    llm_client = Mock()
    codebase_retriever = Mock()
    commit_retriever = Mock()

    # Mock the retrieval agents
    mock_codebase_agent = Mock()
    mock_codebase_agent.run.return_value = CodebaseRetrievalResponse(
        formatted_retrieval="Codebase retrieval results"
    )

    mock_commit_agent = Mock()
    mock_commit_agent.run.return_value = CodebaseRetrievalResponse(
        formatted_retrieval="Commit retrieval results"
    )

    # Initialize handler
    handler = AgentsHandler(
        ri_publisher=ri_publisher,
        llm_clients={"test_llm": llm_client},
        default_llm_client_name="test_llm",
        codebase_retriever=codebase_retriever,
        commit_retriever=commit_retriever,
        edit_client_provider=Mock(),
        tools=[],
    )

    # Create test request with both enabled
    request = CodebaseRetrievalRequest(
        information_request="Find test functions",
        dialog=[],
        blobs=[],
        max_output_length=1000,
        disable_codebase_retrieval=False,
        enable_commit_retrieval=True,
    )

    request_context = RequestContext.create()
    auth_info = AuthInfo(
        tenant_id="test-tenant",
        tenant_name="test",
        shard_namespace="test-namespace",
        cloud="test-cloud",
    )

    # Mock both retrieval agents
    with (
        patch(
            "services.agents.server.agents_handler.CodebaseMultiRetrievalAgent"
        ) as mock_codebase_class,
        patch(
            "services.agents.server.agents_handler.SimpleRetrievalAgent"
        ) as mock_commit_class,
    ):
        mock_codebase_class.return_value = mock_codebase_agent
        mock_commit_class.return_value = mock_commit_agent

        # Run the method
        response = handler.codebase_retrieval(request, request_context, auth_info)

    # Verify response contains both codebase and commit results
    assert isinstance(response, CodebaseRetrievalResponse)
    assert (
        "Codebase retrieval resultsCommit retrieval results"
        == response.formatted_retrieval
    )

    # Verify both retrieval agents were created and called
    mock_codebase_class.assert_called_once_with(
        retriever=codebase_retriever,
        ri_publisher=ri_publisher,
    )
    mock_codebase_agent.run.assert_called_once_with(
        request, llm_client, request_context, auth_info
    )

    mock_commit_class.assert_called_once_with(
        retriever=commit_retriever,
        ri_publisher=ri_publisher,
    )
    mock_commit_agent.run.assert_called_once_with(
        request, None, request_context, auth_info
    )
