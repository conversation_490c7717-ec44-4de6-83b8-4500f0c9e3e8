import json
import random
import time
from collections import OrderedDict
from fnmatch import fnmatch
from functools import partial
from pathlib import Path
from textwrap import dedent
from typing import Any, Callable

import jsonschema
import opentelemetry.trace
import structlog
from prometheus_client import Counter

from base import feature_flags
from base.blob_names.python import blob_names
from base.prompt_format.common import (
    ChatRequestNodeType,
    ChatResultNodeType,
    PromptChunk,
)
from base.prompt_format_chat.lib.abstract_formatted_file import AbstractFormattedFile
from base.prompt_format_chat.lib.formatted_file_v2 import FormattedFileV2
from base.prompt_format_chat.lib.token_counter import RoughTokenCounter
from base.prompt_format_chat.prompt_formatter import Exchange
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from base.third_party_clients.anthropic_direct_client import (
    ResourceExhaustedRpcError as AnthropicResourceExhausted,
)
from base.third_party_clients.anthropic_direct_client import (
    UnavailableRpcError as AnthropicUnavailable,
)
from base.third_party_clients.third_party_model_client import (
    PromptCacheUsage,
    ThirdPartyModelClient,
    ToolChoice,
    ToolChoiceType,
    ToolDefinition,
    ToolUseResponse,
)
from services.agents.agents_pb2 import (
    CodebaseRetrievalRequest,
    CodebaseRetrievalResponse,
)
from services.agents.tool import Tool
from services.chat_host import chat_pb2
from services.chat_host.chat_proto_util import convert_exchange
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.retriever import (
    Blobs,
    RetrievalChunk,
    RetrievalInput,
    Retriever,
)
from services.lib.retrieval.retriever_request_insight_builder import (
    RetrieverRequestInsightBuilder,
)
from services.request_insight import request_insight_pb2
from services.request_insight.publisher import request_insight_publisher

logger = structlog.get_logger()
tracer = opentelemetry.trace.get_tracer(__name__)

_tool_use_counter = Counter(
    "au_retrieval_agent_tool_use_generation",
    "Counts of tool use generation results by the retrieval agent",
    # N.B. having tool_name in the label is only safe because this file uses
    # its own tools; not any provided by a user
    ["tool_name", "result"],
)


# Feature flag for adding line numbers to codebase retrieval output
_ADD_LINE_NUMBERS = feature_flags.BoolFlag("codebase_retrieval_add_line_numbers", False)


# Query generation not robust; for now we return this as a string,
# and let the calling agent decide whether to retry (which has been observed
# to succeed).
# Eventually, we will want to improve LLM robustness or retrieve something even
# if LLM fails to generate queries.
QUERY_GENERATION_FAILURE = CodebaseRetrievalResponse(
    formatted_retrieval="Retrieval failed. Please try again."
)

ABSOLUTE_MAX_CHARS = 80000
MAX_CHARS = feature_flags.IntFlag("codebase_retrieval_budget", 20000)

MODEL_CALLER = "agent-codebase-retrieval"


def _generate_tool_use(
    client: ThirdPartyModelClient,
    message: str,
    tool_param: ToolDefinition,
    record_prompt_cache_usage: Callable[[PromptCacheUsage], None],
) -> ToolUseResponse | None:
    """Generate a tool use from the LLM.

    Handle retry on "overloaded" error (assumes Anthropic today, later PR
    will produce generic errors regardless of underlying LLM provider)

    Args:
        message: The message to send to the LLM.
        tool_param: The tool parameter to use and force via tool_choice

    Returns:
        The tool use response, or None if no tool use was generated.
    """
    TRIES = 3
    retry_delay_sec = 3
    for attempt in range(1, TRIES + 1):
        with tracer.start_as_current_span("llm_generation"):
            try:
                response_stream = client.generate_response_stream(
                    model_caller=MODEL_CALLER,
                    cur_message=message,
                    tool_definitions=[tool_param],
                    tool_choice=ToolChoice(
                        type=ToolChoiceType.TOOL,
                        name=tool_param.name,
                        disable_parallel_tool_use=True,
                    ),
                    max_output_tokens=2048,
                )

                tool_use: ToolUseResponse | None = None
                for msg in response_stream:
                    if msg.prompt_cache_usage:
                        prompt_cache_usage = msg.prompt_cache_usage
                        record_prompt_cache_usage(prompt_cache_usage)
                    if msg.tool_use and tool_use is None:
                        tool_use = msg.tool_use

                return tool_use
            except (AnthropicUnavailable, AnthropicResourceExhausted) as e:
                # The other reasons do not really justify retrying
                retry = "overloaded" in e.message.lower() or "Rate limit" in e.message
                if retry and attempt < TRIES:
                    time.sleep(retry_delay_sec * random.uniform(0.8, 1.2))
                    retry_delay_sec *= 2
                    continue
                raise
    assert False, "Unreachable"


def _requests_from_tool_use(
    tool_use: ToolUseResponse | None, tool: Tool
) -> list[dict[str, Any]]:
    """Get the code section requests from the tool use response.

    Args:
        tool_use: The tool use response.

    Returns:
        The code section requests.
    """
    # Including tool name in metrics and logs here is safe as it's our own tool;
    # not from user; including the input or validation failure is not permissible.
    if tool_use is None:
        _tool_use_counter.labels(tool.name, "missing").inc()
        logger.error("No tool call to %s was made in the response", tool.name)
        return []
    try:
        jsonschema.validate(tool_use.input, tool.input_schema)
    except jsonschema.ValidationError:
        _tool_use_counter.labels(tool.name, "invalid").inc()
        logger.error("Tool call %s failed schema validation", tool.name)
        return []
    result = tool_use.input.get("code_section_requests", [])
    _tool_use_counter.labels(tool.name, "valid" if len(result) else "empty").inc()
    return result


def _retrieve(
    retriever: Retriever[ChatRetrieverPromptInput],
    request_description: str,
    request_path: str,
    request_contains_string: str,
    request_context: RequestContext,
    auth_info: AuthInfo,
    blobs: list[Blobs],
    max_chunks: int,
) -> list[RetrievalChunk]:
    """Perform retrieval for the current state."""
    query = ChatRetrieverPromptInput(
        prefix="",
        suffix="",
        path="",
        message=request_description,
        selected_code="",
        chat_history=[],
    )
    retrieval_input = RetrievalInput(
        prompt_input=query,
        blobs=blobs,
    )
    retrieval_result = retriever.retrieve(
        input_=retrieval_input,
        request_context=request_context,
        auth_info=auth_info,
    )
    chunks = retrieval_result.get_retrieved_chunks()
    filtered_chunks = []
    path_filtered_chunks = []
    string_filtered_chunks = []
    for chunk in chunks:
        if (
            chunk.path is not None
            and request_path != ""
            and not fnmatch(chunk.path, request_path)
            and not fnmatch("/" + chunk.path, request_path)
        ):
            path_filtered_chunks.append(chunk)
            continue  # Skip this chunk
        if request_contains_string != "" and request_contains_string not in chunk.text:
            # String containment is so restrictive that we make it a soft filter and just prioritize matches
            string_filtered_chunks.append(chunk)
            continue  # Skip this chunk
        filtered_chunks.append(chunk)  # Add the chunk to the filtered list

    # Combine the filtered chunks and scores, prioritizing fully matching, then string matching, then path matching
    combined_chunks = filtered_chunks + string_filtered_chunks + path_filtered_chunks
    output_chunks = combined_chunks[:max_chunks]
    return output_chunks


def _record_retrieval(
    ri_publisher: request_insight_publisher.RequestInsightPublisher,
    output_chunks: list[RetrievalChunk],
    request_context: RequestContext,
    auth_info: AuthInfo,
):
    # The agents service instantiates its retriever without a request
    # insight builder, as we want to record the filtered chunks here. If we
    # later plumb filters down into the retriever, then we can remove this.
    RetrieverRequestInsightBuilder(ri_publisher).record_retrieval_result(
        # TODO: new type?
        retrieval_type=request_insight_pb2.RetrievalType.DENSE,
        # We don't have access to this here
        query_prompt=[],
        embedder_tokenizer=None,
        retrieved_chunks=output_chunks,
        request_context=request_context,
        auth_info=auth_info,
    )


def _record_prompt_cache_usage(
    prompt_cache_usage: PromptCacheUsage,
    ri_publisher: request_insight_publisher.RequestInsightPublisher,
    request_context: RequestContext,
    auth_info: AuthInfo,
):
    event = request_insight_publisher.new_event()
    event.prompt_cache_usage.MergeFrom(
        request_insight_pb2.PromptCacheUsage(
            model_caller=prompt_cache_usage.model_caller or MODEL_CALLER,
            input_tokens=prompt_cache_usage.input_tokens,
            cache_read_input_tokens=prompt_cache_usage.cache_read_input_tokens,
            cache_creation_input_tokens=prompt_cache_usage.cache_creation_input_tokens,
            text_input_tokens=prompt_cache_usage.text_input_tokens,
            tool_input_tokens=prompt_cache_usage.tool_input_tokens,
            text_output_tokens=prompt_cache_usage.text_output_tokens,
            tool_output_tokens=prompt_cache_usage.tool_output_tokens,
        )
    )
    ri_publisher.publish_request_insight(
        ri_publisher.update_request_info_request(
            request_context.request_id, [event], auth_info
        )
    )


def _record_exchanges(
    ri_publisher: request_insight_publisher.RequestInsightPublisher,
    exchanges: list[chat_pb2.Exchange],
    request_context: RequestContext,
    auth_info: AuthInfo,
):
    event = request_insight_publisher.new_event()
    event.sub_agent_dialog.MergeFrom(
        request_insight_pb2.SubAgentDialog(dialog=exchanges)
    )
    ri_publisher.publish_request_insight(
        ri_publisher.update_request_info_request(
            request_context.request_id, [event], auth_info
        )
    )


def _format_retrieval(
    chunks: list[RetrievalChunk], max_char_count: int
) -> tuple[str, list[RetrievalChunk]]:
    """Format the retrieval results."""

    tool_output_text = "The following code sections were retrieved:\n"
    unneeded_token_counter = RoughTokenCounter()
    formatted_files: dict[str, AbstractFormattedFile] = OrderedDict()
    char_count = 0
    used_chunks = []

    # Get the feature flag context
    add_line_numbers = _ADD_LINE_NUMBERS.get(feature_flags.get_global_context())

    for chunk in chunks:
        prompt_chunk = chunk.to_prompt_chunk()
        file_id = prompt_chunk.blob_name
        if file_id not in formatted_files:
            old_formatted_file = FormattedFileV2(unneeded_token_counter)
        else:
            old_formatted_file = formatted_files[file_id]

        new_formatted_file = old_formatted_file.add_chunk(prompt_chunk)
        new_len = len(new_formatted_file.get_file_str_and_tok_ct(add_line_numbers)[0])
        old_len = len(old_formatted_file.get_file_str_and_tok_ct(add_line_numbers)[0])
        added_len = new_len - old_len
        if char_count + added_len > max_char_count:
            break

        char_count += added_len
        formatted_files[file_id] = new_formatted_file
        used_chunks.append(chunk)

    for formatted_file in formatted_files.values():
        file_str, _ = formatted_file.get_file_str_and_tok_ct(add_line_numbers)
        file_path = formatted_file.sorted_chunks[0].path
        tool_output_text += f"Path: {file_path}\n{file_str}\n"

    return tool_output_text, used_chunks


def create_directory_structure(chunks: list[RetrievalChunk]):
    """Create a directory structure from a list of Chunks.
    This function takes a list of Chunks and creates a directory structure
    similar to the one in the DirectorySubTreeTool class. It extracts the file paths
    from the Chunks and builds a hierarchical structure of directories and files.
    Args:
        chunks: List of Chunk objects.
    Returns:
        A dictionary representing the directory structure, with the following format:
        {
            "directory_path": {
                "files": [list of Path objects for files],
                "dirs": [list of Path objects for subdirectories],
                "dir_name_set": {set of directory names}
            },
            ...
        }
    """
    directory_structure = {"": {"files": [], "dirs": [], "dir_name_set": set()}}
    file_paths = [Path(chunk.path) for chunk in chunks if chunk.path]

    # Remove duplicates while preserving order
    seen = set()
    unique_file_paths = []
    for path in file_paths:
        if str(path) not in seen:
            seen.add(str(path))
            unique_file_paths.append(path)

    for path in unique_file_paths:
        # For files with no directory (e.g., 'test.py'), add them to the root directory
        if path.parent == Path(".") or path.parent == Path(""):
            if path.name not in directory_structure[""]["dir_name_set"]:
                directory_structure[""]["files"].append(path)
            continue

        original_path = path  # Keep the original path for file entries
        is_file = True
        # Iterate until we've reached the root directory
        while path.name and path != Path(".") and path != Path(""):
            parent = path.parent
            parent_str = str(parent) if parent != Path(".") else ""

            if parent_str not in directory_structure:
                directory_structure[parent_str] = {
                    "files": [],
                    "dirs": [],
                    "dir_name_set": set(),
                }

            # Check if already added
            if path.name not in directory_structure[parent_str]["dir_name_set"]:
                if is_file:
                    directory_structure[parent_str]["files"].append(original_path)
                else:
                    directory_structure[parent_str]["dirs"].append(path)
                    directory_structure[parent_str]["dir_name_set"].add(path.name)
            path = parent
            is_file = False

    # Sort alphabetically
    for key in directory_structure:
        directory_structure[key]["files"].sort()
        directory_structure[key]["dirs"].sort()

    return directory_structure


def format_directory_subtree(
    directory_structure: dict[str, dict[str, Any]],
    starting_dir_str: str,
    max_directory_chars: int,
    max_file_chars: int,
    max_depth: int = 50,
) -> tuple[str, dict[str, bool]]:
    """Format a directory structure into a readable string representation.
    Args:
        directory_structure: A dictionary representing the directory structure, with the format:
            {
                "directory_path": {
                    "files": [list of Path objects for files],
                    "dirs": [list of Path objects for subdirectories],
                    "dir_name_set": {set of directory names}
                },
                ...
            }
        starting_dir_str: The directory to start from.
        max_directory_chars: Maximum number of characters for directory names.
        max_file_chars: Maximum number of characters for file names.
        max_depth: Maximum depth to traverse.
    Returns:
        A tuple containing:
        - The formatted directory structure as a string.
        - A dictionary with metadata about the formatting, including whether it was truncated.
    """
    spaces_per_level = 2

    # Find max level
    if starting_dir_str not in directory_structure:
        return f"Directory {starting_dir_str} not found.", {"truncated": False}

    # Create a simplified version of the directory structure for easier traversal
    simplified_structure = {}
    for path, content in directory_structure.items():
        simplified_structure[path] = {
            "files": [file.name for file in content["files"]],
            "dirs": [dir.name for dir in content["dirs"]],
            "dir_paths": {},
        }
        # Create a mapping from directory name to full path
        for dir in content["dirs"]:
            if path:
                simplified_structure[path]["dir_paths"][dir.name] = f"{path}/{dir.name}"
            else:
                simplified_structure[path]["dir_paths"][dir.name] = dir.name

    current_path = starting_dir_str
    current_level_idx = 0
    total_dir_char_count = 0
    total_file_char_count = 0

    max_dirs_reached = False
    max_files_reached = False

    max_dir_level = -1
    max_file_level = -1

    # First pass: determine the maximum depth we can display
    paths_to_process = [current_path]
    for idx in range(max_depth):
        if not paths_to_process:
            break

        # Reset for next level
        current_dir_char_count = 0
        current_file_char_count = 0
        next_paths = []

        for path in paths_to_process:
            subtree = simplified_structure[path]
            files = subtree["files"]
            dirs = subtree["dirs"]
            dir_paths = subtree["dir_paths"]

            for file in files:
                current_file_char_count += (
                    len(file) + spaces_per_level * current_level_idx
                )

            for dir in dirs:
                current_dir_char_count += (
                    len(dir) + spaces_per_level * current_level_idx
                )
                next_paths.append(dir_paths[dir])

        if (
            not max_dirs_reached
            and total_dir_char_count + current_dir_char_count > max_directory_chars
        ):
            max_dir_level = current_level_idx - 1
            max_dirs_reached = True
        if (
            not max_files_reached
            and total_file_char_count + current_file_char_count > max_file_chars
        ):
            max_file_level = current_level_idx - 1
            max_files_reached = True

        if max_dirs_reached:
            break
        elif not next_paths:
            if not max_dirs_reached:
                max_dir_level = current_level_idx
            if not max_files_reached:
                max_file_level = current_level_idx
        else:
            current_level_idx += 1
            paths_to_process = next_paths
            total_dir_char_count += current_dir_char_count
            total_file_char_count += current_file_char_count

    # Second pass: create the formatted string
    text = ""

    def format_directory(path, level):
        nonlocal text
        if level > max_dir_level:
            return

        subtree = simplified_structure[path]
        files = subtree["files"]
        dirs = subtree["dirs"]
        dir_paths = subtree["dir_paths"]

        # Add files if we're within the file level limit
        if level <= max_file_level:
            for file in sorted(files):
                indent = " " * spaces_per_level * level
                text += f"{indent}{file}\n"

        # Add directories
        for dir in sorted(dirs):
            indent = " " * spaces_per_level * level
            text += f"{indent}{dir}\n"

            # If we've reached the max directory level, add ellipsis
            if level >= max_dir_level:
                indent = " " * spaces_per_level * (level + 1)
                text += f"{indent}...\n"
            else:
                # Otherwise, recursively format the subdirectory
                format_directory(dir_paths[dir], level + 1)

    # Start formatting from the root directory
    # Special case for the root directory - don't print its name
    subtree = simplified_structure[starting_dir_str]
    files = subtree["files"]
    dirs = subtree["dirs"]
    dir_paths = subtree["dir_paths"]

    # Add directories in the root directory first (to match expected output in tests)
    for dir in sorted(dirs):
        text += f"{dir}\n"
        if max_dir_level > 0:
            format_directory(dir_paths[dir], 1)
        else:
            indent = " " * spaces_per_level
            text += f"{indent}...\n"

    # Add files in the root directory
    if max_file_level >= 0:
        for file in sorted(files):
            text += f"{file}\n"

    format_metadata = {
        "truncated": max_dirs_reached or max_files_reached,
    }
    return text, format_metadata


def _extract_and_interleave_chunks(
    query_result_list: list[tuple[dict[str, Any], list[RetrievalChunk]]],
) -> list[RetrievalChunk]:
    """Extract and interleave the chunks from the query result list."""
    list_of_list_of_chunks = [chunks for _, chunks in query_result_list]
    # We want to provide each generated query with equal budget, so we interleave
    # chunks from each query and stop when we reach the budget.
    interleaved_chunks = list(interleave_sequences(list_of_list_of_chunks))
    return interleaved_chunks


class CodebaseRetrievalAgent:
    def __init__(
        self,
        retriever: Retriever[ChatRetrieverPromptInput],
        ri_publisher: request_insight_publisher.RequestInsightPublisher,
    ):
        """
        Args:
            retriever: The agent will use this retriever to search the codebase.
            max_chars: The agent will return at most this many characters per request.
        """
        self.retriever = retriever
        self.ri_publisher = ri_publisher
        self.max_chunks = 1024
        self.codebase_retrieval_tool = CodebaseRetrievalQueryGenerationTool()
        self.query_tool_param = self.codebase_retrieval_tool.get_tool_definition()

    def run(
        self,
        retrieval_request: CodebaseRetrievalRequest,
        llm_client: ThirdPartyModelClient,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> CodebaseRetrievalResponse:
        """Run the tool."""
        context = feature_flags.get_global_context()
        max_chars = min(
            MAX_CHARS.get(context=context),
            ABSOLUTE_MAX_CHARS,
        )
        if retrieval_request.max_output_length:
            max_chars = min(max_chars, retrieval_request.max_output_length)

        # Get low level queries
        with tracer.start_as_current_span("get_retriever_queries"):
            retriever_queries = self.get_retriever_queries(
                llm_client,
                retrieval_request.information_request,
                [convert_exchange(exchange) for exchange in retrieval_request.dialog],
                request_context,
                auth_info,
            )
        if len(retriever_queries) == 0:
            return QUERY_GENERATION_FAILURE

        blobs = [blob_names.Blobs.from_proto(proto=b) for b in retrieval_request.blobs]

        # Retrieve for each query
        query_result_list = []
        for retriever_query in retriever_queries:
            chunks = _retrieve(
                retriever=self.retriever,
                request_description=retriever_query["description"],
                request_path=retriever_query.get("path", ""),
                request_contains_string=retriever_query.get("contains_string", ""),
                request_context=request_context,
                auth_info=auth_info,
                blobs=blobs,
                max_chunks=self.max_chunks,
            )
            _record_retrieval(
                ri_publisher=self.ri_publisher,
                output_chunks=chunks,
                request_context=request_context,
                auth_info=auth_info,
            )

            query_result_list.append((retriever_query, chunks))

        tool_output_str, _ = _format_retrieval(
            _extract_and_interleave_chunks(query_result_list), max_chars
        )

        return CodebaseRetrievalResponse(formatted_retrieval=tool_output_str)

    def get_retriever_queries(
        self,
        llm_client: ThirdPartyModelClient,
        information_request: str,
        dialog_messages: list[Exchange],
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> list[dict[str, Any]]:
        """Generate the queries to the retriever."""

        MAX_DIALOG_CHARS = 30000

        QUERY_GENERATION_SYSTEM_PROMPT = dedent(
            f"""\
            You are a tool-calling agent that generates queries for a dense retrieval tool to gather information from a codebase to help answer a request for information.
            Call the {self.query_tool_param.name} tool to generate queries.
            Please pay careful attention to the tool description to optimally use the tool.
            """
        )

        message_str = ""
        message_str += QUERY_GENERATION_SYSTEM_PROMPT
        message_str += f"The information request is: {information_request}\n"

        DIALOG_PROMPT = dedent(
            """\
            The information request is in the context of an agent attempting to execute a task.
            Below are some messages of the dialogue of this agent and the user:
            """
        )

        if dialog_messages is not None:
            message_str += DIALOG_PROMPT
            message_str += format_dialog_as_string(dialog_messages, MAX_DIALOG_CHARS)

        message_str += (
            f"Once again, the information request is: {information_request}\n"
        )

        tool_use = _generate_tool_use(
            llm_client,
            message_str,
            self.query_tool_param,
            partial(
                _record_prompt_cache_usage,
                ri_publisher=self.ri_publisher,
                request_context=request_context,
                auth_info=auth_info,
            ),
        )

        self.record_exchange(message_str, tool_use, request_context, auth_info)
        return _requests_from_tool_use(tool_use, self.codebase_retrieval_tool)

    def record_exchange(
        self,
        prompt: str,
        tool_use: ToolUseResponse | None,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        req_node = chat_pb2.ChatRequestNode(
            type=chat_pb2.ChatRequestNodeType.TEXT,
            text_node=chat_pb2.ChatRequestText(content=prompt),
        )
        resp_nodes = []
        if tool_use is not None:
            resp_nodes.append(
                chat_pb2.ChatResultNode(
                    type=chat_pb2.ChatResultNodeType.TOOL_USE,
                    tool_use=chat_pb2.ChatResultToolUse(
                        tool_use_id=tool_use.tool_use_id,
                        tool_name=tool_use.tool_name,
                        input_json=json.dumps(tool_use.input),
                    ),
                )
            )
        exchange = chat_pb2.Exchange(
            request_nodes=[req_node],
            response_nodes=resp_nodes,
            request_id=request_context.request_id,
        )
        event = request_insight_publisher.new_event()
        event.sub_agent_dialog.MergeFrom(
            request_insight_pb2.SubAgentDialog(dialog=[exchange])
        )
        self.ri_publisher.publish_request_insight(
            self.ri_publisher.update_request_info_request(
                request_context.request_id, [event], auth_info
            )
        )


class CodebaseMultiRetrievalAgent:
    def __init__(
        self,
        retriever: Retriever[ChatRetrieverPromptInput],
        ri_publisher: request_insight_publisher.RequestInsightPublisher,
    ):
        """
        Args:
            retriever: The agent will use this retriever to search the codebase.
        """
        self.retriever = retriever
        self.ri_publisher = ri_publisher
        self.max_chunks = 1024
        self.query_generation_tool = CodebaseRetrievalQueryGenerationTool()
        self.final_round_query_generation_tool = FinalRoundQueryGenerationTool()

        self.query_tool_param = self.query_generation_tool.get_tool_definition()
        self.final_round_query_tool_param = (
            self.final_round_query_generation_tool.get_tool_definition()
        )

    def run(
        self,
        retrieval_request: CodebaseRetrievalRequest,
        llm_client: ThirdPartyModelClient,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> CodebaseRetrievalResponse:
        """Run the tool."""

        information_request = retrieval_request.information_request
        dialog_messages = [
            convert_exchange(exchange) for exchange in retrieval_request.dialog
        ]

        context = feature_flags.get_global_context()
        max_chars = min(
            MAX_CHARS.get(context=context),
            ABSOLUTE_MAX_CHARS,
        )
        if retrieval_request.max_output_length:
            max_chars = min(max_chars, retrieval_request.max_output_length)

        exchanges = []
        # Get low level queries for first round of retrieval
        with tracer.start_as_current_span("get_retriever_queries"):
            retriever_queries, exchange = self.get_retriever_queries(
                llm_client,
                information_request=information_request,
                request_context=request_context,
                dialog_messages=dialog_messages,
                auth_info=auth_info,
            )
        exchanges.append(exchange)
        if len(retriever_queries) == 0:
            _record_exchanges(
                ri_publisher=self.ri_publisher,
                exchanges=exchanges,
                request_context=request_context,
                auth_info=auth_info,
            )
            return QUERY_GENERATION_FAILURE

        blobs = [blob_names.Blobs.from_proto(proto=b) for b in retrieval_request.blobs]

        # Retrieve for each query
        query_result_list = []
        for retriever_query in retriever_queries:
            chunks = _retrieve(
                retriever=self.retriever,
                request_description=retriever_query["description"],
                request_path=retriever_query.get("path", ""),
                request_contains_string=retriever_query.get("contains_string", ""),
                request_context=request_context,
                auth_info=auth_info,
                max_chunks=self.max_chunks,
                blobs=blobs,
            )
            query_result_list.append((retriever_query, chunks))

        # Final round retrieval
        with tracer.start_as_current_span("get_final_round_retriever_queries"):
            final_round_queries, exchange = self.get_final_round_retriever_queries(
                llm_client,
                information_request=information_request,
                query_result_list=query_result_list,
                dialog_messages=dialog_messages,
                request_context=request_context,
                auth_info=auth_info,
            )
        exchanges.append(exchange)
        if len(final_round_queries) == 0:
            _record_exchanges(
                ri_publisher=self.ri_publisher,
                exchanges=exchanges,
                request_context=request_context,
                auth_info=auth_info,
            )
            return QUERY_GENERATION_FAILURE

        final_result_list = []
        for retriever_query in final_round_queries:
            chunks = _retrieve(
                retriever=self.retriever,
                request_description=retriever_query["description"],
                request_path=retriever_query.get("path", ""),
                request_contains_string=retriever_query.get("contains_string", ""),
                request_context=request_context,
                auth_info=auth_info,
                blobs=blobs,
                max_chunks=self.max_chunks,
            )
            _record_retrieval(
                ri_publisher=self.ri_publisher,
                output_chunks=chunks,
                request_context=request_context,
                auth_info=auth_info,
            )
            final_result_list.append((retriever_query, chunks))

        # Format the results
        tool_output_str = self.format_retrieval_for_output(
            final_result_list,
            max_chars,
        )

        _record_exchanges(
            ri_publisher=self.ri_publisher,
            exchanges=exchanges,
            request_context=request_context,
            auth_info=auth_info,
        )

        return CodebaseRetrievalResponse(formatted_retrieval=tool_output_str)

    def get_retriever_queries(
        self,
        llm_client: ThirdPartyModelClient,
        information_request: str,
        request_context: RequestContext,
        auth_info: AuthInfo,
        dialog_messages: list[Exchange] = [],
    ) -> tuple[list[dict[str, Any]], chat_pb2.Exchange]:
        """Generate the queries to the retriever."""

        MAX_DIALOG_CHARS = 30000

        QUERY_GENERATION_SYSTEM_PROMPT = dedent(
            f"""\
            You are a tool-calling agent that generates queries for a dense retrieval tool to gather information from a codebase to help answer a request for information.
            Call the {self.query_tool_param.name} tool to generate queries.
            Please pay careful attention to the tool description to optimally use the tool.
            """
        )

        message_str = ""
        message_str += QUERY_GENERATION_SYSTEM_PROMPT
        message_str += f"The information request is: {information_request}\n"

        DIALOG_PROMPT = dedent(
            """\
            The information request is in the context of an agent attempting to execute a task.
            Below are some messages of the dialogue of this agent and the user:
            """
        )

        if dialog_messages is not None:
            message_str += DIALOG_PROMPT
            message_str += format_dialog_as_string(dialog_messages, MAX_DIALOG_CHARS)

        message_str += (
            f"Once again, the information request is: {information_request}\n"
        )

        tool_use = _generate_tool_use(
            llm_client,
            message_str,
            self.query_tool_param,
            partial(
                _record_prompt_cache_usage,
                ri_publisher=self.ri_publisher,
                request_context=request_context,
                auth_info=auth_info,
            ),
        )

        request_node = chat_pb2.ChatRequestNode(
            type=chat_pb2.ChatRequestNodeType.TEXT,
            text_node=chat_pb2.ChatRequestText(
                content="Intermediate retrieval query generation."
            ),
        )
        resp_nodes = []
        if tool_use is not None:
            resp_nodes.append(
                chat_pb2.ChatResultNode(
                    type=chat_pb2.ChatResultNodeType.TOOL_USE,
                    tool_use=chat_pb2.ChatResultToolUse(
                        tool_use_id=tool_use.tool_use_id,
                        tool_name=tool_use.tool_name,
                        input_json=json.dumps(tool_use.input),
                    ),
                )
            )
        exchange = chat_pb2.Exchange(
            request_nodes=[request_node],
            response_nodes=resp_nodes,
            request_id=request_context.request_id,
        )
        return _requests_from_tool_use(tool_use, self.query_generation_tool), exchange

    def get_final_round_retriever_queries(
        self,
        llm_client: ThirdPartyModelClient,
        information_request: str,
        query_result_list: list[tuple[dict[str, Any], list[RetrievalChunk]]],
        request_context: RequestContext,
        auth_info: AuthInfo,
        dialog_messages: list[Exchange] = [],
    ) -> tuple[list[dict[str, Any]], chat_pb2.Exchange]:
        """Generate queries for final round retrieval using first round results as context."""

        MAX_DIALOG_CHARS = 30000
        MAX_CONTEXT_CHARS = 100000

        # Format first round results into context
        formatted_intermediate_retrieval_string, _ = _format_retrieval(
            _extract_and_interleave_chunks(query_result_list),
            MAX_CONTEXT_CHARS,
        )

        FINAL_ROUND_SYSTEM_PROMPT = dedent(
            f"""\
            You are a tool-calling agent that generates queries for a final round of dense retrieval.
            Your task is to analyze the code snippets from the first round and generate a final set of queries to:
            1. Condense and expand the most relevant information from the first query.
              We will not show any results of the first query to the user, all the information has to be contained in this round.
              Typically, if one particular file was extremely relevant, there should be a query to retrieve the entire file.
            2. Find any missing information that was not found in the first round.

            Call the {self.final_round_query_tool_param.name} tool to generate these queries.
            Please pay careful attention to the tool description to optimally use the tool.
            """
        )

        message_str = ""
        message_str += FINAL_ROUND_SYSTEM_PROMPT
        message_str += f"The original information request is: {information_request}\n\n"
        message_str += formatted_intermediate_retrieval_string

        if dialog_messages is not None:
            message_str += "\nThis request is in the context of an agent dialogue:\n"
            message_str += format_dialog_as_string(dialog_messages, MAX_DIALOG_CHARS)

        tool_use = _generate_tool_use(
            llm_client,
            message_str,
            self.final_round_query_tool_param,
            partial(
                _record_prompt_cache_usage,
                ri_publisher=self.ri_publisher,
                request_context=request_context,
                auth_info=auth_info,
            ),
        )

        request_node = chat_pb2.ChatRequestNode(
            type=chat_pb2.ChatRequestNodeType.TEXT,
            text_node=chat_pb2.ChatRequestText(
                content="Final round retrieval query generation."
            ),
        )
        resp_nodes = []
        if tool_use is not None:
            resp_nodes.append(
                chat_pb2.ChatResultNode(
                    type=chat_pb2.ChatResultNodeType.TOOL_USE,
                    tool_use=chat_pb2.ChatResultToolUse(
                        tool_use_id=tool_use.tool_use_id,
                        tool_name=tool_use.tool_name,
                        input_json=json.dumps(tool_use.input),
                    ),
                )
            )

        exchange = chat_pb2.Exchange(
            request_nodes=[request_node],
            response_nodes=resp_nodes,
            request_id=request_context.request_id,
        )

        return _requests_from_tool_use(
            tool_use, self.final_round_query_generation_tool
        ), exchange

    def format_retrieval_for_output(
        self,
        query_result_list: list[tuple[dict[str, Any], list[RetrievalChunk]]],
        max_char_count: int,
    ) -> str:
        """Format the retrieval results."""

        tool_output_text = "The following code sections were retrieved:\n"
        unneeded_token_counter = RoughTokenCounter()
        formatted_files: dict[str, AbstractFormattedFile] = OrderedDict()
        # We allow the query generation call to specify certain queries as 'critical'.
        # Critical queries get a configurably increased budget.
        CRITICAL_WEIGHT = 2.0
        NON_CRITICAL_WEIGHT = 1.0

        # Calculate the budget for each query based on the number of critical and non-critical queries.
        num_critical_queries = 0
        num_non_critical_queries = 0
        for query, _ in query_result_list:
            critical = query.get("critical", False)
            if critical:
                num_critical_queries += 1
            else:
                num_non_critical_queries += 1

        denominator = (
            CRITICAL_WEIGHT * num_critical_queries
            + NON_CRITICAL_WEIGHT * num_non_critical_queries
        )
        critical_budget = (CRITICAL_WEIGHT / denominator) * max_char_count
        non_critical_budget = (NON_CRITICAL_WEIGHT / denominator) * max_char_count

        # We fill each budget separately without overflowing
        # This means we may not use all the budget if we don't have enough chunks.
        include_chunks = []
        for query, chunks in query_result_list:
            critical = query.get("critical", False)
            budget = critical_budget if critical else non_critical_budget
            char_count = 0
            for chunk in chunks:
                chars_used = len(chunk.text) + len(chunk.path or "")
                if char_count + chars_used > budget:
                    break
                char_count += chars_used
                include_chunks.append(chunk)

        for chunk in include_chunks:
            prompt_chunk = chunk.to_prompt_chunk()
            file_id = prompt_chunk.blob_name
            if file_id not in formatted_files:
                old_formatted_file = FormattedFileV2(unneeded_token_counter)
            else:
                old_formatted_file = formatted_files[file_id]

            new_formatted_file = old_formatted_file.add_chunk(prompt_chunk)
            formatted_files[file_id] = new_formatted_file

        # Get the feature flag context
        add_line_numbers = _ADD_LINE_NUMBERS.get(feature_flags.get_global_context())

        for formatted_file in formatted_files.values():
            file_str, _ = formatted_file.get_file_str_and_tok_ct(add_line_numbers)
            file_path = formatted_file.sorted_chunks[0].path
            tool_output_text += f"Path: {file_path}\n{file_str}\n"

        return tool_output_text


class CodebaseQueryRewritingRetrievalAgent:
    """A codebase retrieval agent that uses a two-stage query rewriting approach.

    This agent enhances retrieval through a two-stage process:
    1. Performs initial retrieval using the original information request
    2. Uses initial results to generate targeted follow-up queries:
       - Formats initial retrievals (up to max_chars/2)
       - Builds directory tree from initial retrievals
       - Uses LLM to generate refined retrieval queries
    3. Executes second-round retrieval with these follow-up queries
    4. Combines initial and follow-up retrievals within max_chars budget
    """

    def __init__(
        self,
        retriever: Retriever[ChatRetrieverPromptInput],
        ri_publisher: request_insight_publisher.RequestInsightPublisher,
    ):
        """
        Args:
            retriever: The agent will use this retriever to search the codebase.
            ri_publisher: The agent will use this to publish request insights.
        """
        self.retriever = retriever
        self.ri_publisher = ri_publisher
        self.max_chunks = 1024
        self.query_generation_tool = CodebaseRetrievalQueryGenerationTool()
        self.query_tool_param = self.query_generation_tool.get_tool_definition()

    def run(
        self,
        retrieval_request: CodebaseRetrievalRequest,
        llm_client: ThirdPartyModelClient,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> CodebaseRetrievalResponse:
        """Run the tool."""

        context = feature_flags.get_global_context()
        max_chars = min(
            MAX_CHARS.get(context=context),
            ABSOLUTE_MAX_CHARS,
        )
        if retrieval_request.max_output_length:
            max_chars = min(max_chars, retrieval_request.max_output_length)

        blobs = [blob_names.Blobs.from_proto(proto=b) for b in retrieval_request.blobs]

        def retrieve(
            request_description: str,
            request_path: str = "",
            request_contains_string: str = "",
        ):
            return _retrieve(
                retriever=self.retriever,
                request_description=request_description,
                request_path=request_path,
                request_contains_string=request_contains_string,
                request_context=request_context,
                auth_info=auth_info,
                blobs=blobs,
                max_chunks=self.max_chunks,
            )

        information_request = retrieval_request.information_request
        dialog_messages = [
            convert_exchange(exchange) for exchange in retrieval_request.dialog
        ]

        with tracer.start_as_current_span("initial_retrieval"):
            initial_retrievals = retrieve(request_description=information_request)

        exchanges = []
        with tracer.start_as_current_span("get_retriever_queries"):
            retriever_queries, exchange, used_initial_retrievals = (
                self.get_retriever_queries(
                    llm_client,
                    information_request=information_request,
                    request_context=request_context,
                    auth_info=auth_info,
                    dialog_messages=dialog_messages,
                    initial_retrievals=initial_retrievals,
                    max_initial_retrieval_chars=max_chars // 2,
                )
            )
        exchanges.append(exchange)
        if len(retriever_queries) == 0:
            _record_exchanges(
                ri_publisher=self.ri_publisher,
                exchanges=exchanges,
                request_context=request_context,
                auth_info=auth_info,
            )
            return QUERY_GENERATION_FAILURE

        # Retrieve for each query
        with tracer.start_as_current_span("followup_retrieval"):
            query_result_list = []
            for retriever_query in retriever_queries:
                chunks = _retrieve(
                    retriever=self.retriever,
                    request_description=retriever_query["description"],
                    request_path=retriever_query.get("path", ""),
                    request_contains_string=retriever_query.get("contains_string", ""),
                    request_context=request_context,
                    auth_info=auth_info,
                    max_chunks=self.max_chunks,
                    blobs=blobs,
                )
                _record_retrieval(
                    ri_publisher=self.ri_publisher,
                    output_chunks=chunks,
                    request_context=request_context,
                    auth_info=auth_info,
                )
                query_result_list.append((retriever_query, chunks))

        # Format the results
        combined_chunks = used_initial_retrievals + _extract_and_interleave_chunks(
            query_result_list
        )

        tool_output_str, _ = _format_retrieval(
            combined_chunks,
            max_chars,
        )

        _record_exchanges(
            ri_publisher=self.ri_publisher,
            exchanges=exchanges,
            request_context=request_context,
            auth_info=auth_info,
        )

        return CodebaseRetrievalResponse(formatted_retrieval=tool_output_str)

    def get_retriever_queries(
        self,
        llm_client: ThirdPartyModelClient,
        information_request: str,
        request_context: RequestContext,
        auth_info: AuthInfo,
        dialog_messages: list[Exchange],
        initial_retrievals: list[RetrievalChunk],
        max_initial_retrieval_chars: int,
    ) -> tuple[list[dict[str, Any]], chat_pb2.Exchange, list[RetrievalChunk]]:
        """Generate the queries to the retriever."""

        MAX_DIALOG_CHARS = 30000

        formatted_initial_retrieval, used_initial_retrievals = _format_retrieval(
            initial_retrievals, max_initial_retrieval_chars
        )
        directory_structure = create_directory_structure(initial_retrievals)
        formatted_directory_output, _ = format_directory_subtree(
            directory_structure,
            "",
            max_initial_retrieval_chars // 2,
            max_initial_retrieval_chars // 2,
        )

        message_str = f"""\
The information request is: {information_request}

The directory structure is:
{formatted_directory_output}
Keep in mind that this may not be a full directory subtree, only up to a character limit. Some files or directories might be missing, and the structure might be incomplete.

{formatted_initial_retrieval or ""}

You are a tool-calling agent that generates queries for a dense retrieval tool to gather information from a codebase to help answer a request for information.
Call the {self.query_tool_param.name} tool to generate queries.
Please pay careful attention to the tool description to optimally use the tool.
Look at the information gathered already, and determine what additional information is missing. Make sure to also look at the directory tree and previous codebase snippets to guide your search.
"""

        DIALOG_PROMPT = dedent(
            """\
            The information request is in the context of an agent attempting to execute a task.
            Below are some messages of the dialogue of this agent and the user:
            """
        )

        if dialog_messages is not None:
            message_str += DIALOG_PROMPT
            message_str += format_dialog_as_string(dialog_messages, MAX_DIALOG_CHARS)

        message_str += (
            f"Once again, the information request is: {information_request}\n"
        )

        tool_use = _generate_tool_use(
            llm_client,
            message_str,
            self.query_tool_param,
            partial(
                _record_prompt_cache_usage,
                ri_publisher=self.ri_publisher,
                request_context=request_context,
                auth_info=auth_info,
            ),
        )

        request_node = chat_pb2.ChatRequestNode(
            type=chat_pb2.ChatRequestNodeType.TEXT,
            text_node=chat_pb2.ChatRequestText(
                content="Intermediate retrieval query generation."
            ),
        )
        resp_nodes = []
        if tool_use is not None:
            resp_nodes.append(
                chat_pb2.ChatResultNode(
                    type=chat_pb2.ChatResultNodeType.TOOL_USE,
                    tool_use=chat_pb2.ChatResultToolUse(
                        tool_use_id=tool_use.tool_use_id,
                        tool_name=tool_use.tool_name,
                        input_json=json.dumps(tool_use.input),
                    ),
                )
            )
        exchange = chat_pb2.Exchange(
            request_nodes=[request_node],
            response_nodes=resp_nodes,
            request_id=request_context.request_id,
        )
        return (
            _requests_from_tool_use(tool_use, self.query_generation_tool),
            exchange,
            used_initial_retrievals,
        )


class SimpleRetrievalAgent:
    """A simple retrieval agent that directly passes in the query to the retriever."""

    def __init__(
        self,
        retriever: Retriever[ChatRetrieverPromptInput],
        ri_publisher: request_insight_publisher.RequestInsightPublisher,
    ):
        """
        Args:
            retriever: The agent will use this retriever to search the codebase.
            max_chars: The agent will return at most this many characters per request.
        """
        self.retriever = retriever
        self.ri_publisher = ri_publisher
        self.max_chunks = 1024

    def run(
        self,
        retrieval_request: CodebaseRetrievalRequest,
        llm_client: ThirdPartyModelClient | None,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> CodebaseRetrievalResponse:
        """Run the tool."""
        del llm_client

        context = feature_flags.get_global_context()
        max_chars = min(
            MAX_CHARS.get(context=context),
            ABSOLUTE_MAX_CHARS,
        )
        if retrieval_request.max_output_length:
            max_chars = min(max_chars, retrieval_request.max_output_length)

        blobs = [blob_names.Blobs.from_proto(proto=b) for b in retrieval_request.blobs]

        logger.info(
            "Running retrieval agent with %s blobs, desc=%s",
            len(blobs),
            retrieval_request.information_request,
        )
        # Retrieve for each query
        chunks = _retrieve(
            retriever=self.retriever,
            request_description=retrieval_request.information_request,
            request_path="",
            request_contains_string="",
            request_context=request_context,
            auth_info=auth_info,
            blobs=blobs,
            max_chunks=self.max_chunks,
        )
        logger.info("Retrieved chunks: %s", len(chunks))
        _record_retrieval(
            ri_publisher=self.ri_publisher,
            output_chunks=chunks,
            request_context=request_context,
            auth_info=auth_info,
        )

        # TODO(arun): The way we format the output here does depend on the application.
        # Commits e.g. probably should be formatted differently from code chunks, and
        # this was the case for our experiment. This is a placeholder for now, but will
        # be improved.
        tool_output_str, _ = _format_retrieval(chunks, max_chars)

        return CodebaseRetrievalResponse(formatted_retrieval=tool_output_str)

    def record_exchange(
        self,
        prompt: str,
        tool_use: ToolUseResponse | None,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        req_node = chat_pb2.ChatRequestNode(
            type=chat_pb2.ChatRequestNodeType.TEXT,
            text_node=chat_pb2.ChatRequestText(content=prompt),
        )
        resp_nodes = []
        if tool_use is not None:
            resp_nodes.append(
                chat_pb2.ChatResultNode(
                    type=chat_pb2.ChatResultNodeType.TOOL_USE,
                    tool_use=chat_pb2.ChatResultToolUse(
                        tool_use_id=tool_use.tool_use_id,
                        tool_name=tool_use.tool_name,
                        input_json=json.dumps(tool_use.input),
                    ),
                )
            )
        exchange = chat_pb2.Exchange(
            request_nodes=[req_node],
            response_nodes=resp_nodes,
            request_id=request_context.request_id,
        )
        event = request_insight_publisher.new_event()
        event.sub_agent_dialog.MergeFrom(
            request_insight_pb2.SubAgentDialog(dialog=[exchange])
        )
        self.ri_publisher.publish_request_insight(
            self.ri_publisher.update_request_info_request(
                request_context.request_id, [event], auth_info
            )
        )


class CodebaseRetrievalQueryGenerationTool(Tool):
    """A tool that generates one or more structured queries to a codebase retriever."""

    name = "ask_for_codebase_snippets"

    description = dedent(
        """\
        When asked about a particular object or code section, this will generate one or more queries to retrieve relevant codebase information.
        Make sure to ask for concrete sections of code, do not ask high-level questions.
        If you need to know how multiple parts of the codebase interact, ask about each part separately.
        Ask for at most three sections at one time.
        """
    )

    request_code_description = dedent(
        """\
        Description of the codebase section or snippet to ask for.
        Favor short low-level descriptions over high-level questions.
        Do not put paths in here - use the path field instead if you want to specify a path.

        Examples of good uses of description:
        {'description': 'inherits from foo class'}
        {'description': 'test for MainClass'}
        {'description': 'display of purchase button'}

        Examples of bad uses of description:
        Too high level
        {'description': 'code that deals with customers'}
        Describes multiple sections likely to be in different parts of code
        {'description': 'code that deals with customers and orders'}
        """
    )

    request_code_path = dedent(
        """\
        Optional path to the codebase section or snippet to ask for.
        May be a full path or a partial path (e.g. "**/foo.*").
        Do NOT specify unless you are completely certain the path exists in the codebase, either from previously seen snippets or from the subtree directory tool.
        If you are not certain, just leave out this field.

        Examples of good uses of path:
        You have seen that foo class is in a folder foo with subfolders and files.
        {'description': 'inherits from foo class', 'path': '**/foo/**'}
        The user asked "foo in src".
        {'description': 'foo', 'path': '**/src/**'}

        Examples of bad uses of path:
        You are not sure the path exists in the codebase.
        {'description': 'foo', 'path': '**/foo/**'}
        The user asked "foo in src", but you are not sure if src exists.
        {'description': 'foo', 'path': '**/src/**'}
        """
    )

    request_code_contains_string = dedent(
        """\
        Optional field to specify a string that must be present in the snippet.
        If specified, will prioritize snippets that contain this string.
        Ask when you are very sure the right snippet should contain this string.
        The main reason to use this field is when the user explicitly asks for a specific string, e.g. a variable or an error.
        Other less common reasons to use this field are when you are looking for type of object that is always annotated with a specific string.
        Be conservative about using this field for any reason other than the user explicitly asking for a specific string.

        Examples of good uses of contains_string:
        You are looking for a specific log line.
        {'description': 'baz exception log line', 'contains_string': 'type not found'}
        The user asked "foo_var in src".
        {'description': 'foo_var', 'path': '**/src/**', 'contains_string': 'foo_var'}

        Examples of bad uses of contains_string:
        You are not sure the right snippet should contain this string.
        {'description': 'inherits from foo class', 'contains_string': 'foo'}
        The user asked a general question: "test of MainClass", but you don't know what this test is called. Don't guess, leave out the contains_string.
        {'description': 'test of MainClass', 'contains_string': 'MainClassTest'}
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "code_section_requests": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "description": {
                            "type": "string",
                            "description": request_code_description,
                        },
                        "path": {
                            "type": "string",
                            "description": request_code_path,
                        },
                        "contains_string": {
                            "type": "string",
                            "description": request_code_contains_string,
                        },
                    },
                    "required": [
                        "description",
                    ],
                },
            },
        },
        "required": ["code_section_requests"],
    }


class FinalRoundQueryGenerationTool(Tool):
    """A tool that generates one or more structured queries to a codebase retriever."""

    name = "ask_for_codebase_snippets"

    description = dedent(
        """\
        When asked about a particular object or code section, this will generate one or more queries to retrieve relevant codebase information.
        Make sure to ask for concrete sections of code, do not ask high-level questions.
        If you need to know how multiple parts of the codebase interact, ask about each part separately.
        The number of queries you generate will be used to determine the amount of resources allocated to each query.
        Most commonly you should generate 2-3 queries.
        Generate a single query if you need a highly targeted piece of information.
        Generate more than 3 queries only rarely if you know for sure that you need a lot of different information.
        """
    )

    request_code_description = dedent(
        """\
        Description of the codebase section or snippet to ask for.
        Favor short low-level descriptions over high-level questions.
        Do not put paths in here - use the path field instead if you want to specify a path.

        Examples of good uses of description:
        {'description': 'inherits from foo class'}
        {'description': 'test for MainClass'}
        {'description': 'display of purchase button'}

        Examples of bad uses of description:
        Too high level
        {'description': 'code that deals with customers'}
        Describes multiple sections likely to be in different parts of code
        {'description': 'code that deals with customers and orders'}
        """
    )

    request_code_path = dedent(
        """\
        Optional path to the codebase section or snippet to ask for.
        May be a full path or a partial path (e.g. "**/foo.*").
        Do NOT specify unless you are completely certain the path exists in the codebase, either from previously seen snippets or from the subtree directory tool.
        If you are not certain, just leave out this field.

        Examples of good uses of path:
        You have seen that foo class is in a folder foo with subfolders and files.
        {'description': 'inherits from foo class', 'path': '**/foo/**'}
        The user asked "foo in src".
        {'description': 'foo', 'path': '**/src/**'}
        The user asked to see a class in a particular file.
        {'description': 'baz implementation', 'path': 'bar/foo.py'}

        Examples of bad uses of path:
        You are not sure the path exists in the codebase.
        {'description': 'foo', 'path': '**/foo/**'}
        The user asked "foo in src", but you are not sure if src exists.
        {'description': 'foo', 'path': '**/src/**'}
        """
    )

    request_code_contains_string = dedent(
        """\
        Optional field to specify a string that must be present in the snippet.
        If specified, will prioritize snippets that contain this string.
        Ask when you are very sure the right snippet should contain this string.
        The main reason to use this field is when the user explicitly asks for a specific string, e.g. a variable or an error.
        Other less common reasons to use this field are when you are looking for type of object that is always annotated with a specific string.
        Be conservative about using this field for any reason other than the user explicitly asking for a specific string.

        Examples of good uses of contains_string:
        You are looking for a specific log line.
        {'description': 'baz exception log line', 'contains_string': 'type not found'}
        The user asked "foo_var in src".
        {'description': 'foo_var', 'path': '**/src/**', 'contains_string': 'foo_var'}

        Examples of bad uses of contains_string:
        You are not sure the right snippet should contain this string.
        {'description': 'inherits from foo class', 'contains_string': 'foo'}
        The user asked a general question: "test of MainClass", but you don't know what this test is called. Don't guess, leave out the contains_string.
        {'description': 'test of MainClass', 'contains_string': 'MainClassTest'}
        """
    )

    is_critical_description = dedent(
        """\
        Optional field to specify whether a query is critical to the answer.
        If specified, will heavily prioritize snippets that result from this query.
        For example, if a question is primarily about a specific file, you should mark the query that asks for that file as critical.
        Since resources are limited, you should only mark a query as critical if it is critical to the answer.
        The more queries you mark as critical, the more resources will be consumed and the less it is any critical query will receive enough resources.
        Be very conservative about marking queries as critical.
        Typically, only one of the queries should be marked as critical.
        More than one critical query is allowed but should be uncommon.
        If you are not sure, leave this field out.
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "code_section_requests": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "description": {
                            "type": "string",
                            "description": request_code_description,
                        },
                        "path": {
                            "type": "string",
                            "description": request_code_path,
                        },
                        "contains_string": {
                            "type": "string",
                            "description": request_code_contains_string,
                        },
                        "critical": {
                            "type": "boolean",
                            "description": is_critical_description,
                        },
                    },
                    "required": [
                        "description",
                    ],
                },
            },
        },
        "required": ["code_section_requests"],
    }


def format_dialog_as_string(dialog_messages: list[Exchange], char_budget: int) -> str:
    """Format the dialog messages as a string instead of a list of messages."""

    used_chars = 0
    message_strings = []
    for message in reversed(dialog_messages):
        # Handle request message
        if isinstance(message.request_message, str):
            message_str = f"User message:\n{message.request_message}\n"
        else:
            message_str = ""
            for node in message.request_nodes:
                if node.type == ChatRequestNodeType.TEXT:
                    assert node.text_node is not None
                    message_str += f"User message:\n{node.text_node.content}\n"
                elif node.type == ChatRequestNodeType.TOOL_RESULT:
                    assert node.tool_result_node is not None
                    message_str += f"Tool result:\n{node.tool_result_node.content}\n"

        # Handle response message
        if isinstance(message.response_text, str):
            message_str += f"Assistant message:\n{message.response_text}\n"
        else:
            for node in message.response_nodes:
                if node.type == ChatResultNodeType.RAW_RESPONSE:
                    message_str += f"Assistant message:\n{node.content}\n"
                elif node.type == ChatResultNodeType.TOOL_USE:
                    assert node.tool_use is not None
                    message_str += f"Tool call:{node.tool_use.name}:\n"
                    message_str += str(node.tool_use.input) + "\n"

        if used_chars + len(message_str) > char_budget:
            break
        used_chars += len(message_str)
        message_strings.append(message_str)

    return "\n".join(reversed(message_strings))


def interleave_sequences(sequences):
    """Interleave multiple sequences, continuing with remaining elements when shorter sequences end.

    Example:
    >>> list(interleave_sequences([[1, 2], [4, 5, 6, 7]]))
    [1, 4, 2, 5, 6, 7]
    """
    # Convert all sequences to iterators
    iterators = [iter(seq) for seq in sequences]
    # Continue while we have any non-empty iterator
    while iterators:
        # Try to get next item from each iterator
        for i in range(len(iterators)):
            try:
                yield next(iterators[i])
            except StopIteration:
                # Remove exhausted iterator
                iterators.pop(i)
                break
