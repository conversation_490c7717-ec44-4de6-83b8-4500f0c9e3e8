"""General tool definition."""

import json
from abc import abstractmethod
from dataclasses import dataclass
from typing import Any, Generic, TypeVar

import grpc
import structlog
from google.rpc import status_pb2
from pydantic import BaseModel

from base.third_party_clients.third_party_model_client import ToolDefinition
from services.agents import agents_pb2
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext

log = structlog.get_logger()


class RemoteToolError(Exception):
    """Raised when there is an error executing a remote tool."""

    pass


class ToolAuthenticationError(RemoteToolError):
    """Raised this when remote tool authentication fails."""

    pass


class ToolNotAvailableError(RemoteToolError):
    """Raised when a tool is not available due to configuration."""

    pass


class Tool:
    """Base class for a tool that can be called by an LLM."""

    id: agents_pb2.RemoteToolId.ValueType
    name: str
    description: str
    input_schema: dict[str, Any]
    tool_safety: agents_pb2.ToolSafety.ValueType = agents_pb2.ToolSafety.TOOL_UNSAFE
    """
    Default tool safety to return from `get_tool_safety`. If `get_tool_safety`
    is overridden this value may be ignored.
    """

    def get_tool_definition(
        self, request_context: RequestContext | None = None
    ) -> ToolDefinition:
        """Get the tool parameter for LLM."""
        return ToolDefinition(
            name=self.name,
            description=self.description,
            input_schema_json=json.dumps(self.input_schema),
        )

    def get_tool_safety(
        self, context: "ToolRequestContext"
    ) -> agents_pb2.ToolSafety.ValueType:
        """Return the safety type of the tool.

        Subclasses should override this method if they need to implement
        context specific logic for determining the tool safety. Otherwise
        simply setting self.tool_safety should be sufficient.
        """
        return self.tool_safety

    def check_tool_call_safe(
        self, tool_input: dict[str, Any], extra_input: "ToolRequestContext"
    ) -> bool:
        """Check if the tool call is safe with the given input.

        NOTE: The front-end currently only calls this method if `get_tool_safety`
        returns `TOOL_CHECK`.

        Returns:
            bool: True if the tool call is safe, False otherwise.
            For TOOL_SAFE tools, always returns True.
            For TOOL_UNSAFE tools, always returns False.
            For TOOL_CHECK tools, subclasses should override this method.
        """
        if self.tool_safety == agents_pb2.ToolSafety.TOOL_SAFE:
            return True
        if self.tool_safety == agents_pb2.ToolSafety.TOOL_UNSAFE:
            return False
        # For TOOL_CHECK, subclasses should override this method
        return False

    def run(
        self,
        tool_input: dict[str, Any],
        extra_tool_input: Any,
        request_context: RequestContext,
    ) -> str:
        """Execute the tool and return the output string and status code."""
        raise NotImplementedError()

    @abstractmethod
    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        """Return the availability status of the tool.

        This is used to determine whether the tool is available to the model without any extra authentication or configuration from the client .
        """
        raise NotImplementedError()

    def get_oauth_url(self, request_context: RequestContext) -> str:
        """Return the OAuth URL to redirect the user to when needed. Defaults to empty string since most tools don't need authentication. Integration tools should override this method."""
        return ""

    def revoke_tool_access(self, request_context: RequestContext) -> status_pb2.Status:  # type: ignore
        """Revoke tool access by deactivating OAuth for this tool.

        Returns:
            status_pb2.Status: The status of the operation
        """

        return status_pb2.Status(  # type: ignore
            code=grpc.StatusCode.UNIMPLEMENTED.value[0],
            message="Operation not implemented for this tool",
        )

    def test_tool_connection(
        self, request_context: RequestContext
    ) -> status_pb2.Status:  # type: ignore
        """Test the connection for this tool.

        Returns:
            status_pb2.Status: The status of the connection test
        """

        return status_pb2.Status(  # type: ignore
            code=grpc.StatusCode.UNIMPLEMENTED.value[0],
            message="Connection test not implemented for this tool",
        )


class ToolRequestContext(BaseModel):
    """Extra context available on every tool request that can be used by
    tools for implementing context specific logic checks (e.g.: adding
    tenant-based feature flag behavior).

    We go through this Pydantic model instead of adding arguments directly
    to the tool functions as a way to make adding more context easier
    to refactor while still having type checking.
    """

    request_context: RequestContext
    auth_info: AuthInfo


class BaseExtraToolInput:
    """Base class for extra tool input"""

    pass


@dataclass
class EmptyExtraToolInput(BaseExtraToolInput):
    """A placeholder for tools that have no extra tool input. Can also be
    passed when a tool can take extra input but none is provided."""

    pass


T = TypeVar("T", bound=BaseModel)
E = TypeVar("E", bound=BaseExtraToolInput)


class ValidatedTool(Tool, Generic[T, E]):
    """A tool that validates its input using pydantic.

    Instead of implementing `run`, implement `run_validated` and it will be called
    after the input is validated.

    Cohercion behavior can be roughly controled by `strict`. For more control,
    implement a custom validator in the input_model like this:

        from pydantic import BaseModel, validator

        class User(BaseModel):
            username: str
            password: str

            @validator('password')
            def password_must_be_strong(cls, v):
                if len(v) < 8:
                    raise ValueError('Password must be at least 8 characters')
                if not any(c.isupper() for c in v):
                    raise ValueError('Password must contain an uppercase letter')
                return v
    """

    id: agents_pb2.RemoteToolId.ValueType
    """The id of the tool. This is used to identify the tool in API requests."""

    name: str
    """The name of the tool. This is used to identify the tool in the model."""

    description: str
    """A description of the tool. This is shown to the model."""

    # This is awkward, but since python is duck typing you have to
    # always assign T to input_model in a subclass
    input_model: type[T]
    """The pydantic model that defines the input schema and validation requirements."""

    strict: bool = True
    """Whether to use strict validation. True by default. When set to False will try to coerce the
    input to the expected type.  For example "true" -> True, 1 -> "1", "1,2,3" -> ["1", "2", "3"] etc
    """

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        cls.input_schema = cls.input_model.model_json_schema()

    def check_tool_call_safe(
        self, tool_input: dict[str, Any], extra_input: ToolRequestContext
    ) -> bool:
        """Check if the tool call is safe with the given input. Returns
        True iff the tool call is safe.

        This implementation uses pydantic to validate the input and then calls
        `check_validated_input_safe` with the validated input.
        """
        if self.tool_safety == agents_pb2.ToolSafety.TOOL_SAFE:
            return True
        if self.tool_safety == agents_pb2.ToolSafety.TOOL_UNSAFE:
            return False
        validated_input = self.input_model.model_validate(
            tool_input, strict=self.strict
        )
        try:
            return self.check_validated_input_safe(validated_input)
        except Exception as e:
            log.error(f"Failed to check tool safety: {e}")
            return False

    @abstractmethod
    def check_validated_input_safe(self, validated_input: T) -> bool:
        """Check if the tool call is safe with validated input.

        Must be implemented by subclasses that use TOOL_CHECK safety level.
        For TOOL_SAFE and TOOL_UNSAFE tools, this method is not called.
        """
        return self.tool_safety == agents_pb2.ToolSafety.TOOL_SAFE

    def run(
        self,
        tool_input: dict[str, Any],
        extra_tool_input: E,
        request_context: RequestContext,
    ) -> str:
        """Execute the tool through `run_validated` after validating the input using pydantic."""
        validated_input = self.input_model.model_validate(
            tool_input, strict=self.strict
        )
        result = str(
            self.run_validated(validated_input, extra_tool_input, request_context)
        )
        log.info("Tool %s return size: %d", self.name, len(result))
        return result

    def run_validated(
        self, validated_input: T, extra_tool_input: E, request_context: RequestContext
    ) -> str:
        raise NotImplementedError()
