from unittest.mock import Mock

import pytest

import services.agent_loop.agent_loop_pb2 as agent_loop_pb2
import services.agents.agents_pb2_grpc as agents_pb2_grpc
import services.api_proxy.model_finder_pb2_grpc as model_finder_pb2_grpc
from services.agent_loop.server.agent_loop_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)


@pytest.fixture
def agent_loop_handler():
    ri_publisher = Mock(spec=RequestInsightPublisher)
    agents_client = Mock(spec=agents_pb2_grpc.AgentsStub)
    model_finder = Mock(spec=model_finder_pb2_grpc.ModelFinderStub)
    return Agent<PERSON>oop<PERSON>andler(
        ri_publisher=ri_publisher,
        agents_client=agents_client,
        model_finder=model_finder,
    )


@pytest.fixture
def auth_info():
    return AuthInfo(
        tenant_id="1234567890",
        tenant_name="test-tenant",
        shard_namespace="test-namespace",
        cloud="test-cloud",
    )


# TODO(jeff): currently just tests that the agent shuts down.
def test_agent_loop_handler_shutdown(agent_loop_handler, auth_info):
    request = agent_loop_pb2.BeachheadMessage()
    request.remote_agent_id = "test"
    request.sequence_id = 1
    request_context = RequestContext.create()
    response = agent_loop_handler.await_message(request, request_context, auth_info)
    assert response.remote_agent_id == "test"
    assert response.sequence_id == 2
    assert response.HasField("shutdown_loop")
