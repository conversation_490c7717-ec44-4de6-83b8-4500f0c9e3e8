syntax = "proto3";

package public_api;

import "google/api/annotations.proto";
import "google/protobuf/descriptor.proto";
import "google/protobuf/timestamp.proto";
import "services/request_insight/request_insight.proto";

option go_package = "github.com/augmentcode/augment/services/api_proxy/public_api";

// this file is a documentation of the public api.
//
// all endpoints have JSON documents for request and response bodies.
// The formats as describe below
//
// all endpoints are authenticated using an api token in the `AUTHORIZATION` header prefixed with
// `Bearer`. all endpoints should have an UUID string in the `x-request-id` header.
//
// all endpoints can have an UUID string in the `x-request-session-id` header. The header value
// is used to corrolate different requests from the same source session, e.g. the same vscode
// instance.

// The below service definition has two roles:
// - Documentation of all public endpoints, their HTTP methods, and their request/response types
// - A way to define the HTTP paths and methods for each endpoint for JSON => Protobuf transcoding
//
// If you are adding a new endpoint, you should:
// 1. Add the proto messages to this file
// 2. Define a new gRPC method on `Augment`
// 3. Add the HTTP annotations to the service definition.
// 4. Modify the client code with a wrapper using the new endpoint
//    - see `base/augment_client/client.go`
//
// We use google.api.http annotations to define the HTTP paths and methods.
// These paths are relative to the api root path (e.g., dogfood.api.augmentcode.com)
//
// Note that we do NOT implement fully featured `google.api.http` annotations.
// Specifically, the only parameter we use is the `pattern` field, which
// is used to define the HTTP method and explicit path. We do not allow for any
// wildcards, URL parameters, etc.
service Augment {
  rpc BatchUpload(BatchUploadRequest) returns (BatchUploadResponse) {
    option (google.api.http) = {
      post: "/batch-upload"
      body: "*"
    };
  }

  // the Chat endpoint used for the preference panel for "vendors", e.g. "aitutor.*"
  rpc Chat(ChatRequest) returns (ChatResponse) {
    option (google.api.http) = {
      post: "/chat"
      body: "*"
    };
  }

  // the Chat endpoint used for the chat panel in the extension
  rpc ChatStream(ChatRequest) returns (stream ChatResponse) {
    option (google.api.http) = {
      post: "/chat-stream"
      body: "*"
    };
  }

  rpc GenerateCommitMessageStream(GenerateCommitMessageRequest) returns (stream GenerateCommitMessageResponse) {
    option (google.api.http) = {
      post: "/generate-commit-message-stream"
      body: "*"
    };
  }

  rpc InstructionStream(InstructionRequest) returns (stream InstructionResponse) {
    option (google.api.http) = {
      post: "/instruction-stream"
      body: "*"
    };
  }

  rpc SmartPasteStream(InstructionRequest) returns (stream InstructionResponse) {
    option (google.api.http) = {
      post: "/smart-paste-stream"
      body: "*"
    };
  }

  rpc CheckpointBlobs(CheckpointBlobsRequest) returns (CheckpointBlobsResponse) {
    option (google.api.http) = {
      post: "/checkpoint-blobs"
      body: "*"
    };
  }

  rpc Completion(CompletionRequest) returns (CompletionResponse) {
    option (google.api.http) = {
      post: "/completion"
      body: "*"
    };
  }

  // endpoint to indicate completion request resolutions
  rpc ResolveCompletionsRpc(ResolveCompletions) returns (CompletionResolution) {
    option (google.api.http) = {
      post: "/resolve-completions"
      body: "*"
    };
  }

  rpc ResolveSmartPaste(SmartPasteResolution) returns (SmartPasteResolutionResponse) {
    option (google.api.http) = {
      post: "/resolve-smart-paste"
      body: "*"
    };
  }

  rpc ResolveInstruction(InstructionResolution) returns (InstructionResolutionResponse) {
    option (google.api.http) = {
      post: "/resolve-instruction"
      body: "*"
    };
  }

  rpc Edit(EditRequest) returns (EditResponse) {
    option (google.api.http) = {
      post: "/edit"
      body: "*"
    };
  }

  // endpoint to send feedback on edit suggestions
  rpc ResolveEdit(EditResolution) returns (ResolveEditResponse) {
    option (google.api.http) = {
      post: "/resolve-edit"
      body: "*"
    };
  }

  // endpoint to return next edit suggestions
  rpc NextEditStream(NextEditRequest) returns (stream NextEditResponse) {
    option (google.api.http) = {
      post: "/next-edit-stream"
      body: "*"
    };
  }

  // endpoint to indicate next edit request resolutions
  rpc ResolveNextEdit(NextEditResolutionBatch) returns (NextEditResolutionResponse) {
    option (google.api.http) = {
      post: "/resolve-next-edit"
      body: "*"
    };
  }

  // endpoint for recording next edit session events
  rpc NextEditSessionEvent(NextEditSessionEventBatch) returns (NextEditSessionEventResponse) {
    option (google.api.http) = {
      post: "/record-next-edit-session-event"
      body: "*"
    };
  }

  // endpoint for recording onboarding session events
  rpc OnboardingSessionEvent(OnboardingSessionEventBatch) returns (OnboardingSessionEventResponse) {
    option (google.api.http) = {
      post: "/record-onboarding-session-event"
      body: "*"
    };
  }

  // enabled only with exportFullData=true
  // i.e should never be called for normal customers
  rpc RecordPreferenceSample(PreferenceSample) returns (PreferenceSampleResponse) {
    option (google.api.http) = {
      post: "/record-preference-sample"
      body: "*"
    };
  }

  rpc FindMissing(FindMissingRequest) returns (FindMissingResponse) {
    option (google.api.http) = {
      post: "/find-missing"
      body: "*"
    };
  }

  // NOTE: This API is still experimental, and is not stable yet
  //
  // List all external source types - this will not change very often. A type is
  // a category of source, like Documentation, Github, Jira, etc.
  rpc ListExternalSourceTypes(ListExternalSourceTypesRequest) returns (ListExternalSourceTypesResponse) {
    // TODO: this should probably be a get
    option (google.api.http) = {post: "/list-external-source-types"};
  }

  // NOTE: This API is still experimental, and is not stable yet
  //
  // List external sources that can be part of the context for a generation
  // request.
  // This is intended to be a flexible enough API to list different kinds of
  // retrieval sources: documentation sets, Github repos, Github PRs, Jira
  // issues, etc.
  rpc SearchExternalSources(SearchExternalSourcesRequest) returns (SearchExternalSourcesResponse) {
    option (google.api.http) = {
      post: "/search-external-sources"
      body: "*"
    };
  }

  rpc GetImplicitExternalSources(GetImplicitExternalSourcesRequest) returns (GetImplicitExternalSourcesResponse) {
    option (google.api.http) = {
      post: "/get-implicit-external-sources"
      body: "*"
    };
  }

  // endpoint to return information about all available models
  // and other configuration information
  rpc GetModels(GetModelsRequest) returns (GetModelsResponse) {
    option (google.api.http) = {post: "/get-models"};
  }

  // Always use the /batch-upload endpoint to upload content.
  rpc Memorize(MemorizeRequest) returns (MemorizeResponse) {
    option (google.api.http) = {
      post: "/memorize"
      body: "*"
    };
  }

  // endpoint for the extension to report an error (internal or from another API call)
  rpc ReportError(ReportErrorRequest) returns (ReportErrorResponse) {
    option (google.api.http) = {
      post: "/report-error"
      body: "*"
    };
  }

  // endpoint for the client to report Prometheus metrics
  rpc ReportClientMetrics(ClientMetricsRequest) returns (ClientMetricsResponse) {
    option (google.api.http) = {
      post: "/client-metrics"
      body: "*"
    };
  }

  // endpoint for the client to report feature vectors
  rpc ReportFeatureVector(ReportFeatureVectorRequest) returns (ReportFeatureVectorResponse) {
    option (google.api.http) = {
      post: "/report-feature-vector"
      body: "*"
    };
  }

  // endpoint to send user feedback to request insights
  rpc SendCompletionFeedback(CompletionFeedback) returns (CompletionFeedbackResponse) {
    option (google.api.http) = {
      post: "/completion-feedback"
      body: "*"
    };
  }

  // endpoint to send user feedback on chat responses to request insights
  rpc SendChatFeedback(ChatFeedback) returns (ChatFeedbackResponse) {
    option (google.api.http) = {
      post: "/chat-feedback"
      body: "*"
    };
  }

  // endpoint to send client events to request insights and prometheus
  rpc SendClientCompletionTimeline(ClientCompletionTimelineRequest) returns (ClientCompletionTimelineResponse) {
    option (google.api.http) = {
      post: "/client-completion-timelines"
      body: "*"
    };
  }

  // endpoint to send user feedback on next edit responses to request insights
  rpc SendNextEditFeedback(NextEditFeedback) returns (NextEditFeedbackResponse) {
    option (google.api.http) = {
      post: "/next-edit-feedback"
      body: "*"
    };
  }

  rpc SaveChat(SaveChatRequest) returns (SaveChatResponse) {
    option (google.api.http) = {
      post: "/save-chat"
      body: "*"
    };
  }

  // Record generic request-insight request events. This is a simple wrapper around publishing to
  // the RI pub/sub queue, intended to save us from needing a new endpoint every time a client
  // wants to record a new event. It is still recommended to make a separate endpoint for
  // business-critical events like feedback and resolutions. Ask in #team-growth if you have
  // questions.
  rpc RecordRequestEvents(RecordRequestEventsRequest) returns (RecordRequestEventsResponse) {
    option (google.api.http) = {
      post: "/record-request-events"
      body: "*"
    };
  }

  // Record generic request-insight session events. This is a simple wrapper around publishing to
  // the RI pub/sub queue, intended to save us from needing a new endpoint every time a client
  // wants to record a new event.
  rpc RecordSessionEvents(RecordSessionEventsRequest) returns (RecordSessionEventsResponse) {
    option (google.api.http) = {
      post: "/record-session-events"
      body: "*"
    };
  }

  // This /agents/ API is experimental and subject to change.
  // Expected to be used by remote-agents project
  // It may not be available in all namespaces (expect 404)
  rpc LLMGenerate(LLMGenerateRequest) returns (LLMGenerateResponse) {
    option (google.api.http) = {
      post: "/agents/llm-generate"
      body: "*"
    };
  }

  // Agentic Retrieval which iterates on retrieval queries
  // with an LLM. The retrieval is based upon Chat RAG model(s).
  rpc CodebaseRetrieval(CodebaseRetrievalRequest) returns (CodebaseRetrievalResponse) {
    option (google.api.http) = {
      post: "/agents/codebase-retrieval"
      body: "*"
    };
  }

  // Agent accepting edit instruction and iterating on smart paste
  // instructions to accomplish the task
  rpc EditFile(EditFileRequest) returns (EditFileResponse) {
    option (google.api.http) = {
      post: "/agents/edit-file"
      body: "*"
    };
  }

  // List current tool definition, availability, and safety of tools
  // which can be invoked through RunRemoteTool
  // These tools are meant to be presented to an LLM through tool-use or
  // function-calling APIs.
  rpc ListRemoteTools(ListRemoteToolsRequest) returns (ListRemoteToolsResponse) {
    option (google.api.http) = {
      post: "/agents/list-remote-tools"
      body: "*"
    };
  }

  // Invoke a tool (see ListRemoteTools)
  rpc RunRemoteTool(RunRemoteToolRequest) returns (RunRemoteToolResponse) {
    option (google.api.http) = {
      post: "/agents/run-remote-tool"
      body: "*"
    };
  }

  // This API is used to revoke external access for a tool.
  // If there is an oauth grant, it will be revoked so that we will no
  // longer be able to access the external connection.
  rpc RevokeToolAccess(RevokeToolAccessRequest) returns (RevokeToolAccessResponse) {
    option (google.api.http) = {
      post: "/agents/revoke-tool-access"
      body: "*"
    };
  }

  // This API is used to test the connection to an external tool.
  rpc TestToolConnection(TestToolConnectionRequest) returns (TestToolConnectionResponse) {
    option (google.api.http) = {
      post: "/agents/test-tool-connection"
      body: "*"
    };
  }

  rpc CheckToolSafety(CheckToolSafetyRequest) returns (CheckToolSafetyResponse) {
    option (google.api.http) = {
      post: "/agents/check-tool-safety"
      body: "*"
    };
  }

  /////////////////// Remote agents //////////////////////////////////////////

  // All /remote-agents/ APIs are experimental and subject to change.
  rpc CreateRemoteAgent(CreateRemoteAgentRequest) returns (CreateRemoteAgentResponse) {
    option (google.api.http) = {
      post: "/remote-agents/create"
      body: "*"
    };
  }

  // All /remote-agents/ APIs are experimental and subject to change.
  rpc DeleteRemoteAgent(DeleteRemoteAgentRequest) returns (DeleteRemoteAgentResponse) {
    option (google.api.http) = {
      post: "/remote-agents/delete"
      body: "*"
    };
  }

  // All /remote-agents/ APIs are experimental and subject to change.
  rpc ListRemoteAgents(ListRemoteAgentsRequest) returns (ListRemoteAgentsResponse) {
    option (google.api.http) = {
      post: "/remote-agents/list"
      body: "*"
    };
  }

  // Streaming version of ListRemoteAgents
  rpc ListRemoteAgentsStream(ListRemoteAgentsStreamRequest) returns (stream ListRemoteAgentsStreamResponse) {
    option (google.api.http) = {
      post: "/remote-agents/list-stream"
      body: "*"
    };
  }

  // All /remote-agents/ APIs are experimental and subject to change.
  // TODO: add a version that streams the response back?
  rpc GetRemoteAgentChatHistory(GetRemoteAgentChatHistoryRequest) returns (GetRemoteAgentChatHistoryResponse) {
    option (google.api.http) = {
      post: "/remote-agents/get-chat-history"
      body: "*"
    };
  }

  // All /remote-agents/ APIs are experimental and subject to change.
  // Streaming version of GetRemoteAgentHistory
  rpc GetRemoteAgentHistoryStream(GetRemoteAgentHistoryStreamRequest) returns (stream GetRemoteAgentHistoryStreamResponse) {
    option (google.api.http) = {
      post: "/remote-agents/agent-history-stream"
      body: "*"
    };
  }

  // All /remote-agents/ APIs are experimental and subject to change.
  rpc RemoteAgentChat(RemoteAgentChatRequest) returns (RemoteAgentChatResponse) {
    option (google.api.http) = {
      post: "/remote-agents/chat"
      body: "*"
    };
  }

  // All /remote-agents/ APIs are experimental and subject to change.
  rpc InterruptRemoteAgent(InterruptRemoteAgentRequest) returns (InterruptRemoteAgentResponse) {
    option (google.api.http) = {
      post: "/remote-agents/interrupt"
      body: "*"
    };
  }

  // All /remote-agents/ APIs are experimental and subject to change.
  // TODO: Add a RemoveSSH API? or just use DeleteRemoteAgent?
  // Creates a new open SSH port for the remote agent workspace if necessary,
  // but may also reuse an existing one.
  rpc RemoteAgentAddSSHKey(RemoteAgentAddSSHKeyRequest) returns (RemoteAgentAddSSHKeyResponse) {
    option (google.api.http) = {
      post: "/remote-agents/add-ssh-key"
      body: "*"
    };
  }

  // All /remote-agents/ APIs are experimental and subject to change.
  // This APi enables us to get the logs from the workspace.
  rpc RemoteAgentWorkspaceLogs(RemoteAgentWorkspaceLogsRequest) returns (RemoteAgentWorkspaceLogsResponse) {
    option (google.api.http) = {
      post: "/remote-agents/logs"
      body: "*"
    };
  }

  //All /remote-agents/ APIs are experimental and subject to change.
  rpc PauseRemoteAgent(RemoteAgentPauseRequest) returns (RemoteAgentPauseResponse) {
    option (google.api.http) = {
      post: "/remote-agents/pause"
      body: "*"
    };
  }

  //All /remote-agents/ APIs are experimental and subject to change.
  rpc ResumeRemoteAgent(RemoteAgentResumeRequest) returns (RemoteAgentResumeResponse) {
    option (google.api.http) = {
      post: "/remote-agents/resume"
      body: "*"
    };
  }

  // All /agent-workspace/ APIs are experimental and subject to change.
  // The beachhead calls this to tell the backend about status updates
  rpc AgentWorkspaceReportStatus(AgentWorkspaceReportStatusRequest) returns (AgentWorkspaceReportStatusResponse) {
    option (google.api.http) = {
      post: "/agent-workspace/report-status"
      body: "*"
    };
  }

  // All /agent-workspace/ APIs are experimental and subject to change.
  // The beachhead calls this to tell the backend that it's ready for updates
  rpc AgentWorkspaceReportChatHistory(AgentWorkspaceReportChatHistoryRequest) returns (AgentWorkspaceReportChatHistoryResponse) {
    option (google.api.http) = {
      post: "/agent-workspace/report-chat-history"
      body: "*"
    };
  }

  // All /agent-workspace/ APIs are experimental and subject to change.
  // The beachhead calls this to listen for updates from the backend, like user
  // interrupts or user chat messages
  rpc AgentWorkspacePollUpdate(AgentWorkspacePollUpdateRequest) returns (AgentWorkspacePollUpdateResponse) {
    option (google.api.http) = {
      post: "/agent-workspace/poll-update"
      body: "*"
    };
  }

  // All /agent-workspace/ APIs are experimental and subject to change.
  // The beachhead calls this to stream updates from the backend, like user
  // interrupts or user chat messages. This is the streaming version of AgentWorkspacePollUpdate.
  rpc AgentWorkspaceStream(AgentWorkspaceStreamRequest) returns (stream AgentWorkspaceStreamResponse) {
    option (google.api.http) = {
      post: "/agent-workspace/stream"
      body: "*"
    };
  }

  // All /agent-workspace/ APIs are experimental and subject to change.
  // The beachhead calls this to tell the backend about logs updates during the setup
  rpc AgentWorkspaceReportSetupLogs(AgentWorkspaceReportSetupLogsRequest) returns (AgentWorkspaceReportSetupLogsResponse) {
    option (google.api.http) = {
      post: "/agent-workspace/report-setup-logs"
      body: "*"
    };
  }

  /////////////////// GitHub Processor //////////////////////////////////////////

  // List GitHub repositories for the authenticated user
  rpc ListGithubReposForAuthenticatedUser(ListGithubReposForAuthenticatedUserRequest) returns (ListGithubReposForAuthenticatedUserResponse) {
    option (google.api.http) = {
      post: "/github/list-repos"
      body: "*"
    };
  }

  // List branches for a GitHub repository
  rpc ListGithubRepoBranches(ListGithubRepoBranchesRequest) returns (ListGithubRepoBranchesResponse) {
    option (google.api.http) = {
      post: "/github/list-branches"
      body: "*"
    };
  }

  // Get a specific GitHub repository by owner and name
  rpc GetGithubRepo(GetGithubRepoRequest) returns (GetGithubRepoResponse) {
    option (google.api.http) = {
      post: "/github/get-repo"
      body: "*"
    };
  }

  // Check if the user has GitHub OAuth configured
  rpc IsUserGithubConfigured(IsUserGithubConfiguredRequest) returns (IsUserGithubConfiguredResponse) {
    option (google.api.http) = {
      post: "/github/is-user-configured"
      body: "*"
    };
  }

  // Create a new pull request
  rpc CreatePullRequest(CreatePullRequestRequest) returns (CreatePullRequestResponse) {
    option (google.api.http) = {
      post: "/github/create-pull-request"
      body: "*"
    };
  }

  // Get user subscription information
  rpc GetSubscriptionInfo(GetSubscriptionInfoRequest) returns (GetSubscriptionInfoResponse) {
    option (google.api.http) = {
      post: "/subscription-info"
      body: "*"
    };
  }
}

enum ApiVersion {
  // List of supported API versions.
  //
  // The client should send this in the `x-api-version` header.
  //
  // The server should return a 406 Not Acceptable error if the client's version
  // is not supported. The client will react by asking the user to upgrade.
  //
  // When adding a new version, use the next available value and leave a comment
  // describing the changes. When deprecating a version, move the "this is the oldest
  // supported version" comment and update the corresponding api_proxy/server check.

  // Any client before `x-api-version` was added.
  UNSPECIFIED = 0;
  // ^ this is the oldest supported version

  // First version where clients send the `x-api-version` header. No other particular
  // expecations about what clients send or don't but it probably lets us retire some
  // very old, deprecated endpoints and fields.
  VERSIONING_ADDED = 1;

  // Clients on this version or higher should not send `memories` or `blob_names` in
  // backend requests (use the checkpoint-aware `blobs` message instead).
  BLOB_CHECKPOINTS = 2;
}

message CheckpointBlobsRequest {
  // The collection of blobs to register with the backend
  Blobs blobs = 1;
}

message CheckpointBlobsResponse {
  // The new id resulting from a checkpoint
  string new_checkpoint_id = 1;
}

// tab-switch event on the client
message TabSwitchEvent {
  // The path that was switched to
  string path = 1 [debug_redact = true];

  // The blob name of the file that was switched to
  string file_blob_name = 2 [debug_redact = true];
}

// git-diff output for one file on the client
message GitDiffFileInfo {
  // The blob name that contains the contents of the diff
  string content_blob_name = 1;

  // The blob name of the file that the diff affects
  string file_blob_name = 2;
}

// recent content written by the client
message ReplacementText {
  // The blob name and path that this change applies to
  string blob_name = 1;
  string path = 2 [debug_redact = true];
  // The start and end of the modified region of the blob
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  uint32 char_start = 3;
  uint32 char_end = 4;
  // The new content of the modified region
  string replacement_text = 5 [debug_redact = true];
  // Indicates whether the blob already contains this change
  bool present_in_blob = 6;
  // The expected blob name of the file after applying all replacement text
  optional string expected_blob_name = 7;
}

// recent client events
message RecencyInfo {
  repeated TabSwitchEvent tab_switch_events = 1;
  repeated GitDiffFileInfo git_diff_file_info = 2;
  repeated ReplacementText recent_changes = 3;
}

// A single contiguous edit in a file
message FileEdit {
  // The start character offset of the edit in the before version of the file
  uint32 before_start = 1;

  // The text that was removed in the before version of the file
  string before_text = 2 [debug_redact = true];

  // The start character offset of the edit in the after version of the file
  uint32 after_start = 3;

  // The text that was inserted in the after version of the file
  string after_text = 4 [debug_redact = true];
}

message FileEditEvent {
  string path = 1 [debug_redact = true];

  string before_blob_name = 2;

  string after_blob_name = 3;

  repeated FileEdit edits = 4;
}

message CompletionRequest {
  // name of the model to use
  optional string model = 1;

  // the prefix prompt at which the completion should be inserted
  string prompt = 2 [debug_redact = true];

  // relative file path to a given relevant root (e.g. the .augmentroot file).
  string path = 3 [debug_redact = true];

  // an optional suffix for fill-in-the-middle.
  // usually the text after the cursor location at which the completion should
  // be inserted
  optional string suffix = 4 [debug_redact = true];

  // name of memory objects that the client believes the server has for the given model
  // Each name is a sha256 in hex format of the content.
  //
  // the server should return any memory names the server doesn't have in the response
  // the server should perform the completion with the subset of memory objects it has
  //
  // This is deprecated, please use blobs instead.
  repeated string memories = 5 [deprecated = true];

  // top-k value for sampling
  // if no value is set, a default value defined by the server should be used.
  optional int32 top_k = 6;

  // top-p value for sampling
  // if no value is set, a default value defined by the server should be used
  optional float top_p = 7;

  // temperature for sampling
  // if no value is set, a default value defined by the server should be used

  optional float temperature = 8;

  // maximal number of tokens to generate
  // the server is free to generate less tokens than the value provided.

  optional int32 max_tokens = 9;

  // programming laguage that the prompt is in.
  //
  // the name should be one of the language names given to the client
  // in the lanuages field of the `Model`.
  // if not set (or empty), the server might detect a language based on the path
  // or use an undefined default.
  optional string lang = 10;

  // blob name of the blob in which the completion is requested, if known.
  optional string blob_name = 11;

  // character offset within the blob of the start of the prefix, if known
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 prefix_begin = 12;

  // character offset of the location of the cursor, if known. Unlike prefix_begin and suffix_end,
  // cursor_position is the character offset within the current state of the file, not within the
  // given blob_name.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 cursor_position = 13;

  // character offset within the blob of the end of the suffix, if known
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 suffix_end = 14;

  // recent events on the client
  optional RecencyInfo recency_info = 15;

  // Delta format of specifying blob names, with a checkpoint ID. Replaces
  // memories
  Blobs blobs = 16;

  // sequence id for the request; see comments in CompletionRequest
  optional uint32 sequence_id = 17;

  // probe the server to return the current indexing status;
  // when true, CompletionResponse will only include unknown_blob_names and text will always be
  // empty. This flag is deprecated. Please use the FindMissing endpoint instead.
  optional bool probe_only = 18 [deprecated = true];

  // A user filter threshold (undefined, 0.0 to 1.0) for filtering low quality completions.
  optional float filter_threshold = 19;

  // More granular events representing the most recent edits.
  repeated FileEditEvent edit_events = 20;
}

message CompletionItem {
  string text = 1 [debug_redact = true];

  // Skip token handling for details see notion
  // https://www.notion.so/RFC-Handling-skip-tokens-in-backend-and-extension-defbc79c81094d708d961880e399b80c
  // The completion text up to the first skip token is returned in `text`
  // for backwards compatibility
  // `skipped_suffix` represent the starting part of the suffix that is being replaced
  // by an inline completion item
  string skipped_suffix = 2 [debug_redact = true];
  // `suffix_replacement_text` is the text to replace the suffix string with.
  string suffix_replacement_text = 3 [debug_redact = true];

  // The filter score of the completion. A higher score means the completion is more likely to be
  // rejected.
  optional float filter_score = 4;
}

message CompletionResponse {
  // the completion text to be inserted
  string text = 1 [debug_redact = true];

  // all memory object names from the memories parameter the server doesn't know about
  repeated string unknown_memory_names = 2;

  // Suggested prefix and suffix lengths for the next completion request that uses
  // the same model. Returning these values from every request proactively informs
  // the client of any changes in the model parameters.
  uint32 suggested_prefix_char_count = 3;
  uint32 suggested_suffix_char_count = 4;

  // Returned if the checkpoint id is unknown to the server
  optional string unknown_checkpoint_id = 5 [deprecated = true];

  // Max completion timeout in MS
  optional uint32 completion_timeout_ms = 6 [deprecated = true];

  // Multiple completion items can be provided to VSCode.
  // Each item contains information in addition to the completion text.
  // However, we cannot guarantee that VSCode shows all items here,
  // or that the first one is shown as default.  It has its own
  // internal filtering and ranking mechanism that we cannot control.
  repeated CompletionItem completion_items = 7;

  // Set to True if the checkpoint was not found.
  bool checkpoint_not_found = 8;
}

message EditRequest {
  // name of the model to use
  optional string model = 1;

  // the prefix before the selected text
  string prefix = 2 [debug_redact = true];

  // the text selected in the current buffer
  string selected_text = 3 [debug_redact = true];

  // the text after the selected text
  string suffix = 4 [debug_redact = true];

  // name of memory objects that the client believes the server has for the given model
  // Each name is a sha256 in hex format of the content.
  //
  // the server should return any memory names the server doesn't have in the response
  // the server should perform the edit with the subset of memory objects it has
  //
  // This is deprecated, please use blobs instead.
  repeated string blob_names = 5 [deprecated = true];

  // relative file path to a given relevant root (e.g. the .augmentroot file).
  optional string path = 6 [debug_redact = true];

  // the instruction to be used for the prompt
  string instruction = 7 [debug_redact = true];

  // programming laguage that the prompt is in.
  //
  // the name should be one of the language names given to the client
  // in the lanuages field of the `Model`.
  // if not set (or empty), the server might detect a language based on the path
  // or use an undefined default.
  optional string lang = 8;

  // blob name of the blob in which the edit is requested, if known.
  optional string blob_name = 9;

  // character offset within the blob of the start of the prefix, if known
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 prefix_begin = 10;

  // character offset within the blob of the end of the suffix, if known
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 suffix_end = 11;

  // sequence id for the request
  optional uint32 sequence_id = 12;

  // Delta format of specifying blob names, with a checkpoint ID. Replaces
  // blob_names
  Blobs blobs = 13;
}

message EditResponse {
  // the edit text to be inserted
  string text = 1 [debug_redact = true];

  // The subset of the requested blobs that are unknown to the server
  repeated string unknown_blob_names = 2;

  // Set to True if the checkpoint was not found.
  bool checkpoint_not_found = 3;
}

message InstructionRequest {
  // name of the model to use
  optional string model = 1;

  // the prefix before the selected text
  string prefix = 2 [debug_redact = true];

  // the text selected in the current buffer
  string selected_text = 3 [debug_redact = true];

  // the text after the selected text
  string suffix = 4 [debug_redact = true];

  // relative file path to a given relevant root (e.g. the .augmentroot file).
  optional string path = 5 [debug_redact = true];

  // the instruction to be used for the prompt
  string instruction = 6 [debug_redact = true];

  // programming laguage that the prompt is in.
  //
  // the name should be one of the language names given to the client
  // in the lanuages field of the `Model`.
  // if not set (or empty), the server might detect a language based on the path
  // or use an undefined default.
  optional string lang = 7;

  // blob name of the blob in which the edit is requested, if known.
  optional string blob_name = 8;

  // character offset within the blob of the start of the prefix, if known
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 prefix_begin = 9;

  // character offset within the blob of the end of the suffix, if known
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 suffix_end = 10;

  // sequence id for the request
  optional uint32 sequence_id = 11;

  // Delta format of specifying blob names, with a checkpoint ID. Replaces
  // blob_names
  Blobs blobs = 12;

  // History of previous instructions and responses
  repeated Exchange chat_history = 13;

  // (For smart paste) - Selected Code block to paste
  optional string code_block = 14 [debug_redact = true];

  // (For smart paste) - Target file path in which paste should happen
  optional string target_file_path = 15 [debug_redact = true];

  // (For smart paste) - Target file content to paste into
  optional string target_file_content = 16 [debug_redact = true];

  // (For smart paste) - Request ID of the exchange to which the context code should be added
  optional string context_code_exchange_request_id = 17;

  // User guidelines
  optional string user_guidelines = 18 [debug_redact = true];

  // Workspace guidelines
  optional string workspace_guidelines = 19 [debug_redact = true];
}

message InstructionResponse {
  // Human readable explanation of the changes suggested
  string text = 1 [debug_redact = true];

  // The subset of the requested blobs that are unknown to the server
  repeated string unknown_blob_names = 2;

  // Set to True if the checkpoint was not found.
  bool checkpoint_not_found = 3;

  // the text to be pasted
  optional string replacement_text = 4 [debug_redact = true];

  // code line in which to start the replacement, relative to sent code (incl. prefix)
  optional uint32 replacement_start_line = 5;

  // code line in which to end the replacement, relative to sent code (incl. suffix)
  optional uint32 replacement_end_line = 6;

  // Old text to replace
  optional string replacement_old_text = 7 [debug_redact = true];

  // Sequence ID of the replacment
  optional uint32 replacement_sequence_id = 8;
}

message PerFileChangeStats {
  string file_path = 1 [debug_redact = true];
  int32 insertion_count = 2;
  int32 deletion_count = 3;
  string old_file_path = 4 [debug_redact = true];
}

message PerTypeChangedFileStats {
  int32 changed_file_count = 1;

  // The first few changed files. To avoid transmitting too many files, we truncate in
  // the middle. It is important to truncate in the middle so that the model can better
  // understand the overall change.
  repeated PerFileChangeStats per_file_change_stats_head = 2;

  // The last few changed files. To avoid transmitting too many files, we truncate in
  // the middle. It is important to truncate in the middle so that the model can better
  // understand the overall change.
  repeated PerFileChangeStats per_file_change_stats_tail = 3;
}

message ChangedFileStats {
  PerTypeChangedFileStats added_file_stats = 1;
  PerTypeChangedFileStats broken_file_stats = 2;
  PerTypeChangedFileStats copied_file_stats = 3;
  PerTypeChangedFileStats deleted_file_stats = 4;
  PerTypeChangedFileStats modified_file_stats = 5;
  PerTypeChangedFileStats renamed_file_stats = 6;
  PerTypeChangedFileStats unmerged_file_stats = 7;
  PerTypeChangedFileStats unknown_file_stats = 8;
}

message GenerateCommitMessageRequest {
  // Stats of changed files for the repository diff.
  optional ChangedFileStats changed_file_stats = 1;

  // The diff of the repository.
  optional string diff = 2;

  // Relevant commit messages that inform the model about current commit's context.
  repeated string relevant_commit_messages = 3;

  // Example commit messages from the repository to illustrate the style and conventions
  repeated string example_commit_messages = 4;
}

message GenerateCommitMessageResponse {
  // The reponse text
  string text = 1 [debug_redact = true];
}

message ChatFeatureDetectionFlags {
  optional bool support_markdown = 1 [deprecated = true]; // Deprecated Nov 2024
  optional bool support_raw_output = 2;
  optional bool support_relevant_sources = 3;
  optional bool support_tool_use_start = 4;
  optional bool support_parallel_tool_use = 5;
}

enum ChatMode {
  CHAT = 0;
  AGENT = 1;
  MEMORIES = 2;
  ORIENTATION = 3;
  MEMORIES_COMPRESSION = 4;
  REMOTE_AGENT = 5;
}

message ListRemoteAgentsRequest {}

enum RemoteAgentStatus {
  AGENT_UNSPECIFIED = 0;

  // The agent has been created, but has not yet been assigned to a Workspace.
  AGENT_PENDING = 5;

  // The agent is starting and setting up its environment
  AGENT_STARTING = 1;

  // The agent is actively working on its task
  AGENT_RUNNING = 2;

  // The agent is idle and waiting for further instructions, it may have
  // completed its task or may need further instruction
  AGENT_IDLE = 3;

  // The agent encountered an error and cannot continue
  AGENT_FAILED = 4;

  // The agent is in the process of being paused
  AGENT_PAUSING = 6;

  // The agent is paused and can be resumed
  AGENT_PAUSED = 7;

  // There is no deleted status because a deleted agent will no longer show up
  // in any API calls.
}

enum RemoteAgentWorkspaceStatus {
  REMOTE_AGENT_WORKSPACE_STATUS_UNSPECIFIED = 0;
  REMOTE_AGENT_WORKSPACE_STATUS_RUNNING = 1;
  REMOTE_AGENT_WORKSPACE_STATUS_PAUSING = 2;
  REMOTE_AGENT_WORKSPACE_STATUS_PAUSED = 3;
  REMOTE_AGENT_WORKSPACE_STATUS_RESUMING = 4;
  REMOTE_AGENT_WORKSPACE_STATUS_DELETING = 5;
}

enum RemoteWorkspaceSetupStepStatus {
  // Unknown status  (if skipped it will also be unknown)
  REMOTE_WORKSPACE_SETUP_STEP_STATUS_UNKNOWN = 0;
  REMOTE_WORKSPACE_SETUP_STEP_STATUS_RUNNING = 1;
  REMOTE_WORKSPACE_SETUP_STEP_STATUS_SUCCESS = 2;
  REMOTE_WORKSPACE_SETUP_STEP_STATUS_FAILURE = 3;
  REMOTE_WORKSPACE_SETUP_STEP_STATUS_SKIPPED = 4;
}

message RemoteWorkspaceSetupStep {
  // A string we can show users, like "Cloning repository" or "Running setup script"
  string step_description = 1;
  string logs = 2;
  RemoteWorkspaceSetupStepStatus status = 3;
  // this is monotonically increasing but not necessarily consecutive.
  uint32 sequence_id = 4;
  // This is the step number, starting from 0. It is consecutive.
  uint32 step_number = 5;
  // TODO: add an enum here if the UI wants to be able to identify specific steps?
}

message RemoteWorkspaceSetupStatus {
  // While the agent is in starting state, should always contain at least one step that is running
  repeated RemoteWorkspaceSetupStep steps = 1;
}

message RemoteAgent {
  string remote_agent_id = 1;

  RemoteAgentWorkspaceSetup workspace_setup = 2;

  RemoteAgentStatus status = 3;

  google.protobuf.Timestamp started_at = 4;
  google.protobuf.Timestamp updated_at = 5;

  // A summary of the whole agentic session
  string session_summary = 6;

  // Summaries of what happened in each turn
  repeated string turn_summaries = 7;

  optional RemoteAgentSSHConfig ssh_config = 8;

  // Whether this agent is a setup script agent meant to generate a setup script.
  optional bool is_setup_script_agent = 9;

  // Whether the agent has unread updates
  // If true, it means the agent has updates that haven't been viewed by the user
  optional bool has_updates = 10;
  RemoteAgentWorkspaceStatus workspace_status = 11;

  google.protobuf.Timestamp expires_at = 12;
}

message RemoteAgentSSHConfig {
  repeated string public_keys = 1;

  // The hostname and config are the same for all public keys.
  string hostname = 2;
  repeated SSHConfigOption ssh_config_options = 3;
}

message ListRemoteAgentsResponse {
  repeated RemoteAgent remote_agents = 1;

  // The maximum number of total remote agents this user can have
  int32 max_remote_agents = 2;

  // The maximum number of active remote agents this user can have (not including paused agents)
  int32 max_active_remote_agents = 3;
}

message ListRemoteAgentsStreamRequest {
  // Optional: The timestamp of the last update the client has seen.
  // If not provided or mismatching, the server will send the current state of all agents
  // and then stream subsequent updates.
  //
  // NOTE: Timestamp-based optimization is currently unimplemented, so the server
  // will always send the current state of all agents in the initial response.
  optional google.protobuf.Timestamp last_update_timestamp = 1;
}

// AgentListUpdateType defines the different types of updates that can be sent in an agent list stream.
// Each update type corresponds to a specific field in the AgentListUpdate message.
enum AgentListUpdateType {
  // Default unspecified value - should not be used
  AGENT_LIST_UPDATE_TYPE_UNSPECIFIED = 0;

  // A new agent was created
  AGENT_LIST_AGENT_ADDED = 1;

  // An existing agent was updated (status, workspace status, or other properties changed)
  AGENT_LIST_AGENT_UPDATED = 2;

  // An agent was deleted
  AGENT_LIST_AGENT_DELETED = 3;

  // Initial state: all current agents (sent when client first connects)
  AGENT_LIST_ALL_AGENTS = 4;
}

// AgentListUpdate represents a single update in the agent list stream.
// Only one of the optional fields should be set, determined by the 'type' field.
// This allows for different types of updates to be sent in the same stream.
message AgentListUpdate {
  // The type of update, which determines which field is set
  AgentListUpdateType type = 1;

  // The timestamp when this update occurred
  google.protobuf.Timestamp update_timestamp = 2;

  // For AGENT_LIST_AGENT_ADDED, AGENT_LIST_AGENT_UPDATED, and AGENT_LIST_ALL_AGENTS updates
  optional RemoteAgent agent = 3;

  // For AGENT_LIST_AGENT_DELETED updates - just the agent ID
  optional string deleted_agent_id = 4;

  // For AGENT_LIST_ALL_AGENTS updates - all current agents
  repeated RemoteAgent all_agents = 5;

  // The maximum number of total remote agents this user can have
  optional int32 max_agents = 6;

  // The maximum number of active remote agents this user can have (not including paused agents)
  optional int32 max_active_agents = 7;
}

message ListRemoteAgentsStreamResponse {
  // A list of structured updates. Multiple updates can be included in a single
  // response for efficiency.
  repeated AgentListUpdate updates = 1;
}

message GetRemoteAgentChatHistoryRequest {
  string remote_agent_id = 1;

  // The sequence ID of the last processed exchange.
  // Use 0 to indicate that this is the first request, sequence IDs are
  // assigned starting from 1.
  // The server will return exchanges with sequence IDs greater than this value.
  // This enables clients to implement pagination by requesting subsequent pages
  // using the sequence ID of the last exchange from the previous response.
  uint32 last_processed_sequence_id = 2;
}

message GetRemoteAgentHistoryStreamRequest {
  // The ID of the remote agent to stream history for
  string remote_agent_id = 1;

  // The sequence ID of the last processed exchange.
  // The server will return exchanges with sequence IDs greater than this value.
  // This enables clients to implement pagination and reconnection by requesting
  // subsequent updates using the sequence ID of the last exchange from the previous response.
  uint32 last_processed_sequence_id = 2;
}

message ChangedFile {
  // Empty if the file was added
  string old_path = 1;

  // Empty if the file was deleted
  string new_path = 2;

  enum FileChangeType {
    ADDED = 0;
    DELETED = 1;
    MODIFIED = 2;
    RENAMED = 3;
  }
  FileChangeType change_type = 3;

  // TODO: modify this to work better for large files, where we may not want to
  // send the full file contents every time it is changed.
  string old_contents = 4;
  string new_contents = 5;
}

message RemoteAgentExchange {
  Exchange exchange = 1;

  // Files changed as a result of this exchange
  repeated ChangedFile changed_files = 2;

  // Sequence ID for tracking the order of exchanges
  // Sequence ID must be monotonic starting from 1
  uint32 sequence_id = 3;

  // Optional user-facing summary of the turn (or last few turns).
  optional string turn_summary = 4;

  // Timestamp when the exchange was finished.
  google.protobuf.Timestamp finished_at = 5;

  // If set, this indicates to the frontend that some or all of the changed files were unable to be
  // returned. The value is a list of file paths that were skipped. Note that this list may not be
  // exhaustive. To know if the list is exhaustive, check the length against the
  // changed_files_skipped_count field.
  repeated string changed_files_skipped = 6;

  // If set, this indicates the total number of changed files that were skipped.
  optional uint32 changed_files_skipped_count = 7;
}

message GetRemoteAgentChatHistoryResponse {
  repeated RemoteAgentExchange chat_history = 2;

  // User-facing summary of the session.
  optional string session_summary = 3;

  optional RemoteAgent remote_agent = 4;
}

// Define node types for streaming responses
enum AgentHistoryUpdateType {
  // Default unspecified value - should not be used
  AGENT_HISTORY_UPDATE_TYPE_UNSPECIFIED = 0;

  // A complete exchange between the user and agent
  AGENT_HISTORY_EXCHANGE = 1;

  // An incremental update to an existing exchange (text or nodes)
  AGENT_HISTORY_EXCHANGE_UPDATE = 2;

  // An update to the agent's status
  AGENT_HISTORY_AGENT_STATUS = 3;
}

// ExchangeUpdate represents an incremental update to an exchange.
// It is used to stream partial responses as they become available,
// including both text updates and result node updates.
message ExchangeUpdate {
  // The request ID associated with this update.
  // This can be used to correlate the update with a specific request.
  optional string request_id = 1;

  // The sequence ID of the exchange being updated.
  // This allows clients to maintain order and handle reconnection scenarios.
  uint32 sequence_id = 2;

  // The text to append to the current response.
  // This is the incremental text that should be added to the existing response.
  string appended_text = 3;

  // The nodes to append to the current response.
  // These are additional result nodes that should be added to the existing response.
  repeated ChatResultNode appended_nodes = 4;

  // Files changed as a result of this exchange update.
  // These are incremental file changes that should be added to the existing changed_files.
  repeated ChangedFile appended_changed_files = 5;
}

// AgentHistoryUpdate represents a single update in the agent history stream.
// Only one of the optional fields should be set, determined by the 'type' field.
// This allows for different types of updates to be sent in the same stream.
message AgentHistoryUpdate {
  // The type of update, which determines which field is set
  AgentHistoryUpdateType type = 1;

  // For AGENT_HISTORY_EXCHANGE updates - a complete exchange between user and agent
  optional RemoteAgentExchange exchange = 2;

  // For AGENT_HISTORY_EXCHANGE_UPDATE updates - incremental updates to an existing exchange
  // This can include text updates and/or node updates
  optional ExchangeUpdate exchange_update = 3;

  // For AGENT_HISTORY_AGENT_STATUS updates - updates to the agent's status
  optional RemoteAgent agent = 4;
}

message GetRemoteAgentHistoryStreamResponse {
  // A list of structured updates. Multiple updates can be included in a single
  // response. The updates should be associated with at most one sequence ID.
  repeated AgentHistoryUpdate updates = 1;
}

message McpServerConfig {
  string command = 1;
  repeated string args = 2;
  optional int32 timeout_ms = 3;
  map<string, string> env = 4;
  optional bool use_shell_interpolation = 5;
  optional string name = 6;
  optional bool disabled = 7;
}

message RemoteAgentChatRequestDetails {
  // User message, represented as a list of structured content blocks
  // Note: if the `message` field is present, it should be interpreted as
  // a leading text node followed by the list of nodes.
  repeated ChatRequestNode request_nodes = 1;

  // Additional system prompt specified by the user.
  optional string user_guidelines = 2;

  // Additional system prompt specified by the workspace.
  optional string workspace_guidelines = 3;

  // Memories to be included for the Agent system prompt.
  optional string agent_memories = 4;

  // The model ID to use for the request.
  optional string model_id = 5;

  // MCP server configurations to use for this request.
  repeated McpServerConfig mcp_servers = 6;
}

message GithubCommitRef {
  string repository_url = 1;
  string git_ref = 2;
  // Output of git diff --patch <git_ref> in the repo on the client.
  optional string patch = 3;
}

message RemoteAgentWorkspaceSetup {
  oneof starting_files {
    GithubCommitRef github_commit_ref = 1;
  }

  // global git config to set, the values here will be passed to
  // `git config --global <key> <value>`
  // Recommended keys to pass are user.name and user.email
  map<string, string> git_config = 2;
}

message CreateRemoteAgentRequest {
  // The repository + commit to start the agent workspace
  RemoteAgentWorkspaceSetup workspace_setup = 1;

  // The initial request to send to the agent
  RemoteAgentChatRequestDetails initial_request_details = 2;

  // The name of the model to use. Mainly intended for internal use.
  optional string model = 3;

  // The setup script to run to setup the agent workspace. Assumes bash.
  optional string setup_script = 4;

  // HACK: The token of the client to use for the remote agent. This is used
  // internally so that we can run remote agents in a dev deploy but have them
  // use some staging APIs, assuming that staging users are running the rest of
  // their extensions on staging.
  // FIXME: Remove this once remote agents are in staging.
  optional string token = 5 [debug_redact = true];

  // Whether this agent is a setup script agent meant to generate a setup script.
  optional bool is_setup_script_agent = 6;
}

message CreateRemoteAgentResponse {
  // The ID of the created agent
  string remote_agent_id = 1;

  // The status of the agent: either STARTING or ERROR
  RemoteAgentStatus status = 2;
}

message DeleteRemoteAgentRequest {
  string remote_agent_id = 1;
}

message DeleteRemoteAgentResponse {}

message InterruptRemoteAgentRequest {
  // Unique identifier for the remote agent to interupt
  string remote_agent_id = 1;
}

message InterruptRemoteAgentResponse {
  // The status of the agent after the interruptio attempt
  RemoteAgentStatus status = 1;
}

message RemoteAgentChatRequest {
  // Unique identifier for the remote agent
  string remote_agent_id = 1;

  RemoteAgentChatRequestDetails request_details = 2;
}

message RemoteAgentChatResponse {}

message RemoteAgentAddSSHKeyRequest {
  string remote_agent_id = 1;
  repeated string public_keys = 2;
}

message RemoteAgentResumeRequest {
  string remote_agent_id = 1;
}

message RemoteAgentResumeResponse {}

message RemoteAgentPauseRequest {
  string remote_agent_id = 1;
}

message RemoteAgentPauseResponse {}

message SSHConfigOption {
  // Key and value for a single SSH config option, based on
  // https://man7.org/linux/man-pages/man5/ssh_config.5.html.
  string key = 1;
  string value = 2;
}

message RemoteAgentAddSSHKeyResponse {
  // Returns all configured keys, not just the newly added ones.
  RemoteAgentSSHConfig ssh_config = 1;
}

message ChatRequest {
  // The name of the model to use.
  // If not specified, a model will be selected based upon mode
  optional string model = 1;

  // the path to the currently open file
  optional string path = 2 [debug_redact = true];

  // the code before the selected code
  optional string prefix = 3 [debug_redact = true];

  // the code selected in the current buffer
  optional string selected_code = 4 [debug_redact = true];

  // the code after the selcted code
  optional string suffix = 5 [debug_redact = true];

  // the message to be used for the prompt
  string message = 6 [debug_redact = true];

  // history of previous messages alternating request/response
  repeated Exchange chat_history = 7 [debug_redact = true];

  // blob name of the blob in which the completion is requested, if known.
  optional string blob_name = 8;

  // character offset within the blob of the start of the prefix, if known
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 prefix_begin = 9;

  // character offset within the blob of the location of the cursor, if known
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 cursor_position = 10;

  // character offset within the blob of the end of the suffix, if known
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 suffix_end = 11;

  // Blobs to be used in retrieval.
  //
  // This is deprecated, please use blobs instead.
  repeated string blob_names = 12 [deprecated = true];

  // Programming language of the current file.
  optional string lang = 13;

  // Sequence IDs should be strictly increasing within a session.
  //
  // Requests may cancel requests with lower or equal sequence ID from the
  // same session and namespace. This helps prevent accidental cancellation
  // of requests when network messages are reordered.
  uint32 sequence_id = 14;

  // Start of Retrieval sources
  // TODO: eventually move this into a new type, when we want to share this
  // across other APIs?

  // Blobs to include in the retrieval corpus, specified in the compact
  // delta form. Replaces blob_names
  Blobs blobs = 15;

  // Optional list of blobs to focus on in retrieval for this chat request
  repeated string user_guided_blobs = 17;

  // List of external source IDs to use for retrieval.
  repeated string external_source_ids = 19;

  // End of Retrieval sources

  // Makes the server use `preference_sampling_params`.
  // Needed to enable temperature sampling.
  optional bool enable_preference_collection = 16;

  // The request ID of the exchange to which the context code belongs.
  // "new" if context code belongs to the current user message.
  // "context code" includes selected code and current file.
  optional string context_code_exchange_request_id = 18;

  // if set to true, no external sources should be added automatically
  // by the host.   This includes the default `docset://augment` source
  // and implicit docsets.
  //
  // Note: the automatic detection of external sources can also be disabled for other reasons.
  //

  // This is an internal API for testing and evaluation purposes.
  optional bool disable_auto_external_sources = 20;

  // Flags to indicate to backend that the frontend supports specific features
  optional ChatFeatureDetectionFlags feature_detection_flags = 21;

  // Additional system prompt specified by the user.
  optional string user_guidelines = 22;

  // Additional system prompt specified by the workspace.
  optional string workspace_guidelines = 23;

  // Tools that the model can use
  repeated ToolDefinition tool_definitions = 24;

  // User message, represented as a list of structured content blocks
  // Note: if the `message` field is present, it should be interpreted as
  // a leading text node followed by the list of nodes.
  repeated ChatRequestNode nodes = 25;

  // Used to constrain model selection when model not explicitly specified
  // If model is specified, this should be ignored
  // Defaults to CHAT, so behavior of old clients is preserved
  ChatMode mode = 26;

  // Memories to be included for the Agent system prompt.
  optional string agent_memories = 27;

  // The persona type that the AI assistant should adopt for this chat turn.
  optional PersonaType persona_type = 28;

  repeated Rule rules = 29;

  // Whether this message is silent and should not be part of the chat history
  optional bool silent = 30;
}

enum RuleType {
  ALWAYS_ATTACHED = 0;
  MANUAL = 1;
}

message Rule {
  RuleType type = 1;

  string path = 2;

  string content = 3;
}

// The persona type that the AI assistant should adopt for this chat turn.
enum PersonaType {
  // The default persona, which is an expert software engineer.
  DEFAULT = 0;
  // The prototyper persona, which is an expert software engineer that is
  // focused on building new web apps.
  PROTOTYPER = 1;
  // The brainstorm persona, which is an expert software engineer that is
  // focused on planning and brainstorming solutions.
  BRAINSTORM = 2;
  // The reviewer persona, which is an expert software engineer that is
  // focused on reviewing code changes and identifying potential issues.
  REVIEWER = 3;
}

// information about external sources that were used in the chat response.
message IncorporatedExternalSource {
  // The name of the external source that was used.
  //
  // The value is meant to be shown the user.
  //
  // This might map to the name of a external source, but it might also be
  // a different text, e.g. a more detailed description of the source.
  string source_name = 1;

  // The URL the external source.
  optional string link = 2;
}

message ToolDefinition {
  string name = 1;
  string description = 2;
  string input_schema_json = 3;
}

enum ToolChoiceType {
  // The model can decide whether to use a tool or not.
  AUTO = 0;
  // The model must use some tool.
  ANY = 1;
  // The model must use the tool with the given name.
  TOOL = 2;
}

message ToolChoice {
  ToolChoiceType type = 1;
  // Valid for type TOOL; the name of the tool to use
  optional string name = 2;
}

message WorkspaceFileChunk {
  int32 char_start = 1;
  int32 char_end = 2;
  string blob_name = 3;
}

enum ChatResultNodeType {
  // The raw response from the model.
  // Content is a single string with raw response.
  RAW_RESPONSE = 0;

  // Our guess of what user will ask next.
  // Content is "{question1}\n{question2}".
  SUGGESTED_QUESTIONS = 1;

  // Indication that streaming of the main response finished.
  // Content is always empty.
  MAIN_TEXT_FINISHED = 2;

  // Workspace file chunks used in prompt
  // Every line in content is "{file_path}:{char_start}-{char_end}".
  WORKSPACE_FILE_CHUNKS = 3;

  // Sources that were useful to generate the response
  // Every line in content is "{file_path}".
  RELEVANT_SOURCES = 4;

  // Tool use requested by the AI model
  // When tool use generation begins, TOOL_USE_START is sent with name and id.
  // When the tool use is fully generated, TOOL_USE will be sent with all fields
  // populated completely. Real streaming TBD.
  TOOL_USE = 5;
  TOOL_USE_START = 7;

  // Backend code started using this value for FINAL_PARAMETERS with no
  // intention of it being in the actual API. Go ahead and reserve it...
  reserved 6;
}

message ChatResultNode {
  // Unique id of the node.
  int32 id = 1;

  // Type of the node.
  ChatResultNodeType type = 2;

  // Content of the node.
  string content = 3;

  // Additional content for tool results.
  optional ChatResultToolUse tool_use = 4;
}

message ChatResultToolUse {
  string tool_use_id = 1;
  string tool_name = 2;
  string input_json = 3;

  // Whether this is a partial tool use response.
  bool is_partial = 4;
}

enum ChatStopReason {
  // Stop reason unspecified
  REASON_UNSPECIFIED = 0;
  // The model has reached a natural stopping point or stop sequence
  END_TURN = 1;
  // Generation hit max token limit set by client or server
  MAX_TOKENS = 2;
  // The model has requested one or more tool uses
  // N.B. this name must be unique across all enums; TOOL_USE would collide with
  // ChatResultNodeType
  TOOL_USE_REQUESTED = 3;
}

enum ChatRequestNodeType {
  // User message text.
  TEXT = 0;

  // Result of a tool use.
  TOOL_RESULT = 1;

  // An image(Default format: PNG).
  IMAGE = 2;

  // If sent with no auxiliary data in ChatRequestNode,
  // came from a client which failed to hydrate the real
  // IMAGE node.
  IMAGE_ID = 3;

  // IDE state information.
  IDE_STATE = 4;

  // User edits information.
  EDIT_EVENTS = 5;

  // CHECKPOINT_REF is a CLIENT ONLY node type which should never be sent to the
  // server. Alas we cannot avoid bugs and have sent this to the backend, so we
  // give it a name here so that api-proxy can identify and drop such nodes.
  CHECKPOINT_REF = 6;
}

// Type of content node for tool results
enum ToolResultContentNodeType {
  // Unspecified content type
  CONTENT_TYPE_UNSPECIFIED = 0;

  // Text content
  CONTENT_TEXT = 1;
  // Image content
  CONTENT_IMAGE = 2;
}

enum ImageFormatType {
  // Default unspecified value - should not be used
  IMAGE_FORMAT_UNSPECIFIED = 0;
  // PNG format
  PNG = 1;
  // JPEG format
  JPEG = 2;
  // GIF format
  GIF = 3;
  // WebP format
  WEBP = 4;
}

message ChatRequestImage {
  // Base64 encoded image data
  string image_data = 1;
  // Format of the image data
  ImageFormatType format = 2;
}

message WorkspaceFolderInfo {
  // The root directory of the current repository. This is the first ancestor of folder
  // root that is a git directory or has an `.augmentroot` file.
  string repository_root = 1;
  // The directory of the "workspace" the user has opened.
  string folder_root = 2;
}

message TerminalInfo {
  // Unique id of the terminal.
  uint32 terminal_id = 1;
  // The current working directory of the terminal.
  string current_working_directory = 2;
  // Expected future fields: the shell name, etc.
}

message ChatRequestIdeState {
  // The workspaces the user has open, ordered by access time. The first element in this
  // list should be the curently opened workspace folder.
  repeated WorkspaceFolderInfo workspace_folders = 1;

  // Set if the client's workspace folders were unchanged since the last request.
  // This field is only used if `workspace_folders` is empty and is required to allow
  // us to not send repeated information on subsequent requests.
  // This is supporting the rare but possible case where the user has NO workspace
  // folders open and is still using chat.
  bool workspace_folders_unchanged = 2;

  // The current working directory of the interactive terminal.
  // If unset, then we should the path in the previous request.
  optional TerminalInfo current_terminal = 3;
}

// NOTE(arun): These fields are similar to base.diff_utils.SingleEdit, but we don't want
// to depend on that proto here. The other main difference is that we use line numbers
// instead of character offsets.
message ChatRequestSingleEdit {
  // The starting line of the edit in the original text.
  uint32 before_line_start = 1;

  // The text that was present before the edit.
  string before_text = 2;

  // The starting line where the new text is inserted.
  uint32 after_line_start = 3;

  // The new text that replaces the 'before_text'.
  string after_text = 4;
}

// NOTE(arun): These fields are similar to base.diff_utils.GranularEditEvent, but we
// don't want to depend on that proto here.
message ChatRequestFileEdit {
  // The path of the file that was edited.
  string path = 1;

  // If present, the name of the blob that was present before the edit.
  // Providing this field will allow the backend to perform additional diff formatting.
  // It is assumed that this before blob name has been uploaded; it doesn't matter if it
  // was indexed.
  optional string before_blob_name = 2;

  // If present, the name of the blob that was present after the edit.
  // Providing this allows the backend to validate the edit event.
  optional string after_blob_name = 3;

  // A list of individual edits that make up this edit event.
  repeated ChatRequestSingleEdit edits = 4;
}

enum EditEventSource {
  // Default unspecified value
  EDIT_SOURCE_UNSPECIFIED = 0;
  // Edit was performed by the user
  EDIT_SOURCE_USER_EDIT = 1;
  // Edit was performed by reverting to a checkpoint
  EDIT_SOURCE_CHECKPOINT_REVERT = 2;
}

message ChatRequestEditEvents {
  // Contains edit events corresponding to zero or more files *since the last request*.
  repeated ChatRequestFileEdit edit_events = 1;
  // Source of the edit events
  optional EditEventSource source = 2;
}

message ChatRequestNode {
  // Unique id of the node.
  int32 id = 1;

  // Type of the node.
  ChatRequestNodeType type = 2;

  optional ChatRequestText text_node = 3;
  optional ChatRequestToolResult tool_result_node = 4;
  optional ChatRequestImage image_node = 5;
  reserved 6; // Reserved for Image Id.
  optional ChatRequestIdeState ide_state_node = 7;
  optional ChatRequestEditEvents edit_events_node = 8;
  reserved 9; // Reserved for Checkpoint Ref.
}

message ChatRequestText {
  string content = 1;
}

// Content node for tool results - can be text or image
message ToolResultContentNode {
  // Type of the node
  ToolResultContentNodeType type = 1;

  // Text content if this is a text node
  optional string text_content = 2;

  // Image content if this is an image node
  optional ChatRequestImage image_content = 3;
}

message ChatRequestToolResult {
  string tool_use_id = 1;
  // Plain text content (ignored when content_nodes is present)
  string content = 2;
  bool is_error = 3;
  optional string request_id = 4;
  // List of content nodes (text or images)
  // If present, takes precedence over content field
  repeated ToolResultContentNode content_nodes = 5;
}

message ChatResponse {
  // The reponse text
  string text = 1 [debug_redact = true];

  // The subset of the requested blobs that are unknown to the server
  repeated string unknown_blob_names = 2;

  // Set to True if the checkpoint was not found.
  bool checkpoint_not_found = 3;

  // The context chunks for the chat result. This gives the FE info
  // about the retrieved chunks that were used to generate the
  // output prompt, which it then displays on the chat interface.
  repeated WorkspaceFileChunk workspace_file_chunks = 4;

  // the context used for the chat result that is NOT from the workspace (see
  // workspace_file_chunks) there is no ordering assumption between workspace_file_chunks. The
  // list might be truncated by the backend.
  repeated IncorporatedExternalSource incorporated_external_sources = 5;

  // The nodes of the structured chat response.
  repeated ChatResultNode nodes = 6;

  // Reason that generation stopped.
  // When streaming, populated once it is known. Does not
  // imply that the stream has ended.
  optional ChatStopReason stop_reason = 7;
}

// A single exchange between the user and the model.
//
// A request or response message may be represented as a string message or a list of structured
// content nodes/blocks. If both a string and a list of nodes are present, the string should be
// interpreted as a leading text node followed by the list of nodes.
//
// The structured API is preferred, but the text-based is still supported for backwards
// compatibility.
message Exchange {
  string request_message = 1 [debug_redact = true];
  optional string response_text = 2 [debug_redact = true];
  optional string request_id = 3;
  repeated ChatRequestNode request_nodes = 4 [debug_redact = true];
  repeated ChatResultNode response_nodes = 5 [debug_redact = true];
}

// Specifies a collection of blobs using a checkpoint id as a baseline
// collection, along with changes to the baseline.
message Blobs {
  // Name that represents a set of blobs on the server. Not set if there
  // is no baseline checkpoint.
  optional string checkpoint_id = 1;

  // List of blob ids to add to the baseline checkpoint.
  repeated string added_blobs = 2;

  // List of blob ids to remove from the baseline checkpoint.
  repeated string deleted_blobs = 3;
}

message KeyValuePair {
  string key = 1 [debug_redact = true];
  string value = 2 [debug_redact = true];
}

message FileLocation {
  // The path of the file.
  string path = 1 [debug_redact = true];

  // Based on current state of the file. (inclusive)
  uint32 line_start = 2;

  // Based on current state of the file. (exclusive)
  uint32 line_end = 3;
}

message FileRegion {
  // The path of the file.
  string path = 1 [debug_redact = true];

  // Based on current state of the file. (inclusive)
  uint32 char_start = 2;

  // Based on current state of the file. (exclusive)
  uint32 char_end = 3;
}

enum ChangeType {
  ADDED = 0;
  DELETED = 1;
  MODIFIED = 2;
  RENAMED = 3;
}

message WorkingDirectoryChange {
  // The previous path in HEAD.
  optional string before_path = 1 [debug_redact = true];

  // The current path.
  optional string after_path = 2 [debug_redact = true];

  // The type of change.
  ChangeType change_type = 3;

  // The before version in HEAD.
  optional string head_blob_name = 4;

  // The indexed version in the current workspace.
  optional string indexed_blob_name = 5;

  // The current version the IDE sees but might not be indexed yet.
  optional string current_blob_name = 6;
}

message VCSChange {
  // The changes made to the files since the last commit.
  repeated WorkingDirectoryChange working_directory_changes = 1;
}

enum DiagnosticSeverity {
  // Something not allowed by the rules of a language or other means.
  ERROR = 0;
  // Something suspicious but allowed.
  WARNING = 1;
  // Something to inform about but not a problem.
  INFORMATION = 2;
  // Something to hint to a better way of doing it, like proposing a refactoring.
  HINT = 3;
}

message Diagnostic {
  FileLocation location = 1;

  string message = 2 [debug_redact = true];

  DiagnosticSeverity severity = 3;

  // The current version the IDE sees but might not be indexed yet
  optional string current_blob_name = 4;

  // The latest indexed blob name
  optional string blob_name = 5;

  // Char offset within the blob of the start of the diagnostic.
  optional uint32 char_start = 6;

  // Char offset within the blob of the end of the diagnostic.
  optional uint32 char_end = 7;
}

enum NextEditMode {
  UNKNOWN_NEXT_EDIT_MODE = 0;
  // Background NextEdit mode with no user trigger
  BACKGROUND = 1;
  // Foreground NextEdit mode with user trigger
  FOREGROUND = 2;
  // Forced foreground mode with user trigger
  FORCED = 3;
}

enum NextEditScope {
  UNKNOWN_NEXT_EDIT_SCOPE = 0;
  // Return an edit immediately around the cursor or selected text region, if found.
  // The selected region may be expanded in the backend. Returns 0-1 results.
  CURSOR = 1;
  // Return edits found in the current file. Each edit is a single "hunk". Returns
  // 0-N results.
  FILE = 2;
  // Return edits found in the current workspace. Each edit is a single "hunk". Returns
  // 0-N results.
  WORKSPACE = 3;
}

message NextEditRequest {
  // name of the model to use
  optional string model = 1;

  // Sequence IDs should be strictly increasing within a session.
  //
  // Requests may cancel requests with lower or equal sequence ID from the
  // same session and namespace. This helps prevent accidental cancellation
  // of requests when network messages are reordered.
  uint32 sequence_id = 2;

  // programming laguage that the prompt is in.
  //
  // the name should be one of the language names given to the client
  // in the lanuages field of the `Model`.
  // if not set (or empty), the server might detect a language based on the path
  // or use an undefined default.
  optional string lang = 3;

  // the message to be used for the prompt
  string instruction = 4 [debug_redact = true];

  // Blobs to include in the retrieval corpus, specified in the compact delta form.
  Blobs blobs = 5;

  // Recent changes from the client.
  //
  // This field is only relevant for blobs that have not yet been indexed.
  // For the main way to specify recent changes, see edit_events.
  // By contrast, edit_events is for recent edits that have been indexed.
  repeated ReplacementText recent_changes = 6;

  // The changes made to the files since the last commit.
  // Should not be used in new code: replaced by edit_events and recent_changes.
  VCSChange vcs_change = 7 [deprecated = true];

  // relative file path to a given relevant root (e.g. the .augmentroot file).
  optional string path = 8 [debug_redact = true];

  // blob name of the blob in which the edit is requested, if known.
  optional string blob_name = 9;

  // character offset within the blob of the start of the selected text, if known
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 selection_begin_char = 10;

  // character offset within the blob of the end of the selected text, if known
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 selection_end_char = 11;

  // the prefix prompt at which the edit should be inserted
  optional string prefix = 12 [debug_redact = true];

  // the text selected in the current buffer
  optional string selected_text = 13 [debug_redact = true];

  // the text after the selcted text
  optional string suffix = 14 [debug_redact = true];

  // TODO(AU-4493): Start using this.
  repeated Diagnostic diagnostics = 15;

  // Next edit mode. Determines service parameters for this request.
  NextEditMode mode = 16;

  // Response change probability override.
  // Change suggestions will not be returned if probability is below this setting.
  // When not provided, configured probability threshold for the mode will be used.
  optional float change_probability_override = 17;

  // Next edit scope. Determines the scope of edits returned for this request.
  NextEditScope scope = 18;

  // Recent changes from the client.
  //
  // Use this field to supply recent edits whose blobs have been indexed.
  // By contrast, recent_changes can be used for recent edits that have not been indexed.
  repeated FileEditEvent edit_events = 19;

  // A list of locations that the system should not return in the result.
  repeated FileRegion blocked_locations = 20;

  /** The API version used by the client.

     The client bumps up this number whenever this is an incompatible change to the API
     behavior.

     Change log
     - v0: initial version, where a FILE scope request returns results from both the
       cursor chunk and same-file chunks, share a total max_chunk limit.
     - v1: Change the FILE scope results to contain the cursor chunk result followed
      by same-file chunk results. The same-file chunk results have an independent
        max_chunk limit.
     - v2: Send over new diagnostic information for better location retrieval.
     - v3: Exclude cursor chunks in FILE requests in the backend.
  */
  optional uint32 api_version = 21;

  // Helps with early cancellation of requests and hindsight analysis.
  // Note: This is when the request was created, not when it was sent.
  optional google.protobuf.Timestamp client_created_at = 22;

  // Unindexed changes
  //
  // Use this fields `unindexed_edit_events` + `unindexed_edit_events_base_blob_names` to
  // reconstruct current state of files `unindexed_edit_events_base_blob_names` is temporary for
  // the migration. These blob names need to eventually be part of the checkpoint
  // `unindexed_edit_events` will eventually replace the `recent_changes` field holding
  // replacement_text.
  repeated FileEditEvent unindexed_edit_events = 23;
  repeated string unindexed_edit_events_base_blob_names = 24;
}

message CharRange {
  // The character start of the range (inclusive).
  uint32 start = 1;

  // The character end of the range (exclusive).
  uint32 stop = 2;
}

// A span of text in a diff, relating the original and replacement ranges of text.
message DiffSpan {
  // The character range of the original text.
  CharRange original = 1;

  // The character range of the updated text.
  CharRange updated = 2;
}

message NextEditResult {
  // The path of the file to edit.
  string path = 1 [debug_redact = true];

  // The blob name of the file to edit.
  string blob_name = 2;

  // character offset within the blob of the start of edit
  // Based on last known state of the file.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  uint32 char_start = 3;

  // character offset within the blob of the end of the edit (exclusive).
  // Based on last known state of the file.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  uint32 char_end = 4;

  // The existing code.
  string existing_code = 5 [debug_redact = true];

  // The suggested edit.
  string suggested_code = 6 [debug_redact = true];

  // truncation character for the suggested edit
  optional int32 truncation_char = 7;

  // Diff spans for edit suggestion, both changed and unchanged spans
  repeated DiffSpan diff_spans = 8;

  // A natural language description of the change.
  string change_description = 9 [debug_redact = true];

  // Globally Unique suggestion ID
  string suggestion_id = 10;

  // The localization score of the suggestion.
  float localization_score = 11;

  // The editing score of the suggestion.
  float editing_score = 12;

  // Default threshold for `editing_score`. If `editing_score` > `editing_score_threshold`,
  // the suggestion is considered of low quality.
  float editing_score_threshold = 13;

  // A markdown version of the natural language change description.
  string markdown_change_description = 14 [debug_redact = true];
}

message NextEditResponse {
  // The locations and content predicted to come next.
  optional NextEditResult next_edit = 1;

  // The subset of the requested blobs that are unknown to the server
  repeated string unknown_blob_names = 2;

  // Set to True if the checkpoint was not found.
  bool checkpoint_not_found = 3;
}

message NextEditResolutionBatch {
  repeated NextEditResolution resolutions = 1;
}

// resolution of a next edit request
message NextEditResolution {
  string request_id = 1;

  // TODO: WKT Timestamps here?
  // time (sec + nsec since Unix epoch) when the next edit suggestion was shown to the end user
  int64 emit_time_sec = 2;
  int32 emit_time_nsec = 3;

  // time (sec + nsec since Unix epoch) when the user accepted or rejected the next edit
  // suggestion
  int64 resolve_time_sec = 4;
  int32 resolve_time_nsec = 5;

  // true if the user accepted the next edit suggestion, false if the user rejected the next edit
  // suggestion
  bool is_accepted = 6;
}

message NextEditResolutionResponse {}

// next edit session event
message NextEditSessionEvent {
  // ID of the request the session event is related to.
  // Empty iff the event is not related to a specific request.
  optional string related_request_id = 1;

  // ID of the suggestion the session event is related to.
  // Empty iff the event is not related to a specific suggestion.
  optional string related_suggestion_id = 2;

  // Time (sec + nsec since Unix epoch) when the event took place.
  int64 event_time_sec = 3;
  int32 event_time_nsec = 4;

  // Name of the session event.
  // Should never be empty.
  string event_name = 5;

  // Source of the session event.
  // Empty if there is no event source.
  optional string event_source = 6;
}

// Frontend prefers sending a batch of events
message NextEditSessionEventBatch {
  repeated NextEditSessionEvent events = 1;
}

// Empty response for posting events
message NextEditSessionEventResponse {}

// onboarding session event
message OnboardingSessionEvent {
  // Time (sec + nsec since Unix epoch) when the event took place.
  int64 event_time_sec = 1;
  int32 event_time_nsec = 2;

  // Name of the session event.
  // Should never be empty.
  string event_name = 3;
}

// Frontend prefers sending a batch of events
message OnboardingSessionEventBatch {
  repeated OnboardingSessionEvent events = 1;
}

// Empty response for posting events
message OnboardingSessionEventResponse {}

message MemorizeRequest {
  // name of the model to use
  string model = 1 [deprecated = true];

  // content of the file to memorize
  string t = 2 [debug_redact = true];

  // relative path of the file to memorize
  string path = 3 [debug_redact = true];

  // the blob name of the content
  // the blob name is defined as the sha256 hash of the content
  // the server should verify that the blob name was calculated correctly.
  // if the blob name is not set or set to the empty string, the server should
  // accept the request and fill in the correct blob name.
  optional string blob_name = 4;
}

message MemorizeResponse {
  // memory object name
  string mem_object_name = 1;
}

// A upload request may not include more than 1000 blobs, individual blobs
// larger than 256KB, or more than 1MB in total content size. Violating these
// limits, or overloading the underlying storage, will error the whole request.
// The limits are defined in services/content_manager/server/deploy.jsonnet.
// If the request errors for one of these reasons, the suggested retry strategy
// for the client is to break the upload into smaller chunks.
message BatchUploadRequest {
  // List of blobs to upload
  repeated UploadBlob blobs = 1;
}

message UploadBlob {
  // content of the file to memorize
  string content = 1 [debug_redact = true];

  // relative path of the file to memorize
  string path = 2 [debug_redact = true];

  // the blob name is a hash of the path and contents
  // the server should verify that the blob name was calculated correctly.
  // if the blob name is not set or set to the empty string, the server should
  // accept the request and fill in the correct blob name.
  //
  // Note: The production server will ignore this field.
  optional string blob_name = 3;
}

message BatchUploadResponse {
  // The blob names will be returned in the same order as the blobs
  // in the request. A successful response should have exactly as many blob
  // names as blobs.
  repeated string blob_names = 1;
}

message FindMissingRequest {
  // Name of the model that will be used for completion requests using the given blob names.
  // The returned nonindexed_blob_names contains the set of blob names are not indexed for this
  // model.
  optional string model = 1;

  // names of memory objects to check
  //
  // the server should return all memory object names from this
  // list in the response that it doesn't have available for the given model.
  repeated string mem_object_names = 2;
}

message FindMissingResponse {
  // The subset of the requested memory objects (aka blobs) that are completely unknown to
  // the server.
  repeated string unknown_memory_names = 2;

  // The subset of the requested blobs that aren't fully indexed for the given model. A blob
  // is considered fully indexed if the transformed content is present for every retriever used
  // by the model. A blob that is indexed for one retriever but not for another is considered
  // nonindexed. No blob name will be present in both lists.
  repeated string nonindexed_blob_names = 3;
}

message ListExternalSourceTypesRequest {}

// List all known external source types
message ListExternalSourceTypesResponse {
  repeated ExternalSourceType source_types = 1;
}

enum ExternalSourceType {
  UNKNOWN_SOURCE = 0;
  DOCUMENTATION_SET = 1;
  // Not sure yet how this would work for other sources. For example, for Github
  // would we just have Github? Or GithubRepo and GithubPullRequest?
}

message SearchExternalSourcesRequest {
  // The raw search text from the user
  string query = 1;

  // Optionally, only search these kinds of external sources. If empty, then
  // search all external sources.
  repeated ExternalSourceType source_types = 2;
}

message SearchExternalSourcesResponse {
  repeated ExternalSource sources = 1;
}

// An external source that can be part of a generation request's context.
//
// This is rather vague, because I was trying to keep this flexible for when we
// have other integrations, like Github, Notion, etc.
message ExternalSource {
  // Though it is not important for the client to know this, this is in a uri
  // format.
  string id = 1;

  // The name of the source.
  string name = 4;

  // Some string describing this source that can be displayed to users.
  string title = 2;

  // The type of source
  ExternalSourceType source_type = 3;
}

/*
   GetImplicitExternalSourcesRequest is used to get the list of external sources that
   are inferred to be relevant to the conversation, but not explicitly tagged.
*/
message GetImplicitExternalSourcesRequest {
  // TODO(xz): Eventually this should take in selected code, path, language and history as well.

  // The message to be used for the prompt
  string message = 1;
}

message GetImplicitExternalSourcesResponse {
  repeated ExternalSource sources = 1;
}

message GetModelsRequest {}

message Language {
  // name of the language as recognized by the server. this should be treated
  // as the canonical language name. the client and server should use this
  // name when specifying this language in other protocol messages.
  string name = 1;

  // name of the language as identified by vscode. this name can be used by
  // the client to communicate internally with vscode.
  string vscode_name = 2;

  // file extensions for the given language, e.g. ".c"
  repeated string extensions = 3;
}

message Model {
  // value for the number of suggested UTF-8 characters used as prompt before the completion
  // cursor.bool
  //
  // This is in characters and not in bytes as splitting a string on a byte boundary might lead to
  // an malformed string. The client also doesn't operate on tokens or know the tokenizer used, so
  // we select the prompt based on the character count.
  //
  // The client is free to sent more or less characters.
  // The server can reject an inference request sending more characters, but should not reject a
  // request with equal or fewer characters.
  uint32 suggested_prefix_char_count = 1;

  // value for the number of suggested UTF-8 characters used after the completion cursor.
  //
  // The client is free to sent more or less characters.
  // The server can reject an inference request sending more characters, but should not reject a
  // request with equal or fewer characters.
  uint32 suggested_suffix_char_count = 2;

  // value for the maximal number of bytes supported for uploading content to the content manager
  uint32 max_memorize_size_bytes = 3;

  reserved 4;

  // name of the model
  //
  // the model name will not be clear text in production namespaces.
  string name = 5;

  // internal name of the model (debug only)
  optional string internal_name = 6;

  // is the model a default model for some request type based on feature flags
  bool is_default = 7;
}

message GetModelsResponse {
  message FeatureFlags {
    reserved 2, 3, 4;

    // If present and true, client will support performing code edits
    optional bool enable_code_edits = 1 [deprecated = true];

    // If true, client will allow the user to perform chat
    optional bool enable_chat = 5;

    // deprecated - superceded by enable_workspace_manager_ui_launch
    optional bool enable_workspace_manager_ui = 6;

    // superceded by intellij_chat_min_version
    optional bool enable_intellij_chat = 7;

    // If present and true, client will show the workspace manager UI
    optional bool enable_workspace_manager_ui_launch = 8;

    // Optionally pass a list of additional chat models to set for dogfooding.
    optional string additional_chat_models = 9;

    // defines the minimum version of the intellij client to allow chat, supercedes
    // enable_intellij_chat
    optional string intellij_chat_min_version = 10;

    // defines the minimum version of the vscode client to allow next edit.
    optional string vscode_next_edit_min_version = 11;

    // defines the minimum version of the vscode client to allow flywheel features.
    optional string vscode_flywheel_min_version = 12;

    // defines the minimum version of the vscode client to allow external sources (like docsets)
    // in chat
    optional string vscode_external_sources_in_chat_min_version = 13;

    // If true, client will allow instructions
    optional bool enable_instructions = 14;

    // If true, client will allow smart-paste
    optional bool enable_smart_paste = 15;

    reserved 16; // Previously used for enable_share_service

    // defines the minimum version of the vscode client to allow smart-paste
    optional string enable_smart_paste_min_version = 17;

    // If true, client will use viewTextDocument to limit vscode impact
    optional bool enable_view_text_document = 18;

    // If present and true, client will create checkpoints.
    optional bool checkpoint_blobs_v2 = 19;

    // If present and true, client will upload user events (SENSITIVE DATA).
    optional bool enable_data_collection = 20;

    // If present, client will use this value as the threshold for when to show
    // indexing status.
    optional int64 small_sync_threshold = 21;

    // If present, client will use this value as the threshold for when to show
    // indexing notification.
    optional int64 big_sync_threshold = 22;

    // If present and true, client will bypass language filter
    optional bool bypass_language_filter = 23;

    // If present and true, enable hindsight datacollection in the client.
    optional bool enable_hindsight = 24;

    // If present, sets the max size of a blob to upload for retrieval.
    optional int64 max_upload_size_bytes = 25;

    // defined the minimum version of the intellij client to allow forcing completions on all
    // edits.
    optional string intellij_force_completion_min_version = 26;

    optional bool enable_external_sources_in_chat = 27 [deprecated = true];

    // The maximum number of files in a trackable source folder
    optional int64 max_trackable_file_count = 28;

    // The maximum number of files in a source folder before the front will request permission
    // to track it
    optional int64 max_trackable_file_count_without_permission = 29;

    // Pct of tracked files in a folder that must be uploaded for the folder to be considered
    // "uploaded". 0 <= value <= 100
    optional int64 min_uploaded_percentage_without_permission = 30;

    // defines the minimum version of the vscode client to allow chat hint decoration
    optional string vscode_chat_hint_decoration_min_version = 31;

    // Defines a debounce (wait before sending request) time in milliseconds for next edit
    optional int64 next_edit_debounce_ms = 32;

    // If true, the client will enable file edit event collection for completions and send to
    // the backend
    optional bool enable_completion_file_edit_events = 33;

    // defines the minimum version of the vscode client to allow sources in chat
    optional string vscode_sources_min_version = 34;

    // If present and true, client can enable CPU profiling
    optional bool vscode_enable_cpu_profile = 35;

    // If true, the front end will request permission to sync source folders that don't appear
    // to be source repos (for example, if they don't have a .git directory or a .augmentroot
    // file).
    optional bool verify_folder_is_source_repo = 36;

    // If true, the front end will refuse to sync a user's home directory.
    optional bool refuse_to_sync_home_directories = 37;

    // If true, the front end will honor the max_trackable_file_count and
    // max_trackable_file_count_without_permission feature flags. If false, it will not count
    // the number of files in a folder, or enforce any size limits when determining whether to
    // request permission to sync it.
    optional bool enable_file_limits_for_syncing_permission = 38;

    // Defines the minimum version of the vscode and intellij clients to allow chat thread
    // sharing.
    optional string vscode_share_min_version = 39;
    optional string intellij_share_min_version = 40;

    // If true, a summary title is generated for the chat conversation after the first exchange.
    optional bool enable_summary_titles = 41;

    // [Deprecated] If true, the client will enable chat mermaid diagrams.
    optional bool enable_chat_mermaid_diagrams = 42;

    // Defines the mode for smart paste precomputation.
    optional string smart_paste_precompute_mode = 43;

    // Defines the minimum version of the vscode and intellij clients to use the new chat thread
    // UI.
    optional string vscode_new_threads_menu_min_version = 44;
    optional string intellij_new_threads_menu_min_version = 45;

    // Defines the minimum version of the vscode client to use the editable history feature.
    optional string vscode_editable_history_min_version = 46;

    // If true, the IntelliJ client will show the codebase summary in chat after the first sync.
    optional bool intellij_show_summary = 47;

    // If true, the client will be able to send user and workspace-defined guidelines for Chat
    optional bool enable_guidelines = 48;

    // Defines the minimum version of the vscode client to enable chat mermaid diagrams.
    optional string vscode_enable_chat_mermaid_diagrams_min_version = 49;

    // Defines the version of vscode and intellij clients below which we will report them
    // deprecated.
    optional string vscode_deprecated_version = 50;
    optional string intellij_deprecated_version = 51;

    // Minimum version of the vscode client to rely on checkpoint manager
    // context when computing request context, and whether to perform the
    // legacy method of scanning the entire workspace to validate the result.
    optional string vscode_use_checkpoint_manager_context_min_version = 52;
    optional bool vscode_validate_checkpoint_manager_context = 53;

    // Minimum version of the intellij client to use the completions history feature.
    optional string intellij_completions_history_min_version = 54;

    // Minimum version of the intellij client to enable smart paste.
    optional string intellij_smart_paste_min_version = 55;

    // cutoff versions to move users to new experience
    optional string vscode_next_edit_ux1_max_version = 56;
    optional string vscode_next_edit_ux2_max_version = 57;

    // Defines the minimum version of the VS Code client to use the new design system rich text
    // editor component.
    optional string vscode_design_system_rich_text_editor_min_version = 58;
    optional bool allow_client_feature_flag_overrides = 59;

    // Defines the minimum version of the VS Code client to enable chat with tools
    optional string vscode_chat_with_tools_min_version = 60;

    // Defines the minimum version of the IntelliJ client to enable chat with tools
    optional string intellij_chat_with_tools_min_version = 61;

    // Defines minimum versions to use multimodal features in chat
    optional string vscode_chat_multimodal_min_version = 62;
    optional string intellij_chat_multimodal_min_version = 63;

    // Defines the minimum version of the intellij client to enable chat mermaid diagrams.
    optional string intellij_enable_chat_mermaid_diagrams_min_version = 64;

    // Minimum versions required for agent mode
    optional string vscode_agent_mode_min_version = 65;
    optional string intellij_agent_mode_min_version = 66;

    // Comma separated list of blocked IntelliJ versions
    optional string intellij_blocked_versions = 67;

    // Defines the minimum version of the IntelliJ client to use the new design system rich text
    // editor component.
    optional string intellij_design_system_rich_text_editor_min_version = 68;

    // Defines the minimum version of the IntelliJ client to show syncing progress in chat.
    optional string intellij_syncing_progress_min_version = 69;

    // Parameters of memories generation (incl. prompts)
    optional string memories_params = 70;

    // Configuration for ELO model comparison tests
    optional string elo_model_configuration = 71;

    // Defines the minimum version of the VSCode client to show the bottom panel in Next Edit.
    optional string vscode_next_edit_bottom_panel_min_version = 72;

    // Specifies which edit tool to use in VSCode agent mode. Valid values are
    // "backend_edit_tool" and "str_replace_editor_tool".
    optional string vscode_agent_edit_tool = 73;

    // Defines the minimum version of the IntelliJ client to ask for sync permission.
    optional string intellij_ask_for_sync_permission_min_version = 74;

    // Defines the minimum version of the VS Code client to enable background agents.
    optional string vscode_background_agents_min_version = 75;

    // Defines the minimum version of the IntelliJ client to enable background agents.
    optional string intellij_background_agents_min_version = 76;

    // The maximum length of user-defined guidelines
    optional int64 user_guidelines_length_limit = 77;

    // The maximum length of workspace-defined guidelines
    optional int64 workspace_guidelines_length_limit = 78;

    // Defines the minimum version of the VS Code client to enable rich checkpoint info
    optional string vscode_rich_checkpoint_info_min_version = 79;

    // Defines the minimum version of the IntelliJ client to enable the remember tool
    optional string intellij_remember_tool_min_version = 80;

    reserved 81;

    // Defines whether intelliJ has user guidelines feature enabled
    optional bool intellij_enable_user_guidelines = 82;

    // Defines whether intelliJ has workspace guidelines feature enabled
    optional bool intellij_enable_workspace_guidelines = 83;

    // Defines the minimum version of the VS Code client to enable agent mode in stable
    optional string vscode_agent_mode_min_stable_version = 84;

    // Defines the minimum version of the VS Code client to enable virtualized message list
    optional string vscode_virtualized_message_list_min_version = 85;

    // Defines the minimum version of the IntelliJ client to enable virtualized message list
    optional string intellij_virtualized_message_list_min_version = 86;
    // Defines the minimum version of the VS Code client to enable stable prefix truncation
    optional string vscode_chat_stable_prefix_truncation_min_version = 87;

    // Minimal line range that agent reads in file
    optional int64 agent_edit_tool_min_view_size = 88;

    // Defines whether intelliJ has user guidelines shown in the settings page
    // not to be confused with intellij_enable_user_guidelines which is for whether the feature
    // is enabled at all and sent to chats
    optional bool intellij_user_guidelines_in_settings = 89;

    // Defines the schema type of the agent edit tool
    optional string agent_edit_tool_schema_type = 90;

    // If true, memory classification happens on first token received rather than before sending
    // a message
    optional bool memory_classification_on_first_token = 93;

    // Defines the minimum version of the VS Code client to enable direct apply of code changes
    // from codeblocks
    optional string vscode_direct_apply_min_version = 91;

    // Defines the minimum version of the VS Code client to enable personalities feature
    optional string vscode_personalities_min_version = 92;

    // If true, the save file tool will require an instructions reminder to be generated first
    optional bool agent_save_file_tool_instructions_reminder = 94;

    // Defines the polling interval to use for polling the remote agent chat history
    optional int64 remote_agent_chat_history_polling_interval_ms = 95;

    // Defines the polling interval to use for polling the remote agent list
    optional int64 remote_agent_list_polling_interval_ms = 96;

    // Defines whether to use the memory snapshot manager to get memories
    optional bool use_memory_snapshot_manager = 97;

    // Defines the minimum version of the IntelliJ client to allow preference collection
    optional string intellij_preference_collection_allowed_min_version = 98;

    // Defines whether intelliJ should use a homespun gitignore parser, rather than
    // nl.basjes.gitignore.
    optional bool intellij_enable_homespun_gitignore = 99;

    // Defines the minimum version of the VS Code client to enable generate commit message
    optional string vscode_generate_commit_message_min_version = 100;

    // Enable .augment/rules
    optional bool enable_rules = 101;

    // Enable memories custom text editor
    optional bool memories_text_editor_enabled = 102;

    // If true, fuzzy matching is enabled in the str-replace-editor-tool. If false, only exact
    // matches are allowed.
    optional bool agent_edit_tool_enable_fuzzy_matching = 103;

    // Custom message to display when fuzzy matching is used in the str-replace-editor-tool
    optional string agent_edit_tool_fuzzy_match_success_message = 104;

    // Maximum number of differences allowed in fuzzy matching for str-replace-editor-tool
    optional int64 agent_edit_tool_fuzzy_match_max_diff = 105;

    // Maximum ratio of differences to string length allowed in fuzzy matching for
    // str-replace-editor-tool
    optional double agent_edit_tool_fuzzy_match_max_diff_ratio = 106;

    // Minimum number of consecutive matching symbols required between differences in fuzzy
    // matching
    optional int64 agent_edit_tool_fuzzy_match_min_all_match_streak_between_diffs = 107;

    // Enable the prompt enhancer button
    optional bool enable_prompt_enhancer = 108;

    // Enable model registry
    optional bool enable_model_registry = 109;

    // Model registry
    optional string model_registry = 110;

    // Defines the minimum version of the IntelliJ client to enable the prompt enhancer
    optional bool intellij_prompt_enhancer_enabled = 111;

    // If true, special `instructions_reminder` field is added to the str-replace-editor tool
    // input schema to remind the agent to limit the file content
    optional bool agent_edit_tool_instructions_reminder = 112;

    // Enable Sentry error reporting in IntelliJ plugin
    optional bool intellij_enable_sentry = 113;

    // Sampling rate for webview errors in IntelliJ plugin (0.0-1.0)
    optional double intellij_webview_error_sampling_rate = 114;

    // Sampling rate for plugin errors in IntelliJ plugin (0.0-1.0)
    optional double intellij_plugin_error_sampling_rate = 115;

    // Sampling rate for webview traces in IntelliJ plugin (0.0-1.0)
    optional double intellij_webview_trace_sampling_rate = 116;

    // Sampling rate for plugin traces in IntelliJ plugin (0.0-1.0)
    optional double intellij_plugin_trace_sampling_rate = 117;

    // Enable open file manager v2
    optional bool open_file_manager_v2_enabled = 118;

    // Enable webview performance monitoring in IntelliJ plugin
    optional bool intellij_enable_webview_performance_monitoring = 119;

    // Defines the minimum version of the VS Code client to enable the task list feature
    optional string vscode_task_list_min_version = 120;

    // If true, the agent edit tool will show result snippets after successful edits
    optional bool agent_edit_tool_show_result_snippet = 121;

    // Enable EDT freeze detection in IntelliJ plugin
    optional bool intellij_edt_freeze_detection_enabled = 122;

    // Announcement string to show in the client
    optional string client_announcement = 123;

    // Min version of Augment to install in remote agent workspace after ssh
    optional string vscode_remote_agent_ssh_min_version = 124;

    // Maximum number of lines for edit tool instructions reminder
    optional int64 agent_edit_tool_max_lines = 125;

    // Enable grep search tool
    optional bool grep_search_tool_enable = 126;

    // Time limit for grep search tool in seconds
    optional int64 grep_search_tool_timelimit_sec = 127;

    // Output character limit for grep search tool
    optional int64 grep_search_tool_output_chars_limit = 128;

    // Number of context lines to include in grep search tool output
    optional int64 grep_search_tool_num_context_lines = 129;

    // Enable agent auto mode
    optional bool enable_agent_auto_mode = 130;

    // How often to report the chat history when streaming (number of chunks)
    optional int64 agent_report_streamed_chat_every_chunk = 131;

    // Max total size of changed files to send in a single chat update (bytes)
    optional int64 agent_max_total_changed_files_size_bytes = 132;

    // Max number of skipped paths to send when the total changed files size is exceeded
    optional int64 agent_max_changed_files_skipped_paths = 133;

    // How often to update the agent status when idle (milliseconds)
    optional int64 agent_idle_status_update_interval_ms = 134;

    // Maximum number of iterations in a single turn for the agent loop
    optional int64 agent_max_iterations = 135;

    // Agent SSH connection check interval (milliseconds)
    optional int64 agent_ssh_connection_check_interval_ms = 136;

    // Agent SSH connection check log interval (milliseconds)
    optional int64 agent_ssh_connection_check_log_interval_ms = 137;

    // Enable Sentry error reporting in Beachhead service
    optional bool beachhead_enable_sentry = 138;

    // Sampling rate for errors in Beachhead service (0.0-1.0)
    optional double beachhead_error_sampling_rate = 139;

    // Sampling rate for traces in Beachhead service (0.0-1.0)
    optional double beachhead_trace_sampling_rate = 140;

    // Defines the minimum version of the VS Code client to enable the support tool use start feature
    optional string vscode_support_tool_use_start_min_version = 141;

    // Defines whether intelliJ uses indexing v3 or not
    optional bool intellij_indexing_v3_enabled = 142;

    // Sets the minimum version number to enable history summarization in chat conversations
    optional string history_summary_min_version = 143;

    // Maximum expected characters before history summarization is triggered
    optional int64 history_summary_max_chars = 144;

    // Lower threshold for history summarization
    optional int64 history_summary_lower_chars = 145;

    // If true, enables the new threads list functionality
    optional bool enable_new_threads_list = 146;

    // Custom prompt for conversation summarization
    optional string history_summary_prompt = 147;

    // If true, enables storage of untruncated content for retrieval tools
    optional bool enable_untruncated_content_storage = 148;
  }

  // the user tier is determined by the plan the user is on
  // however, the user tier is not the same as the plan
  //
  // for example, a user might be on the trail plan, but their user tier is still professional
  // there might be multiple plans that map to the same user tier
  enum UserTier {
    UNKNOWN = 0;

    // community tier is free and we are allowed to train on the data
    COMMUNITY_TIER = 1;

    // professional tier is paid and we are not allowed to train on the data
    // focused on individual users and small teams
    //
    // a better name would potentially have been "Self-Serve Paid", but the string
    // is part of the public API and can't be changed
    PROFESSIONAL_TIER = 2;

    // enterprise tier is paid and we are not allowed to train on the data
    // focused on large enterprises
    // some features are only available in the enterprise tier, such as Slackbot
    ENTERPRISE_TIER = 3;
  }

  // the name of the default model to use unless a different model is requested
  // might not be set if there is no model available.
  optional string default_model = 1;

  // list of all models available.
  repeated Model models = 2;

  // the programming languages that server supports.
  //
  // Note: this is usually the union of all the languages supported by the
  // models. A model is free to return a 400 error for a completion request in a language it
  // doesn't support.
  repeated Language languages = 3;

  // optional feature flags. if not provided, all flags will be assumed "false"/0.
  optional FeatureFlags feature_flags = 4;

  // information about the user tier
  // The user tier is not identical to the plan. It is a broader category that groups multiple plans together.
  UserTier user_tier = 5;

  // User identification fields
  message User {
    // The user's unique identifier
    string id = 1;
    // The user's email address
    optional string email = 2;
  }

  // User information for the authenticated user making the request
  optional User user = 6;
}

message ResolveCompletions {
  // name of client that requested the completions (for example: "vscode-extension")
  //
  // Every client should set the HTTP standard header "User-Agent" for every request
  // The value in this field is not used.
  optional string client_name = 1 [deprecated = true];

  // array of completion request resolutions to report
  repeated CompletionResolution resolutions = 2;
}

// resolution of a completion request
message CompletionResolution {
  string request_id = 1;

  // TODO: WKT Timestamps here?
  // time (sec + nsec since Unix epoch) when the completion was shown to the end user
  int64 emit_time_sec = 2;
  int32 emit_time_nsec = 3;

  // time (sec + nsec since Unix epoch) when the user accepted or rejected the completion
  int64 resolve_time_sec = 4;
  int32 resolve_time_nsec = 5;

  // index of the accepted completion, or -1 if the completion was rejected
  int32 accepted_idx = 6;
}

// Track interaction with instruction suggestions
message InstructionResolution {
  // Original request id for which this feedback is for
  string request_id = 1;

  // For each chunk shown to the user, was it accepted or rejected
  repeated bool is_accepted_chunks = 2;

  // Did the user click accept all without interacting with any individual chunk
  bool is_accept_all = 3;

  // Did the user reject all (or close the diff viewer) without interacting with any individual
  // chunk
  bool is_reject_all = 4;

  // Time of the initial request
  int64 emit_time_sec = 5;
  int32 emit_time_nsec = 6;

  // Time of the request being resolved
  int64 resolve_time_sec = 7;
  int32 resolve_time_nsec = 8;
}

message InstructionResolutionResponse {}

// Track interaction with smart paste suggestions in diff-viewer
message SmartPasteResolution {
  // Original request id for which this feedback is for
  string request_id = 1;

  // For each chunk shown to the user, was it accepted or rejected
  repeated bool is_accepted_chunks = 2;

  // Did the user click accept all without interacting with any individual chunk
  bool is_accept_all = 3;

  // Did the user reject all (or close the diff viewer) without interacting with any individual
  // chunk
  bool is_reject_all = 4;

  // Time of the initial request
  int64 initial_request_time_sec = 5;
  int32 initial_request_time_nsec = 6;

  // Time of stream finishing successfully - can be before or after apply time
  int64 stream_finish_time_sec = 7;
  int32 stream_finish_time_nsec = 8;

  // Time of initial user interaction (clicking apply) - can be before or after emit time
  int64 apply_time_sec = 9;
  int32 apply_time_nsec = 10;

  // Time of the request being resolved
  int64 resolve_time_sec = 11;
  int32 resolve_time_nsec = 12;
}

message SmartPasteResolutionResponse {}

message CompletionFeedback {
  // The request ID the feedback is for
  string request_id = 1;

  // The rating of the completion
  FeedbackRating rating = 2;

  // A note to accompany the rating
  string note = 3;
}

message CompletionFeedbackResponse {}

message ChatFeedback {
  // The request ID the feedback is for
  string request_id = 1;

  // The rating of the chat response
  FeedbackRating rating = 2;

  // A note to accompany the rating
  string note = 3;

  // The RI types are identical; if we ever need different feedback for them,
  // we can split the endpoints at that time.
  ChatMode mode = 4;
}

message ChatFeedbackResponse {}

message NextEditFeedback {
  // The request ID the feedback is for
  string request_id = 1;

  // The rating of the next edit response
  FeedbackRating rating = 2;

  // A note to accompany the rating
  string note = 3;
}

message NextEditFeedbackResponse {}

message EditResolution {
  // Every client should set the HTTP standard header "User-Agent" for every request
  // The value in this field is not used.
  string client_name = 1 [deprecated = true];

  string request_id = 2;

  // TODO: WKT Timestamps here?
  // time (sec + nsec since Unix epoch) when the edit was shown to the end user
  int64 emit_time_sec = 3;
  int32 emit_time_nsec = 4;

  // time (sec + nsec since Unix epoch) when the user accepted or rejected the edit
  int64 resolve_time_sec = 5;
  int32 resolve_time_nsec = 6;

  bool is_accepted = 7;

  // user-suggested correction of the reponse text
  optional string annotated_text = 8 [debug_redact = true];

  // user-suggested correction of the instruction
  optional string annotated_instruction = 9 [debug_redact = true];
}

message ResolveEditResponse {}

// Represents a user preference between multiple answers
// More: https://www.notion.so/RFC-Preference-data-collection-01058dd73069477c8287652ef4d3c9bb
message PreferenceSample {
  // Requests participating in voting process
  repeated string request_ids = 1;

  // Quantitative comparisons between the requests.
  // For structure, see RFC mentioned above.
  // Keeping it as map to ease experimenting with different collection strategies
  map<string, int32> scores = 2;

  // Free-form feedback given by the user regarding this sample
  string feedback = 3;
}

message PreferenceSampleResponse {}

// The possible ratings a user can give a suggestion
enum FeedbackRating {
  UNSET = 0;
  POSITIVE = 1;
  NEGATIVE = 2;
}

message ReportErrorRequest {
  // request ID for the original request, if any
  optional string original_request_id = 1;

  // Error message from the extension. This SHOULD NOT contain any sensitive metadata.
  string sanitized_message = 2;

  // Stack trace of the error in the extension. Since it only describes locations in our code
  // it SHOULD NOT contain any sensitive metadata.
  string stack_trace = 3;

  // Error diagnostics as unstructured (key, value) string pairs. This MAY contain sensitive
  // metadata.
  repeated KeyValuePair diagnostics = 4 [debug_redact = true];
}

message ReportErrorResponse {}

// A single metric observation from the client. See CLIENT_METRIC_COLLECTOR for usage details
// This is designed for system health metrics like latencies and counts of successful / errored
// actions
message ClientMetricsEvent {
  // String identifying the metric, to be used as a prometheus tag
  string client_metric = 1;
  // Amount to increment the metric by
  uint64 value = 2;
}

message ClientMetricsRequest {
  repeated ClientMetricsEvent metrics = 1;
}

message ClientMetricsResponse {}

message ReportFeatureVectorRequest {
  map<int32, string> feature_vector = 1;
}

message ReportFeatureVectorResponse {}

message ClientCompletionTimelineRequest {
  // array of timelines to report
  repeated ClientCompletionTimeline timelines = 1;
}

message ClientCompletionTimelineResponse {}

message ClientCompletionTimeline {
  string request_id = 1;

  int64 initial_request_time_sec = 2;
  int32 initial_request_time_nsec = 3;

  int64 api_start_time_sec = 4;
  int32 api_start_time_nsec = 5;

  int64 api_end_time_sec = 6;
  int32 api_end_time_nsec = 7;

  int64 emit_time_sec = 8;
  int32 emit_time_nsec = 9;
}

message SaveChatRequest {
  string conversation_id = 1;
  repeated Exchange chat = 2;
  string title = 3 [debug_redact = true];
}

message SaveChatResponse {
  string uuid = 1;
  string url = 2;
}

message RecordRequestEventsRequest {
  repeated request_insight.RequestEvent events = 1;
}

message RecordRequestEventsResponse {}

message RecordSessionEventsRequest {
  repeated request_insight.SessionEvent events = 1;
}

message RecordSessionEventsResponse {}

// Experimental APIs for autofix, subject to change.
// Based on
// https://www.notion.so/RFC-Autofix-Productionizing-Backend-135bba10175a809da7bef76066b3e7f0
message AutofixCommand {
  string input = 1;
  string output = 2;
  optional uint32 exit_code = 3;
}

// Autofix step 1: given a command check in the background whether to enter the fix workflow
message AutofixCheckRequest {
  AutofixCommand command = 1;
}

message AutofixCheckResponse {
  bool is_code_related = 1;
  bool contains_failure = 2;
}

// A single exchange between the user and the model. Right now, it just contains a user message
// and information about the solution corresponding to that message, as well as a request ID for
// each steering exchange.
message UserSteeringExchange {
  string request_message = 1 [debug_redact = true];

  // text summary of the whole plan
  string summary = 2 [debug_redact = true];

  // list of replacements to apply
  repeated TextReplacement replacements = 3;

  string request_id = 4;
}

// Autofix step 2: given a command failure produce a set of suggested fixes
message AutofixPlanRequest {
  AutofixCommand command = 1;
  VCSChange vcs_change = 2;
  Blobs blobs = 3;
  repeated UserSteeringExchange steering_history = 4;
}

message TextReplacement {
  // human readable description
  string description = 1;

  // path to the file with the replacement
  string path = 2 [debug_redact = true];

  // the text to be pasted
  string text = 3 [debug_redact = true];

  // code line in which to start the replacement, relative to sent code (incl. prefix)
  uint32 start_line = 4;

  // code line in which to end the replacement, relative to sent code (incl. suffix)
  uint32 end_line = 5;

  // Old text to replace
  string old_text = 6 [debug_redact = true];

  // Sequence ID of the replacment
  uint32 sequence_id = 7;

  // If provided, the exact blob the replacement is expected to apply to
  optional string old_blob_name = 8;
}

// NOTE: based on InstructionResponse. Consider abstracting a Replacement message.
message AutofixPlanResponse {
  // standard retrieval response fields
  repeated string unknown_blob_names = 1;
  bool checkpoint_not_found = 2;

  // text summary of the whole plan
  optional string summary = 3;

  // list of replacements to apply
  repeated TextReplacement replacements = 4;
}

message LLMGenerateRequest {
  // currently unused; expect to later support multiple models from one host
  string model_name = 1;
  // User message, represented as a list of structured content blocks
  repeated ChatRequestNode user_message = 2 [debug_redact = true];
  // History of previous messages alternating request/response
  repeated Exchange dialog = 3 [debug_redact = true];
  int32 max_tokens = 4;
  string system_prompt = 5 [debug_redact = true];
  float temperature = 6; // default: 0.0f
  repeated ToolDefinition tool_definitions = 7 [debug_redact = true];
  optional ToolChoice tool_choice = 8;
}

message LLMGenerateResponse {
  repeated ChatResultNode response_nodes = 1;
}

message CodebaseRetrievalRequest {
  string information_request = 1 [debug_redact = true];
  repeated Exchange dialog = 2 [debug_redact = true];
  Blobs blobs = 3;
  // Maximum length of formatted_retrieval output
  int32 max_output_length = 4;
  // Flag to disable codebase retrieval
  bool disable_codebase_retrieval = 5;
  // Flag to enable commit retrieval
  bool enable_commit_retrieval = 6;
}

message CodebaseRetrievalResponse {
  string formatted_retrieval = 1;
}

message EditFileRequest {
  string file_path = 1 [debug_redact = true];
  string edit_summary = 2 [debug_redact = true];
  string detailed_edit_description = 3 [debug_redact = true];
  string file_contents = 4 [debug_redact = true];
}

message EditFileResponse {
  string modified_file_contents = 1 [debug_redact = true];
  bool is_error = 2;
}

enum RemoteToolId {
  UNKNOWN = 0;

  // Google search
  WEB_SEARCH = 1;

  // Jira tools
  // To be deprecated in favor of general JIRA tool
  JIRA_SEARCH = 2;
  JIRA_ISSUE = 3;
  JIRA_PROJECT = 4;

  // Notion tools
  // To be deprecated in favor of general NOTION tool
  NOTION_SEARCH = 5;
  NOTION_PAGE = 6;

  // Linear tools
  // Linear search issues tool is deprecated. Use the Linear API tool instead as it
  // supports a superset of the functionality.
  LINEAR_SEARCH_ISSUES = 7 [deprecated = true];

  // GitHub tools
  GITHUB_API = 8;

  // Confluence tools
  // To be deprecated in favor of general CONFLUENCE tool
  CONFLUENCE_SEARCH = 9;
  CONFLUENCE_CONTENT = 10;
  CONFLUENCE_SPACE = 11;

  LINEAR = 12;
  JIRA = 13;
  CONFLUENCE = 14;

  NOTION = 15;

  // Supabase tool
  SUPABASE = 16;

  GLEAN = 17;
}

enum ToolAvailabilityStatus {
  UNKNOWN_STATUS = 0;
  AVAILABLE = 1;
  USER_CONFIG_REQUIRED = 2;
}

enum ToolSafety {
  // Tool always needs user approval to run.
  TOOL_UNSAFE = 0;

  // Tool does not need user approval to run.
  TOOL_SAFE = 1;

  // For some inputs, the tool needs user approval and for some it does not.
  TOOL_CHECK = 2;
}

message RemoteToolIdList {
  repeated RemoteToolId tool_ids = 1;
}

message ListRemoteToolsRequest {
  // A list of tools to get information for, optional for backwards
  // compatibility
  optional RemoteToolIdList tool_id_list = 1;
}

message ListRemoteToolsResponseMessage {
  ToolDefinition tool_definition = 1;

  // Note that not all tools are remote tools, which is why this is separate
  // from ToolDefinition
  RemoteToolId remote_tool_id = 2;
  // The availability status of the tool based on authentication method required
  ToolAvailabilityStatus availability_status = 3;
  ToolSafety tool_safety = 4;
  // OAuth URL that a user can be redirected to for authentication
  string oauth_url = 5;
}

message ListRemoteToolsResponse {
  repeated ListRemoteToolsResponseMessage tools = 1;
}

message AtlassianToolExtraInput {
  // The URL of the Atlassian server, e.g. https://augmentcode.atlassian.net
  string server_url = 1 [debug_redact = true];

  // The user's API token
  string personal_api_token = 2 [debug_redact = true];

  // The user's email address
  string username = 3 [debug_redact = true];
}

message NotionToolExtraInput {
  // The Notion API token
  string api_token = 1 [debug_redact = true];
}

message LinearToolExtraInput {
  // The Linear API token
  string api_token = 1 [debug_redact = true];
}

message GitHubToolExtraInput {
  // The GitHub API token
  string api_token = 1 [debug_redact = true];
}

message RunRemoteToolRequest {
  string tool_name = 1 [deprecated = true];
  string tool_input_json = 2 [debug_redact = true];
  optional RemoteToolId tool_id = 3;

  // Optional: extra input for specific tools
  oneof extra_tool_input {
    AtlassianToolExtraInput atlassian_tool_extra_input = 4;
    NotionToolExtraInput notion_tool_extra_input = 5;
    LinearToolExtraInput linear_tool_extra_input = 6;
    GitHubToolExtraInput github_tool_extra_input = 7;
  }
}

// Status codes for remote tool execution responses
enum RemoteToolResponseStatus {
  // Unknown status
  TOOL_EXECUTION_UNKNOWN_STATUS = 0;

  // Tool executed successfully
  TOOL_EXECUTION_SUCCESS = 1;

  // Tool not found
  TOOL_NOT_FOUND = 2;

  // Invalid input that violates the tool's input schema
  INVALID_TOOL_INPUT = 3;

  // Tool execution failed
  TOOL_EXECUTION_ERROR = 4;

  // Tool is not available due to config
  TOOL_NOT_AVAILABLE = 5;

  // Auth failed
  TOOL_AUTHENTICATION_ERROR = 6;
}

message RunRemoteToolResponse {
  // The main output string that will be shown to the model.
  string tool_output = 1 [debug_redact = true];
  // A description of what the tool did, for logging purposes.
  string tool_result_message = 2 [debug_redact = true];
  // Boolean to indicate if the tool experienced an error
  bool is_error = 3 [deprecated = true];
  // Status code of the response
  RemoteToolResponseStatus status = 4;
}

message CheckToolSafetyRequest {
  RemoteToolId tool_id = 1;
  string tool_input_json = 2 [debug_redact = true];
}

message CheckToolSafetyResponse {
  bool is_safe = 1;
}

message RevokeToolAccessRequest {
  RemoteToolId tool_id = 1;
}

// Status codes for revoking tool access
enum RevokeToolAccessStatus {
  // Unknown status
  REVOKE_TOOL_ACCESS_STATUS_UNKNOWN = 0;

  // Access cannot be revoked or revoke has
  // not been implemented for this tool
  REVOKE_TOOL_ACCESS_STATUS_UNIMPLEMENTED = 1;

  // Tool not found
  REVOKE_TOOL_ACCESS_STATUS_NOT_FOUND = 2;

  // Tool access was successfully revoked
  REVOKE_TOOL_ACCESS_STATUS_SUCCESS = 3;

  // Tool does not have access in the first place
  REVOKE_TOOL_ACCESS_STATUS_NOT_ACTIVE = 4;

  // Revocation failed, tool may still be active
  REVOKE_TOOL_ACCESS_STATUS_FAILED = 5;
}

// Status codes for tool connection test
enum TestToolConnectionStatus {
  // Unknown status
  TEST_TOOL_CONNECTION_STATUS_UNKNOWN = 0;

  // Connection test not implemented for this tool
  TEST_TOOL_CONNECTION_STATUS_UNIMPLEMENTED = 1;

  // Tool id was not found
  TEST_TOOL_CONNECTION_STATUS_NOT_FOUND = 2;

  // Connection is OK and tool is ready to use
  TEST_TOOL_CONNECTION_STATUS_OK = 3;

  // Connection error occurred
  TEST_TOOL_CONNECTION_STATUS_ERROR = 4;

  // Service is unavailable
  TEST_TOOL_CONNECTION_STATUS_SERVICE_UNAVAILABLE = 5;

  // Authentication failure. Likely we did not have proper auth
  TEST_TOOL_CONNECTION_STATUS_AUTHENTICATION_ERROR = 6;
}

message RevokeToolAccessResponse {
  // Status of the tool access revocation operation
  RevokeToolAccessStatus status = 1;
}

message TestToolConnectionRequest {
  RemoteToolId tool_id = 1;
}

message TestToolConnectionResponse {
  TestToolConnectionStatus status = 1;
}

message AgentWorkspaceReportStatusRequest {
  string remote_agent_id = 1;
  RemoteAgentStatus status = 2;

  optional bool has_active_ssh_connection = 3;
}

message AgentWorkspaceReportStatusResponse {}

message AgentWorkspaceReportChatHistoryRequest {
  string remote_agent_id = 1;
  repeated RemoteAgentExchange chat_history = 2;
}

message AgentWorkspaceReportChatHistoryResponse {}

message AgentWorkspacePollUpdateRequest {
  string remote_agent_id = 1;
  // The sequence ID of the last update received
  // by the agent. The server will return all
  // updates with a sequence ID greater than this.
  // NOTE: sequence ID is guranteed to be monotonically
  // increasing, but not necessarily consecutive.
  uint32 last_processed_sequence_id = 2;
}

message AgentWorkspaceInterrupt {}

message AgentWorkspaceChatRequest {
  RemoteAgentChatRequestDetails request_details = 1;
}

message AgentWorkspaceUpdate {
  uint32 sequence_id = 1;
  oneof update {
    AgentWorkspaceInterrupt interrupt = 2;
    AgentWorkspaceChatRequest chat_request = 3;
  }
}

message AgentWorkspacePollUpdateResponse {
  // All updates with a sequence ID greater than the
  // last_processed_sequence_id in the request.
  repeated AgentWorkspaceUpdate updates = 1;
}

message AgentWorkspaceReportSetupLogsRequest {
  string remote_agent_id = 1;
  RemoteWorkspaceSetupStatus workspace_setup_status = 3;
}

message AgentWorkspaceReportSetupLogsResponse {}

message AgentWorkspaceStreamRequest {
  string remote_agent_id = 1;
  // The sequence ID of the last update received
  // by the agent. The server will return all
  // updates with a sequence ID greater than this.
  // NOTE: sequence ID is guranteed to be monotonically
  // increasing, but not necessarily consecutive.
  uint32 last_processed_sequence_id = 2;
}

// Defines the different types of updates that can be sent in a workspace stream.
enum AgentWorkspaceUpdateType {
  // Default unspecified value - should not be used
  AGENT_WORKSPACE_UPDATE_TYPE_UNSPECIFIED = 0;

  // An interrupt request for the workspace
  AGENT_WORKSPACE_UPDATE_INTERRUPT = 1;

  // A chat request for the workspace
  AGENT_WORKSPACE_UPDATE_CHAT_REQUEST = 2;
}

// Represents a single update in the workspace stream.
message AgentWorkspaceStreamUpdate {
  // The type of update, which determines which field is set
  AgentWorkspaceUpdateType type = 1;

  // The sequence ID of this update
  uint32 sequence_id = 2;

  // For AGENT_WORKSPACE_UPDATE_INTERRUPT updates
  optional AgentWorkspaceInterrupt interrupt = 3;

  // For AGENT_WORKSPACE_UPDATE_CHAT_REQUEST updates
  optional AgentWorkspaceChatRequest chat_request = 4;
}

// Represents a response in the workspace stream.
message AgentWorkspaceStreamResponse {
  // A list of structured updates. Multiple updates can be included in a single
  // response. The updates should be associated with at most one sequence ID.
  repeated AgentWorkspaceStreamUpdate updates = 1;
}

message RemoteAgentWorkspaceLogsRequest {
  string remote_agent_id = 1;

  // These are the last step and sequence id that was received from the backend
  // to the frontend. The backend will return all the logs up to 1000 characters
  // including this step, sequence pair.
  // step 0, sequence 0 is a special case where the sequence does not increment,
  // but the returned log updates
  optional uint32 last_processed_step = 2; // default: 0
  optional uint32 last_processed_sequence_id = 3; // default: 0
}

message RemoteAgentWorkspaceLogsResponse {
  string remote_agent_id = 1;
  RemoteWorkspaceSetupStatus workspace_setup_status = 3;
}

// GitHub repository representation
message GithubRepo {
  string owner = 1;
  string name = 2;
  optional string html_url = 3;
  optional string created_at = 4;
  optional string updated_at = 5;
  optional string default_branch = 6;
}

// Request to list GitHub repositories for the authenticated user
message ListGithubReposForAuthenticatedUserRequest {}

// Response containing a list of GitHub repositories
message ListGithubReposForAuthenticatedUserResponse {
  repeated GithubRepo repos = 1;
}

// Request to list branches for a GitHub repository
message ListGithubRepoBranchesRequest {
  // only name and owner of the repository are used
  GithubRepo repo = 1;
  // Page number (1-based, default: 1)
  optional int32 page = 2;
}

// GitHub branch commit information
message GithubBranchCommit {
  string sha = 1;
  string url = 2;
}

// GitHub branch representation
message GithubBranch {
  string name = 1;
  GithubBranchCommit commit = 2;
  bool protected = 3;
}

// Response containing a list of branches
message ListGithubRepoBranchesResponse {
  repeated GithubBranch branches = 1;
  // Whether there are more pages of results
  bool has_next_page = 2;
  // The next page number if has_next_page is true, otherwise 0
  int32 next_page = 3;
}

// Request to get a specific GitHub repository
message GetGithubRepoRequest {
  // only name and owner of the repository are required and used
  GithubRepo repo = 1;
}

// Response containing a GitHub repository
message GetGithubRepoResponse {
  GithubRepo repo = 1;
}

// GitHub user representation
message GithubUser {
  string login = 1;
  string avatar_url = 2;
  string html_url = 3;
}

// GitHub pull request representation
message GithubPullRequest {
  int32 number = 1;
  string title = 2;
  string body = 3;
  string state = 4;
  string html_url = 5;
  string created_at = 6;
  string updated_at = 7;
  string merged_at = 8;
  string closed_at = 9;
  GithubUser user = 10;
  string head_ref = 11;
  string base_ref = 12;
  string head_sha = 13;
  string base_sha = 14;
  bool merged = 15;
  bool draft = 16;
  int32 comments = 17;
  int32 commits = 18;
  int32 additions = 19;
  int32 deletions = 20;
  int32 changed_files = 21;
}

// Request to create a new pull request
message CreatePullRequestRequest {
  GithubRepo repo = 1;
  string title = 2;
  string body = 3;
  string head = 4; // The name of the branch where your changes are implemented
  string base = 5; // The name of the branch you want your changes pulled into
  bool draft = 6; // Whether to create a draft pull request
}

// Response containing the created pull request
message CreatePullRequestResponse {
  GithubPullRequest pull_request = 1;
}

// Request to check if the user has GitHub OAuth configured
message IsUserGithubConfiguredRequest {}

// Response indicating whether the user has GitHub OAuth configured
message IsUserGithubConfiguredResponse {
  bool is_configured = 1;
  // OAuth URL that a user can be redirected to for authentication
  string oauth_url = 2;

  // Whether the user's OAuth is configured but needs to be updated by
  // reinstalling the app (uninstall + reinstall)
  bool configured_but_needs_update = 3;
  // The new scopes that we will get if the app is reinstalled
  string updated_scopes = 4;
}

// Request for getting subscription information
message GetSubscriptionInfoRequest {
  // Empty request as we'll use the authenticated user's information
}

// Response containing subscription status information
message GetSubscriptionInfoResponse {
  oneof subscription {
    EnterpriseSubscription enterprise = 1;
    ActiveSubscription active_subscription = 2;
    InactiveSubscription inactive_subscription = 3;
  }
}

// Enterprise subscription type
message EnterpriseSubscription {}

// Active subscription type
message ActiveSubscription {
  // The date the subscription becomes inactive - could be a trial end date or a paid subscription end date
  google.protobuf.Timestamp end_date = 1;

  // Whether the user is out of credits
  bool usage_balance_depleted = 2;
}

// Inactive subscription type
message InactiveSubscription {}
