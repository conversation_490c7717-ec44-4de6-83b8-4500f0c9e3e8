// the build.rs file is executed by cargo at build-time
// and is used to generate code.
use std::{env, path::Path, path::PathBuf};

fn get_base_dir() -> PathBuf {
    let cwd = env::current_dir().expect("failed to get cwd");
    let root = cwd.join("../../../").canonicalize().unwrap();
    root
}

fn get_external_dir() -> PathBuf {
    let root = get_base_dir();
    if std::env::var("USER").is_err() {
        root.join("..")
    } else {
        root.join("../bazel-augment-external")
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // generate the code from protobuf files via build.rs so that cargo/rust-analyzer continues
    // to work.
    let root = get_base_dir();
    let external_dir = get_external_dir();
    let out_dir = PathBuf::from(env::var("OUT_DIR").unwrap());

    let protos = vec![
        root.join("base/error_details/error_details.proto"),
        root.join("services/agents/agents.proto"),
        root.join("services/chat_host/chat.proto"),
        root.join("services/completion_host/completion.proto"),
        root.join("services/api_proxy/model_finder.proto"),
        root.join("services/api_proxy/public_api.proto"),
        root.join("services/edit_host/edit.proto"),
        root.join("services/next_edit_host/next_edit.proto"),
        root.join("services/share/share.proto"),
        root.join("services/lib/grpc/stream_mux/stream_mux.proto"),
    ];
    for proto in protos {
        let proto_path: &Path = proto.as_ref();

        let proto_dir = proto_path.parent().unwrap();

        let protobuf_src_dir = external_dir.join("protobuf~/src/src");
        let googleapis_dir = external_dir.join("googleapis~");
        let protobuf_any_dir =
            external_dir.join("protobuf~/src/google/protobuf/_virtual_imports/any_proto/");
        let protobuf_descriptor_dir =
            external_dir.join("protobuf~/src/google/protobuf/_virtual_imports/descriptor_proto/");
        let protobuf_duration_dir =
            external_dir.join("protobuf~/src/google/protobuf/_virtual_imports/duration_proto/");
        let protobuf_timestamp_dir =
            external_dir.join("protobuf~/src/google/protobuf/_virtual_imports/timestamp_proto/");
        let includes = vec![
            proto_dir,
            protobuf_src_dir.as_ref(),
            googleapis_dir.as_ref(),
            protobuf_any_dir.as_ref(),
            protobuf_descriptor_dir.as_ref(),
            protobuf_duration_dir.as_ref(),
            protobuf_timestamp_dir.as_ref(),
            &root,
        ];

        tonic_build::configure()
            .extern_path(".google.protobuf.Any", "::prost_wkt_types::Any")
            .extern_path(".google.protobuf.Duration", "::prost_wkt_types::Duration")
            .extern_path(".google.protobuf.Timestamp", "::prost_wkt_types::Timestamp")
            .extern_path(".google.protobuf.Value", "::prost_wkt_types::Value")
            // Derive Rust serialization and deserialization for all types
            .type_attribute(".", "#[derive(serde::Serialize,serde::Deserialize)]")

            // Apply the default serde rules (null / missing field gets interpreted as zero, None, or empty string/array)
            .message_attribute(".", "#[serde(default)]")
            // Make it easier to remove these deprecated CompletionResponse fields without surprising the client
            .field_attribute(
                ".public_api.CompletionResponse.completion_timeout_ms",
                "#[serde(skip_serializing_if = \"Option::is_none\")]",
            )
            .field_attribute(
                ".public_api.CompletionResponse.unknown_checkpoint_id",
                "#[serde(skip_serializing_if = \"Option::is_none\")]",
            )
            // Proto enums require some further specialized handling
            // Custom deserializer for string repr of ChangeType
            .field_attribute(
                ".public_api.WorkingDirectoryChange.change_type",
                "#[serde(deserialize_with = \"crate::handlers_next_edit::deserialize_change_type\")]",
            )
            // Custom deserializer for string repr of DiagnosticSeverity
            .field_attribute(
                ".public_api.Diagnostic.severity",
                "#[serde(deserialize_with = \"crate::handlers_next_edit::deserialize_severity\")]",
            )
            // Custom serializer for string repr of ExternalSourceType
            .field_attribute(
                ".public_api.ListExternalSourceTypesResponse.source_types",
                "#[serde(serialize_with = \"crate::handlers_external_sources::serialize_source_types\")]",
            )
            // Custom deserializer for string repr of ExternalSourceType
            .field_attribute(
                ".public_api.SearchExternalSourcesRequest.source_types",
                "#[serde(deserialize_with = \"crate::handlers_external_sources::deserialize_source_types\")]",
            )
            // Custom serializer for user_tier
            .field_attribute(
                ".public_api.GetModelsResponse.user_tier",
                "#[serde(serialize_with = \"crate::handlers::serialize_user_tier\", deserialize_with = \"crate::handlers::deserialize_user_tier\")]",
            )
            // Custom serializer for string repr of ExternalSourceType
            .field_attribute(
                ".public_api.ExternalSource.source_type",
                "#[serde(serialize_with = \"crate::handlers_external_sources::serialize_source_type\")]",
            )
            // Custom deserializer for string repr of ExternalSourceType (NOTE: only used in handlers_external_sources unit test)
            .field_attribute(
                ".public_api.ExternalSource.source_type",
                "#[serde(deserialize_with = \"crate::handlers_external_sources::deserialize_source_type\")]",
            )
            // Custom deserializer for string repr of ChatMode
            .field_attribute(
                ".public_api.ChatRequest.mode",
                "#[serde(deserialize_with = \"crate::handlers_chat::deserialize_chat_mode\")]",
            )
            .field_attribute(
                ".public_api.ChatFeedback.mode",
                "#[serde(deserialize_with = \"crate::handlers_chat::deserialize_chat_mode\")]",
            )
            // Custom deserializer for string repr of NextEditMode
            .field_attribute(
                ".public_api.NextEditRequest.mode",
                "#[serde(deserialize_with = \"crate::handlers_next_edit::deserialize_next_edit_mode\")]",
            )
            // Custom deserializer for string repr of NextEditScope
            .field_attribute(
                ".public_api.NextEditRequest.scope",
                "#[serde(deserialize_with = \"crate::handlers_next_edit::deserialize_next_edit_scope\")]",
            )
            // Custom deserializer to allow null for NextEditDiagnostics
            .field_attribute(
                ".public_api.NextEditRequest.diagnostics",
                "#[serde(deserialize_with = \"crate::handlers_next_edit::deserialize_next_edit_diagnostics\")]",
            )
            // Custom deserializer to allow null for NextEditRecentChanges
            .field_attribute(
                ".public_api.NextEditRequest.recent_changes",
                "#[serde(deserialize_with = \"crate::handlers_next_edit::deserialize_next_edit_recent_changes\")]",
            )
            // Custom deserializer to allow null for RemoteAgentChatRequestDetails.mcp_servers
            .field_attribute(
                ".public_api.RemoteAgentChatRequestDetails.mcp_servers",
                "#[serde(deserialize_with = \"crate::handlers_remote_agents::deserialize_remote_agent_mcp_server_configs\")]",
            )
            // Prost generates enums for oneofs as normal rust type names (PascalCase) which serde
            // will then use by default; we need to tell it to convert back to snake_case.
            .enum_attribute(".public_api.RunRemoteToolRequest.extra_tool_input", "#[serde(rename_all = \"snake_case\")]")
            .enum_attribute(".public_api.RemoteAgentWorkspaceSetup.starting_files", "#[serde(rename_all = \"snake_case\")]")
            .enum_attribute(".public_api.RemoteAgentClientMessage.client_message", "#[serde(rename_all = \"snake_case\")]")
            .enum_attribute(".public_api.RemoteAgentServerMessage.server_message", "#[serde(rename_all = \"snake_case\")]")
            .enum_attribute(".public_api.AgentWorkspacePollUpdateResponse.update", "#[serde(rename_all = \"snake_case\")]")
            .file_descriptor_set_path(out_dir.join("api_proxy_descriptor.bin"))
            .compile_protos(&[proto_path], &includes)?;
    }

    let model_instance_path = root.join("services/deploy/model_instance/model_instance.proto");
    tonic_build::configure()
        .type_attribute(".", "#[derive(serde::Serialize,serde::Deserialize)]")
        .compile_protos(
            &[&model_instance_path],
            &[model_instance_path.parent().unwrap()],
        )?;

    Ok(())
}
