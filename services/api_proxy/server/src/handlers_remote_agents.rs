use std::convert::TryFrom;
use std::sync::Arc;

use crate::api_auth::User;

use auth_query_client::auth_query::get_token_info_response;

use actix_web::{HttpRequest, HttpResponse};
use request_context::TenantInfo;

use tonic::Status;
use tracing_actix_web::RootSpan;

use crate::handler_utils::{
    gate_on_circuit_breaker, request_context_from_req, status_to_response,
    streaming_http_response_from_receiver, End<PERSON><PERSON><PERSON><PERSON>, Hand<PERSON>, ResponseError,
};
use crate::handlers_chat::{
    OUT_OF_USAGE_CREDITS_DISCLAIMER, OUT_OF_USAGE_CREDITS_DISCLAIMER_MKDOWN_MESSAGE,
};

use crate::model_registry::ModelRegistry;
use crate::public_api_proto;
use crate::request_insight_util::extract_request_metadata;
use content_manager_client::ContentManagerClient;
use request_insight_publisher::request_insight;

use remote_agents_client::chat;
use remote_agents_client::remote_agents;
use serde::{Deserialize, Deserializer};

// Endpoints for features not yet released to the user/tenant/namespace return 404.
pub const ENABLE_REMOTE_AGENTS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("enable_remote_agents", false);
pub const VSCODE_REMOTE_AGENT_MODE_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("vscode_remote_agent_mode_min_version", "");
pub const INTELLIJ_REMOTE_AGENT_MODE_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("intellij_remote_agent_mode_min_version", "");

// TODO: Do we really need different flags for each remote agent endpoint?
pub const CREATE_REMOTE_AGENT_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("create_remote_agent_timeout_ms", 5 * 60 * 1000);
pub const LIST_REMOTE_AGENTS_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("list_remote_agents_timeout_ms", 10 * 1000);
pub const CHAT_REMOTE_AGENT_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("chat_remote_agent_timeout_ms", 180 * 1000); // allow 3 minutes for the resume to work
pub const CHAT_HISTORY_REMOTE_AGENT_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("chat_history_remote_agent_timeout_ms", 60 * 1000);
pub const STOP_REMOTE_AGENT_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("stop_remote_agent_timeout_ms", 5 * 60 * 1000);
pub const DELETE_REMOTE_AGENT_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("delete_remote_agent_timeout_ms", 5 * 60 * 1000);
pub const REMOTE_AGENT_ADD_SSH_KEY_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("remote_agent_add_ssh_key_timeout_ms", 5 * 60 * 1000);
pub const REMOTE_AGENT_RESUME_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("remote_agent_resume_timeout_ms", 5 * 60 * 1000);
pub const REMOTE_AGENT_PAUSE_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("remote_agent_pause_timeout_ms", 5 * 60 * 1000);

// TODO: Do we really need different flags for each remote agent workspace endpoint?
pub const WORKSPACE_REPORT_STATUS_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("workspace_report_status_timeout_ms", 10 * 1000);
pub const WORKSPACE_POLL_UPDATE_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("workspace_poll_update_timeout_ms", 10 * 1000);
pub const WORKSPACE_LOGS_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("workspace_logs_timeout_ms", 10 * 1000);

// The circuit breaker flag, in contrast, cuts off access to an already public endpoint, making
// it respond with "resource exhausted"
pub const CB_REMOTE_AGENTS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_remote_agents", false);

// Custom deserializer functions for handling null values in repeated fields
pub fn deserialize_remote_agent_mcp_server_configs<'de, D>(
    deserializer: D,
) -> Result<Vec<crate::public_api_proto::McpServerConfig>, D::Error>
where
    D: Deserializer<'de>,
{
    let s: Option<Vec<crate::public_api_proto::McpServerConfig>> =
        Deserialize::deserialize(deserializer)?;
    Ok(s.unwrap_or_default())
}

fn gate_agents(
    feature_flags: &feature_flags::FeatureFlagsServiceHandle,
    req: &HttpRequest,
    tenant_info: &TenantInfo,
) -> Result<(), Status> {
    gate_on_circuit_breaker(&CB_REMOTE_AGENTS, feature_flags, req, tenant_info)?;
    if ENABLE_REMOTE_AGENTS.get_from(feature_flags) {
        return Ok(());
    }
    Err(Status::not_found("Not found"))
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> Handler<MR, CNC> {
    fn create_agent_request_from_public_api(
        &self,
        from: public_api_proto::CreateRemoteAgentRequest,
    ) -> Result<remote_agents::CreateAgentRequest, tonic::Status> {
        let chat_request_details = from
            .initial_request_details
            .ok_or_else(|| tonic::Status::invalid_argument("missing initial_request_details"))?;

        let github_ref = match &from.workspace_setup {
            Some(setup) => match &setup.starting_files {
                Some(
                    public_api_proto::remote_agent_workspace_setup::StartingFiles::GithubCommitRef(
                        ref github_commit_ref,
                    ),
                ) => github_commit_ref,
                None => {
                    return Err(tonic::Status::invalid_argument(
                        "missing starting_files in workspace_setup",
                    ));
                }
            },
            None => {
                return Err(tonic::Status::invalid_argument("missing workspace_setup"));
            }
        };

        Ok(remote_agents::CreateAgentRequest {
            config: Some(remote_agents::AgentConfig {
                workspace_setup: Some(remote_agents::WorkspaceSetup {
                    starting_files: Some(remote_agents::workspace_setup::StartingFiles::GithubRef(
                        remote_agents::GithubRef {
                            url: github_ref.repository_url.clone(),
                            r#ref: github_ref.git_ref.clone(),
                        },
                    )),
                    setup_script: from.setup_script,
                    patch: github_ref.patch.clone(),
                    token: from.token,
                    api_url: Some(format!("https://{}", self.config.ingress_hostname.clone())),
                    git_config: from.workspace_setup.unwrap().git_config.clone(),
                }),
                starting_nodes: chat_request_details
                    .request_nodes
                    .into_iter()
                    .map(Into::into)
                    .collect(),
                user_guidelines: chat_request_details.user_guidelines,
                workspace_guidelines: chat_request_details.workspace_guidelines,
                agent_memories: chat_request_details.agent_memories,
                model: from.model,
                is_setup_script_agent: from.is_setup_script_agent,
                mcp_servers: chat_request_details
                    .mcp_servers
                    .into_iter()
                    .map(Into::into)
                    .collect(),
            }),
        })
    }
}

impl From<remote_agents::CreateAgentResponse> for public_api_proto::CreateRemoteAgentResponse {
    fn from(from: remote_agents::CreateAgentResponse) -> Self {
        let agent = from.agent.unwrap();
        public_api_proto::CreateRemoteAgentResponse {
            remote_agent_id: agent.remote_agent_id,
            status: remote_agents_status_to_public_api(agent.status),
        }
    }
}

impl TryFrom<public_api_proto::ListRemoteAgentsRequest> for remote_agents::ListAgentsRequest {
    type Error = tonic::Status;
    fn try_from(_from: public_api_proto::ListRemoteAgentsRequest) -> Result<Self, Self::Error> {
        Ok(remote_agents::ListAgentsRequest {})
    }
}

impl From<remote_agents::ListAgentsResponse> for public_api_proto::ListRemoteAgentsResponse {
    fn from(from: remote_agents::ListAgentsResponse) -> Self {
        public_api_proto::ListRemoteAgentsResponse {
            remote_agents: from.agents.into_iter().map(Into::into).collect(),
            max_remote_agents: from.max_agents,
            max_active_remote_agents: from.max_active_agents,
        }
    }
}

impl TryFrom<public_api_proto::ListRemoteAgentsStreamRequest>
    for remote_agents::ListAgentsStreamRequest
{
    type Error = tonic::Status;
    fn try_from(
        from: public_api_proto::ListRemoteAgentsStreamRequest,
    ) -> Result<Self, Self::Error> {
        Ok(remote_agents::ListAgentsStreamRequest {
            last_update_timestamp: from.last_update_timestamp,
        })
    }
}

impl From<remote_agents::ListAgentsStreamResponse>
    for public_api_proto::ListRemoteAgentsStreamResponse
{
    fn from(from: remote_agents::ListAgentsStreamResponse) -> Self {
        // Convert the remote_agents updates to public_api updates
        let mut updates = Vec::new();

        for update in from.updates {
            match remote_agents::AgentListUpdateType::try_from(update.r#type) {
                Ok(remote_agents::AgentListUpdateType::AgentListAgentAdded) => {
                    if let Some(agent) = update.agent {
                        updates.push(public_api_proto::AgentListUpdate {
                            r#type: public_api_proto::AgentListUpdateType::AgentListAgentAdded
                                as i32,
                            update_timestamp: update.update_timestamp,
                            agent: Some(agent.into()),
                            deleted_agent_id: None,
                            all_agents: vec![],
                            max_agents: update.max_agents,
                            max_active_agents: update.max_active_agents,
                        });
                    }
                }
                Ok(remote_agents::AgentListUpdateType::AgentListAgentUpdated) => {
                    if let Some(agent) = update.agent {
                        updates.push(public_api_proto::AgentListUpdate {
                            r#type: public_api_proto::AgentListUpdateType::AgentListAgentUpdated
                                as i32,
                            update_timestamp: update.update_timestamp,
                            agent: Some(agent.into()),
                            deleted_agent_id: None,
                            all_agents: vec![],
                            max_agents: update.max_agents,
                            max_active_agents: update.max_active_agents,
                        });
                    }
                }
                Ok(remote_agents::AgentListUpdateType::AgentListAgentDeleted) => {
                    updates.push(public_api_proto::AgentListUpdate {
                        r#type: public_api_proto::AgentListUpdateType::AgentListAgentDeleted as i32,
                        update_timestamp: update.update_timestamp,
                        agent: None,
                        deleted_agent_id: update.deleted_agent_id,
                        all_agents: vec![],
                        max_agents: update.max_agents,
                        max_active_agents: update.max_active_agents,
                    });
                }
                Ok(remote_agents::AgentListUpdateType::AgentListAllAgents) => {
                    updates.push(public_api_proto::AgentListUpdate {
                        r#type: public_api_proto::AgentListUpdateType::AgentListAllAgents as i32,
                        update_timestamp: update.update_timestamp,
                        agent: None,
                        deleted_agent_id: None,
                        all_agents: update.all_agents.into_iter().map(Into::into).collect(),
                        max_agents: update.max_agents,
                        max_active_agents: update.max_active_agents,
                    });
                }
                _ => {
                    tracing::warn!("Unknown agent list update type: {}", update.r#type);
                }
            }
        }

        public_api_proto::ListRemoteAgentsStreamResponse { updates }
    }
}

impl TryInto<crate::generation_clients::ResponseStatusCode>
    for remote_agents::ListAgentsStreamResponse
{
    type Error = ();
    fn try_into(self) -> Result<crate::generation_clients::ResponseStatusCode, Self::Error> {
        Ok(crate::generation_clients::ResponseStatusCode::Ok)
    }
}

impl TryFrom<public_api_proto::RemoteAgentChatRequest> for remote_agents::ChatRequest {
    type Error = tonic::Status;
    fn try_from(from: public_api_proto::RemoteAgentChatRequest) -> Result<Self, Self::Error> {
        Ok(remote_agents::ChatRequest {
            remote_agent_id: from.remote_agent_id,
            request_details: from.request_details.map(Into::into),
        })
    }
}

impl From<remote_agents::ChatResponse> for public_api_proto::RemoteAgentChatResponse {
    fn from(_from: remote_agents::ChatResponse) -> Self {
        public_api_proto::RemoteAgentChatResponse {}
    }
}

impl TryFrom<public_api_proto::GetRemoteAgentChatHistoryRequest>
    for remote_agents::ChatHistoryRequest
{
    type Error = tonic::Status;
    fn try_from(
        from: public_api_proto::GetRemoteAgentChatHistoryRequest,
    ) -> Result<Self, Self::Error> {
        Ok(remote_agents::ChatHistoryRequest {
            remote_agent_id: from.remote_agent_id,
            last_processed_sequence_id: from.last_processed_sequence_id,
        })
    }
}

impl TryFrom<public_api_proto::GetRemoteAgentHistoryStreamRequest>
    for remote_agents::AgentHistoryStreamRequest
{
    type Error = tonic::Status;
    fn try_from(
        from: public_api_proto::GetRemoteAgentHistoryStreamRequest,
    ) -> Result<Self, Self::Error> {
        Ok(remote_agents::AgentHistoryStreamRequest {
            remote_agent_id: from.remote_agent_id,
            last_processed_sequence_id: from.last_processed_sequence_id,
        })
    }
}

impl From<remote_agents::ChatHistoryResponse>
    for public_api_proto::GetRemoteAgentChatHistoryResponse
{
    fn from(from: remote_agents::ChatHistoryResponse) -> Self {
        public_api_proto::GetRemoteAgentChatHistoryResponse {
            chat_history: from.chat_history.into_iter().map(Into::into).collect(),
            session_summary: from.session_summary,
            remote_agent: from.agent.map(Into::into),
        }
    }
}

impl From<remote_agents::AgentHistoryStreamResponse>
    for public_api_proto::GetRemoteAgentHistoryStreamResponse
{
    fn from(from: remote_agents::AgentHistoryStreamResponse) -> Self {
        // Convert the remote_agents updates to public_api updates
        let mut updates = Vec::new();

        for update in from.updates {
            match remote_agents::AgentHistoryUpdateType::try_from(update.r#type) {
                Ok(remote_agents::AgentHistoryUpdateType::AgentHistoryExchange) => {
                    if let Some(exchange) = update.exchange {
                        updates.push(public_api_proto::AgentHistoryUpdate {
                            r#type: public_api_proto::AgentHistoryUpdateType::AgentHistoryExchange
                                as i32,
                            exchange: Some(exchange.into()),
                            exchange_update: None,
                            agent: None,
                        });
                    }
                }
                Ok(remote_agents::AgentHistoryUpdateType::AgentHistoryExchangeUpdate) => {
                    if let Some(exchange_update) = update.exchange_update {
                        updates.push(public_api_proto::AgentHistoryUpdate {
                            r#type:
                                public_api_proto::AgentHistoryUpdateType::AgentHistoryExchangeUpdate
                                    as i32,
                            exchange: None,
                            exchange_update: Some(public_api_proto::ExchangeUpdate {
                                request_id: exchange_update.request_id,
                                sequence_id: exchange_update.sequence_id,
                                appended_text: exchange_update.appended_text,
                                appended_nodes: exchange_update
                                    .appended_nodes
                                    .into_iter()
                                    .map(Into::into)
                                    .collect(),
                                appended_changed_files: exchange_update
                                    .appended_changed_files
                                    .into_iter()
                                    .map(Into::into)
                                    .collect(),
                            }),
                            agent: None,
                        });
                    }
                }
                Ok(remote_agents::AgentHistoryUpdateType::AgentHistoryAgentStatus) => {
                    if let Some(agent) = update.agent {
                        updates.push(public_api_proto::AgentHistoryUpdate {
                            r#type:
                                public_api_proto::AgentHistoryUpdateType::AgentHistoryAgentStatus
                                    as i32,
                            exchange: None,
                            exchange_update: None,
                            agent: Some(agent.into()),
                        });
                    }
                }
                _ => {
                    tracing::warn!("Unknown update type: {}", update.r#type);
                }
            }
        }

        public_api_proto::GetRemoteAgentHistoryStreamResponse { updates }
    }
}

impl TryInto<crate::generation_clients::ResponseStatusCode>
    for remote_agents::AgentHistoryStreamResponse
{
    type Error = ();
    fn try_into(self) -> Result<crate::generation_clients::ResponseStatusCode, Self::Error> {
        Ok(crate::generation_clients::ResponseStatusCode::Ok)
    }
}

impl TryFrom<public_api_proto::InterruptRemoteAgentRequest>
    for remote_agents::InterruptAgentRequest
{
    type Error = tonic::Status;
    fn try_from(from: public_api_proto::InterruptRemoteAgentRequest) -> Result<Self, Self::Error> {
        Ok(remote_agents::InterruptAgentRequest {
            remote_agent_id: from.remote_agent_id,
        })
    }
}

impl From<remote_agents::InterruptAgentResponse>
    for public_api_proto::InterruptRemoteAgentResponse
{
    fn from(_from: remote_agents::InterruptAgentResponse) -> Self {
        // Use AGENT_UNSPECIFIED as the default status since the remote_agents service
        // doesn't return a status in its response
        public_api_proto::InterruptRemoteAgentResponse {
            status: public_api_proto::RemoteAgentStatus::AgentUnspecified as i32,
        }
    }
}

impl TryFrom<public_api_proto::DeleteRemoteAgentRequest> for remote_agents::DeleteAgentRequest {
    type Error = tonic::Status;
    fn try_from(from: public_api_proto::DeleteRemoteAgentRequest) -> Result<Self, Self::Error> {
        Ok(remote_agents::DeleteAgentRequest {
            remote_agent_id: from.remote_agent_id,
        })
    }
}

impl From<remote_agents::DeleteAgentResponse> for public_api_proto::DeleteRemoteAgentResponse {
    fn from(_from: remote_agents::DeleteAgentResponse) -> Self {
        public_api_proto::DeleteRemoteAgentResponse {}
    }
}

impl TryFrom<public_api_proto::RemoteAgentAddSshKeyRequest> for remote_agents::AddSshKeyRequest {
    type Error = tonic::Status;
    fn try_from(from: public_api_proto::RemoteAgentAddSshKeyRequest) -> Result<Self, Self::Error> {
        Ok(remote_agents::AddSshKeyRequest {
            remote_agent_id: from.remote_agent_id,
            public_keys: from.public_keys,
        })
    }
}

impl From<remote_agents::AddSshKeyResponse> for public_api_proto::RemoteAgentAddSshKeyResponse {
    fn from(from: remote_agents::AddSshKeyResponse) -> Self {
        public_api_proto::RemoteAgentAddSshKeyResponse {
            ssh_config: from.ssh_config.map(Into::into),
        }
    }
}

impl TryFrom<public_api_proto::RemoteAgentResumeRequest> for remote_agents::ResumeAgentRequest {
    type Error = tonic::Status;
    fn try_from(from: public_api_proto::RemoteAgentResumeRequest) -> Result<Self, Self::Error> {
        Ok(remote_agents::ResumeAgentRequest {
            remote_agent_id: from.remote_agent_id,
        })
    }
}

impl From<remote_agents::ResumeAgentResponse> for public_api_proto::RemoteAgentResumeResponse {
    fn from(_from: remote_agents::ResumeAgentResponse) -> Self {
        public_api_proto::RemoteAgentResumeResponse {}
    }
}

impl TryFrom<public_api_proto::RemoteAgentPauseRequest> for remote_agents::PauseAgentRequest {
    type Error = tonic::Status;
    fn try_from(from: public_api_proto::RemoteAgentPauseRequest) -> Result<Self, Self::Error> {
        Ok(remote_agents::PauseAgentRequest {
            remote_agent_id: from.remote_agent_id,
        })
    }
}

impl From<remote_agents::PauseAgentResponse> for public_api_proto::RemoteAgentPauseResponse {
    fn from(_from: remote_agents::PauseAgentResponse) -> Self {
        public_api_proto::RemoteAgentPauseResponse {}
    }
}

impl TryFrom<public_api_proto::AgentWorkspaceReportStatusRequest>
    for remote_agents::WorkspaceReportStatusRequest
{
    type Error = tonic::Status;
    fn try_from(
        from: public_api_proto::AgentWorkspaceReportStatusRequest,
    ) -> Result<Self, Self::Error> {
        Ok(remote_agents::WorkspaceReportStatusRequest {
            remote_agent_id: from.remote_agent_id,
            status: from.status,
            has_active_ssh_connection: from.has_active_ssh_connection.unwrap_or_default(),
        })
    }
}

impl From<remote_agents::WorkspaceReportStatusResponse>
    for public_api_proto::AgentWorkspaceReportStatusResponse
{
    fn from(_from: remote_agents::WorkspaceReportStatusResponse) -> Self {
        public_api_proto::AgentWorkspaceReportStatusResponse {}
    }
}

impl TryFrom<public_api_proto::AgentWorkspacePollUpdateRequest>
    for remote_agents::WorkspacePollUpdateRequest
{
    type Error = tonic::Status;
    fn try_from(
        from: public_api_proto::AgentWorkspacePollUpdateRequest,
    ) -> Result<Self, Self::Error> {
        Ok(remote_agents::WorkspacePollUpdateRequest {
            remote_agent_id: from.remote_agent_id,
            last_processed_sequence_id: from.last_processed_sequence_id,
        })
    }
}

impl From<remote_agents::WorkspaceUpdate> for public_api_proto::AgentWorkspaceUpdate {
    fn from(from: remote_agents::WorkspaceUpdate) -> Self {
        let update = match &from.update {
            Some(update_value) => {
                // Use the TryFrom implementation to convert the update
                match public_api_proto::agent_workspace_update::Update::try_from(
                    update_value.clone(),
                ) {
                    Ok(converted) => Some(converted),
                    Err(err) => {
                        tracing::error!("Failed to convert workspace update: {}", err);
                        None
                    }
                }
            }
            None => None,
        };

        public_api_proto::AgentWorkspaceUpdate {
            sequence_id: from.sequence_id,
            update,
        }
    }
}

impl From<remote_agents::WorkspaceChatRequest> for public_api_proto::AgentWorkspaceChatRequest {
    fn from(from: remote_agents::WorkspaceChatRequest) -> Self {
        let request_details = from.request_details.map(Into::into);
        public_api_proto::AgentWorkspaceChatRequest { request_details }
    }
}

impl From<remote_agents::WorkspaceLogsResponse>
    for public_api_proto::RemoteAgentWorkspaceLogsResponse
{
    fn from(from: remote_agents::WorkspaceLogsResponse) -> Self {
        public_api_proto::RemoteAgentWorkspaceLogsResponse {
            remote_agent_id: from.remote_agent_id,
            workspace_setup_status: from.workspace_setup_status.map(Into::into),
        }
    }
}

impl TryFrom<public_api_proto::RemoteAgentWorkspaceLogsRequest>
    for remote_agents::WorkspaceLogsRequest
{
    type Error = tonic::Status;
    fn try_from(
        from: public_api_proto::RemoteAgentWorkspaceLogsRequest,
    ) -> Result<Self, Self::Error> {
        Ok(remote_agents::WorkspaceLogsRequest {
            remote_agent_id: from.remote_agent_id,
            last_processed_step: from.last_processed_step,
            last_processed_sequence_id: from.last_processed_sequence_id,
        })
    }
}

impl TryFrom<public_api_proto::AgentWorkspaceReportSetupLogsRequest>
    for remote_agents::WorkspaceReportSetupLogsRequest
{
    type Error = tonic::Status;
    fn try_from(
        from: public_api_proto::AgentWorkspaceReportSetupLogsRequest,
    ) -> Result<Self, Self::Error> {
        Ok(remote_agents::WorkspaceReportSetupLogsRequest {
            remote_agent_id: from.remote_agent_id,
            workspace_setup_status: from.workspace_setup_status.map(Into::into),
        })
    }
}

impl From<remote_agents::WorkspaceReportSetupLogsResponse>
    for public_api_proto::AgentWorkspaceReportSetupLogsResponse
{
    fn from(_from: remote_agents::WorkspaceReportSetupLogsResponse) -> Self {
        public_api_proto::AgentWorkspaceReportSetupLogsResponse {}
    }
}

impl TryFrom<remote_agents::workspace_update::Update>
    for public_api_proto::agent_workspace_update::Update
{
    type Error = tonic::Status;

    fn try_from(from: remote_agents::workspace_update::Update) -> Result<Self, Self::Error> {
        match from {
            remote_agents::workspace_update::Update::Interrupt(_) => {
                Ok(public_api_proto::agent_workspace_update::Update::Interrupt(
                    public_api_proto::AgentWorkspaceInterrupt {},
                ))
            }
            remote_agents::workspace_update::Update::ChatRequest(chat_request) => {
                let agent_workspace_chat_request =
                    public_api_proto::AgentWorkspaceChatRequest::from(chat_request);
                Ok(
                    public_api_proto::agent_workspace_update::Update::ChatRequest(
                        agent_workspace_chat_request,
                    ),
                )
            }
        }
    }
}

impl From<remote_agents::WorkspacePollUpdateResponse>
    for public_api_proto::AgentWorkspacePollUpdateResponse
{
    fn from(from: remote_agents::WorkspacePollUpdateResponse) -> Self {
        // Note: Field number is different between the two protos (2 in remote_agents vs 1 in public_api)
        public_api_proto::AgentWorkspacePollUpdateResponse {
            updates: from.updates.into_iter().map(Into::into).collect(),
        }
    }
}

impl TryFrom<public_api_proto::AgentWorkspaceStreamRequest>
    for remote_agents::WorkspaceStreamRequest
{
    type Error = tonic::Status;
    fn try_from(from: public_api_proto::AgentWorkspaceStreamRequest) -> Result<Self, Self::Error> {
        Ok(remote_agents::WorkspaceStreamRequest {
            remote_agent_id: from.remote_agent_id,
            last_processed_sequence_id: from.last_processed_sequence_id,
        })
    }
}

impl From<remote_agents::WorkspaceStreamUpdate> for public_api_proto::AgentWorkspaceStreamUpdate {
    fn from(from: remote_agents::WorkspaceStreamUpdate) -> Self {
        let update_type = match remote_agents::WorkspaceUpdateType::try_from(from.r#type) {
            Ok(remote_agents::WorkspaceUpdateType::WorkspaceUpdateInterrupt) => {
                public_api_proto::AgentWorkspaceUpdateType::AgentWorkspaceUpdateInterrupt as i32
            }
            Ok(remote_agents::WorkspaceUpdateType::WorkspaceUpdateChatRequest) => {
                public_api_proto::AgentWorkspaceUpdateType::AgentWorkspaceUpdateChatRequest as i32
            }
            _ => {
                tracing::warn!("Unknown workspace update type: {}", from.r#type);
                public_api_proto::AgentWorkspaceUpdateType::Unspecified as i32
            }
        };

        public_api_proto::AgentWorkspaceStreamUpdate {
            r#type: update_type,
            sequence_id: from.sequence_id,
            interrupt: from
                .interrupt
                .map(|_| public_api_proto::AgentWorkspaceInterrupt {}),
            chat_request: from.chat_request.map(Into::into),
        }
    }
}

impl From<remote_agents::WorkspaceStreamResponse>
    for public_api_proto::AgentWorkspaceStreamResponse
{
    fn from(from: remote_agents::WorkspaceStreamResponse) -> Self {
        public_api_proto::AgentWorkspaceStreamResponse {
            updates: from.updates.into_iter().map(Into::into).collect(),
        }
    }
}

impl TryInto<crate::generation_clients::ResponseStatusCode>
    for remote_agents::WorkspaceStreamResponse
{
    type Error = ();
    fn try_into(self) -> Result<crate::generation_clients::ResponseStatusCode, Self::Error> {
        Ok(crate::generation_clients::ResponseStatusCode::Ok)
    }
}

impl From<public_api_proto::Exchange> for chat::Exchange {
    fn from(from: public_api_proto::Exchange) -> Self {
        chat::Exchange {
            request_message: from.request_message,
            response_text: from.response_text,
            request_id: from.request_id,
            request_nodes: from.request_nodes.into_iter().map(Into::into).collect(),
            response_nodes: from.response_nodes.into_iter().map(Into::into).collect(),
        }
    }
}

impl From<remote_agents::SshConfigOption> for public_api_proto::SshConfigOption {
    fn from(from: remote_agents::SshConfigOption) -> Self {
        public_api_proto::SshConfigOption {
            key: from.key,
            value: from.value,
        }
    }
}

impl From<remote_agents::AgentSshConfig> for public_api_proto::RemoteAgentSshConfig {
    fn from(from: remote_agents::AgentSshConfig) -> Self {
        public_api_proto::RemoteAgentSshConfig {
            public_keys: from.public_keys,
            hostname: from.hostname,
            ssh_config_options: from
                .ssh_config_options
                .into_iter()
                .map(Into::into)
                .collect(),
        }
    }
}

impl From<remote_agents::Agent> for public_api_proto::RemoteAgent {
    fn from(from: remote_agents::Agent) -> Self {
        let workspace_setup = match from
            .config
            .clone()
            .and_then(|c| c.workspace_setup)
            .and_then(|ws| ws.starting_files)
        {
            Some(remote_agents::workspace_setup::StartingFiles::GithubRef(github_ref)) => {
                let github_commit_ref = public_api_proto::GithubCommitRef {
                    repository_url: github_ref.url,
                    git_ref: github_ref.r#ref,
                    patch: None,
                };
                Some(public_api_proto::RemoteAgentWorkspaceSetup {
                    starting_files: Some(public_api_proto::remote_agent_workspace_setup::StartingFiles::GithubCommitRef(github_commit_ref)),
                    git_config: from.config.clone().and_then(|c| c.workspace_setup).map(|ws| ws.git_config).unwrap_or_default(),
                })
            }
            _ => None,
        };

        public_api_proto::RemoteAgent {
            remote_agent_id: from.remote_agent_id,
            workspace_setup,
            status: remote_agents_status_to_public_api(from.status),
            workspace_status: remote_agents_workspace_status_to_public_api(from.workspace_status),
            started_at: from.created_at,
            updated_at: from.updated_at,
            session_summary: from
                .config
                .clone()
                .and_then(|config| {
                    if !config.starting_nodes.is_empty() {
                        config.starting_nodes[0]
                            .text_node
                            .as_ref()
                            .map(|text_node| text_node.content.clone())
                    } else {
                        None
                    }
                })
                .unwrap_or_else(|| "No Session Summary".to_string()),
            turn_summaries: vec![],
            ssh_config: from.ssh_config.map(Into::into),
            is_setup_script_agent: from.config.unwrap().is_setup_script_agent,
            has_updates: from.has_updates,
            expires_at: from.expires_at,
        }
    }
}

impl From<public_api_proto::McpServerConfig> for remote_agents::McpServerConfig {
    fn from(from: public_api_proto::McpServerConfig) -> Self {
        remote_agents::McpServerConfig {
            command: from.command,
            args: from.args,
            timeout_ms: from.timeout_ms,
            env: from.env,
            use_shell_interpolation: from.use_shell_interpolation,
            name: from.name,
            disabled: from.disabled,
        }
    }
}

impl From<public_api_proto::RemoteAgentChatRequestDetails> for remote_agents::ChatRequestDetails {
    fn from(from: public_api_proto::RemoteAgentChatRequestDetails) -> Self {
        remote_agents::ChatRequestDetails {
            request_nodes: from.request_nodes.into_iter().map(Into::into).collect(),
            user_guidelines: from.user_guidelines,
            workspace_guidelines: from.workspace_guidelines,
            agent_memories: from.agent_memories,
            model_id: from.model_id,
            mcp_servers: from.mcp_servers.into_iter().map(Into::into).collect(),
        }
    }
}

impl From<remote_agents::McpServerConfig> for public_api_proto::McpServerConfig {
    fn from(from: remote_agents::McpServerConfig) -> Self {
        public_api_proto::McpServerConfig {
            command: from.command,
            args: from.args,
            timeout_ms: from.timeout_ms,
            env: from.env,
            use_shell_interpolation: from.use_shell_interpolation,
            name: from.name,
            disabled: from.disabled,
        }
    }
}

impl From<remote_agents::ChatRequestDetails> for public_api_proto::RemoteAgentChatRequestDetails {
    fn from(from: remote_agents::ChatRequestDetails) -> Self {
        public_api_proto::RemoteAgentChatRequestDetails {
            request_nodes: from.request_nodes.into_iter().map(Into::into).collect(),
            user_guidelines: from.user_guidelines,
            workspace_guidelines: from.workspace_guidelines,
            agent_memories: from.agent_memories,
            model_id: from.model_id,
            mcp_servers: from.mcp_servers.into_iter().map(Into::into).collect(),
        }
    }
}

fn remote_agents_status_to_public_api(i: i32) -> i32 {
    match remote_agents::AgentStatus::try_from(i) {
        Ok(st) => match st {
            remote_agents::AgentStatus::Unspecified => {
                public_api_proto::RemoteAgentStatus::AgentUnspecified as i32
            }
            remote_agents::AgentStatus::Pending => {
                public_api_proto::RemoteAgentStatus::AgentPending as i32
            }
            remote_agents::AgentStatus::Starting => {
                public_api_proto::RemoteAgentStatus::AgentStarting as i32
            }
            remote_agents::AgentStatus::Running => {
                public_api_proto::RemoteAgentStatus::AgentRunning as i32
            }
            remote_agents::AgentStatus::Idle => {
                public_api_proto::RemoteAgentStatus::AgentIdle as i32
            }
            remote_agents::AgentStatus::Failed => {
                public_api_proto::RemoteAgentStatus::AgentFailed as i32
            }
            remote_agents::AgentStatus::Pausing => {
                public_api_proto::RemoteAgentStatus::AgentPausing as i32
            }
            remote_agents::AgentStatus::Paused => {
                public_api_proto::RemoteAgentStatus::AgentPaused as i32
            }
            remote_agents::AgentStatus::PendingDeletion => {
                tracing::error!("Pending deletion is not a valid status for public API");
                public_api_proto::RemoteAgentStatus::AgentUnspecified as i32
            }
        },
        Err(err) => {
            tracing::error!(
                "Unknown remote_agents.proto::AgentStatus::{}, returning as is: {}",
                i,
                err
            );
            i
        }
    }
}

fn remote_agents_workspace_status_to_public_api(i: i32) -> i32 {
    match remote_agents::WorkspaceStatus::try_from(i) {
        Ok(st) => match st {
            remote_agents::WorkspaceStatus::Unspecified => {
                public_api_proto::RemoteAgentWorkspaceStatus::Unspecified as i32
            }
            remote_agents::WorkspaceStatus::Running => {
                public_api_proto::RemoteAgentWorkspaceStatus::Running as i32
            }
            remote_agents::WorkspaceStatus::Pausing => {
                public_api_proto::RemoteAgentWorkspaceStatus::Pausing as i32
            }
            remote_agents::WorkspaceStatus::Paused => {
                public_api_proto::RemoteAgentWorkspaceStatus::Paused as i32
            }
            remote_agents::WorkspaceStatus::Resuming => {
                public_api_proto::RemoteAgentWorkspaceStatus::Resuming as i32
            }
            remote_agents::WorkspaceStatus::Deleting => {
                public_api_proto::RemoteAgentWorkspaceStatus::Deleting as i32
            }
        },
        Err(err) => {
            tracing::error!(
                "Unknown remote_agents.proto::WorkspaceStatus::{}, returning as is: {}",
                i,
                err,
            );
            i
        }
    }
}

impl From<remote_agents::WorkspaceSetupStep> for public_api_proto::RemoteWorkspaceSetupStep {
    fn from(from: remote_agents::WorkspaceSetupStep) -> Self {
        public_api_proto::RemoteWorkspaceSetupStep {
            step_description: from.step_description,
            logs: from.logs,
            status: from.status,
            sequence_id: from.sequence_id,
            step_number: from.step_number,
        }
    }
}

impl From<public_api_proto::RemoteWorkspaceSetupStep> for remote_agents::WorkspaceSetupStep {
    fn from(from: public_api_proto::RemoteWorkspaceSetupStep) -> Self {
        remote_agents::WorkspaceSetupStep {
            step_description: from.step_description,
            logs: from.logs,
            status: from.status,
            sequence_id: from.sequence_id,
            step_number: from.step_number,
        }
    }
}

impl From<remote_agents::WorkspaceSetupStatus> for public_api_proto::RemoteWorkspaceSetupStatus {
    fn from(from: remote_agents::WorkspaceSetupStatus) -> Self {
        public_api_proto::RemoteWorkspaceSetupStatus {
            steps: from.steps.into_iter().map(Into::into).collect(),
        }
    }
}

impl From<public_api_proto::RemoteWorkspaceSetupStatus> for remote_agents::WorkspaceSetupStatus {
    fn from(from: public_api_proto::RemoteWorkspaceSetupStatus) -> Self {
        remote_agents::WorkspaceSetupStatus {
            steps: from.steps.into_iter().map(Into::into).collect(),
        }
    }
}

impl From<public_api_proto::ChatRequestNode> for chat::ChatRequestNode {
    fn from(from: public_api_proto::ChatRequestNode) -> Self {
        chat::ChatRequestNode {
            id: from.id,
            r#type: from.r#type,
            text_node: from.text_node.map(|text_node| chat::ChatRequestText {
                content: text_node.content,
            }),
            tool_result_node: from.tool_result_node.map(|tool_result_node| {
                chat::ChatRequestToolResult {
                    tool_use_id: tool_result_node.tool_use_id,
                    content: tool_result_node.content,
                    is_error: tool_result_node.is_error,
                    request_id: tool_result_node.request_id,
                    content_nodes: tool_result_node
                        .content_nodes
                        .into_iter()
                        .map(|content_node| chat::ToolResultContentNode {
                            r#type: content_node.r#type,
                            text_content: content_node.text_content,
                            image_content: content_node.image_content.map(|image_content| {
                                chat::ChatRequestImage {
                                    image_data: image_content.image_data,
                                    format: image_content.format,
                                }
                            }),
                        })
                        .collect(),
                }
            }),
            image_node: from.image_node.map(|image_node| chat::ChatRequestImage {
                image_data: image_node.image_data,
                format: image_node.format,
            }),
            ide_state_node: from.ide_state_node.map(|ide_state_node| {
                let mut chat_ide_state = chat::ChatRequestIdeState::default();

                // Copy workspace folders from the request
                for folder_info in &ide_state_node.workspace_folders {
                    chat_ide_state
                        .workspace_folders
                        .push(chat::WorkspaceFolderInfo {
                            repository_root: folder_info.repository_root.clone(),
                            folder_root: folder_info.folder_root.clone(),
                        });
                }

                // Set workspace_folders_unchanged flag
                chat_ide_state.workspace_folders_unchanged =
                    ide_state_node.workspace_folders_unchanged;

                if let Some(current_terminal) = &ide_state_node.current_terminal {
                    chat_ide_state.current_terminal = Some(chat::TerminalInfo {
                        terminal_id: current_terminal.terminal_id,
                        current_working_directory: current_terminal
                            .current_working_directory
                            .clone(),
                    });
                }

                chat_ide_state
            }),
            edit_events_node: from.edit_events_node.map(|edit_events_node| {
                chat::ChatRequestEditEvents {
                    edit_events: edit_events_node
                        .edit_events
                        .into_iter()
                        .map(|event| chat::ChatRequestFileEdit {
                            path: event.path,
                            before_blob_name: event.before_blob_name,
                            after_blob_name: event.after_blob_name,
                            edits: event
                                .edits
                                .into_iter()
                                .map(|edit| chat::ChatRequestSingleEdit {
                                    before_line_start: edit.before_line_start,
                                    before_text: edit.before_text,
                                    after_line_start: edit.after_line_start,
                                    after_text: edit.after_text,
                                })
                                .collect(),
                        })
                        .collect(),
                    source: match edit_events_node.source {
                        Some(1) => Some(chat::EditEventSource::UserEdit as i32),
                        Some(2) => Some(chat::EditEventSource::CheckpointRevert as i32),
                        _ => Some(chat::EditEventSource::Unspecified as i32),
                    },
                }
            }),
        }
    }
}

impl From<chat::ChatRequestNode> for public_api_proto::ChatRequestNode {
    fn from(from: chat::ChatRequestNode) -> Self {
        public_api_proto::ChatRequestNode {
            id: from.id,
            r#type: from.r#type,
            text_node: from
                .text_node
                .map(|text_node| public_api_proto::ChatRequestText {
                    content: text_node.content,
                }),
            tool_result_node: from.tool_result_node.map(|tool_result_node| {
                public_api_proto::ChatRequestToolResult {
                    tool_use_id: tool_result_node.tool_use_id,
                    content: tool_result_node.content,
                    is_error: tool_result_node.is_error,
                    request_id: tool_result_node.request_id,
                    content_nodes: tool_result_node
                        .content_nodes
                        .into_iter()
                        .map(|content_node| public_api_proto::ToolResultContentNode {
                            r#type: content_node.r#type,
                            text_content: content_node.text_content,
                            image_content: content_node.image_content.map(|image_content| {
                                public_api_proto::ChatRequestImage {
                                    image_data: image_content.image_data,
                                    format: image_content.format,
                                }
                            }),
                        })
                        .collect(),
                }
            }),
            image_node: from
                .image_node
                .map(|image_node| public_api_proto::ChatRequestImage {
                    image_data: image_node.image_data,
                    format: image_node.format,
                }),
            ide_state_node: from.ide_state_node.map(|ide_state_node| {
                let workspace_folders = ide_state_node
                    .workspace_folders
                    .into_iter()
                    .map(|folder| public_api_proto::WorkspaceFolderInfo {
                        repository_root: folder.repository_root,
                        folder_root: folder.folder_root,
                    })
                    .collect();

                let current_terminal = ide_state_node.current_terminal.map(|terminal| {
                    public_api_proto::TerminalInfo {
                        terminal_id: terminal.terminal_id,
                        current_working_directory: terminal.current_working_directory,
                    }
                });

                public_api_proto::ChatRequestIdeState {
                    workspace_folders,
                    workspace_folders_unchanged: false,
                    current_terminal,
                }
            }),
            edit_events_node: from.edit_events_node.map(|edit_events_node| {
                public_api_proto::ChatRequestEditEvents {
                    edit_events: edit_events_node
                        .edit_events
                        .into_iter()
                        .map(|event| public_api_proto::ChatRequestFileEdit {
                            path: event.path,
                            before_blob_name: event.before_blob_name,
                            after_blob_name: event.after_blob_name,
                            edits: event
                                .edits
                                .into_iter()
                                .map(|edit| public_api_proto::ChatRequestSingleEdit {
                                    before_line_start: edit.before_line_start,
                                    before_text: edit.before_text,
                                    after_line_start: edit.after_line_start,
                                    after_text: edit.after_text,
                                })
                                .collect(),
                        })
                        .collect(),
                    source: match edit_events_node.source {
                        Some(1) => Some(1),
                        Some(2) => Some(2),
                        _ => Some(0),
                    },
                }
            }),
        }
    }
}

impl From<remote_agents::ChatHistoryExchange> for public_api_proto::RemoteAgentExchange {
    fn from(from: remote_agents::ChatHistoryExchange) -> Self {
        public_api_proto::RemoteAgentExchange {
            exchange: from.exchange.map(Into::into),
            changed_files: from.changed_files.into_iter().map(Into::into).collect(),
            sequence_id: from.sequence_id,
            turn_summary: from.turn_summary,
            finished_at: from.finished_at,
            changed_files_skipped: from.changed_files_skipped,
            changed_files_skipped_count: from.changed_files_skipped_count,
        }
    }
}

impl From<public_api_proto::changed_file::FileChangeType>
    for remote_agents::changed_file::FileChangeType
{
    fn from(from: public_api_proto::changed_file::FileChangeType) -> Self {
        match from {
            public_api_proto::changed_file::FileChangeType::Added => Self::Added,
            public_api_proto::changed_file::FileChangeType::Deleted => Self::Deleted,
            public_api_proto::changed_file::FileChangeType::Modified => Self::Modified,
            public_api_proto::changed_file::FileChangeType::Renamed => Self::Renamed,
        }
    }
}

impl TryFrom<public_api_proto::ChangedFile> for remote_agents::ChangedFile {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::ChangedFile) -> Result<Self, Self::Error> {
        let change_type_value = from.change_type;
        let file_change_type =
            match public_api_proto::changed_file::FileChangeType::try_from(change_type_value) {
                Ok(fct) => fct,
                Err(_) => {
                    return Err(tonic::Status::invalid_argument(format!(
                        "Invalid file change type: {}",
                        change_type_value
                    )))
                }
            };
        let change_type = file_change_type.into();
        match change_type {
            remote_agents::changed_file::FileChangeType::Added => Ok(remote_agents::ChangedFile {
                old_path: "".to_string(),
                new_path: from.new_path,
                change_type: change_type.into(),
                old_contents: "".to_string(),
                new_contents: from.new_contents,
            }),
            remote_agents::changed_file::FileChangeType::Deleted => {
                Ok(remote_agents::ChangedFile {
                    old_path: from.old_path,
                    new_path: "".to_string(),
                    change_type: change_type.into(),
                    old_contents: from.old_contents,
                    new_contents: "".to_string(),
                })
            }
            remote_agents::changed_file::FileChangeType::Modified => {
                Ok(remote_agents::ChangedFile {
                    old_path: from.old_path,
                    new_path: from.new_path,
                    change_type: change_type.into(),
                    old_contents: from.old_contents,
                    new_contents: from.new_contents,
                })
            }
            remote_agents::changed_file::FileChangeType::Renamed => {
                Ok(remote_agents::ChangedFile {
                    old_path: from.old_path,
                    new_path: from.new_path,
                    change_type: change_type.into(),
                    old_contents: from.old_contents,
                    new_contents: from.new_contents,
                })
            }
        }
    }
}

impl From<public_api_proto::RemoteAgentExchange> for remote_agents::ChatHistoryExchange {
    fn from(from: public_api_proto::RemoteAgentExchange) -> Self {
        remote_agents::ChatHistoryExchange {
            exchange: from.exchange.map(Into::into),
            changed_files: from
                .changed_files
                .into_iter()
                .filter_map(|file| match file.try_into() {
                    Ok(converted) => Some(converted),
                    Err(err) => {
                        tracing::error!("Failed to convert ChangedFile: {}", err);
                        None
                    }
                })
                .collect(),
            sequence_id: from.sequence_id,
            turn_summary: from.turn_summary,
            finished_at: from.finished_at,
            changed_files_skipped: from.changed_files_skipped,
            changed_files_skipped_count: from.changed_files_skipped_count,
        }
    }
}

impl From<chat::Exchange> for public_api_proto::Exchange {
    fn from(from: chat::Exchange) -> Self {
        public_api_proto::Exchange {
            request_message: from.request_message,
            response_text: from.response_text,
            request_id: from.request_id,
            request_nodes: from
                .request_nodes
                .into_iter()
                .filter(|node| {
                    node.r#type != public_api_proto::ChatRequestNodeType::CheckpointRef as i32
                })
                .map(Into::into)
                .collect(),
            response_nodes: from.response_nodes.into_iter().map(Into::into).collect(),
        }
    }
}

impl From<remote_agents::ChangedFile> for public_api_proto::ChangedFile {
    fn from(from: remote_agents::ChangedFile) -> Self {
        public_api_proto::ChangedFile {
            old_path: from.old_path,
            new_path: from.new_path,
            change_type: from.change_type,
            old_contents: from.old_contents,
            new_contents: from.new_contents,
        }
    }
}

impl From<chat::ChatResultNode> for public_api_proto::ChatResultNode {
    fn from(from: chat::ChatResultNode) -> Self {
        public_api_proto::ChatResultNode {
            id: from.id,
            r#type: from.r#type,
            content: from.content,
            tool_use: from
                .tool_use
                .map(|tool_use| public_api_proto::ChatResultToolUse {
                    tool_use_id: tool_use.tool_use_id,
                    tool_name: tool_use.tool_name,
                    input_json: tool_use.input_json,
                    is_partial: tool_use.is_partial,
                }),
        }
    }
}

impl From<public_api_proto::ChatResultNode> for chat::ChatResultNode {
    fn from(from: public_api_proto::ChatResultNode) -> Self {
        chat::ChatResultNode {
            id: from.id,
            r#type: from.r#type,
            content: from.content,
            tool_use: from.tool_use.map(|tool_use| chat::ChatResultToolUse {
                tool_use_id: tool_use.tool_use_id,
                tool_name: tool_use.tool_name,
                input_json: tool_use.input_json,
                is_partial: tool_use.is_partial,
            }),
        }
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::CreateRemoteAgentRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::CreateRemoteAgentRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::RemoteAgent,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        match check_subscription_status(&feature_flags, &user, &tenant_info) {
            Ok(_) => {}
            Err(status) => return Ok(_status_to_response(status)),
        }

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let timeout_ms =
                CREATE_REMOTE_AGENT_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
            let mut inner_request = self.create_agent_request_from_public_api(request)?;
            // If the client passes a token, then use it. Otherwise, try and use the Authorization
            // bearer token from the request.
            if inner_request
                .config
                .as_ref()
                .and_then(|c| c.workspace_setup.as_ref())
                .and_then(|ws| ws.token.as_ref())
                .map_or(true, |t| t.is_empty())
            {
                match crate::api_auth::extract_token_from_headers(req.headers()) {
                    Ok(token) => {
                        if let Some(config) = inner_request.config.as_mut() {
                            if let Some(workspace_setup) = config.workspace_setup.as_mut() {
                                workspace_setup.token = Some(token);
                            }
                        }
                    }
                    Err(e) => {
                        tracing::warn!("Failed to extract token from headers: {}", e);
                    }
                }
            }
            let inner_result = self
                .remote_agents_client
                .create_agent(
                    &request_context,
                    inner_request,
                    std::time::Duration::from_millis(timeout_ms),
                )
                .await?;
            let result = public_api_proto::CreateRemoteAgentResponse::from(inner_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::ListRemoteAgentsRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::ListRemoteAgentsRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let timeout_ms = LIST_REMOTE_AGENTS_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let inner_request = remote_agents::ListAgentsRequest::try_from(request)?;
        let inner_result = self
            .remote_agents_client
            .list_agents(
                &request_context,
                inner_request,
                std::time::Duration::from_millis(timeout_ms),
            )
            .await?;
        let result = public_api_proto::ListRemoteAgentsResponse::from(inner_result);
        Ok(HttpResponse::Ok().json(result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::ListRemoteAgentsStreamRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::ListRemoteAgentsStreamRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let inner_request = remote_agents::ListAgentsStreamRequest::try_from(request)?;
        let streaming_result = self
            .remote_agents_client
            .list_agents_stream(&request_context, inner_request)
            .await?;

        // Convert the streaming response to an HTTP response
        let (tx, rx) = tokio::sync::mpsc::channel(100);

        // Create a task to forward messages from the streaming result to the receiver
        let stream = streaming_result.into_inner();
        tokio::spawn(async move {
            let mut stream = stream;
            while let Ok(Some(item)) = stream.message().await {
                if tx.send(Ok(item)).await.is_err() {
                    break;
                }
            }
        });

        Ok(streaming_http_response_from_receiver::<
            remote_agents::ListAgentsStreamResponse,
            public_api_proto::ListRemoteAgentsStreamResponse,
        >(
            "remote-agents/list-stream",
            Ok(rx),
            request_context,
            tenant_info,
            Arc::clone(&self.request_insight_publisher),
            &self.feature_flags,
            true, // enable_heartbeat
        )
        .await)
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::RemoteAgentChatRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::RemoteAgentChatRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::RemoteAgent,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        match check_subscription_status(&feature_flags, &user, &tenant_info) {
            Ok(_) => {}
            Err(status) => return Ok(_status_to_response(status)),
        }

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let timeout_ms = CHAT_REMOTE_AGENT_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
            let inner_request = remote_agents::ChatRequest::try_from(request)?;
            let inner_result = self
                .remote_agents_client
                .chat(
                    &request_context,
                    inner_request,
                    std::time::Duration::from_millis(timeout_ms),
                )
                .await?;
            let result = public_api_proto::RemoteAgentChatResponse::from(inner_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::GetRemoteAgentChatHistoryRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::GetRemoteAgentChatHistoryRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let timeout_ms =
            CHAT_HISTORY_REMOTE_AGENT_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let inner_request = remote_agents::ChatHistoryRequest::try_from(request)?;
        let inner_result = self
            .remote_agents_client
            .chat_history(
                &request_context,
                inner_request,
                std::time::Duration::from_millis(timeout_ms),
            )
            .await?;
        let result = public_api_proto::GetRemoteAgentChatHistoryResponse::from(inner_result);
        Ok(HttpResponse::Ok().json(result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::GetRemoteAgentHistoryStreamRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::GetRemoteAgentHistoryStreamRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let inner_request = remote_agents::AgentHistoryStreamRequest::try_from(request)?;
        let streaming_result = self
            .remote_agents_client
            .agent_history_stream(&request_context, inner_request)
            .await?;

        // Convert the streaming response to an HTTP response
        let (tx, rx) = tokio::sync::mpsc::channel(100);

        // Create a task to forward messages from the streaming result to the receiver
        let stream = streaming_result.into_inner();
        tokio::spawn(async move {
            let mut stream = stream;
            while let Ok(Some(item)) = stream.message().await {
                if tx.send(Ok(item)).await.is_err() {
                    break;
                }
            }
        });

        Ok(streaming_http_response_from_receiver::<
            remote_agents::AgentHistoryStreamResponse,
            public_api_proto::GetRemoteAgentHistoryStreamResponse,
        >(
            "remote-agents/agent-history-stream",
            Ok(rx),
            request_context,
            tenant_info,
            Arc::clone(&self.request_insight_publisher),
            &self.feature_flags,
            false, // Disable heartbeat for remote agents history stream
        )
        .await)
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::InterruptRemoteAgentRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::InterruptRemoteAgentRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::RemoteAgent,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let timeout_ms = STOP_REMOTE_AGENT_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64; // Using same timeout flag
            let inner_request = remote_agents::InterruptAgentRequest::try_from(request)?;
            let inner_result = self
                .remote_agents_client
                .interrupt_agent(
                    &request_context,
                    inner_request,
                    std::time::Duration::from_millis(timeout_ms),
                )
                .await?;
            let result = public_api_proto::InterruptRemoteAgentResponse::from(inner_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::DeleteRemoteAgentRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::DeleteRemoteAgentRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::RemoteAgent,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let timeout_ms =
                DELETE_REMOTE_AGENT_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
            let inner_request = remote_agents::DeleteAgentRequest::try_from(request)?;
            let inner_result = self
                .remote_agents_client
                .delete_agent(
                    &request_context,
                    inner_request,
                    std::time::Duration::from_millis(timeout_ms),
                )
                .await?;
            let result = public_api_proto::DeleteRemoteAgentResponse::from(inner_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::RemoteAgentAddSshKeyRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::RemoteAgentAddSshKeyRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::RemoteAgent,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let timeout_ms =
                REMOTE_AGENT_ADD_SSH_KEY_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
            let inner_request = remote_agents::AddSshKeyRequest::try_from(request)?;
            let inner_result = self
                .remote_agents_client
                .add_ssh_key(
                    &request_context,
                    inner_request,
                    std::time::Duration::from_millis(timeout_ms),
                )
                .await?;
            let result = public_api_proto::RemoteAgentAddSshKeyResponse::from(inner_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::RemoteAgentResumeRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::RemoteAgentResumeRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::RemoteAgent,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let timeout_ms =
                REMOTE_AGENT_RESUME_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
            let inner_request = remote_agents::ResumeAgentRequest::try_from(request)?;
            let inner_result = self
                .remote_agents_client
                .resume_agent(
                    &request_context,
                    inner_request,
                    std::time::Duration::from_millis(timeout_ms),
                )
                .await?;
            let result = public_api_proto::RemoteAgentResumeResponse::from(inner_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::RemoteAgentPauseRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::RemoteAgentPauseRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::RemoteAgent,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let timeout_ms =
                REMOTE_AGENT_PAUSE_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
            let inner_request = remote_agents::PauseAgentRequest::try_from(request)?;
            let inner_result = self
                .remote_agents_client
                .pause_agent(
                    &request_context,
                    inner_request,
                    std::time::Duration::from_millis(timeout_ms),
                )
                .await?;
            let result = public_api_proto::RemoteAgentPauseResponse::from(inner_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::AgentWorkspaceReportStatusRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::AgentWorkspaceReportStatusRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let timeout_ms =
            WORKSPACE_REPORT_STATUS_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let inner_request = remote_agents::WorkspaceReportStatusRequest::try_from(request)?;
        let inner_result = self
            .remote_agents_client
            .workspace_report_status(
                &request_context,
                inner_request,
                std::time::Duration::from_millis(timeout_ms),
            )
            .await?;
        let result = public_api_proto::AgentWorkspaceReportStatusResponse::from(inner_result);
        Ok(HttpResponse::Ok().json(result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::AgentWorkspaceReportChatHistoryRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::AgentWorkspaceReportChatHistoryRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let timeout_ms =
            WORKSPACE_REPORT_STATUS_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        // Convert from public API to internal API
        let inner_request = remote_agents::WorkspaceReportChatHistoryRequest {
            remote_agent_id: request.remote_agent_id,
            chat_history: request
                .chat_history
                .into_iter()
                .map(remote_agents::ChatHistoryExchange::from)
                .collect(),
        };
        let _inner_result = self
            .remote_agents_client
            .workspace_report_chat_history(
                &request_context,
                inner_request,
                std::time::Duration::from_millis(timeout_ms),
            )
            .await?;
        let result = public_api_proto::AgentWorkspaceReportChatHistoryResponse {};
        Ok(HttpResponse::Ok().json(result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::AgentWorkspaceReportSetupLogsRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::AgentWorkspaceReportSetupLogsRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::RemoteAgent,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let timeout_ms =
                WORKSPACE_REPORT_STATUS_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
            let inner_request = remote_agents::WorkspaceReportSetupLogsRequest::try_from(request)?;
            let inner_result = self
                .remote_agents_client
                .workspace_report_setup_logs(
                    &request_context,
                    inner_request,
                    std::time::Duration::from_millis(timeout_ms),
                )
                .await?;
            let result =
                public_api_proto::AgentWorkspaceReportSetupLogsResponse::from(inner_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::AgentWorkspacePollUpdateRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::AgentWorkspacePollUpdateRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let timeout_ms = WORKSPACE_POLL_UPDATE_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let inner_request = remote_agents::WorkspacePollUpdateRequest::try_from(request)?;
        let inner_result = self
            .remote_agents_client
            .workspace_poll_update(
                &request_context,
                inner_request,
                std::time::Duration::from_millis(timeout_ms),
            )
            .await?;
        let result = public_api_proto::AgentWorkspacePollUpdateResponse::from(inner_result);
        Ok(HttpResponse::Ok().json(result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::AgentWorkspaceStreamRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::AgentWorkspaceStreamRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let inner_request = remote_agents::WorkspaceStreamRequest::try_from(request)?;
        let streaming_result = match self
            .remote_agents_client
            .workspace_stream(&request_context, inner_request)
            .await
        {
            Ok(result) => result,
            Err(err) => {
                tracing::warn!("Failed to start workspace stream: {}", err);
                return Err(err);
            }
        };

        // Convert the streaming response to an HTTP response
        let (tx, rx) = tokio::sync::mpsc::channel(100);

        // Create a task to forward messages from the streaming result to the receiver
        let stream = streaming_result.into_inner();
        tokio::spawn(async move {
            let mut stream = stream;
            while let Ok(Some(item)) = stream.message().await {
                if tx.send(Ok(item)).await.is_err() {
                    break;
                }
            }
        });

        Ok(streaming_http_response_from_receiver::<
            remote_agents::WorkspaceStreamResponse,
            public_api_proto::AgentWorkspaceStreamResponse,
        >(
            "remote-agents/workspace-stream",
            Ok(rx),
            request_context,
            tenant_info,
            Arc::clone(&self.request_insight_publisher),
            &self.feature_flags,
            false, // Disable heartbeat for remote agents workspace stream
        )
        .await)
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::RemoteAgentWorkspaceLogsRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::RemoteAgentWorkspaceLogsRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let timeout_ms = WORKSPACE_LOGS_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let inner_request = remote_agents::WorkspaceLogsRequest::try_from(request)?;
        let inner_result = self
            .remote_agents_client
            .workspace_logs(
                &request_context,
                inner_request,
                std::time::Duration::from_millis(timeout_ms),
            )
            .await?;
        let result = public_api_proto::RemoteAgentWorkspaceLogsResponse::from(inner_result);
        Ok(HttpResponse::Ok().json(result))
    }
}

pub fn register_handler_flags(registry: &feature_flags::RegistryHandle) {
    VSCODE_REMOTE_AGENT_MODE_FLAG
        .register(registry)
        .expect("Registering VSCODE_REMOTE_AGENT_MODE_FLAG");
    INTELLIJ_REMOTE_AGENT_MODE_FLAG
        .register(registry)
        .expect("Registering INTELLIJ_REMOTE_AGENT_MODE_FLAG");
    ENABLE_REMOTE_AGENTS
        .register(registry)
        .expect("Registering ENABLE_REMOTE_AGENTS");
    CB_REMOTE_AGENTS
        .register(registry)
        .expect("Registering CB_REMOTE_AGENTS");

    CREATE_REMOTE_AGENT_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering CREATE_REMOTE_AGENT_TIMEOUT_MS_FLAG");
    CHAT_REMOTE_AGENT_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering CHAT_REMOTE_AGENT_TIMEOUT_MS_FLAG");
    CHAT_HISTORY_REMOTE_AGENT_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering CHAT_HISTORY_REMOTE_AGENT_TIMEOUT_MS_FLAG");
    LIST_REMOTE_AGENTS_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering LIST_REMOTE_AGENTS_TIMEOUT_MS_FLAG");
    STOP_REMOTE_AGENT_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering STOP_REMOTE_AGENT_TIMEOUT_MS_FLAG");
    DELETE_REMOTE_AGENT_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering DELETE_REMOTE_AGENT_TIMEOUT_MS_FLAG");
    REMOTE_AGENT_ADD_SSH_KEY_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering REMOTE_AGENT_ADD_SSH_KEY_TIMEOUT_MS_FLAG");
    REMOTE_AGENT_RESUME_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering REMOTE_AGENT_RESUME_TIMEOUT_MS_FLAG");
    REMOTE_AGENT_PAUSE_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering REMOTE_AGENT_PAUSE_TIMEOUT_MS_FLAG");
    WORKSPACE_REPORT_STATUS_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering WORKSPACE_REPORT_STATUS_TIMEOUT_MS_FLAG");
    WORKSPACE_POLL_UPDATE_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering WORKSPACE_POLL_UPDATE_TIMEOUT_MS_FLAG");
    WORKSPACE_LOGS_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering WORKSPACE_LOGS_TIMEOUT_MS_FLAG");
}

fn check_subscription_status(
    feature_flags: &feature_flags::FeatureFlagsServiceHandle,
    user: &User,
    _tenant_info: &TenantInfo,
) -> Result<(), Status> {
    tracing::info!(
        "In the check_subscription_status function for user {}",
        user.user_id
    );

    let account_info = if let Some(email) = user.user_email.as_ref().filter(|e| !e.is_empty()) {
        format!(" for account {}", email)
    } else {
        "".to_string()
    };

    // Out of usage credits
    if OUT_OF_USAGE_CREDITS_DISCLAIMER.get_from(feature_flags) {
        if let Some(get_token_info_response::Subscription::ActiveSubscription(active)) =
            &user.subscription
        {
            if active.usage_balance_depleted {
                tracing::info!("User {} is out of credits", user.user_id);

                // Return a 402 Payment Required error
                return Err(tonic::Status::new(
                    tonic::Code::PermissionDenied,
                    OUT_OF_USAGE_CREDITS_DISCLAIMER_MKDOWN_MESSAGE
                        .replace("{account_info}", &account_info),
                ));
            }
        }
    }

    Ok(())
}

fn _status_to_response(status: Status) -> HttpResponse {
    match status.code() {
        tonic::Code::PermissionDenied => {
            HttpResponse::PaymentRequired().json(ResponseError::new(status.message(), None))
        }
        _ => status_to_response(&status),
    }
}
#[cfg(test)]
mod tests {
    #[test]
    fn test_deserialize_remote_agent_chat_request_details_with_null_mcp_servers() {
        // Test that `null` mcp_servers field is deserialized as empty vector
        let json_data = r#"{
            "request_nodes": [
                {
                    "id": 1,
                    "type": 0,
                    "text_node": {
                        "content": "Hello world"
                    }
                }
            ],
            "mcp_servers": null,
            "user_guidelines": "Test guidelines",
            "model_id": "test-model"
        }"#;
        let request_details: crate::public_api_proto::RemoteAgentChatRequestDetails =
            serde_json::from_str(json_data).unwrap();

        assert_eq!(request_details.request_nodes.len(), 1);
        assert_eq!(request_details.mcp_servers.len(), 0); // Should be empty vec, not null
        assert_eq!(
            request_details.user_guidelines,
            Some("Test guidelines".to_string())
        );
        assert_eq!(request_details.model_id, Some("test-model".to_string()));
    }

    #[test]
    fn test_deserialize_remote_agent_chat_request_details_with_populated_mcp_servers() {
        // Test that populated mcp_servers array works correctly
        let json_data = r#"{
            "request_nodes": [],
            "mcp_servers": [
                {
                    "command": "test-command",
                    "args": ["arg1", "arg2"],
                    "name": "test-server"
                }
            ],
            "user_guidelines": "Test guidelines"
        }"#;
        let request_details: crate::public_api_proto::RemoteAgentChatRequestDetails =
            serde_json::from_str(json_data).unwrap();

        assert_eq!(request_details.request_nodes.len(), 0);
        assert_eq!(request_details.mcp_servers.len(), 1);
        assert_eq!(request_details.mcp_servers[0].command, "test-command");
        assert_eq!(request_details.mcp_servers[0].args, vec!["arg1", "arg2"]);
        assert_eq!(
            request_details.mcp_servers[0].name,
            Some("test-server".to_string())
        );
        assert_eq!(
            request_details.user_guidelines,
            Some("Test guidelines".to_string())
        );
    }
}
