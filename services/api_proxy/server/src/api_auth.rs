use crate::config::AuthQueryConfig;
use crate::metrics::SUBSCRIPTION_BLOCK_COUNTER;
use actix_web::http::header;
use actix_web::http::header::HeaderMap;
use feature_flags::FeatureFlagsServiceHandle;
use request_context::TenantInfo;
use secrecy::SecretString;
use std::str::FromStr;
use std::sync::Arc;
use std::time::Duration;
use tonic::transport::ClientTlsConfig;

use auth_entities_proto::auth_entities::{user_id::UserIdType, UserId};
use auth_query_client::auth_query::get_token_info_response;
use auth_query_client::{auth_query::GetTokenInfoRequest, AuthQueryClient, AuthQueryClientImpl};

#[cfg(test)]
use auth_query_client::MockAuthQueryClient;
#[cfg(test)]
use std::collections::HashMap;

pub const ENABLE_IP_ALLOWLISTS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("enable_ip_allowlists", false);

pub const CHECK_SUBSCRIPTION_STATUS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("check_subscription_status", false);

pub const USER_SUSPENSION_ENABLED: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_user_suspension_enabled", false);

pub const ALLOW_WITH_INJECTED_MESSAGE_PATHS: &[&str] = &[
    "/chat-stream",
    "/get-models",
    "/subscription-info",
    "/remote-agents/list",
    "/remote-agents/logs",
    "/remote-agents/agent-history-stream",
];

/// structure implementing the api token based authentication
pub struct ApiAuth {
    namespace: String,
    cloud: String,
    /// list of valid authentication tokens
    client: Box<dyn AuthQueryClient + Send + Sync>,
    feature_flags: feature_flags::FeatureFlagsServiceHandle,
    tenant_cache: Arc<dyn tenant_watcher_client::TenantCacheClient + Send + Sync>,
}

#[derive(Clone, Debug)]
pub struct User {
    pub user_id: String,
    pub opaque_user_id: UserId,
    pub user_email: Option<String>,
    pub subscription: Option<get_token_info_response::Subscription>,
    pub suspensions: Vec<auth_entities_proto::auth_entities::UserSuspension>,
}

impl User {
    /// Checks if the user is on a trial or has a subscription expiring soon
    pub fn is_subscription_expiring_soon(&self, days_threshold: i64) -> Option<i64> {
        match &self.subscription {
            // Check trial subscription
            Some(get_token_info_response::Subscription::Trial(trial)) => {
                if let Some(trial_end) = &trial.trial_end {
                    let now = chrono::Utc::now();
                    let end_datetime = chrono::DateTime::<chrono::Utc>::from_timestamp(
                        trial_end.seconds,
                        trial_end.nanos as u32,
                    )
                    .unwrap_or(now);

                    let duration = end_datetime.signed_duration_since(now);
                    let days_remaining = duration.num_days();

                    if days_remaining >= 0 && days_remaining < days_threshold {
                        return Some(days_remaining);
                    }
                }
            }
            // Check active subscription with end date
            Some(get_token_info_response::Subscription::ActiveSubscription(active)) => {
                if let Some(end_date) = &active.end_date {
                    let now = chrono::Utc::now();
                    let end_datetime = chrono::DateTime::<chrono::Utc>::from_timestamp(
                        end_date.seconds,
                        end_date.nanos as u32,
                    )
                    .unwrap_or(now);

                    let duration = end_datetime.signed_duration_since(now);
                    let days_remaining = duration.num_days();

                    if days_remaining >= 0 && days_remaining < days_threshold {
                        return Some(days_remaining);
                    }
                }
            }
            _ => {}
        }
        None
    }
}

const BEARER: &str = "Bearer ";

/// Extract token from Authorization header
pub fn extract_token_from_headers(headers: &HeaderMap) -> Result<String, actix_web::Error> {
    let auth_header = match headers.get(header::AUTHORIZATION) {
        None => {
            return Err(actix_web::error::ErrorUnauthorized(
                "No Authorization header",
            ))
        }
        Some(authorization) => authorization
            .to_str()
            .map_err(|_| actix_web::error::ErrorBadRequest("Non-ASCII Authorization header")),
    }?;

    if !auth_header.starts_with(BEARER) {
        return Err(actix_web::error::ErrorBadRequest("Not a Bearer"));
    }

    Ok(auth_header[BEARER.len()..].to_string())
}

impl ApiAuth {
    fn is_path_allowing_injected_messages(path: &str) -> bool {
        ALLOW_WITH_INJECTED_MESSAGE_PATHS
            .iter()
            .any(|allowed_path| path.contains(allowed_path))
    }

    #[cfg(test)]
    pub fn new_for_test(hardcoded_tokens: HashMap<String, String>) -> Self {
        ApiAuth {
            namespace: "dev-augie".to_string(),
            cloud: "dev-cloud".to_string(),
            client: Box::new(MockAuthQueryClient::new(hardcoded_tokens)),
            feature_flags: feature_flags::setup_local(),
            tenant_cache: Arc::new(tenant_watcher_client::NullTenantCache::new(
                tenant_watcher_client::tenant_watcher::Tenant::default(),
            )),
        }
    }

    #[cfg(test)]
    pub fn new_with_mock_client(
        client: MockAuthQueryClient,
        feature_flags: Option<feature_flags::FeatureFlagsServiceHandle>,
    ) -> Self {
        ApiAuth {
            namespace: "dev-augie".to_string(),
            cloud: "dev-cloud".to_string(),
            client: Box::new(client),
            feature_flags: feature_flags.unwrap_or_else(feature_flags::setup_local),
            tenant_cache: Arc::new(tenant_watcher_client::NullTenantCache::new(
                tenant_watcher_client::tenant_watcher::Tenant::default(),
            )),
        }
    }

    pub fn new(
        namespace: String,
        cloud: String,
        auth_query_config: AuthQueryConfig,
        client_tls_config: Option<ClientTlsConfig>,
        feature_flags: feature_flags::FeatureFlagsServiceHandle,
        tenant_cache: Arc<dyn tenant_watcher_client::TenantCacheClient + Send + Sync>,
    ) -> Self {
        let endpoint = auth_query_config.endpoint.clone();
        let request_timeout = Duration::from_secs_f32(auth_query_config.request_timeout_secs);
        ApiAuth {
            namespace,
            cloud,
            client: Box::new(AuthQueryClientImpl::new(
                endpoint.as_str(),
                client_tls_config,
                request_timeout,
            )),
            feature_flags,
            tenant_cache,
        }
    }

    /// checks the header if it contains a valid authentication token
    /// Returns a user ID and a signed token
    /// TODO(aswin): once we always have a token, drop the User and read the
    /// user_id from the token instead?
    pub async fn check(
        &self,
        headers: &HeaderMap,
        path: &str,
        request_context: &request_context::RequestContext,
        client_addr_str: String,
    ) -> Result<(User, TenantInfo, SecretString), actix_web::Error> {
        let token = extract_token_from_headers(headers)?;

        let request = GetTokenInfoRequest {
            token: token.to_string(),
            requestor: "api_proxy".to_string(),
        };

        match self.client.get_token_info(request, request_context).await {
            Err(e) => match e.code() {
                tonic::Code::NotFound => {}
                tonic::Code::PermissionDenied => {
                    // Return 403 Forbidden for login failures
                    // Avoiding 401 because it could sometimes falsely trigger IDE sign-in
                    tracing::warn!("Permission Denied: {}", e);
                    return Err(actix_web::error::ErrorForbidden("Permission Denied"));
                }
                _ => {
                    tracing::error!("Failed to get token info: {}", e);
                    return Err(actix_web::error::ErrorInternalServerError(
                        "Failed to get token info",
                    ));
                }
            },
            Ok(response) => {
                let signed_token: SecretString = response.signed_token.into();
                let opaque_user_id = response.opaque_user_id.unwrap_or_else(|| {
                    tracing::warn!("No opaque_user_id returned from auth query");
                    UserId {
                        user_id_type: UserIdType::Unknown.into(),
                        user_id: "".to_string(),
                    }
                });

                let user = User {
                    user_id: response.user_id.to_string(),
                    opaque_user_id: opaque_user_id.clone(),
                    user_email: response.user_email.clone(),
                    subscription: response.subscription,
                    suspensions: response.suspensions,
                };

                let tenant_info = TenantInfo {
                    tenant_id: Some(response.tenant_id.into()),
                    tenant_name: response.tenant_name,
                    shard_namespace: self.namespace.clone(),
                    cloud: self.cloud.clone(),
                    scopes: vec![],
                    user_id: Some(response.user_id.into()),
                    opaque_user_id: Some(opaque_user_id),
                    user_email: response.user_email.map(SecretString::new),
                    service_name: None,
                };

                self.check_ip_allowlist(&client_addr_str, &user, &tenant_info)
                    .await?;
                self.check_subscription_status(path, &user, &tenant_info)
                    .await?;
                self.check_suspensions(path, &user, &tenant_info).await?;
                return Ok((user, tenant_info, signed_token));
            }
        }

        Err(actix_web::error::ErrorUnauthorized("Invalid token"))
    }

    async fn check_ip_allowlist(
        &self,
        client_addr_str: &str,
        user: &User,
        tenant_info: &TenantInfo,
    ) -> Result<(), actix_web::Error> {
        let bound_flags = self.get_feature_flags(user, tenant_info)?;

        // Skip check if feature flag is disabled
        if !ENABLE_IP_ALLOWLISTS.get_from(&bound_flags) {
            return Ok(());
        }

        // Common errors (fail closed and check failed).
        let fail_closed_err = Err(actix_web::error::ErrorInternalServerError(
            "Error evaluating network policy.",
        ));
        let fail_policy_err = Err(actix_web::error::ErrorUnauthorized(
            "Denied by network policy.",
        ));

        // Get tenant config (from CRD), and ip_allowlist flag from there. Fail closed.
        let tenant_id_str = match &tenant_info.tenant_id {
            Some(id) => id.to_string(),
            None => {
                tracing::error!("Missing tenant ID in tenant_info");
                return fail_closed_err;
            }
        };

        let tenant_config = match self.tenant_cache.get_tenant(&tenant_id_str).await {
            Some(config) => config,
            None => {
                tracing::error!(
                    "Failed to get tenant config for tenant: {:?}",
                    tenant_info.tenant_id
                );
                return fail_closed_err;
            }
        };
        let ip_allowlist = tenant_config
            .config
            .as_ref()
            .and_then(|c| c.configs.get("ip_allowlist"))
            .map(|s| s.as_str())
            .unwrap_or("");

        // Parse client addr from connection info. Fail closed.
        let client_addr = match std::net::IpAddr::from_str(client_addr_str) {
            Ok(addr) => addr,
            Err(e) => {
                tracing::error!(
                    "Invalid client IP address: {}, error: {}.",
                    client_addr_str,
                    e
                );
                return fail_closed_err;
            }
        };

        // No allowlist means allow all.
        if ip_allowlist.is_empty() {
            return Ok(());
        }

        // Check against each subnet in the allowlist. Ignore any parsing errors and fail through
        // to denial.
        for range in ip_allowlist.split(',') {
            let range = range.trim();
            match ipnet::IpNet::from_str(range) {
                Ok(network) => {
                    if network.contains(&client_addr) {
                        return Ok(());
                    }
                }
                Err(e) => {
                    // NOTE(mattm): We could return a `fail_closed_err` below if we've encountered
                    // at least one parse error.
                    tracing::error!(
                        "Invalid IP range in allowlist for tenant {:?}: {}, error: {}.",
                        tenant_info.tenant_id,
                        range,
                        e
                    );
                }
            }
        }

        // Deny by policy.
        fail_policy_err
    }
    async fn check_suspensions(
        &self,
        path: &str,
        user: &User,
        tenant_info: &TenantInfo,
    ) -> Result<(), actix_web::Error> {
        // Skip check if feature flag is disabled

        let bound_flags = self.get_feature_flags(user, tenant_info)?;

        // Skip check if feature flag is disabled
        if !USER_SUSPENSION_ENABLED.get_from(&bound_flags) {
            return Ok(());
        }

        // For chat requests, we don't want to return a 403 error by default, we will inject message based on the suspensions later
        // This is a short term, backwards compatibility measure before we make client-side changes to handle the 403 error
        if ApiAuth::is_path_allowing_injected_messages(path) {
            return Ok(());
        }

        if !user.suspensions.is_empty() {
            for suspension in &user.suspensions {
                let suspension_type =
                    match auth_entities_proto::auth_entities::UserSuspensionType::try_from(
                        suspension.suspension_type,
                    ) {
                        Ok(st) => st,
                        Err(_) => {
                            tracing::error!(
                                "Invalid suspension type: {}",
                                suspension.suspension_type
                            );
                            return Err(actix_web::error::ErrorInternalServerError(
                                "Error processing account suspension",
                            ));
                        }
                    };

                tracing::info!("User suspended: {:?}", suspension_type);
            }
            return Err(actix_web::error::ErrorForbidden(
                "Account suspended. Please contact support.",
            ));
        }

        Ok(())
    }

    async fn check_subscription_status(
        &self,
        path: &str,
        user: &User,
        tenant_info: &TenantInfo,
    ) -> Result<(), actix_web::Error> {
        // Use the new standalone function
        let bound_flags = self.get_feature_flags(user, tenant_info)?;

        // Skip check if feature flag is disabled
        if !CHECK_SUBSCRIPTION_STATUS.get_from(&bound_flags) {
            return Ok(());
        }

        // For chat requests, we don't want to return a 402 error by default, we will inject message based on the status later
        // This is a short term, backwards compatibility measure before we make client-side changes to handle the 402 error
        // We also want to allow /get-models so users can still sign-in to and view extension / chat panel
        if ApiAuth::is_path_allowing_injected_messages(path) {
            return Ok(());
        }

        // Check subscription status using oneof variants
        match &user.subscription {
            // No subscription information available, allow
            None => Ok(()),
            // Check subscription type
            Some(subscription) => match subscription {
                // Allow enterprise, active subscription, and trial
                get_token_info_response::Subscription::Enterprise(_) => Ok(()),
                get_token_info_response::Subscription::ActiveSubscription(_) => Ok(()),
                get_token_info_response::Subscription::Trial(_) => Ok(()),
                // Block inactive subscription
                get_token_info_response::Subscription::InactiveSubscription(_) => {
                    // Record the blocking event for negative alert monitoring
                    SUBSCRIPTION_BLOCK_COUNTER
                        .with_label_values(&[tenant_info.metrics_tenant_name()])
                        .inc();

                    Err(actix_web::error::ErrorPaymentRequired(
                        "Subscription inactive or expired. Please visit https://app.augmentcode.com/account to renew.",
                    ))
                }
            },
        }
    }
    pub fn get_feature_flags(
        &self,
        user: &User,
        tenant_info: &TenantInfo,
    ) -> Result<FeatureFlagsServiceHandle, actix_web::Error> {
        let mut bound_flags = self
            .feature_flags
            .bind_attribute("tenant_name", &tenant_info.tenant_name)
            .map_err(|e| {
                tracing::error!("get_feature_flags failed: {:?}", e);
                actix_web::error::ErrorInternalServerError("Failed to bind feature flags to tenant")
            })?;

        if user.opaque_user_id.user_id_type == UserIdType::Augment as i32 {
            bound_flags = bound_flags
                .bind_attribute("user_uuid", &user.opaque_user_id.user_id)
                .map_err(|e| {
                    tracing::error!("get_feature_flags failed: {:?}", e);
                    actix_web::error::ErrorInternalServerError(
                        "Failed to bind feature flags to user uuid",
                    )
                })?;
        }

        if user.opaque_user_id.user_id_type == UserIdType::ApiToken as i32 {
            bound_flags = bound_flags
                .bind_attribute("api_key_user_id", &user.opaque_user_id.user_id)
                .map_err(|e| {
                    tracing::error!("get_feature_flags failed: {:?}", e);
                    actix_web::error::ErrorInternalServerError(
                        "Failed to bind feature flags to user uuid",
                    )
                })?;
        }
        Ok(bound_flags)
    }
}

pub fn register_flags(registry: &feature_flags::RegistryHandle) {
    ENABLE_IP_ALLOWLISTS
        .register(registry)
        .expect("Registering ENABLE_IP_ALLOWLISTS");
    CHECK_SUBSCRIPTION_STATUS
        .register(registry)
        .expect("Registering CHECK_SUBSCRIPTION_STATUS");
}
