use std::{collections::HashMap, sync::Arc, time::Duration, time::Instant};

use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::create_channel;
use grpc_stream_mux::StreamMuxClient;
use moka::future::Cache;
use request_context::{RequestContext, RequestSessionId};
use secrecy::ExposeSecret;
use tokio::sync::mpsc::{self, Receiver};
use tokio_stream::wrappers::ReceiverStream;
use tonic::transport::ClientTlsConfig;
use tonic::IntoStreamingRequest;
use tracing::Instrument;
use tracing_opentelemetry::OpenTelemetrySpanExt;

use crate::augment::model_instance_config::ModelType;
use crate::metrics::STICKY_SESSION_COUNT_GAUGE;
use crate::{chat, completion, edit, next_edit, stream_mux};

use tracing_tonic::client::TracingService;

// Feature flags for request timeouts. Every endpoint (even endpoints on the same server) should get
// its own flag.
pub const COMPLETION_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("completion_timeout_ms", 5 * 1000);
pub const EDIT_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("edit_timeout_ms", 120 * 1000);
pub const CHAT_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("chat_timeout_ms", 120 * 1000);
pub const NEXT_EDIT_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("next_edit_timeout_ms", 120 * 1000);

pub fn register_flags(registry: &feature_flags::RegistryHandle) {
    COMPLETION_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering COMPLETION_TIMEOUT_MS_FLAG");
    EDIT_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering EDIT_TIMEOUT_MS_FLAG");
    CHAT_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering CHAT_TIMEOUT_MS_FLAG");
    NEXT_EDIT_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering NEXT_EDIT_TIMEOUT_MS_FLAG");
}

impl grpc_stream_mux::SupportedRequest for completion::SessionRequest {
    fn context(&self) -> grpc_stream_mux::stream_mux::MuxedRequest {
        self.context.clone().unwrap_or_default()
    }
    fn set_routing_id(&mut self, routing_id: i64) {
        if let Some(ctx) = self.context.as_mut() {
            ctx.routing_id = routing_id
        }
    }
}

impl grpc_stream_mux::SupportedResponse for completion::SessionResponse {
    fn context(&self) -> grpc_stream_mux::stream_mux::MuxedResponse {
        self.context.clone().unwrap_or_default()
    }
}

impl grpc_stream_mux::SupportedRequest for next_edit::SessionRequest {
    fn context(&self) -> grpc_stream_mux::stream_mux::MuxedRequest {
        self.context.clone().unwrap_or_default()
    }
    fn set_routing_id(&mut self, routing_id: i64) {
        if let Some(ctx) = self.context.as_mut() {
            ctx.routing_id = routing_id
        }
    }
}

impl grpc_stream_mux::SupportedResponse for next_edit::SessionResponse {
    fn context(&self) -> grpc_stream_mux::stream_mux::MuxedResponse {
        self.context.clone().unwrap_or_default()
    }
}

#[derive(Clone)]
pub enum Client {
    Completion(Arc<dyn CompletionClient + Send + Sync>),
    Edit(Arc<dyn EditClient + Send + Sync>),
    Chat(Arc<dyn ChatClient + Send + Sync>),
    NextEdit(Arc<dyn NextEditClient + Send + Sync>),
}

#[async_trait]
pub trait ClientFactory {
    fn create(
        &self,
        model_name: &str,
        model_type: ModelType,
        endpoint: &str,
    ) -> Result<Client, tonic::Status>;
}

pub struct ClientImplFactory {
    tls_config: Option<ClientTlsConfig>,
    feature_flags: feature_flags::FeatureFlagsServiceHandle,
}

impl ClientImplFactory {
    pub fn new(
        tls_config: Option<ClientTlsConfig>,
        feature_flags: feature_flags::FeatureFlagsServiceHandle,
    ) -> Self {
        Self {
            tls_config,
            feature_flags,
        }
    }
}

#[async_trait]
pub trait CompletionClient {
    async fn complete(
        &self,
        request_context: &RequestContext,
        request: completion::CompletionRequest,
        use_session_completion: bool,
    ) -> Result<completion::CompletionResponse, tonic::Status>;

    async fn find_missing(
        &self,
        request_context: &RequestContext,
        request: completion::FindMissingRequest,
    ) -> Result<completion::FindMissingResponse, tonic::Status>;
}

#[async_trait]
pub trait EditClient {
    async fn edit(
        &self,
        request_context: &RequestContext,
        request: edit::EditRequest,
    ) -> Result<edit::EditResponse, tonic::Status>;
    async fn instruction_stream(
        &self,
        request_context: &RequestContext,
        request: edit::InstructionRequest,
    ) -> tonic::Result<Receiver<tonic::Result<edit::InstructionResponse>>>;
}

#[async_trait]
pub trait ChatClient {
    async fn chat(
        &self,
        request_context: &RequestContext,
        request: chat::ChatRequest,
    ) -> Result<chat::ChatResponse, tonic::Status>;
    async fn chat_stream(
        &self,
        request_context: &RequestContext,
        request: chat::ChatRequest,
    ) -> tonic::Result<Receiver<tonic::Result<chat::ChatResponse>>>;
    async fn find_missing(
        &self,
        request_context: &RequestContext,
        request: chat::FindMissingRequest,
    ) -> Result<chat::FindMissingResponse, tonic::Status>;
}

#[async_trait]
pub trait NextEditClient {
    async fn next_edit_stream(
        &self,
        request_context: &RequestContext,
        request: next_edit::NextEditRequest,
        use_stream_mux: bool,
    ) -> tonic::Result<Receiver<tonic::Result<next_edit::NextEditResponse>>>;
    async fn find_missing(
        &self,
        request_context: &RequestContext,
        request: next_edit::FindMissingRequest,
    ) -> Result<next_edit::FindMissingResponse, tonic::Status>;
}

type CompletionStreamMuxClient =
    StreamMuxClient<completion::SessionRequest, completion::SessionResponse>;

type NextEditStreamMuxClient =
    StreamMuxClient<next_edit::SessionRequest, next_edit::SessionResponse>;

#[derive(Clone)]
pub struct CompletionClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    feature_flags: feature_flags::FeatureFlagsServiceHandle,
    client: Arc<Mutex<Option<completion::completion_client::CompletionClient<TracingService>>>>,
}

#[derive(Clone)]
pub struct CompletionClientStreamingImpl {
    inner: Arc<CompletionClientImpl>,
    stream_mux_clients: Cache<RequestSessionId, CompletionStreamMuxClient>,
}

#[derive(Clone)]
pub struct EditClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    feature_flags: feature_flags::FeatureFlagsServiceHandle,
    client: Arc<Mutex<Option<edit::edit_client::EditClient<TracingService>>>>,
}

#[derive(Clone)]
pub struct ChatClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    feature_flags: feature_flags::FeatureFlagsServiceHandle,
    client: Arc<Mutex<Option<chat::chat_client::ChatClient<TracingService>>>>,
}

#[derive(Clone)]
pub struct NextEditClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    feature_flags: feature_flags::FeatureFlagsServiceHandle,
    client: Arc<Mutex<Option<next_edit::next_edit_client::NextEditClient<TracingService>>>>,
}

#[derive(Clone)]
pub struct NextEditClientStreamingImpl {
    inner: Arc<NextEditClientImpl>,
    stream_mux_clients: Cache<RequestSessionId, NextEditStreamMuxClient>,
}

// Response error codes that can show up as fields inside responses

pub enum ResponseStatusCode {
    Ok,
    ExceedContextLength,
    WasBlocked,
}

impl TryFrom<chat::ChatResponse> for ResponseStatusCode {
    type Error = ();
    fn try_from(resp: chat::ChatResponse) -> Result<Self, Self::Error> {
        match resp.status_code {
            None => Ok(ResponseStatusCode::Ok), // No status means success
            Some(code) => match chat::ChatResponseStatusCode::try_from(code) {
                Ok(status) => match status {
                    chat::ChatResponseStatusCode::Ok => Ok(ResponseStatusCode::Ok),
                    chat::ChatResponseStatusCode::ExceedContextLength => {
                        Ok(ResponseStatusCode::ExceedContextLength)
                    }
                    chat::ChatResponseStatusCode::WasBlocked => Ok(ResponseStatusCode::WasBlocked),
                },
                Err(_) => {
                    tracing::error!("Failed to convert ChatResponseStatusCode: {:?}", code);
                    Err(())
                }
            },
        }
    }
}

impl TryFrom<edit::InstructionResponse> for ResponseStatusCode {
    type Error = ();
    fn try_from(resp: edit::InstructionResponse) -> Result<Self, Self::Error> {
        match resp.status_code {
            None => Ok(ResponseStatusCode::Ok), // No status means success
            Some(code) => match edit::InstructionResponseStatusCode::try_from(code) {
                Ok(status) => match status {
                    edit::InstructionResponseStatusCode::Ok => Ok(ResponseStatusCode::Ok),
                    edit::InstructionResponseStatusCode::ExceedContextLength => {
                        Ok(ResponseStatusCode::ExceedContextLength)
                    }
                },
                Err(_) => {
                    tracing::error!(
                        "Failed to convert InstructionResponseStatusCode: {:?}",
                        code
                    );
                    Err(())
                }
            },
        }
    }
}

impl TryFrom<next_edit::NextEditResponse> for ResponseStatusCode {
    type Error = ();
    fn try_from(_resp: next_edit::NextEditResponse) -> Result<Self, Self::Error> {
        Ok(ResponseStatusCode::Ok)
    }
}

// Channels

impl CompletionClientImpl {
    async fn get_client(
        &self,
    ) -> Result<
        completion::completion_client::CompletionClient<TracingService>,
        tonic::transport::Error,
    > {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel =
                    create_channel(self.endpoint.to_string(), None, &self.tls_config).await?;
                let client = completion::completion_client::CompletionClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }
}

impl CompletionClientStreamingImpl {
    async fn create_stream_mux_client(
        &self,
        request_context: &RequestContext,
    ) -> tonic::Result<CompletionStreamMuxClient> {
        let name = format!(
            "request {} session {} completion",
            request_context.request_id(),
            request_context.request_session_id(),
        );
        tracing::info!("Creating {} stream mux client", name);
        // 64 is a magic number and best guess
        let (tx, rx) = tokio::sync::mpsc::channel(64);
        let mut client = self.inner.get_client().await.map_err(|e| {
            tracing::error!(
                "completion client to {} not ready: {}",
                self.inner.endpoint,
                e
            );
            tonic::Status::unavailable("completion not ready")
        })?;
        // Send an empty request so the session stream doesn't hang waiting for the first message.
        tx.send(completion::SessionRequest {
            context: Some(stream_mux::MuxedRequest {
                request_id: "".to_string(),
                routing_id: 5000, // 0 is reserved; low values may collide
                request_session_id: request_context.request_session_id().to_string(),
                ..Default::default()
            }),
            request: None,
        })
        .await
        .expect("New channel should not be full yet");

        let mut request = ReceiverStream::new(rx).into_streaming_request();
        request_context.annotate(request.metadata_mut());
        request.set_timeout(Duration::from_secs(60));

        let stream: tonic::Result<tonic::Response<tonic::Streaming<completion::SessionResponse>>> =
            client.complete_session(request).await;

        let stream_mux_client = StreamMuxClient::new(name, tx, receiver_for_stream(stream?, None));
        Ok(stream_mux_client)
    }

    async fn get_stream_mux_client(
        &self,
        request_context: &RequestContext,
    ) -> tonic::Result<CompletionStreamMuxClient> {
        let entry = self
            .stream_mux_clients
            .get(&request_context.request_session_id())
            .await;
        match entry {
            Some(c) if !c.is_closed() => Ok(c.clone()),
            _ => {
                let c = self.create_stream_mux_client(request_context).await?;
                self.stream_mux_clients
                    .insert(request_context.request_session_id(), c.clone())
                    .await;
                let new_client_count = self.stream_mux_clients.entry_count();
                STICKY_SESSION_COUNT_GAUGE
                    .with_label_values(&[&self.inner.endpoint])
                    .set(new_client_count as i64);
                Ok(c)
            }
        }
    }
}

impl EditClientImpl {
    async fn get_client(
        &self,
    ) -> Result<edit::edit_client::EditClient<TracingService>, tonic::transport::Error> {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel =
                    create_channel(self.endpoint.to_string(), None, &self.tls_config).await?;
                let client = edit::edit_client::EditClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }
}

impl ChatClientImpl {
    async fn get_client(
        &self,
    ) -> Result<chat::chat_client::ChatClient<TracingService>, tonic::transport::Error> {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel =
                    create_channel(self.endpoint.to_string(), None, &self.tls_config).await?;
                let client = chat::chat_client::ChatClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }

    async fn _chat_stream(
        &self,
        request_context: &RequestContext,
        request: chat::ChatRequest,
    ) -> tonic::Result<tonic::Response<tonic::Streaming<chat::ChatResponse>>> {
        let client: Result<chat::chat_client::ChatClient<TracingService>, tonic::Status> =
            self.get_client().await.map_err(|e| {
                tracing::error!("chat client to {} not ready: {}", self.endpoint, e);
                tonic::Status::unavailable("chat not ready")
            });
        let mut request: tonic::Request<chat::ChatRequest> = tonic::Request::new(request);
        let timeout_millis = CHAT_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        request.set_timeout(Duration::from_millis(timeout_millis));
        request_context.annotate(request.metadata_mut());

        client?.chat_stream(request).await
    }
}

impl NextEditClientImpl {
    async fn get_client(
        &self,
    ) -> Result<next_edit::next_edit_client::NextEditClient<TracingService>, tonic::transport::Error>
    {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel =
                    create_channel(self.endpoint.to_string(), None, &self.tls_config).await?;
                let client = next_edit::next_edit_client::NextEditClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }

    async fn _next_edit_stream(
        &self,
        request_context: &RequestContext,
        request: next_edit::NextEditRequest,
    ) -> tonic::Result<tonic::Response<tonic::Streaming<next_edit::NextEditResponse>>> {
        let client: Result<
            next_edit::next_edit_client::NextEditClient<TracingService>,
            tonic::Status,
        > = self.get_client().await.map_err(|e| {
            tracing::error!("next edit client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("next edit not ready")
        });
        let mut request: tonic::Request<next_edit::NextEditRequest> = tonic::Request::new(request);
        let timeout_millis = NEXT_EDIT_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        request.set_timeout(Duration::from_millis(timeout_millis));
        request_context.annotate(request.metadata_mut());

        client?.next_edit_stream(request).await
    }
}

impl NextEditClientStreamingImpl {
    async fn create_stream_mux_client(
        &self,
        request_context: &RequestContext,
    ) -> tonic::Result<NextEditStreamMuxClient> {
        let name = format!(
            "request {} session {} next edit",
            request_context.request_id(),
            request_context.request_session_id(),
        );
        tracing::info!("Creating {} stream mux client", name);
        // 64 is a magic number and best guess
        let (tx, rx) = tokio::sync::mpsc::channel(64);
        let mut client = self.inner.get_client().await.map_err(|e| {
            tracing::error!(
                "next edit client to {} not ready: {}",
                self.inner.endpoint,
                e
            );
            tonic::Status::unavailable("next edit not ready")
        })?;
        // Send an empty request so the session stream doesn't hang waiting for the first message.
        tx.send(next_edit::SessionRequest {
            context: Some(stream_mux::MuxedRequest {
                request_id: "".to_string(),
                routing_id: 6000, // 0 is reserved; low values may collide
                request_session_id: request_context.request_session_id().to_string(),
                ..Default::default()
            }),
            request: None,
        })
        .await
        .expect("New channel should not be full yet");

        let mut request = ReceiverStream::new(rx).into_streaming_request();
        request_context.annotate(request.metadata_mut());
        request.set_timeout(Duration::from_secs(60));

        let stream: tonic::Result<tonic::Response<tonic::Streaming<next_edit::SessionResponse>>> =
            client.next_edit_session(request).await;

        let stream_mux_client = StreamMuxClient::new(name, tx, receiver_for_stream(stream?, None));
        Ok(stream_mux_client)
    }

    async fn get_stream_mux_client(
        &self,
        request_context: &RequestContext,
    ) -> tonic::Result<NextEditStreamMuxClient> {
        let entry = self
            .stream_mux_clients
            .get(&request_context.request_session_id())
            .await;
        match entry {
            Some(c) if !c.is_closed() => Ok(c.clone()),
            _ => {
                let c = self.create_stream_mux_client(request_context).await?;
                self.stream_mux_clients
                    .insert(request_context.request_session_id(), c.clone())
                    .await;
                let new_client_count = self.stream_mux_clients.entry_count();
                STICKY_SESSION_COUNT_GAUGE
                    .with_label_values(&[&self.inner.endpoint])
                    .set(new_client_count as i64);
                Ok(c)
            }
        }
    }
}

#[async_trait]
impl ClientFactory for ClientImplFactory {
    fn create(
        &self,
        _model_name: &str,
        model_type: ModelType,
        endpoint: &str,
    ) -> Result<Client, tonic::Status> {
        match model_type {
            ModelType::Inference => {
                let base_impl = CompletionClientImpl {
                    endpoint: endpoint.to_string(),
                    tls_config: self.tls_config.clone(),
                    feature_flags: self.feature_flags.clone(),
                    client: Arc::new(Mutex::new(None)),
                };

                Ok(Client::Completion(Arc::new(
                    CompletionClientStreamingImpl {
                        inner: Arc::new(base_impl),
                        stream_mux_clients: Cache::builder()
                            // Maximum of 64 concurrent sessions
                            .max_capacity(64)
                            // Sessions are available for requests for up to 30 seconds
                            .time_to_live(Duration::from_secs(30))
                            // Dropping the CompletionStreamMuxClient at eviction time has an
                            // important substantive effect: it destructs the last (modulo
                            // outstanding complete() calls) Sender on the gRPC input stream,
                            // causing that stream to get EOS and clean up the channel.
                            .eviction_listener(
                                |k: Arc<RequestSessionId>, _: CompletionStreamMuxClient, c| {
                                    tracing::info!(
                                        "Session {} completion client evicted: {:?}",
                                        k.to_string(),
                                        c
                                    );
                                },
                            )
                            .build(),
                    },
                )))
            }
            ModelType::Edit => Ok(Client::Edit(Arc::new(EditClientImpl {
                endpoint: endpoint.to_string(),
                tls_config: self.tls_config.clone(),
                feature_flags: self.feature_flags.clone(),
                client: Arc::new(Mutex::new(None)),
            }))),
            ModelType::Chat => Ok(Client::Chat(Arc::new(ChatClientImpl {
                endpoint: endpoint.to_string(),
                tls_config: self.tls_config.clone(),
                feature_flags: self.feature_flags.clone(),
                client: Arc::new(Mutex::new(None)),
            }))),
            ModelType::NextEdit => {
                let next_edit_base_impl = NextEditClientImpl {
                    endpoint: endpoint.to_string(),
                    tls_config: self.tls_config.clone(),
                    feature_flags: self.feature_flags.clone(),
                    client: Arc::new(Mutex::new(None)),
                };

                Ok(Client::NextEdit(Arc::new(NextEditClientStreamingImpl {
                    inner: Arc::new(next_edit_base_impl),
                    stream_mux_clients: Cache::builder()
                        // Maximum of 64 concurrent sessions
                        .max_capacity(64)
                        // Sessions are available for requests for up to 30 seconds
                        .time_to_live(Duration::from_secs(30))
                        .eviction_listener(
                            |k: Arc<RequestSessionId>, _: NextEditStreamMuxClient, c| {
                                tracing::info!(
                                    "Session {} next edit client evicted: {:?}",
                                    k.to_string(),
                                    c
                                );
                            },
                        )
                        .build(),
                })))
            }
        }
    }
}

#[async_trait]
impl CompletionClient for CompletionClientImpl {
    async fn complete(
        &self,
        request_context: &RequestContext,
        request: completion::CompletionRequest,
        _use_session_completion: bool,
    ) -> Result<completion::CompletionResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("completion client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("completion not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(Duration::from_millis(
            COMPLETION_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64,
        ));
        request_context.annotate(request.metadata_mut());

        let response = client.complete(request).await?;
        Ok(response.into_inner())
    }

    async fn find_missing(
        &self,
        request_context: &RequestContext,
        request: completion::FindMissingRequest,
    ) -> Result<completion::FindMissingResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("completion client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("model not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request_context.annotate(request.metadata_mut());

        let response = client.find_missing(request).await?;
        Ok(response.into_inner())
    }
}

#[async_trait]
impl CompletionClient for CompletionClientStreamingImpl {
    async fn complete(
        &self,
        request_context: &RequestContext,
        request: completion::CompletionRequest,
        use_session_completion: bool,
    ) -> Result<completion::CompletionResponse, tonic::Status> {
        if !use_session_completion {
            tracing::debug!(
                "Not using session completion because dynamic feature flag was not set"
            );
            return self
                .inner
                .complete(request_context, request, use_session_completion)
                .await;
        }
        let timeout = Duration::from_millis(
            COMPLETION_TIMEOUT_MS_FLAG.get_from(&self.inner.feature_flags) as u64,
        );

        let span = tracing::info_span!("CompleteSession/single_request", request_id = %request_context.request_id());
        let Ok(stream_mux_client) = self.get_stream_mux_client(request_context).await else {
            tracing::warn!(
                "stream mux client to {} not ready, falling back to single request",
                self.inner.endpoint
            );
            return self
                .inner
                .complete(request_context, request, use_session_completion)
                .await;
        };
        let mut otel_context: HashMap<String, String> = HashMap::new();
        opentelemetry::global::get_text_map_propagator(|prop| {
            prop.inject_context(&tracing::Span::current().context(), &mut otel_context)
        });

        let session_request = completion::SessionRequest {
            context: Some(stream_mux::MuxedRequest {
                routing_id: 0,
                request_id: request_context.request_id().to_string(),
                request_session_id: request_context.request_session_id().to_string(),
                auth_token_secret: request_context.token().expose_secret().clone(),
                // A timeout too large to convert will be treated as no timeout
                timeout: prost_wkt_types::Duration::try_from(timeout).ok(),
                otel_context,
                request_source: request_context.request_source().to_string(),
            }),
            request: Some(request),
        };
        tracing::info!("Sending session completion request");
        let response = match stream_mux_client
            .send_request(session_request)
            .instrument(span)
            .await
        {
            Ok(r) => r,
            Err(e) => {
                tracing::error!("session completion request failed to send: {}", e);
                return Err(e);
            }
        };
        let context = response.context.ok_or_else(|| {
            tracing::error!("no context in response");
            tonic::Status::internal("no context in response")
        })?;

        let (code, message): (tonic::Code, String) = match context.status {
            // If field 'status' is clearly present and non-zero, we can rely on it
            Some(status) if status.code != 0 => {
                (tonic::Code::from_i32(status.code), status.message)
            }
            // If field is missing or zero, then server may not have set it, and we need to check
            // the old fields: status_code and status_detail
            Some(_) | None => {
                let code = match stream_mux::StatusCode::try_from(context.status_code) {
                    Ok(stream_mux::StatusCode::Ok) => tonic::Code::Ok,
                    Ok(stream_mux::StatusCode::Cancelled) => tonic::Code::Cancelled,
                    Ok(stream_mux::StatusCode::DeadlineExceeded) => tonic::Code::DeadlineExceeded,
                    Ok(stream_mux::StatusCode::Error) => tonic::Code::Unknown,
                    Err(_) => {
                        tracing::error!("unexpected status code: {:?}", context.status_code);
                        tonic::Code::Internal
                    }
                };
                (code, context.status_detail)
            }
        };
        if code != tonic::Code::Ok {
            tracing::warn!("session completion request failed: {} {}", code, message);
            Err(tonic::Status::new(code, message))
        } else if let Some(inner_response) = response.response {
            tracing::info!("session completion request succeeded");
            Ok(inner_response)
        } else {
            tracing::error!("unexpected OK status with no result");
            Err(tonic::Status::internal("no request result"))
        }
    }

    async fn find_missing(
        &self,
        request_context: &RequestContext,
        request: completion::FindMissingRequest,
    ) -> Result<completion::FindMissingResponse, tonic::Status> {
        self.inner.find_missing(request_context, request).await
    }
}

#[async_trait]
impl EditClient for EditClientImpl {
    async fn edit(
        &self,
        request_context: &RequestContext,
        request: edit::EditRequest,
    ) -> Result<edit::EditResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("edit client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("edit not ready")
        })?;
        let mut request = tonic::Request::new(request);
        let timeout_millis = EDIT_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        request.set_timeout(Duration::from_millis(timeout_millis));
        request_context.annotate(request.metadata_mut());

        let response = client.edit(request).await?;
        Ok(response.into_inner())
    }
    async fn instruction_stream(
        &self,
        request_context: &RequestContext,
        request: edit::InstructionRequest,
    ) -> tonic::Result<Receiver<tonic::Result<edit::InstructionResponse>>> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("edit client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("edit not ready")
        })?;
        let timeout_millis = EDIT_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let mut request = tonic::Request::new(request);
        request.set_timeout(Duration::from_millis(timeout_millis));
        request_context.annotate(request.metadata_mut());

        let response = client.instruction_stream(request).await;
        match response {
            Ok(r) => {
                return Ok(receiver_for_stream(
                    r,
                    Some(Instant::now() + Duration::from_secs(300)),
                ))
            }
            Err(e) => {
                return Err(e);
            }
        }
    }
}

#[async_trait]
impl ChatClient for ChatClientImpl {
    async fn chat(
        &self,
        request_context: &RequestContext,
        request: chat::ChatRequest,
    ) -> Result<chat::ChatResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("chat client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("chat not ready")
        })?;
        let timeout_millis = CHAT_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let mut request = tonic::Request::new(request);
        request.set_timeout(Duration::from_millis(timeout_millis));
        request_context.annotate(request.metadata_mut());

        match client.chat(request).await {
            Ok(response) => return Ok(response.into_inner()),
            Err(e) => {
                return Err(e);
            }
        }
    }

    async fn chat_stream(
        &self,
        request_context: &RequestContext,
        request: chat::ChatRequest,
    ) -> tonic::Result<Receiver<tonic::Result<chat::ChatResponse>>> {
        let start = Instant::now();
        let response = match self._chat_stream(request_context, request.clone()).await {
            Ok(r) => r,
            // Now that we are using headless gRPC routing for chat we sometimes get a transient cancel at the connection level.
            // In the chat path we aren't protected by stream_mux fallback, so hardcode a single retry for parity.
            Err(e)
                if e.code() == tonic::Code::Cancelled
                    && start.elapsed() < Duration::from_secs(1) =>
            {
                tracing::warn!("chat stream cancelled, retrying: {}", e);
                self._chat_stream(request_context, request).await?
            }
            Err(e) => {
                return Err(e);
            }
        };
        return Ok(receiver_for_stream(
            response,
            Some(start + Duration::from_secs(300)),
        ));
    }

    async fn find_missing(
        &self,
        request_context: &RequestContext,
        request: chat::FindMissingRequest,
    ) -> Result<chat::FindMissingResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("chat client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("model not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request_context.annotate(request.metadata_mut());

        let response = client.find_missing(request).await?;
        Ok(response.into_inner())
    }
}

#[async_trait]
impl NextEditClient for NextEditClientImpl {
    async fn next_edit_stream(
        &self,
        request_context: &RequestContext,
        request: next_edit::NextEditRequest,
        _use_stream_mux: bool,
    ) -> tonic::Result<Receiver<tonic::Result<next_edit::NextEditResponse>>> {
        let response: tonic::Result<
            tonic::Response<tonic::Streaming<next_edit::NextEditResponse>>,
        > = self._next_edit_stream(request_context, request).await;
        match response {
            Err(e) => Err(e),
            Ok(r) => Ok(receiver_for_stream(
                r,
                Some(Instant::now() + Duration::from_secs(300)),
            )),
        }
    }

    async fn find_missing(
        &self,
        request_context: &RequestContext,
        request: next_edit::FindMissingRequest,
    ) -> Result<next_edit::FindMissingResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("next_edit client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("model not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request_context.annotate(request.metadata_mut());

        let response = client.find_missing(request).await?;
        Ok(response.into_inner())
    }
}

#[async_trait]
impl NextEditClient for NextEditClientStreamingImpl {
    async fn next_edit_stream(
        &self,
        request_context: &RequestContext,
        request: next_edit::NextEditRequest,
        use_stream_mux: bool,
    ) -> tonic::Result<Receiver<tonic::Result<next_edit::NextEditResponse>>> {
        if !use_stream_mux {
            tracing::debug!(
                "Not using session completion because dynamic feature flag was not set"
            );
            return self
                .inner
                .next_edit_stream(request_context, request, use_stream_mux)
                .await;
        }
        let timeout = Duration::from_millis(
            NEXT_EDIT_TIMEOUT_MS_FLAG.get_from(&self.inner.feature_flags) as u64,
        );

        let span = tracing::info_span!("NextEditSession/streaming_request", request_id = %request_context.request_id());
        let Ok(stream_mux_client) = self.get_stream_mux_client(request_context).await else {
            tracing::warn!(
                "stream mux client to {} not ready, falling back to single request",
                self.inner.endpoint
            );
            return self
                .inner
                .next_edit_stream(request_context, request, use_stream_mux)
                .await;
        };
        let mut otel_context: HashMap<String, String> = HashMap::new();
        opentelemetry::global::get_text_map_propagator(|prop| {
            prop.inject_context(&tracing::Span::current().context(), &mut otel_context)
        });

        let session_request = next_edit::SessionRequest {
            context: Some(stream_mux::MuxedRequest {
                routing_id: 0,
                request_id: request_context.request_id().to_string(),
                request_session_id: request_context.request_session_id().to_string(),
                auth_token_secret: request_context.token().expose_secret().clone(),
                // A timeout too large to convert will be treated as no timeout
                timeout: prost_wkt_types::Duration::try_from(timeout).ok(),
                otel_context,
                request_source: request_context.request_source().to_string(),
            }),
            request: Some(request),
        };

        // Use a buffer size of 64 for the streaming channel
        const BUFFER_SIZE: usize = 64;

        tracing::info!("Sending streaming session next edit request");
        // Use send_streaming_request instead of send_request to get a stream of responses
        let mut session_stream_rx = match stream_mux_client
            .send_streaming_request(session_request, BUFFER_SIZE)
            .instrument(span)
            .await
        {
            Ok(rx) => rx,
            Err(e) => {
                tracing::error!("streaming session next edit request failed to send: {}", e);
                return Err(e);
            }
        };

        // Return the receiver directly - it will receive all responses from the stream
        let (tx, rx) = mpsc::channel(BUFFER_SIZE);
        tokio::spawn(async move {
            while let Some(result) = session_stream_rx.recv().await {
                match result {
                    Ok(response) => {
                        let context = match response.context {
                            Some(context) => context,
                            None => {
                                tracing::error!("no context in response");
                                continue;
                            }
                        };

                        // Check for errors in the context.status field
                        if let Some(status) = context.status {
                            // We do not look at the Ok case, since in that we will have a populated response,
                            // this will be managed below.
                            if status.code != 0 {
                                if let Err(e) = tx
                                    .send(Err(tonic::Status::new(
                                        tonic::Code::from_i32(status.code),
                                        status.message.clone(),
                                    )))
                                    .await
                                {
                                    if !tx.is_closed() {
                                        tracing::error!(
                                            "Failed to send status error response: {:?}",
                                            e
                                        );
                                    }
                                }
                                return;
                            }
                        }

                        match response.response {
                            Some(response) => {
                                // Normal case.
                                if let Err(e) = tx.send(Ok(response)).await {
                                    if !tx.is_closed() {
                                        tracing::error!("Failed to send response: {:?}", e);
                                    }
                                }
                            }
                            None => {
                                // EOS (End of Stream) case. Since there was an empty response, but an ok status,
                                // we need to send an error to signal the end of the stream.
                                if let Err(e) = tx
                                    .send(Err(tonic::Status::new(tonic::Code::Ok, "EOS")))
                                    .await
                                {
                                    if !tx.is_closed() {
                                        tracing::error!("Failed to send EOS response: {:?}", e);
                                    }
                                }
                            }
                        }
                    }
                    Err(e) => {
                        if let Err(e) = tx.send(Err(e)).await {
                            if !tx.is_closed() {
                                tracing::error!("Failed to send error response: {:?}", e);
                            }
                        }
                        return;
                    }
                }
            }
        });
        Ok(rx)
    }

    async fn find_missing(
        &self,
        request_context: &RequestContext,
        request: next_edit::FindMissingRequest,
    ) -> Result<next_edit::FindMissingResponse, tonic::Status> {
        self.inner.find_missing(request_context, request).await
    }
}

// Helper function: convert a tonic streaming response into a tokio mpsc receiver
// We mostly use this pattern for easier mocking of streams since tonic::Streaming is opaque
// Duplicated from `content_manager_util.rs` - might want to consider making a shared rust base
fn receiver_for_stream<T: Send + 'static>(
    response: tonic::Response<tonic::Streaming<T>>,
    deadline: Option<Instant>,
) -> Receiver<tonic::Result<T>> {
    let mut stream = response.into_inner();
    let (tx, rx) = mpsc::channel::<tonic::Result<T>>(4);
    tokio::spawn(async move {
        tracing::info!("receiver_for_stream spawned");
        loop {
            if deadline.map_or(false, |d| Instant::now() > d) {
                tracing::info!("deadline reached");
                return;
            }
            let message = stream.message().await;
            match message {
                Ok(Some(msg)) => {
                    if let Err(send_err) = tx.send(Ok(msg)).await {
                        if !tx.is_closed() {
                            tracing::error!("Failed to send message: {:?}", send_err);
                        }
                        return;
                    }
                }
                Ok(None) => {
                    return; // EOS case
                }
                Err(e) => {
                    if let Err(send_err) = tx.send(Err(e)).await {
                        if !tx.is_closed() {
                            tracing::error!("Failed to send error response: {:?}", send_err);
                        }
                    }
                    return;
                }
            }
        }
    });
    rx
}
