use clap::Parser;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::{fs::File, path::Path};

#[derive(Default, Debug, Serialize, Deserialize, Clone)]
pub struct AuthQueryConfig {
    pub endpoint: String,
    pub request_timeout_secs: f32,
}

/// structure representing the configuration information in the configuration file.
#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
#[cfg_attr(test, derive(Default))]
pub struct Config {
    pub bind_address: String,
    pub port: u16,

    pub metrics_server_bind_address: String,
    pub metrics_server_port: u16,

    // The record-user-events endpoint is exposed iff this config is true.
    pub record_user_events: bool,
    // Controls a /get-models flag telling the client how to configure enableDataCollection.
    // Be careful setting this-- the collected data is considered sensitive!
    pub enable_client_data_collection: bool,

    // if client MTLS should be used.
    pub client_mtls: bool,

    // if client MTLS is used, the paths to the certificates and keys required
    pub client_ca_cert: String,
    pub client_key: String,
    pub client_cert: String,

    pub central_client_mtls: bool,
    pub central_client_ca_cert: String,
    pub central_client_key: String,
    pub central_client_cert: String,

    pub content_manager_endpoint: String,
    pub docset_endpoint: String,
    pub share_endpoint: String,
    pub agents_endpoint: String,
    pub remote_agents_endpoint: String,
    pub github_processor_endpoint: String,
    pub tenant_watcher_endpoint: String,
    pub memstore_endpoint: String,
    pub memstore_timeout_secs: f32,

    pub namespace: String,
    pub cloud: String,

    pub grpc_bind_address: String,
    // if server MTLS should be used.
    pub grpc_server_mtls: bool,

    // if server MTLS is used, the paths to the certificates and keys required
    pub grpc_server_ca_cert: String,
    pub grpc_server_key: String,
    pub grpc_server_cert: String,

    pub https_server_key: String,
    pub https_server_cert: String,

    // Path to .json file-- not the secret itself!!
    pub hmac_secret_path: String,

    pub auth_query: AuthQueryConfig,

    pub dynamic_feature_flags: Option<std::collections::HashMap<String, Value>>,
    pub dynamic_feature_flags_endpoint: Option<String>,
    pub share_service_url: String,

    pub auth_central_url: String,

    pub throttle_cache_size: u64,
    pub throttle_cache_ttl_seconds: u64,

    pub expose_internal_model_names: bool,

    // the user tier of the namespace
    pub user_tier: Option<String>,

    pub bigtable_proxy_endpoint: String,
    pub bigtable_proxy_timeout_secs: f32,

    // The public-facing hostname for this API proxy instance
    pub ingress_hostname: String,
}

impl Config {
    /// read the configuration from a file
    pub fn read(path: &Path) -> Result<Config, tonic::Status> {
        let file = File::open(path).map_err(|e| tonic::Status::internal(e.to_string()))?;

        let config: Config =
            serde_json::from_reader(file).map_err(|e| tonic::Status::internal(e.to_string()))?;
        Ok(config)
    }
}

/// Search for a pattern in a file and display the lines that contain it.
#[derive(Parser, Debug)]
pub struct CliArguments {
    /// path to the configuration file
    #[arg(long)]
    pub config_file: std::path::PathBuf,

    /// optional path to the launch darkly secrets
    #[arg(long)]
    pub launch_darkly_secrets_file: Option<std::path::PathBuf>,

    /// path to the request insight publisher configuration file
    #[arg(long)]
    pub request_insight_publisher_config_file: std::path::PathBuf,
}
