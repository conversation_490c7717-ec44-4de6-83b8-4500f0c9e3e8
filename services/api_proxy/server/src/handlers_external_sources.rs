use actix_web::{HttpRequest, HttpResponse};
use serde::{Deserialize, Deserializer, Serialize, Serializer};
use tracing_actix_web::RootSpan;

use crate::{
    handler_utils::{gate_on_circuit_breaker, request_context_from_req, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>},
    public_api_proto::{
        ExternalSource, ExternalSourceType, GetImplicitExternalSourcesRequest,
        GetImplicitExternalSourcesResponse, ListExternalSourceTypesRequest,
        ListExternalSourceTypesResponse, SearchExternalSourcesRequest,
        SearchExternalSourcesResponse,
    },
    ModelRegistry,
};
use content_manager_client::ContentManagerClient;

pub const CB_LIST_EXTERNAL_SOURCE_TYPES: feature_flags::BoolFlag = feature_flags::BoolFlag::new(
    "api_proxy_circuit_breaker_list_external_source_types",
    false,
);

pub const CB_SEARCH_EXTERNAL_SOURCES: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_search_external_sources", false);

pub fn serialize_source_types<S>(source_types: &[i32], serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    source_types
        .iter()
        .map(|source_type| {
            ExternalSourceType::try_from(*source_type).map_err(|_| {
                serde::ser::Error::custom(format!("invalid source type: {source_type}"))
            })
        })
        .collect::<Result<Vec<_>, _>>()?
        .into_iter()
        .map(|source_type| source_type.as_str_name())
        .collect::<Vec<_>>()
        .serialize(serializer)
}

pub fn deserialize_source_types<'de, D>(deserializer: D) -> Result<Vec<i32>, D::Error>
where
    D: Deserializer<'de>,
{
    let source_types: Vec<String> = Deserialize::deserialize(deserializer)?;
    source_types
        .iter()
        .map(
            |source_type| match ExternalSourceType::from_str_name(source_type) {
                Some(c) => Ok(c.into()),
                None => Err(serde::de::Error::custom(format!(
                    "invalid source type: {source_type}"
                ))),
            },
        )
        .collect::<Result<Vec<i32>, _>>()
}

pub fn serialize_source_type<S>(source_type: &i32, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    ExternalSourceType::try_from(*source_type)
        .map_err(|_| serde::ser::Error::custom(format!("invalid source type: {source_type}")))?
        .as_str_name()
        .serialize(serializer)
}

pub fn deserialize_source_type<'de, D>(deserializer: D) -> Result<i32, D::Error>
where
    D: Deserializer<'de>,
{
    let source_type: String = Deserialize::deserialize(deserializer)?;
    match ExternalSourceType::from_str_name(&source_type) {
        Some(c) => Ok(c.into()),
        None => Err(serde::de::Error::custom(format!(
            "invalid source type: {source_type}"
        ))),
    }
}

pub struct ExternalSourceClients {
    pub docset_client: Box<dyn docset_client::DocsetClient + Send + Sync>,
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<ListExternalSourceTypesRequest>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        _list_req: ListExternalSourceTypesRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        tracing::info!("list external source types request");

        let (user, tenant_info, _request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_on_circuit_breaker(
            &CB_LIST_EXTERNAL_SOURCE_TYPES,
            &feature_flags,
            req,
            &tenant_info,
        )?;

        // A hardcoded list of external source types
        let resp = ListExternalSourceTypesResponse {
            source_types: vec![ExternalSourceType::DocumentationSet.into()],
        };

        Ok(HttpResponse::Ok().json(resp))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<SearchExternalSourcesRequest>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        search_req: SearchExternalSourcesRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        tracing::info!("search external source types request");

        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_on_circuit_breaker(
            &CB_SEARCH_EXTERNAL_SOURCES,
            &feature_flags,
            req,
            &tenant_info,
        )?;

        if search_req.source_types.len() > 1 {
            return Err(tonic::Status::invalid_argument(
                "Only 1 available source type",
            ));
        }
        // There is only one type at the moment, so always search docsets
        let docsets = self
            .external_source_clients
            .docset_client
            .get_doc_sets(&request_context, &search_req.query)
            .await?;

        tracing::info!("search external source types response: {:?}", docsets);

        let resp = SearchExternalSourcesResponse {
            sources: docsets
                .iter()
                .map(|d| ExternalSource {
                    id: d.doc_set_id.clone(),
                    title: d.title.clone(),
                    name: d.name.clone(),
                    source_type: ExternalSourceType::DocumentationSet.into(),
                })
                .collect(),
        };

        Ok(HttpResponse::Ok().json(resp))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<GetImplicitExternalSourcesRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: GetImplicitExternalSourcesRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        tracing::info!("get implicit docsets request");

        let (_user, _tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        // Extract each external resource type.
        // TODO: Support multiple types
        // Get implicit docsets
        let docsets = self
            .external_source_clients
            .docset_client
            .get_implicit_docsets(&request_context, &request.message)
            .await?;

        // Combine all external resources into one response
        let external_sources = docsets
            .iter()
            .map(|docset| ExternalSource {
                id: docset.doc_set_id.clone(),
                title: docset.title.clone(),
                name: docset.name.clone(),
                source_type: ExternalSourceType::DocumentationSet.into(),
            })
            .collect::<Vec<_>>();

        let resp = GetImplicitExternalSourcesResponse {
            sources: external_sources,
        };

        Ok(HttpResponse::Ok().json(resp))
    }
}

#[cfg(test)]
impl ExternalSourceClients {
    pub fn new_for_test() -> Self {
        ExternalSourceClients {
            docset_client: Box::new(docset_client::MockDocSetClient::new(vec![])),
        }
    }

    pub fn new_for_test_with_data(docsets: Vec<docset_client::proto::docset::DocSet>) -> Self {
        ExternalSourceClients {
            docset_client: Box::new(docset_client::MockDocSetClient::new(docsets)),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;

    use crate::agents_client;
    use crate::config::Config;

    use crate::handler_utils::tests::{new_api_auth, new_root_span, setup_req};
    use crate::handler_utils::{handle_api_auth, Handler};
    use crate::handlers_external_sources::ExternalSourceClients;
    use crate::model_registry::tests::{create_registry, FakeClientFactory};
    use crate::request_insight_util::tests::FakeRequestInsightPublisher;
    use actix_web::{body, http, web};
    use content_manager_client::MockContentManagerClient;
    use memstore_client::MockMemstoreClient;

    #[actix_web::test]
    async fn test_search_external_sources() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let feature_flags_handle = feature_flags::setup_local();
        let external_source_clients = ExternalSourceClients::new_for_test_with_data(vec![
            docset_client::proto::docset::DocSet {
                doc_set_id: "docset://123".to_string(),
                name: "python".to_string(),
                title: "test-python".to_string(),
            },
            docset_client::proto::docset::DocSet {
                doc_set_id: "docset://234".to_string(),
                name: "rust".to_string(),
                title: "test-rust".to_string(),
            },
        ]);
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags_handle.clone(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            Arc::new(MockMemstoreClient::new()),
        ));

        let req = setup_req();
        let request = SearchExternalSourcesRequest {
            query: "python".to_string(),
            source_types: vec![ExternalSourceType::DocumentationSet.into()],
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(app_state.clone(), req, web::Json(request), root_span).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        let result: SearchExternalSourcesResponse =
            serde_json::from_slice(&body::to_bytes(resp.into_body()).await.unwrap()).unwrap();
        assert_eq!(
            result.sources,
            vec![ExternalSource {
                id: "docset://123".to_string(),
                name: "python".to_string(),
                title: "test-python".to_string(),
                source_type: ExternalSourceType::DocumentationSet.into(),
            },]
        );
    }

    #[test]
    fn test_serialize_source_types() {
        let list_req = ListExternalSourceTypesResponse {
            source_types: vec![ExternalSourceType::DocumentationSet.into()],
        };
        assert_eq!(
            serde_json::to_string(&list_req).unwrap(),
            "{\"source_types\":[\"DOCUMENTATION_SET\"]}"
        );
    }

    #[test]
    fn test_deserialize_source_types() {
        let json_data = "{\"query\": \"foo\", \"source_types\":[\"DOCUMENTATION_SET\"]}";
        let search_req: SearchExternalSourcesRequest = serde_json::from_str(json_data).unwrap();
        assert_eq!(
            search_req,
            SearchExternalSourcesRequest {
                query: "foo".to_string(),
                source_types: vec![ExternalSourceType::DocumentationSet.into()],
            }
        );
    }

    #[test]
    fn test_serialize_source_type() {
        let search_req = SearchExternalSourcesResponse {
            sources: vec![ExternalSource {
                id: "docset://123".to_string(),
                name: "python".to_string(),
                title: "test-python".to_string(),
                source_type: ExternalSourceType::DocumentationSet.into(),
            }],
        };
        assert_eq!(
            serde_json::to_string(&search_req).unwrap(),
            "{\"sources\":[{\"id\":\"docset://123\",\"name\":\"python\",\"title\":\"test-python\",\"source_type\":\"DOCUMENTATION_SET\"}]}"
        );
    }
}
