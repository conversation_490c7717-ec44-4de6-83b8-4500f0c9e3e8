use crate::api_auth::User;
use crate::api_auth::CHECK_SUBSCRIPTION_STATUS;
use crate::chat::{
    self, ChatPosition, ChatRequest, ChatRequestEditEvents, ChatRequestIdeState, ChatRequestImage,
    ChatRequestNode, ChatRequestNodeType, ChatRequestText, ChatRequestToolResult, ChatResponse,
    ChatResultNode, ChatResultToolUse,
};
use crate::circuit_breaker::CircuitBreaker;
use crate::generation_clients::{Client, ResponseStatusCode};
use crate::handler_utils::{
    convert_blobs_and_names, gate_on_circuit_breaker, get_model, request_context_from_req, retry,
    status_to_response, streaming_http_response_from_receiver, End<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>,
    ResponseError, RetryPolicy,
};
use crate::metrics::{
    CHAT_IMAGE_COUNT_COLLECTOR, CHAT_IMAGE_REQUEST_COLLECTOR, CHAT_IMAGE_TOKEN_COUNT_COLLECTOR,
    HANDLER_RETRY_COLLECTOR, MEMSTORE_INCR_FAILURE_COUNTER, TRIAL_ENDED_MESSAGE_COUNTER,
    TRIAL_ENDING_DISCLAIMER_COUNTER,
};

use crate::model_registry;
use crate::public_api_proto;
use crate::{augment::model_instance_config::ModelType, chat::Exchange, chat::ToolDefinition};
use content_manager_client::ContentManagerClient;
use lazy_static::lazy_static;
use memstore_client::MemstoreClient;
use request_context::{RequestContext, TenantInfo};
use request_insight_publisher::auth_entities;
use std::convert::TryFrom;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Mutex;
use tokio::time::timeout;
use tracing::Instrument;
use tracing_actix_web::RootSpan;

use crate::request_insight_util::{extract_request_metadata, RequestInsightPublisher};
use actix_web::{web, HttpMessage, HttpRequest, HttpResponse};
use auth_query_client::auth_query::get_token_info_response;
use base64::{engine::general_purpose::STANDARD as base64_engine, Engine};
use blob_names::BlobName;
use model_registry::ModelRegistry;
use request_insight_publisher::request_insight;
use serde::{Deserialize, Deserializer};
use tokio::sync::mpsc::Receiver;

pub const ENABLE_MODEL_NAME: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_enable_model_name", true);

pub const CHAT_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("chat_model", "");

pub const CHAT_FALLBACK_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("chat_fallback_model", "");

pub const CHAT_RAW_OUTPUT_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("chat_raw_output_model", "");

pub const CHAT_RAW_OUTPUT_FALLBACK_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("chat_raw_output_fallback_model", "");

pub const AGENT_CHAT_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("agent_chat_model", "");

pub const AGENT_CHAT_FALLBACK_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("agent_chat_fallback_model", "");

pub const REMOTE_AGENT_CHAT_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("remote_agent_chat_model", "");

pub const REMOTE_AGENT_CHAT_FALLBACK_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("remote_agent_chat_fallback_model", "");

pub const AGENT_CONTINUATION_LATENCY_INJECTION_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("agent_continuation_latency_injection", 0.0);

pub const CB_CHAT: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_chat", false);

pub const CB_CHAT_STREAM: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_chat_stream", false);

// different retry flags for chat as it has a different latency profile
pub const CHAT_RETRY_DELAY_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_chat_retry_delay_ms", 200);

pub const CHAT_RETRY_MAX_DELAY_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_chat_retry_max_delay_ms", 500);

pub const CHAT_MAX_RETRIES_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_chat_max_retries", 2);

// see comments in handler_utils.rs for what this is in more detail
pub const CHAT_THROTTLE_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_throttle_chat", false);

pub const CHAT_THROTTLE_FILL_RATE_PER_SEC_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("api_proxy_throttle_chat_fill_rate", 2.0);

pub const CHAT_THROTTLE_CAPACITY_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_throttle_chat_capacity", 10);

pub const CHAT_AGENT_THROTTLE_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_throttle_chat_agent", false);

pub const CHAT_AGENT_THROTTLE_FILL_RATE_PER_SEC_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("api_proxy_throttle_chat_agent_fill_rate", 2.0);

pub const CHAT_AGENT_THROTTLE_CAPACITY_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_throttle_chat_agent_capacity", 10);

pub const CHAT_REMOTE_AGENT_THROTTLE_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_throttle_chat_remote_agent", false);

pub const CHAT_REMOTE_AGENT_THROTTLE_FILL_RATE_PER_SEC_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("api_proxy_throttle_chat_remote_agent_fill_rate", 2.0);

pub const CHAT_REMOTE_AGENT_THROTTLE_CAPACITY_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_throttle_chat_remote_agent_capacity", 10);

pub const CHAT_CBF_FAILURE_THRESHOLD: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("api_proxy_chat_cbf_failure_threshold", 0.1);

// Feature flags for daily request limit
pub const CHAT_AGENT_DAILY_LIMIT_ENABLED_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_chat_agent_daily_limit_enabled", false);

pub const CHAT_AGENT_DAILY_LIMIT_MAX_REQUESTS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_chat_agent_daily_limit_max_requests", 1000);

pub const CHAT_AGENT_DAILY_LIMIT_MESSAGE_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new(
        "api_proxy_chat_agent_daily_limit_message",
        "We're currently experiencing high system volume. To ensure service quality for all users, we've temporarily paused your Agent access. We apologize for the inconvenience. Agent access will resume within 24 hours. [Fair Use Policy](http://www.augmentcode.com/terms-of-service/fair-use)"
    );

pub const CHAT_REMOTE_AGENT_DAILY_LIMIT_ENABLED_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_chat_remote_agent_daily_limit_enabled", false);

pub const CHAT_REMOTE_AGENT_DAILY_LIMIT_MAX_REQUESTS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_chat_remote_agent_daily_limit_max_requests", 1000);

pub const CHAT_REMOTE_AGENT_DAILY_LIMIT_MESSAGE_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new(
        "api_proxy_chat_remote_agent_daily_limit_message",
        "You have reached the daily limit for Remote Agent access. Please try again tomorrow.",
    );

pub const CHAT_CBF_MINIMUM_REQUESTS: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_chat_cbf_minimum_requests", 5);

pub const CHAT_CBF_FAILURE_WINDOW_SECONDS: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_chat_cbf_failure_window_seconds", 300);

pub const CHAT_CBF_RESET_THRESHOLD_SECONDS: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_chat_cbf_reset_threshold_seconds", 600);

pub const MEMORIES_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("memories_model", "");

pub const MEMORIES_FALLBACK_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("memories_fallback_model", "");

pub const MEMORIES_COMPRESSION_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("memories_compression_model", "");

pub const MEMORIES_COMPRESSION_FALLBACK_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("memories_compression_fallback_model", "");

pub const ORIENTATION_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("orientation_model", "");

pub const ORIENTATION_FALLBACK_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("orientation_fallback_model", "");

// Feature flags for subscription status chat injections
pub const TRIAL_EXPIRATION_DISCLAIMER: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_trial_expiration_disclaimer", false);

pub const USER_SUSPENSION_ENABLED: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_user_suspension_enabled", false);

pub const TRIAL_EXPIRATION_DAYS_THRESHOLD: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_trial_expiration_days_threshold", 5);

pub const INACTIVE_SUBSCRIPTION_DISCLAIMER: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_inactive_subscription_disclaimer", false);

pub const OUT_OF_USAGE_CREDITS_DISCLAIMER: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("enforce_usage_credits", false);

// Feature flag for enabling heartbeat stream in chat endpoints
pub const CHAT_HEARTBEAT_STREAM_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_chat_heartbeat_stream", true);

// Markdown message to show to users whose trial is about to expire
pub const TRIAL_EXPIRATION_DISCLAIMER_MKDOWN_MESSAGE: &str =
    "\n\n---\n\n*Your access expires in {days_remaining} days. [Purchase a subscription](https://app.augmentcode.com/account)*";

pub const USER_SUSPENSION_DISCLAIMER_MKDOWN_MESSAGE: &str =
    "\n\n---\n\n*Your account{account_info} has been suspended{reason}. Please contact support [here](https://support.augmentcode.com) for more information.*";

pub const USER_SUSPENSION_FREETRIAL_DISCLAIMER_MKDOWN_MESSAGE: &str =
    "\n\n---\n\n*Your account{account_info} has been restricted. To continue, [purchase a subscription](https://app.augmentcode.com/account).*";

// Markdown message to show to users whose subscription is inactive
pub const INACTIVE_SUBSCRIPTION_DISCLAIMER_MKDOWN_MESSAGE: &str =
    "*Your subscription{account_info} is inactive. \
    If you have another account with an active subscription, \
    please {logout_link} and sign in with that one. \
    Otherwise, update your plan [here](https://app.augmentcode.com/account) to continue using Augment.*";

// Markdown message to show to users whose subscription is out of credits
pub const OUT_OF_USAGE_CREDITS_DISCLAIMER_MKDOWN_MESSAGE: &str =
    "*You are out of user messages{account_info}. Please update your account [here](https://app.augmentcode.com/account) to continue using Augment.*";

// ms to wait to acquire lock to check chat CBF status
const CHAT_CBF_LOCK_TIMEOUT_MS: u64 = 100;

// Error message to return to user when subscription is expired
// Using this to map internal tonic::Status::PermissionDenied error to a HTTP 402 Payment Required error
pub const SUBSCRIPTION_EXPIRED_INTERNAL_ERROR: &str = "subscription_expired";

// Error message to return to user when user is suspended
pub const USER_SUSPENDED_INTERNAL_ERROR: &str = "user_suspended";

// Error message to return to user when out of usage credits
pub const OUT_OF_USAGE_CREDITS_INTERNAL_ERROR: &str = "out_of_usage_credits";

async fn acquire_cbf_lock() -> Result<tokio::sync::MutexGuard<'static, CircuitBreaker>, ()> {
    match timeout(
        Duration::from_millis(CHAT_CBF_LOCK_TIMEOUT_MS),
        CHAT_CBF.lock(),
    )
    .await
    {
        Ok(guard) => Ok(guard),
        Err(_) => {
            tracing::warn!("Failed to lock chat CBF");
            Err(())
        }
    }
}

// Helper to generate the memstore key for daily limits
fn get_memstore_key(user_id: &str, chat_mode_str: &str, date: chrono::NaiveDate) -> String {
    format!(
        "chat_fair_use:{}:{}:{}",
        user_id,
        chat_mode_str,
        date.format("%Y-%m-%d")
    )
}

// Helper for marking a key as should expire by the next midnight in UTC (if sooner than
// the current expiry)
async fn set_expiry_at_next_midnight(
    client: &Arc<dyn MemstoreClient>,
    request_context: &RequestContext,
    key: &str,
) {
    let Some(tomorrow_midnight_naive) = (chrono::Utc::now() + chrono::Duration::days(1))
        .date_naive()
        .and_hms_opt(0, 0, 0)
    else {
        // This should never be possible with `and_hms_opt` called with HH:MM:SS
        // = 00:00:00, but just to feel safe we log an error in this case rather
        // than panic.
        tracing::error!(
            key = %key,
            "Failed to calculate tomorrow midnight with valid HMS values"
        );
        return;
    };

    let tomorrow_midnight_timestamp = tomorrow_midnight_naive.and_utc().timestamp() as u64;

    // Set expiry only when the new expiry is less than current one (LT)
    let expire_options = memstore_client::ExpireAtOptions::new().with_lt();

    if let Err(e) = client
        .expire_at(
            request_context,
            key.to_string(),
            tomorrow_midnight_timestamp,
            expire_options,
        )
        .await
    {
        tracing::warn!(
            key = %key,
            error = ?e,
            "Failed to set expiry for memstore key."
        );
    }
}

// Check if the user has reached the daily request limit for a given chat mode.
// Returns: true if the limit has been reached, false otherwise
async fn check_daily_request_limit(
    user_id: &str,
    feature_flags: &feature_flags::FeatureFlagsServiceHandle,
    chat_mode: public_api_proto::ChatMode,
    request_context: &RequestContext,
    memstore_client: &Arc<dyn MemstoreClient>,
    metrics_tenant_name: &str,
) -> bool {
    async move {
        // Determine max_requests based on chat_mode and feature flags
        let (limit_enabled, max_requests) = match chat_mode {
            public_api_proto::ChatMode::Agent => (
                CHAT_AGENT_DAILY_LIMIT_ENABLED_FLAG.get_from(feature_flags),
                CHAT_AGENT_DAILY_LIMIT_MAX_REQUESTS_FLAG.get_from(feature_flags) as usize,
            ),
            public_api_proto::ChatMode::RemoteAgent => (
                CHAT_REMOTE_AGENT_DAILY_LIMIT_ENABLED_FLAG.get_from(feature_flags),
                CHAT_REMOTE_AGENT_DAILY_LIMIT_MAX_REQUESTS_FLAG.get_from(feature_flags) as usize,
            ),
            _ => {
                // Not a mode with daily limits
                tracing::info!(
                    "Not checking request limit: Selected chat mode {:?} has no limits.",
                    chat_mode
                );
                return false;
            }
        };

        // Record feature flag values in the span
        tracing::Span::current().record("limit_enabled", limit_enabled);
        tracing::Span::current().record("max_requests", max_requests);

        if !limit_enabled || max_requests == 0 {
            tracing::info!(
                "Not checking request limit: limit_enabled={}, max_requests={}",
                limit_enabled,
                max_requests
            );
            return false; // Limit not enabled or limit set to no-limit (0)
        }

        let now_utc = chrono::Utc::now();
        let today_naive = now_utc.date_naive();
        let chat_mode_str = format!("{:?}", chat_mode).to_lowercase();
        let mem_key = get_memstore_key(user_id, &chat_mode_str, today_naive);

        tracing::Span::current().record("memstore_key", &mem_key);

        // Increment the request count in memstore
        match memstore_client.incr(request_context, mem_key.clone()).await {
            Ok(new_count) => {
                // Record the request count in the span
                tracing::Span::current().record("daily_request_count", new_count);

                // Set expiry at the next midnight UTC
                set_expiry_at_next_midnight(memstore_client, request_context, &mem_key).await;

                let over_limit = new_count > max_requests as i64;
                tracing::info!(over_limit, "check_daily_request_limit");
                over_limit
            }
            Err(e) => {
                tracing::error!(
                    user_id = %user_id,
                    key = %mem_key,
                    error = ?e,
                    "Memstore 'incr' failed for daily request limit check"
                );

                // Increment Prometheus metric for memstore incr failures
                MEMSTORE_INCR_FAILURE_COUNTER
                    .with_label_values(&[metrics_tenant_name])
                    .inc();

                // Allow request if memstore operation fails as we aim to fail
                // open rather than fail closed. i.e.: at time of writing we
                // think it's worse to block users who should have access rather
                // than to allow users who should be blocked.
                false
            }
        }
    }
    .instrument(tracing::info_span!(
        "check_daily_request_limit",
        user_id = %user_id,
        chat_mode = ?chat_mode,
        limit_enabled = tracing::field::Empty,
        max_requests = tracing::field::Empty,
        memstore_key = tracing::field::Empty,
        daily_request_count = tracing::field::Empty,
    ))
    .await
}

lazy_static! {
    // Initialize with some reasonable defaults, these will be overriden by feature flags
    pub static ref CHAT_CBF: Mutex<CircuitBreaker> = Mutex::new(
        CircuitBreaker::new(
            0.1,  // failure_threshold
            5,    // minimum_requests
            300,  // failure_window_seconds (5 minutes)
            600   // reset_threshold_seconds (10 minutes)
        )
    );



    // In-memory map to track which users have already been shown the trial expiration message and when
    pub static ref TRIAL_EXPIRATION_ALERTED_USERS: Mutex<std::collections::HashMap<String, chrono::DateTime<chrono::Utc>>> = Mutex::new(
        std::collections::HashMap::new()
    );
}

fn convert_changed_file_stats(
    stats: Option<public_api_proto::ChangedFileStats>,
) -> Option<chat::ChangedFileStats> {
    stats.map(|s| chat::ChangedFileStats {
        added_file_stats: s.added_file_stats.map(convert_per_type_changed_file_stats),
        broken_file_stats: s.broken_file_stats.map(convert_per_type_changed_file_stats),
        copied_file_stats: s.copied_file_stats.map(convert_per_type_changed_file_stats),
        deleted_file_stats: s
            .deleted_file_stats
            .map(convert_per_type_changed_file_stats),
        modified_file_stats: s
            .modified_file_stats
            .map(convert_per_type_changed_file_stats),
        renamed_file_stats: s
            .renamed_file_stats
            .map(convert_per_type_changed_file_stats),
        unmerged_file_stats: s
            .unmerged_file_stats
            .map(convert_per_type_changed_file_stats),
        unknown_file_stats: s
            .unknown_file_stats
            .map(convert_per_type_changed_file_stats),
    })
}

fn convert_per_type_changed_file_stats(
    stats: public_api_proto::PerTypeChangedFileStats,
) -> chat::PerTypeChangedFileStats {
    chat::PerTypeChangedFileStats {
        changed_file_count: stats.changed_file_count,
        per_file_change_stats_head: stats
            .per_file_change_stats_head
            .into_iter()
            .map(convert_per_file_change_stats)
            .collect(),
        per_file_change_stats_tail: stats
            .per_file_change_stats_tail
            .into_iter()
            .map(convert_per_file_change_stats)
            .collect(),
    }
}

fn convert_per_file_change_stats(
    stats: public_api_proto::PerFileChangeStats,
) -> chat::PerFileChangeStats {
    chat::PerFileChangeStats {
        file_path: stats.file_path,
        insertion_count: stats.insertion_count,
        deletion_count: stats.deletion_count,
        old_file_path: stats.old_file_path,
    }
}

fn convert_chat_feature_detection_flags(
    flags: public_api_proto::ChatFeatureDetectionFlags,
) -> chat::ChatFeatureDetectionFlags {
    chat::ChatFeatureDetectionFlags {
        support_relevant_sources: flags.support_relevant_sources,
        support_tool_use_start: flags.support_tool_use_start,
        support_parallel_tool_use: flags.support_parallel_tool_use,
    }
}

pub fn deserialize_chat_mode<'de, D>(deserializer: D) -> Result<i32, D::Error>
where
    D: Deserializer<'de>,
{
    let s: &str = Deserialize::deserialize(deserializer)?;
    match public_api_proto::ChatMode::from_str_name(s) {
        Some(c) => Ok(c.into()),
        None => Err(serde::de::Error::custom(format!("invalid chat type: {s}"))),
    }
}

impl TryFrom<public_api_proto::ChatRequest> for ChatRequest {
    type Error = tonic::Status;

    /// convert the frontend chat request into the modelhost api
    fn try_from(req: public_api_proto::ChatRequest) -> Result<ChatRequest, tonic::Status> {
        let position: Option<ChatPosition> = match (req.prefix_begin, req.suffix_end) {
            (Some(prefix_begin), Some(suffix_end)) => Some(ChatPosition {
                prefix_begin,
                suffix_end,
                blob_name: req.blob_name.unwrap_or_default().to_string(),
            }),
            _ => None,
        };

        // When converting the frontend request to the backend request, if
        // there is a workspace specified in delta format, try to convert it
        // into the proto version. This may fail if the blob ids are not
        // valid hex strings.
        #[allow(deprecated)]
        let legacy_blob_names = req
            .blob_names
            .iter()
            .map(|m| BlobName::new(m).unwrap())
            .collect();
        let blobs = convert_blobs_and_names(&req.blobs, Some(&legacy_blob_names))?;

        #[allow(deprecated)]
        Ok(chat::ChatRequest {
            model_name: req.model.unwrap_or_default(),
            prefix: req.prefix.unwrap_or_default(),
            suffix: req.suffix.unwrap_or_default(),
            path: req.path.unwrap_or_default(),
            message: req.message,
            chat_history: req
                .chat_history
                .into_iter()
                .map(|e| Exchange {
                    request_message: e.request_message,
                    response_text: e.response_text,
                    request_id: e.request_id,
                    request_nodes: e
                        .request_nodes
                        .into_iter()
                        .filter(|node| {
                            node.r#type
                                != public_api_proto::ChatRequestNodeType::CheckpointRef as i32
                        })
                        .map(|node| ChatRequestNode {
                            id: node.id,
                            r#type: node.r#type,
                            text_node: node.text_node.map(|text_node| ChatRequestText {
                                content: text_node.content,
                            }),
                            tool_result_node: node.tool_result_node.map(|tool_result_node| {
                                ChatRequestToolResult {
                                    tool_use_id: tool_result_node.tool_use_id,
                                    content: tool_result_node.content,
                                    is_error: tool_result_node.is_error,
                                    request_id: tool_result_node.request_id,
                                    content_nodes: tool_result_node
                                        .content_nodes
                                        .into_iter()
                                        .map(|content_node| chat::ToolResultContentNode {
                                            r#type: content_node.r#type,
                                            text_content: content_node.text_content,
                                            image_content: content_node.image_content.map(
                                                |image_content| ChatRequestImage {
                                                    image_data: image_content.image_data,
                                                    format: image_content.format,
                                                },
                                            ),
                                        })
                                        .collect(),
                                }
                            }),
                            image_node: node.image_node.map(|image_node| ChatRequestImage {
                                image_data: image_node.image_data,
                                format: image_node.format,
                            }),
                            ide_state_node: node.ide_state_node.map(|ide_state_node| {
                                let mut chat_ide_state = ChatRequestIdeState::default();

                                // Copy workspace folders from the request
                                for folder_info in &ide_state_node.workspace_folders {
                                    chat_ide_state.workspace_folders.push(
                                        chat::WorkspaceFolderInfo {
                                            repository_root: folder_info.repository_root.clone(),
                                            folder_root: folder_info.folder_root.clone(),
                                        },
                                    );
                                }

                                // Set workspace_folders_unchanged flag
                                chat_ide_state.workspace_folders_unchanged =
                                    ide_state_node.workspace_folders_unchanged;

                                if let Some(current_terminal) = &ide_state_node.current_terminal {
                                    chat_ide_state.current_terminal = Some(chat::TerminalInfo {
                                        terminal_id: current_terminal.terminal_id,
                                        current_working_directory: current_terminal
                                            .current_working_directory
                                            .clone(),
                                    });
                                }

                                chat_ide_state
                            }),
                            edit_events_node: node.edit_events_node.map(|edit_events_node| {
                                ChatRequestEditEvents {
                                    edit_events: edit_events_node
                                        .edit_events
                                        .into_iter()
                                        .map(|event| chat::ChatRequestFileEdit {
                                            path: event.path,
                                            before_blob_name: event.before_blob_name,
                                            after_blob_name: event.after_blob_name,
                                            edits: event
                                                .edits
                                                .into_iter()
                                                .map(|edit| chat::ChatRequestSingleEdit {
                                                    before_line_start: edit.before_line_start,
                                                    before_text: edit.before_text,
                                                    after_line_start: edit.after_line_start,
                                                    after_text: edit.after_text,
                                                })
                                                .collect(),
                                        })
                                        .collect(),
                                    source: match edit_events_node.source {
                                        Some(1) => Some(chat::EditEventSource::UserEdit as i32),
                                        Some(2) => {
                                            Some(chat::EditEventSource::CheckpointRevert as i32)
                                        }
                                        _ => Some(chat::EditEventSource::Unspecified as i32),
                                    },
                                }
                            }),
                        })
                        .collect(),
                    response_nodes: e
                        .response_nodes
                        .into_iter()
                        .map(|node| ChatResultNode {
                            id: node.id,
                            r#type: node.r#type,
                            content: node.content,
                            tool_use: node.tool_use.map(|tool_use| ChatResultToolUse {
                                tool_use_id: tool_use.tool_use_id,
                                tool_name: tool_use.tool_name,
                                input_json: tool_use.input_json,
                                is_partial: tool_use.is_partial,
                            }),
                        })
                        .collect(),
                })
                .collect(),
            selected_code: req.selected_code.unwrap_or_default(),
            blobs: vec![blobs],
            lang: req.lang.unwrap_or_default(),
            position,
            sequence_id: req.sequence_id,
            enable_preference_collection: req.enable_preference_collection.unwrap_or(false),
            user_guided_blobs: req.user_guided_blobs,
            context_code_exchange_request_id: req
                .context_code_exchange_request_id
                .unwrap_or_default(),
            external_source_ids: req.external_source_ids,
            user_guidelines: req.user_guidelines,
            workspace_guidelines: req.workspace_guidelines,
            agent_memories: req.agent_memories,
            persona_type: req.persona_type,
            prompt_formatter_name: "".to_string(),
            disable_auto_external_sources: req.disable_auto_external_sources.unwrap_or(false),
            feature_detection_flags: Some(convert_chat_feature_detection_flags(
                req.feature_detection_flags.unwrap_or_default(),
            )),
            tool_definitions: req
                .tool_definitions
                .into_iter()
                .map(|tool| ToolDefinition {
                    name: tool.name,
                    description: tool.description,
                    input_schema_json: tool.input_schema_json,
                })
                .collect(),
            rules: req
                .rules
                .into_iter()
                .map(|rule| chat::Rule {
                    r#type: rule.r#type,
                    path: rule.path,
                    content: rule.content,
                })
                .collect(),
            silent: req.silent.unwrap_or(false),
            nodes: req
                .nodes
                .into_iter()
                .filter(|node| {
                    node.r#type != public_api_proto::ChatRequestNodeType::CheckpointRef as i32
                })
                .map(|node| ChatRequestNode {
                    id: node.id,
                    r#type: node.r#type,
                    text_node: node.text_node.map(|text_node| ChatRequestText {
                        content: text_node.content,
                    }),
                    tool_result_node: node.tool_result_node.map(|tool_result_node| {
                        ChatRequestToolResult {
                            tool_use_id: tool_result_node.tool_use_id,
                            content: tool_result_node.content,
                            is_error: tool_result_node.is_error,
                            request_id: tool_result_node.request_id,
                            content_nodes: tool_result_node
                                .content_nodes
                                .into_iter()
                                .map(|content_node| chat::ToolResultContentNode {
                                    r#type: content_node.r#type,
                                    text_content: content_node.text_content,
                                    image_content: content_node.image_content.map(
                                        |image_content| ChatRequestImage {
                                            image_data: image_content.image_data,
                                            format: image_content.format,
                                        },
                                    ),
                                })
                                .collect(),
                        }
                    }),
                    image_node: node.image_node.map(|image_node| ChatRequestImage {
                        image_data: image_node.image_data,
                        format: image_node.format,
                    }),
                    ide_state_node: node.ide_state_node.map(|ide_state_node| {
                        let mut chat_ide_state = ChatRequestIdeState::default();

                        // Copy workspace folders from the request
                        for folder_info in &ide_state_node.workspace_folders {
                            chat_ide_state
                                .workspace_folders
                                .push(chat::WorkspaceFolderInfo {
                                    repository_root: folder_info.repository_root.clone(),
                                    folder_root: folder_info.folder_root.clone(),
                                });
                        }

                        // Set workspace_folders_unchanged flag
                        chat_ide_state.workspace_folders_unchanged =
                            ide_state_node.workspace_folders_unchanged;

                        if let Some(current_terminal) = &ide_state_node.current_terminal {
                            chat_ide_state.current_terminal = Some(chat::TerminalInfo {
                                terminal_id: current_terminal.terminal_id,
                                current_working_directory: current_terminal
                                    .current_working_directory
                                    .clone(),
                            });
                        }

                        chat_ide_state
                    }),
                    edit_events_node: node.edit_events_node.map(|edit_events_node| {
                        ChatRequestEditEvents {
                            edit_events: edit_events_node
                                .edit_events
                                .into_iter()
                                .map(|event| chat::ChatRequestFileEdit {
                                    path: event.path,
                                    before_blob_name: event.before_blob_name,
                                    after_blob_name: event.after_blob_name,
                                    edits: event
                                        .edits
                                        .into_iter()
                                        .map(|edit| chat::ChatRequestSingleEdit {
                                            before_line_start: edit.before_line_start,
                                            before_text: edit.before_text,
                                            after_line_start: edit.after_line_start,
                                            after_text: edit.after_text,
                                        })
                                        .collect(),
                                })
                                .collect(),
                            source: match edit_events_node.source {
                                Some(1) => Some(chat::EditEventSource::UserEdit as i32),
                                Some(2) => Some(chat::EditEventSource::CheckpointRevert as i32),
                                _ => Some(chat::EditEventSource::Unspecified as i32),
                            },
                        }
                    }),
                })
                .collect(),
            ..Default::default()
        })
    }
}

impl TryFrom<public_api_proto::GenerateCommitMessageRequest> for ChatRequest {
    type Error = tonic::Status;

    fn try_from(
        req: public_api_proto::GenerateCommitMessageRequest,
    ) -> Result<ChatRequest, Self::Error> {
        // When converting the frontend request to the backend request, if
        // there is a workspace specified in delta format, try to convert it
        // into the proto version. This may fail if the blob ids are not
        // valid hex strings.
        Ok(ChatRequest {
            changed_file_stats: convert_changed_file_stats(req.changed_file_stats),
            diff: req.diff,
            relevant_commit_messages: req.relevant_commit_messages,
            example_commit_messages: req.example_commit_messages,
            prompt_formatter_name: "generate-commit-message".to_string(),
            ..Default::default()
        })
    }
}

impl From<ChatResponse> for public_api_proto::ChatResponse {
    /// transfer from a chat response to the public API chat response.
    fn from(mut resp: ChatResponse) -> Self {
        // Convert WasBlocked status code to OK before sending to client
        if resp.status_code == Some(chat::ChatResponseStatusCode::WasBlocked as i32) {
            resp.status_code = Some(chat::ChatResponseStatusCode::Ok as i32);
        }

        public_api_proto::ChatResponse {
            text: resp.text.clone(),
            unknown_blob_names: resp.unknown_blob_names.clone(),
            checkpoint_not_found: resp.checkpoint_not_found,
            workspace_file_chunks: resp
                .workspace_file_chunks
                .into_iter()
                .map(|c| public_api_proto::WorkspaceFileChunk {
                    char_start: c.char_start,
                    char_end: c.char_end,
                    blob_name: c.blob_name,
                })
                .collect(),
            incorporated_external_sources: resp
                .incorporated_external_sources
                .into_iter()
                .map(|c| public_api_proto::IncorporatedExternalSource {
                    source_name: c.source_name,
                    link: c.link,
                })
                .collect(),
            nodes: resp
                .nodes
                .into_iter()
                .map(|n| public_api_proto::ChatResultNode {
                    id: n.id,
                    content: n.content,
                    r#type: n.r#type,
                    tool_use: n
                        .tool_use
                        .map(|tool_use| public_api_proto::ChatResultToolUse {
                            tool_use_id: tool_use.tool_use_id,
                            tool_name: tool_use.tool_name,
                            input_json: tool_use.input_json,
                            is_partial: tool_use.is_partial,
                        }),
                })
                .collect(),
            stop_reason: resp.stop_reason,
        }
    }
}

impl From<ChatResponse> for public_api_proto::GenerateCommitMessageResponse {
    fn from(resp: ChatResponse) -> Self {
        public_api_proto::GenerateCommitMessageResponse {
            text: resp.text.clone(),
        }
    }
}

fn chat_status_to_response(e: &tonic::Status) -> HttpResponse {
    tracing::error!("Error getting chat response {:?}", e);
    match e.code() {
        // Resource Exhausted from Chat is never a user/client rate limit.
        // It's a server or third party system overload.
        // We want to return 529 but our clients render that poorly (changing soon)
        // return 503 with a hint that retry soon may help
        tonic::Code::ResourceExhausted => HttpResponse::ServiceUnavailable()
            .append_header(("Retry-After", "1"))
            .json(ResponseError::new("Service overloaded", None)),
        // Special case for subscription inactive
        tonic::Code::PermissionDenied
            if e.message().contains(SUBSCRIPTION_EXPIRED_INTERNAL_ERROR) =>
        {
            HttpResponse::PaymentRequired().json(ResponseError::new(
                SUBSCRIPTION_EXPIRED_INTERNAL_ERROR,
                None,
            ))
        }

        tonic::Code::PermissionDenied if e.message().contains(USER_SUSPENDED_INTERNAL_ERROR) => {
            HttpResponse::PaymentRequired()
                .json(ResponseError::new(USER_SUSPENDED_INTERNAL_ERROR, None))
        }

        tonic::Code::PermissionDenied
            if e.message().contains(OUT_OF_USAGE_CREDITS_INTERNAL_ERROR) =>
        {
            HttpResponse::PaymentRequired().json(ResponseError::new(
                OUT_OF_USAGE_CREDITS_INTERNAL_ERROR,
                None,
            ))
        }

        _ => status_to_response(e),
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> Handler<MR, CNC> {
    /// Emits a ChatUserMessage event for a user message (without tool result nodes)
    /// This is a wrapper around the utility function in chat_utils.rs
    async fn emit_chat_user_message_event(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        chat_request: &ChatRequest,
        front_chat_mode: public_api_proto::ChatMode,
        resolved_model_name: Option<&str>,
    ) {
        crate::chat_utils::emit_chat_user_message_event(
            &self.request_insight_publisher,
            request_context,
            tenant_info,
            chat_request,
            front_chat_mode,
            resolved_model_name,
        )
        .await;
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<public_api_proto::ChatRequest>
    for Handler<MR, CNC>
{
    fn get_retry_policy(&self, req: &HttpRequest) -> RetryPolicy {
        self.get_retry_policy_from_flags(
            req,
            &CHAT_RETRY_DELAY_MS_FLAG,
            &CHAT_RETRY_MAX_DELAY_MS_FLAG,
            &CHAT_MAX_RETRIES_FLAG,
        )
    }

    async fn handle(
        &self,
        req: &HttpRequest,
        front_chat_request: public_api_proto::ChatRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let front_chat_mode = public_api_proto::ChatMode::try_from(front_chat_request.mode)
            .unwrap_or(public_api_proto::ChatMode::Chat);

        tracing::info!(
            "chat request mode={:?} model={:?}",
            front_chat_mode,
            front_chat_request.model,
        );

        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        // If model name override is false, replace model name with empty string
        let enable_model_name: bool = ENABLE_MODEL_NAME.get_from(&feature_flags);
        let mut front_chat_request = front_chat_request.clone();
        if !enable_model_name
            && front_chat_request.model.is_some()
            && !front_chat_request.model.as_ref().unwrap().is_empty()
            // Note(yuri): to support older clients that send model name (Apr '25)
            && front_chat_request.model.as_ref().unwrap() != "gemini-2-flash-001-simple-port"
        {
            tracing::info!(
                "Overriding model name {:?} to default",
                front_chat_request.model
            );
            front_chat_request.model = Some("".to_string());
        }

        gate_on_circuit_breaker(&CB_CHAT, &feature_flags, req, &tenant_info)?;

        let throttle_chat_agent = match front_chat_mode {
            public_api_proto::ChatMode::Agent
            | public_api_proto::ChatMode::Memories
            | public_api_proto::ChatMode::MemoriesCompression
            | public_api_proto::ChatMode::Orientation => {
                CHAT_AGENT_THROTTLE_FLAG.get_from(&feature_flags)
            }
            public_api_proto::ChatMode::Chat => CHAT_THROTTLE_FLAG.get_from(&feature_flags),
            public_api_proto::ChatMode::RemoteAgent => {
                CHAT_REMOTE_AGENT_THROTTLE_FLAG.get_from(&feature_flags)
            }
        };
        if throttle_chat_agent {
            let fill_rate_per_second = match front_chat_mode {
                public_api_proto::ChatMode::Agent
                | public_api_proto::ChatMode::Memories
                | public_api_proto::ChatMode::MemoriesCompression
                | public_api_proto::ChatMode::Orientation => {
                    CHAT_AGENT_THROTTLE_FILL_RATE_PER_SEC_FLAG.get_from(&feature_flags)
                }
                public_api_proto::ChatMode::Chat => {
                    CHAT_THROTTLE_FILL_RATE_PER_SEC_FLAG.get_from(&feature_flags)
                }
                public_api_proto::ChatMode::RemoteAgent => {
                    CHAT_REMOTE_AGENT_THROTTLE_FILL_RATE_PER_SEC_FLAG.get_from(&feature_flags)
                }
            };
            let capacity = match front_chat_mode {
                public_api_proto::ChatMode::Agent
                | public_api_proto::ChatMode::Memories
                | public_api_proto::ChatMode::MemoriesCompression
                | public_api_proto::ChatMode::Orientation => {
                    CHAT_AGENT_THROTTLE_CAPACITY_FLAG.get_from(&feature_flags) as f64
                }
                public_api_proto::ChatMode::Chat => {
                    CHAT_THROTTLE_CAPACITY_FLAG.get_from(&feature_flags) as f64
                }
                public_api_proto::ChatMode::RemoteAgent => {
                    CHAT_REMOTE_AGENT_THROTTLE_CAPACITY_FLAG.get_from(&feature_flags) as f64
                }
            };
            let endpoint = match front_chat_mode {
                public_api_proto::ChatMode::Agent
                | public_api_proto::ChatMode::Memories
                | public_api_proto::ChatMode::MemoriesCompression
                | public_api_proto::ChatMode::Orientation => "agent_chat",
                public_api_proto::ChatMode::Chat => "chat",
                public_api_proto::ChatMode::RemoteAgent => "remote_agent_chat",
            };
            self.should_throttle(&user.user_id, endpoint, fill_rate_per_second, capacity, 1.0)
                .await?;
        }

        let ri_request_type = match front_chat_mode {
            public_api_proto::ChatMode::Agent => request_insight::RequestType::AgentChat,
            public_api_proto::ChatMode::Memories => request_insight::RequestType::Memories,
            public_api_proto::ChatMode::MemoriesCompression => {
                request_insight::RequestType::MemoriesCompression
            }
            public_api_proto::ChatMode::RemoteAgent => {
                request_insight::RequestType::RemoteAgentChat
            }
            public_api_proto::ChatMode::Orientation => request_insight::RequestType::Orientation,
            public_api_proto::ChatMode::Chat => request_insight::RequestType::Chat,
        };
        let metadata = extract_request_metadata(&request_context, ri_request_type, &user, req);
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        let feature_detection_flags = front_chat_request.feature_detection_flags;
        let relevant_model_flag: &feature_flags::StringFlag = match front_chat_mode {
            public_api_proto::ChatMode::Agent => &AGENT_CHAT_MODEL_FLAG,
            // Remote agent uses the same model as IDE agent
            public_api_proto::ChatMode::RemoteAgent => {
                // Try to get remote agent flag, fallback to agent flag if empty
                let remote_agent_model_flag = REMOTE_AGENT_CHAT_MODEL_FLAG.get_from(&feature_flags);
                if !remote_agent_model_flag.is_empty() {
                    &REMOTE_AGENT_CHAT_MODEL_FLAG
                } else {
                    &AGENT_CHAT_MODEL_FLAG
                }
            }
            public_api_proto::ChatMode::Memories => &MEMORIES_MODEL_FLAG,
            public_api_proto::ChatMode::MemoriesCompression => &MEMORIES_COMPRESSION_MODEL_FLAG,
            public_api_proto::ChatMode::Orientation => &ORIENTATION_MODEL_FLAG,
            public_api_proto::ChatMode::Chat => match feature_detection_flags {
                // Evaluate flags in order of priority, usually new to old
                Some(ref flags) => {
                    if flags.support_raw_output.unwrap_or(false) {
                        &CHAT_RAW_OUTPUT_MODEL_FLAG
                    } else {
                        &CHAT_MODEL_FLAG
                    }
                }
                None => &CHAT_MODEL_FLAG,
            },
        };
        let front_chat_model = front_chat_request
            .model
            .clone()
            .unwrap_or_default()
            .to_string();
        let (com, mi) = get_model::<MR>(
            front_chat_request.model.as_ref(),
            &self.model_registry,
            &feature_flags,
            relevant_model_flag,
            ModelType::Chat,
        )
        .await?;

        let com = match com {
            Client::Chat(c) => c.clone(),
            _ => return Err(tonic::Status::internal("Model is not a chat model")),
        };

        let is_suspicious = self
            .suspicious_user_check
            .is_suspicious(&feature_flags, req)?;

        // Adjust fields
        let mut chat_request: ChatRequest = front_chat_request.try_into()?;
        chat_request.model_name.clone_from(&mi.name);
        chat_request.is_suspicious = is_suspicious;

        let chat_cbf = acquire_cbf_lock().await;
        let is_tripped = match chat_cbf {
            Ok(mut cbf) => {
                cbf.set_failure_threshold(CHAT_CBF_FAILURE_THRESHOLD.get_from(&feature_flags));
                cbf.set_minimum_requests(CHAT_CBF_MINIMUM_REQUESTS.get_from(&feature_flags) as u64);
                cbf.set_failure_window(
                    CHAT_CBF_FAILURE_WINDOW_SECONDS.get_from(&feature_flags) as u64
                );
                cbf.set_reset_threshold(
                    CHAT_CBF_RESET_THRESHOLD_SECONDS.get_from(&feature_flags) as u64
                );
                cbf.is_tripped()
            }
            Err(_) => false,
        };

        // If CBF is tripped, we avoid making requests to defalt model and immediately go to fallback
        let response = match front_chat_model.is_empty() && is_tripped {
            true => Err(tonic::Status::unavailable("CBF bypass for defult model")),
            false => com.chat(&request_context, chat_request.clone()).await,
        };

        let result = match response {
            Ok(result) => {
                if front_chat_model.is_empty() {
                    let chat_cbf = acquire_cbf_lock().await;
                    if let Ok(mut cbf) = chat_cbf {
                        cbf.record_request(true);
                    };
                }
                result
            }
            Err(e)
                if front_chat_mode == public_api_proto::ChatMode::Agent
                    || front_chat_mode == public_api_proto::ChatMode::Memories
                    || front_chat_mode == public_api_proto::ChatMode::MemoriesCompression
                    || front_chat_mode == public_api_proto::ChatMode::Orientation =>
            {
                tracing::warn!("Chat request failed with {:?}, no fallback model", e);
                return Err(e);
            }
            Err(e)
                if matches!(e.code(), tonic::Code::Unavailable) && front_chat_model.is_empty() =>
            {
                let chat_cbf = acquire_cbf_lock().await;
                if let Ok(mut cbf) = chat_cbf {
                    cbf.record_request(false);
                };
                //  If model is unavailable (usually overloaded), try a fallback model
                let relevant_model_flag: &feature_flags::StringFlag = match front_chat_mode {
                    public_api_proto::ChatMode::Agent => &AGENT_CHAT_FALLBACK_MODEL_FLAG,
                    public_api_proto::ChatMode::RemoteAgent => {
                        // Try to get remote agent flag, fallback to agent flag if empty
                        let remote_agent_fallback_flag =
                            REMOTE_AGENT_CHAT_FALLBACK_MODEL_FLAG.get_from(&feature_flags);
                        if !remote_agent_fallback_flag.is_empty() {
                            &REMOTE_AGENT_CHAT_FALLBACK_MODEL_FLAG
                        } else {
                            &AGENT_CHAT_FALLBACK_MODEL_FLAG
                        }
                    }
                    public_api_proto::ChatMode::Memories => &MEMORIES_FALLBACK_MODEL_FLAG,
                    public_api_proto::ChatMode::MemoriesCompression => {
                        &MEMORIES_COMPRESSION_FALLBACK_MODEL_FLAG
                    }
                    public_api_proto::ChatMode::Orientation => &ORIENTATION_FALLBACK_MODEL_FLAG,
                    public_api_proto::ChatMode::Chat => match feature_detection_flags {
                        Some(ref flags) => {
                            if flags.support_raw_output.unwrap_or(false) {
                                &CHAT_RAW_OUTPUT_FALLBACK_MODEL_FLAG
                            } else {
                                &CHAT_FALLBACK_MODEL_FLAG
                            }
                        }
                        None => &CHAT_FALLBACK_MODEL_FLAG,
                    },
                };
                if relevant_model_flag.get_from(&feature_flags).is_empty() {
                    tracing::warn!("Chat request failed with {:?}, no fallback model", e);
                    return Err(e);
                }
                tracing::warn!("Chat request failed with {:?}, trying fallback model", e);
                let (fallback_com, fallback_mi) = get_model::<MR>(
                    None,
                    &self.model_registry,
                    &feature_flags,
                    relevant_model_flag,
                    ModelType::Chat,
                )
                .await?;
                let fallback_com = match fallback_com {
                    Client::Chat(c) => c.clone(),
                    _ => {
                        return Err(tonic::Status::internal(
                            "Fallback model is not a chat model",
                        ))
                    }
                };
                chat_request.model_name.clone_from(&fallback_mi.name);
                fallback_com
                    .chat(&request_context, chat_request.clone())
                    .await?
            }
            Err(e) => return Ok(chat_status_to_response(&e)),
        };
        let error_code = ResponseStatusCode::try_from(result.clone());
        match error_code {
            Ok(ResponseStatusCode::ExceedContextLength) => Ok(HttpResponse::PayloadTooLarge()
                .json(ResponseError::new("Exceed context length", None))),
            Ok(ResponseStatusCode::WasBlocked) => {
                let result = public_api_proto::ChatResponse::from(result);
                Ok(HttpResponse::Ok().json(result))
            }
            Ok(ResponseStatusCode::Ok) => {
                // Check if this is a user message
                let is_user_msg = crate::chat_utils::is_user_message(&chat_request);

                // If this is a user message, check if it has meaningful content
                if is_user_msg {
                    // Check if the response has meaningful content (tool use or non-empty text)
                    let has_meaningful_content = crate::chat_utils::has_meaningful_content(&result);

                    if has_meaningful_content {
                        tracing::info!(
                            "Emitting ChatUserMessage event for response with meaningful content"
                        );
                        // Emit the ChatUserMessage event only if there's meaningful content
                        self.emit_chat_user_message_event(
                            &request_context,
                            &tenant_info,
                            &chat_request,
                            front_chat_mode,
                            None, // No resolved model name available in this context
                        )
                        .await;
                    } else {
                        tracing::info!("Not emitting ChatUserMessage event - no meaningful content in response");
                    }
                }

                let result = public_api_proto::ChatResponse::from(result);
                Ok(HttpResponse::Ok().json(result))
            }
            Err(_) => Ok(HttpResponse::InternalServerError().finish()),
        }
    }
}

#[allow(clippy::too_many_arguments)]
async fn chat_stream<
    MR: ModelRegistry + Send + Sync + 'static,
    CNC: ContentManagerClient + Send + Sync + 'static,
>(
    data: web::Data<Handler<MR, CNC>>,
    req: &HttpRequest,
    user: &User,
    tenant_info: &TenantInfo,
    request_context: &RequestContext,
    chat_request: ChatRequest,
    feature_detection_flags: Option<public_api_proto::ChatFeatureDetectionFlags>,
    front_chat_mode: public_api_proto::ChatMode,
) -> Result<Receiver<tonic::Result<chat::ChatResponse>>, tonic::Status> {
    tracing::info!(
        "chat iteration mode={:?} model={:?}",
        front_chat_mode,
        chat_request.model_name.clone(),
    );

    let feature_flags = data.get_feature_flags(user, tenant_info, Some(req))?;
    gate_on_circuit_breaker(&CB_CHAT_STREAM, &feature_flags, req, tenant_info)?;

    if user.subscription.is_none() {
        tracing::info!("User {} has no subscription", user.user_id);
    } else {
        tracing::info!("User subscription status: {:?}", user.subscription);
    }

    // Check subscription status if both feature flags are enabled
    // Send the user an error or inject a message if the subscription is inactive
    if CHECK_SUBSCRIPTION_STATUS.get_from(&feature_flags) {
        if let Some(get_token_info_response::Subscription::InactiveSubscription(_)) =
            &user.subscription
        {
            tracing::info!(
                "Checking for inactive subscription for user {}",
                user.user_id
            );
            // For Chat and Agent modes, inject a message if the flag is enabled
            if (front_chat_mode == public_api_proto::ChatMode::Chat
                || front_chat_mode == public_api_proto::ChatMode::Agent
                || front_chat_mode == public_api_proto::ChatMode::RemoteAgent)
                && INACTIVE_SUBSCRIPTION_DISCLAIMER.get_from(&feature_flags)
            {
                tracing::info!(
                    "Injecting inactive subscription message for user {}",
                    user.opaque_user_id.user_id
                );

                TRIAL_ENDED_MESSAGE_COUNTER
                    .with_label_values(&[tenant_info.metrics_tenant_name()])
                    .inc();

                let account_info =
                    if let Some(email) = user.user_email.as_ref().filter(|e| !e.is_empty()) {
                        format!(" for account {}", email)
                    } else {
                        "".to_string()
                    };

                let user_agent = req
                    .headers()
                    .get("user-agent")
                    .map(|h| h.to_str().unwrap_or(""))
                    .unwrap_or("");
                let logout_link = if user_agent.starts_with("Augment.vscode-augment") {
                    "[sign out](command:vscode-augment.signOut)"
                } else {
                    "sign out"
                };

                let formatted_message = INACTIVE_SUBSCRIPTION_DISCLAIMER_MKDOWN_MESSAGE
                    .replace("{account_info}", &account_info)
                    .replace("{logout_link}", logout_link);

                return Ok(create_injected_response(formatted_message).await);
            } else {
                // For other modes, return a 402 Payment Required error
                // We need to return a tonic::Status error here, but we are using the const SUBSCRIPTION_EXPIRED_INTERNAL_ERROR
                // so it can be mapped to a HTTP 402 error in chat_status_to_response
                return Err(tonic::Status::new(
                    tonic::Code::PermissionDenied,
                    SUBSCRIPTION_EXPIRED_INTERNAL_ERROR.to_string(),
                ));
            }
        }
    }

    if USER_SUSPENSION_ENABLED.get_from(&feature_flags) && !user.suspensions.is_empty() {
        tracing::info!("Checking for user suspensions for user {}", user.user_id);
        // For Chat and Agent modes, inject a message if the flag is enabled
        if front_chat_mode == public_api_proto::ChatMode::Chat
            || front_chat_mode == public_api_proto::ChatMode::Agent
            || front_chat_mode == public_api_proto::ChatMode::RemoteAgent
        {
            tracing::info!(
                "Injecting user suspension message for user {}",
                user.user_id
            );

            let account_info =
                if let Some(email) = user.user_email.as_ref().filter(|e| !e.is_empty()) {
                    format!(" {}", email)
                } else {
                    "".to_string()
                };

            let reason = user.suspensions[0].suspension_type;
            let formatted_message =
                match auth_entities_proto::auth_entities::UserSuspensionType::try_from(reason) {
                    Ok(auth_entities_proto::auth_entities::UserSuspensionType::FreeTrialAbuse) => {
                        USER_SUSPENSION_FREETRIAL_DISCLAIMER_MKDOWN_MESSAGE
                            .replace("{account_info}", &account_info)
                    }
                    Ok(_) => USER_SUSPENSION_DISCLAIMER_MKDOWN_MESSAGE
                        .replace("{account_info}", &account_info),
                    Err(_) => {
                        return Err(tonic::Status::internal(format!(
                            "Invalid suspension type: {}",
                            reason
                        )))
                    }
                };

            return Ok(create_injected_response(formatted_message).await);
        } else {
            // We need to return a tonic::Status error here, but we are using the const USER_SUSPENDED_INTERNAL_ERROR
            // so it can be mapped to a HTTP 402 error in chat_status_to_response
            return Err(tonic::Status::new(
                tonic::Code::PermissionDenied,
                USER_SUSPENDED_INTERNAL_ERROR.to_string(),
            ));
        }
    }

    if OUT_OF_USAGE_CREDITS_DISCLAIMER.get_from(&feature_flags) {
        if let Some(get_token_info_response::Subscription::ActiveSubscription(active)) =
            &user.subscription
        {
            if active.usage_balance_depleted {
                if front_chat_mode == public_api_proto::ChatMode::Chat
                    || front_chat_mode == public_api_proto::ChatMode::Agent
                    || front_chat_mode == public_api_proto::ChatMode::RemoteAgent
                {
                    tracing::info!(
                        "Injecting out of usage credits message for user {}",
                        user.opaque_user_id.user_id
                    );

                    let account_info =
                        if let Some(email) = user.user_email.as_ref().filter(|e| !e.is_empty()) {
                            format!(" for account {}", email)
                        } else {
                            "".to_string()
                        };

                    let formatted_message = OUT_OF_USAGE_CREDITS_DISCLAIMER_MKDOWN_MESSAGE
                        .replace("{account_info}", &account_info);

                    return Ok(create_injected_response(formatted_message).await);
                } else {
                    // We need to return a tonic::Status error here, but we are using the const OUT_OF_USAGE_CREDITS_INTERNAL_ERROR
                    // so it can be mapped to a HTTP 402 error in chat_status_to_response
                    return Err(tonic::Status::new(
                        tonic::Code::PermissionDenied,
                        OUT_OF_USAGE_CREDITS_INTERNAL_ERROR.to_string(),
                    ));
                }
            }
        }
    }

    // Get request metadata for metrics
    let uri = req.uri().path();
    let request_source = request_context.request_source().to_string();
    let metrics_tenant_name = tenant_info.metrics_tenant_name();

    // Collect metrics for images if present in the request
    let has_images = chat_request
        .nodes
        .iter()
        .any(|node| node.r#type == ChatRequestNodeType::Image as i32);

    let ri_request_type = match front_chat_mode {
        public_api_proto::ChatMode::Agent => request_insight::RequestType::AgentChat,
        public_api_proto::ChatMode::Memories => request_insight::RequestType::Memories,
        public_api_proto::ChatMode::MemoriesCompression => {
            request_insight::RequestType::MemoriesCompression
        }
        public_api_proto::ChatMode::Orientation => request_insight::RequestType::Orientation,
        public_api_proto::ChatMode::Chat => request_insight::RequestType::Chat,
        public_api_proto::ChatMode::RemoteAgent => request_insight::RequestType::RemoteAgentChat,
    };

    let throttle_chat_agent = match front_chat_mode {
        public_api_proto::ChatMode::Agent
        | public_api_proto::ChatMode::Memories
        | public_api_proto::ChatMode::MemoriesCompression
        | public_api_proto::ChatMode::Orientation => {
            CHAT_AGENT_THROTTLE_FLAG.get_from(&feature_flags)
        }
        public_api_proto::ChatMode::Chat => CHAT_THROTTLE_FLAG.get_from(&feature_flags),
        public_api_proto::ChatMode::RemoteAgent => {
            CHAT_REMOTE_AGENT_THROTTLE_FLAG.get_from(&feature_flags)
        }
    };
    if throttle_chat_agent {
        let fill_rate_per_second = match front_chat_mode {
            public_api_proto::ChatMode::Agent
            | public_api_proto::ChatMode::Memories
            | public_api_proto::ChatMode::MemoriesCompression
            | public_api_proto::ChatMode::Orientation => {
                CHAT_AGENT_THROTTLE_FILL_RATE_PER_SEC_FLAG.get_from(&feature_flags)
            }
            public_api_proto::ChatMode::Chat => {
                CHAT_THROTTLE_FILL_RATE_PER_SEC_FLAG.get_from(&feature_flags)
            }
            public_api_proto::ChatMode::RemoteAgent => {
                CHAT_REMOTE_AGENT_THROTTLE_FILL_RATE_PER_SEC_FLAG.get_from(&feature_flags)
            }
        };
        let capacity = match front_chat_mode {
            public_api_proto::ChatMode::Agent
            | public_api_proto::ChatMode::Memories
            | public_api_proto::ChatMode::MemoriesCompression
            | public_api_proto::ChatMode::Orientation => {
                CHAT_AGENT_THROTTLE_CAPACITY_FLAG.get_from(&feature_flags) as f64
            }
            public_api_proto::ChatMode::Chat => {
                CHAT_THROTTLE_CAPACITY_FLAG.get_from(&feature_flags) as f64
            }
            public_api_proto::ChatMode::RemoteAgent => {
                CHAT_REMOTE_AGENT_THROTTLE_CAPACITY_FLAG.get_from(&feature_flags) as f64
            }
        };
        let endpoint = match front_chat_mode {
            public_api_proto::ChatMode::Agent
            | public_api_proto::ChatMode::Memories
            | public_api_proto::ChatMode::MemoriesCompression
            | public_api_proto::ChatMode::Orientation => "agent_chat",
            public_api_proto::ChatMode::Chat => "chat",
            public_api_proto::ChatMode::RemoteAgent => "remote_agent_chat",
        };
        if let Err(e) = data
            .should_throttle(&user.user_id, endpoint, fill_rate_per_second, capacity, 1.0)
            .await
        {
            // Update metrics with error status if request is throttled
            if has_images {
                CHAT_IMAGE_REQUEST_COLLECTOR
                    .with_label_values(&[uri, "throttled", &request_source, metrics_tenant_name])
                    .inc();
            }
            return Err(e);
        }
    }

    let metadata = extract_request_metadata(request_context, ri_request_type, user, req);
    data.request_insight_publisher
        .record_request_metadata(request_context, tenant_info, metadata)
        .await;

    // Check if the user has an Orb subscription, which should bypass fair use limits
    let has_orb_subscription = match &user.subscription {
        Some(get_token_info_response::Subscription::ActiveSubscription(active)) => {
            active.billing_method == auth_entities_proto::auth_entities::BillingMethod::Orb as i32
        }
        Some(get_token_info_response::Subscription::Trial(trial)) => {
            trial.billing_method == auth_entities_proto::auth_entities::BillingMethod::Orb as i32
        }
        Some(get_token_info_response::Subscription::InactiveSubscription(inactive)) => {
            inactive.billing_method == auth_entities_proto::auth_entities::BillingMethod::Orb as i32
        }
        _ => false,
    };

    tracing::info!(
        "should_check_fair_use_limit (!has_orb_subscription): {}",
        !has_orb_subscription
    );

    // Users with Orb subscriptions are limited by Orb and not by Fair Use.
    if !has_orb_subscription
        && check_daily_request_limit(
            &user.opaque_user_id.user_id,
            &feature_flags,
            front_chat_mode,
            request_context,
            &data.memstore_client,
            metrics_tenant_name,
        )
        .await
    {
        // Record the daily request limit exceeded event
        let max_requests = match front_chat_mode {
            public_api_proto::ChatMode::Agent
            | public_api_proto::ChatMode::Memories
            | public_api_proto::ChatMode::MemoriesCompression
            | public_api_proto::ChatMode::Chat
            | public_api_proto::ChatMode::Orientation => {
                CHAT_AGENT_DAILY_LIMIT_MAX_REQUESTS_FLAG.get_from(&feature_flags)
            }
            public_api_proto::ChatMode::RemoteAgent => {
                CHAT_REMOTE_AGENT_DAILY_LIMIT_MAX_REQUESTS_FLAG.get_from(&feature_flags)
            }
        };
        let daily_limit_event = request_insight::RequestEvent {
            time: Some(prost_wkt_types::Timestamp::from(
                std::time::SystemTime::now(),
            )),
            event: Some(
                request_insight::request_event::Event::DailyRequestLimitExceeded(
                    request_insight::DailyRequestLimitExceeded {
                        opaque_user_id: Some(auth_entities::UserId {
                            user_id: user.opaque_user_id.user_id.clone(),
                            user_id_type: user.opaque_user_id.user_id_type,
                        }),
                        limit: max_requests as i32,
                    },
                ),
            ),
            event_id: Some(uuid::Uuid::new_v4().to_string()),
        };

        data.request_insight_publisher
            .record_request_events(request_context, tenant_info, vec![daily_limit_event])
            .await;

        // Create a response with the limit message
        let limit_message = match front_chat_mode {
            public_api_proto::ChatMode::Agent
            | public_api_proto::ChatMode::Memories
            | public_api_proto::ChatMode::MemoriesCompression
            | public_api_proto::ChatMode::Chat
            | public_api_proto::ChatMode::Orientation => {
                CHAT_AGENT_DAILY_LIMIT_MESSAGE_FLAG.get_from(&feature_flags)
            }
            public_api_proto::ChatMode::RemoteAgent => {
                CHAT_REMOTE_AGENT_DAILY_LIMIT_MESSAGE_FLAG.get_from(&feature_flags)
            }
        };
        return Ok(create_injected_response(limit_message).await);
    }

    // Check if this is a user message
    let is_user_msg = crate::chat_utils::is_user_message(&chat_request);

    if has_images {
        CHAT_IMAGE_REQUEST_COLLECTOR
            .with_label_values(&[uri, "processing", &request_source, metrics_tenant_name])
            .inc();

        let image_count = chat_request
            .nodes
            .iter()
            .filter(|node| node.r#type == ChatRequestNodeType::Image as i32)
            .count();
        CHAT_IMAGE_COUNT_COLLECTOR
            .with_label_values(&[uri, "processing", &request_source, metrics_tenant_name])
            .inc_by(image_count as u64);

        // Process each image node for token count metrics
        for node in chat_request.nodes.iter() {
            if node.r#type == ChatRequestNodeType::Image as i32 {
                if let Some(image_node) = &node.image_node {
                    // Try to decode base64 and get image dimensions for token count
                    match base64_engine.decode(&image_node.image_data) {
                        Ok(image_bytes) => match image::load_from_memory(&image_bytes) {
                            Ok(img) => {
                                let height = img.height();
                                let width = img.width();
                                let token_count = (width as f64 * height as f64) / 750.0;

                                CHAT_IMAGE_TOKEN_COUNT_COLLECTOR
                                    .with_label_values(&[
                                        uri,
                                        "processing",
                                        &request_source,
                                        metrics_tenant_name,
                                    ])
                                    .observe(token_count);
                            }
                            Err(e) => {
                                tracing::warn!(
                                    "Failed to decode image for token count calculation: {}",
                                    e
                                );
                            }
                        },
                        Err(e) => {
                            tracing::warn!("Failed to decode base64 image data: {}", e);
                        }
                    }
                }
            }
        }
    }

    let relevant_model_flag: &feature_flags::StringFlag = match front_chat_mode {
        public_api_proto::ChatMode::Agent => &AGENT_CHAT_MODEL_FLAG,
        // Remote agent uses the same model as IDE agent
        public_api_proto::ChatMode::RemoteAgent => {
            // Try to get remote agent flag, fallback to agent flag if empty
            let remote_agent_model_flag = REMOTE_AGENT_CHAT_MODEL_FLAG.get_from(&feature_flags);
            if !remote_agent_model_flag.is_empty() {
                &REMOTE_AGENT_CHAT_MODEL_FLAG
            } else {
                &AGENT_CHAT_MODEL_FLAG
            }
        }
        public_api_proto::ChatMode::Memories => &MEMORIES_FALLBACK_MODEL_FLAG,
        public_api_proto::ChatMode::MemoriesCompression => {
            &MEMORIES_COMPRESSION_FALLBACK_MODEL_FLAG
        }
        public_api_proto::ChatMode::Orientation => &ORIENTATION_FALLBACK_MODEL_FLAG,
        public_api_proto::ChatMode::Chat => match feature_detection_flags {
            Some(ref flags) => {
                // Evaluate flags in order of priority, usually new to old
                if flags.support_raw_output.unwrap_or(false) {
                    &CHAT_RAW_OUTPUT_MODEL_FLAG
                } else {
                    &CHAT_MODEL_FLAG
                }
            }
            None => &CHAT_MODEL_FLAG,
        },
    };

    let is_suspicious = data
        .suspicious_user_check
        .is_suspicious(&feature_flags, req)?;

    let (com, mi) = get_model::<MR>(
        Some(&chat_request.model_name),
        &data.model_registry,
        &feature_flags,
        relevant_model_flag,
        ModelType::Chat,
    )
    .await?;

    let com = match com {
        Client::Chat(c) => c.clone(),
        _ => return Err(tonic::Status::internal("Model is not a chat model")),
    };
    // Adjust fields
    let mut final_request: ChatRequest = chat_request.clone();
    final_request.model_name.clone_from(&mi.name);
    final_request.is_suspicious = is_suspicious;

    // Get the original stream
    let original_stream = com
        .chat_stream(request_context, final_request.clone())
        .await?;

    // If this is not a user message, just return the original stream
    if !is_user_msg {
        return Ok(original_stream);
    }

    // For user messages, create a wrapper stream that checks each response
    // and emits the event as soon as meaningful content is found
    let (tx, rx) = tokio::sync::mpsc::channel(100);
    let request_context_clone = request_context.clone();
    let tenant_info_clone = tenant_info.clone();
    let chat_request_clone = chat_request.clone();
    let front_chat_mode_clone = front_chat_mode;
    let request_insight_publisher_clone = data.request_insight_publisher.clone();
    let resolved_model_name = mi.name.clone();

    // Spawn a task to process the stream
    tokio::spawn(async move {
        let mut original_stream = original_stream;
        let mut should_emit_event = false;

        // Process each response as it arrives
        while let Some(result) = original_stream.recv().await {
            // Check if this response has meaningful content and set flag if needed
            if !should_emit_event {
                if let Ok(response) = &result {
                    if crate::chat_utils::has_meaningful_content(response) {
                        tracing::info!(
                            "Found meaningful content, will emit event after stream completes"
                        );
                        should_emit_event = true;
                    }
                }
            }

            // Forward the response to our new channel
            if tx.send(result).await.is_err() {
                break;
            }
        }

        // Emit event after the stream is finished if we found meaningful content
        if should_emit_event {
            tracing::info!("Emitting ChatUserMessage event after stream completed");
            crate::chat_utils::emit_chat_user_message_event(
                &request_insight_publisher_clone,
                &request_context_clone,
                &tenant_info_clone,
                &chat_request_clone,
                front_chat_mode_clone,
                Some(&resolved_model_name),
            )
            .await;
        } else {
            tracing::info!("Not emitting ChatUserMessage event - no meaningful content in stream");
        }
    });

    tracing::info!("chat_stream (canonical)");
    Ok(rx)
}

/// Creates an injected response for various account status messages
///
/// This helper function creates a standardized chat response with an injected message
/// for subscription status, user suspension, or credit depletion scenarios.
async fn create_injected_response(message: String) -> Receiver<tonic::Result<chat::ChatResponse>> {
    // Create a channel to send the message
    let (tx, rx) = tokio::sync::mpsc::channel(1);

    let nodes = vec![
        // Create a raw response node so that the model does not get sent our injected message
        chat::ChatResultNode {
            id: 0,
            r#type: chat::ChatResultNodeType::RawResponse as i32,
            content: "".to_string(),
            tool_use: None,
        },
        // Add the injected message to a separate node
        chat::ChatResultNode {
            id: 1,
            r#type: chat::ChatResultNodeType::MainTextFinished as i32,
            content: message.clone(),
            tool_use: None,
        },
    ];

    // Create the response
    let response = chat::ChatResponse {
        text: message,
        nodes,
        stop_reason: Some(chat::ChatStopReason::EndTurn as i32),
        ..Default::default()
    };

    // Send the response through the channel
    if let Err(e) = tx.send(Ok(response)).await {
        tracing::error!("Failed to send disclaimer message: {}", e);
    }

    // Close the channel
    drop(tx);

    // Return the receiver
    rx
}

/// Adds a trial expiration disclaimer to a chat response on the last node if it has type MAIN_TEXT_FINISHED and stop_reason is END_TURN
pub async fn maybe_add_trial_expiration_disclaimer(
    response: &mut ChatResponse,
    user: &User,
    metrics_tenant_name: &str,
    days_remaining: i64,
) -> bool {
    for node in response.nodes.iter_mut().rev() {
        if node.r#type == chat::ChatResultNodeType::MainTextFinished as i32
            && response.stop_reason == Some(chat::ChatStopReason::EndTurn as i32)
        {
            // Log the trial expiration disclaimer
            tracing::info!(
                "Injecting trial expiration disclaimer for user {} with {} days remaining",
                user.opaque_user_id.user_id,
                days_remaining
            );

            TRIAL_ENDING_DISCLAIMER_COUNTER
                .with_label_values(&[metrics_tenant_name])
                .inc();

            let disclaimer = TRIAL_EXPIRATION_DISCLAIMER_MKDOWN_MESSAGE
                .replace("{days_remaining}", &days_remaining.to_string());

            // Add the disclaimer to both the response text and node content
            if !node.content.is_empty() {
                // Add to node content if it's not empty
                node.content = format!("{}{}", node.content, disclaimer);
            }
            // Also add to the response text field
            response.text = format!("{}{}", response.text, disclaimer);

            return true;
        }
    }

    false
}

// Helper function to modify chat responses with trial expiration disclaimer
async fn modify_chat_response(
    receiver: Receiver<tonic::Result<ChatResponse>>,
    user: &User,
    metrics_tenant_name: &str,
    feature_flags: &feature_flags::FeatureFlagsServiceHandle,
) -> Receiver<tonic::Result<ChatResponse>> {
    // Check if the feature is enabled
    if !TRIAL_EXPIRATION_DISCLAIMER.get_from(feature_flags) {
        return receiver;
    }

    // Get the days remaining from the user's subscription
    let days_remaining = match user
        .is_subscription_expiring_soon(TRIAL_EXPIRATION_DAYS_THRESHOLD.get_from(feature_flags))
    {
        Some(days) => days,
        None => return receiver,
    };

    // Check if we've already alerted this user in the last 24 hours
    let now = chrono::Utc::now();

    let should_alert = {
        let alerted_data = TRIAL_EXPIRATION_ALERTED_USERS.lock().await;

        if let Some(last_alert_time) = alerted_data.get(&user.user_id) {
            // Check if it's been more than 24 hours since the last alert
            let hours_since_last_alert = now.signed_duration_since(*last_alert_time).num_hours();
            hours_since_last_alert >= 24
        } else {
            // User has never been alerted before
            true
        }
    };

    if !should_alert {
        return receiver;
    }

    // Create a new channel for the modified responses
    let (tx, rx) = tokio::sync::mpsc::channel(100);
    let user_clone = user.clone();
    let metrics_tenant_name = metrics_tenant_name.to_string();

    tokio::spawn(async move {
        let mut receiver = receiver;

        while let Some(result) = receiver.recv().await {
            match result {
                Ok(mut response) => {
                    let disclaimer_added = maybe_add_trial_expiration_disclaimer(
                        &mut response,
                        &user_clone,
                        &metrics_tenant_name,
                        days_remaining,
                    )
                    .await;

                    if disclaimer_added {
                        // Add user to the alerted set with current timestamp
                        let mut alerted_data = TRIAL_EXPIRATION_ALERTED_USERS.lock().await;
                        alerted_data.insert(user_clone.user_id.clone(), now);
                    }

                    if (tx.send(Ok(response)).await).is_err() {
                        break;
                    }
                }
                Err(e) => {
                    if (tx.send(Err(e)).await).is_err() {
                        break;
                    }
                }
            }
        }
    });

    rx
}

pub async fn chat_stream_api_auth<
    MR: ModelRegistry + Send + Sync + 'static,
    CNC: ContentManagerClient + Send + Sync + 'static,
>(
    data: web::Data<Handler<MR, CNC>>,
    req: HttpRequest,
    item: web::Json<public_api_proto::ChatRequest>,
    root_span: RootSpan,
) -> HttpResponse {
    let front_chat_request: public_api_proto::ChatRequest = item.into_inner();
    let mut front_chat_model: String = front_chat_request.model.clone().unwrap_or_default();
    let front_chat_mode = public_api_proto::ChatMode::try_from(front_chat_request.mode)
        .unwrap_or(public_api_proto::ChatMode::Chat);

    let Ok((user, tenant_info, request_context, _start_time)) = request_context_from_req(&req)
    else {
        return HttpResponse::InternalServerError().finish();
    };
    root_span.record("tenant_name", &tenant_info.tenant_name);
    root_span.record("opaque_user_id", &user.opaque_user_id.user_id);

    tracing::info!(
        "chat request mode={:?} model={:?}",
        front_chat_mode,
        front_chat_model.clone(),
    );

    let request_insight_publisher: Arc<dyn RequestInsightPublisher + Send + Sync> =
        data.request_insight_publisher.clone();
    let feature_flags = match data.get_feature_flags(&user, &tenant_info, Some(&req)) {
        Ok(f) => f,
        Err(e) => {
            tracing::error!("Error getting feature flags {:?}", e);
            return status_to_response(&e);
        }
    };
    let feature_detection_flags: Option<public_api_proto::ChatFeatureDetectionFlags> =
        front_chat_request.feature_detection_flags;
    let chat_request: ChatRequest = match front_chat_request.clone().try_into() {
        Ok(r) => r,
        Err(e) => {
            tracing::error!("Error converting to chat request {:?}", e);
            return status_to_response(&e);
        }
    };

    // If model name override is false, replace model name with empty string
    let enable_model_name: bool = ENABLE_MODEL_NAME.get_from(&feature_flags);
    // Note(yuri): gemini exception is to support older clients that send model name (Apr '25)
    if !enable_model_name
        && !front_chat_model.is_empty()
        && front_chat_model != "gemini-2-flash-001-simple-port"
    {
        tracing::info!(
            "Overriding model name {:?} to default",
            front_chat_model.clone()
        );
        front_chat_model = "".to_string();
    }

    let latency_injection = match front_chat_mode {
        public_api_proto::ChatMode::Chat => 0.0,
        public_api_proto::ChatMode::Memories => 0.0,
        public_api_proto::ChatMode::MemoriesCompression => 0.0,
        public_api_proto::ChatMode::Orientation => 0.0,
        public_api_proto::ChatMode::RemoteAgent => 0.0,
        public_api_proto::ChatMode::Agent => {
            if !crate::chat_utils::is_user_message(&chat_request) {
                AGENT_CONTINUATION_LATENCY_INJECTION_FLAG.get_from(&feature_flags)
            } else {
                0.0
            }
        }
    };
    if latency_injection > 0.0 {
        tokio::time::sleep(Duration::from_secs_f64(latency_injection)).await;
    }

    #[allow(clippy::too_many_arguments)]
    async fn handle_chat_stream_iteration<
        MR: ModelRegistry + Send + Sync + 'static,
        CNC: ContentManagerClient + Send + Sync + 'static,
    >(
        data: web::Data<Handler<MR, CNC>>,
        req: &HttpRequest,
        user: &User,
        tenant_info: &TenantInfo,
        request_context: &RequestContext,
        chat_request: ChatRequest,
        feature_detection_flags: Option<public_api_proto::ChatFeatureDetectionFlags>,
        front_chat_mode: public_api_proto::ChatMode,
        request_insight_publisher: Arc<dyn RequestInsightPublisher + Send + Sync>,
        feature_flags: feature_flags::FeatureFlagsServiceHandle,
    ) -> Result<HttpResponse, tonic::Status> {
        let receiver_result: Result<Receiver<Result<ChatResponse, tonic::Status>>, tonic::Status> =
            chat_stream(
                data,
                req,
                user,
                tenant_info,
                request_context,
                chat_request,
                feature_detection_flags,
                front_chat_mode,
            )
            .await;

        match receiver_result {
            Ok(receiver) => {
                let final_receiver = if front_chat_mode == public_api_proto::ChatMode::Chat
                    || front_chat_mode == public_api_proto::ChatMode::Agent
                {
                    // Create a modified receiver for Chat and Agent modes only with potential trial expiration disclaimer
                    modify_chat_response(
                        receiver,
                        user,
                        tenant_info.metrics_tenant_name(),
                        &feature_flags,
                    )
                    .await
                } else {
                    // Use the original receiver for other modes
                    receiver
                };

                let enable_heartbeat = CHAT_HEARTBEAT_STREAM_FLAG.get_from(&feature_flags);
                Ok(streaming_http_response_from_receiver::<
                    ChatResponse,
                    public_api_proto::ChatResponse,
                >(
                    "chat-stream",
                    Ok(final_receiver),
                    request_context.clone(),
                    tenant_info.clone(),
                    request_insight_publisher.clone(),
                    &feature_flags,
                    enable_heartbeat,
                )
                .await)
            }
            Err(e) => Err(e),
        }
    }

    let mut models_to_try: Vec<String> = vec![front_chat_model.clone()];
    // If the client didn't specify a model, we can add fallback/s to the vector
    // No fallback model for Agent yet
    if front_chat_model.is_empty() {
        let fallback_flag: &feature_flags::StringFlag = match front_chat_mode {
            public_api_proto::ChatMode::Agent => &AGENT_CHAT_FALLBACK_MODEL_FLAG,
            public_api_proto::ChatMode::RemoteAgent => &AGENT_CHAT_FALLBACK_MODEL_FLAG,
            public_api_proto::ChatMode::Memories => &MEMORIES_FALLBACK_MODEL_FLAG,
            public_api_proto::ChatMode::MemoriesCompression => {
                &MEMORIES_COMPRESSION_FALLBACK_MODEL_FLAG
            }
            public_api_proto::ChatMode::Orientation => &ORIENTATION_FALLBACK_MODEL_FLAG,
            public_api_proto::ChatMode::Chat => match feature_detection_flags {
                // Evaluate flags in order of priority, usually new to old
                Some(ref flags) => {
                    if flags.support_raw_output.unwrap_or(false) {
                        &CHAT_RAW_OUTPUT_FALLBACK_MODEL_FLAG
                    } else {
                        &CHAT_FALLBACK_MODEL_FLAG
                    }
                }
                None => &CHAT_FALLBACK_MODEL_FLAG,
            },
        };
        let fallback_models = fallback_flag.get_from(&feature_flags);
        if !fallback_models.is_empty() {
            models_to_try.extend(fallback_models.split(',').map(|s| s.trim().to_string()));
        }
        let chat_cbf = acquire_cbf_lock().await;
        if let Ok(mut cbf) = chat_cbf {
            cbf.set_failure_threshold(CHAT_CBF_FAILURE_THRESHOLD.get_from(&feature_flags));
            cbf.set_minimum_requests(CHAT_CBF_MINIMUM_REQUESTS.get_from(&feature_flags) as u64);
            cbf.set_failure_window(CHAT_CBF_FAILURE_WINDOW_SECONDS.get_from(&feature_flags) as u64);
            cbf.set_reset_threshold(
                CHAT_CBF_RESET_THRESHOLD_SECONDS.get_from(&feature_flags) as u64
            );
            if cbf.is_tripped() {
                tracing::warn!("CBF is tripped, prioritizing fallback");
                models_to_try.rotate_left(1);
            }
        }
    }
    let retry_policy = data.get_retry_policy_from_flags(
        &req,
        &CHAT_RETRY_DELAY_MS_FLAG,
        &CHAT_RETRY_MAX_DELAY_MS_FLAG,
        &CHAT_MAX_RETRIES_FLAG,
    );
    // Prepare labels for retry counter
    let uri = req.uri().path();
    let metrics_tenant_name = req
        .extensions()
        .get::<TenantInfo>()
        .map(|t| t.metrics_tenant_name())
        .unwrap_or("unknown")
        .to_string();
    let request_source = req
        .extensions()
        .get::<RequestContext>()
        .map(|s| s.request_source().to_string())
        .unwrap_or("unknown".to_string());

    let retry_result = retry(
        retry_policy.clone(),
        || async {
            let mut num_attempt = 1;
            for model_to_try in &models_to_try {
                let new_chat_request = ChatRequest {
                    model_name: model_to_try.clone(),
                    ..chat_request.clone()
                };
                let handler_result = handle_chat_stream_iteration(
                    data.clone(),
                    &req,
                    &user,
                    &tenant_info,
                    &request_context,
                    new_chat_request,
                    feature_detection_flags,
                    front_chat_mode,
                    request_insight_publisher.clone(),
                    feature_flags.clone(),
                )
                .await;
                match handler_result {
                    Ok(http_response) => {
                        return {
                            if model_to_try.is_empty() {
                                let chat_cbf = acquire_cbf_lock().await;
                                if let Ok(mut cbf) = chat_cbf {
                                    cbf.record_request(true);
                                };
                            }
                            Ok(http_response)
                        }
                    }
                    Err(e)
                        if (matches!(e.code(), tonic::Code::Unavailable)
                            || matches!(e.code(), tonic::Code::ResourceExhausted))
                            && num_attempt < models_to_try.len() =>
                    {
                        if model_to_try.is_empty() {
                            let chat_cbf = acquire_cbf_lock().await;
                            if let Ok(mut cbf) = chat_cbf {
                                cbf.record_request(false);
                            };
                        }
                        tracing::warn!(
                            "Chat request failed with {:?}, attempting fallback",
                            e.clone()
                        );
                        HANDLER_RETRY_COLLECTOR
                            .with_label_values(&[
                                uri,
                                &e.code().to_string(),
                                &request_source,
                                &metrics_tenant_name,
                            ])
                            .inc();
                        num_attempt += 1;
                        continue;
                    }
                    Err(e) => {
                        tracing::warn!(
                            "Chat request failed with {:?} after {} fallbacks",
                            e.clone(),
                            num_attempt - 1
                        );
                        return Err(e);
                    }
                }
            }
            Err(tonic::Status::internal("No models to try"))
        },
        |e| {
            tracing::warn!(
                "Chat request failed on {} models, retrying all",
                models_to_try.len()
            );
            HANDLER_RETRY_COLLECTOR
                .with_label_values(&[
                    uri,
                    &e.code().to_string(),
                    &request_source,
                    &metrics_tenant_name,
                ])
                .inc();
        },
    )
    .await;
    match retry_result {
        Ok(http_response) => http_response,
        Err(e) => chat_status_to_response(&e),
    }
}

pub async fn generate_commit_message_stream_api_auth<
    MR: ModelRegistry + Send + Sync + 'static,
    CNC: ContentManagerClient + Send + Sync + 'static,
>(
    data: web::Data<Handler<MR, CNC>>,
    req: HttpRequest,
    item: web::Json<public_api_proto::GenerateCommitMessageRequest>,
    root_span: RootSpan,
) -> HttpResponse {
    let Ok((user, tenant_info, request_context, _start_time)) = request_context_from_req(&req)
    else {
        return HttpResponse::InternalServerError().finish();
    };
    root_span.record("tenant_name", &tenant_info.tenant_name);
    root_span.record("opaque_user_id", &user.opaque_user_id.user_id);
    let request_insight_publisher = data.request_insight_publisher.clone();
    let chat_request = match item.into_inner().try_into() {
        Ok(r) => r,
        Err(e) => {
            tracing::error!("Error converting to chat request {:?}", e);
            return status_to_response(&e);
        }
    };
    let data_clone = data.clone();
    let receiver_result: Result<Receiver<Result<ChatResponse, tonic::Status>>, tonic::Status> =
        chat_stream(
            data,
            &req,
            &user,
            &tenant_info,
            &request_context,
            chat_request,
            None,
            public_api_proto::ChatMode::Chat,
        )
        .await;
    let feature_flags = match data_clone.get_feature_flags(&user, &tenant_info, Some(&req)) {
        Ok(flags) => flags,
        Err(_) => return HttpResponse::InternalServerError().finish(),
    };
    let enable_heartbeat = CHAT_HEARTBEAT_STREAM_FLAG.get_from(&feature_flags);
    streaming_http_response_from_receiver::<
        ChatResponse,
        public_api_proto::GenerateCommitMessageResponse,
    >(
        "generate-commit-message-stream",
        receiver_result,
        request_context,
        tenant_info,
        request_insight_publisher,
        &feature_flags,
        enable_heartbeat,
    )
    .await
}

pub fn register_handler_flags(registry: &feature_flags::RegistryHandle) {
    AGENT_CHAT_MODEL_FLAG
        .register(registry)
        .expect("Registering AGENT_CHAT_MODEL_FLAG");
    AGENT_CHAT_FALLBACK_MODEL_FLAG
        .register(registry)
        .expect("Registering AGENT_CHAT_FALLBACK_MODEL_FLAG");
    REMOTE_AGENT_CHAT_MODEL_FLAG
        .register(registry)
        .expect("Registering REMOTE_AGENT_CHAT_MODEL_FLAG");
    REMOTE_AGENT_CHAT_FALLBACK_MODEL_FLAG
        .register(registry)
        .expect("Registering REMOTE_AGENT_CHAT_FALLBACK_MODEL_FLAG");
    AGENT_CONTINUATION_LATENCY_INJECTION_FLAG
        .register(registry)
        .expect("Registering AGENT_CONTINUATION_LATENCY_INJECTION_FLAG");
    CHAT_MODEL_FLAG
        .register(registry)
        .expect("Registering CHAT_MODEL_FLAG");
    CHAT_RAW_OUTPUT_MODEL_FLAG
        .register(registry)
        .expect("Registering CHAT_RAW_OUTPUT_MODEL_FLAG");
    CHAT_FALLBACK_MODEL_FLAG
        .register(registry)
        .expect("Registering CHAT_FALLBACK_MODEL_FLAG");
    CHAT_RAW_OUTPUT_FALLBACK_MODEL_FLAG
        .register(registry)
        .expect("Registering CHAT_RAW_OUTPUT_FALLBACK_MODEL_FLAG");
    CHAT_CBF_FAILURE_THRESHOLD
        .register(registry)
        .expect("Registering CHAT_CBF_FAILURE_THRESHOLD");
    CHAT_CBF_MINIMUM_REQUESTS
        .register(registry)
        .expect("Registering CHAT_CBF_MINIMUM_REQUESTS");
    CHAT_CBF_FAILURE_WINDOW_SECONDS
        .register(registry)
        .expect("Registering CHAT_CBF_FAILURE_WINDOW_SECONDS");
    CHAT_CBF_RESET_THRESHOLD_SECONDS
        .register(registry)
        .expect("Registering CHAT_CBF_RESET_THRESHOLD_SECONDS");
    CHAT_AGENT_DAILY_LIMIT_ENABLED_FLAG
        .register(registry)
        .expect("Registering CHAT_AGENT_DAILY_LIMIT_ENABLED_FLAG");
    CHAT_AGENT_DAILY_LIMIT_MAX_REQUESTS_FLAG
        .register(registry)
        .expect("Registering CHAT_AGENT_DAILY_LIMIT_MAX_REQUESTS_FLAG");
    CHAT_AGENT_DAILY_LIMIT_MESSAGE_FLAG
        .register(registry)
        .expect("Registering CHAT_AGENT_DAILY_LIMIT_MESSAGE_FLAG");
    CHAT_REMOTE_AGENT_DAILY_LIMIT_ENABLED_FLAG
        .register(registry)
        .expect("Registering CHAT_REMOTE_AGENT_DAILY_LIMIT_ENABLED_FLAG");
    CHAT_REMOTE_AGENT_DAILY_LIMIT_MAX_REQUESTS_FLAG
        .register(registry)
        .expect("Registering CHAT_REMOTE_AGENT_DAILY_LIMIT_MAX_REQUESTS_FLAG");
    CHAT_REMOTE_AGENT_DAILY_LIMIT_MESSAGE_FLAG
        .register(registry)
        .expect("Registering CHAT_REMOTE_AGENT_DAILY_LIMIT_MESSAGE_FLAG");
    TRIAL_EXPIRATION_DISCLAIMER
        .register(registry)
        .expect("Registering TRIAL_EXPIRATION_DISCLAIMER");
    USER_SUSPENSION_ENABLED
        .register(registry)
        .expect("Registering USER_SUSPENSION_ENABLED");
    TRIAL_EXPIRATION_DAYS_THRESHOLD
        .register(registry)
        .expect("Registering TRIAL_EXPIRATION_DAYS_THRESHOLD");
    INACTIVE_SUBSCRIPTION_DISCLAIMER
        .register(registry)
        .expect("Registering INACTIVE_SUBSCRIPTION_DISCLAIMER");
    OUT_OF_USAGE_CREDITS_DISCLAIMER
        .register(registry)
        .expect("Registering OUT_OF_USAGE_CREDITS_DISCLAIMER");
    CHAT_HEARTBEAT_STREAM_FLAG
        .register(registry)
        .expect("Registering CHAT_HEARTBEAT_STREAM_FLAG");
}

#[cfg(test)]
mod tests {
    use std::sync::Arc;

    use crate::api_auth::User;
    use crate::api_auth::CHECK_SUBSCRIPTION_STATUS;
    use crate::augment::model_instance_config::ModelType;
    use crate::base::blob_names as blob_names_proto;
    use crate::chat;
    use crate::chat::{ChatRequest, ChatRequestNode, ChatRequestText, Exchange};
    use crate::chat::{ChatResponse, ChatResultNode, ChatResultToolUse};
    use crate::handler_utils::handle_api_auth;
    use crate::handler_utils::tests::{
        create_model_instance_config, new_root_span, setup_app_state, setup_req,
    };
    use crate::handlers_chat::INACTIVE_SUBSCRIPTION_DISCLAIMER_MKDOWN_MESSAGE;
    use crate::handlers_chat::{
        chat_stream_api_auth, generate_commit_message_stream_api_auth, modify_chat_response,
        AGENT_CHAT_MODEL_FLAG, CHAT_CBF, CHAT_CBF_FAILURE_THRESHOLD,
        CHAT_CBF_FAILURE_WINDOW_SECONDS, CHAT_CBF_MINIMUM_REQUESTS,
        CHAT_CBF_RESET_THRESHOLD_SECONDS, CHAT_FALLBACK_MODEL_FLAG, CHAT_MODEL_FLAG,
        CHAT_RAW_OUTPUT_FALLBACK_MODEL_FLAG, CHAT_RAW_OUTPUT_MODEL_FLAG,
        INACTIVE_SUBSCRIPTION_DISCLAIMER, REMOTE_AGENT_CHAT_MODEL_FLAG,
        TRIAL_EXPIRATION_ALERTED_USERS, TRIAL_EXPIRATION_DAYS_THRESHOLD,
        TRIAL_EXPIRATION_DISCLAIMER,
    };
    use crate::model_registry::tests::FakeClientFactory;
    use crate::model_registry::{DynamicModelRegistry, ModelRegistry};
    use crate::public_api_proto;
    use actix_web::body::MessageBody;
    use actix_web::{body, http, rt::pin, web, HttpResponse};
    use auth_query_client::auth_query::get_token_info_response;
    use blob_names::BlobName;
    use chrono::{Duration, Utc};
    use futures::future;
    use prost_wkt_types::Timestamp;
    use serial_test::serial;

    async fn setup() {
        // Clean history to avoid cross-test interference
        let mut cbf = CHAT_CBF.lock().await;
        cbf.clear_history();
        cbf.reset();
    }

    async fn parse_json_objects(
        json_string: &str,
    ) -> Vec<public_api_proto::GenerateCommitMessageResponse> {
        let mut responses = Vec::new();
        for line in json_string.lines() {
            let response: public_api_proto::GenerateCommitMessageResponse =
                serde_json::from_str(line).unwrap();
            responses.push(response);
        }
        responses
    }

    async fn add_chat_model(registry: &DynamicModelRegistry) {
        registry
            .update_models(vec![
                create_model_instance_config("model1Chat", ModelType::Chat, 1),
                create_model_instance_config("model2Chat", ModelType::Chat, 1),
                create_model_instance_config("model3Chat", ModelType::Chat, 1),
                create_model_instance_config("unavailableChatModel", ModelType::Chat, 1),
            ])
            .await
            .unwrap();
    }

    async fn run_chat(
        request: &public_api_proto::ChatRequest,
    ) -> (ChatRequest, public_api_proto::ChatResponse) {
        let (resp, fake_client_factory) = run_chat_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        let result: public_api_proto::ChatResponse =
            serde_json::from_slice(&body::to_bytes(resp.into_body()).await.unwrap()).unwrap();
        let modelhost_request = get_last_chat_request("model1Chat", fake_client_factory).await;
        (modelhost_request, result)
    }

    async fn run_chat_raw(
        request: &public_api_proto::ChatRequest,
    ) -> (HttpResponse, Arc<FakeClientFactory>) {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let app_state = setup_app_state(fake_client_factory.clone());
        CHAT_MODEL_FLAG.set_local(&app_state.feature_flags, "model1Chat");
        CHAT_RAW_OUTPUT_MODEL_FLAG.set_local(&app_state.feature_flags, "model3Chat");
        AGENT_CHAT_MODEL_FLAG.set_local(&app_state.feature_flags, "model2Chat");

        let req = setup_req();
        {
            let registry = app_state.model_registry.clone();
            add_chat_model(&registry).await;
        }
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req,
            web::Json(request.clone()),
            root_span,
        )
        .await;
        (resp, fake_client_factory)
    }

    async fn run_chat_stream_raw(
        request: &public_api_proto::ChatRequest,
    ) -> (HttpResponse, Arc<FakeClientFactory>) {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let app_state = setup_app_state(fake_client_factory.clone());
        CHAT_MODEL_FLAG.set_local(&app_state.feature_flags, "model1Chat");
        CHAT_RAW_OUTPUT_MODEL_FLAG.set_local(&app_state.feature_flags, "model3Chat");
        AGENT_CHAT_MODEL_FLAG.set_local(&app_state.feature_flags, "model2Chat");

        let req = setup_req();
        {
            let registry = app_state.model_registry.clone();
            add_chat_model(&registry).await;
        }
        let root_span = new_root_span();
        let resp = chat_stream_api_auth(
            app_state.clone(),
            req,
            web::Json(request.clone()),
            root_span,
        )
        .await;
        (resp, fake_client_factory)
    }

    async fn get_last_chat_request(
        model_name: &str,
        fake_client_factory: Arc<FakeClientFactory>,
    ) -> ChatRequest {
        fake_client_factory
            .fake_chat_clients
            .lock()
            .unwrap()
            .iter()
            .find(|c| c.model_name.as_ref() == model_name)
            .expect("No client for model")
            .get_last_chat_request()
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_model_not_specified() {
        setup().await;
        let (modelhost_request, response) = run_chat(&public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message: "What does this do?".to_string(),
            model: None,
            ..Default::default()
        })
        .await;

        assert_eq!(response.text, "hello");
        print!("{modelhost_request:?}");
        // default values, including model_name, filled in
        assert_eq!(
            modelhost_request,
            ChatRequest {
                model_name: "model1Chat".to_string(),
                selected_code: "fn sort(&vec: Vec<u8>) {}".to_string(),
                message: "What does this do?".to_string(),
                chat_history: vec![],
                suffix: "".to_string(),
                path: "".to_string(),
                blobs: vec![blob_names_proto::Blobs::default()],
                lang: "".to_string(),
                position: None,
                sequence_id: 0,
                feature_detection_flags: Some(chat::ChatFeatureDetectionFlags {
                    support_relevant_sources: None,
                    support_tool_use_start: None,
                    support_parallel_tool_use: None
                }),
                ..ChatRequest::default()
            }
        );
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_model_with_blobs() {
        setup().await;
        let default_frontend_req = public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message: "What does this do?".to_string(),
            ..Default::default()
        };

        let default_chat_req = ChatRequest {
            model_name: "model1Chat".to_string(),
            selected_code: "fn sort(&vec: Vec<u8>) {}".to_string(),
            message: "What does this do?".to_string(),
            chat_history: vec![],
            suffix: "".to_string(),
            path: "".to_string(),
            lang: "".to_string(),
            position: None,
            sequence_id: 0,
            feature_detection_flags: Some(chat::ChatFeatureDetectionFlags {
                support_relevant_sources: None,
                support_tool_use_start: None,
                support_parallel_tool_use: None,
            }),
            ..ChatRequest::default()
        };

        let blob1 = BlobName::from_bytes(&[0; 32]).unwrap();

        for use_blob_names in [true, false] {
            for use_blobs in [true, false] {
                // Test all of the different combinations
                println!(
                    "use_blob_names: {}, use_blobs: {}",
                    use_blob_names, use_blobs
                );

                let blobs = if use_blobs {
                    Some(public_api_proto::Blobs {
                        checkpoint_id: None,
                        added_blobs: vec![String::from(&blob1)],
                        deleted_blobs: vec![],
                    })
                } else {
                    None
                };
                let blob_names = if use_blob_names {
                    vec![String::from(&blob1)]
                } else {
                    vec![]
                };

                // No matter how the API gets the blobs from the client, we want
                // to send this to the rest of the backend
                let blobs_out = blob_names_proto::Blobs {
                    baseline_checkpoint_id: None,
                    added: if use_blob_names || use_blobs {
                        vec![blob1.as_bytes().to_vec()]
                    } else {
                        vec![]
                    },
                    deleted: vec![],
                };

                #[allow(deprecated)]
                let frontend_req = public_api_proto::ChatRequest {
                    blob_names,
                    blobs,
                    ..default_frontend_req.clone()
                };

                #[allow(deprecated)]
                let chat_req = ChatRequest {
                    blobs: vec![blobs_out.clone()],
                    ..default_chat_req.clone()
                };

                let (modelhost_request, _response) = run_chat(&frontend_req).await;
                print!("{modelhost_request:?}");
                assert_eq!(modelhost_request, chat_req);
            }
        }
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_with_history() {
        setup().await;
        let (modelhost_request, response) = run_chat(&public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message: "What does this do?".to_string(),
            chat_history: vec![
                public_api_proto::Exchange {
                    request_message: "How you doing?".to_string(),
                    response_text: Some("Fine thanks".to_string()),
                    request_id: Some("434f8980-8490-453c-8ba7-4eb4a7702d49".to_string()),
                    ..Default::default()
                },
                public_api_proto::Exchange {
                    request_nodes: vec![public_api_proto::ChatRequestNode {
                        id: 1,
                        r#type: 0,
                        text_node: Some(public_api_proto::ChatRequestText {
                            content: "What's the date?".to_string(),
                        }),
                        tool_result_node: None,
                        image_node: None,
                        ide_state_node: None,
                        edit_events_node: None,
                    }],
                    response_nodes: vec![public_api_proto::ChatResultNode {
                        id: 1,
                        r#type: 5,
                        content: "Let me check.".to_string(),
                        tool_use: Some(public_api_proto::ChatResultToolUse {
                            tool_use_id: "tool-123".to_string(),
                            tool_name: "get_date".to_string(),
                            input_json: "{}".to_string(),
                            is_partial: true,
                        }),
                    }],
                    request_id: Some("434f8980-8490-453c-8ba7-4eb4a7702d49".to_string()),
                    ..Default::default()
                },
            ],
            ..Default::default()
        })
        .await;

        assert_eq!(response.text, "hello");
        print!("{modelhost_request:?}");
        // default values, including model_name, filled in
        assert_eq!(
            modelhost_request,
            ChatRequest {
                model_name: "model1Chat".to_string(),
                selected_code: "fn sort(&vec: Vec<u8>) {}".to_string(),
                message: "What does this do?".to_string(),
                chat_history: vec![
                    Exchange {
                        request_message: "How you doing?".to_string(),
                        response_text: Some("Fine thanks".to_string()),
                        request_id: Some("434f8980-8490-453c-8ba7-4eb4a7702d49".to_string()),
                        ..Default::default()
                    },
                    Exchange {
                        request_nodes: vec![ChatRequestNode {
                            id: 1,
                            r#type: 0,
                            text_node: Some(ChatRequestText {
                                content: "What's the date?".to_string(),
                            }),
                            tool_result_node: None,
                            image_node: None,
                            ide_state_node: None,
                            edit_events_node: None,
                        }],
                        response_nodes: vec![ChatResultNode {
                            id: 1,
                            r#type: 5,
                            content: "Let me check.".to_string(),
                            tool_use: Some(ChatResultToolUse {
                                tool_use_id: "tool-123".to_string(),
                                tool_name: "get_date".to_string(),
                                input_json: "{}".to_string(),
                                is_partial: true,
                            }),
                        }],
                        request_id: Some("434f8980-8490-453c-8ba7-4eb4a7702d49".to_string()),
                        ..Default::default()
                    }
                ],
                suffix: "".to_string(),
                path: "".to_string(),
                blobs: vec![blob_names_proto::Blobs::default()],
                lang: "".to_string(),
                position: None,
                sequence_id: 0,
                feature_detection_flags: Some(chat::ChatFeatureDetectionFlags {
                    support_relevant_sources: None,
                    support_tool_use_start: None,
                    support_parallel_tool_use: None
                }),
                ..ChatRequest::default()
            }
        );
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_status_code_error() {
        setup().await;
        let big_message = "a".repeat(2048);
        let request = &public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message: big_message,
            ..Default::default()
        };
        let (resp, _): (HttpResponse, Arc<FakeClientFactory>) = run_chat_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::PAYLOAD_TOO_LARGE);
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_grpc_error() {
        setup().await;
        let message = "RESOURCE_EXHAUSTED".to_string();
        let request = &public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message,
            ..Default::default()
        };
        let (resp, _): (HttpResponse, Arc<FakeClientFactory>) = run_chat_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::SERVICE_UNAVAILABLE);
        assert!(resp.headers().get("Retry-After").is_some());
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_stream_text() {
        setup().await;
        let request = &public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message: "What does this do?".to_string(),
            ..Default::default()
        };
        let (resp, _): (HttpResponse, Arc<FakeClientFactory>) = run_chat_stream_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        // Read the stream body from response
        let box_body: body::BoxBody = resp.into_body();
        pin!(box_body);

        // first chunk
        let bytes: web::Bytes = future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .unwrap()
            .unwrap();
        let front_chat_response =
            serde_json::from_slice::<public_api_proto::ChatResponse>(&bytes).unwrap();
        assert_eq!(front_chat_response.text, "hello1");
        assert_eq!(front_chat_response.unknown_blob_names.len(), 1000);

        // second chunk
        let bytes: web::Bytes = future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .unwrap()
            .unwrap();
        let front_chat_response =
            serde_json::from_slice::<public_api_proto::ChatResponse>(&bytes).unwrap();
        assert_eq!(front_chat_response.text, "hello2");
        assert_eq!(front_chat_response.unknown_blob_names, Vec::<String>::new());

        // EOS
        assert!(future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .is_none());
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_stream_status_code_error() {
        setup().await;
        let big_message = "a".repeat(2048);
        let request = &public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message: big_message,
            ..Default::default()
        };
        let (resp, _): (HttpResponse, Arc<FakeClientFactory>) = run_chat_stream_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::PAYLOAD_TOO_LARGE);
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_stream_grpc_error() {
        setup().await;
        let message = "RESOURCE_EXHAUSTED".to_string();
        let request = &public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message,
            ..Default::default()
        };
        let (resp, _): (HttpResponse, Arc<FakeClientFactory>) = run_chat_stream_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::SERVICE_UNAVAILABLE);
        assert!(resp.headers().get("Retry-After").is_some());
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_stream_error_fallback_code() {
        setup().await;
        let message = "Say Hello".to_string();
        let request = &public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message,
            ..Default::default()
        };

        let fake_client_factory: Arc<FakeClientFactory> = Arc::new(FakeClientFactory::new());
        let app_state = setup_app_state(fake_client_factory.clone());
        CHAT_MODEL_FLAG.set_local(&app_state.feature_flags, "unavailableChatModel"); // Model with this name always fails

        let req = setup_req();
        {
            let registry = app_state.model_registry.clone();
            add_chat_model(&registry).await;
        }
        let root_span = new_root_span();

        // No fallback, fails
        let resp = chat_stream_api_auth(
            app_state.clone(),
            req.clone(),
            web::Json(request.clone()),
            root_span.clone(),
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::SERVICE_UNAVAILABLE);

        // Add fallback, succeeds
        CHAT_FALLBACK_MODEL_FLAG.set_local(&app_state.feature_flags, "model2Chat");
        let resp =
            chat_stream_api_auth(app_state, req, web::Json(request.clone()), root_span).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_stream_error_fallback_with_raw_output_flag_code() {
        setup().await;
        let message = "Say Hello".to_string();
        let request = &public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message,
            feature_detection_flags: Some(public_api_proto::ChatFeatureDetectionFlags {
                support_raw_output: Some(true),
                ..Default::default()
            }),
            ..Default::default()
        };

        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let app_state = setup_app_state(fake_client_factory.clone());
        CHAT_MODEL_FLAG.set_local(&app_state.feature_flags, "model1Chat");
        CHAT_FALLBACK_MODEL_FLAG.set_local(&app_state.feature_flags, "model2Chat,model3Chat");
        CHAT_RAW_OUTPUT_MODEL_FLAG.set_local(&app_state.feature_flags, "unavailableChatModel"); // Model with this name always fails

        let req = setup_req();
        {
            let registry = app_state.model_registry.clone();
            add_chat_model(&registry).await;
        }
        let root_span = new_root_span();

        // No fallback for raw output, fails
        let resp = chat_stream_api_auth(
            app_state.clone(),
            req.clone(),
            web::Json(request.clone()),
            root_span.clone(),
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::SERVICE_UNAVAILABLE);

        // Add fallback for raw output, succeeds
        CHAT_RAW_OUTPUT_FALLBACK_MODEL_FLAG
            .set_local(&app_state.feature_flags, "unavailableChatModel,model3Chat");
        let resp =
            chat_stream_api_auth(app_state, req, web::Json(request.clone()), root_span).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_stream_error_circuit_breaker_failover() {
        setup().await;
        let message = "Say Hello".to_string();
        let request = &public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message,
            feature_detection_flags: Some(public_api_proto::ChatFeatureDetectionFlags {
                support_raw_output: Some(true),
                ..Default::default()
            }),
            ..Default::default()
        };

        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let app_state = setup_app_state(fake_client_factory.clone());
        CHAT_MODEL_FLAG.set_local(&app_state.feature_flags, "model1Chat");
        CHAT_FALLBACK_MODEL_FLAG.set_local(&app_state.feature_flags, "model2Chat");
        CHAT_RAW_OUTPUT_MODEL_FLAG.set_local(&app_state.feature_flags, "unavailableChatModel"); // Model with this name always fails
        CHAT_RAW_OUTPUT_FALLBACK_MODEL_FLAG.set_local(&app_state.feature_flags, "model3Chat");
        CHAT_CBF_FAILURE_THRESHOLD.set_local(&app_state.feature_flags, 0.1);
        CHAT_CBF_MINIMUM_REQUESTS.set_local(&app_state.feature_flags, 2);
        CHAT_CBF_FAILURE_WINDOW_SECONDS.set_local(&app_state.feature_flags, 500);
        CHAT_CBF_RESET_THRESHOLD_SECONDS.set_local(&app_state.feature_flags, 500);

        let req = setup_req();
        {
            let registry = app_state.model_registry.clone();
            add_chat_model(&registry).await;
        }
        let root_span = new_root_span();

        let resp: HttpResponse = chat_stream_api_auth(
            app_state.clone(),
            req.clone(),
            web::Json(request.clone()),
            root_span.clone(),
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        let resp: HttpResponse = chat_stream_api_auth(
            app_state.clone(),
            req.clone(),
            web::Json(request.clone()),
            root_span.clone(),
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        let resp: HttpResponse = chat_stream_api_auth(
            app_state.clone(),
            req.clone(),
            web::Json(request.clone()),
            root_span.clone(),
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        let resp: HttpResponse =
            chat_stream_api_auth(app_state, req, web::Json(request.clone()), root_span).await;
        assert_eq!(resp.status(), http::StatusCode::OK);

        // Verify both models were called until CBF trips, then only fallback is called
        assert_eq!(
            {
                let client = fake_client_factory
                    .fake_chat_clients
                    .lock()
                    .unwrap()
                    .iter()
                    .find(|c| c.model_name.as_ref() == "model3Chat")
                    .expect("model not found")
                    .clone();
                client.get_call_count().await
            },
            4
        );
        assert_eq!(
            {
                let client = fake_client_factory
                    .fake_chat_clients
                    .lock()
                    .unwrap()
                    .iter()
                    .find(|c| c.model_name.as_ref() == "unavailableChatModel")
                    .expect("model not found")
                    .clone();
                client.get_call_count().await
            },
            2
        );
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_stream_element_error() {
        setup().await;
        let request = &public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message: "INJECT_ERROR".to_string(),
            ..Default::default()
        };
        let (resp, _): (HttpResponse, Arc<FakeClientFactory>) = run_chat_stream_raw(request).await;
        // Note that top-level status still reads as OK. This is normal HTTP behavior as far as we know.
        assert_eq!(resp.status(), http::StatusCode::OK);
        // Read the stream body from response
        let box_body: body::BoxBody = resp.into_body();
        pin!(box_body);

        // first chunk
        let bytes: web::Bytes = future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .unwrap()
            .unwrap();
        let front_chat_response =
            serde_json::from_slice::<public_api_proto::ChatResponse>(&bytes).unwrap();
        assert_eq!(front_chat_response.text, "hello1");
        assert_eq!(front_chat_response.unknown_blob_names.len(), 1000);

        // second chunk
        let bytes: web::Bytes = future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .unwrap()
            .unwrap();
        let front_chat_response =
            serde_json::from_slice::<public_api_proto::ChatResponse>(&bytes).unwrap();
        assert_eq!(front_chat_response.text, "hello2");
        assert_eq!(front_chat_response.unknown_blob_names, Vec::<String>::new());

        // error
        assert!(future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .unwrap()
            .is_err());

        // EOS
        assert!(future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .is_none());
    }

    #[actix_web::test]
    #[serial]
    async fn test_generate_commit_message() {
        setup().await;
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let app_state = setup_app_state(fake_client_factory.clone());
        CHAT_MODEL_FLAG.set_local(&app_state.feature_flags, "model1Chat");

        let req = setup_req();
        {
            let registry = app_state.model_registry.clone();
            add_chat_model(&registry).await;
        }
        let root_span = new_root_span();
        let item = web::Json(public_api_proto::GenerateCommitMessageRequest {
            changed_file_stats: Some(public_api_proto::ChangedFileStats {
                modified_file_stats: Some(public_api_proto::PerTypeChangedFileStats {
                    changed_file_count: 1,
                    per_file_change_stats_head: vec![public_api_proto::PerFileChangeStats {
                        file_path: "a_plus_b.py".to_string(),
                        insertion_count: 1,
                        deletion_count: 1,
                        old_file_path: "a_plus_b.py".to_string(),
                    }],
                    per_file_change_stats_tail: vec![],
                }),
                ..Default::default()
            }),
            diff: Some("-a = 0\n+a = 1".to_string()),
            relevant_commit_messages: vec!["Relevant commit message 0".to_string()],
            example_commit_messages: vec![
                "Example commit message 0".to_string(),
                "Example commit message 1".to_string(),
            ],
        });
        let resp =
            generate_commit_message_stream_api_auth(app_state.clone(), req, item, root_span).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        let bytes_to_send = resp.into_body();
        pin!(bytes_to_send);
        let mut response = String::new();
        while let Some(chunk) = future::poll_fn(|cx| bytes_to_send.as_mut().poll_next(cx)).await {
            response.push_str(&String::from_utf8(chunk.unwrap().to_vec()).unwrap());
        }
        let response: Vec<public_api_proto::GenerateCommitMessageResponse> =
            parse_json_objects(&response).await;
        // assert_eq!(response.len(), 2);
        assert_eq!(response[0].text, "hello1");
        assert_eq!(response[1].text, "hello2");
    }

    #[test]
    fn test_chat_request_backwards_compatibility() {
        let json_data = r#"{"path": "foo.py", "message": "What does this do?", "selected_code": "fn sort(&vec: Vec<u8>) {}"}"#;
        let chat_request: public_api_proto::ChatRequest = serde_json::from_str(json_data).unwrap();
        assert_eq!(
            chat_request,
            public_api_proto::ChatRequest {
                path: Some("foo.py".to_string()),
                message: "What does this do?".to_string(),
                selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
                ..Default::default()
            },
        );
    }

    #[test]
    fn test_chat_request_deserialize_mode() {
        let no_field = r#"{"path": "foo.py", "message": "What does this do?", "selected_code": "fn sort(&vec: Vec<u8>) {}"}"#;
        let mut chat_request: public_api_proto::ChatRequest =
            serde_json::from_str(no_field).unwrap();
        assert_eq!(chat_request.mode, public_api_proto::ChatMode::Chat as i32);

        let explicit_chat = r#"{"mode": "CHAT", "path": "foo.py", "message": "What does this do?", "selected_code": "fn sort(&vec: Vec<u8>) {}"}"#;
        chat_request = serde_json::from_str(explicit_chat).unwrap();
        assert_eq!(chat_request.mode, public_api_proto::ChatMode::Chat as i32);

        let explicit_agent = r#"{"mode": "AGENT", "path": "foo.py", "message": "What does this do?", "selected_code": "fn sort(&vec: Vec<u8>) {}"}"#;
        chat_request = serde_json::from_str(explicit_agent).unwrap();
        assert_eq!(chat_request.mode, public_api_proto::ChatMode::Agent as i32);
    }

    #[test]
    fn test_chat_with_null_fields() {
        let json_data = r#"{"model": null, "path": null, "prefix": null, "lang": null, "selected_code": null, "suffix": null, "context_code_exchange_request_id": null, "message": "What is 2+2?"}"#;
        let chat_request: public_api_proto::ChatRequest = serde_json::from_str(json_data).unwrap();
        assert_eq!(
            chat_request,
            public_api_proto::ChatRequest {
                message: "What is 2+2?".to_string(),
                ..Default::default()
            },
        );
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_raw_output_flag() {
        setup().await;
        let req = public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message: "What does this do?".to_string(),
            model: None,
            feature_detection_flags: Some(public_api_proto::ChatFeatureDetectionFlags {
                support_raw_output: Some(true),
                ..Default::default()
            }),
            ..Default::default()
        };
        let (resp, fake_client_factory) = run_chat_raw(&req).await;

        assert_eq!(resp.status(), http::StatusCode::OK);
        let modelhost_request = get_last_chat_request("model3Chat", fake_client_factory).await;
        assert_eq!(
            modelhost_request,
            ChatRequest {
                model_name: "model3Chat".to_string(),
                selected_code: "fn sort(&vec: Vec<u8>) {}".to_string(),
                message: "What does this do?".to_string(),
                chat_history: vec![],
                suffix: "".to_string(),
                path: "".to_string(),
                blobs: vec![blob_names_proto::Blobs::default()],
                lang: "".to_string(),
                position: None,
                sequence_id: 0,
                feature_detection_flags: Some(chat::ChatFeatureDetectionFlags {
                    support_relevant_sources: None,
                    support_tool_use_start: None,
                    support_parallel_tool_use: None
                }),
                ..ChatRequest::default()
            }
        );
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_stream_raw_output_flag() {
        setup().await;
        let req = public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message: "What does this do?".to_string(),
            model: None,
            feature_detection_flags: Some(public_api_proto::ChatFeatureDetectionFlags {
                support_raw_output: Some(true),
                support_tool_use_start: Some(true),
                ..Default::default()
            }),
            ..Default::default()
        };
        let (resp, fake_client_factory) = run_chat_stream_raw(&req).await;

        assert_eq!(resp.status(), http::StatusCode::OK);
        let modelhost_request = get_last_chat_request("model3Chat", fake_client_factory).await;
        assert_eq!(
            modelhost_request,
            ChatRequest {
                model_name: "model3Chat".to_string(),
                selected_code: "fn sort(&vec: Vec<u8>) {}".to_string(),
                message: "What does this do?".to_string(),
                chat_history: vec![],
                suffix: "".to_string(),
                path: "".to_string(),
                blobs: vec![blob_names_proto::Blobs::default()],
                lang: "".to_string(),
                position: None,
                sequence_id: 0,
                feature_detection_flags: Some(chat::ChatFeatureDetectionFlags {
                    support_relevant_sources: None,
                    support_tool_use_start: Some(true),
                    support_parallel_tool_use: None,
                }),
                ..ChatRequest::default()
            }
        );
    }

    #[test]
    fn test_deserialize_chat_request_with_image_node() {
        let json_data = r#"{
            "model": "test-model",
            "sequence_id": 1,
            "nodes": [
                {
                    "id": 1,
                    "type": 0,
                    "text_node": {
                        "content": "What's in this image?"
                    }
                },
                {
                    "id": 2,
                    "type": 2,
                    "image_node": {
                        "format": 1,
                        "image_data": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
                    }
                }
            ]
        }"#;

        let chat_request: public_api_proto::ChatRequest = serde_json::from_str(json_data).unwrap();

        assert_eq!(chat_request.model.unwrap(), "test-model");
        assert_eq!(chat_request.sequence_id, 1);
        assert_eq!(chat_request.nodes.len(), 2);

        // Check text node
        let text_node = &chat_request.nodes[0];
        assert_eq!(text_node.id, 1);
        assert_eq!(
            text_node.r#type,
            public_api_proto::ChatRequestNodeType::Text as i32
        );
        assert!(text_node.tool_result_node.is_none());
        assert!(text_node.image_node.is_none());
        let text = text_node.text_node.as_ref().unwrap();
        assert_eq!(text.content, "What's in this image?");

        // Check image node
        let image_node = &chat_request.nodes[1];
        assert_eq!(image_node.id, 2);
        assert_eq!(
            image_node.r#type,
            public_api_proto::ChatRequestNodeType::Image as i32
        );
        assert!(image_node.text_node.is_none());
        assert!(image_node.tool_result_node.is_none());

        let image = image_node.image_node.as_ref().unwrap();
        assert_eq!(image.format, public_api_proto::ImageFormatType::Png as i32);
        assert_eq!(image.image_data, "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==");
    }

    #[actix_web::test]
    #[serial]
    async fn test_trial_expiration_disclaimer() {
        setup().await;

        // Create a test user with a trial expiring in 3 days
        let now = Utc::now();
        let trial_end = now + Duration::days(3) + Duration::hours(2); // slightly more than 3 days
        let trial_end_timestamp = Timestamp {
            seconds: trial_end.timestamp(),
            nanos: 0,
        };

        let user = User {
            user_id: "test-user-1".to_string(),
            opaque_user_id: auth_entities_proto::auth_entities::UserId::default(),
            user_email: Some("<EMAIL>".to_string()),
            subscription: Some(get_token_info_response::Subscription::Trial(
                auth_query_client::auth_query::Trial {
                    trial_end: Some(trial_end_timestamp),
                    billing_method: auth_entities_proto::auth_entities::BillingMethod::Stripe
                        as i32,
                },
            )),
            suspensions: vec![],
        };

        // Set up feature flags
        let feature_flags = feature_flags::setup_local();
        TRIAL_EXPIRATION_DISCLAIMER.set_local(&feature_flags, true);
        TRIAL_EXPIRATION_DAYS_THRESHOLD.set_local(&feature_flags, 5);
        let tenant_name = "test_tenant";

        // Test 1: Message should be added for first response
        let (tx1, rx1) = tokio::sync::mpsc::channel(10);
        let mut modified_rx1 = modify_chat_response(rx1, &user, tenant_name, &feature_flags).await;

        // Send a test response through the channel
        let response1 = ChatResponse {
            nodes: vec![chat::ChatResultNode {
                r#type: chat::ChatResultNodeType::MainTextFinished as i32,
                content: "Test response".to_string(),
                ..Default::default()
            }],
            text: "Test response".to_string(),
            stop_reason: Some(chat::ChatStopReason::EndTurn as i32),
            ..Default::default()
        };
        tx1.send(Ok(response1)).await.unwrap();

        // Get the modified response
        let modified_response1 = modified_rx1.recv().await.unwrap().unwrap();
        assert!(
            modified_response1.nodes[0]
                .content
                .contains("access expires in 3 days"),
            "Response should contain trial expiration message"
        );

        // Test 2: Message should not be added for the same user on the same day
        let (tx2, rx2) = tokio::sync::mpsc::channel(10);
        let mut modified_rx2 = modify_chat_response(rx2, &user, tenant_name, &feature_flags).await;

        // Send another test response
        let response2 = ChatResponse {
            nodes: vec![chat::ChatResultNode {
                r#type: chat::ChatResultNodeType::MainTextFinished as i32,
                content: "Test response 2".to_string(),
                ..Default::default()
            }],
            text: "Test response 2".to_string(),
            stop_reason: Some(chat::ChatStopReason::EndTurn as i32),
            ..Default::default()
        };
        tx2.send(Ok(response2)).await.unwrap();

        // The second response should be unchanged since the user was already alerted
        let modified_response2 = modified_rx2.recv().await.unwrap().unwrap();
        assert_eq!(
            modified_response2.nodes[0].content, "Test response 2",
            "Response should not be modified for the same user on the same day"
        );

        // Test 3: Message should be added for a different user
        let user2 = User {
            user_id: "test-user-2".to_string(),
            opaque_user_id: auth_entities_proto::auth_entities::UserId::default(),
            user_email: Some("<EMAIL>".to_string()),
            subscription: Some(get_token_info_response::Subscription::Trial(
                auth_query_client::auth_query::Trial {
                    trial_end: Some(trial_end_timestamp),
                    billing_method: auth_entities_proto::auth_entities::BillingMethod::Stripe
                        as i32,
                },
            )),
            suspensions: vec![],
        };

        let (tx3, rx3) = tokio::sync::mpsc::channel(10);
        let mut modified_rx3 = modify_chat_response(rx3, &user2, tenant_name, &feature_flags).await;

        // Send a test response for the second user
        let response3 = ChatResponse {
            nodes: vec![chat::ChatResultNode {
                r#type: chat::ChatResultNodeType::MainTextFinished as i32,
                content: "Test response 3".to_string(),
                ..Default::default()
            }],
            text: "Test response 3".to_string(),
            stop_reason: Some(chat::ChatStopReason::EndTurn as i32),
            ..Default::default()
        };
        tx3.send(Ok(response3)).await.unwrap();

        // The response for the second user should be modified
        let modified_response3 = modified_rx3.recv().await.unwrap().unwrap();
        assert!(
            modified_response3.nodes[0]
                .content
                .contains("access expires in 3 days"),
            "Response should contain trial expiration message for a different user"
        );

        // Test 4: Message should not be added when feature is disabled
        TRIAL_EXPIRATION_DISCLAIMER.set_local(&feature_flags, false);

        let user3 = User {
            user_id: "test-user-3".to_string(),
            opaque_user_id: auth_entities_proto::auth_entities::UserId::default(),
            user_email: Some("<EMAIL>".to_string()),
            subscription: Some(get_token_info_response::Subscription::Trial(
                auth_query_client::auth_query::Trial {
                    trial_end: Some(trial_end_timestamp),
                    billing_method: auth_entities_proto::auth_entities::BillingMethod::Stripe
                        as i32,
                },
            )),
            suspensions: vec![],
        };

        let (tx4, rx4) = tokio::sync::mpsc::channel(10);
        let mut modified_rx4 = modify_chat_response(rx4, &user3, tenant_name, &feature_flags).await;

        // Send a test response
        let response4 = ChatResponse {
            nodes: vec![chat::ChatResultNode {
                r#type: chat::ChatResultNodeType::MainTextFinished as i32,
                content: "Test response 4".to_string(),
                ..Default::default()
            }],
            text: "Test response 4".to_string(),
            stop_reason: Some(chat::ChatStopReason::EndTurn as i32),
            ..Default::default()
        };
        tx4.send(Ok(response4)).await.unwrap();

        // The response should be unchanged
        let modified_response4 = modified_rx4.recv().await.unwrap().unwrap();
        assert_eq!(
            modified_response4.nodes[0].content, "Test response 4",
            "Response should not be modified when feature is disabled"
        );
    }

    #[actix_web::test]
    #[serial]
    async fn test_trial_expiration_disclaimer_midnight_reset() {
        setup().await;

        // Create a test user with a trial expiring in 3 days
        let now = Utc::now();
        let trial_end = now + Duration::days(3) + Duration::hours(2); // slightly more than 3 days so the test response calculates this as 3 days
        let trial_end_timestamp = Timestamp {
            seconds: trial_end.timestamp(),
            nanos: 0,
        };

        let user = User {
            user_id: "test-user-reset".to_string(),
            opaque_user_id: auth_entities_proto::auth_entities::UserId::default(),
            user_email: Some("<EMAIL>".to_string()),
            subscription: Some(get_token_info_response::Subscription::Trial(
                auth_query_client::auth_query::Trial {
                    trial_end: Some(trial_end_timestamp),
                    billing_method: auth_entities_proto::auth_entities::BillingMethod::Stripe
                        as i32,
                },
            )),
            suspensions: vec![],
        };

        let tenant_name = "test_tenant";

        // Set up feature flags
        let feature_flags = feature_flags::setup_local();
        TRIAL_EXPIRATION_DISCLAIMER.set_local(&feature_flags, true);
        TRIAL_EXPIRATION_DAYS_THRESHOLD.set_local(&feature_flags, 5);

        // First, add the user to the alerted set
        {
            let response = ChatResponse {
                nodes: vec![chat::ChatResultNode {
                    r#type: chat::ChatResultNodeType::MainTextFinished as i32,
                    content: "Test response".to_string(),
                    ..Default::default()
                }],
                stop_reason: Some(chat::ChatStopReason::EndTurn as i32),
                ..Default::default()
            };

            let (tx, rx) = tokio::sync::mpsc::channel(10);
            let mut modified_rx =
                modify_chat_response(rx, &user, tenant_name, &feature_flags).await;

            // Send the response through the channel
            tx.send(Ok(response)).await.unwrap();

            // Get the modified response
            let modified_response = modified_rx.recv().await.unwrap().unwrap();
            assert!(
                modified_response.nodes[0]
                    .content
                    .contains("access expires in 3 days"),
                "Response should contain trial expiration message"
            );
        }

        // Now, manually modify the timestamp for this user to be more than 24 hours ago
        {
            let mut alerted_data = TRIAL_EXPIRATION_ALERTED_USERS.lock().await;
            // Set the timestamp to 25 hours ago to trigger a new alert
            let yesterday = Utc::now() - Duration::hours(25);
            alerted_data.insert(user.user_id.clone(), yesterday);
        }

        // Try again - should add the disclaimer because we've "crossed midnight"
        let response2 = ChatResponse {
            nodes: vec![chat::ChatResultNode {
                r#type: chat::ChatResultNodeType::MainTextFinished as i32,
                content: "Test response 2".to_string(),
                ..Default::default()
            }],
            stop_reason: Some(chat::ChatStopReason::EndTurn as i32),
            ..Default::default()
        };

        let (tx2, rx2) = tokio::sync::mpsc::channel(10);
        let mut modified_rx2 = modify_chat_response(rx2, &user, tenant_name, &feature_flags).await;

        // Send the response through the channel
        tx2.send(Ok(response2)).await.unwrap();

        // Get the modified response
        let modified_response2 = modified_rx2.recv().await.unwrap().unwrap();
        assert!(
            modified_response2.nodes[0]
                .content
                .contains("access expires in 3 days"),
            "Response should contain trial expiration message after reset"
        );
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_stream_with_inactive_subscription() {
        setup().await;

        // Create a test user with an inactive subscription
        let user = User {
            user_id: "test-inactive-user".to_string(),
            opaque_user_id: auth_entities_proto::auth_entities::UserId::default(),
            user_email: Some("<EMAIL>".to_string()),
            subscription: Some(get_token_info_response::Subscription::InactiveSubscription(
                auth_query_client::auth_query::InactiveSubscription {
                    billing_method: auth_entities_proto::auth_entities::BillingMethod::Stripe
                        as i32,
                },
            )),
            suspensions: vec![],
        };

        // Set up feature flags
        let feature_flags = feature_flags::setup_local();
        CHECK_SUBSCRIPTION_STATUS.set_local(&feature_flags, true);
        INACTIVE_SUBSCRIPTION_DISCLAIMER.set_local(&feature_flags, true);

        // Create a fake client factory
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let app_state = setup_app_state(fake_client_factory.clone());

        // Apply feature flags to app_state as well
        CHECK_SUBSCRIPTION_STATUS.set_local(&app_state.feature_flags, true);
        INACTIVE_SUBSCRIPTION_DISCLAIMER.set_local(&app_state.feature_flags, true);

        CHAT_MODEL_FLAG.set_local(&app_state.feature_flags, "model1Chat");

        // Set up the request
        let req = setup_req();
        {
            let registry = app_state.model_registry.clone();
            add_chat_model(&registry).await;
        }

        // Create a chat request
        let chat_request = chat::ChatRequest {
            message: "Test message".to_string(),
            ..Default::default()
        };

        // Mock the request context extraction to return our test user
        let tenant_info = request_context::TenantInfo::new_for_test();

        // Create a request context
        let request_context = request_context::RequestContext::new_for_test();

        // Call the chat_stream function directly
        let receiver_result = super::chat_stream(
            app_state.clone(),
            &req,
            &user,
            &tenant_info,
            &request_context,
            chat_request.clone(),
            None,
            public_api_proto::ChatMode::Chat,
        )
        .await;

        // Verify we got a receiver
        assert!(receiver_result.is_ok(), "Should return a receiver");
        let mut receiver = receiver_result.unwrap();

        // Get the response from the receiver
        let response = receiver.recv().await;
        assert!(response.is_some(), "Should receive a response");
        let response = response.unwrap();
        assert!(response.is_ok(), "Response should be Ok");
        let response = response.unwrap();

        // Verify the response contains the inactive subscription disclaimer
        assert_eq!(
            response.text,
            INACTIVE_SUBSCRIPTION_DISCLAIMER_MKDOWN_MESSAGE
                .replace("{account_info}", " <NAME_EMAIL>")
                .replace("{logout_link}", "sign out"),
            "Response should contain inactive subscription disclaimer"
        );

        // Verify there are no more responses
        let next_response = receiver.recv().await;
        assert!(
            next_response.is_none(),
            "Should not receive any more responses"
        );
    }

    #[test]
    fn test_deserialize_image_format_type() {
        let json_data = r#"{ "format": 1, "image_data": "" }"#;
        let image: public_api_proto::ChatRequestImage = serde_json::from_str(json_data).unwrap();
        assert_eq!(
            image,
            public_api_proto::ChatRequestImage {
                format: public_api_proto::ImageFormatType::Png.into(),
                image_data: "".to_string(),
            }
        );
    }

    #[test]
    fn test_deserialize_chat_request_with_tool_result_nodes() {
        // Node 0: Text-only tool result; No request_id, which is optional
        // Node 1: Content nodes for tool result; With request_id
        let json_data = r#"{
            "model": "test-model",
            "sequence_id": 1,
            "nodes": [
                {
                    "id": 0,
                    "type": 1,
                    "tool_result_node": {
                        "tool_use_id": "tool-123",
                        "content": "The time is 9:05",
                        "is_error": false
                    }
                },
                {
                    "id": 1,
                    "type": 1,
                    "tool_result_node": {
                        "tool_use_id": "tool-456",
                        "is_error": false,
                        "request_id": "request-123",
                        "content_nodes": [
                            {
                                "type": 1,
                                "text_content": "Screenshot of browser tab 2"
                            },
                            {
                                "type": 2,
                                "image_content": {
                                    "format": 1,
                                    "image_data": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
                                }
                            }
                        ]
                    }
                }
            ]
        }"#;

        let chat_request: public_api_proto::ChatRequest = serde_json::from_str(json_data).unwrap();
        assert_eq!(chat_request.model.unwrap(), "test-model");
        assert_eq!(chat_request.sequence_id, 1);
        assert_eq!(chat_request.nodes.len(), 2);

        // Check text node
        let text_tool_result_node = &chat_request.nodes[0];
        assert_eq!(text_tool_result_node.id, 0);
        assert_eq!(
            text_tool_result_node.r#type,
            public_api_proto::ChatRequestNodeType::ToolResult as i32
        );
        assert!(text_tool_result_node.text_node.is_none());
        assert!(text_tool_result_node.image_node.is_none());
        let text_tool_result = text_tool_result_node.tool_result_node.as_ref().unwrap();
        assert_eq!(text_tool_result.tool_use_id, "tool-123");
        assert_eq!(text_tool_result.content, "The time is 9:05");
        assert!(!text_tool_result.is_error);

        // Check content nodes tool result
        let multi_modal_tool_result_node = &chat_request.nodes[1];
        assert_eq!(multi_modal_tool_result_node.id, 1);
        assert_eq!(
            multi_modal_tool_result_node.r#type,
            public_api_proto::ChatRequestNodeType::ToolResult as i32
        );
        assert!(multi_modal_tool_result_node.text_node.is_none());
        assert!(multi_modal_tool_result_node.image_node.is_none());
        let multi_modal_tool_result = multi_modal_tool_result_node
            .tool_result_node
            .as_ref()
            .unwrap();
        assert_eq!(multi_modal_tool_result.tool_use_id, "tool-456");
        assert_eq!(multi_modal_tool_result.content, "");
        assert!(!multi_modal_tool_result.is_error);
        assert_eq!(
            multi_modal_tool_result.request_id,
            Some("request-123".to_string())
        );
        let node1 = &multi_modal_tool_result.content_nodes[0];
        assert_eq!(
            node1.r#type,
            public_api_proto::ToolResultContentNodeType::ContentText as i32
        );
        assert_eq!(
            node1.text_content,
            Some("Screenshot of browser tab 2".to_string())
        );
        assert!(node1.image_content.is_none());
        let node2 = &multi_modal_tool_result.content_nodes[1];
        assert_eq!(
            node2.r#type,
            public_api_proto::ToolResultContentNodeType::ContentImage as i32
        );
        assert!(node2.text_content.is_none());
        let image_content = node2.image_content.as_ref().unwrap();
        assert_eq!(
            image_content.format,
            public_api_proto::ImageFormatType::Png as i32
        );
        assert_eq!(image_content.image_data, "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==");
    }

    #[test]
    fn test_deserialize_chat_request_with_ide_state_node() {
        let json_data = r#"{
            "model": "test-model",
            "sequence_id": 1,
            "nodes": [
                {
                    "id": 1,
                    "type": 0,
                    "text_node": {
                        "content": "Where am I?"
                    }
                },
                {
                    "id": 2,
                    "type": 4,
                    "ide_state_node": {
                        "workspace_folders": [
                            {
                                "repository_root": "/home/<USER>/projects/repo",
                                "folder_root": "/home/<USER>/projects/repo/src"
                            }
                        ],
                        "workspace_folders_unchanged": false,
                        "current_terminal": {
                            "terminal_id": 1,
                            "current_working_directory": "/home/<USER>/projects/repo/src/utils"
                        }
                    }
                },
                {
                    "id": 3,
                    "type": 4,
                    "ide_state_node": {
                        "workspace_folders": [],
                        "workspace_folders_unchanged": true,
                        "current_terminal": null
                    }
                },
                {
                    "id": 4,
                    "type": 4,
                    "ide_state_node": {
                        "workspace_folders": [
                            {
                                "repository_root": "/home/<USER>/projects/repo",
                                "folder_root": "/home/<USER>/projects/repo/src"
                            }
                        ],
                        "current_terminal": {
                            "terminal_id": 1
                        }
                    }
                }
            ]
        }"#;
        // NOTE(arun): id=4 intentionally drops some fields to test that code path.

        let chat_request: public_api_proto::ChatRequest = serde_json::from_str(json_data).unwrap();

        assert_eq!(chat_request.model.unwrap(), "test-model");
        assert_eq!(chat_request.sequence_id, 1);
        assert_eq!(chat_request.nodes.len(), 4);

        // Check text node
        let text_node = &chat_request.nodes[0];
        assert_eq!(text_node.id, 1);
        assert_eq!(
            text_node.r#type,
            public_api_proto::ChatRequestNodeType::Text as i32
        );
        assert!(text_node.tool_result_node.is_none());
        assert!(text_node.image_node.is_none());
        let text = text_node.text_node.as_ref().unwrap();
        assert_eq!(text.content, "Where am I?");

        // Check ide state node
        let ide_state_node = &chat_request.nodes[1];
        assert_eq!(ide_state_node.id, 2);
        assert_eq!(
            ide_state_node.r#type,
            public_api_proto::ChatRequestNodeType::IdeState as i32
        );
        assert!(ide_state_node.text_node.is_none());
        assert!(ide_state_node.tool_result_node.is_none());
        assert!(ide_state_node.image_node.is_none());

        let ide_state_node = &chat_request.nodes[2];
        assert_eq!(ide_state_node.id, 3);
        assert_eq!(
            ide_state_node.r#type,
            public_api_proto::ChatRequestNodeType::IdeState as i32
        );
        assert!(ide_state_node.text_node.is_none());
        assert!(ide_state_node.tool_result_node.is_none());
        assert!(ide_state_node.image_node.is_none());

        // NOTE(arun): this is the one with the missing fields.
        let ide_state_node = &chat_request.nodes[3];
        assert_eq!(ide_state_node.id, 4);
        assert_eq!(
            ide_state_node.r#type,
            public_api_proto::ChatRequestNodeType::IdeState as i32
        );
        assert!(ide_state_node.text_node.is_none());
        assert!(ide_state_node.tool_result_node.is_none());
        assert!(ide_state_node.image_node.is_none());
    }

    #[test]
    fn test_deserialize_chat_request_with_tool_use_history() {
        let json_data = r#"{
            "model": "test-model",
            "sequence_id": 1,
            "chat_history": [
                {
                    "request_message": "What's the current time?",
                    "response_text": "Let me check the current time for you.",
                    "request_id": "req-123",
                    "request_nodes": [
                        {
                            "id": 1,
                            "type": 0,
                            "text_node": {
                                "content": "What's the current time?"
                            }
                        }
                    ],
                    "response_nodes": [
                        {
                            "id": 1,
                            "type": 5,
                            "content": "Let me check the current time for you.",
                            "tool_use": {
                                "tool_use_id": "tool-123",
                                "tool_name": "get_time",
                                "input_json": "{}",
                                "is_partial": true
                            }
                        }
                    ]
                }
            ]
        }"#;

        let chat_request: public_api_proto::ChatRequest = serde_json::from_str(json_data).unwrap();

        assert_eq!(chat_request.model.unwrap(), "test-model");
        assert_eq!(chat_request.sequence_id, 1);
        assert_eq!(chat_request.chat_history.len(), 1);

        // Check the exchange
        let exchange = &chat_request.chat_history[0];
        assert_eq!(exchange.request_message, "What's the current time?");
        assert_eq!(
            exchange.response_text,
            Some("Let me check the current time for you.".to_string())
        );
        assert_eq!(exchange.request_id, Some("req-123".to_string()));

        // Check request nodes
        assert_eq!(exchange.request_nodes.len(), 1);
        let request_node = &exchange.request_nodes[0];
        assert_eq!(request_node.id, 1);
        assert_eq!(
            request_node.r#type,
            public_api_proto::ChatRequestNodeType::Text as i32
        );
        let text_node = request_node.text_node.as_ref().unwrap();
        assert_eq!(text_node.content, "What's the current time?");

        // Check response nodes
        assert_eq!(exchange.response_nodes.len(), 1);
        let response_node = &exchange.response_nodes[0];
        assert_eq!(response_node.id, 1);
        assert_eq!(
            response_node.r#type,
            public_api_proto::ChatResultNodeType::ToolUse as i32
        );
        assert_eq!(
            response_node.content,
            "Let me check the current time for you."
        );

        // Check tool use with is_partial set to true
        let tool_use = response_node.tool_use.as_ref().unwrap();
        assert_eq!(tool_use.tool_use_id, "tool-123");
        assert_eq!(tool_use.tool_name, "get_time");
        assert_eq!(tool_use.input_json, "{}");
        assert!(tool_use.is_partial, "is_partial should be true");
    }

    #[test]
    fn test_deserialize_chat_request_with_edit_events_node() {
        let json_data = r#"{
            "model": "test-model",
            "sequence_id": 1,
            "nodes": [
                {
                    "id": 1,
                    "type": 0,
                    "text_node": {
                        "content": "I made some edits"
                    }
                },
                {
                    "id": 2,
                    "type": 5,
                    "edit_events_node": {
                        "edit_events": [
                            {
                                "path": "/home/<USER>/projects/repo/src/main.rs",
                                "before_blob_name": "abc123",
                                "after_blob_name": "def456",
                                "edits": [
                                    {
                                        "before_line_start": 10,
                                        "before_text": "old code",
                                        "after_line_start": 10,
                                        "after_text": "new code"
                                    }
                                ]
                            }
                        ]
                    }
                }
            ]
        }"#;

        let chat_request: public_api_proto::ChatRequest = serde_json::from_str(json_data).unwrap();

        assert_eq!(chat_request.model.unwrap(), "test-model");
        assert_eq!(chat_request.sequence_id, 1);
        assert_eq!(chat_request.nodes.len(), 2);

        // Check text node
        let text_node = &chat_request.nodes[0];
        assert_eq!(text_node.id, 1);
        assert_eq!(
            text_node.r#type,
            public_api_proto::ChatRequestNodeType::Text as i32
        );
        assert!(text_node.tool_result_node.is_none());
        assert!(text_node.image_node.is_none());
        let text = text_node.text_node.as_ref().unwrap();
        assert_eq!(text.content, "I made some edits");

        // Check edit events node
        let edit_events_node = &chat_request.nodes[1];
        assert_eq!(edit_events_node.id, 2);
        assert_eq!(
            edit_events_node.r#type,
            public_api_proto::ChatRequestNodeType::EditEvents as i32
        );
        assert!(edit_events_node.text_node.is_none());
        assert!(edit_events_node.tool_result_node.is_none());
        assert!(edit_events_node.image_node.is_none());
        assert!(edit_events_node.ide_state_node.is_none());
    }

    #[actix_web::test]
    #[serial]
    async fn test_chat_mode_based_flag() {
        setup().await;
        // Test that the feature flag chosen is based upon the chat mode

        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let app_state = setup_app_state(fake_client_factory.clone());
        CHAT_RAW_OUTPUT_MODEL_FLAG.set_local(&app_state.feature_flags, "model1Chat");
        AGENT_CHAT_MODEL_FLAG.set_local(&app_state.feature_flags, "model2Chat");
        {
            let registry = app_state.model_registry.clone();
            add_chat_model(&registry).await;
        }

        let http_req = setup_req();

        let mut req = public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message: "A".to_string(),
            model: None,
            feature_detection_flags: Some(public_api_proto::ChatFeatureDetectionFlags {
                support_raw_output: Some(true),
                ..Default::default()
            }),
            mode: public_api_proto::ChatMode::Chat as i32,
            ..Default::default()
        };

        handle_api_auth(
            app_state.clone(),
            http_req.clone(),
            web::Json(req.clone()),
            new_root_span(),
        )
        .await;
        let mut inner_req = get_last_chat_request("model1Chat", fake_client_factory.clone()).await;
        assert_eq!(inner_req.message, "A".to_string());

        req.message = "B".to_string();
        chat_stream_api_auth(
            app_state.clone(),
            http_req.clone(),
            web::Json(req.clone()),
            new_root_span(),
        )
        .await;
        inner_req = get_last_chat_request("model1Chat", fake_client_factory.clone()).await;
        assert_eq!(inner_req.message, "B".to_string());

        req.mode = public_api_proto::ChatMode::Agent as i32;
        req.message = "C".to_string();
        handle_api_auth(
            app_state.clone(),
            http_req.clone(),
            web::Json(req.clone()),
            new_root_span(),
        )
        .await;
        inner_req = get_last_chat_request("model2Chat", fake_client_factory.clone()).await;
        assert_eq!(inner_req.message, "C".to_string());

        req.message = "D".to_string();
        chat_stream_api_auth(
            app_state.clone(),
            http_req.clone(),
            web::Json(req.clone()),
            new_root_span(),
        )
        .await;
        inner_req = get_last_chat_request("model2Chat", fake_client_factory.clone()).await;
        assert_eq!(inner_req.message, "D".to_string());

        // Setting the model explicitly overrides "mode"
        req.model = Some("model3Chat".to_string());
        req.message = "E".to_string();
        handle_api_auth(
            app_state.clone(),
            http_req.clone(),
            web::Json(req.clone()),
            new_root_span(),
        )
        .await;
        inner_req = get_last_chat_request("model3Chat", fake_client_factory.clone()).await;
        assert_eq!(inner_req.message, "E".to_string());

        req.message = "F".to_string();
        chat_stream_api_auth(
            app_state.clone(),
            http_req.clone(),
            web::Json(req.clone()),
            new_root_span(),
        )
        .await;
        inner_req = get_last_chat_request("model3Chat", fake_client_factory.clone()).await;
        assert_eq!(inner_req.message, "F".to_string());
    }

    #[actix_web::test]
    #[serial]
    async fn test_remote_agent_chat_mode_based_flag() {
        setup().await;
        // Test that the feature flag chosen is based upon the chat mode

        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let app_state = setup_app_state(fake_client_factory.clone());
        CHAT_RAW_OUTPUT_MODEL_FLAG.set_local(&app_state.feature_flags, "model1Chat");
        AGENT_CHAT_MODEL_FLAG.set_local(&app_state.feature_flags, "model2Chat");
        REMOTE_AGENT_CHAT_MODEL_FLAG.set_local(&app_state.feature_flags, "");
        {
            let registry = app_state.model_registry.clone();
            add_chat_model(&registry).await;
        }

        let http_req = setup_req();

        let mut req = public_api_proto::ChatRequest {
            selected_code: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            message: "A".to_string(),
            model: None,
            feature_detection_flags: Some(public_api_proto::ChatFeatureDetectionFlags {
                support_raw_output: Some(true),
                ..Default::default()
            }),
            mode: public_api_proto::ChatMode::RemoteAgent as i32,
            ..Default::default()
        };

        handle_api_auth(
            app_state.clone(),
            http_req.clone(),
            web::Json(req.clone()),
            new_root_span(),
        )
        .await;
        let mut inner_req = get_last_chat_request("model2Chat", fake_client_factory.clone()).await;
        assert_eq!(inner_req.message, "A".to_string());

        REMOTE_AGENT_CHAT_MODEL_FLAG.set_local(&app_state.feature_flags, "model3Chat");
        req.message = "B".to_string();
        handle_api_auth(
            app_state.clone(),
            http_req.clone(),
            web::Json(req.clone()),
            new_root_span(),
        )
        .await;
        inner_req = get_last_chat_request("model3Chat", fake_client_factory.clone()).await;
        assert_eq!(inner_req.message, "B".to_string());
    }
}
