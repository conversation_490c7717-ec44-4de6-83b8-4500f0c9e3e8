use lazy_static::lazy_static;
use prometheus::{
    register_histogram_vec, register_int_counter_vec, register_int_gauge_vec, HistogramVec,
    IntCounterVec, IntGaugeVec, Opts,
};

lazy_static! {
    // TODO: to reduce the cardinality of these metrics, we could simplify the user_agent by
    // stripping out the extension versions from them. We want to keep it in
    // general though so we can identify health checks
    pub static ref RESPONSE_CODE_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new("au_api_proxy_response_code", "Response Codes"),
        &["method", "endpoint", "status_code", "user_agent", "tenant_name", "request_source"]
    )
    .expect("metric can be created");
    pub static ref RESPONSE_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_api_proxy_request_latency",
        "The HTTP request latencies in seconds",
        &["method", "endpoint", "status_code", "user_agent", "tenant_name", "request_source"]
    )
    .expect("metric can be created");
    pub static ref HANDLER_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_api_proxy_handler_latency",
        "The handler latencies in seconds (excludes client lag and middleware latency)",
        &["endpoint", "status_code", "request_source", "tenant_name"]
    )
    .expect("metric can be created");

    pub static ref HANDLER_RETRY_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        "au_api_proxy_handler_retry",
        "The handler retry count",
        &["endpoint", "status_code", "request_source", "tenant_name"]
    )
    .expect("metric can be created");

    pub static ref STREAM_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_api_proxy_stream_latency",
        "The stream latencies in seconds",
        &["endpoint", "status_code", "request_source", "tenant_name"]
    )
    .expect("metric can be created");

    // We can consider removing this metric once we have proper analytics.
    pub static ref COMPLETION_RESOLUTION_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new("au_completion_resolution_count", "Completion accept/reject count"),
        &["status"]  // Either "accepted" or "rejected"
    )
    .expect("metric can be created");

    pub static ref EDIT_RESOLUTION_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new("au_edit_resolution_count", "Edit accept/reject count"),
        &["status"]  // Either "accepted" or "rejected"
    )
    .expect("metric can be created");

    pub static ref NEXT_EDIT_RESOLUTION_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new("au_next_edit_resolution_count", "Next Edit accept/reject count"),
        &["status"]  // Either "accepted" or "rejected"
    )
    .expect("metric can be created");

    pub static ref NEXT_EDIT_USER_EVENT_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new("au_next_edit_user_event_count", "Next Edit user event count"),
        &["status"]
    )
    .expect("metric can be created");

    pub static ref STICKY_SESSION_COUNT_GAUGE: IntGaugeVec = register_int_gauge_vec!(
        "au_api_proxy_sticky_session_count",
        "The number of active sticky sessions",
        &["endpoint"]
    )
    .expect("metric can be created");

    // Use Prometheus tags to build a (somewhat) dynamic metric mechanism for clients.
    // Some use case examples:
    // - success/failure metrics: write two different client_metric tags with the same prefix,
    //   e.g. "all_blobs_upload_success" and "all_blobs_upload_failure"; when an event occurs
    //   increment the appropriate counter by 1.
    // - sized event metrics: write a single client_metric tag and increment it by the associated
    //   amount, e.g. "bulk_upload_blobs" and the count of blobs.
    // In no case should the tag string itself vary based on client state. This is enforced by the
    // handler which has an allowlist of supported metrics.
    // A counter variant.
    pub static ref CLIENT_METRIC_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new("au_client_metrics", "Client metrics"),
        &["client_metric", "user_agent", "tenant_name"]
    )
    .expect("metric can be created");
    // A latency histogram variant. Follows the Prometheus convention of reporting in seconds,
    // but we expect clients to send a count of milliseconds in the `value` field.
    pub static ref CLIENT_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_client_latencies",
        "Client operation latencies",
        &["client_metric", "user_agent", "tenant_name"],
        // Buckets chosen for easily rememberable `le` values with a ~1.6x scaling factor.
        vec![0.005, 0.010, 0.016, 0.025, 0.040, 0.064, 0.100, 0.160, 0.250, 0.400, 0.640, 1.000, 1.600, 2.500, 4.000, 6.400, 10.000, 16.000, 25.000],
    )
    .expect("metric can be created");
    // "Gauge" style use cases (e.g. current_unknown_blobs_count) aren't supported yet.

    pub static ref THROTTLE_EVICTION_COUNT_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        "au_api_proxy_throttle_eviction_count",
        "The number of times a throttle cache entry was evicted",
        &["endpoint", "cause"]
    )
    .expect("metric can be created");

    pub static ref THROTTLE_COUNT_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        "au_api_proxy_throttle_count",
        "The number of times an endpoint was throttled",
        &["endpoint"]
    )
    .expect("metric can be created");

    pub static ref CHAT_IMAGE_REQUEST_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new("au_chat_image_request_count", "Number of chat requests containing images"),
        &["endpoint", "status_code", "request_source", "tenant_name"]
    )
    .expect("metric can be created");

    pub static ref CHAT_IMAGE_COUNT_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new("au_chat_image_count", "Total number of images in chat requests"),
        &["endpoint", "status_code", "request_source", "tenant_name"]
    )
    .expect("metric can be created");

    pub static ref CHAT_IMAGE_TOKEN_COUNT_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_chat_image_token_count",
        "Token count distribution for images in chat requests",
        &["endpoint", "status_code", "request_source", "tenant_name"],
        vec![54.0, 200.0, 350.0, 700.0, 1400.0, 2800.0, 5600.0, 11200.0]
    )
    .expect("metric can be created");

    // metric for circuit breaker failures
    pub static ref CIRCUIT_BREAKER_OPEN_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new("au_api_proxy_circuit_breaker_open_count", "Number of circuit breaker failures"),
        &["endpoint", "tenant_name"]
    )
    .expect("metric can be created");

    pub static ref TRIAL_ENDING_DISCLAIMER_COUNTER: IntCounterVec = register_int_counter_vec!(
        Opts::new("au_trial_ending_disclaimer_total", "Number of times trial ending disclaimer was shown to users"),
        &["tenant_name"]
    )
    .expect("metric can be created");

    pub static ref TRIAL_ENDED_MESSAGE_COUNTER: IntCounterVec = register_int_counter_vec!(
        Opts::new("au_trial_ended_message_total", "Number of times trial ended message was injected into responses"),
        &["tenant_name"]
    )
    .expect("metric can be created");

    pub static ref SUBSCRIPTION_BLOCK_COUNTER: IntCounterVec = register_int_counter_vec!(
        Opts::new("au_subscription_block_total", "Number of times users were blocked due to subscription status"),
        &["tenant_name"]
    )
    .expect("metric can be created");

    pub static ref MEMSTORE_INCR_FAILURE_COUNTER: IntCounterVec = register_int_counter_vec!(
        Opts::new("au_api_proxy_daily_limit_memstore_incr_failure_total", "Number of times memstore incr operation failed during daily request limit checks"),
        &["tenant_name"]
    )
    .expect("metric can be created");
}
