[package]
name = "api_proxy"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
actix-http = { workspace = true }
actix-web = { workspace = true }
actix-web-lab = { workspace = true }
async-lock = { workspace = true }
async-rwlock = { workspace = true }
async-trait =  { workspace = true }
awc = { workspace = true }
auth_query_client = { path = "../../../services/auth/query/client" }
bigtable_proxy_client = { path = "../../bigtable_proxy/client" }
blob_names = { path = "../../../base/blob_names/rust" }
chrono = {workspace = true}
clap = { version = "4.0", features = ["derive"] }
docset_client = { path = "../../../services/integrations/docset/client" }
feature-flags = { path = "../../../base/feature_flags" }
futures = { workspace = true }
github_processor_client = { path = "../../../services/integrations/github/processor/client" }
google-cloud-googleapis = {workspace = true}
grpc_client = { path = "../../../services/lib/grpc/client" }
hex = {workspace = true}
hmac = { version = "0.12" }
ipnet = {workspace = true}
itertools =  {workspace = true}
k8s-openapi = { workspace = true }
kube = { workspace = true }
lazy_static = { workspace = true }
metrics-server = { path = "../../../base/metrics_server/rust" }
memstore_client = { path = "../../../services/memstore/client" }
mockall = "0.13.1"
moka = { workspace = true }
opentelemetry = { workspace = true }
prometheus = {workspace = true}
prost = {workspace = true}
prost-wkt = {workspace = true}
prost-wkt-types = {workspace = true}
rand = { workspace = true }
remote_agents_client = { path = "../../../services/remote_agents/client" }
regex = {workspace = true}
request_context = { path = "../../lib/request_context" }
request_insight_publisher = { path = "../../../services/request_insight/publisher" }
schemars = {workspace = true}
secrecy = {workspace = true}
serde = {workspace = true}
serde_json = {workspace = true}
serde_urlencoded = {workspace = true}
sha2 = {workspace = true}
sha256 = {workspace = true}
grpc_stream_mux = { path = "../../lib/grpc/stream_mux" }
struct_logging = { path = "../../../base/logging" }
tokenbucket = { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true }
tonic = {workspace = true}
tonic-build  = { workspace = true }
tonic-health  = { workspace = true }
tonic-reflection  = { workspace = true }
tower = {workspace = true}
tracing = {workspace = true}
tracing-actix-web = { path = "../../../third_party/tracing-actix-web" }
tracing-tonic = { path = "../../../base/rust/tracing-tonic" }
tracing-opentelemetry = { workspace = true }
url = {workspace = true}
uuid = {workspace = true}
rustls = {workspace = true}
rustls-native-certs = {workspace = true}
rustls-pemfile = {workspace = true}
rustls-pki-types = {workspace = true}
base64 = {workspace = true}
image = {workspace = true}
content_manager_rs_proto = { path = "../../../services/content_manager" }
auth_entities_proto = { path = "../../../services/auth/central/server" }
content_manager_client = { path = "../../../services/content_manager/client" }
share_client = { path = "../../../services/share/client" }
blob_names_rs_proto = { path = "../../../base/blob_names" }
tenant_watcher_client = { path = "../../../services/tenant_watcher/client" }

[dev-dependencies]
actix-rt = {workspace = true}
assert_unordered = {workspace = true}
tonic-build  = { workspace = true }
prost-build = {workspace = true}
prost-wkt-build = {workspace = true}
serial_test = {workspace = true}
tokio = { workspace = true, features = ["test-util"] }

[build-dependencies]
tonic-build = { workspace = true }
