local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  local healthMetric = 'api_proxy_health_check_status_total';
  local healthSpec = {
    displayName: 'API Proxy health below 90%',
    conditionPrometheusQueryLanguage: {
      // Intentionally longer duration than the aggregation period to avoid alerting on blips
      duration: '300s',
      evaluationInterval: '60s',  // match default health check interval
      labels: { severity: 'error' },
      // The join to kube_namespace_created implements a 5-hour grace period for new tenants.
      // TODO: what's the actual cause of observed lags in new tenant health?
      query: |||
        sum by (namespace, cluster) (increase(%(metric)s{status="success"}[%(minutes)sm]))
        / sum by (namespace, cluster)(increase(%(metric)s{}[%(minutes)sm])) < 0.9
        and on (namespace, cluster) kube_namespace_created + 18000 < time()
      ||| % { metric: healthMetric, minutes: 2 },
    },
  };
  local modelHealthMetric = 'api_proxy_health_check_model_status_total';
  local modelHealthCheck(model, modelCondition, minutes) = {
    displayName: 'API Proxy %s model health below 90%%' % model,
    conditionPrometheusQueryLanguage: {
      // Intentionally longer duration than the aggregation period to avoid alerting on blips
      duration: '%ds' % ((minutes * 60) + 180),
      evaluationInterval: '60s',  // match default health check interval
      labels: { severity: 'warning' },
      query: |||
        sum by (model, namespace, cluster) (increase(%(metric)s{status="success", %(modelCondition)s}[%(minutes)sm]))
        / sum by (model, namespace, cluster)(increase(%(metric)s{%(modelCondition)s}[%(minutes)sm])) < 0.9
      ||| % { metric: modelHealthMetric, modelCondition: modelCondition, minutes: minutes },

    },
  };
  local agentHealthMetric = 'api_proxy_health_check_agent_status_total';
  local agentHealthCheck(endpoint, minutes) = {
    displayName: 'API Proxy %s agent health below 90%%' % endpoint,
    conditionPrometheusQueryLanguage: {
      // Intentionally longer duration than the aggregation period to avoid alerting on blips
      duration: '%ds' % ((minutes * 60) + 180),
      evaluationInterval: '60s',  // match default health check interval
      labels: { severity: 'warning' },
      query: |||
        sum by (endpoint, namespace, cluster) (increase(%(metric)s{status="success", endpoint="%(endpoint)s"}[%(minutes)sm]))
        / sum by (endpoint, namespace, cluster)(increase(%(metric)s{endpoint="%(endpoint)s"}[%(minutes)sm])) < 0.9
      ||| % { metric: agentHealthMetric, endpoint: endpoint, minutes: minutes },
    },
  };
  local remoteAgentHealthMetric = 'api_proxy_health_check_remote_agent_status_total';
  local remoteAgentHealthCheck(endpoint, minutes) = {
    displayName: 'API Proxy %s remote agent health below 90%%' % endpoint,
    conditionPrometheusQueryLanguage: {
      // Intentionally longer duration than the aggregation period to avoid alerting on blips
      duration: '%ds' % ((minutes * 60) + 180),
      evaluationInterval: '60s',  // match default health check interval
      labels: { severity: 'warning' },
      query: |||
        sum by (endpoint, namespace, cluster) (increase(%(metric)s{status="success", endpoint="%(endpoint)s"}[%(minutes)sm]))
        / sum by (endpoint, namespace, cluster)(increase(%(metric)s{endpoint="%(endpoint)s"}[%(minutes)sm])) < 0.9
      ||| % { metric: remoteAgentHealthMetric, endpoint: endpoint, minutes: minutes },
    },
  };
  local locationString = 'namespace %s cluster %s' % [monitoringLib.label('namespace'), monitoringLib.label('cluster')];
  local completionMatch = '(qwelden|elden).*';
  local nextEditMatch = 'raven.*';
  local chatMatch = '.*(-(chat|agent)|claude-instruction-.*)$';  // Chat team also owns Claude Instructions
  local smartPasteMatch = 'forger.*';
  local allCategorizedMatch = '(%s|%s|%s|%s)' % [completionMatch, nextEditMatch, chatMatch, smartPasteMatch];
  [
    monitoringLib.alertPolicy(cloud, healthSpec, 'api-proxy-health-check-overall', 'API health check failing for more than %s minutes in %s.' % [2, locationString]),
    monitoringLib.alertPolicy(cloud, modelHealthCheck('completion', 'model=~"%s"' % completionMatch, 2), 'api-proxy-health-check-completion', 'Completion model %s health check failing for more than %s minutes in %s.' % [
      monitoringLib.label('model'),
      2,
      locationString,
    ], team='completion'),
    monitoringLib.alertPolicy(cloud, modelHealthCheck('next edit', 'model=~"%s"' % nextEditMatch, 2), 'api-proxy-health-check-next-edit', 'Next edit model %s health check failing for more than %s minutes in %s.' % [
      monitoringLib.label('model'),
      2,
      locationString,
    ], team='next-edit'),
    monitoringLib.alertPolicy(cloud, modelHealthCheck('smart-paste', 'model=~"%s"' % smartPasteMatch, 2), 'api-proxy-health-check-smart-paste', 'Smart Paste model %s health check failing for more than %s minutes in %s.' % [
      monitoringLib.label('model'),
      2,
      locationString,
    ], team='chat'),
    monitoringLib.alertPolicy(cloud, modelHealthCheck('chat', 'model=~"%s"' % chatMatch, 35), 'api-proxy-health-check-chat', 'Chat model %s health check failing for more than %s minutes in %s.' % [
      monitoringLib.label('model'),
      35,  // Chat uses third party models and deliberately  rate-limits its health checks relative to completion and next edit.
      locationString,
    ], team='chat'),
    monitoringLib.alertPolicy(cloud, modelHealthCheck('uncategorized', 'model!~"%s"' % allCategorizedMatch, 35), 'api-proxy-health-check-model', 'Uncategorized model %s health check failing for more than %s minutes in %s.' % [
      monitoringLib.label('model'),
      35,  // For now assume uncategorized models also rate-limit health checks like chat models.
      locationString,
    ]),
    monitoringLib.alertPolicy(cloud, agentHealthCheck('list-remote-tools', 2), 'api-proxy-health-check-list-remote-tools', 'List remote tools agent health check failing for more than %s minutes in %s.' % [
      2,
      locationString,
    ]),
    monitoringLib.alertPolicy(cloud, remoteAgentHealthCheck('remote-agents/list', 2), 'api-proxy-health-check-remote-agents-list', 'List remote agents health check failing for more than %s minutes in %s.' % [
      2,
      locationString,
    ], team='remote-agents'),
  ]
