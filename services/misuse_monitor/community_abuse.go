package main

import (
	"context"
	"fmt"
	"strconv"

	"cloud.google.com/go/bigquery"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

// If true, search for users but don't actually suspend them.
var communityAbuseDryRunFlag = featureflags.NewBoolFlag("community_abuse_dry_run", true)

type CommunityAbuseJob struct {
	bqClient            *bigquery.Client
	datasetName         string
	jobName             string
	authClient          authclient.AuthClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	tenantCache         tenantwatcherclient.TenantCache
	featureFlagHandle   featureflags.FeatureFlagHandle
}

// Ensure MisuseMonitorJob implements the Job interface
var _ Job = (*CommunityAbuseJob)(nil)

func NewCommunityAbuseJob(
	ctx context.Context,
	projectId string,
	datasetName string,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	tenantCache tenantwatcherclient.TenantCache,
	featureFlagHandle featureflags.FeatureFlagHandle,
) (*CommunityAbuseJob, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !CheckDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, fmt.Errorf("error creating bigquery client: %w", err)
	}

	return &CommunityAbuseJob{
		bqClient:            bqClient,
		datasetName:         datasetName,
		jobName:             "community-duplicate",
		authClient:          authClient,
		tokenExchangeClient: tokenExchangeClient,
		tenantCache:         tenantCache,
		featureFlagHandle:   featureFlagHandle,
	}, nil
}

func (m *CommunityAbuseJob) Close() {
	m.bqClient.Close()
}

func (m *CommunityAbuseJob) Run(ctx context.Context) error {
	// Get users from BigQuery
	duplicates, err := m.getDuplicates(ctx)
	if err != nil {
		return fmt.Errorf("error getting malicious users: %w", err)
	}

	log.Info().Msgf("Total of %d users to process", len(duplicates))

	// Ban the users
	err = m.suspendDuplicates(ctx, duplicates)
	if err != nil {
		return fmt.Errorf("error banning users: %w", err)
	}

	return nil
}

type community_suspect struct {
	ID              string   `bigquery:"opaque_user_id"`
	TenantID        string   `bigquery:"tenant_id"`
	SessionIDs      []string `bigquery:"session_ids"`
	RequestCount    int      `bigquery:"request_count"`
	InactiveIds     []string `bigquery:"inactive_ids"`
	TrialIds        []string `bigquery:"trial_ids"`
	ProfessionalIds []string `bigquery:"professional_ids"`
	CommunityIds    []string `bigquery:"community_ids"`
	TeamIds         []string `bigquery:"team_ids"`
	EnterpriseIds   []string `bigquery:"enterprise_ids"`
}

func (m *CommunityAbuseJob) getDuplicates(ctx context.Context) ([]*community_suspect, error) {
	// Construct the query.
	query := m.bqClient.Query(`
	WITH
	-- aggregate by user activity
	activity AS (
		SELECT
			opaque_user_id,
			session_id,
			MIN(first_request_time) as first_request_time,
			MAX(last_request_time) as last_request_time,
			SUM(request_count) as request_count
		FROM request_aggregates_by_session
		GROUP BY 1,2
	),
	-- identify users
	user_id AS (
		SELECT
			id as opaque_user_id,
			tenant_ids[0] as tenant_id,
			orb_subscription_id as subscription_id,
			LOWER(email) as email,
			EXISTS(
				SELECT 1
				FROM UNNEST(suspensions) AS suspension
				WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_COMMUNITY_ABUSE'
			) as suspended,
			suspension_exempt as exempt
		FROM user
		WHERE ARRAY_LENGTH(tenant_ids) = 1
	),
	-- determine service tier
	tier AS (
		SELECT
			id as tenant_id,
			CASE
				WHEN tier = 'PROFESSIONAL' AND is_self_serve_team
					THEN 'TEAM'
				ELSE tier
			END AS tier
		FROM tenant
	),
	-- determine subscription type
	sub AS (
		SELECT
			subscription_id,
			CASE
				WHEN orb_status = 'ORB_STATUS_ACTIVE'
					THEN CASE
						WHEN external_plan_id = 'orb_trial_plan'
							THEN 'TRIAL'
						ELSE 'ACTIVE'
					END
				ELSE 'INACTIVE'
			END AS subscription_category
		FROM subscription
	),
	-- build user profile from all the above
	profile AS (
		SELECT
			user_id.opaque_user_id,
			user_id.tenant_id,
			user_id.subscription_id,
			user_id.email,
			user_id.suspended,
			user_id.exempt,
			activity.session_id,
			activity.request_count,
			activity.first_request_time,
			activity.last_request_time,
			CASE
				WHEN sub.subscription_category = 'ACTIVE'
					THEN tier.tier
				WHEN sub.subscription_category = 'INACTIVE'
					THEN CASE
						-- Enterprise and teams activity for inactive users always counts as enterprise or teams activity.
						WHEN tier.tier IN ('ENTERPRISE', 'TEAM')
							THEN tier.tier
						ELSE 'INACTIVE'
					END
				ELSE sub.subscription_category
			END as category,
			tier.tier,
			sub.subscription_category
		FROM user_id
		JOIN activity ON activity.opaque_user_id = user_id.opaque_user_id
		JOIN tier ON user_id.tenant_id = tier.tenant_id
		JOIN sub ON user_id.subscription_id = sub.subscription_id
	),
	-- Want to know all the session IDs used by each trial user.
	community AS (
		SELECT
			opaque_user_id,
			tenant_id,
			email,
			tier,
			subscription_category,
			MIN(first_request_time) as first_request_time,
			MAX(last_request_time) as last_request_time,
			ARRAY_AGG(DISTINCT session_id) as session_ids,
			SUM(request_count) as request_count
		FROM profile
		WHERE category = 'COMMUNITY'
		-- Skip suspended and exempt users
		AND NOT profile.suspended AND NOT profile.exempt
		GROUP BY 1, 2, 3, 4, 5
	),
	-- For each community user, what other users shared a session ID?
	suspects AS (
		SELECT
			community.opaque_user_id,
			community.tenant_id,
			community.session_ids,
			community.request_count,
			-- Lists of each category of connected users
			ARRAY_AGG(DISTINCT CASE WHEN other.category = 'INACTIVE' THEN other.opaque_user_id END IGNORE NULLS) AS inactive_ids,
			ARRAY_AGG(DISTINCT CASE WHEN other.category = 'TRIAL' THEN other.opaque_user_id END IGNORE NULLS) AS trial_ids,
			ARRAY_AGG(DISTINCT CASE WHEN other.category = 'PROFESSIONAL' THEN other.opaque_user_id END IGNORE NULLS) AS professional_ids,
			ARRAY_AGG(DISTINCT CASE WHEN other.category = 'COMMUNITY' THEN other.opaque_user_id END IGNORE NULLS) AS community_ids,
			ARRAY_AGG(DISTINCT CASE WHEN other.category = 'TEAM' THEN other.opaque_user_id END IGNORE NULLS) AS team_ids,
			ARRAY_AGG(DISTINCT CASE WHEN other.category = 'ENTERPRISE' THEN other.opaque_user_id END IGNORE NULLS) AS enterprise_ids,
		FROM community
		JOIN profile AS other
		ON other.session_id IN UNNEST(community.session_ids)
		AND community.opaque_user_id != other.opaque_user_id
		-- Don't match users with same email in case it's our error
		AND community.email != other.email
		GROUP BY 1, 2, 3, 4
		ORDER BY
			-- Deal with heaviest community users first
			ARRAY_LENGTH(community_ids) DESC, community.request_count DESC
	)
	SELECT * FROM suspects WHERE ARRAY_LENGTH(community_ids) > 0
	`)

	// Set the default dataset ID in the query config
	query.QueryConfig.DefaultDatasetID = m.datasetName
	query.Parameters = []bigquery.QueryParameter{}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, fmt.Errorf("error running query: %w", err)
	}

	// Parse the results.
	var suspects []*community_suspect
	for {
		var row community_suspect
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, fmt.Errorf("error parsing query results: %w", err)
		} else {
			suspects = append(suspects, &row)
		}
	}
	log.Info().Msgf("Found %d freeTrialDuplicates to ban", len(suspects))
	return suspects, nil
}

func (m *CommunityAbuseJob) suspendDuplicates(
	ctx context.Context,
	suspects []*community_suspect,
) error {
	dryRun, err := communityAbuseDryRunFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting dry run flag, defaulting to true")
		dryRun = true
	}
	if dryRun {
		log.Info().Msg("*** DRY RUN! Not suspending users. ***")
	}

	MisuseUsersFound.WithLabelValues(m.jobName).Set(float64(len(suspects)))

	// Limit users suspended per execution
	suspensionsToIssue := 1000

	sessionId := requestcontext.NewRandomRequestSessionId()
	for _, suspect := range suspects {

		tenant, err := m.tenantCache.GetTenant(suspect.TenantID)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "tenant_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Err(err).Msgf("Error getting tenant %s", suspect.TenantID)
			continue
		}

		// Do the blocking.
		token, err := m.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
			ctx, suspect.TenantID, tenant.ShardNamespace, []tokenexchangepb.Scope{tokenexchangepb.Scope_AUTH_RW},
		)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "token_exchange_error", strconv.FormatBool(dryRun)).Inc()
			log.Error().Err(err).Msgf("Error getting token for tenant %s", suspect.TenantID)
			continue
		}
		requestCtx := requestcontext.New(
			requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token)

		// Suspensions are issued for suspect community accounts only.
		if len(suspect.CommunityIds) == 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "ineligible_suspect", strconv.FormatBool(dryRun)).Inc()
			log.Info().Msgf("Skipping suspect user %s in tenant %s, no community duplicates", suspect.ID, suspect.TenantID)
			continue
		}

		// Check if the user is exempt or already suspended for  community abuse
		userObj, err := m.authClient.GetUser(ctx, requestCtx, suspect.ID, &suspect.TenantID)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "user_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Msgf("Error getting user for suspension for user %s in tenant %s: %v", suspect.ID, suspect.TenantID, err)
			continue
		}
		if userObj.SuspensionExempt {
			log.Info().Msgf("User %s is suspension exempt in tenant %s", suspect.ID, suspect.TenantID)
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_exempt", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if userObj.Suspensions != nil && len(userObj.Suspensions) > 0 {
			alreadySuspended := false
			for _, suspension := range userObj.Suspensions {
				if suspension.SuspensionType == auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_COMMUNITY_ABUSE {
					log.Info().Msgf("User %s is already suspended for community abuse in tenant %s", suspect.ID, suspect.TenantID)
					MisuseActionOutcome.WithLabelValues(m.jobName, "already_suspended", strconv.FormatBool(dryRun)).Inc()
					alreadySuspended = true
					break
				}
			}
			if alreadySuspended {
				continue
			}
		}

		evidence := fmt.Sprintf("Duplicate accounts with community: Shared session IDs: %v inactive: %d trial: %d professional: %d community: %d team: %d enterprise: %d",
			suspect.SessionIDs, len(suspect.InactiveIds), len(suspect.TrialIds), len(suspect.ProfessionalIds), len(suspect.CommunityIds), len(suspect.TeamIds), len(suspect.EnterpriseIds))
		log.Info().Msgf("Misuse monitor detected community duplication by user %s in tenant %s. %s",
			suspect.ID, suspect.TenantID, evidence)
		if !dryRun && suspensionsToIssue > 0 {
			// Issue community abuse suspension
			suspensionID, _, err := m.authClient.CreateUserSuspension(
				ctx, requestCtx, suspect.ID, suspect.TenantID, auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_COMMUNITY_ABUSE, evidence)
			if err != nil {
				log.Error().Msgf("Error creating suspension for user %s from tenant %s: %v", suspect.ID, suspect.TenantID, err)
				MisuseActionOutcome.WithLabelValues(m.jobName, "create_suspension_error", strconv.FormatBool(dryRun)).Inc()
				continue
			}
			suspensionsToIssue--
			log.Info().Msgf("Created suspension %s for user %s in tenant %s", suspensionID, suspect.ID, suspect.TenantID)
		}
		MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_created", strconv.FormatBool(dryRun)).Inc()
	}

	return nil
}
