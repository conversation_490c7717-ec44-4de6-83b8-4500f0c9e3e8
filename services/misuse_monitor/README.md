# Misuse Monitor

This directory contains the misuse-monitor service.

This service runs jobs to periodically detect usage patterns that indicate misuse of the system. Users are banned or suspended from the system appropriately.

## Jobs

### API Misuse

The API misuse job detects users who are making a large number of API requests in a short period of time. This is often an indication of a misbehaving bot or a user who has lost control of their API key.

### Free Trial Duplication

The free trial duplication job detects users who are creating multiple free trial accounts. This is detected by observing the same session ID used by a free trial account and other individual user accounts.

### Community Abuse

The community abuse job detects users who are abusing the community program by using multiple community accounts. This is detected by observing the same session ID across multiple community accounts.

## Adding new jobs

To add a new job, implement the `Job` interface in `misuse_monitor.go`. Then, add a new entry to the `Jobs` array in `deploy.jsonnet` to configure the job's execution interval. Job execution intervals should be balanced between the cost of running the job and the cost of allowing misuse to continue unchecked.

## GRPC

The service exposes a GRPC API for querying job status and triggering jobs on demand. Use GRPC debug to introspect the API.
