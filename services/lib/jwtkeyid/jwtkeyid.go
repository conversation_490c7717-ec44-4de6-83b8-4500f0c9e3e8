package jwtkeyid

import (
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
)

const (
	numBytesPrefix = 9
	keyIDLength    = 12
)

// Returns the key ID from the given certificate. The key ID is the base64
// encoding of the first 12 bytes of the SHA256 of the signature of the
// certificate. Since it is based on the signature, it does not require us to
// store any extra state.
func GetKeyIDFromCert(cert *x509.Certificate) string {
	// Use a SHA256 hash of the signature as the key ID
	hash := sha256.Sum256(cert.Signature)
	hashBytes := hash[:numBytesPrefix]
	keyID := base64.StdEncoding.EncodeToString(hashBytes)
	return keyID
}
