use hyper::Request;
use prometheus::{<PERSON>to<PERSON><PERSON><PERSON>, IntGaugeVec};
use request_context::TenantInfo;
use std::{
    collections::HashMap,
    pin::Pin,
    sync::{<PERSON>, Mutex},
    task::{Context, Poll},
    time::Instant,
};
use tonic::body::BoxBody;
use tower::{Layer, Service};

/// Middleware to intercept gRPC requests/responses and record metrics. Adapted from the tower
/// documentation examples:
/// https://github.com/hyperium/tonic/blob/master/examples/src/tower/server.rs
#[derive(Debug, Clone)]
pub struct MetricsMiddlewareLayer {
    rpc_latency_collector: &'static HistogramVec,
    active_requests_collector: &'static IntGaugeVec,
    monitors: Arc<Mutex<HashMap<String, tokio_metrics_collector::TaskMonitor>>>,
}

impl MetricsMiddlewareLayer {
    pub fn new(
        rpc_latency_collector: &'static HistogramVec,
        active_requests_collector: &'static IntGaugeVec,
    ) -> Self {
        let monitors = Arc::new(Mutex::new(HashMap::new()));

        Self {
            rpc_latency_collector,
            active_requests_collector,
            monitors,
        }
    }
}

impl<S> Layer<S> for MetricsMiddlewareLayer {
    type Service = MetricsMiddleware<S>;

    fn layer(&self, service: S) -> Self::Service {
        MetricsMiddleware {
            inner: service,
            rpc_latency_collector: self.rpc_latency_collector,
            active_requests_collector: self.active_requests_collector,
            monitors: self.monitors.clone(),
        }
    }
}

#[derive(Debug, Clone)]
pub struct MetricsMiddleware<S> {
    inner: S,
    rpc_latency_collector: &'static HistogramVec,
    active_requests_collector: &'static IntGaugeVec,
    monitors: Arc<Mutex<HashMap<String, tokio_metrics_collector::TaskMonitor>>>,
}

impl<S> MetricsMiddleware<S> {
    fn monitor(&self, endpoint: &str) -> tokio_metrics_collector::TaskMonitor {
        let mut monitors = self.monitors.lock().unwrap();
        monitors
            .entry(endpoint.to_string())
            .or_insert_with(|| {
                let m = tokio_metrics_collector::TaskMonitor::new();
                let task_collector = tokio_metrics_collector::default_task_collector();
                task_collector
                    .add(endpoint, m.clone())
                    .expect("cannot add task collector");
                m
            })
            .clone()
    }
}

type BoxFuture<'a, T> = Pin<Box<dyn std::future::Future<Output = T> + Send + 'a>>;

impl<S> Service<Request<BoxBody>> for MetricsMiddleware<S>
where
    S: Service<Request<BoxBody>, Response = hyper::Response<BoxBody>> + Clone + Send + 'static,
    S::Future: Send + 'static,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = BoxFuture<'static, Result<Self::Response, Self::Error>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, req: Request<BoxBody>) -> Self::Future {
        // This is necessary because tonic internally uses `tower::buffer::Buffer`.
        // See https://github.com/tower-rs/tower/issues/547#issuecomment-767629149
        // for details on why this is necessary
        let clone = self.inner.clone();
        let mut inner = std::mem::replace(&mut self.inner, clone);

        let start = Instant::now();
        let endpoint = req.uri().path().to_string();
        let request_source = get_request_source(req.headers()).to_string();
        let rpc_latency_collector: &'static HistogramVec = self.rpc_latency_collector;
        let active_requests_collector: &'static IntGaugeVec = self.active_requests_collector;

        let monitor = self.monitor(&endpoint);
        Box::pin(monitor.instrument(async move {
            let metrics_tenant_name = req
                .extensions()
                .get::<TenantInfo>()
                .map(|t| t.metrics_tenant_name())
                .unwrap_or("unknown")
                .to_string();
            let (_package, service, method) = parse_grpc_method(&endpoint);

            active_requests_collector
                .with_label_values(&[service, method, &metrics_tenant_name])
                .inc();

            let response = inner.call(req).await;

            // Decrement this before checking the response for errors
            active_requests_collector
                .with_label_values(&[service, method, &metrics_tenant_name])
                .dec();

            let response = response?;

            let status_code = get_status_code(response.headers());

            rpc_latency_collector
                .with_label_values(&[
                    service,
                    method,
                    status_code,
                    &request_source,
                    &metrics_tenant_name,
                ])
                .observe(start.elapsed().as_secs_f64());

            Ok(response)
        }))
    }
}

/// Optimistically parse a gRPC method name into package, service, and method.
fn parse_grpc_method(endpoint: &str) -> (&str, &str, &str) {
    // Imitates Python implementation: https://github.com/d5h-foss/grpc-interceptor/blob/d866a1bd09e9e79a02e0ed42a9d59f30c85bb7bc/src/grpc_interceptor/server.py#L232
    let (package_and_service, method) = endpoint.rsplit_once('/').unwrap_or(("", endpoint));
    let (package, service) = package_and_service
        .rsplit_once('.')
        .unwrap_or(("", package_and_service));
    (package, service, method)
}

/// Get the request source from the request headers.
fn get_request_source(headers: &hyper::HeaderMap) -> &str {
    match headers.get("x-request-source") {
        Some(value) => value.to_str().unwrap_or("unknown"),
        None => "unknown",
    }
}

/// Get the gRPC status code from response headers.
fn get_status_code(headers: &hyper::HeaderMap) -> &str {
    // A nicer implementation of this function would be to convert the header value into a
    // tonic::Code, but tonic::Code overrides to_string() in a way that doesn't give nice metrics
    // labels.
    match headers.get("grpc-status") {
        Some(value) => {
            // Default to UNKNOWN if we fail to parse for some reason.
            let code: &str = value.to_str().unwrap_or("2");
            match code {
                "0" => "OK",
                "1" => "CANCELLED",
                "2" => "UNKNOWN",
                "3" => "INVALID_ARGUMENT",
                "4" => "DEADLINE_EXCEEDED",
                "5" => "NOT_FOUND",
                "6" => "ALREADY_EXISTS",
                "7" => "PERMISSION_DENIED",
                "8" => "RESOURCE_EXHAUSTED",
                "9" => "FAILED_PRECONDITION",
                "10" => "ABORTED",
                "11" => "OUT_OF_RANGE",
                "12" => "UNIMPLEMENTED",
                "13" => "INTERNAL",
                "14" => "UNAVAILABLE",
                "15" => "DATA_LOSS",
                "16" => "UNAUTHENTICATED",
                _ => "UNKNOWN",
            }
        }
        None => {
            // This is expected. Tonic doesn't set the grpc-status for successful requests.
            "OK"
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_grpc_method() {
        assert_eq!(
            parse_grpc_method("foo.bar.Baz/method"),
            ("foo.bar", "Baz", "method")
        );
        assert_eq!(
            parse_grpc_method("/foo.bar.Baz/method"),
            ("/foo.bar", "Baz", "method")
        );
        assert_eq!(parse_grpc_method("Baz/method"), ("", "Baz", "method"));
    }
}
