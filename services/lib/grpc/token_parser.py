"""Helper functions for parsing service token arguments."""

import argparse
import pathlib
import json
import logging
import getpass
import pydantic


def _get_cloud_info():
    return json.loads(
        pathlib.Path("deploy/common/cloud_info.json").read_text(encoding="utf-8")
    )


def _get_support_url(namespace: str, tenant_name: str, cloud: str):
    ci = _get_cloud_info()
    # find internal domain
    domain_suffix = ci[cloud]["internalDomainSuffix"]
    print(domain_suffix)
    return f"https://support.{namespace}.t.{domain_suffix}/service-token?tenant_name={tenant_name}"


def get_token(
    args: argparse.Namespace, namespace_arg_name: str = "namespace"
) -> pydantic.SecretStr:
    """Get the token.

    Args:
        args: The arguments. It is expected that add_token_args has been called on the parser.
        namespace_arg_name: The name of the namespace argument. Usually namespace (default) or shard_namespace

    Returns:
        The token.
    """
    p: pathlib.Path | None = args.auth_token_file
    if args.request_token or not p:
        if args.tenant_name is None:
            raise argparse.ArgumentError(
                None, "Must specify --tenant-name when requesting a token"
            )
        namespace = getattr(args, namespace_arg_name)
        url = _get_support_url(namespace, args.tenant_name, args.cloud)
        logging.info("Visit and Copy the token to the clipboard")
        logging.info("Genie permissions are required to get a token")
        logging.info("%s", url)

        secret = getpass.getpass("> ")
        if not secret:
            raise argparse.ArgumentError(None, "No secret provided")

        if p:
            if p.exists():
                if p.stat().st_mode & 0o777 != 0o600:
                    logging.warning("Changing permissions of %s", p)
                p.chmod(0o600)
            else:
                p.touch(mode=0o600)  # to restrict access
            p.write_text(secret)
            logging.info("Wrote token to %s", p)
        return pydantic.SecretStr(secret)
    else:
        if not p.is_absolute():
            raise argparse.ArgumentError(None, "Path must be absolute")
        if not p.exists():
            raise argparse.ArgumentError(
                None, f"File {p} does not exist: Use --request-token to get one"
            )
        return pydantic.SecretStr(
            p.read_text(encoding="utf-8").splitlines()[0].rstrip()
        )


def add_token_args(
    parser: argparse.ArgumentParser,
):
    """Add arguments for service token.

    This function adds the following arguments to the parser:
    - --auth-token-file: Path to file containing authentication token
    - --request-token: If set, the user is asked to visit a URL and copy a token to the clipboard.
    - --tenant-name: The tenant name to use.

    Args:
        parser: The parser to add the arguments to.

    Returns:
        None
    """
    group = parser.add_argument_group(
        "Service Token Arguments",
        description="""Service Token arguments for the service:""",
    )
    group.add_argument(
        "--auth-token-file",
        type=pathlib.Path,
        help="Path to file containing authentication token, or if --request-token is true, to write the new authentication token",
    )
    group.add_argument("--request-token", action="store_true")
    group.add_argument(
        "--tenant-name",
        help="The tenant name to use",
    )
