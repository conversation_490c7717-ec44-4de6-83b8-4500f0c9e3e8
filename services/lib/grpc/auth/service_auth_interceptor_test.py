"""Tests for the service_auth_interceptor module."""

import asyncio  # Required for async tests
import logging
import typing
from dataclasses import dataclass
from unittest.mock import AsyncMock, Mock, patch  # patch may be used in future tests

import grpc
import pydantic  # Used in assertions
import pytest  # Required for asyncio marks

from services.lib.grpc.auth.service_auth import AuthInfo, ServiceAuthException
from services.lib.grpc.auth.service_auth_interceptor import (
    ServiceAuthAsyncInterceptor,
    ServiceAuthInterceptor,
    get_auth_info_from_grpc_context,
)

logging.basicConfig(level=logging.DEBUG)


# mock for a private type of grpc
@dataclass
class Metadata:
    """Mock for grpc.Metadata"""

    key: str
    value: typing.Any


def test_service_auth_interceptor():
    """Tests the ServiceAuthInterceptor implementation."""

    auth = Mock()
    auth.validate_access.return_value = AuthInfo(
        tenant_id=None,
        tenant_name="test-tenant",
        shard_namespace="test-ns",
        cloud="test-cloud",
    )
    context = Mock()
    context.peer_identities.return_value = [b"test-peer-1"]
    context.invocation_metadata.return_value = [
        Metadata("authorization", "Bearer test-token")
    ]
    context.time_remaining.return_value = None
    request = Mock()
    interceptor = ServiceAuthInterceptor(auth)

    def test_method(request, context):
        return "test"

    assert (
        interceptor.intercept(
            test_method, request, context, "/test.TestService/TestMethod"
        )
        == "test"
    )
    auth.validate_access.assert_called_with(
        peer_identities=[b"test-peer-1"],
        auth_token=pydantic.SecretStr("test-token"),
        method_name="/test.TestService/TestMethod",
    )

    auth.validate_access.reset_mock()
    auth.validate_access.side_effect = ServiceAuthException(
        grpc.StatusCode.PERMISSION_DENIED, "Access denied"
    )
    context.peer_identities.return_value = [b"test-peer-2"]
    interceptor.intercept(test_method, request, context, "/test.TestService/TestMethod")
    context.abort.assert_called_with(grpc.StatusCode.PERMISSION_DENIED, "Access denied")


def test_service_auth_interceptor_with_tenant_id():
    """Tests the ServiceAuthInterceptor implementation."""

    auth = Mock()
    auth.validate_access.return_value = AuthInfo(
        tenant_id="**********",
        tenant_name="test-tenant",
        shard_namespace="test-ns",
        cloud="test-cloud",
    )
    context = Mock()
    context.peer_identities.return_value = [b"test-peer-1"]
    context.invocation_metadata.return_value = [
        Metadata("authorization", "Bearer test-token")
    ]
    context.time_remaining.return_value = None
    request = Mock()
    interceptor = ServiceAuthInterceptor(auth)

    def test_method(request, context):
        return "test"

    assert (
        interceptor.intercept(
            test_method, request, context, "/test.TestService/TestMethod"
        )
        == "test"
    )
    auth.validate_access.assert_called_with(
        peer_identities=[b"test-peer-1"],
        auth_token=pydantic.SecretStr("test-token"),
        method_name="/test.TestService/TestMethod",
    )

    auth.validate_access.reset_mock()
    auth.validate_access.side_effect = ServiceAuthException(
        grpc.StatusCode.PERMISSION_DENIED, "Access denied"
    )
    context.peer_identities.return_value = [b"test-peer-2"]
    interceptor.intercept(test_method, request, context, "/test.TestService/TestMethod")
    context.abort.assert_called_with(grpc.StatusCode.PERMISSION_DENIED, "Access denied")


def test_get_auth_info_from_grpc_context():
    """Tests the get_auth_info_from_grpc_context function."""
    context = Mock()
    context.invocation_metadata.return_value = [
        Metadata("x-tenant-name", "test-tenant"),
        Metadata("x-tenant-id", "**********"),
        Metadata("x-shard-namespace", "test-ns"),
        Metadata("x-cloud", "test-cloud"),
        Metadata("x-scopes", "scope1,scope2"),
    ]
    context.time_remaining.return_value = None
    auth_info = get_auth_info_from_grpc_context(context)
    assert auth_info.tenant_name == "test-tenant"
    assert auth_info.tenant_id == "**********"
    assert auth_info.scopes == ["scope1", "scope2"]


@pytest.mark.asyncio
async def test_service_auth_async_interceptor():
    """Tests the ServiceAuthAsyncInterceptor implementation with a successful auth."""
    auth = Mock()
    auth.validate_access.return_value = AuthInfo(
        tenant_id=None,
        tenant_name="test-tenant",
        shard_namespace="test-ns",
        cloud="test-cloud",
    )
    context = Mock()
    context.peer_identities.return_value = [b"test-peer-1"]
    context.invocation_metadata.return_value = [
        Metadata("authorization", "Bearer test-token")
    ]
    context.time_remaining.return_value = None
    request = Mock()
    interceptor = ServiceAuthAsyncInterceptor(auth)

    # Test with a coroutine method
    async def test_async_method(request, context):
        return "test-async"

    result = await interceptor.intercept(
        test_async_method, request, context, "/test.TestService/TestMethod"
    )
    assert result == "test-async"
    auth.validate_access.assert_called_with(
        peer_identities=[b"test-peer-1"],
        auth_token=pydantic.SecretStr("test-token"),
        method_name="/test.TestService/TestMethod",
    )


@pytest.mark.asyncio
async def test_service_auth_async_interceptor_with_auth_exception():
    """Tests the ServiceAuthAsyncInterceptor implementation with an auth exception."""
    auth = Mock()
    auth.validate_access.side_effect = ServiceAuthException(
        grpc.StatusCode.PERMISSION_DENIED, "Access denied"
    )

    # Use regular Mock instead of AsyncMock for methods that return values directly
    context = Mock()
    context.peer_identities.return_value = [b"test-peer-2"]
    context.invocation_metadata.return_value = [
        Metadata("authorization", "Bearer test-token")
    ]
    context.time_remaining.return_value = None

    # Make abort an async method that returns a coroutine
    # Parameters are unused but required for the signature
    async def async_abort(code, details):  # pylint: disable=unused-argument
        pass

    # Set up the abort method as an AsyncMock
    context.abort = AsyncMock()
    context.abort.side_effect = async_abort

    request = Mock()
    interceptor = ServiceAuthAsyncInterceptor(auth)

    async def test_async_method(request, context):
        return "test-async"

    # This should raise the exception and call context.abort
    await interceptor.intercept(
        test_async_method, request, context, "/test.TestService/TestMethod"
    )

    # Check that abort was called with the right arguments
    context.abort.assert_called_with(grpc.StatusCode.PERMISSION_DENIED, "Access denied")


@pytest.mark.asyncio
async def test_service_auth_async_interceptor_with_excluded_path():
    """Tests the ServiceAuthAsyncInterceptor implementation with an excluded path."""
    auth = Mock()
    context = Mock()
    request = Mock()
    interceptor = ServiceAuthAsyncInterceptor(
        auth, exclude_paths=["/grpc.health.v1.Health/Check"]
    )

    # Test with a direct return value (not a coroutine)
    def test_sync_method(request, context):
        return "test-sync"

    result = await interceptor.intercept(
        test_sync_method, request, context, "/grpc.health.v1.Health/Check"
    )
    assert result == "test-sync"
    auth.validate_access.assert_not_called()


@pytest.mark.asyncio
async def test_service_auth_async_interceptor_with_async_iterator():
    """Tests the ServiceAuthAsyncInterceptor with a method that returns an async iterator."""
    auth = Mock()
    auth.validate_access.return_value = AuthInfo(
        tenant_id=None,
        tenant_name="test-tenant",
        shard_namespace="test-ns",
        cloud="test-cloud",
    )
    context = Mock()
    context.peer_identities.return_value = [b"test-peer-1"]
    context.invocation_metadata.return_value = [
        Metadata("authorization", "Bearer test-token")
    ]
    context.time_remaining.return_value = None
    request = Mock()
    interceptor = ServiceAuthAsyncInterceptor(auth)

    # Create a class that implements __aiter__ to simulate a server streaming response
    class AsyncIteratorResponse:
        def __init__(self, items):
            self.items = items

        def __aiter__(self):
            return self

        async def __anext__(self):
            if not self.items:
                raise StopAsyncIteration
            return self.items.pop(0)

    async def test_streaming_method(request, context):
        return AsyncIteratorResponse(["item1", "item2", "item3"])

    result = await interceptor.intercept(
        test_streaming_method, request, context, "/test.TestService/StreamingMethod"
    )

    # Verify the result is an async iterator
    assert hasattr(result, "__aiter__")

    # Collect the items from the iterator
    items = []
    async for item in result:
        items.append(item)

    assert items == ["item1", "item2", "item3"]
    auth.validate_access.assert_called_with(
        peer_identities=[b"test-peer-1"],
        auth_token=pydantic.SecretStr("test-token"),
        method_name="/test.TestService/StreamingMethod",
    )


@pytest.mark.asyncio
async def test_service_auth_async_interceptor_excluded_path_with_async_iterator():
    """Tests the ServiceAuthAsyncInterceptor with an excluded path and async iterator response."""
    auth = Mock()
    context = Mock()
    request = Mock()
    interceptor = ServiceAuthAsyncInterceptor(
        auth, exclude_paths=["/grpc.health.v1.Health/Watch"]
    )

    # Create a class that implements __aiter__ to simulate a server streaming response
    class AsyncIteratorResponse:
        def __init__(self, items):
            self.items = items

        def __aiter__(self):
            return self

        async def __anext__(self):
            if not self.items:
                raise StopAsyncIteration
            return self.items.pop(0)

    async def test_streaming_method(request, context):
        return AsyncIteratorResponse(["health1", "health2"])

    result = await interceptor.intercept(
        test_streaming_method, request, context, "/grpc.health.v1.Health/Watch"
    )

    # Verify the result is an async iterator
    assert hasattr(result, "__aiter__")

    # Collect the items from the iterator
    items = []
    async for item in result:
        items.append(item)

    assert items == ["health1", "health2"]
    auth.validate_access.assert_not_called()


@pytest.mark.asyncio
async def test_service_auth_async_interceptor_with_sync_method():
    """Tests the ServiceAuthAsyncInterceptor with a synchronous method."""
    auth = Mock()
    auth.validate_access.return_value = AuthInfo(
        tenant_id="**********",
        tenant_name="test-tenant",
        shard_namespace="test-ns",
        cloud="test-cloud",
    )
    context = Mock()
    context.peer_identities.return_value = [b"test-peer-1"]
    context.invocation_metadata.return_value = [
        Metadata("authorization", "Bearer test-token")
    ]
    context.time_remaining.return_value = None
    request = Mock()
    interceptor = ServiceAuthAsyncInterceptor(auth)

    # Test with a synchronous method
    def test_sync_method(request, context):
        return "test-sync"

    result = await interceptor.intercept(
        test_sync_method, request, context, "/test.TestService/SyncMethod"
    )
    assert result == "test-sync"
    auth.validate_access.assert_called_with(
        peer_identities=[b"test-peer-1"],
        auth_token=pydantic.SecretStr("test-token"),
        method_name="/test.TestService/SyncMethod",
    )
