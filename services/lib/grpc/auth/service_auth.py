"""A Python client library for the auth service."""

from functools import cached_property
import json
import logging
import re
import typing
from contextlib import contextmanager
from dataclasses import dataclass, field

import grpc
import pydantic
import structlog

from services.auth.central.server import auth_entities_pb2

SELF_SERVE_NAMESPACE_REGEX = re.compile(r"[id][0-9]+")


@dataclass
class AuthInfo:
    """Information about the caller."""

    tenant_id: str | None
    """The ID of the tenant the caller is allowed to access."""

    tenant_name: str
    """The name of the tenant the caller is allowed to access."""

    shard_namespace: str
    """The namespace the token is for, usually the namespace that is currently responsible for the tenant ID above."""

    cloud: str
    """The cloud that this namespace/tenant is in."""

    scopes: list[str] = field(default_factory=list)
    """Names of the scopes associated with the caller."""

    user_id: pydantic.SecretStr | None = None
    """The ID of the user the caller is allowed to access.

    The user id might not be set if the call is not on behalf of a user

    The user id is PII and should not be logged
    """

    opaque_user_id: str | None = None
    """The opaque user ID of the user the caller is allowed to access.

    The opaque user ID is an opaque ID of type as described by
    opaque_user_id_type. This ID can have different meanings, depending on the
    value of opaque_user_id_type.

    This is not PII as it is an opaque identifier.
    """

    opaque_user_id_type: str | None = None

    user_email: pydantic.SecretStr | None = None
    """The email of the user the caller is allowed to access.

    The email is PII and should not be logged.
    """

    service_name: str | None = None

    @cached_property
    def metrics_tenant_name(self) -> str:
        return (
            "[self-serve]"
            if SELF_SERVE_NAMESPACE_REGEX.match(self.shard_namespace)
            else self.tenant_name
        )

    @cached_property
    def iap_email(self) -> str | None:
        """Returns the email address of the user if the token is for an IAP user."""
        opaque_user_id = self.parsed_opaque_user_id
        if (
            opaque_user_id
            and opaque_user_id.user_id_type == auth_entities_pb2.UserId.INTERNAL_IAP
        ):
            if not opaque_user_id.user_id.startswith("iap:"):
                logging.error(
                    "Invalid format for internal IAP user ID %s", opaque_user_id.user_id
                )
                return None
            return opaque_user_id.user_id.partition("iap:")[2]
        return None

    @cached_property
    def parsed_opaque_user_id(self) -> auth_entities_pb2.UserId | None:
        if not self.opaque_user_id or not self.opaque_user_id_type:
            return None
        try:
            return auth_entities_pb2.UserId(
                user_id=self.opaque_user_id,
                user_id_type=auth_entities_pb2.UserId.UserIdType.Value(
                    self.opaque_user_id_type
                ),
            )
        except ValueError:
            logging.error(
                "Failed to parse opaque user id type %s", self.opaque_user_id_type
            )
            return None

    def bind_context_logging(self):
        """Binds the auth info to the log context."""
        structlog.contextvars.bind_contextvars(
            tenant_id=self.tenant_id if self.tenant_id else "",
            tenant_name=self.tenant_name,
            opaque_user_id=self.opaque_user_id,
        )

    @contextmanager
    def with_context_logging(self):
        """Binds the auth info to the log context and yields."""
        r = structlog.contextvars.bind_contextvars(
            tenant_id=self.tenant_id if self.tenant_id else "",
            tenant_name=self.tenant_name,
            opaque_user_id=self.opaque_user_id,
        )
        yield
        structlog.contextvars.unbind_contextvars(*r.keys())


class ServiceAuthException(grpc.RpcError):
    """Exception raised when the auth service returns an error."""

    def __init__(self, status_code: grpc.StatusCode, msg: str):
        self.status_code = status_code
        self.msg = msg

    def code(self) -> grpc.StatusCode:
        """Returns the status code."""
        return self.status_code

    def details(self) -> str:
        """Returns the error message."""
        return self.msg


class ServiceAuth(typing.Protocol):
    """Interface for the auth service."""

    def validate_access(
        self,
        peer_identities: list[bytes] | None,
        auth_token: pydantic.SecretStr | None,
        method_name: str,
    ) -> AuthInfo:
        """Validates access to a tenant.

        If the object cannot authenticate the caller, it should raise a ServiceAuthException.

        Args:
            peer_identities: The peer identities of the caller. These are the commonName and dnsNames
                from the client MTLS certificate if MTLS is used. see deploy/common/cert-lib.jsonnet's createClientCert.
                When peer_identities is None, the caller is not authenticated with MTLS.
            auth_token: The auth token of the caller from the Bearer authorization header if set
            method_name: The name of the method to validate.

        Returns:
            An AuthInfo object with the tenant name if the caller is allowed to call the method,
        """
        raise NotImplementedError()


class RejectAllServiceAuth(ServiceAuth):
    """Rejects all calls."""

    def validate_access(
        self,
        peer_identities: list[bytes] | None,
        auth_token: pydantic.SecretStr | None,
        method_name: str,
    ) -> AuthInfo:
        del peer_identities, auth_token, method_name
        raise ServiceAuthException(grpc.StatusCode.PERMISSION_DENIED, "Access denied")


class AuthPeerIdentifyConfig(pydantic.BaseModel):
    """Configuration for the AuthPeerIdentityServiceAuth."""

    peer_identities: list[str]
    """The peer identities to allowed by this configuration."""

    method: str | None
    """The method regular expression to allow by this configuration.

    If not set, all methods are allowed.
    """

    def match(self, peer_identity: str, method_name: str) -> bool:
        """Checks if the peer identity and method name match."""
        if self.method:
            if not re.match(self.method, method_name):
                return False
        return peer_identity in self.peer_identities


class PeerIdentifyConfig(pydantic.BaseModel):
    """Configuration for the PeerIdentityServiceAuth."""

    rules: list[AuthPeerIdentifyConfig]
    """The list of AuthPeerIdentifyConfig to allow."""

    reject_no_peer_identity: bool = True
    """If true, rejects calls without peer identity."""


def load_peer_identity_auth_config_from_json(json_str: str) -> PeerIdentifyConfig:
    """Loads a list of PeerIdentifyConfig from a JSON string."""

    json_data = json.loads(json_str)
    return PeerIdentifyConfig.parse_obj(json_data)


class PeerIdentityServiceAuth(ServiceAuth):
    """ServiceAuth implementation that uses the peer identity to validate access."""

    def __init__(
        self,
        allow_list: list[AuthPeerIdentifyConfig],
        reject_no_peer_identity: bool,
        default_tenant_name: str,
    ):
        """
        Constructor.

        Args:
            allow_list: A list of AuthPeerIdentifyConfig to allow.
            reject_no_peer_identity: If true, rejects calls without peer identity.
        """
        self.allow_list = allow_list
        self.reject_no_peer_identity = reject_no_peer_identity
        self.default_tenant_name = default_tenant_name
        logging.info(
            "Setting up PeerIdentityServiceAuth with allow_list=%s, reject_no_peer_identity=%s, default_tenant_name=%s",
            allow_list,
            reject_no_peer_identity,
            default_tenant_name,
        )

    def validate_access(
        self,
        peer_identities: list[bytes] | None,
        auth_token: pydantic.SecretStr | None,
        method_name: str,
    ) -> AuthInfo:
        """
        Validates access to a tenant.

        The method checks if a peer identity is in the allow list for the method.

        Args:
            peer_identities: The peer identities of the caller. These are the commonName and dnsNames
                from the client MTLS certificate if MTLS is used. see deploy/common/cert-lib.jsonnet's createClientCert.
            auth_token: The auth token of the caller from the Bearer authorization header if set
            method_name: The name of the method to validate.

        Returns:
            True if the caller is allowed to call the method, False otherwise.
        """
        del auth_token
        if self.reject_no_peer_identity and not peer_identities:
            raise ServiceAuthException(
                grpc.StatusCode.UNAUTHENTICATED, "Unauthenticated"
            )
        if not peer_identities:
            return AuthInfo(
                tenant_id=None,
                tenant_name=self.default_tenant_name,
                user_id=None,
                shard_namespace="",
                cloud="",
            )
        for peer_identity in peer_identities:
            peer_dns_name = peer_identity.partition(b".")[0]
            for config in self.allow_list:
                if config.match(peer_dns_name.decode(), method_name):
                    return AuthInfo(
                        tenant_id=None,
                        tenant_name=self.default_tenant_name,
                        user_id=None,
                        shard_namespace="",
                        cloud="",
                    )

        raise ServiceAuthException(grpc.StatusCode.PERMISSION_DENIED, "Access denied")
