package auth

import (
	"context"
	"testing"

	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
)

func TestNewContext(t *testing.T) {
	ctx := context.Background()
	claims := &AugmentClaims{
		UserID:         "user-id",
		ServiceName:    "service-name",
		TenantID:       "tenant-id",
		TenantName:     "tenant-name",
		ShardNamespace: "shard-namespace",
		Scope:          []string{"CONTENT_RW"},
	}
	ctx = claims.NewContext(ctx)
	if ctx.Value(augmentClaimsKey) != claims {
		t.Error("Claims not set in context")
	}
}

func TestGetAugmentClaims(t *testing.T) {
	ctx := context.Background()
	if c, ok := GetAugmentClaims(ctx); ok || c != nil {
		t.Error("Claims returned from empty context")
	}

	claims := &AugmentClaims{
		UserID:         "user-id",
		ServiceName:    "service-name",
		TenantID:       "tenant-id",
		TenantName:     "tenant-name",
		ShardNamespace: "shard-namespace",
		Scope:          []string{"CONTENT_RW"},
	}
	ctx = claims.NewContext(ctx)
	if c, ok := GetAugmentClaims(ctx); !ok || c != claims {
		t.Error("Claims not set in context")
	}
}

func TestHasScope(t *testing.T) {
	claims := &AugmentClaims{
		UserID:         "user-id",
		ServiceName:    "service-name",
		TenantID:       "tenant-id",
		TenantName:     "tenant-name",
		ShardNamespace: "shard-namespace",
		Scope:          []string{"CONTENT_RW", "SETTINGS_R"},
	}
	if !claims.HasScope(tokenexchangeproto.Scope_CONTENT_RW) {
		t.Error("Should have CONTENT_RW")
	}
	if !claims.HasScope(tokenexchangeproto.Scope_CONTENT_R) {
		t.Error("Should have CONTENT_R")
	}
	if claims.HasScope(tokenexchangeproto.Scope_REQUEST_R) {
		t.Error("Should not have REQUEST_R")
	}
	if claims.HasScope(tokenexchangeproto.Scope_REQUEST_RW) {
		t.Error("Should not have REQUEST_RW")
	}
	if !claims.HasScope(tokenexchangeproto.Scope_SETTINGS_R) {
		t.Error("Should have SETTINGS_R")
	}
	if claims.HasScope(tokenexchangeproto.Scope_SETTINGS_RW) {
		t.Error("Should not have SETTINGS_RW")
	}
}

func TestScopeMatches(t *testing.T) {
	testCases := []struct {
		haveScope    tokenexchangeproto.Scope
		desiredScope tokenexchangeproto.Scope
		expectedBool bool
	}{
		{tokenexchangeproto.Scope_CONTENT_RW, tokenexchangeproto.Scope_CONTENT_RW, true},
		{tokenexchangeproto.Scope_CONTENT_RW, tokenexchangeproto.Scope_CONTENT_R, true},
		{tokenexchangeproto.Scope_CONTENT_RW, tokenexchangeproto.Scope_CONTENT_ADMIN, false},
		{tokenexchangeproto.Scope_CONTENT_R, tokenexchangeproto.Scope_CONTENT_RW, false},
		{tokenexchangeproto.Scope_CONTENT_R, tokenexchangeproto.Scope_CONTENT_R, true},
		{tokenexchangeproto.Scope_CONTENT_R, tokenexchangeproto.Scope_CONTENT_ADMIN, false},
		{tokenexchangeproto.Scope_CONTENT_ADMIN, tokenexchangeproto.Scope_CONTENT_RW, true},
		{tokenexchangeproto.Scope_CONTENT_ADMIN, tokenexchangeproto.Scope_CONTENT_R, true},
		{tokenexchangeproto.Scope_CONTENT_ADMIN, tokenexchangeproto.Scope_CONTENT_ADMIN, true},
		{tokenexchangeproto.Scope_SETTINGS_RW, tokenexchangeproto.Scope_SETTINGS_R, true},
		{tokenexchangeproto.Scope_SETTINGS_RW, tokenexchangeproto.Scope_SETTINGS_RW, true},
		{tokenexchangeproto.Scope_SETTINGS_R, tokenexchangeproto.Scope_SETTINGS_RW, false},
		{tokenexchangeproto.Scope_SETTINGS_R, tokenexchangeproto.Scope_SETTINGS_R, true},
		{tokenexchangeproto.Scope_SETTINGS_R, tokenexchangeproto.Scope_CONTENT_RW, false},
	}
	for _, tc := range testCases {
		if scopeMatches(tc.haveScope, tc.desiredScope) != tc.expectedBool {
			t.Errorf("scopeMatches(%v, %v) returned %v, expected %v", tc.haveScope, tc.desiredScope, !tc.expectedBool, tc.expectedBool)
		}
	}
}

func TestIapEmail(t *testing.T) {
	claims := &AugmentClaims{
		OpaqueUserID:     "iap:<EMAIL>",
		OpaqueUserIDType: authentitiesproto.UserId_INTERNAL_IAP.String(),
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"CONTENT_RW", "SETTINGS_R"},
	}
	if email, ok := claims.GetIapEmail(); !ok || email != "<EMAIL>" {
		t.Errorf("<NAME_EMAIL>, got %v", email)
	}

	claims.ServiceName = ""
	claims.OpaqueUserID = "1234"
	claims.OpaqueUserIDType = authentitiesproto.UserId_INTERNAL_IAP.String()
	if email, ok := claims.GetIapEmail(); ok || email != "" {
		t.Errorf("Expected email empty, got %v", email)
	}

	claims.OpaqueUserID = "iap:1"
	claims.OpaqueUserIDType = authentitiesproto.UserId_AUGMENT.String()
	if email, ok := claims.GetIapEmail(); ok || email != "" {
		t.Errorf("Expected email empty, got %v", email)
	}
}

func TestIsTenantAllowed(t *testing.T) {
	tc := []struct {
		name           string
		claimsTenantId string
		tenantID       string
		allowed        bool
	}{
		{"matching tenants allowed", "foo", "foo", true},
		{"no matching tenants not allowed", "foo", "bar", false},
		{"blank allows any tenant", "", "foo", true},
		{"wildcard allows any tenant", "*", "foo", true},
	}

	for _, tcase := range tc {
		t.Run(tcase.name, func(t *testing.T) {
			claims := &AugmentClaims{
				TenantID: tcase.claimsTenantId,
			}
			if claims.IsTenantAllowed(tcase.tenantID) != tcase.allowed {
				t.Errorf("Expected %v, got %v", tcase.allowed, !tcase.allowed)
			}
		})
	}
}
