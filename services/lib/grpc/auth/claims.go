package auth

import (
	"context"
	"regexp"
	"strings"

	jwt "github.com/golang-jwt/jwt/v5"
	"github.com/rs/zerolog/log"

	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
)

// Go recommends using a custom, unexported type for context keys to avoid collisions.
type augmentClaimsKeyType string

const augmentClaimsKey = augmentClaimsKeyType("augment-claims")

var selfServeNamespaceRegex, _ = regexp.Compile(`^[di][0-9]+$`)

// The claims that our services pass around in JSON web tokens.
type AugmentClaims struct {
	jwt.RegisteredClaims

	// These describe the original principal the token was created for - whether
	// a user or service.
	// We expect at least one of these to be set, but do not expect user and
	// service to be set. OpaqueUserID is an opaque ID of type as described by
	// OpaqueUserIDType, these two fields together make up auth_entities.UserId.
	// UserEmail is the user's email, and UserID is ambiguous but is left around
	// for backwards compatibility.
	// UserID and UserEmail are both PII and should not be logged.
	// OpaqueUserID is not PII as it is an opaque identifier.
	UserID           string `json:"user_id"`
	OpaqueUserID     string `json:"opaque_user_id"`
	OpaqueUserIDType string `json:"opaque_user_id_type"`
	UserEmail        string `json:"user_email"`
	ServiceName      string `json:"service_name"`

	// These describe what the token is for, which namespace and tenant it has access to. If empty,
	// the token is allowed to operate on all tenants in the namespace.
	TenantID string `json:"tenant_id"`

	// This is intended to be used for logging, metrics, etc, since it is more accessible than
	// TenantID.
	TenantName string `json:"tenant_name"`

	// The namespace the token is for, usually the namespace that is currently responsible for the
	// tenant ID above.
	ShardNamespace string `json:"shard_namespace"`

	// The cloud that this namespace/tenant is in
	Cloud string `json:"cloud"`

	// The permissions for the token. See token_exchange.proto for details.
	Scope []string `json:"scope"`

	// Any additional claims that were passed in.
	AdditionalClaims jwt.MapClaims `json:"additional_claims"`
}

const TenantIDWildcard = "*"

// Annotate the log context with the request id.
func (rc *AugmentClaims) AnnotateLogContext(ctx context.Context) context.Context {
	if rc == nil {
		return ctx
	}
	return log.Ctx(ctx).With().Str("tenant_name", rc.TenantName).
		Str("tenant_id", rc.TenantID).
		Str("opaque_user_id", rc.OpaqueUserID).Logger().WithContext(ctx)
}

func (c *AugmentClaims) AllowsAllTenants() bool {
	return c.TenantID == "*" || c.TenantID == ""
}

// returns true if the token is for the given tenant
func (c *AugmentClaims) IsTenantAllowed(tenantID string) bool {
	if c.AllowsAllTenants() {
		return true
	}
	return c.TenantID == tenantID
}

// Allow some scopes to be subsets of others, for example a token with
// CONTENT_RW should pass a CONTENT_R check
func scopeMatches(tokenScope tokenexchangeproto.Scope, desiredScope tokenexchangeproto.Scope) bool {
	if tokenScope == desiredScope {
		return true
	}

	// Some scopes are subsets of others
	switch desiredScope {
	case tokenexchangeproto.Scope_SETTINGS_R:
		return tokenScope == tokenexchangeproto.Scope_SETTINGS_RW
	case tokenexchangeproto.Scope_CONTENT_R:
		return tokenScope == tokenexchangeproto.Scope_CONTENT_RW || tokenScope == tokenexchangeproto.Scope_CONTENT_ADMIN
	case tokenexchangeproto.Scope_CONTENT_RW:
		return tokenScope == tokenexchangeproto.Scope_CONTENT_ADMIN
	case tokenexchangeproto.Scope_AUTH_R:
		return tokenScope == tokenexchangeproto.Scope_AUTH_RW
	case tokenexchangeproto.Scope_REQUEST_R:
		return tokenScope == tokenexchangeproto.Scope_REQUEST_RW
	case tokenexchangeproto.Scope_REQUEST_RESTRICTED_R:
		return tokenScope == tokenexchangeproto.Scope_REQUEST_RESTRICTED_RW
	}
	return false
}

// returns true if the token has the given scope
func (c *AugmentClaims) HasScope(scope tokenexchangeproto.Scope) bool {
	for _, s := range c.Scope {
		if scopeVal, ok := tokenexchangeproto.Scope_value[s]; ok {
			if scopeMatches(tokenexchangeproto.Scope(scopeVal), scope) {
				return true
			}
		} else {
			log.Warn().Msgf("Invalid scope %s", s)
		}
	}
	return false
}

// returns the email address of the user if the token is for an IAP suer
func (c *AugmentClaims) GetIapEmail() (string, bool) {
	opaqueUserID := c.GetOpaqueUserID()
	if opaqueUserID != nil && opaqueUserID.UserIdType == authentitiesproto.UserId_INTERNAL_IAP {
		if email, ok := strings.CutPrefix(opaqueUserID.UserId, "iap:"); ok {
			return email, true
		} else {
			log.Error().Msgf("Invalid format for internal IAP user ID %s", opaqueUserID.UserId)
			return "", false
		}
	}
	return "", false
}

func (c *AugmentClaims) GetOpaqueUserID() *authentitiesproto.UserId {
	if c.OpaqueUserID == "" || c.OpaqueUserIDType == "" {
		return nil
	}
	userIDType, ok := authentitiesproto.UserId_UserIdType_value[c.OpaqueUserIDType]
	if !ok {
		log.Warn().Msgf("Invalid opaque user id type %s", c.OpaqueUserIDType)
		return nil
	}
	return &authentitiesproto.UserId{
		UserId:     c.OpaqueUserID,
		UserIdType: authentitiesproto.UserId_UserIdType(userIDType),
	}
}

func (c *AugmentClaims) MetricsTenantName() string {
	if selfServeNamespaceRegex.MatchString(c.ShardNamespace) {
		return "[self-serve]"
	}
	return c.TenantName
}

// Get a context with claims attached.
func (c *AugmentClaims) NewContext(ctx context.Context) context.Context {
	return context.WithValue(ctx, augmentClaimsKey, c)
}

func WithAugmentClaims(ctx context.Context, claims *AugmentClaims) context.Context {
	return claims.NewContext(ctx)
}

// Get the claims attached to the context, if any.
func GetAugmentClaims(ctx context.Context) (claims *AugmentClaims, ok bool) {
	claims, ok = ctx.Value(augmentClaimsKey).(*AugmentClaims)
	return claims, ok
}
