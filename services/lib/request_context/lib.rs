use std::fmt::{Debug, Display};

use regex::Regex;
use secrecy::{ExposeSecret, SecretString};
use uuid::Uuid;

// reexport UserId from auth_entities_proto
pub use auth_entities_proto::auth_entities::user_id::UserIdType;
pub use auth_entities_proto::auth_entities::UserId;

/// tenant id
///
/// The value is not guaranteed to be unique and should not be used for
/// correctness purposes.
#[derive(PartialEq, Eq, Clone, Hash, Ord, PartialOrd)]
pub struct TenantId(Option<String>);

pub const EMPTY_TENANT_ID: TenantId = TenantId(None);

impl TenantId {
    pub fn new(s: impl Into<String>) -> Self {
        let s = s.into();
        if s.is_empty() {
            EMPTY_TENANT_ID
        } else {
            TenantId(Some(s))
        }
    }
}

impl Default for TenantId {
    fn default() -> Self {
        EMPTY_TENANT_ID
    }
}

impl From<String> for TenantId {
    fn from(s: String) -> Self {
        if s.is_empty() {
            EMPTY_TENANT_ID
        } else {
            TenantId(Some(s))
        }
    }
}

impl From<&str> for TenantId {
    fn from(s: &str) -> Self {
        if s.is_empty() {
            EMPTY_TENANT_ID
        } else {
            TenantId(Some(s.to_string()))
        }
    }
}

impl From<TenantId> for String {
    fn from(id: TenantId) -> Self {
        id.0.unwrap_or_default()
    }
}

impl<'a> From<&'a TenantId> for &'a str {
    fn from(id: &'a TenantId) -> Self {
        id.0.as_deref().unwrap_or("")
    }
}

impl Display for TenantId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0.as_deref().unwrap_or(""))
    }
}

impl Debug for TenantId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self.0 {
            None => write!(f, "TenantId(\"\")"),
            Some(ref s) => write!(f, "TenantId(\"{}\")", s),
        }
    }
}

// HTTP headers used by RequestContext and associated types:
pub const REQUEST_SOURCE_HEADER_NAME: &str = "x-request-source";
pub const AUTHORIZATION_HEADER_NAME: &str = "authorization";
pub const REQUEST_ID_HEADER_NAME: &str = "x-request-id";
pub const REQUEST_SESSION_ID_HEADER_NAME: &str = "x-request-session-id";

/// request id used for tracking.
///
/// The value is not guaranteed to be unique and should not be used for
/// correctness purposes.
#[derive(Debug, PartialEq, Eq, Clone, Copy, Hash)]
pub struct RequestId(Uuid);

impl RequestId {
    /// create a new random request id
    pub fn create_random() -> Self {
        let id = Uuid::new_v4();
        RequestId(id)
    }

    pub fn from_headers(
        headers: &actix_web::http::header::HeaderMap,
        header_name: &str,
    ) -> Option<Self> {
        match headers.get(header_name) {
            None => None,
            Some(v) => match v.to_str() {
                Ok(s) => Self::try_from(s).ok(),
                Err(_) => None,
            },
        }
    }

    pub fn uuid(&self) -> Uuid {
        self.0
    }
}

impl TryFrom<&'_ str> for RequestId {
    type Error = uuid::Error;

    fn try_from(s: &'_ str) -> Result<Self, Self::Error> {
        match Uuid::parse_str(s) {
            Ok(uuid) => Ok(RequestId(uuid)),
            Err(e) => Err(e),
        }
    }
}

pub static NIL_REQUEST_ID: RequestId = RequestId(Uuid::nil());

impl From<&RequestId> for tonic::metadata::AsciiMetadataValue {
    fn from(val: &RequestId) -> Self {
        tonic::metadata::AsciiMetadataValue::try_from(&val.0.to_string()).unwrap()
    }
}

impl Display for RequestId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// session id used for tracking.
///
/// The value is not guaranteed to be unique and should not be used for
/// correctness purposes.
#[derive(Debug, PartialEq, Eq, Clone, Copy, Hash)]
pub struct RequestSessionId(Option<Uuid>);

impl RequestSessionId {
    pub fn new(s: Uuid) -> Self {
        RequestSessionId(Some(s))
    }

    /// create a new random request session id
    pub fn create_random() -> Self {
        let id = Uuid::new_v4();
        RequestSessionId(Some(id))
    }

    pub fn from_headers(headers: &actix_web::http::header::HeaderMap) -> Self {
        match headers.get(REQUEST_SESSION_ID_HEADER_NAME) {
            None => RequestSessionId(None),
            Some(v) => match v.to_str() {
                Ok(s) => match Uuid::parse_str(s) {
                    Ok(id) => RequestSessionId(Some(id)),
                    Err(_e) => RequestSessionId(None),
                },
                Err(_) => RequestSessionId(None),
            },
        }
    }

    // Only for internal use for now
    pub fn empty() -> Self {
        RequestSessionId(None)
    }

    pub fn is_empty(&self) -> bool {
        self.0.is_none()
    }

    pub fn option_to_string(&self) -> Option<String> {
        self.0.map(|v| v.to_string())
    }
}

impl TryFrom<&String> for RequestSessionId {
    type Error = uuid::Error;

    fn try_from(s: &String) -> Result<Self, Self::Error> {
        match Uuid::parse_str(s) {
            Ok(uuid) => Ok(RequestSessionId(Some(uuid))),
            Err(e) => Err(e),
        }
    }
}

impl From<&RequestSessionId> for tonic::metadata::AsciiMetadataValue {
    fn from(val: &RequestSessionId) -> Self {
        match val.0 {
            None => tonic::metadata::AsciiMetadataValue::try_from("").unwrap(),
            Some(id) => tonic::metadata::AsciiMetadataValue::try_from(&id.to_string()).unwrap(),
        }
    }
}

impl Display for RequestSessionId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match &self.0 {
            Some(v) => write!(f, "{v}"),
            None => write!(f, ""),
        }
    }
}

#[derive(Debug, PartialEq, Eq, Clone, Copy, Hash)]
pub enum RequestSource {
    Background,
    Client,
    Evaluation,
    HealthCheck,
    Unknown,
}

impl Display for RequestSource {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "{}",
            match self {
                RequestSource::Background => "background",
                RequestSource::Client => "client",
                RequestSource::Evaluation => "evaluation",
                RequestSource::HealthCheck => "healthCheck",
                RequestSource::Unknown => "unknown",
            }
        )
    }
}

/// RequestContext is a generalization of RequestId to other kinds of metadata that might be passed
/// around the backend in request headers. For now that mostly means JWTs (for multi-tenant auth).
#[derive(Clone, Debug)]
pub struct RequestContext {
    request_id: RequestId, // TODO: retire RequestId and use Uuid directly
    request_session_id: RequestSessionId, // TODO: ditto
    request_source: RequestSource,
    // TODO: non-optional once all deployments use new shard architecture
    token: Option<SecretString>,
}

impl RequestContext {
    // Create a new request context.
    // NOTE: this is designed for new operations only, such as user-initiated requests and
    // background tasks started by internal services. Most paths should use try_from instead.
    pub fn new(
        request_id: RequestId,
        request_session_id: RequestSessionId,
        request_source: RequestSource,
        token: Option<SecretString>,
    ) -> Self {
        Self {
            request_id,
            request_session_id,
            request_source,
            token,
        }
    }

    pub fn new_for_test() -> Self {
        Self {
            request_id: RequestId::create_random(),
            request_session_id: RequestSessionId::create_random(),
            request_source: RequestSource::Unknown,
            token: None,
        }
    }

    // Create a new request context with a new request id, but the same session id.
    pub fn with_new_request_id(&self) -> Self {
        Self {
            request_id: RequestId::create_random(),
            ..self.clone()
        }
    }

    pub fn request_id(&self) -> RequestId {
        self.request_id
    }

    pub fn request_session_id(&self) -> RequestSessionId {
        self.request_session_id
    }

    pub fn request_source(&self) -> RequestSource {
        self.request_source
    }

    pub fn token(&self) -> SecretString {
        self.token
            .clone()
            .unwrap_or(SecretString::new("".to_string()))
    }

    pub fn with_token(mut self, token: SecretString) -> Self {
        self.token = Some(token);
        self
    }

    // Annotate a downstream request with the metadata needed to reconstruct the request context.
    pub fn annotate(&self, metadata: &mut tonic::metadata::MetadataMap) {
        metadata.insert(REQUEST_ID_HEADER_NAME, (&self.request_id).into());
        if !self.request_session_id.is_empty() {
            metadata.insert(
                REQUEST_SESSION_ID_HEADER_NAME,
                (&self.request_session_id).into(),
            );
        }
        metadata.insert(
            REQUEST_SOURCE_HEADER_NAME,
            tonic::metadata::AsciiMetadataValue::try_from(&self.request_source.to_string())
                .unwrap(),
        );
        if let Some(token) = &self.token {
            let token_value = tonic::metadata::AsciiMetadataValue::try_from(&format!(
                "Bearer {}",
                token.expose_secret()
            ))
            .unwrap();
            metadata.insert(AUTHORIZATION_HEADER_NAME, token_value);
        }
    }
}

impl TryFrom<&tonic::metadata::MetadataMap> for RequestContext {
    type Error = tonic::Status;

    fn try_from(metadata: &tonic::metadata::MetadataMap) -> Result<Self, Self::Error> {
        let request_id_value = metadata.get(REQUEST_ID_HEADER_NAME);
        let request_id = request_id_value
            .map(|e| -> Result<RequestId, Self::Error> {
                let request_id_str = e
                    .to_str()
                    .map_err(|_| tonic::Status::invalid_argument("request id is not a string"))?;
                let res = RequestId::try_from(request_id_str)
                    .map_err(|_| tonic::Status::invalid_argument("request id is not a uuid"))?;
                Ok(res)
            })
            .transpose()?
            .unwrap_or(NIL_REQUEST_ID);
        let request_session_id_value = metadata.get(REQUEST_SESSION_ID_HEADER_NAME);
        let request_session_id = request_session_id_value
            .map(|e| -> Result<RequestSessionId, Self::Error> {
                let request_session_id_str = e.to_str().map_err(|_| {
                    tonic::Status::invalid_argument("request session id is not a string")
                })?;
                let res = RequestSessionId::try_from(&request_session_id_str.to_string()).map_err(
                    |_| {
                        tracing::error!(
                            "request session id is not a uuid: '{}'",
                            request_session_id_str
                        );
                        tonic::Status::invalid_argument("request session id is not a uuid")
                    },
                )?;
                Ok(res)
            })
            .transpose()?
            .unwrap_or(RequestSessionId::empty());
        let request_source_value = metadata.get(REQUEST_SOURCE_HEADER_NAME);
        let request_source = request_source_value
            .map(|e| -> Result<RequestSource, Self::Error> {
                let request_source_str = e.to_str().map_err(|_| {
                    tonic::Status::invalid_argument("request source is not a string")
                })?;
                let res = match request_source_str {
                    "background" => RequestSource::Background,
                    "client" => RequestSource::Client,
                    "evaluation" => RequestSource::Evaluation,
                    "healthCheck" => RequestSource::HealthCheck,
                    _ => RequestSource::Unknown,
                };
                Ok(res)
            })
            .transpose()?
            .unwrap_or(RequestSource::Unknown);

        let token_value = metadata.get(AUTHORIZATION_HEADER_NAME);
        let token = token_value
            .map(|e| -> Result<SecretString, Self::Error> {
                let res = e
                    .to_str()
                    .map_err(|_| tonic::Status::invalid_argument("token is not a string"))?
                    .strip_prefix("Bearer ")
                    .ok_or_else(|| {
                        tonic::Status::invalid_argument("token does not use Bearer prefix")
                    })?
                    .to_string();
                Ok(SecretString::new(res))
            })
            .transpose()?;
        Ok(Self {
            request_id,
            request_session_id,
            request_source,
            token,
        })
    }
}

#[derive(PartialEq, Eq, Debug, Hash, Clone, Copy)]
pub enum TokenScope {
    // read access to the auth data
    AuthR,

    // read/write access to the user data
    AuthRw,

    // read access to the requests data
    RequestR,

    // read/write access to the requests data
    RequestRw,

    // read to the content data
    ContentR,

    // read/write access to the content data
    ContentRw,

    // full access to the content data including the user index
    ContentAdmin,

    // read access to the settings data
    SettingsR,

    // read/write access to the settings data
    SettingsRw,

    // read access to confidential request data
    RequestConfidentialR,

    // read/write access to confidential request data
    RequestConfidentialRw,

    // read access to restricted request data
    RequestRestrictedR,

    // read/write access to restricted request data
    RequestRestrictedRw,

    // delete access to bigtable rows
    BigtableDelete,
}

impl TokenScope {
    pub fn matches(&self, expected_scope: TokenScope) -> bool {
        matches!(
            (self, expected_scope),
            (TokenScope::AuthR, TokenScope::AuthR)
                | (TokenScope::AuthRw, TokenScope::AuthR)
                | (TokenScope::AuthRw, TokenScope::AuthRw)
                | (TokenScope::RequestR, TokenScope::RequestR)
                | (TokenScope::RequestRw, TokenScope::RequestR)
                | (TokenScope::RequestRw, TokenScope::RequestRw)
                | (TokenScope::ContentR, TokenScope::ContentR)
                | (TokenScope::ContentRw, TokenScope::ContentR)
                | (TokenScope::ContentRw, TokenScope::ContentRw)
                | (TokenScope::ContentAdmin, TokenScope::ContentR)
                | (TokenScope::ContentAdmin, TokenScope::ContentRw)
                | (TokenScope::ContentAdmin, TokenScope::ContentAdmin)
                | (TokenScope::SettingsR, TokenScope::SettingsR)
                | (TokenScope::SettingsRw, TokenScope::SettingsR)
                | (TokenScope::SettingsRw, TokenScope::SettingsRw)
                | (
                    TokenScope::RequestConfidentialR,
                    TokenScope::RequestConfidentialR
                )
                | (
                    TokenScope::RequestConfidentialRw,
                    TokenScope::RequestConfidentialR
                )
                | (
                    TokenScope::RequestConfidentialRw,
                    TokenScope::RequestConfidentialRw
                )
                | (
                    TokenScope::RequestRestrictedR,
                    TokenScope::RequestRestrictedR
                )
                | (
                    TokenScope::RequestRestrictedRw,
                    TokenScope::RequestRestrictedR
                )
        )
    }

    /// Converts a string to a TokenScope, ignoring invalid values
    ///
    /// This is used when we want to ignore invalid scopes instead of returning an error
    pub fn from_string_ignore_invalid(value: &str) -> Option<Self> {
        match value {
            "AUTH_R" => Some(TokenScope::AuthR),
            "AUTH_RW" => Some(TokenScope::AuthRw),
            "REQUEST_R" => Some(TokenScope::RequestR),
            "REQUEST_RW" => Some(TokenScope::RequestRw),
            "CONTENT_R" => Some(TokenScope::ContentR),
            "CONTENT_RW" => Some(TokenScope::ContentRw),
            "CONTENT_ADMIN" => Some(TokenScope::ContentAdmin),
            "SETTINGS_R" => Some(TokenScope::SettingsR),
            "SETTINGS_RW" => Some(TokenScope::SettingsRw),
            "REQUEST_CONFIDENTIAL_R" => Some(TokenScope::RequestConfidentialR),
            "REQUEST_CONFIDENTIAL_RW" => Some(TokenScope::RequestConfidentialRw),
            "REQUEST_RESTRICTED_R" => Some(TokenScope::RequestRestrictedR),
            "REQUEST_RESTRICTED_RW" => Some(TokenScope::RequestRestrictedRw),
            "BIGTABLE_DELETE" => Some(TokenScope::BigtableDelete),
            _ => None,
        }
    }
}

impl TryFrom<&str> for TokenScope {
    type Error = tonic::Status;

    fn try_from(value: &str) -> Result<Self, Self::Error> {
        match TokenScope::from_string_ignore_invalid(value) {
            Some(scope) => Ok(scope),
            None => Err(tonic::Status::unknown("invalid token scope")),
        }
    }
}

impl TryFrom<String> for TokenScope {
    type Error = tonic::Status;

    fn try_from(value: String) -> Result<Self, Self::Error> {
        TokenScope::try_from(value.as_str())
    }
}

// tenant information
//
// usually extracted from RequestContext's token
#[derive(Clone, Debug)]
pub struct TenantInfo {
    // tenant id
    // the tenant id is currently not set if token exchange is not enabled
    pub tenant_id: Option<TenantId>,

    // tenant name
    pub tenant_name: String,

    // shard namespace
    pub shard_namespace: String,

    pub cloud: String,

    // scopes
    pub scopes: Vec<TokenScope>,

    // The ID of the user the caller is allowed to access.
    //
    // The user id might not be set if the call is not on behalf of a user
    //
    // The user id is PII and should not be logged
    //
    // user_id is deprecated, opaque_user_id is an opaque user ID, and user_email is the user's email
    pub user_id: Option<SecretString>,
    pub opaque_user_id: Option<UserId>,
    pub user_email: Option<SecretString>,

    pub service_name: Option<String>,
}

// only in test
impl PartialEq for TenantInfo {
    fn eq(&self, other: &Self) -> bool {
        self.tenant_id == other.tenant_id
            && self.tenant_name == other.tenant_name
            && self.scopes == other.scopes
            && self.shard_namespace == other.shard_namespace
            && self.cloud == other.cloud
            && self.user_id.as_ref().map(|e| e.expose_secret().to_string())
                == other
                    .user_id
                    .as_ref()
                    .map(|e| e.expose_secret().to_string())
            && self.service_name == other.service_name
    }
}

impl Eq for TenantInfo {}

impl TenantInfo {
    pub fn new_for_test() -> Self {
        Self {
            tenant_id: Some("456".to_string().into()),
            tenant_name: "dev-augie".to_string(),
            shard_namespace: "dev-augie-shard".to_string(),
            cloud: "gcp".to_string(),
            scopes: vec![],
            user_id: None,
            opaque_user_id: None,
            user_email: None,
            service_name: None,
        }
    }

    pub fn new_for_test_with_scopes(scopes: Vec<TokenScope>) -> Self {
        Self {
            tenant_id: Some("456".to_string().into()),
            tenant_name: "dev-augie".to_string(),
            shard_namespace: "dev-augie-shard".to_string(),
            cloud: "gcp".to_string(),
            scopes,
            user_id: None,
            opaque_user_id: None,
            user_email: None,
            service_name: None,
        }
    }

    pub fn annotate(&self, extensions: &mut tonic::Extensions) {
        extensions.insert(self.clone());
    }

    pub fn validate_scope(&self, expected_scope: TokenScope) -> Result<(), tonic::Status> {
        if !self.scopes.iter().any(|s| s.matches(expected_scope)) {
            tracing::warn!(
                "Access denied for scope {:?}: token scopes {:?}",
                expected_scope,
                self.scopes
            );
            Err(tonic::Status::permission_denied("Access denied"))
        } else {
            Ok(())
        }
    }

    // returns the email address of the user if the token is for an IAP user
    pub fn iap_email(&self) -> Option<&str> {
        self.opaque_user_id.as_ref().and_then(|e| {
            if e.user_id_type != UserIdType::InternalIap as i32 {
                return None;
            }
            if !e.user_id.starts_with("iap:") {
                tracing::error!("Invalid format for internal IAP user ID {}", e.user_id);
                return None;
            }
            e.user_id.strip_prefix("iap:")
        })
    }

    pub fn metrics_tenant_name(&self) -> &str {
        lazy_static::lazy_static! {
            static ref SELF_SERVE_NAMESPACE_REGEX: Regex = Regex::new("^[di][0-9]+$").expect("Failed to compile self-serve namespace regex");
        }

        if SELF_SERVE_NAMESPACE_REGEX.is_match(&self.shard_namespace) {
            "[self-serve]"
        } else {
            &self.tenant_name
        }
    }
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use actix_web::http::header::{HeaderMap, HeaderName, HeaderValue};

    use super::*;

    #[test]
    fn test_request_id_new() {
        let r = RequestId::create_random();
        assert_eq!(r.to_string().len(), 36);
    }

    #[test]
    fn test_request_id_random() {
        let r = RequestId::create_random();
        assert_ne!(r, RequestId::create_random());
    }

    #[test]
    fn test_request_id_from_headers() {
        let r = RequestId::create_random();
        let mut headers = HeaderMap::new();
        headers.append(
            HeaderName::from_str("X-Request-ID").unwrap(),
            HeaderValue::from_str(&r.to_string()).unwrap(),
        );

        let r2 = RequestId::from_headers(&headers, REQUEST_ID_HEADER_NAME).unwrap();
        assert_eq!(r, r2)
    }

    #[test]
    fn test_request_id_from_headers_with_missing_header_value() {
        let headers = HeaderMap::new();
        let r = RequestId::from_headers(&headers, REQUEST_ID_HEADER_NAME);
        assert!(r.is_none());
    }

    #[test]
    fn test_request_id_from_headers_with_invalid_header_value() {
        let mut headers = HeaderMap::new();
        headers.append(
            HeaderName::from_str(REQUEST_ID_HEADER_NAME).unwrap(),
            HeaderValue::from_str("foo-bar").unwrap(),
        );
        let r = RequestId::from_headers(&headers, REQUEST_ID_HEADER_NAME);
        assert!(r.is_none());
    }

    #[test]
    fn test_request_context_from_tonic_with_token() {
        let mut metadata = tonic::metadata::MetadataMap::new();
        let request_id = RequestId::create_random();
        metadata.insert(
            REQUEST_ID_HEADER_NAME,
            tonic::metadata::AsciiMetadataValue::from(&request_id),
        );
        metadata.insert(
            AUTHORIZATION_HEADER_NAME,
            tonic::metadata::AsciiMetadataValue::try_from("Bearer baz").unwrap(),
        );
        let r = RequestContext::try_from(&metadata).unwrap();
        assert_eq!(r.request_id, request_id);
        assert_eq!(r.request_session_id, RequestSessionId::empty());
        assert_eq!(r.request_source, RequestSource::Unknown);
        assert_eq!(
            r.token.map(|s| s.expose_secret().clone()),
            Some("baz".to_string())
        );
    }

    #[test]
    fn test_request_context_from_tonic_no_token() {
        let mut metadata = tonic::metadata::MetadataMap::new();
        let request_id = RequestId::create_random();
        metadata.insert(
            REQUEST_ID_HEADER_NAME,
            tonic::metadata::AsciiMetadataValue::from(&request_id),
        );
        let r = RequestContext::try_from(&metadata).unwrap();
        assert_eq!(r.request_id, request_id);
        assert_eq!(r.request_session_id, RequestSessionId::empty());
        assert_eq!(r.token.map(|s| s.expose_secret().clone()), None);
    }

    #[test]
    fn test_request_context_to_tonic_no_token() {
        let mut metadata = tonic::metadata::MetadataMap::new();
        let request_context = RequestContext::new_for_test();
        request_context.annotate(&mut metadata);
        let r = RequestContext::try_from(&metadata).unwrap();
        assert_eq!(r.request_id, request_context.request_id());
        assert_eq!(r.request_session_id, request_context.request_session_id());
        assert_eq!(r.request_source, request_context.request_source());
        assert_eq!(r.token.map(|s| s.expose_secret().clone()), None);
    }

    #[test]
    fn test_request_context_to_tonic_with_token() {
        let mut metadata = tonic::metadata::MetadataMap::new();
        let request_context = RequestContext::new(
            RequestId::create_random(),
            RequestSessionId::create_random(),
            RequestSource::Unknown,
            Some(SecretString::new("baz".to_string())),
        );
        request_context.annotate(&mut metadata);
        let r = RequestContext::try_from(&metadata).unwrap();
        assert_eq!(r.request_id, request_context.request_id());
        assert_eq!(r.request_session_id, request_context.request_session_id());
        assert_eq!(r.request_source, request_context.request_source());
        assert_eq!(
            r.token.map(|s| s.expose_secret().clone()),
            Some("baz".to_string())
        );
    }

    #[test]
    fn test_request_context_request_source_from_tonic() {
        let mut metadata = tonic::metadata::MetadataMap::new();
        // Still need to set the request id since it's required for all requests
        let request_id = RequestId::create_random();
        metadata.insert(
            REQUEST_ID_HEADER_NAME,
            tonic::metadata::AsciiMetadataValue::from(&request_id),
        );
        metadata.insert(
            REQUEST_SOURCE_HEADER_NAME,
            tonic::metadata::AsciiMetadataValue::try_from("client").unwrap(),
        );
        let r = RequestContext::try_from(&metadata).unwrap();
        assert_eq!(r.request_id, request_id);
        assert_eq!(r.request_source, RequestSource::Client);
    }

    #[test]
    fn test_request_context_request_source_to_tonic() {
        let mut metadata = tonic::metadata::MetadataMap::new();
        let request_context = RequestContext::new(
            RequestId::create_random(),
            RequestSessionId::create_random(),
            RequestSource::Client,
            None,
        );
        request_context.annotate(&mut metadata);
        let r = RequestContext::try_from(&metadata).unwrap();
        assert_eq!(r.request_source, RequestSource::Client);
    }

    #[test]
    fn test_tenant_id() {
        let tenant_id = TenantId::new("12345");
        let tenant_id2 = TenantId::new("12345");
        assert_eq!(tenant_id, tenant_id2);

        let tenant_id3 = TenantId::new("");
        assert_eq!(tenant_id3, EMPTY_TENANT_ID);
    }

    #[test]
    fn test_tenant_id_display() {
        let tenant_id = TenantId::new("12345");
        let string = format!("{}", tenant_id);
        assert_eq!(string, "12345");
        let string = format!("{:?}", tenant_id);
        assert_eq!(string, "TenantId(\"12345\")");

        let string = format!("{}", EMPTY_TENANT_ID);
        assert_eq!(string, "");
        let string = format!("{:?}", EMPTY_TENANT_ID);
        assert_eq!(string, "TenantId(\"\")");
    }

    #[test]
    fn test_tenant_id_from_str() {
        let tenant_id = TenantId::new("12345");
        let tenant_id2 = "12345".to_string().into();
        assert_eq!(tenant_id, tenant_id2);

        let tenant_id3: TenantId = "".to_string().into();
        assert_eq!(tenant_id3, EMPTY_TENANT_ID);
    }

    #[test]
    fn test_tenant_id_to_string() {
        let tenant_id = TenantId::new("12345");
        let string = tenant_id.to_string();
        assert_eq!(string, "12345");

        let string = EMPTY_TENANT_ID.to_string();
        assert_eq!(string, "");
    }

    #[test]
    fn test_tenant_info_iap_email() {
        let mut tenant_info = TenantInfo::new_for_test();
        assert_eq!(tenant_info.iap_email(), None);

        tenant_info.service_name = Some("internal_service".to_string());
        assert_eq!(tenant_info.iap_email(), None);

        tenant_info.opaque_user_id = Some(UserId {
            user_id_type: UserIdType::Augment.into(),
            user_id: "iap:1".to_string(),
        });
        assert_eq!(tenant_info.iap_email(), None);

        tenant_info.opaque_user_id = Some(UserId {
            user_id_type: UserIdType::InternalIap.into(),
            user_id: "iap:<EMAIL>".to_string(),
        });
        assert_eq!(tenant_info.iap_email(), Some("<EMAIL>"));

        tenant_info.service_name = None;
        tenant_info.opaque_user_id = Some(UserId {
            user_id_type: UserIdType::InternalIap.into(),
            user_id: "1234".to_string(),
        });
        assert_eq!(tenant_info.iap_email(), None);
    }
}
