"""A retriever that constructs diff chunks from edit events."""

import concurrent.futures
from typing import Dict, Optional, Sequence

import structlog
from prometheus_client import Counter

from base.blob_names.python.blob_names import <PERSON><PERSON><PERSON><PERSON><PERSON>, FilePath
from base.datasets.recency_info import ReplacementText
from base.datasets.recency_info_conversion import from_replacement_text_proto
from base.diff_utils.apply_replacements_to_files import FileReplacementErrorType
from base.diff_utils.diff_formatter import format_file_changes_with_ranges
from base.diff_utils.diff_utils import DiffHunk, File
from base.diff_utils.retriever_util_completion import (
    EditEventConstructionInput,
    FileContentProvider,
    apply_replacements,
    filter_replacement_text,
    get_files_before_replacement,
    get_squashable_edits,
)
from base.logging.secret_logging import get_safe_logger
from base.prompt_format.chunk_origin import ChunkOrigin
from base.prompt_format_retrieve.prompt_formatter import CompletionRetrieverPromptInput
from services.lib.retrieval import (
    retriever_request_insight_builder,
)
from services.lib.file_retriever.file_retriever import FileRetriever
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval import dummy_executor
from services.lib.retrieval.retriever import (
    FindMissingResult,
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
    Retriever,
)

logger = structlog.get_logger()


_file_reconstruction_error_counter = Counter(
    "au_completion_host_file_reconstruction_error_counter",
    "Number of times file reconstruction has failed.",
    labelnames=["model", "tenant_name", "request_source", "error_type"],
)


def record_file_reconstruction_error(
    model_name: str,
    metrics_tenant_name: str,
    request_source: str,
    error_type: FileReplacementErrorType,
):
    _file_reconstruction_error_counter.labels(
        model_name, metrics_tenant_name, request_source, error_type.value
    ).inc()


def diff_hunk_to_retrieval_chunk(
    diff_hunk: DiffHunk,
    filter_duplicated_file_paths: bool = True,
) -> RetrievalChunk:
    """Converts a recent chunk to a retrieval chunk. Only text, path, and origin are really used."""
    return RetrievalChunk(
        text=diff_hunk.text,
        path=diff_hunk.path_header(filter_duplicated_file_paths),
        char_start=0,
        char_end=len(diff_hunk.text),
        blob_name=None,
        chunk_index=None,
        origin=ChunkOrigin.DIFF_RETRIEVER.value,
    )


class ServiceFileContentProvider(FileContentProvider):
    def __init__(
        self,
        file_retriever: FileRetriever,
        auth_info: AuthInfo,
        request_context: RequestContext,
    ):
        self.file_retriever = file_retriever
        self.auth_info = auth_info
        self.request_context = request_context
        self.missing_blobs: set[BlobName] = set()

    def retrieve_files(
        self,
        paths: Dict[BlobName, FilePath],
        expected: bool,
    ) -> Dict[BlobName, File | None]:
        """Retrieve files from the service. Edits self.missing_blobs if expected is True."""
        return self.file_retriever.retrieve_indexed_files(
            blob_name_to_file_path=paths,
            auth_info=self.auth_info,
            missing_blobs=self.missing_blobs,
            request_context=self.request_context,
            expected=expected,
        )


class DiffRetriever(Retriever):
    """A retriever that processes edit events to construct retrieved chunks.

    The expected output is a retrieval result, which consists of the following fields:
     - retrieved_chunks: A list of retrieved chunks (diff chunks) that are constructed from edit events and ordered from most recent to least recent.
     - missing_blob_names: A list of blob names that were not found in the service that we expected to find.
     - checkpoint_not_found: A boolean indicating whether the checkpoint was not found. This is always False for this retriever.
    """

    def __init__(
        self,
        num_results: int,
        max_total_changed_chars: int,
        big_event_lines: int,
        diff_context_lines: int,
        use_smart_header: bool,
        filter_duplicated_file_paths: bool,
        internal_retriever: Retriever,
        file_retriever: FileRetriever,
        ri_builder: Optional[
            retriever_request_insight_builder.RetrieverRequestInsightBuilder
        ] = None,
    ):
        """Construct a new retriever."""
        self._num_results = num_results
        self._max_total_changed_chars = max_total_changed_chars
        self._big_event_lines = big_event_lines
        self._diff_context_lines = diff_context_lines
        self._use_smart_header = use_smart_header
        self._filter_duplicated_file_paths = filter_duplicated_file_paths
        self._internal_retriever = internal_retriever
        self._file_retriever = file_retriever
        self._request_insight_builder = ri_builder
        self._safe_logger = get_safe_logger(logger=logger, secret_logs_enabled=False)

    def _get_current_version_of_outdated_files(
        self,
        replacements: Sequence[ReplacementText],
        file_content_provider: FileContentProvider,
        auth_info: AuthInfo,
        request_context: RequestContext,
        model_name: str,
    ) -> list[File]:
        """
        Use replacement text to reconstruct the current version of all files in replacement text.

        file_content_provider.missing_blobs is updated in this function.

        Returns:
            A list of files with the current version of the files in replacement text.
        """
        replacements = filter_replacement_text(replacements)
        files_before_replacements = get_files_before_replacement(
            replacements, file_content_provider, expected=True
        )

        self._safe_logger.info(
            f"{len(replacements)} recent changes to apply to {len(files_before_replacements)} files"
        )

        reconstructed_files, replacement_error_files = apply_replacements(
            replacements, files_before_replacements, self._safe_logger
        )

        for error in replacement_error_files:
            record_file_reconstruction_error(
                model_name=model_name,
                metrics_tenant_name=auth_info.metrics_tenant_name,
                request_source=request_context.request_source,
                error_type=error.error_type,
            )
            self._safe_logger.warn(f"Ignoring error {error.error_type} for file: ")
            self._safe_logger.secret_warn(
                f"{error.file_path=}, {error.file_blob_name=}"
            )

        return reconstructed_files

    def retrieve(
        self,
        input_: RetrievalInput[CompletionRetrieverPromptInput],
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: Optional[concurrent.futures.Executor] = None,
    ) -> RetrievalResult:
        """Returns the list retrieved chunks."""

        if input_.recency_info is None or input_.edit_events is None:
            return RetrievalResult(
                retrieved_chunks=[], missing_blob_names=[], checkpoint_not_found=False
            )

        if executor is None:
            executor = dummy_executor.DummyExecutor()

        edit_event_construction_input = EditEventConstructionInput(
            active_file_path=input_.prompt_input.path,
            active_file_prefix=input_.prompt_input.prefix,
            active_file_suffix=input_.prompt_input.suffix,
            recent_changes=[
                from_replacement_text_proto(change)
                for change in input_.recency_info.recent_changes
            ],
            edit_events=input_.edit_events,
        )

        service_file_content_provider = ServiceFileContentProvider(
            file_retriever=self._file_retriever,
            auth_info=auth_info,
            request_context=request_context,
        )

        # Step 1. Apply recency chunks on indexed files to get the current version of all the files in recency chunks
        reconstructed_files: list[File] = self._get_current_version_of_outdated_files(
            edit_event_construction_input.recent_changes,
            service_file_content_provider,
            auth_info,
            request_context,
            input_.model_name or "",
        )

        # This active file may not be correct, but we add it to the reconstructed files in case it is and edit events rely on it.
        # If it is incorrect, the blob name will not match and it will be ignored.
        reconstructed_files.append(edit_event_construction_input.active_file)

        # Deduplicate reconstructed files by blob name
        reconstructed_blob_names_to_files = {
            file.blob_name: file for file in reconstructed_files
        }

        # Step 2. Get the current content of the files touched by edit events
        squashable_edits = get_squashable_edits(
            edit_events=edit_event_construction_input.edit_events,
            reconstructed_files=reconstructed_blob_names_to_files,
            file_content_provider=service_file_content_provider,
            safe_logger=self._safe_logger,
        )

        # Step 3. Convert edit events to file changes
        recency_changes = squashable_edits.convert_edit_events_to_modified_files(
            safe_logger=self._safe_logger,
            is_source_file=lambda _: True,
            max_total_changed_chars=self._max_total_changed_chars,
            big_event_lines=self._big_event_lines,
        )

        all_diff_hunks = format_file_changes_with_ranges(
            recency_changes,
            diff_context_lines=self._diff_context_lines,
            diff_filter=lambda path: path.suffix != ".ipynb",
            use_smart_header=self._use_smart_header,
        )

        all_retrieval_chunks = [
            diff_hunk_to_retrieval_chunk(diff_hunk, self._filter_duplicated_file_paths)
            for diff_hunk in all_diff_hunks
        ]

        # Edit events are ordered from oldest to newest, so let's reverse the order to get the most recent changes first
        all_retrieval_chunks.reverse()
        retrieval_chunks = all_retrieval_chunks[: self._num_results]

        return RetrievalResult(
            retrieved_chunks=retrieval_chunks,
            missing_blob_names=service_file_content_provider.missing_blobs,
            checkpoint_not_found=False,
        )

    def find_missing(
        self,
        blob_names: Sequence[str],
        request_context: RequestContext,
        executor: Optional[concurrent.futures.Executor] = None,
    ) -> FindMissingResult:
        # TODO(pranay): can be refactored to calling file_content_provider instead to warm up the cache (need to change FileRetriever to take in BlobNames instead)
        return self._internal_retriever.find_missing(
            blob_names, request_context, executor
        )
