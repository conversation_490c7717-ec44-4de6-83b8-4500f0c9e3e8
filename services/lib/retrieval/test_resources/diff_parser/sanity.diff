diff --git a/.gitignore b/.gitignore
new file mode 100644
index 0000000..017f622
--- /dev/null
+++ b/.gitignore
@@ -0,0 +1,6 @@
+# Python
+*.py[cod]
+__pycache__
+
+# Vim
+*.swp
diff --git a/unidiff/patch.py b/unidiff/patch.py
index a0e8c24..9c94493 100644
--- a/unidiff/patch.py
+++ b/unidiff/patch.py
@@ -30,6 +30,7 @@
 LINE_TYPE_DELETE = '-'
 LINE_TYPE_CONTEXT = ' '

+
 class Hunk(object):
     """Each of the modified blocks of a file."""

@@ -98,7 +99,7 @@ def add_to_modified_counter(self, mods):


 class PatchedFile(list):
-    """Data from a patched file."""
+    """Data of a patched file, each element is a Hunk."""

     def __init__(self, source='', target=''):
         super(PatchedFile, self).__init__()
@@ -109,14 +110,21 @@ def __repr__(self):
         return "%s: %s" % (self.target_file,
                            super(PatchedFile, self).__repr__())

+    def __str__(self):
+        s = self.path + "\n"
+        for e in enumerate([repr(e) for e in self]):
+            s += "Hunk #%s: %s\n" % e
+        s += "\n"
+        return s
+
     def as_unified_diff(self):
         """Output file changes in unified diff format."""
         source = "--- %s\n" % self.source_file
         yield source
-
+
         target = "+++ %s\n" % self.target_file
         yield target
-
+
         for hunk in self:
             hunk_data = hunk.as_unified_diff()
             for line in hunk_data:
@@ -137,7 +145,7 @@ def path(self):
             filepath = self.target_file[2:]
         else:
             filepath = self.source_file
-        return filepath
+        return filepath

     @property
     def added(self):
@@ -156,27 +164,27 @@ def modified(self):

     @property
     def is_added_file(self):
-        """Return True if this a file added by the patch."""
+        """Return True if this patch adds a file."""
         return (len(self) == 1 and self[0].source_start == 0 and
                 self[0].source_length == 0)

     @property
     def is_deleted_file(self):
-        """Return True if this a file deleted by the patch."""
-        return (len(self) == 1 and self[0].target_start == 0 and
+        """Return True if this patch deletes a file."""
+        return (len(self) == 1 and self[0].target_start == 0 and
                 self[0].target_length == 0)

     def is_modified_file(self):
-        """Return True if this a file modified by the patch."""
+        """Return True if this patch modifies a file."""
         return not (self.is_added_file or self.is_deleted_file)


 class PatchSet(list):
-    """Full patch data."""
+    """A list of PatchedFiles."""

     def as_unified_diff(self):
         """Output patch data in unified diff format.
-
+
         It won't necessarily match the original unified diff,
         but it should be equivalent.
         """
@@ -185,4 +193,5 @@ def as_unified_diff(self):
             for line in data:
                 yield line

-
+    def __str__(self):
+        return ''.join([str(e) for e in self])
diff --git a/unidiff/utils.py b/unidiff/utils.py
index eae63e6..29c896a 100644
--- a/unidiff/utils.py
+++ b/unidiff/utils.py
@@ -37,4 +37,3 @@
 # - deleted line
 # \ No newline case (ignore)
 RE_HUNK_BODY_LINE = re.compile(r'^([- \+\\])')
-
