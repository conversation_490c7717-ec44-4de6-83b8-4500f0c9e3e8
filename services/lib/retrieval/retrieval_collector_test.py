"""Test for the multi retriever module."""

import concurrent.futures
from typing import Optional
from unittest import mock

import pytest

from base.blob_names.python.blob_names import Blobs
from base.prompt_format.chunk_origin import ChunkOrigin
from base.prompt_format_retrieve.prompt_formatter import CompletionRetrieverPromptInput
from services.completion_host import completion_pb2
from services.lib.retrieval.retrieval_collector import (
    RetrievalCollector,
    RetrievalCollectorConfig,
)
from services.lib.retrieval.null_retriever import NullRetriever
from services.lib.retrieval.retriever import (
    FindMissingResult,
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext


def gen_blob_name(s: str) -> str:
    """Convert a string to a hex-string blob name."""
    return s.encode("utf-8").hex()


def make_chunk(
    i: int, origin: str = ChunkOrigin.DENSE_RETRIEVER.value
) -> RetrievalChunk:
    return RetrievalChunk(
        text=f"chunk-{i:04d}",
        path="foo.py",
        char_start=i * 10,
        char_end=(i + 1) * 10,
        blob_name=gen_blob_name("blob1"),
        chunk_index=None,
        origin=origin,
    )


@pytest.fixture
def all_chunks():
    return [
        make_chunk(0, origin=ChunkOrigin.DENSE_RETRIEVER.value),
        make_chunk(1, origin=ChunkOrigin.DENSE_RETRIEVER.value),
        make_chunk(2, origin=ChunkOrigin.DENSE_RETRIEVER.value),
        make_chunk(1, origin=ChunkOrigin.RECENCY_RETRIEVER.value),
    ]


@pytest.fixture
def chunks(request, all_chunks):
    indices = request.param
    return [all_chunks[i] for i in indices]


@pytest.fixture
def retriever(all_chunks):
    def retrieve(
        input_: RetrievalInput,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: Optional[concurrent.futures.Executor] = None,
    ):
        return RetrievalResult(
            retrieved_chunks=all_chunks,
            missing_blob_names=["missing"],
            checkpoint_not_found=False,
        )

    retriever = mock.create_autospec(NullRetriever)
    retriever.retrieve.side_effect = retrieve
    retriever.find_missing.return_value = FindMissingResult(
        missing_blob_names=["missing"],
    )
    return retriever


@pytest.fixture
def auth_info():
    return AuthInfo(
        tenant_id="test_tenant_id",
        tenant_name="test_tenant",
        shard_namespace="test_shard_namespace",
        cloud="test_cloud",
    )


@pytest.fixture
def request_context():
    return RequestContext(
        request_id="test_request_id",
        request_session_id="test_request_session_id",
        request_source="unknown",
    )


@pytest.fixture
def executor():
    return concurrent.futures.ThreadPoolExecutor(
        max_workers=2, thread_name_prefix="retrieval_collector_test"
    )


@pytest.mark.parametrize(
    "chunks,modified_chunks_filter_enabled",
    [([0, 1, 2, 3], False), ([0, 2, 3], True)],
    indirect=["chunks"],
)
def test_retrieval_collector(
    chunks,
    modified_chunks_filter_enabled,
    retriever,
    auth_info,
    request_context,
    executor,
):
    """Default test for the signature retrieval system."""
    retrieval_collector = RetrievalCollector(
        retriever=retriever,
        config=RetrievalCollectorConfig(
            enabled=True, modified_chunks_filter_enabled=modified_chunks_filter_enabled
        ),
    )

    test_blob_names = ["blob1", "missing"]
    test_blob_names_hex = [gen_blob_name(x) for x in test_blob_names]

    input_ = RetrievalInput(
        CompletionRetrieverPromptInput(
            path="foo/foo.py",
            prefix="Hi AI!\n",
            suffix="Bye AI!",
        ),
        blobs=[Blobs.from_blob_names(test_blob_names_hex)],
        recency_info=completion_pb2.RecencyInfo(
            tab_switch_events=[],
            git_diff_file_info=[],
            recent_changes=[
                completion_pb2.ReplacementText(
                    blob_name=test_blob_names_hex[0],
                    path="foo.py",
                    char_start=10,
                    char_end=20,
                    replacement_text="new text",
                    present_in_blob=False,
                )
            ],
        ),
    )
    result = retrieval_collector.retrieve(
        input_=input_,
        request_context=request_context,
        auth_info=auth_info,
        executor=executor,
    )

    assert list(result.get_retrieved_chunks()) == chunks
    assert set(result.get_missing_blob_names()) == {"missing"}
    retriever.retrieve.assert_called_once_with(
        input_=input_,
        request_context=request_context,
        auth_info=auth_info,
        executor=executor,
    )


def test_find_missing(retriever, request_context, executor):
    """Test find_missing behavior with multiple retrievers."""
    retrieval_collector = RetrievalCollector(
        retriever=retriever,
        config=RetrievalCollectorConfig(
            enabled=True, modified_chunks_filter_enabled=False
        ),
    )

    blob_names = ["blob1", "missing"]

    result = retrieval_collector.find_missing(
        blob_names=blob_names, request_context=request_context, executor=executor
    )

    assert set(result.missing_blob_names) == {"missing"}
    retriever.find_missing.assert_called_once_with(
        blob_names=blob_names, request_context=request_context, executor=executor
    )
