#%%
# GCE Performance Results
n2_core_results = [
    {
        "count": 16 * 1024,
        "threads": 1,
        "par_simd_dot_call_bench": 4_928_960,
    },
    {
        "count": 32 * 1024,
        "threads": 1,
        "par_simd_dot_call_bench": 9_767_978,
    },
    {
        "count": 64 * 1024,
        "threads": 1,
        "par_simd_dot_call_bench": 19_386_271,
    },
    {
        "count": 128 * 1024,
        "threads": 1,
        "par_simd_dot_call_bench": 38_639_724,
    },
    {
        "count": 256 * 1024,
        "threads": 1,
        "par_simd_dot_call_bench": 76_973_954,
    },
    {
        "count": 512 * 1024,
        "threads": 1,
        "par_simd_dot_call_bench": 153_690_829,
    },
    {
        "count": 16 * 1024,
        "threads": 2,
        "par_simd_dot_call_bench": 2_524_561,
    },
    {
        "count": 32 * 1024,
        "threads": 2,
        "par_simd_dot_call_bench": 4_967_961,
    },
    {
        "count": 64 * 1024,
        "threads": 2,
        "par_simd_dot_call_bench": 9_844_358,
    },
    {
        "count": 128 * 1024,
        "threads": 2,
        "par_simd_dot_call_bench": 19_571_774,
    },
    {
        "count": 256 * 1024,
        "threads": 2,
        "par_simd_dot_call_bench": 38_785_465,
    },
    {
        "count": 512 * 1024,
        "threads": 2,
        "par_simd_dot_call_bench": 77_331_092,
    },
    {
        "count": 16 * 1024,
        "threads": 4,
        "par_simd_dot_call_bench": 630_893,
    },
    {
        "count": 32 * 1024,
        "threads": 4,
        "par_simd_dot_call_bench": 1_356_674,
    },
    {
        "count": 64 * 1024,
        "threads": 4,
        "par_simd_dot_call_bench": 2_859_204,
    },
    {
        "count": 128 * 1024,
        "threads": 4,
        "par_simd_dot_call_bench": 5_702_804,
    },
    {
        "count": 256 * 1024,
        "threads": 4,
        "par_simd_dot_call_bench": 11_246_618,
    },
    {
        "count": 512 * 1024,
        "threads": 4,
        "par_simd_dot_call_bench": 22_276_267,
    },
    {
        "count": 16 * 1024,
        "threads": 8,
        "par_simd_dot_call_bench": 758_984,
    },
    {
        "count": 32 * 1024,
        "threads": 8,
        "par_simd_dot_call_bench": 1_417_167,
    },
    {
        "count": 64 * 1024,
        "threads": 8,
        "par_simd_dot_call_bench": 2_696_369,
    },
    {
        "count": 128 * 1024,
        "threads": 8,
        "par_simd_dot_call_bench": 5_194_109,
    },
    {
        "count": 256 * 1024,
        "threads": 8,
        "par_simd_dot_call_bench": 10_169_853,
    },
    {
        "count": 512 * 1024,
        "threads": 8,
        "par_simd_dot_call_bench": 20_139_271,
    },
    {
        "count": 16 * 1024,
        "threads": 16,
        "par_simd_dot_call_bench": 548_277,
    },
    {
        "count": 32 * 1024,
        "threads": 16,
        "par_simd_dot_call_bench": 1_016_172,
    },
    {
        "count": 64 * 1024,
        "threads": 16,
        "par_simd_dot_call_bench": 2_012_293,
    },
    {
        "count": 128 * 1024,
        "threads": 16,
        "par_simd_dot_call_bench": 3_937_284,
    },
    {
        "count": 256 * 1024,
        "threads": 16,
        "par_simd_dot_call_bench": 7_832_574,
    },
    {
        "count": 512 * 1024,
        "threads": 16,
        "par_simd_dot_call_bench": 15_592_050,
    },
]

# GCE Performance Results
n2_vcpu_results = [
    {
        "count": 16 * 1024,
        "threads": 1,
        "par_simd_dot_call_bench": 3_101_725,
    },
    {
        "count": 32 * 1024,
        "threads": 1,
        "par_simd_dot_call_bench": 6_698_296,
    },
    {
        "count": 64 * 1024,
        "threads": 1,
        "par_simd_dot_call_bench": 11_393_004,
    },
    {
        "count": 128 * 1024,
        "threads": 1,
        "par_simd_dot_call_bench": 22_574_401,
    },
    {
        "count": 256 * 1024,
        "threads": 1,
        "par_simd_dot_call_bench": 44_967_979,
    },
    {
        "count": 512 * 1024,
        "threads": 1,
        "par_simd_dot_call_bench": 89_843_088,
    },
    {
        "count": 16 * 1024,
        "threads": 2,
        "par_simd_dot_call_bench": 862_409,
    },
    {
        "count": 32 * 1024,
        "threads": 2,
        "par_simd_dot_call_bench": 2_239_668,
    },
    {
        "count": 64 * 1024,
        "threads": 2,
        "par_simd_dot_call_bench": 5_324_708,
    },
    {
        "count": 128 * 1024,
        "threads": 2,
        "par_simd_dot_call_bench": 9_704_702,
    },
    {
        "count": 256 * 1024,
        "threads": 2,
        "par_simd_dot_call_bench": 19_821_967,
    },
    {
        "count": 512 * 1024,
        "threads": 2,
        "par_simd_dot_call_bench": 41_794_937,
    },
    {
        "count": 16 * 1024,
        "threads": 4,
        "par_simd_dot_call_bench": 890_350,
    },
    {
        "count": 32 * 1024,
        "threads": 4,
        "par_simd_dot_call_bench": 1_722_823,
    },
    {
        "count": 64 * 1024,
        "threads": 4,
        "par_simd_dot_call_bench": 3_239_262,
    },
    {
        "count": 128 * 1024,
        "threads": 4,
        "par_simd_dot_call_bench": 6_228_479,
    },
    {
        "count": 256 * 1024,
        "threads": 4,
        "par_simd_dot_call_bench": 11_898_815,
    },
    {
        "count": 512 * 1024,
        "threads": 4,
        "par_simd_dot_call_bench": 23_536_262,
    },
    {
        "count": 16 * 1024,
        "threads": 8,
        "par_simd_dot_call_bench": 416_062,
    },
    {
        "count": 32 * 1024,
        "threads": 8,
        "par_simd_dot_call_bench": 853_593,
    },
    {
        "count": 64 * 1024,
        "threads": 8,
        "par_simd_dot_call_bench": 1_682_205,
    },
    {
        "count": 128 * 1024,
        "threads": 8,
        "par_simd_dot_call_bench": 3_317_843,
    },
    {
        "count": 256 * 1024,
        "threads": 8,
        "par_simd_dot_call_bench": 6_549_601,
    },
    {
        "count": 512 * 1024,
        "threads": 8,
        "par_simd_dot_call_bench": 13_015_141,
    },
     {
        "count": 16 * 1024,
        "threads": 16,
        "par_simd_dot_call_bench": 492_064,
    },
    {
        "count": 32 * 1024,
        "threads": 16,
        "par_simd_dot_call_bench": 1_108_742,
    },
    {
        "count": 64 * 1024,
        "threads": 16,
        "par_simd_dot_call_bench": 2_196_634,
    },
    {
        "count": 128 * 1024,
        "threads": 16,
        "par_simd_dot_call_bench": 4_223_865,
    },
    {
        "count": 256 * 1024,
        "threads": 16,
        "par_simd_dot_call_bench": 8_310_321,
    },
    {
        "count": 512 * 1024,
        "threads": 16,
        "par_simd_dot_call_bench": 16_240_155,
    },
]

#%%
#!pip install matplotlib
import matplotlib.pyplot as plt
import numpy as np

for thread_count in [1, 2, 4, 8, 16]:
    x = [r["count"] for r in n2_vcpu_results if r["threads"] == thread_count]
    y = [r["par_simd_dot_call_bench"] for r in n2_vcpu_results if r["threads"] == thread_count]
    plt.scatter(x, y)
    plt.plot(np.unique(x), np.poly1d(np.polyfit(x, y, 1))(np.unique(x)), label="_nolegend_")

plt.ylabel("Latency (ns)")
plt.xlabel("Embeddings count")
plt.title("512D embedding dot products, n2 + f32")
plt.legend(["n2-standard-2", "n2-standard-4", "n2-standard-8", "n2-standard-16", "n2-standard-32"])
plt.xscale("log", base=2)
plt.yscale("log", base=2)
plt.show()
#%%
c3_core_results = [
    {
        "count": 16 * 1024,
        "threads": 1,
        "par_asm_simd_dot_call_bench": 1_374_404,
        "par_simd_dot_call_bench": 1_198_724,
    },
    {
        "count": 32 * 1024,
        "threads": 1,
        "par_asm_simd_dot_call_bench": 2_845_263,
        "par_simd_dot_call_bench": 3_775_043,
    },
    {
        "count": 64 * 1024,
        "threads": 1,
        "par_asm_simd_dot_call_bench": 5_621_831,
        "par_simd_dot_call_bench": 9_448_322,
    },
    {
        "count": 128 * 1024,
        "threads": 1,
        "par_asm_simd_dot_call_bench": 10_674_275,
        "par_simd_dot_call_bench": 19_571_586,
    },
    {
        "count": 256 * 1024,
        "threads": 1,
        "par_asm_simd_dot_call_bench": 22_544_325,
        "par_simd_dot_call_bench": 40_161_505,
    },
    {
        "count": 512 * 1024,
        "threads": 1,
        "par_asm_simd_dot_call_bench": 44_206_697,
        "par_simd_dot_call_bench": 81_178_249,
    },
    {
        "count": 16 * 1024,
        "threads": 2,
        "par_asm_simd_dot_call_bench": 728_212,
        "par_simd_dot_call_bench": 668_515, 
    },
    {
        "count": 32 * 1024,
        "threads": 2,
        "par_asm_simd_dot_call_bench": 1_438_457,
        "par_simd_dot_call_bench": 2_030_911,
    },
    {
        "count": 64 * 1024,
        "threads": 2,
        "par_asm_simd_dot_call_bench": 2_950_380,
        "par_simd_dot_call_bench": 4_640_047,
    },
    {
        "count": 128 * 1024,
        "threads": 2,
        "par_asm_simd_dot_call_bench": 5_948_825,
        "par_simd_dot_call_bench": 10_307_454,
    },
    {
        "count": 256 * 1024,
        "threads": 2,
        "par_asm_simd_dot_call_bench": 12_022_953,
        "par_simd_dot_call_bench": 20_923_634,
    },
    {
        "count": 512 * 1024,
        "threads": 2,
        "par_asm_simd_dot_call_bench": 24_112_095,
        "par_simd_dot_call_bench": 41_900_245,
    },
    {
        "count": 16 * 1024,
        "threads": 4,
        "par_asm_simd_dot_call_bench": 366_327,
        "par_simd_dot_call_bench": 342_840,
    },
    {
        "count": 32 * 1024,
        "threads": 4,
        "par_asm_simd_dot_call_bench": 716_326,
        "par_simd_dot_call_bench": 936_709,
    },
    {
        "count": 64 * 1024,
        "threads": 4,
        "par_asm_simd_dot_call_bench": 1_395_442,
        "par_simd_dot_call_bench": 2_391_897,
    },
    {
        "count": 128 * 1024,
        "threads": 4,
        "par_asm_simd_dot_call_bench": 2_800_908,
        "par_simd_dot_call_bench": 5_068_661,
    },
    {
        "count": 256 * 1024,
        "threads": 4,
        "par_asm_simd_dot_call_bench": 5_670_035,
        "par_simd_dot_call_bench": 10_331_940,
    },
    {
        "count": 512 * 1024,
        "threads": 4,
        "par_asm_simd_dot_call_bench": 11_350_370,
        "par_simd_dot_call_bench": 20_535_909,
    },
    {
        "count": 16 * 1024,
        "threads": 11,
        "par_asm_simd_dot_call_bench": 223_730,
        "par_simd_dot_call_bench": 228_635,
    },
    {
        "count": 32 * 1024,
        "threads": 11,
        "par_asm_simd_dot_call_bench": 364_613,
        "par_simd_dot_call_bench": 450_960,
    },
    {
        "count": 64 * 1024,
        "threads": 11,
        "par_asm_simd_dot_call_bench": 659_756,
        "par_simd_dot_call_bench": 1_249_154,
    },
    {
        "count": 128 * 1024,
        "threads": 11,
        "par_asm_simd_dot_call_bench": 1_408_310,
        "par_simd_dot_call_bench": 2_660_884,
    },
    {
        "count": 256 * 1024,
        "threads": 11,
        "par_asm_simd_dot_call_bench": 2_906_983,
        "par_simd_dot_call_bench": 5_378_766,
    },
    {
        "count": 512 * 1024,
        "threads": 11,
        "par_asm_simd_dot_call_bench": 5_788_517,
        "par_simd_dot_call_bench": 10_677_111,
    },
]

c3_vcpu_results = [
    {
        "count": 16 * 1024,
        "threads": 2,
        "par_asm_simd_dot_call_bench": 597_431,
        "par_simd_dot_call_bench": 612_486, 
    },
    {
        "count": 32 * 1024,
        "threads": 2,
        "par_asm_simd_dot_call_bench": 1_170_014,
        "par_simd_dot_call_bench": 1_614_223,
    },
    {
        "count": 64 * 1024,
        "threads": 2,
        "par_asm_simd_dot_call_bench": 2_241_465,
        "par_simd_dot_call_bench": 4_195_723,
    },
    {
        "count": 128 * 1024,
        "threads": 2,
        "par_asm_simd_dot_call_bench": 4_541_444,
        "par_simd_dot_call_bench": 8_625_531,
    },
    {
        "count": 256 * 1024,
        "threads": 2,
        "par_asm_simd_dot_call_bench": 9_332_513,
        "par_simd_dot_call_bench": 17_949_872,
    },
    {
        "count": 512 * 1024,
        "threads": 2,
        "par_asm_simd_dot_call_bench": 18_614_759,
        "par_simd_dot_call_bench": 35_862_397,
    },
    {
        "count": 16 * 1024,
        "threads": 4,
        "par_asm_simd_dot_call_bench": 331_905,
        "par_simd_dot_call_bench": 343_830,
    },
    {
        "count": 32 * 1024,
        "threads": 4,
        "par_asm_simd_dot_call_bench": 616_150,
        "par_simd_dot_call_bench": 861_623,
    },
    {
        "count": 64 * 1024,
        "threads": 4,
        "par_asm_simd_dot_call_bench": 1_172_942,
        "par_simd_dot_call_bench": 2_086_547,
    },
    {
        "count": 128 * 1024,
        "threads": 4,
        "par_asm_simd_dot_call_bench": 2_308_024,
        "par_simd_dot_call_bench": 4_429_078,
    },
    {
        "count": 256 * 1024,
        "threads": 4,
        "par_asm_simd_dot_call_bench": 4_712_341,
        "par_simd_dot_call_bench": 9_004_212,
    },
    {
        "count": 512 * 1024,
        "threads": 4,
        "par_asm_simd_dot_call_bench": 9_559_588,
        "par_simd_dot_call_bench": 17_954_482,
    },
    {
        "count": 16 * 1024,
        "threads": 11,
        "par_asm_simd_dot_call_bench": 193_891,
        "par_simd_dot_call_bench": 186_078,
    },
    {
        "count": 32 * 1024,
        "threads": 11,
        "par_asm_simd_dot_call_bench": 363_886,
        "par_simd_dot_call_bench": 452_884,
    },
    {
        "count": 64 * 1024,
        "threads": 11,
        "par_asm_simd_dot_call_bench": 644_784,
        "par_simd_dot_call_bench": 1_240_254,
    },
    {
        "count": 128 * 1024,
        "threads": 11,
        "par_asm_simd_dot_call_bench": 1_333_099,
        "par_simd_dot_call_bench": 2_600_192,
    },
    {
        "count": 256 * 1024,
        "threads": 11,
        "par_asm_simd_dot_call_bench": 2_758_177,
        "par_simd_dot_call_bench": 5_226_970,
    },
    {
        "count": 512 * 1024,
        "threads": 11,
        "par_asm_simd_dot_call_bench": 5_483_705,
        "par_simd_dot_call_bench": 10_327_367,
    },
]
#%%
#!pip install matplotlib
import matplotlib.pyplot as plt
import numpy as np

for thread_count in [2, 4, 11]:
    x = [r["count"] for r in c3_vcpu_results if r["threads"] == thread_count]
    y = [r["par_simd_dot_call_bench"] for r in c3_vcpu_results if r["threads"] == thread_count]
    plt.scatter(x, y)
    plt.plot(np.unique(x), np.poly1d(np.polyfit(x, y, 1))(np.unique(x)), label="_nolegend_")

plt.ylabel("Time (ns)")
plt.xlabel("Embeddings count")
plt.title("512D embedding dot products, c3 + f32")
plt.legend(["c3-standard-4", "c3-standard-8", "c3-standard-22"])
plt.xscale("log", base=2)
plt.yscale("log", base=2)
plt.show()
#%%
#!pip install matplotlib
import matplotlib.pyplot as plt
import numpy as np

for thread_count in [2, 4, 11]:
    x = [r["count"] for r in c3_vcpu_results if r["threads"] == thread_count]
    y = [r["par_asm_simd_dot_call_bench"] for r in c3_vcpu_results if r["threads"] == thread_count]
    plt.scatter(x, y)
    plt.plot(np.unique(x), np.poly1d(np.polyfit(x, y, 1))(np.unique(x)), label="_nolegend_")

plt.ylabel("Time (ns)")
plt.xlabel("Embeddings count")
plt.title("512D embedding dot products, c3 + f16")
plt.legend(["c3-standard-4", "c3-standard-8", "c3-standard-22"])
plt.xscale("log", base=2)
plt.yscale("log", base=2)
plt.show()
#%%
#!pip install matplotlib
import matplotlib.pyplot as plt
import numpy as np

labels = []
values = []

# Use only vCPU results since they better reflect the max performance we can get from a VM
for thread_count in [1, 2, 4, 8, 16]:
    x = [r["count"] for r in n2_vcpu_results if r["threads"] == thread_count]
    y = [r["par_simd_dot_call_bench"] for r in n2_vcpu_results if r["threads"] == thread_count]
    values.append(np.poly1d(np.polyfit(x, y, 1))[1]) # [1] is the linear coefficient, i.e. rate
    labels.append("n2-standard-{}, f32".format(thread_count * 2))

for thread_count in [2, 4, 11]:
    x = [r["count"] for r in c3_vcpu_results if r["threads"] == thread_count]
    y = [r["par_simd_dot_call_bench"] for r in c3_vcpu_results if r["threads"] == thread_count]
    values.append(np.poly1d(np.polyfit(x, y, 1))[1])
    labels.append("c3-standard-{}, f32".format(thread_count * 2))

for thread_count in [2, 4, 11]:
    x = [r["count"] for r in c3_vcpu_results if r["threads"] == thread_count]
    y = [r["par_asm_simd_dot_call_bench"] for r in c3_vcpu_results if r["threads"] == thread_count]
    values.append(np.poly1d(np.polyfit(x, y, 1))[1])
    labels.append("c3-standard-{}, f16".format(thread_count * 2))

barlist = plt.bar(labels, values)
colors = ['r', 'r', 'r', 'r', 'r', 'b', 'b', 'b', 'g', 'g', 'g']
for bar, color in zip(barlist, colors):
    bar.set_color(color)

plt.xticks(rotation=60, ha='right')
plt.ylabel("ns per embedding at scale")
plt.grid(axis='y')
plt.title("Cross-platform comparison")
plt.show()