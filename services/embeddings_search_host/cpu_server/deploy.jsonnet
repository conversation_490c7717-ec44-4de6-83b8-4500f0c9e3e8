// K8S deployment file for the embeddings search
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local namespaces = import 'deploy/tenants/namespaces.jsonnet';
local publisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';

local deploy = function(env, namespace, cloud, namespace_config, replicas, suffix)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local isShardNamespace = namespaces.isShardNamespace(env=env, namespace=namespace, cloud=cloud);
  local isBigNamespace = isShardNamespace || namespace_config.flags.useBigEmbeddingsSearch;
  local appName = 'embeddings-search-cpu' + suffix;
  local serviceName = appName + '-svc';
  local services = grpcLib.grpcService(appName=appName, namespace=namespace);
  local requestInsightPublisher = publisherLib(cloud, env, namespace, appName);
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);
  // create a server certificate for MTLS
  local serverCert = certLib.createServerCert(name=appName + '-server-certificate',
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=grpcLib.grpcServiceNames(appName),
                                              volumeName='certs');
  local clientCert = certLib.createClientCert(name=appName + '-client-certificate',
                                              appName=appName,
                                              namespace=namespace);
  local centralClientCert = certLib.createCentralClientCert(name=appName + '-central-client-certificate',
                                                            appName=appName,
                                                            env=env,
                                                            namespace=namespace,
                                                            dnsNames=grpcLib.grpcServiceNames(appName, namespace));
  // IAM service accounts have a 30 character limit. Use a shortened name to get around this.
  local shortAppName = 'embed-search' + suffix;
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName,
  );
  local serviceAccountObjects = serviceAccount.objects + [
    requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
  ];
  local gib = 1024 * 1024 * 1024;
  local config = {
    bind_address: '0.0.0.0:50051',
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    namespace: namespace,
    metrics_server_bind_address: '0.0.0.0',
    metrics_server_port: 9090,
    server_mtls_config: if mtls then serverCert.config else null,
    client_mtls_config: if mtls then clientCert.config else null,
    central_client_mtls_config: if mtls then centralClientCert.config else null,
    content_manager_endpoint: 'content-manager-headless-svc:50051',
    content_manager_request_timeout_s: 10.0,
    auth_config: {
      token_exchange_endpoint: 'token-exchange-central-svc.%s:50051' % cloudInfo.getCentralNamespaceForNamespace(env=env, namespace=namespace, cloud=cloud),
      token_exchange_request_timeout_s: 10.0,
    },
    embeddings_search_endpoint: if mtls then 'https://%s-svc:50051' % appName else 'http://%s-svc:50051' % appName,
    working_set_endpoint: 'working-set-svc:50051',
    // TODO: this is likely bigger than it needs to be.
    checkpoint_cache_size_bytes: if namespace_config.flags.usePremiumCpuHighmem then 2 * gib else (if isBigNamespace then 1 * gib else 512 * 1024 * 1024),
    checkpoint_cache_eviction: 'TinyLFU',
    // Napkin math: embeddings are ~1KB so with 5 transformation keys we have room for ~200k chunks' worth of embeddings per namespace per GiB.
    // TODO: better handling of the multi-tenant (isShardNamespace) case so we can support more tenants per shard
    //
    // Note: if the size of the cache grows, the startupProbe might need to be revisted.
    index_cache_size_bytes: if namespace_config.flags.usePremiumCpuHighmem then 34 * gib else (if isBigNamespace then 14 * gib else 4 * gib),
    index_cache_eviction: 'LRU',
    // The chunk cache should be much smaller than the embedding cache, since
    // each search request will only return a fraction of the chunks it searches
    // the embeddings for.
    // At time of writing, each completion host used <1GB of memory (which is
    // where chunks used to be cached before the SearchChunks() API existed), so
    // that should be plenty of cache here.
    chunk_cache_size_bytes: if namespace_config.flags.usePremiumCpuHighmem then 4 * gib else (if isBigNamespace then 2 * gib else 1 * gib),
    chunk_cache_eviction: 'TinyLFU',
    indexed_checkpoint_cache_size_bytes: if namespace_config.flags.usePremiumCpuHighmem then 8 * gib else (if isBigNamespace then 4 * gib else 1 * gib),
    indexed_checkpoint_cache_eviction: 'LRU',
    cache_tti_seconds: 3 * 24 * 60 * 60,  // 3 days -- enough for the weekend
    cache_replication_timeout: if namespace_config.flags.usePremiumCpuHighmem then 1200 else (if isBigNamespace then 600 else 150),
    thread_count: if isBigNamespace then 6 else 2,
    search_timeout_ms: 1000,
    grpc_max_concurrent_streams: 100,
  };

  local configMapAndDeployment(config, replicas, extraLabels={}, deploymentNameSuffix='') =
    // configMap's name is derived from appName and hash of config, so different configurations produce
    // distinct configMaps
    local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);
    local container =
      {
        name: appName,
        target: {
          name: '//services/embeddings_search_host/cpu_server:image',
          dst: appName,
        },
        ports: [
          {
            containerPort: 50051,
            name: 'grpc-svc',
          },
        ],
        args: [
          '--config-file',
          configMap.filename,
          '--request-insight-publisher-config-file',
          requestInsightPublisher.configFilePath,
        ],
        volumeMounts: [
          configMap.volumeMountDef,
          serverCert.volumeMountDef,
          clientCert.volumeMountDef,
          centralClientCert.volumeMountDef,
          dynamicFeatureFlags.volumeMountDef,
          requestInsightPublisher.volumeMountDef,
        ],
        env: telemetryLib.telemetryEnv(serviceName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env
             + if env == 'DEV' then [
               {
                 name: 'RUST_BACKTRACE',
                 value: 'full',
               },
             ] else [],
        readinessProbe: grpcLib.grpcHealthCheck(serviceName, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
          periodSeconds: 30,
        },
        livenessProbe: grpcLib.grpcHealthCheck(serviceName, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
          periodSeconds: 30,
        },
        startupProbe: grpcLib.grpcHealthCheck(serviceName, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
          // Startup Probe should give time for the cache to be replicated.
          failureThreshold: std.ceil(config.cache_replication_timeout / 30) + 2,
          periodSeconds: 30,
          // timeout for each probe
          timeoutSeconds: 10,
        },
        resources: {
          // Note: the non-shard-namespace sizing is optimistically small, intended to allow three
          // instances of the service to run on the same node. In sharded namespaces we come close to
          // using a whole node. The memory limit needs to exceed the sum of the cache sizes above.
          //
          // In practice a c3-standard-8 node seems to have about 7 vCPU and 26 GiB RAM available.
          // This was empirically calculated by looking at a node in the GKE console and subtracting
          // the CPU/memory used by various other services (fluentbit, gke processes, etc) from the
          // allocatable CPU and memory reported for the node.
          limits: {
            cpu: if isBigNamespace then '7000m' else '2300m',
            // Cache sizing breakdown (see above):
            // - highmem namespace:  34GB embeddings,  8GB ann indexes,  4GB chunks,    2GB checkpoint blobs,   48GB total
            // - big namespace:      14GB embeddings,  4GB ann indexes,  2GB chunks,    1GB checkpoint blobs,   21GB total
            // - small namespace:    4GB embeddings,   1GB ann indexes,  1GB chunks,  0.5GB checkpoint blobs,  6.5GB total
            // We need some overhead for short-lived allocations. There's also a suspected slow resource leak somewhere that causes occasional OOMs.
            memory: if namespace_config.flags.usePremiumCpuHighmem then '54Gi' else (if isBigNamespace then '26Gi' else '8Gi'),
          },
        },
      };
    local pod =
      {
        serviceAccountName: serviceAccount.name,
        containers: [
          container,
        ],
        volumes: [
          serverCert.podVolumeDef,
          clientCert.podVolumeDef,
          centralClientCert.podVolumeDef,
          configMap.podVolumeDef,
          dynamicFeatureFlags.podVolumeDef,
          requestInsightPublisher.podVolumeDef,
        ],
      };
    local cpu = if namespace_config.flags.usePremiumCpuHighmem then 'premiumCpuHighmem' else 'premiumCpu';
    local tolerations = nodeLib.tolerations(resource=cpu, env=env, cloud=cloud);
    // prevent two pods from being scheduled on the same node (by having appName set in affinity)
    // this can limit the effectiveness of the c3 machines in small clusters (e.g. EU). That is
    // a tradeoff we are willing to make.
    local affinity = nodeLib.affinity(resource=cpu, env=env, cloud=cloud, appName=appName);
    local deployment =
      {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        metadata: {
          name: appName + deploymentNameSuffix,
          namespace: namespace,
          labels: {
            app: appName,
          },
          annotations: {
            'reloader.stakater.com/search': 'true',
          },
        },
        spec: {
          // Adjust deploy timeout to allow for cache replication time
          progressDeadlineSeconds: config.cache_replication_timeout * 2 + 300,
          replicas: replicas,
          minReadySeconds: if env == 'DEV' then 0 else 60,
          strategy: {
            type: 'RollingUpdate',
            rollingUpdate: {
              maxSurge: 1,
              maxUnavailable: 0,
            },
          },
          selector: {
            // Note: pod-template-hash will be added to this selector
            // for the ReplicaSet
            matchLabels: {
              app: appName,
            },
          },
          template: {
            metadata: {
              labels: extraLabels + {
                app: appName,
              },
            },
            spec: pod + {
              tolerations: tolerations,
              affinity: affinity,
              priorityClassName: cloudInfo.envToPriorityClass(env, resource=cpu),
            },
          },
        },
      };
    lib.flatten([
      configMap.objects,
      deployment,
    ]);

  // When deploying a second config under the same app/service name for canary/AB testing, in order to
  // avoid dropping below the target replicaCount for the app, use multiple phases:
  // 1. keep full replica count for default deployment, and add desired replicas for the new config
  // 2. reduce the replica count for default deployment
  // 3. Repeat as desired
  local canaryConfig = config + {/* changes here */ };
  local canaryReplicaCount = 0;
  // Later: defaultReplicaCount = std.max(1, replicaCount - canaryReplicaCount);

  local deployObjects = configMapAndDeployment(config, replicas, { release: 'stable' }) +
                        (if canaryReplicaCount > 0 then configMapAndDeployment(canaryConfig, canaryReplicaCount, { release: 'canary' }, '-canary')
                         else []);
  // For deployments with 4 replicas, set minAvailable to 2 to improve availability.
  local pdb = nodeLib.podDisruption(appName=appName, namespace=namespace, env=env, minAvailable=std.floor(replicas / 2));
  lib.flatten([
    deployObjects,
    serviceAccountObjects,
    dynamicFeatureFlags.k8s_objects,
    serverCert.objects,
    clientCert.objects,
    centralClientCert.objects,
    services,
    pdb,
  ]);

function(env, namespace, cloud, namespace_config)

  // Default to 4 replicas on shard namespaces and 2 on legacy namespaces, but be willing to override
  // for tenants with higher CPU load spikes. Note that this scales separately from the working set size
  // which is the parameter isBigNamespace is designed to handle. A namespace that exceeds the supported
  // working set size will have slow completions regardless of how many replicas are available due to
  // cache misses, but we can scale out replicas to reduce CPU contention which scales with working set
  // size times the number of simultaneous inference requests.
  local isShardNamespace = namespaces.isShardNamespace(env=env, namespace=namespace, cloud=cloud);
  local replicaCount = if env == 'DEV' then 1 else
    std.max(if isShardNamespace then 4 else 2, namespace_config.flags.embeddingsSearchReplicasOverride);

  local deploys = (if namespace_config.flags.enablePartitionedEmbeddingsSearch then [] else [deploy(env, namespace, cloud, namespace_config, replicaCount, '')])
                  + std.map(
                    function(i) deploy(env, namespace, cloud, namespace_config, if env == 'DEV' then 1 else 2, '-' + std.toString(i)),
                    std.range(0, namespace_config.flags.embeddingsSearchPartitions - 1),
                  );
  lib.flatten(deploys)
