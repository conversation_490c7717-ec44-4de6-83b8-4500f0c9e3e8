use std::sync::Arc;
use std::time::Duration;
use std::time::Instant;

use crate::config::Config;
use crate::metrics::{
    CacheType, CACHE_BYTES_COUNT, CACHE_DOWNLOAD_LATENCY_COLLECTOR, CACHE_ENTRY_COUNT,
    CACHE_EVICTION_COUNT_COLLECTOR, CACHE_LOOKUP_COUNT_COLLECTOR, CHECKPOINT_GET_BLOBS_COUNTER,
    CHECKPOINT_GET_COUNTER, CHECKPOINT_GET_LATENCY_COLLECTOR,
};
use async_trait::async_trait;
use blob_names::BlobName;
use content_manager_client::ContentManagerClient;
use moka::future::Cache;
use request_context::{RequestContext, TenantId, TenantInfo, EMPTY_TENANT_ID};

type CheckpointId = String;

#[derive(Clone)]
pub struct CheckpointEntry {
    pub checkpoint_key: CheckpointId,
    pub blobs: Vec<BlobName>,
}

type CheckpointResult = Arc<CheckpointEntry>;

/// checkpoint caching and lookup
#[async_trait]
pub trait CheckpointCache {
    // returns a set of blob names for the given checkpoint
    async fn get_checkpoint(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_key: CheckpointId,
    ) -> tonic::Result<CheckpointResult>;
}

pub struct CheckpointCacheImpl {
    data: Arc<CheckpointCacheDataImpl>,
}
impl CheckpointCacheImpl {
    pub fn new(
        config: Config,
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    ) -> Self {
        Self {
            data: Arc::new(CheckpointCacheDataImpl::new(config, content_manager)),
        }
    }
}

#[async_trait]
impl CheckpointCache for CheckpointCacheImpl {
    async fn get_checkpoint(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_key: CheckpointId,
    ) -> tonic::Result<CheckpointResult> {
        if let Some(result) = self
            .data
            .read_from_cache(
                tenant_info.tenant_id.as_ref().unwrap_or(&EMPTY_TENANT_ID),
                &checkpoint_key,
            )
            .await
        {
            CACHE_LOOKUP_COUNT_COLLECTOR
                .with_label_values(&[
                    CacheType::Checkpoint.as_str(),
                    "hit",
                    tenant_info.metrics_tenant_name(),
                ])
                .inc();
            return Ok(result);
        } else {
            CACHE_LOOKUP_COUNT_COLLECTOR
                .with_label_values(&[
                    CacheType::Checkpoint.as_str(),
                    "miss",
                    tenant_info.metrics_tenant_name(),
                ])
                .inc();
        }

        CACHE_ENTRY_COUNT
            .with_label_values(&[CacheType::Checkpoint.as_str()])
            .set(self.data.cache.entry_count() as f64);
        CACHE_BYTES_COUNT
            .with_label_values(&[CacheType::Checkpoint.as_str()])
            .set(self.data.cache.weighted_size() as f64);

        self.data
            .read_from_content_manager(request_context, tenant_info, checkpoint_key)
            .await
    }
}

struct CheckpointCacheDataImpl {
    pub content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    pub cache: Cache<(TenantId, CheckpointId), Arc<CheckpointEntry>>,
}

impl CheckpointCacheDataImpl {
    pub fn new(
        config: Config,
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    ) -> Self {
        let cache = Cache::builder()
            .max_capacity(config.checkpoint_cache_size_bytes)
            .eviction_policy(config.checkpoint_cache_eviction.into())
            // A BlobName is 64 bytes hex-encoded. Add an estimate of the key size
            .weigher(|_, v: &Arc<CheckpointEntry>| (v.as_ref().blobs.len() as u32) * 64 + 128)
            .time_to_idle(Duration::from_secs(config.cache_tti_seconds))
            .eviction_listener(|_, _, c| {
                let removal_cause = format!("{:?}", c);
                CACHE_EVICTION_COUNT_COLLECTOR
                    .with_label_values(&[CacheType::Checkpoint.as_str(), &removal_cause])
                    .inc();
            })
            .build();
        CheckpointCacheDataImpl {
            content_manager,
            cache,
        }
    }

    pub async fn read_from_cache(
        &self,
        tenant_id: &TenantId,
        checkpoint_key: &CheckpointId,
    ) -> Option<Arc<CheckpointEntry>> {
        let cache_key = (tenant_id.clone(), checkpoint_key.clone());
        self.cache.get(&cache_key).await
    }

    pub async fn read_from_content_manager(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_key: CheckpointId,
    ) -> tonic::Result<CheckpointResult> {
        let start = Instant::now();
        let mut response = self
            .content_manager
            .get_checkpoint_blob_names(request_context, &tenant_info.tenant_id, &checkpoint_key)
            .await;
        let mut blobs: Vec<BlobName> = Vec::new();
        while let Some(message) = response.recv().await {
            for blob_name in message?.blob_names.iter() {
                blobs.push(BlobName::from_bytes(blob_name)?);
            }
        }
        CHECKPOINT_GET_COUNTER
            .with_label_values(&[tenant_info.metrics_tenant_name()])
            .inc();
        CHECKPOINT_GET_BLOBS_COUNTER
            .with_label_values(&[tenant_info.metrics_tenant_name()])
            .inc_by(blobs.len() as u64);
        CHECKPOINT_GET_LATENCY_COLLECTOR
            .with_label_values(&[tenant_info.metrics_tenant_name()])
            .observe(start.elapsed().as_secs_f64());
        CACHE_DOWNLOAD_LATENCY_COLLECTOR
            .with_label_values(&[
                CacheType::Checkpoint.as_str(),
                tenant_info.metrics_tenant_name(),
            ])
            .observe(start.elapsed().as_secs_f64());
        let entry = Arc::new(CheckpointEntry {
            checkpoint_key,
            blobs,
        });
        let cache_key = (
            tenant_info.tenant_id.clone().unwrap_or(EMPTY_TENANT_ID),
            entry.checkpoint_key.clone(),
        );
        self.cache.insert(cache_key, entry.clone()).await;
        Ok(entry)
    }
}

#[cfg(test)]
mod tests {
    use mockall::*;

    use crate::config;

    use super::*;

    use content_manager_client::MockContentManagerClient;
    use content_manager_rs_proto::content_manager::GetAllBlobsFromCheckpointResponse;
    use request_context::RequestContext;
    use tokio::sync::mpsc;

    #[tokio::test]
    async fn test_get_checkpoint() {
        let mut mock = MockContentManagerClient::new();
        // First expected call
        mock.expect_get_checkpoint_blob_names()
            .with(
                predicate::always(),
                predicate::always(),
                predicate::eq("checkpoint-id-1"),
            )
            .times(1)
            .return_once(|_, _, _| {
                let (tx, rx) = mpsc::channel(1);
                tokio::spawn(async move {
                    tx.send(Ok(GetAllBlobsFromCheckpointResponse {
                        blob_names: vec![[0; 32].to_vec()],
                    }))
                    .await
                    .unwrap();
                });
                rx
            });
        // Second expected call
        mock.expect_get_checkpoint_blob_names()
            .with(
                predicate::always(),
                predicate::always(),
                predicate::eq("checkpoint-id-2"),
            )
            .times(1)
            .return_once(|_, _, _| {
                let (tx, rx) = mpsc::channel(1);
                tokio::spawn(async move {
                    tx.send(Ok(GetAllBlobsFromCheckpointResponse {
                        blob_names: vec![[1; 32].to_vec(), [2; 32].to_vec()],
                    }))
                    .await
                    .unwrap();
                });
                rx
            });

        let cache = CheckpointCacheImpl::new(Config::default(), Arc::new(mock));
        let tenant_info = TenantInfo::new_for_test();

        // New checkpoint with a single blob name- new content manager request
        let request_context_1 = RequestContext::new_for_test();
        let result_1 = cache
            .get_checkpoint(
                &request_context_1,
                &tenant_info,
                "checkpoint-id-1".to_string(),
            )
            .await
            .expect("Failed to get index elements");
        assert_eq!(result_1.blobs.len(), 1);

        // Same checkpoint as previous- no new content manager request
        let request_context_2 = RequestContext::new_for_test();
        let result_2 = cache
            .get_checkpoint(
                &request_context_2,
                &tenant_info,
                "checkpoint-id-1".to_string(),
            )
            .await
            .expect("Failed to get index elements");
        assert_eq!(result_2.blobs.len(), 1);

        // New checkpoint that returns multiple blob names- new content manager request
        let request_context_3 = RequestContext::new_for_test();
        let result_3 = cache
            .get_checkpoint(
                &request_context_3,
                &tenant_info,
                "checkpoint-id-2".to_string(),
            )
            .await
            .expect("Failed to get index elements");
        assert_eq!(result_3.blobs.len(), 2);

        // Go back to previous checkpoint- no new content manager request
        let request_context_4 = RequestContext::new_for_test();
        let result_4 = cache
            .get_checkpoint(
                &request_context_4,
                &tenant_info,
                "checkpoint-id-1".to_string(),
            )
            .await
            .expect("Failed to get index elements");
        assert_eq!(result_4.blobs.len(), 1);
    }

    #[tokio::test]
    async fn test_get_checkpoint_error() {
        let mut mock = MockContentManagerClient::new();
        mock.expect_get_checkpoint_blob_names()
            .with(
                predicate::always(),
                predicate::always(),
                predicate::eq("nonexistent-checkpoint-id"),
            )
            .times(2)
            .returning(|_, _, _| {
                let (tx, rx) = mpsc::channel(1);
                tokio::spawn(async move {
                    tx.send(Err(tonic::Status::not_found("Checkpoint does not exist")))
                        .await
                        .unwrap();
                });
                rx
            });

        let cache = CheckpointCacheImpl::new(Config::default(), Arc::new(mock));

        assert!(cache
            .get_checkpoint(
                &RequestContext::new_for_test(),
                &TenantInfo::new_for_test(),
                "nonexistent-checkpoint-id".to_string()
            )
            .await
            .is_err());

        assert!(cache
            .get_checkpoint(
                &RequestContext::new_for_test(),
                &TenantInfo::new_for_test(),
                "nonexistent-checkpoint-id".to_string()
            )
            .await
            .is_err());
    }

    #[tokio::test]
    async fn test_cache_tti() {
        let mut mock = MockContentManagerClient::new();
        // First expected call
        mock.expect_get_checkpoint_blob_names()
            .with(
                predicate::always(),
                predicate::always(),
                predicate::eq("checkpoint-keep-checking"),
            )
            .times(1)
            .return_once(|_, _, _| {
                let (tx, rx) = mpsc::channel(1);
                tokio::spawn(async move {
                    tx.send(Ok(GetAllBlobsFromCheckpointResponse {
                        blob_names: vec![[0; 32].to_vec()],
                    }))
                    .await
                    .unwrap();
                });
                rx
            });
        // Second expected call
        mock.expect_get_checkpoint_blob_names()
            .with(
                predicate::always(),
                predicate::always(),
                predicate::eq("checkpoint-stop-checking"),
            )
            .times(1)
            .return_once(|_, _, _| {
                let (tx, rx) = mpsc::channel(1);
                tokio::spawn(async move {
                    tx.send(Ok(GetAllBlobsFromCheckpointResponse {
                        blob_names: vec![[1; 32].to_vec(), [2; 32].to_vec()],
                    }))
                    .await
                    .unwrap();
                });
                rx
            });

        let config = config::Config {
            cache_tti_seconds: 3,
            ..Default::default()
        };
        let cache = CheckpointCacheImpl::new(config.clone(), Arc::new(mock));

        let request_context = &RequestContext::new_for_test();
        let tenant_info = &TenantInfo::new_for_test();

        // initial read to populate cache
        let result = cache
            .get_checkpoint(
                request_context,
                tenant_info,
                "checkpoint-keep-checking".to_string(),
            )
            .await
            .expect("Failed to get checkpoint");
        assert_eq!(
            result.checkpoint_key,
            "checkpoint-keep-checking".to_string(),
            "Unexpected checkpoint key"
        );
        let result = cache
            .get_checkpoint(
                request_context,
                tenant_info,
                "checkpoint-stop-checking".to_string(),
            )
            .await
            .expect("Failed to get checkpoint");
        assert_eq!(
            result.checkpoint_key,
            "checkpoint-stop-checking".to_string(),
            "Unexpected checkpoint key"
        );

        // our cache exp is 3s, so expect that this is still around
        tokio::time::sleep(Duration::from_secs(2)).await;
        let result = cache
            .data
            .read_from_cache(
                tenant_info.tenant_id.as_ref().unwrap_or(&EMPTY_TENANT_ID),
                &"checkpoint-keep-checking".to_string(),
            )
            .await
            .expect("Failed to get checkpoint");
        assert_eq!(
            result.checkpoint_key,
            "checkpoint-keep-checking".to_string(),
            "Unexpected checkpoint key"
        );

        // wait another two seconds -- recently accessed checkpoint should be around; other should not
        tokio::time::sleep(Duration::from_secs(2)).await;
        let result = cache
            .data
            .read_from_cache(
                tenant_info.tenant_id.as_ref().unwrap_or(&EMPTY_TENANT_ID),
                &"checkpoint-stop-checking".to_string(),
            )
            .await;
        assert!(result.is_none());

        let result = cache
            .data
            .read_from_cache(
                tenant_info.tenant_id.as_ref().unwrap_or(&EMPTY_TENANT_ID),
                &"checkpoint-keep-checking".to_string(),
            )
            .await
            .expect("Failed to get checkpoint");
        assert_eq!(
            result.checkpoint_key,
            "checkpoint-keep-checking".to_string(),
            "Unexpected checkpoint key"
        );
    }
}
