use std::collections::{BTreeSet, HashMap};
use std::sync::atomic::AtomicI64;
use std::sync::Arc;

use crate::dot_math::SimdData;
use blob_names::BlobName;
use numpy::NumpyTensor;
use scann_rs::ScannRsIndex;

pub type CheckpointId = String;

// Use this file for types shared between stateful components

// explicitly not clone since atomics are not clone
pub struct BlobEntry<T: SimdData> {
    pub blob_name: BlobName,
    pub tensor: NumpyTensor<T>,
    pub epoch_us: AtomicI64,
}

impl<T: SimdData> BlobEntry<T> {
    pub fn size_bytes(&self) -> u32 {
        // Tensor is 32 bytes per element (T is f16x16 or f32x8)
        (self.tensor.data.len() as u32 * 32) + BLOB_NAME_BYTES
    }
}

#[derive(Clone)]
pub struct AnnIndex {
    // entry: (blob_name, chunk #)
    // TODO: deduplicate blob names without compromising lookups
    pub indices_to_chunks: Vec<(BlobName, usize)>,
    // value side: (start index, # chunks)
    pub blobs_to_indices: HashMap<BlobName, (usize, usize)>,
    pub scann_index: Arc<ScannRsIndex>,
}

const BLOB_NAME_BYTES: u32 = 32;
const USIZE_BYTES: u32 = 4;
const QUANTIZATION_BYTES: u32 = 256; // for dims per block = 2
const INDEX_OVERHEAD_BYTES: u32 = 32 * 1024; // empirical estimate

impl AnnIndex {
    pub fn new(blob_infos: &[(BlobName, usize)], scann_index: Arc<ScannRsIndex>) -> Self {
        let mut indices_to_chunks = Vec::new();
        let mut blobs_to_indices = HashMap::new();
        for (blob_name, num_chunks) in blob_infos.iter() {
            let start_index = indices_to_chunks.len();
            for chunk_index in 0..*num_chunks {
                indices_to_chunks.push((blob_name.clone(), chunk_index));
            }
            blobs_to_indices.insert(blob_name.clone(), (start_index, *num_chunks));
        }
        Self {
            indices_to_chunks,
            blobs_to_indices,
            scann_index,
        }
    }

    pub fn size_bytes(&self) -> u32 {
        self.indices_to_chunks.len() as u32 * (BLOB_NAME_BYTES + USIZE_BYTES + QUANTIZATION_BYTES)
            + self.blobs_to_indices.len() as u32 * (BLOB_NAME_BYTES + USIZE_BYTES + USIZE_BYTES)
            + INDEX_OVERHEAD_BYTES
    }
}

#[derive(Clone)]
pub struct IndexedCheckpoint {
    pub index: Arc<AnnIndex>,
    // Added means "in the checkpoint but not in the index"
    pub added: BTreeSet<BlobName>,
    // Deleted means "in the index but not in the checkpoint"
    pub deleted: BTreeSet<BlobName>,
    pub index_id: String,
}

impl IndexedCheckpoint {
    #[allow(dead_code)]
    pub fn size_bytes(&self) -> u32 {
        self.index.size_bytes()
            + (self.added.len() as u32 * BLOB_NAME_BYTES)
            + (self.deleted.len() as u32 * BLOB_NAME_BYTES)
    }

    pub fn len(&self) -> usize {
        self.index.blobs_to_indices.len()
    }
}

pub enum CheckpointState {
    Indexed(Arc<IndexedCheckpoint>),
    NotIndexed(BTreeSet<BlobName>),
    NotFound,
}

pub struct CheckpointBlobState {
    pub checkpoint_state: CheckpointState,
    pub added_blobs: BTreeSet<BlobName>,
    pub deleted_blobs: BTreeSet<BlobName>,
}

impl CheckpointBlobState {
    pub fn len(&self) -> usize {
        let checkpoint_len = match &self.checkpoint_state {
            CheckpointState::Indexed(indexed_checkpoint) => indexed_checkpoint.len(),
            CheckpointState::NotIndexed(unindexed_checkpoint_blobs) => {
                unindexed_checkpoint_blobs.len()
            }
            CheckpointState::NotFound => 0,
        };
        checkpoint_len + self.added_blobs.len() - self.deleted_blobs.len()
    }
}
