use lazy_static::lazy_static;
use prometheus::{
    register_gauge_vec, register_histogram, register_histogram_vec, register_int_counter,
    register_int_counter_vec, register_int_gauge_vec, GaugeVec, Histogram, HistogramVec,
    IntCounter, IntCounterVec, IntGaugeVec, Opts,
};

#[derive(Debug, PartialEq, Eq)]
pub enum CacheType {
    Index,
    Chunk,
    Checkpoint,
    AnnIndex,
    IndexedCheckpoint,
}

impl CacheType {
    pub fn as_str(&self) -> &'static str {
        match self {
            CacheType::Index => "index",
            CacheType::Chunk => "chunk",
            CacheType::Checkpoint => "checkpoint",
            CacheType::AnnIndex => "ann_index",
            CacheType::IndexedCheckpoint => "indexed_checkpoint",
        }
    }
}

lazy_static! {
    pub static ref CACHE_LOOKUP_COUNT_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_embeddings_search_cache_lookup_count",
            "Cache lookup count"
        ),
        &["cache_type", "result", "tenant_name"]
    )
    .expect("metric can be created");
    pub static ref CACHE_EVICTION_COUNT_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_embeddings_search_cache_eviction_count",
            "Cache eviction count"
        ),
        &["cache_type", "removal_cause"]
    )
    .expect("metric can be created");
    pub static ref CACHE_ENTRY_COUNT: GaugeVec = register_gauge_vec!(
        "au_embeddings_search_cache_entry_count",
        "Number of entries in the cache",
        &["cache_type"]
    )
    .expect("metric can be created");
    pub static ref CACHE_BYTES_COUNT: GaugeVec = register_gauge_vec!(
        "au_embeddings_search_cache_bytes_count",
        "Number of bytes in the cache",
        &["cache_type"]
    )
    .expect("metric can be created");
    pub static ref CACHE_DOWNLOAD_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_embeddings_search_cache_download_latency",
        "The latency of downloading missing cache entries per request, in seconds",
        &["cache_type", "tenant_name"],
        vec![0.0005, 0.001, 0.0025, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0],
    )
    .expect("metric can be created");
    pub static ref CHECKPOINT_GET_COUNTER: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_embeddings_search_checkpoint_get_count",
            "Number of blob checkpoints fetched from content manager"
        ),
        &["tenant_name"]
    )
    .expect("metric can be created");
    pub static ref CHECKPOINT_GET_BLOBS_COUNTER: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_embeddings_search_checkpoint_get_blobs_count",
            "Number of fetched checkpoint blob names"
        ),
        &["tenant_name"]
    )
    .expect("metric can be created");
    pub static ref DOWNLOAD_COUNTER: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_embeddings_search_download_count",
            "Number of downloaded embeddings"
        ),
        &["tenant_name"]
    )
    .expect("metric can be created");
    pub static ref DOWNLOAD_BYTES_COUNTER: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_embeddings_search_download_bytes_count",
            "Size of downloaded embeddings in bytes"
        ),
        &["tenant_name"]
    )
    .expect("metric can be created");
    pub static ref BACKGROUND_FETCH_BLOBS_COUNT_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_embeddings_search_background_fetch_blobs_count",
            "Number of blobs enqueued/dequeued/downloaded for background fetch"
        ),
        &["event_type"]
    )
    .expect("metric can be created");
    pub static ref SEARCH_BLOB_COUNTER: IntCounter = register_int_counter!(
        "au_embeddings_search_blob_count",
        "Number of blobs that are part of an embeddings search"
    )
    .expect("metric can be created");
    pub static ref SEARCH_EMBEDDING_COUNTER: IntCounter = register_int_counter!(
        "au_embeddings_search_embedding_count",
        "Number of blob embeddings that are part of an embeddings search"
    )
    .expect("metric can be created");
    pub static ref BLOBS_PER_REQUEST_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_embeddings_search_blobs_per_request",
        "The number of blobs in a given request",
        &["tenant_name"],
        vec![100.0, 160.0, 250.0, 400.0, 640.0, 1000.0, 1600.0, 2500.0, 4000.0, 6400.0, 10000.0, 16000.0, 25000.0, 40000.0, 64000.0, 100000.0, 160000.0, 250000.0, 400000.0, 640000.0, 1000000.0],
    )
    .expect("metric can be created");
    pub static ref CHECKPOINT_GET_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_embeddings_search_checkpoint_get_latency",
        "The embeddings search blob checkpoint get latencies in seconds",
        &["tenant_name"],
        vec![0.0005, 0.001, 0.0025, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0],
    )
    .expect("metric can be created");
    pub static ref DOWNLOAD_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_embeddings_search_download_latency",
        "The embeddings search download latencies in seconds",
        &["tenant_name"],
        vec![0.0005, 0.001, 0.0025, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0],
    )
    .expect("metric can be created");
    pub static ref CPU_SEARCH_LATENCY_COLLECTOR: Histogram = register_histogram!(
        "au_embeddings_search_cpu_latency",
        "The embeddings search CPU search latencies in seconds",
        vec![0.0005, 0.001, 0.0025, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0],
    )
    .expect("metric can be created");
    pub static ref ANN_SEARCH_LATENCY_COLLECTOR: Histogram = register_histogram!(
        "au_embeddings_search_ann_latency",
        "The embeddings search ANN search latencies in seconds",
        vec![0.0005, 0.001, 0.0025, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0],
    )
    .expect("metric can be created");
    pub static ref PHASE_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_embeddings_search_phase_latency",
        "The embeddings search phase latencies in seconds",
        &["phase", "transformation_key", "request_source"],
        vec![0.0005, 0.001, 0.0025, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0],
    )
    .expect("metric can be created");
    pub static ref RESPONSE_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_rpc_latency_histogram",
        "Histogram of RPC latencies",
        &["service", "endpoint", "status_code", "request_source", "tenant_name"]
    )
    .expect("metric can be created");
    pub static ref ACTIVE_REQUESTS_COLLECTOR: IntGaugeVec = register_int_gauge_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_active_requests_gauge",
        "The number of currently active requests",
        &["service", "endpoint", "tenant_name"],
    )
    .expect("metric can be created");
    pub static ref BLOB_DOWNLOAD_STATE_COUNT: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_embeddings_search_blob_download_state_count",
            "Count of what downloading state each blob searched was in"
        ),
        &["result", "deadline", "tenant_name"]
    )
    .expect("metric can be created");
    pub static ref REPLICA_BLOB_RECEIVED: IntCounter = register_int_counter!(
            "au_embeddings_search_replica_blob_received_count",
            "Count the number of received entries from the cache replicator"
    )
    .expect("metric can be created");
    pub static ref REPLICA_BLOB_SENT: IntCounter = register_int_counter!(
            "au_embeddings_search_replica_blob_sent_count",
            "Count the number of sent entries from the cache replicator"
    )
    .expect("metric can be created");
    pub static ref REPLICA_BYTES_SENT: IntCounter = register_int_counter!(
            "au_embeddings_search_replica_bytes_sent_count",
            "Count the number of bytes sent from the cache replicator"
    )
    .expect("metric can be created");
    pub static ref REPLICA_BLOB_SKIPPED: IntCounter = register_int_counter!(
        "au_embeddings_search_replica_blob_skipped_count",
        "Count the number of cache entries skipped by the cache replicator"
    ).expect("metric can be created");
    pub static ref REPLICA_BYTES_RECEIVED: IntCounter = register_int_counter!(
            "au_embeddings_search_replica_bytes_received_count",
            "Count the number of bytes received from the cache replicator"
    )
    .expect("metric can be created");
    pub static ref ANN_INDEX_SEARCH_QUALITY: HistogramVec = register_histogram_vec!(
            "au_embeddings_search_ann_quality",
            "fraction of sampled results from ANN-accelerated search that match exhaustive search",
            &["transformation_key"],
            vec![0.01, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
    )
    .expect("metric can be created");
}
