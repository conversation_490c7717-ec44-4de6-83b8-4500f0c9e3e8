load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:python.bzl", "py_binary", "py_oci_image", "pytest_test")
load("//tools/bzl:metadata.bzl", "metadata_test")

py_binary(
    name = "server",
    srcs = [
        "indexes.py",
        "server.py",
    ],
    main = "server.py",
    deps = [
        "//base/blob_names/python:blob_names",
        "//base/caching:cache_metrics",
        "//base/caching:lru_cache",
        "//base/logging:struct_logging",
        "//base/proto:tensor",
        "//base/python/signal_handler",
        "//base/tracing:tracing_py",
        "//services/content_manager/client",
        "//services/embeddings_search_host:embeddings_search_py_proto",
        "//services/lib/grpc/auth:service_auth_interceptor",
        "//services/lib/grpc/auth:service_token_auth",
        "//services/lib/grpc/metrics",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/lib/request_context:request_context_py",
        requirement("grpcio-health-checking"),
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("opentelemetry-instrumentation-grpc"),
        requirement("prometheus-client"),
        requirement("protobuf"),
        requirement("numpy"),
        requirement("scann"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services/deploy:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

pytest_test(
    name = "indexes_test",
    srcs = ["indexes_test.py"],
    deps = [
        ":server",
        requirement("numpy"),
        requirement("pytest-grpc"),
    ],
)

pytest_test(
    name = "server_test",
    srcs = ["server_test.py"],
    deps = [
        ":server",
        "//services/content_manager:content_manager_py_proto",
        "//services/embeddings_search_host:embeddings_search_py_proto",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
        requirement("numpy"),
        requirement("pytest-grpc"),
    ],
)
