use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::create_channel;
use request_context::RequestContext; // From the local path dependency
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tonic::transport::ClientTlsConfig;
use tracing_tonic::client::TracingService;

// Include the generated code from memstore.proto
pub mod memstore_proto {
    tonic::include_proto!("memstore");
}

// Re-export or use the generated client and request/response types
use memstore_proto::{
    memstore_client::MemstoreClient as ProtoMemstoreClient, DelRequest, ExpireAtRequest,
    GetRequest, IncrByRequest, IncrRequest, SetRequest,
};

#[async_trait]
pub trait MemstoreClient: Send + Sync {
    /// Retrieves the value for a key.
    /// Returns `Ok(None)` if the key doesn't exist.
    async fn get(
        &self,
        ctx: &RequestContext,
        key: String,
    ) -> Result<Option<Vec<u8>>, tonic::Status>;

    /// Sets the value for a key with optional configuration.
    async fn set(
        &self,
        ctx: &RequestContext,
        key: String,
        value: Vec<u8>,
        options: SetOptions,
    ) -> Result<(), tonic::Status>;

    /// Deletes one or more keys.
    /// Returns the number of keys that were actually deleted.
    async fn del(&self, ctx: &RequestContext, keys: Vec<String>) -> Result<i64, tonic::Status>;

    /// Increments the integer value of a key by one.
    /// Returns the new value of the key after incrementing.
    /// Errors if the key does not exist or the value is not an integer.
    async fn incr(&self, ctx: &RequestContext, key: String) -> Result<i64, tonic::Status>;

    /// Increments the integer value of a key by a specified amount.
    /// Returns the new value of the key after incrementing.
    /// Errors if the key does not exist or the value is not an integer.
    async fn incr_by(
        &self,
        ctx: &RequestContext,
        key: String,
        increment: i64,
    ) -> Result<i64, tonic::Status>;

    /// Sets the expiry time for a key to the specified Unix timestamp.
    /// Returns true if the expiry was successfully set, false otherwise.
    async fn expire_at(
        &self,
        ctx: &RequestContext,
        key: String,
        expire_at: u64,
        options: ExpireAtOptions,
    ) -> Result<bool, tonic::Status>;
}

/// A Rust client for the Memstore gRPC service.
#[derive(Clone)]
pub struct MemstoreClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    request_timeout: Option<Duration>,
    client: Arc<Mutex<Option<ProtoMemstoreClient<TracingService>>>>,
}

// Using the builder pattern here so that adding additional
// options in the future doesn't break existing code.
#[derive(Default)]
pub struct SetOptions {
    // Set the key to expire in `ex` seconds from now.
    pub ex: Option<i64>,
}

impl SetOptions {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn with_expiry(mut self, seconds: i64) -> Self {
        self.ex = Some(seconds);
        self
    }
}

#[derive(Default)]
pub struct ExpireAtOptions {
    pub nx: bool,
    pub xx: bool,
    pub gt: bool,
    pub lt: bool,
}

impl ExpireAtOptions {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn with_nx(mut self) -> Self {
        self.nx = true;
        self
    }

    pub fn with_xx(mut self) -> Self {
        self.xx = true;
        self
    }

    pub fn with_gt(mut self) -> Self {
        self.gt = true;
        self
    }

    pub fn with_lt(mut self) -> Self {
        self.lt = true;
        self
    }
}

impl MemstoreClientImpl {
    /// Creates a new Memstore client.
    ///
    /// # Arguments
    /// * `endpoint` - The server address (e.g., "localhost:50051").
    /// * `client_tls_config` - Optional TLS configuration for the connection.
    /// * `request_timeout` - Optional timeout for requests.
    pub fn new(
        endpoint: &str,
        client_tls_config: Option<ClientTlsConfig>,
        request_timeout: Option<Duration>,
    ) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config: client_tls_config,
            request_timeout,
            client: Arc::new(Mutex::new(None)),
        }
    }

    /// Get the cached client or create a new one if it doesn't exist.
    async fn get_client(
        &self,
    ) -> Result<ProtoMemstoreClient<TracingService>, tonic::transport::Error> {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            // Tonic clients are thread-safe and can be cloned freely.
            Some(c) => Ok(c.clone()),
            None => {
                let channel = create_channel(
                    self.endpoint.to_string(),
                    self.request_timeout,
                    &self.tls_config,
                )
                .await?;
                let client = ProtoMemstoreClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
        }
    }
}

#[async_trait]
impl MemstoreClient for MemstoreClientImpl {
    /// Retrieves the value for a key.
    /// Returns `Ok(None)` if the key doesn't exist.
    async fn get(
        &self,
        ctx: &RequestContext,
        key: String,
    ) -> Result<Option<Vec<u8>>, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("memstore client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("memstore not ready")
        })?;

        let proto_req = GetRequest { key };
        let mut request = tonic::Request::new(proto_req);
        ctx.annotate(request.metadata_mut()); // Add request context metadata

        let response = client.get(request).await?;
        Ok(response.into_inner().value) // `value` is `optional bytes` in the .proto
    }

    /// Sets the value for a key with optional configuration.
    async fn set(
        &self,
        ctx: &RequestContext,
        key: String,
        value: Vec<u8>,
        options: SetOptions,
    ) -> Result<(), tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("memstore client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("memstore not ready")
        })?;

        let proto_req = SetRequest {
            key,
            value,
            ex: options.ex,
        };
        let mut request = tonic::Request::new(proto_req);
        ctx.annotate(request.metadata_mut());

        client.set(request).await?;
        Ok(())
    }

    /// Deletes one or more keys.
    /// Returns the number of keys that were actually deleted.
    async fn del(&self, ctx: &RequestContext, keys: Vec<String>) -> Result<i64, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("memstore client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("memstore not ready")
        })?;

        let proto_req = DelRequest { keys };
        let mut request = tonic::Request::new(proto_req);
        ctx.annotate(request.metadata_mut());

        let response = client.del(request).await?;
        Ok(response.into_inner().num_deleted)
    }

    /// Increments the integer value of a key by one.
    /// Returns the new value of the key after incrementing.
    /// Errors if the key does not exist or the value is not an integer.
    async fn incr(&self, ctx: &RequestContext, key: String) -> Result<i64, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("memstore client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("memstore not ready")
        })?;

        let proto_req = IncrRequest { key };
        let mut request = tonic::Request::new(proto_req);
        ctx.annotate(request.metadata_mut());

        let response = client.incr(request).await?;
        Ok(response.into_inner().new_value)
    }

    /// Increments the integer value of a key by a specified amount.
    /// Returns the new value of the key after incrementing.
    /// Errors if the key does not exist or the value is not an integer.
    async fn incr_by(
        &self,
        ctx: &RequestContext,
        key: String,
        increment: i64,
    ) -> Result<i64, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("memstore client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("memstore not ready")
        })?;

        let proto_req = IncrByRequest { key, increment };
        let mut request = tonic::Request::new(proto_req);
        ctx.annotate(request.metadata_mut());

        let response = client.incr_by(request).await?;
        Ok(response.into_inner().new_value)
    }

    /// Sets the expiry time for a key to the specified Unix timestamp.
    /// Returns true if the expiry was successfully set, false otherwise.
    async fn expire_at(
        &self,
        ctx: &RequestContext,
        key: String,
        expire_at: u64,
        options: ExpireAtOptions,
    ) -> Result<bool, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("memstore client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("memstore not ready")
        })?;

        let proto_req = ExpireAtRequest {
            key,
            expire_at,
            nx: options.nx,
            xx: options.xx,
            gt: options.gt,
            lt: options.lt,
        };
        let mut request = tonic::Request::new(proto_req);
        ctx.annotate(request.metadata_mut());

        let response = client.expire_at(request).await?;
        Ok(response.into_inner().success)
    }
}

/// Mock memstore client for testing purposes. Backed by a real hashmap and simulates
/// most methods.
pub struct MockMemstoreClient {
    // Storage for general key-value pairs (for get/set operations)
    storage: Arc<std::sync::Mutex<HashMap<String, Vec<u8>>>>,
    // Storage for integer values (for incr/incr_by operations)
    counters: Arc<std::sync::Mutex<HashMap<String, i64>>>,
}

impl Default for MockMemstoreClient {
    fn default() -> Self {
        Self::new()
    }
}

impl MockMemstoreClient {
    pub fn new() -> Self {
        Self {
            storage: Arc::new(std::sync::Mutex::new(HashMap::new())),
            counters: Arc::new(std::sync::Mutex::new(HashMap::new())),
        }
    }
}

#[async_trait]
impl MemstoreClient for MockMemstoreClient {
    async fn get(
        &self,
        _ctx: &RequestContext,
        key: String,
    ) -> Result<Option<Vec<u8>>, tonic::Status> {
        let storage = self.storage.lock().unwrap();
        Ok(storage.get(&key).cloned())
    }

    async fn set(
        &self,
        _ctx: &RequestContext,
        key: String,
        value: Vec<u8>,
        _options: SetOptions,
    ) -> Result<(), tonic::Status> {
        let mut storage = self.storage.lock().unwrap();
        storage.insert(key, value);
        Ok(())
    }

    async fn del(&self, _ctx: &RequestContext, keys: Vec<String>) -> Result<i64, tonic::Status> {
        let mut storage = self.storage.lock().unwrap();
        let mut counters = self.counters.lock().unwrap();
        let mut deleted_count = 0;

        for key in keys {
            // Remove from both storage maps and count successful deletions
            let removed_from_storage = storage.remove(&key).is_some();
            let removed_from_counters = counters.remove(&key).is_some();

            if removed_from_storage || removed_from_counters {
                deleted_count += 1;
            }
        }

        Ok(deleted_count)
    }

    async fn incr(&self, _ctx: &RequestContext, key: String) -> Result<i64, tonic::Status> {
        let mut counters = self.counters.lock().unwrap();
        let counter = counters.entry(key).or_insert(0);
        *counter += 1;
        Ok(*counter)
    }

    async fn incr_by(
        &self,
        _ctx: &RequestContext,
        key: String,
        increment: i64,
    ) -> Result<i64, tonic::Status> {
        let mut counters = self.counters.lock().unwrap();
        let counter = counters.entry(key).or_insert(0);
        *counter += increment;
        Ok(*counter)
    }

    // NOTE: expire_at is currently not mocked.
    async fn expire_at(
        &self,
        _ctx: &RequestContext,
        _key: String,
        _expire_at: u64,
        _options: ExpireAtOptions,
    ) -> Result<bool, tonic::Status> {
        Ok(true)
    }
}
