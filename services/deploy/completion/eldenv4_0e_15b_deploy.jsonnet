// Elden 16B model
//
// Elden:
// - combining dense line chunk and signature retrieval
//
// v4:
// - add customized unit test data
// - batch size of 2K and trained on 10M samples gains better overall quality
// c:
// - Use starethanol_smart retrieval
// e:
// - use prism elden v4 filter
//
// bazel run -c opt //services/deploy/completion:eldenv4_0e_15b_kubecfg
// bazel run -c opt //services/deploy:methanol_0416_4_kubecfg
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local completionLib = import 'services/deploy/completion/lib.jsonnet';
local signatureEmbedderConfig = import 'services/deploy/methanol_0416.4_config.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local lineEmbedderConfig = import 'services/deploy/starethanol_smart_config.jsonnet';
function(env, namespace, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local modelConfig = completionLib.base.default + {
    name: 'eldenv4-0e-15b',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/dxy/15B-8K-baseline-bs2ks5k-ffw-fp8',  // pragma: allowlist secret
    checkpoint_sha256: '6a764179e987858014648ffa5f0005ab40501672d141346270496c33c83e361b',  // pragma: allowlist secret
    inference: completionLib.inference.elden_stateless,
    model_arch: completionLib.model_arch.starcoder2,
    post_processing: completionLib.post_processing.prism_eldenv4,
  };

  local inferenceHostConfig = completionLib.inferenceHost(env=env, cloud=cloud).evals;
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelConfig=modelConfig,
    inferTensorPara=inferenceHostConfig.inferTensorPara,
    inferGpu=inferenceHostConfig.inferGpu,
    mtls=mtls,
    replicas=inferenceHostConfig.replicas,
    attentionType=inferenceHostConfig.attentionType,
    allReduceImplementation=inferenceHostConfig.allReduceImplementation,
  );
  local completion() = modelDeployment.completionHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalCollectorConfig={
      enabled: true,
      modified_chunks_filter_enabled: true,
    },
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(lineEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='line'),
      modelDeployment.denseRetrievalConfig(signatureEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='sig'),
      modelDeployment.recencyRetrievalConfig(
        env=env,
        namespace=namespace,
        namespace_config=namespace_config
      ),
    ],
    inferenceMtls=mtls,
    skip_token_str='<|skip|>',
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + completion()
  else if filter == 'inference' then
    inference()
  else if filter == 'completion' then
    completion()
  else
    error ('unknown filter: %s' % filter)
