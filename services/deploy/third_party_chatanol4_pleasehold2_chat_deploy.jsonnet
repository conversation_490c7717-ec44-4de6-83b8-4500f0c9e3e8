/**
  Defines chat models that use a Router model to drive retrieval instead of standard multi-retriever
 */
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local embedderConfig = (import 'services/deploy/chatanol-qwen-v1-1_fp8_config.jsonnet') + {
  modelConfig+: {
    max_retrieval_results: 128,
  },
};
local routerConfig = import 'services/deploy/pleasehold_14b_v2_config.jsonnet';
local postprocessConfig = import 'services/deploy/sentry_v1_config.jsonnet';
local prodThirdPartyModels = import 'services/deploy/third_party_chat_models_prod.jsonnet';
local stagingThirdPartyModels = import 'services/deploy/third_party_chat_models_staging.jsonnet';
local prodAgentModels = import 'services/deploy/third_party_agent_models_prod.jsonnet';
local stagingAgentModels = import 'services/deploy/third_party_agent_models_staging.jsonnet';

function(env, namespace, namespace_config, cloud, namespace_config)
  local name = 'third-party-chatanol4-pleasehold2';
  local chatName = '%s-chat' % name;
  local modelConfig = {
    name: name,
    model_type: 'CHAT',
  };
  // Import models and add suffix to each key
  local prodMultiConfig = {
    [key + '-c4-p2-chat']: prodThirdPartyModels[key]
    for key in std.objectFields(prodThirdPartyModels)
  } + {
    [key + '-c4-p2-agent']: prodAgentModels.add_suffix[key]
    for key in std.objectFields(prodAgentModels.add_suffix)
  } + prodAgentModels.no_suffix;
  local stagingMultiConfig = {
    [key + '-c4-p2-chat']: stagingThirdPartyModels[key]
    for key in std.objectFields(stagingThirdPartyModels)
  } + {
    [key + '-c4-p2-agent']: stagingAgentModels.add_suffix[key]
    for key in std.objectFields(stagingAgentModels.add_suffix)
  } + stagingAgentModels.no_suffix;
  local thirdPartyInferenceMultiConfig = (if env == 'PROD' then {} else stagingMultiConfig) + prodMultiConfig;
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  modelDeployment.chatDeploymentHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=chatName,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalConfigs=[
      modelDeployment.routerConfig(
        routerConfig,
        cloud,
        env,
        namespace,
        namespace_config,
        code_retriever_config=modelDeployment.denseRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
        user_guided_retriever_config=modelDeployment.userGuidedRetrievalConfig(env, namespace, namespace_config),
        docset_retriever_config=modelDeployment.docsetRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
      ),
    ],
    inferenceMtls=mtls,
    thirdPartyInferenceMultiConfig=thirdPartyInferenceMultiConfig,
    postprocessConfig=modelDeployment.postprocessConfig(
      postprocessConfig,
      cloud,
      env,
      namespace,
      namespace_config,
    ),
    prodReplicas=3,
  )
