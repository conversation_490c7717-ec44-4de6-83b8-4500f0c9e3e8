local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local embedderConfig = (import 'services/deploy/chatanol1-18-hybrid-v3_config.jsonnet') + {
  modelConfig+: {
    max_retrieval_results: 128,
  },
};

function(env, namespace, namespace_config, cloud, namespace_config)
  local name = 'chatanol3-third-party-staging';
  local chatName = '%s-chat' % name;
  local modelConfig = {
    name: name,
    model_type: 'CHAT',
  };
  local thirdPartyInferenceMultiConfig = {
    'deepseek-v3-16k-v1-fireworks-chat': {
      client_type: 'fireworks',
      model_name: 'accounts/fireworks/models/deepseek-v3',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-deepseek-v3-v1',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
        inject_current_file_into_retrievals: true,
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 9,
        commit_message_len: 1024 * 3,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 12,  // 12k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'deepseek-r1-16k-v1-fireworks-chat': {
      client_type: 'fireworks',
      model_name: 'accounts/fireworks/models/deepseek-r1',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-deepseek-r1-v1',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
        inject_current_file_into_retrievals: true,
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 9,
        commit_message_len: 1024 * 3,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 12,  // 12k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'deepseek-r1-16k-v2-fireworks-chat': {
      client_type: 'fireworks',
      model_name: 'accounts/fireworks/models/deepseek-r1',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-deepseek-r1-v2',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
        inject_current_file_into_retrievals: true,
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 9,
        commit_message_len: 1024 * 3,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 12,  // 12k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'qwq-32b-v1-fireworks-chat': {
      client_type: 'fireworks',
      model_name: 'accounts/fireworks/models/qwq-32b',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-qwq-v1',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
        inject_current_file_into_retrievals: true,
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 9,
        commit_message_len: 1024 * 3,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 12,  // 12k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'qwq-32b-v2-fireworks-chat': {
      client_type: 'fireworks',
      model_name: 'accounts/fireworks/models/qwq-32b',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-qwq-v2',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
        inject_current_file_into_retrievals: true,
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 9,
        commit_message_len: 1024 * 3,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 12,  // 12k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
  };
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  modelDeployment.chatDeploymentHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=chatName,
    modelPriority=0,  // third party chat models are never the default
    modelConfig=modelConfig,
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
      modelDeployment.userGuidedRetrievalConfig(env, namespace, namespace_config),
      modelDeployment.docsetRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
    ],
    inferenceMtls=mtls,
    thirdPartyInferenceMultiConfig=thirdPartyInferenceMultiConfig,
  )
