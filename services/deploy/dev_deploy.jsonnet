// list of services in dev_deploy
// add any new targets here that are useful for development.
//
// The exception is targets that require two or more GPUs
// or one or more H100 GPUs.
//
// Do not set a default to true if it requires a GPU
// Set a default to true if the basic setup is not workable
// without it, e.g. almost everybody will need an api_proxy.
//
// Set delete_default to false if and only if deleting
// the target takes a long time, e.g. if it create ingresses
local services = [
  {
    name: 'default',
    default: true,
    dependencies: [
      'api_proxy',
      'auth_query',
      'bigtable_proxy',
      'content_manager',
      'embeddings_search_cpu',
      'fake_feature_flags',
      'memstore',
      'request_insight',
      'grpc_debug',
      'support',
      'shard_namespace_base',
      'tenant_config',
      'tenant_watcher',
      'token_exchange',
      'working_set',
      'checkpoint_indexer',
    ],
  },
  {
    name: 'completion',
    dependencies: [
      'eldenv3_3b',
      'methanol_0416_4',
      'starethanol6_16_1_proj512',
    ],
  },
  {
    name: 'edit',
    dependencies: [
      'droid_1B_BF16_v1_edit',
    ],
  },
  {
    name: 'instruction',
    dependencies: [
      'claude_instruction_v3_edit',
      'starethanol_smart',
      'third_party_arbiter',  // Required for load-balanced models
    ],
  },
  {
    name: 'chat',
    dependencies: [
      'third_party_chatanol4_pleasehold2_chat',
      'chatanol-qwen-v1-1',
      'docset',
      'pleasehold_14b_v2',  // Uses H100, not strictly required
      'sentry_v1',  // Uses H100, Not strictly required
      'third_party_arbiter',  // Required for load-balanced models
    ],
  },
  {
    // bazel run //services/deploy:dev_deploy -- --services default nextedit --operation Apply
    name: 'nextedit',
    dependencies: [
      'raven_edit_v6_15b',
      'raven_location_v2',
      'raven_retriever_v1',
    ],
  },
  {
    // bazel run //services/deploy:dev_deploy -- --services default nexteditfp16 --operation Apply
    name: 'nexteditfp16',
    dependencies: [
      'raven_edit_fp16_15b',
      'raven_location_v2',
      'raven_retriever_v1',
    ],
  },
  {
    name: 'agents',
    targets: [
      '//services/agents/server:kubecfg',
    ],
    dependencies: [
      // Retriever for retrieval-agent
      'chatanol-qwen-v1-1',
      'pleasehold_14b_v2',
      // Chat (the agent models are deployed in this host's config)
      'third_party_chatanol4_pleasehold2_chat',
      // Large (2xH100) model for now-- TODO replace with mini or 3rd party model and confirm it's adequate
      // As IDE agent no longer uses edit sub-agent by default, don't deploy this
      // 'forger_smart_paste_v2_qwen_8b_32k_edit',
      'settings',
    ],
  },
  {
    name: 'agent-integrations',
    dependencies: [
      'settings',
      'github_processor',
      'linear',
      'notion',
      'atlassian',
      'supabase',
    ],
  },
  {
    name: 'slackbot',
    dependencies: [
      'slack_bot_webhook',
      'slack_bot_processor',
      'settings',
      'github_webhook',
      'github_processor',
      'github_state',
      'glean',
    ],
  },
  {
    name: 'support_v2',
    delete_default: false,
    targets: [
      '//services/support/remix:kubecfg',
    ],
    dependencies: [
      'request_insight_support_database',
      'request_insight_support_database_exporter',
      'request_insight_central',
      'tenant_watcher',
      'token_exchange',
    ],
  },
  {
    name: 'support_central',
    targets: [
      '//services/support_central:kubecfg',
    ],
    dependencies: [
      'request_insight_central',
      'tenant_watcher',
      'token_exchange',
    ],
  },
  {
    name: 'customer_ui',
    delete_default: false,
    has_ingress: true,
    targets: [
      '//services/customer/frontend:kubecfg',
    ],
    dependencies: [
      'auth',
      'request_insight_core',
      'request_insight_analytics_dataset',
      'request_insight_analytics',
      'tenant_config',
      'tenant_watcher',
      'token_exchange',
    ],
  },
  {
    name: 'analytics_export',
    dependencies: [
      'request_insight_analytics_dataset',
      'request_insight_bigquery_exporter',
    ],
  },
  {
    // This is copied from //services/test:test_kubecfg. It is basically the
    // same, but will follow the dev_deploy pattern of skipping deletion of
    // ingresses, unlike the default e2e test behavior.
    name: 'e2e_test',
    dependencies: [
      'default',
      'third_party_chatanol4_pleasehold2_chat',
      'chatanol-qwen-v1-1',
      'pleasehold_14b_v2',
      'sentry_v1',
      'docset',
      'droid_1B_BF16_v1_edit',
      'fake_feature_flags',
      'eldenv3_3b',
      'starethanol6_16_1_proj512',
      'methanol_0416_4',
    ],
  },
  {
    name: 'feedback_slackbot',
    dependencies: [
      'request_insight_feedback_exporter',
      'devtools_slackbot_server',
    ],
  },
  {
    name: 'api_proxy',
    delete_default: false,
    has_ingress: true,
    targets: ['//services/api_proxy/server:kubecfg'],
  },
  {
    name: 'api_proxy_health_check',
    targets: ['//services/api_proxy/health_check:kubecfg'],
  },
  {
    name: 'auth',
    targets: ['//services/auth/central/server:kubecfg'],
  },
  {
    name: 'auth_query',
    targets: [
      '//services/auth/query/server:kubecfg',
    ],
  },
  {
    name: 'content_manager',
    targets: [
      '//services/content_manager/server:kubecfg',
    ],
  },
  {
    name: 'bigtable_proxy',
    targets: [
      '//services/bigtable_proxy/server:kubecfg',
      '//services/bigtable_proxy/server:kubecfg_central',
    ],
  },
  {
    name: 'fake_feature_flags',
    targets: [
      '//services/test/fake_feature_flags:kubecfg',
    ],
  },
  {
    name: 'memstore',
    targets: [
      '//services/memstore/server:kubecfg',
    ],
  },
  {
    name: 'share',
    targets: [
      '//services/share/server:kubecfg',
    ],
  },
  {
    name: 'tenant_watcher',
    targets: [
      '//services/tenant_watcher/server:kubecfg',
    ],
  },
  {
    name: 'tenant_config',
    targets: [
      '//services/deploy:tenant_config_kubecfg',
    ],
  },
  {
    name: 'token_exchange',
    targets: [
      '//services/token_exchange/server:kubecfg',
    ],
  },
  {
    name: 'embeddings_search_cpu',
    targets: [
      '//services/embeddings_search_host/cpu_server:kubecfg',
    ],
  },
  {
    name: 'embeddings_search_scann',
    targets: [
      '//services/embeddings_search_host/shadow_proxy_server:kubecfg',
      '//services/embeddings_search_host/scann_server:kubecfg',
    ],
  },
  {
    name: 'request_insight',
    dependencies: [
      'request_insight_core',
      'request_insight_central',
      'request_insight_support_database',
      'request_insight_support_database_exporter',
      'request_insight_gcs_proxy',
    ],
  },
  {
    // This is the old "core" RI, with the pub/sub topic only.
    name: 'request_insight_core',
    targets: [
      '//services/request_insight:core_kubecfg',
    ],
  },
  {
    name: 'request_insight_central',
    targets: [
      '//services/request_insight/central/server:kubecfg',
    ],
  },
  {
    name: 'request_insight_gcs_proxy',
    targets: [
      '//services/gcs_proxy/server/main:kubecfg',
    ],
  },
  {
    name: 'request_insight_bigquery_exporter',
    targets: [
      '//services/request_insight/bigquery_exporter:kubecfg',
    ],
  },
  {
    name: 'request_insight_bigquery_full_exporter',
    targets: [
      '//services/request_insight/bigquery_full_exporter:kubecfg',
    ],
  },
  {
    name: 'request_insight_blob_exporter',
    targets: [
      '//services/request_insight/blob_exporter:kubecfg',
    ],
  },
  {
    name: 'request_insight_blob_export_gcs_buckets',
    delete_default: false,
    targets: [
      '//services/request_insight/blob_exporter:blob_exporter_central_bucket',
    ],
  },
  {
    name: 'request_insight_support_database',
    delete_default: false,
    targets: [
      '//services/request_insight/support_database/search_dataset:kubecfg',
      '//services/request_insight/support_database/event_buckets:kubecfg',
    ],
  },
  {
    name: 'request_insight_support_database_exporter',
    targets: [
      '//services/request_insight/support_database/exporter:kubecfg',
    ],
  },
  {
    name: 'request_insight_events_gcs_bucket',
    delete_default: false,
    targets: [
      '//services/request_insight/support_database/event_buckets:kubecfg',
    ],
  },
  {
    name: 'request_insight_analytics',
    targets: [
      '//services/request_insight/analytics:kubecfg',
    ],
  },
  {
    name: 'request_insight_analytics_dataset',
    delete_default: false,
    targets: [
      '//services/request_insight/analytics_dataset:kubecfg',
    ],
  },
  {
    name: 'request_insight_feedback_exporter',
    targets: [
      '//services/request_insight/feedback_slackbot:kubecfg',
    ],
  },
  {
    name: 'request_insight_find_missing',
    targets: [
      '//services/request_insight/find_missing:kubecfg',
    ],
  },
  {
    name: 'devtools_slackbot_server',
    targets: [
      '//tools/bot:kubecfg',
    ],
  },
  {
    name: 'grpc_debug',
    targets: [
      '//services/grpc_debug/server:kubecfg',
    ],
  },
  {
    name: 'settings',
    targets: [
      '//services/settings/server:kubecfg',
    ],
  },
  {
    name: 'support',
    delete_default: false,
    has_ingress: true,
    targets: [
      '//services/support:kubecfg',
    ],
  },
  {
    name: 'shard_namespace_base',
    delete_default: false,
    targets: [
      '//services/deploy:shard_namespace_base_kubecfg',
    ],
  },
  {
    name: 'docset',
    targets: [
      '//services/integrations/docset/server:kubecfg',
    ],
  },
  {
    name: 'slack_bot_webhook',
    delete_default: false,
    has_ingress: true,
    targets: [
      '//services/integrations/slack_bot/webhook:kubecfg',
      '//services/integrations/slack_bot/webhook:kubecfg_pubsub',
    ],
  },
  {
    name: 'slack_bot_processor',
    targets: [
      '//services/integrations/slack_bot/processor/server:kubecfg',
    ],
  },
  {
    name: 'github_webhook',
    delete_default: false,
    has_ingress: true,
    targets: [
      '//services/integrations/github/webhook_listener:kubecfg',
      '//services/integrations/github/webhook_listener:kubecfg_pubsub',
    ],
  },
  {
    name: 'github_processor',
    targets: [
      '//services/integrations/github/processor/server:kubecfg',
    ],
  },
  {
    name: 'github_state',
    targets: [
      '//services/integrations/github/state/server:kubecfg',
    ],
  },
  {
    name: 'raven_edit_v6_15b',
    targets: [
      '//services/deploy/next_edit:raven_edit_v6_15b_kubecfg',
    ],
  },
  {
    name: 'raven_edit_v7_15b',
    targets: [
      '//services/deploy/next_edit:raven_edit_v7_15b_kubecfg',
    ],
  },
  {
    name: 'raven_edit_fp16_15b',
    targets: [
      '//services/deploy/next_edit:raven_edit_fp16_15b_kubecfg',
    ],
  },
  {
    name: 'raven_location_v1',
    targets: [
      '//services/deploy:raven_location_v1_kubecfg',
    ],
  },
  {
    name: 'raven_location_v2',
    targets: [
      '//services/deploy:raven_location_v2_kubecfg',
    ],
  },
  {
    name: 'raven_retriever_v1',
    targets: [
      '//services/deploy:raven_retriever_v1_kubecfg',
    ],
  },
  {
    name: 'eldenv3_3b',
    targets: [
      '//services/deploy/completion:eldenv3_3b_kubecfg',
    ],
  },
  {
    name: 'starethanol6_16_1_proj512',
    targets: [
      '//services/deploy:starethanol6_16_1_proj512_kubecfg',
    ],
  },
  {
    name: 'starethanol6_16_1_proj512_v4',
    targets: [
      '//services/deploy:starethanol6_16_1_proj512_v4_kubecfg',
    ],
  },
  {
    name: 'starethanol_smart',
    targets: [
      '//services/deploy:starethanol_smart_kubecfg',
    ],
  },
  {
    name: 'methanol_0416_4',
    targets: [
      '//services/deploy:methanol_0416_4_kubecfg',
    ],
  },
  {
    name: 'droid_1B_BF16_v1_edit',
    targets: [
      '//services/deploy:droid_1B_BF16_v1_edit_kubecfg',
    ],
  },
  {
    name: 'chatanol1-18-v3',
    targets: [
      '//services/deploy:chatanol1-18-hybrid-v3_kubecfg',
    ],
  },
  {
    name: 'chatanol-qwen-v1-1',
    targets: [
      '//services/deploy:chatanol-qwen-v1-1_kubecfg',
    ],
  },
  {
    name: 'pleasehold_14b_v2',
    targets: [
      '//services/deploy:pleasehold_14b_v2_kubecfg',
    ],
  },
  {
    name: 'sentry_v1',
    targets: [
      '//services/deploy:sentry_v1_kubecfg',
    ],
  },
  {
    name: 'third_party_chatanol4_pleasehold2_chat',
    targets: [
      '//services/deploy:third_party_chatanol4_pleasehold2_chat_kubecfg',
    ],
  },
  {
    name: 'chatanol3_third_party_chat',
    targets: [
      '//services/deploy:chatanol3_third_party_chat_kubecfg',
    ],
  },
  {
    name: 'claude_instruction_v3_edit',
    targets: [
      '//services/deploy:claude_instruction_v3_edit_kubecfg',
    ],
  },
  {
    name: 'forger_smart_paste_sc2_7b_32k_edit',
    targets: [
      '//services/deploy:forger_smart_paste_sc2_7b_32k_edit_kubecfg',
    ],
  },
  {
    name: 'forger_smart_paste_v2_qwen_8b_32k_edit',
    targets: [
      '//services/deploy:forger_smart_paste_v2_qwen_8b_32k_edit_kubecfg',
    ],
  },
  {
    name: 'forger_smart_paste_v2_qwen_14b_32k_edit',
    targets: [
      '//services/deploy:forger_smart_paste_v2_qwen_14b_32k_edit_kubecfg',
    ],
  },
  {
    name: 'forger_v2_qwen_14b_q_32k_edit',
    targets: [
      '//services/deploy:forger_v2_qwen_14b_q_32k_edit_kubecfg',
    ],
  },
  {
    name: 'psc_proxy',
    targets: [
      '//services/psc_proxy:kubecfg',
    ],
  },
  {
    name: 'glean',
    targets: [
      '//services/integrations/glean/server:kubecfg',
    ],
  },
  {
    name: 'linear',
    targets: [
      '//services/integrations/linear/server:kubecfg',
    ],
  },
  {
    name: 'atlassian',
    targets: [
      '//services/integrations/atlassian/server:kubecfg',
    ],
  },
  {
    name: 'notion',
    targets: [
      '//services/integrations/notion/server:kubecfg',
    ],
  },
  {
    name: 'supabase',
    targets: [
      '//services/integrations/supabase/server:kubecfg',
    ],
  },
  {
    name: 'working_set',
    targets: [
      '//services/working_set/server:kubecfg',
    ],
  },
  {
    name: 'remote_agents',
    targets: [
      '//services/remote_agents/server:kubecfg',
    ],
  },
  {
    name: 'remote_agents_all',
    dependencies: [
      'remote_agents',
      'default',
      'chat',
      'agents',  // pulls in required chat model
      'completion',
      'agents',
      'github_processor',  // for github oauth
      'github_webhook',
      'auth',
      'settings',
      'customer_ui',
      'request_insight',  // for the beachhead log viewer
      'memstore',  // for pub/sub
      'nextedit',
    ],
  },
  {
    name: 'checkpoint_indexer',
    targets: [
      '//services/checkpoint_indexer/server:kubecfg',
    ],
  },
  {
    name: 'commit_indexer',
    targets: [
      '//services/deploy:commit_indexer_kubecfg',
    ],
  },
  {
    name: 'third_party_arbiter',
    targets: [
      '//services/third_party_arbiter/server:kubecfg',
    ],
  },
];

// Assert that all names are unique
local names = std.map(function(s) s.name, services);
assert std.length(std.set(names)) == std.length(services) : 'service names must be unique';
// Assert that all dependencies are valid
local dependencies = std.flatMap(function(s) std.get(s, 'dependencies', []), services);
local missing_dependencies = std.filter(function(s) !std.member(names, s), dependencies);
assert std.length(missing_dependencies) == 0 : 'missing dependencies: %s' % std.toString(missing_dependencies);

services
