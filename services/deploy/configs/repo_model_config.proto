syntax = "proto3";
package augment.repo_config;

enum ModelType {
  // the model is used for inference
  INFERENCE = 0;

  // the model is only used to calculate embeddings
  EMBEDDING = 1;
}

// supported model architectures
enum ModelArchType {
  // codegen like model architecture
  CODEGEN = 0;
  // starcoder like model architecture
  STARCODER = 1;
}

message ModelArchConfig {
  ModelArchType arch_type = 1;

  // number of layers
  uint32 num_layers = 2;

  // size of the vocab
  uint32 vocab_size = 3;

  // dimension of the embeddings layer
  uint32 emb_dim = 4;

  // number of heads
  uint32 num_heads = 5;

  // dimension per head
  uint32 head_dim = 6;

  // rotary embedding percentage
  float rotary_pct = 7;

  // norm epsilon
  float norm_eps = 8;
}

message InferenceModelConfig {
  // name of the tokenizer to use.
  // the tokenizer needs to match the tokenizer used to train/fine-tune the model
  string tokenizer_name = 1;

  reserved 2;

  // name of the prompt formatter to use
  // the prompt formatter needs to match the prompts used to train/fine-tune the model
  string prompt_formatter_name = 3;

  // languages supported for the given model
  repeated string languages = 4;

  // The maximal number of tokens supported by the model.
  //
  // The number of output tokens needs to be subtracted from this number when forming
  // the prompt.
  uint32 max_context_length = 5;

  repeated string extra_stop_tokens = 9;
}

message EmbeddingModelConfig {
  // name of the tokenizer to use.
  string tokenizer_name = 1;

  // name of the prompt formatter to use for queries
  // the prompt formatter needs to match the prompts used to train/fine-tune the model
  string query_prompt_formatter_name = 2;

  // name of the prompt formatter to use for keys
  // the prompt formatter needs to match the prompts used to train/fine-tune the model
  string key_prompt_formatter_name = 3;
}

// Next ID: 33
message RepoModelConfig {
  string name = 1;

  reserved 2, 3;

  // path to a deepspeed checkpoint directory
  // the directory should have a "mp_rank_00_model_states.pt" file.
  string efs_deepspeed_checkpoint_path = 16;
  string checkpoint_sha256 = 32;

  reserved 5, 6;

  // path to the deepspeed checkpoint within the repo via Bazel dependencies
  string repo_deepspeed_checkpoint_path = 30;

  reserved 4, 7, 8, 9, 10, 11;

  // the type of the model.
  ModelType model_type = 14;

  reserved 15;

  // model architecture specification
  ModelArchConfig model_arch = 17;

  oneof model_config {
    // needs to be set if the model_type is INFERENCE
    InferenceModelConfig inference = 28;

    // needs to be set if the model_type is EMBEDDING
    EmbeddingModelConfig embedding = 29;
  }

  repeated int32 round_sizes = 31;
}
