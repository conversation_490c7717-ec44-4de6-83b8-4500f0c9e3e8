// a rogue 16B model with stateless caching
//
// Improve latency by arranging prompt to achieve more cache hits
//
// Model Card: https://www.notion.so/Q4-2023-Rogue-SL-models-f7f4dffab2f24bdfa769d43553068fb8
function(env='PROD')
  local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);
  {
    name: 'roguesl-16B-ETH6-FP8',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/roguesl/16b_eth61m_depause_prefretsuf_npref100_olap0_quant20/checkpoint_fp8.pth_v2',  // pragma: allowlist secret
    checkpoint_sha256: '47b0b1518d5aba70a4f72a1e05e5d2547ccbc293baad2867b58fe49c4578fcc7',  // pragma: allowlist secret
    model_type: 'INFERENCE',
    inference: {
      tokenizer_name: 'rogue',
      prompt_formatter_name: 'rogue_sl',
      apportionment_config: {
        max_content_len: 4 * 1024,
        input_fraction: 0.5,
        prefix_fraction: 0.75,
        max_path_tokens: 50,
      },
      prompt_formatter_config: {
        stateless_caching_config: {
          nearby_prefix_token_len: 100,
          quantize_token_len: 20,
          quantize_char_len: 200,
        },
        component_order: ['path', 'prefix', 'suffix', 'retrieval', 'nearby_prefix'],
      },
      languages: lang_presets.starcoder,
      max_context_length: 4 * 1024,
      extra_stop_tokens: ['<|skip|>', '<|pause|>'],
    },
    model_arch: {
      arch_type: 'STARCODER_FP8',
      num_layers: 40,
      vocab_size: 51200,
      emb_dim: 6144,
      num_heads: 48,
      head_dim: 128,
      norm_eps: 1e-5,
    },
    // See https://www.notion.so/Logs-V2-training-small-neural-model-for-speculative-decoding-0dc1005e9af6416b9518bb9c96ce5132
    // for the speculation model.
    speculationModels: [
      {
        model_name: 'sd-roguesl-l6d2048-ETH6-FP8',
        model_arch: {
          arch_type: 'STARCODER_FP8',
          num_layers: 6,
          vocab_size: 51200,
          emb_dim: 2048,
          num_heads: 16,
          head_dim: 128,
          norm_eps: 1e-5,
        },
        weights_path: '/mnt/efs/augment/checkpoints/roguesl/starcoder-l6d2048-longtraining-roguesl/checkpoint_fp8.pth',
        round_sizes: [32, 64, 128, 256, 512, 1024, 2048],
        weights_sha256: 'not needed yet because we are still loading the fp8 format',
      },
    ],
  }
