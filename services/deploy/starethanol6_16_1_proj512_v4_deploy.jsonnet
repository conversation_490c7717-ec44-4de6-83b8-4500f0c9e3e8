local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local embedderConfig = import 'services/deploy/starethanol6_16_1_proj512_v4_config.jsonnet';
function(env, namespace, cloud, namespace_config, filter=null)
  local embedder() = modelDeployment.embedder(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    embedderConfig=embedderConfig,
    replicas=if env == 'PROD' && cloud == 'GCP_US_CENTRAL1_GSC_PROD' then 16 else null
  );
  local indexer() = modelDeployment.embeddingIndexer(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    embedderConfig=embedderConfig,
    chunker='transform_line_level',
    memoryOverride='2Gi',
  );
  local key() = modelDeployment.transformationKey(
    namespace=namespace,
    transformationKeyName=embedderConfig.transformationKey,
    appName=embedderConfig.modelConfig.name,
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace) : 'Indexer and embedder togehter is only supported in DEV';
    embedder() + indexer() + key()
  else if filter == 'embedder' then
    embedder()
  else if filter == 'indexer' then
    indexer() + key()
  else
    error ('unknown filter: %s' % filter)
