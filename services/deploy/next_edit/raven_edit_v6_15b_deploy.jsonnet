/** Deployment configuration for the Next Edit ("Raven") Generation model and host.
Changes since v6:
- Model is trained on improved synthetic dataset that encourages the model to be more
  proactive and less "lazy".
  - TODO: Assess potential increased hallucinations.
- Model is trained to pause earlier (around 500 characters), which should significantly
  reduce the chance to output truncated suggestions.
*/


local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local lib = import 'services/deploy/next_edit/lib.jsonnet';
local ravenLocationConfig = import 'services/deploy/raven_location_v2_config.jsonnet';
local generationRetrievalConfig = import 'services/deploy/raven_retriever_v1_fp8_config.jsonnet';

function(env, namespace, cloud, namespace_config, filter=null)
  local speculationModels = if env == 'PROD' then [] else [
    {
      model_name: 'raven-sd',
      model_arch: {
        arch_type: 'LLAMA_FP8',
        emb_dim: 2048,
        num_layers: 6,
        num_heads: 2,
        head_dim: 128,
        num_queries_per_head: 8,
        attn_split_head_mode: 'KV_HEADS',
        rotary_scaling_factor: 1.0,
        rotary_pct: 1.0,
        vocab_size: 51200,
        mlp_dim_divisible_by: 128,
        ffn_dim_multiplier: 1.5,
        norm_eps: 1e-05,
        max_position_embeddings: 16384,
        unscaled_max_position_embeddings: 16384,
      },
      weights_path: '/mnt/efs/augment/checkpoints/next-edit-spec-decoding/llama3_350m_4K_fb-S27-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k-ebe4659b-7bc3-4366-8b3a-a0fdd58f3d6e--FastForward-fp8',  // pragma: allowlist secret
      weights_sha256: '1b2f46238e87ddc9d2d5fc330fad7ce1daa9d9811bf0dfa3d28d90db4d14b291',  // pragma: allowlist secret
      round_sizes: [64, 128, 256, 512, 1024, 2048],
    },
  ];
  local modelConfig =
    lib.base(env=env).default + {
      name: 'raven-edit-v6-15b',
      efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/next-edit-gen/S28_wip0.6_small1000-R4_ethanol-P21_star2_seq12k_pause500_out600-pr_grouped_10k-starcoder2_15b-ffw-fp8',  // pragma: allowlist secret
      checkpoint_sha256: '34158f920485043b9f8df1b9c1cfcb18db354179059812018ee2a078b545a680',
      model_arch: lib.model_arch.starcoder2_15b + {
        attn_split_head_mode: 'NO_SPLIT',  // required for use_dynamic_sequence_parallel
      },
      speculationModels: speculationModels,
      use_dynamic_sequence_parallel: true,
    };
  local rerankerModelConfig =
    local starcoder2_3b = (import 'services/deploy/configs/starcoder2-3B-fp8-base.jsonnet')(env=env);
    {
      name: 'raven-reranker-v6-3b',
      efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/next-edit-gen/S28_wip0.6_small1000-R4_ethanol-P21_star2_seq12k_pause500_out600-pr_grouped_10k-starcoder2_3b-ffw-fp8',  // pragma: allowlist secret
      checkpoint_sha256: 'b16d0e3037b3a7cf9103da46806bef95e927a135a3bb3a46ca9543ad53370533',
      model_type: 'NEXT_EDIT',
      inference: {
        max_context_length: 6 * 1024,
      },
      cache_pool_size: if env == 'DEV' then 2 else 16,  // FIXME
      model_arch: starcoder2_3b.model_arch,
      round_sizes: [128, 256, 512, 1024, 2048],
      use_sequence_parallel: if env == 'DEV' then false else true,
      use_dynamic_sequence_parallel: false,  // TODO(carl): update
    };
  local chatModelConfig =
    local llama8b = (import 'services/deploy/configs/llama3-1-8b-instruct-fp8.jsonnet')(env);
    llama8b + {
      name: 'raven-describe-v2-8b',
      efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/next-edit-description/llama3i-8b-gemini-distilled-ffwd-fp8',  // pragma: allowlist secret
      checkpoint_sha256: 'a38cb982d4d454565afeff28ad44de96fff5539cedc92fe56d02d9f1a5029187',
      max_context_length: 1024,
      // A relatively short cap on output length.
      max_output_length: 32,
    };
  local handlerConfig = {
    edit_generation_config: {
      ranker_gen_prompt_formatter_config: {
        diff_context_lines: 6,
        max_prompt_tokens: 5500,
        section_budgets: {
          suffix_tks: 1000,
          prefix_tks: 2000,
          diff_tks: 2000,
          filename_tks: 100,
          instruction_tks: 200,
          retrieval_tks: 0,
        },
      },
      max_output_length: 160,
    },
    reranker_filter_threshold: 0.3,
    change_probability_min: null,
    validate_blob_state: false,
    max_filter_score: {
      FOREGROUND: 0.6,  // Using the same threshold as BACKGROUND mode
      BACKGROUND: 0.6,
      FORCED: 1.0,
    },
  };

  local objects = lib.objects(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelConfig=modelConfig,
    generationRetrievalConfigs=[modelDeployment.denseRetrievalConfig(generationRetrievalConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='content')],
    locationRetrievalConfigs=[modelDeployment.denseRetrievalConfig(ravenLocationConfig.queryModelConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='location')],
    handlerConfig=handlerConfig,
    lowQualityFilterConfig={
      checkpoint_path: 'services/next_edit_host/server/prism_models/raven_v6_prism_2.json',
    },
    chatModelConfig=chatModelConfig,
    rerankerModelConfig=rerankerModelConfig,
  );

  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    objects.generationInference + objects.rerankerInference + objects.chatInference + objects.nextEdit
  else if filter == 'inference' then
    objects.generationInference + objects.rerankerInference + objects.chatInference
  else if filter == 'next_edit' then
    objects.nextEdit
  else
    error ('unknown filter: %s' % filter)
