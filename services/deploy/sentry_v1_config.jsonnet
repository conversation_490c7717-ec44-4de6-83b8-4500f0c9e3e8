local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

local name = 'sentry-v1';
local modelConfig = {
  name: name,
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/sentry/v3_ff_fp8',
  checkpoint_sha256: '****************************************************************',  // pragma: allowlist secret
  model_type: 'POSTPROCESS',
  model_arch: {
    arch_type: 'QWEN25CODER_FP8',
    emb_dim: 3584,
    num_layers: 28,
    num_heads: 4,
    num_queries_per_head: 7,
    head_dim: 128,
    attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
    rotary_theta: 1000000.0,
    rotary_scaling_factor: 1.0,
    rotary_pct: 1.0,
    vocab_size: 152064,
    mlp_dim_divisible_by: 512,
    ffn_dim_multiplier: 1.95,
    norm_eps: 1e-06,
    max_position_embeddings: 16384,
  },
  inference: {
    tokenizer_name: 'qwen25coder',
    prompt_formatter_name: 'sentry_v1',
    token_apportionment: {
      max_prompt_len: 1024 * 8,
    },
    max_context_length: 16384,  // Total length of prompt and generation
    max_output_length: 4096,  // Max number of generated tokens
  },
  timeout_s: 5,
  // max_seq_length: 4096,
  round_sizes: [32, 64, 128, 256, 512, 1024, 2048, 4096],
};
modelConfig
