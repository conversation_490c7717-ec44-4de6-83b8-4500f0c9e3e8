load("@python_pip//:requirements.bzl", "requirement")
load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "jsonnet_library",
    "jsonnet_to_json",
)
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library", "kubecfg_multi")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "pytest_test")

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:cmk-lib",
        "//deploy/common:eng-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg(
    name = "shard_namespace_base_kubecfg",
    src = "shard_namespace_base.jsonnet",
    visibility = [
        "//services:__subpackages__",
        "//tools/genie/test:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
    ],
)

kubecfg(
    name = "tenant_config_kubecfg",
    src = "tenant_config.jsonnet",
    # Each tenant object has its own app
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/content_manager/test:__subpackages__",
        "//services/inference_host/test:__subpackages__",
        "//services/test:__subpackages__",
        "//services/token_exchange/test:__subpackages__",
        "//tools/genie/test:__subpackages__",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/tenants:more-prod-tenants-lib",
        "//deploy/tenants:tenants-lib",
    ],
)

py_binary(
    name = "tenant_config",
    srcs = ["tenant_config.py"],
    data = [
        ":tenant_config_kubecfg",
        "//deploy/tenants/namespace_configs:namespace-configs",
        "@bazel_tools//tools/bash/runfiles",
    ],
    deps = [
        "//base/logging:console_logging",
        "//tools/kubecfg:kubecfg_lib",
    ],
)

pytest_test(
    name = "tenant_config_test",
    srcs = ["tenant_config_test.py"],
    deps = [
        ":tenant_config",
        "//base/logging:console_logging",
        "//tools/kubecfg:kubecfg_lib",
    ],
)

kubecfg_library(
    name = "constants",
    srcs = ["constants.jsonnet"],
    visibility = ["//services:__subpackages__"],
)

# library to configure model deployments
kubecfg_library(
    name = "model_deployment_lib",
    srcs = ["model_deployment_lib.jsonnet"],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":constants",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/tenants:namespaces",
        "//services/chat_host/server:kubecfg_lib",
        "//services/completion_host/single_model_server:kubecfg_lib",
        "//services/deploy/configs:config_lib",
        "//services/deploy/model_instance:kubecfg_lib",
        "//services/edit_host/server:kubecfg_lib",
        "//services/embedder_host/deploy:kubecfg_lib",
        "//services/embeddings_indexer:kubecfg_lib",
        "//services/inference_host/server/continuous_batching:kubecfg_lib",
        "//services/next_edit_host/server:kubecfg_lib",
        "//services/reranker/deploy:kubecfg_lib",
    ],
)

kubecfg_library(
    name = "chatanol1-18-hybrid_lib",
    srcs = ["chatanol1-18-hybrid_config.jsonnet"],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "chatanol1-18-hybrid_kubecfg",
    src = "chatanol1-18-hybrid_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol1-18-hybrid_lib",
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_library(
    name = "chatanol1-18-hybrid-v3_lib",
    srcs = ["chatanol1-18-hybrid-v3_config.jsonnet"],
    visibility = [
        "//services/agents/server:__subpackages__",
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "chatanol1-18-hybrid-v3_kubecfg",
    src = "chatanol1-18-hybrid-v3_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol1-18-hybrid-v3_lib",
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_library(
    name = "chatanol-qwen-v1_lib",
    srcs = ["chatanol-qwen-v1_config.jsonnet"],
    visibility = [
        "//services/agents/server:__subpackages__",
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "chatanol-qwen-v1_kubecfg",
    src = "chatanol-qwen-v1_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol-qwen-v1_lib",
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_library(
    name = "chatanol-qwen-v1-1_lib",
    srcs = [
        "chatanol-qwen-v1-1_config.jsonnet",
        "chatanol-qwen-v1-1_fp8_config.jsonnet",
    ],
    visibility = [
        "//services/agents/server:__subpackages__",
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_library(
    name = "commit-chatanol-v1_lib",
    srcs = [
        "commit_indexer_v1_config.jsonnet",
    ],
    visibility = [
        "//services/agents/server:__subpackages__",
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol-qwen-v1-1_lib",
        ":model_deployment_lib",
        "//deploy/common:cloud_info",
    ],
)

kubecfg(
    name = "chatanol-qwen-v1-1_kubecfg",
    src = "chatanol-qwen-v1-1_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol-qwen-v1-1_lib",
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_library(
    name = "starethanol6_16_1_proj512_lib",
    srcs = ["starethanol6_16_1_proj512_config.jsonnet"],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "starethanol6_16_1_proj512_kubecfg",
    src = "starethanol6_16_1_proj512_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_library(
    name = "starethanol6_16_1_proj512_v4_lib",
    srcs = ["starethanol6_16_1_proj512_v4_config.jsonnet"],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "starethanol6_16_1_proj512_v4_kubecfg",
    src = "starethanol6_16_1_proj512_v4_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":starethanol6_16_1_proj512_v4_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_library(
    name = "starethanol_smart_lib",
    srcs = ["starethanol_smart_config.jsonnet"],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "starethanol_smart_kubecfg",
    src = "starethanol_smart_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":starethanol_smart_fp8_lib",
        ":starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_library(
    name = "starethanol_smart_fp8_lib",
    srcs = ["starethanol_smart_fp8_config.jsonnet"],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_library(
    name = "pleasehold_7b_v1_lib",
    srcs = ["pleasehold_7b_v1_config.jsonnet"],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "pleasehold_7b_v1_kubecfg",
    src = "pleasehold_7b_v1_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":pleasehold_7b_v1_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_library(
    name = "pleasehold_14b_v2_lib",
    srcs = ["pleasehold_14b_v2_config.jsonnet"],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "pleasehold_14b_v2_kubecfg",
    src = "pleasehold_14b_v2_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":pleasehold_14b_v2_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_library(
    name = "sentry_v1_lib",
    srcs = ["sentry_v1_config.jsonnet"],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "sentry_v1_kubecfg",
    src = "sentry_v1_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":sentry_v1_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_library(
    name = "methanol_0416_4_lib",
    srcs = ["methanol_0416.4_config.jsonnet"],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "methanol_0416_4_kubecfg",
    src = "methanol_0416.4_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":methanol_0416_4_fp8_lib",
        ":methanol_0416_4_lib",
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_library(
    name = "methanol_0416_4_fp8_lib",
    srcs = ["methanol_0416.4_fp8_config.jsonnet"],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg_multi(
    name = "base_kubecfg",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        ":shard_namespace_base_kubecfg",
        ":tenant_config_kubecfg",
        "//services/api_proxy/server:kubecfg",
        "//services/auth/query/server:kubecfg",
        "//services/bigtable_proxy/server:kubecfg",
        "//services/bigtable_proxy/server:kubecfg_central",
        "//services/content_manager/server:kubecfg",
        "//services/embeddings_search_host/cpu_server:kubecfg",
        "//services/integrations/docset/server:kubecfg",
        "//services/request_insight:core_kubecfg",
        "//services/tenant_watcher/server:kubecfg",
        "//services/test/fake_feature_flags:kubecfg",
        "//services/token_exchange/server:kubecfg",
        "//services/working_set/server:kubecfg",
    ],
)

jsonnet_to_json(
    name = "dev_deploy_json",
    src = "dev_deploy.jsonnet",
    outs = ["dev_deploy.json"],
)

py_binary(
    name = "dev_deploy",
    srcs = ["dev_deploy.py"],
    data = [
        ":dev_deploy_json",
    ],
    deps = [
        "//base/logging:console_logging",
        "//base/python/cloud",
        "//tools/bazel_lib",
        "//tools/kubecfg:kubecfg_lib",
        requirement("InquirerPy"),
    ],
)

kubecfg(
    name = "binks_1B_BF16_chatanol1_18_ug_chat_kubecfg",
    src = "binks_1B_BF16_chatanol1-18_ug_chat_deploy.jsonnet",
    cloud = ["GCP_US_CENTRAL1_DEV"],
    # uses apps for chat and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol1-18-hybrid_lib",
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "chatanol3_third_party_chat_kubecfg",
    src = "chatanol3_third_party_chat_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol1-18-hybrid-v3_lib",
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "chatanol3_third_party_chat_staging_kubecfg",
    src = "chatanol3_third_party_chat_staging_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol1-18-hybrid-v3_lib",
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "chatanol3_third_party_chat_long_context_kubecfg",
    src = "chatanol3_third_party_chat_long_context_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol1-18-hybrid-v3_lib",
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "chatanol-qwen-v1_third_party_chat_kubecfg",
    src = "chatanol-qwen-v1_third_party_chat_deploy.jsonnet",
    # For consistency with inference chat hosts
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol-qwen-v1_lib",
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "chatanol-qwen-v1-1_third_party_chat_kubecfg",
    src = "chatanol-qwen-v1-1_third_party_chat_deploy.jsonnet",
    # For consistency with inference chat hosts
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol-qwen-v1-1_lib",
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "chatanol3_router_third_party_chat_kubecfg",
    src = "chatanol3_router_third_party_chat_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol1-18-hybrid-v3_lib",
        ":model_deployment_lib",
        ":pleasehold_7b_v1_lib",
    ],
)

kubecfg(
    name = "chatanol_q1_router_third_party_chat_kubecfg",
    src = "chatanol_q1_router_third_party_chat_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol-qwen-v1_lib",
        ":model_deployment_lib",
        ":pleasehold_7b_v1_lib",
    ],
)

kubecfg(
    name = "chatanol_q1_1_router_third_party_chat_kubecfg",
    src = "chatanol_q1_1_router_third_party_chat_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol-qwen-v1-1_lib",
        ":model_deployment_lib",
        ":pleasehold_7b_v1_lib",
        ":sentry_v1_lib",
    ],
)

kubecfg(
    name = "chatanol_q1_1_nor_third_party_chat_kubecfg",
    src = "chatanol_q1_1_nor_third_party_chat_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol-qwen-v1-1_lib",
        ":model_deployment_lib",
        ":sentry_v1_lib",
    ],
)

kubecfg(
    name = "chatanol_q1_1_r2_third_party_chat_kubecfg",
    src = "chatanol_q1_1_r2_third_party_chat_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol-qwen-v1-1_lib",
        ":model_deployment_lib",
        ":pleasehold_14b_v2_lib",
        ":sentry_v1_lib",
    ],
)

kubecfg_library(
    name = "third_party_chat_models_prod_lib",
    srcs = ["third_party_chat_models_prod.jsonnet"],
    visibility = ["//services:__subpackages__"],
)

kubecfg_library(
    name = "third_party_chat_models_staging_lib",
    srcs = ["third_party_chat_models_staging.jsonnet"],
    visibility = ["//services:__subpackages__"],
)

kubecfg_library(
    name = "third_party_agent_models_prod_lib",
    srcs = [
        "third_party_agent_models_prod.jsonnet",
        "third_party_agent_models_template.jsonnet",
    ],
    visibility = ["//services:__subpackages__"],
)

kubecfg_library(
    name = "third_party_agent_models_staging_lib",
    srcs = [
        "third_party_agent_models_staging.jsonnet",
        "third_party_agent_models_template.jsonnet",
    ],
    visibility = ["//services:__subpackages__"],
)

kubecfg(
    name = "third_party_chatanol4_pleasehold2_chat_kubecfg",
    src = "third_party_chatanol4_pleasehold2_chat_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol-qwen-v1-1_lib",
        ":model_deployment_lib",
        ":pleasehold_14b_v2_lib",
        ":sentry_v1_lib",
        ":third_party_agent_models_prod_lib",
        ":third_party_agent_models_staging_lib",
        ":third_party_chat_models_prod_lib",
        ":third_party_chat_models_staging_lib",
    ],
)

kubecfg(
    name = "third_party_chatanol4_chat_kubecfg",
    src = "third_party_chatanol4_chat_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol-qwen-v1-1_lib",
        ":model_deployment_lib",
        ":sentry_v1_lib",
        ":third_party_chat_models_prod_lib",
        ":third_party_chat_models_staging_lib",
    ],
)

kubecfg(
    name = "droid_1B_BF16_v1_edit_kubecfg",
    src = "droid_1B_BF16_v1_edit_deploy.jsonnet",
    cloud = ["GCP_US_CENTRAL1_DEV"],
    # uses apps for edit and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "claude_instruction_v2_edit_kubecfg",
    src = "claude_instruction_v2_edit_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "claude_instruction_v3_edit_kubecfg",
    src = "claude_instruction_v3_edit_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "claude_instruction_v4_edit_kubecfg",
    src = "claude_instruction_v4_edit_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "claude_instruction_v2_direct_edit_kubecfg",
    src = "claude_instruction_v2_direct_edit_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "forger_smart_paste_sc2_7b_32k_edit_kubecfg",
    src = "forger_smart_paste_sc2_7b_32k_edit_deploy.jsonnet",
    # uses apps for edit and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "forger_smart_paste_qwen_8b_32k_edit_kubecfg",
    src = "forger_smart_paste_qwen_8b_32k_edit_deploy.jsonnet",
    # uses apps for edit and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "forger_smart_paste_v2_qwen_8b_32k_edit_kubecfg",
    src = "forger_smart_paste_v2_qwen_8b_32k_edit_deploy.jsonnet",
    # uses apps for edit and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "forger_smart_paste_v2_qwen_14b_32k_edit_kubecfg",
    src = "forger_smart_paste_v2_qwen_14b_32k_edit_deploy.jsonnet",
    # uses apps for edit and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "forger_v2_qwen_14b_q_32k_edit_kubecfg",
    src = "forger_v2_qwen_14b_q_32k_edit_deploy.jsonnet",
    # uses apps for edit and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "forger_v3_qwen_14b_inst_fr_32k_edit_kubecfg",
    src = "forger_v3_qwen_14b_inst_fr_32k_edit_deploy.jsonnet",
    # uses apps for edit and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "forger_qwen_udiff_7b_v3_32k_edit_kubecfg",
    src = "forger_qwen_udiff_7b_v3_32k_edit_deploy.jsonnet",
    # uses apps for edit and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

jsonnet_library(
    # keeping for regression testing
    name = "raven_location_v1_lib",
    srcs = [
        "raven_location_v1_config.jsonnet",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":starethanol6_16_1_proj512_lib",
    ],
)

kubecfg(
    # keeping for regression testing
    name = "raven_location_v1_kubecfg",
    src = "raven_location_v1_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":raven_location_v1_lib",
    ],
)

jsonnet_library(
    name = "raven_location_v2_lib",
    srcs = [
        "raven_location_v2_config.jsonnet",
        "raven_location_v2_fp8_config.jsonnet",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
    ],
)

kubecfg(
    name = "raven_location_v2_kubecfg",
    src = "raven_location_v2_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":raven_location_v2_lib",
    ],
)

jsonnet_library(
    name = "raven_retriever_v1_lib",
    srcs = [
        "raven_retriever_v1_config.jsonnet",
        "raven_retriever_v1_fp8_config.jsonnet",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":starethanol6_16_1_proj512_lib",
    ],
)

kubecfg(
    name = "raven_retriever_v1_kubecfg",
    src = "raven_retriever_v1_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":model_deployment_lib",
        ":raven_retriever_v1_lib",
        "//services/deploy/configs:config_lib",
    ],
)

# next edit models - end

kubecfg(
    name = "commit_indexer_kubecfg",
    src = "commit_indexer_v1_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":chatanol-qwen-v1-1_lib",
        ":commit-chatanol-v1_lib",
        ":model_deployment_lib",
        "//services/deploy/configs:config_lib",
    ],
)

jsonnet_library(
    name = "endpoints",
    srcs = [
        "endpoints.jsonnet",
    ],
    visibility = [
        "//services:__subpackages__",
        "//tools:__subpackages__",
        "//tools/genie:__subpackages__",
        "//tools/notion_tenant_sync:__subpackages__",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:grpc-lib",
    ],
)

jsonnet_library(
    name = "tombstones-lib",
    srcs = [
        "tombstones.jsonnet",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        "//deploy/common:lib",
        "//deploy/gcp:monitoring-lib",
        "//deploy/tenants:namespaces",
    ],
)

metadata_test_deps = [
    ":chatanol3_third_party_chat_kubecfg",
    ":chatanol3_third_party_chat_staging_kubecfg",
    ":chatanol3_router_third_party_chat_kubecfg",
    ":chatanol3_third_party_chat_long_context_kubecfg",
    ":chatanol-qwen-v1_third_party_chat_kubecfg",
    ":chatanol-qwen-v1-1_third_party_chat_kubecfg",
    ":chatanol_q1_router_third_party_chat_kubecfg",
    ":chatanol_q1_1_router_third_party_chat_kubecfg",
    ":chatanol_q1_1_nor_third_party_chat_kubecfg",
    ":chatanol_q1_1_r2_third_party_chat_kubecfg",
    ":claude_instruction_v2_edit_kubecfg",
    ":claude_instruction_v3_edit_kubecfg",
    ":claude_instruction_v4_edit_kubecfg",
    ":claude_instruction_v2_direct_edit_kubecfg",
    ":forger_smart_paste_sc2_7b_32k_edit_kubecfg",
    ":forger_smart_paste_v2_qwen_8b_32k_edit_kubecfg",
    ":forger_smart_paste_v2_qwen_14b_32k_edit_kubecfg",
    ":forger_v2_qwen_14b_q_32k_edit_kubecfg",
    ":forger_v3_qwen_14b_inst_fr_32k_edit_kubecfg",
    ":chatanol1-18-hybrid-v3_kubecfg",
    ":chatanol1-18-hybrid_kubecfg",
    ":chatanol-qwen-v1_kubecfg",
    ":chatanol-qwen-v1-1_kubecfg",
    ":pleasehold_7b_v1_kubecfg",
    ":pleasehold_14b_v2_kubecfg",
    ":sentry_v1_kubecfg",
    ":methanol_0416_4_kubecfg",
    ":starethanol_smart_kubecfg",
    ":raven_location_v1_kubecfg",
    ":raven_location_v2_kubecfg",
    ":raven_retriever_v1_kubecfg",
    ":shard_namespace_base_kubecfg",
    ":starethanol6_16_1_proj512_kubecfg",
    ":starethanol6_16_1_proj512_v4_kubecfg",
    ":third_party_chatanol4_chat_kubecfg",
    ":third_party_chatanol4_pleasehold2_chat_kubecfg",
    ":tombstones-lib",
    "//services/deploy/completion:deeprogue_33b_seth6_rec_kubecfg",
    "//services/deploy/completion:eldenv3_15b_kubecfg",
    "//services/deploy/completion:eldenv3_3b_kubecfg",
    "//services/deploy/completion:eldenv4_15b_kubecfg",
    "//services/deploy/completion:eldenv4_0b_15b_kubecfg",
    "//services/deploy/completion:eldenv4_0c_15b_kubecfg",
    "//services/deploy/completion:eldenv4_0d_15b_kubecfg",
    "//services/deploy/completion:eldenv4_0e_15b_kubecfg",
    "//services/deploy/completion:eldenv4_0f_15b_kubecfg",
    "//services/deploy/completion:eldenv4_3_15b_kubecfg",
    "//services/deploy/completion:eldenv4_4a_15b_kubecfg",
    "//services/deploy/completion:eldenv4_4b_15b_kubecfg",
    "//services/deploy/completion:eldenv5_1_15b_kubecfg",
    "//services/deploy/completion:eldenv6_15b_kubecfg",
    "//services/deploy/completion:eldenv6_1_15b_kubecfg",
    "//services/deploy/completion:eldenv7_0_15b_kubecfg",
    "//services/deploy/completion:qweldenv1_14b_kubecfg",
    "//services/deploy/completion:qweldenv1_1_14b_kubecfg",
    "//services/deploy/completion:qweldenv2_14b_kubecfg",
    "//services/deploy/completion:qweldenv2_1_14b_kubecfg",
    "//services/deploy/completion:qweldenv3_14b_kubecfg",
    "//services/deploy/completion:qweldenv3_1_14b_kubecfg",
    "//services/deploy/completion:qweldenv3_2_14b_kubecfg",
    "//services/deploy/completion:roguesl_v2_16b_fp8_seth6_16_1_kubecfg",
    "//services/deploy/completion:roguesl_v3_16b_starethanol6_16_1_proj512_rec_kubecfg",
    "//services/deploy/completion:star2sl_16b_starethanol6_16_1_proj512_rec_kubecfg",
    "//services/deploy/completion:star2v2_15b_kubecfg",
    "//services/deploy/next_edit:raven_edit_v6_15b_kubecfg",
    "//services/deploy/next_edit:raven_edit_v7_15b_kubecfg",
    "//deploy/common:cloud_info",
    "//deploy/common:lib",
    "//deploy/tenants:namespaces",
    ":kubecfg_shared",
]

metadata_test(
    name = "metadata_test",
    timeout = "long",
    src = "METADATA.jsonnet",
    data = [
        ":tenant_config",
    ],
    shard_count = 8,
    deps = metadata_test_deps,
)

# For spot-checking only (see comment in METADATA.jsonnet for suggested usage).
jsonnet_to_json(
    name = "metadata_json",
    src = "METADATA.jsonnet",
    outs = ["METADATA.json"],
    # This shouldn't be directly consumed by any other targets.
    visibility = [],
    deps = [
        ":tombstones-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/tenants:namespaces",
    ],
)
