/**
  Defines third-party chat models configurations used across deployment files
 */

{
  'claude-sonnet-3-5-16k-v5-2': {
    client_type: 'anthropic_vertexai',
    model_name: 'claude-3-5-sonnet-v2@20241022',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-claude-v3',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k for prompt
      inject_current_file_into_retrievals: true,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-3-5-16k-v5-2-direct': {
    client_type: 'anthropic_direct',
    model_name: 'claude-3-5-sonnet-20241022',
    prompt_formatter_name: 'binks-claude-v3',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k for prompt
      inject_current_file_into_retrievals: true,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-3-5-16k-v11-4': {
    client_type: 'anthropic_vertexai',
    model_name: 'claude-3-5-sonnet-v2@20241022',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-claude-v11-1',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k for prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-3-5-16k-v11-4-direct': {
    client_type: 'anthropic_direct',
    model_name: 'claude-3-5-sonnet-20241022',
    prompt_formatter_name: 'binks-claude-v11-1',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k for prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-16k-v14-3': {
    client_type: 'anthropic_vertexai',
    model_name: 'claude-3-7-sonnet@20250219',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-claude-v14',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k for prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-16k-v14-3-direct': {
    client_type: 'anthropic_direct',
    model_name: 'claude-3-7-sonnet-20250219',
    prompt_formatter_name: 'binks-claude-v14',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k for prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-16k-v15-1': {
    client_type: 'anthropic_vertexai',
    model_name: 'claude-3-7-sonnet@20250219',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-claude-v15',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k for prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-16k-v15-1-direct': {
    client_type: 'anthropic_direct',
    model_name: 'claude-3-7-sonnet-20250219',
    prompt_formatter_name: 'binks-claude-v15',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k for prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-16k-v16': {
    client_type: 'anthropic_vertexai',
    model_name: 'claude-3-7-sonnet@20250219',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-claude-v16',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k for prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-16k-v16-direct': {
    client_type: 'anthropic_direct',
    model_name: 'claude-3-7-sonnet-20250219',
    prompt_formatter_name: 'binks-claude-v16',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k for prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-v17': {
    client_type: 'anthropic_vertexai',
    model_name: 'claude-3-7-sonnet@20250219',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-claude-v17',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,
      selected_code_len: -1,
      chat_history_len: 1024 * 8,
      retrieval_len: 1024 * 8,
      retrieval_len_per_each_user_guided_file: 1024 * 6,
      retrieval_len_for_user_guided: 1024 * 12,
      max_prompt_len: 1024 * 180,
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-v17-direct': {
    client_type: 'anthropic_direct',
    model_name: 'claude-3-7-sonnet-20250219',
    prompt_formatter_name: 'binks-claude-v17',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,
      selected_code_len: -1,
      chat_history_len: 1024 * 8,
      retrieval_len: 1024 * 8,
      retrieval_len_per_each_user_guided_file: 1024 * 6,
      retrieval_len_for_user_guided: 1024 * 12,
      max_prompt_len: 1024 * 180,
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-v17-balanced': {
    client_type: 'anthropic_balanced',
    model_name: 'claude-3-7-sonnet@20250219',
    prompt_formatter_name: 'binks-claude-v17',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,
      selected_code_len: -1,
      chat_history_len: 1024 * 8,
      retrieval_len: 1024 * 8,
      retrieval_len_per_each_user_guided_file: 1024 * 6,
      retrieval_len_for_user_guided: 1024 * 12,
      max_prompt_len: 1024 * 180,
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-v18': {
    client_type: 'anthropic_balanced',
    model_name: 'claude-sonnet-4@20250514',
    prompt_formatter_name: 'binks-claude-v18',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,
      selected_code_len: -1,
      chat_history_len: 1024 * 8,
      retrieval_len: 1024 * 8,
      retrieval_len_per_each_user_guided_file: 1024 * 6,
      retrieval_len_for_user_guided: 1024 * 12,
      max_prompt_len: 1024 * 180,
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'gemini-2-flash-16k-v1': {
    client_type: 'vertexai',
    model_name: 'gemini-2.0-flash-exp',
    prompt_formatter_name: 'binks-gemini-2.0-flash-exp',
    temperature: 0.0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 2000,
      retrieval_len_for_user_guided: 3000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
    },
  },
  'gemini-2-flash-128k-v1': {
    client_type: 'vertexai',
    model_name: 'gemini-2.0-flash-exp',
    prompt_formatter_name: 'binks-gemini-2.0-flash-exp',
    temperature: 0.0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 4,
      suffix_len: 1024 * 4,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 8,
      retrieval_len_per_each_user_guided_file: 2000,
      retrieval_len_for_user_guided: 3000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 120,  // 120k tokens for the prompt
    },
  },
  'gemini-2-5-pro-v3': {
    client_type: 'vertexai',
    model_name: 'gemini-2.5-pro-preview-03-25',
    prompt_formatter_name: 'binks-gemini-2.0-flash-exp',
    temperature: 0.0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 2000,
      retrieval_len_for_user_guided: 3000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
    },
  },
  'gemini-2-5-pro-v4': {
    client_type: 'vertexai',
    model_name: 'gemini-2.5-pro-preview-03-25',
    prompt_formatter_name: 'binks-gemini-2.0-flash-exp-v2',
    temperature: 0.0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 2000,
      retrieval_len_for_user_guided: 3000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
    },
  },
  'gemini-2-5-pro-v5': {
    client_type: 'google_genai',
    model_name: 'gemini-2.5-pro-preview-05-06',
    prompt_formatter_name: 'binks-gemini-2.0-flash-exp',
    temperature: 0.0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 2000,
      retrieval_len_for_user_guided: 3000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
    },
  },
  'gemini-2-5-pro-v6': {
    client_type: 'google_genai',
    model_name: 'gemini-2.5-pro-preview-05-06',
    prompt_formatter_name: 'binks-gemini-2.0-flash-exp-v2',
    temperature: 0.0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 2000,
      retrieval_len_for_user_guided: 3000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
    },
  },
  'gemini-2-5-flash-v1': {
    client_type: 'vertexai',
    model_name: 'gemini-2.5-flash-preview-04-17',
    prompt_formatter_name: 'binks-gemini-2.0-flash-exp',
    temperature: 0.0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 2000,
      retrieval_len_for_user_guided: 3000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
    },
  },
  'gemini-2-5-flash-v2': {
    client_type: 'vertexai',
    model_name: 'gemini-2.5-flash-preview-04-17',
    prompt_formatter_name: 'binks-gemini-2.0-flash-exp-v2',
    temperature: 0.0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 2000,
      retrieval_len_for_user_guided: 3000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
    },
  },
  'gpt4-1-v1': {
    client_type: 'openai_vertexai',
    model_name: 'gpt-4.1-2025-04-14',
    gcp_region: 'us-central1',  // Currently only available on us-central1
    prompt_formatter_name: 'binks-openai-v2',
    temperature: 1,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'gpt4-1-mini-v1': {
    client_type: 'openai_vertexai',
    model_name: 'gpt-4.1-mini-2025-04-14',
    gcp_region: 'us-central1',  // Currently only available on us-central1
    prompt_formatter_name: 'binks-openai-v2',
    temperature: 1,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'gpt4-1-nano-v1': {
    client_type: 'openai_vertexai',
    model_name: 'gpt-4.1-nano-2025-04-14',
    gcp_region: 'us-central1',  // Currently only available on us-central1
    prompt_formatter_name: 'binks-openai-v2',
    temperature: 1,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'o3-v1': {
    client_type: 'openai_vertexai',
    model_name: 'o3-2025-04-16',
    gcp_region: 'us-central1',  // Currently only available on us-central1
    prompt_formatter_name: 'binks-openai-v2',
    temperature: 1,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'o4-mini-v1': {
    client_type: 'openai_vertexai',
    model_name: 'o4-mini-2025-04-16',
    gcp_region: 'us-central1',  // Currently only available on us-central1
    prompt_formatter_name: 'binks-openai-v2',
    temperature: 1,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'claude-sonnet-3-7-simple': {
    client_type: 'anthropic_vertexai',
    model_name: 'claude-3-7-sonnet@20250219',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'simple-binks',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 0,
      suffix_len: 0,
      path_len: 256,  // Not used, but we have asserts for it to be > 0
      message_len: -1,
      selected_code_len: -1,
      chat_history_len: -1,  // Included in max_prompt_len
      retrieval_len_per_each_user_guided_file: 0,
      retrieval_len_for_user_guided: 0,
      retrieval_len: 0,
      max_prompt_len: 1024 * 16,  // 16k tokens for the prompt
    },
  },
}
