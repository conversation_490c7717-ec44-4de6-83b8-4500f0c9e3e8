/**
  Defines third-party agent-chat model configurations used across deployment files
 */

local templates = import 'services/deploy/third_party_agent_models_template.jsonnet';
{
  add_suffix: {
    'claude-sonnet-3-5-200k-v2': templates.agent_v2_model_agnostic + {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-5-sonnet-v2@20241022',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-sonnet-3-5-200k-v2-direct': templates.agent_v2_model_agnostic + {
      client_type: 'anthropic_direct',
      model_name: 'claude-3-5-sonnet-20241022',
    },
    'claude-sonnet-3-7-200k-v2': templates.agent_v2_model_agnostic + {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-7-sonnet@20250219',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-sonnet-3-7-200k-v2-direct': templates.agent_v2_model_agnostic + {
      client_type: 'anthropic_direct',
      model_name: 'claude-3-7-sonnet-20250219',
    },
    'claude-sonnet-3-7-150k-v2': templates.agent_v2_150k_model_agnostic + {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-7-sonnet@20250219',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-sonnet-3-7-150k-v2-direct': templates.agent_v2_150k_model_agnostic + {
      client_type: 'anthropic_direct',
      model_name: 'claude-3-7-sonnet-20250219',
    },
    'claude-sonnet-3-5-200k-v3': templates.agent_v3_model_agnostic + {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-5-sonnet-v2@20241022',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-sonnet-3-5-200k-v3-direct': templates.agent_v3_model_agnostic + {
      client_type: 'anthropic_direct',
      model_name: 'claude-3-5-sonnet-20241022',
    },
    'claude-sonnet-3-7-200k-v3': templates.agent_v3_model_agnostic + {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-7-sonnet@20250219',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-sonnet-3-7-200k-v3-direct': templates.agent_v3_model_agnostic + {
      client_type: 'anthropic_direct',
      model_name: 'claude-3-7-sonnet-20250219',
    },
    'claude-sonnet-3-7-200k-v3-balanced': templates.agent_v3_model_agnostic + {
      client_type: 'anthropic_balanced',
      model_name: 'claude-3-7-sonnet@20250219',
    },
    'claude-sonnet-4-0-200k-v3': templates.agent_v3_model_agnostic + {
      client_type: 'anthropic_balanced',
      model_name: 'claude-sonnet-4@20250514',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-sonnet-4-0-200k-v4': templates.agent_model_agnostic + {
      client_type: 'anthropic_balanced',
      model_name: 'claude-sonnet-4@20250514',
      prompt_formatter_name: 'agent-binks-claude-v4',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-sonnet-4-0-200k-v5': templates.agent_model_agnostic + {
      client_type: 'anthropic_balanced',
      model_name: 'claude-sonnet-4@20250514',
      prompt_formatter_name: 'agent-binks-claude-v5',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-opus-4-0-200k-v5': templates.agent_model_agnostic + {
      client_type: 'anthropic_balanced',
      model_name: 'claude-opus-4@20250514',
      prompt_formatter_name: 'agent-binks-claude-v5',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-sonnet-4-0-200k-v6': templates.agent_model_agnostic + {
      client_type: 'anthropic_balanced',
      model_name: 'claude-sonnet-4@20250514',
      prompt_formatter_name: 'agent-binks-claude-v6',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-sonnet-4-0-200k-v7': templates.agent_model_agnostic + {
      client_type: 'anthropic_balanced',
      model_name: 'claude-sonnet-4@20250514',
      prompt_formatter_name: 'agent-binks-claude-v7',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-opus-4-0-200k-v6': templates.agent_model_agnostic + {
      client_type: 'anthropic_balanced',
      model_name: 'claude-opus-4@20250514',
      prompt_formatter_name: 'agent-binks-claude-v6',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-opus-4-0-200k-v7': templates.agent_model_agnostic + {
      client_type: 'anthropic_balanced',
      model_name: 'claude-opus-4@20250514',
      prompt_formatter_name: 'agent-binks-claude-v7',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-sonnet-3-7-150k-v3': templates.agent_v3_150k_model_agnostic + {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-7-sonnet@20250219',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    },
    'claude-sonnet-3-7-150k-v3-direct': templates.agent_v3_150k_model_agnostic + {
      client_type: 'anthropic_direct',
      model_name: 'claude-3-7-sonnet-20250219',
    },
    'claude-sonnet-3-7-task-list-v1': templates.agent_v3_model_agnostic + {
      client_type: 'anthropic_balanced',
      model_name: 'claude-3-7-sonnet@20250219',
      prompt_formatter_name: 'agent-binks-task-list-v1',
    },
    'gpt4-1-200k-v1': templates.agent_v2_model_agnostic + {
      client_type: 'openai_vertexai',
      model_name: 'gpt-4.1-2025-04-14',
      gcp_region: 'us-central1',
    },
    'gemini2-5-pro-200k-v3-1': templates.agent_v3_model_agnostic + {
      client_type: 'google_genai',
      model_name: 'gemini-2.5-pro-preview-03-25',
      gcp_region: 'us-central1',  // Currently only available on us-central1
      prompt_formatter_name: 'agent-binks-gemini',
    },
    'gemini2-5-pro-200k-v3-2': templates.agent_v3_model_agnostic + {
      client_type: 'google_genai',
      model_name: 'gemini-2.5-pro-preview-05-06',
      gcp_region: 'us-central1',  // Currently only available on us-central1
      prompt_formatter_name: 'agent-binks-gemini',
    },
    'gemini2-5-flash-200k-v3': templates.agent_v3_model_agnostic + {
      client_type: 'google_genai',
      model_name: 'gemini-2.5-flash-preview-04-17',
      gcp_region: 'us-central1',  // Currently only available on us-central1
    },
    // 'grok-swe-200k-v4': templates.agent_model_agnostic + {
    //   client_type: 'xai_research',
    //   model_name: 'swe-v6-checkpoint-05-23',
    //   prompt_formatter_name: 'agent-binks-claude-v6',
    //   gcp_region: 'us-central1',
    // },
  },
  no_suffix: {
    'gemini-2-flash-001-simple-port': {
      client_type: 'vertexai',
      model_name: 'gemini-2.0-flash-001',
      prompt_formatter_name: 'simple-binks',
      temperature: 0.0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 0,
        suffix_len: 0,
        path_len: 256,  // Not used, but we have asserts for it to be > 0
        message_len: -1,
        selected_code_len: -1,
        chat_history_len: -1,  // Included in max_prompt_len
        retrieval_len_per_each_user_guided_file: 0,
        retrieval_len_for_user_guided: 0,
        retrieval_len: 0,
        max_prompt_len: 1024 * 16,  // 16k tokens for the prompt
      },
    },
  },
}
