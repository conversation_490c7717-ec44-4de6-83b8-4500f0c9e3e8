syntax = "proto3";

package remote_agents;

import "google/protobuf/timestamp.proto";
import "services/chat_host/chat.proto";

// EXPERIMENTAL: Backwards compatibility is not guaranteed for any of these (yet)
service RemoteAgents {
  // Management APIs for users to call
  // CreateAgent will also kick off the agent loop
  rpc CreateAgent(CreateAgentRequest) returns (CreateAgentResponse) {}
  rpc ListAgents(ListAgentsRequest) returns (ListAgentsResponse) {}
  rpc ListAgentsStream(ListAgentsStreamRequest) returns (stream ListAgentsStreamResponse) {}
  rpc Chat(ChatRequest) returns (ChatResponse) {}
  rpc ChatHistory(ChatHistoryRequest) returns (ChatHistoryResponse) {}
  rpc AgentHistoryStream(AgentHistoryStreamRequest) returns (stream AgentHistoryStreamResponse) {}
  rpc InterruptAgent(InterruptAgentRequest) returns (InterruptAgentResponse) {}
  rpc DeleteAgent(DeleteAgentRequest) returns (DeleteAgentResponse) {}
  rpc WorkspaceLogs(WorkspaceLogsRequest) returns (WorkspaceLogsResponse) {}
  // Creates a new open SSH port for the remote agent workspace if necessary
  rpc AddSSHKey(AddSSHKeyRequest) returns (AddSSHKeyResponse) {}
  // Resumes a paused agent
  rpc ResumeAgent(ResumeAgentRequest) returns (ResumeAgentResponse) {}
  // Pauses a running agent
  rpc PauseAgent(PauseAgentRequest) returns (PauseAgentResponse) {}

  // APIs for the beachhead to use
  rpc WorkspaceReportStatus(WorkspaceReportStatusRequest) returns (WorkspaceReportStatusResponse) {}
  rpc WorkspaceReportChatHistory(WorkspaceReportChatHistoryRequest) returns (WorkspaceReportChatHistoryResponse) {}
  rpc WorkspacePollUpdate(WorkspacePollUpdateRequest) returns (WorkspacePollUpdateResponse) {}
  rpc WorkspaceStream(WorkspaceStreamRequest) returns (stream WorkspaceStreamResponse) {}
  rpc WorkspaceReportSetupLogs(WorkspaceReportSetupLogsRequest) returns (WorkspaceReportSetupLogsResponse) {}
}

/*
   APIs for users to call
*/
message CreateAgentRequest {
  AgentConfig config = 1;
}

// Response for agent creation
message CreateAgentResponse {
  Agent agent = 1;
}

message ListAgentsRequest {}

message ListAgentsResponse {
  repeated Agent agents = 1;

  // The maximum number of total remote agents this user can have
  int32 max_agents = 2;

  // The maximum number of active remote agents this user can have (not including paused agents)
  int32 max_active_agents = 3;
}

message ChatRequest {
  string remote_agent_id = 1;
  ChatRequestDetails request_details = 2;
}

message ChatResponse {}

message ChatHistoryRequest {
  string remote_agent_id = 1;

  // The sequence ID of the last processed exchange.
  // The server will return exchanges with sequence IDs greater than this value.
  // This enables clients to implement pagination by requesting subsequent pages
  // using the sequence ID of the last exchange from the previous response.
  uint32 last_processed_sequence_id = 2;
}

message ChatHistoryResponse {
  repeated ChatHistoryExchange chat_history = 1;
  optional string session_summary = 2;
  optional Agent agent = 3;
}

message InterruptAgentRequest {
  string remote_agent_id = 1;
}

message InterruptAgentResponse {}

message DeleteAgentRequest {
  string remote_agent_id = 1;
}

message DeleteAgentResponse {}

message Agent {
  string remote_agent_id = 1;
  AgentStatus status = 2;
  AgentConfig config = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;

  optional AgentSSHConfig ssh_config = 6;
  // Whether the agent has been read by the user
  // If false, it means the agent has unread updates
  optional bool has_updates = 7;
  WorkspaceStatus workspace_status = 8;

  optional google.protobuf.Timestamp expires_at = 9;
}

enum WorkspaceSetupStepStatus {
  // Unknown status (if skipped it will also be unknown)
  WORKSPACE_SETUP_STEP_STATUS_UNKNOWN = 0;
  WORKSPACE_SETUP_STEP_STATUS_RUNNING = 1;
  WORKSPACE_SETUP_STEP_STATUS_SUCCESS = 2;
  WORKSPACE_SETUP_STEP_STATUS_FAILURE = 3;
  WORKSPACE_SETUP_STEP_STATUS_SKIPPED = 4;
}

message WorkspaceSetupStep {
  // A string we can show users, like "Cloning repository" or "Running setup script"
  string step_description = 1;
  string logs = 2;
  WorkspaceSetupStepStatus status = 3;
  // this is monotonically increasing but not necessarily consecutive.
  uint32 sequence_id = 4;
  // This is the step number, starting from 0. It is consecutive.
  uint32 step_number = 5;
  // TODO: add an enum here if the UI wants to be able to identify specific steps?
}

message WorkspaceSetupStatus {
  // While the agent is in starting state, should always contain at least one step that is running
  repeated WorkspaceSetupStep steps = 1;
}

message AgentSSHConfig {
  repeated string public_keys = 1;

  // The config for all public keys is the same.
  string hostname = 2;
  repeated SSHConfigOption ssh_config_options = 3;
}

// Public facing agent status
enum AgentStatus {
  AGENT_STATUS_UNSPECIFIED = 0;
  // The agent has been created, but has not yet been assigned to a Workspace
  AGENT_STATUS_PENDING = 5;
  // The agent is being set up
  AGENT_STATUS_STARTING = 1;
  // The agent is running
  AGENT_STATUS_RUNNING = 2;
  // The agent is suspended, waiting for user input
  AGENT_STATUS_IDLE = 3;
  // The agent has failed, and may not be recoverable?
  AGENT_STATUS_FAILED = 4;
  // The agent is in the process of being paused
  AGENT_STATUS_PAUSING = 6 [deprecated = true];
  // The agent is paused but can be resumed
  AGENT_STATUS_PAUSED = 7 [deprecated = true];
  // The agent is pending deletion
  AGENT_STATUS_PENDING_DELETION = 8;
}

enum WorkspaceStatus {
  WORKSPACE_STATUS_UNSPECIFIED = 0;
  WORKSPACE_STATUS_RUNNING = 1;
  WORKSPACE_STATUS_PAUSING = 2;
  WORKSPACE_STATUS_PAUSED = 3;
  WORKSPACE_STATUS_RESUMING = 4;
  WORKSPACE_STATUS_DELETING = 5;
}

// The config is immutable after an agent is created
message AgentConfig {
  WorkspaceSetup workspace_setup = 1;
  // TODO: Replace with ChatRequestDetails to align with public_api.proto.
  repeated chat.ChatRequestNode starting_nodes = 2;
  optional string user_guidelines = 3;
  optional string workspace_guidelines = 4;
  optional string agent_memories = 5;
  optional string model = 6;
  optional bool is_setup_script_agent = 7;
  // TODO: add a timeout?
  repeated McpServerConfig mcp_servers = 8;
}

message McpServerConfig {
  string command = 1;
  repeated string args = 2;
  optional int32 timeout_ms = 3;
  map<string, string> env = 4;
  optional bool use_shell_interpolation = 5;
  optional string name = 6;
  optional bool disabled = 7;
}

message ChatRequestDetails {
  repeated chat.ChatRequestNode request_nodes = 1;
  optional string user_guidelines = 2;
  optional string workspace_guidelines = 3;
  optional string agent_memories = 4;
  optional string model_id = 5;
  repeated McpServerConfig mcp_servers = 6;
}

message GithubRef {
  string url = 1;
  string ref = 2;
}

message WorkspaceSetup {
  oneof starting_files {
    GithubRef github_ref = 1;
  }
  // TODO: add more here, like setup commands, env vars, etc?
  optional string patch = 2;
  optional string setup_script = 3;
  optional string token = 4 [debug_redact = true];
  optional string api_url = 5;

  // global git config to set, the values here will be passed to
  // `git config --global <key> <value>`
  // Recommended keys to pass are user.name and user.email
  map<string, string> git_config = 6;
}

// WorkspaceAgentConfig contains all we need to pass to workspace (beachhead)
// at workspace creation time.
// This proto is stored with the Remote Agent Workspace. It is not stored in BigTable.
message WorkspaceAgentConfig {
  string remote_agent_id = 1;
  AgentConfig config = 2;
  map<string, string> experiments = 3; // aka "instruction flags" parsed from _flags:{...}.
  string github_username = 4 [debug_redact = true];
  string github_user_token = 5 [debug_redact = true];
  string github_name = 6 [debug_redact = true];
  string github_email = 7 [debug_redact = true];

  // Pass these explicitly so that we can easily use them in the beachhead to
  // publish logs to RI
  string request_insight_topic_name = 8;
  string tenant_id = 9;
  string tenant_name = 10;
}

message ChatHistoryExchange {
  optional chat.Exchange exchange = 1;
  repeated ChangedFile changed_files = 2;
  uint32 sequence_id = 3;
  optional string turn_summary = 4;
  google.protobuf.Timestamp finished_at = 5;

  // If set, this indicates to the frontend that some or all of the changed files were unable to be
  // returned. The value is a list of file paths that were skipped. Note that this list may not be
  // exhaustive. To know if the list is exhaustive, check the length against the
  // changed_files_skipped_count field.
  repeated string changed_files_skipped = 6;

  // If set, this indicates the total number of changed files that were skipped.
  optional uint32 changed_files_skipped_count = 7;
}

message ChangedFile {
  // Empty if file was added
  string old_path = 1;

  // Empty if the file was deleted
  string new_path = 2;

  string old_contents = 3;
  string new_contents = 4;

  enum FileChangeType {
    ADDED = 0;
    DELETED = 1;
    MODIFIED = 2;
    RENAMED = 3;
  }
  FileChangeType change_type = 5;
}

message AddSSHKeyRequest {
  string remote_agent_id = 1;
  repeated string public_keys = 2;
}

message SSHConfigOption {
  // Key and value for a single SSH config option, based on
  // https://man7.org/linux/man-pages/man5/ssh_config.5.html.
  string key = 1;
  string value = 2;
}

message AddSSHKeyResponse {
  // Returns all configured keys, not just the newly added ones.
  AgentSSHConfig ssh_config = 1;
}

message ResumeAgentRequest {
  string remote_agent_id = 1;
}

message ResumeAgentResponse {}

message PauseAgentRequest {
  string remote_agent_id = 1;
}

message PauseAgentResponse {}

/*
   APIs for the beachhead to WorkspaceSetupStatuses
*/
message WorkspaceReportStatusRequest {
  string remote_agent_id = 1;
  AgentStatus status = 2;
  bool has_active_ssh_connection = 3;
}

message WorkspaceReportStatusResponse {}

message WorkspaceReportChatHistoryRequest {
  string remote_agent_id = 1;
  repeated ChatHistoryExchange chat_history = 2;
}

message WorkspaceReportChatHistoryResponse {}

message WorkspaceInterruptRequest {}

message WorkspaceChatRequest {
  ChatRequestDetails request_details = 1;
}

message WorkspacePollUpdateRequest {
  string remote_agent_id = 1;
  // The sequence ID of the last update processed
  // by the agent. The server will return all
  // updates with a sequence ID greater than this.
  // NOTE: sequence ID is guranteed to be monotonically
  // increasing, but not necessarily consecutive.
  uint32 last_processed_sequence_id = 2;
}

message WorkspaceUpdate {
  uint32 sequence_id = 1;
  oneof update {
    WorkspaceInterruptRequest interrupt = 2;
    WorkspaceChatRequest chat_request = 3;
  }
}

message WorkspacePollUpdateResponse {
  // All updates with a sequence ID greater than the
  // last_processed_sequence_id in the request.
  repeated WorkspaceUpdate updates = 2;
}

message WorkspaceLogsRequest {
  string remote_agent_id = 1;

  // These are the last step and sequence id that was received from the backend
  // to the frontend. The backend will return all the logs after this configuration
  // default values are 0, 0 (which will return all logs)
  optional uint32 last_processed_step = 2;
  optional uint32 last_processed_sequence_id = 3;
}

message WorkspaceLogsResponse {
  string remote_agent_id = 1;
  WorkspaceSetupStatus workspace_setup_status = 3;
}

// This will be called by the beachhead to report the logs for each step
// as they are completed.
// The guarantee is that each step (or chunk of a step) will be reported
// exactly once. Additionally, the sequence id is guaranteed to be monotonically
// increasing, but not necessarily consecutive. Also the length of the logs in each chunk is always < 1000,
// so if the logs are > 1000 chars, the beachhead will need to split them into multiple chunks, and report them
// over multiple calls.
message WorkspaceReportSetupLogsRequest {
  string remote_agent_id = 1;
  WorkspaceSetupStatus workspace_setup_status = 3;
}

message WorkspaceReportSetupLogsResponse {}

// WorkspaceStreamRequest is used to request a stream of workspace updates.
// It provides the necessary information to establish a streaming connection
// and resume from a specific point in the workspace's update history.
message WorkspaceStreamRequest {
  // The ID of the remote agent to stream workspace updates for
  string remote_agent_id = 1;

  // The sequence ID of the last processed update.
  // The server will return updates with sequence IDs greater than this value.
  // This enables clients to implement pagination and reconnection by requesting
  // subsequent updates using the sequence ID of the last update from the previous response.
  uint32 last_processed_sequence_id = 2;
}

// WorkspaceUpdateType defines the different types of updates that can be sent in a workspace stream.
// Each update type corresponds to a specific field in the WorkspaceStreamUpdate message.
enum WorkspaceUpdateType {
  // Default unspecified value - should not be used
  WORKSPACE_UPDATE_TYPE_UNSPECIFIED = 0;

  // An interrupt request for the workspace
  WORKSPACE_UPDATE_INTERRUPT = 1;

  // A chat request for the workspace
  WORKSPACE_UPDATE_CHAT_REQUEST = 2;
}

// WorkspaceStreamUpdate represents a single update in the workspace stream.
// Only one of the optional fields should be set, determined by the 'type' field.
// This allows for different types of updates to be sent in the same stream.
message WorkspaceStreamUpdate {
  // The type of update, which determines which field is set
  WorkspaceUpdateType type = 1;

  // The sequence ID of this update
  uint32 sequence_id = 2;

  // For WORKSPACE_UPDATE_INTERRUPT updates
  optional WorkspaceInterruptRequest interrupt = 3;

  // For WORKSPACE_UPDATE_CHAT_REQUEST updates
  optional WorkspaceChatRequest chat_request = 4;
}

// WorkspaceStreamResponse represents a response in the workspace stream.
// It contains a sequence of updates with strong reconnection semantics.
message WorkspaceStreamResponse {
  // A list of structured updates. Multiple updates can be included in a single
  // response. The updates should be associated with at most one sequence ID.
  repeated WorkspaceStreamUpdate updates = 1;
}

// ListAgentsStreamRequest is used to request a stream of agent list updates.
// It provides the necessary information to establish a streaming connection
// and receive real-time updates about agent status changes, new agents, and deletions.
message ListAgentsStreamRequest {
  // Optional: The timestamp of the last update the client has seen.
  // If not provided, the server will send the current state of all agents
  // and then stream subsequent updates.
  // NOTE: Timestamp-based optimization is currently unimplemented.
  optional google.protobuf.Timestamp last_update_timestamp = 1;
}

// AgentHistoryStreamRequest is used to request a stream of agent history updates.
// It provides the necessary information to establish a streaming connection
// and resume from a specific point in the agent's history.
message AgentHistoryStreamRequest {
  // The ID of the remote agent to stream history for
  string remote_agent_id = 1;

  // The sequence ID of the last processed exchange.
  // The server will return exchanges with sequence IDs greater than this value.
  // This enables clients to implement pagination and reconnection by requesting
  // subsequent updates using the sequence ID of the last exchange from the previous response.
  uint32 last_processed_sequence_id = 2;
}

// AgentListUpdateType defines the different types of updates that can be sent in an agent list stream.
// Each update type corresponds to a specific field in the AgentListUpdate message.
enum AgentListUpdateType {
  // Default unspecified value - should not be used
  AGENT_LIST_UPDATE_TYPE_UNSPECIFIED = 0;

  // A new agent was created
  AGENT_LIST_AGENT_ADDED = 1;

  // An existing agent was updated (status, workspace status, or other properties changed)
  AGENT_LIST_AGENT_UPDATED = 2;

  // An agent was deleted
  AGENT_LIST_AGENT_DELETED = 3;

  // Initial state: all current agents (sent when client first connects)
  AGENT_LIST_ALL_AGENTS = 4;
}

// AgentHistoryUpdateType defines the different types of updates that can be sent in a stream.
// Each update type corresponds to a specific field in the AgentHistoryUpdate message.
enum AgentHistoryUpdateType {
  // Default unspecified value - should not be used
  AGENT_HISTORY_UPDATE_TYPE_UNSPECIFIED = 0;

  // A complete exchange between the user and agent
  AGENT_HISTORY_EXCHANGE = 1;

  // An incremental update to an existing exchange (text or nodes)
  AGENT_HISTORY_EXCHANGE_UPDATE = 2;

  // An update to the agent's status
  AGENT_HISTORY_AGENT_STATUS = 3;
}

// AgentListUpdate represents a single update in the agent list stream.
// Only one of the optional fields should be set, determined by the 'type' field.
// This allows for different types of updates to be sent in the same stream.
message AgentListUpdate {
  // The type of update, which determines which field is set
  AgentListUpdateType type = 1;

  // The timestamp when this update occurred
  google.protobuf.Timestamp update_timestamp = 2;

  // For AGENT_LIST_AGENT_ADDED, AGENT_LIST_AGENT_UPDATED, and AGENT_LIST_ALL_AGENTS updates
  optional Agent agent = 3;

  // For AGENT_LIST_AGENT_DELETED updates - just the agent ID
  optional string deleted_agent_id = 4;

  // For AGENT_LIST_ALL_AGENTS updates - all current agents
  repeated Agent all_agents = 5;

  // The maximum number of total remote agents this user can have
  optional int32 max_agents = 6;

  // The maximum number of active remote agents this user can have (not including paused agents)
  optional int32 max_active_agents = 7;
}

// ListAgentsStreamResponse represents a response in the agent list stream.
// It contains a sequence of updates with real-time agent status changes.
message ListAgentsStreamResponse {
  // A list of structured updates. Multiple updates can be included in a single
  // response for efficiency.
  repeated AgentListUpdate updates = 1;
}

// ExchangeUpdate represents an incremental update to an exchange.
// It is used to stream partial responses as they become available,
// including both text updates and result node updates.
message ExchangeUpdate {
  // The request ID associated with this update.
  // This can be used to correlate the update with a specific request.
  optional string request_id = 1;

  // The sequence ID of the exchange being updated.
  // This allows clients to maintain order and handle reconnection scenarios.
  uint32 sequence_id = 2;

  // The text to append to the current response.
  // This is the incremental text that should be added to the existing response.
  string appended_text = 3;

  // The nodes to append to the current response.
  // These are additional result nodes that should be added to the existing response.
  repeated chat.ChatResultNode appended_nodes = 4;

  // Files changed as a result of this exchange update.
  // These are incremental file changes that should be added to the existing changed_files.
  repeated ChangedFile appended_changed_files = 5;
}

// AgentHistoryUpdate represents a single update in the agent history stream.
// Only one of the optional fields should be set, determined by the 'type' field.
// This allows for different types of updates to be sent in the same stream.
message AgentHistoryUpdate {
  // The type of update, which determines which field is set
  AgentHistoryUpdateType type = 1;

  // For AGENT_HISTORY_EXCHANGE updates - a complete exchange between user and agent
  optional ChatHistoryExchange exchange = 2;

  // For AGENT_HISTORY_EXCHANGE_UPDATE updates - incremental updates to an existing exchange
  // This can include text updates and/or node updates
  optional ExchangeUpdate exchange_update = 3;

  // For AGENT_HISTORY_AGENT_STATUS updates - updates to the agent's status
  optional Agent agent = 4;
}

// AgentHistoryStreamResponse represents a response in the agent history stream.
// It contains a sequence of updates with strong reconnection semantics.
// Exchanges and exchange updates include their own sequence IDs for reconnection semantics,
// while other updates (like agent status) are opportunistically included in the stream.
message AgentHistoryStreamResponse {
  // A list of structured updates. Multiple updates can be included in a single
  // response. The updates should be associated with at most one sequence ID.
  repeated AgentHistoryUpdate updates = 1;
}
