// the build.rs file is executed by cargo at build-time
// and is used to generate code.
use std::{env, path::Path, path::PathBuf};

fn get_base_dir() -> PathBuf {
    let cwd = env::current_dir().expect("failed to get cwd");
    let root = cwd.join("../../../").canonicalize().unwrap();
    root
}

fn get_external_dir() -> PathBuf {
    let root = get_base_dir();
    if std::env::var("USER").is_err() {
        root.join("..")
    } else {
        root.join("../bazel-augment-external")
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // generate the code from protobuf files via build.rs so that cargo/rust-analyzer continues
    // to work.
    let root = get_base_dir();
    let external_dir = get_external_dir();

    let protos = vec![
        root.join("services/chat_host/chat.proto"),
        root.join("services/remote_agents/remote_agents.proto"),
    ];
    for proto in protos {
        let proto_path: &Path = proto.as_ref();

        let proto_dir = proto_path.parent().unwrap();

        let protobuf_src_dir = external_dir.join("protobuf~/src/src");
        let protobuf_timestamp_dir =
            external_dir.join("protobuf~/src/google/protobuf/_virtual_imports/timestamp_proto/");
        let includes = vec![
            proto_dir,
            protobuf_src_dir.as_ref(),
            protobuf_timestamp_dir.as_ref(),
            &root,
        ];

        tonic_build::configure()
            .extern_path(".google.protobuf.Timestamp", "::prost_wkt_types::Timestamp")
            .compile_protos(&[proto_path], &includes)?;
    }

    let blob_names_path = root.join("base/blob_names/blob_names.proto");
    tonic_build::configure()
        .type_attribute(".", "#[derive(serde::Serialize,serde::Deserialize)]")
        .compile_protos(&[&blob_names_path], &[blob_names_path.parent().unwrap()])?;

    Ok(())
}
