package main

import (
	"bytes"
	"context"
	"crypto/sha256"
	"errors"
	"fmt"
	"net/url"
	"slices"
	"strings"
	"time"

	"golang.org/x/crypto/ssh"

	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	chatproto "github.com/augmentcode/augment/services/chat_host/proto"
	githubprocessorclient "github.com/augmentcode/augment/services/integrations/github/processor/client"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	memstoreclient "github.com/augmentcode/augment/services/memstore/client"
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	"github.com/augmentcode/augment/services/remote_agents/server/ws"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
)

const (
	max_remote_agents_per_user_flag        = "max_remote_agents_per_user"
	max_active_remote_agents_per_user_flag = "max_active_remote_agents_per_user"

	defaultChatHistoryExchangeToReturn = 100
)

var (
	max_remote_agents_per_user        = featureflags.NewIntFlag(max_remote_agents_per_user_flag, 100)
	max_active_remote_agents_per_user = featureflags.NewIntFlag(max_active_remote_agents_per_user_flag, 10)
	autoDeleteTTLFlag                 = featureflags.NewIntFlag("remote_agents_auto_delete_ttl_days", 28)
	nowFunc                           = time.Now
	// These are agent statuses that should not be shown to the user
	agentStatusNotShownSet = map[remoteagentsproto.AgentStatus]bool{
		remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION: true,
	}
)

// Custom namespace for remote agent IDs
// This UUID is used to generate deterministic agent IDs from request IDs for duplication safety.
// Requests with same request ID will always produce the same agent ID and hence avoid
// duplicate agent creation. It's part of the salt to make it harder for an attacker to
// guess the agent ID from the request ID.
// IMPORTANT:
// - This value MUST remain constant across all instances and deployments of the service
// - Changing this value will temporarily break deduplication across services pods
// - This is NOT a security-sensitive value and doesn't need to be kept secret
var remoteAgentUUIDNamespace = uuid.MustParse("67261383-ac66-4f39-ba21-e4bbf2047926")

// RemoteAgentsServer implements the RemoteAgents service
type RemoteAgentsServer struct {
	remoteagentsproto.UnimplementedRemoteAgentsServer

	rwc                     ws.RemoteWorkspaceController
	bigtableProxyClient     bigtableproxy.BigtableProxyClient
	githubProcessorClient   githubprocessorclient.GithubProcessorClient
	requestInsightPublisher ripublisher.RequestInsightPublisher
	featureFlagHandle       featureflags.FeatureFlagHandle
	enableInstructionFlags  bool
	agentHistoryStreamer    AgentHistoryStreamer
	agentListStreamer       AgentListStreamer
	requestInsightTopicName string
	workspaceStreamer       WorkspaceStreamer
	memstoreClient          memstoreclient.MemstoreClient
}

// NewRemoteAgentsServer creates a new RemoteAgentsServer
func NewRemoteAgentsServer(
	rwc ws.RemoteWorkspaceController,
	bigtableProxyClient bigtableproxy.BigtableProxyClient,
	githubProcessorClient githubprocessorclient.GithubProcessorClient,
	requestInsightPublisher ripublisher.RequestInsightPublisher,
	featureFlagHandle featureflags.FeatureFlagHandle,
	enableInstructionFlags bool,
	requestInsightTopicName string,
	memstoreClient memstoreclient.MemstoreClient,
) (*RemoteAgentsServer, error) {
	// Create the agent history streamer
	agentHistoryStreamer := NewBigTableAgentHistoryStreamer(bigtableProxyClient, memstoreClient, featureFlagHandle)

	// Create the agent list streamer
	agentListStreamer := NewBigTableAgentListStreamer(bigtableProxyClient, memstoreClient, featureFlagHandle)

	// Create the workspace streamer
	workspaceStreamer := NewWorkspaceStreamer(bigtableProxyClient, memstoreClient)

	return &RemoteAgentsServer{
		rwc:                     rwc,
		bigtableProxyClient:     bigtableProxyClient,
		githubProcessorClient:   githubProcessorClient,
		requestInsightPublisher: requestInsightPublisher,
		featureFlagHandle:       featureFlagHandle,
		enableInstructionFlags:  enableInstructionFlags,
		agentHistoryStreamer:    agentHistoryStreamer,
		agentListStreamer:       agentListStreamer,
		requestInsightTopicName: requestInsightTopicName,
		workspaceStreamer:       workspaceStreamer,
		memstoreClient:          memstoreClient,
	}, nil
}

// numberOfActiveAgents counts the number of active agents for a user and checks against the limit
// Returns the number of active agents for the user
func (s *RemoteAgentsServer) numActiveAgents(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID, userId string) (int, error) {
	// Read all agents from BigTable
	bigtableAgents, err := readAllRemoteAgentsForUser(ctx, requestContext, s.bigtableProxyClient, tenantID, userId, getAgentDeletionTTLDays(s.featureFlagHandle))
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agents")
		return 0, err
	}

	// Count the number of active agents
	activeAgents := 0
	for _, agent := range bigtableAgents {
		if agent.WorkspaceStatus != remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED && agent.WorkspaceStatus != remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING {
			activeAgents++
		}
	}

	return activeAgents, nil
}

// CreateAgent creates a new agent and starts its execution
func (s *RemoteAgentsServer) CreateAgent(ctx context.Context, req *remoteagentsproto.CreateAgentRequest) (*remoteagentsproto.CreateAgentResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, "")
	if err != nil {
		return nil, err
	}

	// Publish request to request insights
	clonedReq := proto.Clone(req).(*remoteagentsproto.CreateAgentRequest)
	// Don't put the token into RI, it should ONLY be visible to the user and
	// there's no reason for us to look at it
	clonedReq.Config.WorkspaceSetup.Token = nil
	requestEvent := ripublisher.NewRequestEvent()
	requestEvent.Event = &riproto.RequestEvent_RemoteAgentsCreateRequest{
		RemoteAgentsCreateRequest: &riproto.RemoteAgentsCreateRequest{
			Request: clonedReq,
		},
	}

	if err := s.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), getTenantInfo(claims), requestEvent); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish request event to request insights for CreateAgent")
	}

	if req.Config == nil {
		log.Ctx(ctx).Error().Msg("Agent config is missing")
		return nil, status.Error(codes.InvalidArgument, "agent config is required")
	}

	// Derive agent ID from request ID for idempotency
	var remoteAgentID string
	// Get request ID from request context
	requestID := requestContext.RequestId.String()
	// Derive deterministic agent ID from request ID
	remoteAgentID, err = deriveAgentIDFromRequestID(requestID, userId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to derive agent ID from request ID")
		return nil, status.Error(codes.InvalidArgument, "Invalid request ID")
	}

	ctx = log.Ctx(ctx).With().Str("remote_agent_id", remoteAgentID).Logger().WithContext(ctx)
	log.Ctx(ctx).Info().Msgf("Creating remote agent for user %s", userId)

	max_num_agents_for_user, err := max_remote_agents_per_user.Get(s.featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to get maximum number of agents per user")
	}

	// Read all agents from BigTable
	bigtableAgents, err := readAllRemoteAgentsForUser(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, getAgentDeletionTTLDays(s.featureFlagHandle))
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agents")
		return nil, err
	}
	// Check total number of agents (including paused agents)
	if len(bigtableAgents) >= max_num_agents_for_user {
		log.Ctx(ctx).Warn().Msgf("User %s has reached the maximum number of agents (%d)", userId)
		return nil, status.Errorf(codes.ResourceExhausted, "You have reached the maximum number of remote agents (%d). Please delete some existing agents before creating new ones.", max_num_agents_for_user)
	}

	// Check if the user has reached the maximum number of active agents
	max_num_active_agents_per_user, err := max_active_remote_agents_per_user.Get(s.featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to get maximum number of active agents per user")
	}

	numActiveAgents, err := s.numActiveAgents(ctx, requestContext, claims.TenantID, userId)
	if err != nil {
		return nil, err
	}

	// if agent is paused and we are at the limit, return an error
	if numActiveAgents >= max_num_active_agents_per_user {
		return nil, status.Errorf(codes.ResourceExhausted, "You have reached the maximum number of active agents (%d). Please pause some existing agents before creating new ones.", max_num_active_agents_per_user)
	}

	// Get the user's github oauth token
	githubURL := req.Config.GetWorkspaceSetup().GetGithubRef().GetUrl()
	repoOwner, repoName, err := parseGithubURL(githubURL)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to parse github URL")
		// Unauthenticated is probably better, but api-proxy turns that into a
		// 500. InvalidArgument gets turned into BadRequest, which is marginally
		// better. Long term we probably want a more structured error in the
		// proto response rather than relying on grpc status codes.
		return nil, status.Error(codes.InvalidArgument, "Invalid github URL")
	}
	githubUserToken, githubUsername, githubEmail, githubName, err := s.githubProcessorClient.GetGithubUserToken(ctx, requestContext, repoOwner, repoName)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get github user token")
		return nil, err
	}
	if githubName.Expose() == "" {
		// If we can't find a name, default to the username
		githubName = githubUsername
	}

	// Write agent config to BigTable, status = AGENT_STATUS_STARTING
	err = writeRemoteAgentAtCreation(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, remoteAgentID, req.Config, s.rwc.Namespace())
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to write agent config to BigTable")
		return nil, err
	}

	// Create a WorkspaceAgentConfig that includes both the agent_id and config
	wsCfg := &remoteagentsproto.WorkspaceAgentConfig{
		RemoteAgentId:           remoteAgentID,
		Config:                  req.Config,
		GithubUsername:          githubUsername.Expose(),
		GithubUserToken:         githubUserToken.Expose(),
		GithubName:              githubName.Expose(),
		GithubEmail:             githubEmail.Expose(),
		RequestInsightTopicName: s.requestInsightTopicName,
		TenantId:                claims.TenantID,
		TenantName:              claims.TenantName,
	}

	if s.enableInstructionFlags {
		iflags := stripInstructionFlags(req.GetConfig().GetStartingNodes()...)
		wsCfg.Experiments = iflags
	}
	if ws, err := s.rwc.ApplyWorkspace(ctx, userId, remoteAgentID, wsCfg); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to apply workspace")
		return nil, err
	} else if wsa, err := ws.Agent(ctx); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get agent")
		return nil, err
	} else {
		response := &remoteagentsproto.CreateAgentResponse{
			Agent: wsa,
		}

		// Publish agent list notification for agent creation
		PublishAgentListNotification(ctx, requestContext, s.memstoreClient, userId, remoteAgentID, "created")

		// Publish response to request insights
		responseEvent := ripublisher.NewRequestEvent()
		responseEvent.Event = &riproto.RequestEvent_RemoteAgentsCreateResponse{
			RemoteAgentsCreateResponse: &riproto.RemoteAgentsCreateResponse{
				Response: proto.Clone(response).(*remoteagentsproto.CreateAgentResponse),
			},
		}
		if err := s.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), getTenantInfo(claims), responseEvent); err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to publish response event to request insights for CreateAgent")
		}

		return response, nil
	}
}

// ListAgents lists all agents
func (s *RemoteAgentsServer) ListAgents(ctx context.Context, req *remoteagentsproto.ListAgentsRequest) (*remoteagentsproto.ListAgentsResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, "")
	if err != nil {
		return nil, err
	}

	log.Ctx(ctx).Debug().Msgf("Listing remote agents for user %s", userId)

	// Read all agents from BigTable
	bigtableAgents, err := readAllRemoteAgentsForUser(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, getAgentDeletionTTLDays(s.featureFlagHandle))
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agents")
		return nil, err
	}

	// Create a map to store all agents (from both workspaces and BigTable)
	agentMap := make(map[string]*remoteagentsproto.Agent)

	// First, add all agents from BigTable to the map
	for id, agent := range bigtableAgents {
		agentMap[id] = agent
	}
	// Return only the agents from BigTable
	agents := make([]*remoteagentsproto.Agent, 0, len(bigtableAgents))
	for _, agent := range bigtableAgents {
		if agentStatusNotShownSet[agent.Status] {
			continue
		}
		agents = append(agents, agent)
	}

	max_num_agents_for_user, err := max_remote_agents_per_user.Get(s.featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get maximum number of agents per user")
		return nil, err
	}
	max_num_active_agents_for_user, err := max_active_remote_agents_per_user.Get(s.featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get maximum number of active agents per user")
		return nil, err
	}
	return &remoteagentsproto.ListAgentsResponse{
		Agents:          agents,
		MaxAgents:       int32(max_num_agents_for_user),
		MaxActiveAgents: int32(max_num_active_agents_for_user),
	}, nil
}

// ListAgentsStream streams agent list updates with real-time updates.
func (s *RemoteAgentsServer) ListAgentsStream(req *remoteagentsproto.ListAgentsStreamRequest, stream remoteagentsproto.RemoteAgents_ListAgentsStreamServer) error {
	ctx := stream.Context()
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, "")
	if err != nil {
		return err
	}

	log.Ctx(ctx).Debug().Msgf("Starting agent list stream for user %s", userId)

	// Use the agent list streamer to handle the streaming
	return s.agentListStreamer.StreamAgentList(ctx, stream, req, userId, claims.TenantID, requestContext)
}

// Chat sends a chat request to the agent.
func (s *RemoteAgentsServer) Chat(ctx context.Context, req *remoteagentsproto.ChatRequest) (*remoteagentsproto.ChatResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}
	if err := checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Chatting to remote agent for user %s", userId)
	// Update last_user_update_received_at
	agentEntity, err := readRemoteAgentEntity(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to read agent from BigTable, skipping last_user_update_timestamp update")
	}
	if agentEntity.Status.Status == remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION {
		return nil, status.Errorf(codes.FailedPrecondition, "Agent is in pending deletion")
	}
	agentEntity.Status.LastUserUpdateReceivedAt = timestamppb.New(nowFunc().In(time.UTC))
	err = agentEntity.AtomicSave(ctx, requestContext, s.bigtableProxyClient, claims.TenantID)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to update agent last_user_update_received_at field")
		// Don't fail the request if we can't update the last_user_update_received_at field
	}

	// Publish request to request insights
	requestEvent := ripublisher.NewRequestEvent()
	requestEvent.Event = &riproto.RequestEvent_RemoteAgentsChatRequest{
		RemoteAgentsChatRequest: &riproto.RemoteAgentsChatRequest{
			Request: req,
		},
	}
	if err := s.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), getTenantInfo(claims), requestEvent); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish request event to request insights for Chat")
	}

	// Resume the agent if it's paused
	if err := s.resumeAgentWorkspaceWithMaxActiveCheck(ctx, requestContext, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to resume agent")
		return nil, err
	}

	// Get a new sequence ID for this update
	sequenceID, err := retryWithExponentialBackoff[uint32](ctx, maxExponentialBackoffAttempts, func() (uint32, error) {
		return readAndIncrementUpdateSequenceID(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId)
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get sequence ID for pending agent update")
		return nil, status.Error(codes.Internal, "Internal service error")
	}

	err = writeAgentChatRequest(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId, req.RequestDetails)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to write pending agent chat request")
		return nil, status.Error(codes.Internal, "Internal service error")
	}

	// Publish a notification about the new pending update
	PublishPendingUpdateNotification(ctx, requestContext, s.memstoreClient, req.RemoteAgentId, sequenceID)
	response := &remoteagentsproto.ChatResponse{}

	// Publish response to request insights
	responseEvent := ripublisher.NewRequestEvent()
	responseEvent.Event = &riproto.RequestEvent_RemoteAgentsChatResponse{
		RemoteAgentsChatResponse: &riproto.RemoteAgentsChatResponse{
			Response: response,
		},
	}
	if err := s.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), getTenantInfo(claims), responseEvent); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish response event to request insights for Chat")
	}

	return response, nil
}

// AgentHistoryStream streams agent history from the agent with incremental updates.
func (s *RemoteAgentsServer) AgentHistoryStream(req *remoteagentsproto.AgentHistoryStreamRequest, stream remoteagentsproto.RemoteAgents_AgentHistoryStreamServer) error {
	ctx := stream.Context()
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return err
	}
	if err := checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		return err
	}

	// Change the HasUpdates field to false to indicate that the user is viewing the agent
	agentEntity, err := readRemoteAgentEntity(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agent config from BigTable")
		return err
	}
	if agentEntity.Status.Status == remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION {
		return status.Errorf(codes.FailedPrecondition, "Agent is in pending deletion")
	}
	if agentEntity.Status.HasUpdates == true {
		agentEntity.Status.HasUpdates = false
		err = agentEntity.AtomicSave(ctx, requestContext, s.bigtableProxyClient, claims.TenantID)
		if err != nil {
			log.Ctx(ctx).Debug().Err(err).Msg("Failed to update agent has_updates field")
			// Don't fail the request if we can't update the has_updates field,
			// it's likely that someone else already did this
		}
	}

	// Use the agent history streamer to handle the streaming
	return s.agentHistoryStreamer.StreamAgentHistory(ctx, stream, req, userId, claims.TenantID, requestContext)
}

// WorkspaceStream streams workspace updates from the server with incremental updates.
func (s *RemoteAgentsServer) WorkspaceStream(req *remoteagentsproto.WorkspaceStreamRequest, stream remoteagentsproto.RemoteAgents_WorkspaceStreamServer) error {
	ctx := stream.Context()
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return err
	}
	if err := checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		return err
	}

	if claims.ExpiresAt != nil {
		// Cancel the context when the token expires, since the JWT will be
		// invalid and requests will fail after that point anyway
		tokenExpiration := claims.ExpiresAt.Time
		var cancel func()
		ctx, cancel = context.WithDeadline(ctx, tokenExpiration)
		defer cancel()
	}

	// Use the workspace streamer to handle the streaming
	return s.workspaceStreamer.StreamWorkspaceUpdates(ctx, stream, req, userId, claims.TenantID, requestContext)
}

// ChatHistory gets chat history from the agent.
func (s *RemoteAgentsServer) ChatHistory(ctx context.Context, req *remoteagentsproto.ChatHistoryRequest) (*remoteagentsproto.ChatHistoryResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}
	if err := checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		return nil, err
	}

	log.Ctx(ctx).Debug().Msgf("Getting remote agent chat history for user %s", userId)

	// TODO(mike): Temporary hack, remove once client change is in
	maxExchangeToReturn := uint32(defaultChatHistoryExchangeToReturn)
	if req.LastProcessedSequenceId == 0 {
		maxExchangeToReturn = 10_000
	}
	agentEntity, err := readRemoteAgentEntity(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId)
	if err != nil || agentEntity == nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to read agent from BigTable")
		return nil, err
	}
	// TODO(mike): remove this in a week
	if agentEntity.Status.Status == remoteagentsproto.AgentStatus_AGENT_STATUS_STARTING {
		log.Ctx(ctx).Debug().Msgf("Agent is starting and not running yet, skipping reading exchanges")
		publicAgent, err := agentEntity.ToPublicAgent(getAgentDeletionTTLDays(s.featureFlagHandle))
		if err != nil {
			return nil, err
		}
		return &remoteagentsproto.ChatHistoryResponse{
			Agent: publicAgent,
		}, nil
	}
	if agentEntity.Status.Status == remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION {
		return nil, status.Errorf(codes.FailedPrecondition, "Agent is in pending deletion")
	}
	exchanges, err := readChatHistory(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId, maxExchangeToReturn, req.LastProcessedSequenceId)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			// Log canceled errors at debug level, since those are from the user
			// and can happen often
			log.Ctx(ctx).Debug().Err(err).Msgf("Failed to read chat history from BigTable", req.RemoteAgentId)
		} else {
			log.Ctx(ctx).Warn().Err(err).Msgf("Failed to read chat history from BigTable", req.RemoteAgentId)
		}
		return nil, err
	}

	// Set has_updates to false since the user is viewing the agent
	agentEntity.Status.HasUpdates = false

	err = agentEntity.AtomicSave(ctx, requestContext, s.bigtableProxyClient, claims.TenantID)
	if err != nil {
		log.Ctx(ctx).Debug().Err(err).Msg("Failed to update agent has_updates field")
		// Don't fail the request if we can't update the has_updates field, it's
		// likely that someone else already did this
	}

	publicAgent, err := agentEntity.ToPublicAgent(getAgentDeletionTTLDays(s.featureFlagHandle))
	if err != nil {
		return nil, err
	}
	return &remoteagentsproto.ChatHistoryResponse{
		ChatHistory: exchanges,
		Agent:       publicAgent,
	}, nil
}

// InterruptAgent interrupts an agent.
func (s *RemoteAgentsServer) InterruptAgent(ctx context.Context, req *remoteagentsproto.InterruptAgentRequest) (*remoteagentsproto.InterruptAgentResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}
	err = checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Interrupting remote agent for user %s", userId)

	// Publish request to request insights
	requestEvent := ripublisher.NewRequestEvent()
	requestEvent.Event = &riproto.RequestEvent_RemoteAgentsInterruptRequest{
		RemoteAgentsInterruptRequest: &riproto.RemoteAgentsInterruptRequest{
			Request: proto.Clone(req).(*remoteagentsproto.InterruptAgentRequest),
		},
	}
	if err := s.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), getTenantInfo(claims), requestEvent); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish response event to request insights for InterruptAgent")
	}

	// Get a new sequence ID for this update
	sequenceID, err := retryWithExponentialBackoff[uint32](ctx, maxExponentialBackoffAttempts, func() (uint32, error) {
		return readAndIncrementUpdateSequenceID(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId)
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get sequence ID for pending agent update")
		return nil, status.Error(codes.Internal, "Internal service error")
	}

	err = writeAgentInterrupt(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to write pending agent interrupt")
		return nil, err
	}

	// Publish a notification about the new pending update
	PublishPendingUpdateNotification(ctx, requestContext, s.memstoreClient, req.RemoteAgentId, sequenceID)
	response := &remoteagentsproto.InterruptAgentResponse{}

	// Publish response to request insights
	responseEvent := ripublisher.NewRequestEvent()
	responseEvent.Event = &riproto.RequestEvent_RemoteAgentsInterruptResponse{
		RemoteAgentsInterruptResponse: &riproto.RemoteAgentsInterruptResponse{
			Response: proto.Clone(response).(*remoteagentsproto.InterruptAgentResponse),
		},
	}
	if err := s.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), getTenantInfo(claims), responseEvent); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish response event to request insights for InterruptAgent")
	}

	return response, nil
}

// DeleteAgent deletes an agent.
func (s *RemoteAgentsServer) DeleteAgent(ctx context.Context, req *remoteagentsproto.DeleteAgentRequest) (*remoteagentsproto.DeleteAgentResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}
	err = checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Deleting remote agent for user %s", userId)

	// Publish request to request insights
	requestEvent := ripublisher.NewRequestEvent()
	requestEvent.Event = &riproto.RequestEvent_RemoteAgentsDeleteRequest{
		RemoteAgentsDeleteRequest: &riproto.RemoteAgentsDeleteRequest{
			Request: proto.Clone(req).(*remoteagentsproto.DeleteAgentRequest),
		},
	}
	if err := s.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), getTenantInfo(claims), requestEvent); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish request event to request insights for DeleteAgent")
	}

	// Only remove the agent from the user mapping, let cron job clean up the rest
	log.Ctx(ctx).Info().Msgf("Marking remote agent %s for pending deletion for user %s", req.RemoteAgentId, userId)
	_, err = retryWithExponentialBackoff(ctx, maxExponentialBackoffAttempts, func() (bool, error) {
		err := pendingDeleteAgent(ctx, requestContext, s.bigtableProxyClient, s.rwc, claims.TenantID, userId, req.RemoteAgentId)
		if err != nil {
			return false, err
		}
		return true, nil
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to mark agent for pending deletion")
		return nil, err
	}

	// Publish agent list notification for agent deletion
	PublishAgentListNotification(ctx, requestContext, s.memstoreClient, userId, req.RemoteAgentId, "deleted")
	log.Ctx(ctx).Info().Msgf("Successfully marked remote agent %s for pending deletion", req.RemoteAgentId)
	response := &remoteagentsproto.DeleteAgentResponse{}

	// Publish response to request insights
	responseEvent := ripublisher.NewRequestEvent()
	responseEvent.Event = &riproto.RequestEvent_RemoteAgentsDeleteResponse{
		RemoteAgentsDeleteResponse: &riproto.RemoteAgentsDeleteResponse{
			Response: proto.Clone(response).(*remoteagentsproto.DeleteAgentResponse),
		},
	}
	if err := s.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), getTenantInfo(claims), responseEvent); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish response event to request insights for DeleteAgent")
	}
	return response, nil
}

// move remote agent to pending deletion status
// agent in pending deletion status won't shown to the user
func pendingDeleteAgent(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, rwc ws.RemoteWorkspaceController, tenantID, userId, agentId string) error {
	log.Ctx(ctx).Info().Msgf("Marking remote agent %s for pending deletion for user %s", agentId, userId)
	agentEntity, err := readRemoteAgentEntity(ctx, requestContext, bigtableProxyClient, tenantID, agentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agent from BigTable")
		return err
	}
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION
	agentEntity.Status.PendingDeletionAt = timestamppb.New(nowFunc().In(time.UTC))
	err = agentEntity.AtomicSave(ctx, requestContext, bigtableProxyClient, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to AtomicSave agent status to BigTable")
		return err
	}
	return nil
}

// delete the remote agent for good
func deleteAgent(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, rwc ws.RemoteWorkspaceController, tenantID, userId, agentId, reason string) error {
	log.Ctx(ctx).Info().Str("reason", reason).Msgf("Performing delete for remote agent %s for user %s", agentId, userId)
	agentEntity, err := readRemoteAgentEntity(ctx, requestContext, bigtableProxyClient, tenantID, agentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agent from BigTable")
		return err
	}
	// check if the agent is in pending deletion status
	if agentEntity.Status.Status != remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION {
		err = fmt.Errorf("Agent %s is not in pending deletion status, but in %s", agentId, agentEntity.Status.Status.String())
		log.Ctx(ctx).Error().Err(err).Msg("Failed to delete agent")
		return err
	}
	// grab lock on agent to prevent race on k8s resource
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_DELETING
	err = agentEntity.AtomicSave(ctx, requestContext, bigtableProxyClient, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to AtomicSave agent workspace status to BigTable")
		return err
	}
	// delete k8s resources
	err = retryWithExponentialBackoffAnyError(ctx, maxExponentialBackoffAttempts, func() error {
		return rwc.DeleteWorkspace(ctx, userId, agentId)
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to delete workspace")
		return err
	}
	// delete the row from BigTable
	_, err = retryWithExponentialBackoff(ctx, maxExponentialBackoffAttempts, func() (bool, error) {
		err := deleteRemoteAgentFromBigTable(ctx, requestContext, bigtableProxyClient, tenantID, userId, agentId)
		if err != nil {
			return false, err
		}
		return true, nil
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to delete agent from BigTable")
		return err
	}
	log.Ctx(ctx).Info().Msgf("Successfully deleted remote agent %s", agentId)
	return nil
}

func (s *RemoteAgentsServer) AddSSHKey(ctx context.Context, req *remoteagentsproto.AddSSHKeyRequest) (*remoteagentsproto.AddSSHKeyResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}
	if err := checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Adding SSH key for remote agent for user %s", userId)

	// Publish request to request insights
	requestEvent := ripublisher.NewRequestEvent()
	requestEvent.Event = &riproto.RequestEvent_RemoteAgentsAddSshKeyRequest{
		RemoteAgentsAddSshKeyRequest: &riproto.RemoteAgentsAddSSHKeyRequest{
			Request: proto.Clone(req).(*remoteagentsproto.AddSSHKeyRequest),
		},
	}
	if err := s.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), getTenantInfo(claims), requestEvent); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish response event to request insights for AddSSHKey")
	}

	// config, err := readRemoteAgentConfig(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId)
	agentEntity, err := readRemoteAgentEntity(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agent config from BigTable")
		return nil, err
	}

	keysToAdd := req.PublicKeys
	keysToAddParsed := make([]ssh.PublicKey, len(keysToAdd))
	for i, key := range keysToAdd {
		if pubKey, _, _, _, err := ssh.ParseAuthorizedKey([]byte(key)); err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to validate SSH key at index %d", i)
			return nil, status.Errorf(codes.InvalidArgument, "Invalid SSH key: %s", err.Error())
		} else {
			keysToAddParsed[i] = pubKey
		}
	}

	if agentEntity.Config.SshConfig == nil {
		// TODO: setup SSH and get hostname and config options
		agentEntity.Config.SshConfig = &remoteagentsproto.AgentSSHConfig{}
	} else {
		keysToSkip := make(map[string]struct{})
		for _, existingKey := range agentEntity.Config.SshConfig.PublicKeys {
			existingPubKey, _, _, _, err := ssh.ParseAuthorizedKey([]byte(existingKey))
			if err != nil {
				// This should never happen...
				log.Ctx(ctx).Error().Err(err).Msgf("Failed to validate existing SSH key %s", existingKey)
				continue
			}
			for i, newKey := range keysToAddParsed {
				if existingPubKey.Type() == newKey.Type() && bytes.Equal(existingPubKey.Marshal(), newKey.Marshal()) {
					log.Ctx(ctx).Debug().Msgf("SSH key %s already exists, skipping", newKey)
					keysToSkip[keysToAdd[i]] = struct{}{}
					break
				}
			}
		}
		keysToAdd = slices.DeleteFunc(keysToAdd, func(key string) bool {
			_, ok := keysToSkip[key]
			return ok
		})
	}
	agentEntity.Config.SshConfig.PublicKeys = append(agentEntity.Config.SshConfig.PublicKeys, keysToAdd...)

	buf, err := protojson.MarshalOptions{UseProtoNames: true, EmitDefaultValues: true, UseEnumNumbers: true}.Marshal(agentEntity.Config.SshConfig)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to marshal ssh keys request")
		return nil, err
	}

	// Add a few retries here in case k8s networking is not up yet. If this
	// passes, then we can try appending SSH keys and getting the SSH config.
	checkHealth := func(ctx context.Context, rws ws.RemoteWorkspace) error {
		return retryWithLinearBackoffAnyError(ctx, maxResumeLinearBackoffAttempts, resumeLinearBackoffDuration, func() error {
			_, _, err := rws.HealthCheck(ctx)
			if err != nil {
				if errors.Is(err, context.Canceled) {
					return err
				}
				return status.Errorf(codes.DeadlineExceeded, "Failed to see running workspace after retries")
			}
			return nil
		})
	}

	if rws, err := s.rwc.GetWorkspace(ctx, userId, req.RemoteAgentId); err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get workspace for remote agent %s for user %s", req.RemoteAgentId, userId)
		return nil, err
	} else if err := checkHealth(ctx, rws); err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to check health of remote agent %s for user %s", req.RemoteAgentId, userId)
		return nil, err
	} else if _, err := rws.AppendSSHKeys(ctx, buf); err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to append ssh keys to remote agent %s for user %s", req.RemoteAgentId, userId)
		return nil, err
	} else if cfg, err := rws.SSHConfig(ctx); err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get ssh config from remote agent %s for user %s", req.RemoteAgentId, userId)
		return nil, err
	} else {
		agentEntity.Config.SshConfig.Hostname = cfg.Hostname
		agentEntity.Config.SshConfig.SshConfigOptions = cfg.SshConfigOptions
	}

	err = agentEntity.AtomicSave(ctx, requestContext, s.bigtableProxyClient, claims.TenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to write agent config to BigTable")
		return nil, err
	}

	response := &remoteagentsproto.AddSSHKeyResponse{
		SshConfig: agentEntity.Config.SshConfig,
	}

	// Publish response to request insights
	responseEvent := ripublisher.NewRequestEvent()
	responseEvent.Event = &riproto.RequestEvent_RemoteAgentsAddSshKeyResponse{
		RemoteAgentsAddSshKeyResponse: &riproto.RemoteAgentsAddSSHKeyResponse{
			Response: proto.Clone(response).(*remoteagentsproto.AddSSHKeyResponse),
		},
	}
	if err := s.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), getTenantInfo(claims), responseEvent); err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish response event to request insights for AddSSHKey")
	}

	return response, nil
}

func (s *RemoteAgentsServer) ResumeAgent(ctx context.Context, req *remoteagentsproto.ResumeAgentRequest) (*remoteagentsproto.ResumeAgentResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}
	if err := checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Resuming remote agent for user %s", userId)

	err = s.resumeAgentWorkspaceWithMaxActiveCheck(ctx, requestContext, claims.TenantID, userId, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}

	return &remoteagentsproto.ResumeAgentResponse{}, nil
}

// resumeAgentWorkspace with maxActiveAgents check
func (s *RemoteAgentsServer) resumeAgentWorkspaceWithMaxActiveCheck(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID, userId, remoteAgentId string) error {
	numActiveAgents, err := s.numActiveAgents(ctx, requestContext, tenantID, userId)
	if err != nil {
		return err
	}
	max_num_active_agents_per_user, err := max_active_remote_agents_per_user.Get(s.featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to get maximum number of active agents per user")
	}

	// if agent is paused and we are at the limit, return an error
	if numActiveAgents >= max_num_active_agents_per_user {
		return status.Errorf(codes.ResourceExhausted, "You have reached the maximum number of active agents (%d). Please pause some existing agents before creating new ones.", max_num_active_agents_per_user)
	}

	return s.resumeAgentWorkspace(ctx, requestContext, tenantID, userId, remoteAgentId)
}

// resumeAgentWorkspace handles the core logic of resuming an agent workspace
func (s *RemoteAgentsServer) resumeAgentWorkspace(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID, userId, remoteAgentId string) error {
	startTime := time.Now()

	// Read the latest agent entity
	agentEntity, err := readRemoteAgentEntity(ctx, requestContext, s.bigtableProxyClient, tenantID, remoteAgentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agent config from BigTable")
		return err
	}

	// TODO: This is for backwards compatibility, remove after all agents
	// created before 5/18/25 have been deleted or migrated
	if agentEntity.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_UNSPECIFIED {
		log.Ctx(ctx).Info().Msg("Agent workspace is unspecified, assuming it is running")
		agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING
		err = agentEntity.AtomicSave(ctx, requestContext, s.bigtableProxyClient, tenantID)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to write agent status to BigTable")
			return err
		}
	}

	// If the agent is already running or resuming, return success
	if agentEntity.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING {
		log.Ctx(ctx).Info().Msg("Agent workspace is already running")
		return nil
	}

	// Rather than using a health check, which may block on the k8s apiserver or
	// the k8s service, wait for the beachhead to update its status by calling
	// ReportStatus, which should set the workspace status to running.
	isWorkspaceRunning := func() error {
		agentEntity, err := readRemoteAgentEntity(ctx, requestContext, s.bigtableProxyClient, tenantID, remoteAgentId)
		if err != nil {
			return err
		}
		if agentEntity.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING {
			return nil
		}
		return fmt.Errorf("workspace is not running")
	}

	if agentEntity.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING {
		log.Ctx(ctx).Info().Msg("Agent workspace is in resuming")
		err = retryWithLinearBackoffAnyError(ctx, maxResumeLinearBackoffAttempts, time.Duration(maxResumeLinearBackoffAttempts), isWorkspaceRunning)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to see running workspace after retries")
			if errors.Is(err, context.Canceled) {
				return err
			}
			return status.Errorf(codes.DeadlineExceeded, "Failed to see running workspace after retries")
		}
		// The workspace status is already running thanks to ReportStatus(), nothing more to update
		return nil
	}

	// Check if the agent is in a valid state to be resumed
	if agentEntity.Status.WorkspaceStatus != remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED {
		log.Ctx(ctx).Error().Msg("Failed to resume agent workspace, agent is not paused")
		return status.Errorf(codes.FailedPrecondition, "Failed to resume agent workspace, agent is not paused")
	}

	// Set status to RESUMING
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING
	agentEntity.Status.LastSshActivityObservedAt = timestamppb.New(nowFunc().In(time.UTC))
	// Alaways allow the user to ssh back. We only want to auto pause for inactivity
	// i.e. user forgot to disconnect an open ssh session.
	// If we don't reset this timestamp and hard ddl kicks in, the user will resume
	// ssh into the pod but soon get kicked out because the pod is auto paused.
	agentEntity.Status.LastUserUpdateReceivedAt = timestamppb.New(nowFunc().In(time.UTC))
	err = agentEntity.AtomicSave(ctx, requestContext, s.bigtableProxyClient, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to write agent status to BigTable")
		return err
	}

	// Re-read the agent entity to ensure the statusCell is fresh for the next AtomicSave
	agentEntity, err = readRemoteAgentEntity(ctx, requestContext, s.bigtableProxyClient, tenantID, remoteAgentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to re-read agent entity before resuming workspace")
		return err
	}

	// Resume the workspace
	err = retryWithExponentialBackoffAnyError(ctx, maxExponentialBackoffAttempts, func() error {
		return s.rwc.ResumeWorkspace(ctx, userId, remoteAgentId)
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to resume workspace after retries")
		RemoteAgentsResumeLatencyHistogram.WithLabelValues("resume_workspace_timeout").Observe(time.Since(startTime).Seconds())
		return err
	}

	// Check if the agent is working
	err = retryWithLinearBackoffAnyError(ctx, maxResumeLinearBackoffAttempts, resumeLinearBackoffDuration, isWorkspaceRunning)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to see running workspace after retries")
		RemoteAgentsResumeLatencyHistogram.WithLabelValues("running_timeout").Observe(time.Since(startTime).Seconds())
		if errors.Is(err, context.Canceled) {
			return err
		}
		return status.Errorf(codes.DeadlineExceeded, "Failed to see running workspace after retries")
	}

	// The workspace status is already running thanks to ReportStatus(), nothing more to update
	RemoteAgentsResumeLatencyHistogram.WithLabelValues("success").Observe(time.Since(startTime).Seconds())
	return nil
}

func pauseAgentWorkspace(ctx context.Context, agentEntity *AgentEntity, requestContext *requestcontext.RequestContext, tenantID string, rwc ws.RemoteWorkspaceController, bigtableProxyClient bigtableproxy.BigtableProxyClient, reason string) error {
	log.Ctx(ctx).Info().Msgf("Pausing remote agent %s due to %s", agentEntity.AgentID, reason)

	// TODO: This is for backwards compatibility, remove after all agents
	// created before 5/18/25 have been deleted or migrated
	if agentEntity.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_UNSPECIFIED {
		log.Ctx(ctx).Info().Msg("Agent workspace is unspecified, assuming it is running")
		agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING
		err := agentEntity.AtomicSave(ctx, requestContext, bigtableProxyClient, tenantID)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to write agent status to BigTable")
			return err
		}
	}

	// If the agent is already paused, return success
	if agentEntity.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED {
		return nil
	}

	// Check if the agent is in a valid state to be paused
	if agentEntity.Status.Status != remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE && agentEntity.Status.Status != remoteagentsproto.AgentStatus_AGENT_STATUS_FAILED {
		log.Ctx(ctx).Error().Msgf("Failed to pause agent because it is not idle or failed but is %s", agentEntity.Status.Status.String())
		return status.Errorf(codes.FailedPrecondition, "Agent is not idle or failed")
	}
	// Check if the workspace is in a valid state to be paused
	if agentEntity.Status.WorkspaceStatus != remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING {
		log.Ctx(ctx).Error().Msg("Failed to pause agent because workspace is not running")
		return status.Errorf(codes.FailedPrecondition, "Workspace is not running")
	}

	// Set status to PAUSING
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
	err := agentEntity.AtomicSave(ctx, requestContext, bigtableProxyClient, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to write agent status to BigTable while setting to PAUSING")
		return err
	}

	// Stop the workspace
	// TODO: add retries with any error
	stopWorkspaceCallError := rwc.StopWorkspace(ctx, agentEntity.Config.UserId, agentEntity.AgentID)
	if stopWorkspaceCallError != nil {
		log.Ctx(ctx).Error().Err(stopWorkspaceCallError).Msg("Failed to stop workspace.")
		return stopWorkspaceCallError
	}

	// Update the status to paused
	// Read the latest agent entity to get the current cell value
	agentEntity, err = readRemoteAgentEntity(ctx, requestContext, bigtableProxyClient, tenantID, agentEntity.AgentID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agent config from BigTable")
		return err
	}

	if agentEntity.Status.WorkspaceStatus != remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING {
		return status.Errorf(codes.FailedPrecondition, "Agent status changed before we could update it to PAUSED")
	}

	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED
	err = agentEntity.AtomicSave(ctx, requestContext, bigtableProxyClient, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to update agent status to PAUSED")
		return err
	}
	return nil
}

func (s *RemoteAgentsServer) PauseAgent(ctx context.Context, req *remoteagentsproto.PauseAgentRequest) (*remoteagentsproto.PauseAgentResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}
	if err := checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Pausing remote agent for user %s", userId)

	// Read the latest agent entity
	agentEntity, err := readRemoteAgentEntity(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agent config from BigTable")
		return nil, err
	}
	if err := pauseAgentWorkspace(ctx, agentEntity, requestContext, claims.TenantID, s.rwc, s.bigtableProxyClient, "user request"); err != nil {
		return nil, err
	}
	response := &remoteagentsproto.PauseAgentResponse{}
	return response, nil
}

func (s *RemoteAgentsServer) WorkspaceReportStatus(ctx context.Context, req *remoteagentsproto.WorkspaceReportStatusRequest) (*remoteagentsproto.WorkspaceReportStatusResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}
	if err := checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		return nil, err
	}

	log.Ctx(ctx).Debug().Msgf("Reporting remote agent status for user %s", userId)

	var lastSSHActivityUpdate *timestamppb.Timestamp
	if req.HasActiveSshConnection {
		lastSSHActivityUpdate = timestamppb.New(nowFunc().In(time.UTC))
	}

	_, err = retryWithExponentialBackoff(ctx, maxExponentialBackoffAttempts, func() (bool, error) {
		agentEntity, err := readRemoteAgentEntity(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId)
		if err != nil {
			return false, err
		}
		// TODO: can remove the UNSPECIFIED case after 3ish weeks
		if agentEntity.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING || agentEntity.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_UNSPECIFIED {
			// If the workspace status isn't already running, update it since we know we are done resuming now that the beachhead is sending updates
			agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING
		}

		statusChanged := agentEntity.Status.Status != req.Status
		if statusChanged {
			// Only do this if the status has changed
			agentEntity.Status.HasUpdates = true
		}
		agentEntity.Status.Status = req.Status

		// Yes, this will have some time delay from when the agent actually saw
		// the SSH connection, but it should be small
		if lastSSHActivityUpdate != nil &&
			(agentEntity.Status.LastSshActivityObservedAt == nil ||
				agentEntity.Status.LastSshActivityObservedAt.AsTime().Before(lastSSHActivityUpdate.AsTime())) {
			agentEntity.Status.LastSshActivityObservedAt = lastSSHActivityUpdate
		}

		err = agentEntity.AtomicSave(ctx, requestContext, s.bigtableProxyClient, claims.TenantID)
		if err != nil {
			return false, err
		}

		// Publish agent list notification if status changed
		if statusChanged {
			PublishAgentListNotification(ctx, requestContext, s.memstoreClient, userId, req.RemoteAgentId, "updated")
		}

		return true, nil
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to write agent status to BigTable")
		return nil, err
	}

	log.Ctx(ctx).Debug().Msgf("Successfully updated status for agent %s to %s", req.RemoteAgentId, req.Status.String())
	return &remoteagentsproto.WorkspaceReportStatusResponse{}, nil
}

func (s *RemoteAgentsServer) WorkspaceReportChatHistory(ctx context.Context, req *remoteagentsproto.WorkspaceReportChatHistoryRequest) (*remoteagentsproto.WorkspaceReportChatHistoryResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}
	if err := checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		return nil, err
	}

	maxSeqID := uint32(0)
	for _, exchange := range req.ChatHistory {
		if exchange.SequenceId > maxSeqID {
			maxSeqID = exchange.SequenceId
		}
	}
	log.Ctx(ctx).Info().Msgf("Reporting remote agent chat history for user %s with max sequence ID %d and %d exchanges", userId, maxSeqID, len(req.ChatHistory))

	if len(req.ChatHistory) == 0 {
		log.Ctx(ctx).Warn().Msg("No chat history to write")
		return &remoteagentsproto.WorkspaceReportChatHistoryResponse{}, nil
	}
	lastSeqID := uint32(0)
	// verify sequence id is monotonic in the received chunks and all greater than 0
	for _, exchange := range req.ChatHistory {
		if exchange.SequenceId <= uint32(lastSeqID) {
			log.Ctx(ctx).Error().Msgf("Sequence ID must be monotonic and greater than 0. Expected > %d, got %d", lastSeqID, exchange.SequenceId)
			return nil, status.Error(codes.InvalidArgument, "sequence ID must be monotonic and greater than 0")
		}
		lastSeqID = exchange.SequenceId
	}

	// Write chat history to bigtable
	_, err = retryWithExponentialBackoff(ctx, maxExponentialBackoffAttempts, func() (bool, error) {
		err := writeChatHistoryChunk(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId, req.ChatHistory)
		if err != nil {
			return false, err
		}
		return true, nil
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to write chat history to BigTable")
		return nil, err
	}

	// Find the highest sequence ID for notification
	var highestSequenceID uint32 = 0
	for _, exchange := range req.ChatHistory {
		if exchange.SequenceId > highestSequenceID {
			highestSequenceID = exchange.SequenceId
		}
	}

	// Publish a notification about the new chat history
	PublishAgentHistoryNotification(ctx, requestContext, s.memstoreClient, req.RemoteAgentId, highestSequenceID)

	// Set has_updates to true to indicate there are unread updates
	_, err = retryWithExponentialBackoff(ctx, maxExponentialBackoffAttempts, func() (bool, error) {
		agentEntity, err := readRemoteAgentEntity(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId)
		if err != nil {
			return false, err
		}
		agentEntity.Status.HasUpdates = true
		agentEntity.Status.LastAgentUpdateReceivedAt = timestamppb.New(nowFunc().In(time.UTC))
		err = agentEntity.AtomicSave(ctx, requestContext, s.bigtableProxyClient, claims.TenantID)
		if err != nil {
			return false, err
		}
		return true, nil
	})
	if err != nil {
		log.Ctx(ctx).Debug().Err(err).Msg("Failed to update agent has_updates field")
		// Don't fail the request if we can't update the has_updates field, it's
		// likely that someone else already did this
	}

	log.Ctx(ctx).Debug().Msgf("Successfully wrote %d chat history exchanges for agent %s", len(req.ChatHistory), req.RemoteAgentId)
	return &remoteagentsproto.WorkspaceReportChatHistoryResponse{}, nil
}

func (s *RemoteAgentsServer) WorkspacePollUpdate(ctx context.Context, req *remoteagentsproto.WorkspacePollUpdateRequest) (*remoteagentsproto.WorkspacePollUpdateResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}
	if err := checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		return nil, err
	}

	log.Ctx(ctx).Debug().Msgf("Polling for remote agent update for user %s", userId)

	allPendingUpdates, err := readPendingAgentUpdates(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId)
	updatesGreaterThanLastSeen := make([]*remoteagentsproto.WorkspaceUpdate, 0)
	for _, update := range allPendingUpdates {
		if update.SequenceId > req.LastProcessedSequenceId {
			updatesGreaterThanLastSeen = append(updatesGreaterThanLastSeen, update.Update)
		}
	}
	return &remoteagentsproto.WorkspacePollUpdateResponse{
		Updates: updatesGreaterThanLastSeen,
	}, nil
}

func (s *RemoteAgentsServer) WorkspaceReportSetupLogs(ctx context.Context, req *remoteagentsproto.WorkspaceReportSetupLogsRequest) (*remoteagentsproto.WorkspaceReportSetupLogsResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}
	if err := checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Reporting remote agent setup logs for user %s", userId)

	// Write the logs to bigtable
	err = writeRemoteAgentLogs(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId, req.WorkspaceSetupStatus, true)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to write workspace logs to BigTable")
		return nil, err
	}
	agentEntity, err := readRemoteAgentEntity(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agent config from BigTable")
		return nil, err
	}
	err = agentEntity.AtomicSave(ctx, requestContext, s.bigtableProxyClient, claims.TenantID)
	if err != nil {
		log.Ctx(ctx).Debug().Err(err).Msg("Failed to update agent has_updates field")
		// Don't fail the request if we can't update the has_updates field, it's
		// likely that someone else already did this
	}
	return &remoteagentsproto.WorkspaceReportSetupLogsResponse{}, nil
}

func (s *RemoteAgentsServer) WorkspaceLogs(ctx context.Context, req *remoteagentsproto.WorkspaceLogsRequest) (*remoteagentsproto.WorkspaceLogsResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}
	if err := checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		return nil, err
	}

	log.Ctx(ctx).Debug().Msgf("Getting remote agent workspace logs for user %s", userId)

	var lastProcessedStep, lastProcessedSequenceId uint32

	// Only use provided values if they exist
	if req.LastProcessedStep != nil {
		lastProcessedStep = *req.LastProcessedStep
	}
	if req.LastProcessedSequenceId != nil {
		lastProcessedSequenceId = *req.LastProcessedSequenceId
	}

	logs, err := readRemoteAgentWorkspaceLogs(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId, lastProcessedStep, lastProcessedSequenceId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read workspace logs from BigTable")
		return nil, err
	}
	agentEntity, err := readRemoteAgentEntity(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, req.RemoteAgentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agent config from BigTable")
		return nil, err
	}
	err = agentEntity.AtomicSave(ctx, requestContext, s.bigtableProxyClient, claims.TenantID)
	if err != nil {
		log.Ctx(ctx).Debug().Err(err).Msg("Failed to update agent has_updates field")
		// Don't fail the request if we can't update the has_updates field, it's
		// likely that someone else already did this
	}

	// This is the first time we are looking at this, so we need to add the container step
	// or we have already written the container step but there could be new updates
	if (len(logs.WorkspaceSetupStatus.Steps) == 0 && lastProcessedStep == 0 && lastProcessedSequenceId == 0) ||
		(len(logs.WorkspaceSetupStatus.Steps) == 1 && logs.WorkspaceSetupStatus.Steps[0].StepNumber == 0) {
		// Get the container logs
		containerStep, err := getContainerSetupStep(ctx, s, userId, req.RemoteAgentId)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to get container logs")
			// Create a default container step if we couldn't get the real one
			containerStep = &remoteagentsproto.WorkspaceSetupStep{
				StepDescription: "Container Setup Status",
				Logs:            "Starting ...",
				Status:          remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_RUNNING,
				SequenceId:      0,
				StepNumber:      0,
			}
		}
		logs.WorkspaceSetupStatus.Steps = []*remoteagentsproto.WorkspaceSetupStep{containerStep}
		// Only write logs if we have a valid container step
		if containerStep != nil {
			writeRemoteAgentLogs(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId, logs.WorkspaceSetupStatus, false)
		}
		return logs, nil
	}

	// Otherwise, we have already written the container step, so just return the logs
	return logs, nil
}

// checkAndSetupRequestContext checks the request context and sets up the
// logging context, and then returns that info to be used or forwarded to future
// requests.
func checkAndSetupRequestContext(ctx context.Context, agentID string) (context.Context, string, *auth.AugmentClaims, *requestcontext.RequestContext, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, "", nil, nil, fmt.Errorf("Error getting request context: %s", err)
	}
	ctx = requestContext.AnnotateLogContext(ctx)
	claims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, "", nil, nil, fmt.Errorf("Unauthenticated")
	}
	ctx = claims.AnnotateLogContext(ctx)
	opaqueUserID := claims.GetOpaqueUserID()
	if opaqueUserID == nil {
		return nil, "", nil, nil, fmt.Errorf("User ID is required")
	}
	if agentID != "" {
		ctx = log.Ctx(ctx).With().Str("remote_agent_id", agentID).Logger().WithContext(ctx)
	}
	return ctx, opaqueUserID.GetUserId(), claims, requestContext, nil
}

func checkUserAuthorization(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userID, agentID string) error {
	if agentID == "" {
		return status.Error(codes.InvalidArgument, "agent ID is required")
	}

	// Get the agent config from BigTable
	agentEntity, err := readRemoteAgentEntity(ctx, requestContext, bigtableProxyClient, tenantID, agentID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agent config from BigTable")
		if strings.Contains(err.Error(), "not found") {
			return status.Errorf(codes.NotFound, "Agent %s not found", agentID)
		}
		return status.Errorf(codes.Internal, "Failed to read agent config")
	}

	// TODO(mike): remove this after dogfood is off dev
	// this is a hack to allow user to access agents that were created
	// before we added user ID to the agent config
	if agentEntity.Config.UserId == "" {
		return nil
	}

	// Check if the user is authorized to access this agent
	if agentEntity.Config.UserId != userID {
		log.Ctx(ctx).Warn().Msgf("User %s is not authorized to access agent %s", userID, agentID)
		return status.Errorf(codes.PermissionDenied, "User %s is not authorized to access agent %s, authorized user is %s", userID, agentID, agentEntity.Config.UserId)
	}

	return nil
}

// newSHA256UUID creates a UUID based on SHA-256 hash of namespace UUID and name.
func newSHA256UUID(namespace uuid.UUID, requestID, opaqueUserID []byte) uuid.UUID {
	h := sha256.New()
	h.Write(namespace[:])
	h.Write(requestID)
	h.Write(opaqueUserID)
	sum := h.Sum(nil)

	var uuid [16]byte
	copy(uuid[:], sum[:16])

	uuid[6] = (uuid[6] & 0x0f) | 0x50 // Set version to 5 (SHA-1 in standard, but we're using SHA-256)
	uuid[8] = (uuid[8] & 0x3f) | 0x80 // Set variant to RFC 4122

	return uuid
}

// deriveAgentIDFromRequestID derives a deterministic agent ID from a request ID
// using SHA-256 for enhanced security.
//
// this function ensures that the same request ID always produces the same agent ID, which allows
// the system to deduplicate repeated requests and prevent duplicate agent creation.
//
// The function uses SHA-256 (rather than SHA-1) for better collision resistance, making it
// computationally infeasible for a user to generate a request ID that would produce
// a specific target agent ID, even if they know the namespace.
//
// The generated agent ID is deterministic from the request ID and opaque user ID, but
// random in nature, ensuring uniqueness and avoiding collisions
func deriveAgentIDFromRequestID(requestID string, opaqueUserID string) (string, error) {
	// Parse the request ID as a UUID
	requestUUID, err := uuid.Parse(requestID)
	if err != nil {
		return "", fmt.Errorf("invalid request ID: %w", err)
	}

	// Generate a name-based UUID using SHA-256
	agentID := newSHA256UUID(remoteAgentUUIDNamespace, []byte(requestUUID.String()), []byte(opaqueUserID))

	return agentID.String(), nil
}

// stripInstructionFlags parses "instruction flags" of the form `_flags:{key[=value],...}` from text nodes
// AND removes them.
func stripInstructionFlags(nodes ...*chatproto.ChatRequestNode) map[string]string {
	iflags := map[string]string{}

	for _, node := range nodes {
		if node.Type != chatproto.ChatRequestNodeType_TEXT {
			continue
		}

		content := node.GetTextNode().GetContent()
		pfx := "_flags:{"

		if start := len(pfx) + strings.Index(content, pfx); start < len(pfx) {
			continue
		} else if end := start + strings.Index(content[start:], "}"); end < start {
			continue
		} else {
			// First split by commas.
			flags := strings.Split(content[start:end], ",")

			for _, flag := range flags {
				// Then split into a key and optional value (defaults to `true`).
				if parts := strings.SplitN(flag, "=", 2); len(parts) == 2 {
					iflags[strings.TrimSpace(parts[0])] = strings.TrimSpace(parts[1])
				} else {
					iflags[strings.TrimSpace(flag)] = "true"
				}
			}

			// Strip flags from instructions.
			prefix := ""
			if start > 0 {
				prefix = strings.TrimSpace(content[:start-len(pfx)])
			}
			suffix := ""
			if end+1 < len(content) {
				suffix = strings.TrimSpace(content[end+1:])
				if prefix != "" {
					suffix = " " + suffix
				}
			}
			node.GetTextNode().Content = prefix + suffix
		}
	}

	return iflags
}

// Extract the tenant info for use in request insights from auth claims
func getTenantInfo(claims *auth.AugmentClaims) *riproto.TenantInfo {
	return &riproto.TenantInfo{
		TenantId:   claims.TenantID,
		TenantName: claims.TenantName,
	}
}

func parseGithubURL(githubUrl string) (repoOwner string, repoName string, err error) {
	// Parse the URL
	u, err := url.Parse(githubUrl)
	if err != nil {
		return "", "", err
	}

	// Check if the URL is a GitHub URL
	if u.Host != "github.com" && u.Host != "www.github.com" {
		return "", "", fmt.Errorf("not a github URL")
	}

	// Trim the leading and trailing slashes from the path, as well as a .git suffix
	path := u.Path
	path = strings.TrimPrefix(strings.TrimSuffix(path, "/"), "/")
	path = strings.TrimSuffix(path, ".git")

	// Split the path into components
	pathComponents := strings.Split(path, "/")
	if len(pathComponents) != 2 {
		return "", "", fmt.Errorf("invalid github URL")
	}

	repoOwner, repoName = pathComponents[0], pathComponents[1]

	return repoOwner, repoName, nil
}

// This gets the container logs for the agent mainly to help the client understand the progress
func getContainerSetupStep(ctx context.Context, s *RemoteAgentsServer, userID, agentID string) (*remoteagentsproto.WorkspaceSetupStep, error) {
	var displayLog string
	workspace, err := s.rwc.GetWorkspace(ctx, userID, agentID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get workspace for agent %s", agentID)
		return nil, err
	}
	podStatus := workspace.PodStatus(ctx)
	if podStatus == "" {
		displayLog = "Starting ..."
	} else if podStatus == "Pending" {
		displayLog = "Allocating resources ..."
	} else if podStatus == "Running" {
		displayLog = "Setting up workspace ..."
	} else {
		displayLog = podStatus
	}
	return &remoteagentsproto.WorkspaceSetupStep{
		StepDescription: "Container Setup Status",
		Logs:            displayLog,
		Status:          remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_RUNNING,
		SequenceId:      0,
		StepNumber:      0,
	}, nil
}

func getAgentDeletionTTLDays(featureFlagHandle featureflags.FeatureFlagHandle) uint32 {
	deletionTTLDays, err := autoDeleteTTLFlag.Get(featureFlagHandle)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to get auto delete TTL feature flag")
	}
	// This is deliberate friction to prevent accidental update of feature flag causing
	// all agents to be deleted
	if deletionTTLDays < 10 {
		log.Warn().Int("auto_delete_ttl_days", deletionTTLDays).Msg("Auto delete TTL is too low, using 10 days")
		deletionTTLDays = 10
	} else if deletionTTLDays > 10*365 {
		log.Warn().Int("auto_delete_ttl_days", deletionTTLDays).Msg("Auto delete TTL is too high, using 3650 days")
		deletionTTLDays = 3650
	}
	return uint32(deletionTTLDays)
}
