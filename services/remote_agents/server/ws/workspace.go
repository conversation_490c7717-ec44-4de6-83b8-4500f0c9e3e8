package ws

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"google.golang.org/protobuf/encoding/protojson"
	tspb "google.golang.org/protobuf/types/known/timestamppb"
	corev1 "k8s.io/api/core/v1"

	"github.com/augmentcode/augment/infra/lib/k8s"
	rapb "github.com/augmentcode/augment/services/remote_agents/proto"
)

// RemoteWorkspace defines the interface for interacting with a remote agent workspace
type RemoteWorkspace interface {
	// ID returns the workspace ID
	ID() string

	// AssertOwner checks if the given user is the owner of the workspace
	AssertOwner(user string) error

	// Status returns the current status of the agent in the workspace
	Status(ctx context.Context) rapb.AgentStatus

	// CreatedAt returns the creation time of the workspace
	CreatedAt() time.Time

	// Agent returns the agent configuration and status
	Agent(ctx context.Context) (*rapb.Agent, error)

	// AgentConfig returns the agent configuration
	AgentConfig() (*rapb.AgentConfig, error)

	// ServiceName returns the service name for the workspace
	ServiceName() string

	// HealthCheck performs a health check on the workspace
	HealthCheck(ctx context.Context) (string, bool, error)

	// Chat sends a chat request to the agent
	Chat(ctx context.Context, req []byte) ([]byte, error)

	// ChatHistory retrieves the chat history from the agent
	ChatHistory(ctx context.Context) ([]byte, error)

	// Interrupt sends an interrupt signal to the agent
	Interrupt(ctx context.Context) ([]byte, error)

	// Delete deletes the workspace and its resources
	Delete(ctx context.Context) error

	// ReportStatus reports the status of the workspace
	WorkspaceReportStatus(ctx context.Context) ([]byte, error)

	// AppendSSHKeys appends new SSH keys to the workspace
	AppendSSHKeys(ctx context.Context, req []byte) ([]byte, error)

	// SSHConfig returns the SSH config for the workspace
	SSHConfig(ctx context.Context) (*rapb.AgentSSHConfig, error)

	// PodStatus
	PodStatus(ctx context.Context) string
}

// RemoteWorkspaceImpl is the concrete implementation of RemoteWorkspace
type RemoteWorkspaceImpl struct {
	id  string
	sec *k8s.Secret
	sts *k8s.StatefulSet
	pod *k8s.Pod // may be nil.
	k8s *k8s.Client

	sshHN   string
	sshPort int
}

// NewRemoteWorkspace creates a new RemoteWorkspace.
func NewRemoteWorkspace(id string, sec *k8s.Secret, sts *k8s.StatefulSet, pod *k8s.Pod, k *k8s.Client, sshHN string, sshPort int) RemoteWorkspace {
	return &RemoteWorkspaceImpl{
		id:  id,
		sec: sec,
		sts: sts,
		pod: pod,
		k8s: k,

		sshHN:   sshHN,
		sshPort: sshPort,
	}
}

func (w *RemoteWorkspaceImpl) ID() string {
	return w.id
}

func (w *RemoteWorkspaceImpl) AssertOwner(user string) error {
	obj := func() *k8s.Object {
		if w.sec != nil {
			return &w.sec.Object
		}
		if w.sts != nil {
			return &w.sts.Object
		}
		if w.pod != nil {
			return &w.pod.Object
		}
		return nil
	}()
	if obj == nil {
		return fmt.Errorf("workspace %s ownership unknown", w.ID())
	}
	if obj.Label("aug.remote-agent-workspace.owner") != user {
		return fmt.Errorf("workspace %s is owned by another user", w.ID())
	}
	return nil
}

func (w *RemoteWorkspaceImpl) Status(ctx context.Context) rapb.AgentStatus {
	if w.pod == nil || w.pod.Status().Condition(corev1.PodReady).Status != corev1.ConditionTrue {
		return rapb.AgentStatus_AGENT_STATUS_STARTING
	}

	healthStatus, agentWorking, err := w.HealthCheck(ctx)
	if err != nil {
		return rapb.AgentStatus_AGENT_STATUS_FAILED
	}
	if healthStatus != "OK" {
		return rapb.AgentStatus_AGENT_STATUS_FAILED
	}

	if agentWorking {
		return rapb.AgentStatus_AGENT_STATUS_RUNNING
	}
	return rapb.AgentStatus_AGENT_STATUS_IDLE
}

func (w *RemoteWorkspaceImpl) CreatedAt() time.Time {
	if w.sec != nil {
		return w.sec.CreationTimestamp()
	}
	if w.sts != nil {
		return w.sts.CreationTimestamp()
	}
	if w.pod != nil {
		return w.pod.CreationTimestamp()
	}
	return time.Time{}
}

func (w *RemoteWorkspaceImpl) Agent(ctx context.Context) (*rapb.Agent, error) {
	cfg, err := w.AgentConfig()
	if err != nil {
		return nil, err
	}
	return &rapb.Agent{
		RemoteAgentId: w.ID(),
		Status:        w.Status(ctx),
		CreatedAt:     tspb.New(w.CreatedAt()),
		Config:        cfg,
	}, nil
}

func (w *RemoteWorkspaceImpl) AgentConfig() (*rapb.AgentConfig, error) {
	if sec := w.sec; sec != nil {
		// Try to read from the new path first
		configBytes := sec.KeyBytes("workspace-agent-config.json.gz")
		if len(configBytes) > 0 {
			return cfgFromJsonGz(configBytes)
		}

		// Fall back to the old path for backward compatibility
		// TODO(mike): remove after two weeks
		configBytes = sec.KeyBytes("agent-config.json.gz")
		if len(configBytes) > 0 {
			return cfgFromJsonGz(configBytes)
		}

		return nil, fmt.Errorf("neither workspace-agent-config.json.gz nor agent-config.json.gz found")
	}
	return nil, fmt.Errorf("secret not found")
}

func (w *RemoteWorkspaceImpl) ServiceName() string {
	return buildName(w.ID())
}

func (w *RemoteWorkspaceImpl) HealthCheck(ctx context.Context) (string, bool, error) {
	name := fmt.Sprintf("%s:%d", w.ServiceName(), httpPort) // NOTE(mattm): Switch to port name?
	buf, err := w.k8s.ServiceGET(name).Suffix("health").DoRaw(ctx)
	if err != nil {
		return "", false, err
	}

	health := struct {
		Status       string `json:"status"`
		AgentWorking bool   `json:"agent_working"`
	}{}
	if err := json.Unmarshal(buf, &health); err != nil {
		return "", false, err
	}
	return health.Status, health.AgentWorking, nil
}

func (w *RemoteWorkspaceImpl) Chat(ctx context.Context, req []byte) ([]byte, error) {
	name := fmt.Sprintf("%s:%d", w.ServiceName(), httpPort) // NOTE(mattm): Switch to port name?
	return w.k8s.ServicePOST(name).
		Suffix("chat").
		Body(req).
		SetHeader("Content-Type", "application/json").
		DoRaw(ctx)
}

func (w *RemoteWorkspaceImpl) ChatHistory(ctx context.Context) ([]byte, error) {
	name := fmt.Sprintf("%s:%d", w.ServiceName(), httpPort) // NOTE(mattm): Switch to port name?
	return w.k8s.ServiceGET(name).Suffix("chat-history").DoRaw(ctx)
}

func (w *RemoteWorkspaceImpl) Interrupt(ctx context.Context) ([]byte, error) {
	name := fmt.Sprintf("%s:%d", w.ServiceName(), httpPort) // NOTE(mattm): Switch to port name?
	return w.k8s.ServicePOST(name).Suffix("interrupt").DoRaw(ctx)
}

// Delete deletes the workspace and its resources
func (w *RemoteWorkspaceImpl) Delete(ctx context.Context) error {
	if w.sec != nil {
		// The secret is owner of the other resources.
		return w.k8s.DeleteSecret(ctx, w.sec.Name())
	} else {
		return nil
	}
}

func (w *RemoteWorkspaceImpl) WorkspaceReportStatus(ctx context.Context) ([]byte, error) {
	name := fmt.Sprintf("%s:%d", w.ServiceName(), httpPort) // NOTE(mattm): Switch to port name?
	return w.k8s.ServiceGET(name).Suffix("workspace-report-status").DoRaw(ctx)
}

func (w *RemoteWorkspaceImpl) AppendSSHKeys(ctx context.Context, req []byte) ([]byte, error) {
	name := fmt.Sprintf("%s:%d", w.ServiceName(), httpPort) // NOTE(mattm): Switch to port name?
	return w.k8s.ServicePOST(name).
		Suffix("ssh/authorized_keys").
		Body(req).
		SetHeader("Content-Type", "application/json").
		DoRaw(ctx)
}

func (w *RemoteWorkspaceImpl) SSHConfig(ctx context.Context) (*rapb.AgentSSHConfig, error) {
	// Get any SSH config from workspace (in particular, the `User` option)

	name := fmt.Sprintf("%s:%d", w.ServiceName(), httpPort) // NOTE(mattm): Switch to port name?
	buf, err := w.k8s.ServiceGET(name).Suffix("ssh/config").DoRaw(ctx)
	if err != nil {
		return nil, err
	}
	wsSSHCfg := &rapb.AgentSSHConfig{}
	if err := protojson.Unmarshal(buf, wsSSHCfg); err != nil {
		return nil, err
	}

	// Append those to the options we know about locally. SSH options are first-come-first-serve, and we
	// want the local options to take precedence over what we get from the workspace.

	cfg := w.localSSHConfig()
	cfg.SshConfigOptions = append(cfg.SshConfigOptions, wsSSHCfg.SshConfigOptions...)

	return cfg, nil
}

func (w *RemoteWorkspaceImpl) PodStatus(ctx context.Context) string {
	if w.pod == nil {
		return ""
	}
	return w.pod.Status().String()
}

func (w *RemoteWorkspaceImpl) localSSHConfig() *rapb.AgentSSHConfig {
	sclient := fmt.Sprintf(`s_client -connect %s:"%%p" -quiet -verify_return_error -servername "%%h"`, w.sshHN)
	return &rapb.AgentSSHConfig{
		Hostname: fmt.Sprintf("%s.%s", w.ServiceName(), w.k8s.Namespace()),
		SshConfigOptions: []*rapb.SSHConfigOption{
			{Key: "Port", Value: fmt.Sprintf("%d", w.sshPort)},
			{Key: "ProxyCommand", Value: "openssl " + sclient},
			{Key: "TCPKeepAlive", Value: "no"},
			{Key: "ServerAliveInterval", Value: "5"},
			{Key: "IgnoreUnknown", Value: "X-Augment-*"},
			{Key: "X-Augment-Proxy-Hostname", Value: w.sshHN},
			{Key: "X-Augment-Proxy-OpenSSL-Command", Value: sclient},
		},
	}
}
