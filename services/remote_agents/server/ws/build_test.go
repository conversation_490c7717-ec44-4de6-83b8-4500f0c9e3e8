package ws

import (
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/augmentcode/augment/infra/lib/k8s"
	rapb "github.com/augmentcode/augment/services/remote_agents/proto"
)

func TestBuildNames(t *testing.T) {
	t.<PERSON>()
	tests := map[string]struct {
		inID         string
		wName        string
		wPodName     string
		wServiceName string
	}{
		"basic": {
			inID:         "id0",
			wName:        "raws-id0",
			wPodName:     "raws-id0-0",
			wServiceName: "raws-id0",
		},
		"empty": {
			inID:         "",
			wName:        "raws-",
			wPodName:     "raws--0",
			wServiceName: "raws-",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.<PERSON>()

			if got, want := buildName(tc.inID), tc.wName; got != want {
				t.Errorf("buildName(): got %v, want %v.", got, want)
			}
			if got, want := buildPodName(tc.inID), tc.wPodName; got != want {
				t.Errorf("buildPodName(): got %v, want %v.", got, want)
			}
			if got, want := buildServiceName(tc.inID), tc.wServiceName; got != want {
				t.Errorf("buildServiceName(): got %v, want %v.", got, want)
			}
		})
	}
}

func TestBuildNamespace(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inNamespace string
		wantJSON    string
	}{
		"basic": {
			inNamespace: "ns0",
			wantJSON: `{
				"apiVersion": "v1",
				"kind": "Namespace",
				"metadata": {
					"name": "ns0",
					"labels": {
						"aug.remote-agent-workspace": "true"
					}
				}

			}`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got := buildNamespace(tc.inNamespace)
			want := k8s.NewNamespaceApplyConfig("")
			if err := want.FromJSON(tc.wantJSON); err != nil {
				t.Fatalf("error parsing wantJSON: %v.", err)
			}
			if diff := cmp.Diff(want.Raw(), got.Raw()); diff != "" {
				t.Errorf("Namespace: -want +got:\n%s", diff)
			}
		})
	}
}

func TestBuildServiceAccount(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inNamespace string
		wantJSON    string
	}{
		"basic": {
			inNamespace: "ns0",
			wantJSON: `{
				"apiVersion": "v1",
				"kind": "ServiceAccount",
				"metadata": {
					"namespace": "aug-system",
					"name": "raws-ns0-sa",
					"labels": {
						"aug.remote-agent-workspace": "true"
					}
				}

			}`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got := buildServiceAccount(tc.inNamespace)
			want := k8s.NewServiceAccountApplyConfig("", "")
			if err := want.FromJSON(tc.wantJSON); err != nil {
				t.Fatalf("error parsing wantJSON: %v.", err)
			}
			if diff := cmp.Diff(want.Raw(), got.Raw()); diff != "" {
				t.Errorf("ServiceAccount: -want +got:\n%s", diff)
			}
		})
	}
}

func TestBuildServiceAccountToken(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inNamespace string
		wantJSON    string
	}{
		"basic": {
			inNamespace: "ns0",
			wantJSON: `{
				"apiVersion": "v1",
				"kind": "Secret",
				"metadata": {
					"namespace": "aug-system",
					"name": "raws-ns0-sa-tok",
					"labels": {
						"aug.remote-agent-workspace": "true"
					},
					"annotations": {
						"kubernetes.io/service-account.name": "raws-ns0-sa"
					}
				},
				"type": "kubernetes.io/service-account-token"

			}`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got := buildServiceAccountToken(tc.inNamespace)
			want := k8s.NewSecretApplyConfig("", "")
			if err := want.FromJSON(tc.wantJSON); err != nil {
				t.Fatalf("error parsing wantJSON: %v.", err)
			}
			if diff := cmp.Diff(want.Raw(), got.Raw()); diff != "" {
				t.Errorf("Secret: -want +got:\n%s", diff)
			}
		})
	}
}

func TestBuildRoleBinding(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inNamespace string
		inSA        string
		wantJSON    string
	}{
		"basic": {
			inNamespace: "ns0",
			inSA:        "sa0",
			wantJSON: `{
				"apiVersion": "rbac.authorization.k8s.io/v1",
				"kind": "RoleBinding",
				"metadata": {
					"namespace": "ns0",
					"name": "raws-ns0-role-binding",
					"labels": {
						"aug.remote-agent-workspace": "true"
					}
				},
				"roleRef": {
					"apiGroup": "rbac.authorization.k8s.io",
					"kind": "ClusterRole",
					"name": "rwc-namespace-controller"
				},
				"subjects": [
					{
						"kind": "ServiceAccount",
						"name": "sa0",
						"namespace": "aug-system"
					}
				]
			}`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got := buildRoleBinding(tc.inNamespace, tc.inSA)
			want := k8s.NewRoleBindingApplyConfig("", "")
			if err := want.FromJSON(tc.wantJSON); err != nil {
				t.Fatalf("error parsing wantJSON: %v.", err)
			}
			if diff := cmp.Diff(want.Raw(), got.Raw()); diff != "" {
				t.Errorf("RoleBinding: -want +got:\n%s", diff)
			}
		})
	}
}

func TestBuildStatefulSet(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inImage     string
		inNamespace string
		inUser      string
		inID        string
		inSecName   string
		inSvcName   string
		inRootVS    string
		inSshPort   int
		inPoolGrps  []string
		inFlags     map[string]string
		wantJSON    string
	}{
		"full": {
			inImage:     "r0-docker.pkg.dev/p0/agents-e0-r0/augment-remote-agent-virt:t0",
			inNamespace: "ns0",
			inUser:      "user0",
			inID:        "id0",
			inSecName:   "sec0",
			inSvcName:   "svc0",
			inSshPort:   2233,
			inPoolGrps:  []string{"g0", "g1"},
			wantJSON: `{
				"apiVersion": "apps/v1",
				"kind": "StatefulSet",
				"metadata": {
					"name": "raws-id0",
					"namespace": "ns0",
					"labels": {
						"aug.remote-agent-workspace": "true",
						"aug.remote-agent-workspace.id": "id0",
						"aug.remote-agent-workspace.owner": "user0",
						"aug.remote-agent-workspace.pool-group": "g0.g1"
					}
				},
				"spec": {
					"replicas": 1,
					"serviceName": "svc0",
					"selector": {
						"matchLabels": {
							"aug.remote-agent-workspace.id": "id0"
						}
					},
					"persistentVolumeClaimRetentionPolicy": {
						"whenDeleted": "Delete",
						"whenScaled": "Retain"
					},
					"template": {
						"metadata": {
							"labels": {
								"aug.remote-agent-workspace": "true",
								"aug.remote-agent-workspace.id": "id0",
								"aug.remote-agent-workspace.owner": "user0",
								"aug.remote-agent-workspace.pool-group": "g0.g1"
							}
						},
						"spec": {
							"enableServiceLinks": false,
							"affinity": {
								"nodeAffinity": {
									"requiredDuringSchedulingIgnoredDuringExecution": {
										"nodeSelectorTerms": [
											{
												"matchExpressions": [
													{
														"key": "raws.augmentcode.com/pool-group",
														"operator": "In",
														"values": ["g0", "g1"]
													}
												]
											}
										]
									}
								},
								"podAffinity": {
									"preferredDuringSchedulingIgnoredDuringExecution": [
										{
											"weight": 50,
											"podAffinityTerm": {
												"labelSelector": {
													"matchExpressions": [
														{
															"key": "aug.remote-agent-workspace",
															"operator": "Exists"
														}
													]
												},
												"namespaceSelector": {},
												"topologyKey": "kubernetes.io/hostname"
											}
										}
									]
								}
							},
							"tolerations": [
								{
									"effect": "NoSchedule",
									"key": "raws.augmentcode.com/pool-group",
									"operator": "Equal",
									"value": "g0"
								},
								{
									"effect": "NoSchedule",
									"key": "raws.augmentcode.com/pool-group",
									"operator": "Equal",
									"value": "g1"
								}
							],
							"containers": [
								{
									"name": "raws-id0",
									"image": "r0-docker.pkg.dev/p0/agents-e0-r0/augment-remote-agent-virt:t0",
									"imagePullPolicy": "Always",
									"securityContext": {
										"privileged": true,
										"runAsUser": 0,
										"runAsGroup": 0
									},
									"resources": {
										"limits": {
											"cpu": "4500m",
											"memory": "18G"
										},
										"requests": {
											"cpu": "4500m",
											"memory": "18G"
										}
									},
									"command": ["/root/startup.sh"],
									"args": [
										"--id=id0",
										"--port=9999",
										"--ssh-port=2233",
										"--persist=/mnt/persist"
									],
									"volumeMounts": [
										{
											"name": "workspace-config",
											"mountPath": "/run/secrets/workspace-config"
										}
									],
									"volumeDevices": [
										{
											"name": "vm-root",
											"devicePath": "/dev/vm-root"
										}
									],
									"startupProbe": {
										"httpGet": {
											"scheme": "HTTP",
											"path": "/health",
											"port": 9999
										},
										"initialDelaySeconds": 10,
										"timeoutSeconds": 1,
										"periodSeconds": 5,
										"failureThreshold": 120,
										"successThreshold": 1
									}
								}
							],
							"volumes": [
								{
									"name": "workspace-config",
									"secret": {
										"secretName": "sec0"
									}
								}
							]
						}
					},
					"volumeClaimTemplates": [
						{
							"apiVersion": "v1",
							"kind": "PersistentVolumeClaim",
							"metadata": {
								"name": "vm-root",
								"namespace": "ns0",
								"labels": {
									"aug.remote-agent-workspace": "true",
									"aug.remote-agent-workspace.id": "id0",
									"aug.remote-agent-workspace.owner": "user0",
									"aug.remote-agent-workspace.pool-group": "g0.g1"
								}
							},
							"spec": {
								"volumeMode": "Block",
								"accessModes": [
									"ReadWriteOnce"
								],
								"resources": {
									"requests": {
										"storage": "64Gi"
									}
								}
							}
						}
					]
				}
			}`,
		},
		"full-from-snapshot": {
			inImage:     "r0-docker.pkg.dev/p0/agents-e0-r0/augment-remote-agent-virt:t0",
			inNamespace: "ns0",
			inUser:      "user0",
			inID:        "id0",
			inSecName:   "sec0",
			inSvcName:   "svc0",
			inRootVS:    "root-vs0",
			inSshPort:   2233,
			inPoolGrps:  []string{"g0", "g1"},
			wantJSON: `{
				"apiVersion": "apps/v1",
				"kind": "StatefulSet",
				"metadata": {
					"name": "raws-id0",
					"namespace": "ns0",
					"labels": {
						"aug.remote-agent-workspace": "true",
						"aug.remote-agent-workspace.id": "id0",
						"aug.remote-agent-workspace.owner": "user0",
						"aug.remote-agent-workspace.pool-group": "g0.g1"
					}
				},
				"spec": {
					"replicas": 1,
					"serviceName": "svc0",
					"selector": {
						"matchLabels": {
							"aug.remote-agent-workspace.id": "id0"
						}
					},
					"persistentVolumeClaimRetentionPolicy": {
						"whenDeleted": "Delete",
						"whenScaled": "Retain"
					},
					"template": {
						"metadata": {
							"labels": {
								"aug.remote-agent-workspace": "true",
								"aug.remote-agent-workspace.id": "id0",
								"aug.remote-agent-workspace.owner": "user0",
								"aug.remote-agent-workspace.pool-group": "g0.g1"
							}
						},
						"spec": {
							"enableServiceLinks": false,
							"affinity": {
								"nodeAffinity": {
									"requiredDuringSchedulingIgnoredDuringExecution": {
										"nodeSelectorTerms": [
											{
												"matchExpressions": [
													{
														"key": "raws.augmentcode.com/pool-group",
														"operator": "In",
														"values": ["g0", "g1"]
													}
												]
											}
										]
									}
								},
								"podAffinity": {
									"preferredDuringSchedulingIgnoredDuringExecution": [
										{
											"weight": 50,
											"podAffinityTerm": {
												"labelSelector": {
													"matchExpressions": [
														{
															"key": "aug.remote-agent-workspace",
															"operator": "Exists"
														}
													]
												},
												"namespaceSelector": {},
												"topologyKey": "kubernetes.io/hostname"
											}
										}
									]
								}
							},
							"tolerations": [
								{
									"effect": "NoSchedule",
									"key": "raws.augmentcode.com/pool-group",
									"operator": "Equal",
									"value": "g0"
								},
								{
									"effect": "NoSchedule",
									"key": "raws.augmentcode.com/pool-group",
									"operator": "Equal",
									"value": "g1"
								}
							],
							"containers": [
								{
									"name": "raws-id0",
									"image": "r0-docker.pkg.dev/p0/agents-e0-r0/augment-remote-agent-virt:t0",
									"imagePullPolicy": "Always",
									"securityContext": {
										"privileged": true,
										"runAsUser": 0,
										"runAsGroup": 0
									},
									"resources": {
										"limits": {
											"cpu": "4500m",
											"memory": "18G"
										},
										"requests": {
											"cpu": "4500m",
											"memory": "18G"
										}
									},
									"command": ["/root/startup.sh"],
									"args": [
										"--id=id0",
										"--port=9999",
										"--ssh-port=2233",
										"--persist=/mnt/persist"
									],
									"volumeMounts": [
										{
											"name": "workspace-config",
											"mountPath": "/run/secrets/workspace-config"
										}
									],
									"volumeDevices": [
										{
											"name": "vm-root",
											"devicePath": "/dev/vm-root"
										}
									],
									"startupProbe": {
										"httpGet": {
											"scheme": "HTTP",
											"path": "/health",
											"port": 9999
										},
										"initialDelaySeconds": 10,
										"timeoutSeconds": 1,
										"periodSeconds": 5,
										"failureThreshold": 120,
										"successThreshold": 1
									}
								}
							],
							"volumes": [
								{
									"name": "workspace-config",
									"secret": {
										"secretName": "sec0"
									}
								}
							]
						}
					},
					"volumeClaimTemplates": [
						{
							"apiVersion": "v1",
							"kind": "PersistentVolumeClaim",
							"metadata": {
								"name": "vm-root",
								"namespace": "ns0",
								"labels": {
									"aug.remote-agent-workspace": "true",
									"aug.remote-agent-workspace.id": "id0",
									"aug.remote-agent-workspace.owner": "user0",
									"aug.remote-agent-workspace.pool-group": "g0.g1"
								}
							},
							"spec": {
								"volumeMode": "Block",
								"accessModes": [
									"ReadWriteOnce"
								],
								"resources": {
									"requests": {
										"storage": "64Gi"
									}
								},
								"dataSource": {
									"apiGroup": "snapshot.storage.k8s.io",
									"kind": "VolumeSnapshot",
									"name": "root-vs0"
								}
							}
						}
					]
				}
			}`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got := buildStatefulSet(tc.inImage, tc.inNamespace, tc.inUser, tc.inID, tc.inSecName, tc.inSvcName, tc.inRootVS, tc.inSshPort, tc.inPoolGrps, tc.inFlags)
			want := k8s.NewStatefulSetApplyConfig("", "")
			if err := want.FromJSON(tc.wantJSON); err != nil {
				t.Fatalf("error parsing wantJSON: %v.", err)
			}
			if diff := cmp.Diff(want.Raw(), got.Raw()); diff != "" {
				t.Errorf("StatefulSet: -want +got:\n%s", diff)
			}
		})
	}
}

func TestBuildService(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inNamespace string
		inUser      string
		inID        string
		inSshPort   int
		wantJSON    string
	}{
		"basic": {
			inNamespace: "ns0",
			inUser:      "user0",
			inID:        "id0",
			inSshPort:   2233,
			wantJSON: `{
				"apiVersion": "v1",
				"kind": "Service",
				"metadata": {
					"name": "raws-id0",
					"namespace": "ns0",
					"labels": {
						"aug.remote-agent-workspace": "true",
						"aug.remote-agent-workspace.id": "id0",
						"aug.remote-agent-workspace.owner": "user0"
					}
				},
				"spec": {
					"type": "ClusterIP",
					"selector": {
						"aug.remote-agent-workspace.id": "id0"
					},
					"publishNotReadyAddresses": true,
					"ports": [
						{
							"name": "http",
							"port": 9999
						},
						{
							"name": "ssh",
							"port": 2233
						}
					]
				}
			}`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got := buildService(tc.inNamespace, tc.inUser, tc.inID, tc.inSshPort)
			want := k8s.NewServiceApplyConfig("", "")
			if err := want.FromJSON(tc.wantJSON); err != nil {
				t.Fatalf("error parsing wantJSON: %v.", err)
			}
			if diff := cmp.Diff(want.Raw(), got.Raw()); diff != "" {
				t.Errorf("Service: -want +got:\n%s", diff)
			}
		})
	}
}

func TestBuildConfigSecret(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inNamespace     string
		inUser          string
		inID            string
		inCfg           *rapb.WorkspaceAgentConfig
		inRevTunKubecfg string
		wantJSON        string
	}{
		"with_user_token": {
			inNamespace: "ns0",
			inUser:      "user0",
			inID:        "id0",
			inCfg: &rapb.WorkspaceAgentConfig{
				RemoteAgentId:   "id0",
				Config:          nil,
				GithubUsername:  "ghusername0",
				GithubUserToken: "ghtoken1",
				GithubName:      "ghname0",
				GithubEmail:     "<EMAIL>",
			},
			wantJSON: `{
				"apiVersion": "v1",
				"kind": "Secret",
				"metadata": {
					"name": "raws-id0",
					"namespace": "ns0",
					"labels": {
						"aug.remote-agent-workspace": "true",
						"aug.remote-agent-workspace.id": "id0",
						"aug.remote-agent-workspace.owner": "user0"
					}
				},
				"data": {
					"workspace-agent-config.json.gz": "PHNuaXA+",
					"github-user-token": "Z2h0b2tlbjE=",
					"github-username": "Z2h1c2VybmFtZTA="
				}
			}`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()

			got, gotErr := buildConfigSecret(tc.inNamespace, tc.inUser, tc.inID, tc.inCfg, tc.inRevTunKubecfg)
			if gotErr != nil {
				t.Fatalf("buildConfigSecret(): %v.", gotErr)
			}
			got.Raw().WithData(map[string][]byte{
				"workspace-agent-config.json.gz": []byte("<snip>"),
			})
			want := k8s.NewSecretApplyConfig("", "")
			if err := want.FromJSON(tc.wantJSON); err != nil {
				t.Fatalf("error parsing wantJSON: %v.", err)
			}
			if diff := cmp.Diff(want.Raw(), got.Raw()); diff != "" {
				t.Errorf("Secret: -want +got:\n%s", diff)
			}
		})
	}
}

func TestWorkspaceAgentConfigToJsonGzAndBack(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inCfg   *rapb.WorkspaceAgentConfig
		wantErr string
	}{
		"nil": {
			inCfg: nil,
		},
		"empty": {
			inCfg: &rapb.WorkspaceAgentConfig{},
		},
		"with_agent_id": {
			inCfg: &rapb.WorkspaceAgentConfig{RemoteAgentId: "test-agent-id"},
		},
		"with_config": {
			inCfg: &rapb.WorkspaceAgentConfig{Config: &rapb.AgentConfig{}},
		},
		"with_both": {
			inCfg: &rapb.WorkspaceAgentConfig{RemoteAgentId: "test-agent-id", Config: &rapb.AgentConfig{}},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()

			// Forward.

			jsonGz, gotErr := workspaceAgentConfigToJsonGz(tc.inCfg)
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			// Reverse, if no error.

			if gotErr != nil {
				return
			}
			reconstructed, err := workspaceAgentConfigFromJsonGz(jsonGz)
			if err != nil {
				t.Fatalf("workspaceAgentConfigFromJsonGz(): %v", err)
			}
			if diff := cmp.Diff(tc.inCfg, reconstructed, protocmp.Transform(), protocmp.IgnoreEmptyMessages()); diff != "" {
				t.Errorf("Round-trip conversion: -original +reconstructed:\n%s", diff)
			}
		})
	}
}
