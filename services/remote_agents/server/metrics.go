package main

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	RemoteAgentsScanJobRuns = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "au_remote_agents_scan_job_runs",
			Help: "Total number of scan remote agents job runs.",
		},
	)

	RemoteAgentsScanJobDurationSeconds = promauto.NewHistogram(
		prometheus.HistogramOpts{
			Name: "au_remote_agents_scan_job_duration_seconds",
			Help: "Duration of scan remote agents job in seconds.",
			// Buckets for durations from 1 minute to 10 minutes, then +Inf
			Buckets: []float64{60, 120, 180, 240, 300, 360, 420, 480, 540, 600},
		},
	)

	RemoteAgentsCronJobPausedTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_remote_agents_cron_job_paused_total",
			Help: "Total number of remote agents pause attempts by the cron job.",
		},
		[]string{"tenant_id", "ttl_type"},
	)

	RemoteAgentsCronJobPauseErrors = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_remote_agents_cron_job_pause_errors",
			Help: "Total number of remote agents pause errors by the cron job.",
		},
		[]string{"tenant_id", "ttl_type"},
	)

	RemoteAgentsCronJobDeletedTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_remote_agents_cron_job_deleted_total",
			Help: "Total number of remote agents deleted by the cron job.",
		},
		[]string{"tenant_id", "delete_reason", "deletion_status"},
	)

	RemoteAgentsCronJobDeleteErrors = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_remote_agents_cron_job_delete_errors",
			Help: "Total number of remote agents delete errors by the cron job.",
		},
		[]string{"tenant_id", "delete_reason"},
	)

	// We're using a gauge metric for the total number of remote agents since
	// we wouldn't know when the cron job started and stopped if we were using
	// a counter.
	RemoteAgentsCronJobObservedTotal = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "au_remote_agents_cron_job_observed_total",
			Help: "Total number of remote agents observed by the cron job.",
		},
		[]string{"tenant_id", "agent_status", "ws_status"},
	)

	RemoteAgentsResumeLatencyHistogram = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "au_remote_agents_resume_latency_seconds",
			Help:    "Histogram of the latency, in seconds, of resume requests for remote agents, this only tracks the time taken to resume the agent from paused to running, it does not include other transitions from resuming to running etc.",
			Buckets: []float64{1, 5, 10, 15, 20, 25, 30, 35, 45, 50, 55, 60, 120},
		},
		[]string{"status"},
	)
)
