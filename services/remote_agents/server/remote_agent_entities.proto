syntax = "proto3";

package remote_agent_config_entities;

import "google/protobuf/timestamp.proto";
import "services/remote_agents/remote_agents.proto";

// For the "Config" column family in bigtable
// Contains the user-defined config for the agent
// Schema: RemoteAgent#<remote_agent_id> -> serialized AgentConfig
message AgentConfig {
  remote_agents.AgentConfig input_config = 1;
  string user_id = 2; // User ID stored internally but not exposed in API
  google.protobuf.Timestamp created_at = 3; // Creation timestamp

  remote_agents.AgentSSHConfig ssh_config = 4;

  // The k8s namespace the agent's workspace was created in. Currently this
  // happens to match the tenant's namespace at the time this agent was created.
  string namespace = 5;
  // Reserved for backward compatibility
  reserved 6, 7;
}

// [Transactional] For the "Status" column family in bigtable
// ** This is the ONLY column we run transactions on. **
// Contains runtime info about the agent, but not outputs like chat dialog, diffs, etc.
// Schema: RemoteAgent#<remote_agent_id> -> serialized AgentStatus
message AgentStatus {
  remote_agents.AgentStatus status = 1;
  // Whether the agent has unread updates
  bool has_updates = 2;
  // TODO: container name?
  // The workspaces that this agent been assigned to.
  // The most recent workspace is the last element in the list.
  Workspace workspace_assignments = 3;
  // The workspace status
  remote_agents.WorkspaceStatus workspace_status = 4;

  // The last time an update from the agent was received
  google.protobuf.Timestamp last_agent_update_received_at = 5;

  // The last time an update from user was received
  google.protobuf.Timestamp last_user_update_received_at = 6;

  // The last time a resume call is made or ssh activity is detected
  // this time is respected by the soft auto pause threshold
  google.protobuf.Timestamp last_ssh_activity_observed_at = 7;

  // The pending deletion timestamp
  google.protobuf.Timestamp pending_deletion_at = 8;
}

// For the "Output" column family in bigtable
// Contains the chat dialog, diffs, etc.
// Schema: RemoteAgent#<remote_agent_id>#output#<reversed_timestamp> -> serialized AgentOutput
// This is split into multiple rows since exchanges and diffs can be very large
message AgentOutput {
  repeated remote_agents.ChatHistoryExchange exchange = 1;
}

// For the "UserMapping" column family in bigtable
// Contains the mapping between a user and their agent IDs
// Schema: UserAgentMapping#<opaque_user_id> -> serialized UserAgentMapping
message UserAgentMapping {
  repeated string agent_ids = 1;
}

// PendingAgentUpdate is used to store updates that need to be sent to the agent
// but haven't been delivered yet.
message PendingAgentUpdate {
  // The sequence ID for this update
  uint32 sequence_id = 1;
  // The update ready to be sent in the response format
  remote_agents.WorkspaceUpdate update = 2;
}

// UpdateSequenceID is used to store and atomically increment the sequence ID for an agent
// Schema: UpdateSequenceID#<remote_agent_id> -> serialized UpdateSequenceID
message UpdateSequenceID {
  // The current sequence ID value
  uint32 value = 1;
}

message Workspace {
  string id = 1;
}
