local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  local cronJobNotCompletingSpec = {
    displayName: 'Remote Agents Cron Job Not Completing',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        max by (namespace) (time() - timestamp(au_remote_agents_cron_job_observed_total)) > 1200
      |||,
    },
  };

  local cronJobNotPausingAgentsSpec = {
    displayName: 'Remote Agents Cron Job Not Pausing Agents',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        absent(sum(rate(au_remote_agents_cron_job_paused_total[1h])))
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      cronJobNotCompletingSpec,
      'remote-agents-cron-job-not-completing',
      'Remote agents cron job has not completed for more than 20 minutes in namespace %s.' % monitoringLib.label('namespace'),
      team='remote-agents'
    ),
    monitoringLib.alertPolicy(
      cloud,
      cronJobNotPausingAgentsSpec,
      'remote-agents-cron-job-not-pausing',
      'Remote agents cron job has not paused any agents for the last 1 hour.',
      team='remote-agents'
    ),
  ]
