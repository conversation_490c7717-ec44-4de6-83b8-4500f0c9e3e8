load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_binary", "go_grpc_library", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

proto_library(
    name = "remote_agent_entities_proto",
    srcs = ["remote_agent_entities.proto"],
    deps = [
        "//services/remote_agents:remote_agents_proto",
        "@protobuf//:timestamp_proto",
    ],
)

go_grpc_library(
    name = "remote_agent_entities_go_proto",
    importpath = "github.com/augmentcode/augment/services/remote_agents/server/entities_proto",
    proto = ":remote_agent_entities_proto",
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//services/chat_host:chat_host_go_proto",
        "//services/remote_agents:remote_agents_go_proto",
    ],
)

go_library(
    name = "server_lib",
    srcs = [
        "agent_history_processor.go",
        "agent_history_streamer.go",
        "agent_list_processor.go",
        "agent_list_streamer.go",
        "cron_tasks.go",
        "main.go",
        "metrics.go",
        "pubsub.go",
        "retries.go",
        "server.go",
        "singleton_task_manager.go",
        "storage.go",
        "workspace_processor.go",
        "workspace_streamer.go",
    ],
    importpath = "github.com/augmentcode/augment/services/remote_agents/server",
    visibility = ["//visibility:private"],
    deps = [
        "//base/feature_flags:feature_flags_go",
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/bigtable_proxy/client:client_go",
        "//services/chat_host:chat_host_go_proto",
        "//services/integrations/github/processor/client:client_go",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/metrics:grpc_metrics_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "//services/memstore/client:client_go",
        "//services/remote_agents:remote_agents_go_proto",
        "//services/remote_agents/server:remote_agent_entities_go_proto",
        "//services/remote_agents/server/ws",
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/publisher:publisher_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_google_uuid//:uuid",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb:go_default_library",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//rest",
        "@io_k8s_client_go//tools/leaderelection",
        "@io_k8s_client_go//tools/leaderelection/resourcelock",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_golang_x_crypto//ssh",
        "@org_golang_x_sync//errgroup",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":kubecfg_monitoring",
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:lock-lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

# Using manually created mock files

go_test(
    name = "server_test",
    srcs = [
        "server_test.go",
        "test_setup.go",
    ],
    # Add direct dependencies on the mock targets to ensure they're built
    data = [
        "//base/feature_flags:feature_flags_go",
        "//base/go/secretstring:secretstring_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/bigtable_proxy/client:client_go",
        "//services/bigtable_proxy/client:mocks",
        "//services/chat_host:chat_host_go_proto",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/request_context:request_context_go",
        "//services/remote_agents:remote_agents_go_proto",
        "//services/remote_agents/server:remote_agent_entities_go_proto",
        "//services/remote_agents/server/ws",
        "//services/remote_agents/server/ws:mock_remote_workspace",
        "//services/remote_agents/server/ws:mock_remote_workspace_controller",
        "//services/remote_agents/server/ws:mocks",
        "@com_github_golang_mock//gomock",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb:go_default_library",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
    embed = [":server_lib"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//base/test_utils/bigtable_utils:bigtable_utils_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/bigtable_proxy/client:fake_client_go",
        "//services/bigtable_proxy/client:mocks",
        "//services/chat_host:chat_host_go_proto",
        "//services/remote_agents/server/ws",
        "//services/remote_agents/server/ws:mocks",
        "@com_github_golang_mock//gomock",
        "@com_github_google_go_cmp//cmp",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_google_protobuf//testing/protocmp",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

# TODO: Implement agent_history_streamer_test.go
# go_test(
#     name = "agent_history_streamer_test",
#     srcs = [
#         "agent_history_streamer_test.go",
#     ],
#     embed = [":server_lib"],
#     deps = [
#         "//services/chat_host:chat_host_go_proto",
#         "//services/lib/request_context:request_context_go",
#         "//services/remote_agents:remote_agents_go_proto",
#         "@com_github_stretchr_testify//assert",
#         "@com_github_stretchr_testify//mock",
#         "@org_golang_google_grpc//codes",
#         "@org_golang_google_grpc//metadata",
#         "@org_golang_google_grpc//status",
#     ],
# )

go_test(
    name = "agent_history_processor_test",
    srcs = [
        "agent_history_processor_test.go",
    ],
    embed = [":server_lib"],
    deps = [
        "//services/chat_host:chat_host_go_proto",
        "//services/lib/request_context:request_context_go",
        "//services/remote_agents:remote_agents_go_proto",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
    ],
)

go_test(
    name = "workspace_processor_test",
    srcs = [
        "workspace_processor_test.go",
    ],
    embed = [":server_lib"],
    deps = [
        "//services/remote_agents:remote_agents_go_proto",
        "//services/remote_agents/server:remote_agent_entities_go_proto",
        "@com_github_stretchr_testify//assert",
    ],
)

go_test(
    name = "agent_list_processor_test",
    srcs = [
        "agent_list_processor_test.go",
    ],
    embed = [":server_lib"],
    deps = [
        "//services/remote_agents:remote_agents_go_proto",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "task_manager_test",
    srcs = [
        "cron_tasks_test.go",
        "test_setup.go",
    ],
    embed = [":server_lib"],
    deps = [
        "//base/feature_flags:feature_flags_go",
        "//base/go/secretstring:secretstring_go",
        "//base/test_utils/bigtable_utils:bigtable_utils_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/lib/request_context:request_context_go",
        "//services/remote_agents:remote_agents_go_proto",
        "//services/remote_agents/server:remote_agent_entities_go_proto",
        "//services/remote_agents/server/ws:mocks",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/tenant_watcher/client:mock_tenant_watcher_client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "//services/token_exchange/client:mock_token_exchange_client_go",
        "@com_github_golang_mock//gomock",
        "@com_github_google_uuid//:uuid",
        "@com_github_prometheus_client_golang//prometheus/testutil",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//require",
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb:go_default_library",
        "@io_k8s_client_go//kubernetes/fake",
        "@io_k8s_client_go//testing",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "storage_test",
    srcs = [
        "storage_test.go",
        "test_setup.go",
    ],
    # Add direct dependencies on the mock targets to ensure they're built
    embed = [":server_lib"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//base/test_utils/bigtable_utils:bigtable_utils_go",
        "//services/chat_host:chat_host_go_proto",
        "//services/remote_agents:remote_agents_go_proto",
        "//services/remote_agents/server:remote_agent_entities_go_proto",
        "//services/remote_agents/server/ws:mocks",
        "@com_github_golang_mock//gomock",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_protobuf//reflect/protoreflect",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_monitoring",
        "//deploy/tenants:namespaces",
    ],
)
