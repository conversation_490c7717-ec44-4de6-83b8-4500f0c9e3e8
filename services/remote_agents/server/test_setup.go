package main

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	bigtableproto "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/secretstring"
	bigtable_utils "github.com/augmentcode/augment/base/test_utils/bigtable_utils"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	proxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	chatproto "github.com/augmentcode/augment/services/chat_host/proto"
	githubprocessorclient "github.com/augmentcode/augment/services/integrations/github/processor/client"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	memstoreclient "github.com/augmentcode/augment/services/memstore/client"
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	entitiesproto "github.com/augmentcode/augment/services/remote_agents/server/entities_proto"
	wsmock "github.com/augmentcode/augment/services/remote_agents/server/ws/mocks"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	testExponentialBackoffAttempts = 2
	testResumeLinearBackoff        = time.Millisecond
)

// Global variables for the emulator and BigTable client
var (
	emulatorHostPort        string
	emulatorCleanup         func()
	tableCleanup            func()
	realBigtableProxyClient bigtableproxy.BigtableProxyClient
)

// TestMain sets up the BigTable emulator for all tests
func TestMain(m *testing.M) {
	// Start the emulator
	var err error
	emulatorHostPort, emulatorCleanup, err = bigtable_utils.StartEmulator()
	if err != nil {
		fmt.Printf("Failed to start BigTable emulator: %v\n", err)
		os.Exit(1)
	}
	bigtable_utils.SetupEmulatorEnv(emulatorHostPort)

	// Create the table with required column families
	ctx := context.Background()
	tableDetails := bigtable_utils.NewTableDetails("REMOTE_AGENTS")
	columnFamilies := []string{
		configFamilyName,
		statusFamilyName,
		userMappingFamilyName,
		outputFamilyName,
		timestampFamilyName,
	}
	tableCleanup, err = bigtable_utils.CreateTable(ctx, tableDetails, columnFamilies)
	if err != nil {
		fmt.Printf("Failed to create BigTable table: %v\n", err)
		emulatorCleanup()
		bigtable_utils.CleanupEmulatorEnv()
		os.Exit(1)
	}

	// Create a real BigTable proxy client
	realBigtableProxyClient, err = createBigtableProxyClient(emulatorHostPort)
	if err != nil {
		fmt.Printf("Failed to create BigTable proxy client: %v\n", err)
		tableCleanup()
		emulatorCleanup()
		bigtable_utils.CleanupEmulatorEnv()
		os.Exit(1)
	}

	// Set a lower value for maxExponentialBackoffAttempts to speed up tests
	setMaxExponentialBackoffAttempts(m, testExponentialBackoffAttempts, testResumeLinearBackoff)

	// Run the tests
	code := m.Run()

	// Clean up
	realBigtableProxyClient.Close()
	tableCleanup()
	emulatorCleanup()
	bigtable_utils.CleanupEmulatorEnv()

	os.Exit(code)
}

// createBigtableProxyClient creates a BigTable proxy client that connects directly to the emulator
func createBigtableProxyClient(hostPort string) (bigtableproxy.BigtableProxyClient, error) {
	// For testing, we'll create a client that connects directly to the emulator
	// This avoids the need to start a separate BigTable proxy service
	return bigtableproxy.NewEmulatorBigtableProxyClient("google-cloud-bigtable-emulator", "test-instance")
}

type testServerWithEmulator struct {
	server                    *RemoteAgentsServer
	ctrl                      *gomock.Controller
	mockRWC                   *wsmock.MockRemoteWorkspaceController
	mockGithubProcessorClient *githubprocessorclient.MockGithubProcessorClient
	featureFlagHandle         *featureflags.LocalFeatureFlagHandler
	riPublisher               *ripublisher.RequestInsightPublisherMockImpl
}

// Every test that uses this helper should call this function at the end of the test (ideally with a defer)
func (tse *testServerWithEmulator) Finish() {
	tse.ctrl.Finish()
}

// setupTestServerWithEmulator creates a test server with the real BigTable proxy client
func setupTestServerWithEmulator(t *testing.T) *testServerWithEmulator {
	ctrl := gomock.NewController(t)

	mockRWC := wsmock.NewMockRemoteWorkspaceController(ctrl)
	mockRWC.EXPECT().
		Namespace().
		Return("test-namespace").
		AnyTimes()

	mockGithubProcessorClient := &githubprocessorclient.MockGithubProcessorClient{}

	// Set up a default token
	mockGithubProcessorClient.On("GetGithubUserToken", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(secretstring.New("test-token"), secretstring.New("test-login"), secretstring.New("test-email"), secretstring.New("test-name"), nil)

	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a nil memstore client for testing
	var memstoreClient memstoreclient.MemstoreClient = nil
	riPublisher := ripublisher.NewRequestInsightPublisherMock()

	server, err := NewRemoteAgentsServer(mockRWC, realBigtableProxyClient, mockGithubProcessorClient, riPublisher, featureFlagHandle, false, "test-ri-topic", memstoreClient)
	require.NoError(t, err)

	tse := &testServerWithEmulator{
		server:                    server,
		ctrl:                      ctrl,
		mockRWC:                   mockRWC,
		mockGithubProcessorClient: mockGithubProcessorClient,
		featureFlagHandle:         featureFlagHandle,
		riPublisher:               riPublisher,
	}
	t.Cleanup(tse.Finish)
	return tse
}

// testContext contains all the common test context information
type testContext struct {
	Ctx        context.Context
	UserID     string
	AgentID    string
	TenantID   string
	RequestCtx *requestcontext.RequestContext
}

// setupTestParameters creates a new test context with auth info
// If agentID is provided, it will be used instead of generating a new one
func setupTestParameters(agentID ...string) testContext {
	ctx := context.Background()
	userID := uuid.New().String()
	// Use provided agent ID or generate a new one
	agentIDValue := uuid.New().String()
	if len(agentID) > 0 && agentID[0] != "" {
		agentIDValue = agentID[0]
	}
	tenantID := "test-tenant"
	// Use a UUID for the request ID to ensure it can be parsed correctly
	requestCtx := requestcontext.New(
		requestcontext.RequestId(uuid.New().String()),
		requestcontext.RequestSessionId("test-session-id"),
		"test-source",
		secretstring.New("test-token"),
	)

	ctx = auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
		OpaqueUserID:     userID,
		OpaqueUserIDType: "AUGMENT",
		TenantID:         tenantID,
	})
	ctx = requestcontext.NewIncomingContext(ctx, requestCtx)

	return testContext{
		Ctx:        ctx,
		UserID:     userID,
		AgentID:    agentIDValue,
		TenantID:   tenantID,
		RequestCtx: requestCtx,
	}
}

// createTestAgentConfig creates a test agent configuration
func createTestAgentConfig() *remoteagentsproto.AgentConfig {
	apiToken := "secret-api-token"
	return &remoteagentsproto.AgentConfig{
		WorkspaceSetup: &remoteagentsproto.WorkspaceSetup{
			StartingFiles: &remoteagentsproto.WorkspaceSetup_GithubRef{
				GithubRef: &remoteagentsproto.GithubRef{
					Url: "https://github.com/augmentcode/augment",
					Ref: "main",
				},
			},
			Token: &apiToken,
		},
		StartingNodes: []*chatproto.ChatRequestNode{
			{
				Id:   1,
				Type: chatproto.ChatRequestNodeType_TEXT,
				TextNode: &chatproto.ChatRequestText{
					Content: "Hello, world!",
				},
			},
		},
	}
}

// createSerializedAgentConfig creates a serialized agent config with the given user ID
func createSerializedAgentConfig(userID string) []byte {
	config := createTestAgentConfig()
	configEntity := &entitiesproto.AgentConfig{
		InputConfig: config,
		UserId:      userID,
		CreatedAt:   timestamppb.Now(),
	}
	protoValue, _ := proto.Marshal(configEntity)
	return protoValue
}

func deleteAllRowsInTable(ctx context.Context, tenantID string, requestContext *requestcontext.RequestContext) error {
	// Create a filter that matches all rows
	filter := bigtableproxy.LatestNFilter(1).Proto()

	// Read all row keys first
	rows, err := realBigtableProxyClient.ReadRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		&bigtableproto.RowSet{
			RowRanges: []*bigtableproto.RowRange{{
				StartKey: &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: []byte("")},
				EndKey:   &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: []byte("")},
			}},
		},
		filter,
		0, // No limit
		requestContext,
	)
	if err != nil {
		return err
	}

	// If no rows, nothing to delete
	if len(rows) == 0 {
		return nil
	}

	// Create mutation entries for each row
	entries := make([]*bigtableproto.MutateRowsRequest_Entry, 0, len(rows))
	for _, row := range rows {
		entries = append(entries, &bigtableproto.MutateRowsRequest_Entry{
			RowKey: row.RowKey,
			Mutations: []*bigtableproto.Mutation{
				{
					Mutation: &bigtableproto.Mutation_DeleteFromRow_{
						DeleteFromRow: &bigtableproto.Mutation_DeleteFromRow{},
					},
				},
			},
		})
	}

	_, err = realBigtableProxyClient.MutateRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		entries,
		requestContext,
	)
	if err != nil {
		return err
	}

	return nil
}
