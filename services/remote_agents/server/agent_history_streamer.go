// Package main implements the Remote Agents server functionality.
//
// This file contains the AgentHistoryStreamer, which is responsible for streaming
// agent history updates to clients. It implements a server-side streaming gRPC endpoint
// that efficiently delivers incremental updates as they become available.
//
// Instead of clients repeatedly polling for updates, the server maintains a long-lived
// connection and streams updates as they occur.
//
// Features of the implementation:
//  1. Polling of BigTable for new agent history exchanges
//  2. Support for reconnection scenarios through sequence ID tracking
//  3. Incremental text updates
//  4. Periodic forced responses to verify client connection liveness
package main

import (
	"context"
	"errors"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	memstoreclient "github.com/augmentcode/augment/services/memstore/client"
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	"github.com/rs/zerolog/log"
)

// Constants for agent history streaming
const (
	// Interval at which we poll for agent history updates
	PollingInterval = 1 * time.Second

	// Maximum number of iterations without a response.
	//
	// In the actix framework used by API proxy, the only way to detect that a client has
	// closed the connection is to attempt to send a response. If the send fails, the
	// connection is closed. When API proxy detects that the connection is closed, it will
	// cancel the stream.
	//
	// For that reason, it is important to periodically send a response even if there are
	// no updates.
	MaxSkippedIterations = 10

	// Batch size for reading chat history
	ChatHistoryBatchSize = 100
)

// Helper function to safely handle nil string pointers
func stringOrEmpty(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// AgentHistoryStreamer defines the interface for streaming agent history
type AgentHistoryStreamer interface {
	// StreamAgentHistory streams agent history for a specific agent with incremental updates
	StreamAgentHistory(
		ctx context.Context,
		stream remoteagentsproto.RemoteAgents_AgentHistoryStreamServer,
		req *remoteagentsproto.AgentHistoryStreamRequest,
		userId string,
		tenantID string,
		requestContext *requestcontext.RequestContext,
	) error
}

// bigTableAgentHistoryStreamer implements AgentHistoryStreamer using BigTable
type bigTableAgentHistoryStreamer struct {
	bigtableProxyClient bigtableproxy.BigtableProxyClient
	memstoreClient      memstoreclient.MemstoreClient
	featureFlagHandle   featureflags.FeatureFlagHandle
}

// NewBigTableAgentHistoryStreamer creates a new bigTableAgentHistoryStreamer
func NewBigTableAgentHistoryStreamer(bigtableProxyClient bigtableproxy.BigtableProxyClient, memstoreClient memstoreclient.MemstoreClient, featureFlagHandle featureflags.FeatureFlagHandle) AgentHistoryStreamer {
	return &bigTableAgentHistoryStreamer{
		bigtableProxyClient: bigtableProxyClient,
		memstoreClient:      memstoreClient,
		featureFlagHandle:   featureFlagHandle,
	}
}

// StreamAgentHistory implements AgentHistoryStreamer.StreamAgentHistory
func (s *bigTableAgentHistoryStreamer) StreamAgentHistory(
	ctx context.Context,
	stream remoteagentsproto.RemoteAgents_AgentHistoryStreamServer,
	req *remoteagentsproto.AgentHistoryStreamRequest,
	userId string,
	tenantID string,
	requestContext *requestcontext.RequestContext,
) error {
	// Create a processor to handle the business logic
	processor := NewAgentHistoryProcessor(req.LastProcessedSequenceId)

	// Keep track of skipped iterations to periodically verify the connection is still alive
	skippedIterations := 0

	// Timer for polling - start with immediate execution
	timer := time.NewTimer(0) // 0 duration for immediate first execution
	defer timer.Stop()

	// Create a channel for the Pub/Sub subscription
	messageChan, cancelSubscription, err := SubscribeToAgentHistoryNotifications(ctx, requestContext, s.memstoreClient, req.RemoteAgentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to subscribe to agent history notifications for agent %s", req.RemoteAgentId)
		// Continue without Pub/Sub, falling back to polling
	} else if cancelSubscription != nil {
		defer cancelSubscription()
	}

	// Poll for updates
	for {
		select {
		case <-ctx.Done():
			err := context.Cause(ctx)
			if errors.Is(err, context.Canceled) {
				log.Ctx(ctx).Info().
					Str("error", err.Error()).
					Uint32("last_processed_sequence_id", processor.getLastProcessedSequenceId()).
					Msg("Agent history stream ended due to context cancellation")
			} else {
				log.Ctx(ctx).Warn().
					Str("error", err.Error()).
					Uint32("last_processed_sequence_id", processor.getLastProcessedSequenceId()).
					Msg("Agent history stream ended due to context error")
			}
			return err
		case message := <-messageChan:
			// Process the Pub/Sub notification
			err := processAgentHistoryNotification(
				ctx,
				message,
				req.RemoteAgentId,
				tenantID,
				userId,
				requestContext,
				s.bigtableProxyClient,
				processor,
				stream,
				&skippedIterations,
				s.featureFlagHandle,
			)
			if err != nil {
				return err
			}

		case <-timer.C:
			// Reset the timer for the next iteration
			timer.Reset(PollingInterval)

			if processor.getLastProcessedSequenceId() < req.LastProcessedSequenceId {
				log.Ctx(ctx).Error().Msgf(
					"Error reading chat history: last processed sequence ID is less than the requested last processed sequence ID. Expected >= %d, got %d",
					req.LastProcessedSequenceId,
					processor.getLastProcessedSequenceId(),
				)
			}

			// Process the timer event using the same function as Pub/Sub notifications
			// We pass nil for the message since we're not processing a notification
			err := processAgentHistoryNotification(
				ctx,
				nil, // No message for timer events
				req.RemoteAgentId,
				tenantID,
				userId,
				requestContext,
				s.bigtableProxyClient,
				processor,
				stream,
				&skippedIterations,
				s.featureFlagHandle,
			)
			if err != nil {
				return err
			}

			// If we have no meaningful updates and we've reached the max skipped iterations,
			// send an empty response to verify the connection is still alive
			if skippedIterations > MaxSkippedIterations {
				// Create an empty response
				emptyResponse := &remoteagentsproto.AgentHistoryStreamResponse{
					Updates: []*remoteagentsproto.AgentHistoryUpdate{},
				}

				// Send the empty response
				if err := stream.Send(emptyResponse); err != nil {
					log.Ctx(ctx).Warn().Err(err).Msgf("Failed to send empty agent history update for agent %s", req.RemoteAgentId)
					return err
				}

				// Reset the counter after sending a response
				skippedIterations = 0
			}
		}
	}
}

// processAgentHistoryNotification processes a Pub/Sub notification for agent history
func processAgentHistoryNotification(
	ctx context.Context,
	message []byte,
	agentId string,
	tenantID string,
	userId string,
	requestContext *requestcontext.RequestContext,
	bigtableProxyClient bigtableproxy.BigtableProxyClient,
	processor *AgentHistoryProcessor,
	stream remoteagentsproto.RemoteAgents_AgentHistoryStreamServer,
	skippedIterations *int,
	featureFlagHandle featureflags.FeatureFlagHandle,
) error {
	// Check if this is a Pub/Sub notification or a timer event
	if message != nil {
		// We received a notification via Pub/Sub
		log.Ctx(ctx).Debug().Msgf("Received Pub/Sub notification for agent %s", agentId)

		// Parse the message to get the sequence ID
		notification, err := UnmarshallAgentHistoryNotification(message)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to parse Pub/Sub notification for agent %s", agentId)
			return nil
		}

		// Check if we need to fetch this update
		if notification.SequenceID <= processor.getLastProcessedSequenceId() {
			log.Ctx(ctx).Debug().Msgf("Skipping already processed sequence ID %d", notification.SequenceID)
			return nil
		}
	} else {
		// This is a timer event, not a Pub/Sub notification
		log.Ctx(ctx).Debug().Msgf("Processing timer event for agent %s", agentId)

		// Increment the counter for iterations without meaningful updates
		*skippedIterations++
	}

	// Fetch the updates from BigTable
	exchanges, err := readChatHistory(
		ctx,
		requestContext,
		bigtableProxyClient,
		tenantID,
		agentId,
		ChatHistoryBatchSize,
		processor.getLastProcessedSequenceId(),
	)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msgf("Failed to read agent history for agent %s", agentId)
		return nil
	}

	// Get the agent
	var agent *remoteagentsproto.Agent = nil
	if len(exchanges) > 0 {
		agent, err = readRemoteAgentForPublic(ctx, requestContext, bigtableProxyClient, tenantID, userId, agentId, getAgentDeletionTTLDays(featureFlagHandle))
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Msgf("Failed to get agent %s", agentId)
			return err
		}
	}

	// Process agent history updates
	updates := processor.ProcessAgentHistory(exchanges, agent, agentId)

	// Check if we have any meaningful updates to send
	if len(updates) == 0 {
		// For Pub/Sub notifications, we just return
		if message != nil {
			return nil
		}

		// For timer events, we check if we need to send a keepalive
		// This is handled by the caller, so we just return
		return nil
	}

	// Create a single response with all updates
	combinedResponse := &remoteagentsproto.AgentHistoryStreamResponse{
		Updates: updates,
	}

	// Send the combined response
	if err := stream.Send(combinedResponse); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msgf("Failed to send combined agent history update for agent %s", agentId)
		return err
	}

	// Reset the counter after a response
	*skippedIterations = 0

	return nil
}
