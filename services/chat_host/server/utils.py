from base.prompt_format.common import Rule, RuleType
from base.prompt_format_chat.prompt_formatter import (
    ChangedFileStats,
    PerFileChangeStats,
    PerTypeChangedFileStats,
)
from services.chat_host import chat_pb2
from google.protobuf.internal.containers import RepeatedCompo<PERSON><PERSON>ieldContainer


def convert_per_file_change_stats_from_proto(
    per_file_change_stats: chat_pb2.PerFileChangeStats,
) -> PerFileChangeStats:
    return PerFileChangeStats(
        path=per_file_change_stats.file_path,
        insertion_count=per_file_change_stats.insertion_count,
        deletion_count=per_file_change_stats.deletion_count,
        old_path=per_file_change_stats.old_file_path,
    )


def convert_per_type_changed_file_stats_from_proto(
    per_type_changed_file_stats: chat_pb2.PerTypeChangedFileStats,
) -> PerTypeChangedFileStats:
    return PerTypeChangedFileStats(
        changed_file_count=per_type_changed_file_stats.changed_file_count,
        per_file_change_stats_head=[
            convert_per_file_change_stats_from_proto(stats)
            for stats in per_type_changed_file_stats.per_file_change_stats_head
        ],
        per_file_change_stats_tail=[
            convert_per_file_change_stats_from_proto(stats)
            for stats in per_type_changed_file_stats.per_file_change_stats_tail
        ],
    )


def convert_changed_file_stats_from_proto(
    changed_file_stats: chat_pb2.ChangedFileStats,
) -> ChangedFileStats:
    return ChangedFileStats(
        added_file_stats=convert_per_type_changed_file_stats_from_proto(
            changed_file_stats.added_file_stats
        ),
        broken_file_stats=convert_per_type_changed_file_stats_from_proto(
            changed_file_stats.broken_file_stats
        ),
        copied_file_stats=convert_per_type_changed_file_stats_from_proto(
            changed_file_stats.copied_file_stats
        ),
        deleted_file_stats=convert_per_type_changed_file_stats_from_proto(
            changed_file_stats.deleted_file_stats
        ),
        modified_file_stats=convert_per_type_changed_file_stats_from_proto(
            changed_file_stats.modified_file_stats
        ),
        renamed_file_stats=convert_per_type_changed_file_stats_from_proto(
            changed_file_stats.renamed_file_stats
        ),
        unmerged_file_stats=convert_per_type_changed_file_stats_from_proto(
            changed_file_stats.unmerged_file_stats
        ),
        unknown_file_stats=convert_per_type_changed_file_stats_from_proto(
            changed_file_stats.unknown_file_stats
        ),
    )


def convert_rules_from_proto(rules: RepeatedCompositeFieldContainer) -> list[Rule]:
    return [
        Rule(
            type=RuleType(rule.type),
            path=rule.path,
            content=rule.content,
        )
        for rule in rules
    ]
