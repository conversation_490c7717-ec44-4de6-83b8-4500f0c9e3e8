"""Postprocess for chat responses."""

from dataclasses import dataclass
from dataclasses_json import dataclass_json
from typing import Sequence, cast

from base.prompt_format.common import ResponseMessage
from base.prompt_format_postprocess import get_postprocess_prompt_formatter_by_name
from base.prompt_format_postprocess.sentry_prompt_formatter import (
    SentryPromptInput,
    SentryTokenApportionment,
)
from base.python.grpc import client_options
from base.tokenizers import create_tokenizer_by_name
from services.inference_host.client import inference_host_client
from services.inference_host.client.inference_host_client import InfererClient
from services.inference_host.client.multiplex import RequestContext
from services.lib.grpc.tls_config import tls_config
from services.lib.grpc.tls_config.tls_config import ClientConfig


def should_run_postprocessing(response_text: str) -> bool:
    """
    Determine if postprocessing should run based on response content.

    Only run postprocessing if the response contains potential code blocks:
    1. Triple backticks (```)
    2. <augment_code_snippet> XML tags

    Args:
        response_text: The response text to check

    Returns:
        True if postprocessing should run, False otherwise
    """
    # Check for triple backticks
    if "```" in response_text:
        return True

    # Check for augment_code_snippet XML tags
    if "<augment_code_snippet" in response_text:
        return True

    return False


@dataclass_json
@dataclass
class PostprocessConfig:
    """Postprocess model configuration."""

    endpoint: str
    """The endpoint of the postprocessing service."""

    mtls: bool
    """Whether to use mtls for the postprocessing service."""

    client_ca_path: str
    """The path to the postprocessing service's CA cert."""

    client_cert_path: str
    """The path to the postprocessing service's client cert."""

    client_key_path: str
    """The path to the postprocessing service's client key."""

    name: str
    """Name of the postprocessor."""

    timeout_s: int
    """The timeout for the postprocessing service in milliseconds."""

    prompt_formatter_name: str
    """Name of the postprocessing prompt formatter."""

    tokenizer_name: str
    """Name of the postprocessing tokenizer."""

    max_output_length: int
    """The maximum length of the output from the postprocessor."""

    token_apportionment: SentryTokenApportionment | dict
    """The token apportionment for the postprocessor."""


def _get_router_client_creds(
    config: PostprocessConfig,
) -> ClientConfig | None:
    """Returns RPC client credentials."""
    if config.mtls:
        return ClientConfig(
            key_path=config.client_key_path,
            cert_path=config.client_cert_path,
            ca_path=config.client_ca_path,
        )
    else:
        return None


def _create_client(config: PostprocessConfig) -> InfererClient:
    credentials = tls_config.get_client_tls_creds(_get_router_client_creds(config))
    endpoint = config.endpoint
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in endpoint,
        )
    )
    stub = inference_host_client.create_inference_stub(
        endpoint, credentials=credentials, options=options
    )
    return InfererClient(lambda: stub)


@dataclass(frozen=True)
class TrimmedOutputTokens:
    """Result of _trim_output_tokens."""

    truncated_tokens: Sequence[int]
    is_truncated: bool
    truncated_log_probs: Sequence[float] | None


# TODO: Duplication - move to a common library.
def _trim_output_tokens(
    eos_to_check: set[int], tokens: Sequence[int], log_probs: Sequence[float] | None
) -> TrimmedOutputTokens:
    """Trims tokens and log_probs."""

    is_truncated = False
    for i, token in enumerate(tokens):
        if token in eos_to_check:
            tokens = tokens[:i]
            is_truncated = True
            if log_probs is not None:
                log_probs = log_probs[:i]
            break

    return TrimmedOutputTokens(
        truncated_tokens=tokens,
        is_truncated=is_truncated,
        truncated_log_probs=log_probs,
    )


@dataclass(frozen=True)
class PostprocessResponse:
    text: str
    tokens: Sequence[int]
    log_probs: Sequence[float] | None


class Postprocess:
    def __init__(self, config: PostprocessConfig | dict):
        if isinstance(config, dict):
            config = cast(
                PostprocessConfig,
                # pylint:disable-next=no-member
                PostprocessConfig.schema().load(config),  # type: ignore
            )
        self.model_name = config.name
        self.client = _create_client(config)
        self.tokenizer = create_tokenizer_by_name(config.tokenizer_name)
        if isinstance(config.token_apportionment, dict):
            config.token_apportionment = SentryTokenApportionment(
                **config.token_apportionment
            )
        self.prompt_formatter = get_postprocess_prompt_formatter_by_name(
            config.prompt_formatter_name,
            self.tokenizer,
            config.token_apportionment,
        )
        self.max_output_length = config.max_output_length
        self.timeout_s = config.timeout_s

    def postprocess(
        self, chat_response: ResponseMessage, request_context: RequestContext
    ) -> PostprocessResponse:
        """Postprocess the response."""
        postprocess_prompt = self.prompt_formatter.format_prompt(
            SentryPromptInput(
                chat_response=chat_response,
            )
        )
        reply = self.client.infer(
            input_tokens=postprocess_prompt.tokens,
            max_output_length=self.max_output_length,
            end_token_ids={self.tokenizer.special_tokens.eos},
            top_k=0,
            top_p=0.0,
            temperature=0.0,
            random_seed=0,
            request_context=request_context,
            timeout_s=self.timeout_s,
        )
        # Remove eos token
        truncated_output = _trim_output_tokens(
            eos_to_check={self.tokenizer.special_tokens.eos},
            tokens=reply.output_tokens,
            log_probs=reply.log_probs,
        )
        tokens = truncated_output.truncated_tokens
        log_probs = truncated_output.truncated_log_probs

        return PostprocessResponse(
            self.tokenizer.detokenize(tokens),
            tokens,
            log_probs,
        )
