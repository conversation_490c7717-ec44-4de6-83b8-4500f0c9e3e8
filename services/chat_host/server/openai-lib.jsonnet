local lib = import 'deploy/common/lib.jsonnet';

function(appName, namespace, cloud)
  local secretName = 'openai-api-key-%s' % appName;  // pragma: allowlist secret
  local apiKeySealedSecret = {
    kind: 'SealedSecret',
    apiVersion: 'bitnami.com/v1alpha1',
    metadata: {
      name: secretName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
    },
    spec: {
      template: {
        metadata: {
          name: secretName,
          annotations: {
            'sealedsecrets.bitnami.com/cluster-wide': 'true',
          },
        },
      },
      encryptedData: {
        GCP_US_CENTRAL1_DEV: {
          'api-key.txt': 'AgCBBl4rL+quQX1iwKB958+sbwqhB/6mOwYLMagSfV+/YsVtfb5EVr/VbGlhlgbtHbD0cQgNO8WYSoMRFwn/gChfabMb7gTHzlwAYcnJpC1sC26li9vlbFCtzG3Cww2MB3ll9byVLkcwlciKX3DReO8s9mLUIA4xhlOXIT3NAhbt96GC8M64ARTfJXh0/FozCg/7YSP8rcUN143/uQiolmpZ5DA3/AHc6ltqS4CY4AQsMhb8d8aNKqV1/XyUsoYybAe0DQ6dKjjuPB8pSDagZ4ed0fVlZwqacNz3AzJS6ifpkJvlwJeQCs2YfINCQXzIUhD62E200yRM1mJkjUZnEBg1CltN7Vmgy/HZPfhDFyzdhdVik+NIoZTaj6EuO0tb1MdqF7RiuhI4zI7fTTZ/5QRThWCeLjeU92YxYxXA0LLJZ2NirEV6t2Lw0gHVFJHmoERW4pgM3BlRKhMrq0T/f9YH5wPBuLsgjjagXoTltiQ0ziCXALKdEKn6vhXgylgnskWS8rdHRgU5r1zf/iXT6VPu/OiuLJ+PgekLXD5XMu2CuuOJN0xL1HQPo4nr+FkdrujuQc1yad/DfddFZT/rYDxPe0Lbq2dCJF4/xSwBMJOdrH84scdQ0GEJ0NXctUTRsI9cEpTC27C31xkYnKlwfeqUPpY5U0NNtn6/hYszXpHUkNRBZIHAXt0VwPuD/nntNWpYJWZdgL/ILUyRbuPTvV/zyRf7N9KjqIH1Vx7uCzy1raxegOTTzE/JyMPJK4cPbYNySx460BqYFaHtulZjNX64ukzL8sBOkQ2xkVeURQqN+4MdxoOU++LtF/TwMn/62fh/4lqW3RoMgGM2hRmjaYJttzerhDFD21Yrnf4FtcAWoom5JXXydgYYYNL1bwLo5mqP0s6ZB8RqM7EVlBWe3nxD9kpaZA==',  // pragma: allowlist secret
        },
        GCP_US_CENTRAL1_PROD: {
          'api-key.txt': 'AgBORm3C4Ufkxa03c2XSXz8X5M+0cY5+CELiKkmESpaI/oSHvDSHqGxKgsHJfyQyTGozIgblUCFMNLJrnJB11FuAp2cZbq2d432eB5XXGOBRBQOWzyo/3rYtwQCyhNyg2k5bU/C0GNp9RVy+9JiRN1K2NhU2TuAVZfexYB6IzIChgNwOuCoRReaV1zl9zXT2X3eiFlmox+9vyC7VbD0c6Zs0EnbIFkXGKuc2Hdd7ZcJhRbTt3S4lJ7+IsHMYoKZD1sA2gNhzatJbQwFj8JHBRu+nRoG2XGlrHHRozDnjnsCmC5/WWlFxtuRsbbR4c20ZePYrq946NSG8c5q9Fa4pHoVCZR7yzIItB54Yl+ztWIrhR7/uppog30fJSvH7dY+siNFe/htQs/myChkZOLKqm0U7qZUglmPqZdngeUngvWSYBz9v8ZkaiP6Dqn6bu5Tl/I2dQXhr1EuP7jZAlrGtAt5VJOO0Qjqyy9KqbmssDSs4oknaW1dANF6NHGRAVzDt8Zt2VCjUNY0jAjghjSE/tBkeDoLeEjgjjV2k7U7HnruZv8h7SM1JIaWevDbop3FZnCu2zinl/hLELe4kgfAh7w6XgkgTaQgGRtGSXcbgeK9Mq+6UrTS63tQRry/iItLVmpVlSV4148EcLZfRZdsSHQjxCcFwXLAGWJNVRW/eoj430TAjXK3RduJgQmuMx1YSAoYoxuZhzv3a1zD6ahjE4r6pp4Qo4F0Jbre+dFHTDFIBpbtLzDwFGsv42BB+PfNblIcbflOQzAXFzy6x8mfzy7UhQmxBvPyMwoBGgafbNHpQHABgaIs10bQU6kCdNF+o7pCjAsh4IMCU8qDB7SydwJqpgk/P5Qsiofi6tCwEYYkT6Bq2l7FYS8+aiWJvicLRpfo6T/NYjaSkWE7HW3Xr+eRloTvMNA==',  // pragma: allowlist secret
        },
        GCP_EU_WEST4_PROD: {
          'api-key.txt': 'AgBGZlOUlP+jTY8ZqQwkZh2bZKoPZBkegtm+G8yNWlDHbAv7oZYOqgBZOu3x9HfZn2HCCfOJ0zstWjZEjAHWUBfxHVtFUfonkSWSULB6OztfxSUOC75+sfy1wdZbsl17Zz+CMtddHIXiBBypMpbbOg/JD/4f8Fj4f5mCnnb1NN5QJHLnUlQsIAvMkVvQZ/XhOYwsNNittNKBvcbmk59t6JWRKwe/Ste+kDHn0r0+0LR5yGX2Lxw+WDDlHFl3logVJ0Pt9s5OA7A4GbHck/9wVs6P9M/WIkSf2aHL1yYvAgnfmOuXlIrfS4sjxJzIk54Jd5zU2eiZTpHfbqDLiAyfyceZcWKW/gEhNM6cCWepelKIyLVbgB6FD5WlT7y7G5Xdu5SkWGNEdHgZN0miGRArh8vvWUMy5SnmrJT3zOnAfRrAcpr6J0Qod7a4ML79Bqf4A5Yv9/v/IxcRefWR7nYrb4f6CT9tngxjtcd343OzlLIdpQm/UpzmoCNqwOmX5ibdcVCwmN/gdcT0rY9c4ZMYNBFueKfTxaMqX82yjo8MnKYZ1R70V5a/+9BxzxLvkAci8RgtbVunNeuOctfY2LlEjkkdk+tehhd77TIYovSVNuG5J8L5wNpuQF44yBcLDeWN7o4ku2UkryMS3hiDBviHph+gW12VbQ5XrEEJAJZOMzzh9mDlXRBcXIJUlZcU2Nc8bFhcZWpjTfdFPDOz75s3RXdyalhV1lNRReAhXzDHYES2MVPqSwCSSOA/2VyLpQtW9DmsiAnPXnOA6A58/2tRhRk4rvHfXHgjKAVxiGwdejn26vVsOTrDmUoQQkM9xod5hgiKD0wddWyr4FDoU2Qjq8uolUQA9YslQfY/6AFyjzJ9qUfy1GD5H+nIMdxVMoLGhp50DjPpMIQxlxrViR0uli25iktkpg==',  // pragma: allowlist secret
        },
      }[cloud],
    },
  };
  {
    api_key_path: '/openai-api-key/api-key.txt',
    volumeMountDef: {
      name: 'openai-api-key',
      mountPath: '/openai-api-key',
      readOnly: true,
    },
    podVolumeDef: {
      name: 'openai-api-key',
      secret: {
        secretName: secretName,  // pragma: allowlist secret
      },
    },
    objects:: [apiKeySealedSecret],
  }
