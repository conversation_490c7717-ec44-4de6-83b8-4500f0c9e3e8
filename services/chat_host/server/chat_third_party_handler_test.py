"""Unit Tests for <PERSON>t<PERSON><PERSON><PERSON>."""

import json
import pathlib
import typing
import uuid
from concurrent.futures.thread import Thread<PERSON>oolExecutor
from unittest import TestCase
from unittest.mock import ANY, MagicMock, patch

import pytest

from base import feature_flags
from base.blob_names import blob_names_pb2
from base.blob_names.python.blob_names import Blobs
from base.prompt_format.common import (
    ChatRequestNodeType,
    ChatResultNodeType,
    ChatResultToolUse,
    DocumentationMetadata,
    Exchange,
    ImageFormatType,
    PersonaType,
    Rule,
    RuleType,
    StopReason,
)
from base.prompt_format_chat.prompt_formatter import (
    Chat<PERSON>rom<PERSON><PERSON>ormatter,
    ChatTokenApportionment,
    StructuredChatPromptOutput,
)
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from base.test_utils.synchronous_executor import SynchronousExecutor
from base.third_party_clients.anthropic_direct_client import InvalidArgumentRpcError
from base.third_party_clients.third_party_model_client import (
    EndOfStream,
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
    ToolUseResponse,
    ToolUseStart,
)
from base.tokenizers.tokenizer import Tokenizer
from services.chat_host import chat_pb2
from services.chat_host.server.chat_handler_metrics import ChatHandlerMetrics
from services.chat_host.server.chat_third_party_handler import (
    _CHAT_GENERATE_TOOL_USE_START,
    _ENABLE_RULES,
    ChatThirdPartyHandler,
    create_chat_third_party_handler,
    get_pseudo_random_from_session_id,
)
from services.chat_host.server.handler import (
    ChatResult,
    ChatResultNode,
)
from services.chat_host.server.postprocess import Postprocess, PostprocessResponse
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.proto import chat_pb2 as chat_common
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.retriever import (
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)

feature_flags.unit_test_setup()


@pytest.fixture()
def feature_flags_context() -> (
    typing.Generator[feature_flags.LocalFeatureFlagSetter, None, None]
):
    yield from feature_flags.feature_flag_fixture()


def _update_nested_dict(d: dict, u: dict) -> dict:
    for k, v in u.items():
        if isinstance(v, dict):
            d[k] = _update_nested_dict(d.get(k, {}), v)
        else:
            d[k] = v
    return d


# TODO non-static mutable tests is discouraged in tests
FAKE_THIRD_PARTY_RESPONSE = iter(
    [ThirdPartyModelResponse(text) for text in ["Fake ", "Response"]]
)


def _get_chat_third_party_handler(
    updates_for_handler_config: dict | None = None,
    retrieval_result: RetrievalResult | None = None,
    third_party_response: typing.Iterable[ThirdPartyModelResponse] | None = None,
    client_type: str = "vertexai",
    model_name: str = "gemini-1.5-flash-001",
    prompt_formatter_name: str = "structured-binks-gemini-1.5-flash-001",
    postprocess: Postprocess | None = None,
) -> ChatThirdPartyHandler:
    """Factory function for creating chat third-party handler.

    Args:
        updates_for_handler_config: Modifies default values of handler config.
    """

    # Mock the file reading operation for API keys
    # This is needed for tests that use client_type="anthropic_vertexai"
    # TODO(zheren) remove this hack once we revert AnthropicMultiClient for
    # anthropic_vertexai
    original_read_text = pathlib.Path.read_text

    def mock_read_text(self, encoding=None, errors=None):
        if str(self).endswith("key.txt"):
            return "test-api-key"
        return original_read_text(self, encoding=encoding, errors=errors)

    pathlib.Path.read_text = mock_read_text

    handler_config = {
        "gcp_project_id": "test-project",
        "gcp_region": "us-east5",
        "anthropic_api_key_path": "test-key-path/key.txt",  # pragma: allowlist secret
        "openai_api_key_path": "test-key-path/key.txt",  # pragma: allowlist secret
        "xai_api_key_path": "test-key-path/key.txt",  # pragma: allowlist secret
        "fireworks_api_key_path": "test-key-path/key.txt",  # pragma: allowlist secret
        "client_type": client_type,
        "model_name": model_name,
        "prompt_formatter_name": prompt_formatter_name,
        "temperature": 0.6,
        "max_output_tokens": 8192,
        "token_apportionment": {
            "prefix_len": 1536,
            "suffix_len": 1024,
            "path_len": 256,
            "chat_history_len": 4096,
            "max_prompt_len": 16 * 1024 - 4096,
            "retrieval_len": -1,
            # Deprecated fields
            "selected_code_len": -1,
            "message_len": -1,
            "explicit_external_context_len": 0,
            "implicit_external_context_len": 4096,
            "overflow_external_context": False,
        },
        "generate_commit_message_token_apportionment": {
            "changed_files_summary_line_threshold": 900,
            "diff_len": 9216,
            "commit_message_len": 3072,
            "relevant_message_len": 1024,
            "max_prompt_len": 16 * 1024 - 4096,
            "path_len": 0,
            "message_len": 0,
            "chat_history_len": 0,
            "prefix_len": 0,
            "selected_code_len": 0,
            "suffix_len": 0,
        },
    }

    if updates_for_handler_config is not None:
        _update_nested_dict(handler_config, updates_for_handler_config)

    ri_publisher = MagicMock(RequestInsightPublisher)
    retriever = MagicMock(Retriever)
    retriever.retrieve.return_value = retrieval_result or RetrievalResult(
        [], [], checkpoint_not_found=False
    )
    metrics = ChatHandlerMetrics()
    content_manager_client = MagicMock()

    handler = create_chat_third_party_handler(
        handler_config,
        "test_namespace",
        ri_publisher,
        metrics,
        content_manager_client,
        retriever,
        postprocess,
    )
    handler.client = MagicMock(ThirdPartyModelClient, auto_spec=True)
    handler.client.generate_response_stream.return_value = (
        third_party_response or FAKE_THIRD_PARTY_RESPONSE
    )

    # Restore the original read_text method
    pathlib.Path.read_text = original_read_text

    return handler


def _get_chat_request(
    prefix: str = "def ",
    selected_code: str = "bar",
    suffix: str = "(a, b)",
    message: str = "Rename bar to foo",
    chat_history: list[chat_pb2.Exchange] | None = None,
    prefix_begin: int | None = None,
    suffix_end: int | None = None,
    path: str = "src/foo.py",
    blob_name: str = "blob0",
    lang: str = "python",
    blobs: blob_names_pb2.Blobs = Blobs.from_fake_blob_names(
        ["blob1", "blob2"]
    ).to_proto(),
    enable_preference_collection: bool = False,
    user_guided_blobs: list[str] | None = None,
    prompt_formatter_name: str = "",
    external_source_ids: typing.Sequence[str] = (),
    user_guidelines: str = "",
    workspace_guidelines: str = "",
    chat_request_nodes: list[chat_pb2.ChatRequestNode] | None = None,
    persona_type: int = 0,
) -> chat_pb2.ChatRequest:
    """Factory function for creating chat request."""

    if prefix_begin is None:
        prefix_begin = 0
    if suffix_end is None:
        suffix_end = len(prefix) + len(selected_code) + len(suffix) - 1
    if chat_history is None:
        chat_history = [
            chat_pb2.Exchange(
                request_message="How you doing?", response_text="Fine thanks"
            )
        ]

    chat_position = chat_pb2.ChatPosition(
        prefix_begin=prefix_begin,
        suffix_end=suffix_end,
        blob_name=blob_name,
    )

    chat_request = chat_pb2.ChatRequest(
        model_name="test_model",
        path=path,
        prefix=prefix,
        selected_code=selected_code,
        chat_history=[
            chat_pb2.Exchange(
                request_message=item.request_message, response_text=item.response_text
            )
            for item in chat_history
        ],
        suffix=suffix,
        message=message,
        position=chat_position,
        lang=lang,
        sequence_id=0,
        blobs=[blobs],
        enable_preference_collection=enable_preference_collection,
        user_guided_blobs=user_guided_blobs,
        prompt_formatter_name=prompt_formatter_name,
        disable_auto_external_sources=False,
        external_source_ids=list(external_source_ids),
        user_guidelines=user_guidelines,
        workspace_guidelines=workspace_guidelines,
        nodes=chat_request_nodes,
        persona_type=chat_pb2.PersonaType.Value(PersonaType(persona_type).name),
    )

    return chat_request


def _get_chat_prompt_formatter():
    token_apportionment = MagicMock(ChatTokenApportionment)
    token_apportionment.retrieval_len = -1
    prompt_formatter = MagicMock(ChatPromptFormatter)
    prompt_formatter.token_apportionment = token_apportionment
    return prompt_formatter


def _run_chat_stream(
    chat_handler: ChatThirdPartyHandler,
    chat_request: chat_pb2.ChatRequest,
    request_id: uuid.UUID | None = None,
    session_id: uuid.UUID | None = None,
) -> tuple[typing.Iterable[ChatResult], RequestContext, AuthInfo]:
    if request_id is None:
        request_id = uuid.UUID("3b608c4f-b7da-4c05-8368-b7414fcd54b7")
    if session_id is None:
        session_id = uuid.UUID("e02cf196-4384-4c56-98c7-a144199038b8")

    request_context = RequestContext(
        request_id=str(request_id),
        request_session_id=str(session_id),
        request_source="unknown",
    )

    executor = SynchronousExecutor()
    auth_info = MagicMock()
    chat_results = chat_handler.chat_stream(
        chat_request,
        request_context,
        auth_info=auth_info,
        executor=executor,
    )
    return chat_results, request_context, auth_info


def _run_chat(
    chat_handler: ChatThirdPartyHandler,
    chat_request: chat_pb2.ChatRequest,
    request_id: uuid.UUID | None = None,
    session_id: uuid.UUID | None = None,
) -> tuple[ChatResult, RequestContext, AuthInfo]:
    chat_results, request_context, auth_info = _run_chat_stream(
        chat_handler, chat_request, request_id, session_id
    )
    full_response = ""
    unknown_blob_names = []
    checkpoint_not_found = False
    for output in chat_results:
        full_response += output.text
        unknown_blob_names.extend(output.unknown_blob_names)
        checkpoint_not_found = checkpoint_not_found or output.checkpoint_not_found
    chat_result = ChatResult(full_response, unknown_blob_names, checkpoint_not_found)
    return chat_result, request_context, auth_info


def response_to_iterator(response_str: str) -> typing.Iterator[ThirdPartyModelResponse]:
    """Yields the response as an iterator."""
    responses = response_str.split(" ")
    for response in responses[:-1]:
        yield ThirdPartyModelResponse(response + " ")
    yield ThirdPartyModelResponse(responses[-1])
    yield ThirdPartyModelResponse(
        "", end_of_stream=EndOfStream(stop_reason=StopReason.END_TURN)
    )


def response_to_iterator_error(
    response_str: str,
) -> typing.Iterator[ThirdPartyModelResponse]:
    """Yields the response as an iterator, but raises an exception on the last response."""
    responses = response_str.split(" ")
    for response in responses[:-1]:
        yield ThirdPartyModelResponse(response + " ")
    yield ThirdPartyModelResponse(responses[-1])
    raise Exception("BOOM!")


class TestChatThirdPartyHandler(TestCase):
    def test_basic_stream(self):
        """Tests the basic usage."""
        chat_handler = _get_chat_third_party_handler()
        iterator, _, _ = _run_chat_stream(chat_handler, _get_chat_request())
        full_response = ""
        for output in iterator:
            full_response += output.text
        assert full_response == "Fake Response"

    def test_persona_type(self):
        """Tests that the persona_type is passed to the prompt formatter."""
        chat_handler = _get_chat_third_party_handler()
        original_format_prompt = chat_handler.prompt_formatter.format_prompt

        def mock_format_prompt(prompt_input):
            # Check the persona_type based on the current test case
            if hasattr(self, "current_persona_type") and self.current_persona_type == 1:
                assert prompt_input.persona_type == PersonaType.PROTOTYPER
            else:
                assert prompt_input.persona_type == PersonaType.DEFAULT
            return original_format_prompt(prompt_input)

        chat_handler.prompt_formatter.format_prompt = mock_format_prompt

        # Test with default persona type
        self.current_persona_type = 0
        _, _, _ = _run_chat_stream(chat_handler, _get_chat_request())

        # Test with prototyper persona type
        self.current_persona_type = 1
        _, _, _ = _run_chat_stream(chat_handler, _get_chat_request(persona_type=1))


@patch("services.chat_host.server.chat_third_party_handler.ChatRequestInsightBuilder")
def test_basic_ri(ChatRequestInsightBuilder_Mock):
    """Tests the basic usage of request insight."""
    full_text = "def foo(a, b)"
    chat_handler = _get_chat_third_party_handler(
        third_party_response=response_to_iterator(full_text)
    )
    assert chat_handler.retriever is not None
    chat_request = _get_chat_request()
    chat_response = chat_pb2.ChatResponse(
        text=full_text,
        unknown_blob_names=[],
        checkpoint_not_found=False,
        nodes=[
            chat_pb2.ChatResultNode(
                id=0,
                type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
                content=full_text,
            ),
            chat_pb2.ChatResultNode(
                id=2,
                type=chat_pb2.ChatResultNodeType.MAIN_TEXT_FINISHED,
                content="",
            ),
            chat_pb2.ChatResultNode(
                id=3,
                type=chat_pb2.ChatResultNodeType.WORKSPACE_FILE_CHUNKS,
                content="",
            ),
        ],
        stop_reason=chat_pb2.ChatStopReason.END_TURN,
    )
    _, request_context, auth_info = _run_chat(chat_handler, chat_request)

    assert ChatRequestInsightBuilder_Mock.call_count == 1
    ChatRequestInsightBuilder_Mock.return_value.record_request.assert_called_once()
    ChatRequestInsightBuilder_Mock.return_value.record_response.assert_called_once()

    ChatRequestInsightBuilder_Mock.return_value.record_request.assert_called_with(
        ANY,
        None,
        chat_request,
        request_context=request_context,
        auth_info=auth_info,
        external_source_ids=[],
    )
    ChatRequestInsightBuilder_Mock.return_value.record_response.assert_called_with(
        chat_response,
        tokenizer=None,
        truncated_output=full_text,
        truncated_log_probs=None,
        request_context=request_context,
        auth_info=auth_info,
        error_message=None,
    )


@patch("services.chat_host.server.chat_third_party_handler.ChatRequestInsightBuilder")
def test_basic_ri_mid_stream_error(ChatRequestInsightBuilder_Mock):
    """Tests the basic usage of request insight."""
    full_text = "def foo(a, b)"
    chat_handler = _get_chat_third_party_handler(
        third_party_response=response_to_iterator_error(full_text)
    )
    assert chat_handler.retriever is not None
    chat_request = _get_chat_request()
    chat_response = chat_pb2.ChatResponse(
        text=full_text,
        unknown_blob_names=[],
        checkpoint_not_found=False,
        stop_reason=None,
    )
    response_iterator, request_context, auth_info = _run_chat_stream(
        chat_handler, chat_request
    )
    response_text = ""
    with pytest.raises(Exception):
        for response in response_iterator:
            response_text += response.text

    assert response_text == "def foo(a, "

    assert ChatRequestInsightBuilder_Mock.call_count == 1
    ChatRequestInsightBuilder_Mock.return_value.record_request.assert_called_once()
    ChatRequestInsightBuilder_Mock.return_value.record_response.assert_called_once()

    ChatRequestInsightBuilder_Mock.return_value.record_request.assert_called_with(
        ANY,
        None,
        chat_request,
        request_context=request_context,
        auth_info=auth_info,
        external_source_ids=[],
    )
    ChatRequestInsightBuilder_Mock.return_value.record_response.assert_called_with(
        chat_response,
        tokenizer=None,
        truncated_output=full_text,
        truncated_log_probs=None,
        request_context=request_context,
        auth_info=auth_info,
        error_message="Exception: BOOM!",
    )


def test_general_retrieve():
    """Tests basic usage of _retrieve()."""
    chat_request = _get_chat_request(prefix_begin=5, suffix_end=15, path="/p1")

    chunks = [
        RetrievalChunk(
            text="a",
            path="/p1",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
        ),
        RetrievalChunk(
            text="b",
            path="/p1",
            char_start=30,
            char_end=40,
            blob_name=None,
            chunk_index=None,
        ),
        RetrievalChunk(
            text="c",
            path="/p3",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
        ),
    ]

    missing_blob_names = ["b1", "b2"]

    chat_handler = _get_chat_third_party_handler(
        retrieval_result=RetrievalResult(
            chunks, missing_blob_names, checkpoint_not_found=False
        )
    )

    assert chat_handler.retriever is not None

    auth_info = MagicMock()
    with ThreadPoolExecutor() as executor:
        retrieval_output = chat_handler.retrieve(
            chat_request,
            RequestContext.create(),
            auth_info=auth_info,
            executor=executor,
        )

    retrieved_chunks = list(retrieval_output.get_retrieved_chunks())
    output_missing_blob_names = set(retrieval_output.get_missing_blob_names())
    assert len(retrieved_chunks) == 3
    assert output_missing_blob_names == set(missing_blob_names)
    assert retrieved_chunks == list(chunks)


def test_retrieve_call():
    """Tests that handler's .chat_stream() correctly calls retrieve()."""
    chat_handler = _get_chat_third_party_handler()
    chat_request = _get_chat_request()

    chat_handler.retrieve = MagicMock(chat_handler.retrieve)

    request_context = RequestContext.create()
    auth_info = MagicMock()

    executor = ThreadPoolExecutor()
    list(chat_handler.chat_stream(chat_request, request_context, auth_info, executor))

    chat_handler.retrieve.assert_called_with(
        chat_request, request_context, auth_info, executor=executor
    )

    executor.shutdown()


def test_retriever_call():
    """Tests that Retriever.retrieve is called correctly."""
    chat_handler = _get_chat_third_party_handler()
    chat_request = _get_chat_request()

    request_context = RequestContext.create()
    auth_info = MagicMock()

    executor = ThreadPoolExecutor()
    list(chat_handler.chat_stream(chat_request, request_context, auth_info, executor))

    expected_retrieval_args = {
        "input_": RetrievalInput(
            ChatRetrieverPromptInput(
                prefix="def ",
                suffix="(a, b)",
                path="src/foo.py",
                selected_code="bar",
                message="Rename bar to foo",
                chat_history=[
                    Exchange(
                        request_message="How you doing?", response_text="Fine thanks"
                    )
                ],
                prefix_begin=0,
                suffix_end=12,
                blob_name="blob0",
            ),
            blobs=[Blobs.from_fake_blob_names(["blob1", "blob2"])],
            disable_auto_external_sources=True,
        ),
        "request_context": request_context,
        "auth_info": auth_info,
        "executor": executor,
    }

    assert isinstance(chat_handler.retriever, MagicMock)
    chat_handler.retriever.retrieve.assert_called_with(**expected_retrieval_args)

    executor.shutdown()


def test_retriever_call_with_checkpoint():
    """Tests that Retriever.retrieve is called correctly with a checkpoint."""
    chat_handler = _get_chat_third_party_handler()
    blobs = Blobs.from_fake_blob_names(["blob3", "blob4"])
    chat_request = _get_chat_request(blobs=blobs.to_proto())

    request_context = RequestContext.create()
    auth_info = MagicMock()

    executor = ThreadPoolExecutor()
    list(chat_handler.chat_stream(chat_request, request_context, auth_info, executor))

    expected_retrieval_args = {
        "input_": RetrievalInput(
            ChatRetrieverPromptInput(
                prefix="def ",
                suffix="(a, b)",
                path="src/foo.py",
                selected_code="bar",
                message="Rename bar to foo",
                chat_history=[
                    Exchange(
                        request_message="How you doing?", response_text="Fine thanks"
                    )
                ],
                prefix_begin=0,
                suffix_end=12,
                blob_name="blob0",
            ),
            blobs=[blobs],
            disable_auto_external_sources=True,
        ),
        "request_context": request_context,
        "auth_info": auth_info,
        "executor": executor,
    }

    assert isinstance(chat_handler.retriever, MagicMock)
    chat_handler.retriever.retrieve.assert_called_with(**expected_retrieval_args)

    executor.shutdown()


def test_empty_blob_list():
    """Tests that retriever is still called when blob list is empty (AU-6477)."""
    chat_handler = _get_chat_third_party_handler()
    chat_request = _get_chat_request(
        blobs=blob_names_pb2.Blobs(baseline_checkpoint_id=None, added=[], deleted=[]),
    )

    request_context = RequestContext.create()
    auth_info = MagicMock()
    with ThreadPoolExecutor() as executor:
        retrieval_output = chat_handler.retrieve(
            chat_request,
            request_context,
            auth_info=auth_info,
            executor=executor,
        )

    assert len(list(retrieval_output.get_retrieved_chunks())) == 0
    assert len(list(retrieval_output.get_missing_blob_names())) == 0
    assert isinstance(chat_handler.retriever, MagicMock)
    chat_handler.retriever.retrieve.assert_called_once()


def test_chat_with_docsets():
    """Tests the chat function with docsets."""
    chat_request = _get_chat_request(prefix_begin=5, suffix_end=15, path="/p1")

    chunks = [
        RetrievalChunk(
            text="a",
            path="/p1",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
        ),
        RetrievalChunk(
            text="b",
            path="",
            char_start=30,
            char_end=40,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            documentation_metadata=DocumentationMetadata(
                source_id="docset://graphite.dev",
                name="graphite.dev",
                page_id="page_id2",
                headers=["header1", "header2"],
            ),
        ),
        RetrievalChunk(
            text="c",
            path="",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            documentation_metadata=DocumentationMetadata(
                source_id="docset://graphite.dev",
                name="graphite.dev",
                page_id="page_id2",
                headers=["header1", "header2"],
            ),
        ),
        RetrievalChunk(
            text="c",
            path="",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            documentation_metadata=DocumentationMetadata(
                source_id="docset://bazel",
                name="bazel",
                page_id="page_id3",
                headers=["header1", "header2"],
            ),
        ),
    ]

    missing_blob_names = ["b1", "b2"]

    chat_handler = _get_chat_third_party_handler(
        retrieval_result=RetrievalResult(
            chunks, missing_blob_names, checkpoint_not_found=False
        ),
        third_party_response=response_to_iterator("Fake Response"),
    )

    # Replace the prompt formatter with a mock
    chat_handler.prompt_formatter = MagicMock()

    # Create a structured prompt output with the retrieval chunks
    structured_output = StructuredChatPromptOutput(
        system_prompt="system prompt",
        chat_history=[],
        message="message",
        retrieved_chunks_in_prompt=chunks,
    )

    # Set the mock to return our structured output
    chat_handler.prompt_formatter.format_prompt.return_value = structured_output

    blobs = Blobs.from_fake_blob_names(["blob3", "blob4"])
    chat_request = _get_chat_request(blobs=blobs.to_proto())

    request_context = RequestContext.create()
    auth_info = MagicMock()

    with SynchronousExecutor() as executor:
        # Use chat_stream and collect all responses
        chat_results = list(
            chat_handler.chat_stream(
                chat_request, request_context, auth_info, executor=executor
            )
        )

        # Find any response with incorporated_external_sources
        has_external_sources = False
        for result in chat_results:
            if result.incorporated_external_sources:
                has_external_sources = True
                assert len(result.incorporated_external_sources) == 2
                break

        assert (
            has_external_sources
        ), "No response contained incorporated_external_sources"


def test_chat_stream_with_docsets():
    """Tests the chat function with docsets."""
    chat_request = _get_chat_request(prefix_begin=5, suffix_end=15, path="/p1")

    chunks = [
        RetrievalChunk(
            text="a",
            path="/p1",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
        ),
        RetrievalChunk(
            text="b",
            path="",
            char_start=30,
            char_end=40,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            documentation_metadata=DocumentationMetadata(
                source_id="docset://graphite.dev",
                name="graphite.dev",
                page_id="page_id2",
                headers=["header1", "header2"],
            ),
        ),
        RetrievalChunk(
            text="c",
            path="",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            documentation_metadata=DocumentationMetadata(
                source_id="docset://graphite.dev",
                name="graphite.dev",
                page_id="page_id2",
                headers=["header1", "header2"],
            ),
        ),
        RetrievalChunk(
            text="c",
            path="",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            documentation_metadata=DocumentationMetadata(
                source_id="docset://bazel",
                name="bazel",
                page_id="page_id3",
                headers=["header1", "header2"],
            ),
        ),
    ]

    missing_blob_names = ["b1", "b2", "b3"]

    chat_handler = _get_chat_third_party_handler(
        retrieval_result=RetrievalResult(
            chunks, missing_blob_names, checkpoint_not_found=False
        ),
        third_party_response=response_to_iterator("Fake Response"),
    )

    blobs = Blobs.from_fake_blob_names(["blob3", "blob4"])
    chat_request = _get_chat_request(blobs=blobs.to_proto())

    request_context: RequestContext = RequestContext.create()
    auth_info = MagicMock()

    with SynchronousExecutor() as executor:
        chat_result = chat_handler.chat_stream(
            chat_request, request_context, auth_info, executor=executor
        )
        sources_results = []
        workspace_file_chunks = []
        sources_counts_per_chunk = []
        unknown_blob_names_counts_per_chunk = []
        count = 0
        for result in chat_result:
            workspace_file_chunks.extend(result.workspace_file_chunks)
            sources_counts_per_chunk.append(len(result.incorporated_external_sources))
            unknown_blob_names_counts_per_chunk.append(len(result.unknown_blob_names))
            for source in result.incorporated_external_sources:
                assert source.source_name in ["graphite.dev", "bazel"]
                assert source.source_id in ["docset://graphite.dev", "docset://bazel"]
                sources_results.append(source.source_name)
            count += 1

        assert workspace_file_chunks == [chunks[0].to_prompt_chunk()]
        assert len(sources_results) == 2
        # First two chunks are incorporated external sources and unknown blobs
        assert sources_counts_per_chunk == [2] + [0] * (
            count - 1
        ), "Expected missing blob names in the first chunk only"
        assert unknown_blob_names_counts_per_chunk == [3] + [0] * (
            count - 1
        ), "Expected missing blob names in the first chunk only"


@pytest.mark.parametrize(
    [
        "max_implicit_external_context_tokens",
        "max_explicit_external_context_tokens",
        "external_sources_ids",
        "incorporated_external_sources",
    ],
    [
        # No implicit budge, no external sources should be retrieved
        (0, 1000, [], []),
        # some implicit budget, graphite is retrieved
        (100, 1000, [], ["docset://graphite.dev", "docset://bazel"]),
        # explicit budget is used
        (0, 1000, ["docset://bazel"], ["docset://graphite.dev", "docset://bazel"]),
    ],
    ids=[
        "no_implicit_budget",
        "some_implicit_budget",
        "explicit_budget",
    ],
)
def test_chat_stream_with_docsets_within_budget(
    max_implicit_external_context_tokens,
    max_explicit_external_context_tokens,
    external_sources_ids,
    incorporated_external_sources,
):
    """Tests the chat function with docsets and workspace files that don't exceed the budget.

    When workspace files are within the budget, all external sources should be included.
    """
    # Each chunk is about 80 tokens
    chunks = [
        RetrievalChunk(
            text="The flags for graphite command `gt move` are --onto and --source. ",
            path="gt-0",
            char_start=30,
            char_end=222,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            documentation_metadata=DocumentationMetadata(
                source_id="docset://graphite.dev",
                name="graphite.dev",
                page_id="page_id2",
                headers=["graphite documentation", "gt move"],
            ),
            score=0.9,
        ),
        RetrievalChunk(
            text="global options that works on `gt move` are --verify and --quiet",
            path="gt-1",
            char_start=250,
            char_end=566,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            documentation_metadata=DocumentationMetadata(
                source_id="docset://graphite.dev",
                name="graphite.dev",
                page_id="page_id2",
                headers=["graphite documentation", "gt move"],
            ),
            score=0.8,
        ),
        RetrievalChunk(
            text="1 A completely unrelated chunk. It should NOT be relevant.",
            path="/p1",
            char_start=0,
            char_end=232,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            score=0.002,
        ),
        RetrievalChunk(
            text="2 A completely unrelated chunk. It should NOT be relevant.",
            path="/p2",
            char_start=250,
            char_end=500,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            score=0.001,
        ),
        RetrievalChunk(
            text="bazel build: Builds the specified targets. ",
            path="bazel-0",
            char_start=0,
            char_end=258,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            documentation_metadata=DocumentationMetadata(
                source_id="docset://bazel",
                name="bazel",
                page_id="page_id3",
                headers=["bazel documentation", "bazel build"],
            ),
            score=0.0001,
        ),
    ]

    missing_blob_names = ["b1", "b2", "b3"]

    # config 1.  We should only get 2 workspace files then run out of tokens
    update_config1 = {
        "token_apportionment": {
            "prefix_len": 10,
            "suffix_len": 10,
            "path_len": 10,
            "chat_history_len": 0,
            "max_prompt_len": 16 * 1024 - 4096,
            "retrieval_len": -1,
            "implicit_external_context_len": max_implicit_external_context_tokens,
            "explicit_external_context_len": max_explicit_external_context_tokens,
            "overflow_external_context": False,
        },
    }
    assert len(chunks) == 5
    chat_handler = _get_chat_third_party_handler(
        updates_for_handler_config=update_config1,
        retrieval_result=RetrievalResult(
            chunks, missing_blob_names, checkpoint_not_found=False
        ),
        third_party_response=response_to_iterator("Fake Response"),
    )

    chat_request = _get_chat_request(
        prefix="",
        selected_code="",
        suffix="",
        message="what are the arguments for `gt move`? ",
        prefix_begin=5,
        suffix_end=15,
        path="/input_file",
        external_source_ids=external_sources_ids,
    )

    request_context: RequestContext = RequestContext.create()
    auth_info = MagicMock()

    with SynchronousExecutor() as executor:
        chat_result = chat_handler.chat_stream(
            chat_request, request_context, auth_info, executor=executor
        )
        sources_results = []
        workspace_file_chunks = []
        sources_counts_per_chunk = []
        unknown_blob_names_counts_per_chunk = []
        count = 0
        for result in chat_result:
            workspace_file_chunks.extend(result.workspace_file_chunks)
            sources_counts_per_chunk.append(len(result.incorporated_external_sources))
            unknown_blob_names_counts_per_chunk.append(len(result.unknown_blob_names))
            for source in result.incorporated_external_sources:
                assert source.source_name in ["graphite.dev", "bazel"]
                assert source.source_id in ["docset://graphite.dev", "docset://bazel"]
                sources_results.append(source.source_id)
            count += 1
        assert set(sources_results) == set(incorporated_external_sources)
        assert len(workspace_file_chunks) == 2
        assert sorted(item.text for item in workspace_file_chunks) == sorted(
            [
                chunks[-3].to_prompt_chunk().text,
                chunks[-2].to_prompt_chunk().text,
            ]
        )


def test_chat_stream_with_docsets_overflow_budget():
    """Tests the chat function with docsets and workspace files that exceed the budget.

    When workspace files exceed the budget, external sources less relevant than all
    workspace files should be excluded, even when overflowing.
    """
    # one relevant docset, a lot of workspace chunks, then one irrelevant external chunk
    chunks = (
        [
            RetrievalChunk(
                text="The flags for graphite command `gt move` are --onto and --source. ",
                path="gt-0",
                char_start=30,
                char_end=222,
                blob_name=None,
                chunk_index=None,
                origin="dense_retriever",
                documentation_metadata=DocumentationMetadata(
                    source_id="docset://graphite.dev",
                    name="graphite.dev",
                    page_id="page_id2",
                    headers=["graphite documentation", "gt move"],
                ),
                score=0.9,
            ),
        ]
        + [
            RetrievalChunk(
                text="1 A completely unrelated chunk. It should NOT be relevant." * 20,
                path=f"/item-{i}",
                char_start=250 * i + 1,
                char_end=250 * (i + 1),
                blob_name=None,
                chunk_index=None,
                origin="dense_retriever",
                score=0.02,
            )
            for i in range(20)
        ]
        + [
            RetrievalChunk(
                text="bazel build: Builds the specified targets. ",
                path="bazel-0",
                char_start=0,
                char_end=258,
                blob_name=None,
                chunk_index=None,
                origin="dense_retriever",
                documentation_metadata=DocumentationMetadata(
                    source_id="docset://bazel",
                    name="bazel",
                    page_id="page_id3",
                    headers=["bazel documentation", "bazel build"],
                ),
                score=0.0001,
            ),
        ]
    )

    missing_blob_names = ["b1", "b2", "b3"]

    update_config1 = {
        "token_apportionment": {
            "prefix_len": 10,
            "suffix_len": 10,
            "path_len": 10,
            "chat_history_len": 0,
            "max_prompt_len": 4096,
            "retrieval_len": -1,
            "implicit_external_context_len": 2500,
            "explicit_external_context_len": 5000,
            "overflow_external_context": True,
        },
    }
    chat_handler = _get_chat_third_party_handler(
        updates_for_handler_config=update_config1,
        retrieval_result=RetrievalResult(
            chunks, missing_blob_names, checkpoint_not_found=False
        ),
        third_party_response=response_to_iterator("Fake Response"),
    )

    chat_request = _get_chat_request(
        prefix="",
        selected_code="",
        suffix="",
        message="what are the arguments for `gt move`? ",
        prefix_begin=5,
        suffix_end=15,
        path="/input_file",
        external_source_ids=["docset://graphite.dev", "docset://bazel"],
    )

    request_context: RequestContext = RequestContext.create()
    auth_info = MagicMock()

    with SynchronousExecutor() as executor:
        chat_result = chat_handler.chat_stream(
            chat_request, request_context, auth_info, executor=executor
        )
        sources_results = []
        count = 0
        for result in chat_result:
            for source in result.incorporated_external_sources:
                assert source.source_name in ["graphite.dev", "bazel"]
                assert source.source_id in ["docset://graphite.dev", "docset://bazel"]
                sources_results.append(source.source_id)
            count += 1
        assert set(sources_results) == {
            "docset://graphite.dev"
        }  # only graphite.dev is relevant


def test_generate_commit_message_prompt_formatter_call():
    """Tests that the generate-commit-message prompt formatter is called."""
    chat_handler = _get_chat_third_party_handler()
    chat_handler.commit_message_prompt_formatter = _get_chat_prompt_formatter()
    chat_handler.commit_message_prompt_formatter.format_prompt.return_value = (
        StructuredChatPromptOutput(
            system_prompt="system prompt",
            chat_history=[],
            message="message",
            retrieved_chunks_in_prompt=[],
        )
    )
    chat_request = _get_chat_request(
        prefix="def ",
        selected_code="bar",
        suffix="(a, b)",
        message="Rename bar to foo",
        chat_history=[],
        prefix_begin=0,
        suffix_end=10,
        path="src/foo.py",
        blob_name="blob0",
        lang="python",
        blobs=Blobs.from_fake_blob_names(["blob1", "blob2"]).to_proto(),
        enable_preference_collection=False,
        user_guided_blobs=None,
        prompt_formatter_name="generate-commit-message",
    )

    auth_info = MagicMock()

    with SynchronousExecutor() as executor:
        list(
            chat_handler.chat_stream(
                chat_request,
                request_context=RequestContext.create(),
                auth_info=auth_info,
                executor=executor,
            )
        )

    chat_handler.commit_message_prompt_formatter.format_prompt.assert_called_once()


def test_codeblock_parsing(feature_flags_context):
    """Tests that the codeblock parsing is working."""

    model_raw_response = """line1
line2
```
<augment_code_snippet path="src/foo.py" mode="EDIT">
line3
line4
</augment_code_snippet>
```
line5
"""

    parsed_gt = """line1
line2
```` path=src/foo.py mode=EDIT
line3
line4
````
line5
"""

    chat_handler = _get_chat_third_party_handler(
        third_party_response=response_to_iterator(model_raw_response),
        client_type="anthropic_vertexai",
        model_name="claude-3-5-sonnet@20240620",
        prompt_formatter_name="binks-claude-v4",
    )
    iterator, _, _ = _run_chat_stream(chat_handler, _get_chat_request())
    all_responses = list(iterator)
    full_response = ""
    for output in all_responses:
        full_response += output.text
    assert full_response == parsed_gt
    assert all_responses[-3].nodes == [
        ChatResultNode(0, ChatResultNodeType.RAW_RESPONSE, model_raw_response)
    ]
    assert all_responses[-2].nodes == [
        # Reason unspecified due to only mocking text output
        ChatResultNode(id=2, type=ChatResultNodeType.MAIN_TEXT_FINISHED, content="")
    ]
    assert all_responses[-1].nodes == [
        ChatResultNode(id=3, type=ChatResultNodeType.WORKSPACE_FILE_CHUNKS, content="")
    ]


def test_codeblock_parsing_with_outer_backticks(feature_flags_context):
    """Tests that the codeblock parsing is working."""

    model_raw_response = """line1
line2
<augment_code_snippet path="src/foo.py" mode="EDIT">
```
line3
line4
```
</augment_code_snippet>
line5
"""

    parsed_gt = """line1
line2
```` path=src/foo.py mode=EDIT
line3
line4
````
line5
"""

    # Then test with chat_return_raw_response=True
    chat_handler = _get_chat_third_party_handler(
        third_party_response=response_to_iterator(model_raw_response),
        client_type="anthropic_vertexai",
        model_name="claude-3-5-sonnet@20240620",
        prompt_formatter_name="binks-claude-v4",
    )
    iterator, _, _ = _run_chat_stream(chat_handler, _get_chat_request())
    all_responses = list(iterator)
    full_response = ""
    for output in all_responses:
        full_response += output.text
    assert full_response == parsed_gt
    assert all_responses[-3].nodes == [
        ChatResultNode(0, ChatResultNodeType.RAW_RESPONSE, model_raw_response)
    ]
    assert all_responses[-2].nodes == [
        ChatResultNode(id=2, type=ChatResultNodeType.MAIN_TEXT_FINISHED, content="")
    ]
    assert all_responses[-1].nodes == [
        ChatResultNode(id=3, type=ChatResultNodeType.WORKSPACE_FILE_CHUNKS, content="")
    ]


def test_chat_stream_with_image():
    """Tests that image nodes are properly formatted and sent to the model."""
    chat_handler = _get_chat_third_party_handler(
        client_type="anthropic_vertexai",
        model_name="claude-3-5-sonnet@20240620",
        prompt_formatter_name="binks-claude-v4",
    )

    # Create image node
    image_node = chat_pb2.ChatRequestNode(
        id=0,  # Add id
        type=chat_pb2.ChatRequestNodeType.IMAGE,
        image_node=chat_pb2.ChatRequestImage(
            image_data="base64_encoded_image_data",
            format=chat_common.ImageFormatType.PNG,
        ),
    )

    # Create chat request with the image node
    chat_request = _get_chat_request(chat_request_nodes=[image_node])

    # Mock the response
    assert isinstance(chat_handler.client, MagicMock)
    chat_handler.client.generate_response_stream.return_value = iter(
        [ThirdPartyModelResponse(text="I see an image")]
    )

    response_iterator, request_context, _ = _run_chat_stream(chat_handler, chat_request)

    # Consume the iterator to ensure the mock is called
    responses = list(response_iterator)

    # Get the formatted request that was sent to the model
    assert len(chat_handler.client.generate_response_stream.call_args_list) == 1
    call_args = chat_handler.client.generate_response_stream.call_args
    formatted_message = call_args.kwargs["cur_message"]

    # Verify the image was properly formatted
    assert len(formatted_message) == 1
    assert formatted_message[0].type == ChatRequestNodeType.IMAGE
    assert formatted_message[0].image_node.image_data == "base64_encoded_image_data"
    assert formatted_message[0].image_node.format == ImageFormatType.PNG

    # Verify we get back the expected response
    assert len(responses) > 0
    assert any(r.text == "I see an image" for r in responses)


def test_chat_stream_with_invalid_image_format():
    """Tests that invalid image formats are properly handled."""
    chat_handler = _get_chat_third_party_handler(
        client_type="anthropic_vertexai",
        model_name="claude-3-5-sonnet@20240620",
        prompt_formatter_name="binks-claude-v4",
    )

    # Mock the client to raise InvalidArgumentRpcError for invalid format
    assert isinstance(chat_handler.client, MagicMock)
    chat_handler.client.generate_response_stream.side_effect = InvalidArgumentRpcError(
        "Image format must be specified"
    )

    # Create image node with invalid format
    image_node = chat_pb2.ChatRequestNode(
        id=0,  # Add id
        type=chat_pb2.ChatRequestNodeType.IMAGE,
        image_node=chat_pb2.ChatRequestImage(
            image_data="base64_encoded_image_data",
            format=chat_common.ImageFormatType.IMAGE_FORMAT_UNSPECIFIED,
        ),
    )

    # Create chat request with the invalid image node
    chat_request = _get_chat_request(chat_request_nodes=[image_node])

    response_iterator, request_context, _ = _run_chat_stream(chat_handler, chat_request)

    with pytest.raises(InvalidArgumentRpcError) as exc_info:
        next(iter(response_iterator))

    assert "Image format must be specified" in str(exc_info.value)


def test_chat_stream_with_multiple_nodes():
    """Tests that a mix of text and image nodes are properly handled."""
    chat_handler = _get_chat_third_party_handler(
        client_type="anthropic_vertexai",
        model_name="claude-3-5-sonnet@20240620",
        prompt_formatter_name="binks-claude-v4",
    )

    # Create text and image nodes
    text_node = chat_pb2.ChatRequestNode(
        id=0,  # Add id
        type=chat_pb2.ChatRequestNodeType.TEXT,
        text_node=chat_pb2.ChatRequestText(content="Here's an image:"),
    )

    image_node = chat_pb2.ChatRequestNode(
        id=1,  # Add id
        type=chat_pb2.ChatRequestNodeType.IMAGE,
        image_node=chat_pb2.ChatRequestImage(
            image_data="base64_encoded_image_data",
            format=chat_common.ImageFormatType.JPEG,
        ),
    )

    ide_state_node = chat_pb2.ChatRequestNode(
        id=2,  # Add id
        type=chat_pb2.ChatRequestNodeType.IDE_STATE,
        ide_state_node=chat_pb2.ChatRequestIdeState(
            workspace_folders=[
                chat_pb2.WorkspaceFolderInfo(
                    repository_root="/test/repo",
                    folder_root="/test/folder",
                ),
            ],
            workspace_folders_unchanged=False,
            current_terminal=chat_pb2.TerminalInfo(
                terminal_id=1,
                current_working_directory="/test/terminal",
            ),
        ),
    )

    edit_events_node = chat_pb2.ChatRequestNode(
        id=3,  # Add id
        type=chat_pb2.ChatRequestNodeType.EDIT_EVENTS,
        edit_events_node=chat_pb2.ChatRequestEditEvents(
            edit_events=[
                chat_pb2.ChatRequestFileEdit(
                    path="/test/file",
                    before_blob_name="before",
                    after_blob_name="after",
                    edits=[
                        chat_pb2.ChatRequestSingleEdit(
                            before_line_start=1,
                            before_text="before",
                            after_line_start=1,
                            after_text="after",
                        ),
                    ],
                ),
            ],
        ),
    )

    # Create chat request with both nodes
    chat_request = _get_chat_request(
        chat_request_nodes=[text_node, image_node, ide_state_node, edit_events_node]
    )

    # Mock the response
    assert isinstance(chat_handler.client, MagicMock)
    chat_handler.client.generate_response_stream.return_value = iter(
        [ThirdPartyModelResponse(text="I see text and an image")]
    )

    response_iterator, _, _ = _run_chat_stream(chat_handler, chat_request)

    # Consume the iterator to ensure the mock is called
    responses = list(response_iterator)

    # Get the formatted request that was sent to the model
    assert len(chat_handler.client.generate_response_stream.call_args_list) == 1
    call_args = chat_handler.client.generate_response_stream.call_args
    formatted_message = call_args.kwargs["cur_message"]

    # Verify both nodes were properly formatted
    assert len(formatted_message) == 4
    assert formatted_message[0].type == ChatRequestNodeType.TEXT
    assert formatted_message[0].text_node.content == "Here's an image:"
    assert formatted_message[1].type == ChatRequestNodeType.IMAGE
    assert formatted_message[1].image_node.image_data == "base64_encoded_image_data"
    assert formatted_message[1].image_node.format == ImageFormatType.JPEG
    assert formatted_message[2].type == ChatRequestNodeType.IDE_STATE
    assert formatted_message[3].type == ChatRequestNodeType.EDIT_EVENTS

    # Verify we get back the expected response
    assert len(responses) > 0
    assert any(r.text == "I see text and an image" for r in responses)


def test_chat_stream_with_postprocess_model():
    """Tests that the chat stream works with the postprocess model."""
    ORIGINAL_RESPONSE_TEXT = "<augment_code_snippet>\n```\nOriginal response text\n```\n</augment_code_snippet>\n"  # Chat model response
    EXPECTED_RESPONSE_TEXT = (
        "````\nOriginal response text\n````\n"  # After stream process
    )
    PROCESSED_RESPONSE_TEXT = "<augment_code_snippet>\n```\nProcessed response text\n```\n</augment_code_snippet>\n"  # After sentry
    FINAL_RESPONSE_TEXT = (
        "````\nProcessed response text\n````\n"  # After sentry and stream process
    )

    postprocess = MagicMock(spec=Postprocess)
    postprocess.model_name = "test_postprocess"
    postprocess.postprocess = MagicMock(spec=Postprocess)
    postprocess.postprocess.return_value = PostprocessResponse(
        text=PROCESSED_RESPONSE_TEXT,
        tokens=[1, 2, 3],
        log_probs=[0.1, 0.2, 0.3],
    )
    postprocess.tokenizer = MagicMock(Tokenizer)
    postprocess.tokenizer.detokenize.return_value = PROCESSED_RESPONSE_TEXT
    postprocess.tokenizer.detokenize_with_offsets.return_value = (
        PROCESSED_RESPONSE_TEXT,
        [0, 4, 8],
    )

    chat_handler = _get_chat_third_party_handler(
        client_type="anthropic_vertexai",
        model_name="claude-3-5-sonnet@20240620",
        prompt_formatter_name="binks-claude-v4",
        postprocess=postprocess,
    )

    chat_request = _get_chat_request()

    # Mock the response
    assert isinstance(chat_handler.client, MagicMock)
    chat_handler.client.generate_response_stream.return_value = iter(
        [
            ThirdPartyModelResponse(
                text=ORIGINAL_RESPONSE_TEXT,
            )
        ]
    )

    response_iterator, _, _ = _run_chat_stream(chat_handler, chat_request)

    # Consume the iterator to ensure the mock is called
    responses = list(response_iterator)

    # Verify we get back the expected response
    assert len(responses) > 0

    # Full text is the original response
    assert "".join([r.text for r in responses if r.text]) == EXPECTED_RESPONSE_TEXT

    all_nodes = [n for r in responses for n in r.nodes]

    # The raw response node should be the sentry-postprocessed text
    raw_content_node = next(
        (n for n in all_nodes if n.type == ChatResultNodeType.RAW_RESPONSE), None
    )
    assert raw_content_node is not None
    assert raw_content_node.content == PROCESSED_RESPONSE_TEXT

    # The main text finished node should be the final text after stream processing
    main_text_finished_node = next(
        (n for n in all_nodes if n.type == ChatResultNodeType.MAIN_TEXT_FINISHED), None
    )
    assert main_text_finished_node is not None
    assert main_text_finished_node.content == FINAL_RESPONSE_TEXT


def test_chat_stream_postprocess_skipped_without_code_blocks(feature_flags_context):
    """Tests that postprocessing is skipped when response contains no code blocks."""
    # Enable the code block filter feature flag
    from services.chat_host.server.chat_third_party_handler import (
        _CHAT_POSTPROCESS_CODE_BLOCK_FILTER_ENABLED,
    )

    feature_flags_context.set_flag(_CHAT_POSTPROCESS_CODE_BLOCK_FILTER_ENABLED, True)
    RESPONSE_TEXT_NO_CODE = "This is a regular text response without any code blocks.\n"

    postprocess = MagicMock(spec=Postprocess)
    postprocess.model_name = "test_postprocess"
    postprocess.postprocess = MagicMock(spec=Postprocess)
    postprocess.tokenizer = MagicMock(Tokenizer)

    chat_handler = _get_chat_third_party_handler(
        client_type="anthropic_vertexai",
        model_name="claude-3-5-sonnet@20240620",
        prompt_formatter_name="binks-claude-v4",
        postprocess=postprocess,
    )

    chat_request = _get_chat_request()

    # Mock the response
    assert isinstance(chat_handler.client, MagicMock)
    chat_handler.client.generate_response_stream.return_value = iter(
        [
            ThirdPartyModelResponse(
                text=RESPONSE_TEXT_NO_CODE,
            )
        ]
    )

    response_iterator, _, _ = _run_chat_stream(chat_handler, chat_request)

    # Consume the iterator to ensure the mock is called
    responses = list(response_iterator)

    # Verify we get back the expected response
    assert len(responses) > 0

    # Verify postprocessing was NOT called since there are no code blocks
    postprocess.postprocess.assert_not_called()

    # The raw response should be the original text (not postprocessed)
    all_nodes = [n for r in responses for n in r.nodes]
    raw_content_node = next(
        (n for n in all_nodes if n.type == ChatResultNodeType.RAW_RESPONSE), None
    )
    assert raw_content_node is not None
    assert raw_content_node.content == RESPONSE_TEXT_NO_CODE


def test_chat_stream_postprocess_runs_when_filter_disabled(feature_flags_context):
    """Tests that postprocessing runs on all responses when code block filter is disabled."""
    # Disable the code block filter feature flag (default behavior)
    from services.chat_host.server.chat_third_party_handler import (
        _CHAT_POSTPROCESS_CODE_BLOCK_FILTER_ENABLED,
    )

    feature_flags_context.set_flag(_CHAT_POSTPROCESS_CODE_BLOCK_FILTER_ENABLED, False)

    RESPONSE_TEXT_NO_CODE = "This is a regular text response without any code blocks.\n"
    PROCESSED_RESPONSE_TEXT = (
        "This is a processed text response without any code blocks.\n"
    )

    postprocess = MagicMock(spec=Postprocess)
    postprocess.model_name = "test_postprocess"
    postprocess.postprocess = MagicMock(spec=Postprocess)
    postprocess.postprocess.return_value = PostprocessResponse(
        text=PROCESSED_RESPONSE_TEXT,
        tokens=[1, 2, 3],
        log_probs=[0.1, 0.2, 0.3],
    )
    postprocess.tokenizer = MagicMock(Tokenizer)

    chat_handler = _get_chat_third_party_handler(
        client_type="anthropic_vertexai",
        model_name="claude-3-5-sonnet@20240620",
        prompt_formatter_name="binks-claude-v4",
        postprocess=postprocess,
    )

    chat_request = _get_chat_request()

    # Mock the response
    assert isinstance(chat_handler.client, MagicMock)
    chat_handler.client.generate_response_stream.return_value = iter(
        [
            ThirdPartyModelResponse(
                text=RESPONSE_TEXT_NO_CODE,
            )
        ]
    )

    response_iterator, _, _ = _run_chat_stream(chat_handler, chat_request)

    # Consume the iterator to ensure the mock is called
    responses = list(response_iterator)

    # Verify we get back the expected response
    assert len(responses) > 0

    # Verify postprocessing WAS called even though there are no code blocks (filter disabled)
    postprocess.postprocess.assert_called_once()

    # The raw response should be the postprocessed text
    all_nodes = [n for r in responses for n in r.nodes]
    raw_content_node = next(
        (n for n in all_nodes if n.type == ChatResultNodeType.RAW_RESPONSE), None
    )
    assert raw_content_node is not None
    assert raw_content_node.content == PROCESSED_RESPONSE_TEXT


@patch("services.chat_host.server.chat_third_party_handler.ChatRequestInsightBuilder")
def test_chat_streaming_tool_use(ChatRequestInsightBuilder_Mock, feature_flags_context):
    """Tests that tool use nodes are properly handled.
    For now, we don't have full streaming, but we announce the start of a ToolUse
    generation, then later the full tool use node.
    When streaming we should see both.
    When not streaming, we should see the final tool use node only.
    """

    feature_flags_context.set_flag(_CHAT_GENERATE_TOOL_USE_START, True)

    generation: list[ThirdPartyModelResponse] = [
        ThirdPartyModelResponse(
            text="I'll write a program",
        ),
        ThirdPartyModelResponse(
            text="",
            tool_use_start=ToolUseStart(
                tool_use_id="tool-123",
                tool_name="get_date",
            ),
        ),
        ThirdPartyModelResponse(
            text="",
            tool_use=ToolUseResponse(
                tool_use_id="tool-123",
                tool_name="get_date",
                input={"param": "value"},
            ),
        ),
        ThirdPartyModelResponse(
            text="",
            end_of_stream=EndOfStream(stop_reason=StopReason.TOOL_USE_REQUESTED),
        ),
    ]
    chat_handler = _get_chat_third_party_handler(
        third_party_response=generation,
        client_type="anthropic_vertexai",
        model_name="claude-3-5-sonnet@20240620",
        prompt_formatter_name="binks-claude-v4",
    )
    request = _get_chat_request()
    request.feature_detection_flags.support_tool_use_start = True
    iterator, _, _ = _run_chat_stream(chat_handler, request)
    responses = list(iterator)

    assert responses.pop(0) == ChatResult(
        text="",
        unknown_blob_names=[],
        checkpoint_not_found=False,
    )
    assert responses.pop(0) == ChatResult(
        text="I'll write a program",
        unknown_blob_names=[],
        checkpoint_not_found=False,
    )
    assert responses.pop(0) == ChatResult(
        text="",
        unknown_blob_names=[],
        checkpoint_not_found=False,
        nodes=[
            ChatResultNode(
                id=1,
                type=ChatResultNodeType.TOOL_USE_START,
                content="",
                tool_use=ChatResultToolUse(
                    tool_use_id="tool-123",
                    name="get_date",
                    input={},
                ),
            ),
        ],
    )
    assert responses.pop(0) == ChatResult(
        text="",
        unknown_blob_names=[],
        checkpoint_not_found=False,
        nodes=[
            ChatResultNode(
                id=1,
                type=ChatResultNodeType.TOOL_USE,
                content="",
                tool_use=ChatResultToolUse(
                    tool_use_id="tool-123",
                    name="get_date",
                    input={"param": "value"},
                ),
            ),
        ],
    )
    assert responses.pop(0) == ChatResult(
        text="\n", unknown_blob_names=[], checkpoint_not_found=False, nodes=[]
    )
    assert responses.pop(0) == ChatResult(
        text="",
        unknown_blob_names=[],
        checkpoint_not_found=False,
        nodes=[
            ChatResultNode(
                id=0,
                type=ChatResultNodeType.RAW_RESPONSE,
                content="I'll write a program\n",
            ),
        ],
    )
    assert responses.pop(0) == ChatResult(
        text="",
        unknown_blob_names=[],
        checkpoint_not_found=False,
        nodes=[
            ChatResultNode(
                id=2,
                type=ChatResultNodeType.MAIN_TEXT_FINISHED,
                content="",
            ),
        ],
        stop_reason=StopReason.TOOL_USE_REQUESTED,
    )

    ri_response = ChatRequestInsightBuilder_Mock.return_value.record_response.call_args[
        0
    ][0]
    assert ri_response.nodes[0] == chat_pb2.ChatResultNode(
        id=1,
        type=chat_pb2.ChatResultNodeType.TOOL_USE_START,
        content="",
        tool_use=chat_pb2.ChatResultToolUse(
            tool_use_id="tool-123",
            tool_name="get_date",
            input_json="",
        ),
    )
    assert ri_response.nodes[1] == chat_pb2.ChatResultNode(
        id=1,
        type=chat_pb2.ChatResultNodeType.TOOL_USE,
        content="",
        tool_use=chat_pb2.ChatResultToolUse(
            tool_use_id="tool-123",
            tool_name="get_date",
            input_json='{"param": "value"}',
        ),
    )
    assert ri_response.stop_reason == StopReason.TOOL_USE_REQUESTED


@pytest.mark.parametrize("client_support", [True, False])
@pytest.mark.parametrize("flag_support", [True, False])
def test_tool_use_start_enablement(feature_flags_context, client_support, flag_support):
    feature_flags_context.set_flag(_CHAT_GENERATE_TOOL_USE_START, flag_support)

    generation: list[ThirdPartyModelResponse] = [
        ThirdPartyModelResponse(
            text="",
            tool_use_start=ToolUseStart(
                tool_use_id="tool-123",
                tool_name="get_date",
            ),
        ),
    ]
    chat_handler = _get_chat_third_party_handler(
        third_party_response=generation,
        client_type="anthropic_vertexai",
        model_name="claude-3-5-sonnet@20240620",
        prompt_formatter_name="binks-claude-v4",
    )
    request = _get_chat_request()
    request.feature_detection_flags.support_tool_use_start = client_support

    iterator, _, _ = _run_chat_stream(chat_handler, request)
    responses = list(iterator)

    node_generated = False
    for response in responses:
        if response.nodes:
            node_generated = any(
                n.type == ChatResultNodeType.TOOL_USE_START for n in response.nodes
            )
            if node_generated:
                break
    assert node_generated == (client_support and flag_support)


def test_get_pseudo_random_from_session_id():
    session_id = "e02cf196-4384-4c56-98c7-a144199038b8"
    random_value = get_pseudo_random_from_session_id(session_id)
    assert 0 <= random_value <= 1


def test_get_pseudo_random_from_session_id_consistency():
    session_id = "e02cf196-4384-4c56-98c7-a144199038b8"
    random_value_1 = get_pseudo_random_from_session_id(session_id)
    random_value_2 = get_pseudo_random_from_session_id(session_id)
    assert random_value_1 == random_value_2

    session_id_2 = "e02cf196-4384-4c56-98c7-a144199038b9"
    random_value_3 = get_pseudo_random_from_session_id(session_id_2)
    assert random_value_3 != random_value_1


def test_rules_conversion_with_feature_flag_enabled(feature_flags_context):
    """Tests that rules are converted from proto when the feature flag is enabled."""
    # Enable the rules feature flag
    feature_flags_context.set_flag(_ENABLE_RULES, True)

    chat_handler = _get_chat_third_party_handler()

    # Create proto rules
    proto_rules = [
        chat_pb2.Rule(
            type=chat_pb2.RuleType.ALWAYS_ATTACHED,
            path="rules/coding_style.md",
            content="Use 4 spaces for indentation.",
        ),
        chat_pb2.Rule(
            type=chat_pb2.RuleType.MANUAL,
            path="rules/naming.md",
            content="Use camelCase for variable names.",
        ),
    ]

    # Create chat request with rules
    chat_request = _get_chat_request()
    chat_request.rules.extend(proto_rules)

    # Replace the prompt formatter with a mock to capture the rules passed to it
    captured_rules = None
    chat_handler.prompt_formatter = MagicMock()

    def mock_format_prompt(prompt_input):
        nonlocal captured_rules
        captured_rules = prompt_input.rules
        # Return a valid StructuredChatPromptOutput
        return StructuredChatPromptOutput(
            system_prompt="system prompt",
            chat_history=[],
            message="message",
            retrieved_chunks_in_prompt=[],
        )

    chat_handler.prompt_formatter.format_prompt = mock_format_prompt

    # Run the chat stream and consume the iterator
    chat_results, _, _ = _run_chat_stream(chat_handler, chat_request)
    # Consume the iterator to trigger the prompt formatter call
    list(chat_results)

    # Verify that rules were converted and passed to the prompt formatter
    assert captured_rules is not None
    assert len(captured_rules) == 2

    # Check first rule - RuleType.ALWAYS_ATTACHED maps to RuleType.ALWAYS_ATTACHED (0)
    assert captured_rules[0].type == RuleType.ALWAYS_ATTACHED
    assert captured_rules[0].path == "rules/coding_style.md"
    assert captured_rules[0].content == "Use 4 spaces for indentation."

    # Check second rule - RuleType.MANUAL maps to RuleType.MANUAL (1)
    assert captured_rules[1].type == RuleType.MANUAL
    assert captured_rules[1].path == "rules/naming.md"
    assert captured_rules[1].content == "Use camelCase for variable names."


def test_rules_conversion_with_feature_flag_disabled(feature_flags_context):
    """Tests that rules are not converted when the feature flag is disabled."""
    # Disable the rules feature flag (default is False)
    feature_flags_context.set_flag(_ENABLE_RULES, False)

    chat_handler = _get_chat_third_party_handler()

    # Create proto rules
    proto_rules = [
        chat_pb2.Rule(
            type=chat_pb2.RuleType.ALWAYS_ATTACHED,
            path="rules/coding_style.md",
            content="Use 4 spaces for indentation.",
        ),
    ]

    # Create chat request with rules
    chat_request = _get_chat_request()
    chat_request.rules.extend(proto_rules)

    # Replace the prompt formatter with a mock to capture the rules passed to it
    captured_rules = None
    chat_handler.prompt_formatter = MagicMock()

    def mock_format_prompt(prompt_input):
        nonlocal captured_rules
        captured_rules = prompt_input.rules
        # Return a valid StructuredChatPromptOutput
        return StructuredChatPromptOutput(
            system_prompt="system prompt",
            chat_history=[],
            message="message",
            retrieved_chunks_in_prompt=[],
        )

    chat_handler.prompt_formatter.format_prompt = mock_format_prompt

    # Run the chat stream and consume the iterator
    chat_results, _, _ = _run_chat_stream(chat_handler, chat_request)
    # Consume the iterator to trigger the prompt formatter call
    list(chat_results)

    # Verify that rules were not converted (empty list passed)
    assert captured_rules is not None
    assert len(captured_rules) == 0


def test_rules_conversion_with_empty_rules(feature_flags_context):
    """Tests that empty rules list is handled correctly."""
    # Enable the rules feature flag
    feature_flags_context.set_flag(_ENABLE_RULES, True)

    chat_handler = _get_chat_third_party_handler()

    # Create chat request with no rules
    chat_request = _get_chat_request()
    # Don't add any rules to the request

    # Replace the prompt formatter with a mock to capture the rules passed to it
    captured_rules = None
    chat_handler.prompt_formatter = MagicMock()

    def mock_format_prompt(prompt_input):
        nonlocal captured_rules
        captured_rules = prompt_input.rules
        # Return a valid StructuredChatPromptOutput
        return StructuredChatPromptOutput(
            system_prompt="system prompt",
            chat_history=[],
            message="message",
            retrieved_chunks_in_prompt=[],
        )

    chat_handler.prompt_formatter.format_prompt = mock_format_prompt

    # Run the chat stream and consume the iterator
    chat_results, _, _ = _run_chat_stream(chat_handler, chat_request)
    # Consume the iterator to trigger the prompt formatter call
    list(chat_results)

    # Verify that an empty rules list was passed
    assert captured_rules is not None
    assert len(captured_rules) == 0
