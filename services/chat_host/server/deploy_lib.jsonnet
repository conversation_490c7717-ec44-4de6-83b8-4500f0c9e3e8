// K8S deployment file for a chat server
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local anthropicLib = import 'services/chat_host/server/anthropic-lib.jsonnet';
local openaiLib = import 'services/chat_host/server/openai-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local requestInsightPublisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';

// function to create a chat host
//    env: environemnt
//    namespace: namespace to use
//    model config: model config (from //services/deploy/configs) object
//    retrievalConfigs: configuration of the retrievals
//    overrideConfig: object that overrides a chat host configuration
function(
  env,
  namespace,
  namespace_config,
  cloud,
  name,
  modelConfig,
  inferenceServices,
  retrievalConfigs,
  inferenceMtls,
  mtls,
  overrideConfig=null,
  thirdPartyInferenceConfig=null,  // For third party models like Claude
  thirdPartyInferenceMultiConfig=null,  // For multiple third part models
  suggestedQuestionsConfig=null,
  postprocessConfig=null,
  prodReplicas=2,
)
  local appName = name;
  // The chat model names are quite long and tend to share prefixes, but we want
  // unique short names because IAM service accounts have a 30 character limit.
  // To get around this, use a hash to generate a short name.
  local shortAppName = 'chat-%s' % std.substr(std.md5('%s-chat' % appName), 0, 7);
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName,
  );
  local anthropicSealedSecret = anthropicLib(appName=appName, namespace=namespace, cloud=cloud);
  local openaiSealedSecret = openaiLib(appName=appName, namespace=namespace, cloud=cloud);
  local xaiSealedSecretName = std.asciiLower(env) + '-chat-xai-api-key';
  local xaiSealedSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='xai-api-key',
    version={
      PROD: '2',
      STAGING: '2',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
    overrideSecretName=xaiSealedSecretName,
  );
  local fireworksSecretName = std.asciiLower(env) + '-chat-fireworks-api-key';
  local fireworksGcpSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='fireworks-api-key',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
    overrideSecretName=fireworksSecretName,
  );
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(cloud=cloud, env=env, namespace=namespace, appName=appName);
  local requestInsightPublisher = requestInsightPublisherLib(cloud, env, namespace, appName=appName);
  local services = grpcLib.grpcService(name, namespace=namespace);
  local isThirdPartyMulti = thirdPartyInferenceMultiConfig != null;
  local isThirdParty = thirdPartyInferenceConfig != null;
  local handlerType = if isThirdPartyMulti then 'ChatThirdPartyMultiHandler' else if isThirdParty then 'ChatThirdPartyHandler' else 'ChatHandler';
  local buildThirdPartyConfig = function(model_name, config) {
    assert model_name != null,
    assert std.objectHas(config, 'client_type'),
    assert std.objectHas(config, 'model_name'),
    gcp_project_id: cloudInfo[cloud].projectId,
    gcp_region: if std.objectHas(config, 'gcp_region') then config.gcp_region else cloudInfo[cloud].region,
    anthropic_api_key_path: anthropicSealedSecret.api_key_path,
    openai_api_key_path: openaiSealedSecret.api_key_path,
    xai_api_key_path: xaiSealedSecret.filePath,
    fireworks_api_key_path: fireworksGcpSecret.filePath,
    client_type: config.client_type,
    model_name: config.model_name,
    prompt_formatter_name: config.prompt_formatter_name,
    token_apportionment: config.token_apportionment,
    generate_commit_message_token_apportionment: std.get(config, 'generate_commit_message_token_apportionment', null),
    slackbot_message_token_apportionment: std.get(config, 'slackbot_message_token_apportionment', null),
    temperature: config.temperature,
    max_output_tokens: config.max_output_tokens,
  };
  local configMap =
    local config = {
      port: 50051,
      model_name: name,
      max_server_workers: 64,
      inference_host_endpoints: if inferenceServices != null then std.mapWithKey(function(k, v) '%s:50051' % v, inferenceServices) else null,
      third_party_arbiter_endpoint: endpointsLib.getThirdPartyArbiterGrpcUrl(env=env, namespace=namespace, cloud=cloud),
      content_manager_endpoint: 'content-manager-svc:50051',
      working_set_endpoint: if namespace_config.flags.workingSetEndpoint != '' then namespace_config.flags.workingSetEndpoint else null,
      feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
      dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
      retrieval: { retrieval_configs: retrievalConfigs },
      postprocess: if postprocessConfig == null then null else postprocessConfig.config,
      handler_type: handlerType,
      handler_config: if isThirdParty then buildThirdPartyConfig(name, thirdPartyInferenceConfig)
      else if isThirdPartyMulti then std.mapWithKey(buildThirdPartyConfig, thirdPartyInferenceMultiConfig)
      else {
        assert std.objectHas(modelConfig, 'inference') && std.objectHas(modelConfig.inference, 'token_apportionment'),
        assert !std.objectHas(modelConfig.inference, 'prompt_formatter_config'),
        assert std.objectHas(modelConfig, 'sampling_params'),
        assert std.objectHas(modelConfig, 'preference_sampling_params'),
        tokenizer_name: modelConfig.inference.tokenizer_name,
        prompt_formatter_name: modelConfig.inference.prompt_formatter_name,
        token_apportionment: modelConfig.inference.token_apportionment,
        generate_commit_message_token_apportionment: std.get(modelConfig.inference, 'generate_commit_message_token_apportionment', null),
        slackbot_message_token_apportionment: std.get(modelConfig.inference, 'slackbot_message_token_apportionment', null),
        max_context_length: modelConfig.inference.max_context_length,
        max_output_length: modelConfig.inference.max_output_length,
        sampling_params: modelConfig.sampling_params,
        preference_sampling_params: modelConfig.preference_sampling_params,
      },
      auth_config: {
        token_exchange_endpoint: 'token-exchange-central-svc.%s:50051' % cloudInfo.getCentralNamespaceForNamespace(env=env, namespace=namespace, cloud=cloud),
      },
    } + if !mtls then {} else {
      client_mtls: {
        ca_path: '/client-certs/ca.crt',
        key_path: '/client-certs/tls.key',
        cert_path: '/client-certs/tls.crt',
      },
      server_mtls: {
        ca_path: '/certs/ca.crt',
        key_path: '/certs/tls.key',
        cert_path: '/certs/tls.crt',
      },
      central_client_mtls: {
        ca_path: '/inference-client-certs/ca.crt',
        key_path: '/inference-client-certs/tls.key',
        cert_path: '/inference-client-certs/tls.crt',
      },
    } + if suggestedQuestionsConfig != null then {
      suggested_questions_config: {
        assert std.objectHas(suggestedQuestionsConfig, 'client_type'),
        assert std.objectHas(suggestedQuestionsConfig, 'model_name'),

        // Suggested question generation takes the whole conversation as input
        // So whole prompt of main Chat model should fit in context here.
        assert thirdPartyInferenceConfig != null,
        assert thirdPartyInferenceConfig.token_apportionment.max_prompt_len + thirdPartyInferenceConfig.max_output_tokens < suggestedQuestionsConfig.max_input_tokens,
        anthropic_api_key_path: anthropicSealedSecret.api_key_path,

        gcp_region: if std.objectHas(suggestedQuestionsConfig, 'gcp_region') then suggestedQuestionsConfig.gcp_region else cloudInfo[cloud].region,
        client_type: suggestedQuestionsConfig.client_type,
        model_name: suggestedQuestionsConfig.model_name,
        temperature: suggestedQuestionsConfig.temperature,
        max_output_tokens: suggestedQuestionsConfig.max_output_tokens,
        gcp_project_id: cloudInfo[cloud].projectId,
      },
    } else {};
    local overriddenConfig = if overrideConfig == null then config else std.mergePatch(config, overrideConfig);
    configMapLib.createConfigMap(appName=appName, namespace=namespace, config=overriddenConfig);
  // create a client TLS certificate to securely access the content manager
  local clientCert = certLib.createClientCert(
    name='%s-client-certificate' % name,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
  );
  local inferenceClientCert = certLib.createCentralClientCert(
    name='%s-infer-client-cert' % name,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='inference-client-certs',
    dnsNames=grpcLib.grpcServiceNames(name, namespace=namespace),
  );
  // create a server certificate for MTLS
  local serverCert = certLib.createServerCert(
    name='%s-server-certificate' % name,
    namespace=namespace,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNames(name),
    volumeName='certs',
  );

  local serviceAccountObjects = serviceAccount.objects + [
    requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
  ];
  // No need to grant access to a Vertex AI model if the client is direct (not via GCP)
  local isVertexAi = (
    isThirdParty && thirdPartyInferenceConfig.client_type != 'anthropic_direct'
  ) || isThirdPartyMulti;
  local grantAiPlatformAccess = if isVertexAi then gcpLib.grantAccess(
    env=env,
    namespace=namespace,
    appName=appName,
    name='aiplatform-%s-chat-grant' % name,
    resourceRef={
      kind: 'Project',
      external: 'projects/%s' % cloudInfo[cloud].projectId,
    },
    bindings=[
      {
        role: 'roles/aiplatform.user',
        members: [
          {
            memberFrom: {
              serviceAccountRef: {
                name: serviceAccount.iamServiceAccountName,
              },
            },
          },
        ],
      },
    ],
    abandon=true,
  ) else {};
  local retrievalObjects = std.map(function(r) r.getRetrievalObjects(name, namespace), retrievalConfigs);
  local postprocessObjects = if postprocessConfig != null then [postprocessConfig.getRetrievalObjects(name, namespace)] else [];
  local allRetrievalObjects = lib.flatten(
    retrievalObjects
    + postprocessObjects
    + [r.innerRetrieversInfo for r in retrievalObjects if std.get(r, 'innerRetrieversInfo', null) != null]
    + [r.innerRetrieverInfo for r in retrievalObjects if std.get(r, 'innerRetrieverInfo', null) != null]
  );
  local container =
    {
      name: 'chat',
      target: {
        name: '//services/chat_host/server:image',
        dst: 'chat',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      env: telemetryLib.telemetryEnv('chat-host', telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      volumeMounts: lib.flatten([
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
        inferenceClientCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
        requestInsightPublisher.volumeMountDef,
        anthropicSealedSecret.volumeMountDef,
        openaiSealedSecret.volumeMountDef,
        xaiSealedSecret.volumeMountDef,
        fireworksGcpSecret.volumeMountDef,
        [r.volumeMountDef for r in allRetrievalObjects if r.volumeMountDef != null],
      ]),
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 2,
          // This is conservative, looks like we usually use <1Gi
          memory: '4Gi',
        },
      },
    };
  local pod =
    {
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      volumes: lib.flatten([
        configMap.podVolumeDef,
        serverCert.podVolumeDef,
        clientCert.podVolumeDef,
        inferenceClientCert.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
        requestInsightPublisher.podVolumeDef,
        anthropicSealedSecret.podVolumeDef,
        openaiSealedSecret.podVolumeDef,
        xaiSealedSecret.podVolumeDef,
        fireworksGcpSecret.podVolumeDef,
        [r.podVolumeDef for r in allRetrievalObjects if r.podVolumeDef != null],
      ]),
    };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local pbd = nodeLib.podDisruption(appName=appName, namespace=namespace, env=env);
  lib.flatten([
    clientCert.objects,
    inferenceClientCert.objects,
    serverCert.objects,
    configMap.objects,
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: name,
        namespace: namespace,
        labels: {
          app: name,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        replicas: if env == 'DEV' then 1 else std.round(prodReplicas * namespace_config.flags.chatHostReplicaScale),
        minReadySeconds: if env == 'DEV' then 0 else 60,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: name,
          },
        },
        template: {
          metadata: {
            labels: {
              app: name,
            },
          },
          spec: pod + {
            affinity: affinity,
            tolerations: tolerations,
            priorityClassName: cloudInfo.envToPriorityClass(env),
          },
        },
      },
    },
    services,
    [r.objects for r in allRetrievalObjects],
    serviceAccountObjects,
    grantAiPlatformAccess,
    dynamicFeatureFlags.k8s_objects,
    anthropicSealedSecret.objects,
    openaiSealedSecret.objects,
    xaiSealedSecret.objects,
    fireworksGcpSecret.objects,
    pbd,
  ])
