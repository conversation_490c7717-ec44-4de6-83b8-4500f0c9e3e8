"""Server of a chat host."""

import argparse
import logging
import os
import pathlib
import random
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from typing import Iterable, Iterator

import grpc
import opentelemetry
import opentelemetry.instrumentation.grpc
from services.chat_host.server.postprocess import <PERSON>pro<PERSON>, PostprocessConfig
from services.inference_host.client.multiplex import MultiplexInferenceStubFactory
import structlog
from dataclasses_json import dataclass_json
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection
from prometheus_client import Counter, Histogram, start_http_server
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
    new_event,
)
from services.request_insight import request_insight_pb2

import base.feature_flags
import base.tracing
import services.lib.grpc.tls_config.tls_config as tls_config
from base.error_details.error_details_pb2 import ErrorDetails
from google.protobuf import json_format
from base.logging.struct_logging import setup_struct_logging
from base.prompt_format_chat.prompt_formatter import ExceedContextLength
from base.python.grpc import client_options
from base.python.opentelemetry_utils.traced_threadpool import TracedThreadPoolExecutor
from base.python.signal_handler.signal_handler import GracefulSignalHandler
from services.lib.retrieval.retriever import RetrievalChunk
from services.chat_host import chat_pb2, chat_pb2_grpc

from services.chat_host.server.request_filter import (
    BanRules,
    allow_request,
    start_regexp_download_thread,
)
from services.chat_host.server.turn_limit import check_turn_limit
from services.third_party_arbiter.client.client import (
    ThirdPartyArbiterClient,
    GrpcThirdPartyArbiterClientImpl,
)
from services.chat_host.server.chat_handler import create_chat_handler
from services.chat_host.server.chat_handler_metrics import ChatHandlerMetrics
from services.chat_host.server.chat_third_party_handler import (
    create_chat_third_party_handler,
)
from services.chat_host.server.handler import (
    ChatHandlerProtocol,
    ChatResult,
    ChatResultStatusCode,
)
from services.chat_host.server.suggested_questions import (
    SuggestedQuestionsGenerator,
    create_suggested_questions_generator,
)
from services.lib.retrieval import retriever_factory
from services.lib.retrieval.retriever_request_insight_builder import (
    RetrieverRequestInsightBuilder,
)
from services.lib.retrieval.retriever import Retriever
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.inference_host import infer_pb2_grpc
from services.inference_host.client import inference_host_client
from services.lib.grpc.auth.service_auth_interceptor import (
    ServiceAuthInterceptor,
    get_auth_info_from_grpc_context,
)
from services.lib.grpc.auth.service_token_auth import (
    GrpcPublicKeySource,
    ServiceTokenAuth,
)
from services.lib.grpc.metrics.metrics import MetricsServerInterceptor
from services.lib.request_context.request_context import RequestContext
from services.token_exchange.client.client import GrpcTokenExchangeClient
from services.working_set.client.client import (
    get_working_set_client,
    WorkingSetClient,
)

log = structlog.get_logger()


_chats_counter = Counter(
    "au_chat_host_counter",
    "Counts chat hosts requests",
    ["model", "status_code", "request_source", "tenant_name"],
)

char_len_buckets = [0] + [2**i for i in range(16)]  # Buckets go up to 2^15 = 32,768

_chat_prefix_size_summary = Histogram(
    "au_chat_host_prefix_size",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=char_len_buckets,
    documentation="Size of the chat prefix in chars",
)
_chat_suffix_size_summary = Histogram(
    "au_chat_host_suffix_size",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=char_len_buckets,
    documentation="Size of the chat suffix in chars",
)
_chat_message_size_summary = Histogram(
    "au_chat_host_message_size",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=char_len_buckets,
    documentation="Size of the chat message in chars",
)
_chat_selected_code_size_summary = Histogram(
    "au_chat_host_selected_code_size",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=char_len_buckets,
    documentation="Size of the selected code in chars",
)
_chat_selected_code_lines_summary = Histogram(
    "au_chat_host_selected_code_lines",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=char_len_buckets,
    documentation="Size of the selected code in lines",
)

# buckets for the latency histogram, from 1ms to 10s
INF = float("inf")
latency_buckets = tuple([(1.3**i - 1) / 100.0 for i in range(35)] + [INF])
_chat_latency = Histogram(
    "au_chat_host_latency_seconds",
    labelnames=["model", "status_code", "request_source", "tenant_name"],
    documentation="Latency of a chat request (in the chat host)",
    buckets=latency_buckets,
)

_chat_requests_cancelled = Counter(
    "au_chat_requests_cancelled",
    "Chat requests cancelled",
    labelnames=["model", "request_source", "tenant_name"],
)

blob_name_buckets = [0] + [2**i for i in range(16)]  # Buckets go up to 2^15 = 32,768

_chat_unknown_blob_name_count_summary = Histogram(
    "au_chat_host_unknown_blob_name_count",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=blob_name_buckets,
    documentation="Number of unknown blob names in a chat request",
)
_chat_find_missing_blob_name_count_summary = Histogram(
    "au_completion_host_find_missing_blob_name_count",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=blob_name_buckets,
    documentation="Number of blob names in a find-missing request",
)
_chat_find_missing_missing_blob_name_count_summary = Histogram(
    "au_completion_host_find_missing_missing_blob_name_count",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=blob_name_buckets,
    documentation="Number of missing raw blob names in a find-missing request",
)
_chat_find_missing_nonindexed_blob_name_count_summary = Histogram(
    "au_completion_host_find_missing_nonindexed_blob_name_count",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=blob_name_buckets,
    documentation="Number of nonindexed blob names in a find-missing request",
)
_chat_find_missing_latency = Histogram(
    "au_completion_host_find_missing_latency_seconds",
    labelnames=["model", "status_code", "request_source", "tenant_name"],
    documentation="Latency of a find-missing request (in the completion host)",
    buckets=latency_buckets,
)
_chat_blocked_counter = Counter(
    "au_chat_blocked_counter",
    "Counts chat hosts requests that are blocked",
    ["model", "request_source", "tenant_name"],
)

# feature flag to enable multiplexing over multiple inference hosts
#
# Example
# {"default": 0.5, "gsc": 0.5}
#
# This assumes that the inference hosts are named "default" and "gsc"
_INFERER_MULTIPLEX = base.feature_flags.StringFlag("chat_inferer_multiplex", "")

_BLOCKED_SLEEP_LOWER_BOUND = base.feature_flags.IntFlag(
    "chat_server_blocked_sleep_lower_bound", 10
)
_BLOCKED_SLEEP_UPPER_BOUND = base.feature_flags.IntFlag(
    "chat_server_blocked_sleep_upper_bound", 20
)

_ENABLE_ERROR_DETAILS_METADATA = base.feature_flags.BoolFlag(
    "chat_server_enable_error_details_metadata", False
)

tracer = base.tracing.setup_opentelemetry()


@dataclass_json
@dataclass
class AuthConfig:
    """Configuration for the token authentication."""

    # the endpoint for the token exchange service
    token_exchange_endpoint: str


@dataclass_json
@dataclass
class Config:
    """Configuration for a chat server."""

    port: int

    # name of the model.
    #
    # this has to match the name of the model instance config
    model_name: str

    # endpoints for the inference hosts
    # key is the name of the inference host, value is the endpoint
    #
    # , e.g.
    # {
    #   "default": "infer-wizard-svc.central:50051",
    #   "gsc": "infer-wizard.central.t.us-central1-gsc.prod.augmentcode.com:50051"
    # }
    inference_host_endpoints: dict[str, str] | None
    content_manager_endpoint: str
    third_party_arbiter_endpoint: str
    working_set_endpoint: str | None
    retrieval: retriever_factory.RetrievalConfig
    postprocess: PostprocessConfig | None
    handler_type: str
    handler_config: dict
    auth_config: AuthConfig

    max_handler_workers: int = 24
    """Maximum number of workers for the handlers.

    NOTE: We have made it much larger because this threadpool is now responsible
    for additional tasks. For example, it parallelizes work inside the multi-retriever and
    the batched reranker. The number is 24 is chosen right now by some back of the envelope
    math. Peak thread usage comes at retrieval time, where we use up to 16 threads for the
    batched reranker, and up to 2 for the multi-retriever. The batched reranker blocks on these
    threads, so we currently don't pass peak thread utilization of 20 threads. The extra
    4 are a buffer. This overall approach to thread mgmt could use improvement.
    """
    max_server_workers: int = 32
    """Maximum number of workers for the server.

    NOTE: Be aware that adjusting this number has a multiplicative affect on the total global number of
    handler workers.
    """

    feature_flags_sdk_key_path: str | None = None
    dynamic_feature_flags_endpoint: str | None = None

    client_mtls: tls_config.ClientConfig | None = None
    central_client_mtls: tls_config.ClientConfig | None = None
    server_mtls: tls_config.ServerConfig | None = None

    # Most models specify 8k output tokens; this generation
    # can take 1m40-1m50s.
    shutdown_grace_period_s: float = 120.0
    suggested_questions_config: dict | None = None


def _set_error_details_metadata(context, ex):
    """Set error details metadata if the exception has error_details.

    Args:
        context: The gRPC context to set metadata on
        ex: The exception that may have error_details
    """
    if (
        hasattr(ex, "error_details")
        and isinstance(ex.error_details, ErrorDetails)
        and _ENABLE_ERROR_DETAILS_METADATA.get(base.feature_flags.get_global_context())
    ):
        context.set_trailing_metadata(
            [
                (
                    "error-details",
                    json_format.MessageToJson(
                        ex.error_details,
                        preserving_proto_field_name=True,
                        use_integers_for_enums=True,
                        indent=None,
                    ),
                )
            ]
        )


class ChatServices(chat_pb2_grpc.ChatServicer):
    """Services to implement the chat service API."""

    def __init__(
        self,
        config: Config,
        handlers: dict[str, ChatHandlerProtocol],
        working_set_client: WorkingSetClient,
        ri_publisher: RequestInsightPublisher,
        ban_regexp: BanRules | None = None,
    ):
        self.config = config
        self.ban_regexp = ban_regexp if ban_regexp is not None else BanRules()
        self.handlers = handlers
        self.background_pool = TracedThreadPoolExecutor(
            thread_name_prefix="background-notify-"
        )
        self.working_set_client = working_set_client
        self.ri_publisher = ri_publisher

    def _get_handler(self, model_name) -> ChatHandlerProtocol:
        if len(self.handlers) == 1:
            # Legacy single-handler deployments don't have the exact model name as the key
            return next(iter(self.handlers.values()))
        handler = self.handlers.get(model_name)
        if handler is None:
            raise ValueError(
                f"Unknown model name: {model_name}, expected one of [{list(self.handlers.keys())}]"
            )
        return handler

    def _generate_chat_stream(
        self,
        request: chat_pb2.ChatRequest,
        context: grpc.ServicerContext,
    ) -> Iterable[ChatResult]:
        handler = self._get_handler(request.model_name)
        request_start_time = time.time()
        status_code = grpc.StatusCode.UNKNOWN
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        try:
            request_context.bind_context_logging()
            auth_info.bind_context_logging()

            _chat_prefix_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.prefix))
            _chat_suffix_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.suffix))
            _chat_message_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.message))
            _chat_selected_code_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.selected_code))
            _chat_selected_code_lines_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(request.selected_code.count("\n") + 1)

            log.info("chat stream content: model_name=%s", request.model_name)

            # register workingset in the background
            self.background_pool.submit(
                self.working_set_client.register_working_set,
                request.blobs,
                request_context,
            )
            # We create an exclusive thread pool per request to avoid deadlocking.
            with TracedThreadPoolExecutor(
                max_workers=self.config.max_handler_workers,
                thread_name_prefix=f"chat-{request_context.request_id[-8:]}-",
            ) as executor:
                session_id = request_context.request_session_id

                # Apply filter rules: block the request or mark it as suspicious if necessary
                if not allow_request(
                    request,
                    session_id,
                    self.ban_regexp,
                    auth_info,
                    request_context=request_context,
                    ri_publisher=self.ri_publisher,
                ):
                    # Register ChatRequest event
                    blocked_text = "Request blocked. Please reach <NAME_EMAIL> if you think this was a mistake."

                    # Create a request event
                    request_event = new_event()
                    ri_request = request_insight_pb2.RIChatRequest(
                        request=request, request_source=request_context.request_source
                    )
                    request_event.chat_host_request.MergeFrom(ri_request)
                    self.ri_publisher.publish_request_insight(
                        self.ri_publisher.update_request_info_request(
                            request_context.request_id, [request_event], auth_info
                        )
                    )

                    # Create a ChatResponse for the blocked message
                    # Use WAS_BLOCKED status code to signal API proxy to sleep
                    blocked_result = ChatResult(
                        text=blocked_text,
                        unknown_blob_names=[],
                        status_code=ChatResultStatusCode.WAS_BLOCKED,
                    )
                    blocked_response = blocked_result.to_chat_response_proto()

                    # Create a response event
                    response_event = new_event()
                    ri_response = request_insight_pb2.RIChatResponse(
                        response=blocked_response
                    )
                    response_event.chat_host_response.MergeFrom(ri_response)
                    self.ri_publisher.publish_request_insight(
                        self.ri_publisher.update_request_info_request(
                            request_context.request_id, [response_event], auth_info
                        )
                    )
                    _chat_blocked_counter.labels(
                        request.model_name,
                        request_context.request_source,
                        auth_info.metrics_tenant_name,
                    ).inc()

                    yield blocked_result
                    return

                # Check if the turn limit has been exceeded
                turn_limit_response = check_turn_limit(request)
                if turn_limit_response is not None:
                    # Create a request event
                    request_event = new_event()
                    ri_request = request_insight_pb2.RIChatRequest(
                        request=request, request_source=request_context.request_source
                    )
                    request_event.chat_host_request.MergeFrom(ri_request)
                    self.ri_publisher.publish_request_insight(
                        self.ri_publisher.update_request_info_request(
                            request_context.request_id, [request_event], auth_info
                        )
                    )

                    # Yield the turn limit response
                    yield turn_limit_response
                    return

                generator = handler.chat_stream(
                    request=request,
                    request_context=request_context,
                    auth_info=auth_info,
                    executor=executor,
                )

                # Collect all unknown blob names across chunks for tracking
                all_unknown_blob_names = set()

                for response in generator:
                    all_unknown_blob_names.update(response.unknown_blob_names)
                    yield response

                _chat_unknown_blob_name_count_summary.labels(
                    request.model_name,
                    request_context.request_source,
                    auth_info.metrics_tenant_name,
                ).observe(len(all_unknown_blob_names))
                status_code = grpc.StatusCode.OK
        except grpc.RpcError as ex:
            status_code = ex.code()  # pylint: disable=no-member # type: ignore
            if status_code == grpc.StatusCode.CANCELLED:
                log.info("Chat cancelled")
                _chat_requests_cancelled.labels(
                    request.model_name,
                    request_context.request_source,
                    auth_info.metrics_tenant_name,
                ).inc()
            elif status_code == grpc.StatusCode.UNAVAILABLE:
                log.warn("Unavailable: %s", ex)
            else:
                log.error("Chat failed: %s %s", status_code, ex)
                log.exception(ex)

            _set_error_details_metadata(context, ex)
            context.abort(
                code=status_code,
                details=ex.details(),  # pylint: disable=no-member # type: ignore
            )
        except ExceedContextLength as ex:
            log.warn("Chat failed: ExceedContextLength %s", ex)
            yield ChatResult(
                text="",
                unknown_blob_names=[],
                status_code=ChatResultStatusCode.EXCEED_CONTEXT_LENGTH,
            )
        except Exception as ex:  # pylint: disable=broad-exception-caught
            log.error("Chat failed: %s", ex)
            log.exception(ex)
            raise
        finally:
            latency = time.time() - request_start_time
            _chats_counter.labels(
                request.model_name,
                str(status_code),
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).inc()
            _chat_latency.labels(
                request.model_name,
                str(status_code),
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(latency)

    def Chat(
        self,
        request: chat_pb2.ChatRequest,
        context: grpc.ServicerContext,
    ) -> chat_pb2.ChatResponse:
        """
        Regular chat implementation has been deprecated in favor of a chat stream dependency.
        Concatenate chat stream events and send it back as one response.
        """
        generator = self._generate_chat_stream(
            request=request,
            context=context,
        )
        response = chat_pb2.ChatResponse()
        for output in generator:
            output_response = output.to_chat_response_proto()
            response.text += output_response.text
            response.unknown_blob_names.extend(output_response.unknown_blob_names)
            response.workspace_file_chunks.extend(output_response.workspace_file_chunks)
            response.incorporated_external_sources.extend(
                output_response.incorporated_external_sources
            )
            response.checkpoint_not_found = (
                response.checkpoint_not_found or output_response.checkpoint_not_found
            )
            response.status_code = output_response.status_code
            response.nodes.extend(output_response.nodes)
            if output_response.HasField("stop_reason") and not response.HasField(
                "stop_reason"
            ):
                response.stop_reason = output_response.stop_reason

        for node in response.nodes:
            if (
                node.type == chat_pb2.ChatResultNodeType.MAIN_TEXT_FINISHED
                and node.content
            ):
                response.text = node.content
        return response

    def ChatStream(
        self,
        request: chat_pb2.ChatRequest,
        context: grpc.ServicerContext,
    ) -> Iterator[chat_pb2.ChatResponse]:
        generator = self._generate_chat_stream(
            request=request,
            context=context,
        )

        for output in generator:
            response = output.to_chat_response_proto()
            yield response

    def ChatRetrieval(
        self,
        request: chat_pb2.ChatRetrievalRequest,
        context: grpc.ServicerContext,
    ) -> chat_pb2.ChatRetrievalResponse:
        handler = self._get_handler(request.model_name)
        # request_start_time = time.time()
        # status_code = grpc.StatusCode.UNKNOWN
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        try:
            request_context.bind_context_logging()
            auth_info.bind_context_logging()

            # TODO(guy) add metrics?

            # We create an exclusive thread pool per request to avoid deadlocking.
            with TracedThreadPoolExecutor(
                max_workers=self.config.max_handler_workers,
                thread_name_prefix=f"chat-retrieval-{request_context.request_id[-8:]}-",
            ) as executor:
                chat_request = chat_pb2.ChatRequest()
                chat_request.model_name = request.model_name
                chat_request.message = request.query
                chat_request.blobs.extend(request.blobs)

                retrieval_result = handler.retrieve(
                    chat_request,
                    request_context,
                    auth_info,
                    executor,
                )

                def chunk_to_proto(chunk: RetrievalChunk) -> chat_pb2.RetrievedChunk:
                    return chat_pb2.RetrievedChunk(
                        text=chunk.text,
                        path=chunk.path,
                        char_start=chunk.char_start,
                        char_end=chunk.char_end,
                        blob_name=chunk.blob_name
                        if chunk.blob_name is not None
                        else "",
                    )

                response = chat_pb2.ChatRetrievalResponse()
                response.retrieved_chunks.extend(
                    [
                        chunk_to_proto(chunk)
                        for chunk in retrieval_result.get_retrieved_chunks()
                    ]
                )
                response.unknown_blob_names.extend(
                    retrieval_result.get_missing_blob_names()
                )
                response.checkpoint_not_found = (
                    retrieval_result.get_checkpoint_not_found()
                )

                # TODO(guy) add metrics?
                # status_code = grpc.StatusCode.OK
                return response
        except grpc.RpcError as ex:
            # status_code = ex.code()  # pylint: disable=no-member # type: ignore
            log.error("ChatRetrieval failed: %s", ex)
            log.exception(ex)

            _set_error_details_metadata(context, ex)
            context.abort(
                code=ex.code(),  # pylint: disable=no-member # type: ignore
                details=ex.details(),  # pylint: disable=no-member # type: ignore
            )
            raise
        except Exception as ex:  # pylint: disable=broad-exception-caught
            log.error("ChatRetrieval failed: %s", ex)
            log.exception(ex)
            raise
        finally:
            # latency = time.time() - request_start_time
            # TODO(guy) add metrics?
            pass

    def FindMissing(
        self,
        request: chat_pb2.FindMissingRequest,
        context: grpc.ServicerContext,
    ) -> chat_pb2.FindMissingResponse:
        handler = self._get_handler(request.model_name)
        request_start_time = time.time()
        status_code = grpc.StatusCode.UNKNOWN
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        try:
            request_context.bind_context_logging()
            auth_info.bind_context_logging()

            _chat_find_missing_blob_name_count_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.blob_names))

            log.info(
                "find_missing content: model_name=%s, blob_count=%d",
                request.model_name,
                len(request.blob_names),
            )

            # We create an exclusive thread pool per request to avoid deadlocking.
            with TracedThreadPoolExecutor(
                max_workers=self.config.max_handler_workers,
                thread_name_prefix=f"find-missing-{request_context.request_id[-8:]}-",
            ) as executor:
                output = handler.find_missing(
                    request,
                    request_context,
                    executor,
                )

            response = chat_pb2.FindMissingResponse()
            response.missing_blob_names.extend(output.missing_blob_names)
            response.nonindexed_blob_names.extend(output.nonindexed_blob_names)

            _chat_find_missing_missing_blob_name_count_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(output.missing_blob_names))
            _chat_find_missing_nonindexed_blob_name_count_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(output.nonindexed_blob_names))
            status_code = grpc.StatusCode.OK
            return response
        except grpc.RpcError as ex:
            status_code = ex.code()  # pylint: disable=no-member # type: ignore
            log.error("FindMissing failed: %s", ex)
            log.exception(ex)

            _set_error_details_metadata(context, ex)
            context.abort(
                code=ex.code(),  # pylint: disable=no-member # type: ignore
                details=ex.details(),  # pylint: disable=no-member # type: ignore
            )
            raise
        except Exception as ex:  # pylint: disable=broad-exception-caught
            log.error("FindMissing failed: %s", ex)
            log.exception(ex)
            raise
        finally:
            latency = time.time() - request_start_time
            _chat_find_missing_latency.labels(
                request.model_name,
                str(status_code),
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(latency)


def _get_inference_stub_factory(
    inferer_endpoints: dict[str, str],
    credentials: grpc.ChannelCredentials | None,
    model_name: str,
) -> inference_host_client.InferenceStubFactoryProtocol:
    """Returns a client to the inference host or inference hosts."""
    rpc_clients: dict[str, infer_pb2_grpc.InfererStub] = {}
    for name, endpoint in inferer_endpoints.items():
        options = client_options.get_grpc_client_options(
            client_options.GrpcClientOptions(load_balancing="headless" in endpoint)
        )

        rpc_client = inference_host_client.create_inference_stub(
            endpoint, credentials=credentials, options=options
        )

        rpc_clients[name] = rpc_client
    if "default" not in rpc_clients:
        raise ValueError("No default client")

    if len(rpc_clients) > 1:
        return MultiplexInferenceStubFactory(
            rpc_clients=rpc_clients,
            model_name=model_name,
            feature_flag=_INFERER_MULTIPLEX,
        )
    else:
        return lambda: list(rpc_clients.values())[0]


def _create_arbiter_client(config: Config) -> ThirdPartyArbiterClient:
    """Creates a ThirdPartyArbiterClient if the endpoint is set.

    Args:
        config: The server configuration.

    Returns:
        A ThirdPartyArbiterClient if the endpoint is set, None otherwise.
    """
    log.info(
        "Creating ThirdPartyArbiterClient with endpoint %s",
        config.third_party_arbiter_endpoint,
    )
    return GrpcThirdPartyArbiterClientImpl(
        endpoint=config.third_party_arbiter_endpoint,
        credentials=tls_config.get_client_tls_creds(config.central_client_mtls),
    )


def _get_handlers(
    config: Config,
    retriever: Retriever,
    postprocess: Postprocess | None,
    metrics: ChatHandlerMetrics,
    ri_publisher: RequestInsightPublisher,
    content_manager_client: ContentManagerClient,
    suggested_questions_generator: SuggestedQuestionsGenerator | None = None,
) -> dict[str, ChatHandlerProtocol]:
    if "POD_NAMESPACE" not in os.environ:
        raise ValueError("POD_NAMESPACE environment variable must be set.")
    namespace = os.environ["POD_NAMESPACE"]
    result = {}
    if config.handler_type == "ChatHandler":
        if config.inference_host_endpoints is None:
            raise ValueError("inference_host_endpoints must be set")
        inference_stub_factory = _get_inference_stub_factory(
            config.inference_host_endpoints,
            tls_config.get_client_tls_creds(config.central_client_mtls),
            config.model_name,
        )
        assert postprocess is None  # Postprocess not currently supported in ChatHandler
        result[config.model_name] = create_chat_handler(
            config=config.handler_config,
            inference_stub_factory=inference_stub_factory,
            namespace=namespace,
            ri_publisher=ri_publisher,
            metrics=metrics,
            content_manager_client=content_manager_client,
            retriever=retriever,
        )
    elif config.handler_type == "ChatThirdPartyHandler":
        arbiter_client = _create_arbiter_client(config)
        result[config.model_name] = create_chat_third_party_handler(
            config=config.handler_config,
            namespace=namespace,
            ri_publisher=ri_publisher,
            metrics=metrics,
            content_manager_client=content_manager_client,
            retriever=retriever,
            postprocess=postprocess,
            suggested_questions_generator=suggested_questions_generator,
            arbiter_client=arbiter_client,
        )
    elif config.handler_type == "ChatThirdPartyMultiHandler":
        arbiter_client = _create_arbiter_client(config)
        for model_name, model_config in config.handler_config.items():
            log.debug(f"Creating handler config: {model_name} -> {model_config}")
            result[model_name] = create_chat_third_party_handler(
                config=model_config,
                namespace=namespace,
                ri_publisher=ri_publisher,
                metrics=metrics,
                content_manager_client=content_manager_client,
                retriever=retriever,
                postprocess=postprocess,
                suggested_questions_generator=suggested_questions_generator,
                arbiter_client=arbiter_client,
            )
    else:
        raise ValueError(f"Unsupported handler type: {config.handler_type=}")
    return result


def get_content_manager_client(config: Config) -> ContentManagerClient:
    """Returns a content manager client."""
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in config.content_manager_endpoint
        )
    )
    return ContentManagerClient.create_for_endpoint(
        config.content_manager_endpoint,
        credentials=tls_config.get_client_tls_creds(config.client_mtls),
        options=options,
    )


def get_token_exchange_client(
    config: Config,
    namespace: str,
) -> GrpcTokenExchangeClient:
    """Returns a token exchange client."""
    return GrpcTokenExchangeClient.create(
        config.auth_config.token_exchange_endpoint,
        namespace,
        tls_config.get_client_tls_creds(config.central_client_mtls),
    )


def run(
    config: Config,
    namespace: str,
    ri_publisher: RequestInsightPublisher,
    shutdown_event: threading.Event,
):
    content_manager_client = get_content_manager_client(config)
    token_exchange_client = get_token_exchange_client(config, namespace)
    working_set_client = get_working_set_client(config)
    ri_builder = RetrieverRequestInsightBuilder(ri_publisher)
    retriever = retriever_factory.create_retriever(
        config.retrieval,
        content_manager_client,
        tls_config.get_client_tls_creds(config.client_mtls),
        ri_builder,
        search_timeout_ms=5000,
        token_exchange_client=token_exchange_client,
    )

    # Download word list if ban_regexp_uri is set
    ban_rules = BanRules()
    start_regexp_download_thread(ban_rules, shutdown_event)

    # If postprocess config exists, create postprocess
    postprocess = None
    if config.postprocess is not None:
        postprocess = Postprocess(config.postprocess)

    suggested_questions_generator = None
    if config.suggested_questions_config is not None:
        suggested_questions_generator = create_suggested_questions_generator(
            config.suggested_questions_config
        )

    metrics = ChatHandlerMetrics()

    handlers = _get_handlers(
        config,
        retriever,
        postprocess,
        metrics,
        ri_publisher,
        content_manager_client,
        suggested_questions_generator,
    )

    token_client = GrpcTokenExchangeClient.create(
        config.auth_config.token_exchange_endpoint,
        namespace,
        tls_config.get_client_tls_creds(config.central_client_mtls),
    )
    service_auth = ServiceTokenAuth(
        GrpcPublicKeySource(token_client),
        required_scopes=["CONTENT_R"],
    )
    auth_interceptor = ServiceAuthInterceptor(service_auth)

    server = grpc.server(
        ThreadPoolExecutor(
            max_workers=config.max_server_workers, thread_name_prefix="server-"
        ),
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(),
            MetricsServerInterceptor(),
            auth_interceptor,
        ],
    )
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    chat_pb2_grpc.add_ChatServicer_to_server(
        ChatServices(config, handlers, working_set_client, ri_publisher, ban_rules),
        server,
    )
    service_names = (
        chat_pb2.DESCRIPTOR.services_by_name["Chat"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)

    server_credentials = tls_config.get_server_tls_creds(config.server_mtls)

    if server_credentials is not None:
        server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        server.add_insecure_port(f"[::]:{config.port}")

    server.start()
    logging.info("Listening on %s", config.port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # type: ignore # pylint: disable=no-member
        config_file.read_text(),
    )


def main():
    """Run the server."""
    handler = GracefulSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    RequestInsightPublisher.add_publisher_arguments(parser)
    args = parser.parse_args()
    logging.info("Args %s", args)

    if "POD_NAMESPACE" not in os.environ:
        raise RuntimeError("POD_NAMESPACE environment variable not set")
    namespace = os.environ["POD_NAMESPACE"]

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    # begin listening for Prometheus requests
    start_http_server(9090)

    config = _load_config(args.config_file)
    logging.info("Config %s", config)

    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)
    custom_endpoint = None
    if config.dynamic_feature_flags_endpoint is not None:
        custom_endpoint = config.dynamic_feature_flags_endpoint

    context = base.feature_flags.Context.setup(path, custom_endpoint)
    base.feature_flags.set_global_context(context)

    ri_publisher = RequestInsightPublisher.create_from_args(args)

    run(config, namespace, ri_publisher, handler.get_shutdown_event())


if __name__ == "__main__":
    main()
