"""Request filtering utilities for chat server."""

import hashlib
import random
import re
import requests
import threading
import time
import unicodedata
from collections import defaultdict
from collections.abc import Callable
from dataclasses import dataclass, field
from typing import Optional

import structlog
from prometheus_client import Counter

import base.feature_flags
from services.chat_host import chat_pb2
from services.request_insight import request_insight_pb2
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
    new_event,
)
from services.lib.request_context.request_context import RequestContext

log = structlog.get_logger()


_BAN_RULES_URL = base.feature_flags.StringFlag("chat_server_ban_rules_url_and_hash", "")
_MAX_CJK_CHAR_COUNT = base.feature_flags.IntFlag("chat_server_max_cjk_char_count", 100)
_BLOCK_HIGH_CJK_COUNT = base.feature_flags.BoolFlag(
    "chat_server_block_high_cjk_count", False
)
_BAN_MAX_SESSIONS_PER_USER = base.feature_flags.IntFlag(
    "chat_server_ban_max_sessions_per_user", 0
)
_BLOCK_CLIENTS_BY_PROMPT_REGEX = base.feature_flags.StringFlag(
    "chat_server_block_clients_by_prompt_regex", ""
)
_BLOCKED_DOMAINS = base.feature_flags.StringFlag("chat_server_blocked_domains", "")

# After this many suspicious requests, requests from this user will be probabilistically
# blocked.
#
# Note: 0 means block requests immediately, starting with the first suspicious one.
_REJECTION_THRESHOLD = base.feature_flags.IntFlag("chat_server_rejection_threshold", 5)

# Base probability for probabilistic blocking.
#
# Once reaching _REJECTION_THRESHOLD, the user's upcoming requests get accepted with
# probabilities [P, P^2, P^3, ...]
_BASE_ACCEPTANCE_PROBABILITY = base.feature_flags.FloatFlag(
    "chat_server_base_acceptance_probability", 0.9
)

_USE_PREFIX_AND_SUFFIX = base.feature_flags.BoolFlag(
    "chat_server_filter_use_prefix_and_suffix", False
)

_STRICT_USES_PREFIX_AND_SUFFIX = base.feature_flags.BoolFlag(
    "chat_server_strict_uses_prefix_and_suffix", True
)

# Prometheus metrics
_request_check_failures = Counter(
    "au_chat_request_check_failures",
    "Count of chat requests that failed content checks",
    ["check_type", "tenant_name"],
)

_cjk_count_exceeded = Counter(
    "au_chat_cjk_count_exceeded",
    "Count of chat requests that exceeded the CJK character count limit",
    ["tenant_name"],
)


def log_suspicious_request(
    check_type: str,
    user_id: str,
    metrics_tenant_name: str,
    auth_info=None,
    request_context: Optional[RequestContext] = None,
    ri_publisher: Optional[RequestInsightPublisher] = None,
) -> None:
    """Log a suspicious request and increment the corresponding metric.

    Args:
        check_type: The type of check that failed (e.g., "api_proxy_suspicion")
        user_id: The ID of the user who made the request
        tenant_name: The name of the tenant
        auth_info: Optional auth info object containing user and tenant information
        request_context: Optional request context for request insight
        ri_publisher: Optional request insight publisher
    """
    # Increment the metric counter
    _request_check_failures.labels(
        check_type=check_type, tenant_name=metrics_tenant_name
    ).inc()

    # Log the suspicious request
    request_id = request_context.request_id if request_context else "<unknown>"
    log.info(
        f"Detected a suspicious request {request_id} from user {user_id}: {check_type}"
    )

    # Publish suspicious event to request insight if publisher is available
    if (
        ri_publisher is not None
        and request_context is not None
        and auth_info is not None
    ):
        # Create a new event
        event = new_event()

        # Create a RequestSuspicious message for suspicious events
        # Set the user ID and email in the request_suspicious field
        event.request_suspicious.CopyFrom(
            request_insight_pb2.RequestSuspicious(
                opaque_user_id=auth_info.parsed_opaque_user_id,
                user_email=auth_info.user_email.get_secret_value()
                if auth_info.user_email
                else "",
                check_type=check_type,
            )
        )

        ri_publisher.publish_request_insight(
            ri_publisher.update_request_info_request(
                request_context.request_id,
                [event],
                auth_info,
            )
        )


def log_blocked_request(
    check_type: str,
    user_id: str,
    metrics_tenant_name: str,
    auth_info=None,
    request_context: Optional[RequestContext] = None,
    ri_publisher: Optional[RequestInsightPublisher] = None,
) -> None:
    """Log a blocked request and increment the corresponding metric.

    Args:
        check_type: The type of check that caused the block (e.g., "cjk_character_count")
        user_id: The ID of the user who made the request
        tenant_name: The name of the tenant
        auth_info: Optional auth info object containing user and tenant information
        request_context: Optional request context for request insight
        ri_publisher: Optional request insight publisher
    """
    # Increment the metric counter
    _request_check_failures.labels(
        check_type=check_type, tenant_name=metrics_tenant_name
    ).inc()

    # Log the blocked request
    request_id = request_context.request_id if request_context else "<unknown>"
    log.info(f"Blocked request {request_id} from user {user_id}: {check_type}")

    # Publish blocked event to request insight if publisher is available
    if (
        ri_publisher is not None
        and request_context is not None
        and auth_info is not None
    ):
        # Create a RequestBlocked message
        # Set the user ID and email in the request_blocked field
        event = new_event()
        event.request_blocked.CopyFrom(
            request_insight_pb2.RequestBlocked(
                opaque_user_id=auth_info.parsed_opaque_user_id,
                user_email=auth_info.user_email.get_secret_value()
                if auth_info.user_email
                else "",
                check_type=check_type,
            )
        )

        ri_publisher.publish_request_insight(
            ri_publisher.update_request_info_request(
                request_context.request_id,
                [event],
                auth_info,
            )
        )


class BanRules:
    def __init__(
        self,
        regexp: str | None = None,
        strict_regexp: str | None = None,
        block_prompt_regexp: str | None = None,
    ):
        assert regexp != ""
        assert strict_regexp != ""
        assert block_prompt_regexp != ""

        self.regex = None if regexp is None else re.compile(regexp)
        self.strict_regex = None if strict_regexp is None else re.compile(strict_regexp)
        self.block_prompt_regex = (
            None if block_prompt_regexp is None else re.compile(block_prompt_regexp)
        )

        self._lock = threading.Lock()

    def set(
        self,
        regexp: str | None,
        strict_regexp: str | None = None,
        block_prompt_regexp: str | None = None,
    ) -> None:
        assert regexp != ""
        assert strict_regexp != ""
        assert block_prompt_regexp != ""

        regex = None if regexp is None else re.compile(regexp)
        strict_regex = None if strict_regexp is None else re.compile(strict_regexp)
        block_prompt_regex = (
            None if block_prompt_regexp is None else re.compile(block_prompt_regexp)
        )
        with self._lock:
            self.regex = regex
            self.strict_regex = strict_regex
            self.block_prompt_regex = block_prompt_regex

    def get(self) -> re.Pattern[str] | None:
        with self._lock:
            return self.regex

    def get_strict(self) -> re.Pattern[str] | None:
        with self._lock:
            return self.strict_regex

    def get_block_prompt(self) -> re.Pattern[str] | None:
        with self._lock:
            return self.block_prompt_regex


def _count_regex_matches(
    regex: re.Pattern[str], context: str, max_matches: int = 10
) -> int:
    count = 0
    for _ in regex.finditer(context):
        count += 1
        if count > max_matches:
            return count

    return count


def _get_message_text(
    request: chat_pb2.ChatRequest,
):
    # Handle flat messages and structured messages (e.g., ones with images)
    return request.message + "".join(
        node.text_node.content
        for node in request.nodes
        if node.type == chat_pb2.ChatRequestNodeType.TEXT
    )


def _check_regex_matches(
    request: chat_pb2.ChatRequest,
    regex: re.Pattern[str],
    max_total_matches: int,
    max_message_matches: int,
    use_prefix_and_suffix: bool,
) -> bool:
    """Check if the request matches the regex within the allowed limits.

    Args:
        request: The chat request to check.
        regex: The regex pattern to match against.
        max_total_matches: Maximum number of total matches allowed across all content.
        max_message_matches: Maximum number of matches allowed in any single message or content piece.
        use_prefix_and_suffix: Whether to include the prefix and suffix in the context.

    Returns:
        True if the request is allowed (within limits), False otherwise.
    """
    # Count matches in the current request content
    if use_prefix_and_suffix:
        context = request.prefix + request.selected_code + request.suffix
    else:
        context = request.selected_code

    context = (
        context
        + _get_message_text(request)
        + request.path
        + request.user_guidelines
        + request.workspace_guidelines
    )

    # Count matches in the current message
    current_matches = _count_regex_matches(regex, context, max_total_matches + 1)

    # Check if current message exceeds the per-message limit
    if current_matches > max_message_matches:
        return False

    # Start tracking total matches across all content
    total_matches = current_matches

    # Count matches in chat history
    if request.chat_history:
        for exchange in request.chat_history:
            # Check message text
            message_matches = _count_regex_matches(
                regex, exchange.request_message, max_total_matches + 1 - total_matches
            ) + _count_regex_matches(
                regex, exchange.response_text, max_total_matches + 1 - total_matches
            )

            # Check if this message exceeds the per-message limit
            if message_matches > max_message_matches:
                return False

            total_matches += message_matches

            # Check if we've exceeded the total limit
            if total_matches > max_total_matches:
                return False

            # Check nodes in the exchange
            for node in exchange.request_nodes:
                if node.type == chat_pb2.ChatRequestNodeType.TEXT and hasattr(
                    node, "text"
                ):
                    # Access text attribute only if it exists
                    text = getattr(node, "text", "")
                    node_matches = _count_regex_matches(
                        regex, text, max_total_matches + 1 - total_matches
                    )

                    total_matches += node_matches

                    # Check if we've exceeded the total limit
                    if total_matches > max_total_matches:
                        return False

    # If we've made it this far, the request is allowed
    return True


@dataclass
class UserRejectionStats:
    """Tracks suspicious request statistics for a user."""

    # Number of rejected requests in current epoch
    current_epoch_rejected_requests: int = 0
    # Number of rejected requests in previous epoch
    previous_epoch_rejected_requests: int = 0
    # Set of session IDs in current epoch
    current_epoch_sessions: set[str] = field(default_factory=set)
    # Set of session IDs in previous epoch
    previous_epoch_sessions: set[str] = field(default_factory=set)


class SuspiciousUserTracker:
    """Tracks users who have had messages rejected and applies probabilistic rejection."""

    def __init__(
        self,
        get_rejection_threshold: Callable[[], int] = lambda: 5,
        get_base_acceptance_probability: Callable[[], float] = lambda: 0.9,
    ):
        """Initialize the tracker.

        Args:
            get_rejection_threshold: Function that returns the rejection threshold
            get_base_acceptance_probability: Function that returns the base acceptance probability
        """
        self.get_rejection_threshold = get_rejection_threshold
        self.get_base_acceptance_probability = get_base_acceptance_probability
        self.user_stats: dict[str, UserRejectionStats] = defaultdict(
            lambda: UserRejectionStats()
        )
        self.current_epoch = self._get_current_epoch()
        self._lock = threading.Lock()

    def _get_current_epoch(self) -> int:
        """Get the current epoch."""
        return int(time.time() / (12 * 3600))  # Epoch every 12 hours

    def _update_epochs(self) -> None:
        """Update epochs if needed."""
        current_epoch = self._get_current_epoch()

        # If we've moved to a new epoch since last update
        # Note: the new epoch in theory may not be the adjacent one
        if current_epoch > self.current_epoch:
            # Shift all users' current epoch stats to previous epoch
            for _, user_stats in self.user_stats.items():
                user_stats.previous_epoch_rejected_requests = (
                    user_stats.current_epoch_rejected_requests
                )
                user_stats.current_epoch_rejected_requests = 0
                user_stats.previous_epoch_sessions = user_stats.current_epoch_sessions
                user_stats.current_epoch_sessions = set()

            # Update the current epoch
            self.current_epoch = current_epoch

    def record_suspicious_request(self, user_id: str) -> None:
        """Record a suspicious request for a user.

        Args:
            user_id: Identifier for the user
            session_id: Optional session ID for the request
        """
        with self._lock:
            self._update_epochs()

            stats = self.user_stats[user_id]
            stats.current_epoch_rejected_requests += 1

    def track_session(self, user_id: str, session_id: str) -> int:
        """Record a session for a user.

        Args:
            user_id: Identifier for the user
            session_id: Session ID for the request

        Returns:
            Number of sessions observed for this user.
        """
        with self._lock:
            self._update_epochs()

            stats = self.user_stats[user_id]
            stats.current_epoch_sessions.add(session_id)

            num_sessions = max(
                len(stats.current_epoch_sessions), len(stats.previous_epoch_sessions)
            )
            return num_sessions

    def should_allow_request(self, user_id: str) -> bool:
        """Determine if a request from this user should be allowed based on their history.

        Args:
            user_id: Identifier for the user

        Returns:
            True if the request should be allowed, False otherwise
        """
        with self._lock:
            self._update_epochs()

            stats = self.user_stats[user_id]
            total_rejected_requests = (
                stats.current_epoch_rejected_requests
                + stats.previous_epoch_rejected_requests
            )

            # Log the number of rejected requests if > 0
            if total_rejected_requests > 0:
                log.info(
                    "The user %s has %d rejected requests",
                    user_id,
                    total_rejected_requests,
                )

            # Get the current rejection threshold
            rejection_threshold = self.get_rejection_threshold()

            # If user hasn't reached the threshold, always allow
            if total_rejected_requests < rejection_threshold:
                return True

            # At this point, total_rejected_requests >= rejection_threshold
            # For users who have reached or exceeded the threshold, we want their acceptance rate
            # to trend towards zero until the epoch resets
            # We use (total_rejected_requests - threshold + 1) as the power to ensure the penalty
            # increases with each rejected request beyond the threshold
            power = total_rejected_requests - rejection_threshold + 1
            acceptance_probability = self.get_base_acceptance_probability() ** power

            # For testing predictability
            random_value = random.random()
            result = random_value < acceptance_probability
            if result:
                log.info(
                    "The user %s is allowed to send a request due to probabilistic acceptance",
                    user_id,
                )
            # If we're rejecting this request, don't increment consecutive_rejections here
            # as that will be handled by record_rejection
            return result


# Global tracker instance
_suspicious_user_tracker = SuspiciousUserTracker(
    get_rejection_threshold=lambda: _REJECTION_THRESHOLD.get(
        base.feature_flags.get_global_context()
    ),
    get_base_acceptance_probability=lambda: _BASE_ACCEPTANCE_PROBABILITY.get(
        base.feature_flags.get_global_context()
    ),
)


def clear_user_tracker(
    rejection_threshold: int | None = None,
    base_acceptance_probability: float | None = None,
) -> None:
    """Reset the global suspicious user tracker.

    This is primarily used for testing to ensure a clean state between tests.

    Args:
        rejection_threshold: Number of rejections before applying probabilistic rejection.
            If None, uses the value from the feature flag.
        base_acceptance_probability: Base probability for accepting messages (0.9 = 90%).
            If None, uses the value from the feature flag.
    """
    global _suspicious_user_tracker

    # Create getter functions based on provided values or current values
    if rejection_threshold is not None:
        # Use a fixed value for testing
        def get_rejection_threshold():
            return rejection_threshold
    else:
        # Use the current getter function
        get_rejection_threshold = _suspicious_user_tracker.get_rejection_threshold

    if base_acceptance_probability is not None:
        # Use a fixed value for testing
        def get_base_acceptance_probability():
            return base_acceptance_probability
    else:
        # Use the current getter function
        get_base_acceptance_probability = (
            _suspicious_user_tracker.get_base_acceptance_probability
        )

    _suspicious_user_tracker = SuspiciousUserTracker(
        get_rejection_threshold=get_rejection_threshold,
        get_base_acceptance_probability=get_base_acceptance_probability,
    )


def clear_blocked_domains_cache() -> None:
    """Reset the global blocked domains cache.

    This is primarily used for testing to ensure a clean state between tests.
    """
    global _blocked_domains_cache
    _blocked_domains_cache.reset()


def allow_request(
    request: chat_pb2.ChatRequest,
    session_id: str | None,
    ban_rules: BanRules,
    auth_info=None,
    request_context: Optional[RequestContext] = None,
    ri_publisher: Optional[RequestInsightPublisher] = None,
) -> bool:
    """Returns True if the request should be allowed.

    Args:
        request: The chat request to check
        ban_rules: The ban rules to apply
        auth_info: Optional auth info object containing user and tenant information
        user_id: Optional identifier for the user making the request

        request_context: Optional request context for request insight
        ri_publisher: Optional request insight publisher

    Returns:
        True if the request should be allowed, False otherwise
    """
    # Extract user ID and tenant name from auth_info if available
    user_id = ""
    metrics_tenant_name = "unknown"
    user_email = None

    ff_context = base.feature_flags.get_global_context()

    # Whether we should report the request as suspicious
    # If a user sends multiple suspicious reports, we start to block them
    is_request_suspicious = False

    # Whether we should reject the request
    should_block_request = False

    if auth_info:
        if hasattr(auth_info, "opaque_user_id") and auth_info.opaque_user_id:
            user_id = str(auth_info.opaque_user_id)
        if hasattr(auth_info, "metrics_tenant_name"):
            metrics_tenant_name = auth_info.metrics_tenant_name
        if hasattr(auth_info, "user_email") and auth_info.user_email:
            # user_email is a pydantic.SecretStr, so we need to get the value
            user_email = auth_info.user_email.get_secret_value()

    # Check for CJK characters
    cjk_count, cjk_ratio = count_cjk_characters(request)

    # Get feature flag values
    max_count = _MAX_CJK_CHAR_COUNT.get(ff_context)
    should_block_count = _BLOCK_HIGH_CJK_COUNT.get(ff_context)

    # Check if the request is from a blocked domain
    if user_email:
        user_domain = get_domain_from_email(user_email)
        if user_domain:
            # Update the cache with the latest blocked domains list
            blocked_domains_entries = _BLOCKED_DOMAINS.get(ff_context)
            _blocked_domains_cache.update(blocked_domains_entries)

            # Check if the domain is in the blocked list
            if _blocked_domains_cache.contains(user_domain):
                log.info(f"Blocking request from blocked domain: {user_domain}")
                is_request_suspicious = True
                should_block_request = True

                # Track the blocked domain in metrics
                _request_check_failures.labels(
                    check_type="blocked_domain", tenant_name=metrics_tenant_name
                ).inc()

    # Check if the CJK character count exceeds the threshold
    if cjk_count > max_count:
        # Always track the metric regardless of whether we block
        _cjk_count_exceeded.labels(tenant_name=metrics_tenant_name).inc()

        if should_block_count:
            log.info(
                f"Rejecting request with high CJK character count: {cjk_count} > {max_count} (ratio: {cjk_ratio:.2f})"
            )
            is_request_suspicious = True
            # Track the CJK count exceeded in metrics
            log_blocked_request(
                "cjk_character_count",
                user_id,
                metrics_tenant_name,
                auth_info,
                request_context,
                ri_publisher,
            )
            return False
        else:
            log.info(
                f"Detected high CJK character count but not blocking (flag disabled): {cjk_count} > {max_count} (ratio: {cjk_ratio:.2f})"
            )

    # Check if API proxy marked this request as suspicious.
    if hasattr(request, "is_suspicious") and getattr(request, "is_suspicious", False):
        is_request_suspicious = True
        # Log and update metrics - suspicious but not blocked
        log_suspicious_request(
            "suspicious_api_proxy",
            user_id,
            metrics_tenant_name,
            auth_info,
            request_context,
            ri_publisher,
        )

    # Check against block prompt regex. Immediately reject and treat as suspicious.
    #
    # The purpose of this regex is to guard against use of unauthorized clients, like
    # GitHub Copilot or Cline.
    block_prompt_regex = ban_rules.get_block_prompt()
    if block_prompt_regex is not None:
        if not _check_regex_matches(
            request,
            block_prompt_regex,
            max_total_matches=0,
            max_message_matches=0,
            use_prefix_and_suffix=_USE_PREFIX_AND_SUFFIX.get(ff_context),
        ):
            # For block prompt regex, even a single match causes immediate rejection
            is_request_suspicious = True
            should_block_request = True

            # Log and update metrics
            log_suspicious_request(
                "suspicious_clients_by_prompt_regex",
                user_id,
                metrics_tenant_name,
                auth_info,
                request_context,
                ri_publisher,
            )
            log_blocked_request(
                "block_clients_by_prompt_regex",
                user_id,
                metrics_tenant_name,
                auth_info,
                request_context,
                ri_publisher,
            )

    # Check against strict regex - even a single match disallows the request.
    # These strict matches are high-confidence signatures that the user is doing
    # something they shouldn't be, like running an unauthorized custom app against
    # the Augment API.
    #
    # We'll treat the user as more suspicious, but won't reject this request. We want
    # to make it harder for someone to reverse-engineer our blocking behavior and adapt
    # their custom app to evade our checks.
    strict_regex = ban_rules.get_strict()
    if strict_regex is not None:
        if not _check_regex_matches(
            request,
            strict_regex,
            max_total_matches=0,
            max_message_matches=0,
            use_prefix_and_suffix=_STRICT_USES_PREFIX_AND_SUFFIX.get(ff_context),
        ):
            # Log and report the request, but don't reject it
            is_request_suspicious = True

            # Log and update metrics - suspicious but not blocked
            log_suspicious_request(
                "suspicious_strict_regex",
                user_id,
                metrics_tenant_name,
                auth_info,
                request_context,
                ri_publisher,
            )

    # Then check against regular regex with higher thresholds.
    #
    # These are terms that are very highly correlated with misuse. A certain number
    # of matches is needed in order for the request to count as suspicious. If it
    # does, we just reject the request outright and also count it as a suspicious
    # request against the user.
    regex = ban_rules.get()
    if regex is not None:
        # For regular regex, we allow more matches
        if not _check_regex_matches(
            request,
            regex,
            max_total_matches=10,
            max_message_matches=5,
            use_prefix_and_suffix=_USE_PREFIX_AND_SUFFIX.get(ff_context),
        ):
            # NSFW and objectionable content is not marked as suspicious because
            # we don't want to block legitimate users with NSFW content in their
            # repositories.
            #
            # We'll block the request because we believe it reduces the chance
            # we get in trouble with our AI provider. If we get clarification
            # from Anthropic/Google about the amount of filtering of our users's
            # inputs that they expect, we may be able to even drop the blocking.
            # We may still want to log even if we don't block so as to have
            # some visibility into how common certain language is.

            # Block the request and log it
            should_block_request = True

            # Log and update metrics
            log_blocked_request(
                "block_regular_regex",
                user_id,
                metrics_tenant_name,
                auth_info,
                request_context,
                ri_publisher,
            )

    # Check if the user has exceeded the maximum number of sessions
    max_sessions = _BAN_MAX_SESSIONS_PER_USER.get(ff_context)

    if max_sessions > 0 and user_id and session_id:
        # Check if user has exceeded session limit
        num_sessions = _suspicious_user_tracker.track_session(
            user_id,
            session_id,
        )
        if num_sessions > max_sessions:
            # Don't actually block the request, just adjust the stats and log it
            is_request_suspicious = True

            # Log and update metrics - suspicious but not blocked
            log_suspicious_request(
                "suspicious_too_many_sessions",
                user_id,
                metrics_tenant_name,
                auth_info,
                request_context,
                ri_publisher,
            )

    # Record the request as suspicious before probabilistic rejection is considered.
    # That way, a suspicious request itself contributes to the statistics used to
    # decide its own probabilistic rejection.
    if is_request_suspicious:
        _suspicious_user_tracker.record_suspicious_request(user_id)

    # Request passed all rule checks
    # Now check if user is being probabilistically rejected based on history
    if (
        user_id
        and not should_block_request
        and not _suspicious_user_tracker.should_allow_request(user_id)
    ):
        should_block_request = True

        # Log and update metrics
        log_blocked_request(
            "block_probabilistic_rejection",
            user_id,
            metrics_tenant_name,
            auth_info,
            request_context,
            ri_publisher,
        )

    return not should_block_request


class Downloader:
    class RequestsDownloaderImpl:
        def download(self, uri: str) -> str:
            response = requests.get(uri, timeout=10)
            response.raise_for_status()
            return response.text

    class NullDownloaderImpl:
        def __init__(self, content: str | Exception):
            self.content = content

        def download(self, _: str) -> str:
            if isinstance(self.content, Exception):
                raise self.content
            return self.content

    @staticmethod
    def create():
        return Downloader(Downloader.RequestsDownloaderImpl())

    @staticmethod
    def create_null(content: str | Exception = "foof"):
        return Downloader(Downloader.NullDownloaderImpl(content))

    def __init__(self, impl: RequestsDownloaderImpl | NullDownloaderImpl):
        self._impl = impl

    def download(self, uri: str) -> str:
        return self._impl.download(uri)


def regexp_definition_parser(content: str) -> tuple[str | None, str | None, str | None]:
    """Parse a regexp definition file.

    Args:
        content: The content of the file.

    Returns:
        A tuple of (regular_regexp, strict_regexp, block_prompt_regexp). Any may be None if not present.
    """
    lines = content.splitlines()

    # First line should be the regular regexp
    if len(lines) == 0 or not lines[0].strip():
        return None, None, None

    regular_regexp = lines[0]
    strict_regexp = None
    block_prompt_regexp = None

    # If there's a second line, it's the strict regexp
    if len(lines) > 1 and lines[1].strip():
        strict_regexp = lines[1]

    # If there's a third line, it's the block prompt regexp
    if len(lines) > 2 and lines[2].strip():
        block_prompt_regexp = lines[2]

    return regular_regexp, strict_regexp, block_prompt_regexp


class RegexpDownloader:
    def __init__(
        self,
        ban_rules: BanRules,
        downloader: Downloader | None = None,
    ):
        self.ban_rules = ban_rules
        self.downloader = downloader or Downloader.create()

    def regexp_download(self):
        ban_regexp_uri = _BAN_RULES_URL.get(base.feature_flags.get_global_context())
        feature_flag_block_prompt_regexp = _BLOCK_CLIENTS_BY_PROMPT_REGEX.get(
            base.feature_flags.get_global_context()
        )
        if not feature_flag_block_prompt_regexp:
            # If empty string, treat as None, *not* as "match everything"
            feature_flag_block_prompt_regexp = None

        # Default to using the feature flag value
        block_prompt_regexp = feature_flag_block_prompt_regexp
        regular_regexp = None
        strict_regexp = None

        if ban_regexp_uri != "":
            components = ban_regexp_uri.split(",")
            try:
                uri, check = (
                    components[0],
                    components[1] if len(components) > 1 else None,
                )
                content = self.downloader.download(uri)

                content_hash = hashlib.sha256(content.encode()).hexdigest()
                if check is not None and check != content_hash:
                    log.error(
                        f"Failed to download ban rules from {uri}: Hash mismatch '{check} vs '{content_hash}'"
                    )
                    raise RuntimeError("Hash mismatch")

                regular_regexp, strict_regexp, file_block_prompt_regexp = (
                    regexp_definition_parser(content)
                )

                # If we have a block prompt regexp from both the file and feature flag, combine them
                if (
                    file_block_prompt_regexp is not None
                    and feature_flag_block_prompt_regexp is not None
                ):
                    block_prompt_regexp = f"(?:{feature_flag_block_prompt_regexp})|(?:{file_block_prompt_regexp})"
                    log.info("Combined block prompt regexps from feature flag and file")
                elif file_block_prompt_regexp is not None:
                    block_prompt_regexp = file_block_prompt_regexp
                    log.info("Using block prompt regexp from file")
                # Otherwise, we're already using the feature flag value (or None)

                self.ban_rules.set(regular_regexp, strict_regexp, block_prompt_regexp)
                log.info(
                    "Ban rules status uri=%s regular=%s strict=%s block_prompt=%s",
                    uri,
                    regular_regexp is not None,
                    strict_regexp is not None,
                    block_prompt_regexp is not None,
                )
            except Exception as e:
                log.error(f"Failed to set ban rules: {e}")
        else:
            self.ban_rules.set(None, None, block_prompt_regexp)
            log.info(
                "Ban rules status regular=False strict=False block_prompt=%s",
                block_prompt_regexp is not None,
            )


class PeriodicBackgroundFunction:
    def __init__(
        self,
        function: Callable[[], None],
        shutdown_event: threading.Event,
        interval_seconds: float = 120.0,
        jitter_seconds: int = 10,
    ):
        self.function = function
        self.shutdown_event = shutdown_event
        self.interval_seconds = interval_seconds
        self.jitter_seconds = jitter_seconds
        self.thread = threading.Thread(target=self.run, daemon=True)

    def start(self):
        self.thread.start()

    def join(self):
        self.thread.join()

    def run(self):
        while not self.shutdown_event.is_set():
            try:
                self.function()
            except Exception as e:
                log.error(f"Exception in periodic function: {e}")
            self.shutdown_event.wait(
                timeout=self.interval_seconds
                + random.randint(
                    -self.jitter_seconds,
                    self.jitter_seconds,
                ),
            )


def start_regexp_download_thread(
    ban_rules: BanRules, shutdown_event: threading.Event
) -> None:
    downloader = RegexpDownloader(ban_rules)
    periodic_downloader = PeriodicBackgroundFunction(
        downloader.regexp_download, shutdown_event
    )
    periodic_downloader.start()


def is_cjk_character(char: str) -> bool:
    """Check if a character is a CJK character (Chinese, Japanese, Korean).

    Args:
        char: The character to check

    Returns:
        True if the character is a CJK character, False otherwise
    """
    # Chinese characters (both simplified and traditional)
    if "\u4e00" <= char <= "\u9fff":  # CJK Unified Ideographs
        return True
    if "\u3400" <= char <= "\u4dbf":  # CJK Unified Ideographs Extension A
        return True
    if "\u20000" <= char <= "\u2a6df":  # CJK Unified Ideographs Extension B
        return True

    # Japanese-specific characters
    if "\u3040" <= char <= "\u309f":  # Hiragana
        return True
    if "\u30a0" <= char <= "\u30ff":  # Katakana
        return True

    # Korean Hangul
    if "\uac00" <= char <= "\ud7af":  # Hangul Syllables
        return True
    if "\u1100" <= char <= "\u11ff":  # Hangul Jamo
        return True

    return False


# Define CJK character ranges for efficient matching
# Ranges within the Basic Multilingual Plane (BMP, code points ≤ 0xFFFF)
_CJK_BMP_RANGES = [
    (0x4E00, 0x9FFF),  # CJK Unified Ideographs
    (0x3400, 0x4DBF),  # CJK Unified Ideographs Extension A
    (0x3040, 0x309F),  # Hiragana
    (0x30A0, 0x30FF),  # Katakana
    (0xAC00, 0xD7AF),  # Hangul Syllables
    (0x1100, 0x11FF),  # Hangul Jamo
]

# Ranges beyond the BMP (code points > 0xFFFF)
_CJK_SUPPLEMENTARY_RANGES = [
    (0x20000, 0x2A6DF),  # CJK Unified Ideographs Extension B
]

# Create a regex pattern that matches any character in the BMP CJK ranges
_CJK_REGEX = re.compile(
    "[" + "".join(chr(start) + "-" + chr(end) for start, end in _CJK_BMP_RANGES) + "]"
)


def get_domain_from_email(email: str | None) -> str | None:
    """Extract domain from an email address.

    Args:
        email: The email address to extract domain from

    Returns:
        The domain part of the email, or None if no @ symbol is found
    """
    if not email:
        return None
    parts = email.split("@")
    if len(parts) != 2:
        return None
    return parts[1]


def parse_domains_list(domains_str: str | None) -> list[str]:
    """Parse a newline-separated list of domains.

    Args:
        domains_str: Newline-separated list of domains

    Returns:
        List of domains, or empty list if the input is empty or None
    """
    if not domains_str:
        return []

    # Split by newline and strip whitespace from each domain
    return [d.strip() for d in domains_str.split("\n") if d.strip()]


class BlockedDomainsCache:
    """Cache for the blocked domains list to avoid parsing the list on every request."""

    def __init__(self):
        self._domains: list[str] = []
        self._last_text: str = ""
        self._lock = threading.Lock()

    def update(self, domains_text: str | None) -> list[str]:
        """Update the cached domains list if the list string has changed.

        Args:
            domains_text: List of domains, newline-separated

        Returns:
            The current list of blocked domains
        """
        if domains_text is None:
            domains_text = ""

        with self._lock:
            # Only parse if the string has changed
            if domains_text != self._last_text:
                self._last_text = domains_text
                self._domains = parse_domains_list(domains_text)

            # Always return a copy to avoid external modification
            return self._domains.copy()

    def contains(self, domain: str | None) -> bool:
        """Check if a domain is in the blocked domains list.

        Args:
            domain: The domain to check

        Returns:
            True if the domain is in the blocked domains list, False otherwise
        """
        if not domain:
            return False

        with self._lock:
            return domain in self._domains

    def reset(self) -> None:
        """Reset the cache to its initial state.

        This is primarily used for testing to ensure a clean state between tests.
        """
        with self._lock:
            self._domains = []
            self._last_text = ""


# Global blocked domains cache
_blocked_domains_cache = BlockedDomainsCache()


def count_cjk_characters(request: chat_pb2.ChatRequest) -> tuple[int, float]:
    """Count the number of CJK characters in a request and calculate the ratio.

    Args:
        request: The chat request to analyze

    Returns:
        A tuple of (count, ratio) where count is the number of CJK characters
        and ratio is the ratio of CJK characters to total characters
    """
    # Extract all text content from the current request
    context = (
        request.prefix
        + request.selected_code
        + request.suffix
        + _get_message_text(request)
        + request.path
        + request.user_guidelines
        + request.workspace_guidelines
    )

    # Add chat history content if it exists
    if hasattr(request, "chat_history") and request.chat_history:
        for exchange in request.chat_history:
            # Add message text
            if hasattr(exchange, "request_message"):
                context += exchange.request_message

            # Add text from nodes in the exchange
            if hasattr(exchange, "request_nodes"):
                for node in exchange.request_nodes:
                    if (
                        hasattr(node, "type")
                        and node.type == chat_pb2.ChatRequestNodeType.TEXT
                        and hasattr(node, "text")
                    ):
                        context += getattr(node, "text", "")

    if not context:
        return 0, 0.0

    # Normalize the text to handle composed characters
    normalized_text = unicodedata.normalize("NFC", context)

    # Use regex to efficiently count CJK characters in the BMP
    cjk_count = len(_CJK_REGEX.findall(normalized_text))

    # For characters beyond the BMP (supplementary characters), check individually
    # This is typically a small subset of characters in most texts
    for char in normalized_text:
        code_point = ord(char)
        if code_point > 0xFFFF:  # Beyond BMP
            for start, end in _CJK_SUPPLEMENTARY_RANGES:
                if start <= code_point <= end:
                    cjk_count += 1
                    break

    # Calculate ratio
    total_chars = len(normalized_text)
    ratio = cjk_count / total_chars if total_chars > 0 else 0.0

    return cjk_count, ratio
