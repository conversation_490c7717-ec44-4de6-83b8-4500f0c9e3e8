"""A Python client library call a chat service."""

import logging
from typing import Iterable, Optional

import grpc

import base.python.grpc.client_options as client_options

from services.lib.request_context.request_context import RequestContext
from services.chat_host import chat_pb2, chat_pb2_grpc


def setup_stub(
    endpoint: str, credentials: Optional[grpc.ChannelCredentials]
) -> chat_pb2_grpc.ChatStub:
    """Setup the client stub for a chat service."""
    logging.info("Creating grpc client to %s with options %s", endpoint, [])
    if not credentials:
        channel = grpc.insecure_channel(endpoint, options=client_options.create())
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create()
        )
    stub = chat_pb2_grpc.ChatStub(channel)
    return stub


class ChatClient:
    """Class to call chat APIs remotely."""

    def __init__(self, endpoint: str, credentials: Optional[grpc.ChannelCredentials]):
        self.stub = setup_stub(endpoint, credentials)

    def chat(
        self,
        request: chat_pb2.ChatRequest,
        request_context: RequestContext,
        timeout: float = 120,
    ) -> chat_pb2.ChatResponse:
        """Generates the memory content for the given blob."""
        response = self.stub.Chat(
            request, timeout=timeout, metadata=request_context.to_metadata()
        )
        return response

    def chat_stream(
        self,
        request: chat_pb2.ChatRequest,
        request_context: RequestContext,
        timeout: float = 120,
    ) -> Iterable[chat_pb2.ChatResponse]:
        response = self.stub.ChatStream(
            request, timeout=timeout, metadata=request_context.to_metadata()
        )
        return response

    def chat_retrieval(
        self,
        request: chat_pb2.ChatRetrievalRequest,
        request_context: RequestContext,
        timeout: float = 120,
    ) -> chat_pb2.ChatRetrievalResponse:
        """Generates the memory content for the given blob."""
        response = self.stub.ChatRetrieval(
            request, timeout=timeout, metadata=request_context.to_metadata()
        )
        return response
