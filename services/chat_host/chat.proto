syntax = "proto3";
package chat;

import "base/blob_names/blob_names.proto";
import public "services/lib/proto/chat.proto";

service Chat {
  rpc Chat(ChatRequest) returns (ChatResponse);
  rpc ChatStream(ChatRequest) returns (stream ChatResponse);
  rpc FindMissing(FindMissingRequest) returns (FindMissingResponse);
  rpc <PERSON>t<PERSON><PERSON>rie<PERSON>(ChatRetrievalRequest) returns (ChatRetrievalResponse);
}

// Describe where cursor is in the current file.
// Useful for messages in the current context
message ChatPosition {
  // The offset in characters where prefix begins, relative to the beginning of file.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  int32 prefix_begin = 1;

  // The offset in characters where suffix ends, relative to the beginning of file.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  int32 suffix_end = 2;

  // The (last known) blob name of the current file.
  string blob_name = 3;
}

message PerFileChangeStats {
  string file_path = 1;
  int32 insertion_count = 2;
  int32 deletion_count = 3;
  string old_file_path = 4;
}

message PerTypeChangedFileStats {
  int32 changed_file_count = 1;

  // The first few changed files. To avoid transmitting too many files, we truncate in
  // the middle. It is important to truncate in the middle so that the model can better
  // understand the overall change.
  repeated PerFileChangeStats per_file_change_stats_head = 2;

  // The last few changed files. To avoid transmitting too many files, we truncate in
  // the middle. It is important to truncate in the middle so that the model can better
  // understand the overall change.
  repeated PerFileChangeStats per_file_change_stats_tail = 3;
}

message ChangedFileStats {
  PerTypeChangedFileStats added_file_stats = 1;
  PerTypeChangedFileStats broken_file_stats = 2;
  PerTypeChangedFileStats copied_file_stats = 3;
  PerTypeChangedFileStats deleted_file_stats = 4;
  PerTypeChangedFileStats modified_file_stats = 5;
  PerTypeChangedFileStats renamed_file_stats = 6;
  PerTypeChangedFileStats unmerged_file_stats = 7;
  PerTypeChangedFileStats unknown_file_stats = 8;
}

message ChatFeatureDetectionFlags {
  reserved 1, 2;
  optional bool support_relevant_sources = 3;
  optional bool support_tool_use_start = 4;
  optional bool support_parallel_tool_use = 5;
}

message ChatRetrievalRequest {
  // The name of the model to use.
  string model_name = 1;

  // the retrieval query
  string query = 2 [debug_redact = true];

  // Blobs to include in the retrieval corpus, specified in the compact
  // delta form. Replaces the blob_names field.
  //
  // Each request needs to set at least a single blobs object (the baseline checkpoint and added might be empty)
  repeated base.blob_names.Blobs blobs = 3;

  // Max number of chunks to return
  int32 max_chunks = 4;
}

message RetrievedChunk {
  string text = 1 [debug_redact = true];
  string path = 2 [debug_redact = true];
  int32 char_start = 3;
  int32 char_end = 4;
  string blob_name = 5;
}

message ChatRetrievalResponse {
  // The retrieved chunks
  repeated RetrievedChunk retrieved_chunks = 1;

  // The subset of the requested blobs that are unknown to the server
  repeated string unknown_blob_names = 2;

  // Set to True if the checkpoint was not found.
  bool checkpoint_not_found = 3;
}

// Note: chat.ChatRequest intentionally does not include ChatMode that
// is present in public_api.ChatRequest, as ChatMode only
// exists to resolve default model. Behavior of the request
// should be controlled by model and prompt formatter. If we
// begin to diverge much more than that in the logic, we
// should probably fork the endpoint.

message ChatRequest {
  // The name of the model to use.
  string model_name = 1;

  // the path to the currently open file
  string path = 2 [debug_redact = true];

  // the code before the selected code
  string prefix = 3 [debug_redact = true];

  // the code selected in the current buffer
  string selected_code = 4 [debug_redact = true];

  // the code after the selcted code
  string suffix = 5 [debug_redact = true];

  // the message to be used for the prompt
  string message = 6 [debug_redact = true];

  // history of previous messages alternating request/response
  repeated Exchange chat_history = 7 [debug_redact = true];

  ChatPosition position = 8;

  reserved 9;

  // Programming language of the current file.
  string lang = 10;

  // Sequence IDs should be strictly increasing within a session.
  //
  // Requests may cancel requests with lower or equal sequence ID from the
  // same session and namespace. This helps prevent accidental cancellation
  // of requests when network messages are reordered.
  uint32 sequence_id = 11;

  // Blobs to include in the retrieval corpus, specified in the compact
  // delta form. Replaces the blob_names field.
  //
  // Each request needs to set at least a single blobs object (the baseline checkpoint and added might be empty)
  repeated base.blob_names.Blobs blobs = 12;

  bool enable_preference_collection = 13;

  // Optional list of blobs to focus on in retrieval for this chat request
  repeated string user_guided_blobs = 14;

  // Optional list of external sources to focus on in retrieval for this chat
  // request
  repeated string external_source_ids = 16;

  // The request ID of the exchange to which the context code belongs.
  // "new" if context code belongs to the current user message.
  // "context code" includes selected code and current file.
  string context_code_exchange_request_id = 15;

  // if set to true, no external sources should be added
  // by the host.
  //
  // Note: the automatic detection of external sources can also be disabled for other reasons.
  bool disable_auto_external_sources = 17;

  // what kind of prompt formatter to use.
  string prompt_formatter_name = 18;

  // Stats of changed files for the repository diff.
  optional ChangedFileStats changed_file_stats = 19;

  // The diff of the repository.
  optional string diff = 20;

  // Relevant commit messages that inform the model about current commit's context.
  repeated string relevant_commit_messages = 21;

  // Example commit messages from the repository to illustrate the style and conventions
  // of commit messages.
  repeated string example_commit_messages = 22;

  // Additional system prompt specified by the user.
  optional string user_guidelines = 23;

  // Additional system prompt specified by the workspace.
  optional string workspace_guidelines = 24;

  // Flags to indicate to backend that the frontend supports specific features
  optional ChatFeatureDetectionFlags feature_detection_flags = 25;

  // Tools that the model can use
  repeated chat.ToolDefinition tool_definitions = 26;

  // User message, represented as a list of structured content blocks
  // Note: if `nodes` is a non-empty list, `message` should be empty.
  repeated ChatRequestNode nodes = 27;

  // Memories to be included for the Agent system prompt.
  optional string agent_memories = 28;

  // The persona type that the AI assistant should adopt for this chat turn.
  optional PersonaType persona_type = 29;

  // set to true if the call has been determined to be suspicious
  // by the caller. The information is based to the chat server
  // for uniform handling and monitoring.
  bool is_suspicious = 30;

  repeated Rule rules = 31;

  // Whether this message is silent and should not be part of the chat history
  bool silent = 32;

  // Tasks to be included for the Agent system prompt. (currently deprecated due to cach concerns)
  optional string agent_tasks = 33;
}

enum RuleType {
  ALWAYS_ATTACHED = 0;
  MANUAL = 1;
}

message Rule {
  RuleType type = 1;

  string path = 2;

  string content = 3;
}

// The persona type that the AI assistant should adopt for this chat turn.
enum PersonaType {
  // The default persona, which is an expert software engineer.
  DEFAULT = 0;
  // The prototyper persona, which is an expert software engineer that is
  // focused on building new web apps.
  PROTOTYPER = 1;
  // The brainstorm persona, which is an expert software engineer that is
  // focused on planning and brainstorming solutions.
  BRAINSTORM = 2;
  // The reviewer persona, which is an expert software engineer that is
  // focused on reviewing code changes and identifying potential issues.
  REVIEWER = 3;
}

message IncorporatedExternalSource {
  // The name of the external source that was used.
  string source_name = 1;

  // The link to the external source.
  optional string link = 2;

  string source_id = 3;

  // TODO: add source type here?
}

message WorkspaceFileChunk {
  int32 char_start = 1;
  int32 char_end = 2;
  string blob_name = 3;
}

enum ChatResponseStatusCode {
  OK = 0; // Default
  EXCEED_CONTEXT_LENGTH = 1;
  WAS_BLOCKED = 2; // Request was blocked. API proxy should sleep a bit to push back.
}

message ChatResponse {
  // The reponse text
  string text = 1 [debug_redact = true];

  // The subset of the requested blobs that are unknown to the server
  repeated string unknown_blob_names = 2;

  // Set to True if the checkpoint was not found.
  bool checkpoint_not_found = 3;

  repeated WorkspaceFileChunk workspace_file_chunks = 4;

  // the context used for the chat result that is NOT from the workspace (see workspace_file_chunks)
  // ther eis no ordering assumption between workspace_file_chunks and this field or between
  // the entries in this list. The list might be truncated by the service.
  repeated IncorporatedExternalSource incorporated_external_sources = 5;

  // Should only exist when requested to use the replace_text tool
  optional ChatReplaceText replace_text_response = 6;

  // The nodes of the structured chat response.
  repeated ChatResultNode nodes = 7;

  // Status code for the chat response.
  optional ChatResponseStatusCode status_code = 8;

  // Reason that generation stopped.
  // When streaming, populated once it is known. Does not
  // imply that the stream has ended.
  optional ChatStopReason stop_reason = 9;
}

message ChatReplaceText {
  string replacement_text = 1 [debug_redact = true];
  int32 start_line_number = 2;
  int32 end_line_number = 3;
}

message ChatResultNode {
  // Unique id of the node.
  int32 id = 1;

  // Type of the node.
  chat.ChatResultNodeType type = 2;

  // Content of the node.
  string content = 3;

  // Additional content if this is a tool-use node.
  optional ChatResultToolUse tool_use = 4;
}

message ChatRequestNode {
  // Unique id of the node.
  int32 id = 1;

  // Type of the node.
  chat.ChatRequestNodeType type = 2;

  // Additional content if this is a text node.
  optional chat.ChatRequestText text_node = 3;

  // Additional content if this is a tool-result node.
  optional chat.ChatRequestToolResult tool_result_node = 4;

  // Additional content if this is an image node.
  optional chat.ChatRequestImage image_node = 5;

  reserved 6; // Reserved for Image Id.

  // Additional content if this is an IDE state node.
  optional chat.ChatRequestIdeState ide_state_node = 7;

  // Additional content if this is a user edits proto.
  optional chat.ChatRequestEditEvents edit_events_node = 8;
}

// NOTE(arun): These fields are similar to base.diff_utils.SingleEdit, but we don't want
// to depend on that proto here. The other main difference is that we use line numbers
// instead of character offsets.

// NOTE(arun): These fields are similar to base.diff_utils.GranularEditEvent, but we
// don't want to depend on that proto here.
// Here's an example of what it looks like in JSON:
// {
//     "id": 2,
//     "type": 5,
//     "edit_events_node": {
//         "edit_events": [
//             {
//                 "path": "/home/<USER>/projects/repo/src/main.rs",
//                 "before_blob_name": "abc123",
//                 "after_blob_name": "def456",
//                 "edits": [
//                     {
//                         "before_line_start": 10,
//                         "before_text": "old code",
//                         "after_line_start": 10,
//                         "after_text": "new code"
//                     }
//                 ]
//             }
//         ]
//     }
// }

// A single exchange between the user and the model.
//
// A request or response message may be represented as a string message or a list of structured content
// nodes/blocks. For both request and response, if nodes are present, the string message should be empty.
//
// The structured API is preferred, but the text-based is still supported for backwards compatibility.
message Exchange {
  string request_message = 1 [debug_redact = true];
  optional string response_text = 2 [debug_redact = true];
  optional string request_id = 3;
  repeated ChatRequestNode request_nodes = 4 [debug_redact = true];
  repeated ChatResultNode response_nodes = 5 [debug_redact = true];
}

message MessageBlock {
  map<string, string> contentBlock = 1;
}

message FindMissingRequest {
  // The model to probe for missing and non-indexed blobs.
  string model_name = 1;

  // The collection of blob names.
  repeated string blob_names = 2;
}

message FindMissingResponse {
  // (deprecated, call ContentManager instead) The subset of blob_names that are completely unknown.
  repeated string missing_blob_names = 1 [deprecated = true];

  // The subset of blob names that are not indexed for the given model.
  repeated string nonindexed_blob_names = 2;
}
