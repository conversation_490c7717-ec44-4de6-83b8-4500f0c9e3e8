"""Unit tests for chat_proto_util.py."""

import json
from typing import List

import pytest

from base.prompt_format.common import (
    ChatRequestEditEvents,
    ChatRequestFileEdit,
    ChatRequestIdeState,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestSingleEdit,
    ChatRequestText,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    EditEventSource,
    RequestMessage,
    ResponseMessage,
    TerminalInfo,
    WorkspaceFolderInfo,
)
from services.chat_host import chat_pb2
from services.chat_host.chat_proto_util import (
    request_message_to_proto,
    request_to_message,
    response_message_to_proto,
    response_to_message,
)


class TestRequestToMessage:
    """Tests for request_to_message function."""

    def test_request_to_message_with_string(self):
        """Test request_to_message with a string message."""
        request_message = "Hello, world!"
        request_nodes: List[chat_pb2.ChatRequestNode] = []
        result = request_to_message(request_message, request_nodes)
        assert result == "Hello, world!"

    def test_request_to_message_with_text_node(self):
        """Test request_to_message with a text node."""
        request_message = ""
        text_node = chat_pb2.ChatRequestNode(
            id=1,
            type=chat_pb2.ChatRequestNodeType.TEXT,
            text_node=chat_pb2.ChatRequestText(content="Hello, world!"),
        )
        request_nodes = [text_node]
        result = request_to_message(request_message, request_nodes)
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].id == 1
        assert result[0].type == ChatRequestNodeType.TEXT
        assert result[0].text_node is not None
        assert result[0].text_node.content == "Hello, world!"
        assert result[0].tool_result_node is None

    def test_request_to_message_with_tool_result_node(self):
        """Test request_to_message with a tool result node."""
        request_message = ""
        tool_result_node = chat_pb2.ChatRequestNode(
            id=2,
            type=chat_pb2.ChatRequestNodeType.TOOL_RESULT,
            tool_result_node=chat_pb2.ChatRequestToolResult(
                tool_use_id="tool-123",
                content="Tool result content",
                is_error=False,
                request_id="req-456",
            ),
        )
        request_nodes = [tool_result_node]
        result = request_to_message(request_message, request_nodes)
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].id == 2
        assert result[0].type == ChatRequestNodeType.TOOL_RESULT
        assert result[0].text_node is None
        assert result[0].tool_result_node is not None
        assert result[0].tool_result_node.tool_use_id == "tool-123"
        assert result[0].tool_result_node.content == "Tool result content"
        assert result[0].tool_result_node.is_error is False
        assert result[0].tool_result_node.request_id == "req-456"

    def test_request_to_message_with_image_id_node(self):
        """Test request_to_message with an image id node."""
        request_message = ""
        image_id_node = chat_pb2.ChatRequestNode(
            id=2,
            type=chat_pb2.ChatRequestNodeType.IMAGE_ID,
        )
        request_nodes = [image_id_node]
        result = request_to_message(request_message, request_nodes)
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].id == 2
        assert result[0].type == ChatRequestNodeType.TEXT
        assert result[0].text_node is not None
        assert result[0].text_node.content == " <failed to load image data> "
        assert result[0].tool_result_node is None

    def test_request_to_message_with_ide_state_node(self):
        """Test request_to_message with an IDE state node."""
        request_message = ""
        ide_state_node = chat_pb2.ChatRequestNode(
            id=3,
            type=chat_pb2.ChatRequestNodeType.IDE_STATE,
            ide_state_node=chat_pb2.ChatRequestIdeState(
                workspace_folders=[
                    chat_pb2.WorkspaceFolderInfo(
                        repository_root="/home/<USER>/repo",
                        folder_root="/home/<USER>/repo/project",
                    ),
                    chat_pb2.WorkspaceFolderInfo(
                        repository_root="/home/<USER>/another-repo",
                        folder_root="/home/<USER>/another-repo/lib",
                    ),
                ],
                workspace_folders_unchanged=True,
                current_terminal=chat_pb2.TerminalInfo(
                    terminal_id=123,
                    current_working_directory="/home/<USER>/repo/project/src",
                ),
            ),
        )
        request_nodes = [ide_state_node]
        result = request_to_message(request_message, request_nodes)
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].id == 3
        assert result[0].type == ChatRequestNodeType.IDE_STATE
        assert result[0].text_node is None
        assert result[0].tool_result_node is None
        assert result[0].ide_state_node is not None
        assert len(result[0].ide_state_node.workspace_folders) == 2
        assert (
            result[0].ide_state_node.workspace_folders[0].repository_root
            == "/home/<USER>/repo"
        )
        assert (
            result[0].ide_state_node.workspace_folders[0].folder_root
            == "/home/<USER>/repo/project"
        )
        assert (
            result[0].ide_state_node.workspace_folders[1].repository_root
            == "/home/<USER>/another-repo"
        )
        assert (
            result[0].ide_state_node.workspace_folders[1].folder_root
            == "/home/<USER>/another-repo/lib"
        )
        assert result[0].ide_state_node.workspace_folders_unchanged is True
        assert result[0].ide_state_node.current_terminal is not None
        assert result[0].ide_state_node.current_terminal.terminal_id == 123
        assert (
            result[0].ide_state_node.current_terminal.current_working_directory
            == "/home/<USER>/repo/project/src"
        )

    def test_request_to_message_with_edit_events_node(self):
        """Test request_to_message with an edit events node."""
        request_message = ""
        edit_events_node = chat_pb2.ChatRequestNode(
            id=4,
            type=chat_pb2.ChatRequestNodeType.EDIT_EVENTS,
            edit_events_node=chat_pb2.ChatRequestEditEvents(
                edit_events=[
                    chat_pb2.ChatRequestFileEdit(
                        path="/home/<USER>/repo/src/main.py",
                        before_blob_name="abc123",
                        after_blob_name="def456",
                        edits=[
                            chat_pb2.ChatRequestSingleEdit(
                                before_line_start=10,
                                before_text="def old_function():\n    return 'old'",
                                after_line_start=10,
                                after_text="def new_function():\n    return 'new'",
                            ),
                            chat_pb2.ChatRequestSingleEdit(
                                before_line_start=20,
                                before_text="# Old comment",
                                after_line_start=20,
                                after_text="# New comment",
                            ),
                        ],
                    ),
                    chat_pb2.ChatRequestFileEdit(
                        path="/home/<USER>/repo/src/utils.py",
                        before_blob_name="ghi789",
                        after_blob_name="jkl012",
                        edits=[
                            chat_pb2.ChatRequestSingleEdit(
                                before_line_start=5,
                                before_text="VERSION = '1.0.0'",
                                after_line_start=5,
                                after_text="VERSION = '1.1.0'",
                            ),
                        ],
                    ),
                ],
                source=chat_pb2.EditEventSource.CHECKPOINT_REVERT,
            ),
        )
        request_nodes = [edit_events_node]
        result = request_to_message(request_message, request_nodes)
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].id == 4
        assert result[0].type == ChatRequestNodeType.EDIT_EVENTS
        assert result[0].text_node is None
        assert result[0].tool_result_node is None
        assert result[0].ide_state_node is None
        assert result[0].edit_events_node is not None
        assert len(result[0].edit_events_node.edit_events) == 2

        # Check first file edit
        file_edit1 = result[0].edit_events_node.edit_events[0]
        assert file_edit1.path == "/home/<USER>/repo/src/main.py"
        assert file_edit1.before_blob_name == "abc123"
        assert file_edit1.after_blob_name == "def456"
        assert len(file_edit1.edits) == 2
        assert file_edit1.edits[0].before_line_start == 10
        assert (
            file_edit1.edits[0].before_text == "def old_function():\n    return 'old'"
        )
        assert file_edit1.edits[0].after_line_start == 10
        assert file_edit1.edits[0].after_text == "def new_function():\n    return 'new'"
        assert file_edit1.edits[1].before_line_start == 20
        assert file_edit1.edits[1].before_text == "# Old comment"
        assert file_edit1.edits[1].after_line_start == 20
        assert file_edit1.edits[1].after_text == "# New comment"

        # Check second file edit
        file_edit2 = result[0].edit_events_node.edit_events[1]
        assert file_edit2.path == "/home/<USER>/repo/src/utils.py"
        assert file_edit2.before_blob_name == "ghi789"
        assert file_edit2.after_blob_name == "jkl012"
        assert len(file_edit2.edits) == 1
        assert file_edit2.edits[0].before_line_start == 5
        assert file_edit2.edits[0].before_text == "VERSION = '1.0.0'"
        assert file_edit2.edits[0].after_line_start == 5
        assert file_edit2.edits[0].after_text == "VERSION = '1.1.0'"

    def test_request_to_message_with_multiple_nodes(self):
        """Test request_to_message with multiple nodes."""
        request_message = ""
        text_node = chat_pb2.ChatRequestNode(
            id=1,
            type=chat_pb2.ChatRequestNodeType.TEXT,
            text_node=chat_pb2.ChatRequestText(content="Hello, world!"),
        )
        tool_result_node = chat_pb2.ChatRequestNode(
            id=2,
            type=chat_pb2.ChatRequestNodeType.TOOL_RESULT,
            tool_result_node=chat_pb2.ChatRequestToolResult(
                tool_use_id="tool-123",
                content="Tool result content",
                is_error=False,
            ),
        )
        image_id_node = chat_pb2.ChatRequestNode(
            id=3,
            type=chat_pb2.ChatRequestNodeType.IMAGE_ID,
        )
        request_nodes = [text_node, tool_result_node, image_id_node]
        result = request_to_message(request_message, request_nodes)
        assert isinstance(result, list)
        assert len(result) == 3
        assert result[0].type == ChatRequestNodeType.TEXT
        assert result[1].type == ChatRequestNodeType.TOOL_RESULT
        assert result[2].type == ChatRequestNodeType.TEXT  # IMAGE_ID gets replaced


class TestResponseToMessage:
    """Tests for response_to_message function."""

    def test_response_to_message_with_string(self):
        """Test response_to_message with a string message."""
        response_message = "Hello, world!"
        response_nodes: List[chat_pb2.ChatResultNode] = []
        result = response_to_message(response_message, response_nodes)
        assert result == "Hello, world!"

    def test_response_to_message_with_raw_response_node(self):
        """Test response_to_message with a raw response node."""
        response_message = ""
        raw_response_node = chat_pb2.ChatResultNode(
            id=1,
            type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
            content="Hello, world!",
        )
        response_nodes = [raw_response_node]
        result = response_to_message(response_message, response_nodes)
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].id == 1
        assert result[0].type == ChatResultNodeType.RAW_RESPONSE
        assert result[0].content == "Hello, world!"
        assert result[0].tool_use is None

    def test_response_to_message_with_tool_use_start_node(self):
        """Test response_to_message with a tool use start node.
        Tool-use-start, like MAIN_TEXT_FINISHED is a streaming event and not part of the "history",
        so it results in an error
        """
        response_message = ""
        tool_use_start_node = chat_pb2.ChatResultNode(
            id=2,
            type=chat_pb2.ChatResultNodeType.TOOL_USE_START,
            content="Tool use start content",
            tool_use=chat_pb2.ChatResultToolUse(
                tool_use_id="tool-123",
                tool_name="my-tool",
                input_json="",
            ),
        )
        response_nodes = [tool_use_start_node]
        with pytest.raises(ValueError):
            response_to_message(response_message, response_nodes)

    def test_response_to_message_with_tool_use_node(self):
        """Test response_to_message with a tool use node."""
        response_message = ""
        tool_use_node = chat_pb2.ChatResultNode(
            id=2,
            type=chat_pb2.ChatResultNodeType.TOOL_USE,
            content="Tool use content",
            tool_use=chat_pb2.ChatResultToolUse(
                tool_use_id="tool-123",
                tool_name="my-tool",
                input_json=json.dumps({"param": "value"}),
            ),
        )
        response_nodes = [tool_use_node]
        result = response_to_message(response_message, response_nodes)
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].id == 2
        assert result[0].type == ChatResultNodeType.TOOL_USE
        assert result[0].content == "Tool use content"
        assert result[0].tool_use is not None
        assert result[0].tool_use.tool_use_id == "tool-123"
        assert result[0].tool_use.name == "my-tool"
        assert result[0].tool_use.input == {"param": "value"}

    def test_response_to_message_with_multiple_nodes(self):
        """Test response_to_message with multiple nodes."""
        response_message = ""
        raw_response_node = chat_pb2.ChatResultNode(
            id=1,
            type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
            content="Hello, world!",
        )
        tool_use_node = chat_pb2.ChatResultNode(
            id=2,
            type=chat_pb2.ChatResultNodeType.TOOL_USE,
            content="Tool use content",
            tool_use=chat_pb2.ChatResultToolUse(
                tool_use_id="tool-123",
                tool_name="my-tool",
                input_json=json.dumps({"param": "value"}),
            ),
        )
        response_nodes = [raw_response_node, tool_use_node]
        result = response_to_message(response_message, response_nodes)
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0].type == ChatResultNodeType.RAW_RESPONSE
        assert result[1].type == ChatResultNodeType.TOOL_USE


class TestRoundTrip:
    """Tests for round-trip conversions."""

    def test_request_message_round_trip_string(self):
        """Test round-trip conversion of a string request message."""
        original_message = "Hello, world!"
        proto_nodes = request_message_to_proto(original_message)
        round_trip_message = request_to_message("", proto_nodes)
        assert isinstance(round_trip_message, list)
        assert len(round_trip_message) == 1
        assert round_trip_message[0].type == ChatRequestNodeType.TEXT
        assert round_trip_message[0].text_node is not None
        assert round_trip_message[0].text_node.content == "Hello, world!"
        assert round_trip_message[0].tool_result_node is None

    def test_request_message_round_trip_ide_state_node(self):
        """Test round-trip conversion of an IDE state request message node."""
        ide_state_node = ChatRequestNode(
            id=3,
            type=ChatRequestNodeType.IDE_STATE,
            text_node=None,
            tool_result_node=None,
            image_node=None,
            ide_state_node=ChatRequestIdeState(
                workspace_folders=[
                    WorkspaceFolderInfo(
                        repository_root="/home/<USER>/repo",
                        folder_root="/home/<USER>/repo/project",
                    ),
                    WorkspaceFolderInfo(
                        repository_root="/home/<USER>/another-repo",
                        folder_root="/home/<USER>/another-repo/lib",
                    ),
                ],
                workspace_folders_unchanged=True,
                current_terminal=TerminalInfo(
                    terminal_id=123,
                    current_working_directory="/home/<USER>/repo/project/src",
                ),
            ),
        )
        original_message: RequestMessage = [ide_state_node]
        proto_nodes = request_message_to_proto(original_message)
        round_trip_message = request_to_message("", proto_nodes)
        assert round_trip_message == original_message

    def test_request_message_round_trip_edit_events_node(self):
        """Test round-trip conversion of an edit events request message node."""
        edit_events_node = ChatRequestNode(
            id=4,
            type=ChatRequestNodeType.EDIT_EVENTS,
            text_node=None,
            tool_result_node=None,
            image_node=None,
            edit_events_node=ChatRequestEditEvents(
                edit_events=[
                    ChatRequestFileEdit(
                        path="/home/<USER>/repo/src/main.py",
                        before_blob_name="abc123",
                        after_blob_name="def456",
                        edits=[
                            ChatRequestSingleEdit(
                                before_line_start=10,
                                before_text="def old_function():\n    return 'old'",
                                after_line_start=10,
                                after_text="def new_function():\n    return 'new'",
                            ),
                            ChatRequestSingleEdit(
                                before_line_start=20,
                                before_text="# Old comment",
                                after_line_start=20,
                                after_text="# New comment",
                            ),
                        ],
                    ),
                    ChatRequestFileEdit(
                        path="/home/<USER>/repo/src/utils.py",
                        before_blob_name="ghi789",
                        after_blob_name="jkl012",
                        edits=[
                            ChatRequestSingleEdit(
                                before_line_start=5,
                                before_text="VERSION = '1.0.0'",
                                after_line_start=5,
                                after_text="VERSION = '1.1.0'",
                            ),
                        ],
                    ),
                ],
                source=EditEventSource.CHECKPOINT_REVERT,
            ),
        )
        original_message: RequestMessage = [edit_events_node]
        proto_nodes = request_message_to_proto(original_message)
        round_trip_message = request_to_message("", proto_nodes)
        assert round_trip_message == original_message

    def test_request_message_round_trip_nodes(self):
        """Test round-trip conversion of request message nodes."""
        text_node = ChatRequestNode(
            id=1,
            type=ChatRequestNodeType.TEXT,
            text_node=ChatRequestText(content="Hello, world!"),
            tool_result_node=None,
            image_node=None,
        )
        tool_result_node = ChatRequestNode(
            id=2,
            type=ChatRequestNodeType.TOOL_RESULT,
            text_node=None,
            tool_result_node=ChatRequestToolResult(
                tool_use_id="tool-123",
                content="Tool result content",
                is_error=False,
                request_id="req-456",
            ),
            image_node=None,
        )
        original_message: RequestMessage = [text_node, tool_result_node]
        proto_nodes = request_message_to_proto(original_message)
        round_trip_message = request_to_message("", proto_nodes)
        assert round_trip_message == original_message

    def test_response_message_round_trip_string(self):
        """Test round-trip conversion of a string response message."""
        original_message = "Hello, world!"
        proto_nodes = response_message_to_proto(original_message)
        round_trip_message = response_to_message("", proto_nodes)
        assert isinstance(round_trip_message, list)
        assert len(round_trip_message) == 1
        assert round_trip_message[0].type == ChatResultNodeType.RAW_RESPONSE
        assert round_trip_message[0].content == "Hello, world!"

    def test_response_message_round_trip_nodes(self):
        """Test round-trip conversion of response message nodes."""
        raw_response_node = ChatResultNode(
            id=1,
            type=ChatResultNodeType.RAW_RESPONSE,
            content="Hello, world!",
            tool_use=None,
        )
        tool_use_node = ChatResultNode(
            id=2,
            type=ChatResultNodeType.TOOL_USE,
            content="Tool use content",
            tool_use=ChatResultToolUse(
                tool_use_id="tool-123",
                name="my-tool",
                input={"param": "value"},
            ),
        )
        original_message: ResponseMessage = [raw_response_node, tool_use_node]
        proto_nodes = response_message_to_proto(original_message)
        round_trip_message = response_to_message("", proto_nodes)
        assert round_trip_message == original_message


class TestResponseMessageToProto:
    """Tests for response_message_to_proto function."""

    def test_response_message_to_proto_with_string(self):
        """Test response_message_to_proto with a string message."""
        response_message = "Hello, world!"
        result = response_message_to_proto(response_message)
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].type == chat_pb2.ChatResultNodeType.RAW_RESPONSE

    def test_response_message_to_proto_with_nodes(self):
        """Test response_message_to_proto with a list of nodes."""
        raw_response_node = ChatResultNode(
            id=1,
            type=ChatResultNodeType.RAW_RESPONSE,
            content="Hello, world!",
            tool_use=None,
        )
        tool_use_start = ChatResultNode(
            id=2,
            type=ChatResultNodeType.TOOL_USE_START,
            content="Tool use start content",
            tool_use=ChatResultToolUse(
                tool_use_id="tool-123",
                name="my-tool",
                # Input not actually expected to be provided by the backed;
                # testing that the conversion strips it either way
                input={"param": "value"},
            ),
        )
        tool_use_node = ChatResultNode(
            id=3,
            type=ChatResultNodeType.TOOL_USE,
            content="Tool use content",
            tool_use=ChatResultToolUse(
                tool_use_id="tool-123",
                name="my-tool",
                input={"param": "value"},
            ),
        )
        suggested_questions_node = ChatResultNode(
            id=4,
            type=ChatResultNodeType.SUGGESTED_QUESTIONS,
            content="Suggested questions content",
            tool_use=None,
        )
        main_text_finished_node = ChatResultNode(
            id=5,
            type=ChatResultNodeType.MAIN_TEXT_FINISHED,
            content="Main text finished content",
            tool_use=None,
        )
        workspace_file_chunks_node = ChatResultNode(
            id=6,
            type=ChatResultNodeType.WORKSPACE_FILE_CHUNKS,
            content="Workspace file chunks content",
            tool_use=None,
        )
        # Final parameters should be stripped; it's not defined in the Chat RPC
        # or Public API layers
        final_parameters_node = ChatResultNode(
            id=8,
            type=ChatResultNodeType.FINAL_PARAMETERS,
            content="Final parameters content",
            tool_use=None,
        )
        relevant_sources_node = ChatResultNode(
            id=7,
            type=ChatResultNodeType.RELEVANT_SOURCES,
            content="Relevant sources content",
            tool_use=None,
        )
        original_message: ResponseMessage = [
            raw_response_node,
            tool_use_start,
            tool_use_node,
            suggested_questions_node,
            main_text_finished_node,
            workspace_file_chunks_node,
            relevant_sources_node,
            final_parameters_node,
        ]
        result = response_message_to_proto(original_message)
        assert isinstance(result, list)
        assert result[0] == chat_pb2.ChatResultNode(
            id=1,
            type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
            content="Hello, world!",
        )
        assert result[1] == chat_pb2.ChatResultNode(
            id=2,
            type=chat_pb2.ChatResultNodeType.TOOL_USE_START,
            content="Tool use start content",
            tool_use=chat_pb2.ChatResultToolUse(
                tool_use_id="tool-123",
                tool_name="my-tool",
                input_json="",
            ),
        )
        assert result[2] == chat_pb2.ChatResultNode(
            id=3,
            type=chat_pb2.ChatResultNodeType.TOOL_USE,
            content="Tool use content",
            tool_use=chat_pb2.ChatResultToolUse(
                tool_use_id="tool-123",
                tool_name="my-tool",
                input_json=json.dumps({"param": "value"}),
            ),
        )
        assert result[3] == chat_pb2.ChatResultNode(
            id=4,
            type=chat_pb2.ChatResultNodeType.SUGGESTED_QUESTIONS,
            content="Suggested questions content",
        )
        assert result[4] == chat_pb2.ChatResultNode(
            id=5,
            type=chat_pb2.ChatResultNodeType.MAIN_TEXT_FINISHED,
            content="Main text finished content",
        )
        assert result[5] == chat_pb2.ChatResultNode(
            id=6,
            type=chat_pb2.ChatResultNodeType.WORKSPACE_FILE_CHUNKS,
            content="Workspace file chunks content",
        )
        assert result[6] == chat_pb2.ChatResultNode(
            id=7,
            type=chat_pb2.ChatResultNodeType.RELEVANT_SOURCES,
            content="Relevant sources content",
        )
        # Again, Final parameters should be stripped
        assert len(result) == 7
