package orb

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/orbcorp/orb-go"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"
)

var (
	// Counter for tracking API operations
	orbAPIOperations = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_orb_api_ops",
			Help: "Number of Orb API operations performed",
		},
		[]string{"operation", "status"},
	)

	// Histogram for tracking API call latencies
	orbAPILatency = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "au_orb_api_latency",
			Help: "Latency of Orb API calls in seconds",
			Buckets: []float64{
				0.005, 0.010, 0.025, 0.050, 0.100, 0.250, 0.500, 1.000, 2.500, 5.000, 10.000,
			},
		},
		[]string{"operation", "status"},
	)

	// Counter for tracking rate limit events by category
	orbAPIRateLimitEvents = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_orb_api_rate_limit_events",
			Help: "Number of Orb API rate limit events",
		},
		[]string{"category"},
	)

	// Gauge for tracking remaining quota by category
	orbAPIRemainingQuota = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "au_orb_api_remaining_quota",
			Help: "Remaining API quota for Orb operations by category",
		},
		[]string{"category"},
	)
)

// MetricsInterceptor wraps an OrbClient to automatically collect metrics
type MetricsInterceptor struct {
	client OrbClient
}

// NewMetricsInterceptor creates a new OrbClient with automatic metrics collection
func NewMetricsInterceptor(client OrbClient) OrbClient {
	return &MetricsInterceptor{client: client}
}

// metricsWrapper wraps a function call with metrics collection
func (m *MetricsInterceptor) metricsWrapper(operation string, fn func() error) error {
	startTime := time.Now()
	err := fn()
	updateMetrics(operation, startTime, err)
	// Update quota from headers after the operation
	updateQuotaFromHeaders(m, operation)
	return err
}

// metricsWrapperWithResult wraps a function call that returns a result with metrics collection
func (m *MetricsInterceptor) metricsWrapperWithResult(operation string, fn func() (interface{}, error)) (interface{}, error) {
	startTime := time.Now()
	result, err := fn()
	updateMetrics(operation, startTime, err)
	// Update quota from headers after the operation
	updateQuotaFromHeaders(m, operation)
	return result, err
}

// extractError extracts a status string from an error
func extractError(err error) string {
	if err == nil {
		return "200 OK"
	}
	if orbErr, ok := err.(*orb.Error); ok {
		if orbErr.StatusCode != 0 {
			return fmt.Sprintf("%d", orbErr.StatusCode)
		}
	}
	return "unknown"
}

// categorizeOperation determines the category of an operation for rate limiting metrics
func categorizeOperation(operation string) string {
	// Credit/ledger operations
	if strings.Contains(operation, "credit") || strings.Contains(operation, "ledger") {
		return "credit_ledger"
	}

	// Write operations (create, update, cancel, etc.)
	if strings.HasPrefix(operation, "create") ||
		strings.HasPrefix(operation, "update") ||
		strings.HasPrefix(operation, "cancel") ||
		strings.HasPrefix(operation, "add") ||
		strings.HasPrefix(operation, "set") ||
		strings.HasPrefix(operation, "purchase") ||
		strings.HasPrefix(operation, "ingest") ||
		strings.HasPrefix(operation, "unschedule") {
		return "write"
	}

	// Everything else is a read operation
	return "read"
}

// updateMetrics records metrics for an API operation
func updateMetrics(operation string, startTime time.Time, err error) {
	duration := time.Since(startTime).Seconds()
	status := extractError(err)

	// Record operation count
	orbAPIOperations.WithLabelValues(operation, status).Inc()

	// Record latency
	orbAPILatency.WithLabelValues(operation, status).Observe(duration)

	category := categorizeOperation(operation)

	// Check for rate limit error
	if orbErr, ok := err.(*orb.Error); ok && orbErr.StatusCode == 429 {
		orbAPIRateLimitEvents.WithLabelValues(category).Inc()

		// Set remaining quota to 0 when we hit rate limit
		orbAPIRemainingQuota.WithLabelValues(category).Set(0)

		// Log rate limit error with details
		log.Warn().
			Str("operation", operation).
			Str("category", category).
			Int("status_code", orbErr.StatusCode).
			Msg("Orb API rate limit hit")
	}

	// Note: Rate limit quota updates are now handled by updateQuotaFromHeaders
	// which is called from the metrics wrapper functions that have access to the client
}

// updateQuotaFromHeaders updates the quota metrics using actual rate limit headers
func updateQuotaFromHeaders(client OrbClient, operation string) {
	// Try to get the underlying OrbClientImpl to access header capture
	if metricsClient, ok := client.(*MetricsInterceptor); ok {
		if orbClient, ok := metricsClient.client.(*OrbClientImpl); ok {
			category := categorizeOperation(operation)
			if rateLimitInfo := orbClient.headerCapture.GetRateLimit(category); rateLimitInfo != nil {
				// Update the quota metric with actual remaining quota from headers
				orbAPIRemainingQuota.WithLabelValues(category).Set(float64(rateLimitInfo.Remaining))
			}
		}
	}
}

func (m *MetricsInterceptor) CreateCustomer(ctx context.Context, customer OrbCustomer, usingStripe bool, idempotencyKey *string) (string, error) {
	result, err := m.metricsWrapperWithResult("create_customer", func() (interface{}, error) {
		return m.client.CreateCustomer(ctx, customer, usingStripe, idempotencyKey)
	})
	if err != nil {
		return "", err
	}
	return result.(string), nil
}

func (m *MetricsInterceptor) CreateSubscription(ctx context.Context, subscription OrbSubscription, idempotencyKey *string) (string, error) {
	result, err := m.metricsWrapperWithResult("create_subscription", func() (interface{}, error) {
		return m.client.CreateSubscription(ctx, subscription, idempotencyKey)
	})
	if err != nil {
		return "", err
	}
	return result.(string), nil
}

func (m *MetricsInterceptor) AddAlertsForCustomer(ctx context.Context, customerOrbID string, currency string) error {
	return m.metricsWrapper("add_alerts_for_customer", func() error {
		return m.client.AddAlertsForCustomer(ctx, customerOrbID, currency)
	})
}

func (m *MetricsInterceptor) AddCreditBalanceDepletedAlert(ctx context.Context, customerOrbID string, currency string, idempotencyKey *string) error {
	return m.metricsWrapper("add_credit_balance_depleted_alert", func() error {
		return m.client.AddCreditBalanceDepletedAlert(ctx, customerOrbID, currency, idempotencyKey)
	})
}

func (m *MetricsInterceptor) AddCreditBalanceRecoveredAlert(ctx context.Context, customerOrbID string, currency string, idempotencyKey *string) error {
	return m.metricsWrapper("add_credit_balance_recovered_alert", func() error {
		return m.client.AddCreditBalanceRecoveredAlert(ctx, customerOrbID, currency, idempotencyKey)
	})
}

func (m *MetricsInterceptor) SetCustomerPlanType(ctx context.Context, planChange OrbPlanChange, idempotencyKey *string) error {
	return m.metricsWrapper("set_customer_plan_type", func() error {
		return m.client.SetCustomerPlanType(ctx, planChange, idempotencyKey)
	})
}

func (m *MetricsInterceptor) IngestEvents(ctx context.Context, events []*OrbEvent) error {
	return m.metricsWrapper("ingest_events", func() error {
		return m.client.IngestEvents(ctx, events)
	})
}

func (m *MetricsInterceptor) GetUserSubscription(ctx context.Context, orbSubscriptionId string, itemIds *ItemIds) (*OrbSubscriptionInfo, error) {
	result, err := m.metricsWrapperWithResult("get_user_subscription", func() (interface{}, error) {
		return m.client.GetUserSubscription(ctx, orbSubscriptionId, itemIds)
	})
	if err != nil {
		return nil, err
	}
	return result.(*OrbSubscriptionInfo), nil
}

func (m *MetricsInterceptor) GetCustomerCreditBalance(ctx context.Context, customerOrbId string, currency string) (float64, error) {
	result, err := m.metricsWrapperWithResult("get_customer_credit_balance", func() (interface{}, error) {
		return m.client.GetCustomerCreditBalance(ctx, customerOrbId, currency)
	})
	if err != nil {
		return 0, err
	}
	return result.(float64), nil
}

func (m *MetricsInterceptor) PurchaseCredits(ctx context.Context, creditPurchase OrbCreditPurchase, idempotencyKey *string) error {
	return m.metricsWrapper("purchase_credits", func() error {
		return m.client.PurchaseCredits(ctx, creditPurchase, idempotencyKey)
	})
}

func (m *MetricsInterceptor) GrantFreeCredits(ctx context.Context, creditGrant OrbCreditGrant, idempotencyKey *string) error {
	return m.metricsWrapper("add_credits", func() error {
		return m.client.GrantFreeCredits(ctx, creditGrant, idempotencyKey)
	})
}

func (m *MetricsInterceptor) CancelOrbSubscription(ctx context.Context, orbSubscriptionId string, cancelTime PlanChangeType, cancelDate *time.Time, idempotencyKey *string) error {
	return m.metricsWrapper("cancel_orb_subscription", func() error {
		return m.client.CancelOrbSubscription(ctx, orbSubscriptionId, cancelTime, cancelDate, idempotencyKey)
	})
}

func (m *MetricsInterceptor) UpdateFixedQuantity(ctx context.Context, quantityUpdate OrbQuantityUpdate, idempotencyKey *string) error {
	return m.metricsWrapper("update_fixed_quantity", func() error {
		return m.client.UpdateFixedQuantity(ctx, quantityUpdate, idempotencyKey)
	})
}

func (m *MetricsInterceptor) VerifyWebhookSignature(r *http.Request) ([]byte, error) {
	// No metrics for webhook verification as it's not an API call
	return m.client.VerifyWebhookSignature(r)
}

func (m *MetricsInterceptor) GetCustomerCreditInfo(ctx context.Context, customerOrbId string, billingPeriodStartDate time.Time, currency string) (*CustomerCreditInfo, error) {
	result, err := m.metricsWrapperWithResult("get_customer_credit_info", func() (interface{}, error) {
		return m.client.GetCustomerCreditInfo(ctx, customerOrbId, billingPeriodStartDate, currency)
	})
	if err != nil {
		return nil, err
	}
	return result.(*CustomerCreditInfo), nil
}

func (m *MetricsInterceptor) GetPlanInformation(ctx context.Context, itemIDs ItemIds, orbSubscriptionID *string, externalPlanID *string) (*OrbPlanInfo, error) {
	result, err := m.metricsWrapperWithResult("get_plan_information", func() (interface{}, error) {
		return m.client.GetPlanInformation(ctx, itemIDs, orbSubscriptionID, externalPlanID)
	})
	if err != nil {
		return nil, err
	}
	return result.(*OrbPlanInfo), nil
}

func (m *MetricsInterceptor) UnschedulePendingSubscriptionCancellation(ctx context.Context, orbSubscriptionId string, idempotencyKey *string) error {
	return m.metricsWrapper("unschedule_pending_subscription_cancellation", func() error {
		return m.client.UnschedulePendingSubscriptionCancellation(ctx, orbSubscriptionId, idempotencyKey)
	})
}

func (m *MetricsInterceptor) UnscheduleFixedQuantity(ctx context.Context, orbSubscriptionID string, priceID string, idempotencyKey *string) error {
	return m.metricsWrapper("unschedule_fixed_quantity", func() error {
		return m.client.UnscheduleFixedQuantity(ctx, orbSubscriptionID, priceID, idempotencyKey)
	})
}

func (m *MetricsInterceptor) GetFailedPaymentInfo(ctx context.Context, orbSubscriptionId string) (*FailedPaymentInfo, error) {
	result, err := m.metricsWrapperWithResult("get_failed_payment_info", func() (interface{}, error) {
		return m.client.GetFailedPaymentInfo(ctx, orbSubscriptionId)
	})
	if err != nil {
		return nil, err
	}
	return result.(*FailedPaymentInfo), nil
}

func (m *MetricsInterceptor) GetInvoice(ctx context.Context, invoiceId string) (*OrbInvoice, error) {
	result, err := m.metricsWrapperWithResult("get_invoice", func() (interface{}, error) {
		return m.client.GetInvoice(ctx, invoiceId)
	})
	if err != nil {
		return nil, err
	}
	return result.(*OrbInvoice), nil
}

func (m *MetricsInterceptor) UpdateCustomerBillingAddress(ctx context.Context, customerOrbID string, address *Address) error {
	return m.metricsWrapper("update_customer_billing_address", func() error {
		return m.client.UpdateCustomerBillingAddress(ctx, customerOrbID, address)
	})
}

func (m *MetricsInterceptor) UpdateCustomerMetadata(ctx context.Context, customerOrbID string, metadata map[string]string) error {
	return m.metricsWrapper("update_customer_metadata", func() error {
		return m.client.UpdateCustomerMetadata(ctx, customerOrbID, metadata)
	})
}

func (m *MetricsInterceptor) GetScheduledPlanChanges(ctx context.Context, orbSubscriptionId string, orbCustomerId string) (*string, error) {
	result, err := m.metricsWrapperWithResult("get_scheduled_plan_changes", func() (interface{}, error) {
		return m.client.GetScheduledPlanChanges(ctx, orbSubscriptionId, orbCustomerId)
	})
	if err != nil {
		return nil, err
	}
	return result.(*string), nil
}

func (m *MetricsInterceptor) UnschedulePlanChanges(ctx context.Context, orbSubscriptionId string, idempotencyKey *string) error {
	return m.metricsWrapper("unschedule_plan_changes", func() error {
		return m.client.UnschedulePlanChanges(ctx, orbSubscriptionId, idempotencyKey)
	})
}

func (m *MetricsInterceptor) GetCustomerInfo(ctx context.Context, orbCustomerId string) (*OrbCustomerInfo, error) {
	result, err := m.metricsWrapperWithResult("get_customer_info", func() (interface{}, error) {
		return m.client.GetCustomerInfo(ctx, orbCustomerId)
	})
	if err != nil {
		return nil, err
	}
	return result.(*OrbCustomerInfo), nil
}

func (m *MetricsInterceptor) ListFailedInvoices(ctx context.Context, maxInvoices int, minInvoiceAmount string) ([]*OrbInvoice, error) {
	result, err := m.metricsWrapperWithResult("list_failed_invoices", func() (interface{}, error) {
		return m.client.ListFailedInvoices(ctx, maxInvoices, minInvoiceAmount)
	})
	if err != nil {
		return nil, err
	}
	return result.([]*OrbInvoice), nil
}
