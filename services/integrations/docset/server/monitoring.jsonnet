local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  local errorSpec = {
    displayName: 'Missing Docset IDs',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'info' },
      query: |||
        sum by (cluster, namespace)(increase(au_docset_missing_count[10m])) > 1
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      errorSpec,
      'docset-missing-ids',
      'Docset IDs are missing',
      team='extended-context',
    ),
  ]
