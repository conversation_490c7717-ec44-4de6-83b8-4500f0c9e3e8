package main

import (
	"errors"
)

type DocsetVersion struct {
	// category
	Category string `json:"category"`

	// id of the docset
	// e.g. "docset://python~3.11"
	// note that the docset id might still be pointing to a different version
	DocsetId string `json:"docset_id"`

	// version id of the docset
	// e.g. "python-12345678-abcd-abcd-abcd-abcdabcdabcd"
	// the docset version is	unique for each docset version.
	// It also denotes the path to the files
	DocsetVersionId string `json:"docset_version_id"`

	// a sha256 checksum of the index.json
	ChecksumSha256 string `json:"checksum"`

	// A human readable name of the docset version
	Name string `json:"name"`

	// A human readable description of the docset version
	Title string `json:"title"`

	Keywords []string `json:"keywords"`
}

func ValidateDocsetVersion(docsetVersion DocsetVersion) error {
	if docsetVersion.Name == "" {
		return errors.New("docset_name is required")
	}
	if docsetVersion.DocsetId == "" {
		return errors.New("docset_id is required")
	}
	if docsetVersion.DocsetVersionId == "" {
		return errors.New("docset_version is required")
	}
	if docsetVersion.ChecksumSha256 == "" {
		return errors.New("checksum is required")
	}
	return nil
}
