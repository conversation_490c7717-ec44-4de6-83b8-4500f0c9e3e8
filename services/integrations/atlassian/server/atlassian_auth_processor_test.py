"""Tests for Atlassian Auth Processor"""

import pytest
from unittest.mock import Mock, patch, ANY
from pydantic import SecretS<PERSON>

from services.integrations.atlassian.server.atlassian_auth_processor import (
    AtlassianAuthProcessor,
    AtlassianAuthError,
)
from services.settings import settings_pb2
from services.integrations.atlassian import atlassian_pb2
from services.lib.request_context.request_context import RequestContext


@pytest.fixture
def mock_settings_client():
    client = Mock()
    # Create a proper settings response
    settings = settings_pb2.UserSettings(
        atlassian_user_settings=settings_pb2.AtlassianUserSettings()
    )
    settings_response = settings_pb2.GetUserSettingsResponse(
        settings=settings, version="test-version-1"
    )
    client.get_user_settings.return_value = settings_response
    return client


@pytest.fixture
def mock_request_context():
    context = Mock(spec=RequestContext)
    return context


@pytest.fixture
def atlassian_auth_processor(mock_settings_client):
    with patch("pathlib.Path.read_text") as mock_read_text:
        mock_read_text.return_value = (
            "test_secret\n"  # Simulate file content with newline
        )
        return AtlassianAuthProcessor(
            mock_settings_client,
            "https://example.com/atlassianCallback",
            "test_client_secret_path",
            "test_client_id_path",
        )


def test_hydrate_jira_user_settings(
    atlassian_auth_processor, mock_settings_client, mock_request_context
):
    """Test successful Jira OAuth flow."""
    # Mock the token exchange response
    with patch("requests.post") as mock_post:
        mock_post.return_value = Mock()
        mock_post.return_value.json.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "scope": "read:jira-user read:jira-work",
        }
        mock_post.return_value.raise_for_status = Mock()

        # Mock the cloud ID response
        with patch("requests.get") as mock_get:
            mock_get.return_value = Mock()
            mock_get.return_value.json.return_value = [
                {"id": "test-cloud-id", "name": "Test Cloud"}
            ]
            mock_get.return_value.raise_for_status = Mock()

            response = atlassian_auth_processor.hydrate_atlassian_user_settings(
                "test_code",
                mock_request_context,
            )

            # Verify the response
            assert response.atlassian_service == atlassian_pb2.AtlassianService.JIRA

            # Verify settings were updated correctly
            mock_settings_client.update_user_settings.assert_called_once()
            call_kwargs = mock_settings_client.update_user_settings.call_args[1]
            assert call_kwargs["request_context"] == mock_request_context
            update_request = call_kwargs["request"]
            assert update_request.expected_version == "test-version-1"
            assert (
                update_request.settings.atlassian_user_settings.jira_settings.access_token
                == "test_access_token"
            )
            assert (
                update_request.settings.atlassian_user_settings.jira_settings.refresh_token
                == "test_refresh_token"
            )
            assert (
                update_request.settings.atlassian_user_settings.jira_settings.cloud_id
                == "test-cloud-id"
            )


def test_hydrate_confluence_user_settings(
    atlassian_auth_processor, mock_settings_client, mock_request_context
):
    """Test successful Confluence OAuth flow."""
    # Mock the token exchange response
    with patch("requests.post") as mock_post:
        mock_post.return_value = Mock()
        mock_post.return_value.json.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "scope": "read:confluence-space.summary read:confluence-content.all",
        }
        mock_post.return_value.raise_for_status = Mock()

        # Mock the cloud ID response
        with patch("requests.get") as mock_get:
            mock_get.return_value = Mock()
            mock_get.return_value.json.return_value = [
                {"id": "test-cloud-id", "name": "Test Cloud"}
            ]
            mock_get.return_value.raise_for_status = Mock()

            response = atlassian_auth_processor.hydrate_atlassian_user_settings(
                "test_code",
                mock_request_context,
            )

            # Verify the response
            assert (
                response.atlassian_service == atlassian_pb2.AtlassianService.CONFLUENCE
            )

            # Verify settings were updated correctly
            mock_settings_client.update_user_settings.assert_called_once()
            call_kwargs = mock_settings_client.update_user_settings.call_args[1]
            assert call_kwargs["request_context"] == mock_request_context
            update_request = call_kwargs["request"]
            assert update_request.expected_version == "test-version-1"
            assert (
                update_request.settings.atlassian_user_settings.confluence_settings.access_token
                == "test_access_token"
            )
            assert (
                update_request.settings.atlassian_user_settings.confluence_settings.refresh_token
                == "test_refresh_token"
            )
            assert (
                update_request.settings.atlassian_user_settings.confluence_settings.cloud_id
                == "test-cloud-id"
            )


def test_hydrate_settings_no_cloud_id(
    atlassian_auth_processor, mock_settings_client, mock_request_context
):
    """Test error handling when no cloud ID is available."""
    with patch("requests.post") as mock_post:
        mock_post.return_value = Mock()
        mock_post.return_value.json.return_value = {
            "access_token": "test_access_token",
            "scope": "read:jira-user",
        }
        mock_post.return_value.raise_for_status = Mock()

        with patch("requests.get") as mock_get:
            mock_get.return_value = Mock()
            mock_get.return_value.json.return_value = []  # Empty resources list
            mock_get.return_value.raise_for_status = Mock()

            with pytest.raises(AtlassianAuthError) as exc_info:
                atlassian_auth_processor.hydrate_atlassian_user_settings(
                    "test_code",
                    mock_request_context,
                )

            assert "No accessible Atlassian resources found" in str(exc_info.value)


def test_hydrate_settings_invalid_code(
    atlassian_auth_processor, mock_settings_client, mock_request_context
):
    """Test error handling with invalid authorization code."""
    with patch("requests.post") as mock_post:
        mock_post.return_value = Mock()
        mock_post.return_value.raise_for_status.side_effect = Exception("Invalid code")

        with pytest.raises(AtlassianAuthError):
            atlassian_auth_processor.hydrate_atlassian_user_settings(
                "invalid_code",
                mock_request_context,
            )


def test_validate_cloud_id_confluence_success(atlassian_auth_processor):
    """Test successful Confluence cloud ID validation."""
    with patch("requests.get") as mock_get:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"accountId": "test_account_id"}
        mock_response.raise_for_status = Mock()
        mock_get.return_value = mock_response

        result = atlassian_auth_processor._validate_cloud_id(
            "test_token", "test_cloud_id", atlassian_pb2.AtlassianService.CONFLUENCE
        )

        assert result is True
        mock_get.assert_called_once_with(
            "https://api.atlassian.com/ex/confluence/test_cloud_id/rest/api/user/current",
            headers={
                "Authorization": "Bearer test_token",
                "Accept": "application/json",
            },
            timeout=60,
        )


def test_validate_cloud_id_jira_success(atlassian_auth_processor):
    """Test successful Jira cloud ID validation."""
    with patch("requests.get") as mock_get:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"accountId": "test_account_id"}
        mock_response.raise_for_status = Mock()
        mock_get.return_value = mock_response

        result = atlassian_auth_processor._validate_cloud_id(
            "test_token", "test_cloud_id", atlassian_pb2.AtlassianService.JIRA
        )

        assert result is True
        mock_get.assert_called_once_with(
            "https://api.atlassian.com/ex/jira/test_cloud_id/rest/api/3/myself",
            headers={
                "Authorization": "Bearer test_token",
                "Accept": "application/json",
            },
            timeout=60,
        )


def test_validate_cloud_id_auth_error_401(atlassian_auth_processor):
    """Test cloud ID validation with 401 authentication error."""
    with patch("requests.get") as mock_get:
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.json.return_value = {"error": "Unauthorized"}
        mock_response.raise_for_status = (
            Mock()
        )  # Don't raise here, let _fetch_account_id handle it
        mock_get.return_value = mock_response

        result = atlassian_auth_processor._validate_cloud_id(
            "test_token", "test_cloud_id", atlassian_pb2.AtlassianService.JIRA
        )

        assert result is False


def test_validate_cloud_id_temporary_error_404(atlassian_auth_processor):
    """Test cloud ID validation with 404 (treated as temporary error)."""
    with patch("requests.get") as mock_get:
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.raise_for_status = (
            Mock()
        )  # Don't raise here, let _fetch_account_id handle it
        mock_get.return_value = mock_response

        result = atlassian_auth_processor._validate_cloud_id(
            "test_token", "test_cloud_id", atlassian_pb2.AtlassianService.CONFLUENCE
        )

        # 404 is treated as temporary error, so validation returns True
        assert result is True


def test_validate_cloud_id_network_error(atlassian_auth_processor):
    """Test cloud ID validation with network error (treated as temporary)."""
    with patch("requests.get") as mock_get:
        mock_get.side_effect = Exception("Network error")

        result = atlassian_auth_processor._validate_cloud_id(
            "test_token", "test_cloud_id", atlassian_pb2.AtlassianService.JIRA
        )

        # Network errors are treated as temporary, so validation returns True
        assert result is True


def test_select_best_cloud_id_prefers_non_deleted(atlassian_auth_processor):
    """Test that non-deleted cloud IDs are preferred."""
    with patch.object(atlassian_auth_processor, "_validate_cloud_id") as mock_validate:
        mock_validate.return_value = True

        resources = [
            {"id": "deleted_cloud", "name": "Deleted Org"},
            {"id": "active_cloud", "name": "Active Org"},
        ]
        deleted_cloud_ids = ["deleted_cloud"]

        cloud_id, selected_from_deleted = (
            atlassian_auth_processor._select_best_cloud_id(
                "test_token",
                resources,
                atlassian_pb2.AtlassianService.CONFLUENCE,
                deleted_cloud_ids,
            )
        )

        assert cloud_id == "active_cloud"
        assert selected_from_deleted is False


def test_select_best_cloud_id_prefers_different_from_current(atlassian_auth_processor):
    """Test that a different cloud ID is preferred when current is provided."""
    with patch.object(atlassian_auth_processor, "_validate_cloud_id") as mock_validate:
        mock_validate.return_value = True

        resources = [
            {"id": "current_cloud", "name": "Current Org"},
            {"id": "other_cloud", "name": "Other Org"},
        ]
        deleted_cloud_ids = []
        current_cloud_id = "current_cloud"

        cloud_id, selected_from_deleted = (
            atlassian_auth_processor._select_best_cloud_id(
                "test_token",
                resources,
                atlassian_pb2.AtlassianService.JIRA,
                deleted_cloud_ids,
                current_cloud_id,
            )
        )

        assert cloud_id == "other_cloud"
        assert selected_from_deleted is False


def test_select_best_cloud_id_fallback_to_deleted(atlassian_auth_processor):
    """Test fallback to deleted cloud IDs when no non-deleted ones are valid."""
    with patch.object(atlassian_auth_processor, "_validate_cloud_id") as mock_validate:
        mock_validate.return_value = True

        resources = [
            {"id": "deleted_cloud_1", "name": "Deleted Org 1"},
            {"id": "deleted_cloud_2", "name": "Deleted Org 2"},
        ]
        deleted_cloud_ids = ["deleted_cloud_1", "deleted_cloud_2"]

        cloud_id, selected_from_deleted = (
            atlassian_auth_processor._select_best_cloud_id(
                "test_token",
                resources,
                atlassian_pb2.AtlassianService.CONFLUENCE,
                deleted_cloud_ids,
            )
        )

        assert cloud_id == "deleted_cloud_1"
        assert selected_from_deleted is True


def test_hydrate_removes_deleted_cloud_id_when_selected(
    atlassian_auth_processor, mock_settings_client, mock_request_context
):
    """Test that selecting a deleted cloud ID removes it from the deleted list."""
    # Set up existing settings with deleted cloud IDs
    settings = settings_pb2.UserSettings(
        atlassian_user_settings=settings_pb2.AtlassianUserSettings(
            confluence_settings=settings_pb2.AtlassianOAuthSettings(
                cloud_id="old_cloud_id"
            ),
            deleted_cloud_ids=["deleted_cloud_1", "deleted_cloud_2"],
        )
    )
    settings_response = settings_pb2.GetUserSettingsResponse(
        settings=settings, version="test-version-1"
    )
    mock_settings_client.get_user_settings.return_value = settings_response

    # Mock the OAuth token exchange
    with patch("requests.post") as mock_post:
        mock_response = Mock()
        mock_response.json.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "scope": "read:confluence-content.all write:confluence-content",
        }
        mock_response.raise_for_status = Mock()
        mock_post.return_value = mock_response

        # Mock the accessible resources call to return deleted cloud IDs
        with patch("requests.get") as mock_get:
            mock_get_responses = [
                # First call: accessible resources
                Mock(
                    json=lambda: [
                        {"id": "deleted_cloud_1", "name": "Deleted Org 1"},
                        {"id": "deleted_cloud_2", "name": "Deleted Org 2"},
                    ],
                    raise_for_status=Mock(),
                ),
                # Second call: account ID fetch (successful)
                Mock(
                    status_code=200,
                    json=lambda: {"accountId": "test_account_id"},
                    raise_for_status=Mock(),
                ),
            ]
            mock_get.side_effect = mock_get_responses

            response = atlassian_auth_processor.hydrate_atlassian_user_settings(
                "test_code", mock_request_context
            )

            # Verify the response is successful
            assert (
                response.atlassian_service == atlassian_pb2.AtlassianService.CONFLUENCE
            )

            # Verify that the selected cloud ID was removed from deleted list
            mock_settings_client.update_user_settings.assert_called_once()
            call_kwargs = mock_settings_client.update_user_settings.call_args[1]
            update_request = call_kwargs["request"]

            # The selected cloud ID should be removed from deleted_cloud_ids
            remaining_deleted = list(
                update_request.settings.atlassian_user_settings.deleted_cloud_ids
            )
            selected_cloud_id = update_request.settings.atlassian_user_settings.confluence_settings.cloud_id

            # The selected cloud ID should not be in the deleted list anymore
            assert selected_cloud_id not in remaining_deleted
            # But the other deleted cloud ID should still be there
            assert len(remaining_deleted) == 1


def test_select_best_cloud_id_mixed_validity(atlassian_auth_processor):
    """Test selection when some cloud IDs are invalid for the service."""
    with patch.object(atlassian_auth_processor, "_validate_cloud_id") as mock_validate:
        # First cloud ID is invalid, second is valid
        mock_validate.side_effect = [False, True]

        resources = [
            {"id": "invalid_cloud", "name": "Invalid Org"},
            {"id": "valid_cloud", "name": "Valid Org"},
        ]
        deleted_cloud_ids = []

        cloud_id, _ = atlassian_auth_processor._select_best_cloud_id(
            "test_token",
            resources,
            atlassian_pb2.AtlassianService.JIRA,
            deleted_cloud_ids,
        )

        assert cloud_id == "valid_cloud"


def test_fetch_cloud_id_with_smart_selection(atlassian_auth_processor):
    """Test that _fetch_cloud_id uses smart selection when service type and deleted cloud IDs are provided."""
    with patch.object(atlassian_auth_processor, "_select_best_cloud_id") as mock_select:
        mock_select.return_value = ("selected_cloud_id", False)

        with patch("requests.get") as mock_get:
            mock_response = Mock()
            mock_response.json.return_value = [
                {"id": "cloud_1", "name": "Org 1"},
                {"id": "cloud_2", "name": "Org 2"},
            ]
            mock_response.raise_for_status = Mock()
            mock_get.return_value = mock_response

            cloud_id, selected_from_deleted = atlassian_auth_processor._fetch_cloud_id(
                "test_token",
                atlassian_pb2.AtlassianService.CONFLUENCE,
                ["deleted_cloud"],
                "current_cloud",
            )

            assert cloud_id == "selected_cloud_id"
            assert selected_from_deleted is False
            mock_select.assert_called_once_with(
                "test_token",
                [
                    {"id": "cloud_1", "name": "Org 1"},
                    {"id": "cloud_2", "name": "Org 2"},
                ],
                atlassian_pb2.AtlassianService.CONFLUENCE,
                ["deleted_cloud"],
                "current_cloud",
            )


def test_revoke_oauth_token_adds_to_deleted_list(
    atlassian_auth_processor, mock_settings_client, mock_request_context
):
    """Test that revoking a token adds the cloud ID to the deleted list."""
    # Set up existing settings with a cloud ID
    settings = settings_pb2.UserSettings(
        atlassian_user_settings=settings_pb2.AtlassianUserSettings(
            jira_settings=settings_pb2.AtlassianOAuthSettings(
                access_token="test_token", cloud_id="test_cloud_id"
            ),
            deleted_cloud_ids=[],
        )
    )
    settings_response = settings_pb2.GetUserSettingsResponse(
        settings=settings, version="test-version-1"
    )
    mock_settings_client.get_user_settings.return_value = settings_response

    request = atlassian_pb2.RevokeOAuthTokenRequest(
        atlassian_service=atlassian_pb2.AtlassianService.JIRA
    )

    response = atlassian_auth_processor.revoke_oauth_token(
        mock_request_context, request
    )

    # Verify the response is successful
    assert response.status.code == 0  # OK

    # Verify that the cloud ID was added to the deleted list
    mock_settings_client.update_user_settings.assert_called_once()
    call_kwargs = mock_settings_client.update_user_settings.call_args[1]
    update_request = call_kwargs["request"]

    # Check that the cloud ID was added to deleted_cloud_ids
    deleted_cloud_ids = list(
        update_request.settings.atlassian_user_settings.deleted_cloud_ids
    )
    assert "test_cloud_id" in deleted_cloud_ids

    # Check that the service settings were cleared
    assert (
        update_request.settings.atlassian_user_settings.jira_settings.access_token == ""
    )
    assert update_request.settings.atlassian_user_settings.jira_settings.cloud_id == ""
