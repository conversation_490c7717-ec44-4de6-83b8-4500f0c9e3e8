// @generated by protoc-gen-connect-es v1.4.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/integrations/notion/notion.proto (package notion, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { HydrateNotionSettingsRequest, HydrateNotionSettingsResponse, NotionGetToolsRequest, NotionGetToolsResponse, NotionReadPageRequest, NotionReadPageResponse, NotionSearchRequest, NotionSearchResponse } from "./notion_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service notion.Notion
 */
export declare const Notion: {
  readonly typeName: "notion.Notion",
  readonly methods: {
    /**
     * @generated from rpc notion.Notion.Search
     */
    readonly search: {
      readonly name: "Search",
      readonly I: typeof NotionSearchRequest,
      readonly O: typeof NotionSearchResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc notion.Notion.ReadPage
     */
    readonly readPage: {
      readonly name: "ReadPage",
      readonly I: typeof NotionReadPageRequest,
      readonly O: typeof NotionReadPageResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc notion.Notion.GetTools
     */
    readonly getTools: {
      readonly name: "GetTools",
      readonly I: typeof NotionGetToolsRequest,
      readonly O: typeof NotionGetToolsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc notion.Notion.HydrateNotionSettings
     */
    readonly hydrateNotionSettings: {
      readonly name: "HydrateNotionSettings",
      readonly I: typeof HydrateNotionSettingsRequest,
      readonly O: typeof HydrateNotionSettingsResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

