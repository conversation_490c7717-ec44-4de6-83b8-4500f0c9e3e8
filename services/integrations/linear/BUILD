load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:python.bzl", "py_grpc_library", "py_library", "py_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:typescript.bzl", "ts_proto_library")
load("@python_pip//:requirements.bzl", "requirement")

proto_library(
    name = "linear_proto",
    srcs = ["linear.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/agents:agents_proto",
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "linear_py_proto",
    protos = [":linear_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//services/agents:agents_py_proto",
        "//third_party/proto:googleapis_status_py_proto",
    ],
)

ts_proto_library(
    name = "linear_ts_proto",
    node_modules = "//:node_modules",
    proto = ":linear_proto",
    # consumed by services/customer/frontend
    visibility = ["//services:__subpackages__"],
)

py_library(
    name = "models",
    srcs = ["models.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        requirement("pydantic"),
    ],
)

py_test(
    name = "models_test",
    srcs = ["models_test.py"],
    deps = [
        ":models",
    ],
)

py_library(
    name = "gql_utils",
    srcs = ["gql_utils.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        requirement("gql"),
        requirement("requests_toolbelt"),
        requirement("websockets"),
        requirement("aiohttp"),
    ],
)

pytest_test(
    name = "gql_utils_test",
    srcs = ["gql_utils_test.py"],
    deps = [
        ":gql_utils",
    ],
)
