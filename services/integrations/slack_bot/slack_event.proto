syntax = "proto3";

package slackbot;

message EventMetadata {
  string team_id = 1;
  string enterprise_id = 2;

  string tenant_id = 3;
  string request_id = 4;
  string tenant_name = 5;
}

message AppMentionEvent {
  string text = 3 [debug_redact = true];
  string user = 4;
  string timestamp = 5;
  string thread_timestamp = 6;
  string channel = 7;
  string event_timestamp = 8;
  string user_team = 9;
  string source_team = 10;
}

message MessageEvent {
  string client_msg_id = 1;
  string user = 2;
  string text = 3 [debug_redact = true];
  string thread_timestamp = 4;
  string timestamp = 5;
  string channel = 6;
  string channel_type = 7;
  string event_timestamp = 8;

  string user_team = 9;
  string source_team = 10;

  // we don't handle edited or deleted messages yet
  string subtype = 11;

  // we also don't handle multimodal message (i.e. through file upload) yet
}

message ReactionAddedEvent {
  string user = 1;
  string item_user = 2;
  string event_timestamp = 3;
  string item_type = 4;
  string item_channel = 5;
  string reaction = 6;
  string item_timestamp = 7;
}

message MemberJoinedChannelEvent {
  string user = 1;
  string channel = 2;
  string event_timestamp = 3;
  string inviter = 4;
}

message AppUninstalledEvent {
  // Unix epoch seconds. https://api.slack.com/apis/events-api#callback-field
  int64 event_timestamp = 1;
  string app_id = 2;
}

message AppHomeOpenedEvent {
  string user = 1;
  string channel = 2;
  string event_timestamp = 3;
}

message SlackEvent {
  EventMetadata metadata = 1;
  string event_type = 2;
  oneof event {
    AppMentionEvent app_mention = 3;
    MessageEvent message = 4;
    ReactionAddedEvent reaction_added = 5;
    MemberJoinedChannelEvent member_joined_channel = 6;
    AppUninstalledEvent app_uninstalled = 7;
    AppHomeOpenedEvent app_home_opened = 8;
  }
}
