local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  local missingCheckpointSpec = {
    displayName: 'Slackbot Responses with Missing Checkpoints',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace, cluster)(increase(au_slack_bot_missing_checkpoint_count[15m])) > 0
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      missingCheckpointSpec,
      'slackbot-missing-checkpoints',
      'Slackbot chat responses are missing checkpoints',
      team='extended-context',
    ),
  ]
