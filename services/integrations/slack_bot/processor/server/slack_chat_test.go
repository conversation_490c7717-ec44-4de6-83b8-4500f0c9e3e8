package main

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"testing"
	"time"

	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/clock"
	chatproto "github.com/augmentcode/augment/services/chat_host/proto"
	gleanproto "github.com/augmentcode/augment/services/integrations/glean/proto"
	slackbotproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	slackboteventproto "github.com/augmentcode/augment/services/integrations/slack_bot/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/slack-go/slack"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/proto"
)

const (
	_BOT_USER_ID   = "U12345"
	_HUMAN_USER_ID = "U54321"
	_CHANNEL_NAME  = "channel-name"
)

func slackTimestamp(t time.Time) string {
	return fmt.Sprintf("%d.123456", t.Unix())
}

type slackChatHandlerTest struct {
	t *testing.T

	mockSlackClientFactory   *MockSlackClientFactory
	mockChatClient           *MockChatClient
	mockTenantCache          *MockTenantCache
	mockTokenExchangeClient  *MockTokenExchangeClient
	mockTenantLookup         *MockTenantLookup
	mockClock                clock.Clock
	mockWebhookTenantMapping *MockWebhookTenantMappingResource
	featureFlags             *featureflags.LocalFeatureFlagHandler
	mockSlackClient          *MockSlackClient
	mockSettingsService      *MockSettingsClient
	mockGleanClient          *MockGleanClient
	mockSlackUserMapping     *MockSlackUserMapping
	handler                  *SlackChatHandler
}

func newSlackChatHandlerTest(t *testing.T) *slackChatHandlerTest {
	mockSlackClientFactory := new(MockSlackClientFactory)
	mockChatClient := new(MockChatClient)
	mockTenantCache := new(MockTenantCache)
	mockTokenExchangeClient := new(MockTokenExchangeClient)
	mockTenantLookup := new(MockTenantLookup)
	mockClock := clock.NewMockClock(time.Now())
	mockWebhookTenantMapping := new(MockWebhookTenantMappingResource)
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()
	mockSlackClient := new(MockSlackClient)
	settingsService := new(MockSettingsClient)
	gleanClient := new(MockGleanClient)
	mockSlackUserMapping := new(MockSlackUserMapping)

	tenantID := "test-tenant-id"
	mockTenantCache.On("GetTenant", tenantID).Return(&tenantproto.Tenant{Id: tenantID}, nil)

	handler, err := NewSlackChatHandler(
		mockTenantCache,
		mockTokenExchangeClient,
		mockTenantLookup,
		mockSlackClientFactory,
		mockChatClient,
		ripublisher.NewRequestInsightPublisherMock(),
		settingsService,
		gleanClient,
		featureFlagHandler,
		mockClock,
		mockWebhookTenantMapping,
		mockSlackUserMapping,
		"/augment", // set slash command to be for prod by default
	)
	assert.NoError(t, err)

	return &slackChatHandlerTest{
		t:                        t,
		mockSlackClientFactory:   mockSlackClientFactory,
		mockChatClient:           mockChatClient,
		mockTenantCache:          mockTenantCache,
		mockTokenExchangeClient:  mockTokenExchangeClient,
		mockTenantLookup:         mockTenantLookup,
		mockClock:                mockClock,
		mockWebhookTenantMapping: mockWebhookTenantMapping,
		featureFlags:             featureFlagHandler,
		mockSlackClient:          mockSlackClient,
		mockSettingsService:      settingsService,
		mockGleanClient:          gleanClient,
		mockSlackUserMapping:     mockSlackUserMapping,
		handler:                  handler,
	}
}

func (st *slackChatHandlerTest) assertExpectations() {
	st.t.Helper()
	st.mockSlackClientFactory.AssertExpectations(st.t)
	st.mockChatClient.AssertExpectations(st.t)
	st.mockTenantCache.AssertExpectations(st.t)
	st.mockTokenExchangeClient.AssertExpectations(st.t)
	st.mockTenantLookup.AssertExpectations(st.t)
	st.mockWebhookTenantMapping.AssertExpectations(st.t)
	st.mockSlackClient.AssertExpectations(st.t)
	st.mockSettingsService.AssertExpectations(st.t)
	st.mockGleanClient.AssertExpectations(st.t)
	st.mockSlackUserMapping.AssertExpectations(st.t)
}

func (st *slackChatHandlerTest) setupGetSlackClient(tenantID string, isAssistantView bool, githubSettings *settingsproto.GithubSettings, gleanSettings *settingsproto.GleanTenantSettings) {
	st.mockTokenExchangeClient.On("GetSignedTokenForService", mock.Anything, mock.Anything, mock.Anything).Once().Return("test-token", nil)
	st.mockSettingsService.On("GetTenantSettings", mock.Anything, mock.Anything).Once().Return(&settingsproto.GetTenantSettingsResponse{
		Settings: &settingsproto.TenantSettings{
			SlackSettings: &settingsproto.SlackSettings{
				AssistantFlowEnabled: isAssistantView,
			},
			GithubSettings:      githubSettings,
			GleanTenantSettings: gleanSettings,
		},
	}, nil)
	st.mockSlackClientFactory.On("GetSlackClient", mock.Anything, mock.Anything, mock.Anything).Once().Return(st.mockSlackClient, nil)
}

func (st *slackChatHandlerTest) setupMockSlackClientFactory(requestId requestcontext.RequestId, tenantID string, messageTs string, threadMessages []slack.Message, channelIsIM bool, channelIsShared bool, isAssistantView bool, gleanSettings *settingsproto.GleanTenantSettings) {
	// returns a new mock slack client factory
	// inside it, the returned slack client expects and mocks all expected calls for
	// HandleEvent, namely:
	// 1. an in-progress reaction is added to the message to ack it
	// 2. the existing replies are fetched
	// 3. a new message is posted
	// 4. the existing message is updated as more chunks are streamed
	// 5. the in-progress reaction is removed
	threadTs := "1234567890.123456"
	st.setupGetSlackClient(tenantID, isAssistantView, nil, gleanSettings)
	channelName := ""
	if !channelIsIM {
		channelName = _CHANNEL_NAME
	}
	st.mockSlackClient.On("GetConversationInfo", &slack.GetConversationInfoInput{ChannelID: "C12345"}).Return(&slack.Channel{GroupConversation: slack.GroupConversation{Conversation: slack.Conversation{IsIM: channelIsIM, IsExtShared: channelIsShared}, Name: channelName}}, nil)

	if !isAssistantView {
		st.mockSlackClient.On("AddReaction", IN_PROGRESS_EMOJI, slack.NewRefToMessage("C12345", messageTs)).Return(nil)
		st.mockSlackClient.On("RemoveReaction", IN_PROGRESS_EMOJI, slack.NewRefToMessage("C12345", messageTs)).Return(nil)
	}

	st.mockSlackClient.On("GetConversationReplies", &slack.GetConversationRepliesParameters{
		ChannelID: "C12345",
		Timestamp: threadTs,
		Cursor:    "",
	}).Return(threadMessages, false, "", nil)

	// Only set up message posting expectations if we're not testing an error case
	if !strings.Contains(requestId.String(), "error") {
		st.mockSlackClient.On("PostMessage", "C12345", mock.AnythingOfType("[]slack.MsgOption")).Return("C12345", "987654321.098765", nil)
		st.mockSlackClient.On("UpdateMessage", "C12345", "987654321.098765", mock.AnythingOfType("[]slack.MsgOption")).Return("C12345", "987654321.098765", "new text", nil)
	}

	st.mockSlackClient.On("BotUserId").Return(_BOT_USER_ID)
	st.mockSlackClient.On("GetUserProfile", mock.AnythingOfType("*slack.GetUserProfileParameters")).Return(&slack.UserProfile{Email: "<EMAIL>"}, nil)
}

func (st *slackChatHandlerTest) setupChatMocks(tenantID string, threadMessages []slack.Message, isShared bool) {
	if !isShared {
		st.mockTenantLookup.On("GetRepos", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*slackbotproto.Repo{{RepoName: "test-repo", RepoOwner: "test-owner"}}, []*blobsproto.Blobs{{BaselineCheckpointId: proto.String("test-checkpoint"), Added: [][]byte{[]byte("test-blob")}, Deleted: nil}}, []*settingsproto.RepoInformation{{RepoOwner: "test-owner", RepoName: "test-repo"}}, int64(123), nil)
	}
	st.mockSlackClient.On("GetConversationReplies", mock.Anything).Return(threadMessages, false, "", nil)
	st.mockSlackClient.On("UpdateMessage", mock.Anything, mock.Anything, mock.Anything).Return("C12345", "987654321.098765", "new text", nil)
}

func createTestSlackEvent(eventType, text, timestamp, user, subtype string, args ...interface{}) ([]byte, error) {
	threadTimestamp := "1234567890.123456"
	tenantID := "test-tenant-id"
	requestId := requestcontext.RequestId("test-request-id")

	event := &slackboteventproto.SlackEvent{
		Metadata: &slackboteventproto.EventMetadata{
			RequestId:  requestId.String(),
			TenantId:   tenantID,
			TenantName: "test-tenant-name",
		},
		EventType: eventType,
	}

	switch eventType {
	case "message":
		event.Event = &slackboteventproto.SlackEvent_Message{
			Message: &slackboteventproto.MessageEvent{
				Text:            text,
				Channel:         "C12345",
				ThreadTimestamp: threadTimestamp,
				Timestamp:       timestamp,
				ChannelType:     "im",
				User:            user,
				Subtype:         subtype,
			},
		}
	case "app_mention":
		event.Event = &slackboteventproto.SlackEvent_AppMention{
			AppMention: &slackboteventproto.AppMentionEvent{
				Text:            text,
				Channel:         "C12345",
				Timestamp:       timestamp,
				ThreadTimestamp: threadTimestamp,
				User:            user,
			},
		}
	case "reaction_added":
		event.Event = &slackboteventproto.SlackEvent_ReactionAdded{
			ReactionAdded: &slackboteventproto.ReactionAddedEvent{
				Reaction:       args[0].(string),
				ItemUser:       _BOT_USER_ID,
				ItemChannel:    "C12345",
				ItemTimestamp:  threadTimestamp,
				EventTimestamp: timestamp,
				User:           user,
			},
		}
	case "app_home_opened":
		event.Event = &slackboteventproto.SlackEvent_AppHomeOpened{
			AppHomeOpened: &slackboteventproto.AppHomeOpenedEvent{
				User:           user,
				Channel:        "C12345",
				EventTimestamp: timestamp,
			},
		}
	case "member_joined_channel":
		event.Event = &slackboteventproto.SlackEvent_MemberJoinedChannel{
			MemberJoinedChannel: &slackboteventproto.MemberJoinedChannelEvent{
				Channel: "C12345",
				User:    _BOT_USER_ID,
				Inviter: user,
			},
		}
	}

	return proto.Marshal(event)
}

func TestHandleEvent_AppMention(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	requestId := requestcontext.RequestId("test-request-id")
	ctx := context.Background()
	userMessage := fmt.Sprintf("Hello, bot! @<%s>", _BOT_USER_ID)

	eventData, err := createTestSlackEvent("app_mention", userMessage, slackTimestamp(st.mockClock.Now()), "", "")
	assert.NoError(t, err)

	threadMessages := []slack.Message{{Msg: slack.Msg{Text: userMessage, User: _HUMAN_USER_ID}}}
	st.setupMockSlackClientFactory(requestId, tenantID, slackTimestamp(st.mockClock.Now()), threadMessages, false, false, false, nil)
	st.setupChatMocks(tenantID, threadMessages, false)

	chatResponseCh := make(chan ChatResponse)
	st.mockChatClient.On("Chat", mock.Anything, tenantID, mock.Anything, mock.Anything, []string(nil), fmt.Sprintf("%s: %s", _HUMAN_USER_ID, userMessage), _BOT_USER_ID, "C12345", _CHANNEL_NAME, false, []*gleanproto.Document(nil)).Return((<-chan ChatResponse)(chatResponseCh), nil)

	go func() {
		chatResponseCh <- ChatResponse{Resp: chatproto.ChatResponse{Text: "Hello, human!"}}
		close(chatResponseCh)
	}()

	err = st.handler.HandleEvent(ctx, eventData)

	assert.NoError(t, err)
}

func TestHandleEvent_DirectMessage(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	requestId := requestcontext.RequestId("test-request-id")
	ctx := context.Background()

	userMessage := "Hello, bot!"
	eventData, err := createTestSlackEvent("app_mention", userMessage, slackTimestamp(st.mockClock.Now()), "", "")
	assert.NoError(t, err)

	threadMessages := []slack.Message{{Msg: slack.Msg{Text: userMessage, User: _HUMAN_USER_ID}}}
	st.setupMockSlackClientFactory(requestId, tenantID, slackTimestamp(st.mockClock.Now()), threadMessages, true, false, false, nil)
	st.setupChatMocks(tenantID, threadMessages, false)

	chatResponseCh := make(chan ChatResponse)
	st.mockChatClient.On("Chat", mock.Anything, tenantID, mock.Anything, mock.Anything, []string(nil), fmt.Sprintf("%s: Hello, bot!", _HUMAN_USER_ID), _BOT_USER_ID, "C12345", "", false, []*gleanproto.Document(nil)).Return((<-chan ChatResponse)(chatResponseCh), nil)

	go func() {
		chatResponseCh <- ChatResponse{Resp: chatproto.ChatResponse{Text: "Hello, human!"}}
		close(chatResponseCh)
	}()

	err = st.handler.HandleEvent(ctx, eventData)

	assert.NoError(t, err)
}

func TestHandleEvent_DirectMessage_AssistantView(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	requestId := requestcontext.RequestId("test-request-id")
	ctx := context.Background()

	userMessage := "Hello, bot!"
	eventData, err := createTestSlackEvent("app_mention", userMessage, slackTimestamp(st.mockClock.Now()), "", "")
	assert.NoError(t, err)

	threadMessages := []slack.Message{{Msg: slack.Msg{Text: userMessage, User: _HUMAN_USER_ID}}}
	st.setupMockSlackClientFactory(requestId, tenantID, slackTimestamp(st.mockClock.Now()), threadMessages, true, false, true, nil)
	st.setupChatMocks(tenantID, threadMessages, false)

	chatResponseCh := make(chan ChatResponse)
	st.mockChatClient.On("Chat", mock.Anything, tenantID, mock.Anything, mock.Anything, []string(nil), fmt.Sprintf("%s: Hello, bot!", _HUMAN_USER_ID), _BOT_USER_ID, "C12345", "", false, []*gleanproto.Document(nil)).Return((<-chan ChatResponse)(chatResponseCh), nil)

	go func() {
		chatResponseCh <- ChatResponse{Resp: chatproto.ChatResponse{Text: "Hello, human!"}}
		close(chatResponseCh)
	}()

	err = st.handler.HandleEvent(ctx, eventData)

	assert.NoError(t, err)
}

func TestHandleChatError_DirectMessage(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	requestId := requestcontext.RequestId("test-request-id-error") // Changed to include "error"
	ctx := context.Background()

	userMessage := "Hello, bot!"
	eventData, err := createTestSlackEvent("app_mention", userMessage, slackTimestamp(st.mockClock.Now()), "", "")
	assert.NoError(t, err)

	threadMessages := []slack.Message{{Msg: slack.Msg{Text: userMessage, User: _HUMAN_USER_ID}}}
	st.setupMockSlackClientFactory(requestId, tenantID, slackTimestamp(st.mockClock.Now()), threadMessages, true, false, false, nil)
	st.mockTenantLookup.On("GetRepos", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*slackbotproto.Repo{{RepoName: "test-repo", RepoOwner: "test-owner"}}, []*blobsproto.Blobs{{BaselineCheckpointId: proto.String("test-checkpoint"), Added: [][]byte{[]byte("test-blob")}, Deleted: nil}}, []*settingsproto.RepoInformation{{RepoOwner: "test-owner", RepoName: "test-repo"}}, int64(123), nil)

	expectedErr := fmt.Errorf("test error")
	chatResponseCh := make(chan ChatResponse)
	st.mockChatClient.On("Chat", mock.Anything, tenantID, mock.Anything, mock.Anything, []string(nil), fmt.Sprintf("%s: Hello, bot!", _HUMAN_USER_ID), _BOT_USER_ID, "C12345", "", false, []*gleanproto.Document(nil)).Return((<-chan ChatResponse)(chatResponseCh), nil)

	go func() {
		chatResponseCh <- ChatResponse{Error: expectedErr}
		close(chatResponseCh)
	}()

	err = st.handler.HandleEvent(ctx, eventData)
	assert.Equal(t, expectedErr.Error(), err.Error())
}

func TestSkipOwnEvent(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	ctx := context.Background()

	eventData, err := createTestSlackEvent("message", "Hello, I'm the bot!", slackTimestamp(st.mockClock.Now()), _BOT_USER_ID, "message_replied")
	assert.NoError(t, err)

	st.setupGetSlackClient(tenantID, false, nil, nil)
	st.mockSlackClient.On("BotUserId").Return(_BOT_USER_ID)

	err = st.handler.HandleEvent(ctx, eventData)

	assert.NoError(t, err)
	st.mockChatClient.AssertNotCalled(t, "Chat") // ensure that we exited early and didn't call chat
}

func TestSkipOwnMention(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	ctx := context.Background()

	eventData, err := createTestSlackEvent("app_mention", "Hey, this is me talking about @bot.", slackTimestamp(st.mockClock.Now()), _BOT_USER_ID, "")
	assert.NoError(t, err)

	st.setupGetSlackClient(tenantID, false, nil, nil)
	st.mockSlackClient.On("BotUserId").Return(_BOT_USER_ID)
	err = st.handler.HandleEvent(ctx, eventData)

	st.mockChatClient.AssertNotCalled(t, "Chat") // ensure that we exited early and didn't call chat
}

func TestHandleDeleteReact(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	ctx := context.Background()

	eventData, err := createTestSlackEvent("reaction_added", "", "2345678900.234567", "", "", DELETE_MESSAGE_EMOJI)
	assert.NoError(t, err)

	st.setupGetSlackClient(tenantID, false, nil, nil)
	st.mockSlackClient.On("BotUserId").Return(_BOT_USER_ID)
	st.mockSlackClient.On("DeleteMessage", "C12345", "1234567890.123456").Return("C12345", "1234567890.123456", nil)

	err = st.handler.HandleEvent(ctx, eventData)

	assert.NoError(t, err)
	st.mockSlackClient.AssertExpectations(t)
}

func TestPostChatResponse(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	requestId := requestcontext.RequestId("test-request-id")
	ctx := context.Background()

	mockSlackClientFactory := new(MockSlackClientFactory)
	mockSlackClient := new(MockSlackClient)

	// don't see a great way to actually test the contents of the messages
	// since the MsgOption object is a function which acts on a slack package-internal config object

	// Hello there\n:clock:
	mockSlackClient.On("PostMessage", "C123", mock.AnythingOfType("[]slack.MsgOption")).Return("C123", "123", nil).Once()
	// Hello there\nA\n:clock:
	mockSlackClient.On("UpdateMessage", "C123", "123", mock.AnythingOfType("[]slack.MsgOption")).Return("C123", "123", "123", nil).Once()
	// Hello there\nA\nAnother line\n:clock:
	// message too long error
	mockSlackClient.On("UpdateMessage", "C123", "123", mock.AnythingOfType("[]slack.MsgOption")).Return("C123", "123", "123", slack.SlackErrorResponse{Err: "msg_too_long"}).Once()
	// Hello there\nA\n (final message)
	mockSlackClient.On("UpdateMessage", "C123", "123", mock.AnythingOfType("[]slack.MsgOption")).Return("C123", "123", "123", nil).Once()
	// Another line\n```really really\n```:clock:
	mockSlackClient.On("PostMessage", "C123", mock.AnythingOfType("[]slack.MsgOption")).Return("C123", "125", nil).Once()
	// Another line\n```really really\nlong code block```\n:clock:
	// message too long error
	mockSlackClient.On("UpdateMessage", "C123", "125", mock.AnythingOfType("[]slack.MsgOption")).Return("C123", "125", "125", slack.SlackErrorResponse{Err: "msg_too_long"}).Once()
	// Another line\n```really really\n (final message)
	mockSlackClient.On("UpdateMessage", "C123", "125", mock.AnythingOfType("[]slack.MsgOption")).Return("C123", "125", "125", nil).Once()
	// ```long code block```\n (final message)
	mockSlackClient.On("PostMessage", "C123", mock.AnythingOfType("[]slack.MsgOption")).Return("C123", "127", nil).Once()

	mockSlackClientFactory.On("GetSlackClient", ctx, tenantID, requestId).Return(mockSlackClient, nil)

	st.handler.clockEmojis = []string{"clock1", "clock2", "clock3"}

	st.mockTenantCache = new(MockTenantCache)
	mockChatResponseChannel := make(chan SlackResponseLine, 5)
	mockChatResponseChannel <- SlackResponseLine{Text: "Hello there\n"}
	mockChatResponseChannel <- SlackResponseLine{Text: "Hello there\nA\n"}
	mockChatResponseChannel <- SlackResponseLine{Text: "Hello there\nA\nAnother line\n"}
	mockChatResponseChannel <- SlackResponseLine{Text: "Hello there\nA\nAnother line\n```really really\n"}
	mockChatResponseChannel <- SlackResponseLine{Text: "Hello there\nA\nAnother line\n```really really\nlong code block```\n"}
	close(mockChatResponseChannel)

	mockRIEventChannel := make(chan *riproto.RISlackbotResponse_Event, 100)

	st.handler.PostChatResponse(ctx, mockSlackClient, "test-tenant-name", "test-tenant-name", "U1234567890", "C123", "1234", "1234", (<-chan SlackResponseLine)(mockChatResponseChannel), mockRIEventChannel, "", []slack.Block(nil))
}

func TestHandleSharedChannelEvent(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	ctx := context.Background()
	requestId := requestcontext.RequestId("test-request-id")

	userMessage := "Hello, bot!"
	eventData, err := createTestSlackEvent("app_mention", userMessage, slackTimestamp(st.mockClock.Now()), "", "")
	assert.NoError(t, err)

	threadMessages := []slack.Message{{Msg: slack.Msg{Text: userMessage, User: _HUMAN_USER_ID}}}
	st.setupMockSlackClientFactory(requestId, tenantID, slackTimestamp(st.mockClock.Now()), threadMessages, true, true, false, nil)
	st.setupChatMocks(tenantID, threadMessages, true)
	// an extra call to PostMessage should apply with the signoff
	st.mockSlackClient.On("PostMessage", mock.Anything, mock.Anything).Return("C12345", "987654321.098765", nil)

	// return an installation id and some repos for allRepos
	st.mockTenantLookup.On("GetRepos", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*slackbotproto.Repo{}, []*blobsproto.Blobs{}, []*settingsproto.RepoInformation{{RepoOwner: "test-owner", RepoName: "test-repo"}}, int64(123), nil)

	chatResponseCh := make(chan ChatResponse)
	st.mockChatClient.On("Chat", mock.Anything, tenantID, mock.Anything, mock.Anything, []string(nil), fmt.Sprintf("%s: Hello, bot!", _HUMAN_USER_ID), _BOT_USER_ID, "C12345", "", true, []*gleanproto.Document(nil)).Return((<-chan ChatResponse)(chatResponseCh), nil)

	go func() {
		chatResponseCh <- ChatResponse{Resp: chatproto.ChatResponse{Text: "Hello, human!"}}
		close(chatResponseCh)
	}()

	err = st.handler.HandleEvent(ctx, eventData)
	assert.NoError(t, err)
}

func testAllowlistedChannelHelper(t *testing.T, allowed bool) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	requestId := requestcontext.RequestId("test-request-id")
	ctx := context.Background()

	userMessage := fmt.Sprintf("Hello, bot! @<%s>", _BOT_USER_ID)
	eventData, err := createTestSlackEvent("app_mention", userMessage, slackTimestamp(st.mockClock.Now()), _HUMAN_USER_ID, "")
	assert.NoError(t, err)

	mockTenantCache := new(MockTenantCache)
	var configs map[string]string
	if allowed {
		configs = map[string]string{"slackbot_allowlist_channels": "[\"C12345\"]"}
	} else {
		configs = map[string]string{"slackbot_allowlist_channels": "[\"some_other_channe0l\"]"}
	}
	mockTenantCache.On("GetTenant", tenantID).Return(&tenantproto.Tenant{Id: tenantID, Config: &tenantproto.Config{Configs: configs}}, nil)
	st.handler.tenantCache = mockTenantCache
	st.mockTenantCache = mockTenantCache

	threadMessages := []slack.Message{{Msg: slack.Msg{Text: userMessage, User: _HUMAN_USER_ID}}}

	if allowed {
		st.setupMockSlackClientFactory(requestId, tenantID, slackTimestamp(st.mockClock.Now()), threadMessages, false, false, false, nil)
		st.setupChatMocks(tenantID, threadMessages, false)
		chatResponseCh := make(chan ChatResponse)
		st.mockChatClient.On("Chat", mock.Anything, tenantID, mock.Anything, mock.Anything, []string(nil), fmt.Sprintf("%s: Hello, bot! @<%s>", _HUMAN_USER_ID, _BOT_USER_ID), _BOT_USER_ID, "C12345", _CHANNEL_NAME, false, []*gleanproto.Document(nil)).Return((<-chan ChatResponse)(chatResponseCh), nil)
		go func() {
			chatResponseCh <- ChatResponse{Resp: chatproto.ChatResponse{Text: "Hello, human!"}}
			close(chatResponseCh)
		}()
	} else {
		st.setupGetSlackClient(tenantID, false, nil, nil)
		st.mockSlackClient.On("BotUserId").Return(_BOT_USER_ID)
		st.mockSlackClient.On("GetConversationInfo", &slack.GetConversationInfoInput{ChannelID: "C12345"}).Return(&slack.Channel{GroupConversation: slack.GroupConversation{Conversation: slack.Conversation{IsExtShared: true}}}, nil)
		st.mockSlackClient.On("GetUserProfile", mock.AnythingOfType("*slack.GetUserProfileParameters")).Return(&slack.UserProfile{Email: "<EMAIL>"}, nil)
		st.mockSlackClient.On("PostMessage", "C12345", mock.AnythingOfType("[]slack.MsgOption")).Return("1234567890.123456", "1234567890.123456", nil)

		st.mockChatClient.AssertNotCalled(t, "Chat") // ensure that we exited early and didn't call chat
	}
	err = st.handler.HandleEvent(ctx, eventData)
	assert.NoError(t, err)
}

func TestHandleAllowlistedChannelEvent(t *testing.T) {
	t.Run("allowlisted", func(t *testing.T) { testAllowlistedChannelHelper(t, true) })
	t.Run("not allowlisted", func(t *testing.T) { testAllowlistedChannelHelper(t, false) })
}

func TestMessageWithAttachments(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	requestId := requestcontext.RequestId("test-request-id")
	ctx := context.Background()

	userMessage := "Hello, bot!"
	eventData, err := createTestSlackEvent("message", userMessage, slackTimestamp(st.mockClock.Now()), "", "")
	assert.NoError(t, err)

	threadMessages := []slack.Message{{Msg: slack.Msg{Text: "Hello, bot!", User: _HUMAN_USER_ID, Attachments: []slack.Attachment{{Text: "attachment"}}}}}
	st.setupMockSlackClientFactory(requestId, tenantID, slackTimestamp(st.mockClock.Now()), threadMessages, true, false, false, nil)
	st.setupChatMocks(tenantID, threadMessages, false)

	chatResponseCh := make(chan ChatResponse)
	st.mockChatClient.On("Chat", mock.Anything, tenantID, mock.Anything, mock.Anything, []string(nil), fmt.Sprintf("%s: Hello, bot!\nQuoted text:\nattachment", _HUMAN_USER_ID), _BOT_USER_ID, "C12345", "", false, []*gleanproto.Document(nil)).Return((<-chan ChatResponse)(chatResponseCh), nil)

	go func() {
		chatResponseCh <- ChatResponse{Resp: chatproto.ChatResponse{Text: "Hello, human!"}}
		close(chatResponseCh)
	}()

	err = st.handler.HandleEvent(ctx, eventData)

	assert.NoError(t, err)
}

// Test that if we get an old message, we ignore it.
func TestSkipOldEvent(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	maxResponseLatencySecs := 10

	tenantID := "test-tenant-id"
	ctx := context.Background()

	eventData, err := createTestSlackEvent("message", "Hello, bot!", slackTimestamp(st.mockClock.Now().Add(-time.Duration(maxResponseLatencySecs+1)*time.Second)), "", "")
	assert.NoError(t, err)

	st.setupGetSlackClient(tenantID, false, nil, nil)
	st.mockSlackClient.On("BotUserId").Return(_BOT_USER_ID)
	st.mockSlackClient.On("GetUserProfile", mock.AnythingOfType("*slack.GetUserProfileParameters")).Return(&slack.UserProfile{Email: "<EMAIL>"}, nil)

	st.featureFlags.Set("slackbot_max_response_latency_secs", maxResponseLatencySecs)
	err = st.handler.HandleEvent(ctx, eventData)

	assert.NoError(t, err)
	st.mockChatClient.AssertNotCalled(t, "Chat") // ensure that we exited early and didn't call chat
}

// Test that if the Slack message timestamp is in an unexpected format we still process the message.
func TestBadTimestamp(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	requestId := requestcontext.RequestId("test-request-id")
	ctx := context.Background()
	badTimestamp := "123.4567.890"

	userMessage := fmt.Sprintf("Hello, bot! @<%s>", _BOT_USER_ID)
	eventData, err := createTestSlackEvent("app_mention", userMessage, badTimestamp, "", "")
	assert.NoError(t, err)

	threadMessages := []slack.Message{{Msg: slack.Msg{Text: userMessage, User: _HUMAN_USER_ID}}}
	st.setupMockSlackClientFactory(requestId, tenantID, badTimestamp, threadMessages, false, false, false, nil)
	st.setupChatMocks(tenantID, threadMessages, false)

	chatResponseCh := make(chan ChatResponse)
	st.mockChatClient.On("Chat", mock.Anything, tenantID, mock.Anything, mock.Anything, []string(nil), fmt.Sprintf("%s: Hello, bot! @<%s>", _HUMAN_USER_ID, _BOT_USER_ID), _BOT_USER_ID, "C12345", _CHANNEL_NAME, false, []*gleanproto.Document(nil)).Return((<-chan ChatResponse)(chatResponseCh), nil)

	go func() {
		chatResponseCh <- ChatResponse{Resp: chatproto.ChatResponse{Text: "Hello, human!"}}
		close(chatResponseCh)
	}()

	err = st.handler.HandleEvent(ctx, eventData)

	assert.NoError(t, err)
}

func TestMessageWithOnlyImageAttachments(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	requestId := requestcontext.RequestId("test-request-id")
	ctx := context.Background()

	eventData, err := createTestSlackEvent("message", "Hello, bot!", slackTimestamp(time.Now()), "", "")
	assert.NoError(t, err)

	// this thread starts off with a message with only an image attachment
	// followed by the bot mention in DMs
	threadReplies := []slack.Message{{Msg: slack.Msg{User: "U55555", Attachments: []slack.Attachment{{ImageURL: "https://example.com/image.png"}}}}, {Msg: slack.Msg{Text: "Hello, bot!", User: _HUMAN_USER_ID}}}
	st.setupMockSlackClientFactory(requestId, tenantID, slackTimestamp(time.Now()), threadReplies, true, false, false, nil)
	st.setupChatMocks(tenantID, threadReplies, false)

	chatResponseCh := make(chan ChatResponse)
	// we expect to filter out the image message
	st.mockChatClient.On("Chat", mock.Anything, tenantID, mock.Anything, mock.Anything, []string(nil),
		"U54321: Hello, bot!", _BOT_USER_ID, "C12345", "", false, []*gleanproto.Document(nil)).Return((<-chan ChatResponse)(chatResponseCh), nil)

	go func() {
		chatResponseCh <- ChatResponse{Resp: chatproto.ChatResponse{Text: "Hello, human!"}}
		close(chatResponseCh)
	}()

	err = st.handler.HandleEvent(ctx, eventData)
	assert.NoError(t, err)
}

func TestMessageCutoffToMention(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	requestId := requestcontext.RequestId("test-request-id")
	ctx := context.Background()

	eventData, err := createTestSlackEvent("message", "Hello, bot!", slackTimestamp(time.Now()), "", "")
	assert.NoError(t, err)

	// this thread has two bot mentions, but because the event is for the first one we expect the chat dialog to be only the first mention
	threadReplies := []slack.Message{{Msg: slack.Msg{Text: "Hello, bot!", User: _HUMAN_USER_ID, Timestamp: slackTimestamp(time.Now())}}, {Msg: slack.Msg{Text: "Hello again, bot!", User: _HUMAN_USER_ID, Timestamp: slackTimestamp(time.Now().Add(time.Second))}}}
	st.setupMockSlackClientFactory(requestId, tenantID, slackTimestamp(time.Now()), threadReplies, true, false, false, nil)
	st.setupChatMocks(tenantID, threadReplies, false)

	chatResponseCh := make(chan ChatResponse)
	st.mockChatClient.On("Chat", mock.Anything, tenantID, mock.Anything, mock.Anything, []string(nil), "U54321: Hello, bot!", _BOT_USER_ID, "C12345", "", false, []*gleanproto.Document(nil)).Return((<-chan ChatResponse)(chatResponseCh), nil)

	go func() {
		chatResponseCh <- ChatResponse{Resp: chatproto.ChatResponse{Text: "Hello, human!"}}
		close(chatResponseCh)
	}()

	err = st.handler.HandleEvent(ctx, eventData)
	assert.NoError(t, err)
}

func TestExponentialBatching(t *testing.T) {
	lines := make(chan SlackResponseLine)
	go func() {
		for i := 1; i < 200; i++ {
			lines <- SlackResponseLine{Text: strings.Repeat("testing\n", i)}
		}
		close(lines)
	}()

	slackChatHandler := new(SlackChatHandler)
	outputCh := slackChatHandler.batchLines(lines)

	// values of expectedBatchSizes are computed as min(floor(1 * backoff_factor ^ x), max_batch_size)
	// ends with 11 to account for the last batch to add up to 200
	expectedBatchSizes := []int{1, 1, 1, 2, 2, 3, 4, 6, 8, 10, 13, 17, 20, 20, 20, 20, 20, 20, 11}
	actualBatchSizes := []int{}
	totalPrevLines := 0
	for batch := range outputCh {
		currentBatchSize := strings.Count(batch.Text, "\n") - totalPrevLines
		actualBatchSizes = append(actualBatchSizes, currentBatchSize)
		totalPrevLines += currentBatchSize
	}
	assert.Equal(t, expectedBatchSizes, actualBatchSizes)
}

func TestGetConversationHistory(t *testing.T) {
	// Create a minimal SlackChatHandler instance
	handler := &SlackChatHandler{}

	// Test case 1: Multiple messages
	messages := []slack.Message{
		{Msg: slack.Msg{Text: "First message", User: "U12345"}},
		{Msg: slack.Msg{Text: "Second message", User: "U67890"}},
		{Msg: slack.Msg{Text: "Third message", User: "U12345"}},
		{Msg: slack.Msg{Text: "Last message", User: "U67890"}},
	}
	timestamp := slackTimestamp(time.Now())
	ctx := context.Background()

	fullHistory, lastMessage := handler.GetConversationHistory(ctx, messages, timestamp)

	expectedHistory := []string{
		"U12345: First message",
		"U67890: Second message",
		"U12345: Third message",
	}

	assert.Equal(t, expectedHistory, fullHistory)
	assert.Equal(t, "U67890: Last message", lastMessage)

	// Test case 2: Empty message list
	emptyHistory, emptyLastMessage := handler.GetConversationHistory(ctx, []slack.Message{}, timestamp)
	assert.Equal(t, []string(nil), emptyHistory)
	assert.Equal(t, "", emptyLastMessage)

	// Test case 3: Single message
	singleMessage := []slack.Message{{Msg: slack.Msg{Text: "Only message", User: "U12345"}}}
	singleHistory, singleLastMessage := handler.GetConversationHistory(ctx, singleMessage, timestamp)
	assert.Equal(t, []string(nil), singleHistory)
	assert.Equal(t, "U12345: Only message", singleLastMessage)
}

func TestGithubPromotion(t *testing.T) {
	testCase := func(t *testing.T, hasInstallationId bool, hasRepos bool, hasCheckpoint bool) {
		st := newSlackChatHandlerTest(t)
		defer st.assertExpectations()

		tenantID := "test-tenant-id"
		ctx := context.Background()
		userMessage := fmt.Sprintf("Hello, bot! @<%s>", _BOT_USER_ID)
		eventData, err := createTestSlackEvent("app_mention", userMessage, slackTimestamp(st.mockClock.Now()), "", "")
		assert.NoError(t, err)

		st.setupGetSlackClient(tenantID, false, nil, nil)
		st.mockSlackClient.On("BotUserId").Return(_BOT_USER_ID)

		// These are just boilerplate so the test doesn't error out.
		chatResponseCh := make(chan ChatResponse)

		threadMessages := []slack.Message{{Msg: slack.Msg{Text: fmt.Sprintf("Hello, bot! @<%s>", _BOT_USER_ID), User: _HUMAN_USER_ID}}}
		st.mockSlackClient.On("AddReaction", mock.Anything, mock.Anything).Return(nil)
		st.mockSlackClient.On("RemoveReaction", mock.Anything, mock.Anything).Return(nil)
		st.mockSlackClient.On(
			"GetConversationInfo", mock.Anything,
		).Return(&slack.Channel{GroupConversation: slack.GroupConversation{Conversation: slack.Conversation{IsIM: false, IsExtShared: false}, Name: ""}}, nil)
		st.mockSlackClient.On("GetUserProfile", mock.Anything).Return(&slack.UserProfile{Email: "<EMAIL>"}, nil)

		// This is what we're actually testing.
		if !hasInstallationId {
			st.mockTenantLookup.On("GetRepos", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*slackbotproto.Repo{}, []*blobsproto.Blobs{}, []*settingsproto.RepoInformation{}, int64(0), nil)
		} else if !hasRepos {
			st.mockTenantLookup.On("GetRepos", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*slackbotproto.Repo{}, []*blobsproto.Blobs{}, []*settingsproto.RepoInformation{}, int64(123), nil)
		} else if !hasCheckpoint {
			st.mockTenantLookup.On("GetRepos", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*slackbotproto.Repo{{RepoName: "test-repo", RepoOwner: "test-owner"}}, []*blobsproto.Blobs{}, []*settingsproto.RepoInformation{{RepoOwner: "test-owner", RepoName: "test-repo"}}, int64(123), nil)
		} else {
			st.mockTenantLookup.On("GetRepos", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*slackbotproto.Repo{{RepoName: "test-repo", RepoOwner: "test-owner"}}, []*blobsproto.Blobs{{BaselineCheckpointId: proto.String("test-checkpoint"), Added: [][]byte{[]byte("test-blob")}, Deleted: nil}}, []*settingsproto.RepoInformation{{RepoOwner: "test-owner", RepoName: "test-repo"}}, int64(123), nil)
		}
		if hasInstallationId && hasRepos && hasCheckpoint {
			//  Getting here means the app was not promoted
			st.mockSlackClient.On("GetConversationReplies", mock.Anything).Return(threadMessages, false, "", nil)
			st.mockSlackClient.On("UpdateMessage", mock.Anything, mock.Anything, mock.Anything).Return("C12345", "987654321.098765", "new text", nil)
			st.mockChatClient.On(
				"Chat", mock.Anything, mock.Anything, mock.Anything,
				mock.Anything, mock.Anything, mock.Anything, mock.Anything,
				mock.Anything, mock.Anything, mock.Anything, mock.Anything,
			).Return((<-chan ChatResponse)(chatResponseCh), nil)
		}

		st.mockSlackClient.On(
			"PostMessage", mock.Anything, mock.Anything, mock.Anything,
		).Return(
			"C12345", "987654321.098765", nil,
		)

		assert.NoError(t, err)

		go func() {
			chatResponseCh <- ChatResponse{Resp: chatproto.ChatResponse{Text: "Hello, human!"}}
			close(chatResponseCh)
		}()
		err = st.handler.HandleEvent(ctx, eventData)
		assert.NoError(t, err)
	}

	// Promotion should occur if no installation ID is found, or if no repos are found, or if no checkpoint is found.

	// No installation ID, no repos, no checkpoint - should promote
	testCase(t, false, false, false)

	// Has installation ID, but no repos - should still promote
	testCase(t, true, false, false)

	// Has installation ID and repos, but no checkpoint - should still promote
	testCase(t, true, true, false)

	// Has installation ID, repos, and checkpoint - should not promote
	testCase(t, true, true, true)
}

func TestReposNotSelected(t *testing.T) {
	testCases := []struct {
		name      string
		isStaging bool
	}{
		{
			name:      "repos_not_selected_staging",
			isStaging: true,
		},
		{
			name:      "repos_not_selected_prod",
			isStaging: false,
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			st := newSlackChatHandlerTest(t)
			defer st.assertExpectations()

			st.featureFlags.Set("slackbot_select_repo_context", true)

			tenantID := "test-tenant-id"
			ctx := context.Background()
			userMessage := fmt.Sprintf("Hello, bot! @<%s>", _BOT_USER_ID)
			eventData, err := createTestSlackEvent("app_mention", userMessage, slackTimestamp(st.mockClock.Now()), "", "")
			assert.NoError(t, err)

			st.setupGetSlackClient(tenantID, false, nil, nil)
			st.mockSlackClient.On("BotUserId").Return(_BOT_USER_ID)

			// These are just boilerplate so the test doesn't error out.
			chatResponseCh := make(chan ChatResponse)
			st.mockSlackClient.On("AddReaction", mock.Anything, mock.Anything).Return(nil)
			st.mockSlackClient.On("RemoveReaction", mock.Anything, mock.Anything).Return(nil)
			st.mockSlackClient.On(
				"GetConversationInfo", mock.Anything,
			).Return(&slack.Channel{GroupConversation: slack.GroupConversation{Conversation: slack.Conversation{IsIM: false, IsExtShared: false}, Name: ""}}, nil)
			st.mockSlackClient.On("GetUserProfile", mock.Anything).Return(&slack.UserProfile{Email: "<EMAIL>"}, nil)

			// No selected repos for the channel, but multiple repos are installed
			st.mockTenantLookup.On("GetRepos", mock.Anything, mock.Anything, mock.Anything, mock.Anything, true).Return([]*slackbotproto.Repo{}, []*blobsproto.Blobs{}, []*settingsproto.RepoInformation{{RepoOwner: "test-owner", RepoName: "test-repo"}, {RepoOwner: "test-owner", RepoName: "test-repo2"}}, int64(123), nil)

			repoSelectButtonValue := base64.StdEncoding.EncodeToString(eventData)

			ext := "/augment"
			if tc.isStaging {
				ext = "/staging-augment"
				st.handler.slashCommand = "/staging-augment"
			}

			expectedBlocks := []slack.Block{
				NewExpandedTextSectionBlock(fmt.Sprintf(SELECT_REPO_MESSAGE, ext)),
				slack.NewActionBlock(
					"repo_select_button_block",
					slack.NewButtonBlockElement(
						"open_repo_select_modal_action",
						repoSelectButtonValue,
						slack.NewTextBlockObject("plain_text", "Select Repositories", false, false),
					),
				),
			}

			st.mockSlackClient.On("PostMessage", mock.Anything, mock.Anything).Return("C12345", "987654321.098765", nil).Once().Run(func(args mock.Arguments) {
				msgOptions := args.Get(1).([]slack.MsgOption)
				_, values, err := slack.UnsafeApplyMsgOptions("tok", "C12345", "api/", msgOptions...)
				assert.NoError(t, err)

				var actualBlocks []map[string]interface{}
				err = json.Unmarshal([]byte(values.Get("blocks")), &actualBlocks)
				assert.NoError(t, err)

				expectedJSON, err := json.Marshal(expectedBlocks)
				assert.NoError(t, err)
				var expectedBlocksMap []map[string]interface{}
				err = json.Unmarshal(expectedJSON, &expectedBlocksMap)
				assert.NoError(t, err)

				assert.Equal(t, expectedBlocksMap, actualBlocks)
			})

			assert.NoError(t, err)

			go func() {
				chatResponseCh <- ChatResponse{Resp: chatproto.ChatResponse{Text: "Hello, human!"}}
				close(chatResponseCh)
			}()
			err = st.handler.HandleEvent(ctx, eventData)
			assert.NoError(t, err)
		})
	}
}

func TestMissingCheckpoint(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	ctx := context.Background()
	userMessage := fmt.Sprintf("Hello, bot! @<%s>", _BOT_USER_ID)
	eventData, err := createTestSlackEvent("app_mention", userMessage, slackTimestamp(st.mockClock.Now()), "", "")
	assert.NoError(t, err)

	// These are just boilerplate so the test doesn't error out.
	chatResponseCh := make(chan ChatResponse)
	st.mockChatClient.On(
		"Chat", mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		mock.Anything, mock.Anything, mock.Anything,
	).Return((<-chan ChatResponse)(chatResponseCh), nil)
	st.setupGetSlackClient("test-tenant-id", false, nil, nil)
	st.mockSlackClient.On(
		"GetConversationInfo", mock.Anything,
	).Return(&slack.Channel{GroupConversation: slack.GroupConversation{Conversation: slack.Conversation{IsIM: false, IsExtShared: false}, Name: ""}}, nil)
	st.mockSlackClient.On("AddReaction", mock.Anything, mock.Anything).Return(nil)
	st.mockTenantLookup.On("GetRepos", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*slackbotproto.Repo{{RepoName: "test-repo", RepoOwner: "test-owner"}}, []*blobsproto.Blobs{{BaselineCheckpointId: proto.String("test-checkpoint"), Added: [][]byte{[]byte("test-blob")}, Deleted: nil}}, []*settingsproto.RepoInformation{{RepoOwner: "test-owner", RepoName: "test-repo"}}, int64(123), nil)
	threadMessages := []slack.Message{{Msg: slack.Msg{Text: userMessage, User: _HUMAN_USER_ID}}}
	st.mockSlackClient.On("GetConversationReplies", mock.Anything).Return(threadMessages, false, "", nil)
	st.mockSlackClient.On("RemoveReaction", mock.Anything, mock.Anything).Return(nil)
	st.mockSlackClient.On("BotUserId").Return(_BOT_USER_ID)
	st.mockSlackClient.On("GetUserProfile", mock.Anything).Return(&slack.UserProfile{Email: "<EMAIL>"}, nil)

	st.mockSlackClient.On(
		"PostMessage", "C12345", mock.Anything,
	).Return(
		"C12345", "987654321.098765", nil,
	).Once().Run(func(args mock.Arguments) {
		assert.Equal(t, "C12345", args.Get(0))
		options := args.Get(1).([]slack.MsgOption)
		endpoint, values, err := slack.UnsafeApplyMsgOptions("tok", "C12345", "api/", options...)
		assert.NoError(t, err)
		t.Logf("values: %v", values)
		assert.Equal(t, "api/chat.postMessage", endpoint)

		// Parse the blocks JSON to verify the content
		var rawBlocks []map[string]interface{}
		err = json.Unmarshal([]byte(values.Get("blocks")), &rawBlocks)
		assert.NoError(t, err)

		// Check the first block has the expected text
		assert.Equal(t, len(rawBlocks), 1)
		firstBlock := rawBlocks[0]
		assert.Equal(t, "section", firstBlock["type"])
		text, ok := firstBlock["text"].(map[string]interface{})
		assert.True(t, ok)
		assert.Equal(t, "mrkdwn", text["type"])
		assert.Equal(t, MISSING_CHECKPOINT_MESSAGE, text["text"])
	})

	assert.NoError(t, err)

	go func() {
		chatResponseCh <- ChatResponse{Resp: chatproto.ChatResponse{Text: "Hello, human!", CheckpointNotFound: true}}
		close(chatResponseCh)
	}()
	err = st.handler.HandleEvent(ctx, eventData)
	assert.NoError(t, err)
}

func TestHandleAppOpenedEvent(t *testing.T) {
	introBlock := slack.NewSectionBlock(
		slack.NewTextBlockObject(
			"mrkdwn",
			fmt.Sprintf(WELCOME_MESSAGE_BASE, _BOT_USER_ID, ""),
			false,
			false,
		),
		nil,
		nil,
	)

	testCases := []struct {
		name           string
		githubRepos    []*settingsproto.RepoInformation
		installationId int64
		expectedBlocks []slack.Block
		isStaging      bool
	}{
		{
			name:           "no_github_app",
			githubRepos:    nil,
			installationId: 0,
			expectedBlocks: []slack.Block{
				introBlock,
				slack.NewSectionBlock(
					slack.NewTextBlockObject(
						"mrkdwn",
						INSTALL_GITHUB_APP_NOTICE,
						false,
						false,
					),
					nil,
					nil,
				),
				installGithubAppButton,
			},
			isStaging: false,
		},
		{
			name: "multiple_repos_prod",
			githubRepos: []*settingsproto.RepoInformation{
				{RepoOwner: "test-owner", RepoName: "repo1"},
				{RepoOwner: "test-owner", RepoName: "repo2"},
				{RepoOwner: "test-owner", RepoName: "repo3"},
			},
			installationId: 123,
			expectedBlocks: []slack.Block{
				introBlock,
				slack.NewSectionBlock(
					slack.NewTextBlockObject(
						"mrkdwn",
						fmt.Sprintf(REPO_SELECT_NOTICE, "/augment"),
						false,
						false,
					),
					nil,
					nil,
				),
				slack.NewActionBlock(
					"repo_select_button_block",
					slack.NewButtonBlockElement(
						"open_repo_select_modal_action",
						"open_repo_select_modal_action",
						slack.NewTextBlockObject("plain_text", "Select Repositories", false, false),
					),
				),
			},
			isStaging: false,
		},
		{
			name: "multiple_repos_staging",
			githubRepos: []*settingsproto.RepoInformation{
				{RepoOwner: "test-owner", RepoName: "repo1"},
				{RepoOwner: "test-owner", RepoName: "repo2"},
				{RepoOwner: "test-owner", RepoName: "repo3"},
			},
			installationId: 123,
			expectedBlocks: []slack.Block{
				introBlock,
				slack.NewSectionBlock(
					slack.NewTextBlockObject(
						"mrkdwn",
						fmt.Sprintf(REPO_SELECT_NOTICE, "/staging-augment"),
						false,
						false,
					),
					nil,
					nil,
				),
				slack.NewActionBlock(
					"repo_select_button_block",
					slack.NewButtonBlockElement(
						"open_repo_select_modal_action",
						"open_repo_select_modal_action",
						slack.NewTextBlockObject("plain_text", "Select Repositories", false, false),
					),
				),
			},
			isStaging: true,
		},
		{
			name: "single_repo",
			githubRepos: []*settingsproto.RepoInformation{
				{RepoOwner: "test-owner", RepoName: "repo1"},
			},
			installationId: 123,
			expectedBlocks: []slack.Block{
				introBlock,
			},
			isStaging: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			st := newSlackChatHandlerTest(t)
			if tc.isStaging {
				st.handler.slashCommand = "/staging-augment"
			}
			defer st.assertExpectations()

			tenantID := "test-tenant-id"
			ctx := context.Background()

			eventData, err := createTestSlackEvent("app_home_opened", "", slackTimestamp(st.mockClock.Now()), _HUMAN_USER_ID, "")
			assert.NoError(t, err)

			st.setupGetSlackClient(tenantID, false, &settingsproto.GithubSettings{
				Repos:          tc.githubRepos,
				InstallationId: tc.installationId,
			}, nil)
			st.mockSlackClient.On("GetConversationHistory", mock.Anything).Return(&slack.GetConversationHistoryResponse{}, nil)
			st.mockSlackClient.On("BotUserId").Return(_BOT_USER_ID)

			st.mockSlackClient.On("PostMessage", mock.Anything, mock.Anything).Return("C12345", "987654321.098765", nil).Once().Run(func(args mock.Arguments) {
				msgOptions := args.Get(1).([]slack.MsgOption)
				_, values, err := slack.UnsafeApplyMsgOptions("tok", "C12345", "api/", msgOptions...)
				assert.NoError(t, err)

				var actualBlocks []map[string]interface{}
				err = json.Unmarshal([]byte(values.Get("blocks")), &actualBlocks)
				assert.NoError(t, err)

				expectedJSON, err := json.Marshal(tc.expectedBlocks)
				assert.NoError(t, err)
				var expectedBlocksMap []map[string]interface{}
				err = json.Unmarshal(expectedJSON, &expectedBlocksMap)
				assert.NoError(t, err)

				assert.Equal(t, expectedBlocksMap, actualBlocks)
			})

			err = st.handler.HandleEvent(ctx, eventData)
			assert.NoError(t, err)
		})
	}
}

func TestHandleMemberJoinedChannel(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	ctx := context.Background()

	eventData, err := createTestSlackEvent("member_joined_channel", "", slackTimestamp(st.mockClock.Now()), _HUMAN_USER_ID, "")
	assert.NoError(t, err)

	st.setupGetSlackClient(tenantID, false, nil, nil)
	st.mockSlackClient.On("BotUserId").Return(_BOT_USER_ID)
	st.mockSlackClient.On("PostMessage", mock.Anything, mock.Anything).Return("C12345", "987654321.098765", nil).Once().Run(func(args mock.Arguments) {
		msgOptions := args.Get(1).([]slack.MsgOption)
		_, values, err := slack.UnsafeApplyMsgOptions("tok", "C12345", "api/", msgOptions...)
		assert.NoError(t, err)

		assert.Equal(t, fmt.Sprintf(CHANNEL_WELCOME_MESSAGE_TEMPLATE, _HUMAN_USER_ID, _BOT_USER_ID), values.Get("text"))
	})

	err = st.handler.HandleEvent(ctx, eventData)
	assert.NoError(t, err)
}

func TestHandleMemberJoinedChannelNotAllowlisted(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	ctx := context.Background()

	mockTenantCache := new(MockTenantCache)

	configs := map[string]string{"slackbot_allowlist_channels": "[\"some_other_channe0l\"]"}

	mockTenantCache.On("GetTenant", tenantID).Return(&tenantproto.Tenant{Id: tenantID, Config: &tenantproto.Config{Configs: configs}}, nil)
	st.handler.tenantCache = mockTenantCache
	st.mockTenantCache = mockTenantCache

	eventData, err := createTestSlackEvent("member_joined_channel", "", slackTimestamp(st.mockClock.Now()), _HUMAN_USER_ID, "")
	assert.NoError(t, err)

	st.setupGetSlackClient(tenantID, false, nil, nil)
	st.mockSlackClient.On("BotUserId").Return(_BOT_USER_ID)

	err = st.handler.HandleEvent(ctx, eventData)
	assert.NoError(t, err)
}

func TestHandleFeedbackReaction(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	tenantID := "test-tenant-id"
	ctx := context.Background()

	eventData, err := createTestSlackEvent("reaction_added", "", "2345678900.234567", _HUMAN_USER_ID, "", POSTIVE_FEEDBACK_EMOJI)
	assert.NoError(t, err)

	st.setupGetSlackClient(tenantID, false, nil, nil)
	st.mockSlackClient.On("BotUserId").Return(_BOT_USER_ID)
	st.mockSlackClient.On("GetConversationReplies", mock.Anything).Return([]slack.Message{{Msg: slack.Msg{Text: "test message", ThreadTimestamp: "1234567890.123456"}}}, false, "", nil)

	st.mockSlackClient.On("PostEphemeral", "C12345", _HUMAN_USER_ID, mock.AnythingOfType("[]slack.MsgOption")).Return("1234567890.123456", nil)

	err = st.handler.HandleEvent(ctx, eventData)

	assert.NoError(t, err)
}

func TestChatWithGleanDocs(t *testing.T) {
	st := newSlackChatHandlerTest(t)
	defer st.assertExpectations()

	st.featureFlags.Set("enable_glean", true)

	tenantID := "test-tenant-id"
	requestId := requestcontext.RequestId("test-request-id")
	ctx := context.Background()

	userMessage := "Hello, bot!"
	eventData, err := createTestSlackEvent("app_mention", userMessage, slackTimestamp(st.mockClock.Now()), _HUMAN_USER_ID, "")
	assert.NoError(t, err)

	threadMessages := []slack.Message{{Msg: slack.Msg{Text: userMessage, User: _HUMAN_USER_ID}}}
	st.setupMockSlackClientFactory(requestId, tenantID, slackTimestamp(st.mockClock.Now()), threadMessages, true, false, false, &settingsproto.GleanTenantSettings{
		OauthProvider:     gleanproto.Provider_GSUITE,
		OauthClientId:     "test-client-id",
		OauthClientSecret: "test-client-secret",
	})
	st.setupChatMocks(tenantID, threadMessages, false)

	st.mockGleanClient.On("Search", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&gleanproto.SearchResponse{Documents: []*gleanproto.Document{{Title: "test title", Url: "test url"}}}, nil)
	st.mockSlackClient.On("AddReaction", GLEAN_EMOJI, mock.Anything).Return(nil)
	st.mockGleanClient.On("IsToolConfigured", mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

	chatResponseCh := make(chan ChatResponse)
	st.mockChatClient.On("Chat", mock.Anything, tenantID, mock.Anything, mock.Anything, []string(nil), fmt.Sprintf("%s: %s", _HUMAN_USER_ID, userMessage), _BOT_USER_ID, "C12345", "", false, []*gleanproto.Document{{Title: "test title", Url: "test url"}}).Return((<-chan ChatResponse)(chatResponseCh), nil)

	go func() {
		chatResponseCh <- ChatResponse{Resp: chatproto.ChatResponse{Text: "Hello, human!"}}
		close(chatResponseCh)
	}()

	err = st.handler.HandleEvent(ctx, eventData)

	assert.NoError(t, err)
}
