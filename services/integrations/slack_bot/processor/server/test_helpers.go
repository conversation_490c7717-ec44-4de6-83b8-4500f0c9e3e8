package main

import (
	"context"
	"io"
	"net/http"
	"os"
	"testing"
	"time"

	blobnamespb "github.com/augmentcode/augment/base/blob_names/proto"

	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/base/logging"
	modelfinderproto "github.com/augmentcode/augment/services/api_proxy/model_finder_proto"
	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	chatproto "github.com/augmentcode/augment/services/chat_host/proto"
	githubstateclient "github.com/augmentcode/augment/services/integrations/github/state/client"
	githubstatepersistproto "github.com/augmentcode/augment/services/integrations/github/state/proto"
	gleanproto "github.com/augmentcode/augment/services/integrations/glean/proto"
	slackbotproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	requestinsightproto "github.com/augmentcode/augment/services/request_insight/proto"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/slack-go/slack"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/augmentcode/augment/services/integrations/webhookmapping"
	settings_service_proto "github.com/augmentcode/augment/services/settings/proto"
)

// TestMain runs before all tests. In this case we want to setup
// context for logging matching what server does so we surface
// logs like `log.Ctx(ctx).Info()`.
func TestMain(m *testing.M) {
	logging.SetupServerLogging()
	os.Exit(m.Run())
}

// Mock SlackClient
type MockSlackClient struct {
	mock.Mock
}

func (m *MockSlackClient) PostMessage(channelID string, options ...slack.MsgOption) (string, string, error) {
	args := m.Called(channelID, options)
	return args.String(0), args.String(1), args.Error(2)
}

func (m *MockSlackClient) PostEphemeral(channelID, userID string, options ...slack.MsgOption) (string, error) {
	args := m.Called(channelID, userID, options)
	return args.String(0), args.Error(1)
}

func (m *MockSlackClient) UpdateMessage(channelID, timestamp string, options ...slack.MsgOption) (string, string, string, error) {
	args := m.Called(channelID, timestamp, options)
	return args.String(0), args.String(1), args.String(2), args.Error(3)
}

func (m *MockSlackClient) DeleteMessage(channelID, timestamp string) (string, string, error) {
	args := m.Called(channelID, timestamp)
	return args.String(0), args.String(1), args.Error(2)
}

func (m *MockSlackClient) GetConversationReplies(parameters *slack.GetConversationRepliesParameters) ([]slack.Message, bool, string, error) {
	args := m.Called(parameters)
	return args.Get(0).([]slack.Message), args.Bool(1), args.String(2), args.Error(3)
}

func (m *MockSlackClient) GetConversationHistory(parameters *slack.GetConversationHistoryParameters) (*slack.GetConversationHistoryResponse, error) {
	args := m.Called(parameters)
	return args.Get(0).(*slack.GetConversationHistoryResponse), args.Error(1)
}

func (m *MockSlackClient) AddReaction(name string, item slack.ItemRef) error {
	args := m.Called(name, item)
	return args.Error(0)
}

func (m *MockSlackClient) RemoveReaction(name string, item slack.ItemRef) error {
	args := m.Called(name, item)
	return args.Error(0)
}

func (m *MockSlackClient) GetConversationInfo(input *slack.GetConversationInfoInput) (*slack.Channel, error) {
	args := m.Called(input)
	return args.Get(0).(*slack.Channel), args.Error(1)
}

func (m *MockSlackClient) BotUserId() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockSlackClient) GetUserProfile(params *slack.GetUserProfileParameters) (*slack.UserProfile, error) {
	args := m.Called(params)
	return args.Get(0).(*slack.UserProfile), args.Error(1)
}

func (m *MockSlackClient) GetUserInfo(userID string) (*slack.User, error) {
	args := m.Called(userID)
	return args.Get(0).(*slack.User), args.Error(1)
}

func (m *MockSlackClient) OpenView(triggerID string, view slack.ModalViewRequest) (*slack.ViewResponse, error) {
	args := m.Called(triggerID, view)
	return args.Get(0).(*slack.ViewResponse), args.Error(1)
}

func (m *MockSlackClient) UpdateView(view slack.ModalViewRequest, externalID string, hash string, viewID string) (*slack.ViewResponse, error) {
	args := m.Called(view, externalID, hash, viewID)
	return args.Get(0).(*slack.ViewResponse), args.Error(1)
}

// Mock SlackClientFactory
type MockSlackClientFactory struct {
	mock.Mock
}

func (m *MockSlackClientFactory) GetSlackClient(ctx context.Context, tenantID string, tenantSettings *settingsproto.TenantSettings) (SlackClient, error) {
	args := m.Called(ctx, tenantID, tenantSettings)
	return args.Get(0).(SlackClient), args.Error(1)
}

func (m *MockSlackClientFactory) GetSlackClientFromToken(tenantID string, tenantName string, token secretstring.SecretString, botUserId string) (SlackClient, error) {
	args := m.Called(tenantID, tenantName, token, botUserId)
	return args.Get(0).(SlackClient), args.Error(1)
}

func (m *MockSlackClientFactory) ClearClientForTenant(tenantID string) {
	m.Called(tenantID)
}

// Mock ChatClient
type MockChatClient struct {
	mock.Mock
}

func (m *MockChatClient) Chat(ctx context.Context, tenantID string, tenantSettings *settingsproto.TenantSettings, requestContext *requestcontext.RequestContext, previousThreadMessages []string, currentMessage string, botId string, channelId string, channelName string, isShared bool, gleanDocuments []*gleanproto.Document) (<-chan ChatResponse, error) {
	args := m.Called(ctx, tenantID, tenantSettings, requestContext, previousThreadMessages, currentMessage, botId, channelId, channelName, isShared, gleanDocuments)
	return args.Get(0).(<-chan ChatResponse), args.Error(1)
}

func (m *MockChatClient) Close() {
	m.Called()
}

// Mock TenantCache
type MockTenantCache struct {
	mock.Mock
}

func (m *MockTenantCache) GetTenant(tenantID string) (*tenantproto.Tenant, error) {
	args := m.Called(tenantID)
	return args.Get(0).(*tenantproto.Tenant), args.Error(1)
}

func (m *MockTenantCache) GetTenantByName(tenantName string) (*tenantproto.Tenant, error) {
	args := m.Called(tenantName)
	return args.Get(0).(*tenantproto.Tenant), args.Error(1)
}

func (m *MockTenantCache) GetAllTenants() ([]*tenantproto.Tenant, error) {
	args := m.Called()
	return args.Get(0).([]*tenantproto.Tenant), args.Error(1)
}

func (m *MockTenantCache) GetTenantsInNamespace(shardNamespace string) ([]*tenantproto.Tenant, error) {
	args := m.Called(shardNamespace)
	return args.Get(0).([]*tenantproto.Tenant), args.Error(1)
}

func (m *MockTenantCache) Close() {
	m.Called()
}

// Mock TenantLookup
type MockTenantLookup struct {
	mock.Mock
}

func (m *MockTenantLookup) GetRequestContext(ctx context.Context, tenantID string, requestContext *requestcontext.RequestContext, scopes []tokenexchangeproto.Scope) (*requestcontext.RequestContext, error) {
	args := m.Called(ctx, tenantID, requestContext, scopes)
	return args.Get(0).(*requestcontext.RequestContext), args.Error(1)
}

func (m *MockTenantLookup) GetRepos(ctx context.Context, tenantSettings *settingsproto.TenantSettings, requestContext *requestcontext.RequestContext, channelId string, useChannel bool) ([]*slackbotproto.Repo, []*blobsproto.Blobs, []*settingsproto.RepoInformation, int64, error) {
	args := m.Called(ctx, tenantSettings, requestContext, channelId, useChannel)
	return args.Get(0).([]*slackbotproto.Repo), args.Get(1).([]*blobsproto.Blobs), args.Get(2).([]*settingsproto.RepoInformation), args.Get(3).(int64), args.Error(4)
}

func (m *MockTenantLookup) GetTenantSlackInfo(ctx context.Context, tenantSettings *settingsproto.TenantSettings) (*TenantSlackInfo, error) {
	args := m.Called(ctx, tenantSettings)
	return args.Get(0).(*TenantSlackInfo), args.Error(1)
}

func (m *MockTenantLookup) GetTenantName(ctx context.Context, tenantID string) (string, error) {
	args := m.Called(ctx, tenantID)
	return args.String(0), args.Error(1)
}

func (m *MockTenantLookup) GetTenantConfigs(ctx context.Context, tenantID string) (map[string]string, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(map[string]string), args.Error(1)
}

// Mock SettingsClient
type MockSettingsClient struct {
	mock.Mock
}

func (m *MockSettingsClient) GetTenantSettings(ctx context.Context, requestContext *requestcontext.RequestContext) (*settings_service_proto.GetTenantSettingsResponse, error) {
	args := m.Called(ctx, requestContext)
	return args.Get(0).(*settings_service_proto.GetTenantSettingsResponse), args.Error(1)
}

func (m *MockSettingsClient) UpdateTenantSettings(ctx context.Context, requestContext *requestcontext.RequestContext, settings *settings_service_proto.TenantSettings, expectedVersion string) (*settings_service_proto.UpdateTenantSettingsResponse, error) {
	args := m.Called(ctx, requestContext, settings, expectedVersion)
	return args.Get(0).(*settings_service_proto.UpdateTenantSettingsResponse), args.Error(1)
}

func (m *MockSettingsClient) GetUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext, userId *string) (*settings_service_proto.GetUserSettingsResponse, error) {
	args := m.Called(ctx, requestContext, userId)
	return args.Get(0).(*settings_service_proto.GetUserSettingsResponse), args.Error(1)
}

func (m *MockSettingsClient) UpdateUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext, userId *string, settings *settings_service_proto.UserSettings, expectedVersion string) (*settings_service_proto.UpdateUserSettingsResponse, error) {
	args := m.Called(ctx, requestContext, userId, settings, expectedVersion)
	return args.Get(0).(*settings_service_proto.UpdateUserSettingsResponse), args.Error(1)
}

func (m *MockSettingsClient) Close() error {
	return nil
}

// Mock WebhookTenantMappingResource
type MockWebhookTenantMappingResource struct {
	mock.Mock
}

func (m *MockWebhookTenantMappingResource) Delete(ctx context.Context, name string) error {
	return nil
}

func (m *MockWebhookTenantMappingResource) Update(ctx context.Context, Spec *webhookmapping.WebhookTenantMappingSpec, name string, appName string) (*webhookmapping.WebhookTenantMapping, error) {
	args := m.Called(ctx, Spec, name, appName)
	return args.Get(0).(*webhookmapping.WebhookTenantMapping), args.Error(1)
}

func (m *MockWebhookTenantMappingResource) List(ctx context.Context, appName string) ([]webhookmapping.WebhookTenantMapping, error) {
	args := m.Called(ctx, appName)
	return args.Get(0).([]webhookmapping.WebhookTenantMapping), args.Error(1)
}

// Mock for RequestInsightPublisher

type MockRequestInsightPublisher struct {
	mock.Mock
}

func (m *MockRequestInsightPublisher) PublishRequestEvent(
	ctx context.Context,
	requestID string,
	tenantInfo *requestinsightproto.TenantInfo,
	event *requestinsightproto.RequestEvent,
) error {
	args := m.Called(ctx, requestID, tenantInfo, event)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishSessionEvent(
	ctx context.Context,
	sessionID string,
	opaqueUserID *authentitiesproto.UserId,
	tenantInfo *requestinsightproto.TenantInfo,
	event *requestinsightproto.SessionEvent,
) error {
	args := m.Called(ctx, sessionID, opaqueUserID, tenantInfo, event)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishTenantEvent(
	ctx context.Context,
	tenantInfo *requestinsightproto.TenantInfo,
	event *requestinsightproto.TenantEvent,
) error {
	args := m.Called(ctx, tenantInfo, event)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishGenericEvent(
	ctx context.Context,
	sessionID string,
	event *requestinsightproto.GenericEvent,
) error {
	args := m.Called(ctx, sessionID, event)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishRequestEvents(
	ctx context.Context,
	requestID string,
	tenantInfo *requestinsightproto.TenantInfo,
	events []*requestinsightproto.RequestEvent,
) error {
	args := m.Called(ctx, requestID, tenantInfo, events)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishSessionEvents(
	ctx context.Context,
	sessionID string,
	opaqueUserID *authentitiesproto.UserId,
	tenantInfo *requestinsightproto.TenantInfo,
	events []*requestinsightproto.SessionEvent,
) error {
	args := m.Called(ctx, sessionID, opaqueUserID, tenantInfo, events)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishTenantEvents(
	ctx context.Context,
	tenantInfo *requestinsightproto.TenantInfo,
	events []*requestinsightproto.TenantEvent,
) error {
	args := m.Called(ctx, tenantInfo, events)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishGenericEvents(
	ctx context.Context,
	sessionID string,
	events []*requestinsightproto.GenericEvent,
) error {
	args := m.Called(ctx, sessionID, events)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) Close() error {
	args := m.Called()
	return args.Error(0)
}

// Mock RoundTripper for http.Client
type MockRoundTripper struct {
	mock.Mock
}

func (rt *MockRoundTripper) RoundTrip(req *http.Request) (*http.Response, error) {
	args := rt.Called(req)
	return args.Get(0).(*http.Response), args.Error(1)
}

// Mock ModelFinderClient
type MockModelFinderClient struct {
	mock.Mock
	modelfinderproto.ModelFinderClient
}

func (m *MockModelFinderClient) GetInferenceModels(ctx context.Context, in *modelfinderproto.GetModelsRequest, opts ...grpc.CallOption) (*modelfinderproto.GetModelsResponse, error) {
	args := m.Called(ctx, in, opts)
	return args.Get(0).(*modelfinderproto.GetModelsResponse), args.Error(1)
}

// Mock ChatProtoClient
type MockChatProtoClient struct {
	mock.Mock
	chatproto.ChatClient
}

func (m *MockChatProtoClient) ChatStream(ctx context.Context, chatRequest *chatproto.ChatRequest, opts ...grpc.CallOption) (chatproto.Chat_ChatStreamClient, error) {
	args := m.Called(ctx, chatRequest)
	return args.Get(0).(chatproto.Chat_ChatStreamClient), args.Error(1)
}

// Mock ChatStreamClient
type MockChatStreamClient struct {
	chatproto.Chat_ChatStreamClient
}

func (m *MockChatStreamClient) Recv() (*chatproto.ChatResponse, error) {
	// closes stream immediately
	return nil, io.EOF
}

// Mock for TokenExchangeClient
type MockTokenExchangeClient struct {
	mock.Mock
}

func (m *MockTokenExchangeClient) GetSignedTokenForService(ctx context.Context, tenantID string, scopes []tokenexchangeproto.Scope) (secretstring.SecretString, error) {
	args := m.Called(ctx, tenantID, scopes)
	return secretstring.New(args.String(0)), args.Error(1)
}

func (m *MockTokenExchangeClient) GetSignedTokenForServiceWithNamespace(ctx context.Context, tenantID string, namespace string, scopes []tokenexchangeproto.Scope) (secretstring.SecretString, error) {
	args := m.Called(ctx, tenantID, namespace, scopes)
	return secretstring.New(args.String(0)), args.Error(1)
}

func (m *MockTokenExchangeClient) GetSignedTokenForUser(ctx context.Context, userID string, opaqueUserID *authentitiesproto.UserId, userEmail *string, tenantID string, namespace *string, additionalClaims map[string]any) (secretstring.SecretString, error) {
	args := m.Called(ctx, userID, opaqueUserID, userEmail, tenantID, namespace, additionalClaims)
	return secretstring.New(args.String(0)), args.Error(1)
}

func (m *MockTokenExchangeClient) GetSignedTokenForIAPToken(ctx context.Context, iapToken secretstring.SecretString, tenantID string, scopes []tokenexchangeproto.Scope, expiration time.Duration) (secretstring.SecretString, error) {
	args := m.Called(ctx, tenantID, iapToken, scopes, expiration)
	return secretstring.New(args.String(0)), args.Error(1)
}

func (m *MockTokenExchangeClient) GetVerificationKey(ctx context.Context) ([]byte, error) {
	args := m.Called(ctx)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockTokenExchangeClient) Close() {}

// Mock for GithubStateClient
var _ githubstateclient.GithubStateClient = &MockGithubStateClient{}

type MockGithubStateClient struct {
	mock.Mock
}

func (m *MockGithubStateClient) GetIndexedRefCheckpoints(ctx context.Context, requestContext *requestcontext.RequestContext) ([]*githubstatepersistproto.RefCheckpoint, error) {
	args := m.Called(ctx, requestContext)
	return args.Get(0).([]*githubstatepersistproto.RefCheckpoint), args.Error(1)
}

func (m *MockGithubStateClient) GetCurrentRefStates(ctx context.Context, requestContext *requestcontext.RequestContext) ([]*githubstatepersistproto.RefCheckpoint, error) {
	args := m.Called(ctx, requestContext)
	return args.Get(0).([]*githubstatepersistproto.RefCheckpoint), args.Error(1)
}

func (m *MockGithubStateClient) GetCurrentRefState(ctx context.Context, requestContext *requestcontext.RequestContext, githubRef *githubstatepersistproto.GithubRef) (*githubstatepersistproto.RefCheckpoint, error) {
	args := m.Called(ctx, requestContext, githubRef)
	return args.Get(0).(*githubstatepersistproto.RefCheckpoint), args.Error(1)
}

func (m *MockGithubStateClient) GetCurrentRefFiles(ctx context.Context, requestContext *requestcontext.RequestContext, githubRef *githubstatepersistproto.GithubRef, commitSha string, filePaths []string) (<-chan githubstateclient.GetCurrentRefFilesResult, error) {
	args := m.Called(ctx, requestContext, githubRef, commitSha, filePaths)
	return args.Get(0).(<-chan githubstateclient.GetCurrentRefFilesResult), args.Error(1)
}

func (m *MockGithubStateClient) UpdateCurrentRefCheckpoint(ctx context.Context, requestContext *requestcontext.RequestContext, githubRef *githubstatepersistproto.GithubRef, commitSha string, parentCommitShas []string, diffCommitSha string, commitTime *timestamppb.Timestamp, checkpointId *blobnamespb.Blobs, diffInfos []*githubstatepersistproto.DiffInfo, forceUpload bool) (*githubstatepersistproto.UpdateCurrentRefCheckpointResponse, error) {
	args := m.Called(ctx, requestContext, githubRef, commitSha, parentCommitShas, diffCommitSha, commitTime, checkpointId, diffInfos, forceUpload)
	return args.Get(0).(*githubstatepersistproto.UpdateCurrentRefCheckpointResponse), args.Error(1)
}

func (m *MockGithubStateClient) DeleteGithubStateForRepos(ctx context.Context, requestContext *requestcontext.RequestContext, repos []*githubstatepersistproto.GithubRepo) error {
	args := m.Called(ctx, requestContext, repos)
	return args.Error(0)
}

func (m *MockGithubStateClient) Close() {
	m.Called()
}

// Mock GleanClient
type MockGleanClient struct {
	mock.Mock
}

func (m *MockGleanClient) Search(ctx context.Context, requestContext *requestcontext.RequestContext, req *gleanproto.SearchRequest) (*gleanproto.SearchResponse, error) {
	args := m.Called(ctx, requestContext, req)
	return args.Get(0).(*gleanproto.SearchResponse), args.Error(1)
}

func (m *MockGleanClient) GetOAuthURL(ctx context.Context, requestContext *requestcontext.RequestContext) (string, error) {
	args := m.Called(ctx, requestContext)
	return args.String(0), args.Error(1)
}

func (m *MockGleanClient) IsToolConfigured(ctx context.Context, requestContext *requestcontext.RequestContext, userId string) (bool, error) {
	args := m.Called(ctx, requestContext, userId)
	return args.Bool(0), args.Error(1)
}

func (m *MockGleanClient) Close() {
	m.Called()
}

// Mock SlackUserMapping
type MockSlackUserMapping struct {
	mock.Mock
}

func (m *MockSlackUserMapping) StoreSlackUserMapping(ctx context.Context, requestContext *requestcontext.RequestContext, slackUserId string) error {
	args := m.Called(ctx, requestContext, slackUserId)
	return args.Error(0)
}

func (m *MockSlackUserMapping) GetAugmentUserId(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, slackUserId string) (string, error) {
	args := m.Called(ctx, requestContext, tenantID, slackUserId)
	return args.String(0), args.Error(1)
}

func (m *MockSlackUserMapping) ClearSlackUserMappingForTenant(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string) error {
	args := m.Called(ctx, requestContext, tenantID)
	return args.Error(0)
}
