package main

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/types/known/timestamppb"

	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
	githubstatepersistproto "github.com/augmentcode/augment/services/integrations/github/state/proto"
	slackbotproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
)

func TestGetRepos(t *testing.T) {
	// Setup
	ctx := context.Background()
	tenantID := "test-tenant"
	requestContext := &requestcontext.RequestContext{}

	channel := "test-channel"

	mockTenantCache := new(MockTenantCache)
	mockTokenExchangeClient := new(MockTokenExchangeClient)
	mockGithubStateClient := new(MockGithubStateClient)

	multiTenantLookup := NewMultiTenantLookup(
		mockTenantCache,
		mockGithubStateClient,
	)

	tenantSettingsResp := &settingsproto.GetTenantSettingsResponse{
		Settings: &settingsproto.TenantSettings{
			GithubSettings: &settingsproto.GithubSettings{
				InstallationId: 12345,
				Repos: []*settingsproto.RepoInformation{
					{RepoOwner: "owner1", RepoName: "repo1"},
					{RepoOwner: "owner2", RepoName: "repo2"},
				},
			},
			SlackSettings: &settingsproto.SlackSettings{
				ChannelMappings: &settingsproto.ChannelMappings{
					ChannelMappings: map[string]*settingsproto.RepoMapping{
						"test-channel": {
							Repos: []*settingsproto.RepoInformation{
								{RepoOwner: "owner1", RepoName: "repo1"},
							},
						},
					},
				},
			},
		},
	}

	mockTenantCache.On("GetTenant", tenantID).Return(&tenantproto.Tenant{Id: tenantID}, nil)

	// Mock expectations
	mockGithubStateClient.On("GetCurrentRefStates", mock.Anything, mock.Anything).Return([]*githubstatepersistproto.RefCheckpoint{
		{
			Ref: &githubstatepersistproto.GithubRef{
				Repo: &githubstatepersistproto.GithubRepo{
					RepoOwner: "owner1",
					RepoName:  "repo1",
				},
			},
			CommitSha:  "abc123",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
		},
	}, nil)
	mockGithubStateClient.On("GetIndexedRefCheckpoints", mock.Anything, mock.Anything).Return([]*githubstatepersistproto.RefCheckpoint{
		{
			Ref: &githubstatepersistproto.GithubRef{
				Repo: &githubstatepersistproto.GithubRepo{
					RepoOwner: "owner1",
					RepoName:  "repo1",
				},
			},
			CommitSha:  "def456",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
			Blobs:      &blobsproto.Blobs{},
		},
	}, nil)

	// Test
	currentRepos, currentCheckpoints, allRepos, installationId, err := multiTenantLookup.GetRepos(ctx, tenantSettingsResp.Settings, requestContext, channel, true)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, int64(12345), installationId)
	assert.Len(t, currentRepos, 1)
	assert.Len(t, currentCheckpoints, 1)
	assert.Len(t, allRepos, 2)

	assert.Equal(t, "owner1", currentRepos[0].RepoOwner)
	assert.Equal(t, "repo1", currentRepos[0].RepoName)
	assert.Equal(t, "abc123", currentRepos[0].CurrentCommitSha)
	assert.Equal(t, "def456", currentRepos[0].IndexedCommitSha)

	// Verify mocks
	mockTokenExchangeClient.AssertExpectations(t)
	mockGithubStateClient.AssertExpectations(t)
}

func TestGetRepos_DontUseChannel(t *testing.T) {
	// Setup
	ctx := context.Background()
	tenantID := "test-tenant"
	requestContext := &requestcontext.RequestContext{}

	channel := "test-channel"

	mockTenantCache := new(MockTenantCache)
	mockTokenExchangeClient := new(MockTokenExchangeClient)
	mockGithubStateClient := new(MockGithubStateClient)

	multiTenantLookup := NewMultiTenantLookup(
		mockTenantCache,
		mockGithubStateClient,
	)

	tenantSettingsResp := &settingsproto.GetTenantSettingsResponse{
		Settings: &settingsproto.TenantSettings{
			GithubSettings: &settingsproto.GithubSettings{
				InstallationId: 12345,
				Repos: []*settingsproto.RepoInformation{
					{RepoOwner: "owner1", RepoName: "repo1"},
					{RepoOwner: "owner2", RepoName: "repo2"},
				},
			},
			SlackSettings: &settingsproto.SlackSettings{},
		},
	}

	mockTenantCache.On("GetTenant", tenantID).Return(&tenantproto.Tenant{Id: tenantID}, nil)

	// Mock expectations
	mockGithubStateClient.On("GetCurrentRefStates", mock.Anything, mock.Anything).Return([]*githubstatepersistproto.RefCheckpoint{
		{
			Ref: &githubstatepersistproto.GithubRef{
				Repo: &githubstatepersistproto.GithubRepo{
					RepoOwner: "owner1",
					RepoName:  "repo1",
				},
			},
			CommitSha:  "abc123",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
		},
	}, nil)
	mockGithubStateClient.On("GetIndexedRefCheckpoints", mock.Anything, mock.Anything).Return([]*githubstatepersistproto.RefCheckpoint{
		{
			Ref: &githubstatepersistproto.GithubRef{
				Repo: &githubstatepersistproto.GithubRepo{
					RepoOwner: "owner1",
					RepoName:  "repo1",
				},
			},
			CommitSha:  "def456",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
			Blobs:      &blobsproto.Blobs{},
		},
	}, nil)

	// Test
	currentRepos, currentCheckpoints, allRepos, installationId, err := multiTenantLookup.GetRepos(ctx, tenantSettingsResp.Settings, requestContext, channel, false)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, int64(12345), installationId)
	assert.Len(t, currentRepos, 2) // this is the important part we are testing - both repos are included in current
	assert.Len(t, currentCheckpoints, 1)
	assert.Len(t, allRepos, 2)

	var repo1, repo2 *slackbotproto.Repo
	if currentRepos[0].RepoName == "repo1" {
		repo1 = currentRepos[0]
		repo2 = currentRepos[1]
	} else {
		repo1 = currentRepos[1]
		repo2 = currentRepos[0]
	}
	assert.Equal(t, "owner1", repo1.RepoOwner)
	assert.Equal(t, "repo1", repo1.RepoName)
	assert.Equal(t, "abc123", repo1.CurrentCommitSha)
	assert.Equal(t, "def456", repo1.IndexedCommitSha)
	assert.Equal(t, "owner2", repo2.RepoOwner)
	assert.Equal(t, "repo2", repo2.RepoName)

	// Verify mocks
	mockTokenExchangeClient.AssertExpectations(t)
	mockGithubStateClient.AssertExpectations(t)
}
