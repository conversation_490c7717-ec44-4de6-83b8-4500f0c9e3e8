package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strings"

	"github.com/augmentcode/augment/base/go/secretstring"
	publicapiproto "github.com/augmentcode/augment/services/api_proxy/public_api"
	githublib "github.com/augmentcode/augment/services/integrations/github/state/lib"
	"github.com/augmentcode/augment/services/integrations/slack_bot/common"
	processorproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	slackbotproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	slackboteventproto "github.com/augmentcode/augment/services/integrations/slack_bot/proto"
	"github.com/augmentcode/augment/services/integrations/webhookmapping"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	requestinsightproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	settings_service "github.com/augmentcode/augment/services/settings/client"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/slack-go/slack"
	statusproto "google.golang.org/genproto/googleapis/rpc/status"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type SlackProcessorServer struct {
	httpClient                   *http.Client
	settingsServiceClient        settings_service.SettingsClient
	basicCreds                   secretstring.SecretString
	slackCallbackUrl             string
	slackClientFactory           SlackClientFactory
	webhookTenantMappingResource webhookmapping.WebhookTenantMappingResource
	requestInsightPublisher      ripublisher.RequestInsightPublisher
	tenantLookup                 TenantLookup
	tokenExchangeClient          tokenexchangeclient.TokenExchangeClient
	slackUserMapping             SlackUserMapping
	userTier                     publicapiproto.GetModelsResponse_UserTier
}

type slackAccessToken struct {
	Ok          bool   `json:"ok"`
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	Scope       string `json:"scope"`
	BotUserId   string `json:"bot_user_id"`
	AppId       string `json:"app_id"`
	Team        struct {
		Name string `json:"name"`
		Id   string `json:"id"`
	} `json:"team"`
	Enterprise struct {
		Name string `json:"name"`
		Id   string `json:"id"`
	} `json:"enterprise"`
	AuthedUser struct {
		Id string `json:"id"`
	} `json:"authed_user"`
}

var slackInstallation = prometheus.NewCounterVec(
	prometheus.CounterOpts{
		Name: "au_slack_installation",
		Help: "Number of slack installations",
	},
	[]string{"tenant_id", "status"},
)

const (
	WELCOME_MESSAGE_INSTALLERS = `:wave: Thanks for installing Augment! I'm your friendly AI-powered coding buddy that understands your codebase.

:rocket: *Pro Tip: Add me to your engineering, dev, or team-specific channels now, so folks can start asking questions!*


:speech_balloon: *How Teams Can Use Me*
• DM me directly for 1:1 help
• Tag <@%[1]s> in any channel/thread
• Join ongoing discussions by tagging me in replies
• React with :+1: or :-1: or use the "Send feedback" slack message shortcut to provide feedback on my responses


:bulb: *Example Questions*
• "How does our auth system work?"
• "What's the best way to implement feature X?"
• _In a thread:_ "<@%[1]s> can you help answer Alice's question about the API?"


:clipboard: *Here's a quick announcement for your team:*

>Hey team! We just added <@%[1]s> to Slack - an AI assistant that understands our codebase. You can DM it directly or tag <@%[1]s> in any channel for help with code questions, debugging, or architecture discussions. Give it a try! :rocket:


:book: Learn more about my superpowers <https://docs.augmentcode.com/using-augment/slack|right here>!


Let's build something awesome together! :computer::sparkles:`
	WELCOME_MESSAGE_INSTALLERS_ALLOWLIST = `:wave: Thanks for installing Augment! I'm your friendly AI-powered coding buddy that understands your codebase.


_*Note*: Augment is currently configured to only respond in the following channels: %[2]s. We'd encourage you to add the bot now to all of these channels. Contact your admin or support if you'd like to enable Augment in more channels or DMs._


:speech_balloon: *How Teams Can Use Me*
• Tag <@%[1]s> in allowed channels
• Join ongoing discussions by tagging me in replies


:bulb: *Example Questions*
• "How does our auth system work?"
• "What's the best way to implement feature X?"
• _In a thread:_ "<@%[1]s> can you help answer Alice's question about the API?"


:clipboard: *Here's a quick announcement for your team:*

>Hey team! We just added <@%[1]s> to Slack - an AI assistant that understands our codebase. You can tag <@%[1]s> in this channel for help with code questions, debugging, or architecture discussions. Give it a try! :rocket:


:book: Learn more about my superpowers <https://docs.augmentcode.com/using-augment/slack|right here>!


Let's build something awesome together! :computer::sparkles:`
	NO_GITHUB_APP_WARNING = `:warning: *One More Step*
To unlock my full capabilities, please <https://docs.augmentcode.com/setup-augment/install-slack-app#1-install-github-app|install the Augment GitHub app>.
`

	REINSTALL_MESSAGE                 = `:wave: Thanks for reinstalling Augment!`
	SELECT_CHANNEL_MESSAGE            = `You have multiple repositories connected to Augment. Please select the ones you want to chat with in this %s.`
	SELECT_CHANNEL_ALREADY_CONFIGURED = `The Augment Slack Bot is currently configured to use the following repositories in this %s:

%s

`
	TOP_REPOS_LIST_MESSAGE = `The most commonly selected repos in your organization are:

%s

`
	HANDLE_REPO_SELECT_SUCCESS_MESSAGE_CHANNEL = `<@%s> has connected this channel to the repo(s) %s. Feel free to ask questions by mentioning <@%s>!`
	HANDLE_REPO_SELECT_SUCCESS_MESSAGE_DM      = `I'm now connected to the repo(s) %s. Feel free to ask me any questions!`
	HANDLE_REPO_SELECT_ERROR_MESSAGE           = `Sorry, there was an error processing your request. Please try again or contact support.`
	// max currently configured repos to show in repo selection modal before truncating
	MAX_REPOS_TO_DISPLAY_MODAL = 3
	// max repos to show as successfully connected to in success message before truncating
	MAX_REPOS_TO_DISPLAY_SUCCESS_MSG = 5
	// [SLACK LIMIT] max number of repos that can be shown in a slack menu
	MAX_REPOS_SLACK_MENU = 100
	// max repos to show as top repos in modal
	MAX_TOP_REPOS = 3
)

func init() {
	prometheus.MustRegister(slackInstallation)
}

func updateContextAnnotation(ctx context.Context, tenantID, tenantName string) context.Context {
	l := zerolog.Ctx(ctx)
	l.UpdateContext(func(c zerolog.Context) zerolog.Context {
		return c.Str("tenant_id", tenantID).Str("tenant_name", tenantName)
	})
	return ctx
}

func validateScope(ctx context.Context, requiredScopes []tokenexchangeproto.Scope) error {
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims from context")
		return fmt.Errorf("Unauthenticated")
	}

	for _, requiredScope := range requiredScopes {
		if !authInfo.HasScope(requiredScope) {
			log.Ctx(ctx).Error().Msgf("Missing required scope: %s", requiredScope)
			return fmt.Errorf("Missing required scope: %s", requiredScope)
		}
	}

	return nil
}

func (s *SlackProcessorServer) getSlackClient(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	tenantID string,
	scopes []tokenexchangeproto.Scope,
) (*requestcontext.RequestContext, *settingsproto.GetTenantSettingsResponse, SlackClient, error) {
	if len(scopes) > 0 {
		// The slack-bot-webhook service cannot get content scopes, so we may
		// need to do that here
		serviceToken, err := s.tokenExchangeClient.GetSignedTokenForService(ctx, tenantID, scopes)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to get new token")
			return requestContext, nil, nil, err
		}
		requestContext = requestContext.WithAuthToken(serviceToken)
	}

	tenantSettingsResp, err := s.settingsServiceClient.GetTenantSettings(ctx, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error getting tenant settings")
		return requestContext, nil, nil, err
	}

	slackClient, err := s.slackClientFactory.GetSlackClient(ctx, tenantID, tenantSettingsResp.Settings)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get slack client")
		return requestContext, nil, nil, err
	}

	return requestContext, tenantSettingsResp, slackClient, nil
}

func (s *SlackProcessorServer) HydrateSlackSettings(ctx context.Context, req *processorproto.HydrateSlackSettingsRequest) (*processorproto.HydrateSlackSettingsResponse, error) {
	if s.userTier != publicapiproto.GetModelsResponse_ENTERPRISE_TIER {
		log.Ctx(ctx).Error().Msgf("slack app installation is not supported in this tier: %s", s.userTier)
		return nil, fmt.Errorf("slack app installation is not supported in this tier: %s", s.userTier)
	}

	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting request context: %s", err)
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims from context")
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	if tenantID == "" {
		log.Ctx(ctx).Error().Msg("tenant_id is required")
		return nil, fmt.Errorf("tenant_id is required")
	}

	ctx = updateContextAnnotation(ctx, authInfo.TenantID, authInfo.TenantName)

	if !authInfo.HasScope(tokenexchangeproto.Scope_SETTINGS_RW) {
		log.Ctx(ctx).Error().Msg("Missing required scope")
		return nil, fmt.Errorf("Missing required scope")
	}

	// We normally don't log user IDs, but knowing who installed an integration
	// is particularly useful
	log.Ctx(ctx).Info().Msgf("HydrateSlackSettings from user %s", authInfo.UserID)

	tenantInfo := &requestinsightproto.TenantInfo{
		TenantId:   authInfo.TenantID,
		TenantName: authInfo.TenantName,
	}
	riEvent := ripublisher.NewRequestEvent()
	installEvent := &requestinsightproto.RISlackbotInstallationEvent{
		Status:    &statusproto.Status{},
		EventType: requestinsightproto.InstallEventType_INSTALL,
	}
	riEvent.Event = &requestinsightproto.RequestEvent_SlackbotInstallationEvent{
		SlackbotInstallationEvent: installEvent,
	}
	metadataEvent := ripublisher.NewRequestEvent()
	metadataEvent.Event = &requestinsightproto.RequestEvent_RequestMetadata{
		RequestMetadata: &requestinsightproto.RequestMetadata{
			// TODO: add a request type?
			// RequestType: requestinsightproto.RequestType_GITHUB_INSTALL,
			SessionId: requestContext.RequestSessionId.String(),
			UserId:    authInfo.UserID,
			UserAgent: "github-processor",
		},
	}
	events := []*requestinsightproto.RequestEvent{metadataEvent, riEvent}

	resp, err := s.hydrateSlackSettingsInner(ctx, req, tenantID, authInfo.TenantName)

	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to install slack bot")
		slackInstallation.WithLabelValues(tenantID, "ERROR").Inc()
		installEvent.Status.Code = int32(status.Code(err))
		installEvent.Status.Message = err.Error()
	} else {
		installEvent.Status.Code = int32(codes.OK)
		installEvent.Status.Message = "Success"
	}

	riErr := s.requestInsightPublisher.PublishRequestEvents(ctx, requestContext.RequestId.String(), tenantInfo, events)
	if riErr != nil {
		log.Ctx(ctx).Error().Err(riErr).Msg("Failed to publish request event")
		// we ignore this error as there is already a (potentially) more important error
	}

	if err != nil {
		return nil, err
	}
	slackInstallation.WithLabelValues(tenantID, "SUCCESS").Inc()
	return resp, nil
}

func (s *SlackProcessorServer) hydrateSlackSettingsInner(ctx context.Context, req *processorproto.HydrateSlackSettingsRequest, tenantID string, tenantName string) (*processorproto.HydrateSlackSettingsResponse, error) {
	// first, we exchange the temporary authorization code for a longer-lived slack access token
	code := req.GetCode()

	data := url.Values{}
	data.Set("code", code)
	data.Set("grant_type", "authorization_code")
	data.Set("redirect_uri", s.slackCallbackUrl)

	// ref: https://api.slack.com/methods/oauth.v2.access
	accessTokenReq, err := http.NewRequest("POST", "https://slack.com/api/oauth.v2.access", strings.NewReader(data.Encode()))
	if err != nil {
		return nil, err
	}
	accessTokenReq.Header.Set("Authorization", "Basic "+s.basicCreds.Expose())
	accessTokenReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	accessTokenResp, err := s.httpClient.Do(accessTokenReq)
	if err != nil {
		return nil, err
	}
	defer accessTokenResp.Body.Close()

	accessToken := slackAccessToken{}
	statusCode := accessTokenResp.StatusCode

	// We read out the bytes separately so we can log the raw response body in case of an error.
	bodyBytes, err := io.ReadAll(accessTokenResp.Body)
	if err != nil {
		return nil, fmt.Errorf("Error reading access token response: %v", err)
	}

	err = json.Unmarshal(bodyBytes, &accessToken)
	if err != nil {
		return nil, fmt.Errorf("Error parsing access token response: %v", err)
	}

	if !accessToken.Ok {
		return nil, fmt.Errorf("Error getting access token. Status code: %d, Response body: '%s'",
			statusCode,
			string(bodyBytes))
	}

	// first, check if we already have a slack workspace installed for this tenant,
	alreadyInstalled := false
	mappings, err := s.webhookTenantMappingResource.List(ctx, "slack-bot-webhook")
	if err != nil {
		return nil, err
	}
	for _, mapping := range mappings {
		if mapping.Spec.TenantID == tenantID {
			alreadyInstalled = true
			// clear the client cache if we reinstalling so we can get a new client with the new token
			s.slackClientFactory.ClearClientForTenant(tenantID)
			break
		}
	}

	// Check for assistant:write scope - the new Slack assistant workflow
	// This check needs to be kept until we release the new flow and all existing customers re-install
	scopes := strings.Split(accessToken.Scope, ",")
	hasAssistantWrite := false
	for _, scope := range scopes {
		if scope == "assistant:write" {
			hasAssistantWrite = true
			log.Ctx(ctx).Info().Msgf("Slack assistant:write scope detected")
			break
		}
	}

	// then we update the tenant's settings with the access token
	// note that the user token already has settings r/w scope, so we
	// can just pass the existing context through
	outgoingRequestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting outgoing request context: %s", err)
	}

	tenantSettingsResponse, err := s.settingsServiceClient.GetTenantSettings(ctx, outgoingRequestContext)
	if err != nil {
		return nil, fmt.Errorf("Error getting tenant settings: %s", err)
	}
	currentSettings := tenantSettingsResponse.GetSettings()
	// go proto unmarshalling sets empty fields to nil bc omitempty
	if currentSettings.SlackSettings == nil {
		currentSettings.SlackSettings = &settingsproto.SlackSettings{}
	}

	currentSettings.SlackSettings.EnterpriseId = accessToken.Enterprise.Id
	currentSettings.SlackSettings.TeamId = accessToken.Team.Id
	currentSettings.SlackSettings.OauthToken = accessToken.AccessToken
	currentSettings.SlackSettings.BotUserId = accessToken.BotUserId
	currentSettings.SlackSettings.AssistantFlowEnabled = hasAssistantWrite

	_, err = s.settingsServiceClient.UpdateTenantSettings(ctx, outgoingRequestContext, currentSettings, tenantSettingsResponse.Version)
	if err != nil {
		return nil, fmt.Errorf("Error updating tenant settings: %s", err)
	}

	slackClient, err := s.slackClientFactory.GetSlackClientFromToken(tenantID, tenantName, secretstring.New(accessToken.AccessToken), accessToken.BotUserId)
	if err != nil {
		return nil, fmt.Errorf("Error getting slack client: %s", err)
	}

	// finally, we upsert a webhook tenant mapping for the workspace
	// if there is already a mapping for this workspace, we will update it
	// intentionally doing this last so we don't create a webhook on an unsuccessful install.

	_, err = s.webhookTenantMappingResource.Update(ctx, &webhookmapping.WebhookTenantMappingSpec{
		WebhookType:  "slack",
		WebhookValue: fmt.Sprintf("%s-%s", accessToken.Enterprise.Id, accessToken.Team.Id),
		TenantID:     tenantID,
	},
		fmt.Sprintf("slack-%s", tenantID),
		"slack-bot-webhook",
	)
	if err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Successfully set up webhook mapping for tenant %s", tenantID)

	hasGithubApp := currentSettings.GithubSettings != nil && currentSettings.GithubSettings.InstallationId != 0

	tenantConfigs, err := s.tenantLookup.GetTenantConfigs(ctx, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get tenant configs")
	}

	allowListedChannels, err := getChannelAllowlistFromConfig(ctx, tenantConfigs)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get channel allowlist")
	}

	log.Ctx(ctx).Info().Msgf("Allowlisted channels: %v", allowListedChannels)

	welcomeMessage := ""
	if len(allowListedChannels) > 0 {
		welcomeMessage = fmt.Sprintf(WELCOME_MESSAGE_INSTALLERS_ALLOWLIST, accessToken.BotUserId, formatAllowlistChannelList(allowListedChannels))
	} else {
		welcomeMessage = fmt.Sprintf(WELCOME_MESSAGE_INSTALLERS, accessToken.BotUserId)
	}

	blocks := []slack.Block{}
	// send a welcome message to the user's direct messages channel
	if !alreadyInstalled {
		blocks = []slack.Block{
			slack.NewSectionBlock(
				slack.NewTextBlockObject("mrkdwn", welcomeMessage, false, false),
				nil,
				nil,
			),
		}
		if !hasGithubApp {
			blocks = append(blocks,
				slack.NewSectionBlock(
					slack.NewTextBlockObject("mrkdwn", fmt.Sprintf(NO_GITHUB_APP_WARNING), false, false),
					nil,
					nil,
				),
				installGithubAppButton,
			)
		}
	} else {
		blocks = []slack.Block{
			slack.NewSectionBlock(
				slack.NewTextBlockObject("mrkdwn", REINSTALL_MESSAGE, false, false),
				nil,
				nil,
			),
		}
	}
	_, _, err = slackClient.PostMessage(accessToken.AuthedUser.Id, slack.MsgOptionBlocks(blocks...), slack.MsgOptionDisableLinkUnfurl())
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to send welcome message")
		// don't return an error here, as this is not critical to the installation process
	} else {
		log.Ctx(ctx).Info().Msg("Successfully sent welcome message")
	}

	return &processorproto.HydrateSlackSettingsResponse{}, nil
}

// OpenRepoSelectModal opens a modal to select a repo for the given channel
// Specific configuration issues are returned in the response so we can send more context to the user. All other server errors are returned in the normal error object.
func (s *SlackProcessorServer) OpenRepoSelectModal(ctx context.Context, req *processorproto.OpenRepoSelectModalRequest) (*processorproto.OpenRepoSelectModalResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error getting outgoing request context")
		return &processorproto.OpenRepoSelectModalResponse{}, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	if req.Metadata == nil {
		log.Ctx(ctx).Error().Msg("Missing metadata")
		return &processorproto.OpenRepoSelectModalResponse{}, fmt.Errorf("Missing metadata")
	}

	if req.Metadata.TenantId == "" {
		log.Ctx(ctx).Error().Msg("Missing tenantID")
		return &processorproto.OpenRepoSelectModalResponse{}, fmt.Errorf("Missing tenantID")
	}

	tenantID := req.GetMetadata().GetTenantId()
	ctx = updateContextAnnotation(ctx, tenantID, req.GetMetadata().GetTenantName())
	log.Ctx(ctx).Info().Msg("OpenRepoSelectModal")

	requestContext, tenantSettingsResp, slackClient, err := s.getSlackClient(ctx, requestContext, tenantID, []tokenexchangeproto.Scope{
		// To read tenant settings
		tokenexchangeproto.Scope_SETTINGS_R,
		// To read from github state
		tokenexchangeproto.Scope_CONTENT_R,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get slack client")
		return &processorproto.OpenRepoSelectModalResponse{}, err
	}

	slackErrorType, err := s.openRepoSelectModalHelper(ctx, tenantSettingsResp.Settings, requestContext, slackClient, req.User, req.Channel, req.TriggerId, req.SlackEventToRegenerate, req.OriginalBotMessageTimestamp, req.ThreadTs)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to handle open repo select")
		return &processorproto.OpenRepoSelectModalResponse{}, err
	}
	if slackErrorType != nil {
		return &processorproto.OpenRepoSelectModalResponse{
			ErrorType: slackErrorType.ErrorType,
		}, nil
	}

	return &processorproto.OpenRepoSelectModalResponse{}, nil
}

func (s *SlackProcessorServer) getRepoMultiSelectMenu(ctx context.Context, allRepos []*settingsproto.RepoInformation, reposForContext []*slackbotproto.Repo) *slack.MultiSelectBlockElement {
	// Create the select menu options
	options := []*slack.OptionBlockObject{}

	// Sort allRepos by RepoName
	sort.Slice(allRepos, func(i, j int) bool {
		return allRepos[i].RepoName < allRepos[j].RepoName
	})

	for _, repo := range allRepos {
		// internal value of the option
		value := fmt.Sprintf("%s/%s", repo.RepoOwner, repo.RepoName)
		// display text of the option
		text := slack.NewTextBlockObject("plain_text", repo.RepoName, false, false)
		// description of the option displayed next to the option
		description := slack.NewTextBlockObject("plain_text", repo.RepoOwner, false, false)
		options = append(options, slack.NewOptionBlockObject(value, text, description))
	}

	curentlySelectedOptions := []*slack.OptionBlockObject{}
	for _, repo := range reposForContext {
		value := fmt.Sprintf("%s/%s", repo.RepoOwner, repo.RepoName)
		text := slack.NewTextBlockObject("plain_text", repo.RepoName, false, false)
		description := slack.NewTextBlockObject("plain_text", repo.RepoOwner, false, false)
		curentlySelectedOptions = append(curentlySelectedOptions, slack.NewOptionBlockObject(value, text, description))
	}

	// Create the multi-select menu
	multiSelectMenu := slack.NewOptionsMultiSelectBlockElement(
		slack.MultiOptTypeStatic,
		slack.NewTextBlockObject("plain_text", "Select repositories", false, false),
		common.ACTION_MULTI_SELECT,
	)

	// Show initially populated items
	multiSelectMenu.InitialOptions = curentlySelectedOptions

	// if there are more than 100 repos, we need to use an external data source
	if len(options) > MAX_REPOS_SLACK_MENU {
		log.Ctx(ctx).Info().Msgf("Found %d repos. Sending external data source modal", len(options))
		multiSelectMenu.Type = slack.MultiOptTypeExternal
	} else {
		multiSelectMenu.Options = options
	}
	return multiSelectMenu
}

func (s *SlackProcessorServer) createModalView(ctx context.Context, requestContext *requestcontext.RequestContext, slackClient SlackClient, userId string, channel string, reposForContext []*slackbotproto.Repo, allRepos []*settingsproto.RepoInformation, allReposButtonSelected bool, slackEventToRegenerate *slackboteventproto.SlackEvent, originalBotMessageTimestamp string, threadTimestamp string, topRepos []*settingsproto.RepoInformation) *slack.ModalViewRequest {
	var channelType string
	if channel[0] == 'D' {
		channelType = "direct message"
	} else {
		channelType = "channel"
	}

	var modalHeader string
	if len(reposForContext) > 0 {
		// sort reposForContext by RepoName
		sort.Slice(reposForContext, func(i, j int) bool {
			return reposForContext[i].RepoName < reposForContext[j].RepoName
		})
		modalHeader = formatConfiguredReposMessage(reposForContext, channelType)
	} else {
		modalHeader = fmt.Sprintf(SELECT_CHANNEL_MESSAGE, channelType)
	}

	var buttonBlock slack.Block
	var selectionBlock slack.Block
	var topReposBlock slack.Block

	if allReposButtonSelected {
		button := slack.NewButtonBlockElement(
			common.ACTION_DESELECT_ALL_BUTTON,
			common.ACTION_DESELECT_ALL_BUTTON,
			slack.NewTextBlockObject("plain_text", fmt.Sprintf("Unselect %d repos", len(allRepos)), false, false),
		)
		buttonBlock = slack.NewActionBlock(
			common.BLOCK_DESELECT_ALL,
			button,
		)
		selectionBlock = slack.NewSectionBlock(
			slack.NewTextBlockObject("mrkdwn", "*:warning: Selecting all repos. Press submit to continue, or unselect to choose individual repos.*", false, false),
			nil,
			nil,
		)
	} else {
		button := slack.NewButtonBlockElement(
			common.ACTION_SELECT_ALL_BUTTON,
			common.ACTION_SELECT_ALL_BUTTON,
			slack.NewTextBlockObject("plain_text", fmt.Sprintf(":warning: Select all %d repos", len(allRepos)), false, false),
		)
		buttonBlock = slack.NewActionBlock(
			common.BLOCK_SELECT_ALL,
			button,
		)
		// show a warning modal onclick if select all means selecting more than 3 repos
		if len(allRepos) > 3 {
			button.Confirm = slack.NewConfirmationBlockObject(
				slack.NewTextBlockObject("plain_text", fmt.Sprintf("Are you sure you want to select all %d repositories?", len(allRepos)), false, false),
				slack.NewTextBlockObject("plain_text", "We do not recommend selecting all repositories as it may impact performance. Consider choosing only the repos you need. You can always change your selection later.", false, false),
				slack.NewTextBlockObject("plain_text", "Select All", false, false),
				slack.NewTextBlockObject("plain_text", "Cancel", false, false),
			)
		}
		multiSelectMenu := s.getRepoMultiSelectMenu(ctx, allRepos, reposForContext)
		input := slack.NewInputBlock(
			common.BLOCK_REPO_SELECT_MULTI_SELECT,
			slack.NewTextBlockObject("plain_text", "Pick one or more repositories from the dropdown list or using the button to select all. We recommend selecting 1-3 repos.", false, false),
			nil,
			multiSelectMenu,
		)
		selectionBlock = input
	}

	// Create the block elements
	blocks := []slack.Block{
		slack.NewSectionBlock(
			slack.NewTextBlockObject("mrkdwn", modalHeader, false, false),
			nil,
			nil,
		),
		selectionBlock,
	}
	if len(topRepos) > 0 && !allReposButtonSelected {
		// We want to show the topReposBlock, because there are top repos and we have not selected all repos
		topReposBlock = slack.NewSectionBlock(
			slack.NewTextBlockObject("mrkdwn", formatTopCommonReposMessage(topRepos), false, false),
			nil,
			nil,
		)
		blocks = append(blocks, topReposBlock)
	}
	blocks = append(blocks,
		buttonBlock,
		slack.NewSectionBlock(
			slack.NewTextBlockObject("mrkdwn", " _Note: A confirmation message will be posted when you submit_", false, false),
			nil,
			nil,
		),
	)

	// Use private metadata to store information about the channel and any slack event we need to regenerate after repos are selected
	repoSelectMetadata := &processorproto.RepoSelectModalMetadata{
		Channel:                     channel,
		SlackEventToRegenerate:      slackEventToRegenerate,
		OriginalBotMessageTimestamp: originalBotMessageTimestamp,
		ThreadTs:                    threadTimestamp,
	}
	privateMetadata, err := common.EncodeProtoMetadata(repoSelectMetadata)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to encode metadata")
		return nil
	}

	// Create the view using blocks
	view := slack.ModalViewRequest{
		Type:            slack.ViewType("modal"),
		Title:           slack.NewTextBlockObject("plain_text", "Select Repositories", false, false),
		Close:           slack.NewTextBlockObject("plain_text", "Cancel", false, false),
		Submit:          slack.NewTextBlockObject("plain_text", "Submit", false, false),
		Blocks:          slack.Blocks{BlockSet: blocks},
		CallbackID:      common.CALLBACK_REPO_SELECT_MODAL,
		PrivateMetadata: privateMetadata,
	}
	return &view
}

// wrapper around the error type so we can return either the error or nil
type RepoModalError struct {
	ErrorType processorproto.OpenRepoSelectModalError
}

// Display interactive dialog to select repos
func (s *SlackProcessorServer) openRepoSelectModalHelper(
	ctx context.Context,
	tenantSettings *settingsproto.TenantSettings,
	requestContext *requestcontext.RequestContext,
	slackClient SlackClient,
	userId string,
	channel string,
	triggerId string,
	slackEventToRegenerate *slackboteventproto.SlackEvent,
	originalBotMessageTimestamp string,
	threadTimestamp string,
) (*RepoModalError, error) {
	// Check to see if the bot is in the channel by seeing if it has access to conversation info
	_, err := slackClient.GetConversationInfo(&slack.GetConversationInfoInput{ChannelID: channel})
	if err != nil {
		if err.Error() == "channel_not_found" {
			if channel[0] == 'C' {
				log.Ctx(ctx).Error().Msg("Bot not in channel")
				return &RepoModalError{ErrorType: processorproto.OpenRepoSelectModalError_BOT_NOT_IN_CHANNEL}, nil
			} else {
				log.Ctx(ctx).Error().Msg("Bot not in DM")
				return &RepoModalError{ErrorType: processorproto.OpenRepoSelectModalError_BOT_NOT_IN_DM}, nil
			}
		}
		return nil, err
	}

	// Get the repos for the given channel
	reposForContext, _, allRepos, installationId, err := s.tenantLookup.GetRepos(ctx, tenantSettings, requestContext, channel, true)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get repos")
		return nil, err
	}
	if installationId == 0 {
		log.Ctx(ctx).Info().Msg("No installation found.")
		return &RepoModalError{ErrorType: processorproto.OpenRepoSelectModalError_NO_GITHUB_APP}, nil
	}
	if len(allRepos) == 0 {
		log.Ctx(ctx).Info().Msg("No repos installed.")
		return &RepoModalError{ErrorType: processorproto.OpenRepoSelectModalError_NO_REPOS}, nil
	}

	var topRepos []*settingsproto.RepoInformation
	if tenantSettings == nil || tenantSettings.SlackSettings == nil || tenantSettings.SlackSettings.ChannelMappings == nil {
		// Protect against nil pointer errors
		log.Ctx(ctx).Error().Msg("No channel mappings found")
	} else {
		topRepos = s.GetMostUsedRepos(ctx, tenantSettings.SlackSettings.ChannelMappings)
	}
	view := s.createModalView(ctx, requestContext, slackClient, userId, channel, reposForContext, allRepos, false, slackEventToRegenerate, originalBotMessageTimestamp, threadTimestamp, topRepos)

	// Open the modal
	_, err = slackClient.OpenView(triggerId, *view)
	if err != nil {
		return nil, fmt.Errorf("Error opening modal: %s", err)
	}

	log.Ctx(ctx).Info().Msg("Successfully sent select modal")

	return nil, nil
}

func (s *SlackProcessorServer) GetMostUsedRepos(ctx context.Context, channelMappings *settingsproto.ChannelMappings) []*settingsproto.RepoInformation {
	// maps name:number channels used
	repoUseCount := make(map[string]int)
	// maps name:repo information
	repoInfoMap := make(map[string]*settingsproto.RepoInformation)

	for _, repoMapping := range channelMappings.ChannelMappings {
		for _, repo := range repoMapping.Repos {
			key := repo.RepoOwner + "/" + repo.RepoName
			repoUseCount[key]++
			repoInfoMap[key] = repo
		}
	}

	// Get all repos
	allRepos := make([]string, len(repoUseCount))
	i := 0
	for repo := range repoUseCount {
		allRepos[i] = repo
		i++
	}

	// Sort repos based on number of channels used, if tied, sort alphabetically
	sort.Slice(allRepos, func(i, j int) bool {
		if repoUseCount[allRepos[i]] == repoUseCount[allRepos[j]] {
			return allRepos[i] < allRepos[j]
		}
		return repoUseCount[allRepos[i]] > repoUseCount[allRepos[j]]
	})

	// Limit to Top Repos
	if len(allRepos) > MAX_TOP_REPOS {
		allRepos = allRepos[:MAX_TOP_REPOS]
	}

	// Get the full RepoInformation for top repos
	allReposInfo := make([]*settingsproto.RepoInformation, len(allRepos))
	for i, repo := range allRepos {
		allReposInfo[i] = repoInfoMap[repo]
	}

	return allReposInfo
}

func (s *SlackProcessorServer) HandleSelectAllReposAction(ctx context.Context, req *processorproto.HandleSelectAllReposActionRequest) (*processorproto.HandleSelectAllReposActionResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting outgoing request context: %s", err)
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	if req.Metadata == nil {
		log.Ctx(ctx).Error().Msg("Missing metadata")
		return nil, status.Error(codes.InvalidArgument, "Missing metadata")
	}

	if req.Metadata.TenantId == "" {
		log.Ctx(ctx).Error().Msg("Missing tenantID")
		return nil, status.Error(codes.InvalidArgument, "Missing tenantID")
	}

	tenantID := req.GetMetadata().GetTenantId()
	ctx = updateContextAnnotation(ctx, tenantID, req.GetMetadata().GetTenantName())
	log.Ctx(ctx).Info().Msg("HandleSelectAllReposAction")

	if req.ViewId == "" {
		log.Ctx(ctx).Error().Msg("Missing viewId")
		return nil, status.Error(codes.InvalidArgument, "Missing viewId")
	}
	if req.Channel == "" {
		log.Ctx(ctx).Error().Msg("Missing channel")
		return nil, status.Error(codes.InvalidArgument, "Missing channel")
	}

	log.Ctx(ctx).Info().Msgf("HandleSelectAllReposAction: viewId=%s", req.ViewId)

	requestContext, tenantSettingsResp, slackClient, err := s.getSlackClient(ctx, requestContext, tenantID, []tokenexchangeproto.Scope{
		// To read tenant settings
		tokenexchangeproto.Scope_SETTINGS_R,
		// To read from github state
		tokenexchangeproto.Scope_CONTENT_R,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get slack client")
		return nil, err
	}

	userId := req.User
	channel := req.Channel

	reposForContext, _, allRepos, installationId, err := s.tenantLookup.GetRepos(ctx, tenantSettingsResp.Settings, requestContext, channel, true)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get repos")
		return nil, err
	}
	if installationId == 0 {
		log.Ctx(ctx).Info().Msg("No installation found")
		return nil, fmt.Errorf("No installation found")
	}
	if len(allRepos) == 0 {
		log.Ctx(ctx).Info().Msg("No repos installed")
		return nil, fmt.Errorf("No repos installed")
	}

	var topRepos []*settingsproto.RepoInformation
	if tenantSettingsResp == nil || tenantSettingsResp.Settings == nil || tenantSettingsResp.Settings.SlackSettings == nil || tenantSettingsResp.Settings.SlackSettings.ChannelMappings == nil {
		// Protect against nil pointer errors
		log.Ctx(ctx).Error().Msg("No channel mappings found")
	} else {
		topRepos = s.GetMostUsedRepos(ctx, tenantSettingsResp.Settings.SlackSettings.ChannelMappings)
	}
	view := s.createModalView(ctx, requestContext, slackClient, userId, channel, reposForContext, allRepos, req.IsSelectAll, req.SlackEventToRegenerate, req.OriginalBotMessageTimestamp, req.ThreadTs, topRepos)

	_, err = slackClient.UpdateView(*view, "", "", req.ViewId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to update view")
		return nil, err
	}
	return &processorproto.HandleSelectAllReposActionResponse{}, nil
}

func (s *SlackProcessorServer) HandleRepoSelection(ctx context.Context, req *processorproto.HandleRepoSelectionRequest) (*processorproto.HandleRepoSelectionResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting outgoing request context: %s", err)
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	if req.Metadata == nil {
		log.Ctx(ctx).Error().Msg("Missing metadata")
		return nil, status.Error(codes.InvalidArgument, "Missing metadata")
	}

	if req.Metadata.TenantId == "" {
		log.Ctx(ctx).Error().Msg("Missing tenantID")
		return nil, status.Error(codes.InvalidArgument, "Missing tenantID")
	}

	tenantID := req.GetMetadata().GetTenantId()
	ctx = updateContextAnnotation(ctx, tenantID, req.GetMetadata().GetTenantName())
	log.Ctx(ctx).Info().Msg("HandleRepoSelection")

	if req.Channel == "" {
		log.Ctx(ctx).Error().Msg("Missing channel")
		return nil, status.Error(codes.InvalidArgument, "Missing channel")
	}
	if len(req.SelectedRepos) == 0 && !req.SelectAll {
		log.Ctx(ctx).Error().Msg("Missing selectedRepos")
		return nil, status.Error(codes.InvalidArgument, "Missing selectedRepos")
	}

	redactedSelectedRepos := ""
	if req.SelectAll {
		redactedSelectedRepos = "all"
	} else {
		for _, repo := range req.SelectedRepos {
			redactedSelectedRepos += githublib.RedactString(repo) + " "
		}
	}

	log.Ctx(ctx).Info().Msgf("HandleRepoSelection: channel=%s, repoName=%s", req.Channel, redactedSelectedRepos)

	requestContext, tenantSettingsResp, slackClient, err := s.getSlackClient(ctx, requestContext, tenantID, []tokenexchangeproto.Scope{
		// To read tenant settings, and update them
		tokenexchangeproto.Scope_SETTINGS_RW,
		// To read from github state
		tokenexchangeproto.Scope_CONTENT_R,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get slack client")
		return nil, err
	}

	repos, err := s.handleRepoSelectionHelper(ctx, tenantSettingsResp, requestContext, slackClient, req.User, req.Channel, req.SelectedRepos, req.SelectAll)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to handle repo selection")
		// post an error message to the user
		err = postMessage(ctx, slackClient, req.Channel, req.ThreadTs, "", req.User, slack.MsgOptionText(HANDLE_REPO_SELECT_ERROR_MESSAGE, false), true)
		return nil, err
	}

	// post a success message to the user
	var slackMessage slack.MsgOption
	threadTs := ""

	repoList := formatRepoListForChannelMsg(repos)
	if req.Channel[0] == 'C' {
		slackMessage = slack.MsgOptionText(fmt.Sprintf(HANDLE_REPO_SELECT_SUCCESS_MESSAGE_CHANNEL, req.User, repoList, slackClient.BotUserId()), false)
	} else {
		slackMessage = slack.MsgOptionText(fmt.Sprintf(HANDLE_REPO_SELECT_SUCCESS_MESSAGE_DM, repoList), false)
		// show repo-select success messages in the thread only if we are in an assistant view
		if tenantSettingsResp.Settings.SlackSettings.AssistantFlowEnabled {
			threadTs = req.ThreadTs
		}
	}

	err = postMessage(ctx, slackClient, req.Channel, threadTs, "", req.User, slackMessage, false)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to post success message")
		return nil, err
	}

	// if there was a bot message that led to opening this modal, delete it to make room for a new response
	if req.OriginalBotMessageTimestamp != "" {
		log.Ctx(ctx).Info().Msgf("Deleting original bot message with timestamp %s", req.OriginalBotMessageTimestamp)
		_, _, err = slackClient.DeleteMessage(req.Channel, req.OriginalBotMessageTimestamp)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to delete original bot message")
			return nil, err
		}
	}

	return &processorproto.HandleRepoSelectionResponse{}, nil
}

func (s *SlackProcessorServer) handleRepoSelectionHelper(ctx context.Context, tenantSettingsResp *settingsproto.GetTenantSettingsResponse, requestContext *requestcontext.RequestContext, slackClient SlackClient,
	userId string, channel string, fullRepoNames []string, selectAll bool,
) (repos []*settingsproto.RepoInformation, err error) {
	outgoingRequestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting outgoing request context: %s", err)
	}

	channelMap := make(map[string]*settingsproto.RepoMapping)
	if tenantSettingsResp.Settings.SlackSettings.ChannelMappings != nil {
		channelMap = tenantSettingsResp.Settings.SlackSettings.ChannelMappings.ChannelMappings
	}

	// use a new empty repo mapping for the channel
	// this will overwrite any existing channel mappings
	channelMap[channel] = &settingsproto.RepoMapping{}

	if selectAll {
		if tenantSettingsResp.Settings.GithubSettings == nil {
			return nil, fmt.Errorf("no github settings found for tenant")
		}
		log.Ctx(ctx).Info().Msgf("Selecting all %d repos for channel %s", len(tenantSettingsResp.Settings.GithubSettings.Repos), channel)
		channelMap[channel].Repos = tenantSettingsResp.Settings.GithubSettings.Repos
	} else {
		for _, fullRepoName := range fullRepoNames {

			parts := strings.SplitN(fullRepoName, "/", 2)

			if len(parts) != 2 {
				return nil, fmt.Errorf("Unexpected format for full repository name: %s", githublib.RedactString(fullRepoName))
			}

			repoOwner := parts[0]
			repoName := parts[1]

			channelMap[channel].Repos = append(channelMap[channel].Repos, &settingsproto.RepoInformation{
				RepoOwner: repoOwner,
				RepoName:  repoName,
			})
		}
	}

	tenantSettingsResp.Settings.SlackSettings.ChannelMappings = &settingsproto.ChannelMappings{
		ChannelMappings: channelMap,
	}

	_, err = s.settingsServiceClient.UpdateTenantSettings(ctx, outgoingRequestContext, tenantSettingsResp.Settings, tenantSettingsResp.Version)
	if err != nil {
		return channelMap[channel].Repos, fmt.Errorf("Error updating tenant settings: %s", err)
	}

	return channelMap[channel].Repos, nil
}

// creates a Slack-formatted link to a GitHub repository
func formatGithubRepoLink(owner, name string) string {
	return fmt.Sprintf("<https://github.com/%s/%s|`%s`>", owner, name, name)
}

// Format a list of Repos
func formatRepos(repos []*slackbotproto.Repo, maxRepos int) string {
	var repoList strings.Builder
	for i, repo := range repos {
		if i < maxRepos {
			repoList.WriteString(fmt.Sprintf("• %s\n", formatGithubRepoLink(repo.RepoOwner, repo.RepoName)))
		} else if i == maxRepos {
			remainingCount := len(repos) - maxRepos
			repoList.WriteString(fmt.Sprintf("     ... and %d more repos\n", remainingCount)) // spacing to left align with repo names
			break
		}
	}
	return repoList.String()
}

// Format repos that are already configured for a channel/message
func formatConfiguredReposMessage(repos []*slackbotproto.Repo, channelType string) string {
	return fmt.Sprintf(SELECT_CHANNEL_ALREADY_CONFIGURED, channelType, formatRepos(repos, MAX_REPOS_TO_DISPLAY_MODAL))
}

// Format repos that are the most commonly used
func formatTopCommonReposMessage(repos []*settingsproto.RepoInformation) string {
	// convert from settingsproto.RepoInformation to slackbotproto.Repo (same stuff inside)
	convertedRepos := make([]*slackbotproto.Repo, 0, len(repos))
	for _, repo := range repos {
		convertedRepos = append(convertedRepos, &slackbotproto.Repo{
			RepoOwner: repo.RepoOwner,
			RepoName:  repo.RepoName,
		})
	}
	return fmt.Sprintf(TOP_REPOS_LIST_MESSAGE, formatRepos(convertedRepos, MAX_TOP_REPOS))
}

// joins strings with commas and "and" before the last item
func joinWithCommasAndAnd(items []string) string {
	switch len(items) {
	case 0:
		return ""
	case 1:
		return items[0]
	case 2:
		return items[0] + " and " + items[1]
	default:
		return strings.Join(items[:len(items)-1], ", ") + ", and " + items[len(items)-1]
	}
}

func formatRepoListForChannelMsg(repos []*settingsproto.RepoInformation) string {
	formattedRepos := make([]string, 0, len(repos))
	for i, repo := range repos {
		if i >= MAX_REPOS_TO_DISPLAY_SUCCESS_MSG {
			remaining := len(repos) - MAX_REPOS_TO_DISPLAY_SUCCESS_MSG
			formattedRepos = append(formattedRepos, fmt.Sprintf("%d more repos", remaining))
			break
		}
		formattedRepos = append(formattedRepos, formatGithubRepoLink(repo.RepoOwner, repo.RepoName))
	}

	return joinWithCommasAndAnd(formattedRepos)
}

func (s *SlackProcessorServer) GetRepoOptions(ctx context.Context, req *processorproto.GetRepoOptionsRequest) (*processorproto.GetRepoOptionsResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting outgoing request context: %s", err)
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	if req.Metadata == nil {
		log.Ctx(ctx).Error().Msg("Missing metadata")
		return nil, status.Error(codes.InvalidArgument, "Missing metadata")
	}
	if req.Metadata.TenantId == "" {
		log.Ctx(ctx).Error().Msg("Missing tenantID")
		return nil, status.Error(codes.InvalidArgument, "Missing tenantID")
	}

	tenantID := req.GetMetadata().GetTenantId()
	ctx = updateContextAnnotation(ctx, tenantID, req.GetMetadata().GetTenantName())
	log.Ctx(ctx).Info().Msg("GetRepoOptions")

	requestContext, tenantSettingsResp, slackClient, err := s.getSlackClient(ctx, requestContext, tenantID, []tokenexchangeproto.Scope{
		// To read tenant settings
		tokenexchangeproto.Scope_SETTINGS_R,
		// To read from github state
		tokenexchangeproto.Scope_CONTENT_R,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get slack client")
		return nil, err
	}

	options, err := s.getRepoOptionsHelper(ctx, tenantSettingsResp.Settings, requestContext, req.SearchQuery, slackClient)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to handle get repo options")
		return nil, err
	}

	// reduce length of options to 100 (slack limit even for external data source)
	if len(options) > MAX_REPOS_SLACK_MENU {
		log.Ctx(ctx).Info().Msgf("Tenant %s has %d repos. Reducing to 100", tenantID, len(options))
		options = options[:MAX_REPOS_SLACK_MENU]
	}

	return &processorproto.GetRepoOptionsResponse{
		Options: options,
	}, nil
}

func (s *SlackProcessorServer) getRepoOptionsHelper(
	ctx context.Context,
	tenantSettings *settingsproto.TenantSettings,
	requestContext *requestcontext.RequestContext,
	searchQuery string,
	slackClient SlackClient,
) ([]*processorproto.OptionBlockObject, error) {
	_, _, allRepos, installationId, err := s.tenantLookup.GetRepos(ctx, tenantSettings, requestContext, "", false)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get repos")
		return nil, err
	}
	if installationId == 0 {
		log.Ctx(ctx).Info().Msg("No installation found.")
		return nil, nil
	}
	// Create the select menu options
	// Using internal OptionBlockObject instead of slack.OptionBlockObject to be able to serialize
	options := make([]*processorproto.OptionBlockObject, 0)

	for _, repo := range allRepos {
		if searchQuery != "" && !strings.Contains(strings.ToLower(repo.RepoName), strings.ToLower(searchQuery)) {
			continue
		}
		option := &processorproto.OptionBlockObject{
			Text: &processorproto.OptionBlockObject_TextBlockObject{
				Type: "plain_text",
				Text: repo.RepoName,
			},
			Description: &processorproto.OptionBlockObject_TextBlockObject{
				Type: "plain_text",
				Text: repo.RepoOwner,
			},
			Value: fmt.Sprintf("%s/%s", repo.RepoOwner, repo.RepoName),
		}
		options = append(options, option)
	}

	// sort by repo name
	sort.Slice(options, func(i, j int) bool {
		return options[i].Text.Text < options[j].Text.Text
	})

	return options, nil
}

func (s *SlackProcessorServer) OpenFeedbackModal(ctx context.Context, req *processorproto.OpenFeedbackModalRequest) (*processorproto.OpenFeedbackModalResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting outgoing request context: %s", err)
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	if err = validateScope(ctx, []tokenexchangeproto.Scope{
		// To read tenant settings
		tokenexchangeproto.Scope_SETTINGS_R,
	}); err != nil {
		return nil, err
	}

	if req.Metadata == nil {
		log.Ctx(ctx).Error().Msg("Missing metadata")
		return nil, status.Error(codes.InvalidArgument, "Missing metadata")
	}
	if req.Metadata.TenantId == "" {
		log.Ctx(ctx).Error().Msg("Missing tenantID")
		return nil, status.Error(codes.InvalidArgument, "Missing tenantID")
	}

	tenantID := req.GetMetadata().GetTenantId()
	ctx = updateContextAnnotation(ctx, tenantID, req.GetMetadata().GetTenantName())
	log.Ctx(ctx).Info().Msg("OpenFeedbackModal")

	if req.TriggerId == "" {
		log.Ctx(ctx).Error().Msg("Missing triggerId")
		return nil, status.Error(codes.InvalidArgument, "Missing triggerId")
	}

	if req.User == "" {
		log.Ctx(ctx).Error().Msg("Missing user")
		return nil, status.Error(codes.InvalidArgument, "Missing user")
	}

	if req.ModalMetadata == nil {
		log.Ctx(ctx).Error().Msg("Missing modalMetadata")
		return nil, status.Error(codes.InvalidArgument, "Missing modalMetadata")
	}

	if req.ModalMetadata.Channel == "" {
		log.Ctx(ctx).Error().Msg("Missing channel in modalMetadata")
		return nil, status.Error(codes.InvalidArgument, "Missing channel in modalMetadata")
	}

	if req.ModalMetadata.MessageTimestamp == "" {
		log.Ctx(ctx).Error().Msg("Missing messageTimestamp in modalMetadata")
		return nil, status.Error(codes.InvalidArgument, "Missing messageTimestamp in modalMetadata")
	}

	requestContext, _, slackClient, err := s.getSlackClient(ctx, requestContext, tenantID, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get slack client")
		return nil, err
	}

	err = s.handleOpenFeedbackModalHelper(ctx, tenantID, req, requestContext, slackClient)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to handle feedback modal request")
		return nil, err
	}

	return &processorproto.OpenFeedbackModalResponse{}, nil
}

func (s *SlackProcessorServer) handleOpenFeedbackModalHelper(ctx context.Context, tenantID string, req *processorproto.OpenFeedbackModalRequest, requestContext *requestcontext.RequestContext, slackClient SlackClient) error {
	log.Ctx(ctx).Info().Msgf("handleOpenFeedbackModalHelper: user=%s, messageTimestamp=%s, messageSender=%s", req.User, req.ModalMetadata.MessageTimestamp, req.ModalMetadata.Channel)

	// Only allow feedback for bot messages
	// This check should only happen when the openFeedbackModal request is from a message shortcut event. For requests through button actions, we've already checked this.
	if req.MessageSender != "" && req.MessageSender != slackClient.BotUserId() {
		log.Ctx(ctx).Error().Msg("Feedback modal request is not about a bot message")
		var err error
		if req.ModalMetadata.ThreadTimestamp != "" {
			_, err = slackClient.PostEphemeral(req.ModalMetadata.Channel, req.User, slack.MsgOptionText("Feedback is only accepted for bot messages.", false), slack.MsgOptionTS(req.ModalMetadata.ThreadTimestamp))
		} else {
			_, err = slackClient.PostEphemeral(req.ModalMetadata.Channel, req.User, slack.MsgOptionText("Feedback is only accepted for bot messages.", false))
		}
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to post ephemeral message")
			return err
		}
		return nil
	}

	sectionHeader := slack.NewSectionBlock(
		slack.NewTextBlockObject("plain_text", "Please share your thoughts below - we always want to improve your experience with Augment!", false, false),
		nil,
		nil,
	)

	radioButtonSelectRating := slack.NewInputBlock(
		common.BLOCK_FEEDBACK_RATING,
		slack.NewTextBlockObject("plain_text", "How was your experience?", false, false),
		nil,
		slack.NewRadioButtonsBlockElement(
			common.ACTION_FEEDBACK_RATING,
			[]*slack.OptionBlockObject{
				{
					Text:  slack.NewTextBlockObject("plain_text", ":thumbsup:  Positive", true, false),
					Value: "POSITIVE",
				},
				{
					Text:  slack.NewTextBlockObject("plain_text", ":thumbsdown:  Negative", true, false),
					Value: "NEGATIVE",
				},
			}...,
		),
	)
	inputElement := slack.NewPlainTextInputBlockElement(slack.NewTextBlockObject("plain_text", "Tell us more about your experience...", false, false), common.ACTION_FEEDBACK_NOTE)
	inputElement.Multiline = true

	textInput := slack.NewInputBlock(
		common.BLOCK_FEEDBACK_NOTE,
		slack.NewTextBlockObject("plain_text", "Additional feedback", false, false),
		nil,
		inputElement,
	)
	textInput.Optional = true

	privateMetadata, err := common.EncodeProtoMetadata(req.ModalMetadata)
	if err != nil {
		return fmt.Errorf("failed to encode modal metadata: %v", err)
	}

	modal := slack.ModalViewRequest{
		Type:   slack.ViewType("modal"),
		Title:  slack.NewTextBlockObject("plain_text", "Augment Feedback", false, false),
		Close:  slack.NewTextBlockObject("plain_text", "Cancel", false, false),
		Submit: slack.NewTextBlockObject("plain_text", "Submit", false, false),
		Blocks: slack.Blocks{BlockSet: []slack.Block{
			sectionHeader,
			radioButtonSelectRating,
			textInput,
		}},
		CallbackID:      common.CALLBACK_FEEDBACK_MODAL,
		PrivateMetadata: privateMetadata,
	}

	resp, err := slackClient.OpenView(req.TriggerId, modal)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to open modal %s", resp.Error)
		return fmt.Errorf("Error opening modal: %s", err)
	}

	return nil
}

func (s *SlackProcessorServer) HandleFeedbackEvent(ctx context.Context, req *processorproto.HandleFeedbackEventRequest) (*processorproto.HandleFeedbackEventResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting outgoing request context: %s", err)
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	if err = validateScope(ctx, []tokenexchangeproto.Scope{
		// To read tenant settings
		tokenexchangeproto.Scope_SETTINGS_R,
	}); err != nil {
		return nil, err
	}

	if req.Metadata == nil {
		log.Ctx(ctx).Error().Msg("Missing metadata")
		return nil, status.Error(codes.InvalidArgument, "Missing metadata")
	}

	if req.Metadata.TenantId == "" {
		log.Ctx(ctx).Error().Msg("Missing tenantID")
		return nil, status.Error(codes.InvalidArgument, "Missing tenantID")
	}

	tenantID := req.GetMetadata().GetTenantId()
	ctx = updateContextAnnotation(ctx, tenantID, req.GetMetadata().GetTenantName())
	log.Ctx(ctx).Info().Msg("HandleFeedbackEvent")

	if req.ModalPrivateMetadata == "" {
		log.Ctx(ctx).Error().Msg("Missing modalPrivateMetadata")
		return nil, status.Error(codes.InvalidArgument, "Missing modalPrivateMetadata")
	}

	if req.Rating == "" {
		log.Ctx(ctx).Error().Msg("Missing rating")
		return nil, status.Error(codes.InvalidArgument, "Missing rating")
	}

	if req.User == "" {
		log.Ctx(ctx).Error().Msg("Missing user")
		return nil, status.Error(codes.InvalidArgument, "Missing user")
	}

	requestContext, _, slackClient, err := s.getSlackClient(ctx, requestContext, tenantID, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get slack client")
		return nil, err
	}

	err = s.handleFeedbackEventHelper(ctx, tenantID, req, requestContext, slackClient)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to handle feedback event")
		return nil, err
	}

	return &processorproto.HandleFeedbackEventResponse{}, nil
}

func (s *SlackProcessorServer) handleFeedbackEventHelper(ctx context.Context, tenantID string, req *processorproto.HandleFeedbackEventRequest, requestContext *requestcontext.RequestContext, slackClient SlackClient) error {
	metadata := &processorproto.FeedbackModalMetadata{}
	err := common.DecodeProtoMetadata(req.ModalPrivateMetadata, metadata)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to decode modal metadata")
		return err
	}

	log.Ctx(ctx).Info().Msgf("handleFeedbackEventHelper: metadata=%v", metadata)

	tenantName, err := s.tenantLookup.GetTenantName(ctx, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get tenant name")
		return err
	}

	var feedbackRating requestinsightproto.FeedbackRating
	switch req.Rating {
	case "POSITIVE":
		feedbackRating = requestinsightproto.FeedbackRating_POSITIVE
	case "NEGATIVE":
		feedbackRating = requestinsightproto.FeedbackRating_NEGATIVE
	default:
		return fmt.Errorf("invalid rating: %s", req.Rating)
	}

	feedbackRiEvent := ripublisher.NewRequestEvent()
	feedbackRiEvent.Event = &requestinsightproto.RequestEvent_SlackbotFeedback{
		SlackbotFeedback: &requestinsightproto.SlackbotFeedback{
			SlackResponseTimestamp: metadata.MessageTimestamp,
			SlackChannelId:         metadata.Channel,
			Rating:                 feedbackRating,
			Note:                   req.Note,
		},
	}
	err = s.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), &requestinsightproto.TenantInfo{
		TenantId:   tenantID,
		TenantName: tenantName,
	}, feedbackRiEvent)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish request event")
		return err
	}

	log.Ctx(ctx).Info().Msgf("Published feedback request event")

	if metadata.ThreadTimestamp != "" {
		_, err = slackClient.PostEphemeral(metadata.Channel, req.User, slack.MsgOptionText("Thank you for your feedback!", false), slack.MsgOptionTS(metadata.ThreadTimestamp))
	} else {
		_, err = slackClient.PostEphemeral(metadata.Channel, req.User, slack.MsgOptionText("Thank you for your feedback!", false))
	}

	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to post ephemeral message")
		return err
	}

	return nil
}

func (s *SlackProcessorServer) LinkSlackUserToAugmentUser(ctx context.Context, req *processorproto.LinkSlackUserToAugmentUserRequest) (*processorproto.LinkSlackUserToAugmentUserResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting outgoing request context: %s", err)
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	if err := validateScope(ctx, []tokenexchangeproto.Scope{
		// To write tenant settings
		tokenexchangeproto.Scope_SETTINGS_RW,
	}); err != nil {
		return nil, err
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims from context")
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	if tenantID == "" {
		log.Ctx(ctx).Error().Msg("tenant_id is required")
		return nil, fmt.Errorf("tenant_id is required")
	}

	augmentUserId := authInfo.UserID
	if augmentUserId == "" {
		log.Ctx(ctx).Error().Msg("user_id is required")
		return nil, fmt.Errorf("user_id is required")
	}

	ctx = updateContextAnnotation(ctx, tenantID, authInfo.TenantName)
	log.Ctx(ctx).Info().Msgf("LinkSlackUserToAugmentUser")

	if req.SlackUserId == "" {
		log.Ctx(ctx).Error().Msg("slack_user_id is required")
		return nil, fmt.Errorf("slack_user_id is required")
	}

	err = s.slackUserMapping.StoreSlackUserMapping(ctx, requestContext, req.SlackUserId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to store slack user mapping")
		return nil, err
	}

	return &processorproto.LinkSlackUserToAugmentUserResponse{}, nil
}

func (s *SlackProcessorServer) ClearSlackUserToAugmentUserMapping(ctx context.Context, req *processorproto.ClearSlackUserToAugmentUserMappingRequest) (*processorproto.ClearSlackUserToAugmentUserMappingResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting outgoing request context: %s", err)
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	if err := validateScope(ctx, []tokenexchangeproto.Scope{
		// To write tenant settings
		tokenexchangeproto.Scope_SETTINGS_RW,
	}); err != nil {
		return nil, err
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims from context")
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	if tenantID == "" {
		log.Ctx(ctx).Error().Msg("tenant_id is required")
		return nil, fmt.Errorf("tenant_id is required")
	}

	ctx = updateContextAnnotation(ctx, tenantID, authInfo.TenantName)
	log.Ctx(ctx).Info().Msgf("ClearSlackUserToAugmentUserMapping")

	err = s.slackUserMapping.ClearSlackUserMappingForTenant(ctx, requestContext, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to clear slack user mapping")
		return nil, err
	}

	return &processorproto.ClearSlackUserToAugmentUserMappingResponse{}, nil
}
