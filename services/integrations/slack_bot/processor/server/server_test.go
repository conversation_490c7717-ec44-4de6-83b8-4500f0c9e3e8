package main

import (
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"strings"
	"testing"

	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
	"github.com/augmentcode/augment/base/go/secretstring"
	publicapiproto "github.com/augmentcode/augment/services/api_proxy/public_api"
	"github.com/augmentcode/augment/services/integrations/slack_bot/common"
	processorproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	slackboteventproto "github.com/augmentcode/augment/services/integrations/slack_bot/proto"
	webhookmapping "github.com/augmentcode/augment/services/integrations/webhookmapping"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestinsightproto "github.com/augmentcode/augment/services/request_insight/proto"
	settings_service_proto "github.com/augmentcode/augment/services/settings/proto"
	"github.com/slack-go/slack"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/proto"
)

func TestHydrateSlackSettings(t *testing.T) {
	claims := &auth.AugmentClaims{
		UserID:         "test-user-id",
		ServiceName:    "test-service-name",
		TenantID:       "test-tenant-id",
		TenantName:     "test-tenant-name",
		ShardNamespace: "test-shard-namespace",
		Scope:          []string{"SETTINGS_RW"},
	}
	ctx := claims.NewContext(context.Background())

	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{
		"authorization": "Bearer test-augment-auth-token",
	}))

	// set up mock settings client
	mockSettingsClientInst := new(MockSettingsClient)
	mockSettingsClientInst.On("GetTenantSettings", mock.Anything, mock.AnythingOfType("*requestcontext.RequestContext")).Return(&settings_service_proto.GetTenantSettingsResponse{
		Settings: &settings_service_proto.TenantSettings{
			SlackSettings: &settings_service_proto.SlackSettings{},
		},
	}, nil)
	mockSettingsClientInst.On("UpdateTenantSettings", mock.Anything, mock.AnythingOfType("*requestcontext.RequestContext"), &settings_service_proto.TenantSettings{
		SlackSettings: &settings_service_proto.SlackSettings{
			EnterpriseId: "test-enterprise-id",
			TeamId:       "test-team-id",
			OauthToken:   "test-oauth-token",
			BotUserId:    "test-bot-user-id",
		},
	}, "").Return(&settings_service_proto.UpdateTenantSettingsResponse{}, nil)

	// set up mock slack API
	mockRoundTripperInst := new(MockRoundTripper)
	mockRoundTripperInst.On("RoundTrip", mock.AnythingOfType("*http.Request")).Return(&http.Response{
		Body: io.NopCloser(strings.NewReader(`{
            "ok": true,
            "access_token": "test-oauth-token",
            "team": {"id": "test-team-id", "name": "Test Team"},
            "enterprise": {"id": "test-enterprise-id", "name": "Test Enterprise"},
			"bot_user_id": "test-bot-user-id"
        }`)),
		StatusCode: http.StatusOK,
	}, nil).Run(func(args mock.Arguments) {
		req := args.Get(0).(*http.Request)
		assert.Equal(t, "POST", req.Method)
		assert.Equal(t, "slack.com", req.URL.Host)
		assert.Equal(t, "/api/oauth.v2.access", req.URL.Path)
		assert.Equal(t, "Basic test-basic-creds", req.Header.Get("Authorization"))
		assert.Equal(t, "application/x-www-form-urlencoded", req.Header.Get("Content-Type"))

		body, _ := io.ReadAll(req.Body)
		expectedBody := "code=test-code&grant_type=authorization_code&redirect_uri=https%3A%2F%2Fexample.com%2FslackCallback"
		assert.Equal(t, expectedBody, string(body))
	}).Once()

	// set up mock webhook tenant mapping resource
	mockWebhookTenantMappingResourceInst := new(MockWebhookTenantMappingResource)
	mockWebhookTenantMappingResourceInst.On("List", mock.Anything, "slack-bot-webhook").Return([]webhookmapping.WebhookTenantMapping{}, nil).Once()
	mockWebhookTenantMappingResourceInst.On("Update", mock.Anything, &webhookmapping.WebhookTenantMappingSpec{
		WebhookType:  "slack",
		WebhookValue: "test-enterprise-id-test-team-id",
		TenantID:     "test-tenant-id",
	}, "slack-test-tenant-id", "slack-bot-webhook").Return(&webhookmapping.WebhookTenantMapping{
		Spec: webhookmapping.WebhookTenantMappingSpec{
			WebhookType:  "slack",
			WebhookValue: "test-enterprise-id-test-team-id",
			TenantID:     "test-tenant-id",
		},
	}, nil)

	// set up mock request insight publisher
	mockRequestInsightPublisher := new(MockRequestInsightPublisher)

	mockRequestInsightPublisher.On("PublishRequestEvents", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(nil)

	// set up mock slack client factory
	mockSlackClientFactory := new(MockSlackClientFactory)
	mockSlackClient := new(MockSlackClient)
	mockSlackClientFactory.On("GetSlackClientFromToken", "test-tenant-id", "test-tenant-name", secretstring.New("test-oauth-token"), "test-bot-user-id").Return(mockSlackClient, nil)
	mockSlackClient.On("PostMessage", mock.Anything, mock.Anything).Return("test-channel-id", "test-timestamp", nil)

	// set up mock tenant lookup
	mockTenantLookup := new(MockTenantLookup)
	mockTenantLookup.On("GetTenantConfigs", mock.Anything, "test-tenant-id").Return(map[string]string{}, nil)

	server := &SlackProcessorServer{
		httpClient:                   &http.Client{Transport: mockRoundTripperInst},
		settingsServiceClient:        mockSettingsClientInst,
		basicCreds:                   secretstring.New("test-basic-creds"),
		slackCallbackUrl:             "https://example.com/slackCallback",
		webhookTenantMappingResource: mockWebhookTenantMappingResourceInst,
		requestInsightPublisher:      mockRequestInsightPublisher,
		slackClientFactory:           mockSlackClientFactory,
		tenantLookup:                 mockTenantLookup,
		userTier:                     publicapiproto.GetModelsResponse_ENTERPRISE_TIER,
	}

	resp, err := server.HydrateSlackSettings(ctx, &processorproto.HydrateSlackSettingsRequest{
		Code: "test-code",
	})
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	if resp == nil {
		t.Errorf("Unexpected response: %v", resp)
	}

	mockTenantLookup.AssertExpectations(t)
	mockSettingsClientInst.AssertExpectations(t)
	mockRoundTripperInst.AssertExpectations(t)
	mockWebhookTenantMappingResourceInst.AssertExpectations(t)
}

func TestHydrateSlackSettings_CommunityTier(t *testing.T) {
	claims := &auth.AugmentClaims{
		UserID:         "test-user-id",
		ServiceName:    "test-service-name",
		TenantID:       "test-tenant-id",
		TenantName:     "test-tenant-name",
		ShardNamespace: "test-shard-namespace",
		Scope:          []string{"SETTINGS_RW"},
	}
	ctx := claims.NewContext(context.Background())
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{
		"authorization": "Bearer test-augment-auth-token",
	}))

	server := &SlackProcessorServer{
		userTier: publicapiproto.GetModelsResponse_COMMUNITY_TIER,
	}

	_, err := server.HydrateSlackSettings(ctx, &processorproto.HydrateSlackSettingsRequest{
		Code: "test-code",
	})
	if err == nil {
		t.Errorf("Expected error, but got nil")
	}
}

func TestOpenRepoSelectModal(t *testing.T) {
	tests := []struct {
		name          string
		repoCount     int
		expectedIsExt bool
		hasExisting   bool // whether there are existing top repos
	}{
		{
			name:          "less than 100 repos - static select menu",
			repoCount:     2,
			expectedIsExt: false,
			hasExisting:   false,
		},
		{
			name:          "more than 100 repos - external select menu",
			repoCount:     101,
			expectedIsExt: true,
			hasExisting:   false,
		},
		{
			name:          "existing top repos",
			repoCount:     3,
			expectedIsExt: false,
			hasExisting:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			settings := new(MockSettingsClient)
			slackClientFactory := new(MockSlackClientFactory)
			slackClient := new(MockSlackClient)
			tenantLookup := new(MockTenantLookup)
			tokenExchangeClient := new(MockTokenExchangeClient)

			server := &SlackProcessorServer{
				settingsServiceClient: settings,
				slackClientFactory:    slackClientFactory,
				tenantLookup:          tenantLookup,
				tokenExchangeClient:   tokenExchangeClient,
				userTier:              publicapiproto.GetModelsResponse_ENTERPRISE_TIER,
			}

			claims := &auth.AugmentClaims{
				UserID:         "test-user-id",
				ServiceName:    "test-service-name",
				TenantID:       "test-tenant-id",
				TenantName:     "test-tenant-name",
				ShardNamespace: "test-shard-namespace",
				Scope:          []string{"SETTINGS_RW"},
			}
			ctx := claims.NewContext(context.Background())
			ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{}))

			tokenExchangeClient.On("GetSignedTokenForService", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
				Once().Return("test-token", nil)
			if tt.hasExisting {
				settings.On("GetTenantSettings", mock.Anything, mock.Anything).Return(&settings_service_proto.GetTenantSettingsResponse{
					Settings: &settings_service_proto.TenantSettings{
						GithubSettings: &settings_service_proto.GithubSettings{
							Repos: []*settings_service_proto.RepoInformation{
								{
									RepoOwner: "test-owner",
									RepoName:  "test-repo",
								},
							},
						},
						// Add channel mappings to the tenant slack settings for other existing channels
						SlackSettings: &settings_service_proto.SlackSettings{
							ChannelMappings: &settings_service_proto.ChannelMappings{
								ChannelMappings: map[string]*settings_service_proto.RepoMapping{
									"other-channel": {
										Repos: []*settings_service_proto.RepoInformation{
											{
												RepoOwner: "test-owner",
												RepoName:  "test-repo2",
											},
											{
												RepoOwner: "test-owner",
												RepoName:  "test-repo1",
											},
										},
									},
									"another-channel": {
										Repos: []*settings_service_proto.RepoInformation{
											{
												RepoOwner: "test-owner",
												RepoName:  "test-repo2",
											},
										},
									},
									"this-channel": {
										Repos: []*settings_service_proto.RepoInformation{
											{
												RepoOwner: "test-owner",
												RepoName:  "test-repo0",
											},
										},
									},
								},
							},
						},
					},
				}, nil)
			} else {
				settings.On("GetTenantSettings", mock.Anything, mock.Anything).Return(&settings_service_proto.GetTenantSettingsResponse{
					Settings: &settings_service_proto.TenantSettings{
						GithubSettings: &settings_service_proto.GithubSettings{
							Repos: []*settings_service_proto.RepoInformation{
								{
									RepoOwner: "test-owner",
									RepoName:  "test-repo",
								},
							},
						},
					},
				}, nil)
			}
			slackClientFactory.On("GetSlackClient", mock.Anything, mock.Anything, mock.Anything).
				Return(slackClient, nil)

			slackClient.On("GetConversationInfo", mock.Anything).Return(&slack.Channel{}, nil)

			// Generate test repos
			repoInfos := make([]*settings_service_proto.RepoInformation, tt.repoCount)
			for i := 0; i < tt.repoCount; i++ {
				repoInfos[i] = &settings_service_proto.RepoInformation{
					RepoOwner: "test-owner",
					RepoName:  fmt.Sprintf("test-repo%d", i),
				}
			}

			currentRepos := []*processorproto.Repo{{
				RepoOwner: "test-owner",
				RepoName:  "test-repo0",
			}}

			blobs := []*blobsproto.Blobs{{
				BaselineCheckpointId: proto.String("test-checkpoint"),
			}}

			tenantLookup.On("GetRepos", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
				Return(currentRepos, blobs, repoInfos, int64(1), nil)

			modalHeader := "The Augment Slack Bot is currently configured to use the following repositories in this channel:\n\n• <https://github.com/test-owner/test-repo0|`test-repo0`>\n\n\n"

			var expectedOptions []*slack.OptionBlockObject
			if !tt.expectedIsExt {
				expectedOptions = make([]*slack.OptionBlockObject, tt.repoCount)
				for i := 0; i < tt.repoCount; i++ {
					expectedOptions[i] = &slack.OptionBlockObject{
						Text:        slack.NewTextBlockObject("plain_text", fmt.Sprintf("test-repo%d", i), false, false),
						Description: slack.NewTextBlockObject("plain_text", "test-owner", false, false),
						Value:       fmt.Sprintf("test-owner/test-repo%d", i),
					}
				}
			}

			expectedMultiSelectMenu := slack.NewOptionsMultiSelectBlockElement(
				slack.MultiOptTypeStatic,
				slack.NewTextBlockObject("plain_text", "Select repositories", false, false),
				common.ACTION_MULTI_SELECT,
			)

			if tt.expectedIsExt {
				expectedMultiSelectMenu.Type = slack.MultiOptTypeExternal
			} else {
				expectedMultiSelectMenu.Options = expectedOptions
			}

			expectedMultiSelectMenu.InitialOptions = []*slack.OptionBlockObject{
				{
					Text:        slack.NewTextBlockObject("plain_text", "test-repo0", false, false),
					Description: slack.NewTextBlockObject("plain_text", "test-owner", false, false),
					Value:       "test-owner/test-repo0",
				},
			}

			var button *slack.ButtonBlockElement

			if tt.repoCount > 3 {
				button = slack.NewButtonBlockElement(
					common.ACTION_SELECT_ALL_BUTTON,
					common.ACTION_SELECT_ALL_BUTTON,
					slack.NewTextBlockObject("plain_text", ":warning: Select all 101 repos", false, false),
				)
				button.Confirm = slack.NewConfirmationBlockObject(
					slack.NewTextBlockObject("plain_text", "Are you sure you want to select all 101 repositories?", false, false),
					slack.NewTextBlockObject("plain_text", "We do not recommend selecting all repositories as it may impact performance. Consider choosing only the repos you need. You can always change your selection later.", false, false),
					slack.NewTextBlockObject("plain_text", "Select All", false, false),
					slack.NewTextBlockObject("plain_text", "Cancel", false, false),
				)
			} else {
				button = slack.NewButtonBlockElement(
					common.ACTION_SELECT_ALL_BUTTON,
					common.ACTION_SELECT_ALL_BUTTON,
					slack.NewTextBlockObject("plain_text", fmt.Sprintf(":warning: Select all %d repos", tt.repoCount), false, false),
				)
			}

			repoSelectMetadata := &processorproto.RepoSelectModalMetadata{
				Channel:                     "C123",
				SlackEventToRegenerate:      &slackboteventproto.SlackEvent{},
				OriginalBotMessageTimestamp: "test-timestamp",
			}
			repoSelectMetadataBytes, err := proto.Marshal(repoSelectMetadata)
			assert.NoError(t, err)
			repoSelectMetadataEncoded := base64.StdEncoding.EncodeToString(repoSelectMetadataBytes)

			expectedBlocks := []slack.Block{
				slack.NewSectionBlock(
					slack.NewTextBlockObject("mrkdwn", modalHeader, false, false),
					nil,
					nil,
				),
				slack.NewInputBlock(
					common.BLOCK_REPO_SELECT_MULTI_SELECT,
					slack.NewTextBlockObject("plain_text", "Pick one or more repositories from the dropdown list or using the button to select all. We recommend selecting 1-3 repos.", false, false),
					nil,
					expectedMultiSelectMenu,
				),
			}
			if tt.hasExisting {
				expectedBlocks = append(expectedBlocks, slack.NewSectionBlock(
					slack.NewTextBlockObject("mrkdwn", "The most commonly selected repos in your organization are:\n\n• <https://github.com/test-owner/test-repo2|`test-repo2`>\n• <https://github.com/test-owner/test-repo0|`test-repo0`>\n• <https://github.com/test-owner/test-repo1|`test-repo1`>\n\n\n", false, false),
					nil,
					nil,
				))
			}
			expectedBlocks = append(expectedBlocks, []slack.Block{
				slack.NewActionBlock(
					common.BLOCK_SELECT_ALL,
					button,
				),
				slack.NewSectionBlock(
					slack.NewTextBlockObject("mrkdwn", " _Note: A confirmation message will be posted when you submit_", false, false),
					nil,
					nil,
				),
			}...)

			expectedView := slack.ModalViewRequest{
				Type:            slack.ViewType("modal"),
				Title:           slack.NewTextBlockObject("plain_text", "Select Repositories", false, false),
				Close:           slack.NewTextBlockObject("plain_text", "Cancel", false, false),
				Submit:          slack.NewTextBlockObject("plain_text", "Submit", false, false),
				Blocks:          slack.Blocks{BlockSet: expectedBlocks},
				CallbackID:      common.CALLBACK_REPO_SELECT_MODAL,
				PrivateMetadata: repoSelectMetadataEncoded,
			}

			slackClient.On("OpenView", mock.Anything, expectedView).
				Return(&slack.ViewResponse{}, nil)

			_, err = server.OpenRepoSelectModal(ctx, &processorproto.OpenRepoSelectModalRequest{
				User:      "test-user",
				Channel:   "C123",
				TriggerId: "test-trigger-id",
				Metadata: &slackboteventproto.EventMetadata{
					TeamId:       "test-team-id",
					EnterpriseId: "test-enterprise-id",
					TenantId:     "test-tenant-id",
					TenantName:   "test-tenant-name",
				},
				SlackEventToRegenerate:      &slackboteventproto.SlackEvent{},
				OriginalBotMessageTimestamp: "test-timestamp",
			})
			assert.NoError(t, err)

			slackClientFactory.AssertExpectations(t)
			slackClient.AssertExpectations(t)
			tenantLookup.AssertExpectations(t)
			tokenExchangeClient.AssertExpectations(t)
			settings.AssertExpectations(t)
		})
	}
}

func TestHandleRepoSelection(t *testing.T) {
	mockSettingsClientInst := new(MockSettingsClient)
	mockSlackClientFactory := new(MockSlackClientFactory)
	mockSlackClient := new(MockSlackClient)
	mockTenantLookup := new(MockTenantLookup)
	mockTokenExchangeClient := new(MockTokenExchangeClient)
	mockRequestInsightPublisher := new(MockRequestInsightPublisher)
	mockWebhookTenantMappingResourceInst := new(MockWebhookTenantMappingResource)

	// Set up server
	server := &SlackProcessorServer{
		settingsServiceClient:        mockSettingsClientInst,
		slackClientFactory:           mockSlackClientFactory,
		webhookTenantMappingResource: mockWebhookTenantMappingResourceInst,
		requestInsightPublisher:      mockRequestInsightPublisher,
		tenantLookup:                 mockTenantLookup,
		tokenExchangeClient:          mockTokenExchangeClient,
		userTier:                     publicapiproto.GetModelsResponse_ENTERPRISE_TIER,
	}

	// Set up context
	claims := &auth.AugmentClaims{
		UserID:         "test-user-id",
		ServiceName:    "test-service-name",
		TenantID:       "test-tenant-id",
		TenantName:     "test-tenant-name",
		ShardNamespace: "test-shard-namespace",
		Scope:          []string{"SETTINGS_RW"},
	}
	ctx := claims.NewContext(context.Background())
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{}))

	settings := &settings_service_proto.TenantSettings{
		SlackSettings: &settings_service_proto.SlackSettings{
			ChannelMappings: &settings_service_proto.ChannelMappings{
				ChannelMappings: map[string]*settings_service_proto.RepoMapping{
					"test-channel": {
						Repos: []*settings_service_proto.RepoInformation{
							{RepoOwner: "test-owner", RepoName: "test-repo1"},
						},
					},
				},
			},
		},
		GithubSettings: &settings_service_proto.GithubSettings{
			InstallationId: 12345,
			Repos: []*settings_service_proto.RepoInformation{
				{RepoOwner: "test-owner", RepoName: "test-repo1"},
				{RepoOwner: "test-owner", RepoName: "test-repo2"},
			},
		},
	}

	mockTokenExchangeClient.On("GetSignedTokenForService", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Once().Return("test-token", nil)
	mockSettingsClientInst.On("GetTenantSettings", mock.Anything, mock.Anything).Return(&settings_service_proto.GetTenantSettingsResponse{
		Settings: settings,
	}, nil)
	mockSlackClientFactory.On("GetSlackClient", mock.Anything, mock.Anything, mock.Anything).Return(mockSlackClient, nil)

	newChannelMap := map[string]*settings_service_proto.RepoMapping{
		"test-channel": {
			Repos: []*settings_service_proto.RepoInformation{
				{RepoOwner: "test-owner", RepoName: "test-repo1"},
				{RepoOwner: "test-owner", RepoName: "test-repo2"},
			},
		},
	}

	expectedSettings := proto.Clone(settings).(*settings_service_proto.TenantSettings)
	expectedSettings.SlackSettings.ChannelMappings.ChannelMappings = newChannelMap

	mockSettingsClientInst.On("UpdateTenantSettings", mock.Anything, mock.Anything, expectedSettings, mock.Anything).Return(&settings_service_proto.UpdateTenantSettingsResponse{}, nil)

	mockSlackClient.On("PostMessage", mock.Anything, mock.Anything).Return("test-channel-id", "test-timestamp", nil)

	// Set up request
	req := &processorproto.HandleRepoSelectionRequest{
		User:          "test-user",
		Channel:       "test-channel",
		SelectedRepos: []string{"test-owner/test-repo1", "test-owner/test-repo2"},
		Timestamp:     "test-timestamp",
		Metadata: &slackboteventproto.EventMetadata{
			TeamId:       "test-team-id",
			EnterpriseId: "test-enterprise-id",
			TenantId:     "test-tenant-id",
			TenantName:   "test-tenant-name",
		},
	}

	// Call the function
	resp, err := server.HandleRepoSelection(ctx, req)

	// Check the response and error
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	// Check the mock calls
	mockTenantLookup.AssertExpectations(t)
	mockTokenExchangeClient.AssertExpectations(t)
	mockSettingsClientInst.AssertExpectations(t)
	mockSlackClientFactory.AssertExpectations(t)
	mockSlackClient.AssertExpectations(t)
	mockRequestInsightPublisher.AssertExpectations(t)
	mockWebhookTenantMappingResourceInst.AssertExpectations(t)
}

func TestGetRepoOptions(t *testing.T) {
	mockSettingsClientInst := new(MockSettingsClient)
	mockSlackClientFactory := new(MockSlackClientFactory)
	mockSlackClient := new(MockSlackClient)
	mockTenantLookup := new(MockTenantLookup)
	mockTokenExchangeClient := new(MockTokenExchangeClient)
	mockRequestInsightPublisher := new(MockRequestInsightPublisher)
	mockWebhookTenantMappingResourceInst := new(MockWebhookTenantMappingResource)

	// Set up server
	server := &SlackProcessorServer{
		settingsServiceClient:        mockSettingsClientInst,
		slackClientFactory:           mockSlackClientFactory,
		webhookTenantMappingResource: mockWebhookTenantMappingResourceInst,
		requestInsightPublisher:      mockRequestInsightPublisher,
		tenantLookup:                 mockTenantLookup,
		tokenExchangeClient:          mockTokenExchangeClient,
		userTier:                     publicapiproto.GetModelsResponse_ENTERPRISE_TIER,
	}

	// Set up context
	claims := &auth.AugmentClaims{
		UserID:         "test-user-id",
		ServiceName:    "test-service-name",
		TenantID:       "test-tenant-id",
		TenantName:     "test-tenant-name",
		ShardNamespace: "test-shard-namespace",
		Scope:          []string{"SETTINGS_RW"},
	}
	ctx := claims.NewContext(context.Background())
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{}))

	mockTokenExchangeClient.On("GetSignedTokenForService", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Once().Return("test-token", nil)
	mockSettingsClientInst.On("GetTenantSettings", mock.Anything, mock.Anything).Return(&settings_service_proto.GetTenantSettingsResponse{
		Settings: &settings_service_proto.TenantSettings{
			GithubSettings: &settings_service_proto.GithubSettings{
				InstallationId: 12345,
			},
		},
	}, nil)
	mockSlackClientFactory.On("GetSlackClient", mock.Anything, mock.Anything, mock.Anything).
		Return(mockSlackClient, nil)

	// Generate test repos
	repoInfos := make([]*settings_service_proto.RepoInformation, 10)
	for i := 0; i < 10; i++ {
		repoInfos[i] = &settings_service_proto.RepoInformation{
			RepoOwner: "test-owner",
			RepoName:  fmt.Sprintf("test-repo%d", i),
		}
	}

	mockTenantLookup.On("GetRepos", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return([]*processorproto.Repo{}, []*blobsproto.Blobs{}, repoInfos, int64(1), nil)

	// Set up request
	req := &processorproto.GetRepoOptionsRequest{
		SearchQuery: "test",
		Metadata: &slackboteventproto.EventMetadata{
			TeamId:       "test-team-id",
			EnterpriseId: "test-enterprise-id",
			TenantId:     "test-tenant-id",
			TenantName:   "test-tenant-name",
		},
		Timestamp: "test-timestamp",
	}

	expectedOptions := make([]*processorproto.OptionBlockObject, 10)
	for i := 0; i < 10; i++ {
		expectedOptions[i] = &processorproto.OptionBlockObject{
			Text: &processorproto.OptionBlockObject_TextBlockObject{
				Type: "plain_text",
				Text: fmt.Sprintf("test-repo%d", i),
			},
			Description: &processorproto.OptionBlockObject_TextBlockObject{
				Type: "plain_text",
				Text: "test-owner",
			},
			Value: fmt.Sprintf("test-owner/test-repo%d", i),
		}
	}

	// Call the function
	resp, err := server.GetRepoOptions(ctx, req)

	// Check the response and error
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, expectedOptions, resp.Options)

	// Check the mock calls
	mockTenantLookup.AssertExpectations(t)
	mockTokenExchangeClient.AssertExpectations(t)
	mockSettingsClientInst.AssertExpectations(t)
	mockSlackClientFactory.AssertExpectations(t)
	mockSlackClient.AssertExpectations(t)
	mockRequestInsightPublisher.AssertExpectations(t)
	mockWebhookTenantMappingResourceInst.AssertExpectations(t)
}

func TestSelectAllReposButton(t *testing.T) {
	settings := new(MockSettingsClient)
	slackClientFactory := new(MockSlackClientFactory)
	slackClient := new(MockSlackClient)
	tenantLookup := new(MockTenantLookup)
	tokenExchangeClient := new(MockTokenExchangeClient)

	server := &SlackProcessorServer{
		settingsServiceClient: settings,
		slackClientFactory:    slackClientFactory,
		tenantLookup:          tenantLookup,
		tokenExchangeClient:   tokenExchangeClient,
		userTier:              publicapiproto.GetModelsResponse_ENTERPRISE_TIER,
	}

	claims := &auth.AugmentClaims{
		UserID:         "test-user-id",
		ServiceName:    "test-service-name",
		TenantID:       "test-tenant-id",
		TenantName:     "test-tenant-name",
		ShardNamespace: "test-shard-namespace",
		Scope:          []string{"SETTINGS_RW"},
	}
	ctx := claims.NewContext(context.Background())
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{}))

	tokenExchangeClient.On("GetSignedTokenForService", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Once().Return("test-token", nil)
	settings.On("GetTenantSettings", mock.Anything, mock.Anything).Return(&settings_service_proto.GetTenantSettingsResponse{
		Settings: &settings_service_proto.TenantSettings{
			GithubSettings: &settings_service_proto.GithubSettings{
				InstallationId: 12345,
			},
		},
	}, nil)
	slackClientFactory.On("GetSlackClient", mock.Anything, mock.Anything, mock.Anything).
		Return(slackClient, nil)

	// Generate test repos
	repoInfos := make([]*settings_service_proto.RepoInformation, 3)
	for i := 0; i < 3; i++ {
		repoInfos[i] = &settings_service_proto.RepoInformation{
			RepoOwner: "test-owner",
			RepoName:  fmt.Sprintf("test-repo%d", i),
		}
	}

	tenantLookup.On("GetRepos", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return([]*processorproto.Repo{}, []*blobsproto.Blobs{}, repoInfos, int64(1), nil)

	modalHeader := "You have multiple repositories connected to Augment. Please select the ones you want to chat with in this channel."

	repoSelectMetadata := &processorproto.RepoSelectModalMetadata{
		Channel: "C123",
	}
	repoSelectMetadataBytes, err := proto.Marshal(repoSelectMetadata)
	assert.NoError(t, err)
	repoSelectMetadataEncoded := base64.StdEncoding.EncodeToString(repoSelectMetadataBytes)

	expectedView := slack.ModalViewRequest{
		Type:   slack.ViewType("modal"),
		Title:  slack.NewTextBlockObject("plain_text", "Select Repositories", false, false),
		Close:  slack.NewTextBlockObject("plain_text", "Cancel", false, false),
		Submit: slack.NewTextBlockObject("plain_text", "Submit", false, false),
		Blocks: slack.Blocks{BlockSet: []slack.Block{
			slack.NewSectionBlock(
				slack.NewTextBlockObject("mrkdwn", modalHeader, false, false),
				nil,
				nil,
			),
			slack.NewSectionBlock(
				slack.NewTextBlockObject("mrkdwn", "*:warning: Selecting all repos. Press submit to continue, or unselect to choose individual repos.*", false, false),
				nil,
				nil,
			),
			slack.NewActionBlock(
				common.BLOCK_DESELECT_ALL,
				slack.NewButtonBlockElement(
					common.ACTION_DESELECT_ALL_BUTTON,
					common.ACTION_DESELECT_ALL_BUTTON,
					slack.NewTextBlockObject("plain_text", "Unselect 3 repos", false, false),
				),
			),
			slack.NewSectionBlock(
				slack.NewTextBlockObject("mrkdwn", " _Note: A confirmation message will be posted when you submit_", false, false),
				nil,
				nil,
			),
		}},
		CallbackID:      "repo_select_modal",
		PrivateMetadata: repoSelectMetadataEncoded,
	}

	slackClient.On("UpdateView", expectedView, mock.Anything, mock.Anything, mock.Anything).
		Return(&slack.ViewResponse{}, nil)

	_, err = server.HandleSelectAllReposAction(ctx, &processorproto.HandleSelectAllReposActionRequest{
		User:    "test-user",
		Channel: "C123",
		Metadata: &slackboteventproto.EventMetadata{
			TeamId:       "test-team-id",
			EnterpriseId: "test-enterprise-id",
			TenantId:     "test-tenant-id",
			TenantName:   "test-tenant-name",
		},
		ViewId:      "test-view-id",
		IsSelectAll: true,
	})
	assert.NoError(t, err)

	slackClientFactory.AssertExpectations(t)
	slackClient.AssertExpectations(t)
	tenantLookup.AssertExpectations(t)
	tokenExchangeClient.AssertExpectations(t)
	settings.AssertExpectations(t)
}

func TestOpenFeedbackModal(t *testing.T) {
	mockSettingsClientInst := new(MockSettingsClient)
	mockSlackClientFactory := new(MockSlackClientFactory)
	mockSlackClient := new(MockSlackClient)
	mockTenantLookup := new(MockTenantLookup)
	mockRequestInsightPublisher := new(MockRequestInsightPublisher)
	mockWebhookTenantMappingResourceInst := new(MockWebhookTenantMappingResource)

	// Set up server
	server := &SlackProcessorServer{
		settingsServiceClient:        mockSettingsClientInst,
		slackClientFactory:           mockSlackClientFactory,
		webhookTenantMappingResource: mockWebhookTenantMappingResourceInst,
		requestInsightPublisher:      mockRequestInsightPublisher,
		tenantLookup:                 mockTenantLookup,
		userTier:                     publicapiproto.GetModelsResponse_ENTERPRISE_TIER,
	}

	// Set up context
	claims := &auth.AugmentClaims{
		UserID:         "test-user-id",
		ServiceName:    "test-service-name",
		TenantID:       "test-tenant-id",
		TenantName:     "test-tenant-name",
		ShardNamespace: "test-shard-namespace",
		Scope:          []string{"SETTINGS_RW"},
	}
	ctx := claims.NewContext(context.Background())
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{}))

	mockSettingsClientInst.On("GetTenantSettings", mock.Anything, mock.Anything).Return(&settings_service_proto.GetTenantSettingsResponse{
		Settings: &settings_service_proto.TenantSettings{
			GithubSettings: &settings_service_proto.GithubSettings{
				InstallationId: 12345,
			},
		},
	}, nil)
	mockSlackClientFactory.On("GetSlackClient", mock.Anything, mock.Anything, mock.Anything).
		Return(mockSlackClient, nil)
	mockSlackClient.On("BotUserId").Return("test-bot-user-id")
	mockSlackClient.On("OpenView", mock.Anything, mock.Anything).Return(&slack.ViewResponse{}, nil)

	// Set up request
	req := &processorproto.OpenFeedbackModalRequest{
		User:          "test-user",
		MessageSender: "test-bot-user-id",
		TriggerId:     "test-trigger-id",
		Metadata: &slackboteventproto.EventMetadata{
			TeamId:       "test-team-id",
			EnterpriseId: "test-enterprise-id",
			TenantId:     "test-tenant-id",
			TenantName:   "test-tenant-name",
		},
		ModalMetadata: &processorproto.FeedbackModalMetadata{
			Channel:          "test-channel",
			MessageTimestamp: "test-message-timestamp",
			ThreadTimestamp:  "test-thread-timestamp",
		},
	}

	// Call the function
	resp, err := server.OpenFeedbackModal(ctx, req)

	// Check the response and error
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	// Check the mock calls
	mockTenantLookup.AssertExpectations(t)
	mockSettingsClientInst.AssertExpectations(t)
	mockSlackClientFactory.AssertExpectations(t)
	mockSlackClient.AssertExpectations(t)
	mockRequestInsightPublisher.AssertExpectations(t)
	mockWebhookTenantMappingResourceInst.AssertExpectations(t)
}

func TestOpenFeedbackModal_NotBotMessage(t *testing.T) {
	mockSettingsClientInst := new(MockSettingsClient)
	mockSlackClientFactory := new(MockSlackClientFactory)
	mockSlackClient := new(MockSlackClient)
	mockTenantLookup := new(MockTenantLookup)
	mockRequestInsightPublisher := new(MockRequestInsightPublisher)
	mockWebhookTenantMappingResourceInst := new(MockWebhookTenantMappingResource)

	// Set up server
	server := &SlackProcessorServer{
		settingsServiceClient:        mockSettingsClientInst,
		slackClientFactory:           mockSlackClientFactory,
		webhookTenantMappingResource: mockWebhookTenantMappingResourceInst,
		requestInsightPublisher:      mockRequestInsightPublisher,
		tenantLookup:                 mockTenantLookup,
		userTier:                     publicapiproto.GetModelsResponse_ENTERPRISE_TIER,
	}

	// Set up context
	claims := &auth.AugmentClaims{
		UserID:         "test-user-id",
		ServiceName:    "test-service-name",
		TenantID:       "test-tenant-id",
		TenantName:     "test-tenant-name",
		ShardNamespace: "test-shard-namespace",
		Scope:          []string{"SETTINGS_RW"},
	}
	ctx := claims.NewContext(context.Background())
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{}))

	mockSettingsClientInst.On("GetTenantSettings", mock.Anything, mock.Anything).Return(&settings_service_proto.GetTenantSettingsResponse{
		Settings: &settings_service_proto.TenantSettings{
			GithubSettings: &settings_service_proto.GithubSettings{
				InstallationId: 12345,
			},
		},
	}, nil)
	mockSlackClientFactory.On("GetSlackClient", mock.Anything, mock.Anything, mock.Anything).
		Return(mockSlackClient, nil)
	mockSlackClient.On("BotUserId").Return("test-bot-user-id")
	mockSlackClient.On("PostEphemeral", mock.Anything, mock.Anything, mock.Anything).Return("", nil)

	// Set up request
	req := &processorproto.OpenFeedbackModalRequest{
		User:          "test-user",
		MessageSender: "test-human-user-id",
		TriggerId:     "test-trigger-id",
		Metadata: &slackboteventproto.EventMetadata{
			TeamId:       "test-team-id",
			EnterpriseId: "test-enterprise-id",
			TenantId:     "test-tenant-id",
			TenantName:   "test-tenant-name",
		},
		ModalMetadata: &processorproto.FeedbackModalMetadata{
			Channel:          "test-channel",
			MessageTimestamp: "test-message-timestamp",
			ThreadTimestamp:  "test-thread-timestamp",
		},
	}

	// Call the function
	resp, err := server.OpenFeedbackModal(ctx, req)

	// Check the response and error
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	// Check the mock calls
	mockTenantLookup.AssertExpectations(t)
	mockSettingsClientInst.AssertExpectations(t)
	mockSlackClientFactory.AssertExpectations(t)
	mockSlackClient.AssertExpectations(t)
	mockRequestInsightPublisher.AssertExpectations(t)
	mockWebhookTenantMappingResourceInst.AssertExpectations(t)
}

func TestHandleFeedbackEvent(t *testing.T) {
	mockSettingsClientInst := new(MockSettingsClient)
	mockSlackClientFactory := new(MockSlackClientFactory)
	mockSlackClient := new(MockSlackClient)
	mockTenantLookup := new(MockTenantLookup)
	mockRequestInsightPublisher := new(MockRequestInsightPublisher)
	mockWebhookTenantMappingResourceInst := new(MockWebhookTenantMappingResource)

	// Set up server
	server := &SlackProcessorServer{
		settingsServiceClient:        mockSettingsClientInst,
		slackClientFactory:           mockSlackClientFactory,
		webhookTenantMappingResource: mockWebhookTenantMappingResourceInst,
		requestInsightPublisher:      mockRequestInsightPublisher,
		tenantLookup:                 mockTenantLookup,
		userTier:                     publicapiproto.GetModelsResponse_ENTERPRISE_TIER,
	}

	// Set up context
	claims := &auth.AugmentClaims{
		UserID:         "test-user-id",
		ServiceName:    "test-service-name",
		TenantID:       "test-tenant-id",
		TenantName:     "test-tenant-name",
		ShardNamespace: "test-shard-namespace",
		Scope:          []string{"SETTINGS_RW"},
	}
	ctx := claims.NewContext(context.Background())
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{}))

	mockTenantLookup.On("GetTenantName", mock.Anything, mock.Anything).Return("test-tenant-name", nil)

	mockSettingsClientInst.On("GetTenantSettings", mock.Anything, mock.Anything).Return(&settings_service_proto.GetTenantSettingsResponse{
		Settings: &settings_service_proto.TenantSettings{
			GithubSettings: &settings_service_proto.GithubSettings{
				InstallationId: 12345,
			},
		},
	}, nil)
	mockSlackClientFactory.On("GetSlackClient", mock.Anything, mock.Anything, mock.Anything).
		Return(mockSlackClient, nil)
	mockSlackClient.On("PostEphemeral", mock.Anything, mock.Anything, mock.Anything).Return("", nil)

	mockRequestInsightPublisher.On("PublishRequestEvent",
		mock.Anything,
		mock.Anything,
		mock.Anything,
		mock.MatchedBy(func(event *requestinsightproto.RequestEvent) bool {
			feedback := event.GetSlackbotFeedback()
			return feedback != nil &&
				feedback.SlackResponseTimestamp == "test-message-timestamp" &&
				feedback.SlackChannelId == "test-channel" &&
				feedback.Rating == requestinsightproto.FeedbackRating_POSITIVE &&
				feedback.Note == "test-note"
		}),
	).Return(nil)

	metadata := &processorproto.FeedbackModalMetadata{
		Channel:          "test-channel",
		MessageTimestamp: "test-message-timestamp",
		ThreadTimestamp:  "test-thread-timestamp",
	}
	metadataBytes, err := proto.Marshal(metadata)
	assert.NoError(t, err)

	metadataString := base64.StdEncoding.EncodeToString(metadataBytes)

	// Set up request
	req := &processorproto.HandleFeedbackEventRequest{
		ModalPrivateMetadata: metadataString,
		Rating:               "POSITIVE",
		Note:                 "test-note",
		User:                 "test-user",
		Metadata: &slackboteventproto.EventMetadata{
			TeamId:       "test-team-id",
			EnterpriseId: "test-enterprise-id",
			TenantId:     "test-tenant-id",
			TenantName:   "test-tenant-name",
		},
	}

	// Call the function
	resp, err := server.HandleFeedbackEvent(ctx, req)

	// Check the response and error
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	// Check the mock calls
	mockTenantLookup.AssertExpectations(t)
	mockSettingsClientInst.AssertExpectations(t)
	mockSlackClientFactory.AssertExpectations(t)
	mockSlackClient.AssertExpectations(t)
	mockRequestInsightPublisher.AssertExpectations(t)
	mockWebhookTenantMappingResourceInst.AssertExpectations(t)
}
