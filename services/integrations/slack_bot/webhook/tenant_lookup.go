package main

import (
	"context"
	"fmt"

	webhookmapping "github.com/augmentcode/augment/services/integrations/webhookmapping"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"

	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type Tenant struct {
	TenantID   string
	TenantName string
	Namespace  string
	Cloud      string
}

type TenantLookup interface {
	LookupTenant(ctx context.Context, enterpriseId string, teamId string) (*Tenant, error)
}

type nullTenantLookup struct{}

func NewNullTenantLookup() TenantLookup {
	return &nullTenantLookup{}
}

func (n *nullTenantLookup) LookupTenant(ctx context.Context, enterpriseId string, teamId string) (*Tenant, error) {
	log.Ctx(ctx).Info().Msgf("no matching tenant found for enterpriseId: %s, teamId: %s", enterpriseId, teamId)
	return nil, status.Errorf(codes.NotFound, "no matching tenant found")
}

type mappingTenantLookup struct {
	mappinginformer webhookmapping.WebhookMappingLookup
	tenantCache     tenantwatcherclient.TenantCache
}

func NewMappingTenantLookup(mappinginformer webhookmapping.WebhookMappingLookup, tenantCache tenantwatcherclient.TenantCache) TenantLookup {
	return &mappingTenantLookup{
		mappinginformer: mappinginformer,
		tenantCache:     tenantCache,
	}
}

func (m *mappingTenantLookup) LookupTenant(ctx context.Context, enterpriseId string, teamId string) (*Tenant, error) {
	v := fmt.Sprintf("%s-%s", enterpriseId, teamId)
	tenantID, err := m.mappinginformer.LookupTenant("slack", v)
	if err != nil {
		return nil, err
	}
	tenant, err := m.tenantCache.GetTenant(tenantID)
	if err != nil {
		return nil, err
	}
	return &Tenant{
		TenantID:   tenant.Id,
		TenantName: tenant.Name,
		Namespace:  tenant.ShardNamespace,
		Cloud:      tenant.Cloud,
	}, nil
}
