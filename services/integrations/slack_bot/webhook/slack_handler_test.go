package main

import (
	"context"
	"crypto/tls"
	"encoding/base64"
	"testing"
	"time"

	"github.com/slack-go/slack"
	"github.com/slack-go/slack/slackevents"
	"google.golang.org/grpc/credentials"
	"google.golang.org/protobuf/proto"

	"github.com/augmentcode/augment/base/go/secretstring"
	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	slackprocessorclient "github.com/augmentcode/augment/services/integrations/slack_bot/processor/client"
	processorproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	slackboteventproto "github.com/augmentcode/augment/services/integrations/slack_bot/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
)

// Mock for PubSubClient
type MockPubSubClient struct {
	LastEvent            []byte
	LastEventOrderingKey string
}

func (m *MockPubSubClient) Publish(ctx context.Context, namespace string, data []byte) error {
	m.LastEvent = data
	return nil
}

func (m *MockPubSubClient) PublishWithOrderingKey(ctx context.Context, namespace string, data []byte, orderingKey string) error {
	m.LastEvent = data
	m.LastEventOrderingKey = orderingKey
	return nil
}

func (m *MockPubSubClient) Close() error {
	return nil
}

// Mock for TenantLookup
type MockTenantLookup struct {
	LastEnterpriseId string
	LastTeamId       string
}

func (m *MockTenantLookup) LookupTenant(ctx context.Context, enterpriseId string, teamId string) (*Tenant, error) {
	m.LastEnterpriseId = enterpriseId
	m.LastTeamId = teamId
	return &Tenant{
		Namespace: "test-namespace",
		TenantID:  "test-tenant-id",
	}, nil
}

// Mock for SlackProcessorClient
type MockSlackProcessorClient struct {
	LastOpenRepoSelectModalRequest        *processorproto.OpenRepoSelectModalRequest
	LastHandleRepoSelectionRequest        *processorproto.HandleRepoSelectionRequest
	LastGetRepoOptionsRequest             *processorproto.GetRepoOptionsRequest
	LastHandleSelectAllReposActionRequest *processorproto.HandleSelectAllReposActionRequest
	LastHandleFeedbackEventRequest        *processorproto.HandleFeedbackEventRequest
	LastOpenFeedbackModalRequest          *processorproto.OpenFeedbackModalRequest
}

func (m *MockSlackProcessorClient) OpenRepoSelectModal(ctx context.Context, requestContext *requestcontext.RequestContext, req *processorproto.OpenRepoSelectModalRequest) (*processorproto.OpenRepoSelectModalResponse, error) {
	m.LastOpenRepoSelectModalRequest = req
	return &processorproto.OpenRepoSelectModalResponse{}, nil
}

func (m *MockSlackProcessorClient) HandleRepoSelection(ctx context.Context, requestContext *requestcontext.RequestContext, req *processorproto.HandleRepoSelectionRequest) (*processorproto.HandleRepoSelectionResponse, error) {
	m.LastHandleRepoSelectionRequest = req
	return &processorproto.HandleRepoSelectionResponse{}, nil
}

func (m *MockSlackProcessorClient) GetRepoOptions(ctx context.Context, requestContext *requestcontext.RequestContext, req *processorproto.GetRepoOptionsRequest) (*processorproto.GetRepoOptionsResponse, error) {
	m.LastGetRepoOptionsRequest = req
	return &processorproto.GetRepoOptionsResponse{}, nil
}

func (m *MockSlackProcessorClient) HandleSelectAllReposAction(ctx context.Context, requestContext *requestcontext.RequestContext, req *processorproto.HandleSelectAllReposActionRequest) (*processorproto.HandleSelectAllReposActionResponse, error) {
	m.LastHandleSelectAllReposActionRequest = req
	return &processorproto.HandleSelectAllReposActionResponse{}, nil
}

func (m *MockSlackProcessorClient) OpenFeedbackModal(ctx context.Context, requestContext *requestcontext.RequestContext, req *processorproto.OpenFeedbackModalRequest) (*processorproto.OpenFeedbackModalResponse, error) {
	m.LastOpenFeedbackModalRequest = req
	return &processorproto.OpenFeedbackModalResponse{}, nil
}

func (m *MockSlackProcessorClient) HandleFeedbackEvent(ctx context.Context, requestContext *requestcontext.RequestContext, req *processorproto.HandleFeedbackEventRequest) (*processorproto.HandleFeedbackEventResponse, error) {
	m.LastHandleFeedbackEventRequest = req
	return &processorproto.HandleFeedbackEventResponse{}, nil
}

func (m *MockSlackProcessorClient) Close() error {
	return nil
}

// Mock for TokenExchangeClient
type MockTokenExchangeClient struct{}

func (m *MockTokenExchangeClient) GetSignedTokenForService(ctx context.Context, tenantID string, scopes []tokenexchangeproto.Scope) (secretstring.SecretString, error) {
	return secretstring.New("test-token"), nil
}

func (m *MockTokenExchangeClient) GetSignedTokenForServiceWithNamespace(ctx context.Context, tenantID string, namespace string, scopes []tokenexchangeproto.Scope) (secretstring.SecretString, error) {
	return secretstring.New("test-token"), nil
}

func (m *MockTokenExchangeClient) GetSignedTokenForIAPToken(ctx context.Context, iapToken secretstring.SecretString, tenantID string, scopes []tokenexchangeproto.Scope, expiration time.Duration) (secretstring.SecretString, error) {
	return secretstring.New("test-token"), nil
}

func (m *MockTokenExchangeClient) GetSignedTokenForUser(ctx context.Context, userID string, opaqueUserID *authentitiesproto.UserId, userEmail *string, tenantID string, namespace *string, additionalClaims map[string]any) (secretstring.SecretString, error) {
	return secretstring.New("test-token"), nil
}

func (m *MockTokenExchangeClient) GetVerificationKey(ctx context.Context) ([]byte, error) {
	return []byte("test-key"), nil
}

func (m *MockTokenExchangeClient) Close() {}

func TestSlackEventHandler_AppMention(t *testing.T) {
	mockPubSubClient := &MockPubSubClient{}
	mockTenantLookup := &MockTenantLookup{}
	mockTokenExchangeClient := &MockTokenExchangeClient{}

	eventHandler := NewSlackEventHandler(mockPubSubClient, mockTenantLookup, mockTokenExchangeClient, "", nil, "", nil)

	innerEvent := slackevents.EventsAPIInnerEvent{
		Type: "app_mention",
		Data: &slackevents.AppMentionEvent{
			Text:            "Hello",
			EventTimeStamp:  "1234567890.123456",
			ThreadTimeStamp: "9876543210.987654",
		},
	}

	outerEvent := slackevents.EventsAPIEvent{
		InnerEvent: innerEvent,
		Data: slackevents.EventsAPICallbackEvent{
			EventTime: 1234567890,
		},
	}

	err := eventHandler.HandleEvent(context.Background(), "T123456789", "E123456789", &outerEvent)
	if err != nil {
		t.Fatal(err)
	}

	var lastEvent slackboteventproto.SlackEvent
	err = proto.Unmarshal(mockPubSubClient.LastEvent, &lastEvent)
	if err != nil {
		t.Fatal(err)
	}

	if lastEvent.EventType != "app_mention" {
		t.Fatalf("Unexpected event type: %s", lastEvent.EventType)
	}

	if lastEvent.Metadata.TeamId != "T123456789" {
		t.Fatalf("Unexpected team ID: %s", lastEvent.Metadata.TeamId)
	}

	if lastEvent.Metadata.EnterpriseId != "E123456789" {
		t.Fatalf("Unexpected enterprise ID: %s", lastEvent.Metadata.EnterpriseId)
	}

	if lastEvent.GetAppMention().Text != "Hello" {
		t.Fatalf("Unexpected text: %s", lastEvent.GetAppMention().Text)
	}
	if mockPubSubClient.LastEventOrderingKey != "9876543210.987654" {
		t.Fatalf("Unexpected ordering key: %s", mockPubSubClient.LastEventOrderingKey)
	}
}

func TestSlackEventHandler_Message(t *testing.T) {
	mockPubSubClient := &MockPubSubClient{}
	mockTenantLookup := &MockTenantLookup{}
	mockTokenExchangeClient := &MockTokenExchangeClient{}

	eventHandler := NewSlackEventHandler(mockPubSubClient, mockTenantLookup, mockTokenExchangeClient, "", nil, "", nil)

	innerEvent := slackevents.EventsAPIInnerEvent{
		Type: "message",
		Data: &slackevents.MessageEvent{
			Text:            "Hello",
			ChannelType:     "im",
			EventTimeStamp:  "1234567890.123456",
			ThreadTimeStamp: "9876543210.987654",
		},
	}

	outerEvent := slackevents.EventsAPIEvent{
		InnerEvent: innerEvent,
		Data: slackevents.EventsAPICallbackEvent{
			EventTime: 1234567890,
		},
	}

	err := eventHandler.HandleEvent(context.Background(), "T123456789", "E123456789", &outerEvent)
	if err != nil {
		t.Fatal(err)
	}

	lastEvent := &slackboteventproto.SlackEvent{}
	err = proto.Unmarshal(mockPubSubClient.LastEvent, lastEvent)
	if err != nil {
		t.Fatal(err)
	}

	if lastEvent.EventType != "message" {
		t.Fatalf("Unexpected event type: %s", lastEvent.EventType)
	}

	if lastEvent.Metadata.TeamId != "T123456789" {
		t.Fatalf("Unexpected team ID: %s", lastEvent.Metadata.TeamId)
	}

	if lastEvent.Metadata.EnterpriseId != "E123456789" {
		t.Fatalf("Unexpected enterprise ID: %s", lastEvent.Metadata.EnterpriseId)
	}

	if lastEvent.GetMessage().Text != "Hello" {
		t.Fatalf("Unexpected text: %s", lastEvent.GetMessage().Text)
	}
	if mockPubSubClient.LastEventOrderingKey != "9876543210.987654" {
		t.Fatalf("Unexpected ordering key: %s", mockPubSubClient.LastEventOrderingKey)
	}
}

func TestSlackEventHandler_MessageEdited(t *testing.T) {
	mockPubSubClient := &MockPubSubClient{}
	mockTenantLookup := &MockTenantLookup{}
	mockTokenExchangeClient := &MockTokenExchangeClient{}

	eventHandler := NewSlackEventHandler(mockPubSubClient, mockTenantLookup, mockTokenExchangeClient, "", nil, "", nil)

	innerEvent := slackevents.EventsAPIInnerEvent{
		Type: "message",
		Data: &slackevents.MessageEvent{
			Text:        "Hello",
			ChannelType: "im",
			Edited:      &slackevents.Edited{User: "U123456"},
		},
	}

	outerEvent := slackevents.EventsAPIEvent{
		InnerEvent: innerEvent,
		Data: slackevents.EventsAPICallbackEvent{
			EventTime: 1234567890,
		},
	}

	err := eventHandler.HandleEvent(context.Background(), "T123456789", "E123456789", &outerEvent)
	if err != nil {
		t.Fatal(err)
	}

	if mockPubSubClient.LastEvent != nil {
		t.Fatal("Expected no event to be published")
	}
}

func TestSlackEventHandler_ReactionAdded(t *testing.T) {
	mockPubSubClient := &MockPubSubClient{}
	mockTenantLookup := &MockTenantLookup{}
	mockTokenExchangeClient := &MockTokenExchangeClient{}

	eventHandler := NewSlackEventHandler(mockPubSubClient, mockTenantLookup, mockTokenExchangeClient, "", nil, "", nil)

	innerEvent := slackevents.EventsAPIInnerEvent{
		Type: "reaction_added",
		Data: &slackevents.ReactionAddedEvent{
			User:           "U123456",
			ItemUser:       "U654321",
			Reaction:       "thumbsup",
			Item:           slackevents.Item{Channel: "C123456", Timestamp: "1234567890.123456", Type: "message"},
			EventTimestamp: "2345678900.234567",
		},
	}

	outerEvent := slackevents.EventsAPIEvent{
		InnerEvent: innerEvent,
		Data: slackevents.EventsAPICallbackEvent{
			EventTime: 1234567890,
		},
	}

	err := eventHandler.HandleEvent(context.Background(), "T123456789", "E123456789", &outerEvent)
	if err != nil {
		t.Fatal(err)
	}

	var lastEvent slackboteventproto.SlackEvent
	err = proto.Unmarshal(mockPubSubClient.LastEvent, &lastEvent)
	if err != nil {
		t.Fatal(err)
	}

	if lastEvent.EventType != "reaction_added" {
		t.Fatalf("Unexpected event type: %s", lastEvent.EventType)
	}

	if lastEvent.Metadata.TeamId != "T123456789" {
		t.Fatalf("Unexpected team ID: %s", lastEvent.Metadata.TeamId)
	}

	if lastEvent.Metadata.EnterpriseId != "E123456789" {
		t.Fatalf("Unexpected enterprise ID: %s", lastEvent.Metadata.EnterpriseId)
	}
	if mockPubSubClient.LastEventOrderingKey != "2345678900.234567" {
		t.Fatalf("Unexpected ordering key: %s", mockPubSubClient.LastEventOrderingKey)
	}

	reactionAdded := lastEvent.GetReactionAdded()
	expectedReactionAdded := slackboteventproto.ReactionAddedEvent{
		User:           "U123456",
		ItemUser:       "U654321",
		Reaction:       "thumbsup",
		ItemChannel:    "C123456",
		ItemType:       "message",
		ItemTimestamp:  "1234567890.123456",
		EventTimestamp: "2345678900.234567",
	}
	if !proto.Equal(reactionAdded, &expectedReactionAdded) {
		t.Fatalf("Unexpected reaction added event: %v", reactionAdded)
	}
}

func TestSlackEventHandler_HandleOpenRepoSelection(t *testing.T) {
	mockPubSubClient := &MockPubSubClient{}
	mockTenantLookup := &MockTenantLookup{}
	mockTokenExchangeClient := &MockTokenExchangeClient{}
	mockSlackProcessorClient := &MockSlackProcessorClient{}

	clientCreds := credentials.NewTLS(&tls.Config{})

	eventHandler := &SlackEventHandlerImpl{
		pubSubClient:                      mockPubSubClient,
		tenantLookup:                      mockTenantLookup,
		tokenExchangeClient:               mockTokenExchangeClient,
		SlackBotProcessorEndpointTemplate: "test-endpoint%s",
		CloudDomainSuffixes:               map[string]string{"test-cloud": "test-suffix"},
		CurrentCloud:                      "test-cloud",
		clientCreds:                       clientCreds,
		createProcessorClientFunc: func(endpoint string, creds credentials.TransportCredentials) (slackprocessorclient.SlackProcessorClient, error) {
			if endpoint != "test-endpoint.test-namespace.t." {
				t.Fatalf("Unexpected endpoint: %s", endpoint)
			}
			if creds != clientCreds {
				t.Fatal("Unexpected creds")
			}
			return mockSlackProcessorClient, nil
		},
	}

	command := &slack.SlashCommand{
		Command:      "/augment",
		Text:         "repo-select",
		UserID:       "U123456",
		ChannelID:    "C123456",
		TeamID:       "T123456789",
		EnterpriseID: "E123456789",
		TriggerID:    "trigger123",
	}

	slackEvent := &slackboteventproto.SlackEvent{
		Metadata: &slackboteventproto.EventMetadata{
			TeamId:       "T123456789",
			EnterpriseId: "E123456789",
			TenantId:     "test-tenant-id",
			RequestId:    requestcontext.RequestId("test-request-id").String(),
		},
	}
	slackEventBytes, err := proto.Marshal(slackEvent)
	if err != nil {
		t.Fatal(err)
	}
	encodedSlackEvent := base64.StdEncoding.EncodeToString(slackEventBytes)

	errStr := eventHandler.HandleOpenRepoSelection(context.Background(), command.TeamID, command.EnterpriseID, command.UserID, command.ChannelID, command.TriggerID, encodedSlackEvent, "test-timestamp", "")
	if errStr != "" {
		t.Fatal(errStr)
	}

	if mockTenantLookup.LastEnterpriseId != "E123456789" || mockTenantLookup.LastTeamId != "T123456789" {
		t.Fatalf("Unexpected tenant lookup: %s, %s", mockTenantLookup.LastEnterpriseId, mockTenantLookup.LastTeamId)
	}

	if mockSlackProcessorClient.LastOpenRepoSelectModalRequest == nil {
		t.Fatal("Expected OpenRepoSelectModal to be called")
	}

	expectedRequest := &processorproto.OpenRepoSelectModalRequest{
		User:      "U123456",
		Channel:   "C123456",
		TriggerId: "trigger123",
		Metadata: &slackboteventproto.EventMetadata{
			TeamId:       "T123456789",
			EnterpriseId: "E123456789",
			TenantId:     "test-tenant-id",
			RequestId:    mockSlackProcessorClient.LastOpenRepoSelectModalRequest.Metadata.RequestId,
		},
		SlackEventToRegenerate:      slackEvent,
		OriginalBotMessageTimestamp: "test-timestamp",
	}

	if !proto.Equal(mockSlackProcessorClient.LastOpenRepoSelectModalRequest, expectedRequest) {
		t.Fatalf("Unexpected OpenRepoSelectModal request: %v", mockSlackProcessorClient.LastOpenRepoSelectModalRequest)
	}
}

func TestSlackEventHandler_HandleRepoSelectionEvent(t *testing.T) {
	mockPubSubClient := &MockPubSubClient{}
	mockTenantLookup := &MockTenantLookup{}
	mockTokenExchangeClient := &MockTokenExchangeClient{}
	mockSlackProcessorClient := &MockSlackProcessorClient{}

	clientCreds := credentials.NewTLS(&tls.Config{})

	eventHandler := &SlackEventHandlerImpl{
		pubSubClient:                      mockPubSubClient,
		tenantLookup:                      mockTenantLookup,
		tokenExchangeClient:               mockTokenExchangeClient,
		SlackBotProcessorEndpointTemplate: "test-endpoint%s",
		CloudDomainSuffixes:               map[string]string{"test-cloud": "test-suffix"},
		CurrentCloud:                      "test-cloud",
		clientCreds:                       clientCreds,
		createProcessorClientFunc: func(endpoint string, creds credentials.TransportCredentials) (slackprocessorclient.SlackProcessorClient, error) {
			if endpoint != "test-endpoint.test-namespace.t." {
				t.Fatalf("Unexpected endpoint: %s", endpoint)
			}
			if creds != clientCreds {
				t.Fatal("Unexpected creds")
			}
			return mockSlackProcessorClient, nil
		},
	}

	repoSelectMetadata := &processorproto.RepoSelectModalMetadata{
		Channel: "C123456",
	}
	repoSelectMetadataBytes, err := proto.Marshal(repoSelectMetadata)
	if err != nil {
		t.Fatal(err)
	}
	encodedRepoSelectMetadata := base64.StdEncoding.EncodeToString(repoSelectMetadataBytes)

	interactivityEvent := &slack.InteractionCallback{
		Type:      slack.InteractionTypeViewSubmission,
		User:      slack.User{ID: "U123456"},
		View:      slack.View{PrivateMetadata: encodedRepoSelectMetadata},
		Team:      slack.Team{ID: "T123456789"},
		ActionTs:  "1234567890.123456",
		TriggerID: "trigger123",
	}

	selectedRepos := []string{"example/repo"}

	err = eventHandler.HandleRepoSelectionEvent(context.Background(), interactivityEvent.Team.ID, "E123456789", interactivityEvent, selectedRepos, false)
	if err != nil {
		t.Fatal(err)
	}

	if mockTenantLookup.LastEnterpriseId != "E123456789" || mockTenantLookup.LastTeamId != "T123456789" {
		t.Fatalf("Unexpected tenant lookup: %s, %s", mockTenantLookup.LastEnterpriseId, mockTenantLookup.LastTeamId)
	}

	if mockSlackProcessorClient.LastHandleRepoSelectionRequest == nil {
		t.Fatal("Expected HandleRepoSelection to be called")
	}

	expectedRequest := &processorproto.HandleRepoSelectionRequest{
		User:          "U123456",
		Channel:       "C123456",
		SelectedRepos: selectedRepos,
		SelectAll:     false,
		Timestamp:     "1234567890.123456",
		Metadata: &slackboteventproto.EventMetadata{
			TeamId:       "T123456789",
			EnterpriseId: "E123456789",
			TenantId:     "test-tenant-id",
			RequestId:    mockSlackProcessorClient.LastHandleRepoSelectionRequest.Metadata.RequestId,
		},
	}

	if !proto.Equal(mockSlackProcessorClient.LastHandleRepoSelectionRequest, expectedRequest) {
		t.Fatalf("Unexpected HandleRepoSelection request: %v", mockSlackProcessorClient.LastHandleRepoSelectionRequest)
	}
}

func TestSlackEventHandler_HandleGetAllReposEvent(t *testing.T) {
	mockPubSubClient := &MockPubSubClient{}
	mockTenantLookup := &MockTenantLookup{}
	mockTokenExchangeClient := &MockTokenExchangeClient{}
	mockSlackProcessorClient := &MockSlackProcessorClient{}

	clientCreds := credentials.NewTLS(&tls.Config{})

	eventHandler := &SlackEventHandlerImpl{
		pubSubClient:                      mockPubSubClient,
		tenantLookup:                      mockTenantLookup,
		tokenExchangeClient:               mockTokenExchangeClient,
		SlackBotProcessorEndpointTemplate: "test-endpoint%s",
		CloudDomainSuffixes:               map[string]string{"test-cloud": "test-suffix"},
		CurrentCloud:                      "test-cloud",
		clientCreds:                       clientCreds,
		createProcessorClientFunc: func(endpoint string, creds credentials.TransportCredentials) (slackprocessorclient.SlackProcessorClient, error) {
			if endpoint != "test-endpoint.test-namespace.t." {
				t.Fatalf("Unexpected endpoint: %s", endpoint)
			}
			if creds != clientCreds {
				t.Fatal("Unexpected creds")
			}
			return mockSlackProcessorClient, nil
		},
	}
	interactivityEvent := &slack.InteractionCallback{
		Type:     slack.InteractionTypeBlockSuggestion,
		Value:    "test",
		User:     slack.User{ID: "U123456"},
		Team:     slack.Team{ID: "T123456789"},
		ActionTs: "1234567890.123456",
	}

	_, err := eventHandler.HandleGetAllReposEvent(context.Background(), interactivityEvent.Team.ID, "E123456789", interactivityEvent)
	if err != nil {
		t.Fatal(err)
	}

	if mockTenantLookup.LastEnterpriseId != "E123456789" || mockTenantLookup.LastTeamId != "T123456789" {
		t.Fatalf("Unexpected tenant lookup: %s, %s", mockTenantLookup.LastEnterpriseId, mockTenantLookup.LastTeamId)
	}

	expectedRequest := &processorproto.GetRepoOptionsRequest{
		SearchQuery: "test",
		Metadata: &slackboteventproto.EventMetadata{
			TeamId:       "T123456789",
			EnterpriseId: "E123456789",
			TenantId:     "test-tenant-id",
			RequestId:    mockSlackProcessorClient.LastGetRepoOptionsRequest.Metadata.RequestId,
		},
		Timestamp: "1234567890.123456",
	}

	if !proto.Equal(mockSlackProcessorClient.LastGetRepoOptionsRequest, expectedRequest) {
		t.Fatalf("Unexpected GetRepoOptions request: %v", mockSlackProcessorClient.LastGetRepoOptionsRequest)
	}
}

func TestSlackEventHandler_HandleSelectAllReposEvent(t *testing.T) {
	mockPubSubClient := &MockPubSubClient{}
	mockTenantLookup := &MockTenantLookup{}
	mockTokenExchangeClient := &MockTokenExchangeClient{}
	mockSlackProcessorClient := &MockSlackProcessorClient{}

	clientCreds := credentials.NewTLS(&tls.Config{})

	eventHandler := &SlackEventHandlerImpl{
		pubSubClient:                      mockPubSubClient,
		tenantLookup:                      mockTenantLookup,
		tokenExchangeClient:               mockTokenExchangeClient,
		SlackBotProcessorEndpointTemplate: "test-endpoint%s",
		CloudDomainSuffixes:               map[string]string{"test-cloud": "test-suffix"},
		CurrentCloud:                      "test-cloud",
		clientCreds:                       clientCreds,
		createProcessorClientFunc: func(endpoint string, creds credentials.TransportCredentials) (slackprocessorclient.SlackProcessorClient, error) {
			if endpoint != "test-endpoint.test-namespace.t." {
				t.Fatalf("Unexpected endpoint: %s", endpoint)
			}
			if creds != clientCreds {
				t.Fatal("Unexpected creds")
			}
			return mockSlackProcessorClient, nil
		},
	}
	repoSelectMetadata := &processorproto.RepoSelectModalMetadata{
		Channel: "C123456",
	}
	repoSelectMetadataBytes, err := proto.Marshal(repoSelectMetadata)
	if err != nil {
		t.Fatal(err)
	}
	encodedRepoSelectMetadata := base64.StdEncoding.EncodeToString(repoSelectMetadataBytes)

	interactivityEvent := &slack.InteractionCallback{
		Type:     slack.InteractionTypeViewSubmission,
		User:     slack.User{ID: "U123456"},
		View:     slack.View{ID: "V123456", PrivateMetadata: encodedRepoSelectMetadata},
		Team:     slack.Team{ID: "T123456789"},
		ActionTs: "1234567890.123456",
	}

	err = eventHandler.HandleSelectAllReposEvent(context.Background(), interactivityEvent.Team.ID, "E123456789", interactivityEvent, true)
	if err != nil {
		t.Fatal(err)
	}

	if mockTenantLookup.LastEnterpriseId != "E123456789" || mockTenantLookup.LastTeamId != "T123456789" {
		t.Fatalf("Unexpected tenant lookup: %s, %s", mockTenantLookup.LastEnterpriseId, mockTenantLookup.LastTeamId)
	}

	if mockSlackProcessorClient.LastHandleSelectAllReposActionRequest == nil {
		t.Fatal("Expected HandleSelectAllReposAction to be called")
	}

	expectedRequest := &processorproto.HandleSelectAllReposActionRequest{
		User:        "U123456",
		Channel:     "C123456",
		IsSelectAll: true,
		Metadata: &slackboteventproto.EventMetadata{
			TeamId:       "T123456789",
			EnterpriseId: "E123456789",
			TenantId:     "test-tenant-id",
			RequestId:    mockSlackProcessorClient.LastHandleSelectAllReposActionRequest.Metadata.RequestId,
		},
		ViewId: "V123456",
	}

	if !proto.Equal(mockSlackProcessorClient.LastHandleSelectAllReposActionRequest, expectedRequest) {
		t.Fatalf("Unexpected HandleSelectAllReposAction request: %v", mockSlackProcessorClient.LastHandleSelectAllReposActionRequest)
	}
}

func TestSlackEventHandler_HandleFeedbackEvent(t *testing.T) {
	mockPubSubClient := &MockPubSubClient{}
	mockTenantLookup := &MockTenantLookup{}
	mockTokenExchangeClient := &MockTokenExchangeClient{}
	mockSlackProcessorClient := &MockSlackProcessorClient{}

	clientCreds := credentials.NewTLS(&tls.Config{})

	eventHandler := &SlackEventHandlerImpl{
		pubSubClient:                      mockPubSubClient,
		tenantLookup:                      mockTenantLookup,
		tokenExchangeClient:               mockTokenExchangeClient,
		SlackBotProcessorEndpointTemplate: "test-endpoint%s",
		CloudDomainSuffixes:               map[string]string{"test-cloud": "test-suffix"},
		CurrentCloud:                      "test-cloud",
		clientCreds:                       clientCreds,
		createProcessorClientFunc: func(endpoint string, creds credentials.TransportCredentials) (slackprocessorclient.SlackProcessorClient, error) {
			if endpoint != "test-endpoint.test-namespace.t." {
				t.Fatalf("Unexpected endpoint: %s", endpoint)
			}
			if creds != clientCreds {
				t.Fatal("Unexpected creds")
			}
			return mockSlackProcessorClient, nil
		},
	}

	interactivityEvent := &slack.InteractionCallback{
		Type:      slack.InteractionTypeViewSubmission,
		User:      slack.User{ID: "U123456"},
		View:      slack.View{PrivateMetadata: "C123456"},
		Team:      slack.Team{ID: "T123456789"},
		ActionTs:  "1234567890.123456",
		TriggerID: "trigger123",
	}

	err := eventHandler.HandleFeedbackEvent(context.Background(), interactivityEvent.Team.ID, "E123456789", interactivityEvent, "POSITIVE", "test note")
	if err != nil {
		t.Fatal(err)
	}

	if mockTenantLookup.LastEnterpriseId != "E123456789" || mockTenantLookup.LastTeamId != "T123456789" {
		t.Fatalf("Unexpected tenant lookup: %s, %s", mockTenantLookup.LastEnterpriseId, mockTenantLookup.LastTeamId)
	}

	if mockSlackProcessorClient.LastHandleFeedbackEventRequest == nil {
		t.Fatal("Expected HandleFeedbackEvent to be called")
	}

	expectedRequest := &processorproto.HandleFeedbackEventRequest{
		ModalPrivateMetadata: "C123456",
		User:                 "U123456",
		Rating:               "POSITIVE",
		Note:                 "test note",
		Metadata: &slackboteventproto.EventMetadata{
			TeamId:       "T123456789",
			EnterpriseId: "E123456789",
			TenantId:     "test-tenant-id",
			RequestId:    mockSlackProcessorClient.LastHandleFeedbackEventRequest.Metadata.RequestId,
		},
	}

	if !proto.Equal(mockSlackProcessorClient.LastHandleFeedbackEventRequest, expectedRequest) {
		t.Fatalf("Unexpected HandleFeedbackEvent request: %v", mockSlackProcessorClient.LastHandleFeedbackEventRequest)
	}
}
