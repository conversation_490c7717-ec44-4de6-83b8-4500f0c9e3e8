package main

import (
	"context"
	"fmt"

	"google.golang.org/grpc/credentials"
	"google.golang.org/protobuf/proto"

	"github.com/augmentcode/augment/services/integrations/slack_bot/common"
	slackprocessorclient "github.com/augmentcode/augment/services/integrations/slack_bot/processor/client"
	processorproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	slackboteventproto "github.com/augmentcode/augment/services/integrations/slack_bot/proto"
	"github.com/augmentcode/augment/services/lib/pubsub"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"

	"github.com/rs/zerolog/log"
	"github.com/slack-go/slack"
	"github.com/slack-go/slack/slackevents"
)

type SlackEventHandler interface {
	HandleEvent(ctx context.Context, teamId string, enterpriseId string, outerEvent *slackevents.EventsAPIEvent) error
	HandleOpenRepoSelection(ctx context.Context, teamId string, enterpriseId string, user string, channel string, triggerId string, value string, messageTs string, threadTs string) string
	HandleRepoSelectionEvent(ctx context.Context, teamId string, enterpriseId string, interactivityEvent *slack.InteractionCallback, selectedRepos []string, selectAll bool) error
	HandleGetAllReposEvent(ctx context.Context, teamId string, enterpriseId string, event *slack.InteractionCallback) (*processorproto.GetRepoOptionsResponse, error)
	HandleSelectAllReposEvent(ctx context.Context, teamId string, enterpriseId string, interactivityEvent *slack.InteractionCallback, isSelectAll bool) error
	HandleOpenFeedbackModalEvent(ctx context.Context, teamId string, enterpriseId string, event *slack.InteractionCallback, feedbackMetadata *processorproto.FeedbackModalMetadata) error
	HandleFeedbackEvent(ctx context.Context, teamId string, enterpriseId string, event *slack.InteractionCallback, rating string, note string) error
}

type SlackEventHandlerImpl struct {
	pubSubClient                      pubsub.PublishClient
	tenantLookup                      TenantLookup
	tokenExchangeClient               tokenexchangeclient.TokenExchangeClient
	SlackBotProcessorEndpointTemplate string
	CloudDomainSuffixes               map[string]string
	CurrentCloud                      string
	clientCreds                       credentials.TransportCredentials
	createProcessorClientFunc         func(endpoint string, creds credentials.TransportCredentials) (slackprocessorclient.SlackProcessorClient, error)
}

func NewSlackEventHandler(pubSubClient pubsub.PublishClient, tenantLookup TenantLookup, tokenExchangeClient tokenexchangeclient.TokenExchangeClient, slackBotProcessorEndpointTemplate string, cloudDomainSuffixes map[string]string, currentCloud string, clientCreds credentials.TransportCredentials) SlackEventHandler {
	return &SlackEventHandlerImpl{
		pubSubClient:                      pubSubClient,
		tenantLookup:                      tenantLookup,
		tokenExchangeClient:               tokenExchangeClient,
		SlackBotProcessorEndpointTemplate: slackBotProcessorEndpointTemplate,
		CloudDomainSuffixes:               cloudDomainSuffixes,
		CurrentCloud:                      currentCloud,
		clientCreds:                       clientCreds,
		createProcessorClientFunc:         slackprocessorclient.NewSlackProcessorClient,
	}
}

func (h *SlackEventHandlerImpl) HandleEvent(ctx context.Context,
	teamId string, enterpriseId string, outerEvent *slackevents.EventsAPIEvent,
) error {
	event := &outerEvent.InnerEvent
	requestId := requestcontext.NewRandomRequestId()
	ctx = log.Ctx(ctx).With().
		Str("request_id", requestId.String()).
		Str("enterprise_id", enterpriseId).
		Str("team_id", teamId).
		Str("outer_event_type", outerEvent.Type).
		Str("inner_event_type", event.Type).
		Logger().WithContext(ctx)

	log.Ctx(ctx).Info().Msgf("Received event: type=%s, enterpriseId=%s, teamId=%s, requestId=%s", event.Type,
		enterpriseId, teamId, requestId)

	tenant, err := h.tenantLookup.LookupTenant(ctx, enterpriseId, teamId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to lookup tenant")
		return err
	}

	// Add tenant information to the logger context.
	ctx = log.Ctx(ctx).With().Str("tenant_id", tenant.TenantID).Str("tenant_name", tenant.TenantName).Logger().WithContext(ctx)

	eventMetadata := &slackboteventproto.EventMetadata{
		TeamId:       teamId,
		EnterpriseId: enterpriseId,
		TenantId:     tenant.TenantID,
		TenantName:   tenant.TenantName,
		RequestId:    requestId.String(),
	}
	eventProto := &slackboteventproto.SlackEvent{
		Metadata:  eventMetadata,
		EventType: event.Type,
	}
	var orderingKey string
	if event.Type == "app_mention" {
		e := event.Data.(*slackevents.AppMentionEvent)
		// we ignore message edits for now
		if e.Edited != nil {
			log.Ctx(ctx).Info().Msgf("Skipping mention bc message edited: requestId=%s, shardNamespace=%s eventType=%s", requestId, tenant.TenantName, tenant.Namespace, event.Type)
			return nil
		}
		appMention := &slackboteventproto.AppMentionEvent{
			Text:            e.Text,
			User:            e.User,
			Timestamp:       e.TimeStamp,
			ThreadTimestamp: e.ThreadTimeStamp,
			Channel:         e.Channel,
			EventTimestamp:  e.EventTimeStamp,
			UserTeam:        e.UserTeam,
			SourceTeam:      e.SourceTeam,
		}
		eventProto.Event = &slackboteventproto.SlackEvent_AppMention{
			AppMention: appMention,
		}
		orderingKey = e.ThreadTimeStamp
	} else if event.Type == "message" {
		e := event.Data.(*slackevents.MessageEvent)

		// for now, we only want to respond to direct messages without mention
		// everything else should be handled by app mention events.
		// in DMs, we also really only care when either a user responds to a thread
		// (subtype == "message_replied") or when a user sends a new message (subtype == "")
		// we filter here to avoid filling our pubsub queue with message we don't care about yet

		if e.ChannelType != "im" || (e.SubType != "" && e.SubType != "message_replied") {
			log.Ctx(ctx).Info().Msgf("Skipping message bc not direct message: requestId=%s, shardNamespace=%s eventType=%s", requestId, tenant.Namespace, event.Type)
			return nil
		}

		// we ignore message edits for now
		if e.Edited != nil {
			log.Ctx(ctx).Info().Msgf("Skipping message bc message edited: requestId=%s, shardNamespace=%s eventType=%s", requestId, tenant.Namespace, event.Type)
			return nil
		}

		message := &slackboteventproto.MessageEvent{
			ClientMsgId:     e.ClientMsgID,
			User:            e.User,
			Text:            e.Text,
			ThreadTimestamp: e.ThreadTimeStamp,
			Timestamp:       e.TimeStamp,
			Channel:         e.Channel,
			ChannelType:     e.ChannelType,
			EventTimestamp:  e.EventTimeStamp,
			UserTeam:        e.UserTeam,
			SourceTeam:      e.SourceTeam,
			Subtype:         e.SubType,
		}
		eventProto.Event = &slackboteventproto.SlackEvent_Message{
			Message: message,
		}
		orderingKey = e.ThreadTimeStamp
	} else if event.Type == "reaction_added" {
		e := event.Data.(*slackevents.ReactionAddedEvent)
		reaction := &slackboteventproto.ReactionAddedEvent{
			User:           e.User,
			ItemUser:       e.ItemUser,
			EventTimestamp: e.EventTimestamp,
			ItemType:       e.Item.Type,
			ItemChannel:    e.Item.Channel,
			Reaction:       e.Reaction,
			ItemTimestamp:  e.Item.Timestamp,
		}
		eventProto.Event = &slackboteventproto.SlackEvent_ReactionAdded{
			ReactionAdded: reaction,
		}
		// this doesn't do anything bc event ts are unique, we don't need ordering on reaction processing for now
		orderingKey = e.EventTimestamp
	} else if event.Type == "member_joined_channel" {
		e := event.Data.(*slackevents.MemberJoinedChannelEvent)
		memberJoinedChannel := &slackboteventproto.MemberJoinedChannelEvent{
			User:           e.User,
			Channel:        e.Channel,
			EventTimestamp: e.EventTimestamp,
			Inviter:        e.Inviter,
		}
		eventProto.Event = &slackboteventproto.SlackEvent_MemberJoinedChannel{
			MemberJoinedChannel: memberJoinedChannel,
		}
		// this doesn't do anything bc event ts are unique, we don't need ordering on member joined channel processing for now
		orderingKey = e.EventTimestamp
	} else if event.Type == "app_uninstalled" {
		// Cast the outer event to EventsAPICallbackEvent to access EventTime
		callbackEvent := outerEvent.Data.(*slackevents.EventsAPICallbackEvent)
		appUninstalled := &slackboteventproto.AppUninstalledEvent{
			EventTimestamp: int64(callbackEvent.EventTime),
			AppId:          outerEvent.APIAppID,
		}
		eventProto.Event = &slackboteventproto.SlackEvent_AppUninstalled{
			AppUninstalled: appUninstalled,
		}
		// We already need to handle out of order handling from Slack's webhooks, so enforcing pubsub
		// ordering is irrelevant.
		orderingKey = ""
	} else if event.Type == "app_home_opened" {
		e := event.Data.(*slackevents.AppHomeOpenedEvent)
		appHomeOpened := &slackboteventproto.AppHomeOpenedEvent{
			User:           e.User,
			Channel:        e.Channel,
			EventTimestamp: e.EventTimeStamp,
		}
		eventProto.Event = &slackboteventproto.SlackEvent_AppHomeOpened{
			AppHomeOpened: appHomeOpened,
		}
		// this doesn't do anything bc event ts are unique, we don't need ordering on app home opened processing for now
		orderingKey = e.EventTimeStamp
	} else {
		log.Ctx(ctx).Info().Msgf("Unexpected event type: %s", event.Type)
		return nil
	}

	eventData, err := proto.Marshal(eventProto)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal event")
		return err
	}
	err = h.pubSubClient.PublishWithOrderingKey(ctx, tenant.Namespace, eventData, orderingKey)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish to Pub/Sub")
		return err
	}
	log.Ctx(ctx).Info().Msgf("Published event: type=%s, requestId=%s, shardNamespace=%s", event.Type, requestId, tenant.Namespace)
	return nil
}

func (h *SlackEventHandlerImpl) handleOpenRepoSelectionHelper(ctx context.Context, teamId string, enterpriseId string, user string, channel string, triggerId string, value string, messageTs string, threadTs string) (*processorproto.OpenRepoSelectModalResponse, error) {
	requestId := requestcontext.NewRandomRequestId()
	log.Ctx(ctx).Info().Msgf("Received Open Repo Selection: type=%s, enterpriseId=%s, teamId=%s, requestId=%s", "open_repo_selection",
		enterpriseId, teamId, requestId)

	tenant, err := h.tenantLookup.LookupTenant(ctx, enterpriseId, teamId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to lookup tenant")
		return nil, err
	}
	tenantID := tenant.TenantID

	eventMetadata := &slackboteventproto.EventMetadata{
		TeamId:       teamId,
		EnterpriseId: enterpriseId,
		TenantId:     tenant.TenantID,
		TenantName:   tenant.TenantName,
		RequestId:    requestId.String(),
	}
	req := &processorproto.OpenRepoSelectModalRequest{
		Metadata:  eventMetadata,
		User:      user,
		Channel:   channel,
		TriggerId: triggerId,
		ThreadTs:  threadTs,
	}

	// If the value is not empty or the value is not the default action ID, then we need to regenerate a response to the user quetion after repos are selected
	if value != "" && value != common.ACTION_OPEN_REPO_SELECT_MODAL {
		log.Ctx(ctx).Info().Msgf("Will regenerate response to Slack event after repo selection")
		metadata := &slackboteventproto.SlackEvent{}
		err := common.DecodeProtoMetadata(value, metadata)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to unmarshal button value to SlackEvent proto")
			return nil, err
		}

		req.SlackEventToRegenerate = metadata
		req.OriginalBotMessageTimestamp = messageTs
	}

	log.Ctx(ctx).Info().Msgf("Getting service token for tenant %s", tenantID)
	serviceToken, err := h.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(ctx, tenantID, tenant.Namespace, []tokenexchangeproto.Scope{
		tokenexchangeproto.Scope_SETTINGS_R,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get service token: %w", err)
	}

	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "background", serviceToken)
	log.Ctx(ctx).Info().Msgf("Calling slack processor client for tenant %s", tenantID)
	slackProcessorClient, err := h.createShardAwareSlackProcessorClient(tenantID, tenant.Namespace, tenant.Cloud)
	if err != nil {
		return nil, fmt.Errorf("failed to create slack processor client: %w", err)
	}
	resp, err := slackProcessorClient.OpenRepoSelectModal(ctx, requestContext, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to handle open repo selection")
		return nil, fmt.Errorf("failed to handle open repo selection: %w", err)
	}
	return resp, nil
}

func (h *SlackEventHandlerImpl) HandleOpenRepoSelection(ctx context.Context, teamId string, enterpriseId string, user string, channel string, triggerId string, value string, messageTs string, threadTs string) string {
	resp, err := h.handleOpenRepoSelectionHelper(ctx, teamId, enterpriseId, user, channel, triggerId, value, messageTs, threadTs)
	if err != nil {
		return h.handleOpenRepoSelectionError(processorproto.OpenRepoSelectModalError_DEFAULT)
	}
	if resp.ErrorType != processorproto.OpenRepoSelectModalError_NO_ERROR {
		log.Ctx(ctx).Error().Msgf("Open repo selection error: %s", resp.ErrorType)
		return h.handleOpenRepoSelectionError(resp.ErrorType)
	}
	return ""
}

func (h *SlackEventHandlerImpl) handleOpenRepoSelectionError(error processorproto.OpenRepoSelectModalError) string {
	switch error {
	case processorproto.OpenRepoSelectModalError_BOT_NOT_IN_CHANNEL:
		return "Augment needs to be invited to the channel before you can select repositories or chat."

	case processorproto.OpenRepoSelectModalError_BOT_NOT_IN_DM:
		return "Repository selection is only allowed in DMs with the bot or in a channel where the bot is present."

	case processorproto.OpenRepoSelectModalError_NO_GITHUB_APP:
		return "Please install the Augment GitHub app to use this feature. You can find the installation instructions <https://docs.augmentcode.com/setup-augment/install-slack-app#1-install-github-app|here>"

	case processorproto.OpenRepoSelectModalError_NO_REPOS:
		return "Please install the Augment GitHub app on at least one repository to use this feature. You can find the installation instructions <https://docs.augmentcode.com/setup-augment/install-slack-app#1-install-github-app|here>"

	default:
		return "Sorry, there was an error processing your request. Please try again."
	}
}

func (h *SlackEventHandlerImpl) HandleRepoSelectionEvent(ctx context.Context, teamId string, enterpriseId string, interactivityEvent *slack.InteractionCallback, selectedRepos []string, selectAll bool) error {
	requestId := requestcontext.NewRandomRequestId()
	log.Ctx(ctx).Info().Msgf("Received interactivity event: type=%s, enterpriseId=%s, teamId=%s, requestId=%s", interactivityEvent.Type,
		enterpriseId, teamId, requestId)

	tenant, err := h.tenantLookup.LookupTenant(ctx, enterpriseId, teamId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to lookup tenant")
		return err
	}

	tenantID := tenant.TenantID
	eventMetadata := &slackboteventproto.EventMetadata{
		TeamId:       teamId,
		EnterpriseId: enterpriseId,
		TenantId:     tenantID,
		TenantName:   tenant.TenantName,
		RequestId:    requestId.String(),
	}
	repoSelectMetadata := &processorproto.RepoSelectModalMetadata{}
	err = common.DecodeProtoMetadata(interactivityEvent.View.PrivateMetadata, repoSelectMetadata)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to decode metadata")
		return err
	}

	req := &processorproto.HandleRepoSelectionRequest{
		Metadata:                    eventMetadata,
		User:                        interactivityEvent.User.ID,
		Channel:                     repoSelectMetadata.Channel,
		Timestamp:                   interactivityEvent.ActionTs,
		SelectedRepos:               selectedRepos,
		SelectAll:                   selectAll,
		OriginalBotMessageTimestamp: repoSelectMetadata.OriginalBotMessageTimestamp,
		ThreadTs:                    repoSelectMetadata.ThreadTs,
	}

	log.Ctx(ctx).Info().Msgf("Getting service token for tenant %s", tenantID)
	serviceToken, err := h.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(ctx, tenantID, tenant.Namespace, []tokenexchangeproto.Scope{
		tokenexchangeproto.Scope_SETTINGS_RW,
	})
	if err != nil {
		return err
	}

	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "background", serviceToken)
	log.Ctx(ctx).Info().Msgf("Calling slack processor client for tenant %s", tenantID)
	slackProcessorClient, err := h.createShardAwareSlackProcessorClient(tenantID, tenant.Namespace, tenant.Cloud)
	if err != nil {
		return err
	}
	_, err = slackProcessorClient.HandleRepoSelection(ctx, requestContext, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to handle repo selection")
		return err
	}
	log.Ctx(ctx).Info().Msgf("Called slack processor client for tenant %s", tenantID)

	// If theres a slack event to regenerate, we need regenerate the response
	if repoSelectMetadata.SlackEventToRegenerate != nil {
		err = h.HandleRegenerateResponseEvent(ctx, repoSelectMetadata.SlackEventToRegenerate)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to regenerate response")
			return err
		}
	}
	return nil
}

func (h *SlackEventHandlerImpl) HandleGetAllReposEvent(ctx context.Context, teamId string, enterpriseId string, event *slack.InteractionCallback) (*processorproto.GetRepoOptionsResponse, error) {
	requestId := requestcontext.NewRandomRequestId()
	log.Ctx(ctx).Info().Msgf("Received get all repos event: type=%s, enterpriseId=%s, teamId=%s, requestId=%s", event.Type,
		enterpriseId, teamId, requestId)

	tenant, err := h.tenantLookup.LookupTenant(ctx, enterpriseId, teamId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to lookup tenant")
		return nil, err
	}

	tenantID := tenant.TenantID
	eventMetadata := &slackboteventproto.EventMetadata{
		TeamId:       teamId,
		EnterpriseId: enterpriseId,
		TenantId:     tenantID,
		TenantName:   tenant.TenantName,
		RequestId:    requestId.String(),
	}
	req := &processorproto.GetRepoOptionsRequest{
		Metadata:    eventMetadata,
		Timestamp:   event.ActionTs,
		SearchQuery: event.Value,
	}

	log.Ctx(ctx).Info().Msgf("Getting service token for tenant %s", tenantID)
	serviceToken, err := h.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(ctx, tenantID, tenant.Namespace, []tokenexchangeproto.Scope{
		tokenexchangeproto.Scope_SETTINGS_R,
	})
	if err != nil {
		return nil, err
	}

	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "background", serviceToken)
	log.Ctx(ctx).Info().Msgf("Calling slack processor client for tenant %s", tenantID)
	slackProcessorClient, err := h.createShardAwareSlackProcessorClient(tenantID, tenant.Namespace, tenant.Cloud)
	if err != nil {
		return nil, err
	}
	resp, err := slackProcessorClient.GetRepoOptions(ctx, requestContext, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get all repos")
		return nil, err
	}
	log.Ctx(ctx).Info().Msgf("Called slack processor client for tenant %s", tenantID)
	return resp, nil
}

func (h *SlackEventHandlerImpl) HandleSelectAllReposEvent(ctx context.Context, teamId string, enterpriseId string, interactivityEvent *slack.InteractionCallback, isSelectAll bool) error {
	requestId := requestcontext.NewRandomRequestId()
	log.Ctx(ctx).Info().Msgf("Received select all repos event: type=%s, enterpriseId=%s, teamId=%s, requestId=%s, isSelectAll=%t", interactivityEvent.Type,
		enterpriseId, teamId, requestId, isSelectAll)

	tenant, err := h.tenantLookup.LookupTenant(ctx, enterpriseId, teamId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to lookup tenant")
		return err
	}

	tenantID := tenant.TenantID
	eventMetadata := &slackboteventproto.EventMetadata{
		TeamId:       teamId,
		EnterpriseId: enterpriseId,
		TenantId:     tenantID,
		TenantName:   tenant.TenantName,
		RequestId:    requestId.String(),
	}

	repoSelectMetadata := &processorproto.RepoSelectModalMetadata{}
	err = common.DecodeProtoMetadata(interactivityEvent.View.PrivateMetadata, repoSelectMetadata)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to decode metadata")
		return err
	}

	req := &processorproto.HandleSelectAllReposActionRequest{
		Metadata:                    eventMetadata,
		User:                        interactivityEvent.User.ID,
		Channel:                     repoSelectMetadata.Channel,
		SlackEventToRegenerate:      repoSelectMetadata.SlackEventToRegenerate,
		ViewId:                      interactivityEvent.View.ID,
		IsSelectAll:                 isSelectAll,
		OriginalBotMessageTimestamp: repoSelectMetadata.OriginalBotMessageTimestamp,
		ThreadTs:                    repoSelectMetadata.ThreadTs,
	}

	log.Ctx(ctx).Info().Msgf("Getting service token for tenant %s", tenantID)
	serviceToken, err := h.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(ctx, tenantID, tenant.Namespace, []tokenexchangeproto.Scope{
		tokenexchangeproto.Scope_SETTINGS_R,
	})
	if err != nil {
		return err
	}

	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "background", serviceToken)
	log.Ctx(ctx).Info().Msgf("Calling slack processor client for tenant %s", tenantID)
	slackProcessorClient, err := h.createShardAwareSlackProcessorClient(tenantID, tenant.Namespace, tenant.Cloud)
	if err != nil {
		return err
	}
	_, err = slackProcessorClient.HandleSelectAllReposAction(ctx, requestContext, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get all repos")
		return err
	}
	log.Ctx(ctx).Info().Msgf("Called slack processor client for tenant %s", tenantID)
	return nil
}

func (h *SlackEventHandlerImpl) HandleOpenFeedbackModalEvent(ctx context.Context, teamId string, enterpriseId string, event *slack.InteractionCallback, feedbackMetadata *processorproto.FeedbackModalMetadata) error {
	requestId := requestcontext.NewRandomRequestId()
	log.Ctx(ctx).Info().Msgf("Received feedback modal request event: type=%s, enterpriseId=%s, teamId=%s, requestId=%s", event.Type,
		enterpriseId, teamId, requestId)

	tenant, err := h.tenantLookup.LookupTenant(ctx, enterpriseId, teamId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to lookup tenant")
		return err
	}
	tenantID := tenant.TenantID

	eventMetadata := &slackboteventproto.EventMetadata{
		TeamId:       teamId,
		EnterpriseId: enterpriseId,
		TenantId:     tenantID,
		TenantName:   tenant.TenantName,
		RequestId:    requestId.String(),
	}

	messageSender := ""
	if feedbackMetadata == nil {
		feedbackMetadata = &processorproto.FeedbackModalMetadata{
			Channel:          event.Channel.ID,
			MessageTimestamp: event.Message.Timestamp,
			ThreadTimestamp:  event.Message.ThreadTimestamp,
		}
		messageSender = event.Message.User
	}

	req := &processorproto.OpenFeedbackModalRequest{
		Metadata:      eventMetadata,
		User:          event.User.ID,
		TriggerId:     event.TriggerID,
		ModalMetadata: feedbackMetadata,
		MessageSender: messageSender,
	}

	log.Ctx(ctx).Info().Msgf("Getting service token for tenant %s", tenantID)
	serviceToken, err := h.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(ctx, tenantID, tenant.Namespace, []tokenexchangeproto.Scope{
		tokenexchangeproto.Scope_SETTINGS_R,
	})
	if err != nil {
		return err
	}

	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "background", serviceToken)
	log.Ctx(ctx).Info().Msgf("Calling slack processor client for tenant %s", tenantID)
	slackProcessorClient, err := h.createShardAwareSlackProcessorClient(tenantID, tenant.Namespace, tenant.Cloud)
	if err != nil {
		return err
	}
	_, err = slackProcessorClient.OpenFeedbackModal(ctx, requestContext, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to handle feedback modal request")
		return err
	}
	log.Ctx(ctx).Info().Msgf("Called slack processor client for tenant %s", tenantID)
	return nil
}

func (h *SlackEventHandlerImpl) HandleFeedbackEvent(ctx context.Context, teamId string, enterpriseId string, event *slack.InteractionCallback, rating string, note string) error {
	requestId := requestcontext.NewRandomRequestId()
	log.Ctx(ctx).Info().Msgf("Received feedback event: type=%s, enterpriseId=%s, teamId=%s, requestId=%s", event.Type,
		enterpriseId, teamId, requestId)

	tenant, err := h.tenantLookup.LookupTenant(ctx, enterpriseId, teamId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to lookup tenant")
		return err
	}
	tenantID := tenant.TenantID

	eventMetadata := &slackboteventproto.EventMetadata{
		TeamId:       teamId,
		EnterpriseId: enterpriseId,
		TenantId:     tenantID,
		TenantName:   tenant.TenantName,
		RequestId:    requestId.String(),
	}
	req := &processorproto.HandleFeedbackEventRequest{
		Metadata:             eventMetadata,
		ModalPrivateMetadata: event.View.PrivateMetadata,
		User:                 event.User.ID,
		Rating:               rating,
		Note:                 note,
	}

	log.Ctx(ctx).Info().Msgf("Getting service token for tenant %s", tenantID)
	serviceToken, err := h.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(ctx, tenantID, tenant.Namespace, []tokenexchangeproto.Scope{
		tokenexchangeproto.Scope_SETTINGS_R,
	})
	if err != nil {
		return err
	}

	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "background", serviceToken)
	log.Ctx(ctx).Info().Msgf("Calling slack processor client for tenant %s", tenantID)
	slackProcessorClient, err := h.createShardAwareSlackProcessorClient(tenantID, tenant.Namespace, tenant.Cloud)
	if err != nil {
		return err
	}
	_, err = slackProcessorClient.HandleFeedbackEvent(ctx, requestContext, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to handle feedback event")
		return err
	}
	log.Ctx(ctx).Info().Msgf("Called slack processor client for tenant %s", tenantID)
	return nil
}

func (h *SlackEventHandlerImpl) HandleRegenerateResponseEvent(ctx context.Context, slackEvent *slackboteventproto.SlackEvent) error {
	// The event has an old request id, so we need to generate a new one
	requestId := requestcontext.NewRandomRequestId()
	slackEvent.Metadata.RequestId = requestId.String()

	log.Ctx(ctx).Info().Msgf("Received regenerate response event: type=%s, enterpriseId=%s, teamId=%s, requestId=%s", slackEvent.EventType,
		slackEvent.Metadata.EnterpriseId, slackEvent.Metadata.TeamId, requestId)

	var orderingKey string
	switch slackEvent.Event.(type) {
	case *slackboteventproto.SlackEvent_Message:
		orderingKey = slackEvent.GetMessage().ThreadTimestamp
	case *slackboteventproto.SlackEvent_AppMention:
		orderingKey = slackEvent.GetAppMention().ThreadTimestamp
	default:
		return fmt.Errorf("invalid event type for regenerate response: %s", slackEvent.EventType)
	}

	tenant, err := h.tenantLookup.LookupTenant(ctx, slackEvent.Metadata.EnterpriseId, slackEvent.Metadata.TeamId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to lookup tenant")
		return err
	}

	eventData, err := proto.Marshal(slackEvent)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal event")
		return err
	}

	err = h.pubSubClient.PublishWithOrderingKey(ctx, tenant.Namespace, eventData, orderingKey)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish to Pub/Sub")
		return err
	}
	log.Ctx(ctx).Info().Msgf("Published event: requestId=%s, shardNamespace=%s", requestId, tenant.Namespace)
	return nil
}

func (h *SlackEventHandlerImpl) createShardAwareSlackProcessorClient(tenantID, shardNamespace, cloud string) (slackprocessorclient.SlackProcessorClient, error) {
	var suffix string
	if cloud == h.CurrentCloud {
		suffix = fmt.Sprintf("-svc.%s", shardNamespace)
	} else {
		suffix = fmt.Sprintf(".%s.t.%s", shardNamespace, h.CloudDomainSuffixes[cloud])
	}

	endpoint := fmt.Sprintf(h.SlackBotProcessorEndpointTemplate, suffix)

	return h.createProcessorClientFunc(endpoint, h.clientCreds)
}
