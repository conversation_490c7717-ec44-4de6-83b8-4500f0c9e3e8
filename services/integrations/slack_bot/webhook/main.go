package main

import (
	"context"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"sync"
	"syscall"
	"time"

	"github.com/gorilla/mux"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gorilla/mux/otelmux"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"k8s.io/client-go/dynamic"

	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	slackwebhookproto "github.com/augmentcode/augment/services/integrations/slack_bot/webhook/proto"
	webhookmapping "github.com/augmentcode/augment/services/integrations/webhookmapping"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	pubsub "github.com/augmentcode/augment/services/lib/pubsub"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
)

var (
	configFile = flag.String("config", "", "Path to config file")
	secretsDir = flag.String("secret-directory", "", "Path to secrets")
	kubeconfig = flag.String("kubeconfig", "tools/deploy/auth_kube_config.yaml", "Path to kubeconfig file")
)

type Config struct {
	GrpcPort        int    `json:"grpc_port"`
	WebPort         int    `json:"web_port"`
	PubSubProjectID string `json:"pubsub_project_id"`
	TopicTemplate   string `json:"topic_template"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	ServerMtls        *tlsconfig.ServerConfig `json:"server_mtls"`
	CentralClientMtls *tlsconfig.ClientConfig `json:"central_client_mtls"`

	TokenExchangeEndpoint string `json:"token_exchange_endpoint"`
	TenantWatcherEndpoint string `json:"tenant_watcher_endpoint"`

	HttpsServerKey  string `json:"https_server_key"`
	HttpsServerCert string `json:"https_server_cert"`

	// When in dev, the namespace to read webhooktenantmappings from
	Namespace string `json:"namespace"`

	// The kube contexts to read webhooktenantmappings from
	KubeContexts []string `json:"kube_contexts"`

	// Used to create a  slack processor client
	SlackBotProcessorEndpointTemplate string `json:"slack_bot_processor_endpoint_template"`
	CurrentCloud                      string `json:"current_cloud"`
	CloudDomainSuffixesString         string `json:"cloud_domain_suffixes"`
	// Unmarshalled version of CloudDomainSuffixesString
	CloudDomainSuffixes map[string]string
}

func loadConfig(path string, config *Config) error {
	f, err := os.Open(path)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}

	var cloudDomainSuffixes map[string]string
	err = json.Unmarshal([]byte(config.CloudDomainSuffixesString), &cloudDomainSuffixes)
	if err != nil {
		return fmt.Errorf("error parsing cloud_domain_suffixes: %v", err)
	}
	config.CloudDomainSuffixes = cloudDomainSuffixes

	log.Info().Msgf("Config: %+v", config)
	return nil
}

type SecretConfig struct {
	SlackSigningSecret secretstring.SecretString
}

func loadSecretConfig(path string, config *SecretConfig) error {
	signingSecretFile := filepath.Join(path, "signing_secret")
	signingSecret, err := os.ReadFile(signingSecretFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error reading signing secret file")
	}
	config.SlackSigningSecret = secretstring.New(string(signingSecret))

	return nil
}

// Returns true if we want logging and tracing for this request and false
// otherwise
func logRequest(r *http.Request) bool {
	// Don't spam logs and traces with health checks
	return r.URL.Path != "/health"
}

func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if logRequest(r) {
			log.Info().Msgf("%s %s", r.Method, r.URL.Path)
			next.ServeHTTP(w, r)
			// TODO: also log the response?
		} else {
			next.ServeHTTP(w, r)
		}
	})
}

func healthCheck(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("ok"))
}

type slackbotWebhookServer struct {
	pubSubClient pubsub.PublishClient
	tenantCache  tenantwatcherclient.TenantCache
}

func (s *slackbotWebhookServer) EmitEvent(ctx context.Context, req *slackwebhookproto.EmitEventRequest) (*slackwebhookproto.EmitEventResponse, error) {
	log.Ctx(ctx).Info().Msgf("EmitEvent: tenantID=%s", req.TenantId)
	if req.TenantId == "" {
		log.Ctx(ctx).Error().Msg("Missing tenant ID")
		return nil, status.Error(codes.InvalidArgument, "Missing tenant ID")
	}
	t, err := s.tenantCache.GetTenant(req.TenantId)
	if err != nil {
		if errors.Is(err, tenantwatcherclient.ErrTenantNotFound) {
			log.Ctx(ctx).Warn().Msg("Tenant not found: Ignoring event")
			return nil, status.Error(codes.NotFound, "Tenant not found")
		}
		log.Ctx(ctx).Error().Err(err).Msg("Error getting tenant")
		return nil, err
	}

	eventData, err := proto.Marshal(req.Event)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error marshalling event")
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("EmitEvent: Publish to tenantID=%s, shardNamespace=%s", req.TenantId, t.ShardNamespace)
	err = s.pubSubClient.Publish(ctx, t.ShardNamespace, eventData)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error publishing to Pub/Sub")
		return nil, err
	}
	return &slackwebhookproto.EmitEventResponse{}, nil
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()

	var config Config
	var secretConfig SecretConfig

	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	log.Info().Msgf("Kubeconfig: %s", kubeconfig)
	if _, err := os.Stat(*kubeconfig); os.IsNotExist(err) {
		log.Fatal().Err(err).Msg("kubeconfig file does not exist")
		os.Exit(1)
	}

	if err := loadConfig(*configFile, &config); err != nil {
		log.Fatal().Msgf("Failed to load app config: %v", err)
	}

	if *secretsDir == "" {
		log.Fatal().Msg("Missing secret token file")
	}

	if err := loadSecretConfig(*secretsDir, &secretConfig); err != nil {
		log.Fatal().Msgf("Failed to load secret config: %v", err)
	}

	ctx := context.Background()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	go func() {
		sig := <-sigChan
		log.Ctx(ctx).Info().Msgf("Received signal: %v", sig)
		cancel()
	}()

	pubSubClient, err := pubsub.NewPublishClient(ctx, config.PubSubProjectID, config.TopicTemplate)
	if err != nil {
		log.Ctx(ctx).Fatal().Msgf("Failed to create Pub/Sub client: %v", err)
	}
	defer pubSubClient.Close()

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Ctx(ctx).Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Ctx(ctx).Fatal().Err(err).Msgf("Error creating client credentials")
	}

	// Setup tenant watcher client.
	tenantWatcherClient := tenantwatcherclient.New(config.TenantWatcherEndpoint, grpc.WithTransportCredentials(centralClientCreds))
	// Listen to all namespaces
	tenantCache := tenantwatcherclient.NewTenantCache(tenantWatcherClient, config.Namespace)
	defer tenantCache.Close()

	if len(config.KubeContexts) == 0 {
		log.Ctx(ctx).Fatal().Msg("Must specify at least one kube context")
	}

	var webhookMappingInformer webhookmapping.WebhookTenantMappingInformer
	if config.Namespace == "" {
		dynamicClientsets := make([]dynamic.Interface, len(config.KubeContexts))
		for i, context := range config.KubeContexts {
			dynamicClientset, err := webhookmapping.CreateDynamicClientForContext(*kubeconfig, context)
			if err != nil {
				log.Ctx(ctx).Fatal().Msgf("Failed to create dynamic client: %v", err)
			}
			dynamicClientsets[i] = dynamicClientset
		}
		webhookMappingInformer = webhookmapping.NewInformer(dynamicClientsets, time.Minute)
	} else {
		if len(config.KubeContexts) > 1 {
			log.Ctx(ctx).Fatal().Msg("Can only specify a namespace with a single kube context")
		}
		dynamicClientset, err := webhookmapping.CreateDynamicClientForContext(*kubeconfig, config.KubeContexts[0])
		if err != nil {
			log.Ctx(ctx).Fatal().Msgf("Failed to create dynamic client: %v", err)
		}
		webhookMappingInformer = webhookmapping.NewNamespacedInformer(dynamicClientset, config.Namespace, time.Minute)
	}
	webhookMappingLookup := webhookmapping.NewLookup(webhookMappingInformer)
	go webhookMappingInformer.Run(ctx.Done())

	namespace := os.Getenv("POD_NAMESPACE")
	if namespace == "" {
		log.Ctx(ctx).Fatal().Msg("POD_NAMESPACE environment variable must be set.")
	}

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Ctx(ctx).Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	tenantLookup := NewMappingTenantLookup(webhookMappingLookup, tenantCache)
	slackEventHandler := NewSlackEventHandler(pubSubClient, tenantLookup, tokenExchangeClient, config.SlackBotProcessorEndpointTemplate, config.CloudDomainSuffixes, config.CurrentCloud, centralClientCreds)
	slackWebHandler := NewSlackWebHandler(slackEventHandler, secretConfig.SlackSigningSecret, false)

	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		r := mux.NewRouter()
		r.HandleFunc("/health", healthCheck)
		r.NotFoundHandler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			log.Ctx(ctx).Warn().Msgf("Not found: %s", r.URL.Path)
			http.Error(w, "Not Found", http.StatusNotFound)
		})
		r.HandleFunc("/slack/events", slackWebHandler.HandleEvent)
		r.HandleFunc("/slack/commands", slackWebHandler.HandleCommand)
		r.HandleFunc("/slack/interactivity", slackWebHandler.HandleInteractivityEvent)
		r.HandleFunc("/slack/repos", slackWebHandler.HandleGetAllRepos)
		r.Use(otelmux.Middleware("webhook-listener", otelmux.WithFilter(logRequest)))
		r.Use(loggingMiddleware)

		srv := &http.Server{
			Handler: r,
			Addr:    fmt.Sprintf(":%d", config.WebPort),
		}
		log.Ctx(ctx).Info().Msgf("Server is running on port %d", config.WebPort)

		go func() {
			select {
			case <-ctx.Done():
				srv.Shutdown(context.Background())
			}
		}()

		if err := srv.ListenAndServeTLS(config.HttpsServerCert, config.HttpsServerKey); err != nil && err != http.ErrServerClosed {
			log.Ctx(ctx).Fatal().Msgf("Failed to listen: %v", err)
		}
		log.Ctx(ctx).Info().Msg("Server stopped")
	}()

	if config.GrpcPort == 0 {
		log.Ctx(ctx).Info().Msg("gRPC server disabled")
	} else {
		wg.Add(1)
		go func() {
			defer wg.Done()
			serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
			if err != nil {
				log.Ctx(ctx).Fatal().Err(err).Msg("Error creating TLS config")
			}

			var opts []grpc.ServerOption
			opts = append(opts, grpc.Creds(serverTls))
			opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
			opts = append(opts, grpc.ChainUnaryInterceptor(
				recovery.UnaryServerInterceptor(),
				srvMetrics.UnaryServerInterceptor(),
			))
			opts = append(opts, grpc.ChainStreamInterceptor(
				recovery.StreamingServerInterceptor(),
				srvMetrics.StreamServerInterceptor(),
			))

			serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
			authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
			opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
			opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept))

			grpcServer := grpc.NewServer(opts...)
			// setup prometheus metrics for GRPC calls
			srvMetrics.InitializeMetrics(grpcServer)

			// setup reflection for debugging
			reflection.Register(grpcServer)
			// setup health service
			healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

			slackWebhookServer := &slackbotWebhookServer{
				pubSubClient: pubSubClient,
				tenantCache:  tenantCache,
			}
			slackwebhookproto.RegisterSlackBotWebhookServer(grpcServer, slackWebhookServer)

			lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.GrpcPort))
			if err != nil {
				log.Ctx(ctx).Fatal().Err(err).Msgf("Failed to listen")
			}
			log.Ctx(ctx).Info().Msgf("Listening on %v", lis.Addr())

			go func() {
				select {
				case <-ctx.Done():
					grpcServer.GracefulStop()
				}
			}()

			err = grpcServer.Serve(lis)
			if err != nil {
				log.Ctx(ctx).Fatal().Err(err).Msg("Error serving")
			} else {
				log.Ctx(ctx).Fatal().Msg("gRPC server closed")
			}
		}()
	}

	wg.Wait()
	log.Ctx(ctx).Info().Msg("Server stopped")
}
