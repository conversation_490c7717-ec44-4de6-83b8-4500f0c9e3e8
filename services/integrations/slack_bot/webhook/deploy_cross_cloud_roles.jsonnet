local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(env, namespace, cloud, namespace_config)
  // Copied from deploy.jsonnet
  local appName = 'slack-bot-webhook';
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloudInfo.getLeadClusterForCluster(cloud), namespace, iam=true, overridePrefix='slack-bot-w'
  );

  // give the GCP_US_CENTRAL1_PROD slack-bot-webhook SA access to all clusters
  if cloudInfo.isProdCluster(cloud) && cloud != 'GCP_US_CENTRAL1_PROD' then [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: 'slack-bot-webhook-%s-cross-cloud-role-binding' % std.asciiLower(env),
        labels: {
          app: 'slack-bot-webhook',
        },
      },
      subjects: [
        // always give the slack-bot-webhook service account of the prod project permission to sync
        {
          kind: 'User',
          name: serviceAccount.serviceAccountGcpEmailAddress,
        },
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'webhooktenantmapping-reader',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
  ] else [
  ]
