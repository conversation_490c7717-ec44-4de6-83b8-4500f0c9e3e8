import pytest
import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from typing import <PERSON>ple
import time
from services.integrations.slack_bot.webhook import (
    slack_webhook_pb2_grpc,
    slack_webhook_pb2,
)
from services.integrations.slack_bot import slack_event_pb2
from services.lib.request_context.request_context import RequestContext
from services.integrations.slack_bot.test.fake_slack.client import FakeSlackClient
from services.settings import settings_pb2_grpc, settings_pb2
from services.integrations.github.state import github_state_pb2, github_state_pb2_grpc

from google.protobuf import timestamp_pb2
from base.blob_names import blob_names_pb2

BOT_USER = "U12345"
SLACK_USER = "U12346"
SLACK_CHANNEL = "C12345"


def test_slack_bot_deploy(
    application_deploy: k8s_test_helper.DeployInfo,
    slack_bot_tenant: Tuple[str, str],
    request_context: Request<PERSON>ontext,
    slack_webhook_client: slack_webhook_pb2_grpc.SlackBotWebhookStub,
    fake_slack_client: FakeSlackClient,
    settings_client: settings_pb2_grpc.SettingsStub,
    github_state_client: github_state_pb2_grpc.GithubStateStub,
):
    # Update Settings with a slack and github installation
    req = settings_pb2.UpdateTenantSettingsRequest(
        settings=settings_pb2.TenantSettings(
            slack_settings=settings_pb2.SlackSettings(
                enterprise_id="E12345",
                team_id="T12345",
                oauth_token="test-oauth-token",
                bot_user_id=BOT_USER,
            ),
            github_settings=settings_pb2.GithubSettings(
                installation_id=12345,
                repos=[
                    settings_pb2.RepoInformation(
                        repo_owner="test-owner", repo_name="test-repo"
                    )
                ],
            ),
        ),
    )
    settings_client.UpdateTenantSettings(req, metadata=request_context.to_metadata())

    # Update Github State with a repository
    req = github_state_pb2.UpdateCurrentRefCheckpointRequest(
        ref=github_state_pb2.GithubRef(
            ref="main",
            repo=github_state_pb2.GithubRepo(
                repo_owner="test-owner", repo_name="test-repo"
            ),
        ),
        commit_sha="abc123",
        commit_time=timestamp_pb2.Timestamp(seconds=1234567890),
        parent_commit_shas=["abc122"],
        blobs=blob_names_pb2.Blobs(baseline_checkpoint_id=""),
    )
    github_state_client.UpdateCurrentRefCheckpoint(
        iter([req]), metadata=request_context.to_metadata()
    )

    # Wait for github state to move checkpoint to indexed
    for _ in range(30):
        time.sleep(10)

        indexed_refs = None
        try:
            indexed_refs: github_state_pb2.GetIndexedRefCheckpointsResponse = (
                github_state_client.GetIndexedRefCheckpoints(
                    github_state_pb2.GetIndexedRefCheckpointsRequest(),
                    metadata=request_context.to_metadata(),
                )
            )
        except Exception as e:
            print(f"Error getting indexed ref checkpoints: {e}")
            continue

        found = False
        for ref in indexed_refs.ref_checkpoints:
            if ref.ref.repo.repo_name == "test-repo":
                found = True
                break
        if found:
            break
    else:
        assert False, "timeout waiting for github state to update"

    req = slack_webhook_pb2.EmitEventRequest(
        tenant_id=slack_bot_tenant[1],
        event=slack_event_pb2.SlackEvent(
            metadata=slack_event_pb2.EventMetadata(
                team_id="T12345",
                enterprise_id="E12345",
                tenant_id=slack_bot_tenant[1],
                tenant_name=slack_bot_tenant[0],
                request_id="test-request-id",
            ),
            event_type="app_mention",
            app_mention=slack_event_pb2.AppMentionEvent(
                text=f"Hello, {BOT_USER}!",
                user=SLACK_USER,
                timestamp="1234567890.123456",
                thread_timestamp="1234567890.123456",
                channel=SLACK_CHANNEL,
                event_timestamp="1234567890.123456",
                user_team="T12345",
                source_team="T12345",
            ),
        ),
    )
    slack_webhook_client.EmitEvent(req, metadata=request_context.to_metadata())
    fake_slack_client.next_response(
        "conversations.replies",
        {
            "ok": True,
            "messages": [
                {
                    "type": "message",
                    "ts": "1234567890.123456",
                    "text": f"Hello, {BOT_USER}!",
                    "user": SLACK_USER,
                }
            ],
        },
    )

    def check_message(messages) -> bool:
        has_add_reaction = any(m for m in messages if m["name"] == "reactions.add")
        has_post_message = any(m for m in messages if m["name"] == "chat.postMessage")
        has_update_message = any(m for m in messages if m["name"] == "chat.update")
        has_remove_reaction = any(
            m for m in messages if m["name"] == "reactions.remove"
        )
        return (
            has_add_reaction
            and has_post_message
            and has_remove_reaction
            and has_update_message
        )

    for _ in range(10):
        messages = fake_slack_client.get(timeout=10)
        print(messages, flush=True)
        if check_message(messages):
            break
        time.sleep(6)
    else:
        pytest.fail("Timeout waiting for messages")
