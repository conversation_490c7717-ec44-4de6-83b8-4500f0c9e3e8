load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "server_lib",
    srcs = [
        "file_utils.go",
        "github_clients.go",
        "github_event_handler.go",
        "kms_signer.go",
        "lag_tracker.go",
        "main.go",
        "path_filter.go",
        "server.go",
    ],
    importpath = "github.com/augmentcode/augment/services/integrations/github/processor/server",
    visibility = ["//visibility:private"],
    deps = [
        "//base/blob_names:blob_names_go",
        "//base/blob_names:blob_names_go_proto",
        "//base/feature_flags:feature_flags_go",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "//services/api_proxy:public_api_go_proto",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/content_manager:content_manager_go_proto",
        "//services/content_manager/client:client_go",
        "//services/integrations/github:github_event_go_proto",
        "//services/integrations/github/processor:processor_go_proto",
        "//services/integrations/github/state:github_state_go_proto",
        "//services/integrations/github/state/client:client_go",
        "//services/integrations/github/state/lib:github_state_lib",
        "//services/integrations/lib:http_client_go",
        "//services/integrations/webhookmapping",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/service:grpc_service_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/publisher:publisher_go",
        "//services/settings:settings_go_proto",
        "//services/settings/client:client_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_bradleyfalzon_ghinstallation_v2//:ghinstallation",
        "@com_github_denormal_go_gitignore//:go-gitignore",
        "@com_github_golang_jwt_jwt_v4//:jwt",
        "@com_github_google_go_github_v64//github",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_kms//apiv1",
        "@com_google_cloud_go_kms//apiv1/kmspb",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@io_opentelemetry_go_otel//:otel",
        "@io_opentelemetry_go_otel//attribute",
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes:go_default_library",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status:go_default_library",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    base = "//tools/docker:ubuntu_git_base_image",
    binary = ":server",
    # add the grpc health probe
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg_library(
    name = "app_secret_lib",
    srcs = ["app_secret_lib.jsonnet"],
)

kubecfg_library(
    name = "key_lib",
    srcs = ["key_lib.jsonnet"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/integrations:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":key_lib",
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/integrations/github/processor/server:app_secret_lib",
        "//services/lib/pubsub:pubsub-lib",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    visibility = [
        "//services/auth/central:__subpackages__",
    ],
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

kubecfg(
    name = "keyring_kubecfg",
    src = "keyring_deploy.jsonnet",
    deps = [
        ":key_lib",
        "//deploy/common:cloud_info",
    ],
)

go_test(
    name = "test",
    srcs = [
        "file_utils_test.go",
        "github_event_handler_test.go",
        "lag_tracker_test.go",
        "path_filter_test.go",
        "server_test.go",
        "test_helpers.go",
    ],
    embed = [":server_lib"],
    deps = [
        "//base/blob_names:blob_names_go",
        "//base/blob_names:blob_names_go_proto",
        "//base/feature_flags:feature_flags_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/content_manager:content_manager_go_proto",
        "//services/content_manager/client:client_go",
        "//services/integrations/github:github_event_go_proto",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/settings:settings_go_proto",
        "//services/settings/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "@com_github_google_go_github_v64//github",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_model//go",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//status",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":keyring_kubecfg",
        ":kubecfg",
        ":kubecfg_monitoring",
        "//deploy/tenants:namespaces",
    ],
)
