package main

import (
	"bytes"
	"context"
	"encoding/binary"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"testing"
	"time"

	blobnames "github.com/augmentcode/augment/base/blob_names"
	blobnamespb "github.com/augmentcode/augment/base/blob_names/proto"
	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/secretstring"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	contentmanagerproto "github.com/augmentcode/augment/services/content_manager/proto"
	githubeventproto "github.com/augmentcode/augment/services/integrations/github/eventpb"
	githubproto "github.com/augmentcode/augment/services/integrations/github/eventpb"
	githubstateclient "github.com/augmentcode/augment/services/integrations/github/state/client"
	githubstatepersistproto "github.com/augmentcode/augment/services/integrations/github/state/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	requestinsightproto "github.com/augmentcode/augment/services/request_insight/proto"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	settingsserviceproto "github.com/augmentcode/augment/services/settings/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"

	"github.com/google/go-github/v64/github"
	"github.com/stretchr/testify/mock"
	statusproto "google.golang.org/genproto/googleapis/rpc/status"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/stretchr/testify/assert"
)

func blobNameToProto(t *testing.T, path string, content []byte) *blobnamespb.BlobName {
	name, err := blobnames.NewBlobNameProto(blobnames.GetBlobName(path, content), false)
	assert.NoError(t, err)
	return name
}

func blobNameToBytes(t *testing.T, path string, content []byte) []byte {
	t.Helper()
	blobName := blobnames.GetBlobName(path, content)
	blobBytes, err := blobnames.DecodeHexBlobName(blobName)
	assert.NoError(t, err)
	return blobBytes
}

type eventHandlerTest struct {
	t *testing.T

	mockTokenExchange                *MockTokenExchangeClient
	mockContentManager               *MockContentManagerClient
	mockGithubStateClient            *MockGithubStateClient
	mockGithubClient                 *MockGithubRepositoriesClient
	mockClients                      *MockGithubClients
	mockLagTracker                   *MockLagTracker
	mockSettingsClient               *mockSettingsClient
	mockWebhookTenantMappingResource *MockWebhookTenantMappingResource
	mockRequestInsightPublisher      *MockRequestInsightPublisher
	featureFlagHandle                *featureflags.LocalFeatureFlagHandler

	handler *githubEventHandlerImpl
}

func newEventHandlerTest(t *testing.T) *eventHandlerTest {
	mockTokenExchange := new(MockTokenExchangeClient)
	mockContentManager := new(MockContentManagerClient)
	mockGithubStateClient := new(MockGithubStateClient)
	mockFeatureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
	mockGithubClient := new(MockGithubRepositoriesClient)
	mockClients := new(MockGithubClients)
	mockWebhookTenantMappingResource := new(MockWebhookTenantMappingResource)
	mockLagTracker := new(MockLagTracker)
	mockSettingsClient := new(mockSettingsClient)
	mockRequestInsightPublisher := new(MockRequestInsightPublisher)

	handler := &githubEventHandlerImpl{
		contentManagerClient:         mockContentManager,
		tokenExchangeClient:          mockTokenExchange,
		githubStateClient:            mockGithubStateClient,
		githubClients:                mockClients,
		featureFlagHandle:            mockFeatureFlagHandle,
		registerRepoWithLatestCommit: true,
		webhookTenantMappingResource: mockWebhookTenantMappingResource,
		lagTracker:                   mockLagTracker,
		settingsServiceClient:        mockSettingsClient,
		requestInsightPublisher:      mockRequestInsightPublisher,
	}

	return &eventHandlerTest{
		t:                                t,
		mockTokenExchange:                mockTokenExchange,
		mockContentManager:               mockContentManager,
		mockGithubStateClient:            mockGithubStateClient,
		mockGithubClient:                 mockGithubClient,
		mockClients:                      mockClients,
		mockLagTracker:                   mockLagTracker,
		mockSettingsClient:               mockSettingsClient,
		mockWebhookTenantMappingResource: mockWebhookTenantMappingResource,
		mockRequestInsightPublisher:      mockRequestInsightPublisher,
		featureFlagHandle:                mockFeatureFlagHandle,
		handler:                          handler,
	}
}

func (et *eventHandlerTest) assertExpectations() {
	et.t.Helper()
	et.mockTokenExchange.AssertExpectations(et.t)
	et.mockContentManager.AssertExpectations(et.t)
	et.mockGithubStateClient.AssertExpectations(et.t)
	et.mockGithubClient.AssertExpectations(et.t)
	et.mockClients.AssertExpectations(et.t)
	et.mockLagTracker.AssertExpectations(et.t)
	et.mockSettingsClient.AssertExpectations(et.t)
	et.mockWebhookTenantMappingResource.AssertExpectations(et.t)
}

func (et *eventHandlerTest) mockRegisterRepos(emptyState bool, files map[string]string, ignored map[string]struct{}) blobnames.CheckpointID {
	et.t.Helper()

	// First we get the commit
	et.mockGithubClient.On("GetCommit", mock.Anything, "testOwner", "testRepo", mock.Anything, mock.Anything).Return(&github.RepositoryCommit{
		SHA:     github.String("000"),
		Parents: []*github.Commit{},
		Commit: &github.Commit{
			Author: &github.CommitAuthor{
				Date: &github.Timestamp{Time: timestamppb.Now().AsTime()},
			},
		},
	}, &github.Response{}, nil)

	if emptyState {
		ref := &githubstatepersistproto.GithubRef{
			Ref: "main",
			Repo: &githubstatepersistproto.GithubRepo{
				RepoOwner: "testOwner",
				RepoName:  "testRepo",
			},
		}
		var emptyRefState *githubstatepersistproto.RefCheckpoint
		et.mockGithubStateClient.On("GetCurrentRefState", mock.Anything, mock.Anything, ref).Return(emptyRefState, status.Error(codes.NotFound, "not found")).Once()
	}

	et.mockGithubClient.On("GetArchiveLink", mock.Anything, "testOwner", "testRepo", github.Tarball, &github.RepositoryContentGetOptions{Ref: "000"}, 1).Return(&url.URL{Path: "fake_tarball"}, &github.Response{}, nil)

	// Then download the repo
	checkpoint, blobNames, uploads, tarball := fakeRepoTarball(et.t, files, ignored)
	mockHttpClient := &http.Client{Transport: &mockTarballHttpTransport{repoTarball: tarball}}
	et.mockClients.On("httpClient").Once().Return(mockHttpClient)

	// Create a checkpoint and update the ref with it
	checkpointStr := checkpoint.String()
	var blobs *blobnamespb.Blobs
	if checkpointStr != "" {
		et.mockContentManager.On("BatchUploadBlobContent", mock.Anything, uploads, contentmanagerproto.IndexingPriority_DEFAULT, mock.Anything).Return([]*contentmanagerproto.UploadBlobResult{}, nil)
		et.mockContentManager.On("CheckpointBlobs", mock.Anything, &blobnamespb.Blobs{Added: blobNamesToBytes(et.t, blobNames)}, mock.Anything).Return(checkpoint.String(), nil)
		blobs = &blobnamespb.Blobs{BaselineCheckpointId: &checkpointStr}
	}
	et.mockGithubStateClient.On("UpdateCurrentRefCheckpoint", mock.Anything, mock.Anything, &githubstatepersistproto.GithubRef{Ref: "main", Repo: &githubstatepersistproto.GithubRepo{RepoOwner: "testOwner", RepoName: "testRepo"}}, "000", []string{}, "", mock.Anything, blobs, mock.Anything, mock.Anything).Return(&githubstatepersistproto.UpdateCurrentRefCheckpointResponse{}, nil)
	return checkpoint
}

func (et *eventHandlerTest) mockIgnoreFileDiff(augmentignore []byte, badGetContentsResp bool) {
	var getContentsResp *github.Response
	if !badGetContentsResp {
		getContentsResp = &github.Response{
			Response: &http.Response{StatusCode: http.StatusNotFound},
		}
	}

	et.mockGithubStateClient.On("GetCurrentRefFiles", mock.Anything, mock.Anything, &githubstatepersistproto.GithubRef{Ref: "main", Repo: &githubstatepersistproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"}}, "baseCommit", []string{".augmentignore", ".gitignore"}).
		Return(func() (<-chan githubstateclient.GetCurrentRefFilesResult, error) {
			fakeFiles := make(chan githubstateclient.GetCurrentRefFilesResult, 10)
			defer close(fakeFiles)
			fileinfos := []*githubstatepersistproto.FileInfo{}
			if augmentignore != nil {
				fileinfos = append(fileinfos, &githubstatepersistproto.FileInfo{
					FilePath: ".augmentignore",
					BlobName: blobNameToProto(et.t, ".augmentignore", augmentignore),
					Status:   &statusproto.Status{Code: int32(codes.OK)},
				})
			} else {
				fileinfos = append(fileinfos, &githubstatepersistproto.FileInfo{
					FilePath: ".augmentignore",
					Status:   &statusproto.Status{Code: int32(codes.NotFound)},
				})
			}
			fileinfos = append(fileinfos, &githubstatepersistproto.FileInfo{
				FilePath: ".gitignore",
				Status:   &statusproto.Status{Code: int32(codes.NotFound)},
			})
			fakeFiles <- githubstateclient.GetCurrentRefFilesResult{
				Resp: &githubstatepersistproto.GetCurrentRefFilesResponse{
					CommitSha: "baseCommit",
					FileInfos: fileinfos,
				},
			}
			return fakeFiles, nil
		}())
	if augmentignore != nil {
		blobkeys := []*contentmanagerproto.BlobContentKey{
			{BlobName: blobnames.GetBlobName(".augmentignore", augmentignore).String()},
		}
		et.mockContentManager.On("BatchDownloadContent", mock.Anything, blobkeys, mock.Anything, mock.Anything).
			Return(func() (<-chan contentmanagerclient.BatchDownloadContentResult, error) {
				fakeContent := make(chan contentmanagerclient.BatchDownloadContentResult, 10)
				defer close(fakeContent)
				fakeContent <- contentmanagerclient.BatchDownloadContentResult{
					Resp: &contentmanagerproto.BatchGetContentResponse{
						Response: &contentmanagerproto.BatchGetContentResponse_FinalContent{
							FinalContent: &contentmanagerproto.BatchGetContentFinalContent{
								Content:  augmentignore,
								Metadata: []*contentmanagerproto.BlobMetadata{{Key: "path", Value: ".augmentignore"}},
							},
						},
					},
				}
				return fakeContent, nil
			}())
	} else {
		var nilContent *github.RepositoryContent
		var nilContents []*github.RepositoryContent
		et.mockGithubClient.On("GetContents", mock.Anything, mock.Anything, mock.Anything, ".augmentignore", mock.Anything).
			Once().
			Return(nilContent, nilContents, getContentsResp, errors.New("no .augmentignore"))
		if badGetContentsResp {
			// If we fail to get a file for some unknown error, we should exit and not try .gitignore
			return
		}
	}
	var nilContent *github.RepositoryContent
	var nilContents []*github.RepositoryContent
	et.mockGithubClient.On("GetContents", mock.Anything, mock.Anything, mock.Anything, ".gitignore", mock.Anything).
		Once().
		Return(nilContent, nilContents, getContentsResp, errors.New("no .gitignore"))
}

func TestDedupGithubSettingsRepos(t *testing.T) {
	testCases := []struct {
		name     string
		input    []*settingsproto.RepoInformation
		expected []*settingsproto.RepoInformation
	}{
		{
			name: "empty slice",
			input: []*settingsproto.RepoInformation{
				{},
			},
			expected: []*settingsproto.RepoInformation{
				{},
			},
		},
		{
			name: "one duplicate",
			input: []*settingsproto.RepoInformation{
				{RepoOwner: "owner", RepoName: "repo"},
				{RepoOwner: "owner", RepoName: "repo"},
			},
			expected: []*settingsproto.RepoInformation{
				{RepoOwner: "owner", RepoName: "repo"},
			},
		},
		{
			name: "multiple duplicates",
			input: []*settingsproto.RepoInformation{
				{RepoOwner: "owner", RepoName: "repo"},
				{RepoOwner: "owner", RepoName: "repo"},
				{RepoOwner: "owner2", RepoName: "repo2"},
				{RepoOwner: "owner2", RepoName: "repo2"},
			},
			expected: []*settingsproto.RepoInformation{
				{RepoOwner: "owner", RepoName: "repo"},
				{RepoOwner: "owner2", RepoName: "repo2"},
			},
		},
	}

	for _, tc := range testCases {
		t.Logf("running testcase %s", tc.name)
		settings := &settingsproto.TenantSettings{
			GithubSettings: &settingsproto.GithubSettings{
				Repos: tc.input,
			},
		}
		dedupGithubSettingsRepos(settings)
		assert.Equal(t, tc.expected, settings.GithubSettings.Repos)
	}
}

func TestParseGitApplyErrorForFilesToIgnore(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected []string
	}{
		{
			name:     "no binary files",
			input:    "error: patch failed: foo.txt:1\nerror: foo.txt: patch does not apply",
			expected: nil,
		},
		{
			name:     "one binary file",
			input:    "error: cannot apply binary patch to 'foo.txt'\nerror: foo.txt: patch does not apply",
			expected: []string{"foo.txt"},
		},
		{
			name:     "one binary file, extra message",
			input:    "error: cannot apply binary patch to 'foo.txt' without full index line error\nerror: foo.txt: patch does not apply",
			expected: []string{"foo.txt"},
		},
		{
			name:     "one binary file with quotes",
			input:    "error: cannot apply binary patch to 'foo'bar.txt'\nerror: foo'bar.txt: patch does not apply",
			expected: []string{"foo'bar.txt"},
		},
		{
			name:     "one binary file with weird characters",
			input:    "error: cannot apply binary patch to 'foo \"'bar.txt'\nerror: foo \"'bar.txt: patch does not apply",
			expected: []string{"foo \"'bar.txt"},
		},
		{
			name:     "multiple binary files",
			input:    "error: cannot apply binary patch to 'foo.txt'\nerror: foo.txt: patch does not apply\nerror: cannot apply binary patch to 'bar.txt'\nerror: bar.txt: patch does not apply",
			expected: []string{"foo.txt", "bar.txt"},
		},
		{
			name:     "one symlink",
			input:    "error: foo.txt: wrong type",
			expected: []string{"foo.txt"},
		},
		{
			name:     "multiple symlinks",
			input:    "error: foo.txt: wrong type\nerror: bar.txt: wrong type",
			expected: []string{"foo.txt", "bar.txt"},
		},
		{
			name:     "mixed",
			input:    "error: cannot apply binary patch to 'foo.txt'\nerror: foo.txt: patch does not apply\nerror: bar.txt: wrong type",
			expected: []string{"foo.txt", "bar.txt"},
		},
	}

	for _, tc := range testCases {
		t.Logf("running testcase %s", tc.name)
		assert.Equal(t, tc.expected, parseGitApplyErrorForFilesToIgnore(tc.input))
	}
}

func TestRegisterRepo(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	ctx := context.Background()
	requestContext := &requestcontext.RequestContext{}
	tenantID := "test-tenant"
	repoInfo := &settingsproto.RepoInformation{
		RepoOwner: "testOwner",
		RepoName:  "testRepo",
	}

	checkpoint := et.mockRegisterRepos(true, map[string]string{
		"hello.txt": "world!",
	}, nil)

	blobs, errWithRateLimit := et.handler.RegisterRepo(ctx, requestContext, et.mockGithubClient,
		repoInfo, tenantID, "test-tenant-name", "main", "000")

	// Verification
	assert.Nil(t, errWithRateLimit)
	assert.Equal(t, checkpoint.String(), *blobs.BaselineCheckpointId)
}

func TestRegisterRepoOnlyIgnoredFiles(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	ctx := context.Background()
	requestContext := &requestcontext.RequestContext{}
	tenantID := "test-tenant"
	repoInfo := &settingsproto.RepoInformation{
		RepoOwner: "testOwner",
		RepoName:  "testRepo",
	}

	et.featureFlagHandle.Set("max_upload_size_bytes", 10)

	checkpoint := et.mockRegisterRepos(true, map[string]string{
		"large.txt": "hello, world!",
	}, map[string]struct{}{
		// This file is too large
		"large.txt": {},
	})

	blobs, errWithRateLimit := et.handler.RegisterRepo(ctx, requestContext, et.mockGithubClient,
		repoInfo, tenantID, "test-tenant-name", "main", "000")

	// Verification
	assert.Nil(t, errWithRateLimit)
	assert.Equal(t, "", checkpoint.String())
	assert.Nil(t, blobs)
}

func TestForcePushRegistersAsNewRepo(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	requestContext := requestcontext.New(requestcontext.NewRandomRequestId(), requestcontext.NewRandomRequestSessionId(), "background", secretstring.New("mocked_token"))
	ctx := requestContext.AnnotateLogContext(context.Background())
	tenantID := "test-tenant"

	// Set up expectation for GetSignedTokenForService
	et.mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "test-tenant", []tokenexchangeproto.Scope{tokenexchangeproto.Scope_CONTENT_RW, tokenexchangeproto.Scope_SETTINGS_RW}).Return("mocked_token", nil)

	et.mockGithubClient.On("Get", mock.Anything, "testOwner", "testRepo", mock.Anything).Return(&github.Repository{
		DefaultBranch: github.String("main"),
	}, &github.Response{}, nil)

	// Set up a current state for the repo
	checkpointId := "123"
	et.mockGithubStateClient.On("GetCurrentRefState", mock.Anything, mock.Anything, &githubstatepersistproto.GithubRef{Ref: "main", Repo: &githubstatepersistproto.GithubRepo{RepoOwner: "testOwner", RepoName: "testRepo"}}).Return(&githubstatepersistproto.RefCheckpoint{
		Ref: &githubstatepersistproto.GithubRef{
			Ref:  "main",
			Repo: &githubstatepersistproto.GithubRepo{RepoOwner: "testOwner", RepoName: "testRepo"},
		},
		CommitSha:  "111",
		CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
		Blobs:      &blobnamespb.Blobs{BaselineCheckpointId: &checkpointId},
	}, nil)

	// Now RegisterRepos() should get called
	et.mockRegisterRepos(false, map[string]string{
		"hello.txt": "world!",
	}, nil)

	riResult := &requestinsightproto.RIGithubProcessingResult{}

	// Handle Push should register the repo as a new repo
	err := et.handler.HandlePush(ctx, &githubeventproto.GithubEvent_Push{
		Push: &githubeventproto.PushEvent{
			Repository: &githubeventproto.Repository{
				Owner: "testOwner",
				Name:  "testRepo",
			},
			AfterSha: "000",
			Ref:      "main",
			Forced:   true,
		},
	}, tenantID, "test-tenant-name", requestContext.RequestId, et.mockGithubClient, riResult)

	assert.NoError(t, err)
}

func TestRegisterRepoAugmentIgnore(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	ctx := context.Background()
	requestContext := &requestcontext.RequestContext{}
	tenantID := "test-tenant"
	repoInfo := &settingsproto.RepoInformation{
		RepoOwner: "testOwner",
		RepoName:  "testRepo",
	}

	checkpoint := et.mockRegisterRepos(true, map[string]string{
		"hello.txt":      "world!",
		"hello2.txt":     "world2!",
		"hello.pem":      "secret",
		"world.txt":      "hello!",
		".augmentignore": "hello.txt",
		".gitignore":     "hello2.txt",
	}, map[string]struct{}{
		"hello.txt":  {},
		"hello2.txt": {},
		"hello.pem":  {},
	})

	blobs, errWithRateLimit := et.handler.RegisterRepo(ctx, requestContext, et.mockGithubClient,
		repoInfo, tenantID, "test-tenant-name", "main", "000")

	// Verification
	assert.Nil(t, errWithRateLimit)
	assert.Equal(t, checkpoint.String(), *blobs.BaselineCheckpointId)
}

// Test that parseCompareCommits returns all the information about which files need to be downloaded, modifed, removed, etc. and
// getFileDownloadInfo parses what needs to be downloaded from content manager versus github
func TestParseCompareCommits(t *testing.T) {
	mockContentManager := new(MockContentManagerClient)
	mockGithubStateClient := new(MockGithubStateClient)

	handler := &githubEventHandlerImpl{
		contentManagerClient: mockContentManager,
		githubStateClient:    mockGithubStateClient,
	}

	comparison := &github.CommitsComparison{
		Files: []*github.CommitFile{
			{
				Filename: github.String("new.txt"),
				Status:   github.String("added"),
				Patch:    github.String("@@ -0,0 +1 @@\n+New content"),
			},
			{
				Filename:         github.String("renamed_new.txt"),
				PreviousFilename: github.String("renamed_old.txt"),
				Status:           github.String("renamed"),
				Patch:            github.String("@@ -1 +1 @@\n-Old content\n+Updated content"),
			},
			{
				Filename: github.String("existing.txt"),
				Status:   github.String("removed"),
			},
			{
				Filename: github.String("modified.txt"), // not in content manager
				Status:   github.String("modified"),
				Patch:    github.String("@@ -2,0 +2,1 @@\n-line 1\n+line 1\n@@ -5,1 +5,2 @@\n-line 2\n+line 2\n@@ -10,1 +10,0 @@\n-line 2\n"),
			},
		},
	}

	diffInfos, filenamesToDownload, err := handler.parseCompareCommits(comparison)

	// Assert parseCompareCommits results
	assert.NoError(t, err)
	if err != nil {
		t.Fatalf("parseCompareCommits failed: %v", err)
	}

	assert.NotNil(t, diffInfos)
	assert.NotNil(t, filenamesToDownload)
	assert.Len(t, filenamesToDownload, 3)
	assert.ElementsMatch(t, filenamesToDownload, []string{"renamed_old.txt", "existing.txt", "modified.txt"}, "filenamesToDownload should contain renamed_old.txt, existing.txt, and modified.txt")

	blobName1, err := blobnames.NewBlobNameProto("renamed-blob", false)
	assert.NoError(t, err)
	blobName2, err := blobnames.NewBlobNameProto("existing-blob", false)
	assert.NoError(t, err)

	mockGithubStateClient.On("GetCurrentRefFiles", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(func() (<-chan githubstateclient.GetCurrentRefFilesResult, error) {
		resultChan := make(chan githubstateclient.GetCurrentRefFilesResult, 1)
		go func() {
			defer close(resultChan)
			resultChan <- githubstateclient.GetCurrentRefFilesResult{
				Resp: &githubstatepersistproto.GetCurrentRefFilesResponse{
					CommitSha: "testCommitSha",
					FileInfos: []*githubstatepersistproto.FileInfo{
						{
							FilePath: "renamed_old.txt",
							BlobName: blobName1,
							Status:   &statusproto.Status{Code: int32(codes.OK)},
						},
						{
							FilePath: "existing.txt",
							BlobName: blobName2,
							Status:   &statusproto.Status{Code: int32(codes.OK)},
						},
						{
							FilePath: "modified.txt",
							Status:   &statusproto.Status{Code: int32(codes.NotFound)},
						},
					},
				},
				Err: nil,
			}
		}()
		return resultChan, nil
	}())

	contentManagerDownloads, githubDownloads, err := handler.getFileDownloadInfo(context.Background(), nil, nil, "testCommitSha", filenamesToDownload, diffInfos)

	assert.NoError(t, err)
	assert.NotNil(t, githubDownloads)

	assert.Len(t, contentManagerDownloads.downloadBlobContentKeys, 2)
	assert.Len(t, contentManagerDownloads.filenamesToDownload, 2)
	assert.ElementsMatch(t, contentManagerDownloads.filenamesToDownload, []string{"renamed_old.txt", "existing.txt"}, "filenamesToDownload should contain both renamed_old.txt and existing.txt")

	assert.Len(t, githubDownloads, 1)
	assert.Equal(t, "modified.txt", githubDownloads[0])

	_, ok := diffInfos["new.txt"]
	assert.True(t, ok)

	renamedFileInfo, ok := diffInfos["renamed_old.txt"]
	assert.True(t, ok)
	assert.Equal(t, &DiffInfo{
		filename:    "renamed_new.txt",
		oldBlobName: blobnames.BlobName("renamed-blob"),
		oldFileName: "renamed_old.txt",
		renamed:     true,
	}, renamedFileInfo)

	existingFileInfo, ok := diffInfos["existing.txt"]
	assert.True(t, ok)
	assert.Equal(t, &DiffInfo{
		filename:    "existing.txt",
		oldBlobName: blobnames.BlobName("existing-blob"),
		oldFileName: "existing.txt",
		removed:     true,
	}, existingFileInfo)

	modifiedFileInfo, ok := diffInfos["modified.txt"]
	assert.True(t, ok)
	assert.Equal(t, &DiffInfo{
		filename: "modified.txt",
	}, modifiedFileInfo)
}

// Test applying simple diff adding a file
func TestApplyAddFileDiff(t *testing.T) {
	const tenantID = "test-tenant"
	ctx := context.Background()
	rc := &requestcontext.RequestContext{}
	ref := &githubstatepersistproto.GithubRef{
		Ref: "main",
		Repo: &githubstatepersistproto.GithubRepo{
			RepoOwner: "owner",
			RepoName:  "repo",
		},
	}
	base, head := "baseSHA", "headSHA"

	mockContentManager := new(MockContentManagerClient)
	mockFeatureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	handler := &githubEventHandlerImpl{
		contentManagerClient: mockContentManager,
		featureFlagHandle:    mockFeatureFlagHandle,
	}

	diffInfo := &DiffInfo{
		filename: "a.txt",
		removed:  false,
	}
	diffInfos := map[string]*DiffInfo{
		"a.txt": diffInfo,
	}

	simplePatch := `--- a/a.txt
+++ b/a.txt
@@ -0,0 +1 @@
+New content
`
	expectedNewContent := "New content\n"

	test_dir, err := createTempDir()
	assert.NoError(t, err)

	ignoredFiles, err := handler.applyDiff(ctx, tenantID, rc, simplePatch, test_dir, nil, nil, ref, base, head)
	assert.NoError(t, err)
	assert.Empty(t, ignoredFiles)

	blobsToadd, blobsToRemove, err := handler.processPatchedFiles(ctx, tenantID, diffInfos, test_dir, ignoredFiles)
	assert.NoError(t, err)

	assert.Len(t, blobsToadd, 1)
	assert.Len(t, blobsToRemove, 0)

	// ensure a.txt content has been updated by comparing blobname
	assert.Equal(t, diffInfo.newBlobName, blobnames.GetBlobName("a.txt", []byte(expectedNewContent)))

	// Test an applyDiff case with an initial ignore list, to ensure it gets
	// passed through
	fakeIgnore := []string{"xyz.txt"}
	ignoredFiles, err = handler.applyDiff(ctx, tenantID, rc, simplePatch, test_dir, fakeIgnore, nil, ref, base, head)
	assert.NoError(t, err)
	assert.Equal(t, ignoredFiles, fakeIgnore)
}

// test a diff with modifications, additions, and renames
func TestApplyMultipleDiffs(t *testing.T) {
	const tenantID = "test-tenant"
	ctx := context.Background()
	rc := &requestcontext.RequestContext{}
	ref := &githubstatepersistproto.GithubRef{
		Ref: "main",
		Repo: &githubstatepersistproto.GithubRepo{
			RepoOwner: "owner",
			RepoName:  "repo",
		},
	}
	base, head := "baseSHA", "headSHA"

	mockContentManager := new(MockContentManagerClient)
	mockFeatureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	handler := &githubEventHandlerImpl{
		contentManagerClient: mockContentManager,
		featureFlagHandle:    mockFeatureFlagHandle,
	}

	diffInfo1 := &DiffInfo{
		filename: "a.py",
		removed:  false,
	}

	diffInfo2 := &DiffInfo{
		filename:    "renamed.py",
		oldFileName: "b.py",
		removed:     false,
	}

	// test to make sure diffs are able to apply in directories that don't exist locally
	diffInfo3 := &DiffInfo{
		filename: "test_dir2/another_file.txt",
		removed:  false,
	}

	diffInfos := map[string]*DiffInfo{
		"a.py":                       diffInfo1,
		"renamed.py":                 diffInfo2,
		"test_dir2/another_file.txt": diffInfo3,
	}

	a_before := []byte(`class A:
    def __init__(self):
        self.x = 1

    def method(self):
        return self.x

    def method2(self):
        return self.x


class B(A):
    def __init__(self):
        super().__init__()
        self.x = 2

    def method(self):
        return self.x + self.method2()
`)

	b_content := []byte(`hello`)

	test_dir, err := createTempDir()
	assert.NoError(t, err)

	err = createFile(test_dir, "a.py", a_before)
	assert.NoError(t, err)

	err = createFile(test_dir, "b.py", b_content)
	assert.NoError(t, err)

	patch := `diff --git a/a.py b/a.py
index 536ed5a94..62054e8a5 100644
--- a/a.py
+++ b/a.py
@@ -5,9 +5,6 @@ class A:
     def method(self):
         return self.x

-    def method2(self):
-        return self.x
-

 class B(A):
     def __init__(self):
@@ -15,4 +12,4 @@ class B(A):
         self.x = 2

     def method(self):
-        return self.x + self.method2()
+        return self.x
diff --git a/b.py b/renamed.py
similarity index 100%
rename from b.py
rename to renamed.py
diff --git a/test_dir2/another_file.txt b/test_dir2/another_file.txt
new file mode 100644
index 000000000..a7dc7846f
--- /dev/null
+++ b/test_dir2/another_file.txt
@@ -0,0 +1 @@
+here's a new file in a new dir
`
	ignoredFiles, err := handler.applyDiff(ctx, tenantID, rc, patch, test_dir, nil, nil, ref, base, head)
	assert.NoError(t, err)
	assert.Empty(t, ignoredFiles)

	blobsToadd, _, err := handler.processPatchedFiles(ctx, tenantID, diffInfos, test_dir, ignoredFiles)
	assert.NoError(t, err)

	assert.Len(t, blobsToadd, 3)

	a_after := []byte(`class A:
    def __init__(self):
        self.x = 1

    def method(self):
        return self.x


class B(A):
    def __init__(self):
        super().__init__()
        self.x = 2

    def method(self):
        return self.x
`)

	assert.Equal(t, diffInfo1.newBlobName, blobnames.GetBlobName("a.py", []byte(a_after)))
	assert.Equal(t, diffInfo2.newBlobName, blobnames.GetBlobName("renamed.py", []byte(b_content)))
	assert.Equal(t, diffInfo3.newBlobName, blobnames.GetBlobName("test_dir2/another_file.txt", []byte("here's a new file in a new dir\n")))
}

func TestUploadDiffNoChanges(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	// compare commits to get the diff
	et.mockGithubClient.On("CompareCommits", mock.Anything, "owner", "repo", "baseCommit", "head", mock.Anything).
		Return(&github.CommitsComparison{
			Files: []*github.CommitFile{}, // Empty files list
			Commits: []*github.RepositoryCommit{
				{
					SHA: github.String("head"),
					Parents: []*github.Commit{
						{SHA: github.String("baseCommit")},
					},
				},
			},
			Status: github.String("ahead"),
		}, &github.Response{}, nil)
	et.mockGithubClient.On("CompareCommitsRaw", mock.Anything, "owner", "repo", "baseCommit", "head", mock.Anything).
		Return("", &github.Response{}, nil)

	// There is no diff, so update the current ref checkpoint
	et.mockGithubStateClient.On("UpdateCurrentRefCheckpoint", mock.Anything, mock.Anything, mock.Anything, "head", []string{"baseCommit"}, "baseCommit", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(&githubstatepersistproto.UpdateCurrentRefCheckpointResponse{}, nil)

	riResult := &requestinsightproto.RIGithubProcessingResult{}

	checkpointId := "checkpointId"

	refCheckpoint := &githubstatepersistproto.RefCheckpoint{
		Ref: &githubstatepersistproto.GithubRef{
			Ref: "main",
			Repo: &githubstatepersistproto.GithubRepo{
				RepoOwner: "owner",
				RepoName:  "repo",
			},
		},
		CommitSha: "baseCommit",
		Blobs: &blobnamespb.Blobs{
			BaselineCheckpointId: &checkpointId,
		},
	}

	// Call UploadDiff
	uploadDiffResult, err := et.handler.UploadDiff(
		context.Background(),
		&requestcontext.RequestContext{},
		"tenantID",
		"tenantName",
		et.mockGithubClient,
		refCheckpoint,
		"head",
		&timestamppb.Timestamp{},
		riResult,
	)

	// Assert no error
	assert.NoError(t, err)
	assert.Equal(t, uploadDiffResult, noReRegister)

	// Assert that certain methods were not called (because there were no changes)
	et.mockGithubClient.AssertNotCalled(t, "GetContents")
	et.mockContentManager.AssertNotCalled(t, "UploadBlobs")
}

func TestUploadDiff(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	aBefore := []byte(`import os

def list_all():
    for ent in os.listdir():
        print(ent)
                if os.path.isdir(ent):
                    list_all()
                else:
                    print(ent)

list_all()
`)
	aAfter := []byte(`import os

def list_all(path):
    for ent in os.listdir(path):
        print(ent)
                if os.path.isdir(ent):
                    list_all()
                else:
                    print(ent)

list_all(".")
`)

	bAfter := []byte("b\n")

	cBefore := []byte("c\n")

	diff := `diff --git a/a.py b/a.py
index fcf5493..03e69e0 100644
--- a/a.py
+++ b/a.py
@@ -1,11 +1,11 @@
 import os

-def list_all():
-    for ent in os.listdir():
+def list_all(path):
+    for ent in os.listdir(path):
         print(ent)
                 if os.path.isdir(ent):
                     list_all()
                 else:
                     print(ent)

-list_all()
+list_all(".")
diff --git a/b.py b/b.py
new file mode 100644
index 000000000..a7dc7846f
--- /dev/null
+++ b/b.py
@@ -0,0 +1 @@
+b
diff --git a/c.py b/c.py
deleted file mode 100644
index f2ad6c7..0000000
--- a/c.py
+++ /dev/null
@@ -1 +0,0 @@
-c
`

	// compare commits to get the diff
	et.mockGithubClient.On("CompareCommits", mock.Anything, "owner", "repo", "baseCommit", "head", mock.Anything).
		Return(&github.CommitsComparison{
			Files: []*github.CommitFile{
				{
					Filename: github.String("a.py"),
					Status:   github.String("modified"),
				},
				{
					Filename: github.String("b.py"),
					Status:   github.String("added"),
				},
				{
					Filename: github.String("c.py"),
					Status:   github.String("removed"),
				},
			},
			Commits: []*github.RepositoryCommit{
				{
					SHA: github.String("head"),
					Parents: []*github.Commit{
						{SHA: github.String("baseCommit")},
					},
				},
			},
			Status: github.String("ahead"),
		}, &github.Response{}, nil)
	et.mockGithubClient.On("CompareCommitsRaw", mock.Anything, "owner", "repo", "baseCommit", "head", mock.Anything).
		Return(diff, &github.Response{}, nil)

	// First, try to get ignore file info
	et.mockIgnoreFileDiff(nil, false)

	// Get the contents of the files in the diff
	et.mockGithubStateClient.On("GetCurrentRefFiles", mock.Anything, mock.Anything, &githubstatepersistproto.GithubRef{Ref: "main", Repo: &githubstatepersistproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"}}, "baseCommit", []string{"a.py", "c.py"}).
		Return(func() (<-chan githubstateclient.GetCurrentRefFilesResult, error) {
			fakeFiles := make(chan githubstateclient.GetCurrentRefFilesResult, 10)
			defer close(fakeFiles)
			fakeFiles <- githubstateclient.GetCurrentRefFilesResult{
				Resp: &githubstatepersistproto.GetCurrentRefFilesResponse{
					CommitSha: "baseCommit",
					FileInfos: []*githubstatepersistproto.FileInfo{
						{
							FilePath: "a.py",
							BlobName: blobNameToProto(t, "a.py", aBefore),
							Status:   &statusproto.Status{Code: int32(codes.OK)},
						},
						{
							FilePath: "c.py",
							BlobName: blobNameToProto(t, "c.py", cBefore),
							Status:   &statusproto.Status{Code: int32(codes.OK)},
						},
					},
				},
			}
			return fakeFiles, nil
		}())

	blobkeys := []*contentmanagerproto.BlobContentKey{
		{BlobName: blobnames.GetBlobName("a.py", aBefore).String()},
		{BlobName: blobnames.GetBlobName("c.py", cBefore).String()},
	}
	et.mockContentManager.On("BatchDownloadContent", mock.Anything, blobkeys, mock.Anything, mock.Anything).
		Return(func() (<-chan contentmanagerclient.BatchDownloadContentResult, error) {
			fakeContent := make(chan contentmanagerclient.BatchDownloadContentResult, 10)
			defer close(fakeContent)
			fakeContent <- contentmanagerclient.BatchDownloadContentResult{
				Resp: &contentmanagerproto.BatchGetContentResponse{
					Response: &contentmanagerproto.BatchGetContentResponse_FinalContent{
						FinalContent: &contentmanagerproto.BatchGetContentFinalContent{
							Content:  aBefore,
							Metadata: []*contentmanagerproto.BlobMetadata{{Key: "path", Value: "a.py"}},
						},
					},
				},
			}
			fakeContent <- contentmanagerclient.BatchDownloadContentResult{
				Resp: &contentmanagerproto.BatchGetContentResponse{
					Response: &contentmanagerproto.BatchGetContentResponse_FinalContent{
						FinalContent: &contentmanagerproto.BatchGetContentFinalContent{
							Content:  cBefore,
							Metadata: []*contentmanagerproto.BlobMetadata{{Key: "path", Value: "c.py"}},
						},
					},
				},
			}
			return fakeContent, nil
		}())

	// Apply the diff then update content manager
	uploads := []*contentmanagerproto.UploadBlobContent{
		{
			Content:  aAfter,
			Metadata: []*contentmanagerproto.BlobMetadata{{Key: "path", Value: "a.py"}},
		},
		{
			Content:  bAfter,
			Metadata: []*contentmanagerproto.BlobMetadata{{Key: "path", Value: "b.py"}},
		},
	}
	et.mockContentManager.On("BatchUploadBlobContent", mock.Anything, uploads, mock.Anything, mock.Anything).
		Return([]*contentmanagerproto.UploadBlobResult{
			{BlobName: blobnames.GetBlobName("a.py", aAfter).String()},
			{BlobName: blobnames.GetBlobName("b.py", bAfter).String()},
		}, nil)
	oldCheckpointId, newCheckpointId := "oldCheckpointId", "newCheckpointId"
	blobs := &blobnamespb.Blobs{
		BaselineCheckpointId: &oldCheckpointId,
		Added:                [][]byte{blobNameToBytes(t, "a.py", aAfter), blobNameToBytes(t, "b.py", bAfter)},
		Deleted:              [][]byte{blobNameToBytes(t, "a.py", aBefore), blobNameToBytes(t, "c.py", cBefore)},
	}
	et.mockContentManager.On("CheckpointBlobs", mock.Anything, blobs, mock.Anything).Return(newCheckpointId, nil)

	// Then update the current ref checkpoint
	et.mockGithubStateClient.On("UpdateCurrentRefCheckpoint", mock.Anything, mock.Anything, mock.Anything, "head", []string{"baseCommit"}, "baseCommit", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(&githubstatepersistproto.UpdateCurrentRefCheckpointResponse{}, nil)

	riResult := &requestinsightproto.RIGithubProcessingResult{}

	refCheckpoint := &githubstatepersistproto.RefCheckpoint{
		Ref: &githubstatepersistproto.GithubRef{
			Ref: "main",
			Repo: &githubstatepersistproto.GithubRepo{
				RepoOwner: "owner",
				RepoName:  "repo",
			},
		},
		CommitSha: "baseCommit",
		Blobs:     &blobnamespb.Blobs{BaselineCheckpointId: &oldCheckpointId},
	}

	// Call UploadDiff
	uploadDiffResult, err := et.handler.UploadDiff(
		context.Background(),
		&requestcontext.RequestContext{},
		"tenantID",
		"tenantName",
		et.mockGithubClient,
		refCheckpoint,
		"head",
		&timestamppb.Timestamp{},
		riResult,
	)

	// Assert no error
	assert.NoError(t, err)
	assert.Equal(t, uploadDiffResult, noReRegister)
}

func TestUploadDiffAugmentIgnore(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	augmentIgnore := []byte(`*.py
!d.py
`)

	dBefore := []byte("dBefore\n")
	dAfter := []byte("dAfter\n")

	diff := `diff --git a/a.py b/a.py
index fcf5493..03e69e0 100644
--- a/a.py
+++ b/a.py
@@ -1,11 +1,11 @@
 import os

-def list_all():
-    for ent in os.listdir():
+def list_all(path):
+    for ent in os.listdir(path):
         print(ent)
                 if os.path.isdir(ent):
                     list_all()
                 else:
                     print(ent)

-list_all()
+list_all(".")
diff --git a/b.py b/b.py
new file mode 100644
index 000000000..a7dc7846f
--- /dev/null
+++ b/b.py
@@ -0,0 +1 @@
+b
diff --git a/c.py b/c.py
deleted file mode 100644
index f2ad6c7..0000000
--- a/c.py
+++ /dev/null
@@ -1 +0,0 @@
-c
diff --git a/d.py b/d.py
index 1234567..8901234 100644
--- a/d.py
+++ b/d.py
@@ -1 +1 @@
-dBefore
+dAfter
`

	// compare commits to get the diff
	et.mockGithubClient.On("CompareCommits", mock.Anything, "owner", "repo", "baseCommit", "head", mock.Anything).
		Return(&github.CommitsComparison{
			Files: []*github.CommitFile{
				{
					Filename: github.String("a.py"),
					Status:   github.String("modified"),
				},
				{
					Filename: github.String("b.py"),
					Status:   github.String("added"),
				},
				{
					Filename: github.String("c.py"),
					Status:   github.String("removed"),
				},
				{
					Filename: github.String("d.py"),
					Status:   github.String("modified"),
				},
			},
			Commits: []*github.RepositoryCommit{
				{
					SHA: github.String("head"),
					Parents: []*github.Commit{
						{SHA: github.String("baseCommit")},
					},
				},
			},
			Status: github.String("ahead"),
		}, &github.Response{}, nil)
	et.mockGithubClient.On("CompareCommitsRaw", mock.Anything, "owner", "repo", "baseCommit", "head", mock.Anything).
		Return(diff, &github.Response{}, nil)

	// First, try to get ignore file info
	et.mockIgnoreFileDiff(augmentIgnore, false)

	// Get the contents of the files in the diff
	et.mockGithubStateClient.On("GetCurrentRefFiles", mock.Anything, mock.Anything, &githubstatepersistproto.GithubRef{Ref: "main", Repo: &githubstatepersistproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"}}, "baseCommit", []string{"d.py"}).
		Return(func() (<-chan githubstateclient.GetCurrentRefFilesResult, error) {
			fakeFiles := make(chan githubstateclient.GetCurrentRefFilesResult, 10)
			defer close(fakeFiles)
			fakeFiles <- githubstateclient.GetCurrentRefFilesResult{
				Resp: &githubstatepersistproto.GetCurrentRefFilesResponse{
					CommitSha: "baseCommit",
					FileInfos: []*githubstatepersistproto.FileInfo{
						{
							FilePath: "d.py",
							BlobName: blobNameToProto(t, "d.py", dBefore),
							Status:   &statusproto.Status{Code: int32(codes.OK)},
						},
					},
				},
			}
			return fakeFiles, nil
		}())

	blobkeys := []*contentmanagerproto.BlobContentKey{
		{BlobName: blobnames.GetBlobName("d.py", dBefore).String()},
	}
	et.mockContentManager.On("BatchDownloadContent", mock.Anything, blobkeys, mock.Anything, mock.Anything).
		Return(func() (<-chan contentmanagerclient.BatchDownloadContentResult, error) {
			fakeContent := make(chan contentmanagerclient.BatchDownloadContentResult, 10)
			defer close(fakeContent)
			fakeContent <- contentmanagerclient.BatchDownloadContentResult{
				Resp: &contentmanagerproto.BatchGetContentResponse{
					Response: &contentmanagerproto.BatchGetContentResponse_FinalContent{
						FinalContent: &contentmanagerproto.BatchGetContentFinalContent{
							Content:  dBefore,
							Metadata: []*contentmanagerproto.BlobMetadata{{Key: "path", Value: "d.py"}},
						},
					},
				},
			}
			return fakeContent, nil
		}())
	// No other content downloads

	// Apply the diff then update content manager
	uploads := []*contentmanagerproto.UploadBlobContent{
		{
			Content:  dAfter,
			Metadata: []*contentmanagerproto.BlobMetadata{{Key: "path", Value: "d.py"}},
		},
	}
	et.mockContentManager.On("BatchUploadBlobContent", mock.Anything, uploads, mock.Anything, mock.Anything).
		Return([]*contentmanagerproto.UploadBlobResult{
			{BlobName: blobnames.GetBlobName("d.py", dAfter).String()},
		}, nil)
	oldCheckpointId, newCheckpointId := "oldCheckpointId", "newCheckpointId"
	blobs := &blobnamespb.Blobs{
		BaselineCheckpointId: &oldCheckpointId,
		Added:                [][]byte{blobNameToBytes(t, "d.py", dAfter)},
		Deleted:              [][]byte{blobNameToBytes(t, "d.py", dBefore)},
	}
	et.mockContentManager.On("CheckpointBlobs", mock.Anything, blobs, mock.Anything).Return(newCheckpointId, nil)

	// Then update the current ref checkpoint
	blobs = &blobnamespb.Blobs{
		BaselineCheckpointId: &newCheckpointId,
	}
	diffInfos := []*githubstatepersistproto.DiffInfo{
		{
			FilePath: "d.py",
			Change: &githubstatepersistproto.DiffInfo_ContentBlobName{
				ContentBlobName: blobNameToProto(t, "d.py", dAfter),
			},
		},
	}
	et.mockGithubStateClient.On("UpdateCurrentRefCheckpoint", mock.Anything, mock.Anything, mock.Anything, "head", []string{"baseCommit"}, "baseCommit", mock.Anything, blobs, diffInfos, false).
		Return(&githubstatepersistproto.UpdateCurrentRefCheckpointResponse{}, nil)

	riResult := &requestinsightproto.RIGithubProcessingResult{}

	refCheckpoint := &githubstatepersistproto.RefCheckpoint{
		Ref: &githubstatepersistproto.GithubRef{
			Ref: "main",
			Repo: &githubstatepersistproto.GithubRepo{
				RepoOwner: "owner",
				RepoName:  "repo",
			},
		},
		CommitSha: "baseCommit",
		Blobs:     &blobnamespb.Blobs{BaselineCheckpointId: &oldCheckpointId},
	}

	// Call UploadDiff
	uploadDiffResult, err := et.handler.UploadDiff(
		context.Background(),
		&requestcontext.RequestContext{},
		"tenantID",
		"tenantName",
		et.mockGithubClient,
		refCheckpoint,
		"head",
		&timestamppb.Timestamp{},
		riResult,
	)

	// Assert no error
	assert.NoError(t, err)
	assert.Equal(t, uploadDiffResult, noReRegister)
}

func TestUploadDiffAugmentIgnoreUploadDiffResult(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	// Create a diff with changes to both ignored and non-ignored files
	diffContent := `diff --git a/.augmentignore b/.augmentignore
index 0000000..1234567 100644
--- a/.augmentignore
+++ b/.augmentignore
@@ -0,0 +1 @@
+ignored.txt
diff --git a/ignored.txt b/ignored.txt
index 0000000..1234567 100644
--- a/ignored.txt
+++ b/ignored.txt
@@ -0,0 +1 @@
+This file should be ignored
diff --git a/not_ignored.txt b/not_ignored.txt
index 0000000..1234567 100644
--- a/not_ignored.txt
+++ b/not_ignored.txt
@@ -0,0 +1 @@
+This file should not be ignored
`

	// We should run both CompareCommits then realize that we have an
	// augmentignore file change and return uploadDiffResult=true
	et.mockGithubClient.On("CompareCommits", mock.Anything, "testOwner", "testRepo", "baseSHA", "headSHA", mock.Anything).
		Return(&github.CommitsComparison{
			Files: []*github.CommitFile{
				{Filename: github.String(".augmentignore"), Status: github.String("added")},
				{Filename: github.String("ignored.txt"), Status: github.String("added")},
				{Filename: github.String("not_ignored.txt"), Status: github.String("added")},
			},
			Commits: []*github.RepositoryCommit{
				{
					SHA: github.String("headSHA"),
					Parents: []*github.Commit{
						{SHA: github.String("baseSHA")},
					},
				},
			},
			Status: github.String("ahead"),
		}, &github.Response{}, nil)
	et.mockGithubClient.On("CompareCommitsRaw", mock.Anything, "testOwner", "testRepo", "baseSHA", "headSHA", mock.Anything).
		Return(diffContent, &github.Response{}, nil)

	// Create the refCheckpoint
	refCheckpoint := &githubstatepersistproto.RefCheckpoint{
		Ref: &githubstatepersistproto.GithubRef{
			Ref: "main",
			Repo: &githubstatepersistproto.GithubRepo{
				RepoOwner: "testOwner",
				RepoName:  "testRepo",
			},
		},
		CommitSha: "baseSHA",
		Blobs: &blobnamespb.Blobs{
			BaselineCheckpointId: github.String("old_checkpoint_id"),
		},
	}

	// Call UploadDiff
	uploadDiffResult, err := et.handler.UploadDiff(
		context.Background(),
		&requestcontext.RequestContext{},
		"test-tenant",
		"test-tenant-name",
		et.mockGithubClient,
		refCheckpoint,
		"headSHA",
		&timestamppb.Timestamp{},
		&requestinsightproto.RIGithubProcessingResult{},
	)

	// Assert no error and uploadDiffResult is true
	assert.NoError(t, err)
	assert.Equal(t, ignoreFileChanged, uploadDiffResult)
}

func TestUploadDiffReRegisterOnLargeChanges(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	ctx := context.Background()
	tenantID := "test-tenant"

	// Mock CompareCommits to return a large number of files
	et.mockGithubClient.On("CompareCommits", mock.Anything, "testOwner", "testRepo", "111", "000", mock.Anything).
		Return(&github.CommitsComparison{
			Files:   make([]*github.CommitFile, 300),
			Commits: []*github.RepositoryCommit{{SHA: github.String("new-commit-sha")}},
			Status:  github.String("ahead"),
		}, &github.Response{}, nil)

	// Set up expectation for GetSignedTokenForService
	et.mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "test-tenant", []tokenexchangeproto.Scope{tokenexchangeproto.Scope_CONTENT_RW, tokenexchangeproto.Scope_SETTINGS_RW}).Return("mocked_token", nil)

	et.mockGithubClient.On("Get", mock.Anything, "testOwner", "testRepo", mock.Anything).Return(&github.Repository{
		DefaultBranch: github.String("main"),
	}, &github.Response{}, nil)

	// Set up a current state for the repo
	checkpointId := "123"
	et.mockGithubStateClient.On("GetCurrentRefState", mock.Anything, mock.Anything, &githubstatepersistproto.GithubRef{Ref: "main", Repo: &githubstatepersistproto.GithubRepo{RepoOwner: "testOwner", RepoName: "testRepo"}}).Return(&githubstatepersistproto.RefCheckpoint{
		Ref: &githubstatepersistproto.GithubRef{
			Ref:  "main",
			Repo: &githubstatepersistproto.GithubRepo{RepoOwner: "testOwner", RepoName: "testRepo"},
		},
		CommitSha:  "111",
		CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
		Blobs:      &blobnamespb.Blobs{BaselineCheckpointId: &checkpointId},
	}, nil)

	// Now RegisterRepos() should get called
	et.mockRegisterRepos(false, map[string]string{
		"hello.txt": "world!",
	}, nil)

	requestId := requestcontext.NewRandomRequestId()
	riResult := &requestinsightproto.RIGithubProcessingResult{}

	// Handle Push should register the repo as a new repo
	err := et.handler.HandlePush(ctx, &githubeventproto.GithubEvent_Push{
		Push: &githubeventproto.PushEvent{
			Repository: &githubeventproto.Repository{
				Owner: "testOwner",
				Name:  "testRepo",
			},
			AfterSha: "000",
			Ref:      "main",
		},
	}, tenantID, "test-tenant-name", requestId, et.mockGithubClient, riResult)

	// Assert expected results
	assert.NoError(t, err)
}

func TestUploadDiffReRegisterOnFileRenameIgnore(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	augmentIgnore := []byte("*.out\n")

	// file1 := []byte("hello 1\n")
	// file2 := []byte("hello 2\n")
	// file3 := []byte("hello 3\n")
	// file4 := []byte("hello 4\n")

	diff := `diff --git a/file1.out b/file1.txt
similarity index 100%
rename from file1.out
rename to file1.txt
diff --git a/file2.txt b/file2.out
similarity index 100%
rename from file2.txt
rename to file2.out
diff --git a/file3.txt b/file3.py
similarity index 100%
rename from file3.txt
rename to file3.py
diff --git a/file4.out b/file4_v2.out
similarity index 100%
rename from file4.out
rename to file4_v2.out
`

	// compare commits to get the diff
	et.mockGithubClient.On("CompareCommits", mock.Anything, "owner", "repo", "baseCommit", "head", mock.Anything).
		Return(&github.CommitsComparison{
			Files: []*github.CommitFile{
				{
					// ignored -> included
					Filename:         github.String("file1.out"),
					PreviousFilename: github.String("file1.txt"),
					Status:           github.String("renamed"),
				},
				{
					// included -> ignored
					Filename:         github.String("file2.txt"),
					PreviousFilename: github.String("file2.out"),
					Status:           github.String("renamed"),
				},
				{
					// stays included
					Filename:         github.String("file3.py"),
					PreviousFilename: github.String("file3.txt"),
					Status:           github.String("renamed"),
				},
				{
					// stays ignored
					Filename:         github.String("file4.out"),
					PreviousFilename: github.String("file4_v2.out"),
					Status:           github.String("renamed"),
				},
			},
			Commits: []*github.RepositoryCommit{
				{
					SHA: github.String("head"),
					Parents: []*github.Commit{
						{SHA: github.String("baseCommit")},
					},
				},
			},
			Status: github.String("ahead"),
		}, &github.Response{}, nil)
	et.mockGithubClient.On("CompareCommitsRaw", mock.Anything, "owner", "repo", "baseCommit", "head", mock.Anything).
		Return(diff, &github.Response{}, nil)

	// First, try to get ignore file info
	et.mockIgnoreFileDiff(augmentIgnore, false)

	riResult := &requestinsightproto.RIGithubProcessingResult{}
	oldCheckpointId := "oldCheckpointId"
	refCheckpoint := &githubstatepersistproto.RefCheckpoint{
		Ref: &githubstatepersistproto.GithubRef{
			Ref: "main",
			Repo: &githubstatepersistproto.GithubRepo{
				RepoOwner: "owner",
				RepoName:  "repo",
			},
		},
		CommitSha: "baseCommit",
		Blobs:     &blobnamespb.Blobs{BaselineCheckpointId: &oldCheckpointId},
	}

	// Call UploadDiff
	uploadDiffResult, err := et.handler.UploadDiff(
		context.Background(),
		&requestcontext.RequestContext{},
		"tenantID",
		"tenantName",
		et.mockGithubClient,
		refCheckpoint,
		"head",
		&timestamppb.Timestamp{},
		riResult,
	)

	// Assert no error
	assert.NoError(t, err)
	assert.Equal(t, uploadDiffResult, fileIgnoreStatusChanged)
}

func TestUploadDiffWithSubmodule(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	diff := `diff --git a/submodule b/submodule
index abc1234..def5678 160000
--- a/submodule
+++ b/submodule
@@ -1 +1 @@
-Subproject commit abc1234deadbeefcafe
\ No newline at end of file
+Subproject commit def5678deadbeefcafe
\ No newline at end of file
`

	// Mock CompareCommits to return a submodule change
	et.mockGithubClient.On("CompareCommits", mock.Anything, "owner", "repo", "baseCommit", "head", mock.Anything).
		Return(&github.CommitsComparison{
			Files: []*github.CommitFile{
				{
					Filename: github.String("submodule"),
					Status:   github.String("modified"),
					Patch:    github.String(diff),
				},
			},
			Commits: []*github.RepositoryCommit{
				{
					SHA: github.String("head"),
					Parents: []*github.Commit{
						{SHA: github.String("baseCommit")},
					},
				},
			},
			Status: github.String("ahead"),
		}, &github.Response{}, nil)

	// Mock CompareCommitsRaw
	et.mockGithubClient.On("CompareCommitsRaw", mock.Anything, "owner", "repo", "baseCommit", "head", mock.Anything).
		Return(diff, &github.Response{}, nil)

	// Mock the ignore file check
	et.mockIgnoreFileDiff(nil, false)

	// Mock GetCurrentRefFiles with a missing submodule
	et.mockGithubStateClient.On("GetCurrentRefFiles", mock.Anything, mock.Anything, &githubstatepersistproto.GithubRef{Ref: "main", Repo: &githubstatepersistproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"}}, "baseCommit", []string{"submodule"}).
		Return(func() (<-chan githubstateclient.GetCurrentRefFilesResult, error) {
			fakeFiles := make(chan githubstateclient.GetCurrentRefFilesResult, 10)
			fakeFiles <- githubstateclient.GetCurrentRefFilesResult{
				Resp: &githubstatepersistproto.GetCurrentRefFilesResponse{
					CommitSha: "baseCommit",
					FileInfos: []*githubstatepersistproto.FileInfo{
						{
							FilePath: "submodule",
							Status:   &statusproto.Status{Code: int32(codes.NotFound)},
						},
					},
				},
			}
			defer close(fakeFiles)
			return fakeFiles, nil
		}())

	// Mock GetContents to return a "no download link" error for the submodule
	var nilContents []*github.RepositoryContent
	et.mockGithubClient.On("GetContents",
		mock.Anything,
		"owner",
		"repo",
		"submodule",
		&github.RepositoryContentGetOptions{Ref: "baseCommit"},
	).Return(
		&github.RepositoryContent{
			DownloadURL: nil,
			Type:        github.String("submodule"),
		},
		nilContents,
		&github.Response{Response: &http.Response{StatusCode: http.StatusOK}},
		nil,
	)

	oldCheckpointId := "oldCheckpointId"
	refCheckpoint := &githubstatepersistproto.RefCheckpoint{
		Ref: &githubstatepersistproto.GithubRef{
			Ref: "main",
			Repo: &githubstatepersistproto.GithubRepo{
				RepoOwner: "owner",
				RepoName:  "repo",
			},
		},
		CommitSha: "baseCommit",
		Blobs:     &blobnamespb.Blobs{BaselineCheckpointId: &oldCheckpointId},
	}

	riResult := &requestinsightproto.RIGithubProcessingResult{}

	et.mockGithubStateClient.On("UpdateCurrentRefCheckpoint", mock.Anything, mock.Anything, mock.Anything, "head", []string{"baseCommit"}, "baseCommit", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(&githubstatepersistproto.UpdateCurrentRefCheckpointResponse{}, nil)

	// Call UploadDiff
	uploadDiffResult, err := et.handler.UploadDiff(
		context.Background(),
		&requestcontext.RequestContext{},
		"tenantID",
		"tenantName",
		et.mockGithubClient,
		refCheckpoint,
		"head",
		&timestamppb.Timestamp{},
		riResult,
	)

	// Assert results
	assert.NoError(t, err)
	assert.Equal(t, uploadDiffResult, noReRegister)

	// Verify that no content was uploaded (since it's a submodule)
	et.mockContentManager.AssertNotCalled(t, "BatchUploadBlobContent")
}

func TestCreateCheckpointSplitsIntoMultipleCalls(t *testing.T) {
	// Create a mock content manager client
	mockContentManager := new(MockContentManagerClient)

	// Create the githubEventHandlerImpl
	geh := &githubEventHandlerImpl{
		contentManagerClient: mockContentManager,
	}

	// Create 2002 blob names
	blobNamesAdded := make([]blobnames.BlobName, 20002)
	for i := range blobNamesAdded {
		bytes := make([]byte, 32)

		// Fill the first 4 bytes with a counter
		binary.BigEndian.PutUint32(bytes, uint32(i))

		// Fill the rest with a fixed pattern
		for j := 4; j < 32; j++ {
			bytes[j] = byte(j)
		}

		// Convert to hex string
		blobNamesAdded[i] = blobnames.BlobName(hex.EncodeToString(bytes))
	}

	blobNamesDeleted := make([]blobnames.BlobName, 2)
	for i := range blobNamesDeleted {
		bytes := make([]byte, 32)

		// Fill the first 4 bytes with a counter
		binary.BigEndian.PutUint32(bytes, uint32(i))

		// Fill the rest with a fixed pattern
		for j := 4; j < 32; j++ {
			bytes[j] = byte(j)
		}

		// Convert to hex string
		blobNamesDeleted[i] = blobnames.BlobName(hex.EncodeToString(bytes))
	}

	// Set up the mock expectation - CheckpointBlobs should be called 3 times
	// the checkpoint returned from the previous call should be passed as the baseline checkpoint in the next call
	// deleted blobs should only be in the first call
	mockContentManager.On("CheckpointBlobs",
		mock.Anything,
		mock.AnythingOfType("*proto.Blobs"),
		mock.Anything,
	).Run(func(args mock.Arguments) {
		blobs := args.Get(1).(*blobnamespb.Blobs)
		assert.Nil(t, blobs.BaselineCheckpointId)
		assert.Equal(t, 9998, len(blobs.Added))
		assert.Equal(t, 2, len(blobs.Deleted))
	}).Return("checkpoint_id_1", nil).Once()

	mockContentManager.On("CheckpointBlobs",
		mock.Anything,
		mock.AnythingOfType("*proto.Blobs"),
		mock.Anything,
	).Run(func(args mock.Arguments) {
		blobs := args.Get(1).(*blobnamespb.Blobs)
		assert.Equal(t, "checkpoint_id_1", *blobs.BaselineCheckpointId)
		assert.Equal(t, 10000, len(blobs.Added))
		assert.Nil(t, blobs.Deleted)
	}).Return("checkpoint_id_2", nil).Once()

	mockContentManager.On("CheckpointBlobs",
		mock.Anything,
		mock.AnythingOfType("*proto.Blobs"),
		mock.Anything,
	).Run(func(args mock.Arguments) {
		blobs := args.Get(1).(*blobnamespb.Blobs)
		assert.Equal(t, "checkpoint_id_2", *blobs.BaselineCheckpointId)
		assert.Equal(t, 4, len(blobs.Added))
		assert.Nil(t, blobs.Deleted)
	}).Return("checkpoint_id_3", nil).Once()

	// Call createCheckpoint
	result, err := geh.createCheckpoint(context.Background(), blobNamesAdded, blobNamesDeleted, nil, &requestcontext.RequestContext{})

	// Assert no error
	assert.NoError(t, err)

	// Verify that CheckpointBlobs was called 3 times
	mockContentManager.AssertNumberOfCalls(t, "CheckpointBlobs", 3)

	// Verify the final result
	assert.NotNil(t, result)
	assert.NotNil(t, result.BaselineCheckpointId)
	assert.Equal(t, "checkpoint_id_3", *result.BaselineCheckpointId)
}

func (et *eventHandlerTest) setupInstallationTest(action string, settingsIncludesRepo bool, extraRepos []*githubproto.Repository) *githubproto.GithubEvent_Installation {
	et.t.Helper()
	et.mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "test-tenant", []tokenexchangeproto.Scope{tokenexchangeproto.Scope_CONTENT_RW, tokenexchangeproto.Scope_SETTINGS_RW}).Return("mocked_token", nil)

	settings := &settingsserviceproto.TenantSettings{
		GithubSettings: &settingsserviceproto.GithubSettings{
			InstallationId: int64(123),
		},
	}
	repos := append(extraRepos, &githubproto.Repository{
		Owner: "testOwner",
		Name:  "testRepo",
	})

	if settingsIncludesRepo {
		for _, repo := range repos {
			settings.GithubSettings.Repos = append(settings.GithubSettings.Repos, &settingsproto.RepoInformation{
				RepoOwner: repo.Owner,
				RepoName:  repo.Name,
			})
		}
	}

	et.mockSettingsClient.On("GetTenantSettings", mock.Anything, mock.Anything).Return(&settingsserviceproto.GetTenantSettingsResponse{
		Settings: settings,
	}, nil).Once()

	return &githubproto.GithubEvent_Installation{
		Installation: &githubproto.InstallationEvent{
			Repos:  repos,
			Action: action,
		},
	}
}

func TestHandleInstallationNewRepo(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	ctx := context.Background()
	tenantID := "test-tenant"
	tenantName := "test-tenant-name"
	requestId := requestcontext.NewRandomRequestId()

	installEvent := et.setupInstallationTest("added", false, nil)

	et.mockGithubClient.On("Get", mock.Anything, "testOwner", "testRepo", mock.Anything).Return(&github.Repository{
		DefaultBranch: github.String("main"),
	}, &github.Response{}, nil)

	et.mockRegisterRepos(true, map[string]string{
		"hello.txt": "world!",
	}, nil)

	et.mockSettingsClient.On("UpdateTenantSettings", mock.Anything, mock.AnythingOfType("*requestcontext.RequestContext"), &settingsserviceproto.TenantSettings{
		GithubSettings: &settingsserviceproto.GithubSettings{
			InstallationId: 123,
			Repos: []*settingsproto.RepoInformation{
				{
					RepoOwner: "testOwner",
					RepoName:  "testRepo",
				},
			},
		},
	}, "").Return(&settingsserviceproto.UpdateTenantSettingsResponse{}, nil)

	err := et.handler.HandleInstallation(ctx, installEvent, tenantID, tenantName, requestId, et.mockGithubClient)
	assert.NoError(t, err)
}

func TestHandleUninstallSingleRepo(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	ctx := context.Background()
	tenantID := "test-tenant"
	tenantName := "test-tenant-name"
	requestId := requestcontext.NewRandomRequestId()

	installEvent := et.setupInstallationTest("removed", true, nil)

	et.mockGithubStateClient.On("DeleteGithubStateForRepos",
		mock.Anything,
		mock.Anything,
		[]*githubstatepersistproto.GithubRepo{
			{RepoOwner: "testOwner", RepoName: "testRepo"},
		},
	).Return(nil)
	et.mockSettingsClient.On("UpdateTenantSettings", mock.Anything, mock.AnythingOfType("*requestcontext.RequestContext"), &settingsserviceproto.TenantSettings{
		GithubSettings: &settingsserviceproto.GithubSettings{
			InstallationId: 123,
			Repos:          []*settingsproto.RepoInformation{},
		},
	}, "").Return(&settingsserviceproto.UpdateTenantSettingsResponse{}, nil)

	err := et.handler.HandleInstallation(ctx, installEvent, tenantID, tenantName, requestId, et.mockGithubClient)
	assert.NoError(t, err)
}

func TestHandleAppUninstallation(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	ctx := context.Background()
	tenantID := "test-tenant"
	tenantName := "test-tenant-name"
	requestId := requestcontext.NewRandomRequestId()

	installEvent := et.setupInstallationTest("deleted", true, nil)

	et.mockGithubStateClient.On("DeleteGithubStateForRepos",
		mock.Anything,
		mock.Anything,
		[]*githubstatepersistproto.GithubRepo{
			{RepoOwner: "testOwner", RepoName: "testRepo"},
		},
	).Return(nil)
	et.mockSettingsClient.On("UpdateTenantSettings", mock.Anything, mock.AnythingOfType("*requestcontext.RequestContext"), &settingsserviceproto.TenantSettings{
		GithubSettings: &settingsserviceproto.GithubSettings{},
	}, "").Return(&settingsserviceproto.UpdateTenantSettingsResponse{}, nil)

	et.mockWebhookTenantMappingResource.On("Delete", mock.Anything, "github-test-tenant").Return(nil)
	et.mockLagTracker.On("stopTenantLagTracker", tenantID)
	et.mockRequestInsightPublisher.On("PublishRequestEvent", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

	err := et.handler.HandleInstallation(ctx, installEvent, tenantID, tenantName, requestId, et.mockGithubClient)
	assert.NoError(t, err)
}

func blobNamesToBytes(t *testing.T, blobNames []blobnames.BlobName) [][]byte {
	t.Helper()
	blobNamesBytes := make([][]byte, 0, len(blobNames))
	for _, blobName := range blobNames {
		blobBytes, err := blobnames.DecodeHexBlobName(blobName)
		assert.NoError(t, err)
		blobNamesBytes = append(blobNamesBytes, blobBytes)
	}
	return blobNamesBytes
}

func TestRegisterEmptyRepo(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	ctx := context.Background()
	tenantID := "test-tenant"
	requestContext := &requestcontext.RequestContext{}

	et.mockGithubClient.On("GetCommit", mock.Anything, "testOwner", "testRepo", mock.Anything, mock.Anything).Return(&github.RepositoryCommit{}, &github.Response{Response: &http.Response{StatusCode: 409}}, errors.New("empty repo"))

	blobs, errWithRateLimit := et.handler.RegisterRepo(ctx, requestContext, et.mockGithubClient,
		&settingsproto.RepoInformation{
			RepoOwner: "testOwner",
			RepoName:  "testRepo",
		}, tenantID, "test-tenant-name", "main", "000")

	assert.Nil(t, errWithRateLimit)
	assert.Equal(t, &blobsproto.Blobs{}, blobs)
}

type mockTarballHttpTransport struct {
	repoTarball []byte
}

func (mt *mockTarballHttpTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	return &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(bytes.NewReader(mt.repoTarball)),
	}, nil
}

func TestHandleInstallationRateLimit_Get(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	ctx := context.Background()
	tenantID := "test-tenant"
	tenantName := "test-tenant-name"
	requestId := requestcontext.NewRandomRequestId()

	installEvent := et.setupInstallationTest("added", false, nil)

	// First call hits rate limit
	resetTime := time.Now().Add(time.Second)
	et.mockGithubClient.On("Get", mock.Anything, "testOwner", "testRepo", mock.Anything).
		Return(&github.Repository{}, &github.Response{
			Rate: github.Rate{
				Remaining: 0,
				Reset:     github.Timestamp{Time: resetTime},
			},
		}, fmt.Errorf("rate limit exceeded")).Once()

	// Second call succeeds
	et.mockGithubClient.On("Get", mock.Anything, "testOwner", "testRepo", mock.Anything).
		Return(&github.Repository{
			DefaultBranch: github.String("main"),
		}, &github.Response{}, nil).Once()

	et.mockRegisterRepos(true, map[string]string{
		"hello.txt": "world!",
	}, nil)

	et.mockSettingsClient.On("UpdateTenantSettings", mock.Anything, mock.AnythingOfType("*requestcontext.RequestContext"), &settingsserviceproto.TenantSettings{
		GithubSettings: &settingsserviceproto.GithubSettings{
			InstallationId: 123,
			Repos: []*settingsproto.RepoInformation{
				{
					RepoOwner: "testOwner",
					RepoName:  "testRepo",
				},
			},
		},
	}, "").Return(&settingsserviceproto.UpdateTenantSettingsResponse{}, nil)

	err := et.handler.HandleInstallation(ctx, installEvent, tenantID, tenantName, requestId, et.mockGithubClient)
	assert.NoError(t, err)
}

func TestHandleInstallationRateLimit_GetCommit(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	ctx := context.Background()
	tenantID := "test-tenant"
	tenantName := "test-tenant-name"
	requestId := requestcontext.NewRandomRequestId()

	installEvent := et.setupInstallationTest("added", false, nil)

	et.mockGithubClient.On("Get", mock.Anything, "testOwner", "testRepo", mock.Anything).
		Return(&github.Repository{
			DefaultBranch: github.String("main"),
		}, &github.Response{}, nil)

	// First call to GetCommit hits rate limit
	et.mockGithubClient.On("GetCommit", mock.Anything, "testOwner", "testRepo", mock.Anything, mock.Anything).
		Return(&github.RepositoryCommit{}, &github.Response{
			Response: &http.Response{
				StatusCode: http.StatusForbidden, // 409
			},
			Rate: github.Rate{
				Remaining: 0,
				Reset:     github.Timestamp{Time: time.Now().Add(time.Second)},
			},
		}, fmt.Errorf("rate limit exceeded")).Once()

	// Second call within this helper succeeds
	et.mockRegisterRepos(true, map[string]string{
		"hello.txt": "world!",
	}, nil)

	et.mockSettingsClient.On("UpdateTenantSettings", mock.Anything, mock.AnythingOfType("*requestcontext.RequestContext"), &settingsserviceproto.TenantSettings{
		GithubSettings: &settingsserviceproto.GithubSettings{
			InstallationId: 123,
			Repos: []*settingsproto.RepoInformation{
				{
					RepoOwner: "testOwner",
					RepoName:  "testRepo",
				},
			},
		},
	}, "").Return(&settingsserviceproto.UpdateTenantSettingsResponse{}, nil)

	err := et.handler.HandleInstallation(ctx, installEvent, tenantID, tenantName, requestId, et.mockGithubClient)
	assert.NoError(t, err)
}

func TestHandleInstallationMissingRepo(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	ctx := context.Background()
	tenantID := "test-tenant"
	tenantName := "test-tenant-name"
	requestId := requestcontext.NewRandomRequestId()

	installEvent := et.setupInstallationTest("added", false, []*githubproto.Repository{
		{
			Owner: "testOwner",
			Name:  "missingRepo",
		},
	})

	// We add both repos initially
	et.mockSettingsClient.On("UpdateTenantSettings", mock.Anything, mock.Anything, &settingsserviceproto.TenantSettings{
		GithubSettings: &settingsserviceproto.GithubSettings{
			InstallationId: 123,
			Repos: []*settingsproto.RepoInformation{
				{
					RepoOwner: "testOwner",
					RepoName:  "missingRepo",
				},
				{
					RepoOwner: "testOwner",
					RepoName:  "testRepo",
				},
			},
		},
	}, "").Return(&settingsserviceproto.UpdateTenantSettingsResponse{}, nil).Once()

	et.mockGithubClient.On("Get", mock.Anything, "testOwner", "testRepo", mock.Anything).Return(&github.Repository{
		DefaultBranch: github.String("main"),
	}, &github.Response{}, nil).Once()
	et.mockRegisterRepos(true, map[string]string{
		"hello.txt": "world!",
	}, nil)

	et.mockGithubClient.On("Get", mock.Anything, "testOwner", "missingRepo", mock.Anything).
		Return(&github.Repository{}, &github.Response{
			Response: &http.Response{
				StatusCode: http.StatusNotFound, // 404
			},
			Rate: github.Rate{
				Remaining: 100,
				Reset:     github.Timestamp{Time: time.Now().Add(time.Second)},
			},
		}, fmt.Errorf("repo not found")).Once()

	// But then remove one repo after we see it's missing
	et.mockSettingsClient.On("UpdateTenantSettings", mock.Anything, mock.Anything, &settingsserviceproto.TenantSettings{
		GithubSettings: &settingsserviceproto.GithubSettings{
			InstallationId: 123,
			Repos: []*settingsproto.RepoInformation{
				{
					RepoOwner: "testOwner",
					RepoName:  "testRepo",
				},
			},
		},
	}, "").Return(&settingsserviceproto.UpdateTenantSettingsResponse{}, nil).Once()

	err := et.handler.HandleInstallation(ctx, installEvent, tenantID, tenantName, requestId, et.mockGithubClient)
	assert.NoError(t, err)
}

func TestSkipLargeFile(t *testing.T) {
	et := newEventHandlerTest(t)
	defer et.assertExpectations()

	ctx := context.Background()
	requestContext := &requestcontext.RequestContext{}
	tenantID := "test-tenant"
	repoInfo := &settingsproto.RepoInformation{
		RepoOwner: "testOwner",
		RepoName:  "testRepo",
	}

	et.featureFlagHandle.Set("max_upload_size_bytes", 10)

	checkpoint := et.mockRegisterRepos(true, map[string]string{
		"hello.txt": "world!",
		"large.txt": "hello, world!",
	}, map[string]struct{}{
		// This file is too large
		"large.txt": {},
	})

	blobs, errWithRateLimit := et.handler.RegisterRepo(ctx, requestContext, et.mockGithubClient,
		repoInfo, tenantID, "test-tenant-name", "main", "000")

	// Verification
	assert.Nil(t, errWithRateLimit)
	assert.Equal(t, checkpoint.String(), *blobs.BaselineCheckpointId)
}

func TestUploadDiffWithGetContentsErrors(t *testing.T) {
	runTest := func(respContent *github.RepositoryContent, resp *github.Response, nilIgnoreFileResp bool, respError error) {
		et := newEventHandlerTest(t)
		defer et.assertExpectations()

		diff := `diff --git a/a.py b/a.py
	index fcf5493..03e69e0 100644
	--- a/a.py
	+++ b/a.py
	@@ -1,11 +1,11 @@
	import os

	-def list_all():
	-    for ent in os.listdir():
	+def list_all(path):
	+    for ent in os.listdir(path):
			print(ent)
					if os.path.isdir(ent):
						list_all()
					else:
						print(ent)

	-list_all()
	+list_all(".")
	`

		// Mock CompareCommits to return a submodule change
		et.mockGithubClient.On("CompareCommits", mock.Anything, "owner", "repo", "baseCommit", "head", mock.Anything).
			Return(&github.CommitsComparison{
				Files: []*github.CommitFile{
					{
						Filename: github.String("a.py"),
						Status:   github.String("modified"),
						Patch:    github.String(diff),
					},
				},
				Commits: []*github.RepositoryCommit{
					{
						SHA: github.String("head"),
						Parents: []*github.Commit{
							{SHA: github.String("baseCommit")},
						},
					},
				},
				Status: github.String("ahead"),
			}, &github.Response{}, nil)

		// Mock CompareCommitsRaw
		et.mockGithubClient.On("CompareCommitsRaw", mock.Anything, "owner", "repo", "baseCommit", "head", mock.Anything).
			Return(diff, &github.Response{}, nil)

		// Mock the ignore file check
		et.mockIgnoreFileDiff(nil, nilIgnoreFileResp)

		if !nilIgnoreFileResp {
			// Mock GetCurrentRefFiles with a missing submodule
			et.mockGithubStateClient.On("GetCurrentRefFiles", mock.Anything, mock.Anything, &githubstatepersistproto.GithubRef{Ref: "main", Repo: &githubstatepersistproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"}}, "baseCommit", []string{"a.py"}).
				Return(func() (<-chan githubstateclient.GetCurrentRefFilesResult, error) {
					fakeFiles := make(chan githubstateclient.GetCurrentRefFilesResult, 10)
					fakeFiles <- githubstateclient.GetCurrentRefFilesResult{
						Resp: &githubstatepersistproto.GetCurrentRefFilesResponse{
							CommitSha: "baseCommit",
							FileInfos: []*githubstatepersistproto.FileInfo{
								{
									FilePath: "a.py",
									Status:   &statusproto.Status{Code: int32(codes.NotFound)},
								},
							},
						},
					}
					defer close(fakeFiles)
					return fakeFiles, nil
				}())

			// Mock GetContents to return an error in GetContents
			var nilContents []*github.RepositoryContent
			et.mockGithubClient.On("GetContents",
				mock.Anything,
				"owner",
				"repo",
				"a.py",
				&github.RepositoryContentGetOptions{Ref: "baseCommit"},
			).Once().Return(
				respContent,
				nilContents,
				resp,
				respError,
			)
		}

		oldCheckpointId := "oldCheckpointId"
		refCheckpoint := &githubstatepersistproto.RefCheckpoint{
			Ref: &githubstatepersistproto.GithubRef{
				Ref: "main",
				Repo: &githubstatepersistproto.GithubRepo{
					RepoOwner: "owner",
					RepoName:  "repo",
				},
			},
			CommitSha: "baseCommit",
			Blobs:     &blobnamespb.Blobs{BaselineCheckpointId: &oldCheckpointId},
		}

		// Call UploadDiff
		_, err := et.handler.UploadDiff(
			context.Background(),
			&requestcontext.RequestContext{},
			"tenantID",
			"tenantName",
			et.mockGithubClient,
			refCheckpoint,
			"head",
			&timestamppb.Timestamp{},
			nil,
		)

		// Assert we return an error and don't crash
		assert.Error(t, err)
	}

	t.Run("error, no response", func(t *testing.T) {
		runTest(&github.RepositoryContent{}, nil, false, errors.New("test error"))
	})
	t.Run("no error, no file", func(t *testing.T) {
		runTest(nil, &github.Response{}, false, nil)
	})
	t.Run("error, no file or response", func(t *testing.T) {
		runTest(nil, nil, false, errors.New("test error"))
	})
	t.Run("no response for ignorefile", func(t *testing.T) {
		runTest(nil, nil, true, nil)
	})
}
