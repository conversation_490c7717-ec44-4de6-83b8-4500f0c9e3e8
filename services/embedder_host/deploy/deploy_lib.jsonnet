local embedder_host = import 'services/embedder_host/server/deploy_lib.jsonnet';

function(env,
         namespace,
         namespace_config,
         cloud,
         name,
         modelInstanceConfigMapName,
         modelConfig,
         gpu,
         replicas,
         embedder_host_handler,
         gpuCount=1,
         global=false)
  embedder_host(
    env,
    namespace,
    namespace_config,
    cloud,
    name,
    modelInstanceConfigMapName,
    modelConfig,
    gpu,
    replicas,
    embedder_host_handler,
    gpuCount=1,
    global=global,
  )
