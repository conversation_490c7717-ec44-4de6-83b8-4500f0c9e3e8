load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "migration_lib",
    srcs = [
        "migration_util.go",
        "stripe_client.go",
        "stripe_to_orb_migration_job.go",
        "stripe_to_orb_validation_job.go",
    ],
    importpath = "github.com/augmentcode/augment/services/billing/central/migration",
    visibility = ["//services/billing/central:__pkg__"],
    deps = [
        "//base/logging:logging_go",
        "//services/auth/central/client:auth_client_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/integrations/orb:orb_lib",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/tenant_watcher/util:go_lib",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_github_stripe_stripe_go_v80//:go_default_library",
        "@com_github_stripe_stripe_go_v80//customer",
        "@com_github_stripe_stripe_go_v80//subscription",
    ],
)

go_test(
    name = "stripe_to_orb_migration_job_test_go",
    srcs = [
        "stripe_to_orb_migration_job_test.go",
    ],
    embed = [":migration_lib"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/auth/central/client:auth_client_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/integrations/orb:orb_lib",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "@com_github_rs_zerolog//log",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//require",
        "@com_github_stripe_stripe_go_v80//:go_default_library",
    ],
)
