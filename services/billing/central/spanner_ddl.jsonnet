// Schema DDL statements go here. This is an immutable list. ConfigConnector will apply just new
// statements. Changes are applied atomically and are all-or-nothing.
//
// Every DDL statement should have its own entry in the list and should NOT end with a semicolon or
// include SQL comments, or parsing will fail.
//
// See Spanner docs for best practices: https://cloud.google.com/spanner/docs/schema-and-data-model

[

]
