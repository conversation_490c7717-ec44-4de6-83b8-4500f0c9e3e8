# Settings

The Settings server is a gRPC server that stores and retrieves tenant and user settings.

## Interface

see settings.proto for the GRPC protocol definition.

## Consistency

The settings service is strongly consistent.
Updates can protect themselves against concurrent updates by using the version field. Without the version field, the last update wins.

## Security

The Settings server is secured using mTLS.
The tenant is protected by service tokens.

The user settings are only available to the same user
or to background services/IAP users.

The settings can contain restricted information and
should not be logged or exposed to the public.

## PII

However, for user settings use the user id as the key, which is considered PII.

## Persistence

The Settings server uses BigTable via the bigtable proxy service.
