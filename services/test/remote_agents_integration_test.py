"""Integration tests for remote agents API.

This test suite covers the basic functionality of the remote agents API:
- Creating remote agents
- Listing remote agents
- Chatting with remote agents
- Getting chat history
- Deleting remote agents

These tests interact with
- a live deployment of the remote agents service.
- a running beachhead instance.
- agent service that supports beachhead.
"""

import time
import pytest
from contextlib import contextmanager
from dataclasses import asdict
from pathlib import Path
from typing import Tuple, Optional, Any, Union, List, Generator

from base.augment_client.client import (
    AugmentClient,
    GithubCommitRef,
    RemoteAgentWorkspaceSetup,
)
from services.api_proxy.public_api_pb2 import (
    RemoteAgentStatus,
    RemoteAgentWorkspaceStatus,
)
import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from services.chat_host import chat_pb2


# Override the kubecfg script to use the remote agents kubecfg
@pytest.fixture(scope="session")
def kubecfg_script():
    """Returns the kubecfg script to use."""
    return Path("services/test/remote_agents_kubecfg.sh")


# Helper functions for common test operations
def cleanup_existing_agents(augment_client: AugmentClient) -> None:
    """Delete all existing agents to avoid max limit errors."""
    existing_agents = augment_client.list_remote_agents()
    for agent in existing_agents.remote_agents:
        print(f"Deleting existing agent {agent.remote_agent_id}")
        try:
            augment_client.delete_remote_agent(agent.remote_agent_id)
        except Exception as e:
            print(f"Failed to delete agent {agent.remote_agent_id}: {e}")


def create_default_workspace_setup(
    initial_prompt: str | None = None,
    commit_ref: GithubCommitRef | None = None,
) -> Tuple[RemoteAgentWorkspaceSetup, list]:
    """Create the default workspace setup and initial request nodes used by all tests."""
    if initial_prompt is None:
        initial_prompt = "Summarize this repository."

    # Create a simple workspace with GitHub repo reference
    if commit_ref is None:
        commit_ref = GithubCommitRef(
            repository_url="https://github.com/augmentcode/augment-swebench-agent",
            git_ref="main",
        )

    # Create a simple workspace setup with GitHub repo reference
    workspace_setup = RemoteAgentWorkspaceSetup()
    workspace_setup.starting_files = {"github_commit_ref": asdict(commit_ref)}

    # Create a simpler initial request nodes structure
    initial_request_nodes = [
        {
            "id": 1,
            "type": 0,  # TEXT type (using 0 instead of 1 to match ChatRequestNodeType.TEXT)
            "text_node": {"content": initial_prompt},
        }
    ]

    return workspace_setup, initial_request_nodes


@contextmanager
def create_and_verify_agent(
    augment_client: AugmentClient,
    workspace_setup: RemoteAgentWorkspaceSetup,
    initial_request_nodes: list,
) -> Generator[str, None, None]:
    """Create a remote agent and verify it was created successfully."""
    # Create a remote agent
    create_response = augment_client.create_remote_agent(
        workspace_setup=workspace_setup, initial_request_nodes=initial_request_nodes
    )
    agent_id = create_response.remote_agent_id
    print(f"Created agent {agent_id}")

    # Verify the agent was created by listing agents
    response = augment_client.list_remote_agents()
    agent_found = False
    agent_status = None
    for agent in response.remote_agents:
        if agent.remote_agent_id == agent_id:
            agent_found = True
            agent_status = agent.status
            assert agent.started_at is not None, "Agent started_at should not be None"
            assert agent.updated_at is not None, "Agent updated_at should not be None"
            break
    assert agent_found, f"Created agent {agent_id} not found in list response"
    # Initial status could be STARTING, RUNNING, or IDLE depending on timing
    assert agent_status in [
        RemoteAgentStatus.AGENT_STARTING,
        RemoteAgentStatus.AGENT_RUNNING,
        RemoteAgentStatus.AGENT_IDLE,
    ], f"Initial agent status is {agent_status}, expected STARTING, RUNNING, or IDLE"

    yield agent_id

    # Clean up the agent
    delete_and_verify_agent(augment_client, agent_id)


def wait_for_agent_idle(
    augment_client: AugmentClient, agent_id: str, max_wait_time: int = 240
) -> None:
    """Wait for the agent to become IDLE (ready to receive chat messages)."""
    wait_interval = 5  # Check every 5 seconds
    wait_time = 0
    agent_ready = False

    while wait_time < max_wait_time:
        # List agents to check status
        response = augment_client.list_remote_agents()
        for agent in response.remote_agents:
            if agent.remote_agent_id == agent_id:
                if agent.status == RemoteAgentStatus.AGENT_IDLE:
                    agent_ready = True
                    break

        if agent_ready:
            break

        time.sleep(wait_interval)
        wait_time += wait_interval

    assert agent_ready, f"Agent did not become IDLE within {max_wait_time} seconds"


def find_agent_status(augment_client: AugmentClient, agent_id: str) -> Optional[Any]:
    """Find and return the status of a specific agent."""
    response = augment_client.list_remote_agents()
    for agent in response.remote_agents:
        if agent.remote_agent_id == agent_id:
            return agent.status
    return None


def find_agent_workspace_status(
    augment_client: AugmentClient, agent_id: str
) -> Optional[Any]:
    """Find and return the status of a specific agent."""
    response = augment_client.list_remote_agents()
    for agent in response.remote_agents:
        if agent.remote_agent_id == agent_id:
            return agent.workspace_status
    return None


def delete_and_verify_agent(augment_client: AugmentClient, agent_id: str) -> None:
    """Delete an agent and verify it was deleted successfully."""
    # Delete the agent
    augment_client.delete_remote_agent(agent_id)

    # Verify the agent was deleted by listing agents
    response = augment_client.list_remote_agents()
    for agent in response.remote_agents:
        if agent.remote_agent_id == agent_id:
            raise Exception(f"Agent {agent_id} still found after deletion")


def wait_for_agent_status(
    augment_client: AugmentClient,
    agent_id: str,
    expected_status: Union[Any, List[Any]],
    timeout: int = 60,
) -> None:
    """Wait for the agent to reach a specific status or one of multiple statuses.

    Args:
        augment_client: The client to use for API calls
        agent_id: The agent ID to check
        expected_status: A single status or list of acceptable statuses
        max_wait_time: Maximum time to wait in seconds
    """
    wait_interval = 5  # Check every 5 seconds
    wait_time = 0
    status_reached = False

    # Convert single status to list for uniform handling
    if not isinstance(expected_status, list):
        expected_statuses = [expected_status]
    else:
        expected_statuses = expected_status

    while wait_time < timeout:
        agent_status = find_agent_status(augment_client, agent_id)
        if agent_status in expected_statuses:
            status_reached = True
            break

        time.sleep(wait_interval)
        wait_time += wait_interval

    assert (
        status_reached
    ), f"Agent did not reach any of {expected_statuses} within {timeout} seconds"


def wait_for_agent_workspace_status(
    augment_client: AugmentClient,
    agent_id: str,
    expected_status: Union[Any, List[Any]],
    max_wait_time: int = 240,  # Longer wait time for resume
) -> None:
    """Wait for the agent to reach a specific status or one of multiple statuses.

    Args:
        augment_client: The client to use for API calls
        agent_id: The agent ID to check
        expected_status: A single status or list of acceptable statuses
        max_wait_time: Maximum time to wait in seconds
    """
    wait_interval = 5  # Check every 5 seconds
    wait_time = 0
    status_reached = False

    # Convert single status to list for uniform handling
    if not isinstance(expected_status, list):
        expected_statuses = [expected_status]
    else:
        expected_statuses = expected_status

    while wait_time < max_wait_time:
        agent_status = find_agent_workspace_status(augment_client, agent_id)
        if agent_status in expected_statuses:
            status_reached = True
            break

        time.sleep(wait_interval)
        wait_time += wait_interval

    assert (
        status_reached
    ), f"Agent did not reach any of {expected_statuses} within {max_wait_time} seconds"


def verify_agent_status(
    augment_client: AugmentClient, agent_id: str, expected_status: Any
) -> None:
    """Verify that an agent has the expected status."""
    agent_status = find_agent_status(augment_client, agent_id)
    assert agent_status is not None, f"Agent {agent_id} not found in list response"
    assert (
        agent_status == expected_status
    ), f"Agent status is {agent_status}, expected {expected_status}"


def wait_for_chat_message_in_chat_history(
    augment_client: AugmentClient,
    agent_id: str,
    chat_message: str,
    chat_timeout: int = 120,
    chat_poll_interval: int = 5,
) -> None:
    time.sleep(chat_poll_interval)
    for i in range(chat_timeout // chat_poll_interval):
        response = augment_client.get_remote_agent_history(remote_agent_id=agent_id)
        if any(
            chat_message in str(exchange.exchange) for exchange in response.chat_history
        ):
            break
        time.sleep(chat_poll_interval)
    else:
        raise TimeoutError(
            f"Chat message not found in chat history after {chat_timeout} seconds"
        )


@contextmanager
def setup_test_agent(
    augment_client: AugmentClient,
    initial_prompt: str | None = None,
    commit_ref: GithubCommitRef | None = None,
) -> Generator[str, None, None]:
    """Common setup for all tests: cleanup, create agent, and wait for IDLE.

    Args:
        augment_client: The client to use for API calls

    Returns:
        str: The created agent ID
    """

    # Clean up existing agents to avoid max limit errors
    cleanup_existing_agents(augment_client)

    workspace_setup, initial_request_nodes = create_default_workspace_setup(
        initial_prompt, commit_ref
    )
    initial_request_nodes[0]["text_node"]["content"] = (
        "How many files are in this repo?"
    )

    # Create and verify the agent
    with create_and_verify_agent(
        augment_client, workspace_setup, initial_request_nodes
    ) as agent_id:
        # Wait for the agent to become IDLE
        wait_for_agent_idle(augment_client, agent_id)

        yield agent_id


def test_create_agent_and_chat(augment_client: AugmentClient):
    """Tests the create, chat, list, and delete remote agent endpoints.

    This test:
    1. Creates a remote agent
    2. Sends a chat message to the agent
    3. Verifies the chat appears in history
    4. Deletes the remote agent

    Args:
        augment_client: The client to use for API calls
    """
    # Setup test agent (cleanup, create, wait for IDLE)
    with setup_test_agent(augment_client) as agent_id:
        chat_message = "What package management tool does it use?"
        augment_client.remote_agent_chat(
            remote_agent_id=agent_id,
            message=chat_message,
        )

        wait_for_chat_message_in_chat_history(augment_client, agent_id, chat_message)

        # Verify that the agent's updated_at field has been updated
        response = augment_client.list_remote_agents()
        for agent in response.remote_agents:
            if agent.remote_agent_id == agent_id:
                assert (
                    agent.updated_at is not None
                ), "Agent updated_at should not be None"
                break


def test_create_agent_and_immediate_interrupt(augment_client: AugmentClient):
    """Tests the interrupt remote agent endpoint.

    This test:
    1. Creates a remote agent
    2. Interrupts the agent
    3. Verifies the agent is interrupted
    4. Deletes the remote agent

    Args:
        augment_client: The client to use for API calls
    """
    # Try to get this prompt to be slow so we can be sure the interrupt lands
    # before the agent goes to idle
    long_running_prompt = (
        "Sleep for 10 minutes, then create a new readme in every directory."
    )
    workspace_setup, initial_request_nodes = create_default_workspace_setup(
        initial_prompt=long_running_prompt
    )
    with create_and_verify_agent(
        augment_client, workspace_setup, initial_request_nodes
    ) as agent_id:
        # interrupt the agent and verify it goes to idle
        augment_client.interrupt_remote_agent(agent_id)
        wait_for_agent_status(
            augment_client,
            agent_id,
            RemoteAgentStatus.AGENT_IDLE,
            timeout=120,
        )

        # verify that the chat history is not complete - there should be at most one
        # text node and one tool block, and no changed files
        response = augment_client.get_remote_agent_history(remote_agent_id=agent_id)
        assert len(response.chat_history) <= 2, "Chat history should be interrupted"
        for exchange in response.chat_history:
            assert len(exchange.changed_files) == 0, "Changed files should be empty"


def test_interrupt_idle_agent(augment_client: AugmentClient):
    """Tests that interrupting an idle agent does not error.

    This test:
    1. Creates a remote agent
    2. Interrupts the agent
    3. Verifies the agent is interrupted
    4. Deletes the remote agent

    Args:
        augment_client: The client to use for API calls
    """
    with setup_test_agent(augment_client) as agent_id:
        augment_client.interrupt_remote_agent(agent_id)
        wait_for_agent_status(
            augment_client,
            agent_id,
            RemoteAgentStatus.AGENT_IDLE,
            timeout=10,
        )


def test_chat_and_interrupt(augment_client: AugmentClient):
    """Tests that interrupting an agent in the middle of a chat does not error.

    This test:
    1. Creates a remote agent
    2. Sends a chat message to the agent
    3. Interrupts the agent
    4. Verifies the agent is interrupted
    5. Deletes the remote agent

    Args:
        augment_client: The client to use for API calls
    """
    with setup_test_agent(augment_client) as agent_id:
        # Send a chat message to the agent
        chat_message = "Hello, agent."
        augment_client.remote_agent_chat(
            remote_agent_id=agent_id,
            message=chat_message,
        )

        # mimic a quick followup interrupt
        time.sleep(3)
        augment_client.interrupt_remote_agent(agent_id)

        # Verify the agent is interrupted
        wait_for_agent_status(
            augment_client,
            agent_id,
            RemoteAgentStatus.AGENT_IDLE,
            timeout=10,
        )


def test_immediate_chat_after_creation(augment_client: AugmentClient):
    """Tests that chatting to an agent immediately after creation does not error.

    This test:
    1. Creates a remote agent
    2. Sends a chat message to the agent
    3. Verifies the chat appears in history
    4. Deletes the remote agent

    Args:
        augment_client: The client to use for API calls
    """
    workspace_setup, initial_request_nodes = create_default_workspace_setup()

    with create_and_verify_agent(
        augment_client, workspace_setup, initial_request_nodes
    ) as agent_id:
        # Send a chat message to the agent
        chat_message = "Hello, agent. I just want a summarize on readme."
        augment_client.remote_agent_chat(
            remote_agent_id=agent_id,
            message=chat_message,
        )
        wait_for_agent_status(
            augment_client,
            agent_id,
            RemoteAgentStatus.AGENT_IDLE,
            timeout=120,
        )
        # Verify the chat appears in history
        response = augment_client.get_remote_agent_history(remote_agent_id=agent_id)
        message_found = False
        for exchange in response.chat_history:
            exchange_str = str(exchange.exchange)
            if chat_message in exchange_str:
                message_found = True
                break
        assert message_found, "Chat message not found in chat history"


def test_large_file_creation(augment_client: AugmentClient):
    """Tests that creating a large file does not error.

    This test:
    1. Creates a remote agent
    2. Sends a chat message to the agent
    3. Verifies the chat appears in history
    4. Deletes the remote agent

    Args:
        augment_client: The client to use for API calls
    """
    workspace_setup, initial_request_nodes = create_default_workspace_setup()
    with create_and_verify_agent(
        augment_client, workspace_setup, initial_request_nodes
    ) as agent_id:
        # create 100 x 5MB files
        chat_message = """
    Use following command to help me create a list of large files.
    for i in {1..100}; do dd if=/dev/zero of=file_$i.md bs=5M count=1; done

    use this to create files for me"""
        augment_client.remote_agent_chat(
            remote_agent_id=agent_id,
            message=chat_message,
        )
        wait_for_agent_status(
            augment_client,
            agent_id,
            RemoteAgentStatus.AGENT_IDLE,
            timeout=120,
        )
        # Verify the chat appears in history
        response = augment_client.get_remote_agent_history(remote_agent_id=agent_id)
        for i in range(1, 100):
            assert f"file_{i}.md" in str(
                [
                    file.new_path
                    for exchange in response.chat_history
                    for file in exchange.changed_files
                ]
            ), f"file_{i}.md not found in chat history"


def test_create_agent_empty_repo(augment_client: AugmentClient):
    """Tests the create, chat, list, and delete remote agent endpoints.

    This test:
    1. Creates a remote agent
    2. Sends a chat message to the agent
    3. Verifies the chat appears in history
    4. Deletes the remote agent

    Args:
        augment_client: The client to use for API calls
    """
    empty_repo = GithubCommitRef(
        repository_url="https://github.com/augment-test-org/test-empty",
        git_ref="main",
    )
    with setup_test_agent(augment_client, commit_ref=empty_repo) as agent_id:
        wait_for_agent_idle(augment_client, agent_id)

    # Delete and verify the agent was deleted
    delete_and_verify_agent(augment_client, agent_id)


def test_create_agent_repo_with_submodules_success(augment_client: AugmentClient):
    """Tests that agents can be created with repositories containing accessible submodules.

    This test verifies that:
    1. The git clone operation succeeds for repos with submodules
    2. The git submodule update operation succeeds for accessible submodules
    3. The agent becomes IDLE and can respond to chat messages
    4. The workspace initialization completes successfully

    Args:
        augment_client: The client to use for API calls
    """
    # Use the test repo that has accessible submodules
    submodule_repo = GithubCommitRef(
        repository_url="https://github.com/augment-test-org/test-submodule-main",
        git_ref="main",
    )

    # Setup test agent using the standard helper
    initial_prompt = (
        "Count how many README.md files are in this repository including submodules."
    )
    with setup_test_agent(
        augment_client, initial_prompt=initial_prompt, commit_ref=submodule_repo
    ) as agent_id:
        # Send a chat message to the agent
        chat_message = "Tell me the count of README.md files in the format 'There are X README.md files.' replacing 'X' with the number of files."
        augment_client.remote_agent_chat(
            remote_agent_id=agent_id,
            message=chat_message,
        )
        wait_for_agent_status(
            augment_client,
            agent_id,
            RemoteAgentStatus.AGENT_IDLE,
            timeout=120,
        )
        # Verify the chat appears in history
        response = augment_client.get_remote_agent_history(remote_agent_id=agent_id)

        print("\n=== SUBMODULE SUCCESS TEST - Parsing Response ===")

        # Extract the agent's response text
        agent_response = ""
        for exchange in response.chat_history:
            agent_response += str(exchange.exchange)

        print(f"Full Agent Response:\n{agent_response}")
        assert (
            "There are 2 README.md files." in agent_response
        ), "Expected 2 README.md files."
        print("=== END SUBMODULE SUCCESS TEST ===\n")


def test_create_agent_repo_with_submodules_failure(augment_client: AugmentClient):
    """Tests that agents can be created with repositories where submodule access fails.

    This test verifies that:
    1. The git clone operation succeeds for the main repo
    2. The git submodule update operation fails (non-fatal) for inaccessible submodules
    3. The agent still becomes IDLE despite submodule failure
    4. The workspace initialization completes successfully even with submodule failure

    Args:
        augment_client: The client to use for API calls
    """
    # Use the test repo that has private/inaccessible submodules
    submodule_repo = GithubCommitRef(
        repository_url="https://github.com/augment-test-org/test-submodule-main-private",
        git_ref="main",
    )

    # Setup test agent using the standard helper
    initial_prompt = (
        "Count how many README.md files are in this repository including submodules."
    )
    with setup_test_agent(
        augment_client, initial_prompt=initial_prompt, commit_ref=submodule_repo
    ) as agent_id:
        # Send a chat message to the agent
        chat_message = "Tell me the count of README.md files in the format 'There are X README.md files.' replacing 'X' with the number of files."
        augment_client.remote_agent_chat(
            remote_agent_id=agent_id,
            message=chat_message,
        )
        wait_for_agent_status(
            augment_client,
            agent_id,
            RemoteAgentStatus.AGENT_IDLE,
            timeout=120,
        )
        # Verify the chat appears in history
        response = augment_client.get_remote_agent_history(remote_agent_id=agent_id)

        print("\n=== SUBMODULE FAILURE TEST - Parsing Response ===")

        # Extract the agent's response text
        agent_response = ""
        for exchange in response.chat_history:
            agent_response += str(exchange.exchange)

        print(f"Full Agent Response:\n{agent_response}")
        assert (
            "There are 1 README.md files." in agent_response
        ), "Expected 1 README.md files."
        print("=== END SUBMODULE FAILURE TEST ===\n")


def test_pause_and_resume_agent(augment_client: AugmentClient):
    """Tests the pause and resume remote agent endpoints.

    This test:
    1. Creates a remote agent
    2. Pauses the agent
    3. Verifies the agent is paused
    4. Resumes the agent
    5. Chat to the agent
    6. Deletes the remote agent

    Args:
        augment_client: The client to use for API calls
    """
    with setup_test_agent(augment_client) as agent_id:
        augment_client.pause_remote_agent(agent_id)
        wait_for_agent_workspace_status(
            augment_client,
            agent_id,
            RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED,
        )

        augment_client.resume_remote_agent(agent_id)

        chat_message = "Hello, agent."
        augment_client.remote_agent_chat(
            remote_agent_id=agent_id,
            message=chat_message,
        )


def test_chat_to_paused_agent_resumes_workspace(augment_client: AugmentClient):
    """Tests that chatting to a paused agent resumes the workspace.
    This test:
    1. Creates a remote agent
    2. Pauses the agent
    3. Sends a chat message to the paused agent
    4. Verifies the agent resumes (becomes IDLE or RUNNING)

    Args:
        augment_client: The client to use for API calls
    """
    with setup_test_agent(augment_client) as agent_id:
        # Pause the agent
        augment_client.pause_remote_agent(agent_id)

        # Wait for and verify the agent is paused
        wait_for_agent_workspace_status(
            augment_client,
            agent_id,
            RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED,
        )

        # Send a chat message to the agent
        # TODO: investigate and make timeout stricter
        chat_message = "Hello, paused agent."
        augment_client.remote_agent_chat(
            remote_agent_id=agent_id,
            message=chat_message,
            timeout=240,
        )

        # Verify the agent is resumed (becomes IDLE or RUNNING)
        # We'll check for either status since the agent might be processing the chat
        wait_for_agent_workspace_status(
            augment_client,
            agent_id,
            RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RUNNING,
        )


# make sure we can access tools
def test_chat_with_tools(
    augment_client: AugmentClient,
):
    """Tests that chatting with tools works.
    This test:
    1. Creates a remote agent
    2. Sends a chat message to the agent that requires a tool
    3. Verifies the agent used the tool
    4. Deletes the remote agent

    Args:
        augment_client: The client to use for API calls
    """
    with setup_test_agent(augment_client) as agent_id:
        # Send a chat message to the agent
        chat_message = "Print the current time."
        augment_client.remote_agent_chat(
            remote_agent_id=agent_id,
            message=chat_message,
        )

        wait_for_chat_message_in_chat_history(
            augment_client,
            agent_id,
            chat_message,
            chat_timeout=240,
        )

        # Verify the agent used the tool
        response = augment_client.get_remote_agent_history(remote_agent_id=agent_id)
        found_tool_use = False
        for exchange in response.chat_history:
            for node in exchange.exchange.response_nodes:
                if node.type == chat_pb2.ChatResultNodeType.TOOL_USE:
                    assert node.tool_use is not None
                    found_tool_use = True
        assert found_tool_use, "Agent did not use a tool"
