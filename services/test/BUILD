load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg_multi")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:python.bzl", "py_library")

kubecfg_multi(
    name = "test_kubecfg",
    deps = [
        # Maybe this (agents) moves to base_kubecfg once it's in all namespaces
        "//services/agents/server:kubecfg",
        "//services/deploy:base_kubecfg",
        "//services/deploy:chatanol-qwen-v1-1_kubecfg",
        "//services/deploy:chatanol-qwen-v1-1_third_party_chat_kubecfg",
        "//services/deploy:claude_instruction_v2_edit_kubecfg",
        "//services/deploy:methanol_0416_4_kubecfg",
        "//services/deploy:pleasehold_14b_v2_kubecfg",
        "//services/deploy:raven_location_v2_kubecfg",
        "//services/deploy:raven_retriever_v1_kubecfg",
        "//services/deploy:sentry_v1_kubecfg",
        "//services/deploy:starethanol6_16_1_proj512_kubecfg",
        "//services/deploy:third_party_chatanol4_pleasehold2_chat_kubecfg",
        "//services/deploy/completion:eldenv3_3b_kubecfg",
        "//services/deploy/next_edit:raven_edit_v6_15b_kubecfg",
        "//services/deploy:forger_smart_paste_v2_qwen_8b_32k_edit_kubecfg",
        "//services/third_party_arbiter/server:kubecfg",
    ],
)

pytest_test(
    name = "api_end_to_end_test",
    size = "enormous",
    timeout = "eternal",
    srcs = [
        "api_endpoints_test.py",
        "conftest.py",
        "tokens.py",
    ],
    args = [
        "--cloud",
        "GCP_US_CENTRAL1_DEV",
    ],
    data = [
        ":test_kubecfg",
        "//base/static_analysis:testdata",
        "@k8s_binary//file:kubectl",
        "@lm_evaluation_harness//:all",
    ] + glob(["test_data/*.*"]),
    tags = [
        "exclusive",
        "gcp",
        "postmerge-test",
        "system-test",
        "system-test-gpu",
    ],
    deps = [
        "//base/augment_client:client",
        "//base/blob_names/python:blob_names",
        "//base/error_details:error_details_py_proto",
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        "//base/test_utils:testing_utils",
        "//services/api_proxy/access_token",
        "//services/api_proxy/client:grpc_client",
        "//services/api_proxy/client:memorize_path",
        "//services/tenant_watcher/client",
        "//services/test/fake_feature_flags:client_py",
        "//services/token_exchange/client:client_py",
        "//services/token_exchange/server:token_exchange_test_setup",
        requirement("pydantic"),
        requirement("requests"),
        requirement("kubernetes"),
        requirement("pyyaml"),
    ],
)

pytest_test(
    name = "agent_integration_test",
    size = "enormous",
    timeout = "eternal",
    srcs = ["agent_integration_test.py"],
    tags = [
        "manual",
        "postmerge-test",
    ],
    deps = [
        ":agent_integration_test_utils",
        "//services/agents/server:agents_handler",
        "//services/chat_host/server:chat_third_party_handler",
        requirement("google-cloud-secret-manager"),
    ],
)

kubecfg_multi(
    name = "remote_agents_kubecfg",
    deps = [
        "//services/agents/server:kubecfg",
        "//services/deploy:base_kubecfg",
        "//services/remote_agents/server:kubecfg",
        "//services/bigtable_proxy/server:kubecfg_central",
        "//services/memstore/server:kubecfg",
        "//services/integrations/github/processor/server:kubecfg",
        "//services/integrations/github/webhook_listener:kubecfg_pubsub",
        "//services/settings/server:kubecfg",
        # copy from test_kubecfg above
        "//services/deploy:chatanol-qwen-v1-1_kubecfg",
        "//services/deploy:chatanol-qwen-v1-1_third_party_chat_kubecfg",
        "//services/deploy:third_party_chatanol4_pleasehold2_chat_kubecfg",
    ],
)

pytest_test(
    name = "remote_agents_integration_test",
    size = "enormous",
    timeout = "eternal",
    srcs = [
        "conftest.py",
        "remote_agents_integration_test.py",
        "tokens.py",
    ],
    args = [
        "--beachhead-img-deploy-path=$(location //clients/beachhead/img:deploy-dev)",
        "--beachhead-oci-innie-path=$(location //clients/beachhead/img/innie:innie_oci)",
        "--beachhead-oci-outie-path=$(location //clients/beachhead/img/outie:outie_oci_testing)",
        "--beachhead-jsonnet-path=$(location @google_jsonnet_go//cmd/jsonnet)",
        "--beachhead-clusters-path=research/infra/cfg/clusters/clusters.jsonnet",
        "--beachhead-crane-path=$(location @com_github_google_go_containerregistry//cmd/crane)",
    ],
    data = [
        ":remote_agents_kubecfg",
        "//clients/beachhead/img:deploy-dev",
        "//clients/beachhead/img/innie:innie_oci",
        "//clients/beachhead/img/outie:outie_oci_testing",  # Use the version with env var
        "//research/infra/cfg/clusters:jsonnet_files",
        "@com_github_google_go_containerregistry//cmd/crane",
        "@google_jsonnet_go//cmd/jsonnet",
        "@k8s_binary//file:kubectl",
    ] + glob(["test_data/*.*"]),
    tags = [
        # TODO: uncomment following once test is stable on CI
        "exclusive",
        "gcp",
        "postmerge-test",
        "system-test",
        # "manual",
    ],
    deps = [
        "//base/augment_client:client",
        "//base/blob_names/python:blob_names",
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        "//base/test_utils:testing_utils",
        "//services/api_proxy:model_finder_py_proto",
        "//services/api_proxy:public_api_py_proto",
        "//services/api_proxy/access_token",
        "//services/api_proxy/client:grpc_client",
        "//services/api_proxy/client:memorize_path",
        "//services/chat_host/server:chat_third_party_handler",
        "//services/tenant_watcher/client",
        "//services/test/fake_feature_flags:client_py",
        "//services/token_exchange/client:client_py",
        "//services/token_exchange/server:token_exchange_test_setup",
        requirement("pydantic"),
        requirement("requests"),
        requirement("kubernetes"),
        requirement("pyyaml"),
        requirement("google-cloud-secret-manager"),
    ],
)

py_library(
    name = "agent_integration_test_utils",
    testonly = True,
    srcs = [
        "agent_integration_mock_tools.py",
        "agent_integration_test_utils.py",
    ],
    deps = [
        "//base/test_utils:synchronous_executor",
        "//services/agents/server:agents_handler",
        "//services/chat_host/server:chat_third_party_handler",
    ],
)
