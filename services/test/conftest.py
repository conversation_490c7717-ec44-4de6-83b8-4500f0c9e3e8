# pylint: disable=redefined-outer-name
"""conftest.py is a special file that pytest will automatically load.

pytest will automatically load and execute before any other test files. This is a
good place to put fixtures that are used by multiple test files.
"""

import json
import subprocess
import time
import typing
import urllib3
import uuid
from dataclasses import dataclass
from pathlib import Path
from typing import Generator, Optional

import grpc
import kubernetes
import pytest
import requests


import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from base.augment_client.client import AugmentClient, AugmentModelClient
from base.python.cloud import cloud as cloud_lib
from base.python.grpc.health_check import HealthChecker
from services.api_proxy import model_finder_pb2, model_finder_pb2_grpc
from services.api_proxy.client import grpc_client as model_finder_grpc_client
from services.lib.request_context.request_context import RequestContext
from services.tenant_watcher.client.client import TenantsClient
from services.test.fake_feature_flags.client import FakeFeatureFlagsClient
from services.test.tokens import TokenUtil
from services.token_exchange import token_exchange_pb2
from services.token_exchange.client.client import (
    GrpcTokenExchangeClient,
    TokenExchangeClient,
)

# Disable SSL warnings for test environment
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Container details


def pytest_addoption(parser):
    """Add command line options to pytest."""
    parser.addoption(
        "--endpoint",
        action="store",
        help="endpoint to use instead of using kubernetes based auto-discovery",
        default=None,
    )
    parser.addoption(
        "--namespace",
        action="store",
        help="namespace to use instead of using kubernetes based auto-discovery",
        default=None,
    )
    parser.addoption(
        "--cloud",
        help="Cloud to use",
        default=cloud_lib.get_default_cloud(),
        choices=cloud_lib.get_cloud_list(gcp_only=True),
    )
    parser.addoption(
        "--skip-deployment",
        action="store_true",
        help="skip deploy and delete of the models",
        default=False,
    )
    parser.addoption(
        "--skip-deployment-teardown",
        action="store_true",
        help="skip deploy and delete of the models",
        default=not k8s_test_helper.is_running_in_test_infra(),
    )
    if not k8s_test_helper.is_running_in_test_infra():
        parser.addoption(
            "--teardown-deployment",
            action="store_true",
            help="tear down the complete deployment after the run",
            default=False,
        )
    parser.addoption(
        "--skip-deployment-check",
        action="store_true",
        help="skip checking if the deployments are settled",
        default=False,
    )
    parser.addoption(
        "--token-file",
        action="store",
        help="file to read the API token from",
        default=None,
    )
    parser.addoption(
        "--beachhead-img-deploy-path",
        action="store",
        default="",
        help="Path to the beachhead deploy binary",
    )
    parser.addoption(
        "--beachhead-oci-innie-path",
        action="store",
        default="",
        help="Path to the beachhead innie OCI image",
    )
    parser.addoption(
        "--beachhead-oci-outie-path",
        action="store",
        default="",
        help="Path to the beachhead outie OCI image",
    )
    parser.addoption(
        "--beachhead-jsonnet-path",
        action="store",
        default="",
        help="Path to the jsonnet executable",
    )
    parser.addoption(
        "--beachhead-clusters-path",
        action="store",
        default="research/infra/cfg/clusters/clusters.jsonnet",
        help="Path to the cluster definitions",
    )
    parser.addoption(
        "--beachhead-crane-path",
        action="store",
        default="",
        help="Path to the crane binary",
    )


def _test_response(url: str, token: str) -> bool:
    try:
        response = requests.post(
            f"{url}/get-models",
            json={},
            headers={
                "authorization": f"Bearer {token}",
            },
            verify=False,
            timeout=60,
        )
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        return False
    except requests.exceptions.RequestException as ex:
        print(ex, flush=True)
        return False


def _test_response_2(
    url: str,
    credentials: Optional[grpc.ChannelCredentials],
    service_name: str,
    options: list[tuple[str, str]] | None = None,
) -> bool:
    try:
        checker = HealthChecker(url, credentials, options=options)
        status = checker.is_serving(service_name)
        return status
    except grpc.RpcError as ex:
        print(ex, flush=True)
        return False


@pytest.fixture(scope="session")
def augment_client(
    application_shard,  # pylint: disable=redefined-outer-name
) -> Generator[AugmentClient, None, None]:
    """Returns an AugmentClient to use.

    It uses parameters with retry enabled and a higher timeout.
    """
    client = AugmentClient(
        application_shard.url,
        token=application_shard.token,
        retry_count=16,
        retry_sleep=10,
        verify=False,
        timeout=120,
    )
    print("Using session", client.request_session_id)
    yield client


@pytest.fixture(scope="session")
def augment_client_factory(
    application_shard,  # pylint: disable=redefined-outer-name
) -> Generator[typing.Callable[..., AugmentClient], None, None]:
    """Returns an factory for AugmentClient to use."""

    def _create_client(*args, **kwargs) -> AugmentClient:
        client = AugmentClient(
            application_shard.url,
            token=application_shard.token,
            verify=False,
            *args,
            **kwargs,
        )
        print("Using session", client.request_session_id)
        return client

    yield _create_client


NEXT_FORWARDED_PORT = 50052


def get_next_forwarded_port() -> int:
    """Return a fresh local port."""
    global NEXT_FORWARDED_PORT  # pylint: disable=global-statement
    port = NEXT_FORWARDED_PORT
    NEXT_FORWARDED_PORT += 1
    return port


@pytest.fixture(scope="session")
def augment_model_client(
    augment_client: AugmentClient,  # pylint: disable=redefined-outer-name
) -> AugmentModelClient:
    """Returns an AugmentModelClient to use for the default model."""
    return augment_client.client_for_model("")


def create_request_session_id() -> str:
    """Creates a new request session id.

    Args:
        None

    Returns:
        A new request session id
    """
    return str(uuid.uuid4())


@pytest.fixture(scope="session")
def tenant_id(
    tenant_watcher_client: TenantsClient | None,
) -> str | None:
    if tenant_watcher_client is None:
        print("No tenant watcher client, returning None for tenant ID")
        return None
    tenants = tenant_watcher_client.get_tenants()
    assert tenants, "Failed to find any active tenants"
    tid = tenants[0].id
    assert tid, "Failed to find tenant ID for tenant 'augment'"
    return tid


@pytest.fixture(scope="session")
def request_session_id() -> Generator[str, None, None]:
    """Return a request context."""
    yield create_request_session_id()


@pytest.fixture()
def request_context(
    request_session_id: str,
    token_exchange_client: TokenExchangeClient | None,
    tenant_id: str | None,
) -> Generator[RequestContext | None, None, None]:
    """Return a request context."""
    if tenant_id is None or token_exchange_client is None:
        yield None
        return

    token = token_exchange_client.get_signed_token_for_service(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.REQUEST_RW]
    )
    yield RequestContext.create_for_session(request_session_id, auth_token=token)


@pytest.fixture(scope="session")
def tenant_watcher_client(
    application_shard_deploy: k8s_test_helper.DeployInfo,
    external_endpoint_mode: bool,
) -> Generator[TenantsClient | None, None, None]:
    """Return an GRPC stub to access the Tenant Watcher.

    If applicable, it will update or create the application in Kubernetes.
    """
    if external_endpoint_mode:
        yield None
        return

    credentials = application_shard_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "tenant-central-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with application_shard_deploy.kubectl.port_forward(
            "deployment/tenant-central", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            if not _test_response_2(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(2)
                continue
            else:
                yield TenantsClient(
                    url,
                    credentials=credentials,
                    options=options,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield TenantsClient(
            url,
            credentials=credentials,
            options=options,
        )


@pytest.fixture(scope="session")
def feature_flags_client(
    application_shard_deploy: k8s_test_helper.DeployInfo,
    external_endpoint_mode: bool,
) -> Generator[FakeFeatureFlagsClient | None, None, None]:
    """Return a client for the fake feature flags service.

    If applicable, it will update or create the application in Kubernetes.
    """
    if external_endpoint_mode:
        yield None
        return

    with application_shard_deploy.kubectl.port_forward(
        "deployment/fake-feature-flags", 8080, get_next_forwarded_port()
    ) as port:
        url = f"http://localhost:{port}"
        yield FakeFeatureFlagsClient(
            url,
        )


@pytest.fixture(autouse=True)
def feature_flag_clear(
    feature_flags_client: FakeFeatureFlagsClient, external_endpoint_mode: bool
):
    """Clear all feature flags before each test."""
    if external_endpoint_mode:
        return
    feature_flags_client.clear()
    # we treat it as a string
    feature_flags_client.update(
        "indexer_embedder_multiplex",
        json.dumps(json.dumps({"default": 0.5, "gsc": 0.5})),
        wait=1,
    )


@pytest.fixture(scope="session")
def token_exchange_client(
    application_shard_deploy: k8s_test_helper.DeployInfo,
    external_endpoint_mode: bool,
) -> Generator[TokenExchangeClient | None, None, None]:
    """Return an GRPC stub to access the Token Exchange Client.

    If applicable, it will update or create the application in Kubernetes.
    """
    if external_endpoint_mode:
        yield None
        return

    credentials = application_shard_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "token-exchange-central-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with application_shard_deploy.kubectl.port_forward(
            "deployment/token-exchange-central", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            if not _test_response_2(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(2)
                continue
            else:
                yield GrpcTokenExchangeClient(
                    url,
                    credentials=credentials,
                    options=options,
                    namespace=application_shard_deploy.namespace,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield GrpcTokenExchangeClient(
            url,
            credentials=credentials,
            options=options,
            namespace=application_shard_deploy.namespace,
        )


@pytest.fixture(scope="session")
def model_finder_client(
    application_shard_deploy: k8s_test_helper.DeployInfo,  # pylint: disable=redefined-outer-name
) -> Generator[model_finder_pb2_grpc.ModelFinderStub, None, None]:
    """Returns a ModelFinderStub to use."""
    credentials = application_shard_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "model-finder-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with application_shard_deploy.kubectl.port_forward(
            "deployment/api-proxy", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            if not _test_response_2(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(2)
                continue
            else:
                yield model_finder_grpc_client.setup_stub(
                    url,
                    credentials=credentials,
                    options=options,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield model_finder_grpc_client.setup_stub(
            url,
            credentials=credentials,
            options=options,
        )


_DEFAULT_AUTOFIX_MODEL_NAMES = []  # Autofix tests are currently disabled

_DEFAULT_COMPLETION_MODEL_NAMES = [
    "eldenv3-3b",
    "",
]

_DEFAULT_NEXT_EDIT_MODEL_NAMES = ["raven-edit-v6-15b", ""]

_DEFAULT_CHAT_MODEL_NAMES = [
    "claude-sonnet-3-5-16k-v11-4-chat",
    "claude-sonnet-v17-c4-p2-chat",
    "gemini-2-flash-001-simple-port",
]

_DEFAULT_AGENT_MODEL_NAMES = [
    "claude-sonnet-4-0-200k-v5-c4-p2-agent",
]

_DEFAULT_SMART_PASTE_MODEL_NAMES = [
    "forger-smart-paste-v2-qwen-8b-32k-edit",
]

_DEFAULT_ALL_MODELS = (
    [("completion", model_name) for model_name in _DEFAULT_COMPLETION_MODEL_NAMES]
    + [("next_edit", model_name) for model_name in _DEFAULT_NEXT_EDIT_MODEL_NAMES]
    + [("chat", model_name) for model_name in _DEFAULT_CHAT_MODEL_NAMES]
    + [("agent", model_name) for model_name in _DEFAULT_AGENT_MODEL_NAMES]
    + [("smart_paste", model_name) for model_name in _DEFAULT_SMART_PASTE_MODEL_NAMES]
)


_DISCOVERED_MODELS = ()


def discover_models_from_endpoint(config):
    """Return the models from the endpoint."""
    global _DISCOVERED_MODELS  # pylint: disable=global-statement
    if _DISCOVERED_MODELS:
        return _DISCOVERED_MODELS
    namespace = config.getoption("--namespace")
    assert namespace

    kubectl = k8s_test_helper.get_kubectl(
        cloud=config.getoption("--cloud"),
        namespace=namespace,
    )
    credentials = kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "model-finder-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    with kubectl.port_forward(
        "deployment/api-proxy", 50051, get_next_forwarded_port()
    ) as port:
        url = f"localhost:{port}"
        model_finder = model_finder_grpc_client.setup_stub(
            url,
            credentials=credentials,
            options=options,
        )
        assert model_finder
        models = model_finder.GetInferenceModels(model_finder_pb2.GetModelsRequest())
    all_models = [
        ("completion", ""),
        ("next_edit", ""),
        ("chat", ""),
        ("smart_paste", ""),
    ]
    for model in models.models:
        if model.HasField("inference"):
            all_models.append(("completion", model.name))
        elif model.HasField("edit") and "smart-paste" in model.name:
            all_models.append(("smart_paste", model.name))
        elif model.HasField("chat"):
            all_models.append(("chat", model.name))
        elif model.HasField("next_edit"):
            all_models.append(("next_edit", model.name))
        else:
            raise ValueError(f"Unknown model type {model}")
    _DISCOVERED_MODELS = tuple(all_models)
    return _DISCOVERED_MODELS


def build_and_push_beachhead_image(config):
    """Builds and pushes the beachhead image for remote agent testing.

    This function uses the deploy-dev binary to build and push the beachhead image
    to the dev environment with a custom tag based on the current Kubernetes namespace.

    Args:
        config: pytest config

    Returns:
        str: The tag used for the image
    """
    binary_path = config.getoption("--beachhead-img-deploy-path")
    oci_innie_path = config.getoption("--beachhead-oci-innie-path")
    oci_outie_path = config.getoption("--beachhead-oci-outie-path")
    jsonnet_path = config.getoption("--beachhead-jsonnet-path")
    clusters_path = config.getoption("--beachhead-clusters-path")
    crane_path = config.getoption("--beachhead-crane-path")
    if (
        not binary_path
        or not oci_innie_path
        or not oci_outie_path
        or not jsonnet_path
        or not clusters_path
        or not crane_path
    ):
        print(
            f"Skipping beachhead image build and push, missing one of the following:"
            f" --beachhead-img-deploy-path={binary_path}, "
            f" --beachhead-oci-innie-path={oci_innie_path}, "
            f" --beachhead-oci-outie-path={oci_outie_path}, "
            f" --beachhead-jsonnet-path={jsonnet_path}, "
            f" --beachhead-clusters-path={clusters_path}, "
            f" --beachhead-crane-path={crane_path}"
        )
        return ""

    tag = k8s_test_helper.get_dev_namespace()
    cmd = [
        binary_path,
        "push",
        "--env=DEV",
        f"--tag={tag}",
    ]

    # Add the paths to the dependencies if they are provided
    if oci_innie_path:
        cmd.append(f"--oci-innie={oci_innie_path}")
    if oci_outie_path:
        cmd.append(f"--oci-outie={oci_outie_path}")
    if jsonnet_path:
        cmd.append(f"--jsonnet={jsonnet_path}")
    if clusters_path:
        cmd.append(f"--clusters={clusters_path}")
    if crane_path:
        cmd.append(f"--crane={crane_path}")

    print(f"Building and pushing beachhead image with tag: {tag}")
    print(f"Command: {' '.join(cmd)}")

    try:
        result = subprocess.run(
            cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )
        print(f"Successfully built and pushed beachhead image: {result.stdout}")
        return tag
    except subprocess.CalledProcessError as e:
        print(f"Failed to build and push beachhead image: {e.stderr}")
        raise


@pytest.fixture(scope="session", autouse=True)
def beachhead_image(request):
    """Build and push the beachhead image for remote agent testing."""
    yield build_and_push_beachhead_image(request.config)


def pytest_generate_tests(metafunc):
    endpoint = metafunc.config.getoption("--endpoint")
    if endpoint:
        all_models = discover_models_from_endpoint(metafunc.config)
    else:
        all_models = _DEFAULT_ALL_MODELS
    if "completion_model_name" in metafunc.fixturenames:
        test_input_output = [item[1] for item in all_models if item[0] == "completion"]

        # Parametrize the test function
        metafunc.parametrize(
            "completion_model_name", test_input_output, ids=test_input_output
        )
    if "edit_model_name" in metafunc.fixturenames:
        test_input_output = [item[1] for item in all_models if item[0] == "edit"]

        # Parametrize the test function
        metafunc.parametrize("edit_model_name", test_input_output)
    if "chat_model_name" in metafunc.fixturenames:
        test_input_output = [item[1] for item in all_models if item[0] == "chat"]

        # Parametrize the test function
        metafunc.parametrize("chat_model_name", test_input_output)
    if "agent_model_name" in metafunc.fixturenames:
        test_input_output = [item[1] for item in all_models if item[0] == "agent"]

        # Parametrize the test function
        metafunc.parametrize("agent_model_name", test_input_output)
    if "next_edit_model_name" in metafunc.fixturenames:
        test_input_output = [item[1] for item in all_models if item[0] == "next_edit"]

        # Parametrize the test function
        metafunc.parametrize("next_edit_model_name", test_input_output)
    if "op_type" in metafunc.fixturenames:
        test_input_output = [item for item in all_models]
        test_ids = [f"{item[0]}_{item[1]}" for item in all_models]

        # Parametrize the test function
        metafunc.parametrize("op_type,model_name", test_input_output, ids=test_ids)
    if "smart_paste_model_name" in metafunc.fixturenames:
        test_input_output = [item[1] for item in all_models if item[0] == "smart_paste"]

        # Parametrize the test function
        metafunc.parametrize("smart_paste_model_name", test_input_output)


@pytest.fixture(scope="session")
def is_running_in_test_infra() -> bool:
    """Returns whether we are running in k8s.

    When running in k8s, we don't need to setup ingresses for
    communicating with the services.
    """
    return k8s_test_helper.is_running_in_test_infra()


@pytest.fixture(scope="session")
def kubecfg_script():
    """Returns the kubecfg script to use."""
    return Path("services/test/test_kubecfg.sh")


@pytest.fixture(scope="session")
def application_shard_deploy(request, is_running_in_test_infra, kubecfg_script):
    """Return the deploy info of the application shard to use.

    If applicable, it will update or create the application in Kubernetes.
    """
    k8s_test_helper.print_link_to_logs()
    endpoint = request.config.getoption("--endpoint")
    if endpoint:
        namespace = request.config.getoption("--namespace")
        assert namespace
        yield k8s_test_helper.DeployInfo(
            namespace=namespace,
            deployments=[],
            resolves=[],
            kubectl=k8s_test_helper.get_kubectl(
                cloud=request.config.getoption("--cloud"),
                namespace=request.config.getoption("--namespace"),
            ),
        )
        return
    skip_deploy = request.config.getoption("--skip-deployment")
    skip_deploy_check = request.config.getoption("--skip-deployment-check")
    skip_deploy_check_teardown = request.config.getoption(
        "--skip-deployment-teardown"
    ) and not request.config.getoption("--teardown-deployment")
    cloud = request.config.getoption("--cloud")
    with k8s_test_helper.deploy(
        skip_deploy=skip_deploy,
        skip_deploy_check=skip_deploy_check,
        skip_deploy_check_teardown=skip_deploy_check_teardown,
        cloud=cloud,
        kubecfg_binaries=[
            kubecfg_script,
        ],
    ) as deploy_info:
        yield deploy_info


@pytest.fixture(scope="session")
def tokens(
    application_shard_deploy: k8s_test_helper.DeployInfo, external_endpoint_mode: bool
) -> Generator[TokenUtil | None, None, None]:
    """Yield a token util objects that makes adding api tokens easier."""
    if external_endpoint_mode:
        # in external endpoint mode, the test can't create api tokens.
        yield None
        return
    tokens = TokenUtil(application_shard_deploy)
    yield tokens


@pytest.fixture(scope="session")
def user_token(tokens: TokenUtil | None, request):
    """Return a user token for the default tenant to use."""
    token_file = request.config.getoption("--token-file")
    if token_file:
        yield Path(token_file).read_text(encoding="utf-8").strip()
    elif not tokens:
        raise ValueError("No token file specified")
    else:
        yield tokens.add_token("e2e-test", "augment")


@dataclass
class ApplicationShard:
    """Information about the application shard."""

    namespace: str
    url: str
    token: str


@pytest.fixture(scope="session")
def external_endpoint_mode(request):
    """Return True if the endpoint mode is enabled.

    External mode can be against any augment deployment incl. staging. Certain
    features like feature flags, tenant and token creation are not available.
    """
    return request.config.getoption("--endpoint") is not None


@pytest.fixture(scope="session")
def application_shard(
    user_token: str,
    application_shard_deploy: k8s_test_helper.DeployInfo,
    request,
    is_running_in_test_infra,
):
    """Return an URL prefix for the application shard to use.

    If applicable, it will update or create the application in Kubernetes.
    """
    endpoint = request.config.getoption("--endpoint")
    if endpoint:
        yield ApplicationShard(
            namespace=application_shard_deploy.namespace,
            url=f"https://{endpoint}",
            token=user_token,
        )
        return

    if not is_running_in_test_infra:
        _wait_ingress(
            application_shard_deploy.kubectl.api_client,
            application_shard_deploy.namespace,
            "api-proxy",
        )
        api_proxy_endpoint = _get_address(
            application_shard_deploy.kubectl.api_client,
            application_shard_deploy.namespace,
            "api-proxy",
        )
        assert api_proxy_endpoint, "Failed to find api proxy endpoint"
        url = f"https://{api_proxy_endpoint}"
    else:
        url = f"https://api-proxy-svc.{application_shard_deploy.namespace}:8082"

    print(f"Wait for response from {url}", flush=True)
    for _ in range(120):
        if _test_response(url, user_token):
            print(f"Got valid response from {url}", flush=True)
            break
        else:
            time.sleep(1)
            continue
    else:
        print(f"TIMEOUT testing response from {url}")
        assert False

    yield ApplicationShard(
        namespace=application_shard_deploy.namespace,
        url=url,
        token=user_token,
    )


def _wait_ingress(api_client: kubernetes.client.ApiClient | None, namespace, app_name):
    v1 = kubernetes.client.NetworkingV1Api(api_client)
    for _ in range(240):
        ret = v1.list_namespaced_ingress(namespace)
        for ingress in ret.items:
            if not ingress.metadata or not ingress.metadata.labels:
                continue
            ingress_app_name = ingress.metadata.labels.get("app")
            if (
                ingress_app_name == app_name
                and ingress.status.load_balancer
                and ingress.status.load_balancer.ingress
                and len(ingress.status.load_balancer.ingress) > 0
            ):
                print("Ingress is ready", flush=True)
                return
        time.sleep(1)


def _get_address(api_client: kubernetes.client.ApiClient | None, namespace, app_name):
    v1 = kubernetes.client.CoreV1Api(api_client)
    ret = v1.list_namespaced_service(namespace)
    for service in ret.items:
        if not service.metadata or not service.metadata.labels:
            continue
        service_app_name = service.metadata.labels.get("app")
        if service_app_name == app_name:
            ingress = service.status.load_balancer.ingress
            if ingress:
                hostname = ingress[0].hostname
                return hostname

    v1 = kubernetes.client.NetworkingV1Api(api_client)
    ret = v1.list_namespaced_ingress(namespace)
    for ingress in ret.items:
        if not ingress.metadata or not ingress.metadata.labels:
            continue
        ingress_app_name = ingress.metadata.labels.get("app")
        if ingress_app_name == app_name:
            return ingress.spec.rules[0].host


def _get_pod(
    api_client: kubernetes.client.ApiClient | None, namespace: str, deployment_name: str
) -> Optional[str]:
    v1 = kubernetes.client.CoreV1Api(api_client)
    ret = v1.list_namespaced_pod(namespace)
    for service in ret.items:
        name = service.metadata.name
        if name.startswith(deployment_name):
            return name
