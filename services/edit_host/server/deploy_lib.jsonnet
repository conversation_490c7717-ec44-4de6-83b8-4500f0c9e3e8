// K8S deployment file for an edit server
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local anthropicLib = import 'services/chat_host/server/anthropic-lib.jsonnet';
local openaiLib = import 'services/chat_host/server/openai-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local requestInsightPublisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';

// function to create an edit host
//    env: environemnt
//    namespace: namespace to use
//    model config: model config (from //services/deploy/configs) object
//    retrievalConfigs: configuration of the retrievals
//    overrideConfig: object that overrides an edit host configuration
function(
  env,
  namespace,
  namespace_config,
  cloud,
  name,
  modelConfig,
  inferenceServices,
  retrievalConfigs,
  inferenceMtls,
  mtls,
  overrideConfig=null,
  thirdPartyInferenceConfig=null,  // For third-party clients
)
  local appName = name;
  // The edit model names are quite long and tend to share prefixes, but we want
  // unique short names because IAM service accounts have a 30 character limit.
  // To get around this, use a hash to generate a short name.
  local shortAppName = 'edit-%s' % std.substr(std.md5('%s-edit' % name), 0, 7);
  local serviceAccount = gcpLib.createServiceAccount(
    app=appName, cloud=cloud, env=env, namespace=namespace, overridePrefix=shortAppName, iam=true
  );
  local anthropicSealedSecret = anthropicLib(appName=appName, namespace=namespace, cloud=cloud);
  local openaiSealedSecret = openaiLib(appName=appName, namespace=namespace, cloud=cloud);
  local xaiSealedSecretName = std.asciiLower(env) + '-edit-xai-api-key';
  local xaiSealedSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='xai-api-key',
    version={
      PROD: '2',
      STAGING: '2',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
    overrideSecretName=xaiSealedSecretName,
  );
  local fireworksSecretName = std.asciiLower(env) + '-edit-fireworks-api-key';
  local fireworksGcpSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='fireworks-api-key',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
    overrideSecretName=fireworksSecretName,
  );
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(cloud=cloud, env=env, namespace=namespace, appName=appName);
  local requestInsightPublisher = requestInsightPublisherLib(cloud, env, namespace, appName=appName);
  local services = grpcLib.grpcService(name, namespace=namespace);
  // create a client TLS certificate to securely access the content manager
  local clientCert = certLib.createClientCert(
    name='%s-client-certificate' % name,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
  );
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-infer-client-cert' % name,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='inference-client-certs',
    dnsNames=grpcLib.grpcServiceNames(name, namespace=namespace)
  );
  // create a server certificate for MTLS
  local serverCert = certLib.createServerCert(
    name='%s-server-certificate' % name,
    namespace=namespace,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNames(name),
    volumeName='certs',
  );
  local isThirdParty = thirdPartyInferenceConfig != null;
  local handlerType = if std.objectHas(modelConfig, 'handler_type') then modelConfig.handler_type else if isThirdParty then 'InstructionHandlerThirdParty' else 'InstructionHandlerTokenized';
  local configMap =
    local config = {
      port: 50051,
      model_name: name,
      inference_host_endpoints: if isThirdParty then null else std.mapWithKey(function(k, v) '%s:50051' % v, inferenceServices),
      content_manager_endpoint: 'content-manager-svc:50051',
      working_set_endpoint: if namespace_config.flags.workingSetEndpoint != '' then namespace_config.flags.workingSetEndpoint else null,
      feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
      dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
      third_party_arbiter_endpoint: endpointsLib.getThirdPartyArbiterGrpcUrl(env=env, namespace=namespace, cloud=cloud),
      retrieval: { retrieval_configs: retrievalConfigs },
      handler_type: handlerType,
      handler_config: if isThirdParty then {
        assert std.objectHas(thirdPartyInferenceConfig, 'client_type'),
        assert std.objectHas(thirdPartyInferenceConfig, 'model_name'),
        gcp_project_id: cloudInfo[cloud].projectId,
        gcp_region: if std.objectHas(thirdPartyInferenceConfig, 'gcp_region') then thirdPartyInferenceConfig.gcp_region else cloudInfo[cloud].region,
        anthropic_api_key_path: anthropicSealedSecret.api_key_path,
        openai_api_key_path: openaiSealedSecret.api_key_path,
        xai_api_key_path: xaiSealedSecret.filePath,
        fireworks_api_key_path: fireworksGcpSecret.filePath,
        client_type: thirdPartyInferenceConfig.client_type,
        model_name: thirdPartyInferenceConfig.model_name,
        instruction_token_apportionment: thirdPartyInferenceConfig.instruction_token_apportionment,
        smart_paste_token_apportionment: thirdPartyInferenceConfig.smart_paste_token_apportionment,
        temperature: thirdPartyInferenceConfig.temperature,
        max_output_length: thirdPartyInferenceConfig.max_output_length,
      } else {
        assert std.objectHas(modelConfig, 'inference') && std.objectHas(modelConfig.inference, 'token_apportionment'),
        assert !std.objectHas(modelConfig.inference, 'prompt_formatter_config'),
        assert std.objectHas(modelConfig, 'sampling_params'),
        assert std.objectHas(modelConfig, 'post_process_config'),
        tokenizer_name: modelConfig.inference.tokenizer_name,
        prompt_formatter_name: if std.objectHas(modelConfig.inference, 'prompt_formatter_name') then modelConfig.inference.prompt_formatter_name else null,
        token_apportionment: modelConfig.inference.token_apportionment,
        max_context_length: modelConfig.inference.max_context_length,
        sampling_params: modelConfig.sampling_params,
        post_process_config: modelConfig.post_process_config,
        max_output_length: if std.objectHas(modelConfig.inference, 'max_output_length') then modelConfig.inference.max_output_length else null,
      },
      auth_config: {
        token_exchange_endpoint: 'token-exchange-central-svc.%s:50051' % cloudInfo.getCentralNamespaceForNamespace(env=env, namespace=namespace, cloud=cloud),
      },
    } + if !mtls then {} else {
      client_mtls: clientCert.config,
      server_mtls: serverCert.config,
      central_client_mtls: centralClientCert.config,
    };
    local overriddenConfig = if overrideConfig == null then config else std.mergePatch(config, overrideConfig);
    configMapLib.createConfigMap(appName=appName, namespace=namespace, config=overriddenConfig);
  local serviceAccountObjects = serviceAccount.objects + [
    requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
  ];
  local grantAiPlatformAccess = if isThirdParty then gcpLib.grantAccess(
    env=env,
    namespace=namespace,
    appName=appName,
    name='aiplatform-%s-edit-grant' % name,
    resourceRef={
      kind: 'Project',
      external: 'projects/%s' % cloudInfo[cloud].projectId,
    },
    bindings=[
      {
        role: 'roles/aiplatform.user',
        members: [
          {
            memberFrom: {
              serviceAccountRef: {
                name: serviceAccount.iamServiceAccountName,
              },
            },
          },
        ],
      },
    ],
    abandon=true,
  ) else {};
  local retrievalObjects = std.map(function(r) r.getRetrievalObjects(name, namespace), retrievalConfigs);
  local container =
    {
      name: 'edit',
      target: {
        name: '//services/edit_host/server:image',
        dst: 'edit',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      env: telemetryLib.telemetryEnv('edit-host', telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      volumeMounts: lib.flatten([
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
        centralClientCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
        requestInsightPublisher.volumeMountDef,
        anthropicSealedSecret.volumeMountDef,
        openaiSealedSecret.volumeMountDef,
        xaiSealedSecret.volumeMountDef,
        fireworksGcpSecret.volumeMountDef,
        [r.volumeMountDef for r in retrievalObjects if r.volumeMountDef != null],
      ]),
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 1,
          // This is conservative, looks like we usually use <1Gi
          memory: '2Gi',
        },
      },
    };
  local pod =
    {
      priorityClassName: cloudInfo.envToPriorityClass(env),
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      volumes: lib.flatten([
        configMap.podVolumeDef,
        clientCert.podVolumeDef,
        centralClientCert.podVolumeDef,
        serverCert.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
        requestInsightPublisher.podVolumeDef,
        anthropicSealedSecret.podVolumeDef,
        openaiSealedSecret.podVolumeDef,
        xaiSealedSecret.podVolumeDef,
        fireworksGcpSecret.podVolumeDef,
        [r.podVolumeDef for r in retrievalObjects if r.volumeMountDef != null],
      ]),
    };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local pbd = nodeLib.podDisruption(appName=appName, namespace=namespace, env=env);
  lib.flatten([
    clientCert.objects,
    centralClientCert.objects,
    serverCert.objects,
    configMap.objects,
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: name,
        namespace: namespace,
        labels: {
          app: name,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        replicas: if env == 'DEV' then 1 else 2,
        minReadySeconds: if env == 'DEV' then 0 else 60,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: name,
          },
        },
        template: {
          metadata: {
            labels: {
              app: name,
            },
          },
          spec: pod + {
            affinity: affinity,
            tolerations: tolerations,
            priorityClassName: cloudInfo.envToPriorityClass(env),
          },
        },
      },
    },
    services,
    [r.objects for r in retrievalObjects],
    serviceAccountObjects,
    grantAiPlatformAccess,
    dynamicFeatureFlags.k8s_objects,
    anthropicSealedSecret.objects,
    openaiSealedSecret.objects,
    xaiSealedSecret.objects,
    fireworksGcpSecret.objects,
    pbd,
  ])
