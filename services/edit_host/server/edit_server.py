"""Server of a edit host."""

import argparse
import logging
import os
import pathlib
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from typing import Iterator, Optional

import grpc
import opentelemetry
import opentelemetry.instrumentation.grpc
from services.inference_host.client.multiplex import (
    InferenceStubFactoryProtocol,
    MultiplexInferenceStubFactory,
)
import structlog
from dataclasses_json import dataclass_json
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection
from prometheus_client import Counter, Histogram, start_http_server

import base.feature_flags
import base.tracing
import services.lib.grpc.tls_config.tls_config as tls_config
from base.logging.struct_logging import setup_struct_logging

# We use chat prompt formatter in instructions for now, it raises this ExceedContextLength
from base.prompt_format_chat.prompt_formatter import ExceedContextLength
from base.python.grpc import client_options
from base.python.opentelemetry_utils.traced_threadpool import TracedThr<PERSON><PERSON><PERSON><PERSON>xecutor
from base.python.signal_handler.signal_handler import <PERSON><PERSON><PERSON>ignal<PERSON><PERSON><PERSON>
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval import retriever_factory
from services.lib.retrieval.retriever_request_insight_builder import (
    RetrieverRequestInsightBuilder,
)
from services.lib.retrieval.retriever import Retriever
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.edit_host import edit_pb2, edit_pb2_grpc
from services.edit_host.server.edit_handler import create_edit_handler
from services.edit_host.server.instruction_handler import (
    create_instruction_handler_third_party,
    create_instruction_handler_tokenized,
)
from services.edit_host.server.instruction_handler_metrics import (
    InstructionHandlerMetrics,
)
from services.edit_host.server.handler import (
    EditHandlerProtocol,
    InstructionResult,
    InstructionResultStatusCode,
)
from services.inference_host import infer_pb2_grpc
from services.inference_host.client import inference_host_client
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.token_exchange.client import client as token_exchange_client
from services.lib.grpc.auth.service_auth_interceptor import (
    get_auth_info_from_grpc_context,
    ServiceAuthInterceptor,
)
from services.lib.grpc.auth.service_token_auth import (
    GrpcPublicKeySource,
    ServiceTokenAuth,
)
from services.lib.grpc.metrics.metrics import MetricsServerInterceptor
from services.working_set.client.client import get_working_set_client, WorkingSetClient
from services.third_party_arbiter.client.client import (
    GrpcThirdPartyArbiterClientImpl,
    ThirdPartyArbiterClient,
)

log = structlog.get_logger()

# feature flag to enable multiplexing over multiple inference hosts
#
# Example
# {"default": 0.5, "gsc": 0.5}
#
# This assumes that the inference hosts are named "default" and "gsc"
_INFERER_MULTIPLEX = base.feature_flags.StringFlag("edit_inferer_multiplex", "")

_edits_counter = Counter(
    "au_edit_host_counter",
    "Counts edit hosts requests",
    ["model", "status_code", "request_source", "tenant_name"],
)

char_len_buckets = [0] + [2**i for i in range(15)]  # Buckets go up to 2^14 = 16384

_edit_prefix_size_summary = Histogram(
    "au_edit_host_prefix_size",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=char_len_buckets,
    documentation="Size of the edit prefix in chars",
)
_edit_suffix_size_summary = Histogram(
    "au_edit_host_suffix_size",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=char_len_buckets,
    documentation="Size of the edit suffix in chars",
)
_edit_instruction_size_summary = Histogram(
    "au_edit_host_instruction_size",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=char_len_buckets,
    documentation="Size of the edit instruction in chars",
)
_edit_selected_text_size_summary = Histogram(
    "au_edit_host_selected_text_size",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=char_len_buckets,
    documentation="Size of the selected text in chars",
)
_edit_selected_text_lines_summary = Histogram(
    "au_edit_host_selected_text_lines",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=char_len_buckets,
    documentation="Size of the selected text in lines",
)
# buckets for the latency histogram, from 1ms to 10s
INF = float("inf")
latency_buckets = tuple([(1.2**i - 1) / 100.0 for i in range(45)] + [INF])
_edit_latency = Histogram(
    "au_edit_host_latency_seconds",
    labelnames=["model", "status_code", "request_source", "tenant_name"],
    documentation="Latency of a edit request (in the edit host)",
    buckets=latency_buckets,
)

_edit_requests_cancelled = Counter(
    "au_edit_requests_cancelled",
    "Edit requests cancelled",
    labelnames=["model", "request_source", "tenant_name"],
)


tracer = base.tracing.setup_opentelemetry()


@dataclass_json
@dataclass
class AuthConfig:
    """Configuration for the token authentication."""

    # the endpoint for the token exchange service
    token_exchange_endpoint: str


@dataclass_json
@dataclass
class Config:
    """Configuration for an edit server."""

    port: int

    # name of the model.
    #
    # this has to match the name of the model instance config
    model_name: str

    inference_host_endpoints: dict[str, str] | None
    third_party_arbiter_endpoint: str
    content_manager_endpoint: str
    working_set_endpoint: str | None

    retrieval: retriever_factory.RetrievalConfig
    handler_type: str
    handler_config: dict

    auth_config: AuthConfig

    max_handler_workers: int = 4
    """Maximum number of workers for the handlers."""
    max_server_workers: int = 32
    """Maximum number of workers for the server."""

    feature_flags_sdk_key_path: Optional[str] = None
    dynamic_feature_flags_endpoint: str | None = None

    client_mtls: Optional[tls_config.ClientConfig] = None
    central_client_mtls: Optional[tls_config.ClientConfig] = None
    server_mtls: Optional[tls_config.ServerConfig] = None

    shutdown_grace_period_s: float = 20.0


class EditServices(edit_pb2_grpc.EditServicer):
    """Services to implement the edit service API."""

    def __init__(
        self,
        config: Config,
        handler: EditHandlerProtocol,
        working_set_client: WorkingSetClient,
    ):
        self.config = config
        self.handler = handler
        self.working_set_client = working_set_client
        self.background_pool = TracedThreadPoolExecutor(
            thread_name_prefix="background-notify-"
        )

    def _generate_instruction_stream(
        self,
        request: edit_pb2.InstructionRequest,
        context: grpc.ServicerContext,
    ) -> Iterator[InstructionResult]:
        request_start_time = time.time()
        status_code = grpc.StatusCode.UNKNOWN
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        try:
            request_context.bind_context_logging()
            auth_info.bind_context_logging()

            _edit_prefix_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.prefix))
            _edit_suffix_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.suffix))
            _edit_instruction_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.instruction))
            _edit_selected_text_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.selected_text))
            _edit_selected_text_lines_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(request.selected_text.count("\n") + 1)

            log.info("instruction: model_name=%s", request.model_name)

            # register the workingset in the background
            self.background_pool.submit(
                self.working_set_client.register_working_set,
                request.blobs,
                request_context,
            )
            # We create an exclusive thread pool per request to avoid deadlocking.
            with TracedThreadPoolExecutor(
                max_workers=self.config.max_handler_workers,
                thread_name_prefix=f"edit-{request_context.request_id[-8:]}-",
            ) as executor:
                generator = self.handler.instruction_stream(
                    request=request,
                    request_context=request_context,
                    auth_info=auth_info,
                    executor=executor,
                )

                for response in generator:
                    yield response

            # Record response metrics
            status_code = grpc.StatusCode.OK
        except grpc.RpcError as ex:
            status_code = ex.code()  # pylint: disable=no-member # type: ignore
            if status_code == grpc.StatusCode.CANCELLED:
                log.info("Instruction cancelled")
                _edit_requests_cancelled.labels(
                    request.model_name,
                    request_context.request_source,
                    auth_info.metrics_tenant_name,
                ).inc()
            elif status_code == grpc.StatusCode.UNAVAILABLE:
                log.warn("Unavailable: %s", ex)
            else:
                log.error("Instruction failed: %s %s", status_code, ex)
                log.exception(ex)
            context.abort(
                code=status_code,
                details=ex.details(),  # pylint: disable=no-member # type: ignore
            )
            raise
        except ExceedContextLength as ex:
            log.warn("Instruction failed: ExceedContextLength %s", ex)
            yield InstructionResult(
                text="",
                unknown_blob_names=[],
                status_code=InstructionResultStatusCode.EXCEED_CONTEXT_LENGTH,
            )
        except Exception as ex:  # pylint: disable=broad-exception-caught
            log.error("Instruction failed: %s", ex)
            log.exception(ex)
            raise
        finally:
            latency = time.time() - request_start_time
            _edits_counter.labels(
                request.model_name,
                str(status_code),
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).inc()
            _edit_latency.labels(
                request.model_name,
                str(status_code),
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(latency)

    def Edit(
        self,
        request: edit_pb2.EditRequest,
        context: grpc.ServicerContext,
    ) -> edit_pb2.EditResponse:
        request_start_time = time.time()
        status_code = grpc.StatusCode.UNKNOWN
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        try:
            request_context.bind_context_logging()
            auth_info.bind_context_logging()

            _edit_prefix_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.prefix))
            _edit_suffix_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.suffix))
            _edit_instruction_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.instruction))
            _edit_selected_text_size_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(len(request.selected_text))
            _edit_selected_text_lines_summary.labels(
                request.model_name,
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(request.selected_text.count("\n") + 1)

            log.info("edit content: model_name=%s", request.model_name)

            # We create an exclusive thread pool per request to avoid deadlocking.
            with TracedThreadPoolExecutor(
                max_workers=self.config.max_handler_workers,
                thread_name_prefix=f"edit-{request_context.request_id[-8:]}-",
            ) as executor:
                output = self.handler.edit(
                    request=request,
                    request_context=request_context,
                    auth_info=auth_info,
                    executor=executor,
                )

            response = edit_pb2.EditResponse()
            response.text = output.text
            response.unknown_blob_names.extend(output.unknown_blob_names)
            response.checkpoint_not_found = output.checkpoint_not_found
            status_code = grpc.StatusCode.OK
            return response
        except grpc.RpcError as ex:
            status_code = ex.code()  # pylint: disable=no-member # type: ignore
            if status_code == grpc.StatusCode.CANCELLED:
                log.info("Edit cancelled")
                _edit_requests_cancelled.labels(
                    request.model_name,
                    request_context.request_source,
                    auth_info.metrics_tenant_name,
                ).inc()
            else:
                log.error("Edit failed: %s", ex)
                log.exception(ex)
            context.abort(
                code=ex.code(),  # pylint: disable=no-member # type: ignore
                details=ex.details(),  # pylint: disable=no-member # type: ignore
            )
            raise
        except ExceedContextLength as ex:
            log.error("Edit failed: %s", ex)
            log.exception(ex)
            context.abort(
                code=grpc.StatusCode.RESOURCE_EXHAUSTED,
                details=ex.message,
            )
            raise
        except Exception as ex:  # pylint: disable=broad-exception-caught
            log.error("Edit failed: %s", ex)
            log.exception(ex)
            raise
        finally:
            latency = time.time() - request_start_time
            _edits_counter.labels(
                request.model_name,
                str(status_code),
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).inc()
            _edit_latency.labels(
                request.model_name,
                str(status_code),
                request_context.request_source,
                auth_info.metrics_tenant_name,
            ).observe(latency)

    def InstructionStream(
        self,
        request: edit_pb2.InstructionRequest,
        context: grpc.ServicerContext,
    ) -> Iterator[edit_pb2.InstructionResponse]:
        generator = self._generate_instruction_stream(
            request=request,
            context=context,
        )

        for output in generator:
            yield output.to_proto()


def _get_inference_stub_factory(
    inferer_endpoints: dict[str, str] | None,
    credentials: grpc.ChannelCredentials | None,
    model_name: str,
) -> inference_host_client.InferenceStubFactoryProtocol | None:
    """Returns a client to the inference host or inference hosts."""
    if inferer_endpoints is None:
        return None
    rpc_clients: dict[str, infer_pb2_grpc.InfererStub] = {}
    for name, endpoint in inferer_endpoints.items():
        options = client_options.get_grpc_client_options(
            client_options.GrpcClientOptions(load_balancing="headless" in endpoint)
        )

        rpc_client = inference_host_client.create_inference_stub(
            endpoint, credentials=credentials, options=options
        )

        rpc_clients[name] = rpc_client
    if "default" not in rpc_clients:
        raise ValueError("No default client")

    if len(rpc_clients) > 1:
        return MultiplexInferenceStubFactory(
            rpc_clients=rpc_clients,
            model_name=model_name,
            feature_flag=_INFERER_MULTIPLEX,
        )
    else:
        return lambda: list(rpc_clients.values())[0]


def _create_arbiter_client(config: Config) -> ThirdPartyArbiterClient:
    """Creates a ThirdPartyArbiterClient if the endpoint is set.

    Args:
        config: The server configuration.

    Returns:
        A ThirdPartyArbiterClient if the endpoint is set, None otherwise.
    """
    log.info(
        "Creating ThirdPartyArbiterClient with endpoint %s",
        config.third_party_arbiter_endpoint,
    )
    return GrpcThirdPartyArbiterClientImpl(
        endpoint=config.third_party_arbiter_endpoint,
        credentials=tls_config.get_client_tls_creds(config.central_client_mtls),
    )


def _get_handler(
    config: Config,
    inference_stub_factory: InferenceStubFactoryProtocol | None,
    retriever: Retriever,
    ri_publisher: RequestInsightPublisher,
) -> EditHandlerProtocol:
    if "POD_NAMESPACE" not in os.environ:
        raise ValueError("POD_NAMESPACE environment variable must be set.")
    namespace = os.environ["POD_NAMESPACE"]
    if config.handler_type == "EditHandler":
        if inference_stub_factory is None:
            raise ValueError("inference_stub_factory must be set")
        return create_edit_handler(
            config=config.handler_config,
            inference_stub_factory=inference_stub_factory,
            namespace=namespace,
            ri_publisher=ri_publisher,
            retriever=retriever,
        )
    elif config.handler_type == "InstructionHandlerThirdParty":
        arbiter_client = _create_arbiter_client(config)
        return create_instruction_handler_third_party(
            config=config.handler_config,
            namespace=namespace,
            ri_publisher=ri_publisher,
            metrics=InstructionHandlerMetrics(),
            retriever=retriever,
            arbiter_client=arbiter_client,
        )
    elif config.handler_type == "InstructionHandlerTokenized":
        if inference_stub_factory is None:
            raise ValueError("inference_stub_factory must be set")
        return create_instruction_handler_tokenized(
            config=config.handler_config,
            inference_stub_factory=inference_stub_factory,
            namespace=namespace,
            ri_publisher=ri_publisher,
            metrics=InstructionHandlerMetrics(),
            retriever=retriever,
        )
    else:
        raise ValueError(f"Unsupported handler type: {config.handler_type=}")


def get_content_manager_client(config: Config) -> ContentManagerClient:
    """Returns a content manager client."""
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in config.content_manager_endpoint
        )
    )
    return ContentManagerClient.create_for_endpoint(
        config.content_manager_endpoint,
        credentials=tls_config.get_client_tls_creds(config.client_mtls),
        options=options,
    )


def run(
    config: Config,
    namespace: str,
    ri_publisher: RequestInsightPublisher,
    shutdown_event: threading.Event,
):
    content_manager_client = get_content_manager_client(config)
    inference_stub_factory = _get_inference_stub_factory(
        config.inference_host_endpoints,
        tls_config.get_client_tls_creds(config.central_client_mtls),
        config.model_name,
    )
    working_set_client = get_working_set_client(config)
    ri_builder = RetrieverRequestInsightBuilder(ri_publisher)
    retriever = retriever_factory.create_retriever(
        config.retrieval,
        content_manager_client,
        tls_config.get_client_tls_creds(config.client_mtls),
        ri_builder,
        search_timeout_ms=5000,
    )

    handler = _get_handler(config, inference_stub_factory, retriever, ri_publisher)

    token_client = token_exchange_client.GrpcTokenExchangeClient.create(
        config.auth_config.token_exchange_endpoint,
        namespace,
        tls_config.get_client_tls_creds(config.central_client_mtls),
    )
    service_auth = ServiceTokenAuth(
        GrpcPublicKeySource(token_client),
        required_scopes=["CONTENT_R"],
    )
    auth_interceptor = ServiceAuthInterceptor(service_auth)

    server = grpc.server(
        ThreadPoolExecutor(
            max_workers=config.max_server_workers, thread_name_prefix="server-"
        ),
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(),
            MetricsServerInterceptor(),
            auth_interceptor,
        ],
    )
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    edit_pb2_grpc.add_EditServicer_to_server(
        EditServices(config, handler, working_set_client), server
    )
    service_names = (
        edit_pb2.DESCRIPTOR.services_by_name["Edit"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)

    server_credentials = tls_config.get_server_tls_creds(config.server_mtls)

    if server_credentials is not None:
        server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        server.add_insecure_port(f"[::]:{config.port}")
    server.start()
    logging.info("Listening on %s", config.port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # type: ignore # pylint: disable=no-member
        config_file.read_text()
    )


def main():
    """Run the server."""
    handler = GracefulSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    RequestInsightPublisher.add_publisher_arguments(parser)
    args = parser.parse_args()
    logging.info("Args %s", args)

    if "POD_NAMESPACE" not in os.environ:
        raise RuntimeError("POD_NAMESPACE environment variable not set")
    namespace = os.environ["POD_NAMESPACE"]

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    # begin listening for Prometheus requests
    start_http_server(9090)

    config = _load_config(args.config_file)
    logging.info("Config %s", config)

    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)
    custom_endpoint = None
    if config.dynamic_feature_flags_endpoint is not None:
        custom_endpoint = config.dynamic_feature_flags_endpoint

    context = base.feature_flags.Context.setup(path, custom_endpoint)
    base.feature_flags.set_global_context(context)

    ri_publisher = RequestInsightPublisher.create_from_args(args)

    run(config, namespace, ri_publisher, handler.get_shutdown_event())


if __name__ == "__main__":
    main()
