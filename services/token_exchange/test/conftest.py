"""conftest.py is a special file that pytest will automatically load.

pytest will automatically load and execute before any other test files. This is a
good place to put fixtures that are used by multiple test files.
"""

import contextlib
import time
from pathlib import Path
from typing import Callable, Generator, Optional

import grpc
import kubernetes
import pytest

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from base.python.cloud import cloud as cloud_lib
from base.python.grpc.health_check import HealthChecker
from services.tenant_watcher.client.client import TenantsClient
from services.token_exchange.client.client import (
    GrpcTokenExchangeClient,
    TokenExchangeClient,
)

# Container details


def pytest_addoption(parser):
    """Add command line options to pytest."""
    parser.addoption(
        "--skip-deployment",
        action="store_true",
        help="skip deploy and delete of the models",
        default=False,
    )
    parser.addoption(
        "--cloud",
        help="Cloud to use",
        default=cloud_lib.get_default_cloud(),
        choices=cloud_lib.get_cloud_list(gcp_only=True),
    )


def _test_response(
    url: str,
    credentials: Optional[grpc.ChannelCredentials],
    service_name: str,
    options: list[tuple[str, str]] | None = None,
) -> bool:
    try:
        checker = HealthChecker(url, credentials, options=options)
        status = checker.is_serving(service_name)
        return status
    except grpc.RpcError as ex:
        print(ex, flush=True)
        return False


def _get_pod(
    api_client: kubernetes.client.ApiClient | None, namespace: str, deployment_name: str
) -> Optional[str]:
    v1 = kubernetes.client.CoreV1Api(api_client)
    ret = v1.list_namespaced_pod(namespace)
    for service in ret.items:
        name = service.metadata.name
        if name.startswith(deployment_name):
            return name


@pytest.fixture(scope="session")
def tenant_id(
    tenant_watcher_client: TenantsClient,
) -> str:
    tenants = tenant_watcher_client.get_tenants()
    assert tenants
    tid = tenants[0].id
    assert tid, "Failed to find tenant ID for tenant 'augment'"
    return tid


@pytest.fixture(scope="session")
def tenant_watcher_client(
    token_exchange_deploy: k8s_test_helper.DeployInfo,
) -> Generator[TenantsClient, None, None]:
    """Return an GRPC stub to access the tenant watcher.

    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = token_exchange_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "tenant-central-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with token_exchange_deploy.kubectl.port_forward(
            "deployment/tenant-central", 50051, 50053
        ) as port:
            url = f"localhost:{port}"
            time.sleep(5)
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(10)
                continue
            else:
                yield TenantsClient(
                    url,
                    credentials=credentials,
                    options=options,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield TenantsClient(
            url,
            credentials=credentials,
            options=options,
        )


def cleanup_old_token_exchange_secrets(
    deploy_info: k8s_test_helper.DeployInfo,
):
    """Clean up any old secrets."""
    deployments = [
        "token-exchange-central",
        "listen-cert-rotate",
    ]

    # Scale down the deployments to 0 replicas
    for deployment in deployments:
        deploy_info.kubectl.run(
            args=[
                "scale",
                "deployment",
                deployment,
                "--replicas=0",
                "--namespace",
                deploy_info.namespace,
            ]
        )

    # Delete the secrets, to avoid secrets from previous runs interfering with
    # this run
    result = deploy_info.kubectl.run(
        args=[
            "delete",
            "secret",
            "token-exchange-central-jwt-cert",
            "token-exchange-central-extra-jwt-cert",
            "token-exchange-central-server-cert",
            "--namespace",
            deploy_info.namespace,
            "--ignore-not-found",
        ]
    )
    assert result.returncode == 0 or "NotFound" in result.stdout

    # Scale the deployments back up
    for deployment in deployments:
        deploy_info.kubectl.run(
            args=[
                "scale",
                "deployment",
                deployment,
                "--replicas=1",
                "--namespace",
                deploy_info.namespace,
            ]
        )


@pytest.fixture(scope="session")
def token_exchange_deploy(
    request,
) -> Generator[k8s_test_helper.DeployInfo, None, None]:
    """Deploys a token exchange server as pytest fixture."""
    k8s_test_helper.print_link_to_logs()
    skip_deploy = request.config.getoption("--skip-deployment")
    cloud = request.config.getoption("--cloud")

    with k8s_test_helper.deploy(
        skip_deploy=skip_deploy,
        cloud=cloud,
        kubecfg_binaries=[
            Path("services/token_exchange/test/test_kubecfg.sh"),
        ],
    ) as deploy_info:
        cleanup_old_token_exchange_secrets(deploy_info)

        yield deploy_info

        # Also clean up the configmap created by listen-cert-rotate
        result = deploy_info.kubectl.run(
            args=[
                "delete",
                "configmap",
                "token-exchange-central-jwks-configmap",
                "--namespace",
                deploy_info.namespace,
            ]
        )
        assert result.returncode == 0, result.stderr


@pytest.fixture(scope="session")
def create_token_exchange_client(
    token_exchange_deploy: k8s_test_helper.DeployInfo,
) -> Callable:
    """Return a function to create a GRPC stub to access the token exchange server."""

    @contextlib.contextmanager
    def _token_exchange_client() -> Generator[TokenExchangeClient, None, None]:
        credentials = token_exchange_deploy.kubectl.load_grpc_credentials()
        # change the endpoint name when verifying the TLS certificate as "localhost" is not
        # on the certificate.
        target_name_override_endpoint = "token-exchange-central-svc"
        options: list[tuple[str, str]] = [
            ("grpc.ssl_target_name_override", target_name_override_endpoint)
        ]

        url = ""
        for _ in range(30):
            with token_exchange_deploy.kubectl.port_forward(
                "deployment/token-exchange-central", 50051, 50052
            ) as port:
                url = f"localhost:{port}"
                time.sleep(5)
                if not _test_response(
                    url, credentials=credentials, options=options, service_name=""
                ):
                    time.sleep(10)
                    continue
                else:
                    yield GrpcTokenExchangeClient(
                        url,
                        credentials=credentials,
                        options=options,
                        namespace=token_exchange_deploy.namespace,
                    )
                    break
        else:
            print(f"TIMEOUT testing response from {url}")
            # the test will likely fail
            yield GrpcTokenExchangeClient(
                url,
                credentials=credentials,
                options=options,
                namespace=token_exchange_deploy.namespace,
            )

    return _token_exchange_client
