"""Integration tests for token_exchange."""

import json
import time

import jwt
import pydantic

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from services.auth.central.server import auth_entities_pb2
from services.lib.grpc.auth.service_token_auth import (
    GrpcPublicKeySource,
    ServiceTokenAuth,
)
from services.token_exchange import token_exchange_pb2


def test_basic(
    token_exchange_deploy: k8s_test_helper.DeployInfo,
    tenant_id: str,
    create_token_exchange_client,
):
    """Get a signed key and verify it."""
    with create_token_exchange_client() as token_exchange_client:
        source = GrpcPublicKeySource(token_exchange_client)
        auth = ServiceTokenAuth(source, required_scopes=["CONTENT_R"])

        token = token_exchange_client.get_signed_token_for_user(
            "user123",
            auth_entities_pb2.UserId(
                user_id="user123",
                user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
            ),
            "<EMAIL>",
            tenant_id,
        )
        auth_info = auth.validate_access([], token, "test")
        assert auth_info is not None
        assert auth_info.tenant_id == tenant_id
        assert auth_info.shard_namespace == token_exchange_deploy.namespace


def get_jwt_kid(token: pydantic.SecretStr) -> str:
    """Get the kid from a JWT."""
    unverified_header = jwt.get_unverified_header(token.get_secret_value())
    key_id = unverified_header.get("kid")
    if key_id is None:
        return ""
    return str(key_id)


def jwks_keys(
    token_exchange_deploy: k8s_test_helper.DeployInfo,
) -> dict[str, dict[str, str]]:
    """Get the keys in the configmap.

    Returns a dict of kid -> key.
    """
    result = token_exchange_deploy.kubectl.run(
        args=[
            "get",
            "configmap",
            "token-exchange-central-jwks-configmap",
            "--namespace",
            token_exchange_deploy.namespace,
            "-o",
            "json",
        ]
    )
    assert result.returncode == 0
    secret_json = json.loads(result.stdout)
    data = secret_json["data"]

    keys = {}
    for jwk in json.loads(data["publicJwks.json"])["keys"]:
        keys[jwk["kid"]] = jwk
    return keys


def wait_for_kids_in_configmap(
    token_exchange_deploy: k8s_test_helper.DeployInfo,
    kids: list[str],
):
    """Wait for the given kid to show up in the configmap."""
    for _ in range(30):
        if all(kid in jwks_keys(token_exchange_deploy) for kid in kids):
            return
        print("Waiting for kid to show up in configmap")
        time.sleep(5)
    assert False, f"Timed out waiting for kids {kids} to show up in configmap: {jwks_keys(token_exchange_deploy)}"


def rotate_cert(
    token_exchange_deploy: k8s_test_helper.DeployInfo,
    current_kid: str,
):
    """Rotate the cert used by token-exchange to sign jwts.

    This works by updating the commonName of the cert, see
    https://cert-manager.io/docs/usage/certificate/#reissuance-triggered-by-user-actions.

    """
    # Wait for the current kid to show up in the configmap first, then rotate
    # the cert and verifies that we have another, new kid in the configmap. This
    # helps avoid races where the current kid is not yet in the configmap at the
    # time this function is called.
    # listen-cert-rotate is supposed to do this
    wait_for_kids_in_configmap(token_exchange_deploy, [current_kid])

    jwks_before = jwks_keys(token_exchange_deploy)
    num_keys_before = len(jwks_before)
    result = token_exchange_deploy.kubectl.run(
        args=[
            "patch",
            "certificate",
            "token-exchange-central-jwt-cert",
            "--type=merge",
            "--patch",
            '{"spec":{"commonName":"token-exchange-central-jwt-cert-test"}}',
        ]
    )
    assert result.returncode == 0, result.stderr

    # Wait for the new cert's kid to show up in the configmap
    for _ in range(30):
        if len(jwks_keys(token_exchange_deploy)) > num_keys_before:
            print("Rotated cert token-exchange-central-jwt-cert")
            return
        print("Waiting for cert rotation to complete")
        time.sleep(5)
    assert (
        False
    ), f"Timed out waiting for cert rotation: {jwks_keys(token_exchange_deploy)}"


def test_cert_rotation(
    token_exchange_deploy: k8s_test_helper.DeployInfo,
    tenant_id: str,
    create_token_exchange_client,
):
    """Tests that cert rotation is handled correctly."""
    with create_token_exchange_client() as token_exchange_client:
        source = GrpcPublicKeySource(token_exchange_client)
        auth = ServiceTokenAuth(source, required_scopes=["REQUEST_R"])

        # Sign and verify a token. Use a service token which lasts longer than a
        # user token
        token1 = token_exchange_client.get_signed_token_for_service(
            tenant_id, [token_exchange_pb2.REQUEST_R]
        )
        auth.validate_access([], token1, "test")

        # Rotate the certificate
        rotate_cert(token_exchange_deploy, get_jwt_kid(token1))

        # Get a second token, and validate that both tokens still work. Without
        # restarting the token-exchange service, the kid should not have changed
        token2 = token_exchange_client.get_signed_token_for_service(
            tenant_id, [token_exchange_pb2.REQUEST_R]
        )
        auth.validate_access([], token1, "test")
        auth.validate_access([], token2, "test")
        assert get_jwt_kid(token1) == get_jwt_kid(token2)

    # Restart token-exchange to pick up the new cert, and get a new client to
    # the new pod
    token_exchange_deploy.kubectl.rollout("token-exchange-central")

    with create_token_exchange_client() as token_exchange_client:
        source = GrpcPublicKeySource(token_exchange_client)
        auth = ServiceTokenAuth(source, required_scopes=["REQUEST_R"])

        # Get a new token and validate it
        token3 = token_exchange_client.get_signed_token_for_service(
            tenant_id, [token_exchange_pb2.REQUEST_R]
        )
        auth.validate_access([], token3, "test")

        # The new token should have a new kid
        assert get_jwt_kid(token3) != get_jwt_kid(token1)

        # All three tokens should be in the configmap
        wait_for_kids_in_configmap(
            token_exchange_deploy,
            [get_jwt_kid(token1), get_jwt_kid(token2), get_jwt_kid(token3)],
        )

        # But the old tokens should still work
        auth.validate_access([], token1, "test")
        auth.validate_access([], token2, "test")
