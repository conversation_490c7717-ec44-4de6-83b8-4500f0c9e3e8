package main

import (
	"context"
	"slices"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	tokenproto "github.com/augmentcode/augment/services/token_exchange/proto"
)

type AclCheck interface {
	Check(ctx context.Context, email string, tenantID string, shardNamespace string, scopes []string) error
	CheckCentral(ctx context.Context, scopes []string) error
}

type NoopAclCheck struct{}

func (n *NoopAclCheck) Check(ctx context.Context, email string, tenantID string, shardNamespace string, scopes []string) error {
	return nil
}

func (n *NoopAclCheck) CheckCentral(ctx context.Context, scopes []string) error {
	return nil
}

type InformerAclCheck struct {
	informer            SupportUIAccessInformer
	trustedAclNamespace string
}

func NewInformerAclCheck(informer SupportUIAccessInformer, trustedAclNamespace string) *InformerAclCheck {
	return &InformerAclCheck{
		informer:            informer,
		trustedAclNamespace: trustedAclNamespace,
	}
}

func checkScopes(ctx context.Context, scopes []string, validScopes []string) bool {
	valid := true
	for _, scope := range scopes {
		if !slices.Contains(validScopes, scope) {
			log.Ctx(ctx).Warn().Msgf("Invalid scope %s", scope)
			valid = false
			break
		}
	}
	return valid
}

func isShardNamespaceAccessAllowed(acl *SupportUIAccess, shardNamespace string, trustedAclNamespace string) bool {
	if acl.GetNamespace() == trustedAclNamespace && acl.Spec.NamespaceScope != "" {
		if acl.Spec.NamespaceScope == "*" {
			return true
		}
		return acl.Spec.NamespaceScope == shardNamespace
	}
	return acl.GetNamespace() == shardNamespace
}

func (i *InformerAclCheck) match(acl *SupportUIAccess, userName string, tenantID string, shardNamespace string) bool {
	if acl.Spec.UserName != userName {
		return false
	}

	if !isShardNamespaceAccessAllowed(acl, shardNamespace, i.trustedAclNamespace) {
		return false
	}

	if acl.Spec.Scope == "full" {
		// full mean access to all tenants in the namespace
		return true
	}
	return acl.Spec.TenantID == "*" || acl.Spec.TenantID == tenantID
}

// checkAcl checks if a single ACL entry allows the requested access
func (i *InformerAclCheck) checkAcl(ctx context.Context, acl *SupportUIAccess, userName string, scopes []string) bool {
	// Parse the expiration time string to time.Time
	expirationTime, err := time.Parse(time.RFC3339, acl.Spec.ExpiresAt)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error parsing expiration time")
		return false
	}

	currentTime := time.Now().UTC()
	if currentTime.Before(expirationTime) {
		log.Ctx(ctx).Info().Msg("The token is still valid.")
	} else {
		log.Ctx(ctx).Info().Msg("The token has expired.")
		return false
	}

	// Check if custom scopes are defined
	if acl.Spec.TokenScopes != nil && len(acl.Spec.TokenScopes) > 0 {
		log.Ctx(ctx).Info().Msgf("Using token scopes: %v", acl.Spec.TokenScopes)
		if len(scopes) == 0 {
			log.Ctx(ctx).Warn().Msg("Scope is required")
			return false
		}
		if !checkScopes(ctx, scopes, acl.Spec.TokenScopes) {
			return false
		}
		return true
	}

	// Fall back to standard scope checks if no custom scopes are defined
	if acl.Spec.Scope == "full" {
		// allow any scope
		return true
	} else if acl.Spec.Scope == "requests" {
		if len(scopes) == 0 {
			log.Ctx(ctx).Warn().Msg("Scope is required")
			return false
		}
		validScopes := []string{"CONTENT_ADMIN", "CONTENT_R", "CONTENT_RW", "REQUEST_R", "REQUEST_RESTRICTED_R"}
		if !checkScopes(ctx, scopes, validScopes) {
			return false
		}
		return true
	} else if acl.Spec.Scope == "users" {
		if len(scopes) == 0 {
			log.Ctx(ctx).Warn().Msg("Scope is required")
			return false
		}
		validScopes := []string{"AUTH_R", "AUTH_RW"}
		if !checkScopes(ctx, scopes, validScopes) {
			return false
		}
		return true
	} else {
		log.Ctx(ctx).Warn().Msgf("Invalid scope %s", acl.Spec.Scope)
		return false
	}
}

func (i *InformerAclCheck) Check(ctx context.Context, email string, tenantID string, shardNamespace string, scopes []string) error {
	// remove REQUEST_CONFIDENTIAL_R as it is always granted for IAP
	newScopes := make([]string, 0, len(scopes))
	for _, scope := range scopes {
		if scope == "REQUEST_CONFIDENTIAL_R" {
			// Skip REQUEST_CONFIDENTIAL_R as it's always granted
			continue
		}
		newScopes = append(newScopes, scope)
	}

	// If we only had REQUEST_CONFIDENTIAL_R or no scopes at all, return success
	if len(newScopes) == 0 {
		// nothing to check if there are no scopes are requested
		return nil
	}

	// Use the filtered scopes for the rest of the function
	scopes = newScopes
	acls, err := i.informer.List()
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error listing acls")
		return status.Error(codes.Internal, "Error listing acls")
	}

	// check that the emails ends on @augmentcode.com
	if !strings.HasSuffix(email, "@augmentcode.com") {
		log.Ctx(ctx).Error().Msgf("Invalid token email suffix")
		return status.Error(codes.PermissionDenied, "Invalid token")
	}
	if len(scopes) == 0 {
		log.Ctx(ctx).Info().Msg("No scopes, skipping acl check")
		return nil
	}
	// extract suffix
	userName := email[0 : len(email)-len("@augmentcode.com")]

	for _, scope := range scopes {
		if _, ok := tokenproto.Scope_value[scope]; !ok {
			log.Ctx(ctx).Warn().Msgf("Invalid scope %s", scope)
			return status.Error(codes.InvalidArgument, "Invalid scope")
		}
	}

	for _, acl := range acls {
		log.Ctx(ctx).Info().Msgf("Checking acl %v", acl)
		if !i.match(&acl, userName, tenantID, shardNamespace) {
			continue
		}

		if i.checkAcl(ctx, &acl, userName, scopes) {
			return nil
		}
	}

	log.Ctx(ctx).Warn().Msg("Access denied")
	return status.Error(codes.PermissionDenied, "Access denied")
}

func (i *InformerAclCheck) CheckCentral(ctx context.Context, scopes []string) error {
	// For now the only central scope is REQUEST_CONFIDENTIAL_R, which doesn't require any ACL.
	for _, scope := range scopes {
		if scope != "REQUEST_CONFIDENTIAL_R" {
			log.Ctx(ctx).Warn().Msgf("Invalid scope %s", scope)
			return status.Error(codes.PermissionDenied, "Access denied")
		}
	}
	return nil
}
