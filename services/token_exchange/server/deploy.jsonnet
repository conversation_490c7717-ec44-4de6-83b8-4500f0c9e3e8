local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';

function(env, namespace, cloud, namespace_config)
  local appName = 'token-exchange-central';
  local shortAppName = 'tk-exch';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=true, overridePrefix=shortAppName);
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);

  local grpcService = grpcLib.grpcService(appName=appName, namespace=namespace);

  local requestInsightPublisher = (import 'services/request_insight/publisher/publisher_lib.jsonnet')(
    cloud, env, namespace, appName
  );
  local requestInsightPublishingEnabled = requestInsightPublisher.requestInsightPublishingEnabled;
  local requestInsightPublisherAccess = requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName);

  // This is created by listen-cert-rotate, and contains public keys for earlier
  // versions of the cert used by token-exchange.
  local certHistoryConfigMapName = 'token-exchange-central-jwks-configmap';

  // create a server certificate for MTLS
  local serverCert = certLib.createCentralServerCert(
    name='%s-server-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
    volumeName='certs',
  );
  local clientCert = certLib.createCentralClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    env=env,
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
    appName=appName,
    volumeName='client-certs',
  );
  local jwtCert = certLib.createPrivateKeyCert(
    name='%s-jwt-cert' % appName,
    namespace=namespace,
    appName=appName,
    algorithm='ECDSA',
    size=256,
    // We sync this across clusters using the cloud-sync tool, to enable cross
    // cluster validation of tokens
    cloudSync=true,
  );
  local roles = [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'RoleBinding',
      metadata: {
        name: '%s-read-config-maps' % appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          name: serviceAccount.name,
        },
      ],
      roleRef: {
        kind: 'Role',
        name: '%s-read-config-maps' % appName,
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'Role',
      metadata: {
        name: '%s-read-config-maps' % appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      rules: [
        {
          apiGroups: [''],  // "" indicates the core API group
          resources: ['configmaps'],
          resourceNames: [certHistoryConfigMapName],
          verbs: ['get', 'watch', 'list'],
        },
      ],
    },
  ] + if env == 'DEV' then [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'RoleBinding',
      metadata: {
        name: 'token-exchange-support-accessrole-binding',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          namespace: namespace,
          name: serviceAccount.name,
        },
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'support-ui-accesses-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
  ] else [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: '%s-token-exchange-support-accessrole-binding' % namespace,
        labels: {
          app: appName,
        },
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          namespace: namespace,
          name: serviceAccount.name,
        },
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'support-ui-accesses-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
  ];

  // CONTENT_RW & SETTINGS_RW were the default scopes before
  // we added explicit scopes for services when requesting user tokens
  local user_token_configs = [
    {
      regex: '^auth-query-svc$',
      scopes: ['CONTENT_RW', 'CONTENT_R', 'SETTINGS_RW', 'SETTINGS_R'],
    },
    // customer-ui-svc needs auth data for tier switching
    {
      regex: '^customer-ui-svc$',
      scopes: ['CONTENT_RW', 'CONTENT_R', 'SETTINGS_RW', 'SETTINGS_R', 'AUTH_RW', 'AUTH_R'],
    },
    {
      regex: '^slack-bot-processor-svc$',
      scopes: ['CONTENT_RW', 'CONTENT_R', 'SETTINGS_RW', 'SETTINGS_R'],
    },
    {
      regex: '^grpc-debug-svc$',
      scopes: ['CONTENT_RW', 'CONTENT_R', 'SETTINGS_RW', 'SETTINGS_R'],
    },
    {
      regex: '^auth-central-grpc-svc$',
      scopes: ['CONTENT_RW', 'CONTENT_R', 'SETTINGS_RW', 'SETTINGS_R'],
    },
    {
      regex: '^tenant-gc-svc$',
      scopes: ['CONTENT_RW', 'CONTENT_R', 'SETTINGS_RW', 'SETTINGS_R'],
    },
  ] + (if env == 'DEV' then [
         // support should to be able to generate user tokens for testing only
         {
           regex: '^augment-support$',
           scopes: ['CONTENT_RW', 'CONTENT_R', 'SETTINGS_RW', 'SETTINGS_R'],
         },
       ] else []);

  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local iapAudience = '/projects/%s/global/backendServices/' % cloudInfo[cloud].projectNumber;
  local extraJwtTokens = if env == 'DEV' then {
    config: [],
    volumeMounts: [],
    volumes: [],
  } else if cloud == 'GCP_US_CENTRAL1_PROD' || cloud == 'GCP_EU_WEST4_PROD' then {
    config: [
      // either CENTRAL1 OR EU
      {
        algorithm: 'ES256',
        cert_path: '/extra-jwt-certs/tls.crt',
        key_path: '/extra-jwt-certs/tls.key',
      },
      // add GCP_US_CENTRAL1_GSC_PROD
      {
        algorithm: 'ES256',
        cert_path: '/extra-%s-certs/tls.crt' % cloudInfo.GCP_US_CENTRAL1_GSC_PROD.shortName,
        key_path: '/extra-%s-certs/tls.key' % cloudInfo.GCP_US_CENTRAL1_GSC_PROD.shortName,
      },
    ],
    volumeMounts: [
      {
        name: 'extra-jwt-certs',
        mountPath: '/extra-jwt-certs',
        readOnly: true,
      },
      {
        name: 'extra-%s-certs' % cloudInfo.GCP_US_CENTRAL1_GSC_PROD.shortName,
        mountPath: '/extra-%s-certs' % cloudInfo.GCP_US_CENTRAL1_GSC_PROD.shortName,
        readOnly: true,
      },
    ],
    volumes: [
      {
        name: 'extra-jwt-certs',
        secret: {
          // This is copied between clusters by cloud-sync
          secretName: 'token-exchange-central-extra-jwt-cert',  // pragma: allowlist secret
        },
      },
      {
        name: 'extra-%s-certs' % cloudInfo.GCP_US_CENTRAL1_GSC_PROD.shortName,
        secret: {
          // This is copied between clusters by cloud-sync
          secretName: 'token-exchange-central-%s-cert' % cloudInfo.GCP_US_CENTRAL1_GSC_PROD.shortName,  // pragma: allowlist secret
        },
      },
    ],
  }
  else  // cloud == 'GCP_US_CENTRAL1_GSC_PROD'
    // add the two other clusters by short name
    {
      config: [
        {
          algorithm: 'ES256',
          cert_path: '/extra-%s-certs/tls.crt' % cloudInfo.GCP_US_CENTRAL1_PROD.shortName,
          key_path: '/extra-%s-certs/tls.key' % cloudInfo.GCP_US_CENTRAL1_PROD.shortName,
        },
        {
          algorithm: 'ES256',
          cert_path: '/extra-%s-certs/tls.crt' % cloudInfo.GCP_EU_WEST4_PROD.shortName,
          key_path: '/extra-%s-certs/tls.key' % cloudInfo.GCP_EU_WEST4_PROD.shortName,
        },
      ],
      volumeMounts: [
        {
          name: 'extra-%s-certs' % cloudInfo.GCP_US_CENTRAL1_PROD.shortName,
          mountPath: '/extra-%s-certs' % cloudInfo.GCP_US_CENTRAL1_PROD.shortName,
          readOnly: true,
        },
        {
          name: 'extra-%s-certs' % cloudInfo.GCP_EU_WEST4_PROD.shortName,
          mountPath: '/extra-%s-certs' % cloudInfo.GCP_EU_WEST4_PROD.shortName,
          readOnly: true,
        },
      ],
      volumes: [
        {
          name: 'extra-%s-certs' % cloudInfo.GCP_US_CENTRAL1_PROD.shortName,
          secret: {
            // This is copied between clusters by cloud-sync
            secretName: 'token-exchange-central-%s-cert' % cloudInfo.GCP_US_CENTRAL1_PROD.shortName,  // pragma: allowlist secret
          },
        },
        {
          name: 'extra-%s-certs' % cloudInfo.GCP_EU_WEST4_PROD.shortName,
          secret: {
            // This is copied between clusters by cloud-sync
            secretName: 'token-exchange-central-%s-cert' % cloudInfo.GCP_EU_WEST4_PROD.shortName,  // pragma: allowlist secret
          },
        },
      ],
    };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config={
    port: 50051,
    server_mtls: if mtls then serverCert.config else null,
    client_mtls: if mtls then clientCert.config else null,
    request_insight_publisher_config_path: if requestInsightPublishingEnabled then requestInsightPublisher.configFilePath else '',
    prom_port: 9090,
    // token exchange server config
    jwt_signing_key: jwtCert.config + {
      algorithm: 'ES256',
    },
    central_namespace: namespace,
    central_cloud: cloud,
    trusted_acl_namespace: namespace,
    iap_jwt_verifier_disabled: namespace_config.flags.iapJwtVerifierDisabled,
    extra_jwt_verification_keys: extraJwtTokens.config,
    // We skip peer validation if we are in dev and forceMtls is not set.
    skip_peer_validation: env == 'DEV' && (!namespace_config.flags.forceMtls || namespace_config.flags.skipTokenExchangePeerValidation),
    user_token_expiration: if env == 'DEV' then '15m' else '5m',
    iap_token_expiration: '5m',
    iap_token_max_expiration: '1h',
    iap_audience_prefix: [iapAudience],
    service_token_configs: [
      // the indexer service needs to be able to read and write content
      {
        regex: '^embedding-indexer-[a-z0-9-]+-svc',
        scopes: ['CONTENT_RW', 'CONTENT_R'],
        expiration: '60m',
      },
      // augment support (usually oncall) with Genie permissions can do anything
      {
        regex: 'augment-support$',
        scopes: ['CONTENT_ADMIN', 'CONTENT_RW', 'CONTENT_R', 'REQUEST_RW', 'REQUEST_R', 'AUTH_RW', 'AUTH_R', 'SETTINGS_RW', 'SETTINGS_R'],
        expiration: '60m',
      },
      // the auth query service needs to be able to read and write auth data
      // and the tenant gc service needs to be able to read and write auth data to remove users from deleted tenants
      {
        regex: '^auth-query-svc$|^tenant-gc-svc$',
        scopes: ['AUTH_RW', 'AUTH_R'],
        expiration: '60m',
      },
      // request insight needs to be able to read and write request data and read content (but getcontent also require CONTENT_RW)
      {
        regex: '^request-insight-svc$',
        scopes: ['REQUEST_RW', 'REQUEST_R', 'CONTENT_RW', 'REQUEST_RESTRICTED_R'],
        expiration: '60m',
      },
      // The request insight sync job needs to read user data and all subscriptions.
      {
        regex: '^request-insight-sync-svc$',
        scopes: ['AUTH_R', 'PII_ADMIN'],
        expiration: '60m',
      },
      // The request insight support db exporter needs to read and write to GCS
      {
        regex: '^request-insight-support-database-exporter-svc$',
        scopes: ['REQUEST_RESTRICTED_RW', 'REQUEST_RESTRICTED_R'],
        expiration: '60m',
      },
      // RI find missing can only read content (but most read operations require write permissions for catchup)
      {
        regex: '^request-insight-find-missing-svc$|^ri-blob-exporter-svc$|^ri-backfill-recency-blobs-svc$',
        scopes: ['CONTENT_RW'],
        expiration: '60m',
      },
      // background embedding search find missing calls can only read content
      {
        regex: '^embeddings-search-cpu-svc$|^embeddings-search-cpu-[0-9]+-svc$',
        scopes: ['CONTENT_RW'],
        expiration: '60m',
      },
      // background docset uploader can upload content
      {
        regex: '^doc-sets-svc$',
        scopes: ['CONTENT_RW'],
        expiration: '60m',
      },
      // Chat hosts read available docsets
      {
        regex: '^chat-.*-svc$',
        scopes: ['CONTENT_R'],
        expiration: '60m',
      },
      {
        regex: '^auth-central-grpc-svc$',
        scopes: ['AUTH_R', 'AUTH_RW'],
        expiration: '60m',
      },
      {
        regex: '^checkpoint-indexer-svc$',
        scopes: ['CONTENT_RW'],
        expiration: '60m',
      },
      // customer ui can only read auth data
      {
        regex: '^customer-ui-svc$',
        scopes: ['AUTH_R'],
        expiration: '60m',
      },
      // github processor can read and write content and settings
      {
        regex: '^github-processor-svc$',
        scopes: ['CONTENT_RW', 'CONTENT_R', 'SETTINGS_R', 'SETTINGS_RW'],
        expiration: '60m',
      },
      // slack bot processor can read and write content and settings
      {
        regex: '^slack-bot-processor-svc$',
        scopes: ['CONTENT_RW', 'CONTENT_R', 'SETTINGS_R', 'SETTINGS_RW'],
        expiration: '15m',
      },
      // slack bot webhook can read and write settings
      {
        regex: '^slack-bot-webhook-grpc-svc$',
        scopes: ['SETTINGS_R', 'SETTINGS_RW'],
        expiration: '15m',
      },
      // github state
      {
        regex: '^github-state-svc$',
        scopes: ['CONTENT_RW', 'CONTENT_R'],
        expiration: '60m',
      },
      // glean can read and write settings
      {
        regex: '^glean-svc$',
        scopes: ['SETTINGS_R', 'SETTINGS_RW'],
        expiration: '60m',
      },
      // notion can read and write settings
      {
        regex: '^notion-svc$',
        scopes: ['SETTINGS_R', 'SETTINGS_RW'],
        expiration: '60m',
      },
      // linear can read and write settings
      {
        regex: '^linear-svc$',
        scopes: ['SETTINGS_R', 'SETTINGS_RW'],
        expiration: '60m',
      },
      {
        regex: '^working-set-svc$',
        scopes: ['CONTENT_RW', 'CONTENT_R'],
        expiration: '60m',
      },
      {
        regex: '^misuse-monitor-svc$',
        scopes: ['AUTH_RW'],
        expiration: '60m',
      },
      // billing service need to read user data related to billing
      {
        regex: '^billing-svc$',
        scopes: ['AUTH_R'],
        expiration: '60m',
      },
      // billing central service need to read and write user data related to billing
      {
        regex: '^billing-central-svc$',
        scopes: ['AUTH_R', 'AUTH_RW'],
        expiration: '60m',
      },
      // remote agents service need to read and write remote agents data asynchronously
      {
        regex: '^remote-agents-svc$',
        scopes: ['CONTENT_RW', 'CONTENT_R'],
        expiration: '60m',
      },
    ],
    user_token_configs: user_token_configs,
    allowed_cross_namespace_peers: [
      '^auth-central-grpc-svc$',
      '^slack-bot-webhook-grpc-svc$',
      '^request-insight-sync-svc$',
      '^tenant-gc-svc$',
      '^misuse-monitor-svc$',
      '^billing-central-svc$',
    ],
    iap_token_allowed_regexp: '^support-ui-svc$|^support-ui-v2-svc$|^support-central-svc$',
    central_iap_token_allowed_regexp: '^support-ui-v2-svc$',
    tenant_watcher_endpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    acl_namespace: if env == 'DEV' then namespace else '',
    acl_check_enabled: namespace_config.flags.tokenExchangeAclCheck,
    should_cache_tokens: true,
    user_cache_expiration: '10s',
    service_cache_expiration: '10m',
    max_cache_keys: 10000,
    read_keys_from_config_map: true,
    config_map_name: certHistoryConfigMapName,
  });
  local container =
    {
      name: 'token-exchange',
      target: {
        name: '//services/token_exchange/server:image',
        dst: 'token_exchange_server',
      },
      args: [
        '--config',
        configMap.filename,
      ],
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % appName, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % appName, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      env: lib.flatten([
        telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)),
        dynamicFeatureFlags.env,
      ]),
      volumeMounts: lib.flatten([
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
        jwtCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
        if requestInsightPublishingEnabled then requestInsightPublisher.volumeMountDef else [],
        extraJwtTokens.volumeMounts,
      ]),
      resources: {
        limits: {
          cpu: 2,
          memory: '2Gi',
        },
      },
    };
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    tolerations: tolerations,
    affinity: affinity,
    volumes: lib.flatten([
      serverCert.podVolumeDef,
      clientCert.podVolumeDef,
      jwtCert.podVolumeDef,
      configMap.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
      if requestInsightPublishingEnabled then requestInsightPublisher.podVolumeDef else [],
      extraJwtTokens.volumes,
    ]),
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        // Restart if any of the configmaps associated with this deployment
        // change. Particularly the extra configmaps which may contain public
        // keys from other clusters (US/EU).
        'reloader.stakater.com/auto': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 4,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
          annotations: {
            'reloader.stakater.com/auto': 'true',
          },
        },
        spec: pod,
      },
    },
  };
  local pdb = nodeLib.podDisruption(appName=appName, namespace=namespace, env=env);

  lib.flatten([
    configMap.objects,
    deployment,
    grpcService,
    serverCert.objects,
    clientCert.objects,
    jwtCert.objects,
    roles,
    serviceAccount.objects,
    dynamicFeatureFlags.k8s_objects,
    pdb,
    if requestInsightPublishingEnabled then requestInsightPublisherAccess else [],
  ])
