// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/token_exchange/token_exchange.proto (package token_exchange, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, Duration, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage, Struct } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { UserId } from "../auth/central/server/auth_entities_pb.js";

/**
 * @generated from enum token_exchange.Scope
 */
export declare enum Scope {
  /**
   * @generated from enum value: AUTH_R = 0;
   */
  AUTH_R = 0,

  /**
   * @generated from enum value: AUTH_RW = 1;
   */
  AUTH_RW = 1,

  /**
   * @generated from enum value: REQUEST_R = 2;
   */
  REQUEST_R = 2,

  /**
   * @generated from enum value: REQUEST_RW = 3;
   */
  REQUEST_RW = 3,

  /**
   * @generated from enum value: CONTENT_R = 4;
   */
  CONTENT_R = 4,

  /**
   * @generated from enum value: CONTENT_RW = 5;
   */
  CONTENT_RW = 5,

  /**
   * @generated from enum value: CONTENT_ADMIN = 8;
   */
  CONTENT_ADMIN = 8,

  /**
   * @generated from enum value: SETTINGS_R = 6;
   */
  SETTINGS_R = 6,

  /**
   * @generated from enum value: SETTINGS_RW = 7;
   */
  SETTINGS_RW = 7,

  /**
   * @generated from enum value: REQUEST_CONFIDENTIAL_R = 9;
   */
  REQUEST_CONFIDENTIAL_R = 9,

  /**
   * @generated from enum value: REQUEST_RESTRICTED_R = 10;
   */
  REQUEST_RESTRICTED_R = 10,

  /**
   * @generated from enum value: REQUEST_RESTRICTED_RW = 11;
   */
  REQUEST_RESTRICTED_RW = 11,

  /**
   * @generated from enum value: BIGTABLE_DELETE = 12;
   */
  BIGTABLE_DELETE = 12,

  /**
   * @generated from enum value: PII_ADMIN = 14;
   */
  PII_ADMIN = 14,
}

/**
 * @generated from message token_exchange.GetSignedTokenForUserRequest
 */
export declare class GetSignedTokenForUserRequest extends Message<GetSignedTokenForUserRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: auth_entities.UserId opaque_user_id = 6;
   */
  opaqueUserId?: UserId;

  /**
   * @generated from field: optional string user_email = 5;
   */
  userEmail?: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  /**
   * @generated from field: string shard_namespace = 3;
   */
  shardNamespace: string;

  /**
   * @generated from field: google.protobuf.Struct additional_claims = 4;
   */
  additionalClaims?: Struct;

  /**
   * @generated from field: repeated token_exchange.Scope scopes = 7;
   */
  scopes: Scope[];

  constructor(data?: PartialMessage<GetSignedTokenForUserRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "token_exchange.GetSignedTokenForUserRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSignedTokenForUserRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSignedTokenForUserRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSignedTokenForUserRequest;

  static equals(a: GetSignedTokenForUserRequest | PlainMessage<GetSignedTokenForUserRequest> | undefined, b: GetSignedTokenForUserRequest | PlainMessage<GetSignedTokenForUserRequest> | undefined): boolean;
}

/**
 * @generated from message token_exchange.GetSignedTokenForUserResponse
 */
export declare class GetSignedTokenForUserResponse extends Message<GetSignedTokenForUserResponse> {
  /**
   * @generated from field: string signed_token = 1;
   */
  signedToken: string;

  constructor(data?: PartialMessage<GetSignedTokenForUserResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "token_exchange.GetSignedTokenForUserResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSignedTokenForUserResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSignedTokenForUserResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSignedTokenForUserResponse;

  static equals(a: GetSignedTokenForUserResponse | PlainMessage<GetSignedTokenForUserResponse> | undefined, b: GetSignedTokenForUserResponse | PlainMessage<GetSignedTokenForUserResponse> | undefined): boolean;
}

/**
 * @generated from message token_exchange.GetSignedTokenForServiceRequest
 */
export declare class GetSignedTokenForServiceRequest extends Message<GetSignedTokenForServiceRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: string shard_namespace = 2;
   */
  shardNamespace: string;

  /**
   * @generated from field: repeated token_exchange.Scope scopes = 3;
   */
  scopes: Scope[];

  constructor(data?: PartialMessage<GetSignedTokenForServiceRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "token_exchange.GetSignedTokenForServiceRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSignedTokenForServiceRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSignedTokenForServiceRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSignedTokenForServiceRequest;

  static equals(a: GetSignedTokenForServiceRequest | PlainMessage<GetSignedTokenForServiceRequest> | undefined, b: GetSignedTokenForServiceRequest | PlainMessage<GetSignedTokenForServiceRequest> | undefined): boolean;
}

/**
 * @generated from message token_exchange.GetSignedTokenForServiceResponse
 */
export declare class GetSignedTokenForServiceResponse extends Message<GetSignedTokenForServiceResponse> {
  /**
   * @generated from field: string signed_token = 1;
   */
  signedToken: string;

  constructor(data?: PartialMessage<GetSignedTokenForServiceResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "token_exchange.GetSignedTokenForServiceResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSignedTokenForServiceResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSignedTokenForServiceResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSignedTokenForServiceResponse;

  static equals(a: GetSignedTokenForServiceResponse | PlainMessage<GetSignedTokenForServiceResponse> | undefined, b: GetSignedTokenForServiceResponse | PlainMessage<GetSignedTokenForServiceResponse> | undefined): boolean;
}

/**
 * @generated from message token_exchange.GetSignedTokenForIAPTokenRequest
 */
export declare class GetSignedTokenForIAPTokenRequest extends Message<GetSignedTokenForIAPTokenRequest> {
  /**
   * @generated from field: string iap_token = 1;
   */
  iapToken: string;

  /**
   * @generated from field: string shard_namespace = 2;
   */
  shardNamespace: string;

  /**
   * @generated from field: string tenant_id = 5;
   */
  tenantId: string;

  /**
   * @generated from field: google.protobuf.Duration expiration = 4;
   */
  expiration?: Duration;

  /**
   * @generated from field: repeated token_exchange.Scope scopes = 3;
   */
  scopes: Scope[];

  constructor(data?: PartialMessage<GetSignedTokenForIAPTokenRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "token_exchange.GetSignedTokenForIAPTokenRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSignedTokenForIAPTokenRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSignedTokenForIAPTokenRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSignedTokenForIAPTokenRequest;

  static equals(a: GetSignedTokenForIAPTokenRequest | PlainMessage<GetSignedTokenForIAPTokenRequest> | undefined, b: GetSignedTokenForIAPTokenRequest | PlainMessage<GetSignedTokenForIAPTokenRequest> | undefined): boolean;
}

/**
 * @generated from message token_exchange.GetSignedTokenForIAPTokenResponse
 */
export declare class GetSignedTokenForIAPTokenResponse extends Message<GetSignedTokenForIAPTokenResponse> {
  /**
   * @generated from field: string signed_token = 1;
   */
  signedToken: string;

  constructor(data?: PartialMessage<GetSignedTokenForIAPTokenResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "token_exchange.GetSignedTokenForIAPTokenResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSignedTokenForIAPTokenResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSignedTokenForIAPTokenResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSignedTokenForIAPTokenResponse;

  static equals(a: GetSignedTokenForIAPTokenResponse | PlainMessage<GetSignedTokenForIAPTokenResponse> | undefined, b: GetSignedTokenForIAPTokenResponse | PlainMessage<GetSignedTokenForIAPTokenResponse> | undefined): boolean;
}

/**
 * @generated from message token_exchange.GetVerificationKeyRequest
 */
export declare class GetVerificationKeyRequest extends Message<GetVerificationKeyRequest> {
  constructor(data?: PartialMessage<GetVerificationKeyRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "token_exchange.GetVerificationKeyRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetVerificationKeyRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetVerificationKeyRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetVerificationKeyRequest;

  static equals(a: GetVerificationKeyRequest | PlainMessage<GetVerificationKeyRequest> | undefined, b: GetVerificationKeyRequest | PlainMessage<GetVerificationKeyRequest> | undefined): boolean;
}

/**
 * @generated from message token_exchange.GetVerificationKeyResponse
 */
export declare class GetVerificationKeyResponse extends Message<GetVerificationKeyResponse> {
  /**
   * @generated from field: bytes jwks = 1;
   */
  jwks: Uint8Array;

  constructor(data?: PartialMessage<GetVerificationKeyResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "token_exchange.GetVerificationKeyResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetVerificationKeyResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetVerificationKeyResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetVerificationKeyResponse;

  static equals(a: GetVerificationKeyResponse | PlainMessage<GetVerificationKeyResponse> | undefined, b: GetVerificationKeyResponse | PlainMessage<GetVerificationKeyResponse> | undefined): boolean;
}

