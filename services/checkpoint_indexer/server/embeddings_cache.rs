use std::sync::Arc;
use std::time::Duration;

use async_trait::async_trait;
use bytes::Bytes;
use content_manager_client::BlobScope;
use content_manager_client::ContentManagerClient;
use grpc_service::send_and_ignore;
use half::f16;
use itertools::Itertools;
use moka::future::Cache;
use numpy::NumpyTensor;
use tokio::sync::mpsc;
use tokio::sync::mpsc::Receiver;
use tonic::Result;
use tracing::Instrument;

use crate::{
    config::Config,
    metrics::{
        CacheType, CACHE_BYTES_COUNT, CACHE_ENTRY_COUNT, CACHE_EVICTION_COUNT_COLLECTOR,
        CACHE_LOOKUP_COUNT_COLLECTOR,
    },
};
use blob_names::BlobName;
use content_manager_rs_proto::content_manager::batch_get_content_response::Response::{
    FinalContent, NotFoundContent,
};
use request_context::{RequestContext, TenantId, TenantInfo, EMPTY_TENANT_ID};

const EMBEDDINGS_SUB_KEY: &str = "embeddings.npy";

/// A large number of blob names may exceed the gRPC request size limit. (We already stream the
/// getContent response.) To avoid hitting the limit, we can split the keys into batches. A batch
/// size of 4096 is approximately 256KB for 64byte hex strings.
pub const BLOB_KEY_BATCH_SIZE: usize = 4096;

#[derive(Clone, Debug, Eq, PartialEq, Hash)]
pub struct EmbeddingsKey {
    pub blob_name: BlobName,
    pub transformation_key: String,
}

pub struct ChunkEntry {
    pub key: EmbeddingsKey,
    pub tensor: NumpyTensor<f16>,
}

pub type ChunkCacheEntry = Arc<ChunkEntry>;

pub enum ChunkResult {
    Found(Arc<ChunkEntry>),
    Missing(EmbeddingsKey),
}

#[async_trait]
pub trait EmbeddingsCache {
    /// Returns the requested embeddings in the same order as they were requested.
    /// The entry is None if the request chunk is missing.
    async fn get_embeddings(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        chunk_keys: Vec<EmbeddingsKey>,
    ) -> Receiver<Result<ChunkResult>>;
}

pub struct EmbeddingsCacheImpl {
    data: Arc<EmbeddingsCacheDataImpl>,
}
impl EmbeddingsCacheImpl {
    pub fn new(
        config: Config,
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    ) -> Self {
        Self {
            data: Arc::new(EmbeddingsCacheDataImpl::new(config, content_manager)),
        }
    }
}

#[async_trait]
impl EmbeddingsCache for EmbeddingsCacheImpl {
    async fn get_embeddings(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        chunk_keys: Vec<EmbeddingsKey>,
    ) -> Receiver<Result<ChunkResult>> {
        // 32 is a somewhat arbitrary size, usually callers ask for the top 32 results
        let (tx, rx) = mpsc::channel::<Result<ChunkResult>>(32);
        let data = self.data.clone();
        let request_context = request_context.clone();
        let tenant_info = tenant_info.clone();
        tracing::info!(
            "Getting {} chunks with {} cached entries taking {} bytes",
            chunk_keys.len(),
            self.data.cache.entry_count(),
            self.data.cache.weighted_size()
        );

        let mut cached = vec![None; chunk_keys.len()];

        let tenant_id = tenant_info.tenant_id.clone().unwrap_or(EMPTY_TENANT_ID);
        for (index, chunk_key) in chunk_keys.iter().enumerate() {
            let cached_chunk = data.read_from_cache(&tenant_id, chunk_key.clone()).await;
            if let Some(cached_chunk) = cached_chunk {
                CACHE_LOOKUP_COUNT_COLLECTOR
                    .with_label_values(&[
                        CacheType::Embeddings.as_str(),
                        "hit",
                        tenant_info.metrics_tenant_name(),
                    ])
                    .inc();
                cached[index] = Some(cached_chunk);
            } else {
                CACHE_LOOKUP_COUNT_COLLECTOR
                    .with_label_values(&[
                        CacheType::Embeddings.as_str(),
                        "miss",
                        tenant_info.metrics_tenant_name(),
                    ])
                    .inc();
            }
        }

        CACHE_ENTRY_COUNT
            .with_label_values(&[CacheType::Embeddings.as_str()])
            .set(data.cache.entry_count() as f64);
        CACHE_BYTES_COUNT
            .with_label_values(&[CacheType::Embeddings.as_str()])
            .set(data.cache.weighted_size() as f64);

        let skip_download = cached.iter().all(|x| x.is_some());
        let mut downloaded_chunks_rx = if skip_download {
            // Create an empty receiver stream to simplify later code
            let (_empty_tx, empty_rx) = mpsc::channel::<Result<ChunkResult>>(1);
            empty_rx
        } else {
            let missing_keys = chunk_keys
                .iter()
                .enumerate()
                .filter(|(index, _key)| cached[*index].is_none())
                .map(|(_, key)| key)
                .cloned()
                .collect();
            data.read_from_content_manager(&request_context, &tenant_info, missing_keys)
                .await
        };

        let background_span = tracing::info_span!("get_chunks background");
        tokio::spawn(
            async move {
                for (index, x) in cached.iter().enumerate() {
                    match x {
                        // This chunk is cached
                        Some(cached_chunk) => {
                            if !send_and_ignore(
                                &tx,
                                Ok(ChunkResult::Found(cached_chunk.clone())),
                                "get_chunks cached chunk",
                            )
                            .await
                            {
                                return;
                            }
                        }
                        // This chunk is not cached, listen for the next chunk
                        // from the download stream (which is ordered).
                        None => match downloaded_chunks_rx.recv().await {
                            Some(Ok(maybe_chunk)) => {
                                if let ChunkResult::Found(chunk) = maybe_chunk {
                                    data.cache
                                        .insert(
                                            (tenant_id.clone(), chunk_keys[index].clone()),
                                            chunk.clone(),
                                        )
                                        .await;
                                    if !send_and_ignore(
                                        &tx,
                                        Ok(ChunkResult::Found(chunk)),
                                        "get_chunks downloaded chunk",
                                    )
                                    .await
                                    {
                                        return;
                                    }
                                } else {
                                    // we found a missing chunk. We continue draining the stream, but
                                    // read_from_content_manager will stop downloading on the next batch.
                                    if !send_and_ignore(
                                        &tx,
                                        Ok(maybe_chunk),
                                        "get_chunks none error",
                                    )
                                    .await
                                    {
                                        return;
                                    }
                                }
                            }
                            Some(Err(e)) => {
                                send_and_ignore(&tx, Err(e), "get_chunks error").await;
                                return;
                            }
                            None => {
                                // End of stream
                                tracing::error!(
                                    "missing chunk {:?} and any other chunks after index {}",
                                    chunk_keys[index],
                                    index
                                );
                                return;
                            }
                        },
                    }
                }
            }
            .instrument(background_span),
        );

        rx
    }
}

struct EmbeddingsCacheDataImpl {
    pub content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    pub cache: Cache<(TenantId, EmbeddingsKey), ChunkCacheEntry>,
}

impl EmbeddingsCacheDataImpl {
    pub fn new(
        config: Config,
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    ) -> Self {
        let cache = Cache::builder()
            .max_capacity(config.embeddings_cache_size_bytes)
            .eviction_policy(moka::policy::EvictionPolicy::lru())
            // weigh by the size of the tensor (32 bytes per f16x16 or f32x8), add an estimate of the key size
            .weigher(|_, v: &ChunkCacheEntry| (v.as_ref().tensor.size_bytes() as u32 + 128))
            .time_to_idle(Duration::from_secs(config.cache_tti_seconds))
            .eviction_listener(|_, _, c| {
                let removal_cause = format!("{:?}", c);
                CACHE_EVICTION_COUNT_COLLECTOR
                    .with_label_values(&[CacheType::Embeddings.as_str(), &removal_cause])
                    .inc();
            })
            .build();
        EmbeddingsCacheDataImpl {
            content_manager,
            cache,
        }
    }

    pub async fn read_from_cache(
        &self,
        tenant_id: &TenantId,
        chunk_key: EmbeddingsKey,
    ) -> Option<ChunkCacheEntry> {
        let cache_key = (tenant_id.clone(), chunk_key);
        self.cache.get(&cache_key).await
    }

    // The ordering of the output vector is the same as the input vector - the
    // two will the same length and the indices will match up.
    // This function does not update the cache, that is left to the caller.
    pub async fn read_from_content_manager(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        chunk_keys: Vec<EmbeddingsKey>,
    ) -> Receiver<Result<ChunkResult>> {
        // 32 is a somewhat arbitrary size
        let (tx, rx) = mpsc::channel::<Result<ChunkResult>>(32);

        let download_span = tracing::info_span!("read_from_content_manager download");
        let request_context = request_context.clone();
        let tenant_info = tenant_info.clone();
        let content_manager = self.content_manager.clone();

        tokio::spawn(
            async move {
                let total_chunks = chunk_keys.len();
                let mut processed_count = 0;
                let mut missing_count = 0;

                // Process chunks in batches of BATCH_SIZE
                for chunk_start in (0..chunk_keys.len()).step_by(BLOB_KEY_BATCH_SIZE) {
                    let chunk_end =
                        std::cmp::min(chunk_start + BLOB_KEY_BATCH_SIZE, chunk_keys.len());
                    let batch_keys = &chunk_keys[chunk_start..chunk_end];

                    tracing::info!(
                        "Processing batch of {} blobs (total: {}/{})",
                        batch_keys.len(),
                        processed_count,
                        total_chunks
                    );

                    // Create the batch request
                    let blobs = batch_keys
                        .iter()
                        .map(|key| {
                            (
                                key.blob_name.clone(),
                                BlobScope {
                                    transformation_key: key.transformation_key.clone(),
                                    sub_key: EMBEDDINGS_SUB_KEY.to_string(),
                                },
                            )
                        })
                        .collect_vec();
                    let blobs_clone = blobs.clone();

                    // Get content for this batch
                    let mut response = content_manager
                        .get_content_multiple_scopes(
                            &request_context,
                            &tenant_info.tenant_id,
                            blobs,
                            None,
                        )
                        .await;

                    // Process responses for this batch
                    let mut batch_index = 0;
                    while let Some(next_message) = response.recv().await {
                        let next_message = match next_message {
                            Ok(message) => message,
                            Err(e) => {
                                send_and_ignore(&tx, Err(e), "get_chunks error").await;
                                return;
                            }
                        };

                        // Calculate the global index for the current chunk
                        let current_index = chunk_start + batch_index;
                        let (blob_name, blob_scope) = &blobs_clone[batch_index];

                        match next_message.response {
                            Some(NotFoundContent(missing_content)) => {
                                tracing::warn!("chunk not found: {:?}", missing_content);
                                missing_count += 1;
                                if !send_and_ignore(
                                    &tx,
                                    Ok(ChunkResult::Missing(EmbeddingsKey {
                                        blob_name: blob_name.to_owned(),
                                        transformation_key: blob_scope
                                            .transformation_key
                                            .to_owned(),
                                    })),
                                    "get_chunks none error",
                                )
                                .await
                                {
                                    return;
                                }
                            }
                            Some(FinalContent(final_content)) => {
                                assert_eq!(
                                    String::from(&chunk_keys[current_index].blob_name),
                                    final_content.blob_name
                                );
                                assert_eq!(
                                    chunk_keys[current_index].transformation_key,
                                    final_content.transformation_key
                                );
                                let bytes = Bytes::from(final_content.content);
                                match numpy::read_npy(bytes.clone()) {
                                    Ok(array) => {
                                        let entry = Arc::new(ChunkEntry {
                                            key: chunk_keys[current_index].clone(),
                                            tensor: array,
                                        });
                                        if !send_and_ignore(
                                            &tx,
                                            Ok(ChunkResult::Found(entry)),
                                            "get_chunks chunk",
                                        )
                                        .await
                                        {
                                            return;
                                        }
                                    }
                                    Err(e) => {
                                        tracing::error!("Failed to read numpy array: {:?}", e);
                                        if !send_and_ignore(
                                            &tx,
                                            Ok(ChunkResult::Missing(EmbeddingsKey {
                                                blob_name: blob_name.to_owned(),
                                                transformation_key: blob_scope
                                                    .transformation_key
                                                    .to_owned(),
                                            })),
                                            "get_chunks none error",
                                        )
                                        .await
                                        {
                                            return;
                                        }
                                    }
                                }
                            }
                            None => {}
                        }
                        batch_index += 1;
                        processed_count += 1;
                    }

                    // Verify we processed all chunks in this batch
                    if batch_index != batch_keys.len() {
                        tracing::error!(
                            "Failed to get all chunks from batch: expected={}, actual={}",
                            batch_keys.len(),
                            batch_index
                        );
                    }
                }

                if missing_count != 0 {
                    tracing::info!("Found {} missing chunks", missing_count)
                }

                // Verify we processed all chunks
                if processed_count != total_chunks {
                    tracing::error!(
                        "Failed to get all chunks from content manager: expected={}, actual={}",
                        total_chunks,
                        processed_count
                    );
                }
            }
            .instrument(download_span),
        );

        rx
    }
}
