//! # Index Factory Module
//!
//! This module provides the core functionality for creating and managing search indexes
//! for embeddings associated with checkpoints. It implements the ScaNN (Scalable Nearest Neighbors)
//! algorithm for efficient similarity searches.
//!
//! The main components are:
//! - `IndexFactory` trait: Defines the interface for creating indexes
//! - `IndexFactoryImpl`: Concrete implementation that uses ScaNN for index creation
//! - `FactoryConfig`: Configuration options for the index factory

use crate::embeddings_cache::ChunkResult;
use crate::metrics::{
    INDEX_ASSET_UPLOAD_LATENCY, INDEX_CREATION_LATENCY, INDEX_EMBEDDINGS_COUNT,
    INDEX_EMBEDDINGS_SIZE_BYTES, INDEX_METADATA_UPLOAD_LATENCY, INDEX_OPERATION_COUNTER,
};
use async_trait::async_trait;
use content_manager_client::{AnnIndexBlobInfoData, ContentManagerClient};
use grpc_service::log_response_fn;
use half::f16;
use itertools::{Either, Itertools};
use numpy::NumpyTensor;
use request_context::{RequestContext, TenantInfo};
use scann_rs::ScannRsAsset;
use std::sync::Arc;
use std::time::Instant;

use crate::{
    checkpoint_cache::CheckpointCache,
    embeddings_cache::{EmbeddingsCache, EmbeddingsKey},
};

use tokio::sync::mpsc::Receiver;

/// Number of dimensions in the embeddings used for indexing
pub const EMBEDDING_DIMENSIONS: usize = 512;
/// Number of chunks to query at the same times from content manager
const BLOB_QUERY_CHUNK_SIZE: usize = 100;

/// The `IndexFactory` trait defines the interface for creating search indexes
/// from embeddings associated with checkpoints.
///
/// Implementations of this trait are responsible for:
/// - Retrieving embeddings for a checkpoint
/// - Processing those embeddings to create an efficient search index
/// - Uploading the index assets and metadata
/// - Returning the index ID for future reference
#[async_trait]
pub trait IndexFactory {
    /// Creates an index for the given checkpoint and transformation key.
    ///
    /// # Arguments
    /// * `request_context` - The context of the request, including request ID and session information
    /// * `tenant_info` - Information about the tenant making the request
    /// * `checkpoint_id` - The ID of the checkpoint to create an index for
    /// * `transformation_key` - The key identifying the transformation to apply to the embeddings
    ///
    /// # Returns
    /// * `Result<String, tonic::Status>` - The index ID on success, or an error status on failure
    async fn create_index(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_id: &str,
        transformation_key: &str,
    ) -> Result<String, tonic::Status>;
}

/// Configuration for the ScaNN index factory.
///
/// This struct contains parameters that control how the ScaNN index is built.
/// For more details on the ScaNN algorithm, see the documentation in //third_party/scann_rs.
#[derive(Clone, Debug)]
pub struct FactoryConfig {
    /// Number of threads to use for training the index.
    /// More threads can speed up index creation for large datasets.
    pub training_threads: usize,

    /// Number of dimensions per block for asymmetric hashing.
    /// Usually 8 is a good value for 512-dimensional embeddings.
    pub dims_per_block: usize,

    /// Threshold for asymmetric hashing.
    /// Controls the trade-off between accuracy and speed.
    /// Lower values prioritize accuracy, higher values prioritize speed.
    /// Usually 0.2 is a good value for most use cases.
    pub ah_threadhold: f32,

    /// Number of training samples to use to train the code book.
    /// More samples can improve index quality but increase training time.
    pub training_sample_size: usize,

    /// Number of missing blobs to abort index creation on
    pub max_missing_blobs: usize,
    /// Threshold to stop indexing if the number of embeddings is so high it might crash the
    /// indexer
    pub max_embeddings_to_index: usize,
    /// Threshold to stop indexing if the number of blobs is so high it might DoS the indexer
    pub max_blobs_to_index: usize,
}

impl Default for FactoryConfig {
    fn default() -> Self {
        Self {
            training_threads: 1,
            dims_per_block: 8,
            ah_threadhold: 0.2,
            training_sample_size: 1024,
            max_missing_blobs: 1000,
            max_embeddings_to_index: 10000000,
            max_blobs_to_index: 500000,
        }
    }
}

/// Implementation of the IndexFactory trait that uses ScaNN for creating search indexes.
///
/// This implementation handles:
/// - Retrieving checkpoint data and embeddings from caches
/// - Processing embeddings using the ScaNN algorithm
/// - Serializing the index into assets
/// - Uploading assets and metadata to the content manager
/// - Tracking metrics for index creation and upload operations
pub struct IndexFactoryImpl {
    /// Configuration for the index factory
    config: FactoryConfig,
    /// Client for interacting with the content manager service
    content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    /// Cache for checkpoint data to avoid repeated fetches
    checkpoint_cache: Arc<dyn CheckpointCache + Send + Sync + 'static>,
    /// Cache for embeddings data to improve performance
    embeddings_cache: Arc<dyn EmbeddingsCache + Send + Sync + 'static>,
    /// The ScaNN factory for building indexes
    factory: Arc<scann_rs::ScannFactory>,
}

impl IndexFactoryImpl {
    pub fn new(
        config: FactoryConfig,
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
        checkpoint_cache: Arc<dyn CheckpointCache + Send + Sync + 'static>,
        embeddings_cache: Arc<dyn EmbeddingsCache + Send + Sync + 'static>,
    ) -> tonic::Result<Self> {
        let mut factory =
            scann_rs::ScannFactory::new(EMBEDDING_DIMENSIONS, config.training_threads)?;
        factory.score_ah(
            config.dims_per_block,
            config.ah_threadhold,
            config.training_sample_size,
        );
        Ok(Self {
            config,
            content_manager,
            checkpoint_cache,
            embeddings_cache,
            factory: Arc::new(factory),
        })
    }

    async fn create_index(
        &self,
        embeddings: Vec<f32>,
        shape: Vec<usize>,
        metrics_tenant_name: &str,
        transformation_key: &str,
    ) -> Result<Vec<ScannRsAsset>, tonic::Status> {
        // Record metrics about embeddings
        let num_embeddings = shape[0];
        let embeddings_size_bytes = embeddings.len() * std::mem::size_of::<f32>();
        if num_embeddings == 0 {
            tracing::warn!("No embeddings available for index creation");
            return Err(tonic::Status::invalid_argument(
                "no embeddings available for index creation",
            ));
        }

        tracing::info!(
            "Creating index for {} embeddings ({} bytes)",
            num_embeddings,
            embeddings_size_bytes
        );

        INDEX_EMBEDDINGS_COUNT
            .with_label_values(&[metrics_tenant_name, transformation_key])
            .set(num_embeddings as i64);
        INDEX_EMBEDDINGS_SIZE_BYTES
            .with_label_values(&[metrics_tenant_name, transformation_key])
            .set(embeddings_size_bytes as i64);

        // Increment operation counter
        INDEX_OPERATION_COUNTER
            .with_label_values(&[
                metrics_tenant_name,
                transformation_key,
                "create_index",
                "started",
            ])
            .inc();

        // Create the index and measure time
        let start_time = Instant::now();

        // Clone the factory and other necessary data for the spawned task
        let factory = self.factory.clone();
        let embeddings = embeddings.clone();

        // Spawn a blocking task to perform the CPU-intensive index building
        let result = tokio::task::spawn_blocking(move || {
            factory
                .build(embeddings.as_slice(), num_embeddings)
                .and_then(|index| index.serialize())
        })
        .await
        .unwrap_or_else(|e| {
            tracing::error!("Thread join error during index building: {}", e);
            Err(tonic::Status::internal(format!("Thread join error: {}", e)))
        });

        // Record index creation latency and status
        let duration = start_time.elapsed().as_secs_f64();
        let status = if result.is_ok() { "success" } else { "error" };

        INDEX_CREATION_LATENCY
            .with_label_values(&[metrics_tenant_name, transformation_key, status])
            .observe(duration);

        INDEX_OPERATION_COUNTER
            .with_label_values(&[
                metrics_tenant_name,
                transformation_key,
                "create_index",
                status,
            ])
            .inc();

        match result {
            Ok(assets) => {
                tracing::info!(
                    "Index created with {} embeddings ({} bytes) in {:.2}s",
                    num_embeddings,
                    embeddings_size_bytes,
                    duration
                );
                Ok(assets)
            }
            Err(err) => {
                tracing::warn!(
                    "Failed to create index with {} embeddings ({} bytes) in {:.2}s: {}",
                    num_embeddings,
                    embeddings_size_bytes,
                    duration,
                    err
                );
                Err(err)
            }
        }
    }

    async fn upload_assets(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_id: &str,
        transformation_key: &str,
        assets: Vec<ScannRsAsset>,
    ) -> Result<(), tonic::Status> {
        let start_time = Instant::now();

        // Increment operation counter
        INDEX_OPERATION_COUNTER
            .with_label_values(&[
                tenant_info.metrics_tenant_name(),
                transformation_key,
                "upload_assets",
                "started",
            ])
            .inc();

        let asset_data = assets
            .iter()
            .map(|asset| (asset.asset_name.clone(), asset.data.clone()))
            .collect::<Vec<_>>();

        // Calculate total asset size for logging
        let total_asset_size: usize = asset_data.iter().map(|(_, data)| data.len()).sum();

        let result = self
            .content_manager
            .upload_ann_index_assets(
                request_context,
                &tenant_info.tenant_id,
                transformation_key,
                checkpoint_id,
                &asset_data,
            )
            .await;

        // Record asset upload latency and status
        let duration = start_time.elapsed().as_secs_f64();
        let status = if result.is_ok() { "success" } else { "error" };

        INDEX_ASSET_UPLOAD_LATENCY
            .with_label_values(&[
                tenant_info.metrics_tenant_name(),
                transformation_key,
                status,
            ])
            .observe(duration);

        INDEX_OPERATION_COUNTER
            .with_label_values(&[
                tenant_info.metrics_tenant_name(),
                transformation_key,
                "upload_assets",
                status,
            ])
            .inc();

        match result {
            Ok(_) => {
                tracing::info!(
                    "Uploaded {} index assets ({} bytes) in {:.2}s",
                    asset_data.len(),
                    total_asset_size,
                    duration
                );
                Ok(())
            }
            Err(err) => {
                tracing::error!(
                    "Failed to upload {} index assets ({} bytes) in {:.2}s: {}",
                    asset_data.len(),
                    total_asset_size,
                    duration,
                    err
                );
                Err(err)
            }
        }
    }

    async fn upload_metadata(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_id: &str,
        transformation_key: &str,
        blob_infos: Vec<AnnIndexBlobInfoData>,
    ) -> tonic::Result<()> {
        let start_time = Instant::now();

        // Increment operation counter
        INDEX_OPERATION_COUNTER
            .with_label_values(&[
                tenant_info.metrics_tenant_name(),
                transformation_key,
                "upload_metadata",
                "started",
            ])
            .inc();

        let result = self
            .content_manager
            .upload_ann_index_blob_infos(
                request_context,
                &tenant_info.tenant_id,
                transformation_key,
                checkpoint_id,
                &blob_infos,
            )
            .await
            .map(|_| ());

        // Record metadata upload latency and status
        let duration = start_time.elapsed().as_secs_f64();
        let status = if result.is_ok() { "success" } else { "error" };

        INDEX_METADATA_UPLOAD_LATENCY
            .with_label_values(&[
                tenant_info.metrics_tenant_name(),
                transformation_key,
                status,
            ])
            .observe(duration);

        INDEX_OPERATION_COUNTER
            .with_label_values(&[
                tenant_info.metrics_tenant_name(),
                transformation_key,
                "upload_metadata",
                status,
            ])
            .inc();

        match &result {
            Ok(_) => {
                tracing::info!(
                    "Uploaded metadata for {} blobs in {:.2}s",
                    blob_infos.len(),
                    duration
                );
            }
            Err(err) => {
                tracing::error!(
                    "Failed to upload metadata for {} blobs in {:.2}s: {}",
                    blob_infos.len(),
                    duration,
                    err
                );
            }
        }

        result
    }

    async fn concat_embeddings(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_id: &str,
        mut receiver: Receiver<tonic::Result<ChunkResult>>,
    ) -> Result<(NumpyTensor<f16>, Vec<AnnIndexBlobInfoData>), tonic::Status> {
        let mut data = vec![];
        let mut shape = 0;
        let mut blob_infos = vec![];
        let mut missing_blobs = vec![];
        while let Some(r) = receiver.recv().await {
            match r {
                Ok(ChunkResult::Found(embeddings)) => {
                    tracing::debug!("Received embedding for {}", embeddings.key.blob_name);
                    // empty tensors are a possibility due to the way the chunker works, and also
                    // valid
                    if embeddings.tensor.size() != 0 {
                        if embeddings.tensor.shape.len() != 2 {
                            return Err(tonic::Status::invalid_argument(
                                "embeddings must be a matrix",
                            ));
                        }
                        if embeddings.tensor.shape[1] != EMBEDDING_DIMENSIONS {
                            return Err(tonic::Status::invalid_argument(format!(
                                "embeddings must have {} dimensions",
                                EMBEDDING_DIMENSIONS
                            )));
                        }
                    }
                    let chunk_shape = embeddings.tensor.shape[0];
                    shape += chunk_shape;
                    data.extend(embeddings.tensor.data.iter().cloned());
                    blob_infos.push(AnnIndexBlobInfoData {
                        blob_name: embeddings.key.blob_name.to_owned(),
                        chunk_count: u32::try_from(chunk_shape).unwrap(),
                    });
                    if shape > self.config.max_embeddings_to_index {
                        tracing::info!(
                            checkpoint_id = checkpoint_id,
                            transformation_key = embeddings.key.transformation_key,
                            embeddings = shape,
                            "Checkpoint {} has too many embeddings to index: {}",
                            checkpoint_id,
                            shape,
                        );
                        return Err(tonic::Status::invalid_argument(
                            "too many embeddings in checkpoint",
                        ));
                    }
                }
                Ok(ChunkResult::Missing(key)) => {
                    // record the missing embedding so we can look it up later to determine if it's
                    // pending indexing, or if it was never properly uploaded
                    tracing::warn!("Missing embeddings at key: {:?}", key);
                    missing_blobs.push(key);
                }
                Err(e) => {
                    tracing::warn!("Failed to get embedding: {}", e);
                    return Err(e);
                }
            }
        }

        let mut missing_blobs_count = 0;
        let mut waiting_on_blobs = false;
        for chunk in missing_blobs.chunks(BLOB_QUERY_CHUNK_SIZE) {
            let blob_content_keys = chunk
                .iter()
                .map(
                    |key| content_manager_rs_proto::content_manager::BlobContentKey {
                        blob_name: key.blob_name.to_string(),
                        transformation_key: "".into(),
                        sub_key: "".into(),
                    },
                )
                .collect_vec();
            let infos = self
                .content_manager
                .batch_get_blob_infos(request_context, &tenant_info.tenant_id, blob_content_keys)
                .await?;
            let (missing_blobs, pending_blobs): (Vec<_>, Vec<_>) =
                infos.blob_infos.into_iter().partition_map(|resp| {
                    let blob_name = resp.blob_content_key.unwrap().blob_name;
                    match resp.blob_info {
                        Some(_) => Either::Right(blob_name),
                        None => Either::Left(blob_name),
                    }
                });
            if !pending_blobs.is_empty() {
                waiting_on_blobs = true;
                tracing::info!(
                    "Found {} missing blobs that are waiting on embeddings: {:?}",
                    pending_blobs.len(),
                    pending_blobs[0],
                );
            }
            missing_blobs_count += missing_blobs.len();
            if !missing_blobs.is_empty() {
                tracing::info!(
                    "Found {} mising blobs: {:?}",
                    missing_blobs.len(),
                    missing_blobs[0],
                )
            }
        }
        if waiting_on_blobs {
            return Err(tonic::Status::not_found(
                "missing pending blobs in embedding",
            ));
        }
        if missing_blobs_count > self.config.max_missing_blobs {
            return Err(tonic::Status::not_found("too many missing blobs"));
        }
        tracing::info!("total num_embeddings={}", shape);
        Ok((
            NumpyTensor::new(data, vec![shape, EMBEDDING_DIMENSIONS], 1),
            blob_infos,
        ))
    }

    async fn get_embeddings(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_id: &str,
        transformation_key: &str,
    ) -> Result<(Vec<f32>, Vec<usize>, Vec<AnnIndexBlobInfoData>), tonic::Status> {
        let checkpoint_result = self
            .checkpoint_cache
            .get_checkpoint(request_context, tenant_info, checkpoint_id)
            .await?;

        if checkpoint_result.blobs.len() > self.config.max_blobs_to_index {
            tracing::error!(
                "Checkpoint {} has too many blobs to index: {} > {}",
                checkpoint_id,
                checkpoint_result.blobs.len(),
                self.config.max_blobs_to_index,
            );
            return Err(tonic::Status::invalid_argument(
                "too many blobs in checkpoint",
            ));
        }

        let chunk_keys = checkpoint_result
            .blobs
            .iter()
            .map(|blob_name| EmbeddingsKey {
                blob_name: blob_name.clone(),
                transformation_key: transformation_key.to_string(),
            })
            .collect::<Vec<_>>();

        let receiver = self
            .embeddings_cache
            .get_embeddings(request_context, tenant_info, chunk_keys.clone())
            .await;
        // This may end up filtering out embeddings that were in the checkpoint if e.g. a blob is
        // determined to be permanently missing, which will propagate to the rest of the indexing
        // pipeline (i.e. the embedding will then be missing from the index and ANN assets)
        let (embeddings, blob_infos) = self
            .concat_embeddings(request_context, tenant_info, checkpoint_id, receiver)
            .await?;

        let embeddings_f32_data: Vec<f32> = embeddings.data.iter().map(|x| f32::from(*x)).collect();
        Ok((embeddings_f32_data, embeddings.shape, blob_infos))
    }

    async fn check_for_existing_index(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_id: &str,
        transformation_key: &str,
    ) -> Option<String> {
        // We check blob_infos because it's the last content manager entry written by
        // create_and_upload_index below (after all the assets) and poses the least
        // risk of us ending up with a partially written index.
        let result = self
            .content_manager
            .get_ann_index_blob_infos(
                request_context,
                &tenant_info.tenant_id,
                transformation_key,
                // TODO this is a spot where checkpoint_id == new index_id is load bearing
                checkpoint_id,
            )
            .await;

        match result {
            Ok(_) => Some(checkpoint_id.to_string()),
            // Technically only NotFound actually means "no such index exists" here,
            // but proceeding is less harmful than crashing in other error cases.
            Err(_) => None,
        }
    }

    async fn create_and_upload_index(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_id: &str,
        transformation_key: &str,
    ) -> Result<String, tonic::Status> {
        tracing::info!(
            "Index creation requested for checkpoint_id={}, transformation_key={}",
            checkpoint_id,
            transformation_key
        );

        // To mitigate the impact of potential duplicated-request cases, check content manager
        // for an existing index before creating a new one.
        if let Some(index_id) = self
            .check_for_existing_index(
                request_context,
                tenant_info,
                checkpoint_id,
                transformation_key,
            )
            .await
        {
            tracing::warn!(
                "Index already exists for checkpoint_id={:?}, transformation_key={:?}",
                checkpoint_id,
                transformation_key
            );
            return Ok(index_id);
        }

        let (assets, blob_infos) = {
            tracing::info!(
                "Fetching embeddings for checkpoint_id={}, transformation_key={}",
                checkpoint_id,
                transformation_key
            );

            let (embeddings_f32_data, shape, blob_infos) = self
                .get_embeddings(
                    request_context,
                    tenant_info,
                    checkpoint_id,
                    transformation_key,
                )
                .await?;

            tracing::info!(
                "Embeddings memory usage: {} data, {} metadata",
                embeddings_f32_data.len() * std::mem::size_of::<f32>(),
                blob_infos.len() * std::mem::size_of::<AnnIndexBlobInfoData>(),
            );

            tracing::info!(
                "Creating index for checkpoint_id={}, transformation_key={}",
                checkpoint_id,
                transformation_key,
            );

            // Pass tenant name and transformation key to create_index for metrics
            let assets = self
                .create_index(
                    embeddings_f32_data,
                    shape,
                    tenant_info.metrics_tenant_name(),
                    transformation_key,
                )
                .await?;

            tracing::info!(
                "Assets memory usage: {:?}",
                assets.iter().map(|asset| asset.data.len()).collect_vec(),
            );

            (assets, blob_infos)
        };

        tracing::info!(
            "Uploading assets for checkpoint_id={}, transformation_key={}",
            checkpoint_id,
            transformation_key
        );

        self.upload_assets(
            request_context,
            tenant_info,
            checkpoint_id,
            transformation_key,
            assets,
        )
        .await?;

        tracing::info!(
            "Uploading metadata for checkpoint_id={}, transformation_key={}",
            checkpoint_id,
            transformation_key
        );

        self.upload_metadata(
            request_context,
            tenant_info,
            checkpoint_id,
            transformation_key,
            blob_infos,
        )
        .await?;

        tracing::info!(
            "Index creation complete for checkpoint_id={}, transformation_key={}",
            checkpoint_id,
            transformation_key
        );

        Ok(checkpoint_id.to_string())
    }
}

/// Implementation of the IndexFactory trait for IndexFactoryImpl.
///
/// This implementation provides the main entry point for index creation,
/// handling metrics tracking, logging, and error handling around the core
/// index creation process.
#[async_trait]
impl IndexFactory for IndexFactoryImpl {
    /// Creates an index for the given checkpoint and transformation key.
    ///
    /// This method orchestrates the entire index creation process:
    /// 1. Retrieves checkpoint data and embeddings
    /// 2. Processes embeddings to create a ScaNN index
    /// 3. Uploads the index assets and metadata
    /// 4. Returns the index ID
    ///
    /// It also handles metrics tracking and logging throughout the process.
    async fn create_index(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_id: &str,
        transformation_key: &str,
    ) -> Result<String, tonic::Status> {
        log_response_fn(
            || async {
                let overall_start_time = Instant::now();

                // Increment operation counter for overall index creation
                INDEX_OPERATION_COUNTER
                    .with_label_values(&[
                        tenant_info.metrics_tenant_name(),
                        transformation_key,
                        "create_index_overall",
                        "started",
                    ])
                    .inc();

                tracing::info!(
                    "create_index request: checkpoint_id={:?}, transformation_key={:?}",
                    checkpoint_id,
                    transformation_key
                );

                // Use a result variable to track success/failure
                let result = self
                    .create_and_upload_index(
                        request_context,
                        tenant_info,
                        checkpoint_id,
                        transformation_key,
                    )
                    .await;

                // Record overall metrics
                let overall_duration = overall_start_time.elapsed().as_secs_f64();
                let status = if result.is_ok() { "success" } else { "error" };

                INDEX_OPERATION_COUNTER
                    .with_label_values(&[
                        tenant_info.metrics_tenant_name(),
                        transformation_key,
                        "create_index_overall",
                        status,
                    ])
                    .inc();

                match &result {
                    Ok(_) => {
                        tracing::info!(
                            "Index creation completed for checkpoint_id={:?}, transformation_key={:?} in {:.2}s",
                            checkpoint_id,
                            transformation_key,
                            overall_duration
                        );
                    }
                    Err(err) => {
                        tracing::warn!(
                            "Index creation failed for checkpoint_id={:?}, transformation_key={:?} in {:.2}s: {}",
                            checkpoint_id,
                            transformation_key,
                            overall_duration,
                            err
                        );
                    }
                }

                result
            },
            "create_index",
        )
        .await
    }
}

#[cfg(test)]
mod tests {
    use crate::checkpoint_cache::CheckpointEntry;
    use crate::embeddings_cache::ChunkEntry;

    use super::*;
    use async_trait::async_trait;
    use blob_names::BlobName;
    use content_manager_client::{
        AnnIndexBlobInfoData, BlobScope, ContentManagerClient, GetAnnIndexBlobInfosResult,
        GetBestAnnIndexResult, UploadContent,
    };
    use content_manager_rs_proto::content_manager::{
        AddAnnIndexMappingResponse, BatchBlobInfoResponse, BatchGetBlobInfoResponse,
        BatchGetContentResponse, BlobContentKey, GetAllBlobsFromCheckpointResponse,
        GetBlobInfoResponse, UploadAnnIndexAssetsResponse, UploadAnnIndexBlobInfosResponse,
    };
    use half::f16;
    use numpy::NumpyTensor;
    use prost_wkt_types::Timestamp;
    use request_context::{RequestContext, TenantId, TenantInfo};
    use secrecy::{ExposeSecret, SecretString, SecretVec};
    use std::collections::HashMap;
    use std::sync::{Arc, Mutex};
    use std::time::Instant;
    use tokio::sync::mpsc;

    // Mock ContentManagerClient implementation
    struct MockContentManagerClient {
        // Track calls to methods
        upload_blob_content_calls: Mutex<Vec<(String, Vec<u8>)>>,
        batch_upload_blob_content_calls: Mutex<Vec<Vec<UploadContent>>>,
        find_missing_blobs_calls: Mutex<Vec<Vec<String>>>,
        checkpoint_blobs_calls: Mutex<Vec<blob_names_rs_proto::base::blob_names::Blobs>>,
    }

    impl MockContentManagerClient {
        fn new() -> Self {
            Self {
                upload_blob_content_calls: Mutex::new(Vec::new()),
                batch_upload_blob_content_calls: Mutex::new(Vec::new()),
                find_missing_blobs_calls: Mutex::new(Vec::new()),
                checkpoint_blobs_calls: Mutex::new(Vec::new()),
            }
        }
    }

    #[async_trait]
    impl ContentManagerClient for MockContentManagerClient {
        async fn upload_blob_content(
            &self,
            _request_context: &RequestContext,
            path: &SecretString,
            content: &SecretVec<u8>,
            _deadline: Option<Instant>,
        ) -> Result<String, tonic::Status> {
            // Record the call
            self.upload_blob_content_calls.lock().unwrap().push((
                path.expose_secret().to_string(),
                content.expose_secret().to_vec(),
            ));

            // Return a mock blob name
            Ok("mock_blob_name".to_string())
        }

        async fn batch_upload_blob_content(
            &self,
            _request_context: &RequestContext,
            blobs: Vec<UploadContent>,
            _deadline: Option<Instant>,
        ) -> Result<Vec<BlobName>, tonic::Status> {
            // Record the call
            self.batch_upload_blob_content_calls
                .lock()
                .unwrap()
                .push(blobs);

            // Return mock blob names
            let blob_name1 = BlobName::try_from(
                "0000000000000000000000000000000000000000000000000000000000000001",
            )
            .unwrap();
            let blob_name2 = BlobName::try_from(
                "0000000000000000000000000000000000000000000000000000000000000002",
            )
            .unwrap();
            Ok(vec![blob_name1, blob_name2])
        }

        async fn find_missing_blobs(
            &self,
            _request_context: &RequestContext,
            blob_names: &[String],
            _deadline: Option<Instant>,
        ) -> Result<Vec<String>, tonic::Status> {
            // Record the call
            self.find_missing_blobs_calls
                .lock()
                .unwrap()
                .push(blob_names.to_vec());

            // Return no missing blobs
            Ok(Vec::new())
        }

        async fn checkpoint_blobs(
            &self,
            _request_context: &RequestContext,
            blobs: &blob_names_rs_proto::base::blob_names::Blobs,
            _deadline: Option<Instant>,
        ) -> Result<String, tonic::Status> {
            // Record the call
            self.checkpoint_blobs_calls
                .lock()
                .unwrap()
                .push(blobs.clone());

            // Return a mock checkpoint ID
            Ok("mock_checkpoint_id".to_string())
        }

        async fn get_blob_info(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _blob_content_key: BlobContentKey,
        ) -> tonic::Result<GetBlobInfoResponse> {
            Ok(GetBlobInfoResponse {
                content_hash: "mock_hash".into(),
                size: 10,
                metadata: vec![],
                informed_transformation_keys: vec![],
                uploaded_transformation_keys: vec![],
                time: Some(Timestamp {
                    seconds: 0,
                    nanos: 0,
                }),
            })
        }

        async fn batch_get_blob_infos(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            blob_content_keys: Vec<BlobContentKey>,
        ) -> tonic::Result<BatchGetBlobInfoResponse> {
            let blob_infos = blob_content_keys
                .into_iter()
                .map(|blob_content_key| BatchBlobInfoResponse {
                    blob_content_key: Some(blob_content_key),
                    blob_info: Some(GetBlobInfoResponse {
                        content_hash: "mock_hash".into(),
                        size: 10,
                        metadata: vec![],
                        informed_transformation_keys: vec![],
                        uploaded_transformation_keys: vec![],
                        time: Some(Timestamp {
                            seconds: 0,
                            nanos: 0,
                        }),
                    }),
                })
                .collect_vec();
            Ok(BatchGetBlobInfoResponse { blob_infos })
        }

        async fn get_content(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _blob_scope: &BlobScope,
            _blob_names: &[BlobName],
            _deadline: Option<Instant>,
        ) -> mpsc::Receiver<tonic::Result<BatchGetContentResponse>> {
            // Create a channel to return mock responses
            let (tx, rx) = mpsc::channel(1);

            // We don't need to send anything for the tests
            drop(tx);

            rx
        }

        async fn get_content_multiple_scopes(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _blobs: Vec<(BlobName, BlobScope)>,
            _deadline: Option<Instant>,
        ) -> mpsc::Receiver<tonic::Result<BatchGetContentResponse>> {
            // Create a channel to return mock responses
            let (tx, rx) = mpsc::channel(1);

            // We don't need to send anything for the tests
            drop(tx);

            rx
        }

        async fn get_checkpoint_blob_names(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _checkpoint_id: &str,
        ) -> mpsc::Receiver<tonic::Result<GetAllBlobsFromCheckpointResponse>> {
            // Create a channel to return mock responses
            let (tx, rx) = mpsc::channel(1);

            // We don't need to send anything for the tests
            drop(tx);

            rx
        }

        async fn get_best_ann_index(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _checkpoint_id: &str,
        ) -> tonic::Result<GetBestAnnIndexResult> {
            Ok(GetBestAnnIndexResult {
                index_id: String::from("badcafe"),
                added_blobs: vec![],
                removed_blobs: vec![],
            })
        }

        async fn get_ann_index_blob_infos(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _index_id: &str,
        ) -> tonic::Result<GetAnnIndexBlobInfosResult> {
            Ok(GetAnnIndexBlobInfosResult { infos: vec![] })
        }

        async fn get_ann_index_asset(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _index_id: &str,
            _sub_key: &str,
        ) -> tonic::Result<Vec<u8>> {
            return Ok(b"foobar".to_vec());
        }

        async fn add_ann_index_mapping(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _checkpoint_id: &str,
            _index_id: &str,
            _added_blobs: &[BlobName],
            _removed_blobs: &[BlobName],
        ) -> tonic::Result<AddAnnIndexMappingResponse> {
            return Ok(AddAnnIndexMappingResponse {});
        }

        async fn upload_ann_index_blob_infos(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _index_id: &str,
            _embeddings: &[AnnIndexBlobInfoData],
        ) -> tonic::Result<UploadAnnIndexBlobInfosResponse> {
            Ok(UploadAnnIndexBlobInfosResponse {})
        }

        async fn upload_ann_index_assets(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _index_id: &str,
            _assets: &[(String, Vec<u8>)],
        ) -> tonic::Result<UploadAnnIndexAssetsResponse> {
            return Ok(UploadAnnIndexAssetsResponse {});
        }
    }

    // Mock CheckpointCache implementation
    struct MockCheckpointCache {
        // Map of checkpoint_id to list of blob names
        checkpoints: Mutex<HashMap<String, Vec<BlobName>>>,
    }

    impl MockCheckpointCache {
        fn new() -> Self {
            let mut checkpoints = HashMap::new();
            let blob1 = BlobName::try_from(
                "0000000000000000000000000000000000000000000000000000000000000001",
            )
            .unwrap();
            let blob2 = BlobName::try_from(
                "0000000000000000000000000000000000000000000000000000000000000002",
            )
            .unwrap();
            checkpoints.insert("test-checkpoint".to_string(), vec![blob1, blob2]);
            Self {
                checkpoints: Mutex::new(checkpoints),
            }
        }
    }

    #[async_trait]
    impl CheckpointCache for MockCheckpointCache {
        async fn get_checkpoint(
            &self,
            _request_context: &RequestContext,
            _tenant_info: &TenantInfo,
            checkpoint_id: &str,
        ) -> Result<Arc<CheckpointEntry>, tonic::Status> {
            let checkpoints = self.checkpoints.lock().unwrap();
            if let Some(blobs) = checkpoints.get(checkpoint_id) {
                Ok(Arc::new(CheckpointEntry {
                    checkpoint_key: checkpoint_id.to_string(),
                    blobs: blobs.clone(),
                }))
            } else {
                Err(tonic::Status::not_found(format!(
                    "Checkpoint not found: {}",
                    checkpoint_id
                )))
            }
        }
    }

    // Mock EmbeddingsCache implementation
    struct MockEmbeddingsCache {
        // Map of (blob_name, transformation_key) to embeddings
        embeddings: Mutex<HashMap<(BlobName, String), NumpyTensor<f16>>>,
    }

    impl MockEmbeddingsCache {
        fn new() -> Self {
            let mut embeddings = HashMap::new();

            // Create mock embeddings for blob1 with test-transformation
            let blob1 = BlobName::try_from(
                "0000000000000000000000000000000000000000000000000000000000000001",
            )
            .unwrap();
            let blob1_embeddings = create_mock_embeddings(2, EMBEDDING_DIMENSIONS);
            embeddings.insert((blob1, "test-transformation".to_string()), blob1_embeddings);

            // Create mock embeddings for blob2 with test-transformation
            let blob2 = BlobName::try_from(
                "0000000000000000000000000000000000000000000000000000000000000002",
            )
            .unwrap();
            let blob2_embeddings = create_mock_embeddings(3, EMBEDDING_DIMENSIONS);
            embeddings.insert((blob2, "test-transformation".to_string()), blob2_embeddings);

            Self {
                embeddings: Mutex::new(embeddings),
            }
        }
    }

    // Helper function to create mock embeddings
    fn create_mock_embeddings(rows: usize, cols: usize) -> NumpyTensor<f16> {
        let mut data = Vec::with_capacity(rows * cols);
        for i in 0..(rows * cols) {
            data.push(f16::from_f32(i as f32 / (rows * cols) as f32));
        }
        NumpyTensor::new(data, vec![rows, cols], 1)
    }

    #[async_trait]
    impl EmbeddingsCache for MockEmbeddingsCache {
        async fn get_embeddings(
            &self,
            _request_context: &RequestContext,
            _tenant_info: &TenantInfo,
            keys: Vec<EmbeddingsKey>,
        ) -> mpsc::Receiver<tonic::Result<ChunkResult>> {
            let (tx, rx) = mpsc::channel(keys.len());
            let embeddings_map = self.embeddings.lock().unwrap().clone();

            tokio::spawn(async move {
                for key in keys {
                    let result = match embeddings_map
                        .get(&(key.blob_name.clone(), key.transformation_key.clone()))
                    {
                        Some(tensor) => {
                            let chunk_entry = ChunkEntry {
                                key: key.clone(),
                                tensor: tensor.clone(),
                            };
                            let chunk_result = Arc::new(chunk_entry);
                            Ok(ChunkResult::Found(chunk_result))
                        }
                        None => Ok(ChunkResult::Missing(EmbeddingsKey {
                            blob_name: key.blob_name,
                            transformation_key: key.transformation_key,
                        })),
                    };

                    if tx.send(result).await.is_err() {
                        break;
                    }
                }
            });

            rx
        }
    }

    // We can't easily test the create_index method directly because it requires
    // a lot of setup and mocking. Instead, we'll test the individual components.

    #[tokio::test]
    async fn test_concat_embeddings() {
        // Create mock dependencies
        let content_manager = Arc::new(MockContentManagerClient::new());
        let checkpoint_cache = Arc::new(MockCheckpointCache::new());
        let embeddings_cache = Arc::new(MockEmbeddingsCache::new());

        // Create the index factory
        let factory = IndexFactoryImpl::new(
            FactoryConfig::default(),
            content_manager.clone(),
            checkpoint_cache,
            embeddings_cache.clone(),
        )
        .unwrap();

        // Create a request context and tenant info for testing
        let request_context = RequestContext::new_for_test();
        let tenant_info = TenantInfo::new_for_test();

        // Create keys for the embeddings
        let blob1 =
            BlobName::try_from("0000000000000000000000000000000000000000000000000000000000000001")
                .unwrap();
        let blob1_str = blob1.to_string();
        let blob2 =
            BlobName::try_from("0000000000000000000000000000000000000000000000000000000000000002")
                .unwrap();
        let blob2_str = blob2.to_string();
        let keys = vec![
            EmbeddingsKey {
                blob_name: blob1,
                transformation_key: "test-transformation".to_string(),
            },
            EmbeddingsKey {
                blob_name: blob2,
                transformation_key: "test-transformation".to_string(),
            },
        ];

        // Get the embeddings
        let receiver = embeddings_cache
            .get_embeddings(&request_context, &tenant_info, keys)
            .await;

        // Call concat_embeddings
        let result = factory
            .concat_embeddings(&request_context, &tenant_info, "", receiver)
            .await;

        // Verify the result
        assert!(result.is_ok(), "concat_embeddings should succeed");
        let (tensor, blob_infos) = result.unwrap();

        // The concatenated tensor should have 5 rows (2 from blob1 + 3 from blob2)
        assert_eq!(tensor.shape, vec![5, EMBEDDING_DIMENSIONS]);
        blob_infos
            .into_iter()
            .for_each(|info| match info.blob_name.to_string() {
                s if s == blob1_str => assert_eq!(info.chunk_count, 2),
                s if s == blob2_str => assert_eq!(info.chunk_count, 3),
                s => unreachable!("unexpected blob value: {:?}", s),
            });
    }

    #[tokio::test]
    async fn test_missing_embeddings() {
        // Create a mock EmbeddingsCache that returns None for some embeddings
        struct MockEmptyEmbeddingsCache;

        #[async_trait]
        impl EmbeddingsCache for MockEmptyEmbeddingsCache {
            async fn get_embeddings(
                &self,
                _request_context: &RequestContext,
                _tenant_info: &TenantInfo,
                keys: Vec<EmbeddingsKey>,
            ) -> mpsc::Receiver<tonic::Result<ChunkResult>> {
                let (tx, rx) = mpsc::channel(keys.len());

                tokio::spawn(async move {
                    for key in keys {
                        // Return Missing for all embeddings which won't allow the index to be
                        // created if the embeddings are still "pending"
                        if tx.send(Ok(ChunkResult::Missing(key))).await.is_err() {
                            break;
                        }
                    }
                });

                rx
            }
        }

        // Create mock dependencies
        let content_manager = Arc::new(MockContentManagerClient::new());
        let checkpoint_cache = Arc::new(MockCheckpointCache::new());
        let embeddings_cache = Arc::new(MockEmptyEmbeddingsCache);

        // Create the index factory
        let factory = IndexFactoryImpl::new(
            FactoryConfig::default(),
            content_manager.clone(),
            checkpoint_cache,
            embeddings_cache.clone(),
        )
        .unwrap();

        // Create a request context and tenant info for testing
        let request_context = RequestContext::new_for_test();
        let tenant_info = TenantInfo::new_for_test();

        // Create keys for the embeddings
        let blob1 =
            BlobName::try_from("0000000000000000000000000000000000000000000000000000000000000001")
                .unwrap();
        let keys = vec![EmbeddingsKey {
            blob_name: blob1,
            transformation_key: "test-transformation".to_string(),
        }];

        // Get the embeddings
        let receiver = embeddings_cache
            .get_embeddings(&request_context, &tenant_info, keys)
            .await;

        // Call concat_embeddings
        let result = factory
            .concat_embeddings(&request_context, &tenant_info, "", receiver)
            .await;

        // Verify the result
        assert!(
            result.is_err(),
            "concat_embeddings should fail with missing embeddings"
        );
        // Check the error code
        if let Err(err) = result {
            assert_eq!(
                err.code(),
                tonic::Code::NotFound,
                "Error should be NotFound"
            );
        }
    }

    #[tokio::test]
    async fn test_invalid_embedding_dimensions() {
        // Create a mock EmbeddingsCache that returns embeddings with wrong dimensions
        struct MockInvalidEmbeddingsCache;

        #[async_trait]
        impl EmbeddingsCache for MockInvalidEmbeddingsCache {
            async fn get_embeddings(
                &self,
                _request_context: &RequestContext,
                _tenant_info: &TenantInfo,
                keys: Vec<EmbeddingsKey>,
            ) -> mpsc::Receiver<tonic::Result<ChunkResult>> {
                let (tx, rx) = mpsc::channel(keys.len());

                tokio::spawn(async move {
                    for key in keys {
                        // Create embeddings with wrong dimensions (256 instead of EMBEDDING_DIMENSIONS)
                        let tensor = create_mock_embeddings(2, 256);
                        let chunk_result = Arc::new(ChunkEntry {
                            key: key.clone(),
                            tensor,
                        });

                        if tx.send(Ok(ChunkResult::Found(chunk_result))).await.is_err() {
                            break;
                        }
                    }
                });

                rx
            }
        }

        // Create mock dependencies
        let content_manager = Arc::new(MockContentManagerClient::new());
        let checkpoint_cache = Arc::new(MockCheckpointCache::new());
        let embeddings_cache = Arc::new(MockInvalidEmbeddingsCache);

        // Create the index factory
        let factory = IndexFactoryImpl::new(
            FactoryConfig::default(),
            content_manager.clone(),
            checkpoint_cache,
            embeddings_cache.clone(),
        )
        .unwrap();

        // Create a request context and tenant info for testing
        let request_context = RequestContext::new_for_test();
        let tenant_info = TenantInfo::new_for_test();

        // Create keys for the embeddings
        let blob1 =
            BlobName::try_from("0000000000000000000000000000000000000000000000000000000000000001")
                .unwrap();
        let keys = vec![EmbeddingsKey {
            blob_name: blob1,
            transformation_key: "test-transformation".to_string(),
        }];

        // Get the embeddings
        let receiver = embeddings_cache
            .get_embeddings(&request_context, &tenant_info, keys)
            .await;

        // Call concat_embeddings
        let result = factory
            .concat_embeddings(&request_context, &tenant_info, "", receiver)
            .await;

        // Verify the result
        assert!(
            result.is_err(),
            "concat_embeddings should fail with invalid dimensions"
        );
        // Check the error code
        if let Err(err) = result {
            assert_eq!(
                err.code(),
                tonic::Code::InvalidArgument,
                "Error should be InvalidArgument"
            );
        }
    }
}
