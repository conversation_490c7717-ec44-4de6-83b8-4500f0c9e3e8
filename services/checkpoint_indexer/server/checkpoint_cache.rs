use std::sync::Arc;
use std::time::Duration;
use std::time::Instant;

use crate::config::Config;
use crate::metrics::{
    CacheType, CACHE_BYTES_COUNT, CACHE_ENTRY_COUNT, CACHE_EVICTION_COUNT_COLLECTOR,
    CACHE_LOOKUP_COUNT_COLLECTOR, CHECKPOINT_GET_BLOBS_COUNTER, CHECKPOINT_GET_COUNTER,
    CHECKPOINT_GET_LATENCY_COLLECTOR,
};
use async_trait::async_trait;
use blob_names::BlobName;
use content_manager_client::ContentManagerClient;
use moka::future::Cache;
use request_context::{RequestContext, TenantId, TenantInfo, EMPTY_TENANT_ID};

#[derive(Clone)]
pub struct CheckpointEntry {
    pub checkpoint_key: String,
    pub blobs: Vec<BlobName>,
}

type CheckpointResult = Arc<CheckpointEntry>;

/// checkpoint caching and lookup
#[async_trait]
pub trait CheckpointCache {
    // returns a set of blob names for the given checkpoint
    async fn get_checkpoint(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_key: &str,
    ) -> tonic::Result<CheckpointResult>;
}

pub struct CheckpointCacheImpl {
    data: Arc<CheckpointCacheDataImpl>,
}
impl CheckpointCacheImpl {
    pub fn new(
        config: Config,
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    ) -> Self {
        Self {
            data: Arc::new(CheckpointCacheDataImpl::new(config, content_manager)),
        }
    }
}

#[async_trait]
impl CheckpointCache for CheckpointCacheImpl {
    async fn get_checkpoint(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_key: &str,
    ) -> tonic::Result<CheckpointResult> {
        if let Some(result) = self
            .data
            .read_from_cache(
                tenant_info.tenant_id.as_ref().unwrap_or(&EMPTY_TENANT_ID),
                checkpoint_key,
            )
            .await
        {
            CACHE_LOOKUP_COUNT_COLLECTOR
                .with_label_values(&[
                    CacheType::Checkpoint.as_str(),
                    "hit",
                    tenant_info.metrics_tenant_name(),
                ])
                .inc();
            return Ok(result);
        } else {
            CACHE_LOOKUP_COUNT_COLLECTOR
                .with_label_values(&[
                    CacheType::Checkpoint.as_str(),
                    "miss",
                    tenant_info.metrics_tenant_name(),
                ])
                .inc();
        }

        CACHE_ENTRY_COUNT
            .with_label_values(&[CacheType::Checkpoint.as_str()])
            .set(self.data.cache.entry_count() as f64);
        CACHE_BYTES_COUNT
            .with_label_values(&[CacheType::Checkpoint.as_str()])
            .set(self.data.cache.weighted_size() as f64);

        self.data
            .read_from_content_manager(request_context, tenant_info, checkpoint_key)
            .await
    }
}

struct CheckpointCacheDataImpl {
    pub content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    pub cache: Cache<(TenantId, String), Arc<CheckpointEntry>>,
}

impl CheckpointCacheDataImpl {
    pub fn new(
        config: Config,
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    ) -> Self {
        let cache = Cache::builder()
            .max_capacity(config.checkpoint_cache_size_bytes)
            .eviction_policy(moka::policy::EvictionPolicy::tiny_lfu())
            // A BlobName is 64 bytes hex-encoded. Add an estimate of the key size
            .weigher(|_, v: &Arc<CheckpointEntry>| (v.as_ref().blobs.len() as u32) * 64 + 128)
            .time_to_idle(Duration::from_secs(config.cache_tti_seconds))
            .eviction_listener(|_, _, c| {
                let removal_cause = format!("{:?}", c);
                CACHE_EVICTION_COUNT_COLLECTOR
                    .with_label_values(&[CacheType::Checkpoint.as_str(), &removal_cause])
                    .inc();
            })
            .build();
        CheckpointCacheDataImpl {
            content_manager,
            cache,
        }
    }

    pub async fn read_from_cache(
        &self,
        tenant_id: &TenantId,
        checkpoint_key: &str,
    ) -> Option<Arc<CheckpointEntry>> {
        let cache_key = (tenant_id.clone(), checkpoint_key.to_string());
        self.cache.get(&cache_key).await
    }

    pub async fn read_from_content_manager(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_key: &str,
    ) -> tonic::Result<CheckpointResult> {
        let start = Instant::now();
        let mut response = self
            .content_manager
            .get_checkpoint_blob_names(request_context, &tenant_info.tenant_id, checkpoint_key)
            .await;
        let mut blobs: Vec<BlobName> = Vec::new();
        while let Some(message) = response.recv().await {
            for blob_name in message?.blob_names.iter() {
                blobs.push(BlobName::from_bytes(blob_name)?);
            }
        }
        CHECKPOINT_GET_COUNTER
            .with_label_values(&[tenant_info.metrics_tenant_name()])
            .inc();
        CHECKPOINT_GET_BLOBS_COUNTER
            .with_label_values(&[tenant_info.metrics_tenant_name()])
            .inc_by(blobs.len() as u64);
        CHECKPOINT_GET_LATENCY_COLLECTOR
            .with_label_values(&[tenant_info.metrics_tenant_name()])
            .observe(start.elapsed().as_secs_f64());
        blobs.sort();
        let entry = Arc::new(CheckpointEntry {
            checkpoint_key: checkpoint_key.to_string(),
            blobs,
        });
        let cache_key = (
            tenant_info.tenant_id.clone().unwrap_or(EMPTY_TENANT_ID),
            entry.checkpoint_key.clone(),
        );
        self.cache.insert(cache_key, entry.clone()).await;
        Ok(entry)
    }
}

#[cfg(test)]
mod tests {
    use crate::config;
    use crate::test::get_test_data_path;
    use async_trait::async_trait;
    use content_manager_client::{
        AnnIndexBlobInfoData, BlobScope, GetAnnIndexBlobInfosResult, GetBestAnnIndexResult,
        UploadContent,
    };
    use content_manager_rs_proto::content_manager::{
        self, AddAnnIndexMappingResponse, BatchBlobInfoResponse, BatchGetBlobInfoResponse,
        BlobContentKey, GetBlobInfoResponse, UploadAnnIndexAssetsResponse,
        UploadAnnIndexBlobInfosResponse,
    };
    use futures::StreamExt;
    use itertools::Itertools;
    use prost_wkt_types::Timestamp;
    use secrecy::{SecretString, SecretVec};
    use std::collections::{HashMap, HashSet};
    use std::time::Duration;
    use tokio::sync::mpsc;
    use tokio::sync::mpsc::Receiver;

    use super::*;

    pub struct MockGetContentRequest {
        pub request_id: request_context::RequestId,
        pub blobs: Vec<(BlobName, BlobScope)>,
    }

    pub struct MockContentManagerClient {
        pub last_get_content_request: std::sync::Mutex<Option<MockGetContentRequest>>,
        pub requested_blob_counter: std::sync::atomic::AtomicUsize,
        pub last_checkpoint_blob_names_request:
            std::sync::Mutex<Option<request_context::RequestId>>,
        pub blobs: HashSet<String>,
        // A subset of blobs
        pub blobs_missing_chunks: HashSet<String>,
        pub checkpoints: HashMap<String, Vec<String>>,
        pub content: Vec<u8>,
        pub get_content_lock: tokio::sync::RwLock<()>,
    }

    impl MockContentManagerClient {
        pub fn new(
            blobs: HashSet<String>,
            checkpoints: HashMap<String, Vec<String>>,
            filename: &str,
        ) -> MockContentManagerClient {
            MockContentManagerClient {
                last_get_content_request: std::sync::Mutex::new(None),
                requested_blob_counter: std::sync::atomic::AtomicUsize::new(0),
                last_checkpoint_blob_names_request: std::sync::Mutex::new(None),
                blobs_missing_chunks: HashSet::new(),
                blobs,
                checkpoints,
                // Simulate blob content (all with the same npy data)
                content: std::fs::read(get_test_data_path(filename)).unwrap(),
                get_content_lock: tokio::sync::RwLock::new(()),
            }
        }
    }

    #[async_trait]
    impl ContentManagerClient for MockContentManagerClient {
        async fn upload_blob_content(
            &self,
            _request_context: &RequestContext,
            _path: &SecretString,
            _content: &SecretVec<u8>,
            _deadline: Option<Instant>,
        ) -> Result<String, tonic::Status> {
            Err(tonic::Status::unimplemented("not implemented"))
        }

        async fn batch_upload_blob_content(
            &self,
            _request_context: &RequestContext,
            _blobs: Vec<UploadContent>,
            _deadline: Option<Instant>,
        ) -> Result<Vec<BlobName>, tonic::Status> {
            Err(tonic::Status::unimplemented("not implemented"))
        }

        async fn find_missing_blobs(
            &self,
            _request_context: &RequestContext,
            _blob_names: &[String],
            _deadline: Option<Instant>,
        ) -> Result<Vec<String>, tonic::Status> {
            Err(tonic::Status::unimplemented("not implemented"))
        }

        async fn checkpoint_blobs(
            &self,
            _request_context: &RequestContext,
            _blobs: &blob_names_rs_proto::base::blob_names::Blobs,
            _deadline: Option<Instant>,
        ) -> Result<String, tonic::Status> {
            Err(tonic::Status::unimplemented("not implemented"))
        }

        async fn get_blob_info(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _blob_content_key: BlobContentKey,
        ) -> tonic::Result<GetBlobInfoResponse> {
            Ok(GetBlobInfoResponse {
                content_hash: "mock_hash".into(),
                size: 10,
                metadata: vec![],
                informed_transformation_keys: vec![],
                uploaded_transformation_keys: vec![],
                time: Some(Timestamp {
                    seconds: 0,
                    nanos: 0,
                }),
            })
        }

        async fn batch_get_blob_infos(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            blob_content_keys: Vec<BlobContentKey>,
        ) -> tonic::Result<BatchGetBlobInfoResponse> {
            let blob_infos = blob_content_keys
                .into_iter()
                .map(|blob_content_key| BatchBlobInfoResponse {
                    blob_content_key: Some(blob_content_key),
                    blob_info: Some(GetBlobInfoResponse {
                        content_hash: "mock_hash".into(),
                        size: 10,
                        metadata: vec![],
                        informed_transformation_keys: vec![],
                        uploaded_transformation_keys: vec![],
                        time: Some(Timestamp {
                            seconds: 0,
                            nanos: 0,
                        }),
                    }),
                })
                .collect_vec();
            Ok(BatchGetBlobInfoResponse { blob_infos })
        }

        async fn get_content(
            &self,
            request_context: &RequestContext,
            tenant_id: &Option<TenantId>,
            blob_scope: &BlobScope,
            blob_names: &[BlobName],
            deadline: Option<Instant>,
        ) -> Receiver<tonic::Result<content_manager::BatchGetContentResponse>> {
            let _guard = self.get_content_lock.read().await;
            self.requested_blob_counter
                .fetch_add(blob_names.len(), std::sync::atomic::Ordering::Relaxed);

            self.get_content_multiple_scopes(
                request_context,
                tenant_id,
                blob_names
                    .iter()
                    .map(|b| (b.clone(), blob_scope.clone()))
                    .collect(),
                deadline,
            )
            .await
        }

        async fn get_content_multiple_scopes(
            &self,
            request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            blobs: Vec<(BlobName, BlobScope)>,
            deadline: Option<Instant>,
        ) -> Receiver<tonic::Result<content_manager::BatchGetContentResponse>> {
            let _guard = self.get_content_lock.read().await;
            self.requested_blob_counter
                .fetch_add(blobs.len(), std::sync::atomic::Ordering::Relaxed);

            {
                let mut d = self.last_get_content_request.lock().unwrap();
                *d = Some(MockGetContentRequest {
                    request_id: request_context.request_id(),
                    blobs: blobs.clone(),
                });
            }

            let (tx, rx) =
                mpsc::channel::<tonic::Result<content_manager::BatchGetContentResponse>>(1024);

            for (blob_name, blob_scope) in blobs {
                // Simulate cancellation
                if let Some(d) = deadline {
                    if Instant::now() > d {
                        let _ = tx.send(Err(tonic::Status::cancelled("cancelled"))).await;
                    }
                }

                // Simulate missing blob keys, or a blob that has embeddings but no chunks
                if !self.blobs.contains(&blob_name.to_string())
                    || (blob_scope.sub_key == "embeddings.npy"
                        && self.blobs_missing_chunks.contains(&blob_name.to_string()))
                {
                    let _ = tx.send(Ok(content_manager::BatchGetContentResponse {
                            response: Some(
                                content_manager::batch_get_content_response::Response::NotFoundContent(
                                    content_manager::BatchGetContentNotFound {
                                        blob_name: blob_name.to_string(),
                                        transformation_key: blob_scope.transformation_key.clone(),
                                        sub_key: blob_scope.sub_key.clone(),
                                    },
                                ),
                            ),
                        })).await;
                    continue;
                }

                let _ = tx
                    .send(Ok(content_manager::BatchGetContentResponse {
                        response: Some(
                            content_manager::batch_get_content_response::Response::FinalContent(
                                content_manager::BatchGetContentFinalContent {
                                    blob_name: blob_name.to_string(),
                                    transformation_key: blob_scope.transformation_key.clone(),
                                    sub_key: blob_scope.sub_key.clone(),
                                    content: self.content.clone(),
                                    final_hash: sha256::digest(&self.content),
                                    metadata: vec![content_manager::BlobMetadata {
                                        key: "path".to_string(),
                                        value: "services/to/blob".to_string(),
                                    }],
                                },
                            ),
                        ),
                    }))
                    .await;
            }
            rx
        }

        async fn get_checkpoint_blob_names(
            &self,
            request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            checkpoint_id: &str,
        ) -> Receiver<tonic::Result<content_manager::GetAllBlobsFromCheckpointResponse>> {
            {
                let mut d = self.last_checkpoint_blob_names_request.lock().unwrap();
                *d = Some(request_context.request_id());
            }
            let blobs: Vec<tonic::Result<content_manager::GetAllBlobsFromCheckpointResponse>> =
                if let Some(blobs) = self.checkpoints.get(checkpoint_id) {
                    vec![Ok(content_manager::GetAllBlobsFromCheckpointResponse {
                        blob_names: blobs
                            .iter()
                            .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                            .collect(),
                    })]
                } else {
                    vec![Err(tonic::Status::not_found("Checkpoint does not exist"))]
                };
            let mut stream = Box::pin(tokio_stream::iter(blobs));
            let (tx, rx) = mpsc::channel::<
                tonic::Result<content_manager::GetAllBlobsFromCheckpointResponse>,
            >(128);
            tokio::spawn(async move {
                while let Some(message) = stream.next().await {
                    let _ = tx.send(message).await;
                }
            });
            rx
        }

        async fn get_best_ann_index(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _checkpoint_id: &str,
        ) -> tonic::Result<GetBestAnnIndexResult> {
            Ok(GetBestAnnIndexResult {
                index_id: String::from("badcafe"),
                added_blobs: vec![],
                removed_blobs: vec![],
            })
        }

        async fn get_ann_index_blob_infos(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _index_id: &str,
        ) -> tonic::Result<GetAnnIndexBlobInfosResult> {
            Ok(GetAnnIndexBlobInfosResult { infos: vec![] })
        }

        async fn get_ann_index_asset(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _index_id: &str,
            _sub_key: &str,
        ) -> tonic::Result<Vec<u8>> {
            return Ok(b"foobar".to_vec());
        }

        async fn upload_ann_index_blob_infos(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _index_id: &str,
            _infos: &[AnnIndexBlobInfoData],
        ) -> tonic::Result<UploadAnnIndexBlobInfosResponse> {
            Ok(UploadAnnIndexBlobInfosResponse {})
        }

        async fn add_ann_index_mapping(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _checkpoint_id: &str,
            _index_id: &str,
            _added_blobs: &[BlobName],
            _removed_blobs: &[BlobName],
        ) -> tonic::Result<AddAnnIndexMappingResponse> {
            return Ok(AddAnnIndexMappingResponse {});
        }

        async fn upload_ann_index_assets(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _index_id: &str,
            _assets: &[(String, Vec<u8>)],
        ) -> tonic::Result<UploadAnnIndexAssetsResponse> {
            return Ok(UploadAnnIndexAssetsResponse {});
        }
    }

    #[tokio::test]
    async fn test_get_checkpoint() {
        let client = Arc::new(MockContentManagerClient::new(
            HashSet::default(),
            HashMap::from([
                (
                    "checkpoint-id-1".to_string(),
                    vec![
                        "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a"
                            .to_string(),
                    ],
                ),
                (
                    "checkpoint-id-2".to_string(),
                    vec![
                        "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a"
                            .to_string(),
                        "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c"
                            .to_string(),
                    ],
                ),
            ]),
            "services/checkpoint_indexer/server/test_data/tk1_sk1_hello.npy",
        ));

        let cache = CheckpointCacheImpl::new(Config::default(), client.clone());
        let tenant_info = TenantInfo::new_for_test();

        // New checkpoint with a single blob name- new content manager request
        let request_context_1 = RequestContext::new_for_test();
        let result_1 = cache
            .get_checkpoint(&request_context_1, &tenant_info, "checkpoint-id-1")
            .await
            .expect("Failed to get index elements");
        assert_eq!(result_1.blobs.len(), 1);
        // Check that a new content manager request was made
        assert_eq!(
            *client.last_checkpoint_blob_names_request.lock().unwrap(),
            Some(request_context_1.request_id())
        );

        // Same checkpoint as previous- no new content manager request
        let request_context_2 = RequestContext::new_for_test();
        let result_2 = cache
            .get_checkpoint(&request_context_2, &tenant_info, "checkpoint-id-1")
            .await
            .expect("Failed to get index elements");
        assert_eq!(result_2.blobs.len(), 1);
        assert_eq!(
            *client.last_checkpoint_blob_names_request.lock().unwrap(),
            Some(request_context_1.request_id())
        );

        // New checkpoint that returns multiple blob names- new content manager request
        let request_context_3 = RequestContext::new_for_test();
        let result_3 = cache
            .get_checkpoint(&request_context_3, &tenant_info, "checkpoint-id-2")
            .await
            .expect("Failed to get index elements");
        assert_eq!(result_3.blobs.len(), 2);
        assert_eq!(
            *client.last_checkpoint_blob_names_request.lock().unwrap(),
            Some(request_context_3.request_id())
        );

        // Go back to previous checkpoint- no new content manager request
        let request_context_4 = RequestContext::new_for_test();
        let result_4 = cache
            .get_checkpoint(&request_context_4, &tenant_info, "checkpoint-id-1")
            .await
            .expect("Failed to get index elements");
        assert_eq!(result_4.blobs.len(), 1);
        // Check that no new content manager request was made
        assert_eq!(
            *client.last_checkpoint_blob_names_request.lock().unwrap(),
            Some(request_context_3.request_id())
        );
    }

    #[tokio::test]
    async fn test_get_checkpoint_error() {
        let client = Arc::new(MockContentManagerClient::new(
            HashSet::default(),
            HashMap::default(),
            "services/checkpoint_indexer/server/test_data/tk1_sk1_hello.npy",
        ));
        let cache = CheckpointCacheImpl::new(Config::default(), client.clone());

        assert!(cache
            .get_checkpoint(
                &RequestContext::new_for_test(),
                &TenantInfo::new_for_test(),
                "nonexistent-checkpoint-id"
            )
            .await
            .is_err());

        assert!(cache
            .get_checkpoint(
                &RequestContext::new_for_test(),
                &TenantInfo::new_for_test(),
                "nonexistent-checkpoint-id"
            )
            .await
            .is_err());
    }

    #[tokio::test]
    async fn test_cache_tti() {
        let client = Arc::new(MockContentManagerClient::new(
            HashSet::default(),
            HashMap::from([
                (
                    "checkpoint-keep-checking".to_string(),
                    vec![
                        "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a"
                            .to_string(),
                    ],
                ),
                (
                    "checkpoint-stop-checking".to_string(),
                    vec![
                        "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a"
                            .to_string(),
                        "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c"
                            .to_string(),
                    ],
                ),
            ]),
            "services/checkpoint_indexer/server/test_data/tk1_sk1_hello.npy",
        ));

        let config = config::Config {
            cache_tti_seconds: 3,
            ..Default::default()
        };
        let cache = CheckpointCacheImpl::new(config.clone(), client.clone());

        let request_context = &RequestContext::new_for_test();
        let tenant_info = &TenantInfo::new_for_test();

        // initial read to populate cache
        let result = cache
            .get_checkpoint(request_context, tenant_info, "checkpoint-keep-checking")
            .await
            .expect("Failed to get checkpoint");
        assert_eq!(
            result.checkpoint_key,
            "checkpoint-keep-checking".to_string(),
            "Unexpected checkpoint key"
        );
        let result = cache
            .get_checkpoint(request_context, tenant_info, "checkpoint-stop-checking")
            .await
            .expect("Failed to get checkpoint");
        assert_eq!(
            result.checkpoint_key,
            "checkpoint-stop-checking".to_string(),
            "Unexpected checkpoint key"
        );

        // our cache exp is 3s, so expect that this is still around
        tokio::time::sleep(Duration::from_secs(2)).await;
        let result = cache
            .data
            .read_from_cache(
                tenant_info.tenant_id.as_ref().unwrap_or(&EMPTY_TENANT_ID),
                "checkpoint-keep-checking",
            )
            .await
            .expect("Failed to get checkpoint");
        assert_eq!(
            result.checkpoint_key,
            "checkpoint-keep-checking".to_string(),
            "Unexpected checkpoint key"
        );

        // wait another two seconds -- recently accessed checkpoint should be around; other should not
        tokio::time::sleep(Duration::from_secs(2)).await;
        let result = cache
            .data
            .read_from_cache(
                tenant_info.tenant_id.as_ref().unwrap_or(&EMPTY_TENANT_ID),
                "checkpoint-stop-checking",
            )
            .await;
        assert!(result.is_none());

        let result = cache
            .data
            .read_from_cache(
                tenant_info.tenant_id.as_ref().unwrap_or(&EMPTY_TENANT_ID),
                "checkpoint-keep-checking",
            )
            .await
            .expect("Failed to get checkpoint");
        assert_eq!(
            result.checkpoint_key,
            "checkpoint-keep-checking".to_string(),
            "Unexpected checkpoint key"
        );
    }
}
