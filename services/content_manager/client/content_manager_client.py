"""A Python client library for the content manager."""

import hashlib
import logging
import typing
from dataclasses import dataclass, replace
from pathlib import Path
from typing import Callable, Iterable, List, Optional, Sequence, Tuple

import grpc
from prometheus_client import Counter
from google.protobuf.timestamp_pb2 import Timestamp

from base.blob_names.python.blob_names import Blobs
from base.python.grpc import client_options
from services.content_manager import content_manager_pb2, content_manager_pb2_grpc
from services.lib.request_context.request_context import RequestContext

_corrupted_content_counter = Counter(
    "au_content_manager_corrupted_content_counter",
    "Counts corrupted content received by the content manager",
)

# set to half of the actual on-wire limit to leave some room for error
MAX_MESSAGE_SIZE = 2 * 1024 * 1024
BLOB_NAME_SIZE = 32

ContentManagerStub = content_manager_pb2_grpc.ContentManagerStub


class ContentManagerException(grpc.RpcError):
    """Exception thrown by the content manager client."""

    def __init__(self, status_code: grpc.StatusCode, msg: str):
        super().__init__()
        self.status_code = status_code
        self.msg = msg

    def code(self) -> grpc.StatusCode:
        return self.status_code

    def details(self) -> str:
        return self.msg

    def __str__(self) -> str:
        return f"ContentManagerException({self.status_code}: {self.msg})"


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> content_manager_pb2_grpc.ContentManagerStub:
    """Setup the client stub for the content manager."""
    logging.info("Creating grpc client to %s with options %s", endpoint, options)
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = content_manager_pb2_grpc.ContentManagerStub(channel)
    return stub


def _get_content_hash(content: bytes) -> str:
    """Compute the hash of the content to verify we recieved the right bytes."""
    sha256_hash = hashlib.sha256()
    sha256_hash.update(content)
    return sha256_hash.hexdigest()


@dataclass(frozen=True)
class ContentKey:
    """Content key identifying a raw blob content or a transformed blob content."""

    blob_name: str
    transformation_key: str = ""
    sub_key: str = ""


@dataclass(frozen=True)
class AnnIndexKey:
    """Key that uniquely identifies an ANN index."""

    transformation_key: str
    index_id: str


@dataclass(frozen=True)
class GetBestAnnIndexResult:
    """Result of a get_best_ann_index call."""

    index_id: str
    added_blobs: List[bytes]
    removed_blobs: List[bytes]


@dataclass(frozen=True)
class AnnIndexAsset:
    """Asset data for an ANN index."""

    sub_key: str
    data: bytes


@dataclass(frozen=True)
class AnnIndexBlobInfo:
    """Information on a single blobe in the index."""

    blob_name: bytes
    chunk_count: int


class ContentManagerClient:
    """Class to simplify the usage of the content manager API."""

    def __init__(self, stub):
        """Constructs a new content manager."""
        self.stub = stub

    @classmethod
    def create_for_endpoint(
        cls,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        """Constructs a new content manager client from endpoint and credentials."""
        stub = setup_stub(endpoint, credentials, options=options)
        return cls(stub)

    def upload(
        self,
        content: bytes,
        path: str,
        request_context: RequestContext,
        timeout: float = 10,
    ) -> tuple[str, bool]:
        """Upload a given byte array to the content manager.

        Returns:
        - A tuple of blob name and if the blob already existed
        """
        request = content_manager_pb2.UploadBlobContentRequest()
        new_metadata = request.metadata.add()
        new_metadata.key = "path"
        new_metadata.value = path
        request.content = content
        response = self.stub.UploadBlobContent(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return (response.blob_name, response.existed)

    def batch_upload(
        self,
        blobs: list[tuple[bytes, str]],
        request_context: RequestContext,
        timeout: float = 60,
        priority: content_manager_pb2.IndexingPriority.ValueType = content_manager_pb2.IndexingPriority.DEFAULT,
    ) -> list[tuple[str, bool]]:
        """Upload a list of contents to the content manager.

        Note: the caller HAS to check the length of the list of blob names returned.
        An list that doesn't have the same size as the list of argument blobs means that
        not all blobs have been uploaded, and the caller should retry with the remaining blob names.

        Returns:
        - A list with an entry for each blob in the argument. Each entry
          is a tuple of blob name and if the blob already existed.
        """
        request = content_manager_pb2.BatchUploadBlobContentRequest(
            priority=priority,
        )
        blob_upload_contents = []
        for content, path in blobs:
            blob_upload_content = content_manager_pb2.UploadBlobContent(content=content)
            new_metadata = blob_upload_content.metadata.add()
            new_metadata.key = "path"
            new_metadata.value = path
            blob_upload_contents.append(blob_upload_content)
        request.entries.extend(blob_upload_contents)
        response = self.stub.BatchUploadBlobContent(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return [(r.blob_name, r.existed) for r in response.results]

    def find_missing(
        self,
        blob_names: typing.Sequence[str],
        request_context: RequestContext,
        transformation_key: str | None = None,
        sub_key: str | None = None,
        tenant_id: str | None = None,
        timeout: float = 10,
    ) -> list[str]:
        """Returns the list of the missing blob names."""
        request = content_manager_pb2.FindMissingBlobRequest(
            tenant_id=tenant_id or "",
        )
        request.transformation_key = transformation_key or ""
        request.sub_key = sub_key or ""
        for blob_name in blob_names:
            request.blob_names.append(blob_name)
        response = self.stub.FindMissingBlobs(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.unknown_blob_names

    def subscribe_flow_controlled(
        self,
        transformation_key: str,
        request_context: RequestContext,
        window_provider: Iterable[int],
    ) -> Iterable[content_manager_pb2.SubscribeBlobUpdatesResponse]:
        """Subscribes to notifications of uploads, with flow control."""
        request = content_manager_pb2.SubscribeBlobUpdatesRequest()
        request.transformation_key = transformation_key

        def generator():
            for window in window_provider:
                request.window = window
                yield request

        for response in self.stub.SubscribeBlobUpdatesFlowControlled(
            generator(), metadata=request_context.to_metadata()
        ):
            logging.debug("%s", response)
            yield response

    def list_blob_content_keys(
        self,
        blob_name: str,
        request_context: RequestContext,
    ) -> list[content_manager_pb2.BlobContentsKey]:
        """Lists the content keys for a given blob."""
        request = content_manager_pb2.ListBlobContentKeysRequest()
        request.blob_name = blob_name

        response = self.stub.ListBlobContentKeys(
            request, metadata=request_context.to_metadata()
        )
        logging.debug("%s", response)
        return response.keys

    def delete_blob(
        self,
        blob_name: str,
        user_id: str,
        request_context: RequestContext,
        tenant_id: str | None = None,
        timestamp: Optional[Timestamp] = None,
    ) -> bool:
        """Deletes a blob.

        Args:
            blob_name: The name of the blob to delete.
            user_id: The ID of the user who owns the blob.
            request_context: The request context.
            tenant_id: Optional tenant ID.
            timestamp: Optional timestamp to specify when the blob was uploaded.
                    This is useful for deleting user index entries when the blob
                    content has already been deleted.

        Returns:
            True if the blob and user index entry (if provided) were found and
            deleted, and False if the blob or user index entry were not found.
        """
        request = content_manager_pb2.DeleteBlobRequest(
            blob_name=blob_name,
            user_id=user_id,
            tenant_id=tenant_id or "",
        )

        if timestamp:
            request.timestamp.CopyFrom(timestamp)

        try:
            response = self.stub.DeleteBlob(
                request, metadata=request_context.to_metadata()
            )
            logging.debug("%s", response)
            return True
        except grpc.RpcError as ex:
            status_code = ex.code()  # pylint: disable=no-member # type: ignore
            if status_code == grpc.StatusCode.NOT_FOUND:
                logging.warning(
                    "Could not find blob and/or user index when attempting to delete: %s",
                    ex,
                )
                return False
            else:
                details = ex.details()  # pylint: disable=no-member # type: ignore
                raise ContentManagerException(status_code, details) from ex

    def batch_delete_blobs(
        self,
        blob_user_pairs: list[
            typing.Union[tuple[str, str], tuple[str, str, Timestamp]]
        ],
        request_context: RequestContext,
        tenant_id: str | None = None,
        ignore_index: bool = False,
    ) -> bool:
        """Deletes a batch of blobs.

        Args:
            blob_user_pairs: List of (blob_name, user_id, Optional[timestamp]) pairs to delete
            request_context: The request context
            tenant_id: Optional tenant ID
            ignore_index: If true, will not delete the user index entries.

        Returns:
            True if the blobs and user index entries (if applicable) were found and deleted, and False
            if some of the blob deletion was not successful.
        """
        entries = []
        for pair in blob_user_pairs:
            if len(pair) == 2:
                blob_name, user_id = pair
                timestamp = None
            else:
                blob_name, user_id, timestamp = pair

            entries.append(
                content_manager_pb2.BatchDeleteBlobsRequest.BlobUserPair(
                    blob_name=blob_name,
                    user_id=user_id,
                    timestamp=timestamp,
                )
            )

        request = content_manager_pb2.BatchDeleteBlobsRequest(
            entries=entries,
            tenant_id=tenant_id or "",
            ignore_index=ignore_index,
        )

        try:
            response = self.stub.BatchDeleteBlobs(
                request, metadata=request_context.to_metadata()
            )
            logging.debug("%s", response)
            return True
        except grpc.RpcError as ex:
            status_code = ex.code()  # pylint: disable=no-member # type: ignore
            if status_code == grpc.StatusCode.NOT_FOUND:
                logging.warning(
                    "Could not find blob and/or user index when attempting to delete: %s",
                    ex,
                )
                return False
            else:
                details = ex.details()  # pylint: disable=no-member # type: ignore
                raise ContentManagerException(status_code, details) from ex

    def get_info(
        self,
        blob_name: str,
        request_context: RequestContext,
        transformation_key: Optional[str] = None,
        sub_key: Optional[str] = None,
        timeout: float = 10,
    ) -> content_manager_pb2.GetBlobInfoResponse:
        """Returns the blob info for a given blob."""
        request = content_manager_pb2.GetBlobInfoRequest()
        request.blob_name = blob_name
        if transformation_key is not None:
            request.transformation_key = str(transformation_key)
        if sub_key is not None:
            request.sub_key = str(sub_key)

        response = (
            self.stub.GetBlobInfo(  # send GRPC request to server thats running in rust
                request,
                timeout=timeout,
                metadata=request_context.to_metadata(),
            )
        )
        logging.debug("%s", response)
        return response

    def batch_get_info(
        self,
        keys: Sequence[ContentKey],
        request_context: RequestContext,
        tenant_id: str | None = None,
        timeout: float = 10,
    ) -> typing.Sequence[content_manager_pb2.BatchBlobInfoResponse]:
        """Returns the blob info for a given batch of blobs."""
        reqs = []
        for key in keys:
            req = content_manager_pb2.BlobContentKey(
                blob_name=key.blob_name,
                transformation_key=key.transformation_key,
                sub_key=key.sub_key,
            )
            reqs.append(req)

        batch_info_request = content_manager_pb2.BatchGetBlobInfoRequest(
            blob_content_keys=reqs,
        )

        if tenant_id is not None:
            batch_info_request.tenant_id = tenant_id

        response = self.stub.BatchGetBlobInfo(  # send GRPC request to server thats running in rust
            batch_info_request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        logging.debug("%s", response)
        return response.blob_infos

    def subscribe_and_wait(
        self,
        blob_name: str,
        transformation_key: str,
        request_context: RequestContext,
    ) -> Optional[content_manager_pb2.SubscribeBlobUpdatesResponse]:
        """Subscribes to uploads and waits until a given blob name was reported.

        All other subscription notifications are ignored.
        """
        request = content_manager_pb2.SubscribeBlobUpdatesRequest()
        request.transformation_key = transformation_key

        for response in self.stub.SubscribeBlobUpdates(
            request, metadata=request_context.to_metadata()
        ):
            logging.debug("%s", response)
            if response.blob_name == blob_name:
                return response

    def download_all(
        self,
        blob_name: str,
        request_context: RequestContext,
        transformation_key: Optional[str] = None,
        sub_key: Optional[str] = None,
        timeout: float = 10,
    ) -> tuple[bytes, dict[str, str]]:
        """Download the complete content of a blob or transformed blob."""
        download_request = content_manager_pb2.GetContentRequest()
        download_request.blob_name = blob_name
        if transformation_key is not None:
            download_request.transformation_key = str(transformation_key)
        if sub_key is not None:
            download_request.sub_key = str(sub_key)
        download_content = b""
        metadata = {}
        for response in self.stub.GetContent(
            download_request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        ):
            download_content += response.content
            if response.metadata:
                assert response.final_hash
                metadata = {b.key: b.value for b in response.metadata}
        return download_content, metadata

    def download_chunked(
        self,
        blob_name: str,
        request_context: RequestContext,
        transformation_key: Optional[str] = None,
        sub_key: Optional[str] = None,
    ) -> Iterable[bytes]:
        """Download a blob or transformed blob in chunks."""
        download_request = content_manager_pb2.GetContentRequest()
        download_request.blob_name = blob_name
        if transformation_key is not None:
            download_request.transformation_key = str(transformation_key)
        if sub_key is not None:
            download_request.sub_key = str(sub_key)
        for response in self.stub.GetContent(
            download_request, metadata=request_context.to_metadata()
        ):
            yield response.content

    def _batch_download_chunked(
        self,
        keys: Sequence[ContentKey],
        request_context: RequestContext,
        tenant_id: str | None = None,
    ) -> Iterable[content_manager_pb2.BatchGetContentResponse]:
        """Download a batch of blobs or/and transformed blobs in chunks."""
        reqs = []
        for key in keys:
            req = content_manager_pb2.GetContentRequest(
                blob_name=key.blob_name,
                transformation_key=key.transformation_key,
                sub_key=key.sub_key,
            )
            if tenant_id is not None:
                req.tenant_id = tenant_id
            reqs.append(req)
        download_request = content_manager_pb2.BatchGetContentRequest(
            requests=reqs,
        )
        if tenant_id is not None:
            download_request.tenant_id = tenant_id

        yield from self.stub.BatchGetContent(
            download_request, metadata=request_context.to_metadata()
        )

    def batch_download_all(
        self,
        keys: Sequence[ContentKey],
        request_context: RequestContext,
        tenant_id: str | None = None,
        micro_batch_size: int = 1024,
    ) -> Iterable[tuple[bytes, dict[str, str]] | None]:
        """Download a batch of blobs or/and transformed blobs.

        Args:
            keys: a list of content keys to download.
            micro_batch_size: The largest batch size we'll use to query the content
                manager. It is preferrable to use the largest value that works
                because creating a new gRPC stream is expensive. A value of ~64Ki
                is the theoretical limit (= 4Mb request), but we are using a
                default of 1Ki to reduce latency, especially as many consumers
                are likely to time out before being able to consume all the results.
            tenant_id: The tenant id to use for the request, only necessary if
                the request context does not have a tenant id.

        Returns:
            An iterable of tuples of content and metadata. If the content is not
            found, the tuple will be None.
        """
        # Short-circuit and don't call content manager if you don't need to.
        if not keys:
            return []

        def get_batch(
            keys: Sequence[ContentKey],
        ) -> Iterable[tuple[bytes, dict[str, str]] | None]:
            """Internal version that actually does the logic."""
            assert len(keys) <= micro_batch_size

            # We separately iterate through the keys to test we have received all the
            # input we are expecting.
            key_it = iter(keys)

            current_key, partial_bytes = None, []
            # NOTE(arun): The responses supposed to come in the same order as the keys per
            # contract, but they may either be "not found" or be returned in chunks.
            for response in self._batch_download_chunked(
                keys, request_context=request_context, tenant_id=tenant_id
            ):
                if current_key is None:
                    current_key, partial_bytes = next(key_it), []
                if response.HasField("not_found_content"):
                    assert current_key == ContentKey(
                        response.not_found_content.blob_name,
                        response.not_found_content.transformation_key,
                        response.not_found_content.sub_key,
                    )
                    yield None
                    current_key, partial_bytes = None, []
                elif response.HasField("final_content"):
                    assert current_key == ContentKey(
                        response.final_content.blob_name,
                        response.final_content.transformation_key,
                        response.final_content.sub_key,
                    )
                    partial_bytes.append(response.final_content.content)
                    content = b"".join(partial_bytes)
                    content_hash = _get_content_hash(content)
                    if content_hash != response.final_content.final_hash:
                        logging.error(
                            "Content hash mismatch: expected final hash=%s, expected=%s, blob_name=%s/%s/%s, keys=%s",
                            response.final_content.final_hash,
                            content_hash,
                            response.final_content.blob_name,
                            response.final_content.transformation_key,
                            response.final_content.sub_key,
                            keys,
                        )
                        _corrupted_content_counter.inc()
                        yield None
                    else:
                        metadata = {
                            metadatum.key: metadatum.value
                            for metadatum in response.final_content.metadata
                        }
                        yield content, metadata
                    current_key, partial_bytes = None, []
                else:
                    raise ValueError(
                        f"Response not set: {response.WhichOneof('response')}"
                    )

            # The stream has ended, so we should have received all the keys.
            if remaining_keys := list(key_it):
                raise ValueError(f"Response not received for keys: {remaining_keys}")

        for batch_start in range(0, len(keys), micro_batch_size):
            batch = keys[batch_start : batch_start + micro_batch_size]
            yield from get_batch(batch)

    def upload_transformed_content(
        self,
        blob_name: str,
        transformation_key: str,
        contents: Iterable[tuple[str, bytes]],
        receipt_handle: str,
        request_context: RequestContext,
        metadata: Optional[dict[str, str]] = None,
        timeout: float = 30,
    ):
        """Upload transformed content for a given blob name and transformation key."""
        requests = []

        for sub_key, content in contents:
            upload_request = content_manager_pb2.UploadTransformedBlobContentRequest()
            upload_request.receipt_handle = receipt_handle
            upload_request.blob_name = blob_name
            upload_request.transformation_key = transformation_key
            doc = upload_request.contents.add()
            doc.sub_key = sub_key
            doc.content = content
            if metadata:
                for key, value in metadata.items():
                    doc.metadata.add(key=key, value=value)
            requests.append(upload_request)

        self.stub.UploadTransformedBlobContent(
            iter(requests), timeout=timeout, metadata=request_context.to_metadata()
        )

    def upload_transformed_content_from_files(
        self,
        blob_name: str,
        transformation_key: str,
        files: list[tuple[str, Path]],
        receipt_handle: str,
        request_context: RequestContext,
        metadata: Optional[dict[str, str]] = None,
        timeout: float = 30,
    ):
        """Upload transformed content from a set of files."""
        self.upload_transformed_content(
            blob_name,
            transformation_key,
            ((sub_key, path.read_bytes()) for sub_key, path in files),
            receipt_handle,
            request_context,
            metadata=metadata,
            timeout=timeout,
        )

    def checkpoint_blobs(self, blobs: Blobs, request_context: RequestContext) -> str:
        """Checkpoint a set of blob names."""
        checkpoint_request = content_manager_pb2.CheckpointBlobsRequest()
        if blobs.baseline_checkpoint_id is not None:
            checkpoint_request.blobs.baseline_checkpoint_id = (
                blobs.baseline_checkpoint_id
            )
        checkpoint_request.blobs.added.extend(blobs.added)
        checkpoint_request.blobs.deleted.extend(blobs.deleted)
        response = self.stub.CheckpointBlobs(
            checkpoint_request, metadata=request_context.to_metadata()
        )
        return response.checkpoint_id

    def get_all_blobs_from_checkpoint(
        self,
        baseline_checkpoint_id: str,
        request_context: RequestContext,
        tenant_id: str | None = None,
    ) -> list[bytes] | None:
        """Return the list of blob names represented by the given checkpoint id.

        Returns None if the checkpoint id is not found.

        Raises ContentManagerException if the Content Manager is unable to fulfill
        the request.
        """
        request = content_manager_pb2.GetAllBlobsFromCheckpointRequest(
            tenant_id=tenant_id or ""
        )

        request.checkpoint_id = baseline_checkpoint_id
        blob_names: list[bytes] = []
        try:
            for response in self.stub.GetAllBlobsFromCheckpoint(
                request, metadata=request_context.to_metadata()
            ):
                blob_names.extend(response.blob_names)
        except grpc.RpcError as ex:
            status_code = ex.code()  # pylint: disable=no-member # type: ignore
            if status_code == grpc.StatusCode.NOT_FOUND:
                # It's unexpected but possible the checkpoint was garbage
                # collected from the content manager.
                return None
            else:
                details = ex.details()  # pylint: disable=no-member # type: ignore
                raise ContentManagerException(status_code, details) from ex

        return blob_names

    def get_user_blobs(
        self,
        user_id: str,
        tenant_id: str,
        request_context: RequestContext,
        limit: Optional[int] = None,
        min_timestamp: Optional[Timestamp] = None,
        max_timestamp: Optional[Timestamp] = None,
    ) -> typing.Sequence[content_manager_pb2.UploadInfo]:
        """Return the list of blob names and associated upload times for the
        given user in the given tenant.
        """
        request = content_manager_pb2.GetUserBlobsRequest(
            user_id=user_id,
            tenant_id=tenant_id,
            limit=limit,
            min_timestamp=min_timestamp,
            max_timestamp=max_timestamp,
        )

        try:
            response = self.stub.GetUserBlobs(
                request, metadata=request_context.to_metadata()
            )
            return response.entries
        except grpc.RpcError as ex:
            status_code = ex.code()  # pylint: disable=no-member # type: ignore
            details = ex.details()  # pylint: disable=no-member # type: ignore
            raise ContentManagerException(status_code, details) from ex

    def get_best_ann_index(
        self,
        transformation_key: str,
        checkpoint_id: str,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> content_manager_pb2.GetBestAnnIndexResponse:
        """Query for the optimal ANN index for a given checkpoint.

        Args:
            transformation_key: The transformation key to use.
            checkpoint_id: The checkpoint ID to query for.
            request_context: The request context.
            timeout: The timeout in seconds.

        Returns:
            A GetBestAnnIndexResult containing the index_id and blob deltas.
        """
        request = content_manager_pb2.GetBestAnnIndexRequest(
            transformation_key=transformation_key,
            checkpoint_id=checkpoint_id,
        )

        try:
            response = self.stub.GetBestAnnIndex(
                request,
                timeout=timeout,
                metadata=request_context.to_metadata(),
            )
            return response
        except grpc.RpcError as ex:
            if isinstance(ex, grpc.Call):
                status_code = ex.code()
                details = ex.details()
                raise ContentManagerException(status_code, details) from ex
            else:
                raise ex  # unknown exception should be bubbled up

    def get_ann_index_blob_infos(
        self,
        key: AnnIndexKey,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> List[content_manager_pb2.AnnIndexBlobInfo]:
        """Get the list of blob names associated with each index_id specified in the request.

        Args:
            keys: List of AnnIndexKey objects to query for.
            request_context: The request context.
            timeout: The timeout in seconds.

        Returns:
            A list of tuples containing (AnnIndexKey, list of blob names).
        """
        request = content_manager_pb2.GetAnnIndexBlobInfosRequest(
            key=content_manager_pb2.AnnIndexKey(
                transformation_key=key.transformation_key,
                index_id=key.index_id,
            )
        )

        current_blobs = []
        for response in self.stub.GetAnnIndexBlobInfos(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        ):
            current_blobs.extend(response.blob_infos)

        return current_blobs

    def get_ann_index_asset(
        self,
        transformation_key: str,
        index_id: str,
        sub_key: str,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> bytes:
        """Get the index asset for the given ANN index id and subkey.

        Args:
            transformation_key: The transformation key to use.
            index_id: The index ID to query for.
            request_context: The request context.
            timeout: The timeout in seconds.

        Returns:
            The asset data.
        """
        request = content_manager_pb2.GetAnnIndexAssetRequest(
            key=content_manager_pb2.AnnIndexAssetKey(
                transformation_key=transformation_key,
                index_id=index_id,
                sub_key=sub_key,
            ),
        )

        asset = bytearray()
        for response in self.stub.GetAnnIndexAsset(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        ):
            asset.extend(response.data)

        return bytes(asset)

    def add_ann_index_mapping(
        self,
        transformation_key: str,
        checkpoint_id: str,
        index_id: str,
        added_blobs: List[bytes],
        removed_blobs: List[bytes],
        request_context: RequestContext,
        timeout: float = 30,
    ) -> None:
        """Enter an index mapping for a (tenant, tkey, checkpoint) key tuple.

        Args:
            transformation_key: The transformation key to use.
            checkpoint_id: The checkpoint ID to map.
            index_id: The index ID to map to.
            added_blobs: Blobs in checkpoint but not included in the index.
            removed_blobs: Blobs not in checkpoint but included in the index.
            request_context: The request context.
            timeout: The timeout in seconds.
        """
        request = content_manager_pb2.AddAnnIndexMappingRequest(
            key=content_manager_pb2.AddAnnIndexMappingRequestKey(
                transformation_key=transformation_key,
                checkpoint_id=checkpoint_id,
            ),
            index_id=index_id,
            added_blobs=added_blobs,
            removed_blobs=removed_blobs,
        )

        self.stub.AddAnnIndexMapping(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )

    def upload_ann_index_blob_infos(
        self,
        transformation_key: str,
        index_id: str,
        infos: list[AnnIndexBlobInfo],
        request_context: RequestContext,
        timeout: float = 60,
    ) -> None:
        def generate_requests():
            chunk_size = MAX_MESSAGE_SIZE // (BLOB_NAME_SIZE + 4)
            chunked = [
                infos[i : i + chunk_size] for i in range(0, len(infos), chunk_size)
            ]
            for i, chunk in enumerate(chunked):
                key = (
                    content_manager_pb2.AnnIndexKey(
                        transformation_key=transformation_key, index_id=index_id
                    )
                    if i == 0
                    else None
                )
                request = content_manager_pb2.UploadAnnIndexBlobInfosRequest(
                    key=key,
                    blob_infos=[
                        content_manager_pb2.AnnIndexBlobInfo(
                            blob_name=info.blob_name, chunk_count=info.chunk_count
                        )
                        for info in chunk
                    ],
                    last_message=(i == len(chunked) - 1),
                )
                yield request

        self.stub.UploadAnnIndexBlobInfos(
            generate_requests(), timeout=timeout, metadata=request_context.to_metadata()
        )

    def upload_ann_index_assets(
        self,
        transformation_key: str,
        index_id: str,
        assets: List[Tuple[str, bytes]],
        request_context: RequestContext,
        timeout: float = 60,
    ) -> None:
        """Upload index assets for a (tenant, tkey, index_id) ann index key tuple.

        Args:
            transformation_key: The transformation key to use.
            index_id: The index ID to upload assets for.
            assets: List of AnnIndexAsset objects to upload.
            request_context: The request context.
            timeout: The timeout in seconds.
        """

        def generate_requests():
            for i, (sub_key, data) in enumerate(assets):
                request = content_manager_pb2.UploadAnnIndexAssetsRequest(
                    key=content_manager_pb2.AnnIndexAssetKey(
                        transformation_key=transformation_key,
                        index_id=index_id,
                        sub_key=sub_key,
                    ),
                    data=data,
                    last_message=(i == len(assets) - 1),
                )
                yield request

        self.stub.UploadAnnIndexAssets(
            generate_requests(),
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
