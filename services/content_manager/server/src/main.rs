use std::{net::SocketAddr, sync::Arc, time::Duration};

use crate::gcp_object_store::GcpObjectStoreImpl;
use crate::gcp_queue_manager::GcpQueueManagerImpl;
use crate::queue_manager::QueueManager;
use crate::rate_limiter::RateLimiter;
use clap::Parser;
use config::CliArguments;
use grpc_auth::{Grpc<PERSON>uth, GrpcAuthMiddlewareLayer};
use grpc_metrics::MetricsMiddlewareLayer;
use grpc_tls_config::{get_client_tls_creds, get_server_tls_creds};
use object_store::ObjectStore;
use rate_limiter::{NoopRateLimiter, TokenBucketRateLimiter};
use request_insight_publisher::{RequestInsightPublisherConfig, RequestInsightPublisherImpl};
use struct_logging::setup_struct_logging;
use token_exchange_client::{TokenExchangeClientImpl, TokenGrpcAuth};
use tokio::select;
use tokio::signal::unix::{signal, SignalKind};
use tonic::transport::Server;
use tonic_reflection::server::ServerReflectionServer;

use crate::metrics::{ACTIVE_REQUESTS_COLLECTOR, RESPONSE_LATENCY_COLLECTOR};
use crate::{
    config::Config, content_manager_service::ContentManagerImpl,
    transformation_keys::TransformationKeyWatcher,
};

mod config;
mod content_manager_service;
mod gcp_object_store;
mod gcp_queue_manager;
mod metrics;
mod object_store;
mod queue_manager;
mod rate_limiter;
mod transformation_keys;
mod util;

pub mod proto {
    pub mod base {
        pub use blob_names_rs_proto::base::blob_names;
    }

    pub(crate) const FILE_DESCRIPTOR_SET: &[u8] =
        tonic::include_file_descriptor_set!("content_manager_descriptor");

    pub use content_manager_rs_proto::content_manager;

    pub mod content_manager_store {
        tonic::include_proto!("content_manager_store");
    }
}

async fn run_with_object_store<
    OS: ObjectStore + Send + Sync + 'static,
    QM: QueueManager + Send + Sync + 'static,
>(
    config: Config,
    namespace: String,
    content_manager: ContentManagerImpl<OS, QM>,
    queue_manager: Arc<QM>,
) -> Result<(), Box<dyn std::error::Error>> {
    let (_health_reporter, health_service) = tonic_health::server::health_reporter();

    metrics_server::setup_default_metrics();
    // register and init runtime metrics. needs unstable tokio flag to work
    prometheus::default_registry()
        .register(Box::new(
            tokio_metrics_collector::default_runtime_collector(),
        ))
        .unwrap();

    let metrics_server = metrics_server::setup_metrics_http_server(
        &config.metrics_server_bind_address,
        config.metrics_server_port,
    )?;

    let client = kube::Client::try_default()
        .await
        .expect("Failed to start kube client");
    let watcher = TransformationKeyWatcher::new(client, &config.namespace, queue_manager);

    let server_tls_config =
        get_server_tls_creds(&config.server_mtls_config).expect("Failed to create TLS config");

    let addr: SocketAddr = config.bind_address.parse()?;
    let server = match server_tls_config {
        None => Server::builder(),
        Some(server_tls_config) => Server::builder()
            .tls_config(server_tls_config)
            .expect("Failed to create rpc server"),
    };

    let listener = tokio::net::TcpListener::bind(addr)
        .await
        .expect("Failed to bind");
    tracing::info!(
        "Listening on {:?}",
        listener.local_addr().expect("Failed to get local address")
    );

    let central_client_tls_config = get_client_tls_creds(&config.central_client_mtls_config)
        .expect("Failed to create TLS config");

    let grpc_auth: Arc<dyn GrpcAuth + Send + Sync> = {
        let token_exchange = TokenExchangeClientImpl::new(
            &config.auth_config.token_exchange_endpoint,
            namespace.to_string(),
            central_client_tls_config,
            Duration::from_secs_f32(config.auth_config.token_exchange_request_timeout_s),
        );
        Arc::new(TokenGrpcAuth::new(
            Arc::new(token_exchange),
            vec![], // the scopes are handled by the handler functions
        ))
    };

    let reflection_service: ServerReflectionServer<_> =
        tonic_reflection::server::Builder::configure()
            .register_encoded_file_descriptor_set(proto::FILE_DESCRIPTOR_SET)
            .build_v1()?;

    let mut sigterm_notifier = signal(SignalKind::terminate()).expect("handle SIGTERM");

    let server = server
        .timeout(Duration::from_secs(300))
        // should be >= max_concurrent_streams to prevent deadlock, see hyperium/hyper#3559
        .concurrency_limit_per_connection(config.grpc_max_concurrent_streams.try_into().unwrap())
        .max_concurrent_streams(Some(config.grpc_max_concurrent_streams))
        .http2_keepalive_timeout(Some(Duration::from_secs(15)))
        .http2_keepalive_interval(Some(Duration::from_secs(15)))
        .tcp_keepalive(Some(Duration::from_secs(15)))
        .trace_fn(tracing_tonic::server::trace_fn)
        .layer(
            tower::ServiceBuilder::new()
                .layer(MetricsMiddlewareLayer::new(
                    &RESPONSE_LATENCY_COLLECTOR,
                    &ACTIVE_REQUESTS_COLLECTOR,
                ))
                .layer(GrpcAuthMiddlewareLayer::new(grpc_auth))
                .into_inner(),
        )
        .add_service(content_manager.new_server())
        .add_service(health_service)
        .add_service(reflection_service)
        .serve_with_incoming_shutdown(
            tokio_stream::wrappers::TcpListenerStream::new(listener),
            async move {
                sigterm_notifier.recv().await;
            },
        );

    if config.health_logger_frequency_s > 0.0 {
        // We are seeing some weird stalls and don't know exactly what is going on.
        // This is a bit of a hail mary to see if the task executor or logging is
        // getting stuck perhaps.
        tokio::spawn(async move {
            loop {
                tracing::info!(
                    "Health logging every {}s... ok",
                    config.health_logger_frequency_s
                );
                tokio::time::sleep(Duration::from_secs_f64(config.health_logger_frequency_s)).await;
            }
        });
    } else {
        tracing::info!("Health logging disabled");
    }

    select! {
        server = futures::future::join(server, metrics_server) => {
            panic!("servers done: {server:?}");
        },
        watcher = watcher.run() => {
            panic!("watcher failed: {watcher:?}");
        },
    };
}

async fn run(args: CliArguments) -> Result<(), Box<dyn std::error::Error>> {
    let config = Config::read(&args.config_file).expect("Failed to read config file");
    tracing::info!("{:?}", config);

    let namespace = match std::env::var("POD_NAMESPACE") {
        Ok(name) => name,
        Err(_) => panic!("POD_NAMESPACE environment variable must be set."),
    };

    // Remove underscore on first use
    let _registry = feature_flags::new_registry();

    // Remove underscore on first use
    let feature_flags = feature_flags::setup(
        "content_manager",
        "0.0.0",
        config.feature_flags_sdk_key_path.as_ref(),
        config.dynamic_feature_flags_endpoint.as_deref(),
    )
    .await;

    let rate_limiter: Box<dyn RateLimiter + Send + Sync> =
        match &config.upload_notification_rate_limit {
            Some(config) => Box::new(TokenBucketRateLimiter::new(config.cache_size)),
            None => Box::new(NoopRateLimiter {}),
        };

    let queue_manager =
        Arc::new(GcpQueueManagerImpl::new(&config, rate_limiter, feature_flags.clone()).await);

    let object_store = GcpObjectStoreImpl::new(&config).await;

    let audit_logger = Arc::new(audit::stdout_audit_logger());

    let request_insight_publisher_config =
        RequestInsightPublisherConfig::read(&args.request_insight_publisher_config_file)
            .expect("Failed to read publisher config file");
    let request_insight_publisher =
        Arc::new(RequestInsightPublisherImpl::new(request_insight_publisher_config).await);

    let content_manager = ContentManagerImpl::new(
        config.clone(),
        queue_manager.clone(),
        Arc::new(object_store),
        feature_flags,
        audit_logger,
        request_insight_publisher,
    );

    run_with_object_store(config, namespace, content_manager, queue_manager).await
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    setup_struct_logging().expect("Failed to setup logging");

    let args = CliArguments::parse();
    tracing::info!("{:?}", args);

    run(args).await
}
