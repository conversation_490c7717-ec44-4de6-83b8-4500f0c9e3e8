use std::collections::BTreeSet;
use std::collections::HashMap;
use std::sync::Arc;

use crate::object_store::AnnIndexBlobInfoValue;
use crate::{
    config::Config,
    metrics::{
        BATCH_UPLOAD_SIZE_BYTES_COLLECTOR, FIND_MISSING_BLOB_COUNT,
        FIND_MISSING_UNKNOWN_BLOB_COUNT, NUM_BLOBS_UPLOADED_COLLECTOR,
        UPLOAD_BLOB_SIZE_BYTES_COLLECTOR, UPLOAD_CONSISTENCY_ERROR_COUNT,
    },
    object_store::{
        AnnIndexAssetKey, AnnIndexAssetStream, AnnIndexMapping, AnnIndexMappingKey,
        AnnIndexMappingValue, BatchGetContentStream, ObjectStore,
        TryNextResult::{FinalContent, UnknownBlob},
        UploadAnnIndexAsset, UploadAnnIndexBlobInfosPayload, UploadContent,
    },
    proto::content_manager::{
        batch_get_content_response::Response::{
            FinalContent as ProtoFinalContent, NotFoundContent as ProtoNotFoundContent,
        },
        content_manager_server::{ContentManager, ContentManagerServer},
        AddAnnIndexMappingRequest, AddAnnIndexMappingResponse, AnnIndexBlobInfo,
        BatchBlobInfoResponse, BatchDeleteBlobsRequest, BatchDeleteBlobsResponse,
        BatchGetBlobInfoRequest, BatchGetBlobInfoResponse, BatchGetContentFinalContent,
        BatchGetContentNotFound, BatchGetContentRequest, BatchGetContentResponse,
        BatchUploadBlobContentRequest, BatchUploadBlobContentResponse, BlobMetadata,
        CheckpointBlobsRequest, CheckpointBlobsResponse, DeleteBlobRequest, DeleteBlobResponse,
        FindMissingBlobRequest, FindMissingBlobResponse, GetAllBlobsFromCheckpointRequest,
        GetAllBlobsFromCheckpointResponse, GetAnnIndexAssetRequest, GetAnnIndexAssetResponse,
        GetAnnIndexBlobInfosRequest, GetAnnIndexBlobInfosResponse, GetBestAnnIndexRequest,
        GetBestAnnIndexResponse, GetBlobInfoRequest, GetBlobInfoResponse, GetContentRequest,
        GetContentResponse, GetUserBlobsRequest, GetUserBlobsResponse, IndexingPriority,
        ListBlobContentKeysRequest, ListBlobContentKeysResponse, SubscribeBlobUpdatesRequest,
        SubscribeBlobUpdatesResponse, UploadAnnIndexAssetsRequest, UploadAnnIndexAssetsResponse,
        UploadAnnIndexBlobInfosRequest, UploadAnnIndexBlobInfosResponse, UploadBlobContent,
        UploadBlobContentRequest, UploadBlobContentResponse, UploadBlobResult, UploadInfo,
        UploadTransformedBlobContentRequest, UploadTransformedBlobContentResponse,
    },
    queue_manager::{
        BlobPriority, FlowControl, ObjectNotification, QueueManager, SubscribeCallbackError,
    },
    util::{
        BlobContentKey::{self, Raw, Transformed},
        BlobName, CheckpointId, ObjectId, ObjectTransformationKeyInfo,
    },
};
use blob_names::{self, get_blob_name, SortedBlobNameBytesVec};
use bytes::BytesMut;
use chrono::{DateTime, Utc};
use feature_flags::FeatureFlagsServiceHandle;
use grpc_auth::tenant_info_from_grpc_req;
use grpc_service::{log_response_fn, log_response_status};
use itertools::Itertools;
use prost_wkt_types::Timestamp;
use request_context::UserId;
use request_context::{RequestContext, TenantId, TenantInfo, TokenScope, EMPTY_TENANT_ID};
use request_insight_publisher::{
    request_insight::{
        content_manager_upload_blobs::UploadedBlobInfo, session_event::Event,
        ContentManagerCheckpointBlobs, ContentManagerUploadBlobs, RecordSessionEventsRequest,
        SessionEvent,
    },
    to_tenant_info_proto, RequestInsightPublisher,
};
use secrecy::{ExposeSecret, SecretString, SecretVec};
use tokio::sync::mpsc::{self, Sender};
use tokio_stream::{wrappers::ReceiverStream, StreamExt};
use tonic::{Request, Response, Status, Streaming};
use tracing::Instrument;

pub const ENABLE_CHECKUP_INDEX_UPLOAD_CONSISTENCY_CHECK: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new(
        "content_manager_enable_checkup_index_upload_consistency_check",
        true,
    );

const MAX_MESSAGE_SIZE: usize = 2 * 1024 * 1024;

// sends a message to a channel and ignores errors
// returns true if the message was sent
async fn send_and_ignore<T>(tx: &Sender<T>, msg: T, sendsite: &str) -> bool {
    if let Err(send_error) = tx.send(msg).await {
        if tx.is_closed() {
            tracing::debug!("Channel closed in {}", sendsite);
        } else {
            tracing::info!(
                "Failed to send notification in {}: {}",
                sendsite,
                send_error
            );
        }
        false
    } else {
        true
    }
}

/// checks if a catchup notification needs to be performed on a given content key
///
/// This function is usually called if a transformed key wasn't found.
async fn check_catchup<OR: ObjectStore + Send + Sync, QM: QueueManager + Send + Sync>(
    object_store: &OR,
    queue_manager: &QM,
    request_context: &RequestContext,
    opaque_user_id: Option<&UserId>,
    tenant_id: &TenantId,
    keys: &[BlobContentKey],
    feature_flags: &feature_flags::FeatureFlagsServiceHandle,
) -> Result<(), tonic::Status> {
    let raw_keys = keys
        .iter()
        .flat_map(|k| match k {
            Raw(_) => None,
            Transformed(blob_name, _, _) => Some(Raw(blob_name.clone())),
        })
        .collect_vec();

    let infos = object_store
        .get_blob_infos(request_context, tenant_id, raw_keys.as_slice())
        .await?;

    // this section compares the list of transformation keys that were informed about this blob
    // with the list of transformation keys that exist. If there are transformation keys that
    // exist, but were not informed about the blob, a notification is sent. This is not only
    // done for the transformation key requested, but for all transformation keys that exist.
    let validations = infos
        .iter()
        .filter_map(|(key, blob_info)| {
            blob_info
                .as_ref()
                .map(|blob_info| ObjectTransformationKeyInfo {
                    object_id: ObjectId::BlobName(key.blob_name().clone()),
                    transformation_keys: blob_info
                        .informed_transformation_keys
                        .iter()
                        .map(|e| e.transformation_key.clone())
                        .collect_vec(),
                })
        })
        .collect_vec();
    let informed_keys = queue_manager
        .validate_notifications(
            tenant_id,
            opaque_user_id,
            &request_context.request_session_id(),
            validations.as_slice(),
            feature_flags,
            crate::queue_manager::BlobPriority::Low,
        )
        .await?;
    object_store
        .record_informed_keys(request_context, tenant_id, informed_keys)
        .await?;

    if !ENABLE_CHECKUP_INDEX_UPLOAD_CONSISTENCY_CHECK.get_from(feature_flags) {
        return Ok(());
    }

    // this section compares the the list of uploaded transformtion keys with the list
    // of transformation keys that were requested. If there are transformation keys that
    // were marked as uploaded, but failed to be downloaded, that is an indication of a problem.

    // this are keys with a transformation key that were requested, but not found
    let key_with_transformation_key_set: BTreeSet<(&BlobName, &String)> = keys
        .iter()
        .filter_map(|k| match k {
            Raw(_) => None,
            Transformed(blob_name, transformation_key, _) => Some((blob_name, transformation_key)),
        })
        .collect();

    let recently = Utc::now() - chrono::Duration::minutes(2);
    infos.iter().for_each(|(key, blob_info)| {
        if let Some(blob_info) = blob_info
        {
            blob_info
            .uploaded_transformation_keys
            .iter()
            .for_each(|e| {
                if e.time < recently && key_with_transformation_key_set
                    .contains(&(key.blob_name(), &e.transformation_key))
                {
                    // a key with a transformation key was not found, but it was marked as uploaded.
                    //
                    // in theory this should never happen, but there was a problem in upload error handling
                    // which led to this state in an unknown number of cases.
                    //
                    // in theory this could also happen due to an outdated local metadata cache.
                    // this can also happen due to client bug as in it is asking for a sub key that genuninely doesn't exist,
                    // but that is unlikely
                    UPLOAD_CONSISTENCY_ERROR_COUNT.inc();
                    tracing::error!(
                        "Uploaded consistency error: transformation_key={}, blob_name={} tenant_id={}",
                        e.transformation_key,
                        key.blob_name(),
                        tenant_id,
                    );
                }
            }
            );
        }
        });

    Ok(())
}

// Avoid log lines that scale with the number of keys being read
fn log_keys(keys: &[BlobContentKey]) -> String {
    match keys {
        [] => "[] (0)".to_string(),
        [first] => format!("[{:?}] (1)", first), // single element slice
        [first, .., last] => format!("[{:?}, .., {:?}] ({})", first, last, keys.len()),
    }
}

pub struct ContentManagerImpl<OS: ObjectStore + Send + Sync, QM: QueueManager + Send + Sync> {
    config: Config,
    queue_manager: Arc<QM>,
    object_store: Arc<OS>,
    feature_flags: feature_flags::FeatureFlagsServiceHandle,
    audit_logger: Arc<audit::AuditLogger>,
    request_insight_publisher: Arc<dyn RequestInsightPublisher + Send + Sync + 'static>,
}

impl<OS: ObjectStore + Send + Sync + 'static, QM: QueueManager + Send + Sync + 'static>
    ContentManagerImpl<OS, QM>
{
    pub fn new(
        config: Config,
        queue_manager: Arc<QM>,
        object_store: Arc<OS>,
        feature_flags: feature_flags::FeatureFlagsServiceHandle,
        audit_logger: Arc<audit::AuditLogger>,
        request_insight_publisher: Arc<dyn RequestInsightPublisher + Send + Sync + 'static>,
    ) -> Self {
        ContentManagerImpl {
            config,
            queue_manager,
            object_store,
            feature_flags,
            audit_logger,
            request_insight_publisher,
        }
    }

    pub fn new_server(self) -> ContentManagerServer<Self> {
        ContentManagerServer::new(self)
    }

    pub fn get_tenant_id(
        &self,
        auth_tenant_id: &Option<TenantId>,
        req_tenant_id: &TenantId,
    ) -> Result<TenantId, tonic::Status> {
        match auth_tenant_id {
            None => {
                if req_tenant_id == &EMPTY_TENANT_ID {
                    tracing::warn!("tenant_id is not set in request, no token");
                    Err(tonic::Status::invalid_argument("tenant_id must be set"))
                } else {
                    Ok(req_tenant_id.clone())
                }
            }
            Some(auth_tenant_id) => match (auth_tenant_id, req_tenant_id) {
                (&EMPTY_TENANT_ID, &EMPTY_TENANT_ID) => {
                    tracing::warn!("tenant_id is not set in request, namespace-scoped token");
                    Err(tonic::Status::invalid_argument("tenant_id must be set"))
                }
                (tenant_id, &EMPTY_TENANT_ID) => Ok(tenant_id.clone()),
                (&EMPTY_TENANT_ID, req_tenant_id) => Ok(req_tenant_id.clone()),
                (tenant_id, req_tenant_id) if tenant_id == req_tenant_id => Ok(tenant_id.clone()),
                (tenant_id, req_tenant_id) => Err(tonic::Status::permission_denied(format!(
                    "tenant_id mismatch: tenant_id={:?} req_tenant_id={:?}",
                    tenant_id, req_tenant_id
                ))),
            },
        }
    }

    fn get_upload_content_from_single_request(
        &self,
        request: UploadBlobContentRequest,
    ) -> Result<UploadContent, Status> {
        let upload = UploadBlobContent {
            metadata: request.metadata,
            content: request.content,
        };
        let path_name = self.get_path(&upload)?;
        let c = SecretVec::new(upload.content);
        let blob_name = BlobName::new(get_blob_name(&path_name, &c).as_str());

        Ok(UploadContent {
            blob_key: BlobContentKey::Raw(blob_name),
            metadata: upload
                .metadata
                .into_iter()
                .map(|m| (m.key, SecretString::new(m.value)))
                .collect_vec(),
            content: c,
        })
    }

    fn get_upload_content_from_batch_request(
        &self,
        request: BatchUploadBlobContentRequest,
    ) -> Result<Vec<UploadContent>, Status> {
        request
            .entries
            .into_iter()
            .map(|e| {
                let path_name = self.get_path(&e)?;
                let c = SecretVec::new(e.content);
                let blob_name = BlobName::new(get_blob_name(&path_name, &c).as_str());

                Ok(UploadContent {
                    blob_key: BlobContentKey::Raw(blob_name),
                    metadata: e
                        .metadata
                        .into_iter()
                        .map(|m| (m.key, SecretString::new(m.value)))
                        .collect_vec(),
                    content: c,
                })
            })
            .collect()
    }

    fn should_notify_on_upload(&self, existing_blob: bool) -> bool {
        // do notification check if the blob was not found or if we also do catchup on upload
        !existing_blob || self.config.catchup_on_reupload
    }

    fn get_feature_flags(
        &self,
        tenant_info: &TenantInfo,
    ) -> Result<FeatureFlagsServiceHandle, tonic::Status> {
        self.feature_flags
            .bind_attribute("tenant_name", &tenant_info.tenant_name)
            .map_err(|e| {
                tracing::error!("get_feature_flags failed: {:?}", e);
                tonic::Status::internal("Failed to bind feature flags")
            })
    }

    async fn upload_blob_content(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        tenant_id: &TenantId,
        uploads: Vec<UploadContent>,
        priority: crate::queue_manager::BlobPriority,
    ) -> Result<Vec<UploadBlobResult>, tonic::Status> {
        let feature_flags = self.get_feature_flags(tenant_info)?;

        tracing::info!(
            "upload blob content: {:?}",
            uploads.iter().map(|e| &e.blob_key).collect_vec()
        );

        let num_blobs = uploads.len();
        if num_blobs > self.config.batch_upload_blob_limit {
            tracing::warn!(
                "Batch upload contains too many blobs: {:?} (limit: {:?} blobs)",
                num_blobs,
                self.config.batch_upload_blob_limit
            );
            return Err(tonic::Status::out_of_range(
                "Batch upload contains too many blobs",
            ));
        }

        let batch_upload_size = uploads
            .iter()
            .map(|e| e.content.expose_secret().len())
            .sum::<usize>();
        if batch_upload_size > self.config.batch_upload_content_limit {
            tracing::warn!(
                "Batch upload contains too much content: {:?} bytes (limit: {:?} bytes)",
                batch_upload_size,
                self.config.batch_upload_content_limit
            );
            return Err(tonic::Status::out_of_range(
                "Batch upload contains too much content",
            ));
        }
        if uploads
            .iter()
            .map(|e| e.content.expose_secret().len())
            .any(|l| l > self.config.upload_content_limit)
        {
            // TODO: add more detail here?
            tracing::warn!("Upload contains blobs with too much content");
            return Err(tonic::Status::out_of_range(
                "Upload contains blobs with too much content",
            ));
        }
        if uploads
            .iter()
            .filter(|e| e.blob_key.is_raw())
            .map(|e| e.content.expose_secret().len())
            .any(|l| l > self.config.upload_raw_content_limit)
        {
            // TODO: add more detail here?
            tracing::warn!("Upload contains raw blobs with too much content");
            return Err(tonic::Status::out_of_range(
                "Upload contains raw blobs with too much content",
            ));
        }
        let blob_size_by_name: HashMap<String, usize> = uploads
            .iter()
            .map(|upload| {
                (
                    upload.blob_key.blob_name().inner().to_string(),
                    upload.content.expose_secret().len(),
                )
            })
            .collect();

        // Now that we have validated the request, record metrics
        NUM_BLOBS_UPLOADED_COLLECTOR
            .with_label_values(&[tenant_info.metrics_tenant_name()])
            .observe(num_blobs as f64);
        BATCH_UPLOAD_SIZE_BYTES_COLLECTOR
            .with_label_values(&[tenant_info.metrics_tenant_name()])
            .observe(batch_upload_size as f64);
        uploads.iter().for_each(|e| {
            let content_size = e.content.expose_secret().len();
            UPLOAD_BLOB_SIZE_BYTES_COLLECTOR
                .with_label_values(&[
                    &e.blob_key.is_raw().to_string(),
                    &tenant_info.metrics_tenant_name().to_string(),
                ])
                .observe(content_size as f64);
        });

        if uploads.is_empty() {
            return Ok(vec![]);
        }

        // We know that the user id will always be set for user uploads as it is extracted from the
        // token. If this is a service upload, we will have a None value and will not write the
        // user index.
        let user_id = tenant_info.user_id.as_ref();

        let r = self
            .object_store
            .batch_upload_blob(request_context, tenant_id, uploads, user_id)
            .await?;
        tracing::debug!("uploaded blobs: {:?}", r);
        let transformation_key_infos: Vec<_> = r
            .iter()
            .filter_map(|(key, blob_info)| {
                if key.is_raw() && self.should_notify_on_upload(blob_info.is_some()) {
                    match blob_info {
                        Some(blob_info) => Some(ObjectTransformationKeyInfo {
                            object_id: ObjectId::BlobName(key.blob_name().clone()),
                            // do not notify on these transformation keys as they are already informed about the blob
                            transformation_keys: blob_info
                                .informed_transformation_keys
                                .iter()
                                .map(|e| e.transformation_key.clone())
                                .collect_vec(),
                        }),
                        None =>
                        // this is a new blob, so all transformation keys are informed about it
                        {
                            Some(ObjectTransformationKeyInfo {
                                object_id: ObjectId::BlobName(key.blob_name().clone()),
                                transformation_keys: vec![],
                            })
                        }
                    }
                } else {
                    // do not catchup on transformed keys
                    None
                }
            })
            .collect();

        let informed_keys = self
            .queue_manager
            .validate_notifications(
                tenant_id,
                tenant_info.opaque_user_id.as_ref(),
                &request_context.request_session_id(),
                transformation_key_infos.as_slice(),
                &feature_flags,
                priority,
            )
            .await?;
        self.object_store
            .record_informed_keys(request_context, tenant_id, informed_keys)
            .await?;

        let results: Vec<_> = r
            .into_iter()
            .map(|e| UploadBlobResult {
                existed: e.1.is_some(),
                blob_name: e.0.blob_name().inner().to_string(),
            })
            .collect();

        // Publish request insight event
        self.publish_upload_blobs_ri_event(
            request_context,
            tenant_info,
            &results,
            &blob_size_by_name,
        )
        .await;

        Ok(results)
    }

    fn get_path(&self, upload: &UploadBlobContent) -> Result<SecretString, tonic::Status> {
        match upload
            .metadata
            .iter()
            .find(|&item| item.key == "path")
            .map(|item| &item.value)
        {
            Some(s) => Ok(SecretString::new(s.to_string())),
            None => Err(Status::invalid_argument("path not set")),
        }
    }

    async fn publish_upload_blobs_ri_event(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        results: &[UploadBlobResult],
        blob_size_by_name: &HashMap<String, usize>,
    ) {
        let uploaded_blobs: Vec<UploadedBlobInfo> = results
            .iter()
            .map(|result| UploadedBlobInfo {
                blob_name: result.blob_name.clone(),
                size_bytes: blob_size_by_name
                    .get(&result.blob_name)
                    .cloned()
                    .unwrap_or(0) as u64,
                existed: result.existed,
            })
            .collect();
        let upload_event = ContentManagerUploadBlobs { uploaded_blobs };
        let event = SessionEvent {
            event_id: Some(uuid::Uuid::new_v4().to_string()),
            time: Some(prost_wkt_types::Timestamp::from(
                std::time::SystemTime::now(),
            )),
            event: Some(Event::ContentManagerUploadBlobs(upload_event)),
        };
        let request = RecordSessionEventsRequest {
            session_id: request_context.request_session_id().to_string(),
            opaque_user_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![event],
        };
        self.request_insight_publisher
            .record_session_events(request)
            .await;
    }

    async fn publish_checkpoint_blobs_ri_event(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_id: String,
        baseline_checkpoint_id: String,
        added_blobs_count: u32,
        deleted_blobs_count: u32,
    ) {
        let checkpoint_event = ContentManagerCheckpointBlobs {
            checkpoint_id,
            baseline_checkpoint_id,
            added_blobs_count,
            deleted_blobs_count,
        };
        let event = SessionEvent {
            event_id: Some(uuid::Uuid::new_v4().to_string()),
            time: Some(prost_wkt_types::Timestamp::from(
                std::time::SystemTime::now(),
            )),
            event: Some(Event::ContentManagerCheckpointBlobs(checkpoint_event)),
        };
        let request = RecordSessionEventsRequest {
            session_id: request_context.request_session_id().to_string(),
            opaque_user_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![event],
        };
        self.request_insight_publisher
            .record_session_events(request)
            .await;
    }
}

// takes a notification and constructs a SubscribeBlobUpdatesResponse out of it.
async fn get_subscribe_response<
    OS: ObjectStore + Send + Sync + 'static,
    QM: QueueManager + Send + Sync + 'static,
>(
    object_store: Arc<OS>,
    queue_manager: Arc<QM>,
    transformation_key: &str,
    n: Option<ObjectNotification>,
    request_context: &RequestContext,
) -> Result<Option<SubscribeBlobUpdatesResponse>, Status> {
    match n {
        Some(ObjectNotification {
            object_id: ObjectId::BlobName(blob_name),
            receipt_handle,
            tenant_id,
        }) => {
            match object_store
                .get_blob_content(request_context, &tenant_id, &Raw(blob_name.clone()))
                .await
            {
                Err(e) => {
                    tracing::error!(
                        "Failed to get blob content: transformation_key={}, blob_name={}, tenant_id={}, request_id={}, err={}",
                        transformation_key,
                        blob_name,
                        tenant_id,
                        request_context.request_id(),
                        e
                    );
                    if e.code() == tonic::Code::NotFound {
                        tracing::info!(
                            "Delete obsolete notification: transformation_key={}, blob_name={}, tenant_id={}, request_id={}",
                            transformation_key,
                            blob_name,
                            tenant_id,
                            request_context.request_id(),
                        );
                        // if the blob is gone, there is no point in the notification anymore
                        if let Err(e) = queue_manager
                            .ack_notification(transformation_key, &[&receipt_handle])
                            .await
                        {
                            tracing::error!("Failed to delete notification for missing object {}: transformation_key={}, object_id={} tenant_id={}",
                            e, transformation_key, blob_name, tenant_id);
                        }
                        Ok(None)
                    } else {
                        Err(e)
                    }
                }
                Ok((blob_info, raw_content)) => {
                    // when there is blob content for the transformation key, there is no point in notifying an indexer again.
                    if blob_info.is_transformation_key_uploaded(transformation_key) {
                        tracing::info!(
                            "Delete duplicate notification: transformation_key={}, blob_name={} tenant_id={}, request_id={}",
                            transformation_key,
                            blob_name,
                            tenant_id,
                            request_context.request_id(),

                        );
                        // if the blob is gone, there is no point in the notification anymore
                        if let Err(e) = queue_manager
                            .ack_notification(transformation_key, &[&receipt_handle])
                            .await
                        {
                            tracing::error!("Failed to delete notification for missing object {}: transformation_key={} object_id={} tenant_id={}",
                            e, transformation_key, blob_name, tenant_id);
                        }
                        Ok(None)
                    } else {
                        tracing::info!("Send blob update to subscriber: blob_name={} tenant_id={} transformation_key={} receipt_handle={} request_id={}",
                            blob_name, tenant_id, transformation_key, receipt_handle, request_context.request_id());
                        Ok(Some(SubscribeBlobUpdatesResponse {
                            blob_name: blob_name.inner().to_string(),
                            raw_content: raw_content.to_vec(),
                            receipt_handle,
                            metadata: blob_info
                                .metadata
                                .into_iter()
                                .map(|m| BlobMetadata {
                                    key: m.0,
                                    value: m.1.expose_secret().to_string(),
                                })
                                .collect(),
                            time: Some(Timestamp {
                                seconds: blob_info.time.timestamp(),
                                nanos: 0,
                            }),
                            tenant_id: tenant_id.to_string(),
                        }))
                    }
                }
            }
        }
        // an empty notification serves a kind of keepalive
        // TODO(luke): handle checkpoint notification case here?
        _ => Ok(Some(SubscribeBlobUpdatesResponse {
            blob_name: "".to_string(),
            raw_content: vec![],
            receipt_handle: "".to_string(),
            metadata: vec![],
            time: None,
            tenant_id: "".to_string(),
        })),
    }
}

fn _convert_proto_timestamp(timestamp: Option<Timestamp>) -> Option<DateTime<Utc>> {
    timestamp.map(|ts| {
        DateTime::<Utc>::from_timestamp(ts.seconds, ts.nanos as u32).expect("Invalid timestamp")
    })
}

#[tonic::async_trait]
impl<OS: ObjectStore + Send + Sync + 'static, QM: QueueManager + Send + Sync + 'static>
    ContentManager for ContentManagerImpl<OS, QM>
{
    async fn upload_blob_content(
        &self,
        request: Request<UploadBlobContentRequest>,
    ) -> Result<Response<UploadBlobContentResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;

        let span =
            tracing::info_span!("upload blob content", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentRw)?;
                let inner_request = request.into_inner();
                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &inner_request.tenant_id.as_str().into(),
                )?;
                let blobs = vec![self.get_upload_content_from_single_request(inner_request)?];

                if let Some(iap_email) = tenant_info.iap_email() {
                    for key in blobs.iter() {
                        self.audit_logger.write_audit_log(
                            iap_email,
                            audit::INTERNAL_IAP,
                            Some(tenant_info.tenant_name.as_str()),
                            Some(audit::Resource::Blob {
                                blob_name: key.blob_key.blob_name().to_string(),
                            }),
                            Some(format!(
                                "Uploaded blob {:?} in tenant {}",
                                key.blob_key, tenant_info.tenant_name
                            )),
                        )
                    }
                }

                tracing::info!(
                    "upload blob content: blob_keys={:?} tenant_id={}",
                    blobs.iter().map(|e| &e.blob_key).collect_vec(),
                    tenant_id
                );

                let priority = BlobPriority::Default;
                let results = self
                    .upload_blob_content(
                        &request_context,
                        &tenant_info,
                        &tenant_id,
                        blobs,
                        priority,
                    )
                    .await?;
                let response = results.into_iter().next().expect("no results");
                let reply = UploadBlobContentResponse {
                    existed: response.existed,
                    blob_name: response.blob_name,
                };
                Ok(Response::new(reply))
            },
            "upload_blob_content",
        )
        .instrument(span)
        .await
    }

    async fn batch_upload_blob_content(
        &self,
        request: Request<BatchUploadBlobContentRequest>,
    ) -> Result<Response<BatchUploadBlobContentResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let span = tracing::info_span!("batch upload blob content", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentRw)?;
                let inner_request = request.into_inner();

                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &inner_request.tenant_id.as_str().into(),
                )?;

                let priority = match inner_request.priority.try_into() {
                    Ok(IndexingPriority::Default) => BlobPriority::Default,
                    _ => BlobPriority::Low,
                };

                let blobs: Vec<UploadContent> =
                    self.get_upload_content_from_batch_request(inner_request)?;

                if let Some(iap_email) = tenant_info.iap_email() {
                    for key in blobs.iter() {
                        self.audit_logger.write_audit_log(
                            iap_email,
                            audit::INTERNAL_IAP,
                            Some(tenant_info.tenant_name.as_str()),
                            Some(audit::Resource::Blob {
                                blob_name: key.blob_key.blob_name().to_string(),
                            }),
                            Some(format!(
                                "Uploaded blob {:?} in tenant {}",
                                key.blob_key, tenant_info.tenant_name
                            )),
                        )
                    }
                }

                tracing::info!(
                    "upload blob content: blob_keys={:?} tenant_id={}",
                    blobs.iter().map(|e| &e.blob_key).collect_vec(),
                    tenant_id
                );

                let results = self
                    .upload_blob_content(
                        &request_context,
                        &tenant_info,
                        &tenant_id,
                        blobs,
                        priority,
                    )
                    .await?;
                let reply = BatchUploadBlobContentResponse { results };
                Ok(Response::new(reply))
            },
            "batch_upload_blob_content",
        )
        .instrument(span)
        .await
    }

    async fn delete_blob(
        &self,
        request: Request<DeleteBlobRequest>,
    ) -> Result<Response<DeleteBlobResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;

        let span = tracing::info_span!("delete blob", request_id = %request_context.request_id());
        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentAdmin)?;
                let inner_request = request.into_inner();

                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &inner_request.tenant_id.as_str().into(),
                )?;
                let user_id = SecretString::new(inner_request.user_id);

                // Convert the timestamp if provided
                let timestamp: Option<chrono::DateTime<chrono::Utc>> =
                    _convert_proto_timestamp(inner_request.timestamp);
                if let Some(iap_email) = tenant_info.iap_email() {
                    self.audit_logger.write_audit_log(
                        iap_email,
                        audit::INTERNAL_IAP,
                        Some(tenant_info.tenant_name.as_str()),
                        Some(audit::Resource::Blob {
                            blob_name: inner_request.blob_name.clone(),
                        }),
                        Some(format!(
                            "Delete blob {:?} in tenant {} {}",
                            inner_request.blob_name,
                            tenant_info.tenant_name,
                            timestamp.map_or("".to_string(), |ts| format!("with timestamp {}", ts))
                        )),
                    )
                }

                tracing::info!(
                    "delete blob info request blob_name={:?} tenant_id={} timestamp={:?}",
                    inner_request.blob_name,
                    tenant_id,
                    timestamp
                );

                self.object_store
                    .delete_blob(
                        &request_context,
                        &tenant_id,
                        &inner_request.blob_name.into(),
                        &user_id,
                        timestamp,
                    )
                    .await?;

                let reply = DeleteBlobResponse {};
                Ok(Response::new(reply))
            },
            "delete_blob",
        )
        .instrument(span)
        .await
    }

    async fn batch_delete_blobs(
        &self,
        request: Request<BatchDeleteBlobsRequest>,
    ) -> Result<Response<BatchDeleteBlobsResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;

        let span =
            tracing::info_span!("batch delete blobs", request_id = %request_context.request_id());
        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentAdmin)?;
                let inner_request = request.into_inner();

                if inner_request.entries.len() > self.config.batch_delete_blob_limit {
                    tracing::warn!(
                        "Batch delete contains too many blobs: {:?} (limit: {:?} blobs)",
                        inner_request.entries.len(),
                        self.config.batch_delete_blob_limit
                    );
                    return Err(tonic::Status::out_of_range(
                        "Batch delete contains too many blobs",
                    ));
                }

                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &inner_request.tenant_id.as_str().into(),
                )?;

                // Prepare the batch deletion request
                let deletion_entries: Vec<_> = inner_request
                    .entries
                    .iter()
                    .map(|entry| {
                        let timestamp = _convert_proto_timestamp(entry.timestamp);
                        (
                            entry.blob_name.clone().into(),
                            SecretString::new(entry.user_id.clone()),
                            timestamp,
                        )
                    })
                    .collect();

                // Perform batch deletion
                let response = self
                    .object_store
                    .batch_delete_blobs(
                        &request_context,
                        &tenant_id,
                        &deletion_entries,
                        inner_request.ignore_index,
                    )
                    .await?;

                // Single audit log entry for the batch operation
                if let Some(iap_email) = tenant_info.iap_email() {
                    self.audit_logger.write_audit_log(
                        iap_email,
                        audit::INTERNAL_IAP,
                        Some(tenant_info.tenant_name.as_str()),
                        None, // No specific resource, it's a batch operation
                        Some(format!(
                            "Batch delete of {} blobs in tenant {}",
                            inner_request.entries.len(),
                            tenant_info.tenant_name,
                        )),
                    );
                }

                Ok(Response::new(response))
            },
            "batch_delete_blobs",
        )
        .instrument(span)
        .await
    }

    async fn get_blob_info(
        &self,
        request: Request<GetBlobInfoRequest>,
    ) -> Result<Response<GetBlobInfoResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;

        let span = tracing::info_span!("get blob info", request_id = %request_context.request_id());
        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentR)?;
                let inner_request = request.into_inner();
                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &inner_request.tenant_id.as_str().into(),
                )?;

                if let Some(iap_email) = tenant_info.iap_email() {
                    self.audit_logger.write_audit_log(
                        iap_email,
                        audit::INTERNAL_IAP,
                        Some(tenant_info.tenant_name.as_str()),
                        Some(audit::Resource::Blob {
                            blob_name: inner_request.blob_name.clone(),
                        }),
                        Some(format!(
                            "Get blob info for blob {:?} in tenant {}",
                            inner_request.blob_name, tenant_info.tenant_name
                        )),
                    )
                }

                tracing::info!(
                    "get blob info request blob_name={:?} tenant_id={}",
                    inner_request.blob_name,
                    tenant_id
                );

                let blob_key = BlobContentKey::from((
                    &inner_request.blob_name,
                    &inner_request.transformation_key,
                    &inner_request.sub_key,
                ));

                let blob_infos = self
                    .object_store
                    .get_blob_infos(&request_context, &tenant_id, &[blob_key])
                    .await?;
                match blob_infos.into_iter().next() {
                    Some((_, Some(blob_info))) => {
                        let reply: GetBlobInfoResponse = blob_info.try_into()?;
                        Ok(Response::new(reply))
                    }
                    _ => Err(tonic::Status::not_found("Blob not found")),
                }
            },
            "get_blob_info",
        )
        .instrument(span)
        .await
    }

    async fn batch_get_blob_info(
        &self,
        request: Request<BatchGetBlobInfoRequest>,
    ) -> Result<Response<BatchGetBlobInfoResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let span =
            tracing::info_span!("get batch blob info", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentR)?;
                let inner_request = request.into_inner();

                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &inner_request.tenant_id.as_str().into(),
                )?;

                let keys: Vec<BlobContentKey> = inner_request
                    .blob_content_keys
                    .into_iter()
                    .map(|e| {
                        BlobContentKey::from((&e.blob_name, &e.transformation_key, &e.sub_key))
                    })
                    .sorted()
                    .collect();

                if let Some(iap_email) = tenant_info.iap_email() {
                    for key in keys.iter() {
                        self.audit_logger.write_audit_log(
                            iap_email,
                            audit::INTERNAL_IAP,
                            Some(tenant_info.tenant_name.as_str()),
                            Some(audit::Resource::Blob {
                                blob_name: key.blob_name().to_string(),
                            }),
                            Some(format!(
                                "Get blob info for blob {:?} in tenant {}",
                                key, tenant_info.tenant_name
                            )),
                        )
                    }
                }

                tracing::info!(
                    "batched get blob info request {}",
                    log_keys(keys.as_slice()),
                );

                let blob_infos = self
                    .object_store
                    .get_blob_infos(&request_context, &tenant_id, keys.as_slice())
                    .await?;

                let blob_infos: tonic::Result<Vec<BatchBlobInfoResponse>> = blob_infos
                    .into_iter()
                    .map(|(key, blob_info)| match blob_info {
                        Some(blob_info) => {
                            let bi = blob_info.try_into()?;
                            Ok(BatchBlobInfoResponse {
                                blob_content_key: Some(key.into()),
                                blob_info: Some(bi),
                            })
                        }
                        None => Ok(BatchBlobInfoResponse {
                            blob_content_key: Some(key.into()),
                            blob_info: None,
                        }),
                    })
                    .collect();

                let reply = BatchGetBlobInfoResponse {
                    blob_infos: blob_infos?,
                };
                Ok(Response::new(reply))
            },
            "batch_get_blob_info",
        )
        .instrument(span)
        .await
    }

    async fn list_blob_content_keys(
        &self,
        request: Request<ListBlobContentKeysRequest>,
    ) -> Result<Response<ListBlobContentKeysResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let span = tracing::info_span!("list blob content keys", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentR)?;
                let inner_request = request.into_inner();
                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &inner_request.tenant_id.as_str().into(),
                )?;

                if let Some(iap_email) = tenant_info.iap_email() {
                    self.audit_logger.write_audit_log(
                        iap_email,
                        audit::INTERNAL_IAP,
                        Some(tenant_info.tenant_name.as_str()),
                        Some(audit::Resource::Blob {
                            blob_name: inner_request.blob_name.clone(),
                        }),
                        Some(format!(
                            "List blob content keys for blob {:?} in tenant {}",
                            inner_request.blob_name, tenant_info.tenant_name
                        )),
                    )
                }

                tracing::info!(
                    "list blob content keys request blob_name={:?} tenant_id={}",
                    inner_request.blob_name,
                    tenant_id
                );

                let keys = self
                    .object_store
                    .list_blob_content_keys(
                        &request_context,
                        &tenant_id,
                        &BlobName::new(&inner_request.blob_name),
                    )
                    .await?;

                let reply = ListBlobContentKeysResponse {
                    keys: keys
                        .into_iter()
                        .map(|k| crate::proto::content_manager::BlobContentsKey {
                            transformation_key: k.transformation_key,
                            sub_key: k.sub_key,
                        })
                        .collect(),
                };
                Ok(Response::new(reply))
            },
            "list_blob_content_keys",
        )
        .instrument(span)
        .await
    }

    async fn find_missing_blobs(
        &self,
        request: Request<FindMissingBlobRequest>,
    ) -> Result<Response<FindMissingBlobResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let span =
            tracing::info_span!("find missing blobs", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                // we will call record_informed_keys, so we need write access
                tenant_info.validate_scope(TokenScope::ContentRw)?;
                let inner_request = request.into_inner();
                let feature_flags = self.get_feature_flags(&tenant_info)?;

                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &inner_request.tenant_id.as_str().into(),
                )?;

                FIND_MISSING_BLOB_COUNT.observe(inner_request.blob_names.len() as f64);

                let blob_names: Vec<BlobContentKey> = inner_request
                    .blob_names
                    .into_iter()
                    .map(|e| {
                        BlobContentKey::from((
                            e,
                            inner_request.transformation_key.clone(),
                            inner_request.sub_key.clone(),
                        ))
                    })
                    .collect_vec();

                if let Some(iap_email) = &tenant_info.iap_email() {
                    for key in blob_names.iter() {
                        self.audit_logger.write_audit_log(
                            iap_email,
                            audit::INTERNAL_IAP,
                            Some(tenant_info.tenant_name.as_str()),
                            Some(audit::Resource::Blob {
                                blob_name: key.blob_name().to_string(),
                            }),
                            Some(format!(
                                "Find missing blobs for blob {:?} in tenant {}",
                                key, tenant_info.tenant_name
                            )),
                        )
                    }
                }

                tracing::info!(
                    "find_missing_blobs request blob_names {} tenant_id {}",
                    log_keys(blob_names.as_slice()),
                    tenant_id
                );
                let get_blob_infos_span = tracing::info_span!("get_blob_infos");
                let check_result = self
                    .object_store
                    .get_blob_infos(&request_context, &tenant_id, &blob_names)
                    .instrument(get_blob_infos_span)
                    .await?;

                let mut unknown_memory_names: Vec<String> = vec![];
                let (exists, not_exists): (Vec<_>, Vec<_>) =
                    check_result.into_iter().partition(|e| e.1.is_some());
                not_exists.into_iter().for_each(|(blob_key, _)| {
                    unknown_memory_names.push(blob_key.blob_name().to_string());
                });
                if !exists.is_empty() {
                    let validations: Vec<_> = exists
                        .into_iter()
                        .map(|(blob_key, blob_info)| {
                            let blob_info = blob_info.unwrap();
                            ObjectTransformationKeyInfo {
                                object_id: ObjectId::BlobName(blob_key.blob_name().clone()),
                                transformation_keys: blob_info
                                    .informed_transformation_keys
                                    .into_iter()
                                    .map(|e| e.transformation_key)
                                    .collect_vec(),
                            }
                        })
                        .collect();
                    let informed_keys = self
                        .queue_manager
                        .validate_notifications(
                            &tenant_id,
                            tenant_info.opaque_user_id.as_ref(),
                            &request_context.request_session_id(),
                            validations.as_slice(),
                            &feature_flags,
                            crate::queue_manager::BlobPriority::Low,
                        )
                        .await?;
                    self.object_store
                        .record_informed_keys(&request_context, &tenant_id, informed_keys)
                        .await?;
                }

                FIND_MISSING_UNKNOWN_BLOB_COUNT.observe(unknown_memory_names.len() as f64);

                let reply = FindMissingBlobResponse {
                    unknown_blob_names: unknown_memory_names,
                };
                Ok(Response::new(reply))
            },
            "find_missing_blobs",
        )
        .instrument(span)
        .await
    }

    /// a client wants to upload a set of sub_keys as transformed content of a given blob and transformation key
    async fn upload_transformed_blob_content(
        &self,
        request: Request<Streaming<UploadTransformedBlobContentRequest>>,
    ) -> Result<Response<UploadTransformedBlobContentResponse>, Status> {
        log_response_fn(|| async {
            let request_context = RequestContext::try_from(request.metadata())?;
            let tenant_info = tenant_info_from_grpc_req(&request)
                .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
            let span = tracing::info_span!("upload transformed content handler", request_id = %request_context.request_id());

            tenant_info.validate_scope(TokenScope::ContentRw)?;

            let mut in_stream = request.into_inner();

            let mut blob_name: Option<BlobName> = None;
            let mut transformation_key: Option<String> = None;
            let mut receipt_handle: Option<String> = None;
            let mut previous_tenant_id: Option<TenantId> = None;

            while let Some(result) = in_stream.next().instrument(span.clone()).await {
                match result {
                    Err(e) => return Err(e),
                    Ok(inner_request) => {
                        if blob_name.is_none() && inner_request.blob_name.is_empty() {
                            return Err(Status::invalid_argument("blob_name not set"));
                        }
                        // the transformation key is not allowed to change
                        if transformation_key.is_none()
                            && inner_request.transformation_key.is_empty()
                        {
                            return Err(Status::invalid_argument("transformation_key not set"));
                        }
                        blob_name = Some(BlobName::new(&inner_request.blob_name));
                        transformation_key = Some(inner_request.transformation_key.to_string());
                        if receipt_handle.is_none() {
                            receipt_handle = Some(inner_request.receipt_handle.to_string());
                        }

                        let tenant_id = self.get_tenant_id(&tenant_info.tenant_id, &inner_request.tenant_id.as_str().into())?;
                        if previous_tenant_id.is_none() {
                            previous_tenant_id = Some(tenant_id.clone());
                        } else if previous_tenant_id.as_ref().unwrap() != &tenant_id {
                            return Err(Status::invalid_argument("tenant_id must not change"));
                        }

                        if let Some(iap_email) = tenant_info.iap_email() {
                                self.audit_logger.write_audit_log(
                                    iap_email,
                                    audit::INTERNAL_IAP,
                                    Some(tenant_info.tenant_name.as_str()),
                                    Some(audit::Resource::Blob {
                                        blob_name: inner_request.blob_name.clone(),
                                    }),
                                    Some(format!(
                                        "Uploaded transformed content for blob {:?} in tenant {}",
                                        inner_request.blob_name, tenant_info.tenant_name
                                    )),
                                )
                        }

                        tracing::info!(
                            "upload_transformed_blob_content request blob_name={:?} tenant_id={:?} transformation_key={:?}",
                             inner_request.blob_name, tenant_id, inner_request.transformation_key
                        );

                        let uploads = inner_request.contents.into_iter().map(|e| {
                            let blob_key = BlobContentKey::Transformed(
                                BlobName::new(&inner_request.blob_name),
                                inner_request.transformation_key.to_string(),
                                e.sub_key.to_string(),
                            );
                            UploadContent {
                                blob_key,
                                content: SecretVec::new(e.content),
                                metadata: e.metadata
                                    .into_iter()
                                    .map(|m| (m.key, SecretString::new(m.value)))
                                    .collect_vec(),
                            }
                        }).collect();

                        self.object_store.batch_upload_blob(&request_context, &tenant_id, uploads, None).await?;
                    }
                }
            }
            if transformation_key.is_none() {
                return Err(Status::invalid_argument("no requests send"));
            }

             // if a notification was attached, mark the notification as done
            let span = tracing::info_span!("ack_notification");
            if let Some(receipt_handle) = &receipt_handle {
                if !receipt_handle.is_empty() {
                    self.queue_manager
                        .ack_notification( transformation_key.as_ref().unwrap(),
                         &[receipt_handle.as_str()])
                        .instrument(span)
                        .await?;
                }
            }

            let record_span = tracing::info_span!("record_uploaded_keys");
            self.object_store
                .record_uploaded_keys(
                    &request_context,
                    previous_tenant_id.as_ref().expect("previous_tenant_id must be set"),
                    vec![ObjectTransformationKeyInfo {
                    object_id: ObjectId::BlobName(blob_name.clone().unwrap()),
                    transformation_keys: vec![transformation_key.clone().unwrap()],
                }])
                .instrument(record_span)
                .await?;

            tracing::info!(
                "upload_transformed_blob_content finished: blob_name={:?} transformation_key={:?}, receipt_handle={:?}",
                blob_name,
                transformation_key,
                receipt_handle
            );

            let reply = UploadTransformedBlobContentResponse {};
            Ok(Response::new(reply))
        }, "upload_transformed_blob_content")
        .await
    }

    type SubscribeBlobUpdatesFlowControlledStream =
        ReceiverStream<Result<SubscribeBlobUpdatesResponse, Status>>;

    async fn subscribe_blob_updates_flow_controlled(
        &self,
        request: Request<Streaming<SubscribeBlobUpdatesRequest>>,
    ) -> Result<Response<Self::SubscribeBlobUpdatesFlowControlledStream>, Status> {
        log_response_fn(|| async {
            let request_context = RequestContext::try_from(request.metadata())?;
            let tenant_info = tenant_info_from_grpc_req(&request)
                .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
            if tenant_info.tenant_id.as_ref().unwrap_or(&EMPTY_TENANT_ID) != &EMPTY_TENANT_ID {
                // the caller needs namespace-scoped permissions
                tracing::warn!("tenant_id must not be set: {:?}", tenant_info);
                return Err(tonic::Status::permission_denied("tenant_id must not be set"));
            }

            tenant_info.validate_scope(TokenScope::ContentR)?;

            if let Some(iap_email) = tenant_info.iap_email() {
                self.audit_logger.write_audit_log(
                    iap_email,
                    audit::INTERNAL_IAP,
                    Some(tenant_info.tenant_name.as_str()),
                    None,
                    Some(format!(
                        "Subscribe to blob updates in tenant {}",
                        tenant_info.tenant_name
                    )),
                )
        }

            let mut request_stream = request.into_inner();
            let inner_request =
                match request_stream.message().await? {
                    Some(inner_request) => inner_request,
                    None => return Err(tonic::Status::invalid_argument("request must be set")),
                };

            let (tx_flow, rx_flow) = tokio::sync::mpsc::channel::<FlowControl>(1);

            if let Err(e) = tx_flow.send(FlowControl::Ack(inner_request.window)).await {
                panic!("subscribe_blob_updates_flow_controlled: error on flow control channel: {:?}", e);
            }

            tokio::spawn(async move {
                loop {
                    match request_stream.message().await {
                        Ok(Some(inner_request)) => {
                            if let Err(e) = tx_flow.send(FlowControl::Ack(inner_request.window)).await {
                                tracing::info!("subscribe_blob_updates_flow_controlled: error on flow control channel: {:?}", e);
                                return;
                            }
                        }
                        Ok(None) => {
                            tracing::info!("subscribe_blob_updates_flow_controlled: stop seen on stream");
                            drop(tx_flow);
                            return;
                        }
                        Err(e) => {
                            tracing::info!("subscribe_blob_updates_flow_controlled: error on stream: {:?}", e);
                            drop(tx_flow);
                            return;
                        }
                    }
                }
            });

            let flow_control = ReceiverStream::new(rx_flow);

            tracing::info!(
                "subscribe_blob_updates request transformation_key={:?}, request_id={:?}",
                inner_request.transformation_key, request_context.request_id()
            );

            let (tx, rx) = mpsc::channel::<Result<SubscribeBlobUpdatesResponse, Status>>(4);
            let object_store = self.object_store.clone();
            let queue_manager = self.queue_manager.clone();
            let transformation_key = inner_request.transformation_key;
            let feature_flags = self.feature_flags.clone();
            tokio::spawn(async move {
                let r = queue_manager.subscribe(
                    &transformation_key,
                    request_context.request_id(),
                    feature_flags,
                    |blob_notification| async {
                            match get_subscribe_response(
                                    object_store.clone(),
                                    queue_manager.clone(),
                                    &transformation_key,
                                    blob_notification,
                                    // NOTE: this context's token may expire at some point within
                                    // the stream's lifetime. We rely on the caller to restart the
                                    // stream if that happens.
                                    &request_context,
                                )
                                .await {
                                Ok(Some(resp)) => {
                                    if !send_and_ignore(&tx, Ok(resp), "subscribe_blob_updates").await {
                                        Err(SubscribeCallbackError::Stop)
                                    } else {
                                        Ok(1)
                                    }
                                },
                                Err(e) => {
                                    match e.code() {
                                        tonic::Code::Unauthenticated => {
                                            tracing::warn!(
                                                "subscribe_blob_updates error: transformation_key={:?} err={:?}, request_id={}",
                                                transformation_key,
                                                e,
                                                request_context.request_id(),
                                            );
                                        }
                                        _ => {
                                            tracing::error!(
                                                "subscribe_blob_updates error: transformation_key={:?} err={:?}, request_id={}",
                                                transformation_key,
                                                e,
                                                request_context.request_id(),
                                            );
                                        }
                                    }
                                    if !send_and_ignore(&tx, Err(e), "subscribe_blob_updates").await {
                                        Err(SubscribeCallbackError::Stop)
                                    } else {
                                        Err(SubscribeCallbackError::Err)
                                    }
                                },
                                // Tolerated error cases (blob gone or content is already there)
                                Ok(None) => {
                                    tracing::info!("subscribe_blob_updates: check: transformation_key={:?} request_id={}", transformation_key, request_context.request_id());
                                    if tx.is_closed() {
                                        Err(SubscribeCallbackError::Stop)
                                    } else {
                                        Ok(0)
                                    }
                                }
                            }
                        }, flow_control).await;
                tracing::info!("subscribe_blob_updates: done: transformation_key={:?} request_id={} result={:?}", transformation_key, request_context.request_id(), r);
                if let Err(e) = r {
                    send_and_ignore(&tx, Err(e), "subscribe_blob_updates").await;
                }
            });

            tokio::task::yield_now().await; // encourage the spawned task(s) to be scheduled
            Ok(Response::new(ReceiverStream::new(rx)))
        }, "subscribe_blob_updates")
        .await
    }

    type GetContentStream = ReceiverStream<Result<GetContentResponse, Status>>;

    /// return the raw or transformed content of a given blob
    async fn get_content(
        &self,
        request: Request<GetContentRequest>,
    ) -> Result<Response<Self::GetContentStream>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let span = tracing::info_span!("get content", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                // we will call record_informed_keys, so we need write access
                tenant_info.validate_scope(TokenScope::ContentRw)?;
                let inner_request = request.into_inner();
                let span = tracing::info_span!("get handler", blob_name = inner_request.blob_name);

                tracing::info!("get_content request {:?}", inner_request);

                if let Some(iap_email) = tenant_info.iap_email() {
                    self.audit_logger.write_audit_log(
                        iap_email,
                        audit::INTERNAL_IAP,
                        Some(tenant_info.tenant_name.as_str()),
                        Some(audit::Resource::Blob {
                            blob_name: inner_request.blob_name.clone(),
                        }),
                        Some(format!(
                            "Get content for blob {:?} in tenant {}",
                            inner_request.blob_name, tenant_info.tenant_name
                        )),
                    )
                }

                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &inner_request.tenant_id.as_str().into(),
                )?;

                let object_store = self.object_store.clone();
                let queue_manager = self.queue_manager.clone();
                let feature_flags = self.get_feature_flags(&tenant_info)?;
                let (tx, rx) = mpsc::channel::<Result<GetContentResponse, Status>>(4);
                tokio::spawn(
                    async move {
                        if inner_request.blob_name.is_empty() {
                            send_and_ignore(
                                &tx,
                                Err(Status::invalid_argument("blob_name not set")),
                                "get_content",
                            )
                            .await;
                            return;
                        }

                        let key: BlobContentKey = (
                            &inner_request.blob_name,
                            &inner_request.transformation_key,
                            &inner_request.sub_key,
                        )
                            .into();
                        let s = object_store
                            .get_blob_content(&request_context, &tenant_id, &key)
                            .await;
                        let (blob_info, content) = match s {
                            Err(status) => {
                                if let Err(status) = check_catchup(
                                    object_store.as_ref(),
                                    queue_manager.as_ref(),
                                    &request_context,
                                    tenant_info.opaque_user_id.as_ref(),
                                    &tenant_id,
                                    &[key],
                                    &feature_flags,
                                )
                                .await
                                {
                                    log_response_status(&status, "get_content/check_catchup");
                                }

                                log_response_status(&status, "get_content");
                                send_and_ignore(&tx, Err(status), "get_content").await;
                                return;
                            }
                            Ok(s) => s,
                        };

                        let resp = GetContentResponse {
                            content: content.to_vec(),
                            final_hash: blob_info.digest.to_string(),
                            metadata: blob_info
                                .metadata
                                .into_iter()
                                .map(|(k, v)| BlobMetadata {
                                    key: k,
                                    value: v.expose_secret().to_string(),
                                })
                                .collect(),
                        };
                        send_and_ignore(&tx, Ok(resp), "get_content").await;
                    }
                    .instrument(span),
                );
                Ok(Response::new(ReceiverStream::new(rx)))
            },
            "get_content",
        )
        .instrument(span)
        .await
    }

    type BatchGetContentStream = ReceiverStream<Result<BatchGetContentResponse, Status>>;

    async fn batch_get_content(
        &self,
        request: Request<BatchGetContentRequest>,
    ) -> Result<Response<Self::BatchGetContentStream>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let outer_span =
            tracing::info_span!("batched get content", request_id = %request_context.request_id());

        log_response_fn(|| async {
            // we will call record_informed_keys, so we need write access
            tenant_info.validate_scope(TokenScope::ContentRw)?;
            let inner_request = request.into_inner();
            let inner_span = tracing::info_span!("batched get handler", request_id = %request_context.request_id());

            let tenant_id = self.get_tenant_id(
                &tenant_info.tenant_id,
                &inner_request.tenant_id.as_str().into(),
            )?;
            let keys = inner_request
                .requests
                .into_iter()
                .map(|e| {
                    if e.transformation_key.is_empty() {
                        Raw(BlobName::new(&e.blob_name))
                    } else {
                        Transformed(BlobName::new(&e.blob_name), e.transformation_key, e.sub_key)
                    }
                })
                .collect_vec();

                if let Some(iap_email) = tenant_info.iap_email() {
                    for key in keys.iter() {
                    self.audit_logger.write_audit_log(
                        iap_email,
                        audit::INTERNAL_IAP,
                        Some(tenant_info.tenant_name.as_str()),
                        Some(audit::Resource::Blob {
                            blob_name: key.blob_name().to_string(),
                        }),
                        Some(format!(
                            "Get content for blob {:?} in tenant {}",
                            key, tenant_info.tenant_name
                        )),
                    )
                    }
                }

            tracing::info!(
                "batched get_content request {} tenant_id {}",
                log_keys(keys.as_slice()),
                tenant_id,
            );

            let object_store = self.object_store.clone();
            let queue_manager = self.queue_manager.clone();
            let feature_flags = self.get_feature_flags(&tenant_info)?;
            let (tx, rx) = mpsc::channel::<Result<BatchGetContentResponse, Status>>(4);
            tokio::spawn(
                async move {
                    let s = object_store
                        .batched_get_blob_content(&request_context, &tenant_id, keys)
                        .await;
                    let mut stream = match s {
                        Err(status) => {
                            log_response_status(&status, "batch_get_content");
                            send_and_ignore(&tx, Err(status), "batch_get_content").await;
                            return;
                        }
                        Ok(s) => s,
                    };

                    let mut content = BytesMut::with_capacity(2 * 1024 * 1024);

                    let mut unknown_blob_keys: Vec<BlobContentKey> = vec![];

                    loop {
                        tracing::debug!("received content: try_next");
                        let r = stream.try_next(&request_context, &tenant_id).await;
                        match r {
                            Err(e) => {
                                // try forwarding the error
                                log_response_status(&e, "batch_get_content/try_next");
                                send_and_ignore(&tx, Err(e), "batch_get_content").await;
                                break;
                            }
                            Ok(None) => {
                                tracing::debug!("received content eos");
                                break;
                            }
                            Ok(Some(FinalContent(key, info, bytes))) => {
                                tracing::debug!(
                                    "received final content: key={:?} info={:?} len={}",
                                    key,
                                    info,
                                    bytes.len()
                                );
                                // we reached the end of the stream.
                                let (blob_name, transformation_key, sub_key) = key.into();
                                content.extend_from_slice(&bytes);
                                let resp: BatchGetContentResponse = BatchGetContentResponse {
                                    response: Some(ProtoFinalContent(
                                        BatchGetContentFinalContent {
                                            content: content.to_vec(),
                                            final_hash: info.digest.to_string(),
                                            metadata: info
                                                .metadata
                                                .into_iter()
                                                .map(|(k, v)| BlobMetadata {
                                                    key: k,
                                                    value: v.expose_secret().to_string(),
                                                })
                                                .chain(std::iter::once(BlobMetadata {
                                                    key: "upload_time".to_string(),
                                                    value: info.time.to_rfc3339_opts(
                                                        chrono::SecondsFormat::Millis,
                                                        true,
                                                    ),
                                                }))
                                                .collect(),
                                            blob_name: blob_name.to_string(),
                                            transformation_key,
                                            sub_key,
                                        },
                                    )),
                                };
                                if !send_and_ignore(&tx, Ok(resp), "batch_get_content").await {
                                    break;
                                }
                                tracing::debug!("Finished get_content request",);
                                content.clear();
                            }
                            Ok(Some(UnknownBlob(key))) => {
                                tracing::debug!("received unknown blob: key={:?}", key);
                                unknown_blob_keys.push(key.clone());
                                let (blob_name, transformation_key, sub_key) = key.into();
                                let resp = BatchGetContentResponse {
                                    response: Some(ProtoNotFoundContent(BatchGetContentNotFound {
                                        blob_name: blob_name.to_string(),
                                        transformation_key,
                                        sub_key,
                                    })),
                                };
                                if !send_and_ignore(&tx, Ok(resp), "batch_get_content").await {
                                    break;
                                }
                            }
                        }
                    }
                    // at the end (regardless of error or eos), we will do catchup checks on the unknown keys
                    if !unknown_blob_keys.is_empty() {
                        tracing::info!(
                            "batch_get_content catchup check: unknown_blob_keys={}",
                            log_keys(unknown_blob_keys.as_slice())
                        );
                        if let Err(status) = check_catchup(
                            object_store.as_ref(),
                            queue_manager.as_ref(),
                            &request_context,
                            tenant_info.opaque_user_id.as_ref(),
                            &tenant_id,
                            unknown_blob_keys.as_slice(),
                            &feature_flags,
                        )
                        .await
                        {
                            log_response_status(&status, "batch_get_content/check_catchup");
                        }
                    }
                }
                .instrument(inner_span),
            );
            Ok(Response::new(ReceiverStream::new(rx)))
        }, "batch_get_content")
        .instrument(outer_span)
        .await
    }

    type GetAllBlobsFromCheckpointStream =
        ReceiverStream<Result<GetAllBlobsFromCheckpointResponse, Status>>;

    async fn get_all_blobs_from_checkpoint(
        &self,
        request: Request<GetAllBlobsFromCheckpointRequest>,
    ) -> Result<Response<Self::GetAllBlobsFromCheckpointStream>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let span = tracing::info_span!("get_all_blobs_from_checkpoint", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentR)?;
                let inner_request = request.into_inner();
                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &inner_request.tenant_id.as_str().into(),
                )?;

                let checkpoint = inner_request.checkpoint_id;

                if let Some(iap_email) = tenant_info.iap_email() {
                    self.audit_logger.write_audit_log(
                        iap_email,
                        audit::INTERNAL_IAP,
                        Some(tenant_info.tenant_name.as_str()),
                        None,
                        Some(format!(
                            "Get all blobs from checkpoint {:?} in tenant {}",
                            checkpoint, tenant_info.tenant_name
                        )),
                    )
                }

                let object_store = self.object_store.clone();

                let (tx, rx) =
                    mpsc::channel::<Result<GetAllBlobsFromCheckpointResponse, Status>>(4);
                tokio::spawn(async move {
                    let all_blob_names = if !checkpoint.is_empty() {
                        match object_store
                            .get_blobs_from_checkpoint(
                                &request_context,
                                &tenant_id,
                                &CheckpointId::from(checkpoint),
                            )
                            .await
                        {
                            Err(status) => {
                                log_response_status(&status, "get_all_blobs_from_checkpoint");
                                send_and_ignore(&tx, Err(status), "get_all_blobs_from_checkpoint")
                                    .await;
                                return;
                            }
                            Ok(blob_names) => blob_names,
                        }
                    } else {
                        SortedBlobNameBytesVec::default()
                    };

                    // Split the blob names into chunks, to keep each response message size under under 4MB.
                    // 16K blob names (each 32 bytes) is approximately 512KB
                    let chunk_size = 16384;
                    let chunks = all_blob_names.as_vec().chunks(chunk_size);

                    // Respond with each chunk-sized message
                    for chunk in chunks {
                        let blob_names = chunk.to_vec();
                        let reply = GetAllBlobsFromCheckpointResponse { blob_names };
                        if !send_and_ignore(&tx, Ok(reply), "get_all_blobs_from_checkpoint").await {
                            break;
                        }
                    }
                });
                Ok(Response::new(ReceiverStream::new(rx)))
            },
            "get_all_blobs_from_checkpoint",
        )
        .instrument(span)
        .await
    }

    async fn checkpoint_blobs(
        &self,
        request: Request<CheckpointBlobsRequest>,
    ) -> Result<Response<CheckpointBlobsResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let span =
            tracing::info_span!("checkpoint blobs", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentRw)?;
                let inner_request = request.into_inner();

                if let Some(iap_email) = tenant_info.iap_email() {
                    self.audit_logger.write_audit_log(
                        iap_email,
                        audit::INTERNAL_IAP,
                        Some(tenant_info.tenant_name.as_str()),
                        None,
                        Some(format!(
                            "Create checkpoint in tenant {}",
                            tenant_info.tenant_name
                        )),
                    )
                }

                let blobs = match inner_request.blobs {
                    Some(d) => d,
                    None => return Err(tonic::Status::invalid_argument("blobs must be provided")),
                };

                let tenant_id =
                    self.get_tenant_id(&tenant_info.tenant_id, &inner_request.tenant_id.into())?;
                let baseline_checkpoint_id =
                    blobs.baseline_checkpoint_id.clone().unwrap_or_default();
                let added_blobs_count = blobs.added.len() as u32;
                let deleted_blobs_count = blobs.deleted.len() as u32;

                let results = self
                    .object_store
                    .checkpoint_blobs(&request_context, &tenant_id, blobs)
                    .await?;

                // Publish request insight event
                self.publish_checkpoint_blobs_ri_event(
                    &request_context,
                    &tenant_info,
                    results.to_string(),
                    baseline_checkpoint_id,
                    added_blobs_count,
                    deleted_blobs_count,
                )
                .await;

                let reply = CheckpointBlobsResponse {
                    checkpoint_id: results.to_string(),
                };
                Ok(Response::new(reply))
            },
            "checkpoint_blobs",
        )
        .instrument(span)
        .await
    }

    async fn get_user_blobs(
        &self,
        request: Request<GetUserBlobsRequest>,
    ) -> Result<Response<GetUserBlobsResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let span =
            tracing::info_span!("get_user_blobs", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentAdmin)?;
                let inner_request = request.into_inner();
                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &inner_request.tenant_id.as_str().into(),
                )?;
                let user_id = SecretString::new(inner_request.user_id);
                let limit = inner_request.limit.map(|limit32| limit32 as i64);
                let min_timestamp = inner_request.min_timestamp;
                let max_timestamp = inner_request.max_timestamp;

                if let Some(iap_email) = tenant_info.iap_email() {
                    self.audit_logger.write_audit_log(
                        iap_email,
                        audit::INTERNAL_IAP,
                        Some(tenant_info.tenant_name.as_str()),
                        None,
                        Some(format!(
                            "Get user blobs for user in tenant {}",
                            tenant_info.tenant_name
                        )),
                    )
                }

                let upload_infos = self
                    .object_store
                    .get_blobs_from_user(
                        &request_context,
                        &tenant_id,
                        &user_id,
                        limit,
                        min_timestamp,
                        max_timestamp,
                    )
                    .await?;

                let entries: Vec<UploadInfo> = upload_infos
                    .into_iter()
                    .map(|(blob_name, time)| UploadInfo { blob_name, time })
                    .collect();

                let reply = GetUserBlobsResponse { entries };
                Ok(Response::new(reply))
            },
            "get_user_blobs",
        )
        .instrument(span)
        .await
    }

    async fn get_best_ann_index(
        &self,
        request: Request<GetBestAnnIndexRequest>,
    ) -> Result<Response<GetBestAnnIndexResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;

        let span =
            tracing::info_span!("get_best_ann_index", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentR)?;
                let inner_request = request.into_inner();
                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &inner_request.tenant_id.unwrap_or("".into()).into(),
                )?;
                let mappings = self
                    .object_store
                    .get_ann_index_mappings(
                        &request_context,
                        &tenant_id,
                        &AnnIndexMappingKey {
                            transformation_key: inner_request.transformation_key,
                            checkpoint_id: inner_request.checkpoint_id.into(),
                        },
                    )
                    .await?;
                mappings
                    .into_iter()
                    .fold(None, |best: Option<AnnIndexMapping>, mapping| match best {
                        Some(prev_best) => {
                            // pick lowest delta count, breaking ties with newer timestamp,
                            // breaking further ties by earlier insertion order (which will be
                            // pretty much random given that insertion order is determined by
                            // index_id which is assumed to be reasonably unique and random)
                            let prev_best_delta_cnt =
                                prev_best.added_blobs.len() + prev_best.removed_blobs.len();
                            let delta_cnt = mapping.added_blobs.len() + mapping.removed_blobs.len();
                            if delta_cnt < prev_best_delta_cnt
                                || delta_cnt == prev_best_delta_cnt
                                    && mapping.timestamp > prev_best.timestamp
                            {
                                Some(mapping)
                            } else {
                                Some(prev_best)
                            }
                        }
                        None => Some(mapping),
                    })
                    .map(|best| {
                        let added_blobs = best
                            .added_blobs
                            .into_iter()
                            .map(|blob_name| blob_name.as_bytes().to_vec())
                            .collect_vec();
                        let removed_blobs = best
                            .removed_blobs
                            .into_iter()
                            .map(|blob_name| blob_name.as_bytes().to_vec())
                            .collect_vec();
                        Response::new(GetBestAnnIndexResponse {
                            index_id: best.index_id,
                            added_blobs,
                            removed_blobs,
                        })
                    })
                    .ok_or_else(|| tonic::Status::not_found("No index entry found for query"))
            },
            "get_best_ann_index",
        )
        .instrument(span)
        .await
    }

    type GetAnnIndexBlobInfosStream = ReceiverStream<Result<GetAnnIndexBlobInfosResponse, Status>>;
    async fn get_ann_index_blob_infos(
        &self,
        request: Request<GetAnnIndexBlobInfosRequest>,
    ) -> Result<Response<Self::GetAnnIndexBlobInfosStream>, Status> {
        // extra space for info attribs
        const INFOS_PER_MESSAGE: usize = MAX_MESSAGE_SIZE / (blob_names::BLOB_DIGEST_LEN + 4);
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;

        let span = tracing::info_span!("get_ann_index_blob_infos", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentR)?;
                let inner_request = request.into_inner();
                let key = inner_request
                    .key
                    .ok_or_else(|| tonic::Status::internal("'key' must be set in request"))?;
                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &key.tenant_id.unwrap_or("".into()).into(),
                )?;
                let (tx, rx) = mpsc::channel::<tonic::Result<GetAnnIndexBlobInfosResponse>>(4);
                let object_store = self.object_store.clone();
                tracing::info!(
                    "get_ann_index_blob_infos transformation_key={} index_id={} starting",
                    key.transformation_key,
                    key.index_id
                );

                tokio::spawn(async move {
                    let blob_infos = object_store
                        .get_ann_index_blob_infos(
                            &request_context,
                            &tenant_id,
                            &key.transformation_key,
                            &key.index_id,
                        )
                        .await;
                    match blob_infos {
                        Ok(blob_infos) => {
                            tracing::info!(
                                "get_ann_index_blob_infos transformation_key={} index_id={} got {} blob_infos",
                                key.transformation_key,
                                key.index_id,
                                blob_infos.len()
                            );
                            let chunk_reqs = blob_infos
                                .chunks(INFOS_PER_MESSAGE)
                                .map(|chunk| {
                                    let blob_infos = chunk
                                        .iter()
                                        .map(|blob_info| AnnIndexBlobInfo {
                                            blob_name: blob_info.blob_name.as_bytes().to_vec(),
                                            chunk_count: blob_info.chunk_count,
                                        })
                                        .collect_vec();
                                    GetAnnIndexBlobInfosResponse {
                                        blob_infos,
                                    }
                                });
                            // not particularly important that these send in order, but we'll do it
                            // sequentially for simplicity, as the performance gain from sending
                            // concurrently is limited (we expect that the time it takes to
                            // chunk-split and serialize is negligible compared to network latency).
                            for chunk in chunk_reqs {
                                send_and_ignore(&tx, Ok(chunk), "get_ann_index_blob_infos").await;
                            }
                        }
                        Err(status) => {
                            send_and_ignore(&tx, Err(status), "get_ann_index_blob_infos").await;
                        }
                    }
                    tracing::info!(
                        "get_ann_index_blob_infos transformation_key={} index_id={} finished",
                        key.transformation_key,
                        key.index_id
                    );
                });

                Ok(Response::new(ReceiverStream::new(rx)))
            },
            "get_ann_index_blob_infos",
        )
        .instrument(span)
        .await
    }

    type GetAnnIndexAssetStream = ReceiverStream<Result<GetAnnIndexAssetResponse, Status>>;
    async fn get_ann_index_asset(
        &self,
        request: Request<GetAnnIndexAssetRequest>,
    ) -> Result<Response<Self::GetAnnIndexAssetStream>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let inner_request = request.into_inner();
        let key = inner_request
            .key
            .ok_or_else(|| tonic::Status::internal("'key' must be set in request"))?;
        let tenant_id = self.get_tenant_id(
            &tenant_info.tenant_id,
            &key.tenant_id.unwrap_or("".into()).into(),
        )?;
        let span =
            tracing::info_span!("get_ann_index_asset", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentR)?;

                let transformation_key = key.transformation_key;
                let index_id = key.index_id;
                let sub_key = key.sub_key;

                let (tx, rx) = mpsc::channel::<Result<GetAnnIndexAssetResponse, Status>>(4);
                let object_store = self.object_store.clone();

                tokio::spawn(async move {
                    let ann_index_asset_key = AnnIndexAssetKey {
                        index_id,
                        transformation_key,
                        sub_key,
                    };
                    match object_store
                        .get_ann_index_asset(&request_context, &tenant_id, &ann_index_asset_key)
                        .await
                    {
                        Ok(mut stream) => loop {
                            match stream.try_next(&request_context, &tenant_id).await {
                                Ok(Some(data)) => {
                                    for chunk in data.chunks(MAX_MESSAGE_SIZE) {
                                        let reply = GetAnnIndexAssetResponse {
                                            data: chunk.to_vec(),
                                        };
                                        send_and_ignore(&tx, Ok(reply), "get_ann_index_asset")
                                            .await;
                                    }
                                }
                                Ok(None) => {
                                    break;
                                }
                                Err(status) => {
                                    log_response_status(&status, "get_ann_index_asset");
                                    send_and_ignore(&tx, Err(status), "get_ann_index_asset").await;
                                    break;
                                }
                            }
                        },
                        Err(status) => {
                            log_response_status(&status, "get_ann_index_asset");
                            send_and_ignore(&tx, Err(status), "get_ann_index_asset").await;
                        }
                    }
                });
                Ok(Response::new(ReceiverStream::new(rx)))
            },
            "get_ann_index_asset",
        )
        .instrument(span)
        .await
    }

    async fn add_ann_index_mapping(
        &self,
        request: Request<AddAnnIndexMappingRequest>,
    ) -> Result<Response<AddAnnIndexMappingResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;

        let span = tracing::info_span!("add_ann_index_mapping", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentRw)?;
                let inner_request = request.into_inner();
                let request_key = inner_request
                    .key
                    .ok_or_else(|| tonic::Status::internal("key must be set on request"))?;
                let tenant_id = self.get_tenant_id(
                    &tenant_info.tenant_id,
                    &request_key.tenant_id.unwrap_or("".into()).into(),
                )?;
                let added_blobs = inner_request
                    .added_blobs
                    .into_iter()
                    .map(|blob_bytes| blob_names::BlobName::from_bytes(blob_bytes.as_slice()))
                    .collect::<Result<Vec<_>, _>>()?;
                let removed_blobs = inner_request
                    .removed_blobs
                    .into_iter()
                    .map(|blob_bytes| blob_names::BlobName::from_bytes(blob_bytes.as_slice()))
                    .collect::<Result<Vec<_>, _>>()?;
                tracing::info!(
                    "Adding ann index mapping: checkpoint {}, index_id {}, added {:?}, removed {:?}",
                    request_key.checkpoint_id,
                    inner_request.index_id,
                    added_blobs,
                    removed_blobs
                );
                let key = AnnIndexMappingKey {
                    transformation_key: request_key.transformation_key,
                    checkpoint_id: request_key.checkpoint_id.into(),
                };
                let value = AnnIndexMappingValue {
                    index_id: inner_request.index_id,
                    added_blobs,
                    removed_blobs,
                };
                self.object_store
                    .set_ann_index_mapping(&request_context, &tenant_id, &key, value)
                    .await?;
                Ok(Response::new(AddAnnIndexMappingResponse {}))
            },
            "add_ann_index_mapping",
        )
        .instrument(span)
        .await
    }

    async fn upload_ann_index_blob_infos(
        &self,
        request: Request<Streaming<UploadAnnIndexBlobInfosRequest>>,
    ) -> Result<Response<UploadAnnIndexBlobInfosResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let mut tenant_id = tenant_info.tenant_id.clone();

        let span = tracing::info_span!("upload_ann_index_blob_infos", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentRw)?;
                let mut stream = request.into_inner();
                let mut got_last = false;
                let mut payload: Option<UploadAnnIndexBlobInfosPayload> = None;
                tracing::info!("upload_ann_index_blob_infos starting");

                loop {
                    let message = stream.message().await?;
                    match (message, got_last) {
                        (Some(req), false) => {
                            got_last = req.last_message;
                            let mut blob_infos = req
                                .blob_infos
                                .into_iter()
                                .map(|blob_info| {
                                    let blob_name = blob_names::BlobName::from_bytes(
                                        blob_info.blob_name.as_slice(),
                                    )?;
                                    Ok(AnnIndexBlobInfoValue {
                                        blob_name,
                                        chunk_count: blob_info.chunk_count,
                                    })
                                })
                                .collect::<tonic::Result<Vec<_>>>()?;
                            match (&mut payload, req.key) {
                                (Some(_), Some(_)) => {
                                    return Err(tonic::Status::internal(
                                        "key should only be set on first message of req",
                                    ))
                                }
                                (Some(ref mut payload_ref), None) => {
                                    payload_ref.blob_infos.append(&mut blob_infos);
                                }
                                (None, Some(key)) => {
                                    if let Some(tid) = key.tenant_id {
                                        tenant_id = Some(tid.into());
                                    }
                                    payload = Some(UploadAnnIndexBlobInfosPayload {
                                        transformation_key: key.transformation_key,
                                        index_id: key.index_id,
                                        blob_infos,
                                    });
                                }
                                (None, None) => {
                                    return Err(tonic::Status::internal(
                                        "First request message must set 'key'",
                                    ))
                                }
                            }
                        }
                        (Some(req), true) => {
                            tracing::error!("Received request after last_message flag: {:?}", req);
                            return Err(tonic::Status::internal("request after last_message"));
                        }
                        (None, false) => {
                            return Err(tonic::Status::internal("EOS without last_message"));
                        }
                        (None, true) => {
                            break;
                        }
                    }
                }
                // validations
                let tenant_id = tenant_id.ok_or_else(|| {
                    tonic::Status::internal("tenant_id must be set in response or token")
                })?;
                let payload =
                    payload.ok_or_else(|| tonic::Status::internal("empty stream received"))?;
                // mutate
                tracing::info!(
                    "upload_ann_index_blob_infos writing {} blob_infos",
                    payload.blob_infos.len()
                );
                self.object_store
                    .upload_ann_index_blob_infos(&request_context, &tenant_id, payload)
                    .await?;
                Ok(Response::new(UploadAnnIndexBlobInfosResponse {}))
            },
            "upload_ann_index_blob_infos",
        )
        .instrument(span)
        .await
    }

    async fn upload_ann_index_assets(
        &self,
        request: Request<Streaming<UploadAnnIndexAssetsRequest>>,
    ) -> Result<Response<UploadAnnIndexAssetsResponse>, Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        tenant_info.validate_scope(TokenScope::ContentRw)?;
        let mut tenant_id = tenant_info.tenant_id.clone().unwrap();

        let span = tracing::info_span!("upload_ann_index_assets", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                let mut stream = request.into_inner();
                let mut current_asset: Option<UploadAnnIndexAsset> = None;
                while let Some(message) = stream.message().await? {
                    match current_asset {
                        Some(ref mut current_asset) => {
                            if let Some(key) = message.key {
                                debug_assert!(current_asset.key.sub_key != key.sub_key);
                                debug_assert!(current_asset.key.index_id == key.index_id);
                                debug_assert!(
                                    current_asset.key.transformation_key == key.transformation_key
                                );
                                if let Some(tid) = key.tenant_id {
                                    debug_assert!(tenant_id == TenantId::from(tid));
                                }

                                let upload_asset = std::mem::replace(
                                    current_asset,
                                    UploadAnnIndexAsset {
                                        key: AnnIndexAssetKey {
                                            index_id: key.index_id,
                                            transformation_key: key.transformation_key,
                                            sub_key: key.sub_key,
                                        },
                                        data: message.data,
                                    },
                                );
                                self.object_store
                                    .upload_ann_index_asset(
                                        &request_context,
                                        &tenant_id,
                                        upload_asset,
                                    )
                                    .await?;
                            } else {
                                current_asset.data.extend(message.data);
                            }
                        }
                        None => {
                            let key = message.key.ok_or_else(|| {
                                tonic::Status::invalid_argument("key must be set on first message")
                            })?;
                            if let Some(tid) = key.tenant_id {
                                tenant_id = TenantId::from(tid);
                            }
                            current_asset = Some(UploadAnnIndexAsset {
                                key: AnnIndexAssetKey {
                                    index_id: key.index_id,
                                    transformation_key: key.transformation_key,
                                    sub_key: key.sub_key,
                                },
                                data: message.data,
                            });
                        }
                    }
                    if message.last_message {
                        if let Some(current_asset) = current_asset {
                            self.object_store
                                .upload_ann_index_asset(&request_context, &tenant_id, current_asset)
                                .await?;
                        }
                        break;
                    }
                }
                Ok(Response::new(UploadAnnIndexAssetsResponse {}))
            },
            "upload_ann_index_assets",
        )
        .instrument(span)
        .await
    }
}
