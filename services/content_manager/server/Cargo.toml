[package]
name = "content_manager"
version = "0.1.0"
edition = "2021"

[dependencies]
audit = { path = "../../../base/logging/audit" }
async-lock = { workspace = true }
async-rwlock = { workspace = true }
async-trait = { workspace = true }
async-std = { workspace = true }
async-channel = { workspace = true }
base64 = { workspace = true }
bigtable_proxy_client = { path = "../../bigtable_proxy/client" }
blob_names = { path = "../../../base/blob_names/rust" }
blob_names_rs_proto = { path = "../../../base/blob_names" }
bytes = { workspace = true }
chrono = { workspace = true }
clap = { workspace = true }
content_manager_rs_proto = { path = ".." }
feature-flags = { path = "../../../base/feature_flags" }
futures = { workspace = true }
futures-buffered = { workspace = true }
google-cloud-auth = { workspace = true }
google-cloud-gax = { workspace = true }
google-cloud-googleapis = { workspace = true }
google-cloud-pubsub = { workspace = true }
grpc_auth = { path = "../../lib/grpc/auth" }
grpc_metrics = { path = "../../lib/grpc/metrics" }
grpc_tls_config = { path = "../../lib/grpc/tls_config" }
hex = { workspace = true }
hex-literal = { workspace = true }
http = { workspace = true }
hyper = { workspace = true }
itertools = { workspace = true }
k8s-openapi = { workspace = true }
kube = { workspace = true }
lazy_static = { workspace = true }
metrics-server = { path = "../../../base/metrics_server/rust" }
moka = { workspace = true }
multimap = { workspace = true }
rand = { workspace = true }
pin-project = { workspace = true }
prometheus = { workspace = true }
prost = { workspace = true }
prost-types = { workspace = true }
prost-wkt = {workspace = true}
prost-wkt-types = {workspace = true}
schemars = { workspace = true }
secrecy = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
sha2 = { workspace = true }
sha256 = { workspace = true }
struct_logging = { path = "../../../base/logging" }
token_exchange_client = { path = "../../token_exchange/client" }
tokio = { workspace = true }
tokio-metrics-collector = { workspace = true }
tokio-stream = { workspace = true }
tonic = { workspace = true, features = ["tls", "gzip"] }
tonic-build = { workspace = true }
tonic-health = { workspace = true }
tonic-reflection = { workspace = true }
tower = { workspace = true }
tracing = { workspace = true }
tracing-tonic = { path = "../../../base/rust/tracing-tonic" }
tokenbucket = { workspace = true }
request_context = { path = "../../lib/request_context" }
uuid = { workspace = true }
regex = { workspace = true }
grpc_service = { path = "../../lib/grpc/service" }
request_insight_publisher = { path = "../../request_insight/publisher" }

[dev-dependencies]
actix-rt = { workspace = true }
assert_unordered = { workspace = true }
tonic-build = { workspace = true }

[build-dependencies]
tonic-build = { workspace = true }
