load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:rust.bzl", "rust_binary", "rust_oci_image", "rust_test")

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cluster_wide = True,
)

proto_library(
    name = "content_manager_store_proto",
    srcs = ["content_manager_store.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/content_manager:content_manager_proto",
        "@protobuf//:timestamp_proto",
    ],
)

rust_binary(
    name = "content_manager_server",
    srcs = glob(["src/**/*.rs"]),
    aliases = aliases(),
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    rustc_flags = [
        "--cfg",
        "tokio_unstable",
    ],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/blob_names:blob_names_rs_proto",
        "//base/blob_names/rust:blob_names",
        "//base/feature_flags:feature_flags_rs",
        "//base/logging:struct_logging_rs",
        "//base/logging/audit:audit_rs",
        "//base/metrics_server/rust:metrics_server",
        "//base/rust/tracing-tonic",
        "//services/bigtable_proxy/client:client_rs",
        "//services/content_manager:content_manager_rs_proto",
        "//services/lib/grpc/auth:grpc_auth",
        "//services/lib/grpc/metrics:grpc_metrics",
        "//services/lib/grpc/service:grpc_service",
        "//services/lib/grpc/tls_config:grpc_tls_config_rs",
        "//services/lib/request_context:request_context_rs",
        "//services/request_insight/publisher:publisher_rs",
        "//services/token_exchange/client:client_rs",
        "//third_party/bigtable_rs",
    ],
)

rust_oci_image(
    name = "image",
    package_name = package_name(),
    binary = "content_manager_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = ["//services:__subpackages__"],
)

rust_test(
    name = "content_manager_server_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":content_manager_server",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        ":content_manager_store_proto",
        "@protobuf//:any_proto",
        "@protobuf//:protoc",
        "@protobuf//:timestamp_proto",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//deploy/tenants:namespaces",
        "//services/deploy:endpoints",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_shared",
        ":monitoring_kubecfg",
        "//deploy/tenants:namespaces",
    ],
)
