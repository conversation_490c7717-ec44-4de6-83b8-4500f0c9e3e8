"""Inference server.

This server accepts requests to predict continuations of token sequences.

Initial design doc: https://www.notion.so/Inference-Server-Design-Proposal-d27484c83ec04f8a9de999a972b2eb2a

The goal of this design is to do both contexts processing and decoding at the
same time, while offering low latency and keeping the FLOPS utilization
reasonably high. The design here is mostly for small deployment sizes, where
we cannot afford to have two sets of GPUs for the two phases, and we do not
want to pause decoding while a new context has to be encoded.

Batching:  We use a variant of "continuous batching", probably most similar to
"selective batching". The server operates in rounds (~batches) of `round_size`
(e.g. 128) tokens. The tokens for a round may include tokens for requests that
are in the decoding phase and requests that are in the context processing
phase. So the batch dimension and the sequence length dimensions are flattened
and the requests are concatenated, and we ensure that the total length of the
requets in a round does not exceed `round_size`.

Continuous batching: https://www.anyscale.com/blog/continuous-batching-llm-inference
Selective batching: https://www.usenix.org/system/files/osdi22-yu.pdf

Slide deck illustrating the batching algorithm:
https://docs.google.com/presentation/d/16LjuWeND_sQW5fTmmkDckxSJNn5AOw_wtF8MKCsCXOo/edit#slide=id.p

To enable this batching strategy, the model implementations are split out into
a stateless model forward pass, which manages all the parameters and the
stateful attention module which does not handle any parameters. The
BasicAttention module encapsulates the KV cache for a single sequence and
offers to __call__ method, which receives the qkv tensor and returns the
attention result (and it updates its internal KV cache state). To batch
multiple requests into a round, we wrap their BasicAttention modules with a
RoundAttention object. When RoundAttention is called with a qkv tensor, it
splits the tensor along the batch dimension and calls the respective
BasicAttention modules.

Caching:  TODO(markus)

Concurrency:  The server has exactly one worker thread that continuously tries
to build batches, feed them to the language model, and update the requests
involved in the batch. Incoming requests are assumed to be called in individual
threads, calling the `callback_predict` method, which returns a future
so that the thread can wait for the result if it wants to. Other than that
there are no additional python threads spawned in this implementation to avoid
bottlenecks due to the GIL. Please keep it that way as far as possible and
talk to other people when making any changes in that regard.

A call to `callback_predict` creates a `Request` object (or returns
immediately) and enqueues it for processing. The request contains a lock,
which allows the incoming threads to wait for the result without a busy wait
loop.

To ensure that incoming threads and the worker thread do not interfere with
another, we have one lock that is held whenever the request queue is modified.
"""

import dataclasses
import functools
import hashlib
import logging
import os
import sys
import threading
import time
import traceback
from collections import OrderedDict, defaultdict
from typing import Callable, Dict, Iterable, List, Optional, Sequence, Tuple, Union

import grpc
import opentelemetry.context
import opentelemetry.trace
import structlog
import torch
from prometheus_client import Counter, Histogram

from base.fastforward import batching, fwd
from base.prometheus import sampled_histogram
from services.inference_host.server.continuous_batching import (
    generation_state,
    longest_overlap_lm,
    pool,
    speculation,
)
from services.inference_host.server.continuous_batching.inference_runner import (
    HashedUserID,
    InferenceException,
    InferenceResult,
    InferenceRunner,
    SamplingParams,
    SessionID,
    TenantId,
    Token,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext

# A tuple of (source_namespace, sha(user_id)). Used to identify a user
# of the inference runner.
# The user_id is PII, so we hash it to be sure we don't accidentally log it.
InferenceUserID = Tuple[TenantId, HashedUserID]

tracer = opentelemetry.trace.get_tracer(__name__)

_MAGIC_SEPARATOR = "######"
MAX_REQUEST_QUEUE_LEN = 1_000
INF = float("inf")
# Exponentially increasing buckets with ~20% width. Min is ~10ms and max is ~20s.
_LATENCY_BUCKETS = tuple((1.2**i - 1) / 25.0 for i in range(36)) + (INF,)
# NOTE: We are using "seconds" as the base unit for all latency metrics
# following the official guidelines  https://prometheus.io/docs/practices/naming/#base-units

# It's useful to specify an order since the same code writes most of these metrics and the order of
# arguments has to match how the labels are specified. See update_metrics.
COMMON_LABELS = ["status_code", "model_name", "request_source"]

_MAX_REQUESTS_PER_USER = 100

_request_counter = Counter(
    "au_inference_host_request",
    "Metrics about request counts in the batching inference host",
    COMMON_LABELS + ["source_namespace", "tenant_name"],
)
_request_latency_s = Histogram(
    "au_inference_host_request_latency_seconds",
    "Latency metrics about request counts in the batching inference host",
    COMMON_LABELS + ["source_namespace", "tenant_name"],
    buckets=_LATENCY_BUCKETS,
)
_requests_in_round_histogram = Histogram(
    "au_inference_host_round_size",
    "Histogram for the number of requests in a round",
    # has no namespace label as a round can include requests from multiple namespaces
    ["model_name", "model_id"],
    buckets=[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 20, 25, 30, 35, 40, 45, 50, INF],
)
_tokens_in_round_histogram = Histogram(
    "au_inference_host_tokens_in_round",
    "Histogram for the number of tokens in a round",
    # has no namespace label as a round can include tokens from multiple namespaces
    ["model_name", "model_id"],
    buckets=[1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, INF],
)
_round_capped_by_rir_limit = Counter(
    "au_inference_host_round_capped_by_rir_limit",
    "Number of rounds capped by the number of requests in a round",
    ["model_name"],
)
_round_latency_s = Histogram(
    "au_inference_host_round_latency_seconds",
    "Latency metrics about request counts in the batching inference host",
    [
        # Note the large number of labels - keep the cardinality of each metrics low.
        "model_name",
        "model_id",
        "requests_in_round",
        "tokens_in_round",
    ],
    buckets=_LATENCY_BUCKETS,
)
_context_processing_latency_v2_s = Histogram(
    "au_inference_host_context_processing_latency_v2_seconds",
    "Latency metrics about processing incoming prompt (generation is not included)",
    COMMON_LABELS,
    buckets=_LATENCY_BUCKETS,
)
_generation_latency_v2_s = Histogram(
    "au_inference_host_generation_latency_v2_seconds",
    "Latency metrics about generating tokens (processing incoming prompt is not included)",
    COMMON_LABELS,
    buckets=_LATENCY_BUCKETS,
)
_speculated_token_counter = Counter(
    "au_inference_host_speculated_token_processed",
    "Metrics about speculated tokens processed",
    ["model_name", "request_source", "source_namespace", "tenant_name"],
)
_speculated_token_accepted_counter = Counter(
    "au_inference_host_speculated_token_accepted",
    "Number of speculated tokens accepted",
    ["model_name", "request_source", "source_namespace", "tenant_name"],
)
_active_requests_sampled_histogram = sampled_histogram.SampledHistogram(
    "au_inference_host_active_requests",
    description="Histogram of active requests per model",
    interval_seconds=1.0,
    labels=["model_name"],
    buckets=tuple(range(20)),
)
_processed_prompt_token_counter = Counter(
    "au_inference_host_processed_prompt_token",
    "Number of prompt tokens processed",
    ["model_name", "request_source", "source_namespace", "tenant_name"],
)
_generated_tokens_per_round_histogram = Histogram(
    "au_inference_host_generated_tokens_per_round",
    "Number of tokens generated",
    ["model_name", "request_source", "model_id"],
    buckets=[
        0,
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        10,
        12,
        14,
        16,
        20,
        24,
        28,
        32,
        40,
        48,
        56,
        64,
        INF,
    ],
)
_wasted_tokens_counter = Counter(
    "au_inference_host_wasted_tokens",
    "Number of tokens wasted",
    ["model_name", "request_source", "model_id"],
)

# The following statistics about the generation process are computed on the per-request level.
_request_level_latency_per_token_s = Histogram(
    "au_inference_host_request_level_latency_per_token_seconds",
    "Request latency normalized by the number of generated tokens",
    COMMON_LABELS,
    # buckets ranging from 0.2ms to 60ms
    buckets=[(1.3**i) / 6000.0 for i in range(1, 24)] + [INF],
)
_request_level_speculated_tokens_per_round = Histogram(
    "au_inference_host_request_level_speculated_tokens_per_round",
    "Number of proposed speculated tokens per single generation round on the per-request level (averaged within a request)",
    COMMON_LABELS,
    buckets=[0, 1, 2, 3, 4, 6, 8, 10, 12, 16, 20, 24, 32, 40, 48, 64] + [INF],
)
_request_level_accepted_speculated_tokens_per_round = Histogram(
    "au_inference_host_request_level_accepted_speculated_tokens_per_round",
    "Number of accepted speculated tokens per single generation round  on the per-request level (averaged within a request)",
    COMMON_LABELS,
    buckets=[0, 1, 2, 3, 4, 6, 8, 10, 12, 16, 20, 24, 32, 40, 48, 64] + [INF],
)
_request_level_speculated_tokens_fraction = Histogram(
    "au_inference_host_request_level_speculated_tokens_fraction",
    "Fraction of accepted speculated tokens among all generated tokens on the per-request level (averaged within request)",
    COMMON_LABELS,
    buckets=[x / 10 for x in range(11)],
)
_request_level_speculated_tokens_acceptance_fraction = Histogram(
    "au_inference_host_request_level_speculated_tokens_acceptance_fraction",
    "Fraction of accepted speculated tokens among proposed speculated tokens on the per-request level (averaged within request)",
    COMMON_LABELS,
    buckets=[x / 10 for x in range(11)],
)
_request_level_generated_tokens = Histogram(
    "au_inference_host_request_level_generated_tokens",
    "Number of generated tokens per request",
    COMMON_LABELS,
    buckets=[
        0,
        1,
        2,
        3,
        4,
        6,
        8,
        12,
        16,
        24,
        32,
        48,
        64,
        96,
        128,
        192,
        256,
        INF,
    ],
)
_request_level_queueing_time_seconds = Histogram(
    "au_inference_host_request_level_queueing_time_seconds",
    "Queueing time per request",
    COMMON_LABELS + ["source_namespace", "tenant_name"],
    # buckets from 2.5ms to 11s. Using low resolution to reduce
    # the number of time series we need to store per source_namespace.
    buckets=[(1.5**x - 1) / 200.0 for x in range(1, 20)] + [INF],
)

_decoding_rounds_per_request = Histogram(
    "au_inference_host_decoding_rounds_per_request",
    "Number of rounds per request that generated at least one token.",
    COMMON_LABELS,
    buckets=[
        0,
        1,
        2,
        3,
        4,
        6,
        8,
        10,
        12,
        16,
        20,
        24,
        32,
        40,
        48,
        64,
        96,
        128,
        192,
        256,
        384,
        512,
        768,
        1024,
        1536,
        2048,
    ]
    + [INF],
)

_non_neural_speculation_runtime_s_histogram = Histogram(
    "au_inference_host_non_neural_speculation_runtime_s",
    "Time spent in non-neural speculation",
    ["model_name", "request_source"],
    # buckets from 0.1ms to 100ms
    buckets=[2**i / 10000.0 for i in range(10)] + [INF],
)
_neural_speculation_runtime_s_histogram = Histogram(
    "au_inference_host_neural_speculation_runtime_s",
    "Time spent in neural speculation",
    ["model_name", "request_source"],
    # buckets from 1ms to 1000ms
    buckets=[2**i / 1000.0 for i in range(10)] + [INF],
)


logger = structlog.get_logger()

# TODO for functionality:
# - sampling
# - later: multiple samples per prefix

# TODO for robustness:
# - tests
#   - sampling with high temperature returns different results
#   - results of long context processing and decoding don't change under different round sizes
# - documentation:
#   - performance characteristics
#   - model interface
#   - security concept
#   - caching strategy


def _safe_div(numerator: Union[int, float], denominator: int) -> Optional[float]:
    """Returns numerator / denominator if denominator != 0 else None."""
    if denominator == 0:
        return None
    else:
        return numerator / denominator


@dataclasses.dataclass
class _RequestGenerationStats:
    """Statistics for the Request."""

    creation_time: float

    # The time when the last context processing round
    # has finished. Note that this round includes
    # the first generated tokens.
    context_processing_finished_last_time_s: float
    # The time when the round with ONLY context
    # processing has finished. None of the tokens
    # were generated during this round.
    only_context_processing_finished_last_time_s: float
    processing_start_time: Optional[float] = None

    n_decoding_rounds: int = 0
    n_generated_tokens: int = 0
    n_proposed_speculated_tokens: int = 0
    n_accepted_speculated_tokens: int = 0

    non_neural_speculation_runtime_s: float = 0
    neural_speculation_runtime_s: float = 0

    def get_generation_latency(self, current_time: float) -> float:
        return current_time - self.context_processing_finished_last_time_s

    def get_context_processing_latency(self, start_time: float) -> float:
        return self.context_processing_finished_last_time_s - start_time

    def get_average_latency_per_token(self, current_time: float) -> Optional[float]:
        generation_latency = self.get_generation_latency(current_time)
        return _safe_div(generation_latency, self.n_generated_tokens)

    def get_average_speculated_tokens_per_round(self) -> Optional[float]:
        return _safe_div(self.n_proposed_speculated_tokens, self.n_decoding_rounds)

    def get_average_accepted_speculated_tokens_per_round(self) -> Optional[float]:
        return _safe_div(self.n_accepted_speculated_tokens, self.n_decoding_rounds)

    def get_accepted_speculated_tokens_fraction_among_generated(
        self,
    ) -> Optional[float]:
        return _safe_div(self.n_accepted_speculated_tokens, self.n_generated_tokens)

    def get_accepted_speculated_tokens_fraction_among_proposed(self) -> Optional[float]:
        return _safe_div(
            self.n_accepted_speculated_tokens, self.n_proposed_speculated_tokens
        )

    def update_metrics(
        self,
        model_name: str,
        auth_info: AuthInfo,
        request_context: RequestContext,
        status_code: grpc.StatusCode,
        current_time_override: Optional[float] = None,
    ):
        current_time = current_time_override or time.time()

        def log_if_not_none(
            metric: Histogram, value: Optional[float], use_namespace=True
        ):
            if value is not None:
                labels = [str(status_code), model_name, request_context.request_source]
                if use_namespace:
                    labels.append(auth_info.shard_namespace or "")
                    labels.append(auth_info.metrics_tenant_name)
                metric.labels(*labels).observe(value)

        latency_s = current_time - self.creation_time
        _request_counter.labels(
            str(status_code),
            model_name,
            request_context.request_source,
            auth_info.shard_namespace or "",
            auth_info.metrics_tenant_name,
        ).inc()
        _request_latency_s.labels(
            str(status_code),
            model_name,
            request_context.request_source,
            auth_info.shard_namespace or "",
            auth_info.metrics_tenant_name,
        ).observe(latency_s)

        log_if_not_none(
            _request_level_speculated_tokens_per_round,
            self.get_average_speculated_tokens_per_round(),
            use_namespace=False,
        )
        log_if_not_none(
            _request_level_accepted_speculated_tokens_per_round,
            self.get_average_accepted_speculated_tokens_per_round(),
            use_namespace=False,
        )

        log_if_not_none(
            _request_level_speculated_tokens_fraction,
            self.get_accepted_speculated_tokens_fraction_among_generated(),
            use_namespace=False,
        )
        log_if_not_none(
            _request_level_speculated_tokens_acceptance_fraction,
            self.get_accepted_speculated_tokens_fraction_among_proposed(),
            use_namespace=False,
        )
        log_if_not_none(
            _request_level_latency_per_token_s,
            self.get_average_latency_per_token(current_time),
            use_namespace=False,
        )
        log_if_not_none(
            _generation_latency_v2_s,
            self.get_generation_latency(current_time),
            use_namespace=False,
        )
        log_if_not_none(
            _request_level_generated_tokens,
            self.n_generated_tokens,
            use_namespace=False,
        )
        if self.processing_start_time:
            log_if_not_none(
                _request_level_queueing_time_seconds,
                self.processing_start_time - self.creation_time,
            )
            log_if_not_none(
                _context_processing_latency_v2_s,
                self.get_context_processing_latency(self.processing_start_time),
                use_namespace=False,
            )
        log_if_not_none(
            _decoding_rounds_per_request,
            self.n_decoding_rounds,
            use_namespace=False,
        )

        _non_neural_speculation_runtime_s_histogram.labels(
            model_name,
            request_context.request_source,
        ).observe(self.non_neural_speculation_runtime_s)
        _neural_speculation_runtime_s_histogram.labels(
            model_name,
            request_context.request_source,
        ).observe(self.neural_speculation_runtime_s)


ModelID = str


def _no_assert_runner_thread():
    pass


@dataclasses.dataclass
class _Request:
    """Request to decode tokens, given a prompt."""

    request_context: RequestContext
    auth_info: AuthInfo
    sequence_id: int
    priority: int
    sub_request_id: str
    telemetry_context: opentelemetry.context.Context  # type: ignore
    model_name: str
    prompt: list[Token]  # includes prompt only, without generated tokens
    end_token_ids: tuple[Token, ...]  # using tuple as immutable list
    max_decode_tokens: int
    sampling_params: SamplingParams

    ordered_models: list[ModelID]  # order in which models shall be executed
    main_model_id: ModelID

    use_non_neural_speculation: bool

    target_tokens: Optional[Sequence[Token]] = None

    assert_runner_thread: Callable[[], None] = _no_assert_runner_thread

    cache_index_dict: dict[ModelID, pool.CacheEntry] = dataclasses.field(
        default_factory=dict
    )

    timeout_sec: Optional[float] = None

    _status_lock = threading.Lock()
    _is_success: bool = False
    _is_cancelled: bool = False
    _exception: Optional[InferenceException] = None

    def __post_init__(self):
        self._creation_time = time.time()
        self._tokens_added = threading.Event()
        self._result_ready = threading.Event()
        # TODO(yury): convert this into Read/Write lock?
        self._generation_state_lock = threading.Lock()
        if len(self.end_token_ids) > 100:
            self.mark_error(
                InferenceException(
                    grpc.StatusCode.INVALID_ARGUMENT, "Too many end tokens"
                )
            )

        self._generation_stats = _RequestGenerationStats(
            creation_time=self._creation_time,
            # We set `context_processing_finished_last_time_s` to the
            # request creation time for the situation when
            # there are no context processing rounds.
            # If the request performs context processing-only
            # rounds then this variable will be updated.
            context_processing_finished_last_time_s=self._creation_time,
            only_context_processing_finished_last_time_s=self._creation_time,
        )

        try:
            # Init the state and re-raise any exceptions due to invalid args.
            self.generation_state = generation_state.GenerationState(
                prompt=self.prompt,
                end_token_ids=self.end_token_ids,
                max_decode_tokens=self.max_decode_tokens,
                ordered_models=self.ordered_models,
                main_model_id=self.main_model_id,
                sampling_params=self.sampling_params,
                target_tokens=self.target_tokens,
            )
        except generation_state.GenerationStateException as e:
            raise InferenceException(
                grpc.StatusCode.INTERNAL, "Error while initializing generation state"
            ) from e
        if not self.use_non_neural_speculation:
            self._non_neural_speculation_model = speculation.NullModel()
        else:
            self._non_neural_speculation_model = longest_overlap_lm.factory()
        self._non_neural_speculation_model.fit(self.prompt)

    def result(self) -> InferenceResult:
        """Waits for the request to finish and return the result or throw an InferenceException."""
        status_code = grpc.StatusCode.UNKNOWN
        try:
            ttl = self.timeout_sec if self.timeout_sec is not None else None
            acquired_successfully = self._result_ready.wait(timeout=ttl)
            with self._status_lock:
                if not acquired_successfully:
                    if self._is_expired():
                        self._exception = InferenceException(
                            grpc.StatusCode.DEADLINE_EXCEEDED, "Expired"
                        )
                    else:
                        self._exception = InferenceException(
                            grpc.StatusCode.UNKNOWN, "Unknown error"
                        )
                if self._exception:
                    status_code = self._exception.status_code
                    raise self._exception
                elif self._is_success:
                    status_code = grpc.StatusCode.OK
                    # after branch, will drop the lock and collect the result
                elif self._is_cancelled:
                    status_code = grpc.StatusCode.CANCELLED
                    raise InferenceException(grpc.StatusCode.CANCELLED, "Cancelled")
                else:
                    status_code = grpc.StatusCode.UNKNOWN
                    raise InferenceException(
                        grpc.StatusCode.UNKNOWN,
                        "Unknown error: result got unlocked without success or cancellation flags.",
                    )
            assert status_code == grpc.StatusCode.OK
            with self._generation_state_lock:
                (
                    result,
                    log_probs,
                    num_correct_predictions,
                ) = self.generation_state.get_generated_tokens_and_logprobs()

                assert len(result) <= self.max_decode_tokens, (
                    self,
                    self.generation_state,
                    result,
                )
                return InferenceResult(result, log_probs, num_correct_predictions)
        finally:
            self._generation_stats.update_metrics(
                self.model_name, self.auth_info, self.request_context, status_code
            )

    def result_generator(self) -> Iterable[InferenceResult]:
        """Returns an iteratable that yields chunks of tokens as they are generated."""
        status_code = grpc.StatusCode.UNKNOWN
        is_done = False
        token_index = 0
        try:
            while not is_done:
                tokens_added = self._tokens_added.wait(
                    timeout=self._remaining_time_sec()
                )
                self._tokens_added.clear()
                with self._status_lock:
                    is_done = self._is_success
                    if not is_done:
                        if self._is_expired():
                            self._exception = InferenceException(
                                grpc.StatusCode.DEADLINE_EXCEEDED, "Expired"
                            )
                        elif self._is_cancelled:
                            self._exception = InferenceException(
                                grpc.StatusCode.CANCELLED, "Cancelled"
                            )
                        elif not tokens_added:
                            self._exception = InferenceException(
                                grpc.StatusCode.UNKNOWN, "Unknown error"
                            )
                        if self._exception:
                            status_code = self._exception.status_code
                            raise self._exception

                with self._generation_state_lock:
                    (
                        result,
                        log_probs,
                        num_correct_predictions,
                    ) = self.generation_state.get_generated_tokens_and_logprobs()

                result_round = result[token_index:]
                log_probs_round = log_probs[token_index:]
                token_index = len(result)
                if result_round and len(result_round):
                    yield InferenceResult(
                        result_round,
                        log_probs_round,
                        num_correct_predictions,
                    )
            status_code = grpc.StatusCode.OK
        finally:
            self._generation_stats.update_metrics(
                self.model_name, self.auth_info, self.request_context, status_code
            )

    def _add_non_neural_speculation_tokens(self, max_tokens_to_speculate: int):
        """Returns additional tokens to process from the non-neural speculation model."""
        assert max_tokens_to_speculate >= 0
        start_time = time.time()
        # TODO(markus): Move this logic into the speculation model.
        with tracer.start_span(
            "Non-neural speculation", context=self.telemetry_context
        ) as speculation_span:
            max_tokens_we_can_append = (
                self.generation_state.get_max_decode_tokens_left()
            )
            if max_tokens_we_can_append is not None:
                max_tokens_to_speculate = min(
                    max_tokens_to_speculate, max_tokens_we_can_append
                )

            speculated = self._non_neural_speculation_model.predict_next_k_tokens(
                self.generation_state.tokens,
                k=max_tokens_to_speculate,
                num_predictions=1,
            )

            speculation_span.set_attribute(
                "max_tokens_to_speculate", max_tokens_to_speculate
            )

            if len(speculated) > 0:
                speculated = speculated[0]
                speculation_span.set_attribute("speculated_tokens", len(speculated))
                num_added_speculated_tokens = (
                    self.generation_state.add_non_neural_speculative_tokens(speculated)
                )
                logger.debug(
                    "Adding speculated tokens: %d",
                    num_added_speculated_tokens,
                )
        self._generation_stats.non_neural_speculation_runtime_s += (
            time.time() - start_time
        )
        return speculated

    def tokens_to_process(
        self, model_id: ModelID, max_num_tokens: int
    ) -> Sequence[Token]:
        """Returns tokens that can be processed in the next round."""
        self.assert_runner_thread()
        # Lock because we might modify self.generation_state
        with self._generation_state_lock:
            logger.debug(
                "Remaining tokens to process in this request [model ID %s]: %d",
                model_id,
                self.generation_state.get_num_tokens_to_process(model_id),
            )

            to_process = self.generation_state.tokens_to_process(
                model_id, max_num_tokens
            )
            assert (
                len(to_process) > 0 or self.is_done()
            ), "Requests must always have at least one token to process (or be done)."
            if len(to_process) < max_num_tokens:
                # We still can process a little more tokens.
                self._add_non_neural_speculation_tokens(
                    max_num_tokens - len(to_process)
                )
                # Re-run `tokens_to_process` since we might have added more tokens.
                to_process = self.generation_state.tokens_to_process(
                    model_id, max_num_tokens
                )
        assert len(to_process) <= max_num_tokens
        return to_process

    def _get_tokens(self):
        """Returns the tokens generated by the model for testing purposes."""
        return self.generation_state.tokens

    def release_cache(self):
        """Release the attention cache."""
        # TODO: document how release cache interacts with cache hits and when
        # it should be called.
        self.assert_runner_thread()

        for model_id in self.ordered_models:
            if model_id in self.cache_index_dict:
                self.cache_index_dict[model_id].release(self.request_context.request_id)
                del self.cache_index_dict[model_id]

    def mark_success(self):
        """Marks the request as success.

        This will notify any listener on result().
        """
        logger.debug("Request marked success")

        with self._status_lock:
            assert not self._is_success
            self._is_success = True
        self._tokens_added.set()
        self._result_ready.set()

    def is_done(self) -> bool:
        return self._result_ready.is_set() or self._is_expired()

    def mark_cancelled(self):
        """Marks the request as cancelled.

        This will notify any listener on result().
        """
        with self._status_lock:
            self._is_cancelled = True
        self._tokens_added.set()
        self._result_ready.set()

    def mark_error(self, err: InferenceException):
        with self._status_lock:
            self._exception = err
        self._tokens_added.set()
        self._result_ready.set()

    def _is_expired(self) -> bool:
        """Returns true if the request is expired."""
        return (
            self.timeout_sec is not None
            and self._creation_time + self.timeout_sec < time.time()
        )

    def _remaining_time_sec(self) -> float | None:
        """Returns the remaining time for the request in seconds."""
        if self.timeout_sec is None:
            return None
        elapsed_sec = time.time() - self._creation_time
        return self.timeout_sec - elapsed_sec

    def record_round_end_time(
        self, model_id: ModelID, round_start_time: float, num_round_tokens: int
    ):
        """Records the end time of a round for the given model.

        This must be a separate function from process logits, as process_logits
        can happen asynchronously with the round execution itself.


        """
        current_time = time.time()
        if model_id == self.main_model_id:
            # If this is the first generation round, record the start time.
            if self._generation_stats.processing_start_time is None:
                self._generation_stats.processing_start_time = round_start_time

            if not self.generation_state.is_done_context_processing(model_id):
                # Round did not include any generation, only context processing
                self._generation_stats.only_context_processing_finished_last_time_s = (
                    current_time
                )

            if (
                self.generation_state.get_num_processed_tokens(model_id)
                - num_round_tokens
                < self.generation_state.prompt_length
            ):
                # Round included some generation and some context processing
                self._generation_stats.context_processing_finished_last_time_s = (
                    current_time
                )

        if model_id != self.main_model_id:
            self._generation_stats.neural_speculation_runtime_s += (
                current_time - round_start_time
            )

    def process_logits(
        self,
        model_id: ModelID,
        logits: fwd.Logits2D,
        round_tokens: Sequence[Token],
        telemetry_span: Optional[opentelemetry.trace.Span] = None,
    ):
        """Updates the request with the result of a processed round."""
        start_time = time.time()
        self.assert_runner_thread()
        try:
            with self._generation_state_lock:
                pre_round_tokens = self.generation_state.num_processed_tokens[model_id]
                prompt_length = self.generation_state.prompt_length

                result = self.generation_state.process_logits(
                    model_id,
                    logits,
                    round_tokens=round_tokens,
                )
                # Reset the attention cache for all models to match the tokens we've processed.
                # Note that this is important to do for all models because
                # when one model, like the main model, processes tokens,
                # it can change how many tokens other models,
                # like speculative models, have processed.
                for other_model_id in self.ordered_models:
                    if other_model_id in self.cache_index_dict:
                        self.cache_index_dict[other_model_id].attn.reset(
                            cache_idx=self.cache_index_dict[
                                other_model_id
                            ].get_cache_idx(),
                            to_position=self.generation_state.num_processed_tokens[
                                other_model_id
                            ],
                        )

                post_round_tokens = self.generation_state.num_processed_tokens[model_id]
        except generation_state.GenerationStateException as e:
            raise InferenceException(
                grpc.StatusCode.INTERNAL, "Error while running process_logits"
            ) from e

        # We can notify result_generator now, but do not mark_success until
        # we've updated all the generation stats, as those stats are read by the
        # thread waiting on success/cancel/error, and we could under-report those
        # metrics.
        self._tokens_added.set()

        # From the system perspective, we have only one main model and the rest is speculation models.
        # Therefore, we only update speculation metrics if the current model is the main model.
        if model_id == self.main_model_id:
            if pre_round_tokens < prompt_length:
                # Round included some prompt processing
                num_prompt_tokens_processed = (
                    min(prompt_length, post_round_tokens) - pre_round_tokens
                )
                _processed_prompt_token_counter.labels(
                    self.model_name,
                    self.request_context.request_source,
                    self.auth_info.shard_namespace or "",
                    self.auth_info.metrics_tenant_name,
                ).inc(num_prompt_tokens_processed)

        # From the system perspective, we have only one main model and the rest is speculation models.
        # Therefore, we only update speculation metrics if the current model is the main model.
        if model_id == self.main_model_id and result.n_generated_tokens:
            _speculated_token_counter.labels(
                self.model_name,
                self.request_context.request_source,
                self.auth_info.shard_namespace or "",
                self.auth_info.metrics_tenant_name,
            ).inc(result.n_proposed_speculated_tokens)
            _speculated_token_accepted_counter.labels(
                self.model_name,
                self.request_context.request_source,
                self.auth_info.shard_namespace or "",
                self.auth_info.metrics_tenant_name,
            ).inc(result.n_accepted_speculated_tokens)
            self._generation_stats.n_decoding_rounds += 1
            self._generation_stats.n_generated_tokens += result.n_generated_tokens
            self._generation_stats.n_proposed_speculated_tokens += (
                result.n_proposed_speculated_tokens
            )
            self._generation_stats.n_accepted_speculated_tokens += (
                result.n_accepted_speculated_tokens
            )

        _generated_tokens_per_round_histogram.labels(
            self.model_name,
            self.request_context.request_source,
            "main_model" if model_id == self.main_model_id else "speculative_model",
        ).observe(result.n_generated_tokens)
        _wasted_tokens_counter.labels(
            self.model_name,
            self.request_context.request_source,
            "main_model" if model_id == self.main_model_id else "speculative_model",
        ).inc(result.n_proposed_speculated_tokens - result.n_accepted_speculated_tokens)

        if self.generation_state.is_completed():
            self.mark_success()

        # Update telemetry
        if telemetry_span is not None:
            telemetry_span.set_attribute("model_id", model_id)
            telemetry_span.set_attribute(
                "num_generated_tokens", result.n_generated_tokens
            )
            telemetry_span.set_attribute(
                "num_speculated_tokens", result.n_proposed_speculated_tokens
            )
            telemetry_span.set_attribute(
                "num_accepted_speculated_tokens", result.n_accepted_speculated_tokens
            )
            telemetry_span.set_attribute(
                "process_logits_runtime_s", time.time() - start_time
            )

    def get_tenant_id(self) -> pool.TenantID:
        assert self.auth_info.tenant_id is not None
        return self.auth_info.tenant_id


def _exception_callback(status_code: grpc.StatusCode, msg: str):
    raise InferenceException(status_code, msg)


def exception_factory(status_code: grpc.StatusCode, msg: str):
    """Returns a lambda that will raise an InferenceException when called."""
    return lambda: _exception_callback(status_code, msg)


@dataclasses.dataclass
class ModelConfig:
    """Things we need to configure a neural model.

    step_fn: a function that takes a list of tokens and an attention
        function, and returns a list of logits to sample from.
    attn_factory: For a given cache length, generates a BasicAttention
        object compatible with step_fn.
    round_sizes: The optimal sizes for rounds
    model_name: Name of the individual model.
    do_warmup: Whether to warm up the model.
    """

    step_fn: fwd.ForwardStepFn
    attn_factory: fwd.AttentionFactory
    round_sizes: Sequence[int] = (128,)
    model_name: Optional[str] = None
    do_warmup: bool = False


class ModelRunner:
    """A class with goodies to run a model."""

    def __init__(
        self,
        model_name: str,
        model_config: ModelConfig,
        cache_pool_size: int,
        max_seq_length: int,
    ):
        self.model_name = model_name
        self.model_config = model_config
        self.max_round_size, self.round_sizes = batching.process_round_sizes(
            model_config.round_sizes
        )
        self.mc_attn = model_config.attn_factory.create_cache_pool(
            max_length=max_seq_length, num_attention_caches=cache_pool_size
        )
        self.round_attn = batching.RoundAttention(
            max_round_size=self.max_round_size,
            mc_attn=self.mc_attn,
        )
        self.attn_pool = pool.CachePool(
            attn=self.mc_attn,
            model_name=self.model_name,
            pool_size=cache_pool_size,
        )
        self.step_fn = model_config.step_fn
        if model_config.do_warmup:
            self.warm_up_round_sizes()

    @property
    def max_requests_in_round(self) -> int | None:
        """Maximum number of requests in a round."""
        return self.mc_attn.get_max_requests_in_round()

    @property
    def small_request_max_seqlen(self) -> int | None:
        """Maximum sequence length for a small request."""
        return self.mc_attn.get_small_request_max_seqlen()

    def warm_up_round_sizes(self):
        logging.info("Starting warmup for %s", self.model_name)
        start = time.time()
        self.round_attn.reset()
        for round_size in self.round_sizes:
            self.step_fn([0] * round_size, self.round_attn)
            self.round_attn.reset()
            logging.info("Warm up %d completed for %s", round_size, self.model_name)
        warmup_time = time.time() - start
        logging.info(
            "Warmup complete for %s in %f seconds", self.model_name, warmup_time
        )

    def get_padding_cache_idx(self) -> int:
        return self.round_attn.get_padding_cache_idx()


class ContinuousBatchingInferenceRunner(InferenceRunner):
    """Schedules context processing and decoding from multiple inference requests."""

    def __init__(
        self,
        model_name: str,
        models: list[ModelConfig],
        max_seq_length: int,
        main_model_id: Optional[ModelID] = None,
        cache_pool_size: int = 32,
        default_timeout_sec: float = 10.0,
        os_exit_on_error: bool = False,
        use_non_neural_speculation: bool = True,
        use_sorted_batching: bool = False,
        max_requests_per_user: int = _MAX_REQUESTS_PER_USER,
    ):
        """Create an inference runner.

        Args:
            model_name: name of the model
            models: list of models ordered by the increasing quality and cost.
            max_seq_length: Maximum sequence length for the attention caches.
            main_model_id: ID of the main model.
            cache_pool_size: Number of attention caches to use.
            default_timeout_sec: Default timeout for incoming requests.
            os_exit_on_error: If true, call `os._exit(1)` in the runner thread when
                an error occurs. This helps to shut down the server quickly. Do not
                this in tests.
            speculative_models: list of speculative models ordered by the increasing
                quality and cost.
            non_neural_speculation_factory: Used for speculative decoding.
            use_sorted_batching: If true, use sorted batching for multi-request attention.
            max_requests_per_user: Maximum number of requests per user.
        """
        self.model_name = model_name
        if not models:
            raise ValueError("At least one model must be provided.")

        # Provide custom model names for individual models
        self.models: OrderedDict[ModelID, ModelRunner] = OrderedDict()
        for index, model in enumerate(models):
            current_model_name = model.model_name or self.model_name + "_" + str(index)
            self.models[current_model_name] = ModelRunner(
                current_model_name,
                model,
                cache_pool_size,
                max_seq_length,
            )
        self.main_model_id = main_model_id or list(self.models.keys())[-1]
        self.max_seq_len = max_seq_length

        self.default_timeout_sec = default_timeout_sec

        self.requests: list[_Request] = []
        # We allow for multiple active requests per session.
        # All must be of the same sequence_id and unique sub_request_ids.
        self.user_request_map: Dict[
            InferenceUserID, Dict[SessionID, List[_Request]]
        ] = defaultdict(lambda: defaultdict(list))
        self._shutdown: bool = False
        self._runner_thread = None
        self.os_exit_on_error = os_exit_on_error
        self.use_non_neural_speculation = use_non_neural_speculation
        self.use_sorted_batching = use_sorted_batching
        self._max_requests_per_user = max_requests_per_user

        # Synchronize access to various fields, including self.requests,
        # self.session_map, self._shutdown, self._runner_thread
        self._inference_runner_lock = threading.Lock()
        # Mechanism to allow the run thread to go to sleep if no work
        # is available, but to wake when there is
        self._run_wakeup = threading.Event()

        def measure_active_requests():
            with self._inference_runner_lock:
                return len(self.requests)

        _active_requests_sampled_histogram.register_callback(
            measure_active_requests, [self.model_name]
        )
        _active_requests_sampled_histogram.start()

    def infer(
        self,
        request_context: RequestContext,
        auth_info: AuthInfo,
        prompt: list[Token],
        max_decode_tokens: int,
        sampling_params: Optional[SamplingParams] = None,
        end_token_ids: Sequence[int] = (),
        timeout_sec: Optional[float] = None,
        sequence_id: int = 0,
        priority: int = 0,
        sub_request_id: str = "",
        target_tokens: Optional[Sequence[Token]] = None,
    ) -> Callable[[], InferenceResult]:
        """Enqueues the request tokens to the inference model.

        Prediction request MAY be cancelled if a another request with the
        same session_id and a larger request_id is received.

        If a request_id is repeated, the repeated request is answered with
        an error saying "Duplicate request_id".

        The function doesn't raise InferenceException. All error reporting
        is delayed until the future callback is invoked. The future callback might
        raise InferenceExceptions.

        Args:
            request_context: holds request and session identifiers.
            prompt: the prompt; a list of tokens.
            max_decode_tokens: maximum number of tokens to generate
            random_seed: random seed to use for sampling
            namespace: namespace of the source of the request
            sampling_params: sampling parameters, including temperature, seed, etc.
                If None, use greedy decoding.
            end_token_ids: list of token IDs that end the decoding
            timeout_sec: Optional override for `self.default_timeout_sec`.
            sequence_id: sequence id of the request
            priority: priority of the request
            sub_request_id: Sub-request id to use for cancellations.
                Requests with the same sequence id and different sub-request id are allowed
            target_tokens: Target tokens used to compute token accuracy.

        Returns:
            A callable that returns the list of tokens and the loprobs.

        """
        request, exception_factory = self._enqueue_request(
            request_context,
            auth_info,
            prompt,
            max_decode_tokens,
            sampling_params,
            end_token_ids,
            timeout_sec,
            sequence_id,
            priority,
            sub_request_id,
            target_tokens,
        )
        if exception_factory:
            return exception_factory
        assert request is not None
        return request.result

    def infer_stream(
        self,
        request_context: RequestContext,
        auth_info: AuthInfo,
        prompt: list[Token],
        max_decode_tokens: int,
        sampling_params: Optional[SamplingParams] = None,
        end_token_ids: Sequence[int] = (),
        timeout_sec: Optional[float] = None,
        sequence_id: int = 0,
        priority: int = 0,
        sub_request_id: str = "",
        target_tokens: Optional[Sequence[Token]] = None,
    ) -> Callable[[], Iterable[InferenceResult]]:
        """Enqueues the request tokens to the inference model and returns an iteratable.

        Args:
            request_context: holds request and session identifiers.
            prompt: the prompt; a list of tokens.
            max_decode_tokens: maximum number of tokens to generate
            random_seed: random seed to use for sampling
            namespace: namespace of the source of the request
            sampling_params: sampling parameters, including temperature, seed, etc.
                If None, use greedy decoding.
            end_token_ids: list of token IDs that end the decoding
            timeout_sec: Optional override for `self.default_timeout_sec`.
            sequence_id: sequence id of the request
            priority: priority of the request
            sub_request_id: Sub-request id to use for cancellations.
                Requests with the same sequence id and different sub-request id are allowed
            target_tokens: Target tokens used to compute token accuracy.

        Returns:
            A callable that returns the list of tokens and the loprobs.

        """
        request, exception_factory = self._enqueue_request(
            request_context,
            auth_info,
            prompt,
            max_decode_tokens,
            sampling_params,
            end_token_ids,
            timeout_sec,
            sequence_id,
            priority,
            sub_request_id,
            target_tokens,
        )
        if exception_factory:
            return exception_factory
        assert request is not None
        return request.result_generator

    def _enqueue_request(
        self,
        request_context: RequestContext,
        auth_info: AuthInfo,
        prompt: list[Token],
        max_decode_tokens: int,
        sampling_params: Optional[SamplingParams],
        end_token_ids: Sequence[int],
        timeout_sec: Optional[float],
        sequence_id: int,
        priority: int,
        sub_request_id: str,
        target_tokens: Optional[Sequence[Token]],
    ) -> Tuple[Optional[_Request], Optional[Callable]]:
        trace_context = opentelemetry.context.get_current()
        if len(prompt) + max_decode_tokens > self.max_seq_len + 1:
            raise InferenceException(
                grpc.StatusCode.INVALID_ARGUMENT,
                f"Error: Prompt ({len(prompt)}) + max_decode_tokens {max_decode_tokens} too long ({self.max_seq_len+1=}).",
            )
        request = _Request(
            request_context=request_context,
            auth_info=auth_info,
            sequence_id=sequence_id,
            priority=priority,
            sub_request_id=sub_request_id,
            telemetry_context=trace_context,
            model_name=self.model_name,
            prompt=list(prompt),
            sampling_params=sampling_params or SamplingParams(),
            end_token_ids=tuple(end_token_ids),
            max_decode_tokens=max_decode_tokens,
            timeout_sec=timeout_sec or self.default_timeout_sec,
            assert_runner_thread=self.assert_runner_thread,
            ordered_models=list(self.models.keys()),
            main_model_id=self.main_model_id,
            use_non_neural_speculation=self.use_non_neural_speculation,
            target_tokens=target_tokens,
        )

        user_id = str(auth_info.user_id.get_secret_value()) if auth_info.user_id else ""
        hashed_user_id = (
            auth_info.tenant_id or "",
            hashlib.sha256(user_id.encode("utf-8")).hexdigest(),
        )
        del user_id
        session_id = request_context.request_session_id

        # Cannot use lock via context manager because of timeout.
        # We need a timeout to avoid incoming threads being blocked in case of
        # bugs in the server.
        lock_acquired = self._inference_runner_lock.acquire(blocking=True, timeout=0.1)
        if not lock_acquired:
            status_code = grpc.StatusCode.INTERNAL
            _request_counter.labels(
                str(status_code),
                self.model_name,
                request_context.request_source,
                auth_info.shard_namespace or "",
                auth_info.metrics_tenant_name,
            ).inc()
            return None, exception_factory(
                status_code,
                "Error: Could not enqueue request. Is the server in a deadlock?",
            )
        try:
            # TODO: move this code out of the incoming request callback.
            # Instead enqueue newly arrived requests and process them with the
            # main thread.
            previous_requests_by_session_id = self.user_request_map[
                hashed_user_id
            ]  # defaultdict may add {}

            # Try to find the requests with the same session_id.
            previous_requests = previous_requests_by_session_id.get(session_id, None)
            if previous_requests is None:
                logger.debug(
                    "New session ID detected.",
                    session_id=request_context.request_session_id,
                )
                if len(previous_requests_by_session_id) >= self._max_requests_per_user:
                    logger.info(
                        "Too many sessions for user. Evicting oldest session.",
                        session_id=request_context.request_session_id,
                        tenant_id=auth_info.tenant_id,
                    )
                    # delete all sessions that are done
                    for (
                        session_id,
                        other_requests,
                    ) in list(previous_requests_by_session_id.items()):
                        if all(request.is_done() for request in other_requests):
                            del previous_requests_by_session_id[session_id]
                    if (
                        len(previous_requests_by_session_id)
                        >= self._max_requests_per_user
                    ):
                        return None, exception_factory(
                            grpc.StatusCode.RESOURCE_EXHAUSTED,
                            "Error: Too many active sessions for user.",
                        )

            elif not all(req.is_done() for req in previous_requests):
                existing_request = next(
                    filter(
                        lambda req: not req.is_done(),
                        previous_requests,
                    )
                )

                """
                Existing requests for a session will all have the same priority and sequence ID.
                This is enforced by cancelling requests with lower priority or sequence ID if the new request is not cancelled.
                This request will be cancelled if
                    1. It has a lower priority than the existing request.
                    2. It has the same priority and a lower sequence ID than the existing request.
                """
                if existing_request.priority > priority:
                    logger.info(
                        "Incoming request has lower priority %s than the priority "
                        "%s of the currently active request %s. Incoming request "
                        "is not allowed to disrupt a higher priority request.",
                        priority,
                        existing_request.priority,
                        existing_request.request_context.request_id,
                        session_id=request_context.request_session_id,
                    )
                    _request_counter.labels(
                        str(grpc.StatusCode.CANCELLED),
                        self.model_name,
                        request_context.request_source,
                        auth_info.shard_namespace or "",
                        auth_info.metrics_tenant_name,
                    ).inc()
                    return None, exception_factory(
                        grpc.StatusCode.CANCELLED,
                        "Error: Incoming request has lower priority than the currently "
                        "active requests.",
                    )

                if (
                    existing_request.priority == priority
                    and existing_request.sequence_id > sequence_id
                ):
                    logger.info(
                        "Incoming request has lower sequence id %s than the sequence "
                        "id %s of the currently active request %s of same priority %s. Incoming request "
                        "is likely out-of-order and is cancelled on arrival.",
                        sequence_id,
                        existing_request.sequence_id,
                        priority,
                        existing_request.request_context.request_id,
                        session_id=request_context.request_session_id,
                    )
                    _request_counter.labels(
                        str(grpc.StatusCode.CANCELLED),
                        self.model_name,
                        request_context.request_source,
                        auth_info.shard_namespace or "",
                        auth_info.metrics_tenant_name,
                    ).inc()
                    return None, exception_factory(
                        grpc.StatusCode.CANCELLED,
                        "Error: Incoming request has lower sequence id than the currently "
                        "active requests.",
                    )

                """
                Existing requests for a session are cancelled if
                    1. This request has higher priority.
                    2. This request has the same priority and a higher sequence ID.
                    3. This request has the same priority, sequence ID, and sub-request ID.
                        This would be a request retry. The newer request will proceed.
                """
                for req in previous_requests:
                    if (
                        req.priority < priority
                        or (req.priority == priority and req.sequence_id < sequence_id)
                        or (
                            req.priority == priority
                            and req.sequence_id == sequence_id
                            and req.sub_request_id == sub_request_id
                        )
                    ):
                        logger.info(
                            "Request is cancelling %s from same session. (Priority, SequenceId, SubRequestId): "
                            "(%s, %s, %s) (previous) vs (%s, %s, %s) (new)",
                            req.request_context.request_id,
                            req.priority,
                            req.sequence_id,
                            req.sub_request_id,
                            priority,
                            sequence_id,
                            sub_request_id,
                        )
                        req.mark_cancelled()

            if len(self.requests) >= MAX_REQUEST_QUEUE_LEN:
                _request_counter.labels(
                    str(grpc.StatusCode.RESOURCE_EXHAUSTED),
                    self.model_name,
                    request_context.request_source,
                    auth_info.shard_namespace or "",
                    auth_info.metrics_tenant_name,
                ).inc()
                return None, exception_factory(
                    grpc.StatusCode.RESOURCE_EXHAUSTED, "Request queue is full."
                )
            active_requests = [
                req for req in previous_requests or [] if not req.is_done()
            ] + [request]
            previous_requests_by_session_id[session_id] = active_requests
            self.requests.append(request)
        finally:
            self._inference_runner_lock.release()
        self._run_wakeup.set()
        logger.debug(
            "Enqueued request for session %s: input_token_len=%s, max_decode_tokens=%s",
            session_id,
            len(request.generation_state.tokens),
            request.max_decode_tokens,
        )
        return request, None

    def _run_in_fresh_thread(self):
        assert self._runner_thread is None

        def run_thread_with_error_handling():
            try:
                functools.partial(self.run, forever=True)()
            except Exception as ex:  # pylint: disable=broad-exception-caught
                # Shutting down system to rather have an aggressive
                # failure than a silent deadlock.
                print(ex)
                logging.error("Uncaught error in runner thread: %s", ex)
                logging.exception(ex)
                self.stop()  # sets self.shutdown = True
                logging.shutdown()
                sys.stdout.flush()
                sys.stderr.flush()
                if self.os_exit_on_error:
                    # don't use sys.exit as it only raises an exception.
                    os._exit(1)

        with self._inference_runner_lock:
            if not self._shutdown:
                self._runner_thread = threading.Thread(
                    target=run_thread_with_error_handling,
                    name="InferenceRunner",
                    daemon=True,
                )
                self._runner_thread.start()

    def assert_runner_thread(self):
        assert (
            self._runner_thread is None
            or self._runner_thread is threading.current_thread()
        )

    def run(self, forever=False):
        """Runs the inference runner.

        Args:
            forever: if True, the function runs until shutdown is called.
                Otherwise, it only runs for the currently scheduled requests.
        """
        self.assert_runner_thread()

        while True:
            # Clear the Event variable before we check stuff that's sent from other
            # threads (e.g. shutdown, requests enqueued). The event variable exists
            # to tell us to wake up and check stuff, and we're awake and about to
            # check stuff.
            self._run_wakeup.clear()

            with self._inference_runner_lock:
                if not (forever or self.requests):
                    break

                if self._shutdown:
                    break

            model_id, round_tokens, request_infos, requests = self._gather_round()
            if not round_tokens:
                # We'll get woken up if somebody notified us between _gather_round
                # and wait() because _run_wakeup is an Event and has state (unlike
                # a condition variable)
                self._run_wakeup.wait()
                continue

            self._process_round(model_id, round_tokens, request_infos, requests)

    def stop(self):
        """Stop processing rounds; object cannot be reused after this call."""
        with self._inference_runner_lock:
            self._shutdown = True
            for request in self.requests:
                request.mark_error(
                    InferenceException(
                        grpc.StatusCode.FAILED_PRECONDITION,
                        "InferenceServer is shutting down.",
                    )
                )
            local_thread_ref = self._runner_thread

        self._run_wakeup.set()

        if local_thread_ref:
            if threading.current_thread() is not local_thread_ref:
                # This timeout should cover the startup time of server (including
                # CUDA graph warmup) and should be enough for our tests.
                local_thread_ref.join(timeout=10.0)
                if local_thread_ref.is_alive():
                    raise RuntimeError("Error: Failed to shut down runner thread.")

    def __enter__(self):
        """Use as context manager to simplify testing."""
        self._run_in_fresh_thread()
        return self

    def __exit__(self, exc_type, exc_value, trace):
        """Use as context manager to simplify testing."""
        if exc_type is not None or exc_value is not None:
            logging.error("Error: Uncaught exception in the context handler.")
            logging.exception(exc_value)
            traceback.print_exception(exc_type, exc_value, trace)
        self.stop()

    def _gather_round(
        self,
    ) -> Tuple[ModelID, list[int], list[batching.RequestInRound], list[_Request]]:
        logging.debug("Gather round")

        round_tokens = []
        request_infos = []
        requests_in_round = []
        requests_to_remove = set()
        request_ids_in_round = set()

        with self._inference_runner_lock:
            local_requests = self.requests.copy()

        if len(local_requests) > 0:
            # We decide whether to do a round with speculative decoding model
            # based on the state of the very first (higher priority) request.
            model_id = local_requests[0].generation_state.decide_which_model_to_use()
        else:
            # No requests available. Fall back to the main model.
            model_id = self.main_model_id

        model = self.models[model_id]

        remaining_round_size = model.max_round_size
        max_requests_in_round: int = model.max_requests_in_round or remaining_round_size
        small_request_size: int = model.small_request_max_seqlen or remaining_round_size

        can_take_a_large_request = True

        for r_idx, request in enumerate(local_requests):
            assert remaining_round_size >= 0
            # Break if we're out of tokens
            if remaining_round_size == 0:
                break
            # Break if we're using sorted batches and have run out of requests
            if (
                self.use_sorted_batching
                and len(requests_in_round) >= max_requests_in_round
            ):
                _round_capped_by_rir_limit.labels(self.model_name).inc()
                break
            if request.is_done():
                requests_to_remove.add(r_idx)
                request.release_cache()
                continue
            if request.request_context.request_id in request_ids_in_round:
                # Limit to one request per request ID in the round
                # This can happen only if there are multiple request for the same request/sequence ID with different sub-request IDs.
                continue
            request_ids_in_round.add(request.request_context.request_id)

            # Check for cache hit before computing `remaining_tokens_to_process`
            if model_id not in request.cache_index_dict:
                with tracer.start_span(
                    "reserve_cache_index", context=request.telemetry_context
                ) as span:
                    cache_index, overlap = self.models[model_id].attn_pool.reserve(
                        tokens=request.generation_state.get_prompt_tokens(),
                        request_id=request.request_context.request_id,
                        session_id=request.request_context.request_session_id,
                        tenant=request.get_tenant_id(),
                        request_context=request.request_context,
                    )
                    if cache_index is None:
                        logger.info(
                            "No free cache object available for request",
                        )
                        break  # stop adding requests to round
                    span.set_attribute("model_id", model_id)
                    span.set_attribute("last_used", cache_index.last_used)
                    span.set_attribute("overlap", overlap)
                    request.cache_index_dict[model_id] = cache_index
                    request.generation_state.set_num_processed_tokens(model_id, overlap)
            should_take_large_request = (
                request.generation_state.get_num_tokens_to_process(model_id)
                > small_request_size
            )
            if (not self.use_sorted_batching) or (
                should_take_large_request and can_take_a_large_request
            ):
                max_tokens_to_take = remaining_round_size
            else:
                max_tokens_to_take = min(remaining_round_size, small_request_size)
            tokens_to_process = request.tokens_to_process(
                model_id=model_id,
                max_num_tokens=max_tokens_to_take,
            )
            num_tokens_taken = len(tokens_to_process)
            assert num_tokens_taken <= remaining_round_size
            # If this is a large request, mark that we have claimed the large request slot in this round
            if num_tokens_taken > small_request_size:
                can_take_a_large_request = False
            if num_tokens_taken == 0:
                # TODO: this is a bit of an awkward case; check if this can actually happen or
                # if we can drop this case.
                if len(request.generation_state.tokens) > 0:
                    if not request.is_done():
                        request.mark_success()
                    logger.debug(
                        "FULL CACHE HIT? marked as success.",
                    )
                else:
                    logger.error(
                        "Request has no tokens to process",
                    )
                    request.mark_error(
                        InferenceException(
                            grpc.StatusCode.UNKNOWN, "Request has no tokens to process."
                        )
                    )
                assert request.is_done()
                requests_to_remove.add(r_idx)
                request.release_cache()
                continue

            # TODO(markus): switch to using batching.Round
            in_round = batching.RequestInRound(
                num_tokens=num_tokens_taken,
                cache_idx=request.cache_index_dict[model_id].get_cache_idx(),
                round_start_idx=len(round_tokens),
            )
            round_tokens.extend(tokens_to_process)
            request_infos.append(in_round)
            requests_in_round.append(request)
            remaining_round_size -= num_tokens_taken

        with self._inference_runner_lock:
            new_queue = []
            for r_idx, request in enumerate(self.requests):
                if r_idx not in requests_to_remove:
                    new_queue.append(request)
            self.requests = new_queue

        return model_id, round_tokens, request_infos, requests_in_round

    def _process_round(
        self,
        model_id: ModelID,
        round_tokens: list[Token],
        request_infos: list[batching.RequestInRound],
        requests: list[_Request],
    ):
        assert len(request_infos) == len(requests)
        _requests_in_round_histogram.labels(self.model_name, model_id).observe(
            len(requests)
        )
        _tokens_in_round_histogram.labels(self.model_name, model_id).observe(
            len(round_tokens)
        )

        if not round_tokens:
            logger.info("Empty round. No tokens to process")
            return

        child_spans = []
        for request_info, request in zip(request_infos, requests):
            span = tracer.start_span(
                f"Round with {request_info.num_tokens} request tokens",
                context=request.telemetry_context,
            )
            span.set_attribute("request tokens in round", request_info.num_tokens)
            span.set_attribute("total tokens in round", len(round_tokens))
            child_spans.append(span)

        round_start_time = time.time()

        model = self.models[model_id]
        assert len(round_tokens) <= model.max_round_size, round_tokens

        round_ = batching.Round(
            tokens=round_tokens,
            requests_in_round=request_infos,
        )
        if self.use_sorted_batching:
            # Sort the batch and get the permutation idxs
            (
                round_,
                orig_to_sorted_request_idxs,
            ) = round_.sort_and_extend_requests_for_multirequest_attention(
                max_requests_in_round=model.max_requests_in_round,
                max_small_request_size=model.small_request_max_seqlen,
            )
            assert round_.is_multirequest_sorted
        else:
            # Otherwise, the permutation idxs are the identity
            orig_to_sorted_request_idxs = list(range(len(request_infos)))
        round_ = round_.pad_to_next_round_size(
            round_sizes=model.round_sizes,
        )
        round_size_after_padding = len(round_.tokens)
        model.round_attn.load_requests(round_)
        logits = model.step_fn(round_.tokens, model.round_attn)

        assert isinstance(logits, fwd.Logits2D)
        assert logits.ndim == 2

        # update telemetry spans while the GPU is busy
        for i, (request_in_round, request, telemetry_span) in enumerate(
            zip(request_infos, requests, child_spans)
        ):
            telemetry_span.set_attribute("num_requests_in_round", len(request_infos))
            processed_tokens = request.generation_state.get_num_processed_tokens(
                model_id
            )
            telemetry_span.set_attribute(
                "num_processed_tokens_before", processed_tokens
            )
            telemetry_span.set_attribute("model_id", model_id)

        # update requests
        for i, (request_in_round, request, telemetry_span) in enumerate(
            zip(request_infos, requests, child_spans)
        ):
            sorted_request_in_round = round_.requests_in_round[
                orig_to_sorted_request_idxs[i]
            ]
            logits_start = sorted_request_in_round.round_start_idx
            assert sorted_request_in_round.num_tokens == request_in_round.num_tokens
            assert sorted_request_in_round.cache_idx == request_in_round.cache_idx
            logits_for_request = logits[
                logits_start : logits_start + request_in_round.num_tokens
            ]
            round_tokens_for_request = round_tokens[
                request_in_round.round_start_idx : request_in_round.round_start_idx
                + request_in_round.num_tokens
            ]
            request.process_logits(
                model_id,
                logits_for_request,
                round_tokens_for_request,
                telemetry_span=telemetry_span,
            )

        # We synchronize here to avoid scheduling multiple rounds into the
        # future and enable interruptibility after every round. We will need
        # to observe how much time we waste between rounds.
        torch.cuda.synchronize()

        # Update telemetry spans only after the GPU is done;
        # This prevents us measuring the time before the GPU is done
        # processing the round.
        for request, request_in_round in zip(requests, request_infos):
            request.record_round_end_time(
                model_id, round_start_time, request_in_round.num_tokens
            )

        for child in child_spans:
            child.end()

        # limit the cardinality of the metrics by bucketing to power of 2
        def _get_power_of_two_bucket_label(n: int) -> str:
            if n == 0:
                return "0"
            # Round up to next power of 2 and get the bucket range
            next_power = 2 ** ((n - 1).bit_length())
            return str(next_power)

        batch_size_label = _get_power_of_two_bucket_label(round_size_after_padding)
        requests_in_round_label = _get_power_of_two_bucket_label(len(requests))

        _round_latency_s.labels(
            self.model_name,
            "main_model" if model_id == self.main_model_id else "speculation_model",
            requests_in_round_label,
            batch_size_label,
        ).observe(time.time() - round_start_time)
