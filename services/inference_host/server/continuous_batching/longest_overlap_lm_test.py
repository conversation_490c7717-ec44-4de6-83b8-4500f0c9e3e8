"""Tests for longest_overlap_lm."""

import pytest

from base import feature_flags
from services.inference_host.server.continuous_batching import longest_overlap_lm as lol
from services.inference_host.server.continuous_batching import speculation

feature_flags.unit_test_setup()


def test_predict_end_of_sequence_cutoff():
    """Requesting k=2, but only 1 token is available, so we should only a of length 1."""
    predictor = lol.LongestOverlapLM()
    predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])
    ps = predictor.predict_next_k_tokens([2, 3], k=2, num_predictions=1)
    assert len(ps) == 1
    assert all(len(p) <= 2 for p in ps), ps
    assert tuple(ps[0]) == (5,)


def test_multiple_predictions():
    predictor = lol.LongestOverlapLM()
    predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])
    ps = predictor.predict_next_k_tokens([2, 3], k=2, num_predictions=3)
    assert len(ps) == 3
    assert all(len(p) <= 2 for p in ps), ps
    assert tuple(ps[0]) == (5,)


def test_predict_2_tokens():
    predictor = lol.LongestOverlapLM()
    predictor.fit([1, 3, 2, 3, 4, 2, 3, 5, 6, 7])
    ps = predictor.predict_next_k_tokens([2, 3], k=2, num_predictions=1)
    assert len(ps) == 1
    assert tuple(ps[0]) == (5, 6)


def test_predict_middle_of_sequence():
    predictor = lol.LongestOverlapLM()
    predictor.fit([1, 3, 2, 3, 4, 2, 3, 5, 6, 7])
    ps = predictor.predict_next_k_tokens([2, 3, 4], k=2, num_predictions=1)
    assert len(ps) == 1
    assert tuple(ps[0]) == (2, 3)


def test_no_exact_match():
    predictor = lol.LongestOverlapLM()
    predictor.fit([1, 3, 2, 3, 4, 2, 3, 5, 6, 7])
    ps = predictor.predict_next_k_tokens([12, 3, 4], k=2, num_predictions=1)
    assert len(ps) == 1
    assert tuple(ps[0]) == (2, 3)


def test_find_match_pos():
    tokens = [1, 2, 3, 1, 3, 4, 5, 1, 3]
    assert tuple(
        lol._find_match_pos(
            [1, 2, 3, 1, 3, 4, 5, 1, 3],
            [1, 3],
            searchable_token_hashes=lol._get_hash(tokens),
        )
    ) == tuple([4, 8])


def test_raises_on_zero_predictions():
    predictor = lol.LongestOverlapLM()
    predictor.fit([1, 3, 2, 3, 4, 2, 3, 5])
    with pytest.raises(speculation.SpeculationException):
        _ = predictor.predict_next_k_tokens([2, 3], k=2, num_predictions=0)


def test_no_exact_match_rust():
    predictor = lol.LongestOverlapLMRust()
    prompt = [1, 3, 2, 3, 4, 2, 3, 5, 6, 7]
    ps = predictor.predict_next_k_tokens(prompt + [12, 3, 4], k=2, num_predictions=1)
    assert len(ps) == 1
    assert tuple(ps[0]) == (2, 3)


def test_predict_end_of_sequence_cutoff_rust():
    """Requesting k=2, but only 1 token is available, so we should only a of length 1."""
    predictor = lol.LongestOverlapLMRust()
    prompt = [1, 3, 2, 3, 4, 2, 3, 5]
    ps = predictor.predict_next_k_tokens(prompt + [2, 3], k=2, num_predictions=1)
    assert len(ps) == 1
    assert all(len(p) <= 2 for p in ps), ps
    assert tuple(ps[0]) == (5, 2)


def test_predict_2_tokens_rust():
    predictor = lol.LongestOverlapLMRust()
    prompt = [1, 3, 2, 3, 4, 2, 3, 5, 6, 7]
    ps = predictor.predict_next_k_tokens(prompt + [2, 3], k=2, num_predictions=1)
    assert len(ps) == 1
    assert tuple(ps[0]) == (5, 6)


def test_predict_middle_of_sequence_rust():
    predictor = lol.LongestOverlapLMRust()
    prompt = [1, 3, 2, 3, 4, 2, 3, 5, 6, 7]
    ps = predictor.predict_next_k_tokens(prompt + [2, 3, 4], k=2, num_predictions=1)
    assert len(ps) == 1
    assert tuple(ps[0]) == (2, 3)


def test_predict_rust_with_skips():
    predictor = lol.LongestOverlapLMRust(max_tokens_skipped_override=2)
    prompt = [4, 10, 1, 2, 3, 4, 5, 10, 99, 2, 3, 4, 10]
    ps = predictor.predict_next_k_tokens(prompt, k=1, num_predictions=1)
    assert len(ps) == 1
    assert tuple(ps[0]) == (99,)
