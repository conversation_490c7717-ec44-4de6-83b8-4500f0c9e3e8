import contextlib
import logging
import threading
from typing import Callable, Iterable, Optional, Sequence
from unittest.mock import MagicMock, patch

import grpc
import pydantic
import pytest

from services.inference_host import infer_pb2_grpc
from services.inference_host.server.continuous_batching import server
from services.inference_host.client.inference_host_client import (
    InfererClient,
)
from services.inference_host.server.continuous_batching.inference_runner import (
    InferenceException,
    InferenceResult,
    InferenceRunner,
    SamplingParams,
)
from services.lib.grpc.auth.service_auth import (
    AuthInfo,
    ServiceAuth,
    ServiceAuthException,
)
from services.lib.request_context.request_context import RequestContext

Token = int


class MockRunner(InferenceRunner):
    def __init__(self):
        self.result: InferenceResult | Exception
        self.arrivals = threading.Semaphore(0)
        self.unpause: threading.Event = threading.Event()
        self.reset()

    def reset(self):
        self.result = InferenceResult([], [])
        self.arrivals = threading.Semaphore(0)
        self.unpause.set()

    def call(self):
        self.arrivals.release()
        self.unpause.wait()
        if isinstance(self.result, Exception):
            raise self.result
        return self.result

    def call_stream(self):
        self.arrivals.release()
        self.unpause.wait()
        if isinstance(self.result, Exception):
            raise self.result
        return [self.result]

    def infer(
        self,
        request_context: RequestContext,
        auth_info: AuthInfo,
        prompt: list[Token],
        max_decode_tokens: int,
        sampling_params: Optional[SamplingParams] = None,
        end_token_ids: Sequence[int] = (),
        timeout_sec: Optional[float] = None,
        sequence_id: int = 0,
        priority: int = 0,
        sub_request_id: str = "",
        target_tokens: Optional[Sequence[Token]] = None,
    ) -> Callable[[], InferenceResult]:
        return self.call

    def infer_stream(
        self,
        request_context: RequestContext,
        auth_info: AuthInfo,
        prompt: list[Token],
        max_decode_tokens: int,
        sampling_params: Optional[SamplingParams] = None,
        end_token_ids: Sequence[int] = (),
        timeout_sec: Optional[float] = None,
        sequence_id: int = 0,
        priority: int = 0,
        sub_request_id: str = "",
        target_tokens: Optional[Sequence[Token]] = None,
    ) -> Callable[[], Iterable[InferenceResult]]:
        return self.call_stream


class MockServiceAuth(ServiceAuth):
    def __init__(self):
        self.allow_tokens: set[str] = set()

    def reset(self):
        self.allow_tokens.clear()

    def allow_token(self, token: str):
        self.allow_tokens.add(token)

    def validate_access(
        self,
        peer_identities: list[bytes] | None,
        auth_token: pydantic.SecretStr | None,
        method_name: str,
    ) -> AuthInfo:
        if auth_token and auth_token.get_secret_value() in self.allow_tokens:
            return AuthInfo(
                tenant_id="1234567890",
                tenant_name="test-tenant",
                shard_namespace="test-namespace",
                cloud="test-cloud",
            )
        raise ServiceAuthException(grpc.StatusCode.UNAUTHENTICATED, "Unauthenticated")


class MockKit:
    def __init__(self):
        # arch doesn't matter when mocking runner, but must be in the config
        arch = server.ModelArch(
            arch_type="LLAMA",
            num_layers=1,
            vocab_size=1,
            emb_dim=1,
            num_heads=1,
            head_dim=1,
        )
        # What in the config actually matters:
        # 1. port
        # 2. max_rpc_threads
        # 3. max_rpc_streams
        # 4. mtls (None)
        # Tests can read these from the kit
        self.config = server.Config(
            port=0,  # Let the server pick a port
            max_seq_length=0,
            round_sizes=[],
            model_arch=arch,
            model_name="mock",
            weights_path="mock",
            max_rpc_threads=16,
            speculation_models=[],
            auth_config=server.AuthConfig(token_exchange_endpoint="mock"),
            cache_size=1,
            server_mtls=None,
            client_mtls=None,
            max_rpc_streams=4,
        )
        self._runner = MockRunner()
        self.runner = MagicMock(spec=InferenceRunner, wraps=self._runner)
        self._service_auth = MockServiceAuth()
        self.service_auth = MagicMock(spec=ServiceAuth, wraps=self._service_auth)
        self.stub: MagicMock  # with spec=infer_pb2_grpc.InfererServicer

    def reset_mocks(self, calls_only=False):
        """If calls_only, only reset the call_args of the MagicMock wrappers,
        don't reset the mock behaviors (return values, allowed tokens, etc)
        """
        self.runner.reset_mock()
        self.service_auth.reset_mock()
        self.stub.reset_mock()
        if calls_only:
            return
        self._runner.reset()
        self._service_auth.reset()


@pytest.fixture(scope="module")
def shared_kit():
    """Start a grpc server with real InfererServices, but mock runner and auth. Return a kit to control and observe
    the mocks. The server is shared across all tests.
    """
    kit = MockKit()
    # Mock runner and auth, then serve
    mod_path = "services.inference_host.server.continuous_batching.server"
    runner_ctx = contextlib.nullcontext(enter_result=kit.runner)
    with patch(mod_path + "._inference_runner", return_value=runner_ctx), patch(
        mod_path + "._setup_service_auth", return_value=kit.service_auth
    ):
        shutdown_event = threading.Event()
        srv = server.Server(kit.config, "test-tenant", shutdown_event)
        serving_thread = threading.Thread(target=srv.serve)
        serving_thread.start()
        srv.listening.wait()

        channel = grpc.insecure_channel(f"localhost:{srv.port}")
        # Block until we can connect to server; failing if it takes too long
        grpc.channel_ready_future(channel).result(timeout=5)
        kit.stub = MagicMock(
            spec=infer_pb2_grpc.InfererServicer,
            wraps=infer_pb2_grpc.InfererStub(channel),
        )
        try:
            yield kit
        finally:
            print("Stopping test server")
            shutdown_event.set()
            serving_thread.join()
            print("Test server stopped; thread joined")


@pytest.fixture(scope="function")
def kit(shared_kit):
    """As tests share the same test server, reset the state of the mocks before
    each test"""
    shared_kit.reset_mocks()
    yield shared_kit


def infer_args(session):
    return dict(
        input_tokens=[1],
        max_output_length=1,
        end_token_ids=[1],
        top_k=1,
        top_p=1.0,
        temperature=1.0,
        random_seed=0,
        request_context=RequestContext.create_for_session(
            session, auth_token=pydantic.SecretStr(session + "_token")
        ),
        timeout_s=None,
    )


@pytest.fixture(scope="function", params=[0.0, 10.0])
def client(kit, request):
    client = InfererClient(
        lambda: kit.stub, sticky_session_seconds=request.param, sticky_session_count=100
    )
    try:
        yield client
    finally:
        client._close_all_streams()


def test_ok(kit, client):
    """Basic end-to-end to check that the right RPCs are being called, and auth
    is being checked the expected number of times. These behaviors are exercised
    more rigorously in other tests.
    """
    inject_result = InferenceResult([1, 2, 3], [])
    kit._runner.result = inject_result
    kit._service_auth.allow_token("session_1_token")

    result = client.infer(**infer_args("session_1"))  # type: ignore
    assert result.output_tokens == inject_result.tokens

    rpc_stream = client.sticky_session_seconds > 0.0
    if rpc_stream:
        assert kit.stub.Infer.call_count == 0
        assert kit.stub.StreamingInfer.call_count == 1
        # Once for the stream, once for the request itself
        assert kit.service_auth.validate_access.call_count == 2
    else:
        assert kit.stub.Infer.call_count == 1
        assert kit.stub.StreamingInfer.call_count == 0
        assert kit.service_auth.validate_access.call_count == 1
    assert kit.runner.infer.call_count == 1

    kit.reset_mocks(calls_only=True)

    # Call again
    client.infer(**infer_args("session_1"))  # type: ignore

    if rpc_stream:
        # No additional RPC calls, but one additional auth call
        assert kit.stub.Infer.call_count == 0
        assert kit.stub.StreamingInfer.call_count == 0
        assert kit.service_auth.validate_access.call_count == 1
    else:
        assert kit.stub.Infer.call_count == 1
        assert kit.stub.StreamingInfer.call_count == 0
        assert kit.service_auth.validate_access.call_count == 1
    assert kit.runner.infer.call_count == 1

    client._close_all_streams()


def test_auth(kit, client):
    inject_result = InferenceResult([1, 2, 3], [])
    kit._runner.result = inject_result

    args = infer_args("session_1")
    with pytest.raises(grpc.RpcError) as ex:
        client.infer(**args)  # type: ignore
    assert ex.value.code() == grpc.StatusCode.UNAUTHENTICATED
    # Either Infer or StreamingInfer is unauthenticated, and nothing
    # is retried
    assert kit.service_auth.validate_access.call_count == 1
    assert kit.stub.Infer.call_count + kit.stub.StreamingInfer.call_count == 1

    # Now allow the call. Make several calls; we should check each time.
    kit.reset_mocks(calls_only=True)
    kit._service_auth.allow_token("session_1_token")
    n_calls = 10
    for n in range(n_calls):
        result = client.infer(**args)
        assert result.output_tokens == inject_result.tokens
    rpc_stream = client.sticky_session_seconds > 0.0
    if rpc_stream:
        assert kit.stub.Infer.call_count == 0
        assert kit.stub.StreamingInfer.call_count == 1
        # Add one for the stream auth itself
        assert kit.service_auth.validate_access.call_count == n_calls + 1
    else:
        assert kit.stub.Infer.call_count == n_calls
        assert kit.stub.StreamingInfer.call_count == 0
        assert kit.service_auth.validate_access.call_count == n_calls
    assert kit.runner.infer.call_count == n_calls

    # Remaining behavior is only for StreamingInfer
    if not rpc_stream:
        return

    # Disallow the token again
    kit._service_auth.reset()

    # The stream stays up, but individual messages over the stream will be rejected
    # due to auth. This should be routed back to the client correctly.
    kit.reset_mocks(calls_only=True)
    for n in range(n_calls):
        with pytest.raises(grpc.RpcError) as ex:
            client.infer(**args)  # type: ignore
        assert ex.value.code() == grpc.StatusCode.UNAUTHENTICATED
    assert kit.service_auth.validate_access.call_count == n_calls
    assert kit.stub.StreamingInfer.call_count == 0
    # No calls should make it to the runner
    assert kit.runner.infer.call_count == 0

    # Re-allow
    kit._service_auth.allow_token("session_1_token")
    kit.reset_mocks(calls_only=True)
    client.infer(**args)
    assert kit.service_auth.validate_access.call_count == 1
    assert kit.runner.infer.call_count == 1


def test_cancel(kit, client):
    """If the runner raises a cancellation error, that should be plumbed back
    to the client as cancellation (not UNKNOWN, which is an easy bug to write
    if the error is not caught by the RPC handler method)
    """
    kit._runner.result = InferenceException(grpc.StatusCode.CANCELLED, "Cancelled")
    kit._service_auth.allow_token("session_1_token")

    args = infer_args("session_1")
    with pytest.raises(grpc.RpcError) as ex:
        client.infer(**args)  # type: ignore
    assert ex.value.code() == grpc.StatusCode.CANCELLED

    # Cancellation of individual requests doesn't bring down RPC stream
    for _ in range(10):
        with pytest.raises(grpc.RpcError):
            client.infer(**args)  # type: ignore
    kit._runner.result = InferenceResult([1, 2, 3], [])
    client.infer(**args)  # type: ignore

    assert kit.runner.infer.call_count == 12
    if client.sticky_session_seconds > 0.0:
        assert kit.stub.Infer.call_count == 0
        assert kit.stub.StreamingInfer.call_count == 1
    else:
        assert kit.stub.Infer.call_count == 12
        assert kit.stub.StreamingInfer.call_count == 0


def test_other_error(kit, client):
    """If the runner raises a non-cancellation error, that should be plumbed back
    to the client for that request alone, leaving the stream running to handle
    future / concurrent requests.
    """
    kit._runner.result = InferenceException(grpc.StatusCode.INTERNAL, "Internal")
    kit._service_auth.allow_token("session_1_token")

    args = infer_args("session_1")

    with pytest.raises(grpc.RpcError) as unary_ex:
        client.infer(**args)  # type: ignore
    assert unary_ex.value.code() == grpc.StatusCode.INTERNAL
    assert unary_ex.value.details() == "Internal"

    kit.reset_mocks()
    kit._service_auth.allow_token("session_1_token")
    client.infer(**args)  # type: ignore
    # Prior error didn't tear down the stream; it was re-used (or this
    # is the test of Unary RPC client)
    assert kit.stub.StreamingInfer.call_count == 0
    assert kit.runner.infer.call_count == 1


def test_max_rpc_streams(kit):
    """Issue requests form double the number of sessions which the server supports
    streams for. Current design and expected behavior is to reject new streams once
    the limit has been reached. The client will retry via unary RPC.
    Evicting idle streams on the server is a potential future.
    """
    client = InfererClient(
        lambda: kit.stub, sticky_session_seconds=100.0, sticky_session_count=100
    )
    kit._runner.result = InferenceResult([1, 2, 3], [])

    # Issue requests from double the number of sessions which the server supports
    # streams for.
    streams = kit.config.max_rpc_streams
    assert streams > 0
    for i in range(2 * streams):
        kit._service_auth.allow_token(f"session_{i}_token")
        result = client.infer(**infer_args(f"session_{i}"))  # type: ignore
        assert result.output_tokens == kit._runner.result.tokens
        logging.info(f"Session {i} result: {result}")

    # All attempted to use streams; half fell back to unary
    assert kit.stub.Infer.call_count == streams
    assert kit.stub.StreamingInfer.call_count == 2 * streams
    assert kit.runner.infer.call_count == 2 * streams

    # The first half of sessions still have a usable stream to the server,
    # which additional calls will utilize
    kit.reset_mocks(calls_only=True)
    for i in range(streams):
        result = client.infer(**infer_args(f"session_{i}"))  # type: ignore
        assert result.output_tokens == kit._runner.result.tokens
    assert kit.stub.Infer.call_count == 0
    assert kit.stub.StreamingInfer.call_count == 0  # No new streams
    assert kit.runner.infer.call_count == streams

    client._close_all_streams()
