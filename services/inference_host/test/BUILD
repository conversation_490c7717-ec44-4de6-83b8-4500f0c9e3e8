load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg_multi")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

kubecfg_multi(
    name = "test_kubecfg",
    deps = [
        "//services/deploy:shard_namespace_base_kubecfg",
        "//services/deploy:tenant_config_kubecfg",
        "//services/deploy/completion:rogue_1B_fp8_starethanol6_16_1_proj512_kubecfg",
        "//services/deploy/completion:starcoder2_100m_fp8_kubecfg",
        "//services/deploy/completion:starcoder2_100m_fp8_sequence_parallel_kubecfg",
        "//services/request_insight:core_kubecfg",
        "//services/tenant_watcher/server:kubecfg",
        "//services/test/fake_feature_flags:kubecfg",
        "//services/token_exchange/server:kubecfg",
    ],
)

pytest_test(
    name = "inference_host_test",
    size = "enormous",
    timeout = "eternal",
    srcs = [
        "conftest.py",
        "inference_host_test.py",
    ],
    data = [
        ":test_kubecfg",
        "@k8s_binary//file:kubectl",
    ],
    tags = [
        "exclusive",
        "gcp",
        "postmerge-test",
        "system-test",
        "system-test-gpu",
    ],
    deps = [
        "//base/blob_names/python:blob_names",
        "//base/fastforward/starcoder:sample_data",
        "//base/python/grpc:health_check",
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        "//services/inference_host/client",
        "//services/tenant_watcher/client",
        "//services/token_exchange/client:client_py",
        requirement("kubernetes"),
    ],
)
