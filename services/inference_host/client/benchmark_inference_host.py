"""Utility to run requests against an inference host."""

import argparse
import json
import logging
import pandas as pd
import pathlib
import time

from base.logging.console_logging import setup_console_logging
from services.inference_host.client import inference_host_client


TIMEOUT_S = 300


def main(args: argparse.Namespace):
    """Main entry function."""
    with pathlib.Path(args.input_path).open("r", encoding="utf8") as f:
        data = json.load(f)

    if args.take_first_n_samples is not None:
        data = data[: args.take_first_n_samples]

    # Setup the client
    stub = inference_host_client.create_inference_stub(args.endpoint)
    client = inference_host_client.InfererClient(lambda: stub)
    request_context = inference_host_client.RequestContext.create()

    def run(input_tokens, max_decode_tokens):
        return client.infer(
            input_tokens=input_tokens,
            max_output_length=max_decode_tokens,
            end_token_ids=args.end_token_ids,
            top_k=1,
            top_p=1.0,
            temperature=1.0,
            random_seed=0,
            request_context=request_context,
            timeout_s=TIMEOUT_S,
        ).output_tokens

    stats = []
    for index, sample in enumerate(data):
        context = sample["context"]

        datum: dict[str, int | float] = {
            "context_length": len(context),
        }
        max_decode_tokens = args.max_seq_length - len(context)
        assert max_decode_tokens > 0

        if args.measure_context_processing_separately:
            start_time = time.time()
            predictions = run(context, 1)
            datum["context_processing_latency_ms"] = 1000.0 * (time.time() - start_time)

        # If `measure_context_processing_separately` then we have cached the context processing,
        # so another `run` call only only measure the generation latency.
        start_time = time.time()
        predictions = run(context, max_decode_tokens)
        current_latency_ms = 1000.0 * (time.time() - start_time)
        datum.update(
            {
                "generation_length": len(predictions),
                "generation_latency_ms": current_latency_ms,
            }
        )
        if "label" in sample:
            datum["exact_match"] = int(predictions == sample["label"])
        stats.append(datum)
        print(datum)

    df = pd.DataFrame(stats)
    df.to_csv(args.output_prefix + ".csv", index=False)
    logging.info(f"Processed {len(stats)} samples. Average statistics:")
    print(df.describe())


def _get_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser()
    parser.add_argument("--endpoint", default="localhost:8888")
    parser.add_argument(
        "--input_path",
        required=True,
        type=str,
        help="Path to the input data",
    )
    parser.add_argument(
        "--output_prefix",
        required=True,
        type=str,
        help="Output path to save stats and metrics",
    )
    parser.add_argument(
        "--max_seq_length",
        required=True,
        type=int,
        default=None,
        help="Max sequence length (will decode up to this length)",
    )
    parser.add_argument(
        "--end_token_ids",
        metavar="N",
        type=int,
        nargs="+",
        default=[32014, 32021],
        help="end of generation token ids (default is droid-specific: 32014, 32021)",
    )
    parser.add_argument(
        "--take_first_n_samples",
        required=False,
        type=int,
        default=None,
        help="Number of samples to consider. Default: all",
    )
    parser.add_argument(
        "--measure_context_processing_separately", action="store_true", default=False
    )
    return parser.parse_args()


if __name__ == "__main__":
    setup_console_logging()
    args = _get_args()
    logging.debug("args %s", args)
    main(args)
