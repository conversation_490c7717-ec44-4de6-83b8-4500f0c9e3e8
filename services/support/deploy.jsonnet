// K8S deployment file for the support web UI
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';
function(env, namespace, cloud, namespace_config)
  local appName = 'support-ui';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, cloud=cloud, namespace=namespace, appName=appName);
  // create a client TLS certificate to securely access the request insight system.
  local clientCert = certLib.createClientCert(
    name='support-ui-client-certificate',
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
  );

  local centralClientCert = certLib.createCentralClientCert(
    name='support-ui-central-client-certificate',
    appName=appName,
    namespace=namespace,
    env=env,
    volumeName='central-client-certs',
    dnsNames=['support-ui-svc.' + namespace],
  );
  local ingressHostname = endpoints.get_support_hostname(env=env, cloud=cloud, namespace=namespace);
  local ingressFacingCert = certLib.createPublicServerCert(name='support-ui-public-server-certificate',
                                                           namespace=namespace,
                                                           appName=appName,
                                                           dnsNames=[ingressHostname],
                                                           env=env,
                                                           volumeName='https-certs');
  local backendConfig = gcpLib.createBackendConfig(app=appName,
                                                   cloud=cloud,
                                                   namespace=namespace,
                                                   healthCheck={
                                                     checkIntervalSec: 15,
                                                     port: 5000,
                                                     type: 'HTTPS',
                                                     requestPath: '/health',
                                                   },
                                                   iap=true);
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'support-ui-svc',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        'cloud.google.com/app-protocols': std.manifestJson({ 'public-https': 'HTTPS' }),
      },
    },
    spec: {
      type: 'NodePort',
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 443,
          name: 'public-https',
          targetPort: 'public-https',
        },
      ],
    },
  };
  // IAP audience checked via prefix. Can be extended by namespace config
  local iapAudience = '/projects/%s/global/backendServices/%s' % [cloudInfo[cloud].projectNumber, if std.objectHas(namespace_config, 'iapAudience') then namespace_config.iapAudience else ''];
  local tokenExchangeEndpoint = endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace);
  local tenantWatcherGrpcUrl = endpoints.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud);
  local config = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: 'support-ui-config',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
    },
    data: {
      'flask.cfg': |||
        REQUEST_INSIGHT_CENTRAL_GRPC_URL="%(requestInsightCentralGprcUrl)s"
        CONTENT_MANAGER_GRPC_URL="content-manager-svc:50051"
        MODEL_FINDER_GRPC_URL="model-finder-svc:50051"
        GRPC_DEBUG_GRPC_URL="%(grpcDebugGrpcUrl)s"
        AUTH_CENTRAL_GRPC_URL="%(authCentralGrpcUrl)s:50051"
        TOKEN_EXCHANGE_GRPC_URL="%(tokenExchangeEndpoint)s"
        TENANT_WATCHER_GRPC_URL="%(tenantWatcherGrpcUrl)s"
        PORT=5000
        CLIENT_MTLS=%(clientMtls)s
        CA_CERT="/client-certs/ca.crt"
        CLIENT_KEY="/client-certs/tls.key"
        CLIENT_CERT="/client-certs/tls.crt"
        CENTRAL_CA_CERT="/central-client-certs/ca.crt"
        CENTRAL_CLIENT_KEY="/central-client-certs/tls.key"
        CENTRAL_CLIENT_CERT="/central-client-certs/tls.crt"
        HTTPS_SERVER_KEY="/https-certs/tls.key"
        HTTPS_SERVER_CERT="/https-certs/tls.crt"
        IAP_AUDIENCE="%(iapAudience)s"
        NAMESPACE="%(namespace)s"
        FEATURE_FLAGS_SDK_KEY_PATH="%(featureFlagsSdkKeyPath)s"
        IAP_JWT_VERIFIER_DISABLED=%(iapJwtVerifierDisabled)s
        GENIE_URL="https://genie.%(internalDomainSuffix)s"
        DYNAMIC_FEATURE_FLAGS_ENDPOINT="%(dynamic_feature_flags_endpoint)s"
      ||| % {
        requestInsightCentralGprcUrl: endpoints.getRequestInsightCentralGrpcUrl(env=env, location=std.substr(cloudInfo[cloud].region, 0, 2)),
        clientMtls: if mtls then 'True' else 'False',
        iapAudience: iapAudience,
        namespace: namespace,
        grpcDebugGrpcUrl: 'grpc-debug-svc:50051',
        authCentralGrpcUrl: endpoints.getAuthCentralGrpcUrl(env=env, cloud=cloud, namespace=namespace),
        tenantWatcherGrpcUrl: tenantWatcherGrpcUrl,
        tokenExchangeEndpoint: tokenExchangeEndpoint,
        featureFlagsSdkKeyPath: dynamicFeatureFlags.secretsFilePath,
        iapJwtVerifierDisabled: if namespace_config.flags.iapJwtVerifierDisabled then 'True' else 'False',
        internalDomainSuffix: cloudInfo[cloud].internalDomainSuffix,
        dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else '',
      },
    },
  };

  local serviceAccount = gcpLib.createServiceAccount(app='support-ui', cloud=cloud, env=env, namespace=namespace);
  local container =
    {
      name: 'support-ui',
      target: {
        name: '//services/support/backend:image',
        dst: 'support-ui',
      },
      ports: [
        {
          containerPort: 5000,
          name: 'public-https',
        },
      ],
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
        clientCert.volumeMountDef,
        centralClientCert.volumeMountDef,
        ingressFacingCert.volumeMountDef,
        {
          mountPath: '/tmp/prometheus_multiproc_dir',
          name: 'prometheus-multiproc-dir',
        },
        dynamicFeatureFlags.volumeMountDef,
      ],
      env: lib.flatten([
        {
          name: 'CONFIG_FILE',
          value: '/config/flask.cfg',
        },
        {
          name: 'PROMETHEUS_MULTIPROC_DIR',
          value: '/tmp/prometheus_multiproc_dir',
        },
        telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      ]),
      readinessProbe: {
        httpGet: {
          scheme: 'HTTPS',
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 5,
        periodSeconds: 10,
      },
      livenessProbe: {
        httpGet: {
          scheme: 'HTTPS',
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 15,
        periodSeconds: 20,
      },
      resources: {
        limits: {
          cpu: 1,
          memory: '4Gi',
        },
      },
    };
  local pod =
    {
      priorityClassName: cloudInfo.envToPriorityClass(env),
      serviceAccountName: serviceAccount.name,
      tolerations: tolerations,
      affinity: affinity,
      containers: [
        container,
      ],
      volumes: [
        {
          name: 'prometheus-multiproc-dir',
          emptyDir: {},
        },
        clientCert.podVolumeDef,
        centralClientCert.podVolumeDef,
        ingressFacingCert.podVolumeDef,
        {
          name: 'config',
          configMap: {
            name: 'support-ui-config',
            items: [
              {
                key: 'flask.cfg',
                path: 'flask.cfg',
              },
            ],
          },
        },
        dynamicFeatureFlags.podVolumeDef,
      ],
    };
  local frontendConfig = gcpLib.createFrontendConfig(app=appName, cloud=cloud, namespace=namespace);
  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: appName,
        },
        name: 'support-ui-ingress',
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: 'support-ssl-cert',  // pragma: allowlist secret
            hosts: [ingressHostname],
          },
        ],
        rules: [
          {
            host: ingressHostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: 'support-ui-svc',
                      port: {
                        number: 443,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'support-ui',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };
  lib.flatten([
    clientCert.objects,
    centralClientCert.objects,
    ingressFacingCert.objects,
    config,
    service,
    deployment,
    serviceAccount.objects,
    ingressObjects,
    dynamicFeatureFlags.k8s_objects,
  ])
