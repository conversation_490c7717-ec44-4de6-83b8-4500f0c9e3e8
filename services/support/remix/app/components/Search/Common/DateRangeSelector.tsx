import { Flex, Text, Select } from "@radix-ui/themes";

type DateRangeValidationError = {
  dateRange?: string;
};

type DateRangeSelectorProps = {
  startTime: string;
  endTime: string;
  onStartTimeChange: (value: string) => void;
  onEndTimeChange: (value: string) => void;
  validationErrors: DateRangeValidationError;
};

type TimePreset = {
  label: string;
  getValue: () => { startTime: string; endTime: string };
};

const TIME_PRESETS: TimePreset[] = [
  {
    label: "Last hour",
    getValue: () => {
      const now = new Date();
      const startTime = new Date(now);
      startTime.setHours(now.getHours() - 1);
      return {
        startTime: startTime
          .toLocaleString("sv")
          .replace(" ", "T")
          .slice(0, 16),
        endTime: now.toLocaleString("sv").replace(" ", "T").slice(0, 16),
      };
    },
  },
  {
    label: "Last 24 hours",
    getValue: () => {
      const now = new Date();
      const startTime = new Date(now);
      startTime.setDate(now.getDate() - 1);
      return {
        startTime: startTime
          .toLocaleString("sv")
          .replace(" ", "T")
          .slice(0, 16),
        endTime: now.toLocaleString("sv").replace(" ", "T").slice(0, 16),
      };
    },
  },
  {
    label: "Last week",
    getValue: () => {
      const now = new Date();
      const startTime = new Date(now);
      startTime.setDate(now.getDate() - 7);
      return {
        startTime: startTime
          .toLocaleString("sv")
          .replace(" ", "T")
          .slice(0, 16),
        endTime: now.toLocaleString("sv").replace(" ", "T").slice(0, 16),
      };
    },
  },
  {
    label: "Last month",
    getValue: () => {
      const now = new Date();
      const startTime = new Date(now);
      startTime.setMonth(now.getMonth() - 1);
      return {
        startTime: startTime
          .toLocaleString("sv")
          .replace(" ", "T")
          .slice(0, 16),
        endTime: now.toLocaleString("sv").replace(" ", "T").slice(0, 16),
      };
    },
  },
  {
    label: "Last 3 months",
    getValue: () => {
      const now = new Date();
      const startTime = new Date(now);
      startTime.setMonth(now.getMonth() - 3);
      return {
        startTime: startTime
          .toLocaleString("sv")
          .replace(" ", "T")
          .slice(0, 16),
        endTime: now.toLocaleString("sv").replace(" ", "T").slice(0, 16),
      };
    },
  },
  {
    label: "Last 6 months",
    getValue: () => {
      const now = new Date();
      const startTime = new Date(now);
      startTime.setMonth(now.getMonth() - 6);
      return {
        startTime: startTime
          .toLocaleString("sv")
          .replace(" ", "T")
          .slice(0, 16),
        endTime: now.toLocaleString("sv").replace(" ", "T").slice(0, 16),
      };
    },
  },
];

export const getDefaultDates = () => {
  const now = new Date();
  const startTime = new Date(now);
  startTime.setDate(now.getDate() - 7);

  return {
    startTime: startTime.toLocaleString("sv").replace(" ", "T").slice(0, 16),
    endTime: now.toLocaleString("sv").replace(" ", "T").slice(0, 16),
  };
};

export const validateDateRange = (
  startTime: string,
  endTime: string,
): boolean => {
  if (startTime && endTime) {
    // Create Date objects from the local datetime strings
    const startDate = new Date(startTime);
    const endDate = new Date(endTime);

    if (startDate > endDate) {
      return false;
    }
  }
  return true;
};

export default function DateRangeSelector({
  startTime,
  endTime,
  onStartTimeChange,
  onEndTimeChange,
  validationErrors,
}: DateRangeSelectorProps) {
  const handleStartTimeChange = (value: string) => {
    if (value) {
      onStartTimeChange(value);
    }
  };

  const handleEndTimeChange = (value: string) => {
    if (value) {
      onEndTimeChange(value);
    }
  };

  const handlePresetChange = (value: string) => {
    const preset = TIME_PRESETS.find((p) => p.label === value);
    if (preset) {
      const { startTime: newStartTime, endTime: newEndTime } =
        preset.getValue();
      onStartTimeChange(newStartTime);
      onEndTimeChange(newEndTime);
    }
  };

  return (
    <Flex gap="2" align="end" justify="end">
      <Flex direction="column" gap="1">
        <Text size="1" color="gray">
          Time Range
        </Text>
        <Select.Root
          defaultValue="Last week"
          onValueChange={handlePresetChange}
        >
          <Select.Trigger
            placeholder="Quick select..."
            style={{
              boxSizing: "border-box",
              height: "38px",
              padding: "6px 8px",
              borderRadius: "4px",
              border: "1px solid var(--gray-a7)",
              backgroundColor: "white",
              lineHeight: "normal",
              minHeight: "38px",
              maxHeight: "38px",
              width: "140px", // Added fixed width to accommodate longest option
            }}
          />
          <Select.Content>
            {TIME_PRESETS.map((preset) => (
              <Select.Item key={preset.label} value={preset.label}>
                {preset.label}
              </Select.Item>
            ))}
          </Select.Content>
        </Select.Root>
      </Flex>

      <Flex direction="column" gap="1">
        <Text size="1" color="gray">
          Start Time (Local)
        </Text>
        <input
          type="datetime-local"
          step="1"
          value={startTime}
          onChange={(e) => handleStartTimeChange(e.target.value)}
          onInput={(e) => handleStartTimeChange(e.currentTarget.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              e.preventDefault();
            }
          }}
          required
          style={{
            padding: "6px 8px",
            borderRadius: "4px",
            border: "1px solid var(--gray-a7)",
            width: "auto",
          }}
        />
      </Flex>

      <Flex direction="column" gap="1">
        <Text size="1" color="gray">
          End Time (Local)
        </Text>
        <input
          type="datetime-local"
          step="1"
          value={endTime}
          onChange={(e) => handleEndTimeChange(e.target.value)}
          onInput={(e) => handleEndTimeChange(e.currentTarget.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              e.preventDefault();
            }
          }}
          required
          style={{
            padding: "6px 8px",
            borderRadius: "4px",
            border: "1px solid var(--gray-a7)",
            width: "auto",
          }}
        />
      </Flex>
      {validationErrors.dateRange && (
        <Text color="red" size="1">
          {validationErrors.dateRange}
        </Text>
      )}
    </Flex>
  );
}
