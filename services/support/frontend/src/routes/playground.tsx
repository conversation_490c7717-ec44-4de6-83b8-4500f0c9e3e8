import React from "react";
import { useParams } from "react-router-dom";
import { LayoutComponent } from "../lib/layout";
import { ReplayRequest } from "../components/replay/ReplayRequest";
import { Alert } from "antd";
import { getTenantIdForName, useTenantInfo } from "../contexts/tenant_provider";
import { TenantInfoComponent } from "../components/tenant_component";

export default function PlaygroundComponent() {
  const { requestId, tenantName }: any = useParams();

  function setTenantInfo(tenantName: string, tenantId: string) {
    const breadcrumbs = [
      { label: "Playground", link: `/t/${tenantName}/playground` },
    ];

    if (requestId) {
      breadcrumbs.push({
        label: "Replay",
        link: `/t/${tenantName}/playground/${requestId}`,
      });
    }

    return (
      <LayoutComponent
        children={
          requestId && (
            <ReplayRequest
              requestId={requestId}
              tenantId={tenantId}
              tenantName={tenantName}
            />
          )
        }
        selectedMenuKey={"playground"}
        breadcrumbs={breadcrumbs}
        selectedTenantName={tenantName}
        pageSuffix={`playground/${requestId}`}
      />
    );
  }
  return (
    <TenantInfoComponent
      tenantName={tenantName}
      setTenantInfo={setTenantInfo}
      pageSuffix={`playground/${requestId}`}
    />
  );
}
