import React, { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { LayoutComponent } from "../lib/layout";
import { ClearContent } from "../components/ClearContent";
import { TenantInfoComponent } from "../components/tenant_component";

export default function ClearContentComponent() {
  const { tenantName, userID } = useParams();
  const navigate = useNavigate();
  const [tenantId, setTenantId] = useState<string | undefined>(undefined);

  function handleClose() {
    if (tenantName) {
      navigate(`/t/${tenantName}/content`);
    } else {
      navigate("/content");
    }
  }

  function setTenantInfo(tenantName: string, tenantId: string) {
    setTenantId(tenantId);

    return (
      <LayoutComponent
        selectedMenuKey={"content"}
        breadcrumbs={[
          { label: "Content", link: `/t/${tenantName}/content` },
          { label: `Clear Content - ${userID}`, link: "" },
        ]}
        selectedTenantName={tenantName}
        showTenantDropdown={true}
      >
        <ClearContent
          tenantId={tenantId}
          userId={userID!}
          onClose={handleClose}
        />
      </LayoutComponent>
    );
  }

  return (
    <TenantInfoComponent
      tenantName={tenantName}
      setTenantInfo={setTenantInfo}
      pageSuffix="clear-content"
    />
  );
}
