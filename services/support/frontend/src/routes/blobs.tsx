import { <PERSON><PERSON>, Button, Form, Input } from "antd";
import { LayoutComponent } from "../lib/layout";
import { useNavigate, useParams } from "react-router-dom";
import { TenantInfoComponent } from "../components/tenant_component";

type BlobNameSearchForm = {
  blobName: string;
};

type ClearContentForm = {
  userId: string;
};

function BlobNameSearchComponent({
  tenantId,
  tenantName,
}: {
  tenantId: string;
  tenantName: string;
}) {
  const [blobNameForm] = Form.useForm();
  const navigate = useNavigate();

  const onFormFinish = (values: BlobNameSearchForm) => {
    navigate(`/t/${tenantName}/content/blob/${values.blobName}`);
  };

  return (
    <Form
      name="blob_name"
      form={blobNameForm}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      style={{ maxWidth: 600 }}
      initialValues={{ remember: true }}
      onFinish={onFormFinish}
      autoComplete="off"
    >
      <Form.Item
        label="Blob Name"
        name="blobName"
        rules={[{ required: true, message: "Please input the blob name" }]}
      >
        <Input />
      </Form.Item>

      <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
        <Button type="primary" htmlType="submit">
          Submit
        </Button>
      </Form.Item>
    </Form>
  );
}

function ClearContentSearchComponent({
  tenantId,
  tenantName,
}: {
  tenantId: string;
  tenantName: string;
}) {
  const [clearContentForm] = Form.useForm();
  const navigate = useNavigate();

  const onFormFinish = (values: ClearContentForm) => {
    navigate(`/t/${tenantName}/clear-content/${values.userId}`);
  };

  return (
    <Form
      name="clear_content"
      form={clearContentForm}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      style={{ maxWidth: 600 }}
      initialValues={{ remember: true }}
      onFinish={onFormFinish}
      autoComplete="off"
    >
      <Form.Item
        label="User ID"
        name="userId"
        rules={[{ required: true, message: "Please input the user ID" }]}
      >
        <Input placeholder="Enter user ID to check content before clearing" />
      </Form.Item>

      <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
        <Button type="primary" htmlType="submit">
          Check User Content
        </Button>
      </Form.Item>
    </Form>
  );
}

function BlobsComponent() {
  const { tenantName }: any = useParams();

  function setTenantInfo(tenantName: string, tenantId: string) {
    const children = (
      <div>
        <div style={{ marginBottom: "40px" }}>
          <h2>Search Blob</h2>
          <BlobNameSearchComponent
            tenantId={tenantId}
            tenantName={tenantName}
          />
        </div>

        <div style={{ marginBottom: "40px" }}>
          <h2>Manage User Content</h2>
          <ClearContentSearchComponent
            tenantId={tenantId}
            tenantName={tenantName}
          />
        </div>
      </div>
    );
    return (
      <LayoutComponent
        children={children}
        selectedMenuKey={"content"}
        breadcrumbs={[{ label: "Content", link: `t/${tenantName}/content` }]}
        selectedTenantName={tenantName}
        showTenantDropdown={true}
        pageSuffix="content"
      />
    );
  }

  return (
    <TenantInfoComponent
      tenantName={tenantName}
      setTenantInfo={setTenantInfo}
      pageSuffix="content"
    />
  );
}

export default BlobsComponent;
