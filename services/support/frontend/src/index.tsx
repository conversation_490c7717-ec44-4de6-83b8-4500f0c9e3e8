import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import App from "./App";
import ErrorPage from "./error-page";
import RequestPageComponent from "./routes/request";
import RequestsComponent from "./routes/requests";
import PlaygroundComponent from "./routes/playground";
import BlobPageComponent, { TransformedBlobPageComponent } from "./routes/blob";
import BlobsComponent from "./routes/blobs";
import UsersComponent from "./routes/users";
import CheckpointPageComponent from "./routes/checkpoint";
import DeleteUserComponent from "./routes/users-delete";
import UpdateUserComponent from "./routes/users-update";
import ClearContentComponent from "./routes/clear-content";
import { FeatureFlagsProvider } from "./contexts/FeatureFlagsProvider";
import ServiceTokenPageComponent from "./routes/service_token";
import { TenantInfoProvider } from "./contexts/tenant_provider";
import TenantsPageComponent from "./routes/tenants";
import GrpcDebugPageComponent from "./routes/grpc_debug";
import RemoteAgentsLogsComponent from "./routes/remote_agents_logs";

// sets up the different routes
const router = createBrowserRouter([
  {
    path: "/",
    element: <App />,
    errorElement: <ErrorPage />,
  },
  {
    path: "t/:tenantName",
    element: <App />,
    errorElement: <ErrorPage />,
  },
  {
    path: "/tenants",
    element: <TenantsPageComponent />,
  },
  {
    path: "/requests",
    element: <RequestsComponent />,
  },
  {
    path: "t/:tenantName/requests",
    element: <RequestsComponent />,
  },
  {
    path: "request/:requestId",
    element: <RequestPageComponent />,
  },
  {
    path: "t/:tenantName/request/:requestId",
    element: <RequestPageComponent />,
  },
  {
    path: "playground",
    element: <PlaygroundComponent />,
  },
  {
    path: "t/:tenantName/playground",
    element: <PlaygroundComponent />,
  },
  {
    path: "playground/:requestId",
    element: <PlaygroundComponent />,
  },
  {
    path: "t/:tenantName/playground/:requestId",
    element: <PlaygroundComponent />,
  },
  {
    path: "content/blob/:blobName",
    element: <BlobPageComponent />,
  },
  {
    path: "t/:tenantName/content/blob/:blobName",
    element: <BlobPageComponent />,
  },
  {
    path: "content/blob/:blobName/:transformationKey/:subKey",
    element: <TransformedBlobPageComponent />,
  },
  {
    path: "t/:tenantName/content/blob/:blobName/:transformationKey/:subKey",
    element: <TransformedBlobPageComponent />,
  },
  {
    path: "t/:tenantName/content/checkpoint/:checkpointId",
    element: <CheckpointPageComponent />,
  },
  {
    path: "content/checkpoint/:checkpointId",
    element: <CheckpointPageComponent />,
  },
  {
    path: "t/:tenantName/content",
    element: <BlobsComponent />,
  },
  {
    path: "content",
    element: <BlobsComponent />,
  },

  {
    path: "t/:tenantName/users/delete/:userID",
    element: <DeleteUserComponent />,
  },
  {
    path: "users/delete/:userID",
    element: <DeleteUserComponent />,
  },
  {
    path: "t/:tenantName/users/update/:userID",
    element: <UpdateUserComponent />,
  },
  {
    path: "t/:tenantName/clear-content/:userID",
    element: <ClearContentComponent />,
  },
  {
    path: "clear-content/:userID",
    element: <ClearContentComponent />,
  },
  {
    path: "t/:tenantName/users",
    element: <UsersComponent />,
  },
  {
    path: "users",
    element: <UsersComponent />,
  },
  {
    path: "service-token",
    element: <ServiceTokenPageComponent />,
  },
  {
    path: "grpc-debug",
    element: <GrpcDebugPageComponent />,
  },
  {
    path: "t/:tenantName/remote-agents-logs",
    element: <RemoteAgentsLogsComponent />,
  },
  {
    path: "remote-agents-logs",
    element: <RemoteAgentsLogsComponent />,
  },
]);

const root = ReactDOM.createRoot(document.getElementById("root")!);
root.render(
  <React.StrictMode>
    <TenantInfoProvider>
      <FeatureFlagsProvider>
        <RouterProvider router={router} />
      </FeatureFlagsProvider>
    </TenantInfoProvider>
  </React.StrictMode>,
);
