import axios from "axios";

export type BlobMetadata = {
  key: string;
  value: string;
};

export type BlobInfoData = {
  contentHash: string;
  size: number;
  metadata?: BlobMetadata[];
  informedTransformationKeys?: string[];
  uploadedTransformationKeys?: string[];
  time?: string;
};

export type AnnIndexInfo = {
  transformationKey: string;
  indexId: string;
  // These blob names are hex encoded.
  blobsAddedToAnnIndex: string[];
  blobsRemovedFromAnnIndex: string[];
};

export type CheckpointInfoData = {
  // These blob names are hex encoded.
  blobNames: string[];
  annIndices: AnnIndexInfo[];
};

export async function getBlobInfo(
  tenantId: string,
  blobName: string,
  transformationKey: string,
  subKey: string,
): Promise<BlobInfoData> {
  const { data: response }: { data: any } = await axios.get(
    `/api/tenant/${tenantId}/blob_info/${blobName}`,
    {
      params: {
        transformation_key: transformationKey,
        sub_key: subKey,
      },
    },
  );
  return response;
}

export type BlobInfoRequest = {
  blobName?: string;
  transformationKey?: string;
  subKey?: string;
};

export type BatchBlobInfoResponse = {
  blobContentKey: BlobInfoRequest;
  blobInfo: BlobInfoData;
};

export async function getBatchedBlobInfo(
  tenantId: string,
  keys: BlobInfoRequest[],
): Promise<BatchBlobInfoResponse[]> {
  const { data: response }: { data: any } = await axios.post(
    `/api/tenant/${tenantId}/batch_blob_infos`,
    {
      keys: keys,
    },
  );
  return response.blobInfos;
}

export type BlobContentKey = {
  transformationKey: string;
  subKey: string;
};

export async function getBlobContentKeys(
  tenantId: string,
  blobName: string,
): Promise<BlobContentKey[]> {
  const { data: response }: { data: any } = await axios.get(
    `/api/tenant/${tenantId}/blob_content_keys/${blobName}`,
  );
  return response.keys;
}

export type BlobContent = {
  // note the empty string will be represented by undefined
  content?: string;
  metadata?: string[][];
};

export async function getBlobContent(
  tenantId: string,
  blobName: string,
  transformationKey?: string,
  subKey?: string,
): Promise<BlobContent> {
  const { data: response }: { data: any } = await axios.get(
    `/api/tenant/${tenantId}/blob_content/${blobName}`,
    {
      params: {
        transformation_key: transformationKey,
        sub_key: subKey,
      },
    },
  );
  return response;
}

export async function getCheckpointInfo(
  tenantId: string,
  checkpointId: string,
): Promise<CheckpointInfoData> {
  const { data: response }: { data: any } = await axios.get(
    `/api/tenant/${tenantId}/checkpoint_info/${checkpointId}`,
  );
  return response;
}

export async function clearUserContent(
  tenantId: string,
  userId: string,
  dryRun = true,
): Promise<{ count: number }> {
  const { data: response }: { data: { count: number } } = await axios.post(
    `/api/tenant/${tenantId}/user/${userId}/clear_content`,
    {
      dry_run: dryRun,
    },
  );
  return response;
}
