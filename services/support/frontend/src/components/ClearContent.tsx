import React, { useState, useEffect } from "react";
import { <PERSON>, Alert, But<PERSON>, notification } from "antd";
import { clearUserContent } from "../lib/content";

type Props = {
  tenantId: string;
  userId: string;
  onClose: () => void;
};

export function ClearContent({ tenantId, userId, onClose }: Props) {
  const [isLoading, setIsLoading] = useState<boolean>(true); // Start with loading true
  const [error, setError] = useState<string | undefined>(undefined);
  const [dryRunResult, setDryRunResult] = useState<
    { count: number } | undefined
  >(undefined);
  const [deletionResult, setDeletionResult] = useState<
    { count: number } | undefined
  >(undefined);

  // Load dry run results on component mount
  useEffect(() => {
    async function loadDryRunResults() {
      setIsLoading(true);
      setError(undefined);

      try {
        const response = await clearUserContent(tenantId, userId, true);
        setDryRunResult({ count: response.count });
      } catch (err) {
        console.error("Failed to load dry run results:", err);
        setError(`Failed to load blob count for user ${userId}: ${err}`);
      } finally {
        setIsLoading(false);
      }
    }

    loadDryRunResults();
  }, [tenantId, userId]);

  async function handleClearBlobs() {
    setIsLoading(true);
    setError(undefined);
    setDeletionResult(undefined);

    try {
      const response = await clearUserContent(tenantId, userId, false);
      const count = response.count;

      setDeletionResult({ count });

      notification.success({
        message: "Content Cleared",
        description: `Successfully deleted ${count} blob(s) for user ${userId}`,
        placement: "topRight",
      });
    } catch (err) {
      console.error("Failed to clear blobs:", err);
      setError(`Failed to clear blobs for user ${userId}: ${err}`);
      notification.error({
        message: `Failed to clear blobs for user ${userId}`,
        description: String(err),
        placement: "topRight",
      });
    } finally {
      setIsLoading(false);
    }
  }

  if (isLoading && !dryRunResult) {
    return (
      <div style={{ padding: "20px", textAlign: "center" }}>
        <Spin size="large" />
        <p>Loading blob count for user {userId}...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: "20px" }}>
      <h2>Clear Content for User: {userId}</h2>

      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: "20px" }}
        />
      )}

      {dryRunResult && (
        <Alert
          message="Blob Count"
          description={`Found ${dryRunResult.count} blob(s) that would be deleted for this user`}
          type="info"
          showIcon
          style={{ marginBottom: "20px" }}
        />
      )}

      {deletionResult && (
        <Alert
          message="Clear Content Result"
          description={`Successfully deleted ${deletionResult.count} blob(s)`}
          type="success"
          showIcon
          style={{ marginBottom: "20px" }}
        />
      )}

      <div style={{ display: "flex", gap: "10px", justifyContent: "flex-end" }}>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          type="primary"
          danger
          onClick={handleClearBlobs}
          loading={isLoading}
          disabled={
            !dryRunResult || dryRunResult.count === 0 || !!deletionResult
          }
        >
          Clear Content
        </Button>
      </div>
    </div>
  );
}
