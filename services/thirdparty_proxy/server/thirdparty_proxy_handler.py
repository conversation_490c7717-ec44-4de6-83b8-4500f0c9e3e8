import logging
from dataclasses import dataclass
from typing import Dict, <PERSON><PERSON><PERSON>, Iterator, List
import json

import grpc

from base.prompt_format.common import (
    ChatRequestEditEvents as BaseChatRequestEditEvents,
    ChatRequestFileEdit as BaseChatRequestFileEdit,
    ChatRequestIdeState as BaseChatRequestIdeState,
    ChatRequestImage as BaseChatRequestImage,
    ChatRequestNode as BaseChatRequestNode,
    ChatRequestNodeType as BaseChatRequestNodeType,
    ChatRequestSingleEdit as BaseChatRequestSingleEdit,
    ChatRequestText as BaseChatRequestText,
    ChatRequestToolResult as BaseChatRequestToolResult,
    ChatResultNode as BaseChatResultNode,
    ChatResultNodeType as BaseChatResultNodeType,
    ChatResultToolUse as BaseChatResultToolUse,
    ImageFormatType as BaseImageFormatType,
    RequestMessage as BaseRequestMessage,
    ResponseMessage as BaseResponseMessage,
    TerminalInfo as BaseTerminalInfo,
    WorkspaceFolderInfo as BaseWorkspaceFolderInfo,
)
from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient
from base.third_party_clients.fireworks_client import FireworksClient
from base.third_party_clients.openai_client import OpenAIClient
from base.third_party_clients.third_party_model_client import (
    Exchange as BaseExchange,
)
from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelClient,
)
from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelResponse as BaseThirdPartyModelResponse,
)
from base.third_party_clients.third_party_model_client import (
    ToolChoice as BaseToolChoice,
)
from base.third_party_clients.third_party_model_client import (
    ToolChoiceType as BaseToolChoiceType,
)
from base.third_party_clients.third_party_model_client import (
    ToolDefinition as BaseToolDefinition,
)
from base.third_party_clients.vertexai_client import VertexAiClient
from services.lib.request_context.request_context import RequestContext
from services.thirdparty_proxy.server.anthropic_clients import AnthropicMultiClient
from services.thirdparty_proxy.thirdparty_proxy_pb2 import (
    ChatRequestImage as ProtoChatRequestImage,
    ChatRequestText as ProtoChatRequestText,
    ChatRequestToolResult as ProtoChatRequestToolResult,
    ToolChoice as ProtoToolChoice,
    ToolChoiceType as ProtoToolChoiceType,
    ToolDefinition as ProtoToolDefinition,
    ChatRequestNode as ProtoChatRequestNode,
    ChatResultNode as ProtoChatResultNode,
    ChatResultNodeType as ProtoChatResultNodeType,
    ChatResultToolUse as ProtoChatResultToolUse,
    Exchange as ProtoExchange,
    PromptCacheUsage as ProtoPromptCacheUsage,
    ReplaceTextResponse as ProtoReplaceTextResponse,
    RequestMessage as ProtoRequestMessage,
    ResponseMessage as ProtoResponseMessage,
    ThirdPartyRequest as ProtoThirdPartyRequest,
    ThirdPartyResponse as ProtoThirdPartyResponse,
    ToolUseResponse as ProtoToolUseResponse,
)
from services.thirdparty_proxy.thirdparty_proxy_pb2_grpc import ThirdPartyProxyServicer

logger = logging.getLogger(__name__)

# Incoming model names from our services
VERTEXAI_CLAUDE_3_5 = "claude-3-5-sonnet-v2@20241022"
VERTEXAI_CLAUDE_3_7 = "claude-3-7-sonnet@20250219"
ANTHROPIC_CLAUDE_3_5 = "claude-3-5-sonnet-20241022"
ANTHROPIC_CLAUDE_3_7 = "claude-3-7-sonnet-20250219"


@dataclass
class HandlerConfig:
    anthropic_api_key: str
    openai_api_key: str
    xai_api_key: str
    fireworks_api_key: str
    gcp_project_id: str


class ThirdPartyProxyHandler(ThirdPartyProxyServicer):
    """Handler implementation for ThirdPartyProxy service."""

    def __init__(self, handler_config: HandlerConfig):
        self.anthropic_api_key = handler_config.anthropic_api_key
        self.openai_api_key = handler_config.openai_api_key
        self.fireworks_api_key = handler_config.fireworks_api_key
        self.gcp_project_id = handler_config.gcp_project_id
        self.client_registry: Dict[str, List[ThirdPartyModelClient]] = {
            ANTHROPIC_CLAUDE_3_5: [
                self._create_client_anthropic_multi(VERTEXAI_CLAUDE_3_5),
            ],
            ANTHROPIC_CLAUDE_3_7: [
                self._create_client_anthropic_multi(VERTEXAI_CLAUDE_3_7),
            ],
            VERTEXAI_CLAUDE_3_5: [
                self._create_client_anthropic_multi(VERTEXAI_CLAUDE_3_5),
            ],
            VERTEXAI_CLAUDE_3_7: [
                self._create_client_anthropic_multi(VERTEXAI_CLAUDE_3_7),
            ],
        }

    def _create_client_anthropic_multi(
        self, model_name, temperature=0, max_output_tokens=8192
    ):
        return AnthropicMultiClient(
            api_key=self.anthropic_api_key,
            project_id=self.gcp_project_id,
            model_name=model_name,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
        )

    def _get_clients(self, model_name: str) -> List[ThirdPartyModelClient]:
        """Get the appropriate clients based on the model name."""
        # If client doesn't exist, create it
        result = self.client_registry.get(model_name)
        if result is None:
            raise ValueError(f"No clients found for model {model_name}")
        return result

    def GenerateStream(
        self, request: ProtoThirdPartyRequest, context: grpc.ServicerContext
    ) -> Iterator[ProtoThirdPartyResponse]:
        """
        Implements the GenerateStream RPC method.
        Converts the gRPC request to a third-party client request and streams back responses.
        """
        request_context = RequestContext.from_grpc_context(context)
        request_context.bind_context_logging()

        logger.info(f"Received GenerateStream request for model: {request.model_name}")

        # Get the appropriate client list for this request
        clients = self._get_clients(request.model_name)

        # Convert request message and chat history
        cur_message = self._convert_message_to_base(request.cur_message)

        # Convert chat history
        chat_history = self._convert_chat_history_to_base(request.chat_history)

        # Convert tool definitions
        tool_definitions = self._convert_tool_definitions_to_base(
            request.tool_definitions
        )

        tool_choice = None
        # Convert tool choice only if tools are defined
        if len(request.tools) or len(tool_definitions) > 0:
            tool_choice = self._convert_tool_choice_to_base(request.tool_choice)

        # Generate response stream

        try:
            # Temporarily only use first client in list
            response_stream = clients[0].generate_response_stream(
                model_caller=request.model_caller,
                system_prompt=request.system_prompt,
                cur_message=cur_message,
                chat_history=chat_history,
                tools=list(request.tools),
                tool_definitions=tool_definitions,
                tool_choice=tool_choice,
                temperature=request.temperature,
                max_output_tokens=request.max_output_tokens
                if request.max_output_tokens > 0
                else None,
                prefill=request.prefill if request.prefill else None,
                use_caching=request.use_caching,
                request_context=request_context,
                yield_final_parameters=request.yield_final_parameters,
            )

            # Stream responses back
            for response in response_stream:
                yield self._convert_third_party_response_to_proto(response)

        except Exception as e:
            logger.error(f"Error in GenerateStream: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error processing request: {str(e)}")

    # Request conversion functions

    def _convert_message_to_base(
        self, message_proto: ProtoRequestMessage
    ) -> BaseRequestMessage:
        """Convert a request message from proto to the format expected by clients."""
        if not len(message_proto.nodes):
            return message_proto.text

        nodes = []
        for node in message_proto.nodes:
            new_node = None
            if node.type == ProtoChatRequestNode.NodeType.TEXT:
                new_node = BaseChatRequestNode(
                    id=node.id,
                    type=BaseChatRequestNodeType.TEXT,
                    text_node=BaseChatRequestText(content=node.text_node.content),
                    tool_result_node=None,
                    image_node=None,
                    ide_state_node=None,
                    edit_events_node=None,
                )
            elif node.type == ProtoChatRequestNode.NodeType.TOOL_RESULT:
                new_node = BaseChatRequestNode(
                    id=node.id,
                    type=BaseChatRequestNodeType.TOOL_RESULT,
                    text_node=None,
                    tool_result_node=BaseChatRequestToolResult(
                        tool_use_id=node.tool_result_node.tool_use_id,
                        content=node.tool_result_node.content,
                        is_error=node.tool_result_node.is_error,
                        request_id=node.tool_result_node.request_id
                        if node.tool_result_node.HasField("request_id")
                        else None,
                    ),
                    image_node=None,
                    ide_state_node=None,
                    edit_events_node=None,
                )
            elif node.type == ProtoChatRequestNode.NodeType.IMAGE:
                new_node = BaseChatRequestNode(
                    id=node.id,
                    type=BaseChatRequestNodeType.IMAGE,
                    text_node=None,
                    tool_result_node=None,
                    image_node=BaseChatRequestImage(
                        image_data=node.image_node.image_data,
                        format=BaseImageFormatType(node.image_node.format),
                    ),
                    ide_state_node=None,
                    edit_events_node=None,
                )
            elif node.type == ProtoChatRequestNode.NodeType.IDE_STATE:
                # Convert workspace folders
                workspace_folders = []
                for folder in node.ide_state_node.workspace_folders:
                    workspace_folders.append(
                        BaseWorkspaceFolderInfo(
                            repository_root=folder.repository_root,
                            folder_root=folder.folder_root,
                        )
                    )

                # Convert terminal info if present
                current_terminal = None
                if node.ide_state_node.HasField("current_terminal"):
                    current_terminal = BaseTerminalInfo(
                        terminal_id=node.ide_state_node.current_terminal.terminal_id,
                        current_working_directory=node.ide_state_node.current_terminal.current_working_directory,
                    )

                new_node = BaseChatRequestNode(
                    id=node.id,
                    type=BaseChatRequestNodeType.IDE_STATE,
                    text_node=None,
                    tool_result_node=None,
                    image_node=None,
                    ide_state_node=BaseChatRequestIdeState(
                        workspace_folders=workspace_folders,
                        workspace_folders_unchanged=node.ide_state_node.workspace_folders_unchanged,
                        current_terminal=current_terminal,
                    ),
                    edit_events_node=None,
                )
            elif node.type == ProtoChatRequestNode.NodeType.EDIT_EVENTS:
                # Convert edits
                edit_events = []
                for edit_event in node.edit_events_node.edit_events:
                    edit_events.append(
                        BaseChatRequestFileEdit(
                            path=edit_event.path,
                            before_blob_name=edit_event.before_blob_name,
                            after_blob_name=edit_event.after_blob_name,
                            edits=[
                                BaseChatRequestSingleEdit(
                                    before_line_start=edit.before_line_start,
                                    before_text=edit.before_text,
                                    after_line_start=edit.after_line_start,
                                    after_text=edit.after_text,
                                )
                                for edit in edit_event.edits
                            ],
                        )
                    )

                new_node = BaseChatRequestNode(
                    id=node.id,
                    type=BaseChatRequestNodeType.EDIT_EVENTS,
                    text_node=None,
                    tool_result_node=None,
                    image_node=None,
                    ide_state_node=None,
                    edit_events_node=BaseChatRequestEditEvents(
                        edit_events=edit_events,
                    ),
                )

            if new_node is None:
                raise ValueError(f"Unknown node type {node.type}")
            nodes.append(new_node)
        return nodes

    def _convert_response_message_to_base(
        self, response_proto: ProtoResponseMessage
    ) -> BaseResponseMessage:
        """Convert a response message from proto to the format expected by clients."""
        if not len(response_proto.nodes):
            return response_proto.text

        nodes = []
        for node in response_proto.nodes:
            tool_use = None
            if node.HasField("tool_use"):
                input_dict = json.loads(node.tool_use.input_json)
                tool_use = BaseChatResultToolUse(
                    name=node.tool_use.tool_name,
                    input=input_dict,
                    tool_use_id=node.tool_use.tool_use_id,
                )

            final_parameters = None
            if node.final_parameters:
                final_parameters = {k: v for k, v in node.final_parameters.items()}

            nodes.append(
                BaseChatResultNode(
                    id=node.id,
                    type=BaseChatResultNodeType(node.type),
                    content=node.content,
                    tool_use=tool_use,
                    final_parameters=final_parameters,
                )
            )
        return nodes

    def _convert_chat_history_to_base(
        self, chat_history_proto: Iterable[ProtoExchange]
    ) -> list[BaseExchange]:
        """Convert chat history from proto to the format expected by clients."""
        chat_history = []
        for exchange in chat_history_proto:
            req_msg = self._convert_message_to_base(exchange.request_message)
            resp_msg = self._convert_response_message_to_base(exchange.response_message)
            chat_history.append(BaseExchange(req_msg, resp_msg))
        return chat_history

    def _convert_tool_definitions_to_base(
        self, tool_definitions_proto: Iterable[ProtoToolDefinition]
    ) -> list[BaseToolDefinition]:
        """Convert tool definitions from proto to the format expected by clients."""
        tool_definitions = []
        for tool_def in tool_definitions_proto:
            tool_definitions.append(
                BaseToolDefinition(
                    name=tool_def.name,
                    description=tool_def.description,
                    input_schema_json=tool_def.input_schema_json,
                )
            )
        return tool_definitions

    def _convert_tool_choice_to_base(
        self, tool_choice: ProtoToolChoice | None
    ) -> BaseToolChoice | None:
        """Convert tool choice from proto to the format expected by clients."""
        if tool_choice is None:
            return None

        type = (
            BaseToolChoiceType.TOOL
            if tool_choice.type == ProtoToolChoiceType.TOOL
            else BaseToolChoiceType.ANY
            if tool_choice.type == ProtoToolChoiceType.ANY
            else BaseToolChoiceType.AUTO
            if tool_choice.type == ProtoToolChoiceType.AUTO
            else None
        )
        if type is None:
            raise ValueError(f"Unknown tool choice type {tool_choice.type}")

        name = tool_choice.name if tool_choice.name else None
        return BaseToolChoice(
            type=type,
            name=name,
        )

    # Response conversion functions

    def _convert_third_party_response_to_proto(
        self, response: BaseThirdPartyModelResponse
    ) -> ProtoThirdPartyResponse:
        """Convert a ThirdPartyModelResponse to a ThirdPartyResponse proto."""
        proto_response = ProtoThirdPartyResponse()

        if response.text:
            proto_response.text = response.text

        if response.replace_text_response:
            replace = ProtoReplaceTextResponse(
                old_text=response.replace_text_response.old_text,
                replacement_text=response.replace_text_response.replacement_text,
                start_line_number=response.replace_text_response.start_line_number,
                end_line_number=response.replace_text_response.end_line_number,
                sequence_id=response.replace_text_response.sequence_id,
            )
            proto_response.replace_text_response.CopyFrom(replace)

        if response.tool_use:
            tool_use = ProtoToolUseResponse()
            tool_use.tool_name = response.tool_use.tool_name
            tool_use.input_json = json.dumps(response.tool_use.input)
            tool_use.tool_use_id = response.tool_use.tool_use_id
            proto_response.tool_use.CopyFrom(tool_use)

        if response.prompt_cache_usage:
            cache_usage = ProtoPromptCacheUsage()
            cache_usage.input_tokens = response.prompt_cache_usage.input_tokens
            cache_usage.cache_creation_input_tokens = (
                response.prompt_cache_usage.cache_creation_input_tokens
            )
            cache_usage.cache_read_input_tokens = (
                response.prompt_cache_usage.cache_read_input_tokens
            )
            proto_response.prompt_cache_usage.CopyFrom(cache_usage)

        if response.final_parameters:
            for key, value in response.final_parameters.items():
                proto_response.final_parameters[key] = str(value)

        return proto_response
