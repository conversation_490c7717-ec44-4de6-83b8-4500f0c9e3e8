import winston from "winston";
import { isDevelopment, isProduction } from "@augment-internal/systemenv";

const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

winston.addColors({
  error: "red",
  warn: "yellow",
  info: "green",
  http: "magenta",
  debug: "white",
});

const LEVEL_SEVERITY = {
  error: "ERROR",
  warn: "WARN",
  info: "INFO",
  http: "INFO",
  debug: "DEBUG",
};

const infoFilter = winston.format((origInfo) => {
  if (typeof origInfo === "object") {
    const { level, ...info } = origInfo;
    return {
      ...info,
      level,
      time: new Date().toJSON(),
      severity: LEVEL_SEVERITY[level as keyof typeof LEVEL_SEVERITY],
    };
  }
  return origInfo;
})();

const format = isProduction()
  ? winston.format.combine(
      infoFilter,
      winston.format.timestamp(),
      winston.format.json(),
    )
  : winston.format.combine(
      winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss:ms" }),
      winston.format.colorize({ all: true }),
      winston.format.printf(
        ({
          level,
          message,
          timestamp,
          stack,
          durationMs,
        }: winston.Logform.TransformableInfo) => {
          const formattedTimestamp =
            typeof timestamp === "string"
              ? timestamp
              : new Date().toISOString();
          return `${level === "error" ? "❌" : ""} ${level}: ${message} ${
            level === "error"
              ? "| 🕗 " + formattedTimestamp.substring(0, 19)
              : ""
          } ${durationMs == null ? "" : `${durationMs}ms `}${"\n"} ${
            level === "error" ? stack : ""
          }`;
        },
      ),
      winston.format.metadata(),
    );

export const logger = winston.createLogger({
  level: process.env.DEBUG || isDevelopment() ? "debug" : "info",
  levels,
  format,
  transports: [new winston.transports.Console()],
});
