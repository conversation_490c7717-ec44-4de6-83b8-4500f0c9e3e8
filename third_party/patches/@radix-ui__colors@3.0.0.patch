diff --git a/amber-alpha.css b/amber-alpha.css
index 0caa60274613a2e6dca1750fb70b076edd6a7f71..ea177cec445bf7e99a9668e8436485066d0ed1ad 100644
--- a/amber-alpha.css
+++ b/amber-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --amber-a1: #c0800004;
   --amber-a2: #f4d10016;
   --amber-a3: #ffde003d;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --amber-a1: color(display-p3 0.757 0.514 0.024 / 0.016);
       --amber-a2: color(display-p3 0.902 0.804 0.008 / 0.079);
       --amber-a3: color(display-p3 0.965 0.859 0.004 / 0.22);
diff --git a/amber.css b/amber.css
index 8650d777b07884abfbf5c2e41817df671fa35b4f..ab05472b1cd00a211e88e525d093f96beb4c69c1 100644
--- a/amber.css
+++ b/amber.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --amber-1: #fefdfb;
   --amber-2: #fefbe9;
   --amber-3: #fff7c2;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --amber-1: color(display-p3 0.995 0.992 0.985);
       --amber-2: color(display-p3 0.994 0.986 0.921);
       --amber-3: color(display-p3 0.994 0.969 0.782);
diff --git a/blue-alpha.css b/blue-alpha.css
index 4cb8d5de8a13e8c0b6ded5e17002a577781b5b09..6de07bfa5fd29ad965967bf50482358264eef129 100644
--- a/blue-alpha.css
+++ b/blue-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --blue-a1: #0080ff04;
   --blue-a2: #008cff0b;
   --blue-a3: #008ff519;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --blue-a1: color(display-p3 0.024 0.514 1 / 0.016);
       --blue-a2: color(display-p3 0.024 0.514 0.906 / 0.04);
       --blue-a3: color(display-p3 0.012 0.506 0.914 / 0.087);
diff --git a/blue.css b/blue.css
index 925a337254ba33e915f2f4f888dea272a851c8b3..55d6d649d96de4b63bbc696071b4147e52ae6b92 100644
--- a/blue.css
+++ b/blue.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --blue-1: #fbfdff;
   --blue-2: #f4faff;
   --blue-3: #e6f4fe;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --blue-1: color(display-p3 0.986 0.992 0.999);
       --blue-2: color(display-p3 0.96 0.979 0.998);
       --blue-3: color(display-p3 0.912 0.956 0.991);
diff --git a/bronze-alpha.css b/bronze-alpha.css
index 268dbfc2d0c64615e3b59476a76c36958a9a619d..a778ca3f484f3796746442795c8c140cebcffc8d 100644
--- a/bronze-alpha.css
+++ b/bronze-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --bronze-a1: #55000003;
   --bronze-a2: #cc33000a;
   --bronze-a3: #92250015;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --bronze-a1: color(display-p3 0.349 0.024 0.024 / 0.012);
       --bronze-a2: color(display-p3 0.71 0.22 0.024 / 0.04);
       --bronze-a3: color(display-p3 0.482 0.2 0.008 / 0.083);
diff --git a/bronze.css b/bronze.css
index e2b3a7b3110cc9f0bd1346b4475efe700923606a..dac84fc4f9253c28fc4340cfd6527f308bed7dfd 100644
--- a/bronze.css
+++ b/bronze.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --bronze-1: #fdfcfc;
   --bronze-2: #fdf7f5;
   --bronze-3: #f6edea;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --bronze-1: color(display-p3 0.991 0.988 0.988);
       --bronze-2: color(display-p3 0.989 0.97 0.961);
       --bronze-3: color(display-p3 0.958 0.932 0.919);
diff --git a/brown-alpha.css b/brown-alpha.css
index 8e0f723114ced2c1ff26af455b1b2345c13cba2e..11a35145148c4c3d49a359e876290919346dfa3d 100644
--- a/brown-alpha.css
+++ b/brown-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --brown-a1: #aa550003;
   --brown-a2: #aa550009;
   --brown-a3: #a04b0018;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --brown-a1: color(display-p3 0.675 0.349 0.024 / 0.012);
       --brown-a2: color(display-p3 0.675 0.349 0.024 / 0.036);
       --brown-a3: color(display-p3 0.573 0.314 0.012 / 0.091);
diff --git a/brown.css b/brown.css
index ce813360ced7a6fe87bcb0f73dc3db6446227eb7..6784e78586b6f36d7369ca59fe04e07f233d7755 100644
--- a/brown.css
+++ b/brown.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --brown-1: #fefdfc;
   --brown-2: #fcf9f6;
   --brown-3: #f6eee7;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --brown-1: color(display-p3 0.995 0.992 0.989);
       --brown-2: color(display-p3 0.987 0.976 0.964);
       --brown-3: color(display-p3 0.959 0.936 0.909);
diff --git a/crimson-alpha.css b/crimson-alpha.css
index fa924f6db409fc492a55c2c59d63147d2e301e12..a3c90106d815bd81524472b9b5ffb492099b2a73 100644
--- a/crimson-alpha.css
+++ b/crimson-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --crimson-a1: #ff005503;
   --crimson-a2: #e0004008;
   --crimson-a3: #ff005216;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --crimson-a1: color(display-p3 0.675 0.024 0.349 / 0.012);
       --crimson-a2: color(display-p3 0.757 0.02 0.267 / 0.032);
       --crimson-a3: color(display-p3 0.859 0.008 0.294 / 0.083);
diff --git a/crimson.css b/crimson.css
index 054cd50aa01e1ef9121bb63a8e623db0211b74fc..b4edc7391344ad6d86558ed971cfa74b872e7987 100644
--- a/crimson.css
+++ b/crimson.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --crimson-1: #fffcfd;
   --crimson-2: #fef7f9;
   --crimson-3: #ffe9f0;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --crimson-1: color(display-p3 0.998 0.989 0.992);
       --crimson-2: color(display-p3 0.991 0.969 0.976);
       --crimson-3: color(display-p3 0.987 0.917 0.941);
diff --git a/cyan-alpha.css b/cyan-alpha.css
index 2261e1bf6d1beb2848ab5cf451cfc5728ce4daeb..9fa81cf6a3de78e8f9a930468f36ed2e0ab3bfd2 100644
--- a/cyan-alpha.css
+++ b/cyan-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --cyan-a1: #0099cc05;
   --cyan-a2: #009db10d;
   --cyan-a3: #00c2d121;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --cyan-a1: color(display-p3 0.02 0.608 0.804 / 0.02);
       --cyan-a2: color(display-p3 0.02 0.557 0.647 / 0.044);
       --cyan-a3: color(display-p3 0.004 0.694 0.796 / 0.114);
diff --git a/cyan.css b/cyan.css
index 1f2e57c787cf6197e309d17173d54b747c434098..b486977e785dd00b338bf7728e2f646ccf736845 100644
--- a/cyan.css
+++ b/cyan.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --cyan-1: #fafdfe;
   --cyan-2: #f2fafb;
   --cyan-3: #def7f9;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --cyan-1: color(display-p3 0.982 0.992 0.996);
       --cyan-2: color(display-p3 0.955 0.981 0.984);
       --cyan-3: color(display-p3 0.888 0.965 0.975);
diff --git a/gold-alpha.css b/gold-alpha.css
index 1b21c54725480da54232e41d44647a4cf5370a1c..15ded9383c17ca1297cf0a4830166fb6e970e14d 100644
--- a/gold-alpha.css
+++ b/gold-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --gold-a1: #55550003;
   --gold-a2: #9d8a000d;
   --gold-a3: #75600018;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --gold-a1: color(display-p3 0.349 0.349 0.024 / 0.012);
       --gold-a2: color(display-p3 0.592 0.514 0.024 / 0.048);
       --gold-a3: color(display-p3 0.4 0.357 0.012 / 0.091);
diff --git a/gold.css b/gold.css
index 5d2bce43ea9fd0e226e1e89b320ddcf336d46a47..e53567bdfe9e2a582d962156f3b225ce9dd03401 100644
--- a/gold.css
+++ b/gold.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --gold-1: #fdfdfc;
   --gold-2: #faf9f2;
   --gold-3: #f2f0e7;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --gold-1: color(display-p3 0.992 0.992 0.989);
       --gold-2: color(display-p3 0.98 0.976 0.953);
       --gold-3: color(display-p3 0.947 0.94 0.909);
diff --git a/grass-alpha.css b/grass-alpha.css
index 65a120768043ebd0de28731e304b1e2b008457d1..a34c524d8fe0d43ca8b488025a229a0a32ec6b36 100644
--- a/grass-alpha.css
+++ b/grass-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --grass-a1: #00c00004;
   --grass-a2: #0099000a;
   --grass-a3: #00970016;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --grass-a1: color(display-p3 0.024 0.757 0.024 / 0.016);
       --grass-a2: color(display-p3 0.024 0.565 0.024 / 0.036);
       --grass-a3: color(display-p3 0.059 0.576 0.008 / 0.083);
diff --git a/grass.css b/grass.css
index 09957145ee0beffaa6756e3d101b3f63ddad48d6..1790203d4e589bb527ec082ca6503fb239c8d2ef 100644
--- a/grass.css
+++ b/grass.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --grass-1: #fbfefb;
   --grass-2: #f5fbf5;
   --grass-3: #e9f6e9;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --grass-1: color(display-p3 0.986 0.996 0.985);
       --grass-2: color(display-p3 0.966 0.983 0.964);
       --grass-3: color(display-p3 0.923 0.965 0.917);
diff --git a/gray-alpha.css b/gray-alpha.css
index 86eccb377b5253cf8b2c681a52efe106a426fef4..580e03c721d03f2e06708de98465651df62b1a2b 100644
--- a/gray-alpha.css
+++ b/gray-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --gray-a1: #00000003;
   --gray-a2: #00000006;
   --gray-a3: #0000000f;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --gray-a1: color(display-p3 0 0 0 / 0.012);
       --gray-a2: color(display-p3 0 0 0 / 0.024);
       --gray-a3: color(display-p3 0 0 0 / 0.063);
diff --git a/gray.css b/gray.css
index 0e7d2166b586b3176101890e5080df32b17d16e6..6b758790982b34c7465bce4084a9b917b3cfb4bd 100644
--- a/gray.css
+++ b/gray.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --gray-1: #fcfcfc;
   --gray-2: #f9f9f9;
   --gray-3: #f0f0f0;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --gray-1: color(display-p3 0.988 0.988 0.988);
       --gray-2: color(display-p3 0.975 0.975 0.975);
       --gray-3: color(display-p3 0.939 0.939 0.939);
diff --git a/green-alpha.css b/green-alpha.css
index fcdc67e6c55da146c504d1654a1d8672cd936ad0..cc6976fdb0c63bbcb201d2887bd1b0cede87b3ad 100644
--- a/green-alpha.css
+++ b/green-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --green-a1: #00c04004;
   --green-a2: #00a32f0b;
   --green-a3: #00a43319;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --green-a1: color(display-p3 0.024 0.757 0.267 / 0.016);
       --green-a2: color(display-p3 0.024 0.565 0.129 / 0.036);
       --green-a3: color(display-p3 0.012 0.596 0.145 / 0.087);
diff --git a/green.css b/green.css
index 17c57eb17d65e309eebab893a61c29974f95821f..ab5f45a703ae56bd1015dafda5508d00f35270f8 100644
--- a/green.css
+++ b/green.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --green-1: #fbfefc;
   --green-2: #f4fbf6;
   --green-3: #e6f6eb;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --green-1: color(display-p3 0.986 0.996 0.989);
       --green-2: color(display-p3 0.963 0.983 0.967);
       --green-3: color(display-p3 0.913 0.964 0.925);
diff --git a/indigo-alpha.css b/indigo-alpha.css
index 59d9cb230074242b57a6acc8438eb1ae7adc2ee6..d1bdb5d5249da3a98aad0be987d9d6e8d80e7042 100644
--- a/indigo-alpha.css
+++ b/indigo-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --indigo-a1: #00008002;
   --indigo-a2: #0040ff08;
   --indigo-a3: #0047f112;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --indigo-a1: color(display-p3 0.02 0.02 0.51 / 0.008);
       --indigo-a2: color(display-p3 0.024 0.161 0.863 / 0.028);
       --indigo-a3: color(display-p3 0.008 0.239 0.886 / 0.067);
diff --git a/indigo.css b/indigo.css
index ddca600e51e1d04734f543afa81e3e39d01fc342..9816da714c1507d828d033d1d6e5cda59d5d5d7b 100644
--- a/indigo.css
+++ b/indigo.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --indigo-1: #fdfdfe;
   --indigo-2: #f7f9ff;
   --indigo-3: #edf2fe;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --indigo-1: color(display-p3 0.992 0.992 0.996);
       --indigo-2: color(display-p3 0.971 0.977 0.998);
       --indigo-3: color(display-p3 0.933 0.948 0.992);
diff --git a/iris-alpha.css b/iris-alpha.css
index 31ce53e1f48f25f61e943230c2e2c3d6af946d51..8d3db34e70f492c5ca894ac38d859cf2b1490c68 100644
--- a/iris-alpha.css
+++ b/iris-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --iris-a1: #0000ff02;
   --iris-a2: #0000ff07;
   --iris-a3: #0011ee0f;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --iris-a1: color(display-p3 0.02 0.02 1 / 0.008);
       --iris-a2: color(display-p3 0.024 0.024 0.863 / 0.028);
       --iris-a3: color(display-p3 0.004 0.071 0.871 / 0.059);
diff --git a/iris.css b/iris.css
index 95aaa680ce3f2bc23e562caefc93ae33cbf52271..03942cda33d6598553e5c7a97b45bb2253c29003 100644
--- a/iris.css
+++ b/iris.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --iris-1: #fdfdff;
   --iris-2: #f8f8ff;
   --iris-3: #f0f1fe;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --iris-1: color(display-p3 0.992 0.992 0.999);
       --iris-2: color(display-p3 0.972 0.973 0.998);
       --iris-3: color(display-p3 0.943 0.945 0.992);
diff --git a/jade-alpha.css b/jade-alpha.css
index b91d4910ed24377ed9cc4d25cd232b0988ee7392..3da9735c028b7d035b967f715125b48bbf2c4e3e 100644
--- a/jade-alpha.css
+++ b/jade-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --jade-a1: #00c08004;
   --jade-a2: #00a3460b;
   --jade-a3: #00ae4819;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --jade-a1: color(display-p3 0.024 0.757 0.514 / 0.016);
       --jade-a2: color(display-p3 0.024 0.612 0.22 / 0.04);
       --jade-a3: color(display-p3 0.012 0.596 0.235 / 0.087);
diff --git a/jade.css b/jade.css
index 39f1ba386c234b2a87c3383b1a08841913380b46..5c5364b98f0f2c568648e20659c3d90109d4efaf 100644
--- a/jade.css
+++ b/jade.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --jade-1: #fbfefd;
   --jade-2: #f4fbf7;
   --jade-3: #e6f7ed;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --jade-1: color(display-p3 0.986 0.996 0.992);
       --jade-2: color(display-p3 0.962 0.983 0.969);
       --jade-3: color(display-p3 0.912 0.965 0.932);
diff --git a/lime-alpha.css b/lime-alpha.css
index 8c4f404efdef7215fe96dd755e6ee143dce11878..b61f8ef36a4653e1ca5920d179ca5d6221594336 100644
--- a/lime-alpha.css
+++ b/lime-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --lime-a1: #66990005;
   --lime-a2: #6b95000c;
   --lime-a3: #96c80029;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --lime-a1: color(display-p3 0.412 0.608 0.02 / 0.02);
       --lime-a2: color(display-p3 0.514 0.592 0.024 / 0.048);
       --lime-a3: color(display-p3 0.584 0.765 0.008 / 0.15);
diff --git a/lime.css b/lime.css
index 8def0c2884c8e27041514ce3e5c35704e14b9fd8..a9853b394ba32646f4a9fa31828950cf1a88f6f9 100644
--- a/lime.css
+++ b/lime.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --lime-1: #fcfdfa;
   --lime-2: #f8faf3;
   --lime-3: #eef6d6;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --lime-1: color(display-p3 0.989 0.992 0.981);
       --lime-2: color(display-p3 0.975 0.98 0.954);
       --lime-3: color(display-p3 0.939 0.965 0.851);
diff --git a/mauve-alpha.css b/mauve-alpha.css
index 4ac1c38557643881902bda3bfc263c9799ef22eb..15aa7d1b023021cde1c16c2cf1353f78faff9d8f 100644
--- a/mauve-alpha.css
+++ b/mauve-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --mauve-a1: #55005503;
   --mauve-a2: #2b005506;
   --mauve-a3: #30004010;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --mauve-a1: color(display-p3 0.349 0.024 0.349 / 0.012);
       --mauve-a2: color(display-p3 0.184 0.024 0.349 / 0.024);
       --mauve-a3: color(display-p3 0.129 0.008 0.255 / 0.063);
diff --git a/mauve.css b/mauve.css
index 7c9c023974e433b0ee52c788970931dbd68080b7..af9b5b5626133fcdfd50554821251d2b5c6e282a 100644
--- a/mauve.css
+++ b/mauve.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --mauve-1: #fdfcfd;
   --mauve-2: #faf9fb;
   --mauve-3: #f2eff3;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --mauve-1: color(display-p3 0.991 0.988 0.992);
       --mauve-2: color(display-p3 0.98 0.976 0.984);
       --mauve-3: color(display-p3 0.946 0.938 0.952);
diff --git a/mint-alpha.css b/mint-alpha.css
index 13dfe38815466be8fdb85699e60004763b03842b..7484d886559807c051aaa908f5b35da5ecb9b5a3 100644
--- a/mint-alpha.css
+++ b/mint-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --mint-a1: #00d5aa06;
   --mint-a2: #00b18a0d;
   --mint-a3: #00d29e22;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --mint-a1: color(display-p3 0.02 0.804 0.608 / 0.02);
       --mint-a2: color(display-p3 0.02 0.647 0.467 / 0.044);
       --mint-a3: color(display-p3 0.004 0.761 0.553 / 0.114);
diff --git a/mint.css b/mint.css
index f80c1b38ed7df235e36cd7060a069f0a897ef246..c7f16d225c31cc63ed27deef9785b516934d500f 100644
--- a/mint.css
+++ b/mint.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --mint-1: #f9fefd;
   --mint-2: #f2fbf9;
   --mint-3: #ddf9f2;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --mint-1: color(display-p3 0.98 0.995 0.992);
       --mint-2: color(display-p3 0.957 0.985 0.977);
       --mint-3: color(display-p3 0.888 0.972 0.95);
diff --git a/olive-alpha.css b/olive-alpha.css
index c24c79521243d236cec4886bf0c58e8579fcbc47..347131c8b9e458c576e6959280d81ea9791d4416 100644
--- a/olive-alpha.css
+++ b/olive-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --olive-a1: #00550003;
   --olive-a2: #00490007;
   --olive-a3: #00200010;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --olive-a1: color(display-p3 0.024 0.349 0.024 / 0.012);
       --olive-a2: color(display-p3 0.024 0.302 0.024 / 0.028);
       --olive-a3: color(display-p3 0.008 0.129 0.008 / 0.063);
diff --git a/olive.css b/olive.css
index 80e93722c7eed1b9ea5ffea75a3ce0d413a87982..90b93b01b71861d30a80d19ace78e8996b7183f1 100644
--- a/olive.css
+++ b/olive.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --olive-1: #fcfdfc;
   --olive-2: #f8faf8;
   --olive-3: #eff1ef;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --olive-1: color(display-p3 0.989 0.992 0.989);
       --olive-2: color(display-p3 0.974 0.98 0.973);
       --olive-3: color(display-p3 0.939 0.945 0.937);
diff --git a/orange-alpha.css b/orange-alpha.css
index 41421ab0cf74bc88660abb68fc4a73e059ccc32d..050d6a046c99d790c9ad9b772d40e9fa83d9774c 100644
--- a/orange-alpha.css
+++ b/orange-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --orange-a1: #c0400004;
   --orange-a2: #ff8e0012;
   --orange-a3: #ff9c0029;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --orange-a1: color(display-p3 0.757 0.267 0.024 / 0.016);
       --orange-a2: color(display-p3 0.886 0.533 0.008 / 0.067);
       --orange-a3: color(display-p3 0.922 0.584 0.008 / 0.15);
diff --git a/orange.css b/orange.css
index 4182cb510ed98b06b25ef1ab819c9a5c295fb4ec..c71441556ee5deeb7dbedd78663451f542e4629c 100644
--- a/orange.css
+++ b/orange.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --orange-1: #fefcfb;
   --orange-2: #fff7ed;
   --orange-3: #ffefd6;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --orange-1: color(display-p3 0.995 0.988 0.985);
       --orange-2: color(display-p3 0.994 0.968 0.934);
       --orange-3: color(display-p3 0.989 0.938 0.85);
diff --git a/pink-alpha.css b/pink-alpha.css
index 638948d63ecbef140924cfa311d911a6144ce0fd..8ddf1b6332e1a1b09c485b0affac51e16730f6d2 100644
--- a/pink-alpha.css
+++ b/pink-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --pink-a1: #ff00aa03;
   --pink-a2: #e0008008;
   --pink-a3: #f4008c16;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --pink-a1: color(display-p3 0.675 0.024 0.675 / 0.012);
       --pink-a2: color(display-p3 0.757 0.02 0.51 / 0.032);
       --pink-a3: color(display-p3 0.765 0.008 0.529 / 0.083);
diff --git a/pink.css b/pink.css
index b761a232da911755a5d33cb2c720676bd9b1625c..01efd71f7cb9f32651b2e52640443b2065277746 100644
--- a/pink.css
+++ b/pink.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --pink-1: #fffcfe;
   --pink-2: #fef7fb;
   --pink-3: #fee9f5;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --pink-1: color(display-p3 0.998 0.989 0.996);
       --pink-2: color(display-p3 0.992 0.97 0.985);
       --pink-3: color(display-p3 0.981 0.917 0.96);
diff --git a/plum-alpha.css b/plum-alpha.css
index 0a2b4b0024998887dcc74faa5ac5ef6db1ff248f..03c552e8d1e2494090151d55380918492b8aecb4 100644
--- a/plum-alpha.css
+++ b/plum-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --plum-a1: #aa00ff03;
   --plum-a2: #c000c008;
   --plum-a3: #cc00cc14;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --plum-a1: color(display-p3 0.675 0.024 1 / 0.012);
       --plum-a2: color(display-p3 0.58 0.024 0.58 / 0.028);
       --plum-a3: color(display-p3 0.655 0.008 0.753 / 0.079);
diff --git a/plum.css b/plum.css
index 9b7e000a7b9dff537ae997cc64bbca2a8059122d..3906fe9b6289984e3f02be9983fc1d2d0dcff5ab 100644
--- a/plum.css
+++ b/plum.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --plum-1: #fefcff;
   --plum-2: #fdf7fd;
   --plum-3: #fbebfb;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --plum-1: color(display-p3 0.995 0.988 0.999);
       --plum-2: color(display-p3 0.988 0.971 0.99);
       --plum-3: color(display-p3 0.973 0.923 0.98);
diff --git a/purple-alpha.css b/purple-alpha.css
index e1d0a8710118a902e0eb0cac61e90a2307111472..a0adf91ce0f28623782b852521c4953669e8f191 100644
--- a/purple-alpha.css
+++ b/purple-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --purple-a1: #aa00aa03;
   --purple-a2: #8000e008;
   --purple-a3: #8e00f112;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --purple-a1: color(display-p3 0.675 0.024 0.675 / 0.012);
       --purple-a2: color(display-p3 0.443 0.024 0.722 / 0.028);
       --purple-a3: color(display-p3 0.506 0.008 0.835 / 0.071);
diff --git a/purple.css b/purple.css
index 6e4720f699ad4536e2cc4510679db0fe496c7116..bd384cda10d96415972855ef7a4f43837f77b486 100644
--- a/purple.css
+++ b/purple.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --purple-1: #fefcfe;
   --purple-2: #fbf7fe;
   --purple-3: #f7edfe;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --purple-1: color(display-p3 0.995 0.988 0.996);
       --purple-2: color(display-p3 0.983 0.971 0.993);
       --purple-3: color(display-p3 0.963 0.931 0.989);
diff --git a/red-alpha.css b/red-alpha.css
index 492b32d8ea105dce31373533abc3b41ae075d4f7..92cd5f357a863822d9076ef4428d532a65be8279 100644
--- a/red-alpha.css
+++ b/red-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --red-a1: #ff000003;
   --red-a2: #ff000008;
   --red-a3: #f3000d14;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --red-a1: color(display-p3 0.675 0.024 0.024 / 0.012);
       --red-a2: color(display-p3 0.863 0.024 0.024 / 0.028);
       --red-a3: color(display-p3 0.792 0.008 0.008 / 0.075);
diff --git a/red.css b/red.css
index 041855fca3392fa32538e900448129fcb7dd33a6..f343a028f46d6eedf52d7f176ca54de7c795f5f8 100644
--- a/red.css
+++ b/red.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --red-1: #fffcfc;
   --red-2: #fff7f7;
   --red-3: #feebec;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --red-1: color(display-p3 0.998 0.989 0.988);
       --red-2: color(display-p3 0.995 0.971 0.971);
       --red-3: color(display-p3 0.985 0.925 0.925);
diff --git a/ruby-alpha.css b/ruby-alpha.css
index aa9e1aa51b51301daeb8ead43da3996d83c0cc8c..6ce39b173825dbef2ff5092fc6bb3cae7b959f89 100644
--- a/ruby-alpha.css
+++ b/ruby-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --ruby-a1: #ff005503;
   --ruby-a2: #ff002008;
   --ruby-a3: #f3002515;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --ruby-a1: color(display-p3 0.675 0.024 0.349 / 0.012);
       --ruby-a2: color(display-p3 0.863 0.024 0.024 / 0.028);
       --ruby-a3: color(display-p3 0.804 0.008 0.11 / 0.079);
diff --git a/ruby.css b/ruby.css
index 03dcf2e939befb157c99956e1e02d35a66cf37d3..2e322c2e6088ac260d3901bf4ffb096738b00d66 100644
--- a/ruby.css
+++ b/ruby.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --ruby-1: #fffcfd;
   --ruby-2: #fff7f8;
   --ruby-3: #feeaed;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --ruby-1: color(display-p3 0.998 0.989 0.992);
       --ruby-2: color(display-p3 0.995 0.971 0.974);
       --ruby-3: color(display-p3 0.983 0.92 0.928);
diff --git a/sage-alpha.css b/sage-alpha.css
index f86e457558acdc6b8d14cdb5ec9420cd59e308d0..ee0c4929454aaad8d711d80a83dd73d3840f2710 100644
--- a/sage-alpha.css
+++ b/sage-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --sage-a1: #00804004;
   --sage-a2: #00402008;
   --sage-a3: #002d1e11;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --sage-a1: color(display-p3 0.024 0.514 0.267 / 0.016);
       --sage-a2: color(display-p3 0.02 0.267 0.145 / 0.032);
       --sage-a3: color(display-p3 0.008 0.184 0.125 / 0.067);
diff --git a/sage.css b/sage.css
index 94ec37bf5c20fc58234865b718fad066939a8ab0..5c1f12a7bedfefe60f63821e69799041d2e45014 100644
--- a/sage.css
+++ b/sage.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --sage-1: #fbfdfc;
   --sage-2: #f7f9f8;
   --sage-3: #eef1f0;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --sage-1: color(display-p3 0.986 0.992 0.988);
       --sage-2: color(display-p3 0.97 0.977 0.974);
       --sage-3: color(display-p3 0.935 0.944 0.94);
diff --git a/sand-alpha.css b/sand-alpha.css
index 8446b6ab247b10b5e8eda34e26653a2e0c18da0c..3bb446c72e7b8fd296a8f19adeb7924f345fd867 100644
--- a/sand-alpha.css
+++ b/sand-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --sand-a1: #55550003;
   --sand-a2: #25250007;
   --sand-a3: #20100010;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --sand-a1: color(display-p3 0.349 0.349 0.024 / 0.012);
       --sand-a2: color(display-p3 0.161 0.161 0.024 / 0.028);
       --sand-a3: color(display-p3 0.067 0.067 0.008 / 0.063);
diff --git a/sand.css b/sand.css
index cd12f9599ce70c8d3ff53a594bc25164d345982e..4d18dd0c4bc9adb4055a216bc3784a57b18404e4 100644
--- a/sand.css
+++ b/sand.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --sand-1: #fdfdfc;
   --sand-2: #f9f9f8;
   --sand-3: #f1f0ef;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --sand-1: color(display-p3 0.992 0.992 0.989);
       --sand-2: color(display-p3 0.977 0.977 0.973);
       --sand-3: color(display-p3 0.943 0.942 0.936);
diff --git a/sky-alpha.css b/sky-alpha.css
index 5f056e4e18313112698e65c4ceeaa6430500a01f..ba86ba1f56ae510a920758ab7b505a9278b2858d 100644
--- a/sky-alpha.css
+++ b/sky-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --sky-a1: #00d5ff06;
   --sky-a2: #00a4db0e;
   --sky-a3: #00b3ee1e;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --sky-a1: color(display-p3 0.02 0.804 1 / 0.02);
       --sky-a2: color(display-p3 0.024 0.592 0.757 / 0.048);
       --sky-a3: color(display-p3 0.004 0.655 0.886 / 0.102);
diff --git a/sky.css b/sky.css
index 1d53336be0cc1fb5c89bed36d1575ace88bcbe46..4a8f93767a909434f0fe7f070347685a04002a45 100644
--- a/sky.css
+++ b/sky.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --sky-1: #f9feff;
   --sky-2: #f1fafd;
   --sky-3: #e1f6fd;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --sky-1: color(display-p3 0.98 0.995 0.999);
       --sky-2: color(display-p3 0.953 0.98 0.99);
       --sky-3: color(display-p3 0.899 0.963 0.989);
diff --git a/slate-alpha.css b/slate-alpha.css
index 0ce65187e3bb1cd6c93ff55e00a5e541cff28a8a..808612643b7d0bfcc9d7fcbab28a7f4f0b038921 100644
--- a/slate-alpha.css
+++ b/slate-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --slate-a1: #00005503;
   --slate-a2: #00005506;
   --slate-a3: #0000330f;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --slate-a1: color(display-p3 0.024 0.024 0.349 / 0.012);
       --slate-a2: color(display-p3 0.024 0.024 0.349 / 0.024);
       --slate-a3: color(display-p3 0.004 0.004 0.204 / 0.059);
diff --git a/slate.css b/slate.css
index 62bf44a8f169d5385a037a724581f05446d9b1d4..83bc33bb9894a2154e68b95ef1758aefb2e3bb8e 100644
--- a/slate.css
+++ b/slate.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --slate-1: #fcfcfd;
   --slate-2: #f9f9fb;
   --slate-3: #f0f0f3;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --slate-1: color(display-p3 0.988 0.988 0.992);
       --slate-2: color(display-p3 0.976 0.976 0.984);
       --slate-3: color(display-p3 0.94 0.941 0.953);
diff --git a/teal-alpha.css b/teal-alpha.css
index e4b6a856613b0953619653dc3b199638d6e5df88..716a62f44346dd02750201e74c3949be31f50574 100644
--- a/teal-alpha.css
+++ b/teal-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --teal-a1: #00cc9905;
   --teal-a2: #00aa800c;
   --teal-a3: #00c69d1f;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --teal-a1: color(display-p3 0.024 0.757 0.514 / 0.016);
       --teal-a2: color(display-p3 0.02 0.647 0.467 / 0.044);
       --teal-a3: color(display-p3 0.004 0.741 0.557 / 0.106);
diff --git a/teal.css b/teal.css
index 5d1ae4ed3acf40dfd07fe4c03020beb26832e9f3..f1e0dd3de56977a2df5341475490a57bbc9f2a31 100644
--- a/teal.css
+++ b/teal.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --teal-1: #fafefd;
   --teal-2: #f3fbf9;
   --teal-3: #e0f8f3;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --teal-1: color(display-p3 0.983 0.996 0.992);
       --teal-2: color(display-p3 0.958 0.983 0.976);
       --teal-3: color(display-p3 0.895 0.971 0.952);
diff --git a/tomato-alpha.css b/tomato-alpha.css
index b44dd9099283c5683e875c8b8717f445dd671671..6128bd4559140c2e41289d8b3370ec6ca54b9c40 100644
--- a/tomato-alpha.css
+++ b/tomato-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --tomato-a1: #ff000003;
   --tomato-a2: #ff200008;
   --tomato-a3: #f52b0018;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --tomato-a1: color(display-p3 0.675 0.024 0.024 / 0.012);
       --tomato-a2: color(display-p3 0.757 0.145 0.02 / 0.032);
       --tomato-a3: color(display-p3 0.831 0.184 0.012 / 0.091);
diff --git a/tomato.css b/tomato.css
index b722b180c3e742938fbc86e8d163615ee31fa12b..e2c01d46092f3e690ab500de155a9d0a5d72006c 100644
--- a/tomato.css
+++ b/tomato.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --tomato-1: #fffcfc;
   --tomato-2: #fff8f7;
   --tomato-3: #feebe7;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --tomato-1: color(display-p3 0.998 0.989 0.988);
       --tomato-2: color(display-p3 0.994 0.974 0.969);
       --tomato-3: color(display-p3 0.985 0.924 0.909);
diff --git a/violet-alpha.css b/violet-alpha.css
index 20e84ddcd9b762a5eaab68f94f14c111ebc361ae..26035403aaff39f1b2c7a6856bccdb4217bc0fdd 100644
--- a/violet-alpha.css
+++ b/violet-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --violet-a1: #5500aa03;
   --violet-a2: #4900ff07;
   --violet-a3: #4400ee0f;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --violet-a1: color(display-p3 0.349 0.024 0.675 / 0.012);
       --violet-a2: color(display-p3 0.161 0.024 0.863 / 0.028);
       --violet-a3: color(display-p3 0.204 0.004 0.871 / 0.059);
diff --git a/violet.css b/violet.css
index dcf4e8f19318f33ab1aa24b64907e4014f46f51b..5110fa178e9c881acba75d644f772fba810b1e6a 100644
--- a/violet.css
+++ b/violet.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --violet-1: #fdfcfe;
   --violet-2: #faf8ff;
   --violet-3: #f4f0fe;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --violet-1: color(display-p3 0.991 0.988 0.995);
       --violet-2: color(display-p3 0.978 0.974 0.998);
       --violet-3: color(display-p3 0.953 0.943 0.993);
diff --git a/yellow-alpha.css b/yellow-alpha.css
index b76148de391ab7af88d6ce9349ca18341c949f29..e81a5857d767310e6a03408049c511bb8e2bb9e1 100644
--- a/yellow-alpha.css
+++ b/yellow-alpha.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --yellow-a1: #aaaa0006;
   --yellow-a2: #f4dd0016;
   --yellow-a3: #ffee0047;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --yellow-a1: color(display-p3 0.675 0.675 0.024 / 0.024);
       --yellow-a2: color(display-p3 0.953 0.855 0.008 / 0.079);
       --yellow-a3: color(display-p3 0.988 0.925 0.004 / 0.251);
diff --git a/yellow.css b/yellow.css
index f0e45e456a5913ce449cb71885e56d7905fdf71e..b06266f3c1aaa86d3acde5463d6ea4c502da9081 100644
--- a/yellow.css
+++ b/yellow.css
@@ -1,4 +1,4 @@
-:root, .light, .light-theme {
+.light, .light-theme {
   --yellow-1: #fdfdf9;
   --yellow-2: #fefce9;
   --yellow-3: #fffab8;
@@ -15,7 +15,7 @@

 @supports (color: color(display-p3 1 1 1)) {
   @media (color-gamut: p3) {
-    :root, .light, .light-theme {
+    .light, .light-theme {
       --yellow-1: color(display-p3 0.992 0.992 0.978);
       --yellow-2: color(display-p3 0.995 0.99 0.922);
       --yellow-3: color(display-p3 0.997 0.982 0.749);
