// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



syntax = "proto2";

package research_scann;

import "third_party/scann/scann/proto/auto_tuning.proto";
import "third_party/scann/scann/proto/brute_force.proto";
import "third_party/scann/scann/proto/crowding.proto";
import "third_party/scann/scann/proto/disjoint_restrict_token.proto";
import "third_party/scann/scann/proto/distance_measure.proto";
import "third_party/scann/scann/proto/exact_reordering.proto";
import "third_party/scann/scann/proto/hash.proto";
import "third_party/scann/scann/proto/input_output.proto";
import "third_party/scann/scann/proto/metadata.proto";
import "third_party/scann/scann/proto/min_distance.proto";
import "third_party/scann/scann/proto/partitioning.proto";

message ScannConfig {
  extensions 1000 to max;

  optional string dataset_name = 32;

  optional int32 num_neighbors = 3 [default = **********];

  optional float epsilon_distance = 4 [default = inf];

  optional MinDistanceConfig min_distance = 44;

  reserved 24, 37, 39;

  optional Crowding crowding = 29;

  optional DistanceMeasureConfig distance_measure = 5;

  optional ExactReordering exact_reordering = 17;

  optional InputOutputConfig input_output = 6;

  optional BruteForceConfig brute_force = 7;

  optional PartitioningConfig partitioning = 8;

  optional HashConfig hash = 13;

  optional MetadataConfig metadata = 20;

  optional int32 num_single_shard_neighbors = 21;

  optional bytes custom_search_method = 22;

  reserved 23;

  optional DisjointRestrictToken disjoint_restrict_token = 31;

  optional AutopilotConfig autopilot = 43;

  reserved 11;
  reserved 14;
  reserved 15;
  reserved 19;
  reserved 26;
  reserved 27;
  reserved 28;
  reserved 30;
  reserved 34;
  reserved 35;
  reserved 36;
  reserved 38;
  reserved 40;
  reserved "all_pair";
  reserved "offline_distributed_config";
  reserved "chunked_hamming";
  reserved "sparse_logistic";
  reserved "generalized_hash_search_config";
  reserved "random_searcher";
  reserved "sparse_dense_hybrid_batched";
  reserved "inverted_index";
  reserved "incremental_updates";
  reserved "auto_tuning";
  reserved "scoring_extension";

  reserved 16, 33, 18;
  reserved "chunked_inverted_index";
  reserved "legacy_inverted_index";
  reserved "pruned_inverted_index_cosine";

  enum SearchParadigm {
    QUERY_DATABASE = 0;
    ALL_PAIR = 1;
  }
  optional SearchParadigm search_paradigm = 1;

  enum SearchType {
    KNN = 0;
    ENN = 1;
    HYBRID = 2;
  }
  optional SearchType search_type = 2;

  reserved 25;
}
