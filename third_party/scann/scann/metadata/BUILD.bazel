load("//third_party/scann:scann.bzl", "scann_cc_library")
# Description:
#   This package defines classes for returning various metadata that might be
#   returned from a nearest-neighbor query along with docid and distance.

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

# Libraries
# ===========================================================================

scann_cc_library(
    name = "metadata_getter",
    srcs = ["metadata_getter.cc"],
    hdrs = ["metadata_getter.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/data_format:features_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/synchronization",
    ],
)

# Tests
# ===========================================================================
