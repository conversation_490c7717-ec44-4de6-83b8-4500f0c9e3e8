name: clang-tidy-review

on:
  pull_request:
    branches:
      - master

jobs:
  clang-tidy-review:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - uses: ZedThree/clang-tidy-review@v0.13.0
      id: review
      with:
        lgtm_comment_body: ''
        build_dir: build
        cmake_command: cmake . -B build -DCMAKE_EXPORT_COMPILE_COMMANDS=on
        split_workflow: true

    - uses: ZedThree/clang-tidy-review/upload@v0.13.0
